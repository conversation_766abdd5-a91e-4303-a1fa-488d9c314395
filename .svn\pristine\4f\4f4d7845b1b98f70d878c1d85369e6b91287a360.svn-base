﻿namespace ContinuityPatrol.Application.Features.Archive.Queries.GetTableNameUnique;

public class GetTableNameUniqueQueryHandler : IRequestHandler<GetTableNameUniqueQuery, bool>
{
    private readonly IArchiveRepository _archiveRepository;

    public GetTableNameUniqueQueryHandler(IArchiveRepository archiveRepository)
    {
        _archiveRepository = archiveRepository;
    }

    public async Task<bool> Handle(GetTableNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _archiveRepository.IsTableNameExist(request.TableName, request.Id);
    }
}