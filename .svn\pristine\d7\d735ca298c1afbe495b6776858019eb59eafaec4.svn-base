﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetPaginatedList;

public class GetBusinessServiceHealthStatusPaginatedListQueryHandler : IRequestHandler<
    GetBusinessServiceHealthStatusPaginatedListQuery, PaginatedResult<BusinessServiceHealthStatusListVm>>
{
    private readonly IBusinessServiceHealthStatusRepository _businessServiceHealthRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceHealthStatusPaginatedListQueryHandler(
        IBusinessServiceHealthStatusRepository businessServiceHealthRepository, IMapper mapper)
    {
        _businessServiceHealthRepository = businessServiceHealthRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<BusinessServiceHealthStatusListVm>> Handle(
        GetBusinessServiceHealthStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new BusinessServiceHealthStatusFilterSpecification(request.SearchString);

        var queryable = await _businessServiceHealthRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var businessServiceHealth = _mapper.Map<PaginatedResult<BusinessServiceHealthStatusListVm>>(queryable);

        return businessServiceHealth;
        //var queryable = _businessServiceHealthRepository.GetPaginatedQuery();

        //var productFilterSpec = new BusinessServiceHealthStatusFilterSpecification(request.SearchString);

        //var businessServiceHealth = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BusinessServiceHealthStatusListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return businessServiceHealth;
    }
}