using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BackUpFixture
{
    public List<BackUpListVm> BackUpListVm { get; }
    public BackUpDetailVm BackUpDetailVm { get; }
    public CreateBackUpCommand CreateBackUpCommand { get; }
    public UpdateBackUpCommand UpdateBackUpCommand { get; }

    public BackUpFixture()
    {
        var fixture = new Fixture();

        // Create sample BackUp list data
        BackUpListVm = new List<BackUpListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-01.company.com",
                DatabaseName = "ContinuityPatrolDB",
                UserName = "backup_admin",
                Password = "encrypted_password_123",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = "C:\\Backups\\Daily",
                BackUpType = "Full",
                CronExpression = "0 0 2 * * *",
                ScheduleType = "Daily",
                ScheduleTime = "02:00:00",
                Properties = "{\"compression\":true,\"encryption\":true,\"retention\":30}",
                KeepBackUpLast = "30",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Backup-Node-01"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-02.company.com",
                DatabaseName = "LogsDB",
                UserName = "log_backup",
                Password = "encrypted_password_456",
                IsLocalServer = false,
                IsBackUpServer = true,
                BackUpPath = "\\\\backup-server\\logs",
                BackUpType = "Incremental",
                CronExpression = "0 0 */6 * * *",
                ScheduleType = "Every 6 Hours",
                ScheduleTime = "06:00:00",
                Properties = "{\"compression\":false,\"encryption\":true,\"retention\":7}",
                KeepBackUpLast = "7",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Backup-Node-02"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                HostName = "db-server-03.company.com",
                DatabaseName = "AnalyticsDB",
                UserName = "analytics_backup",
                Password = "encrypted_password_789",
                IsLocalServer = true,
                IsBackUpServer = true,
                BackUpPath = "D:\\Backups\\Analytics",
                BackUpType = "Differential",
                CronExpression = "0 0 0 * * 0",
                ScheduleType = "Weekly",
                ScheduleTime = "00:00:00",
                Properties = "{\"compression\":true,\"encryption\":false,\"retention\":90}",
                KeepBackUpLast = "90",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "Backup-Node-03"
            }
        };

        // Create detailed BackUp data
        BackUpDetailVm = new BackUpDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            HostName = "enterprise-db.company.com",
            DatabaseName = "EnterpriseDB",
            UserName = "enterprise_backup",
            Password = "enterprise_encrypted_password",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = "\\\\enterprise-backup\\critical",
            BackUpType = "Full",
            CronExpression = "0 0 1 1 * *",
            ScheduleType = "Monthly",
            ScheduleTime = "01:00:00",
            Properties = "{\"compression\":true,\"encryption\":true,\"retention\":365,\"priority\":\"high\"}",
            KeepBackUpLast = "365",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Enterprise-Backup-Node"
        };

        // Create command for creating BackUp
        CreateBackUpCommand = new CreateBackUpCommand
        {
            HostName = "new-db-server.company.com",
            DatabaseName = "NewApplicationDB",
            UserName = "new_backup_user",
            Password = "new_encrypted_password",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = "C:\\Backups\\NewApp",
            BackUpType = "Full",
            CronExpression = "0 0 3 * * *",
            ScheduleType = "Daily",
            ScheduleTime = "03:00:00",
            Properties = "{\"compression\":true,\"encryption\":true,\"retention\":14}",
            KeepBackUpLast = "14",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "New-Backup-Node"
        };

        // Create command for updating BackUp
        UpdateBackUpCommand = new UpdateBackUpCommand
        {
            Id = Guid.NewGuid().ToString(),
            HostName = "updated-db-server.company.com",
            DatabaseName = "UpdatedApplicationDB",
            UserName = "updated_backup_user",
            Password = "updated_encrypted_password",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = "\\\\updated-backup\\app",
            BackUpType = "Incremental",
            CronExpression = "0 0 */12 * * *",
            ScheduleType = "Every 12 Hours",
            ScheduleTime = "12:00:00",
            Properties = "{\"compression\":false,\"encryption\":true,\"retention\":21}",
            KeepBackUpLast = "21",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Updated-Backup-Node"
        };
    }
}
