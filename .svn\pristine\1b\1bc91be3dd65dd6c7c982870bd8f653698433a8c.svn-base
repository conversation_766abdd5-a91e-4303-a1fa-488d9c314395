﻿using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;

namespace ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetDetail;

public class GetIncidentLogsDetailQueryHandler : IRequestHandler<GetIncidentLogsDetailQuery, IncidentLogsDetailVm>
{
    private readonly IIncidentLogsRepository _incidentLogsRepository;
    private readonly IMapper _mapper;

    public GetIncidentLogsDetailQueryHandler(IIncidentLogsRepository incidentLogsRepository, IMapper mapper)
    {
        _mapper = mapper;
        _incidentLogsRepository = incidentLogsRepository;
    }

    public async Task<IncidentLogsDetailVm> Handle(GetIncidentLogsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var incidentLogs = await _incidentLogsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(incidentLogs, nameof(Domain.Entities.IncidentLogs),
            new NotFoundException(nameof(Domain.Entities.IncidentLogs), request.Id));

        return _mapper.Map<IncidentLogsDetailVm>(incidentLogs);
    }
}