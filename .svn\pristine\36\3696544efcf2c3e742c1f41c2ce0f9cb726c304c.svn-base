﻿using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;

namespace ContinuityPatrol.Application.UnitTests.Features.RpoSlaDeviationReport.Queries
{
    public class GetRpoSlaDeviationReportByBusinessServiceIdQueryHandlerTests
    {
        private readonly Mock<IRpoSlaDeviationReportRepository> _mockRpoSlaDeviationReportRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRpoSlaDeviationReportByBusinessServiceIdQueryHandler _handler;

        public GetRpoSlaDeviationReportByBusinessServiceIdQueryHandlerTests()
        {
            _mockRpoSlaDeviationReportRepository = new Mock<IRpoSlaDeviationReportRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetRpoSlaDeviationReportByBusinessServiceIdQueryHandler(_mockRpoSlaDeviationReportRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ReturnsReportList_WhenReportsExist()
        {
            var businessServiceId = Guid.NewGuid().ToString();
            var query = new GetRpoSlaDeviationReportByBusinessServiceIdQuery
            {
                BusinessServiceId = businessServiceId
            };

            var reportEntities = new List<Domain.Entities.RpoSlaDeviationReport>
            {
                new Domain.Entities.RpoSlaDeviationReport
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectName = "InfraObject1"
                },
                new Domain.Entities.RpoSlaDeviationReport
                {
                    ReferenceId = Guid.NewGuid().ToString(),
                    InfraObjectName = "InfraObject2"
                }
            };

            var reportDtos = new List<RpoSlaDeviationReportListVm>
            {
                new RpoSlaDeviationReportListVm
                {
                    Id = reportEntities[0].ReferenceId,
                    InfraObjectName = reportEntities[0].InfraObjectName
                },
                new RpoSlaDeviationReportListVm
                {
                    Id = reportEntities[1].ReferenceId,
                    InfraObjectName = reportEntities[1].InfraObjectName
                }
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId))
                .ReturnsAsync(reportEntities);

            _mockMapper
                .Setup(mapper => mapper.Map<List<RpoSlaDeviationReportListVm>>(reportEntities))
                .Returns(reportDtos);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("InfraObject1", result[0].InfraObjectName);
            Assert.Equal("InfraObject2", result[1].InfraObjectName);

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RpoSlaDeviationReportListVm>>(reportEntities), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoReportsExist()
        {
            var businessServiceId = Guid.NewGuid().ToString();
            var query = new GetRpoSlaDeviationReportByBusinessServiceIdQuery
            {
                BusinessServiceId = businessServiceId
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId))
                .ReturnsAsync(new List<Domain.Entities.RpoSlaDeviationReport>());

            _mockMapper
                .Setup(mapper => mapper.Map<List<RpoSlaDeviationReportListVm>>(It.IsAny<List<Domain.Entities.RpoSlaDeviationReport>>()))
                .Returns(new List<RpoSlaDeviationReportListVm>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RpoSlaDeviationReportListVm>>(It.IsAny<List<Domain.Entities.RpoSlaDeviationReport>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryFails()
        {
            var businessServiceId = Guid.NewGuid().ToString();
            var query = new GetRpoSlaDeviationReportByBusinessServiceIdQuery
            {
                BusinessServiceId = businessServiceId
            };

            _mockRpoSlaDeviationReportRepository
                .Setup(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
            Assert.Equal("Database error", exception.Message);

            _mockRpoSlaDeviationReportRepository.Verify(repo => repo.GetRpoSlaDeviationReportListByBusinessServiceId(businessServiceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<RpoSlaDeviationReportListVm>>(It.IsAny<List<Domain.Entities.RpoSlaDeviationReport>>()), Times.Never);
        }
    }
}
