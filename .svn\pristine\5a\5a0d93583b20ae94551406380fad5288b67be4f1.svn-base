using ContinuityPatrol.Application.Features.DataLag.Events.Create;

namespace ContinuityPatrol.Application.Features.DataLag.Commands.Create;

public class CreateDataLagCommandHandler : IRequestHandler<CreateDataLagCommand, CreateDataLagResponse>
{
    private readonly IDataLagRepository _dataLagRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateDataLagCommandHandler(IMapper mapper, IDataLagRepository dataLagRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _dataLagRepository = dataLagRepository;
    }

    public async Task<CreateDataLagResponse> Handle(CreateDataLagCommand request, CancellationToken cancellationToken)
    {
        var dataLag = _mapper.Map<Domain.Entities.DataLag>(request);

        dataLag = await _dataLagRepository.AddAsync(dataLag);

        var response = new CreateDataLagResponse
        {
            Message = Message.Create(nameof(Domain.Entities.DataLag), dataLag.BusinessServiceName),

            Id = dataLag.ReferenceId
        };

        await _publisher.Publish(new DataLagCreatedEvent { Name = dataLag.BusinessServiceName }, cancellationToken);

        return response;
    }
}