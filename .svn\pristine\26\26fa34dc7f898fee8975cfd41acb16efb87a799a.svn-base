﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class PageSolutionMappingService : BaseClient, IPageSolutionMappingService
{
    public PageSolutionMappingService(IConfiguration config, IAppCache cache, ILogger<PageBuilderService> logger) : base(config, cache, logger) { }

    public async Task<List<PageSolutionMappingListVm>> GetPageSolutionMappingList()
    {
        var request = new RestRequest("api/v6/pagesolutionmapping");

        return await GetFromCache<List<PageSolutionMappingListVm>>(request, "GetPageSolutionMapping");
    }

    public async Task<PageSolutionMappingDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/pagesolutionmapping/id?id={id}");

        return await Get<PageSolutionMappingDetailVm>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreatePageSolutionMappingCommand createPageSolutionMappingCommand)
    {
        var request = new RestRequest("api/v6/pagesolutionmapping", Method.Post);

        request.AddJsonBody(createPageSolutionMappingCommand);

        return await Post<BaseResponse>(request);
    }
    public async Task<BaseResponse> UpdateAsync(UpdatePageSolutionMappingCommand updatePageSolutionMappingCommand)
    {
        var request = new RestRequest("api/v6/pagesolutionmapping", Method.Put);

        request.AddJsonBody(updatePageSolutionMappingCommand);

        return await Put<BaseResponse>(request);
    }
    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/pagesolutionmapping/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }
    public async Task<bool> IsPageSolutionMappingNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/pagesolutionmapping/name-exist?pageSolutionName={name}&id={id}");

        return await Get<bool>(request);

    }
    public async Task<PaginatedResult<PageSolutionMappingListVm>> GetPaginatedPageSolutionMapping(GetPageSolutionMappingPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/pagesolutionmapping/paginated-list");

        return await Get<PaginatedResult<PageSolutionMappingListVm>>(request);
    }
}
