using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SettingFixture : IDisposable
{
    public List<Setting> SettingPaginationList { get; set; }
    public List<Setting> SettingList { get; set; }
    public Setting SettingDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SettingFixture()
    {
        var fixture = new Fixture();

        SettingList = fixture.Create<List<Setting>>();

        SettingPaginationList = fixture.CreateMany<Setting>(20).ToList();

        SettingDto = fixture.Create<Setting>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
