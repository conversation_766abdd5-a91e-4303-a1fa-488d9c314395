﻿using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Domain.ViewModels.WorkflowModel;

public class WorkflowViewModel
{
    public List<GetInfraObjectNameVm> GetInfraObjectNameVms;

    public List<UserNameVm> UserListVms;


    public string Id { get; set; }
    public string Name { get; set; }
    public string Properties { get; set; }
    public bool IsVerify { get; set; }
    public bool IsLock { get; set; }
    public bool IsPublish { get; set; }
    public string Version { get; set; }

    //public string IsfourEye { get; set; }
    public PaginatedResult<WorkflowListVm> PaginatedWorkFlow { get; set; }
    public List<WorkflowCategoryListVm> WorkflowCategoryListVms { get; set; }
    public List<WorkflowCategoryViewListVm>workflowCategoryViewListVms { get; set; }
    public List<WorkflowInfraObjectListVm> WorkflowInfraObjectListVms { get; set; }

    public List<ReplicationMasterListVm> ReplicationModelName { get; set; }

    [ValidateNever] public IEnumerable<SelectListItem> UserRoles { get; set; }

    [ValidateNever] public IEnumerable<SelectListItem> WorkflowActionTypeListVms { get; set; }
}