using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaImpactCategoryFixture : IDisposable
{
    public List<FiaImpactCategory> FiaImpactCategoryPaginationList { get; set; }
    public List<FiaImpactCategory> FiaImpactCategoryList { get; set; }
    public FiaImpactCategory FiaImpactCategoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FiaImpactCategoryFixture()
    {
        var fixture = new Fixture();

        FiaImpactCategoryList = fixture.Create<List<FiaImpactCategory>>();

        FiaImpactCategoryPaginationList = fixture.CreateMany<FiaImpactCategory>(20).ToList();

        FiaImpactCategoryDto = fixture.Create<FiaImpactCategory>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
