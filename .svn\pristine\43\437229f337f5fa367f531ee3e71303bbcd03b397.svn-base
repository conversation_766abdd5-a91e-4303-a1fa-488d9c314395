﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetByWorkflowOperationIdQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly GetByWorkflowOperationIdQueryHandler _handler;

    public GetByWorkflowOperationIdQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetByWorkflowOperationIdRepository(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _handler = new GetByWorkflowOperationIdQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnWorkflowOperationId_When_ValidWorkflowOperationId()
    {
        var result = await _handler.Handle(new GetByWorkflowOperationIdQuery { WorkflowOperationId = _workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId }, CancellationToken.None);

        result.ShouldBeOfType<List<GetByWorkflowOperationIdVm>>();

        result[0].Id.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);
        result[0].InfraObjectId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectName);
        result[0].WorkflowId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowName);
        result[0].CurrentActionId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionId);
        result[0].CurrentActionName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionName);
        result[0].Status.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Status);
        result[0].Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Message);
        result[0].WorkflowOperationId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId);
        result[0].ProgressStatus.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ProgressStatus);
        result[0].JobName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].JobName);
        result[0].WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowVersion);

    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetByWorkflowOperationIdQuery(), CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Once);
     }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowOperationId()
    {
        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupEmptyRepository();

        var handler = new GetByWorkflowOperationIdQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetByWorkflowOperationIdQuery { WorkflowOperationId = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        var handler = new GetByWorkflowOperationIdQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);

        var result = await handler.Handle(new GetByWorkflowOperationIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}
