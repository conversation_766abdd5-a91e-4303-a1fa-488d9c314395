using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DatabaseFixture : IDisposable
{
    public List<Database> DatabasePaginationList { get; set; }
    public List<Database> DatabaseList { get; set; }
    public Database DatabaseDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DatabaseFixture()
    {
        var fixture = new Fixture();

        DatabaseList = fixture.Create<List<Database>>();

        DatabasePaginationList = fixture.CreateMany<Database>(20).ToList();

        DatabasePaginationList.ForEach(x => x.CompanyId = CompanyId);

        DatabaseList.ForEach(x => x.CompanyId = CompanyId);

        DatabaseDto = fixture.Create<Database>();

        DatabaseDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
