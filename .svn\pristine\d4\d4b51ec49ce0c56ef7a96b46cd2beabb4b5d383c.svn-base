﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BusinessServiceRepositoryMocks
{
    public static Mock<IBusinessServiceRepository> CreateBusinessServiceRepository(List<BusinessService> businessServices)
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        businessServiceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServices);

        businessServiceRepository.Setup(repo => repo.AddAsync(It.IsAny<BusinessService>())).ReturnsAsync(
            (BusinessService businessService) =>
            {
                businessService.Id = new Fixture().Create<int>();

                businessService.ReferenceId = new Fixture().Create<Guid>().ToString();

                businessServices.Add(businessService);

                return businessService;
            });

        return businessServiceRepository;
    }

    public static Mock<IBusinessServiceRepository> UpdateBusinessServiceRepository(List<BusinessService> businessServices)
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        businessServiceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServices);

        businessServiceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServices.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessService>())).ReturnsAsync((BusinessService businessService) =>
        {
            var index = businessServices.FindIndex(item => item.ReferenceId == businessService.ReferenceId);

            businessServices[index] = businessService;

            return businessService;
        });


        return businessServiceRepository;
    }

    public static Mock<IBusinessServiceRepository> DeleteBusinessServiceRepository(List<BusinessService> businessServices)
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        businessServiceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServices);

        businessServiceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServices.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessService>())).ReturnsAsync((BusinessService businessService) =>
        {
            var index = businessServices.FindIndex(item => item.ReferenceId == businessService.ReferenceId);

            businessService.IsActive = false;

            businessServices[index] = businessService;

            return businessService;
        });

        return businessServiceRepository;
    }

    public static Mock<IBusinessServiceRepository> GetBusinessServiceRepository(List<BusinessService> businessServices)
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        businessServiceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServices);

        businessServiceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServices.SingleOrDefault(x => x.ReferenceId == i));

        return businessServiceRepository;
    }

    public static Mock<IBusinessServiceRepository> GetBusinessServiceNamesRepository(List<BusinessService> businessService)
    {
        var businessServiceNamesRepository = new Mock<IBusinessServiceRepository>();

        businessServiceNamesRepository.Setup(repo => repo.GetBusinessServiceNames()).ReturnsAsync(businessService);

        return businessServiceNamesRepository;
    }

    public static Mock<IBusinessServiceRepository> GetBusinessServiceNameUniqueRepository(List<BusinessService> businessServices)
    {
        var businessServiceNameUniqueRepository = new Mock<IBusinessServiceRepository>();

        businessServiceNameUniqueRepository.Setup(repo => repo.IsBusinessServiceNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => businessServices.Exists(x => x.Name == i && x.ReferenceId == j));

        return businessServiceNameUniqueRepository;
    }

    public static Mock<IBusinessServiceRepository> GetBusinessServiceEmptyRepository()
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        businessServiceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<BusinessService>());

        return businessServiceRepository;
    }

    public static Mock<IBusinessServiceRepository> GetPaginatedBusinessServiceRepository(List<BusinessService> businessServices)
    {
        var businessServiceRepository = new Mock<IBusinessServiceRepository>();

        var queryableBusinessServices = businessServices.BuildMock();

        businessServiceRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableBusinessServices);

        return businessServiceRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateBusinessServiceEventRepository(List<UserActivity> userActivities)
    {
        var businessServiceEventRepository = new Mock<IUserActivityRepository>();

        businessServiceEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        businessServiceEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return businessServiceEventRepository;
    }
}