﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndActionTypeUnique;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndType;
using ContinuityPatrol.Application.Features.Template.Queries.GetList;
using ContinuityPatrol.Application.Features.Template.Queries.GetTemplateByInfraObjectId;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetWorkflowActionByNodeId;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionTypeModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.ITAutomation.Controllers;

public class WorkflowConfigurationControllerTests
{
    private WorkflowConfigurationController _controller;
    private readonly Mock<ILogger<WorkflowConfigurationController>> _mockLogger = new();
    private readonly Mock<IWorkflowCategoryService> _mockWorkflowCategoryService = new();
    private readonly Mock<IWorkflowActionTypeService> _mockWorkflowActionTypeService = new();
    private readonly Mock<IInfraObjectService> _mockInfraObjectService = new();
    private readonly Mock<IUserRoleService> _mockUserRoleService = new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<IWorkflowInfraObjectService> _mockWorkflowInfraObjectService = new();
    private readonly Mock<ITemplateService> _mockTemplateService = new();
    private readonly Mock<IWorkflowActionService> _mockWorkflowActionService = new();
    private readonly Mock<IWorkflowService> _mockWorkflowService = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IWorkflowProfileInfoService> _mockWorkflowProfileInfoService = new();
    private readonly Mock<IWorkflowPredictionService> _mockWorkflowPriService = new();
    private readonly Mock<IPublisher> _mockPublisher = new();

    public WorkflowConfigurationControllerTests()
    {
        Initialize();
    }
    public void Initialize()
    {
        _controller = new WorkflowConfigurationController(
            _mockLogger.Object,
            _mockDataProvider.Object,
			Mock.Of<IMapper>(),
            _mockPublisher.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        // _controller = new WorkflowConfigurationController(
        //    _mockLogger.Object,
        //    Mock.Of<IWorkflowCategoryService>(),
        //    Mock.Of<IWorkflowService>(),
        //    Mock.Of<IWorkflowActionTypeService>(),
        //    Mock.Of<IWorkflowActionService>(),
        //    Mock.Of<IMapper>(),
        //    Mock.Of<IWorkflowPredictionService>(),
        //    _mockDataProvider.Object,
        //    Mock.Of<ITemplateService>(),
        //    Mock.Of<IUserRoleService>(),
        //    Mock.Of<IWorkflowOperationGroupService>(),
        //    Mock.Of<IWorkflowExecutionTempService>(),
        //    _mockInfraObjectService.Object,
        //    Mock.Of<IInfraObjectRepository>(),
        //    _mockWorkflowInfraObjectService.Object,
        //    _mockWorkflowProfileInfoService.Object

        //);
    }

    [Fact]
    public async Task List_ReturnsViewResultWithWorkflowViewModel()
    {
        var workflowCategories = new List<WorkflowCategoryViewListVm>
        {
            new WorkflowCategoryViewListVm { CategoryName = "Category1" },
            new WorkflowCategoryViewListVm { CategoryName = "Category2" }
        };

        var workflowActions = new List<WorkflowActionTypeListVm>
        {
            new WorkflowActionTypeListVm { Id = "1", ActionType = "Action1" },
            new WorkflowActionTypeListVm { Id = "2", ActionType = "Action2" }
        };

        var infraObjectNames = new List<GetInfraObjectNameVm>
        {
            new GetInfraObjectNameVm { Name = "InfraObject1" },
            new GetInfraObjectNameVm { Name = "InfraObject2" }
        };

        var userRoleList = new List<UserRoleListVm>
		{
            new UserRoleListVm{ 
                Id="1",
                IsDelete=true,
                Logo="PTE",
                Role="Admin"
            }
        };

        var userNames = new List<UserNameVm> {
            new UserNameVm
            {
                Id = "1",
                LoginName="user",
                Role="Admin",
                RoleName="Tester"
            }
        };

		_mockDataProvider.Setup(service => service.WorkflowCategory.GetWorkflowCategoryViewList())
            .ReturnsAsync(workflowCategories);
		_mockDataProvider.Setup(service => service.WorkflowActionTypes.GetWorkflowActionList())
            .ReturnsAsync(workflowActions);
		_mockDataProvider.Setup(service => service.InfraObject.GetInfraObjectNames())
            .ReturnsAsync(infraObjectNames);
        _mockDataProvider.Setup(service => service.UserRole.GetUserRoles()).ReturnsAsync(userRoleList);
        _mockDataProvider.Setup(dp => dp.User.GetUserNames()).ReturnsAsync(userNames);

        var result = await _controller.List() as ViewResult;
        var model = result.Model as WorkflowViewModel;

        Assert.NotNull(result);
        Assert.NotNull(model);
        Assert.Equal(2, model.workflowCategoryViewListVms.Count);
        Assert.Equal(2, model.GetInfraObjectNameVms.Count);
            
    }

    [Fact]
    public async Task GetInfraObjectList_ReturnsJsonResultWithInfraObjectNames()
    {
        var infraObjectNames = new List<GetInfraObjectNameVm>
        {
            new GetInfraObjectNameVm { Name = "InfraObject1" },
            new GetInfraObjectNameVm { Name = "InfraObject2" }
        };

		_mockDataProvider.Setup(service => service.InfraObject.GetInfraObjectNames())
            .ReturnsAsync(infraObjectNames);

        var result = await _controller.GetInfraObjectList() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetInfraObjectList_ThrowsException_ReturnsJsonWithError()
    {
        _mockInfraObjectService.Setup(service => service.GetInfraObjectNames())
            .ThrowsAsync(new Exception("Test Exception"));

        var result = await _controller.GetInfraObjectList() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
        //_mockLogger.Verify(l => l.LogError(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task GetInfraObjectListByReplication_ValidReplicationTypeId_ReturnsJsonResult()
    {
        var replicationTypeId = "valid-id";
        var infraObjectList = new List<InfraObjectListByReplicationTypeVm>
        {
            new InfraObjectListByReplicationTypeVm { Name = "Object1" },
            new InfraObjectListByReplicationTypeVm { Name = "Object2" }
        };

		_mockDataProvider.Setup(repo => repo.InfraObject.GetInfraObjectListByReplicationTypeId(replicationTypeId))
            .ReturnsAsync(infraObjectList);

        var result = await _controller.GetInfraObjectListByReplicationTypeId(replicationTypeId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetInfraObjectListByReplication_InvalidReplicationTypeId_ReturnsJsonWithError()
    {
        var invalidReplicationTypeId = "";

        var result = await _controller.GetInfraObjectListByReplicationTypeId(invalidReplicationTypeId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }
    [Fact]
    public async Task GetWorkFlowList_ReturnsJsonResultWithWorkFlowDetails()
    {
        var workflowDetails = new List<WorkflowNameVm> { };
        _mockDataProvider.Setup(dp => dp.Workflow.GetWorkflowNames())
            .ReturnsAsync(workflowDetails);

        var result = await _controller.GetWorkflowList() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetWorkFlowActionsById_ValidId_ReturnsJsonResultWithActions()
    {
        var workflowActions = new WorkflowActionDetailVm() { };
        string workflowId = "validId";
        _mockDataProvider.Setup(dp => dp.WorkflowAction.GetByReferenceId(workflowId))
            .ReturnsAsync(workflowActions);

        var result = await _controller.GetWorkFlowActionsById(workflowId) as JsonResult;

        Assert.NotNull(result);
        dynamic jsonData = result.Value;
        Assert.Equal(workflowActions, jsonData);
    }

    [Fact]
    public async Task GetWorkFlowActionsById_InvalidId_ReturnsEmptyJsonResult()
    {
        var result = await _controller.GetWorkFlowActionsById("") as JsonResult;

        Assert.NotNull(result);
        Assert.Equal("", result.Value);
    }

    [Fact]
    public async Task GetInfraList_ReturnsInfraObjectList()
    {
        var infraList = new List<InfraObjectListVm>
        {
            new InfraObjectListVm { Id = "1", Name = "Infra1" },
            new InfraObjectListVm { Id = "2", Name = "Infra2" }
        };
		_mockDataProvider.Setup(service => service.InfraObject.GetInfraObjectList())
            .ReturnsAsync(infraList);

        var result = await _controller.GetInfraList();

        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public void WorkFlowDataEncrypt_ValidData_ReturnsEncryptedData()
    {
        string data = "sampleData";
        string encryptedData = "encryptedData";
        //SecurityHelper.Encrypt  = (string d) => encryptedData;

        var result = _controller.WorkFlowDataEncrypt(data) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public void WorkFlowDataDecrypt_ValidData_ReturnsDecryptedData()
    {
        string data = "encryptedData";
        string decryptedData = "decryptedData";
        //SecurityHelper.Decrypt = (string d) => decryptedData;

        var result = _controller.WorkFlowDataDecrypt(data) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task CheckInfraobjectAttach_ValidId_Returnsfalse()
    {
        string workflowId = "validWorkflowId";
        _mockWorkflowInfraObjectService.Setup(service => service.WorkflowInfraObjectByWorkflowIdExist(workflowId))
            .ReturnsAsync(true);

        var result = await _controller.CheckInfraObjectAttachByWorkflowId(workflowId);

        Assert.False(result);
    }

    [Fact]
    public async Task CheckInfraobjectAttach_InvalidId_ReturnsFalse()
    {
        var result = await _controller.CheckInfraObjectAttachByWorkflowId("");

        Assert.False(result);
    }

    [Fact]
    public async Task CheckProfileAttach_ValidId_ReturnsJsonResultWithProfileAttach()
    {
        string workflowId = "validWorkflowId";
        var profileAttached = new GetWorkflowProfileInfoByWorkflowIdVm();
		_mockDataProvider.Setup(service => service.WorkflowProfileInfo.WorkflowProfileInfoByWorkflowIdExist(workflowId))
            .ReturnsAsync(profileAttached);

        var result = await _controller.CheckProfileAttachedByWorkflowId(workflowId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task CheckProfileAttach_InvalidId_ReturnsJsonWithError()
    {
        var result = await _controller.CheckProfileAttachedByWorkflowId("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetDatabaseListByName_ReturnsDatabaseList()
    {
        var databaseList = new List<ComponentTypeModel> { };
        _mockDataProvider.Setup(dp => dp.ComponentType.GetComponentTypeListByName("Database"))
            .ReturnsAsync(databaseList);

        var result = await _controller.GetComponentTypeByDatabaseList() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetDatabaseByServerId_ValidId_ReturnsDatabaseList()
    {
        string serverId = "validServerId";
        var databaseList = new List<GetDatabaseByServerIdVm> { };
        _mockDataProvider.Setup(dp => dp.Database.GetByServerId(serverId))
            .ReturnsAsync(databaseList);

        var result = await _controller.GetDatabaseByServerId(serverId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetDatabaseByServerId_InvalidId_ReturnsJsonWithError()
    {
        var result = await _controller.GetDatabaseByServerId("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetInfraDetails_ValidId_ReturnsJsonResultWithInfraDetails()
    {
        string workflowId = "validId";
        var infraDetails = new List<GetInfraObjectByWorkflowIdVm>() { };
		_mockDataProvider.Setup(service => service.WorkflowInfraObject.GetInfraObjectByWorkflowId(workflowId))
            .ReturnsAsync(infraDetails);

        var result = await _controller.GetWorkflowInfraObjectByWorkflowId(workflowId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetInfraDetails_InvalidId_ReturnsJsonWithError()
    {
        var result = await _controller.GetWorkflowInfraObjectByWorkflowId("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetTemplateList_ReturnsJsonResultWithTemplateDetails()
    {
        var templateDetails = new List<GetTemplateListVm> { };
		_mockDataProvider.Setup(service => service.Template.GetTemplateList())
            .ReturnsAsync(templateDetails);

        var result = await _controller.GetTemplateList() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetWorkflowAction_ValidNodeId_ReturnsJsonResultWithAction()
    {
        string nodeId = "validNodeId";
        var actionDetails = new List<GetWorkflowActionByNodeIdVm>() { };
		_mockDataProvider.Setup(service => service.WorkflowAction.GetWorkflowActionByNodeId(nodeId))
            .ReturnsAsync(actionDetails);

        var result = await _controller.GetWorkflowActionByNodeId(nodeId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetWorkflowAction_InvalidNodeId_ReturnsJsonWithError()
    {
        var result = await _controller.GetWorkflowActionByNodeId("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsJsonResultWithReplicationNames()
    {
        string databaseId = "validDatabaseId";
        string replicationMasterId = "validReplicationMasterId";
        string type = "ReplicationType";
        var replicationNames = new List<InfraReplicationMappingListVm> { };
        _mockDataProvider.Setup(service => service.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
            .ReturnsAsync(replicationNames);

        var result = await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_ValidActivityType_ReturnsJsonResultWithReplicationMasters()
    {
        string activityType = "validActivityType";
        var replicationMasters = new List<GetByInfraMasterNameVm> { };
        _mockDataProvider.Setup(service => service.ReplicationMaster.GetReplicationMasterByInfraMasterName(activityType))
            .ReturnsAsync(replicationMasters);

        var result = await _controller.GetReplicationMasterByInfraMasterName(activityType) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_InvalidActivityType_ReturnsJsonWithError()
    {
        var result = await _controller.GetReplicationMasterByInfraMasterName("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetReplicationNames_ReturnsJsonResultWithReplicationNames()
    {
        var replicationNames = new List<ReplicationListVm> { };
        _mockDataProvider.Setup(service => service.Replication.GetReplicationList())
            .ReturnsAsync(replicationNames);

        var result = await _controller.GetReplicationNames() as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetWorkflowById_ValidId_ReturnsJsonResultWithWorkflow()
    {
        string workflowId = "validId";
        var workflowDetails = new WorkflowDetailVm() { Name = "Workflow1" };
		_mockDataProvider.Setup(service => service.Workflow.GetByReferenceId(workflowId))
            .ReturnsAsync(workflowDetails);

        var result = await _controller.GetWorkflowById(workflowId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetWorkflowById_InvalidId_ReturnsJsonWithError()
    {
        var result = await _controller.GetWorkflowById("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task CreateOrUpdate_CreateWorkflow_ReturnsJsonResultWithSuccess()
    {
        var createWorkflowVm = new AutoFixture.Fixture().Create<WorkflowViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("Id","");
        var collection = new FormCollection(dic);
        var createCommand = new CreateWorkflowCommand ();
        var createResult = new CreateWorkflowResponse() { };
        _mockMapper.Setup(mapper => mapper.Map<CreateWorkflowCommand>(createWorkflowVm))
            .Returns(createCommand);
		_mockDataProvider.Setup(service => service.Workflow.CreateAsync(createCommand))
            .ReturnsAsync(createResult);

        _controller.ControllerContext = new ControllerContextMocks().Default();
        var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
        WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
        _controller.Request.Form = collection;
        var result = await _controller.CreateOrUpdate(createWorkflowVm) as JsonResult;


        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }
    [Fact]
    public async Task GetInfraObjectById_ValidId_ReturnsJsonResultWithInfraDetails()
    {
        string infraObjectId = "validId";
        var infraObjectDetails = new InfraObjectDetailVm { Name = "Infra1" };
		_mockDataProvider.Setup(service => service.InfraObject.GetInfraObjectById(infraObjectId))
            .ReturnsAsync(infraObjectDetails);

        var result = await _controller.GetInfraObjectById(infraObjectId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task GetInfraObjectById_InvalidId_ReturnsJsonWithError()
    {
        var result = await _controller.GetInfraObjectById("") as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetWorkflowPrediction_ValidId_ReturnsJsonResultWithPredictionData()
    {
        string actionId = "validActionId";
        string previousId = "validPreviousId";
        var predictionData = new WorkflowPredictionResult { };
        _mockWorkflowPriService.Setup(service => service.GetWorkflowPredictionListByActionId(actionId, previousId))
            .ReturnsAsync(predictionData);

        var result = await _controller.GetWorkflowPrediction(actionId, previousId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetWorkflowPrediction_ThrowsException_ReturnsJsonWithError()
    {
        string actionId = "validActionId";
		_mockDataProvider.Setup(service => service.WorkflowPredictionServices.GetWorkflowPredictionListByActionId(actionId, null))
            .ThrowsAsync(new Exception("Test exception"));

        var result = await _controller.GetWorkflowPrediction(actionId, null) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task Delete_ValidWorkflowId_RedirectsToList()
    {
        string workflowId = "validId";
        var deleteResult = new BaseResponse { Message = "Deleted successfully" };
        _mockDataProvider.Setup(provider => provider.Workflow.DeleteAsync(workflowId))
            .ReturnsAsync(deleteResult);

        var result = await _controller.Delete(workflowId) as RedirectToActionResult;

        Assert.NotNull(result);
        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task AttachInfraObject_ValidRequest_ReturnsJsonResultWithSuccess()
    {
        var attachWorkflow = new CreateWorkflowInfraObjectCommand { CompanyId = "Company1" };
        var resultData = new BaseResponse { Success = true };
		_mockDataProvider.Setup(service => service.WorkflowInfraObject.CreateAsync(attachWorkflow))
            .ReturnsAsync(resultData);

        var result = await _controller.AttachInfraObject(attachWorkflow) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task DetachInfraObject_ValidRequest_ReturnsJsonResultWithSuccess()
    {
        var detachWorkflow = new DeleteWorkflowInfraObjectCommand { InfraObjectId = "Infra1" };
        var resultData = new BaseResponse { Success = true };
		_mockDataProvider.Setup(service => service.WorkflowInfraObject.DeleteAsync(detachWorkflow))
            .ReturnsAsync(resultData);

        var result = await _controller.DetachInfraObject(detachWorkflow) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task CreateTemplate_ValidCreateRequest_ReturnsJsonResultWithSuccess()
    {
        var createTemplateVm = new AutoFixture.Fixture().Create<TemplateViewModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("Id", "");
        var collection = new FormCollection(dic);
        var createCommand = new CreateTemplateCommand { Name = "Template1" };
        var resultData = new BaseResponse { Success = true };
        _mockMapper.Setup(mapper => mapper.Map<CreateTemplateCommand>(createTemplateVm))
            .Returns(createCommand);
        _mockDataProvider.Setup(service => service.Template.CreateAsync(createCommand))
            .ReturnsAsync(resultData);

        _controller.ControllerContext = new ControllerContextMocks().Default();
        var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
        WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
        _controller.Request.Form = collection;
        var result = await _controller.CreateTemplate(createTemplateVm) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task WorkflowSaveAs_ValidCommand_ReturnsJsonResultWithSuccess()
    {
        var saveAsWorkflowCommand = new SaveAsWorkflowCommand { };
        var resultData = new SaveAsWorkflowResponse { Success = true };
		_mockDataProvider.Setup(service => service.Workflow.SaveAsWorkflow(saveAsWorkflowCommand))
            .ReturnsAsync(resultData);

        var result = await _controller.WorkflowSaveAs(saveAsWorkflowCommand) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task WorkFlowNameExist_ValidName_ReturnsTrue()
    {
        string workflowName = "TestWorkflow";
        string id = "validId";
        _mockDataProvider.Setup(provider => provider.Workflow.IsWorkflowNameExist(workflowName, id))
            .ReturnsAsync(true);

        var result = await _controller.WorkFlowNameExist(workflowName, id);

        Assert.True(result);
    }
    [Fact]
    public async Task GetTemplateByTypes_ValidParams_ReturnsJsonResultWithSuccess()
    {
        string replicationTypeId = "type1";
        string actionType = "action1";
        string entityType = "entity1";
        string type = "typeA";
        string templateType = "templateA";

        var resultData = new List<GetByReplicationTypeIdAndTypeVm>() { };
        _mockDataProvider.Setup(provider => provider.Template.GetTemplateByTypes(replicationTypeId, actionType, entityType, type, templateType))
            .ReturnsAsync(resultData);

        var result = await _controller.GetTemplateByTypes(replicationTypeId, actionType, entityType, type, templateType) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task TemplateNameExist_ValidName_ReturnsTrue()
    {
        string templateName = "Template1";
        string id = "123";
        _mockDataProvider.Setup(provider => provider.Template.IsTemplateNameExist(templateName, id))
            .ReturnsAsync(true);

        var result = await _controller.TemplateNameExist(templateName, id);

        Assert.True(result);
    }

    //[Fact]
    //public async Task GetVersionData_ValidWorkflowId_ReturnsJsonResultWithSuccess()
    //{
    //    string workflowId = "workflow1";
    //    var versionData = new List<WorkflowHistoryByWorkflowIdVm> { };
    //    _mockDataProvider.Setup(provider => provider.WorkflowHistory.GetWorkflowHistoryByWorkflowId(workflowId))
    //        .ReturnsAsync(versionData);

    //    var result = await _controller.GetVersionData(workflowId) as JsonResult;

    //    Assert.NotNull(result);
    //    var json = JsonConvert.SerializeObject(result.Value);
    //    Assert.Contains("\"Success\":true", json);
    //}

    [Fact]
    public async Task GetTemplateDetails_ValidInfraObjectId_ReturnsJsonResultWithSuccess()
    {
        string infraObjectId = "infra1";
        var templateDetails = new List<GetTemplateByInfraObjectIdVm> { };
        _mockDataProvider.Setup(provider => provider.Template.GetTemplateByInfraObjectId(infraObjectId))
            .ReturnsAsync(templateDetails);

        var result = await _controller.GetTemplateByInfraObjectId(infraObjectId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public async Task TemplateValidation_ValidParams_ReturnsJsonResultWithSuccess()
    {
        string actionType = "action1";
        string replicationTypeId = "replication1";
        var validationMessage = new TemplateByReplicationTypeIdAndActionTypeUniqueQueryVm();
        _mockDataProvider.Setup(provider => provider.Template.IsTemplateByReplicationTypeIdAndActionTypeNameUnique(actionType, replicationTypeId))
            .ReturnsAsync(validationMessage);

        var result = await _controller.TemplateValidation(actionType, replicationTypeId) as JsonResult;

        Assert.NotNull(result);
        var json = JsonConvert.SerializeObject(result.Value);
        Assert.Contains("\"Success\":true", json);
    }

    [Fact]
    public void GetRunBookReport_ValidWorkflowId_ReturnsFileResult()
    {
        string workflowId = "workflow1";
        var runBookData = new GetRunBookReportVm { WorkflowName = "RunBook1" };
        _mockDataProvider.Setup(provider => provider.Report.GetRunbookReportByWorkflowId(workflowId))
            .ReturnsAsync(runBookData);
        _mockDataProvider.Setup(provider => provider.Company.GetCompanyById(It.IsAny<string>()))
            .ReturnsAsync(new CompanyDetailVm { CompanyLogo = "logo.png" });

        var result =  _controller.GetRunBookReport(workflowId) ;

        Assert.NotNull(result);
            
    }

    [Fact]
    public async Task GetRunBookReport_ExceptionOccurs_ReturnsContentResultWithError()
    {
        string workflowId = "workflow1";
        _mockDataProvider.Setup(provider => provider.Report.GetRunbookReportByWorkflowId(workflowId))
            .ThrowsAsync(new Exception("Test Exception"));
		_controller.ControllerContext = new ControllerContextMocks().Default();
		
        var com = new UserSession {CompanyId=null};
		WebHelper.CurrentSession.Set("SESSION", com);
        var compamydetailvm = new CompanyDetailVm { 
           CompanyLogo=null,
           
        };
        var workflow = new GetRunBookReportVm { 
        
            WorkflowName="Test"
        };

		_mockDataProvider.Setup(x => x.Company.GetCompanyById(com.CompanyId)).ReturnsAsync(compamydetailvm);
		_mockDataProvider.Setup(x => x.Report.GetRunbookReportByWorkflowId(workflowId)).ReturnsAsync(workflow);

		RunBook.ReportGeneratedName = "Test";
		var result = await _controller.GetRunBookReport(workflowId) as ContentResult;

        Assert.Null(result);
        
    }
}