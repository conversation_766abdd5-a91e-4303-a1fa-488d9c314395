﻿using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Web.Areas.Alert.Controllers;
using Newtonsoft.Json;


namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class ManageAlerts : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<ManageAlertController> _logger;
        public List<AlertMasterListVm> ManageAlertReport = new List<AlertMasterListVm>();
        private string _reportGenerateName;
        public ManageAlerts(string data, string reportGeneratedName)
        {
            try
            {
                _logger = ManageAlertController._logger;
                _reportGenerateName = reportGeneratedName;
                var jsonValue = JsonConvert.DeserializeObject<PaginatedResult<AlertMasterListVm>>(data);
                ManageAlertReport.AddRange(jsonValue.Data);
                InitializeComponent();
                this.DataSource = ManageAlertReport;
                srNumber.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex) { _logger.LogError("Error occurred while display the Manage Alert Report. The error message : " + ex.Message); throw; }
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + _reportGenerateName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occurred while display the Manage Alert Report's User name. The error message : " + ex.Message); throw; }
        }
        private int serialNumber = 1;
        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;
            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        public void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occurred while display the Manage Alerts Report's CP Version. The error message : " + ex.Message); throw; }
        }
    }
}