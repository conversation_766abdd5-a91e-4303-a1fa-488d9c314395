﻿//using ContinuityPatrol.Infrastructure.Contract;
//using ValidationException = ContinuityPatrol.Shared.Core.Exceptions.ValidationException;

//namespace ContinuityPatrol.Application.Features.InfraObject.Commands.CreateBulkData;

//public class CreateBulkDataCommandHandler : IRequestHandler<CreateBulkDataCommand, CreateBulkDataResponse>
//{
//    private readonly IDatabaseRepository _databaseRepository;
//    private readonly IInfraObjectRepository _infraObjectRepository;
//    private readonly IMapper _mapper;
//    private readonly IReplicationRepository _replicationRepository;
//    private readonly IServerRepository _serverRepository;
//    private readonly ISiteRepository _siteRepository;
//    private readonly ILicenseValidationService _licenseValidationService;
//    private readonly ILicenseManagerRepository _licenseManagerRepository;
//    private readonly ISiteTypeRepository _siteTypeRepository;
//    private readonly IComponentTypeRepository _componentTypeRepository;

//    public CreateBulkDataCommandHandler(IMapper mapper, IServerRepository serverRepository,
//        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository,
//        IInfraObjectRepository infraObjectRepository, ISiteRepository siteRepository, 
//        ILicenseValidationService licenseValidationService, 
//        ILicenseManagerRepository licenseManagerRepository,
//        ISiteTypeRepository siteTypeRepository,
//        IComponentTypeRepository componentTypeRepository)
//    {
//        _mapper = mapper;
//        _serverRepository = serverRepository;
//        _databaseRepository = databaseRepository;
//        _replicationRepository = replicationRepository;
//        _infraObjectRepository = infraObjectRepository;
//        _siteRepository = siteRepository;
//        _licenseValidationService = licenseValidationService;
//        _licenseManagerRepository = licenseManagerRepository;
//        _siteTypeRepository = siteTypeRepository;
//        _componentTypeRepository = componentTypeRepository;
//    }

//    public async Task<CreateBulkDataResponse> Handle(CreateBulkDataCommand request, CancellationToken cancellationToken)
//    {
//        var validator = new CreateBulkDataCommandValidator(_serverRepository, _databaseRepository,
//            _replicationRepository, _infraObjectRepository, 
//            _siteRepository,_licenseValidationService,
//            _licenseManagerRepository, _siteTypeRepository, _componentTypeRepository);

//        var validationResult = await validator.ValidateAsync(request, cancellationToken);

//        if (validationResult.Errors.Count > 0) throw new ValidationException(validationResult);
//        //Server
//        var servers = _mapper.Map<List<Domain.Entities.Server>>(request.ServerCommand);

//        servers = await _serverRepository.AddRangeAsync(servers) as List<Domain.Entities.Server>;

//        //Database
//        var databases = _mapper.Map<List<Domain.Entities.Database>>(request.DatabaseCommand);

//        databases = await _databaseRepository.AddRangeAsync(databases) as List<Domain.Entities.Database>;

//        //Replication
//        var replications = _mapper.Map<List<Domain.Entities.Replication>>(request.ReplicationCommand);

//        replications = await _replicationRepository.AddRangeAsync(replications) as List<Domain.Entities.Replication>;

//        //InfraObject
//        var infraObjects = _mapper.Map<List<Domain.Entities.InfraObject>>(request.InfraObjectCommand);

//        infraObjects = await _infraObjectRepository.AddRangeAsync(infraObjects) as List<Domain.Entities.InfraObject>;

//        var response = new CreateBulkDataResponse
//        {
//            Message =
//                $"Server '{servers!.Count}'Database'{databases!.Count}'Replication'{replications!.Count}'InfraObject'{infraObjects!.Count}' has been created successfully"
//        };
//        return response;
//    }
//}

