﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class InfraReplicationMappingRepositoryMocks
{
    public static Mock<IInfraReplicationMappingRepository> CreateInfraReplicationMappingRepository(List<InfraReplicationMapping> infraReplicationMappings)
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        infraReplicationMappingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraReplicationMappings);

        infraReplicationMappingRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraReplicationMapping>())).ReturnsAsync(
            (InfraReplicationMapping infraReplicationMapping) =>
            {
                infraReplicationMapping.Id = new Fixture().Create<int>();

                infraReplicationMapping.ReferenceId = new Fixture().Create<Guid>().ToString();

                infraReplicationMappings.Add(infraReplicationMapping);

                return infraReplicationMapping;
            });

        return infraReplicationMappingRepository;
    }

    public static Mock<IInfraReplicationMappingRepository> UpdateInfraReplicationMappingRepository(List<InfraReplicationMapping> infraReplicationMappings)
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        infraReplicationMappingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraReplicationMappings);

        infraReplicationMappingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraReplicationMappings.SingleOrDefault(x => x.ReferenceId == i));

        infraReplicationMappingRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraReplicationMapping>())).ReturnsAsync((InfraReplicationMapping infraReplicationMapping) =>
        {
            var index = infraReplicationMappings.FindIndex(item => item.ReferenceId == infraReplicationMapping.ReferenceId);

            infraReplicationMappings[index] = infraReplicationMapping;

            return infraReplicationMapping;

        });
        return infraReplicationMappingRepository;
    }

    public static Mock<IInfraReplicationMappingRepository> DeleteInfraReplicationMappingRepository(List<InfraReplicationMapping> infraReplicationMappings)
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        infraReplicationMappingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraReplicationMappings);

        infraReplicationMappingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraReplicationMappings.SingleOrDefault(x => x.ReferenceId == i));

        infraReplicationMappingRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraReplicationMapping>())).ReturnsAsync((InfraReplicationMapping infraReplicationMapping) =>
        {
            var index = infraReplicationMappings.FindIndex(item => item.ReferenceId == infraReplicationMapping.ReferenceId);

            infraReplicationMapping.IsActive = false;

            infraReplicationMappings[index] = infraReplicationMapping;

            return infraReplicationMapping;
        });

        return infraReplicationMappingRepository;
    }

    public static Mock<IInfraReplicationMappingRepository> GetInfraReplicationMappingRepository(List<InfraReplicationMapping> infraReplicationMappings)
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        infraReplicationMappingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraReplicationMappings);

        infraReplicationMappingRepository.Setup(repo => repo.GetInfraReplicationMappingByDatabaseId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(infraReplicationMappings);

        infraReplicationMappingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraReplicationMappings.SingleOrDefault(x => x.ReferenceId == i));

        return infraReplicationMappingRepository;
    }

    public static Mock<IInfraReplicationMappingRepository> GetInfraReplicationMappingEmptyRepository()
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        infraReplicationMappingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<InfraReplicationMapping>());

        return infraReplicationMappingRepository;
    }

    public static Mock<IInfraReplicationMappingRepository> GetPaginatedInfraReplicationMappingRepository(List<InfraReplicationMapping> infraReplicationMappings)
    {
        var infraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        var queryableInfraReplicationMapping = infraReplicationMappings.BuildMock();

        infraReplicationMappingRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableInfraReplicationMapping);

        return infraReplicationMappingRepository;
    }
}