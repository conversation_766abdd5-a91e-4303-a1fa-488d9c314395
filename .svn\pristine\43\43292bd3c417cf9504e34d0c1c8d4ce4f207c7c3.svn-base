using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftJob.Events.Delete;

public class DriftJobDeletedEventHandler : INotificationHandler<DriftJobDeletedEvent>
{
    private readonly ILogger<DriftJobDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftJobDeletedEventHandler(ILoggedInUserService userService, ILogger<DriftJobDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DriftJobDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.DriftJob}",
            Entity = Modules.DriftJob.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Drift Job Management '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Drift Job Management '{deletedEvent.Name}' deleted successfully.");
    }
}