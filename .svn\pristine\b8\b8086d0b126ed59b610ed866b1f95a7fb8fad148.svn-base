using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberAirGapFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string SourceSiteId = "SITE_SOURCE_001";
    public const string TargetSiteId = "SITE_TARGET_001";
    public const string SourceComponentId = "COMP_SOURCE_001";
    public const string TargetComponentId = "COMP_TARGET_001";

    public List<CyberAirGap> CyberAirGapPaginationList { get; set; }
    public List<CyberAirGap> CyberAirGapList { get; set; }
    public CyberAirGap CyberAirGapDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberAirGapFixture()
    {
        var fixture = new Fixture();

        CyberAirGapList = fixture.Create<List<CyberAirGap>>();

        CyberAirGapPaginationList = fixture.CreateMany<CyberAirGap>(20).ToList();

        //

        CyberAirGapDto = fixture.Create<CyberAirGap>();

        CyberAirGapDto.ReferenceId = Guid.NewGuid().ToString();
        CyberAirGapDto.IsActive = true;
        CyberAirGapDto.SourceSiteId = SourceSiteId;
        CyberAirGapDto.TargetSiteId = TargetSiteId;
        CyberAirGapDto.SourceComponentId = SourceComponentId;
        CyberAirGapDto.TargetComponentId = TargetComponentId;
        CyberAirGapDto.Status = "Active";
        CyberAirGapDto.IsAttached = true;
        CyberAirGapDto.WorkflowStatus = "Enabled";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
