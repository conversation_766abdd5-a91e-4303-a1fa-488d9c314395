﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetWorkflowActionResultByOperationGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByMaintenanceInfraObject;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByWorkflowOperationId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningUserDetails;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupByNodeId;
using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowOperationGroupListByWorkflowId;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowOperationGroupProfile : Profile
{
    public WorkflowOperationGroupProfile()
    {
        CreateMap<WorkflowOperationGroup, CreateWorkflowOperationGroupCommand>().ReverseMap();
        CreateMap<WorkflowOperation, CreateWorkflowOperationGroupCommand>().ReverseMap();
        CreateMap<WorkflowOperationGroup, CreateWorkflowOperationGroupListCommand>().ReverseMap();
        CreateMap<UpdateWorkflowOperationGroupCommand, WorkflowOperationGroup>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<LogHubVm, GetLogByGroupIdVm>().ReverseMap();

        CreateMap<InfraObject, GetMaintenanceInfraObjectListVm>()
            .ForMember(dest => dest.InfraObjectId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.InfraObjectName, opt => opt.MapFrom(src => src.Name));

        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperationGroup, GetByWorkflowOperationIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupByInfraObjectIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperation, WorkflowOperationGroupRunningStatusVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperation, WorkflowOperationGroupByNodeIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupListByWorkflowIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<WorkflowOperation, WorkflowOperationGroupRunningUserVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));




        //WorkflowActionResultCount 
        CreateMap<List<WorkflowActionResult>, WorkflowActionStatusCount>()
            //.ForMember(dest => dest.OperationId,
            //    opt => opt.MapFrom(src => src.FirstOrDefault(x => x.WorkflowOperationId.IsNotNullOrWhiteSpace()).WorkflowOperationId ?? "NA"))
            .ForMember(dest => dest.SkipCount,
                opt => opt.MapFrom(src => src.Count(x =>
                    x.Status.Trim().ToLower().Equals("skip") || x.Status.Trim().ToLower().Equals("skipped"))))
            .ForMember(dest => dest.SuccessCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("success"))))
            .ForMember(dest => dest.BypassedCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("bypassed"))))
            .ForMember(dest => dest.ErrorCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("error"))))
            .ForMember(dest => dest.RunningCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("running"))));

        CreateMap<List<WorkflowActionResult>, ProfileRunningCountListVm>()
            .ForMember(dest => dest.SkipCount,
                opt => opt.MapFrom(src => src.Count(x =>
                    x.Status.Trim().ToLower().Equals("skip") || x.Status.Trim().ToLower().Equals("skipped"))))
            .ForMember(dest => dest.SuccessCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("success"))))
            .ForMember(dest => dest.BypassedCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("bypassed"))))
            .ForMember(dest => dest.ErrorCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("error"))))
            .ForMember(dest => dest.RunningCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("running"))));

                

        //Web
        CreateMap<UpdateWorkflowOperationGroupCommand, WorkflowOperationGroupDetailVm>().ReverseMap();
        CreateMap<UpdateWorkflowActionResultCommand, WorkflowActionResultByOperationGroupIdVm>().ReverseMap();
        CreateMap<UpdateWorkflowOperationCommand, WorkflowOperationDetailVm>().ReverseMap();

        CreateMap<WorkflowOperationGroup, WorkflowOperationGroupRunningStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}