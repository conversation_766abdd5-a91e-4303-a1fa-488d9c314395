﻿

// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end



// create chart
var chart = am4core.create("ResilienceHealthChart", am4charts.GaugeChart);

chart.innerRadius = -15;
if (chart.logo) {
    chart.logo.disabled = true;
}

// Change the padding values
chart.padding(-10, -20, -20, -20)

var axis = chart.xAxes.push(new am4charts.ValueAxis());
axis.min = 0;
axis.max = 100;
axis.strictMinMax = true;
axis.renderer.labels.template.disabled = true;

var colorSet = new am4core.ColorSet();

var gradient = new am4core.LinearGradient();
gradient.stops.push({ color: am4core.color("red") })
gradient.stops.push({ color: am4core.color("yellow") })
gradient.stops.push({ color: am4core.color("green") })

axis.renderer.line.stroke = gradient;
axis.renderer.line.strokeWidth = 5;
axis.renderer.line.strokeOpacity = 1;

axis.renderer.grid.template.disabled = true;


var hand = chart.hands.push(new am4charts.ClockHand());
hand.radius = am4core.percent(97);
hand.fill = am4core.color("#fbbd60");
hand.stroke = am4core.color("#fbbd60");
hand.radius = am4core.percent(50);
hand.innerRadius = am4core.percent(0);
hand.radius = am4core.percent(80);
hand.startWidth = 10;
function ResilienceHealthChartdata(infraObjectId, infraObjectName, datalagValue, configuredRPO, data, moniterType, currentRPO, rpoThreshold) {
    
    const dataLagValuehealth = (datalagValue !== undefined && datalagValue !== "" && datalagValue !== null && datalagValue !== "0")
        ? `${datalagValue}`
        : 'NA';
    const currentRPOValuehealth = (currentRPO !== undefined && currentRPO !== "" && currentRPO !== null && currentRPO !== "0")
        ? `${currentRPO}`
        : 'NA';

    let result = "";
    let styleValue = 0;

    let rpoResult = '';
    let rpoStyleValue = 0
    
    if (currentRPOValuehealth !== "NA" && moniterType?.toLowerCase() !== "openshift") {
        hand.disabled = false;
        if (currentRPOValuehealth?.includes(".")) {
            const value = currentRPOValuehealth?.split(".");
            const hours = value[0] * 24;
            const minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const min = minutes?.split(':');
            const firstValue = parseInt(min[0]) + parseInt(hours);
            rpoResult = firstValue + ":" + min[1];
            rpoStyleValue = (parseInt(firstValue) * 60) + parseInt(min[1]);
        } else if (currentRPOValuehealth?.includes("+")) {
            const value = currentRPOValuehealth?.split(" ");
            rpoResult = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = rpoResult?.split(':')?.slice(1, 2)?.join(':');
            rpoStyleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        } else {
            rpoResult = currentRPOValuehealth?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = rpoResult.split(':')?.slice(1, 2)?.join(':');
            rpoStyleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        }
    }
    else {
        hand.disabled = true;
    }

    if (dataLagValuehealth !== "NA" && moniterType?.toLowerCase() !== "openshift" ) {
        hand.disabled = false;
        if (dataLagValuehealth?.includes(".")) {
            const value = dataLagValuehealth?.split(".");
            const hours = value[0] * 24;
            const minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const min = minutes?.split(':');
            const firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            styleValue = (parseInt(firstValue) * 60) + parseInt(min[1]);
        } else if (dataLagValuehealth?.includes("+")) {
            const value = dataLagValuehealth?.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        } else {
            result = dataLagValuehealth?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result.split(':')?.slice(1, 2)?.join(':');
            styleValue = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
        }
    } 
    else {
        hand.disabled = true;
    }

    //setTimeout(() => {

    //    if ((styleValue > Number(configuredRPO) || data?.percentage < 100) && dataLagValuehealth != "NA") {
    //        hand.showValue(13, 1000, am4core.ease.cubicOut);
    //    } else if ((styleValue < Number(configuredRPO) || data?.percentage >=100 )&& dataLagValuehealth != "NA") {
    //        hand.showValue(93, 1000, am4core.ease.cubicOut);
    //    }
    //}, 100);
    setTimeout(() => {
        if (styleValue === 0 && data?.percentage >= 100) {
            hand.showValue(100, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Excellent")
                .addClass("text-success");
        } else if (Number(configuredRPO) >= rpoStyleValue) {
            hand.showValue(30, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Critical")
                .addClass("text-danger");
        } else if (rpoStyleValue <= Number(rpoThreshold)) {
            hand.showValue(20, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Poor")
                .addClass("text-danger");
        } else if (data?.percentage >= 90 && data?.percentage < 100) {
            hand.showValue(data?.percentage, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Very Good")
                .addClass("text-success");
        } else if (data?.percentage >= 80 && data?.percentage < 90) {
            hand.showValue(data?.percentage, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Good")
                .addClass("text-success");
        } else if (data?.percentage >= 50 && data?.percentage < 80) {
            hand.showValue(data.percentage, 1000, am4core.ease.cubicOut, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Fair")
                .addClass("text-warning");
        } else {
            hand.showValue(data?.percentage, 1000, am4core.ease.cubicOut);
            $("#Resilience_Health")
                .removeClass("text-warning text-info text-success text-danger")
                .text("Un Known")
                .addClass("text-info");
        }
        
    }, 100);


    $("#Resilience_infraobject").text(infraObjectName).attr('title', infraObjectName);

    //if ((styleValue > Number(configuredRPO) || data?.percentage < 100) && dataLagValuehealth != "NA"  && moniterType?.toLowerCase() !== "openshift") {
    //    $("#Resilience_Health").removeClass("text-success text-info text-warning");
    //    $("#Resilience_Health").text("")
    //    $("#Resilience_Health").text("Critical").addClass("text-warning");
    //} else if ((styleValue < Number(configuredRPO) || data?.percentage >= 100) && dataLagValuehealth != "NA"  && moniterType?.toLowerCase() !== "openshift") {
    //    $("#Resilience_Health").removeClass("text-warning text-info text-success");
    //    $("#Resilience_Health").text("")
    //    $("#Resilience_Health").text("Good").addClass("text-success");
    //} else if (dataLagValuehealth === "NA" || data?.percentage === "" || moniterType?.toLowerCase() === "openshift") {
    //    $("#Resilience_Health").removeClass("text-warning text-success text-info");
    //    $("#Resilience_Health").text("")
    //    $("#Resilience_Health").text("NA").addClass("text-info");
    //}
    if (styleValue === 0 && data?.percentage >= 100) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Excellent").addClass("text-success");
    } else if (Number(configuredRPO) >= rpoStyleValue) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Critical").addClass("text-danger");
    }
    else if (rpoStyleValue <= Number(rpoThreshold)) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Poor").addClass("text-danger");
    } else if (data?.percentage >= 90 && data?.percentage < 100) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Very Good").addClass("text-success");
    } else if (data?.percentage >= 80 && data?.percentage < 90) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Good").addClass("text-success");
    } else if (data?.percentage >= 50 && data?.percentage < 80) {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Fair").addClass("text-warning");
    } else {
        $("#Resilience_Health").removeClass("text-warning text-info text-success");
        $("#Resilience_Health").text("")
        $("#Resilience_Health").text("Un Known").addClass("text-danger");
    }

}


