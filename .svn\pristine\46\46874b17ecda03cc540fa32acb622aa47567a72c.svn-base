using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RpoSlaDeviationReportFixture : IDisposable
{
    public List<RpoSlaDeviationReport> RpoSlaDeviationReportPaginationList { get; set; }
    public List<RpoSlaDeviationReport> RpoSlaDeviationReportList { get; set; }
    public RpoSlaDeviationReport RpoSlaDeviationReportDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string BusinessServiceName = "Test Business Service";
    public const string BusinessFunctionId = "BF_123";
    public const string BusinessFunctionName = "Test Business Function";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string InfraObjectName = "Test Infrastructure Object";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RpoSlaDeviationReportFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RpoSlaDeviationReport>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, () => BusinessServiceId)
            .With(x => x.BusinessServiceName, () => BusinessServiceName)
            .With(x => x.BusinessFunctionId, () => BusinessFunctionId)
            .With(x => x.BusinessFunctionName, () => BusinessFunctionName)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => InfraObjectName)
            .With(x => x.ProductionLogGeneratedTime, () => DateTime.UtcNow.AddHours(-2))
            .With(x => x.ProductionArchiveLogSequence, () => _fixture.Create<string>())
            .With(x => x.AppliedLogTimeAtDr, () => DateTime.UtcNow.AddHours(-1))
            .With(x => x.ArchiveLogAppliedAtDr, () => _fixture.Create<string>())
            .With(x => x.DataLag, () => "1 hour")
            .With(x => x.TimeStamp, () => DateTime.UtcNow)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RpoSlaDeviationReportList = _fixture.CreateMany<RpoSlaDeviationReport>(5).ToList();
        RpoSlaDeviationReportPaginationList = _fixture.CreateMany<RpoSlaDeviationReport>(20).ToList();
        RpoSlaDeviationReportDto = _fixture.Create<RpoSlaDeviationReport>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RpoSlaDeviationReport CreateRpoSlaDeviationReportWithBusinessServiceId(string businessServiceId)
    {
        return _fixture.Build<RpoSlaDeviationReport>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, businessServiceId)
            .With(x => x.BusinessServiceName, BusinessServiceName)
            .With(x => x.BusinessFunctionId, BusinessFunctionId)
            .With(x => x.BusinessFunctionName, BusinessFunctionName)
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.ProductionLogGeneratedTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.ProductionArchiveLogSequence, _fixture.Create<string>())
            .With(x => x.AppliedLogTimeAtDr, DateTime.UtcNow.AddHours(-1))
            .With(x => x.ArchiveLogAppliedAtDr, _fixture.Create<string>())
            .With(x => x.DataLag, "1 hour")
            .With(x => x.TimeStamp, DateTime.UtcNow)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpoSlaDeviationReport CreateRpoSlaDeviationReportWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RpoSlaDeviationReport>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, BusinessServiceId)
            .With(x => x.BusinessServiceName, BusinessServiceName)
            .With(x => x.BusinessFunctionId, BusinessFunctionId)
            .With(x => x.BusinessFunctionName, BusinessFunctionName)
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.ProductionLogGeneratedTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.ProductionArchiveLogSequence, _fixture.Create<string>())
            .With(x => x.AppliedLogTimeAtDr, DateTime.UtcNow.AddHours(-1))
            .With(x => x.ArchiveLogAppliedAtDr, _fixture.Create<string>())
            .With(x => x.DataLag, "1 hour")
            .With(x => x.TimeStamp, DateTime.UtcNow)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpoSlaDeviationReport CreateRpoSlaDeviationReportWithDates(
        string businessServiceId = null,
        string infraObjectId = null,
        DateTime? createdDate = null,
        DateTime? lastModifiedDate = null)
    {
        return _fixture.Build<RpoSlaDeviationReport>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, businessServiceId ?? BusinessServiceId)
            .With(x => x.BusinessServiceName, BusinessServiceName)
            .With(x => x.BusinessFunctionId, BusinessFunctionId)
            .With(x => x.BusinessFunctionName, BusinessFunctionName)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.ProductionLogGeneratedTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.ProductionArchiveLogSequence, _fixture.Create<string>())
            .With(x => x.AppliedLogTimeAtDr, DateTime.UtcNow.AddHours(-1))
            .With(x => x.ArchiveLogAppliedAtDr, _fixture.Create<string>())
            .With(x => x.DataLag, "1 hour")
            .With(x => x.TimeStamp, DateTime.UtcNow)
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.LastModifiedDate, lastModifiedDate ?? DateTime.UtcNow)
            .Create();
    }

    public RpoSlaDeviationReport CreateRpoSlaDeviationReportWithProperties(
        string businessServiceId = null,
        string businessServiceName = null,
        string businessFunctionId = null,
        string businessFunctionName = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string dataLag = null,
        bool isActive = true)
    {
        return _fixture.Build<RpoSlaDeviationReport>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.BusinessServiceId, businessServiceId ?? BusinessServiceId)
            .With(x => x.BusinessServiceName, businessServiceName ?? BusinessServiceName)
            .With(x => x.BusinessFunctionId, businessFunctionId ?? BusinessFunctionId)
            .With(x => x.BusinessFunctionName, businessFunctionName ?? BusinessFunctionName)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.InfraObjectName, infraObjectName ?? InfraObjectName)
            .With(x => x.ProductionLogGeneratedTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.ProductionArchiveLogSequence, _fixture.Create<string>())
            .With(x => x.AppliedLogTimeAtDr, DateTime.UtcNow.AddHours(-1))
            .With(x => x.ArchiveLogAppliedAtDr, _fixture.Create<string>())
            .With(x => x.DataLag, dataLag ?? "1 hour")
            .With(x => x.TimeStamp, DateTime.UtcNow)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public List<RpoSlaDeviationReport> CreateMultipleRpoSlaDeviationReportsWithDateRange(
        string businessServiceId, DateTime startDate, DateTime endDate, int count)
    {
        var reports = new List<RpoSlaDeviationReport>();
        var dateIncrement = (endDate - startDate).TotalDays / count;

        for (int i = 0; i < count; i++)
        {
            var reportDate = startDate.AddDays(i * dateIncrement);
            reports.Add(CreateRpoSlaDeviationReportWithDates(
                businessServiceId: businessServiceId,
                createdDate: reportDate,
                lastModifiedDate: reportDate));
        }

        return reports;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonBusinessServiceIds = { "BS_001", "BS_002", "BS_003" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonDataLagValues = { "30 minutes", "1 hour", "2 hours", "4 hours" };
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
    }
}
