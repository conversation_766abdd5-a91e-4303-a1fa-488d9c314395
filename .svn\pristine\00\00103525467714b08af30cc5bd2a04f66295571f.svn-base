﻿using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetNames;

namespace ContinuityPatrol.Application.UnitTests.Features.ReportSchedule.Queries
{
    public class GetReportScheduleNameQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
        private readonly GetReportScheduleNameQueryHandler _handler;

        public GetReportScheduleNameQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
            _handler = new GetReportScheduleNameQueryHandler(
                _mockMapper.Object,
                _mockReportScheduleRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnListOfReportScheduleNameVm_WhenReportSchedulesExist()
        {
            var query = new GetReportScheduleNameQuery();
            var reportSchedules = new List<Domain.Entities.ReportSchedule>
            {
                new Domain.Entities.ReportSchedule
                {
                    Id = 1,
                    ReportName = "Daily Report",
                },
                new Domain.Entities.ReportSchedule
                {
                    Id = 2,
                    ReportName = "Weekly Report",
                }
            };

            var reportScheduleNameVm = new List<GetReportScheduleNameVm>
            {
                new GetReportScheduleNameVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportName = "Daily Report"
                }, 
                new GetReportScheduleNameVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReportName = "Weekly Report"
                }
            };

            _mockReportScheduleRepository.Setup(r => r.GetReportScheduleNames())
                .ReturnsAsync(reportSchedules);

            _mockMapper.Setup(m => m.Map<List<GetReportScheduleNameVm>>(reportSchedules))
                .Returns(reportScheduleNameVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(reportScheduleNameVm[0].Id, result[0].Id);
            Assert.Equal(reportScheduleNameVm[1].Id, result[1].Id);

            _mockReportScheduleRepository.Verify(r => r.GetReportScheduleNames(), Times.Once);

            _mockMapper.Verify(m => m.Map<List<GetReportScheduleNameVm>>(reportSchedules), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoReportSchedulesExist()
        {
            var query = new GetReportScheduleNameQuery();
            var reportSchedules = new List<Domain.Entities.ReportSchedule>();

            _mockReportScheduleRepository.Setup(r => r.GetReportScheduleNames())
                .ReturnsAsync(reportSchedules);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockReportScheduleRepository.Verify(r => r.GetReportScheduleNames(), Times.Once);

            _mockMapper.Verify(m => m.Map<List<GetReportScheduleNameVm>>(It.IsAny<List<Domain.Entities.ReportSchedule>>()), Times.Never);
        }
    }
}
