﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixTemplate.Command.Create;

public class CreateApprovalMatrixTemplateCommand : IRequest<CreateApprovalMatrixTemplateResponse>
{
    public string UserId { get; set; }
    public string UserName { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public DateTime Time { get; set; }
    public string NotificationType { get; set; }
    public string Properties { get; set; }
    public string Emails { get; set; }
    public string Rule { get; set; }
    public string WorkflowModification { get; set; }
    public string WorkflowProfileExecution { get; set; }
    public string ProfileModification { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}