﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'DataSyncAppReplication';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { DataSyncmonitorStatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
function DataSyncmonitorStatus(id, type) {
    $.ajax({
        url: "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType",
        method: 'GET',
        data: {
            monitorId: id,
            type: type
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res?.data;
          
            let data = JSON?.parse(value?.properties)   
           
            ReplicationType.push(checkAndReplace(data?.Monitor_Type));
            let dataVal = data?.DataSyncReplicationModels[0]?.MonitoringModel.DataSyncJobIds
            let prStatus
            let drStatus
            DataSyncTableList(dataVal)
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }

            $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
            $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                let data = JSON?.parse(value?.properties);                
                value?.serverStatus?.forEach(server => {
                    Object.keys(server).forEach(key => {
                        let data = server[key]
                        prStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';
                        drStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';
                    })
                    
                })                

                $('#PR_Server_Name').text(checkAndReplace(data?.PrDataSyncReplicationModel?.PrMonitoringModel?.PR_Server_Name))
                $('#PR_Server_IpAddress').text(checkAndReplace(data?.PrDataSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress)).prepend(prStatus)

                let customSite = data?.DataSyncReplicationModels?.length > 1;
                if (customSite) {
                    $("#Sitediv").show();
                } else {
                    $("#Sitediv").hide();
                }

                $(".siteContainer").empty();

                data?.DataSyncReplicationModels?.forEach((a, index) => {
                    let selectTab = `
                         <li class="nav-item siteListChange" id='siteName${index}'>
                              <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
                            </li>
                         <li class="nav-item vr"></li>`;
                    $(".siteContainer").append(selectTab);
                });
                if (data?.DataSyncReplicationModels?.length > 0) {
                    $("#siteName0 .nav-link").addClass("active");
                    displaySiteData(data?.DataSyncReplicationModels[0]);
                }
                $(document).on('click', '.siteListChange', function () {
                    $(".siteListChange .nav-link").removeClass("active");
                    $(this).find(".nav-link").addClass("active");
                    let siteId = $(this)[0].id
                    let getSiteName = $(`#${siteId} .siteName`).text()

                    let MonitoringModel = data?.DataSyncReplicationModels?.find(d => d?.Type === getSiteName);
                    if (MonitoringModel) {
                        displaySiteData(MonitoringModel);
                    }
                });
                function displaySiteData(siteData) {                    
                    $("#DR_Server_IpAddress").text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                    $('#DR_Server_Name').text(checkAndReplace(siteData?.MonitoringModel?.Server_Name))
                }
            }
        }
    })
}

let ReplicationType = [];

async function DataSyncTableList(data) {
    
    let dataSyncJobIds =data
    $.ajax({
        url: "/Monitor/Monitoring/GetFastMonitorList",
        method: 'GET',
        data: {
            dataSyncJobIds: JSON?.stringify(dataSyncJobIds)
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res.data;    
          
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }

            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                    let data = value;                          
                    let SourcePath = '';
                   
                    let TotalNumberoffiles = [];
                    let TotalFilesSize = [];
                    let NumberOfRegFilesTransfer = [];
                    let SDirectory = [];
                    let TDirectory = [];
                    let TotalFileSize = [];
                    let LastFileSize = [];
                    let LastFileName = [];
                    let skipFile = [];
                    $('#dataSynctable').empty();
                    
                       data?.length && data?.forEach((list, i) => {                                
                        SourcePath = checkAndReplace(list?.sourceIP?.split(',')[0])              
                        LastFileSize.push(checkAndReplace(list?.lastFileSize))
                        LastFileName.push(checkAndReplace(list?.lastFileName));
                        SDirectory.push(checkAndReplace(list?.sourcePath));
                        TDirectory.push(checkAndReplace(list?.destinationPath))                     
                        TotalFileSize.push(checkAndReplace(list?.totalFilesCount))
                        skipFile.push(checkAndReplace(list?.skippedFilesCount))
                       });
                       
                    let jobData = ''
                let iconClass = TotalNumberoffiles !== 'NA' ? 'cp-files me-1 fs-6 text-primary' : TotalFilesSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : NumberOfRegFilesTransfer != "NA" ? "cp-files me-1 fs-6 text-primary" : TotalTransferfileSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : skipFile !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : 'text-danger me-1 fs-6 cp-disable';
                    
                    jobData += `
                        <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th></th>

                                </tr>
                            </thead>
                            <tbody>
                        <tr>
                           <td class="fw-semibold text-truncate "><i class="text-secondary cp-server-ip me-1"></i> Server IP</td>
                            <td class="text-truncate"><i class="cp-fal-server me-1 fs-6 text-success"></i>${SourcePath}</td>
                        </tr>
                         <tr>
                             <td class="fw-semibold text-truncate "><i class="text-secondary cp-replication-type me-1"></i>Replication Type</td>
                             <td class="text-truncate"><i class="cp-replication-type me-1 fs-6 text-primary"></i>${ReplicationType}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-type me-1"></i>Source Directory</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 fs-6 text-primary"></i>${SDirectory}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-type me-1"></i>Target Directory</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 fs-6 text-primary"></i>${TDirectory}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-replication-connect me-1"></i>Total Replication Files</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${TotalFileSize}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-control-file-name me-1"></i>Last Replicated File Name</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${LastFileName}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-size me-1"></i>Last Replicated File Size (in KB)</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${LastFileSize}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-size me-1"></i>Skipped Files</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${skipFile}</td>
                        </tr>
                        </tbody>
                            `

                    $('#dataSynctable').append(jobData);

                }
            }
    });

}