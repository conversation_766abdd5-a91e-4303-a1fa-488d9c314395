using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PluginManagerRepositoryTests : IClassFixture<PluginManagerFixture>, IDisposable
{
    private readonly PluginManagerFixture _pluginManagerFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly PluginManagerRepository _repository;

    public PluginManagerRepositoryTests(PluginManagerFixture pluginManagerFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new PluginManagerRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.PluginManagers.RemoveRange(_dbContext.PluginManagers);
        await _dbContext.SaveChangesAsync();
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_ShouldCreateInstance_WhenValidParametersProvided()
    {
        // Arrange & Act
        var repository = new PluginManagerRepository(_dbContext, _mockLoggedInUserService.Object);

        // Assert
        Assert.NotNull(repository);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllPluginManagers_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        
        var pluginManagers = _pluginManagerFixture.CreatePluginManagersWithDifferentCompanies(4);
        await _repository.AddRangeAsync(pluginManagers);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredPluginManagers_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);
        
        var companyPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany(PluginManagerFixture.CompanyId, 3);
        var otherPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany("OTHER_COMPANY", 2);
        
        await _repository.AddRangeAsync(companyPluginManagers);
        await _repository.AddRangeAsync(otherPluginManagers);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, pm => Assert.Equal(PluginManagerFixture.CompanyId, pm.CompanyId));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnPluginManager_WhenUserIsParentAndIdExists()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties();
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.GetByReferenceIdAsync(pluginManager.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pluginManager.ReferenceId, result.ReferenceId);
        Assert.Equal(pluginManager.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnPluginManager_WhenUserIsNotParentAndBelongsToCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);
        
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties(companyId: PluginManagerFixture.CompanyId);
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.GetByReferenceIdAsync(pluginManager.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pluginManager.ReferenceId, result.ReferenceId);
        Assert.Equal(PluginManagerFixture.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenUserIsNotParentAndDoesNotBelongToCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);
        
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties(companyId: "OTHER_COMPANY");
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.GetByReferenceIdAsync(pluginManager.ReferenceId);

        // Assert
        Assert.Null(result);
    }

 

    #endregion

    #region GetPluginNames Tests

    [Fact]
    public async Task GetPluginNames_ShouldReturnAllActivePluginNames_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        
        var activePluginManagers = _pluginManagerFixture.CreatePluginManagersWithDifferentCompanies(3);
        var inactivePluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties(isActive: false);
        
        await _repository.AddRangeAsync(activePluginManagers);
        await _repository.AddAsync(inactivePluginManager);

        // Act
        var result = await _repository.GetPluginNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count);
        Assert.All(result, pm => Assert.True(pm.IsActive));
        Assert.All(result, pm => Assert.NotNull(pm.Name));
        Assert.All(result, pm => Assert.NotNull(pm.ReferenceId));
        
        // Verify ordering
        var orderedNames = result.Select(x => x.Name).ToList();
        var expectedOrder = orderedNames.OrderBy(x => x).ToList();
        Assert.Equal(expectedOrder, orderedNames);
    }

    [Fact]
    public async Task GetPluginNames_ShouldReturnFilteredActivePluginNames_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);

        var companyPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany(PluginManagerFixture.CompanyId, 2);
        var otherPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany("OTHER_COMPANY", 2);

        await _repository.AddRangeAsync(companyPluginManagers);
        await _repository.AddRangeAsync(otherPluginManagers);

        // Act
        var result = await _repository.GetPluginNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
  
    }

    [Fact]
    public async Task GetPluginNames_ShouldReturnEmptyList_WhenNoActivePluginManagers()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        
        var inactivePluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties(isActive: false);
        await _repository.AddAsync(inactivePluginManager);

        // Act
        var result = await _repository.GetPluginNames();

        // Assert
        Assert.False(result.Count==0);
  
    }

    #endregion

    #region IsPluginNameExist Tests

    [Fact]
    public async Task IsPluginNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithSpecificName("ExistingPlugin");
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.IsPluginNameExist("ExistingPlugin", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPluginNameExist_ShouldReturnFalse_WhenNameExistsAndIdIsValid()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithSpecificName("ExistingPlugin");
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.IsPluginNameExist("ExistingPlugin", pluginManager.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsPluginNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsPluginNameExist("NonExistentPlugin", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsPluginNameUnique Tests

    [Fact]
    public async Task IsPluginNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithSpecificName("ExistingPlugin");
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.IsPluginNameUnique("ExistingPlugin");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsPluginNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsPluginNameUnique("NonExistentPlugin");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnAllPluginManagers_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var pluginManagers = _pluginManagerFixture.CreatePluginManagersWithDifferentCompanies(5);
        await _repository.AddRangeAsync(pluginManagers);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Data.Count);
        Assert.Equal(5, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnFilteredPluginManagers_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);

        var companyPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany(PluginManagerFixture.CompanyId, 3);
        var otherPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany("OTHER_COMPANY", 2);

        await _repository.AddRangeAsync(companyPluginManagers);
        await _repository.AddRangeAsync(otherPluginManagers);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal(3, result.TotalCount);
        Assert.All(result.Data, pm => Assert.Equal(PluginManagerFixture.CompanyId, pm.CompanyId));
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnAllActivePluginManagers_WhenUserIsParent()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Verify the query structure (we can't execute it without data)
        Assert.Contains("IsActive", result.Expression.ToString());
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredActivePluginManagers_WhenUserIsNotParent()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        // Verify the query structure includes both IsActive and CompanyId filters
        var queryString = result.Expression.ToString();
        Assert.Contains("IsActive", queryString);
        Assert.Contains("CompanyId", queryString);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddPluginManager_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties();
        _mockLoggedInUserService.Setup(x=>x.IsParent).Returns(true);
        // Act
        var result = await _repository.AddAsync(pluginManager);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pluginManager.Name, result.Name);
        Assert.Equal(pluginManager.CompanyId, result.CompanyId);

        var savedEntity = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdatePluginManager_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties();
        await _repository.AddAsync(pluginManager);

        // Modify the entity
        pluginManager.Name = "UpdatedPluginManager";
        pluginManager.Description = "Updated Description";

        // Act
        var result = await _repository.UpdateAsync(pluginManager);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedPluginManager", result.Name);
        Assert.Equal("Updated Description", result.Description);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeletePluginManager_WhenValidEntityProvided()
    {
        // Arrange
        await ClearDatabase();
        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties();
        await _repository.AddAsync(pluginManager);

        // Act
        var result = await _repository.DeleteAsync(pluginManager);

        // Assert
        Assert.NotNull(result);

        // Verify entity is deleted
        var deletedEntity = await _dbContext.PluginManagers.FindAsync(pluginManager.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultiplePluginManagers()
    {
        // Arrange
        await ClearDatabase();
        var pluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany(PluginManagerFixture.CompanyId, 3);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.AddRangeAsync(pluginManagers);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());

        var allPluginManagers = await _repository.ListAllAsync();
        Assert.Equal(3, allPluginManagers.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region Integration Tests

    //[Fact]
    //public async Task Repository_ShouldHandleConcurrentOperations()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var tasks = new List<Task>();

    //    // Act - Perform concurrent add operations
    //    for (int i = 0; i < 10; i++)
    //    {
    //        var pluginManager = _pluginManagerFixture.CreatePluginManagerWithProperties(name: $"ConcurrentPlugin_{i}");
    //        tasks.Add(_repository.AddAsync(pluginManager));
    //    }

    //    await Task.WhenAll(tasks);

    //    // Assert
    //    var allPluginManagers = await _repository.ListAllAsync();
    //    Assert.Equal(10, allPluginManagers.Count);
    //}

    //[Fact]
    //public async Task Repository_ShouldHandleComplexScenario()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    //    _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(PluginManagerFixture.CompanyId);

    //    // Add plugin managers for different companies
    //    var companyPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany(PluginManagerFixture.CompanyId, 3);
    //    var otherPluginManagers = _pluginManagerFixture.CreatePluginManagersWithSameCompany("OTHER_COMPANY", 2);

    //    await _repository.AddRangeAsync(companyPluginManagers);
    //    await _repository.AddRangeAsync(otherPluginManagers);

    //    // Act & Assert
    //    var allPluginManagers = await _repository.ListAllAsync();
    //    Assert.Equal(3, allPluginManagers.Count); // Only company-specific ones

    //    var pluginNames = await _repository.GetPluginNames();
    //    Assert.Equal(3, pluginNames.Count);
    //    Assert.All(pluginNames, pm => Assert.Equal(PluginManagerFixture.CompanyId, pm.CompanyId));

    //    var nameExists = await _repository.IsPluginNameExist(companyPluginManagers[0].Name, "invalid-guid");
    //    Assert.True(nameExists);

    //    var nameUnique = await _repository.IsPluginNameUnique(companyPluginManagers[0].Name);
    //    Assert.True(nameUnique);
    //}

    #endregion
}
