﻿using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowExecutionEventLog.Queries;

public class GetWorkflowExecutionEventLogDetailQueryHandlerTests : IClassFixture<WorkflowExecutionEventLogFixture>
{
    private readonly WorkflowExecutionEventLogFixture _workflowExecutionEventLogFixture;

    private readonly Mock<IWorkflowExecutionEventLogRepository> _mockWorkflowExecutionEventLogRepository;

    private readonly GetWorkflowExecutionEventLogDetailQueryHandler _handler;

    public GetWorkflowExecutionEventLogDetailQueryHandlerTests(WorkflowExecutionEventLogFixture workflowExecutionEventLogFixture)
    {
        _workflowExecutionEventLogFixture = workflowExecutionEventLogFixture;

        _mockWorkflowExecutionEventLogRepository = WorkflowExecutionEventLogRepositoryMocks.GetWorkflowExecutionEventLogRepository(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs);

        _handler = new GetWorkflowExecutionEventLogDetailQueryHandler(_workflowExecutionEventLogFixture.Mapper, _mockWorkflowExecutionEventLogRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_WorkflowExecutionEventLog_Details_When_Valid()
    {
        var result = await _handler.Handle(new GetWorkflowExecutionEventLogDetailQuery { Id = _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<WorkflowExecutionEventLogDetailVm>();
        result.Id.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ReferenceId);
        result.WorkflowActionName.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowActionName);
        result.CompanyId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].CompanyId);
        result.LoginName.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].LoginName);
        result.StartTime.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].StartTime);
        result.EndTime.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].EndTime);
        result.Status.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Status);
        result.UserEvent.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].UserEvent);
        result.Message.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Message);
        result.WorkflowOperationId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationId);
        result.WorkflowOperationGroupId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationGroupId);
        result.InfraObjectId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].InfraObjectId);
        result.ActionId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ActionId);
        result.ConditionActionId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ConditionActionId);
        result.SkipStep.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].SkipStep);
        result.IsReload.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].IsReload);
        result.Direction.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Direction);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowExecutionEventLogId()
    {
        var handler = new GetWorkflowExecutionEventLogDetailQueryHandler(_workflowExecutionEventLogFixture.Mapper, _mockWorkflowExecutionEventLogRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowExecutionEventLogDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetTemplateByReferenceId_OneTime()
    {
        await _handler.Handle(new GetWorkflowExecutionEventLogDetailQuery { Id = _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowExecutionEventLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}