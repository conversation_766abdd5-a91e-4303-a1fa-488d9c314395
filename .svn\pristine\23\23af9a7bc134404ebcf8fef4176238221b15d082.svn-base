﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="Hyper-V Detail Monitoring">
            <i class="cp-monitoring"></i><span>Hyper-V Detail Monitoring :</span>
            <span id="infraName"></span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    
    <div class="monitor_pages mb-2">
        <div class="row g-2 mt-0">
            <div class="col d-grid">
                <div class="card Card_Design_None mb-2 ">
                    <div class="card-header card-title">
                        HyperV_Cluster_Infra
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Hyper-V VM Summary</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-info">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-ip-address me-1 fs-6"></i>Hyper-V Host IP Address/HostName
                                    </td>
                                    <td class="text-truncate" id="PR_Hyper_V_Host_IP_Address"></td>
                                    <td class="text-truncate" id="DR_Hyper_V_Host_IP_Address"></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-name  me-1 fs-6"></i>VM Name
                                    </td>
                                    <td class="text-truncate" id="PR_VM_Name"><i class="text-primary cp-name  me-1 fs-6"></i></td>
                                    <td class="text-truncate" id="DR_VM_Name"><i class="text-primary cp-name  me-1 fs-6"></i></td>
                                </tr> 
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-vm_name me-1 fs-6"></i>VM State
                                    </td>
                                    <td class="text-truncate" id="PR_VM_State"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                    <td class="text-truncate" id="DR_VM_State"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-ip-address me-1 fs-6"></i>VM IP Address/HostName
                                    </td>
                                    <td class="text-truncate" id="PR_VM_IP_Addresses"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                    <td class="text-truncate" id="DR_VM_IP_Addresses"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                </tr>   
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-network me-1 fs-6"></i>VM Networking Status
                                    </td>
                                    <td class="text-truncate" id="PR_VM_Networking_Status"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                    <td class="text-truncate" id="DR_VM_Networking_Status"><i class="text-danger cp-disable me-1 fs-6"></i></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col d-grid">
                <div class="row g-2">
                    <div class="col-lg-12">
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title">Solution Diagram</div>
                            <div class="card-body text-center">
                                 <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>


        </div>
        <div class="row g-2">
            <div class="col-12">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title">VM Replication Health Details</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Hyper-V</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-info">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-replication-type me-1 fs-6"></i>Replication Type
                                    </td>
                                    <td class="text-truncate" id="PR_Replication_Type"></td>
                                    <td class="text-truncate" id="DR_Replication_Type"></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-database-warning me-1 fs-6"></i>Replication State
                                    </td>
                                    <td class="text-truncate" id="PR_Replication_State">
                                        <i class="text-primary cp-reload me-1 fs-6"></i>
                                    </td>
                                    <td class="text-truncate" id="DR_Replication_State">
                                        <i class="text-primary cp-reload me-1 fs-6"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-replication-connect me-1 fs-6"></i>Replication Mode
                                    </td>
                                    <td class="text-truncate" id="PR_Replication_Mode">
                                        <i class="text-success cp-refresh me-1 fs-6"></i>
                                    </td>
                                    <td class="text-truncate" id="DR_Replication_Mode">
                                        <i class="text-success cp-refresh me-1 fs-6"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-manage-server me-1 fs-6"></i>Current Primary Server
                                    </td>
                                    <td class="text-truncate" id="PR_Current_Primary_Server">
                                        
                                    </td>
                                    <td class="text-truncate" id="DR_Current_Primary_Server">
                                       
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-replication-type me-1 fs-6"></i>Current Replica Server
                                    </td>
                                    <td class="text-truncate" id="PR_Current_Replication_Server">
                                      
                                    </td>
                                    <td class="text-truncate" id="DR_Current_Replication_Server">
                                        
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-health-Up me-1 fs-6"></i>Replication Health
                                    </td>
                                    <td class="text-truncate" id="PR_Replication_Health">
                                        <i class="text-warning cp-normal me-1 fs-6"></i>
                                    </td>
                                    <td class="text-truncate" id="DR_Replication_Health">
                                        <i class="text-warning cp-normal me-1 fs-6"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-replication-rotate me-1 fs-6"></i>Replica Port
                                    </td>
                                    <td class="text-truncate" id="PR_Replication_Port">
                                        <i class="text-success cp-connected me-1 fs-6"></i>
                                    </td>
                                    <td class="text-truncate" id="DR_Replication_Port">
                                        <i class="text-success cp-connected me-1 fs-6"></i>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-authentication me-1 fs-6"></i>Authentication Type
                                    </td>
                                    <td class="text-truncate" id="PR_Authentication_Type">
                                        <i class="text-primary cp-key me-1 fs-6"></i>
                                    </td>
                                    <td class="text-truncate" id="DR_Authentication_Type">
                                        <i class="text-primary cp-key me-1 fs-6"></i>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title"></div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Pending Replication</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-info">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-database-sizes me-1 fs-6"></i>Size of data yet to be replicated (KB)
                                    </td>
                                    <td class="text-truncate" id="PR_Replicated_Size"></td>
                                    <td class="text-truncate" id="DR_Replicated_Size"></td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-dataguard-status me-1 fs-6"></i>Last synchronized at
                                    </td>
                                    <td class="text-truncate" id="PR_Last_synchronized_At">
                                        <i class="text-primary cp-table-date me-1 fs-6"></i> 
                                    </td>
                                    <td class="text-truncate" id="DR_Last_synchronized_At">
                                 
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-datalog me-1 fs-6"></i>DataLag (HH:MM:SS)
                                    </td>
                                    <td class="text-truncate" id="PR_Datalag">
                                        <i class="cp-time text-primary mt-2"></i>
                                    </td>
                                    <td class="text-truncate">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
                @* <div class="card Card_Design_None h-100">
                    <div class="card-header card-title">RSync_App_Infra</div>
                    <div class="card-body pt-0 p-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Service Name</th>
                                    <th class="text-primary">PR Mailbox Server</th>
                                    <th class="text-info">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-server me-1 fs-6"></i>Server Name
                                    </td>
                                    <td class="text-truncate">Postgres10.5_DB_Prod</td>
                                    <td class="text-truncate">Postgres10.5_DB_DR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold">
                                        <i class="text-secondary cp-ip-address me-1 fs-6"></i>IP Address/HostName
                                    </td>
                                    <td class="text-truncate">
                                        <i class="text-success cp-fal-server me-1 fs-6"></i>**************
                                    </td>
                                    <td class="text-truncate">
                                        <i class="text-success cp-fal-server me-1 fs-6"></i>**************
                                    </td>
                                </tr>
                              
                            </tbody>
                        </table>
                    </div>
                </div> *@
            </div>
           
        </div>

    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
@* <script src="~/js/monitoring/hp3parpremira.js"></script> *@
<script src="~/js/Monitoring/MonitoringHyperV.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>