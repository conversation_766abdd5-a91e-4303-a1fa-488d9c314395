using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class EmployeeService : BaseClient, IEmployeeService
{
    public EmployeeService(IConfiguration config, IAppCache cache, ILogger<EmployeeService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<EmployeeListVm>> GetEmployeeList()
    {
        var request = new RestRequest("api/v6/employees");

        return await GetFromCache<List<EmployeeListVm>>(request, "GetEmployeeList");
    }

    public async Task<BaseResponse> CreateAsync(CreateEmployeeCommand createEmployeeCommand)
    {
        var request = new RestRequest("api/v6/employees", Method.Post);

        request.AddJsonBody(createEmployeeCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateEmployeeCommand updateEmployeeCommand)
    {
        var request = new RestRequest("api/v6/employees", Method.Put);

        request.AddJsonBody(updateEmployeeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/employees/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<EmployeeDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/employees/{id}");

        return await Get<EmployeeDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsEmployeeNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/employees/name-exist?employeeName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<EmployeeListVm>> GetPaginatedEmployees(GetEmployeePaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/employees/paginated-list");

      return await Get<PaginatedResult<EmployeeListVm>>(request);
  }
   #endregion
}
