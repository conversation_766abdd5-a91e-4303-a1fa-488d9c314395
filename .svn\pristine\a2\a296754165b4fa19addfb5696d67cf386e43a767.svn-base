﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;


public class ServerSubTypeRepositoryMocks
{
    public static Mock<IServerSubTypeRepository> CreateServerSubTypeRepository(List<ServerSubType> serverSubTypes)
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverSubTypes);

        mockServerSubTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<ServerSubType>())).ReturnsAsync(
            (ServerSubType serverSubType) =>
            {
                serverSubType.Id = new Fixture().Create<int>();

                serverSubType.ReferenceId = new Fixture().Create<Guid>().ToString();

                serverSubTypes.Add(serverSubType);

                return serverSubType;
            });

        return mockServerSubTypeRepository;
    }

    public static Mock<IServerSubTypeRepository> UpdateServerSubTypeRepository(List<ServerSubType> serverSubTypes)
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverSubTypes);

        mockServerSubTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => serverSubTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockServerSubTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ServerSubType>())).ReturnsAsync((ServerSubType serverSubType) =>
        {
            var index = serverSubTypes.FindIndex(item => item.ReferenceId == serverSubType.ReferenceId);

            serverSubTypes[index] = serverSubType;

            return serverSubType;
        });

        return mockServerSubTypeRepository;
    }

    public static Mock<IServerSubTypeRepository> DeleteServerSubTypeRepository(List<ServerSubType> serverSubTypes)
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverSubTypes);

        mockServerSubTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => serverSubTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockServerSubTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ServerSubType>())).ReturnsAsync((ServerSubType serverSubType) =>
        {
            var index = serverSubTypes.FindIndex(item => item.ReferenceId == serverSubType.ReferenceId);

            serverSubType.IsActive = false;

            serverSubTypes[index] = serverSubType;

            return serverSubType;
        });

        return mockServerSubTypeRepository;
    }

    public static Mock<IServerSubTypeRepository> GetServerSubTypeRepository(List<ServerSubType> serverSubTypes)
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverSubTypes);

        mockServerSubTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => serverSubTypes.SingleOrDefault(x => x.ReferenceId == i));

        return mockServerSubTypeRepository;
    }

    public static Mock<IServerSubTypeRepository> GetServerSubTypeEmptyRepository()
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ServerSubType>());

        return mockServerSubTypeRepository;
    }

    public static Mock<IServerSubTypeRepository> GetPaginatedServerSubTypeRepository(List<ServerSubType> serverSubTypes)
    {
        var mockServerSubTypeRepository = new Mock<IServerSubTypeRepository>();

        var queryableServerSubType = serverSubTypes.BuildMock();

        mockServerSubTypeRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServerSubType);

        return mockServerSubTypeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateServerSubTypeEventRepository(List<UserActivity> userActivities)
    {
        var mockServerSubTypeRepository = new Mock<IUserActivityRepository>();

        mockServerSubTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockServerSubTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockServerSubTypeRepository;
    }
}
