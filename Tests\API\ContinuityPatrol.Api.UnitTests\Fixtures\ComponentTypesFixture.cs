using AutoFixture;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ComponentTypesFixture : IDisposable
{
    public CreateComponentTypeCommand CreateComponentTypeCommand { get; set; }
    public UpdateComponentTypeCommand UpdateComponentTypeCommand { get; set; }
    public List<ComponentTypeListVm> ComponentTypeListVm { get; set; }
    public ComponentTypeDetailVm ComponentTypeDetailVm { get; set; }

    public ComponentTypesFixture()
    {
        CreateComponentTypeCommand = AutoComponentTypesFixture.Create<CreateComponentTypeCommand>();
        UpdateComponentTypeCommand = AutoComponentTypesFixture.Create<UpdateComponentTypeCommand>();
        ComponentTypeListVm = AutoComponentTypesFixture.CreateMany<ComponentTypeListVm>(3).ToList();
        ComponentTypeDetailVm = AutoComponentTypesFixture.Create<ComponentTypeDetailVm>();
    }

    public Fixture AutoComponentTypesFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateComponentTypeCommand>(c => c
                .With(b => b.ComponentName, "Enterprise Database Server")
                .With(b => b.FormTypeId, Guid.NewGuid().ToString)
                .With(b => b.FormTypeName, "Database Form Type")
                .With(b => b.Properties, "{\"type\":\"database\",\"version\":\"2022\",\"edition\":\"enterprise\"}")
                .With(b => b.ComponentProperties, "{\"maxConnections\":1000,\"memoryGB\":64,\"storageGB\":2048}")
                .With(b => b.Logo, "database-server-logo.png")
                .With(b => b.Version, "15.0.2000.5")
                .With(b => b.IsDatabase, true)
                .With(b => b.IsReplication, false)
                .With(b => b.IsServer, true)
                .With(b => b.IsCustom, false));

            fixture.Customize<UpdateComponentTypeCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ComponentName, "Updated Enterprise Database Server")
                .With(b => b.FormTypeId, Guid.NewGuid().ToString)
                .With(b => b.FormTypeName, "Updated Database Form Type")
                .With(b => b.Properties, "{\"type\":\"database\",\"version\":\"2023\",\"edition\":\"enterprise\"}")
                .With(b => b.ComponentProperties, "{\"maxConnections\":1500,\"memoryGB\":128,\"storageGB\":4096}")
                .With(b => b.Logo, "updated-database-server-logo.png")
                .With(b => b.Version, "16.0.1000.6")
                .With(b => b.IsDatabase, true)
                .With(b => b.IsReplication, true)
                .With(b => b.IsServer, true)
                .With(b => b.IsCustom, false));

            fixture.Customize<ComponentTypeListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ComponentName, () => $"Component-{fixture.Create<string>().Substring(0, 8)}")
                .With(b => b.FormTypeId, Guid.NewGuid().ToString)
                .With(b => b.FormTypeName, () => $"FormType-{fixture.Create<string>().Substring(0, 6)}")
                .With(b => b.Properties, () => $"{{\"type\":\"{fixture.Create<string>().Substring(0, 5)}\",\"category\":\"enterprise\"}}")
                .With(b => b.ComponentProperties, () => $"{{\"config\":\"{fixture.Create<string>().Substring(0, 10)}\"}}")
                .With(b => b.Logo, () => $"logo-{fixture.Create<string>().Substring(0, 5)}.png")
                .With(b => b.Version, () => $"{fixture.Create<int>() % 10 + 1}.{fixture.Create<int>() % 10}.{fixture.Create<int>() % 1000}")
                .With(b => b.IsDatabase, () => fixture.Create<bool>())
                .With(b => b.IsReplication, () => fixture.Create<bool>())
                .With(b => b.IsServer, () => fixture.Create<bool>())
                .With(b => b.IsCustom, () => fixture.Create<bool>()));

            fixture.Customize<ComponentTypeDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ComponentName, "Enterprise Application Server")
                .With(b => b.FormTypeId, Guid.NewGuid().ToString)
                .With(b => b.FormTypeName, "Application Server Form Type")
                .With(b => b.Properties, "{\"type\":\"application\",\"framework\":\".NET\",\"version\":\"8.0\"}")
                .With(b => b.ComponentProperties, "{\"threads\":200,\"memoryGB\":32,\"diskGB\":1024}")
                .With(b => b.Logo, "app-server-logo.png")
                .With(b => b.Version, "8.0.100")
                .With(b => b.IsDatabase, false)
                .With(b => b.IsReplication, false)
                .With(b => b.IsServer, true)
                .With(b => b.IsCustom, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
