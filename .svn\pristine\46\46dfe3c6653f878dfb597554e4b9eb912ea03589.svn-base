﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence
{
    public interface IActiveDirectoryMonitorLogRepository
    {
        Task<List<ActiveDirectoryMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
        Task<List<ActiveDirectoryMonitorLog>> GetDetailByType(string type);
    }
}
