﻿namespace ContinuityPatrol.Application.Features.Report.Queries.GetNames;

public class GetReportNameQueryHandler : IRequestHandler<GetReportNameQuery, List<GetReportNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IReportRepository _reportRepository;

    public GetReportNameQueryHandler(IMapper mapper, IReportRepository reportRepository)
    {
        _mapper = mapper;
        _reportRepository = reportRepository;
    }

    public async Task<List<GetReportNameVm>> Handle(GetReportNameQuery request, CancellationToken cancellationToken)
    {
        var reports = await _reportRepository.GetReportNames();

        var reportDto = _mapper.Map<List<GetReportNameVm>>(reports);

        return reportDto;
    }
}