﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Commands;

public class UpdateWorkflowActionTests : IClassFixture<WorkflowActionFixture>, IClassFixture<SolutionHistoryFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private readonly SolutionHistoryFixture _solutionHistoryFixture;

    private readonly Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    private readonly Mock<ISolutionHistoryRepository> _mockSolutionHistoryRepository;

    private readonly UpdateWorkflowActionCommandHandler _handler;

    public UpdateWorkflowActionTests(WorkflowActionFixture workflowActionFixture, SolutionHistoryFixture solutionHistoryFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        _solutionHistoryFixture = solutionHistoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockVersionManager = new Mock<IVersionManager>();

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.UpdateWorkflowActionRepository(_workflowActionFixture.WorkflowActions);

        _mockSolutionHistoryRepository = SolutionHistoryRepositoryMocks.UpdateSolutionHistoryRepository(_solutionHistoryFixture.SolutionHistories);

        _handler = new UpdateWorkflowActionCommandHandler(_workflowActionFixture.Mapper, _mockWorkflowActionRepository.Object, mockPublisher.Object, _mockSolutionHistoryRepository.Object, mockLoggedInUserService.Object, mockVersionManager.Object);
    }

    [Fact]
    public async Task Handle_ValidWorkflowAction_UpdateToWorkflowActionsRepo()
    {
        _workflowActionFixture.UpdateWorkflowActionCommand.Id = _workflowActionFixture.WorkflowActions[0].ReferenceId;

        var result = await _handler.Handle(_workflowActionFixture.UpdateWorkflowActionCommand, CancellationToken.None);

        var workflowAction = await _mockWorkflowActionRepository.Object.GetByReferenceIdAsync(result.WorkflowActionId);

        Assert.Equal(_workflowActionFixture.UpdateWorkflowActionCommand.ActionName, workflowAction.ActionName);
    }

    [Fact]
    public async Task Handle_Return_ValidWorkflowActionResponse_When_WorkflowActionUpdated()
    {
        _workflowActionFixture.UpdateWorkflowActionCommand.Id = _workflowActionFixture.WorkflowActions[0].ReferenceId;

        var result = await _handler.Handle(_workflowActionFixture.UpdateWorkflowActionCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateWorkflowActionResponse));

        result.WorkflowActionId.ShouldBeGreaterThan(0.ToString());

        result.WorkflowActionId.ShouldBe(_workflowActionFixture.UpdateWorkflowActionCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _workflowActionFixture.UpdateWorkflowActionCommand.Id = _workflowActionFixture.WorkflowActions[0].ReferenceId;

        await _handler.Handle(_workflowActionFixture.UpdateWorkflowActionCommand, CancellationToken.None);

        _mockWorkflowActionRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowActionRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowActionId()
    {
        _workflowActionFixture.UpdateWorkflowActionCommand.Id = int.MaxValue.ToString();
        await Assert.ThrowsAsync<NotFoundException>(() =>
        _handler.Handle(_workflowActionFixture.UpdateWorkflowActionCommand, CancellationToken.None));
    }
}