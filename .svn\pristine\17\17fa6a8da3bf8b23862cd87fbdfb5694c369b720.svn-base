﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.PaginatedView;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class SolutionMappingController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<SolutionMappingController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    public SolutionMappingController(IPublisher publisher, IDataProvider dataProvider, ILogger<SolutionMappingController> logger, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in SolutionMapping");

        await _publisher.Publish(new PageSolutionMappingPaginatedEvent());
        return View();
    }

    [AntiXss]
    public async Task<IActionResult> GetPageWidgetList()
    {
        _logger.LogDebug("Entering GetPageWidgetList method in SolutionMapping");

        try
        {
            var pageSolutionList = await _dataProvider.PageSolutionMapping.GetPageSolutionMappingList();
            _logger.LogDebug("Successfully retrieved page widget list in SolutionMapping");
            return Json(new { Success = true, Message = pageSolutionList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occured on solution mapping page while retrieving the page widget list.", ex);
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(PageSolutionMappingViewModel pageSolutionMapping)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in SolutionMapping");

        var pageSolutionId = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;

            if (pageSolutionId.IsNullOrWhiteSpace())
            {
                var pageSolutionModel = _mapper.Map<CreatePageSolutionMappingCommand>(pageSolutionMapping);
                result = await _dataProvider.PageSolutionMapping.CreateAsync(pageSolutionModel);
                _logger.LogDebug($"Creating solution mapping '{pageSolutionModel.Name}'");
            }
            else
            {
                var pageSolutionModel = _mapper.Map<UpdatePageSolutionMappingCommand>(pageSolutionMapping);
                result = await _dataProvider.PageSolutionMapping.UpdateAsync(pageSolutionModel);
                _logger.LogDebug($"Updating solution mapping '{pageSolutionModel.Name}'");
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in SolutionMapping.");
            return Json(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on solution mapping page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on solution mapping page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in SolutionMapping");
        try
        {
            var deleteWidgetPage = await _dataProvider.PageSolutionMapping.DeleteAsync(id);
            TempData.NotifySuccess(deleteWidgetPage.Message);
            _logger.LogDebug("Successfully deleted record in SolutionMapping");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on solution mapping.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [AntiXss]
    public async Task<IActionResult> GetPageBuilderList()
    {
        _logger.LogDebug("Entering GetPageBuilderList method in SolutionMapping");

        try
        {
            var builderList = await _dataProvider.PageBuilder.GetPageBuilderList();
            _logger.LogDebug("Successfully retrieved page builder list in SolutionMapping");
            return Json(new { Success = true, Message = builderList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occured on solution mapping page while retrieving the page builder list.", ex);
            return Json(new { Success = false, Message = "Data not found." });
        }
    }


    [HttpGet]
    public async Task<JsonResult> GetPagination(GetPageSolutionMappingPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in SolutionMapping");

        try
        {
            var nameList = await _dataProvider.PageSolutionMapping.GetPaginatedPageSolutionMapping(query);
            _logger.LogDebug("Successfully retrieved solution mapping paginated list on SolutionMapping");
            return Json(new { success = true, data = nameList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on solution mapping page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<JsonResult> GetTypeByDatabaseIdAndReplicationMasterId(string databaseId, string replicationMasterId, string type)
    {
        _logger.LogDebug("Entering GetTypeByDatabaseIdAndReplicationMasterId method in SolutionMapping");

        try
        {
            var replicationNames = await _dataProvider.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);
            _logger.LogDebug($"Successfully retrieved infra replication mapping by databaseId '{databaseId}' and replicationMasterId '{replicationMasterId}'");
            return Json(new { Success = true, data = replicationNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on solution mapping page while retrieving infra replication mapping by databaseId and replicationMasterId.", ex);
            return ex.GetJsonException();
        }

    }

}

