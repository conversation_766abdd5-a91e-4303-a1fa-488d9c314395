﻿using ContinuityPatrol.Domain.ViewModels.NodeModel;

namespace ContinuityPatrol.Application.Features.Node.Queries.GetNames;

public class GetNodeNameQueryHandler : IRequestHandler<GetNodeNameQuery, List<NodeNameVm>>
{
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;

    public GetNodeNameQueryHandler(IMapper mapper, INodeRepository nodeRepository)
    {
        _mapper = mapper;
        _nodeRepository = nodeRepository;
    }

    public async Task<List<NodeNameVm>> Handle(GetNodeNameQuery request, CancellationToken cancellationToken)
    {
        var node = await _nodeRepository.GetNodeNames();

        return _mapper.Map<List<NodeNameVm>>(node);
    }
}