﻿using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.NodeWorkflowExecution.Validators;

public class UpdateNodeWorkflowExecutionValidatorTests
{
    private readonly Mock<INodeWorkflowExecutionRepository> _mockNodeWorkflowExecutionRepository;

    public UpdateNodeWorkflowExecutionValidatorTests()
    {
        var nodeWorkflowExecutions = new Fixture().Create<List<Domain.Entities.NodeWorkflowExecution>>();

        _mockNodeWorkflowExecutionRepository = NodeWorkflowExecutionRepositoryMocks.UpdateNodeWorkflowExecutionRepository(nodeWorkflowExecutions);
    }

    //WorkflowName

    [Theory]
    [AutoNodeWorkflowExecutionData]
    public async Task Verify_Update_WorkflowName_InNodeWorkflowExecution_WithEmpty(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    {
        var validator = new UpdateNodeWorkflowExecutionCommandValidator();

        updateNodeWorkflowExecutionCommand.WorkflowName = "";

        var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionWorkflowNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeWorkflowExecutionData]

    public async Task Verify_Update_WorkflowName_InNodeWorkflowExecution_IsNull(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    {
        var validator = new UpdateNodeWorkflowExecutionCommandValidator();

        updateNodeWorkflowExecutionCommand.WorkflowName = null;

        var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionWorkflowNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    //ProfileName

    //[Theory]
    //[AutoNodeWorkflowExecutionData]

    //public async Task Verify_Update_ProfileName_InNodeWorkflowExecution_WithEmpty(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    //{
    //    var validator = new UpdateNodeWorkflowExecutionCommandValidator();

    //    updateNodeWorkflowExecutionCommand.ProfileName = "";

    //    var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionProfileNameRequired, validateResult.Errors[0].ErrorMessage);
    //}

    //[Theory]
    //[AutoNodeWorkflowExecutionData]

    //public async Task Verify_Update_ProfileName_InNodeWorkflowExecution_IsNull(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    //{
    //    var validator = new UpdateNodeWorkflowExecutionCommandValidator();

    //    updateNodeWorkflowExecutionCommand.ProfileName = null;

    //    var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionProfileNameNotNullRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //NodeName

    [Theory]
    [AutoNodeWorkflowExecutionData]

    public async Task Verify_Update_NodeName_InNodeWorkflowExecution_WithEmpty(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    {
        var validator = new UpdateNodeWorkflowExecutionCommandValidator();

        updateNodeWorkflowExecutionCommand.NodeName = "";

        var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionNodeNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoNodeWorkflowExecutionData]

    public async Task Verify_Update_NodeName_InNodeWorkflowExecution_IsNull(UpdateNodeWorkflowExecutionCommand updateNodeWorkflowExecutionCommand)
    {
        var validator = new UpdateNodeWorkflowExecutionCommandValidator();

        updateNodeWorkflowExecutionCommand.NodeName = null;

        var validateResult = await validator.ValidateAsync(updateNodeWorkflowExecutionCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.NodeWorkflowExecution.NodeWorkflowExecutionNodeNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }
}