using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Delete;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetByName;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetList;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ComponentTypesControllerTests : IClassFixture<ComponentTypesFixture>
{
    private readonly ComponentTypesFixture _componentTypesFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ComponentTypesController _controller;

    public ComponentTypesControllerTests(ComponentTypesFixture componentTypesFixture)
    {
        _componentTypesFixture = componentTypesFixture;

        var testBuilder = new ControllerTestBuilder<ComponentTypesController>();
        _controller = testBuilder.CreateController(
            _ => new ComponentTypesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetComponentTypes_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetComponentTypeListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_componentTypesFixture.ComponentTypeListVm);

        // Act
        var result = await _controller.GetComponentTypes();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var componentTypes = Assert.IsAssignableFrom<List<ComponentTypeListVm>>(okResult.Value);
        Assert.Equal(3, componentTypes.Count);
    }

    [Fact]
    public async Task GetComponentTypeById_ReturnsExpectedDetail()
    {
        // Arrange
        var componentTypeId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetComponentTypeDetailQuery>(q => q.Id == componentTypeId), default))
            .ReturnsAsync(_componentTypesFixture.ComponentTypeDetailVm);

        // Act
        var result = await _controller.GetComponentTypeById(componentTypeId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var componentType = Assert.IsType<ComponentTypeDetailVm>(okResult.Value);
        Assert.NotNull(componentType);
    }

    [Fact]
    public async Task CreateComponentType_Returns201Created()
    {
        // Arrange
        var command = _componentTypesFixture.CreateComponentTypeCommand;
        var expectedMessage = $"Component Type '{command.ComponentName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateComponentTypeResponse
            {
                Message = expectedMessage,
                ComponentTypeId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateComponentType(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateComponentTypeResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateComponentType_ReturnsOk()
    {
        // Arrange
        var command = _componentTypesFixture.UpdateComponentTypeCommand;
        var expectedMessage = $"Component Type '{command.ComponentName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateComponentTypeResponse
            {
                Message = expectedMessage,
                ComponentTypeId = command.Id
            });

        // Act
        var result = await _controller.UpdateComponentType(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<UpdateComponentTypeResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteComponentType_ReturnsOk()
    {
        // Arrange
        var componentTypeId = Guid.NewGuid().ToString();
        var expectedMessage = "Component Type 'Test Component' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteComponentTypeCommand>(c => c.Id == componentTypeId), default))
            .ReturnsAsync(new DeleteComponentTypeResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteComponentType(componentTypeId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteComponentTypeResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetComponentTypeById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetComponentTypeById("invalid-guid"));
    }

    [Fact]
    public async Task CreateComponentType_ValidatesComponentName()
    {
        // Arrange
        var command = new CreateComponentTypeCommand
        {
            ComponentName = "", // Empty component name should cause validation error
            FormTypeId = Guid.NewGuid().ToString(),
            FormTypeName = "Test Form Type"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("ComponentName is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateComponentType(command));
    }

    [Fact]
    public async Task UpdateComponentType_ValidatesComponentTypeExists()
    {
        // Arrange
        var command = new UpdateComponentTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            ComponentName = "Updated Component",
            FormTypeId = Guid.NewGuid().ToString(),
            FormTypeName = "Updated Form Type"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ComponentType not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateComponentType(command));
    }

    [Fact]
    public async Task CreateComponentType_HandlesComplexComponentProperties()
    {
        // Arrange
        var command = new CreateComponentTypeCommand
        {
            ComponentName = "Enterprise High-Availability Database Cluster",
            FormTypeId = Guid.NewGuid().ToString(),
            FormTypeName = "Enterprise Database Cluster Form",
            Properties = "{\"type\":\"database\",\"edition\":\"enterprise\",\"haEnabled\":true,\"clustering\":\"active-passive\",\"backup\":\"automated\"}",
            ComponentProperties = "{\"maxConnections\":5000,\"memoryGB\":256,\"storageGB\":10240,\"replicationNodes\":3,\"failoverTime\":\"30s\"}",
            Logo = "enterprise-db-cluster-logo.png",
            Version = "2023.1.0.1000",
            IsDatabase = true,
            IsReplication = true,
            IsServer = true,
            IsCustom = false
        };

        var expectedMessage = $"Component Type '{command.ComponentName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateComponentTypeResponse
            {
                Message = expectedMessage,
                ComponentTypeId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateComponentType(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateComponentTypeResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateComponentType_HandlesComponentTypeFlags()
    {
        // Arrange
        var command = new UpdateComponentTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            ComponentName = "Updated Enterprise Application Server",
            FormTypeId = Guid.NewGuid().ToString(),
            FormTypeName = "Updated Application Server Form",
            Properties = "{\"type\":\"application\",\"framework\":\".NET 8\",\"containerized\":true}",
            ComponentProperties = "{\"threads\":500,\"memoryGB\":64,\"diskGB\":2048,\"loadBalanced\":true}",
            Logo = "updated-app-server-logo.png",
            Version = "8.0.200",
            IsDatabase = false,
            IsReplication = false,
            IsServer = true,
            IsCustom = true
        };

        var expectedMessage = $"Component Type '{command.ComponentName}' has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateComponentTypeResponse
            {
                Message = expectedMessage,
                ComponentTypeId = command.Id
            });

        // Act
        var result = await _controller.UpdateComponentType(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<UpdateComponentTypeResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.ComponentTypeId);
    }

    [Fact]
    public async Task DeleteComponentType_HandlesComponentTypeInUse()
    {
        // Arrange
        var componentTypeId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteComponentTypeCommand>(c => c.Id == componentTypeId), default))
            .ThrowsAsync(new InvalidOperationException("Component Type 'Enterprise Database' is currently in use"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.DeleteComponentType(componentTypeId));
    }

    [Fact]
    public async Task CreateComponentType_ValidatesPropertiesFormat()
    {
        // Arrange
        var command = new CreateComponentTypeCommand
        {
            ComponentName = "Test Component",
            FormTypeId = Guid.NewGuid().ToString(),
            FormTypeName = "Test Form Type",
            Properties = "invalid-json-format", // Invalid JSON should cause validation error
            ComponentProperties = "{\"valid\":\"json\"}"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Properties must be valid JSON format"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateComponentType(command));
    }

    [Fact]
    public async Task GetComponentTypeListByName_ReturnsExpectedList()
    {
        // Arrange
        var componentName = "Database";
        var expectedComponentTypes = new List<ComponentTypeModel>
        {
            new ComponentTypeModel { Id = Guid.NewGuid().ToString(), ComponentName = "SQL Server Database" },
            new ComponentTypeModel { Id = Guid.NewGuid().ToString(), ComponentName = "Oracle Database" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetComponentTypeListByNameQuery>(q => q.Name == componentName), default))
            .ReturnsAsync(expectedComponentTypes);

        // Act
        var result = await _controller.GetComponentTypeListByName(componentName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var componentTypes = Assert.IsType<List<ComponentTypeModel>>(okResult.Value);
        Assert.Equal(2, componentTypes.Count);
        Assert.All(componentTypes, ct => Assert.Contains("Database", ct.ComponentName));
    }

    [Fact]
    public async Task GetPaginatedComponentTypes_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetComponentTypePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            FormTypeId = Guid.NewGuid().ToString()
        };

        var expectedData = _componentTypesFixture.ComponentTypeListVm.Take(2).ToList();
        var expectedResults = PaginatedResult<ComponentTypeListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetComponentTypePaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedComponentTypes(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<ComponentTypeListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task IsComponentTypeNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var typeName = "Database Server";
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetComponentTypeNameUniqueQuery>(q =>
                q.Type == typeName && q.Id == componentId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsComponentTypeNameExist(typeName, componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsComponentTypeNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var typeName = "Unique Component Type";
        var componentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetComponentTypeNameUniqueQuery>(q =>
                q.Type == typeName && q.Id == componentId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsComponentTypeNameExist(typeName, componentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }
}
