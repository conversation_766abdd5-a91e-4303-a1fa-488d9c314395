using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DB2HADRMonitorLogRepositoryTests : IClassFixture<DB2HADRMonitorLogFixture>
{
    private readonly DB2HADRMonitorLogFixture _db2HadrMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DB2HADRMonitorLogRespository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public DB2HADRMonitorLogRepositoryTests(DB2HADRMonitorLogFixture db2HadrMonitorLogFixture)
    {
        _db2HadrMonitorLogFixture = db2HadrMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration=ConfigurationRepositoryMocks.GetConnectionString();
        //_mockConfiguration = new Mock<IConfiguration>();

        //// Setup configuration mock
        //_mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("Server=localhost;Database=test;");
        //_mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("mysql");

        _repository = new DB2HADRMonitorLogRespository(_dbContext, _mockConfiguration.Object);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;

        // Act
        var result = await _repository.AddAsync(db2HadrMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HadrMonitorLog.Type, result.Type);
        Assert.Equal(db2HadrMonitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.Db2HadrMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        await _repository.AddAsync(db2HadrMonitorLog);

        db2HadrMonitorLog.Type = "UpdatedType";

        // Act
        var result = await _repository.UpdateAsync(db2HadrMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        await _repository.AddAsync(db2HadrMonitorLog);

        // Act
        var result = await _repository.DeleteAsync(db2HadrMonitorLog);

        // Assert
        Assert.Equal(db2HadrMonitorLog.Type, result.Type);
        Assert.Empty(_dbContext.Db2HadrMonitorLogs);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        var addedEntity = await _repository.AddAsync(db2HadrMonitorLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        await _repository.AddAsync(db2HadrMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(db2HadrMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HadrMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(db2HadrMonitorLog.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList;
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(logs.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnEntitiesWithMatchingType()
    {
        // Arrange
        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList;
        logs.ForEach(x=>x.Type=logs[0].Type);
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType(logs[0].Type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(logs.Count, result.Count);
        Assert.All(result, x => Assert.Equal(logs[0].Type, x.Type));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList;
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDetailByType("non-existent-type");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var activeLogs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList;
        activeLogs.ForEach(x=>x.Type= activeLogs[0].Type);
        var inactiveLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        inactiveLog.IsActive = false;
        inactiveLog.Type = activeLogs[0].Type;

        await _repository.AddRangeAsync(activeLogs);
        _dbContext.Db2HadrMonitorLogs.Add(inactiveLog);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(activeLogs[0].Type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3,result.Count); 
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetDb2HadrMonitorLogByInfraObjectId Tests

    [Fact]
    public async Task GetDb2HadrMonitorLogByInfraObjectId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var db2HadrMonitorLog = _db2HadrMonitorLogFixture.DB2HADRMonitorLogDto;
        await _dbContext.Db2HadrMonitorLogs.AddAsync(db2HadrMonitorLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetDb2HadrMonitorLogByInfraObjectId(db2HadrMonitorLog.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2HadrMonitorLog.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDb2HadrMonitorLogByInfraObjectId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList;
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetDb2HadrMonitorLogByInfraObjectId("8cb642ad-b714-40ca-ba2a-b0bec743bc50");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDb2HadrMonitorLogByInfraObjectId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => 
            _repository.GetDb2HadrMonitorLogByInfraObjectId("invalid-guid"));
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEntitiesInDateRange()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);

        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList.Take(3).ToList();
        logs[0].CreatedDate = startDate.AddDays(1);
        logs[1].CreatedDate = startDate.AddDays(2);
        logs[2].CreatedDate = DateTime.Now.AddDays(-10);

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            DB2HADRMonitorLogFixture.InfraObjectId,
            startDate.ToString("yyyy-MM-dd"),
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(DB2HADRMonitorLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= startDate.Date && x.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenNoEntitiesInRange()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);

        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList.Take(1).ToList();
        logs[0].CreatedDate = DateTime.Now.AddDays(-10);

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            DB2HADRMonitorLogFixture.InfraObjectId,
            startDate.ToString("yyyy-MM-dd"),
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenInfraObjectIdNotFound()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);

        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList.Take(1).ToList();
        logs[0].CreatedDate = startDate.AddDays(1);

        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            "non-existent-infra-id",
            startDate.ToString("yyyy-MM-dd"),
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var logs = _db2HadrMonitorLogFixture.DB2HADRMonitorLogList.Take(3).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(logs);
        var initialCount = logs.Count;

        var toUpdate = logs.Take(2).ToList();
        toUpdate.ForEach(x => x.Type = "UpdatedType");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = logs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Type == "UpdatedType").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
