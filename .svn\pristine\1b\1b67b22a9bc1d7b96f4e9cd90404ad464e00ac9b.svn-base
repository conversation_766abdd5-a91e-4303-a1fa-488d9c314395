using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class ApprovalMatrixApprovalService : BaseService,IApprovalMatrixApprovalService
{
    public ApprovalMatrixApprovalService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<ApprovalMatrixApprovalListVm>> GetApprovalMatrixApprovalList()
    {
        Logger.LogInformation("Get All ApprovalMatrixApprovals");

        return await Mediator.Send(new GetApprovalMatrixApprovalListQuery());
    }

    public async Task<ApprovalMatrixApprovalDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixApproval Id");

        Logger.LogInformation($"Get ApprovalMatrixApproval Detail by Id '{id}'");

        return await Mediator.Send(new GetApprovalMatrixApprovalDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixApprovalCommand createApprovalMatrixApprovalCommand)
    {
        Logger.LogInformation($"Create ApprovalMatrixApproval '{createApprovalMatrixApprovalCommand}'");

        return await Mediator.Send(createApprovalMatrixApprovalCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixApprovalCommand updateApprovalMatrixApprovalCommand)
    {
        Logger.LogInformation($"Update ApprovalMatrixApproval '{updateApprovalMatrixApprovalCommand}'");

        return await Mediator.Send(updateApprovalMatrixApprovalCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixApproval Id");

        Logger.LogInformation($"Delete ApprovalMatrixApproval Details by Id '{id}'");

        return await Mediator.Send(new DeleteApprovalMatrixApprovalCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsApprovalMatrixApprovalNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "ApprovalMatrixApproval Name");

     Logger.LogInformation($"Check Name Exists Detail by ApprovalMatrixApproval Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetApprovalMatrixApprovalNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<ApprovalMatrixApprovalListVm>> GetPaginatedApprovalMatrixApprovals(GetApprovalMatrixApprovalPaginatedListQuery query)
{
    Logger.LogInformation("Get Searching Details in ApprovalMatrixApproval Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
