﻿
<link href="~/lib/jquery-ui/jquery-ui.css" rel="stylesheet" />
<link href="~/css/menu-builder.css" rel="stylesheet" />
<div class="page-content" style="    height: calc(100vh - 65px);">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-navigate-back me-2"></i>Menu Builder
            </h6>
            <form class="d-flex align-items-center">

                @*  <button class="btn btn-primary btn-sm me-2" type="button" id="hide" title="Load Balancer">
                    <i class="cp-table"></i>
                </button> *@
                <div class="btn-group me-2">
                    <button class="btn btn-outline-primary" id="importMenu">
                        <i class="cp-import me-1"></i>Import
                    </button>
                    <button class="btn btn-outline-primary" id="exportMenu">
                        <i class="cp-export me-1"></i>Export
                    </button>
                </div>
               
            </form>

        </div>
        <div class="card-body">
   

    <div class="row">
        <!-- Left Panel - Menu Builder Form -->
        <div class="col-lg-4 col-md-5">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Add Menu Item
                    </h5>
                </div>
                <div class="card-body">
                    <form id="menuItemForm">
                        <!-- Menu Type Selection -->
                        <div class="form-group">
                            <label for="menuType" class="form-label">Menu Type</label>
                            <div class="input-group">
                                <select class="form-select" id="menuType" required>
                                    <option value="">Select Type</option>
                                    <option value="category">Category</option>
                                    <option value="subcategory">Sub-Category</option>
                                    <option value="page">Page Link</option>
                                </select>
                            </div>
                         
                        </div>

                        <!-- Menu Title -->
                        <div class="form-group">
                            <label for="menuTitle" class="form-label">Title</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="menuTitle" placeholder="Enter menu title" required>
                            </div>
                        
                        </div>

                        <!-- Parent Category (for subcategories and pages) -->
                        <div class="form-group" id="parentCategoryGroup" style="display: none;">
                            <label for="parentCategory" class="form-label">Parent Category</label>
                            <div class="input-group">
                                <select class="form-select" id="parentCategory">
                                    <option value="">Select Parent Category</option>
                                </select>
                            </div>
                          
                            <div class="form-text" id="parentCategoryHelp">
                                Choose where to place this item in the menu hierarchy
                            </div>
                        </div>

                        <!-- Page Selection (for page links) -->
                        <div class="form-group" id="pageSelectionGroup" style="display: none;">
                            <label for="pageSearch" class="form-label">Select Page</label>
                            <div class="input-group">
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="pageSearch" placeholder="Search pages...">
                                    <div class="dropdown-menu w-100" id="pageDropdown">
                                        <!-- Pages will be populated here -->
                                    </div>
                                </div>
                            </div>
                          
                            <input type="hidden" id="selectedPageId">
                        </div>

                        <!-- Role Assignment -->
                       @*  <div class="form-group">
                            <label for="menuRoles" class="form-label">Allowed Roles</label>
                            <div class="input-group">
                                <select class="form-select" id="menuRoles" multiple>
                                    <option value="admin">Admin</option>
                                    <option value="manager">Manager</option>
                                    <option value="user">User</option>
                                    <option value="guest">Guest</option>
                                </select>
                            </div>
                           
                            <div class="form-text">Hold Ctrl/Cmd to select multiple roles</div>
                        </div> *@

                        <!-- Icon Selection -->
                        <div class="form-group">
                            <label for="menuIcon" class="form-label">Icon (Font Awesome class)</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="menuIcon" placeholder="e.g., fas fa-home">
                                <div class="form-text">
                                            <a href="#" data-bs-toggle="collapse" data-bs-target="#collapseExample">Browse icons</a>
                                </div>
                            </div>
                                    <div class="collapse" id="collapseExample" style="">
                                        <div class="form-label">Category Icon</div>
                                        <div class="Category_Icon" style="height:calc(50vh - 150px);overflow:auto">
                                            <table class="table table-bordered mb-1">
                                                <tbody>
                                                    <tr>
                                                        <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                                        <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                                        <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                                        <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                                        <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                                        <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                                        <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Goldengate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Firewall" class="cp-firewall custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                                        <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                                        <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                                        <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover"></i></td>
                                                        <td><i title="rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                                        <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                                        <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                                        <td><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                                                        <td><i title="Netapp" class="cp-netapp" cursorshover="true"></i></td>
                                                        <td><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Microsoft" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                                        <td><i title="sun-ilom" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                                        <td><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                                        <td><i title="virtualization" class="cp-virtualization_new custom-cursor-on-hover"></i></td>
                                                        <td><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="vmware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Web" class="cp-web custom-cursor-on-hover"></i></td>
                                                        <td><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                                        <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                                        <td><i title="EMC" class="cp-EMC custom-cursor-on-hover"></i></td>
                                                        <td><i title="oracle-ops" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                                                        <td><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                                                        <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i></td>
                                                        <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                                        <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                                        <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                                        <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                                        <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                                        <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                                        <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                                        <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                                        <td><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                                        <td><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                                        <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                                        <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                                        <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                                        <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                                        <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                                        <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                                        <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                                        <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                                        <td><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                                        <td><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                                                        <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                                        <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                                        <td><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                                                        <td><i title="Mongo DB" class="cp-mongo-db"></i></td>
                                                    </tr>
                                                    <tr>
                                                        <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                                        <td><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                                                        <td><i title="Mainframe" class="cp-server-cloud custom-cursor-on-hover"></i></td>
                                                        <td><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="addMenuItem">
                                <i class="fas fa-plus me-1"></i>Add Menu Item
                            </button>
                            <button type="button" class="btn btn-secondary" id="cancelEdit" style="display: none;">
                                <i class="fas fa-times me-1"></i>Cancel Edit
                            </button>
                        </div>
                    </form>
                </div>
            </div>

           
        </div>

        <!-- Right Panel - Menu Preview -->
        <div class="col-lg-8 col-md-7">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>Menu Preview
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" id="expandAll">
                            <i class="fas fa-expand-alt me-1"></i>Expand All
                        </button>
                        <button class="btn btn-outline-secondary" id="collapseAll">
                            <i class="fas fa-compress-alt me-1"></i>Collapse All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="menuPreview" class="menu-container">
                        <div class="text-muted text-center py-5">
                            <i class="fas fa-bars fa-3x mb-3"></i>
                            <p>No menu items yet. Add your first menu item to get started.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    </div>
</div>
<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Import Menu Structure
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="menuFileInput" class="form-label">Select JSON File</label>
                    <input type="file" class="form-control" id="menuFileInput" accept=".json">
                    <div class="form-text">
                        Upload a JSON file containing menu structure data. This will replace your current menu.
                    </div>
                </div>

                <div id="importPreview" class="mt-3" style="display: none;">
                    <h6>Preview:</h6>
                    <div class="border rounded p-3 bg-light">
                        <pre id="importPreviewContent" class="mb-0" style="max-height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>

                <div id="importError" class="alert alert-danger mt-3" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="importErrorMessage"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmImport" disabled>
                    <i class="fas fa-check me-1"></i>Import Menu
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Menu Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Edit form will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for import -->
<input type="file" id="hiddenFileInput" accept=".json" style="display: none;">

<!-- Live Menu Preview Modal -->
<div class="modal fade" id="liveMenuModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-play me-2"></i>Live Menu Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <!-- Role Selector -->
                <div class="bg-light p-3 border-bottom">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <label for="previewRole" class="form-label mb-0">
                                <i class="fas fa-user me-1"></i>Preview as Role:
                            </label>
                            <select class="form-select form-select-sm d-inline-block w-auto ms-2" id="previewRole">
                                <option value="admin">Admin</option>
                                <option value="manager">Manager</option>
                                <option value="user">User</option>
                                <option value="guest">Guest</option>
                            </select>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-sm btn-outline-primary" id="refreshPreview">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Live Navigation Bar -->
                <div id="liveNavContainer">
                    <!-- Navigation will be rendered here -->
                </div>

                <!-- Sample Page Content -->
                <div class="container-fluid py-4">
                    <div id="pageContent">
                        <div class="text-center py-5">
                            <i class="fas fa-mouse-pointer fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Click on menu items to see page content</h4>
                            <p class="text-muted">This demonstrates how your menu would work in a real application</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close Preview</button>
                <button type="button" class="btn btn-primary" id="openInNewTab">
                    <i class="fas fa-external-link-alt me-1"></i>Open in New Tab
                </button>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/jquery-ui/jquery-ui.min.js"></script>
<script src="~/js/menu-builder/data.js"></script>
<script src="~/js/menu-builder/menu-builder.js"></script>