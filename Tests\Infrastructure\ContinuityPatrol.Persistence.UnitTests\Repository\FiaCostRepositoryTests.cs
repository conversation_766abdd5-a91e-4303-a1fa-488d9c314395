using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FiaCostRepositoryTests : IClassFixture<FiaCostFixture>, IDisposable
{
    private readonly FiaCostFixture _fiaCostFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FiaCostRepository _repository;

    public FiaCostRepositoryTests(FiaCostFixture fiaCostFixture)
    {
        _fiaCostFixture = fiaCostFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FiaCostRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region GetByBusinessFunctionId Tests

    [Fact]
    public async Task GetByBusinessFunctionId_ReturnsFiaCost_WhenBusinessFunctionIdExists()
    {
        // Arrange
        var businessFunctionId = "BF_001";
        _fiaCostFixture.FiaCostDto.BusinessFunctionId = businessFunctionId;
        _fiaCostFixture.FiaCostDto.IsActive = true;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctionId, result.BusinessFunctionId);
    }

    [Fact]
    public async Task GetByBusinessFunctionId_ReturnsNull_WhenBusinessFunctionIdDoesNotExist()
    {
        // Arrange
        var nonExistentBusinessFunctionId = "NON_EXISTENT_BF";

        _fiaCostFixture.FiaCostDto.BusinessFunctionId = "BF_001";
        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessFunctionId(nonExistentBusinessFunctionId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByBusinessFunctionId_ReturnsNull_WhenFiaCostIsInactive()
    {
        // Arrange
        var businessFunctionId = "BF_002";
        _fiaCostFixture.FiaCostDto.BusinessFunctionId = businessFunctionId;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Manually set as inactive after saving
        _fiaCostFixture.FiaCostDto.IsActive = false;
        _dbContext.FiaCosts.Update(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByBusinessFunctionId_ReturnsFirstMatch_WhenMultipleActiveRecordsExist()
    {
        // Arrange
        var businessFunctionId = "BF_003";

        var fiaCost1 = new Domain.Entities.FiaCost
        {
            BusinessFunctionId = businessFunctionId,
            BusinessFunctionName = "Function 1",
            ReferenceId = Guid.NewGuid().ToString()
        };

        var fiaCost2 = new Domain.Entities.FiaCost
        {
            BusinessFunctionId = businessFunctionId,
            BusinessFunctionName = "Function 2",
            ReferenceId = Guid.NewGuid().ToString()
        };

        await _dbContext.FiaCosts.AddRangeAsync(new[] { fiaCost1, fiaCost2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctionId, result.BusinessFunctionId);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var businessFunctionName = "Test Function";
        _fiaCostFixture.FiaCostDto.BusinessFunctionName = businessFunctionName;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(businessFunctionName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Function";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var businessFunctionName = "Existing Function";

        _fiaCostFixture.FiaCostDto.ReferenceId = existingId;
        _fiaCostFixture.FiaCostDto.BusinessFunctionName = businessFunctionName;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(businessFunctionName, existingId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var differentId = Guid.NewGuid().ToString();
        var businessFunctionName = "Duplicate Function";

        _fiaCostFixture.FiaCostDto.BusinessFunctionName = businessFunctionName;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(businessFunctionName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        // Arrange
        var invalidId = "not-a-valid-guid";
        var businessFunctionName = "Function With Invalid ID";

        _fiaCostFixture.FiaCostDto.BusinessFunctionName = businessFunctionName;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(businessFunctionName, invalidId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        // Arrange
        var businessFunctionName = "Case Sensitive Function";
        _fiaCostFixture.FiaCostDto.BusinessFunctionName = businessFunctionName;

        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("CASE SENSITIVE FUNCTION", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyString()
    {
        // Act
        var result = await _repository.IsNameExist("", null);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFiaCost_WhenValidFiaCost()
    {
        // Arrange
        var fiaCost = _fiaCostFixture.FiaCostDto;
        fiaCost.BusinessFunctionName = "Test Business Function";

        // Act
        var result = await _repository.AddAsync(fiaCost);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(fiaCost.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Single(_dbContext.FiaCosts);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenFiaCostIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsFiaCost_WhenExists()
    {
        // Arrange
        _fiaCostFixture.FiaCostDto.Id = 1;
        await _dbContext.FiaCosts.AddAsync(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fiaCostFixture.FiaCostDto.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateFiaCost_WhenValidFiaCost()
    {
        // Arrange
        _dbContext.FiaCosts.Add(_fiaCostFixture.FiaCostDto);
        await _dbContext.SaveChangesAsync();

        _fiaCostFixture.FiaCostDto.BusinessFunctionName = "Updated Function Name";

        // Act
        var result = await _repository.UpdateAsync(_fiaCostFixture.FiaCostDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Function Name", result.BusinessFunctionName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenFiaCostIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion
}
