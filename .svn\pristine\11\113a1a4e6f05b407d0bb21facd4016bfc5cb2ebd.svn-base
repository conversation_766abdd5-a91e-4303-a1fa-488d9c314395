﻿let lastLogIdArray = []

$(async function () {
    configurationURL = $('#Chat_Bot').attr('signalRurl');

    let url = configurationURL + "loghub";
    let connection = new signalR.HubConnectionBuilder()
        .withUrl(url, {
            skipNegotiation: true,
            rejectUnauthorized: false,
            transport: signalR.HttpTransportType.WebSockets
        })
        .configureLogging(signalR.LogLevel.Information)
        .build();

    async function startLogViewSignalRConnection() {
        try {
            await connection.start();
            await connection.on("logevent", (message) => {
                if (message && message.length) {
                    let activeGroupId = $('.runningWorkflowContainer.Active-Card').attr('operationgroupid')
                    if (!lastLogIdArray.includes(message[0].arrived) && activeGroupId == message[0]?.workflowOperationGroupId) {
                        if ($('#workflowExecutionLogViewer').is(':visible')) {
                            bindingLogInModal(message);
                        } else {
                            BindingLogEvent(message);
                        }
                    }
                }
            });
            console.log("LogHub Initiated.");
        } catch (err) {
            console.log("SignalR DisConnected from LogHub.");
            setTimeout(startLogViewSignalRConnection, 8000);
        }
    };

    connection.onclose(async () => {
        await new Promise(resolve => setTimeout(resolve, 5001));
        await startLogViewSignalRConnection();
    });

   // startLogViewSignalRConnection();

    $("#logDownload").on('click', (e) => {
        if ($('#workflowLogViewerModal').text() && $('#workflowLogViewerModal').text().length) {
            let text = $('#workflowLogViewerModal').text().replace(/[ \t]+/g, "");
            let blob = new Blob([text], { type: "text/plain" });
            let link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            let workflowId = $('.runningWorkflowContainer.Active-Card').attr('id')
            let workflowName = $(`#${workflowId} .workflowTextContainer`).text()
            link.download = workflowName + '_' + new Date().toLocaleDateString().split('/').reverse().join('') + '_' + new Date().toLocaleTimeString().split(':')[0] + '-' + new Date().toLocaleTimeString().split(':')[1] + ".txt";
            document.body.appendChild(link);
            link.click();
        }
    })

    $("#workflowFullScreenLogViewer").on('click', async function () {
        let activeGroupId = $('.runningWorkflowContainer.Active-Card').attr('operationgroupid')
        if (activeGroupId) {
            let data = {
                groupId: activeGroupId,
                isFilter: false
            }
            LogViewData(data, 'workflowLogViewerModal');
        }
    })

    $('#btnLogViewModalClose').on('click', function () {
        $("#workflowExecutionLogViewer").modal("hide")
    })

    $('.scrollUp').on('click', function () {
        scrollToUp();
    })

    $('.scrollDown').on('click', function () {
        scrollToBottom();
    })

    //$('.highlightSpan').on('click', function () {

    //    if (spans && currentIndex < spans?.length) {
    //        spans.removeClass('highlight'); // Remove highlight from all spans
    //        $(spans[currentIndex]).addClass('highlight'); // Highlight the current span
    //        let container = $('.scrollStatic');
    //        let scrollTo = $(spans[currentIndex])

    //        // Calculating new position of scrollbar 
    //        let position = scrollTo.offset().top
    //            - container.offset().top
    //            + container.scrollTop();

    //        // Setting the value of scrollbar 
    //        container.animate({
    //            scrollTop: position
    //        });
    //        currentIndex++; // Move to the next index
    //    } else {
    //        currentIndex = 0 // Optional: Message when no more spans are left

    //        if (spans) {
    //            spans.removeClass('highlight'); // Remove highlight from all spans
    //            $(spans[currentIndex]).addClass('highlight'); // Highlight the current span
    //            let container = $('.scrollStatic');
    //            let scrollTo = $(spans[currentIndex])

    //            // Calculating new position of scrollbar 
    //            let position = scrollTo.offset().top
    //                - container.offset().top
    //                + container.scrollTop();

    //            // Setting the value of scrollbar 
    //            container.animate({
    //                scrollTop: position
    //            });
    //            currentIndex++;
    //        }

    //    }
    //});
})

let currentErrorIndex = 0
$('#btnFindLogViewError').on('click', function () {
    let logErrorContainer = $('#workflowLogViewerModal span.text-danger')
    logErrorContainer.removeClass('highlight')
    logErrorContainer.eq(currentErrorIndex).addClass('highlight')[0].scrollIntoView()

    if ($('#workflowLogViewerModal span.text-danger').length - 1 === currentErrorIndex) {
        currentErrorIndex = 0
    } else {
        currentErrorIndex++;
    }
})
$("#searchLogViewer").on("keyup", function () {
    var filter = $(this).val();
    $("#workflowLogViewerModal").children().each(function () {
        var $i = 0;
        $(this).find("span").each(function () {
            var splitText = $(this).text()
            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });

        if ($i > 0) {
            $(this).closest("span").show();
        } else {
            $(this).closest("span").hide();
        }

    });

})

//document.getElementById("search-logViewer").addEventListener("search", function (event) {
//    $("#LogViewZoomData span").removeAttr("style")
//});


async function LogViewData(data, logId) {

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getLogsByGroupId,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result) {
                if (result && Array.isArray(result) && result?.length) {
                    if (result[0]?.workflowOperationGroupId == data?.groupId) {
                        if (logId === 'workflowLogViewer') {
                            if (!lastLogIdArray.includes(result[0]?.arrived)) {
                                BindingLogEvent(result);
                            }
                        } else {
                            bindingLogInModal(result)
                        }
                    }
                } else {
                    if (logId === 'workflowLogViewer') {
                        $('#workflowLogViewer').empty().append(noDataImages.logView)
                    } else {
                        $('#workflowLogViewerModal').empty().append("<img src='../../img/isomatric/Log_Viewer_No_Data_Found.svg' class='Card_NoData_ImgExeExe' style='width:430px;margin-left:410px;margin-top:90px;margin-bottom:80px;'>")
                        $('#btnsLogContainer').addClass('d-none')
                        setTimeout(() => {
                            $("#workflowExecutionLogViewer").modal("show")
                        }, 200)
                    }
                }
            }
        }
    })
}

const bindingLogInModal = (data) => {
    $('#btnsLogContainer').removeClass('d-none')
    $(`#workflowLogViewerModal`).find('img').remove()
    lastLogIdArray = []
    let length = data.length;
    let modalHtml = ''
    for (let i = 0; i < length; i++) {
        let logEntry = data[i];
        let logType = logEntry.level.toLowerCase();
        let logLevel = logType == 'information' ? 'text-success' : logType == 'warning' ? 'text-warning' : logType == 'error' ? 'text-danger' : logType == 'debug' ? 'text-primary' : 'text-secondary';
        if (!logEntry?.message.includes('<html>') || !logEntry?.message.includes('<body>') || !logEntry?.message.includes('<div>') || !logEntry?.message.includes('<image')) {
            modalHtml += `<div class='d-flex flex-row p-1'>
            <span class=''>${logEntry?.timestamp} : </span> <span class='${logLevel} ms-2'>${logEntry?.level} : </span> <section class='ms-2'> ${JSON.stringify(logEntry?.message)}</span>
            </div>`
            lastLogIdArray.push(logEntry.arrived)
        }
       
    }
    $('#workflowLogViewerModal').empty().html(modalHtml)
    setTimeout(() => {
        $("#workflowExecutionLogViewer").modal("show")
    }, 200)
}

function BindingLogEvent(message) {
    $('#workflowLogViewer').empty().append(noDataImages.logView)
    lastLogIdArray = [];
    let length = message.length;
    let LogContainer = '';
    for (let i = 0; i < length; i++) {
        let logEntry = message[i];
        let logType = logEntry.level.toLowerCase();

        let logLevel = logType == 'information' ? 'text-success' : logType == 'warning' ? 'text-warning' : logType == 'error' ? 'text-danger' : logType == 'debug' ? 'text-primary' : 'text-secondary';

        LogContainer += `<div>
                <span>${logEntry.timestamp}</span> : <span class='${logLevel}'>${logEntry.level}</span> : <span>${logEntry.message}</span>
            </div>`
        lastLogIdArray.push(logEntry.arrived)
    }

    setTimeout(() => {
        $(`#workflowLogViewer`).find('img').remove()
        $(`#workflowLogViewer`).append(LogContainer)
        $('.autoscroll').animate({ scrollTop: $('.autoscroll').prop("scrollHeight") }, 'slow')
    }, 300)
}

const scrollToBottom = () => {
    const $scrollStatic = $(".scrollStatic");
    $scrollStatic.animate({ scrollTop: $scrollStatic.prop("scrollHeight") }, 700);
};

const scrollToUp = () => {
    $(".scrollStatic").animate({ scrollTop: 0 }, 700);
};

$('#btnLogStateUpdate').on('click', async function () {
    let workflowId = $('.runningWorkflowContainer.Active-Card').attr('id')
    let activeGroupId = $(`#${workflowId}`).attr('operationGroupId')
    let workflowName = $(`#${workflowId} .workflowTextContainer`).text();

    let updateStatus = {
        'id': activeGroupId,
        'workflowName': workflowName,
        'isLog': true
    }

    if ($('#logViewerToggle').hasClass('text-danger')) {
        updateStatus.isLog = false;
    }

    await updateWorkflowLogStatus(updateStatus)
})

$('#logViewerToggle').on('click', async function (e) {
    e.stopPropagation();
    let workflowId = $('.runningWorkflowContainer.Active-Card').attr('id')
    let workflowName = $(`#${workflowId} .workflowTextContainer`).text();
    if ($(this).hasClass('text-danger')) {
        //updateStatus.isLog = false;
        //lastLogIdArray = [];
        //$('#workflowLogViewer').empty().html(noDataImages.logView);
        //await LogViewData(data, 'workflowLogViewer');
        $('#textLogState').text('disable')
    } else {
        $('#textLogState').text('enable')
    }

    $('#updateLogWorkflowName').text(workflowName)
    $('#updateLogStateModal').modal('show')
})

const updateWorkflowLogStatus = async (data) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.updateLogStatus,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (data.isLog) {
                    $('#logViewerToggle').removeClass('text-success').addClass('text-danger').attr('title', 'Disable')
                } else {
                    $('#logViewerToggle').removeClass('text-danger').addClass('text-success').attr('title', 'Enable')
                }
                notificationAlert('success', result.data.message)
               
            } else {
                errorNotification(result)
            }
        }
    })
    $('#updateLogStateModal').modal('hide')
}