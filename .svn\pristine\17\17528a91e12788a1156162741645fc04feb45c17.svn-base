﻿namespace ContinuityPatrol.Application.Features.HeatMapLog.Commands.Update;

public class UpdateHeatMapLogCommandHandler : IRequestHandler<UpdateHeatMapLogCommand, UpdateHeatMapLogResponse>
{
    private readonly IHeatMapLogRepository _heatMapLogRepository;
    private readonly IMapper _mapper;

    public UpdateHeatMapLogCommandHandler(IMapper mapper, IHeatMapLogRepository heatMapLogRepository)
    {
        _mapper = mapper;
        _heatMapLogRepository = heatMapLogRepository;
    }

    public async Task<UpdateHeatMapLogResponse> Handle(UpdateHeatMapLogCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _heatMapLogRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.HeatMapLog), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateHeatMapLogCommand), typeof(Domain.Entities.HeatMapLog));

        await _heatMapLogRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateHeatMapLogResponse
        {
            Message = Message.Update(nameof(Domain.Entities.HeatMapLog), eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}