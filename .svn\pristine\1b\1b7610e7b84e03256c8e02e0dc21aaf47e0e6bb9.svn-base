﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowIdUnique;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowProfileInfoByProfileId;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class WorkflowProfileInfoController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowProfileInfoListVm>>> GetWorkflowProfileInfoList()
    {
        Logger.LogDebug("Get All WorkflowProfileInfoList");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoListQuery()));
    }

    [HttpGet("{id}", Name = "GetWorkflowProfileInfo")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<WorkflowProfileInfoDetailVm>> GetWorkflowProfileInfoById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowProfileInfo Id");

        Logger.LogDebug($"Get WorkflowProfileInfo Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoDetailQuery { Id = id }));
    }

    [HttpGet]
    [Route("by/{profileId}")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GetWorkflowProfileInfoByProfileIdVm>>> GetWorkflowProfileInfoByProfileId(
        string profileId)
    {
        Logger.LogDebug($"Get WorkflowProfileInfo Detail by Profile Id '{profileId}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoByProfileIdQuery { ProfileId = profileId }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateWorkflowProfileInfoResponse>> CreateWorkflowProfileInfo(
        [FromBody] CreateWorkflowProfileInfoCommand createWorkflowProfileInfoCommand)
    {
        Logger.LogDebug($"Create WorkflowProfileInfo '{createWorkflowProfileInfoCommand.ProfileName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateWorkflowProfileInfo),await Mediator.Send(createWorkflowProfileInfoCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateWorkflowProfileInfoResponse>> UpdateWorkflowProfileInfo(
        [FromBody] UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfoCommand)
    {
        Logger.LogDebug($"Update WorkflowProfileInfo '{updateWorkflowProfileInfoCommand.ProfileName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateWorkflowProfileInfoCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteWorkflowProfileInfoResponse>> DeleteWorkflowProfileInfo(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowProfileInfo Id");

        Logger.LogDebug($"Delete WorkflowProfileInfo Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteWorkflowProfileInfoCommand { Id = id }));
    }

    [HttpGet]
    [Route("names")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<WorkflowProfileInfoNameVm>>> WorkflowProfileInfoNames()
    {
        Logger.LogDebug("Get All WorkflowProfileInfo Names");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoNameQuery()));
    }

    [Route("name-exist")]
    [HttpGet]
    public async Task<ActionResult> IsWorkflowProfileNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowProfileInfo Name");

        Logger.LogDebug($"Check Name Exists Detail by WorkflowProfileInfo Name '{name}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoNameUniqueQuery { WorkflowProfileName = name, WorkflowProfileId = id }));
    }

    [Route("workflowid-exist")]
    [HttpGet]
    public async Task<ActionResult> IsWorkflowIdExist(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Check Workflow Id '{workflowId}'");

        return Ok(await Mediator.Send(new GetWorkflowProfileInfoByWorkflowIdUniqueQuery { WorkflowId = workflowId }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<WorkflowProfileInfoListVm>>> GetPaginatedWorkflowProfileInfos([FromQuery] GetWorkflowProfileInfoPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowProfileInfo Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =
        {
            ApplicationConstants.Cache.AllWorkflowProfileInfosCacheKey + LoggedInUserService.CompanyId,
            ApplicationConstants.Cache.AllWorkflowProfileInfosNameCacheKey
        };

        ClearCache(cacheKeys);
    }
}