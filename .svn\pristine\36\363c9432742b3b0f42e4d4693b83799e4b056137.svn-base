﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Infrastructure.Extensions;

public static class ExceptionExtension
{
    public static void Exception(this Serilog.ILogger logger, string message, Exception exception)
    {
        message += $" Exception : {exception.Message}";

        if (exception.InnerException != null)
        {
            message += $" Inner Exception : {exception.InnerException.Message}";
        }
        logger.Error(message);
    }

    public static void Exception(this Microsoft.Extensions.Logging.ILogger logger, string message, Exception exception)
    {
        message += $" Exception : {exception.Message}";

        if (exception.InnerException != null)
        {
            message += $" Inner Exception : {exception.InnerException.Message}";
        }
        logger.LogError(message);
    }
    public static string GetMessage(this Exception exception)
    {
        var errorMessage = exception.Message;

        if (exception.InnerException != null)
        {
            errorMessage += $"Inner Exception : {exception.InnerException.Message}";
        }
        return errorMessage;
    }

    public static JsonResult GetJsonException(this Exception exception)
    {
        var errorData = exception switch
        {
            ValidationException ex => new BaseResponse{ Success = false, Message = ex.ValidationErrors.FirstOrDefault(), ErrorCode = 0 },
            AuthenticationException exe => new BaseResponse { Success = false, Message = exe.Message, ErrorCode = 1001 },
            Exception e => new BaseResponse { Success = false, Message = e.GetMessage(), ErrorCode = 0 },
            _ => null
        };

        return new JsonResult(errorData ?? new BaseResponse());
    }
}

