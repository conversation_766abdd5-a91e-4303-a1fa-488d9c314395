﻿using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;

namespace ContinuityPatrol.Application.UnitTests.Features.UserInfraObject.Queries
{
    public class GetByUserIdAndPropertiesQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IUserInfraObjectRepository> _mockUserInfraObjectRepository;
        private readonly GetByUserIdAndPropertiesQueryHandler _handler;

        public GetByUserIdAndPropertiesQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();
            _handler = new GetByUserIdAndPropertiesQueryHandler(_mockUserInfraObjectRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenDataExists()
        {
            var userId = Guid.NewGuid().ToString();
            var query = new GetByUserIdAndPropertiesQuery { UserId = userId };

            var userInfraObjects = new List<Domain.Entities.UserInfraObject>
            {
                new Domain.Entities.UserInfraObject { UserId = userId, Properties = "Value1" },
                new Domain.Entities.UserInfraObject { UserId = userId, Properties = "Value2" }
            };

            var expectedVm = new List<GetByUserIdAndPropertiesVm>
            {
                new GetByUserIdAndPropertiesVm { UserId = userId },
                new GetByUserIdAndPropertiesVm { Properties = "Value2" }
            };

            _mockUserInfraObjectRepository
                .Setup(repo => repo.GetByUserIdAndProperties())
                .ReturnsAsync(userInfraObjects);

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects))
                .Returns(expectedVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(expectedVm[0].Properties, result[0].Properties);
            Assert.Equal(expectedVm[1].Properties, result[1].Properties);

            _mockUserInfraObjectRepository.Verify(repo => repo.GetByUserIdAndProperties(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoDataFound()
        {
            var userId = Guid.NewGuid().ToString();
            var query = new GetByUserIdAndPropertiesQuery { UserId = userId };

            var userInfraObjects = new List<Domain.Entities.UserInfraObject>();

            var expectedVm = new List<GetByUserIdAndPropertiesVm>();

            _mockUserInfraObjectRepository
                .Setup(repo => repo.GetByUserIdAndProperties())
                .ReturnsAsync(userInfraObjects);

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects))
                .Returns(expectedVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockUserInfraObjectRepository.Verify(repo => repo.GetByUserIdAndProperties(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAndMapperOnce_WhenCalled()
        {
            var query = new GetByUserIdAndPropertiesQuery { UserId = Guid.NewGuid().ToString() };

            var userInfraObjects = new List<Domain.Entities.UserInfraObject>
            {
                new Domain.Entities.UserInfraObject { UserId = Guid.NewGuid().ToString(), Properties = "Value" }
            };

            var expectedVm = new List<GetByUserIdAndPropertiesVm>
            {
                new GetByUserIdAndPropertiesVm { Properties = "Value" }
            };

            _mockUserInfraObjectRepository
                .Setup(repo => repo.GetByUserIdAndProperties())
                .ReturnsAsync(userInfraObjects);

            _mockMapper
                .Setup(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects))
                .Returns(expectedVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            _mockUserInfraObjectRepository.Verify(repo => repo.GetByUserIdAndProperties(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<GetByUserIdAndPropertiesVm>>(userInfraObjects), Times.Once);
        }
    }
}
