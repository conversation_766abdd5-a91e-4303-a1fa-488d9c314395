﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ReplicationRepositoryMocks
{
    public static Mock<IReplicationRepository> CreateReplicationRepository(List<Replication> replications)
    {
        var mockReplicationRepository = new Mock<IReplicationRepository>();

        mockReplicationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replications);

        mockReplicationRepository.Setup(repo => repo.AddAsync(It.IsAny<Replication>())).ReturnsAsync((Replication replication) =>
        {
            replication.Id = new Fixture().Create<int>();

            replication.ReferenceId = new Fixture().Create<Guid>().ToString();

            replications.Add(replication);

            return replication;
        });
        return mockReplicationRepository;
    }

    public static Mock<IReplicationRepository> DeleteReplicationRepository(List<Replication> replications)
    {
        var mockReplicationRepository = new Mock<IReplicationRepository>();

        mockReplicationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replications);

        mockReplicationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replications.SingleOrDefault(x => x.ReferenceId == i));

      //  mockReplicationRepository.Setup(repo => repo.GetReplicationByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(replications);

        mockReplicationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Replication>())).ReturnsAsync((Replication replication) =>
        {
            var index = replications.FindIndex(item => item.Id == replication.Id);

            replication.IsActive = false;

            replications[index] = replication;

            return replication;
        });
        return mockReplicationRepository;
    }
    public static Mock<IReplicationRepository> UpdateReplicationRepository(List<Replication> replications)
    {
        var mockReplicationRepository = new Mock<IReplicationRepository>();

        mockReplicationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replications);

        mockReplicationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replications.SingleOrDefault(x => x.ReferenceId == i));

        mockReplicationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Replication>())).ReturnsAsync((Replication replication) =>
        {
            var index = replications.FindIndex(item => item.Id == replication.Id);

            replications[index] = replication;

            return replication;
        });

        return mockReplicationRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationRepository(List<Replication> replications)
    {
        var replicationRepository = new Mock<IReplicationRepository>();

        replicationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replications);

        replicationRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(replications);

        replicationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replications.SingleOrDefault(x => x.ReferenceId == i));

        return replicationRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationNameRepository(List<Replication> replications)
    {
        var replicationNameRepository = new Mock<IReplicationRepository>();

        replicationNameRepository.Setup(repo => repo.GetReplicationNames()).ReturnsAsync(replications);

        return replicationNameRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationNameUniqueRepository(List<Replication> replications)
    {
        var replicationNameUniqueRepository = new Mock<IReplicationRepository>();

        replicationNameUniqueRepository.Setup(repo => repo.IsReplicationNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => replications.Exists(x => x.Name == i && x.ReferenceId == j));

        return replicationNameUniqueRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationEmptyRepository()
    {
        var mockReplicationRepository = new Mock<IReplicationRepository>();

        mockReplicationRepository.Setup(repo => repo.GetType(It.IsAny<string>())).ReturnsAsync(new List<Replication>());

        mockReplicationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Replication>());

        mockReplicationRepository.Setup(repo => repo.GetReplicationNames()).ReturnsAsync(new List<Replication>());

        mockReplicationRepository.Setup(repo => repo.GetReplicationByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<Replication>());

        return mockReplicationRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationListByLicenseKey(List<Replication> replications)
    {
        var mockReplicationRepository = new Mock<IReplicationRepository>();

        mockReplicationRepository.Setup(repo => repo.GetReplicationListByLicenseKey(It.IsAny<string>())).ReturnsAsync(replications);

        return mockReplicationRepository;
    }

    public static Mock<IReplicationRepository> GetPaginatedReplicationRepository(List<Replication> replications)
    {
        var replicationRepository = new Mock<IReplicationRepository>();

        var queryableReplication = replications.BuildMock();

        replicationRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableReplication);

        return replicationRepository;
    }

    public static Mock<IReplicationRepository> GetReplicationTypeRepository(List<Replication> replications)
    {
        var replicationRepository = new Mock<IReplicationRepository>();

        var queryableReplication = replications.BuildMock();

        replicationRepository.Setup(repo => repo.GetReplicationByType(It.IsAny<string>())).Returns(queryableReplication);

        return replicationRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateReplicationEventRepository(List<UserActivity> userActivities)
    {
        var mockReplicationEventRepository = new Mock<IUserActivityRepository>();

        mockReplicationEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockReplicationEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
        {
            userActivity.Id = new Fixture().Create<int>();

            userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

            userActivities.Add(userActivity);

            return userActivity;
        });
        return mockReplicationEventRepository;
    }
}