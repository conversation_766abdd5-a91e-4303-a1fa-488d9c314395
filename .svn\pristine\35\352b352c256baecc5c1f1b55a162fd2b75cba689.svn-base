using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectViewFixture : IDisposable
{
    public List<InfraObjectView> InfraObjectViewPaginationList { get; set; }
    public List<InfraObjectView> InfraObjectViewList { get; set; }
    public InfraObjectView InfraObjectViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string BusinessFunctionId = "BF_123";
    public const string InfraObjectName = "Test Infrastructure Object";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectViewFixture()
    {
        var fixture = new Fixture();

        InfraObjectViewList = fixture.Create<List<InfraObjectView>>();

        InfraObjectViewPaginationList = fixture.CreateMany<InfraObjectView>(20).ToList();

        // Setup proper test data for InfraObjectViewPaginationList
        InfraObjectViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectViewPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectViewPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectViewPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraObjectViewPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);

        // Setup proper test data for InfraObjectViewList
        InfraObjectViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectViewList.ForEach(x => x.IsActive = true);
        InfraObjectViewList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectViewList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraObjectViewList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);

        InfraObjectViewDto = fixture.Create<InfraObjectView>();
        InfraObjectViewDto.ReferenceId = Guid.NewGuid().ToString();
        InfraObjectViewDto.IsActive = true;
        InfraObjectViewDto.Name = InfraObjectName;
        InfraObjectViewDto.Description = "Test infrastructure object description";
        InfraObjectViewDto.CompanyId = CompanyId;
        InfraObjectViewDto.BusinessServiceId = BusinessServiceId;
        InfraObjectViewDto.BusinessServiceName = "Test Business Service";
        InfraObjectViewDto.BusinessFunctionId = BusinessFunctionId;
        InfraObjectViewDto.BusinessFunctionName = "Test Business Function";
        InfraObjectViewDto.Type = 1;
        InfraObjectViewDto.SubType = "Windows Server";
        InfraObjectViewDto.DRReady = true;
        InfraObjectViewDto.NearDR = false;
        InfraObjectViewDto.RecoveryType = 1;
        InfraObjectViewDto.ServerProperties = "Server123";
        InfraObjectViewDto.DatabaseProperties = "Database456";
        InfraObjectViewDto.ReplicationProperties = "Replication789";
        InfraObjectViewDto.Priority = "High";
        InfraObjectViewDto.State = "Active";
        InfraObjectViewDto.ReplicationStatus = 1;
        InfraObjectViewDto.DROperationStatus = 1;
        InfraObjectViewDto.IsPair = false;
        InfraObjectViewDto.IsDrift = false;
        InfraObjectViewDto.PairInfraObjectId = null;
        InfraObjectViewDto.PairInfraObjectName = null;
        InfraObjectViewDto.IsAssociate = false;
        InfraObjectViewDto.IsAssociateInfraObjectId = null;
        InfraObjectViewDto.IsAssociateInfraObjectName = null;
        InfraObjectViewDto.ReplicationTypeId = "RT_123";
        InfraObjectViewDto.ReplicationTypeName = "Database Replication";
        InfraObjectViewDto.ReplicationCategoryTypeId = "RCT_123";
        InfraObjectViewDto.ReplicationCategoryType = "Synchronous";
        InfraObjectViewDto.TypeName = "Server";
        InfraObjectViewDto.SubTypeId = "ST_123";
        InfraObjectViewDto.NodeProperties = "Node123";
        InfraObjectViewDto.Reason = null;
        InfraObjectViewDto.SiteProperties = "Site123";
        InfraObjectViewDto.BsSiteProperties = "BsSite123";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
