﻿using ContinuityPatrol.Application.Features.LogViewer.Commands.Create;
using ContinuityPatrol.Application.Features.LogViewer.Commands.Update;
using ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class ServerLogControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IDataProvider> _mockDataProvider;
        private readonly Mock<ILogger<ServerLogController>> _mockLogger;
        private readonly ServerLogController _controller;

        public ServerLogControllerShould()
        {
            _mockPublisher = new Mock<IPublisher>();
            _mockMapper = new Mock<IMapper>();
            _mockDataProvider = new Mock<IDataProvider>();
            _mockLogger = new Mock<ILogger<ServerLogController>>();

            _controller = new ServerLogController(_mockPublisher.Object, _mockMapper.Object, _mockDataProvider.Object, _mockLogger.Object);

            // Setup HttpContext and TempData
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "testuser"),
                new Claim(ClaimTypes.NameIdentifier, "123")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = httpContext
            };

            var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>());
            _controller.TempData = tempData;
        }

        #region CreateOrUpdate Tests

        [Fact]
        public async Task CreateOrUpdate_WithValidModelForCreate_ReturnsRedirectToList()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "Test Server Log",
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            var createCommand = new CreateLogViewerCommand
            {
                Name = serverLog.Name,
                UserName = serverLog.UserName,
                Password = "encrypted_password",
                FolderPath = serverLog.FolderPath,
                IPAddress = serverLog.IPAddress
            };

            var response = new BaseResponse { Message = "Server log created successfully" };

            // Setup form collection without id (for create)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>());
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateLogViewerCommand>(It.IsAny<LogViewerViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidModelForUpdate_ReturnsRedirectToList()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Id = "123",
                Name = "Updated Server Log",
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            var updateCommand = new UpdateLogViewerCommand
            {
                Id = serverLog.Id,
                Name = serverLog.Name,
                UserName = serverLog.UserName,
                Password = "encrypted_password",
                FolderPath = serverLog.FolderPath,
                IPAddress = serverLog.IPAddress
            };

            var response = new BaseResponse { Message = "Server log updated successfully" };

            // Setup form collection with id (for update)
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", new StringValues("123") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<UpdateLogViewerCommand>(It.IsAny<LogViewerViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyName_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "", // Empty name
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Never);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyUserName_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "Test Server Log",
                UserName = "", // Empty username
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Never);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyPassword_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "Test Server Log",
                UserName = "testuser",
                Password = "", // Empty password
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Never);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyFolderPath_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "Test Server Log",
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "", // Empty folder path
                IPAddress = "***********"
            };

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Never);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullValues_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = null, // Null name
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()), Times.Never);
            _mockDataProvider.Verify(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithExceptionDuringCreate_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Name = "Test Server Log",
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            var createCommand = new CreateLogViewerCommand();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>());
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<CreateLogViewerCommand>(It.IsAny<LogViewerViewModel>()))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.LogViewer.CreateAsync(It.IsAny<CreateLogViewerCommand>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithExceptionDuringUpdate_ReturnsRedirectToListWithError()
        {
            // Arrange
            var serverLog = new LogViewerViewModel
            {
                Id = "123",
                Name = "Test Server Log",
                UserName = "testuser",
                Password = "testpassword",
                FolderPath = "/test/path",
                IPAddress = "***********"
            };

            var updateCommand = new UpdateLogViewerCommand();
            var formCollection = new FormCollection(new Dictionary<string, StringValues>
            {
                { "id", new StringValues("123") }
            });
            _controller.ControllerContext.HttpContext.Request.Form = formCollection;

            _mockMapper.Setup(m => m.Map<UpdateLogViewerCommand>(It.IsAny<LogViewerViewModel>()))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.LogViewer.UpdateAsync(It.IsAny<UpdateLogViewerCommand>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.CreateOrUpdate(serverLog);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        #endregion

        #region List Tests

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
        }

        #endregion

        #region GetPagination Tests

        [Fact]
        public async Task GetPagination_WithValidQuery_ReturnsJsonResultWithData()
        {
            // Arrange
            var query = new GetLogViewerPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "test"
            };

            var paginatedResult = new PaginatedResult<LogViewerListVm>
            {
                Data = new List<LogViewerListVm>
                {
                    new LogViewerListVm
                    {
                        Id = "1",
                        Name = "Test Server Log",
                        UserName = "testuser",
                        Password = "encrypted_password",
                        FolderPath = "/test/path",
                        IPAddress = "***********"
                    }
                },
                TotalCount = 1,
                CurrentPage = 1,
                PageSize = 10
            };

            _mockDataProvider.Setup(dp => dp.LogViewer.GetPaginatedLogViewerList(It.IsAny<GetLogViewerPaginatedListQuery>()))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("Success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.LogViewer.GetPaginatedLogViewerList(It.IsAny<GetLogViewerPaginatedListQuery>()), Times.Once);
        }

        [Fact]
        public async Task GetPagination_WithException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetLogViewerPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10
            };

            _mockDataProvider.Setup(dp => dp.LogViewer.GetPaginatedLogViewerList(It.IsAny<GetLogViewerPaginatedListQuery>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        #endregion

        #region Delete Tests

        [Fact]
        public async Task Delete_WithValidId_ReturnsRedirectToListWithSuccess()
        {
            // Arrange
            var id = "123";
            var response = new BaseResponse { Message = "Server log deleted successfully" };

            _mockDataProvider.Setup(dp => dp.LogViewer.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_WithException_ReturnsRedirectToListWithWarning()
        {
            // Arrange
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.DeleteAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_WithNullId_CallsDeleteMethod()
        {
            // Arrange
            string id = null;
            var response = new BaseResponse { Message = "Server log deleted successfully" };

            _mockDataProvider.Setup(dp => dp.LogViewer.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_WithEmptyId_CallsDeleteMethod()
        {
            // Arrange
            var id = "";
            var response = new BaseResponse { Message = "Server log deleted successfully" };

            _mockDataProvider.Setup(dp => dp.LogViewer.DeleteAsync(It.IsAny<string>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockDataProvider.Verify(dp => dp.LogViewer.DeleteAsync(id), Times.Once);
        }

        #endregion

        #region IsServerLogNameExist Tests

        [Fact]
        public async Task IsServerLogNameExist_WithValidNameAndId_ReturnsTrue()
        {
            // Arrange
            var name = "Test Server Log";
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.True(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        [Fact]
        public async Task IsServerLogNameExist_WithValidNameAndId_ReturnsFalse()
        {
            // Arrange
            var name = "Test Server Log";
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.False(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        [Fact]
        public async Task IsServerLogNameExist_WithNullId_ReturnsExpectedResult()
        {
            // Arrange
            var name = "Test Server Log";
            string id = null;

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.True(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        [Fact]
        public async Task IsServerLogNameExist_WithException_ReturnsFalse()
        {
            // Arrange
            var name = "Test Server Log";
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.False(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        [Fact]
        public async Task IsServerLogNameExist_WithEmptyName_CallsDataProvider()
        {
            // Arrange
            var name = "";
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.False(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        [Fact]
        public async Task IsServerLogNameExist_WithNullName_CallsDataProvider()
        {
            // Arrange
            string name = null;
            var id = "123";

            _mockDataProvider.Setup(dp => dp.LogViewer.IsLogViewerNameUnique(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsServerLogNameExist(name, id);

            // Assert
            Assert.False(result);
            _mockDataProvider.Verify(dp => dp.LogViewer.IsLogViewerNameUnique(name, id), Times.Once);
        }

        #endregion
    }
}
