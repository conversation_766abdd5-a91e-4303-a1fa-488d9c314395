﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.Features.Replication.Commands.Update;

public class UpdateReplicationCommandValidator : AbstractValidator<UpdateReplicationCommand>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly IReplicationRepository _replicationRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;


    public UpdateReplicationCommandValidator(IReplicationRepository replicationRepository,
        ILicenseManagerRepository licenseManagerRepository, ISiteRepository siteRepository,
        ILicenseValidationService licenseValidationService, ISiteTypeRepository siteTypeRepository)
    {
        _replicationRepository = replicationRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _siteTypeRepository = siteTypeRepository;

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p)
            .MustAsync(IsLicenseActiveAsync)
            .WithMessage("License is in 'InActive' state")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p)
            .MustAsync(ReplicationCountValidation)
            .WithMessage("Replication count Reached maximum limit.")
            .When(p => !p.Type.ToLower().Contains("perpetuuiti"));

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s\-]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid Operational Service")
            .NotNull()
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Must(GetJsonProperties.IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.");

        RuleFor(e => e)
            .MustAsync(ReplicationNameUnique)
            .WithMessage("A same name already exists.");
    }

    public async Task<bool> IsLicenseActiveAsync(UpdateReplicationCommand p, CancellationToken cancellationToken)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;
        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                         throw new InvalidException("License GetList is null.");

        return licenseDtl.IsState;
    }

    private async Task<bool> ReplicationNameUnique(UpdateReplicationCommand e, CancellationToken cancellationToken)
    {
        return !await _replicationRepository.IsReplicationNameExist(e.Name, e.Id);
    }

    public async Task<bool> ReplicationCountValidation(UpdateReplicationCommand p, CancellationToken cancellationToken)
    {
        if (!p.Type.ToLower().Contains("perpetuuiti")) return true;

        if (string.IsNullOrWhiteSpace(p.LicenseId)) return false;

        var replication = await _replicationRepository.GetByReferenceIdAsync(p.Id);

        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                             throw new InvalidException("License GetList is null.");

        if (p.LicenseId.Equals(replication.LicenseId) && p.SiteId.Equals(replication.SiteId))
        {
            var isExpired = await _licenseValidationService.IsLicenseExpired(licenseManager.ExpiryDate);

            if (!isExpired) throw new InvalidOperationException("The license key has expired.");
        }
        else
        {
            var site = await _siteRepository.GetByReferenceIdAsync(p.SiteId);

            Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
                new NotFoundException(nameof(Domain.Entities.Site), p.SiteId));

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
                new NotFoundException(nameof(Domain.Entities.SiteType), site.TypeId));

            var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

            var replicationCount = await _replicationRepository.GetReplicationCountByLicenseKey(p.LicenseId, siteType.ReferenceId);

            return await _licenseValidationService.IsReplicationLicenseCountExitMaxLimit(licenseManager, siteType,
                replicationCount, index);
        }

        return true;
    }
}