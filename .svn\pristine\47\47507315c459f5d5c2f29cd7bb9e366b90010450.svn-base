using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DynamicSubDashboardFilterSpecification : Specification<DynamicSubDashboard>
{
    public DynamicSubDashboardFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("dynamicdashboardid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DynamicDashBoardId.Contains(stringItem.Replace("dynamicdashboardid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("dynamicdashboardname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.DynamicDashBoardName.Contains(stringItem.Replace("dynamicdashboardname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.DynamicDashBoardId.Contains(searchString) ||
                    p.DynamicDashBoardName.Contains(searchString) || p.Properties.Contains(searchString);
            }
        }
    }
}