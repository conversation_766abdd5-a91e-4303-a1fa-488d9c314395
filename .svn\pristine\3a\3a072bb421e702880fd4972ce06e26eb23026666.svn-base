using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetNameUnique;

//using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Cyber;

public class CyberComponentService : BaseService, ICyberComponentService
{
    public CyberComponentService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<CyberComponentListVm>> GetCyberComponentList()
    {
        Logger.LogDebug("Get All CyberComponents");

        return await Mediator.Send(new GetCyberComponentListQuery());
    }

    public async Task<CyberComponentDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponent Id");

        Logger.LogDebug($"Get CyberComponent Detail by Id '{id}'");

        return await Mediator.Send(new GetCyberComponentDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateCyberComponentCommand createCyberComponentCommand)
    {
        Logger.LogDebug($"Create CyberComponent '{createCyberComponentCommand}'");

        return await Mediator.Send(createCyberComponentCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberComponentCommand updateCyberComponentCommand)
    {
        Logger.LogDebug($"Update CyberComponent '{updateCyberComponentCommand}'");

        return await Mediator.Send(updateCyberComponentCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponent Id");

        Logger.LogDebug($"Delete CyberComponent Details by Id '{id}'");

        return await Mediator.Send(new DeleteCyberComponentCommand { Id = id });
    }
    public async Task<List<CyberComponentBySiteIdVm>> GetCyberComponentBySiteId(string siteId)
    {
        Guard.Against.InvalidGuidOrEmpty(siteId, "site Id");

        Logger.LogDebug($"Get CyberComponent Detail by site Id '{siteId}'");

        return await Mediator.Send(new GetCyberComponentBySiteIdQuery { SiteId = siteId });
    }
    public async Task<List<GetInfrastructureSummaryVm>> GetInfrastructureSummary()
    {
        Logger.LogDebug($"Get Infrastructure Summary");

        return await Mediator.Send(new GetInfrastructureSummaryQuery());
    }

    #region NameExist
    public async Task<bool> IsCyberComponentNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "CyberComponent Name");

        Logger.LogInformation($"Check Name Exists Detail by CyberComponent Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetCyberComponentNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<CyberComponentListVm>> GetPaginatedCyberComponents(GetCyberComponentPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in CyberComponent Paginated List");

        return await Mediator.Send(query);
    }
    #endregion
}
