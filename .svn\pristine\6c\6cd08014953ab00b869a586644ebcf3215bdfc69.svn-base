using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ZertoVpgMonitorLogFixture : IDisposable
{
    public List<ZertoVpgMonitorLog> ZertoVpgMonitorLogPaginationList { get; set; }
    public List<ZertoVpgMonitorLog> ZertoVpgMonitorLogList { get; set; }
    public ZertoVpgMonitorLog ZertoVpgMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ZertoVpgMonitorLogFixture()
    {
        var fixture = new Fixture();

        ZertoVpgMonitorLogList = fixture.Create<List<ZertoVpgMonitorLog>>();

        ZertoVpgMonitorLogPaginationList = fixture.CreateMany<ZertoVpgMonitorLog>(20).ToList();

        ZertoVpgMonitorLogDto = fixture.Create<ZertoVpgMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
