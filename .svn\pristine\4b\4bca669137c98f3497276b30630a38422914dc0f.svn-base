﻿namespace ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;

public class CreateWorkflowInfraObjectCommand : IRequest<CreateWorkflowInfraObjectResponse>
{
    public string CompanyId { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ActionType { get; set; }
    public string TotalRTO { get; set; }
    public string WorkflowVersion { get; set; }
    public bool IsAttach { get; set; }

    public override string ToString()
    {
        return $"InfraObjectId: {InfraObjectId};";
    }
}