﻿using ContinuityPatrol.Application.Features.CredentialProfile.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Events;

public class UpdateCredentialProfileEventTests : IClassFixture<CredentialProfileFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly CredentialProfileUpdatedEventHandler _handler;

    public UpdateCredentialProfileEventTests(CredentialProfileFixture credentialProfileFixture, UserActivityFixture userActivityFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockCredentialProfileEventLogger = new Mock<ILogger<CredentialProfileUpdatedEventHandler>>();

        _mockUserActivityRepository = CredentialProfileRepositoryMocks.CreateCredentialProfileEventRepository(_userActivityFixture.UserActivities);

        _handler = new CredentialProfileUpdatedEventHandler(mockLoggedInUserService.Object, mockCredentialProfileEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateCredentialProfileEventUpdated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_credentialProfileFixture.CredentialProfileUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_credentialProfileFixture.CredentialProfileUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}