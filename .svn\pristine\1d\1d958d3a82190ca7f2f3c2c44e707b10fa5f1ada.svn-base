﻿using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Create;
using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoNodeWorkflowExecutionDataAttribute : AutoDataAttribute
{
    public AutoNodeWorkflowExecutionDataAttribute() : base(() =>
    {
        var fixture = new Fixture();

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<CreateNodeWorkflowExecutionCommand>(p => p.WorkflowName, 10));

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<UpdateNodeWorkflowExecutionCommand>(p => p.WorkflowName, 10));


        return fixture;

    })
    {

    }
}