﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Queries
{
    public class GetVeritasClusterListQueryHandlerTests
    {
        private readonly Mock<IVeritasClusterRepository> _mockVeritasClusterRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetVeritasClusterListQueryHandler _handler;

        public GetVeritasClusterListQueryHandlerTests()
        {
            _mockVeritasClusterRepository = new Mock<IVeritasClusterRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetVeritasClusterListQueryHandler(_mockMapper.Object, _mockVeritasClusterRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsMappedVeritasClusters_WhenDataExists()
        {
            var veritasClusters = new List<Domain.Entities.VeritasCluster>
            {
                new Domain.Entities.VeritasCluster { Id = 1, ClusterName = "Cluster1", IsActive = true },
                new Domain.Entities.VeritasCluster { Id = 2, ClusterName = "Cluster2", IsActive = true }
            };

            var veritasClusterVms = new List<VeritasClusterListVm>
            {
                new VeritasClusterListVm { Id = Guid.NewGuid().ToString(), ClusterName = "Cluster1" },
                new VeritasClusterListVm { Id = Guid.NewGuid().ToString(), ClusterName = "Cluster2" }
            };

            _mockVeritasClusterRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(veritasClusters);

            _mockMapper.Setup(m => m.Map<List<VeritasClusterListVm>>(veritasClusters))
                .Returns(veritasClusterVms);

            var query = new GetVeritasClusterListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(veritasClusterVms.Count, result.Count);
            Assert.Equal(veritasClusterVms[0].ClusterName, result[0].ClusterName);
            _mockVeritasClusterRepository.Verify(r => r.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<VeritasClusterListVm>>(veritasClusters), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
        {
            _mockVeritasClusterRepository.Setup(r => r.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.VeritasCluster>());

            _mockMapper.Setup(m => m.Map<List<VeritasClusterListVm>>(It.IsAny<List<Domain.Entities.VeritasCluster>>()))
                .Returns(new List<VeritasClusterListVm>());

            var query = new GetVeritasClusterListQuery();

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
            _mockVeritasClusterRepository.Verify(r => r.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<VeritasClusterListVm>>(It.IsAny<List<Domain.Entities.VeritasCluster>>()), Times.Never);
        }
    }
}
