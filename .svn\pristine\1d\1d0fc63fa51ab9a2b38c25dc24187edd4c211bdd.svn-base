using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ArchiveRepository : BaseRepository<Archive>, IArchiveRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ArchiveRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Archive>> ListAllAsync()
    {
        return await FilterRequiredField(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking()).ToListAsync();
    }
    public override async Task<PaginatedResult<Archive>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Archive> specification, string sortColumn, string sortOrder)
    {
        var query = _loggedInUserService.IsParent
            ? Entities.Specify(specification).DescOrderById()
            : Entities.Specify(specification)
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById();

        return await FilterRequiredField(query.AsNoTracking())
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<Archive> GetPaginatedQuery()
    {
        return FilterRequiredField(base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().OrderByDescending(x => x.Id));
    }

    public override async Task<Archive> GetByReferenceIdAsync(string id)
    {
        return await base.GetByReferenceId(id,
            archive => archive.CompanyId.Equals(_loggedInUserService.CompanyId) && archive.ReferenceId.Equals(id)
        ).AsNoTracking().FirstOrDefaultAsync();
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.ArchiveProfileName.Equals(name));
        }

        var matchingEntities = await Entities
            .Where(e => e.ArchiveProfileName.Equals(name))
            .AsNoTracking()
            .ToListAsync();

        return matchingEntities.Unique(id);
    }

    public async Task<bool> IsSolutionTypeExist(string profileName)
    {
        return await _dbContext.Archives.AsNoTracking()
            .AnyAsync(x => x.ArchiveProfileName.ToLower().Equals(profileName));
    }



    public async Task<bool> IsTableNameExist(string tableName, string id)
    {
        var allTableNameProperties = await _dbContext.Archives
            .AsNoTracking()
            .Select(e => e.TableNameProperties)
            .ToListAsync();

        foreach (var json in allTableNameProperties)
        {
            if (string.IsNullOrWhiteSpace(json)) continue;

            var tableEntries = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(json);

            if (tableEntries?.Any(dict =>
                    dict.ContainsKey("tableName") && dict["tableName"].ToLower() == tableName.ToLower()) == true)
            {
                return true;
            }
        }

        return false;
    }

    public async Task<List<Archive>>GetByTableAccessId(string id)
    {
        return await FilterRequiredField(
            Entities.AsNoTracking().Where(x => x.TableNameProperties.Contains(id))
        ).ToListAsync();
    }
    private IQueryable<Archive>FilterRequiredField(IQueryable<Archive> archives)
    {
        return archives.Select(x => new Archive
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            ArchiveProfileName = x.ArchiveProfileName,
            TableNameProperties = x.TableNameProperties,
            CompanyId = x.CompanyId,
            Count = x.Count,
            CronExpression = x.CronExpression,
            ScheduleTime = x.ScheduleTime,
            ScheduleType = x.ScheduleType,
            BackUpType = x.BackUpType,
            Type = x.Type,
            ClearBackup = x.ClearBackup,
            NodeId = x.NodeId,
            NodeName = x.NodeName,
        });
    }
}