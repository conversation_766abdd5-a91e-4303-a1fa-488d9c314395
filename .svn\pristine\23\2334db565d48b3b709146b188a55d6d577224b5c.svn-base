﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Commands;

public class CreateWorkflowCategoryTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private readonly Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    private readonly CreateWorkflowCategoryCommandHandler _handler;
    private readonly Mock<IVersionManager> _versionManager=new();

    public CreateWorkflowCategoryTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockVersionManager = new Mock<IVersionManager>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.CreateWorkflowCategoryRepository(_workflowCategoryFixture.WorkflowCategories);


        _handler = new CreateWorkflowCategoryCommandHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object, mockPublisher.Object, mockLoggedInUserService.Object , _versionManager.Object);

        _handler = new CreateWorkflowCategoryCommandHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object, mockPublisher.Object, mockLoggedInUserService.Object, _versionManager.Object);

        _handler = new CreateWorkflowCategoryCommandHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object, mockPublisher.Object, mockLoggedInUserService.Object, mockVersionManager.Object);

    }

    [Fact]
    public async Task Handle_Should_IncreaseWorkflowCategoryCount_When_AddValidWorkflowCategory()
    {
        await _handler.Handle(_workflowCategoryFixture.CreateWorkflowCategoryCommand, CancellationToken.None);

        var allCategories = await _mockWorkflowCategoryRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_workflowCategoryFixture.WorkflowCategories.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulWorkflowCategoryResponse_When_AddValidWorkflowCategory()
    {
        var result = await _handler.Handle(_workflowCategoryFixture.CreateWorkflowCategoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateWorkflowCategoryResponse));

        result.WorkflowCategoryId.ShouldBeGreaterThan(0.ToString());

        //result.ReferenceId.ShouldNotBeNullOrEmpty();

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowCategoryFixture.CreateWorkflowCategoryCommand, CancellationToken.None);

        _mockWorkflowCategoryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowCategory>()), Times.Once);
    }
}