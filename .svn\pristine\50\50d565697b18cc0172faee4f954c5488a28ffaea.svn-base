﻿namespace ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

public class DashboardImpactAvailabilityDetailVm
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }

    public int TotalBusinessFunctionCount { get; set; }
    public int BusinessFunctionTotalImpacted { get; set; }
    public int BusinessFunctionMajorImpactCount { get; set; }
    public int BusinessFunctionPartialImpactCount { get; set; }
    public int BusinessFunctionUnderRPOCount { get; set; }

    public int TotalInfraObjectCount { get; set; }
    public int InfraTotalImpactCount { get; set; }
    public int InfraMajorImpactCount { get; set; }
    public int InfraPartialImpactCount { get; set; }
    public int InfraUnderRPOCount { get; set; }
}
