using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DB2HADRMonitorLogFixture : IDisposable
{
    public List<DB2HADRMonitorLog> DB2HADRMonitorLogPaginationList { get; set; }
    public List<DB2HADRMonitorLog> DB2HADRMonitorLogList { get; set; }
    public DB2HADRMonitorLog DB2HADRMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
    public const string Type = "TestType";
    public const string Status = "Active";

    public ApplicationDbContext DbContext { get; private set; }

    public DB2HADRMonitorLogFixture()
    {
        var fixture = new Fixture();

        DB2HADRMonitorLogList = fixture.Create<List<DB2HADRMonitorLog>>();

        DB2HADRMonitorLogPaginationList = fixture.CreateMany<DB2HADRMonitorLog>(20).ToList();

        DB2HADRMonitorLogPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DB2HADRMonitorLogPaginationList.ForEach(x => x.IsActive = true);
        DB2HADRMonitorLogPaginationList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());

        DB2HADRMonitorLogList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DB2HADRMonitorLogList.ForEach(x => x.IsActive = true);
        DB2HADRMonitorLogList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());


        DB2HADRMonitorLogDto = fixture.Create<DB2HADRMonitorLog>();
        DB2HADRMonitorLogDto.ReferenceId = Guid.NewGuid().ToString();
        DB2HADRMonitorLogDto.IsActive = true;
        DB2HADRMonitorLogDto.InfraObjectId = Guid.NewGuid().ToString();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
