﻿using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Commands
{
    public class ForgotPasswordTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;
        private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
        private readonly Mock<ILogger<ForgotPasswordCommandHandler>> _mockLogger;
        private readonly Mock<IUserLoginRepository> _mockUserLoginRepository;
        private readonly Mock<IAlertRepository> _mockAlertRepository;
        private readonly Mock<IPublisher> _mockPublisher;

        private readonly ForgotPasswordCommandHandler _handler;

        public ForgotPasswordTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockEmailService = new Mock<IEmailService>();
            _mockMapper = new Mock<IMapper>();
            _mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUserInfoRepository = new Mock<IUserInfoRepository>();
            _mockGlobalSettingRepository = new Mock<IGlobalSettingRepository>();
            _mockLogger = new Mock<ILogger<ForgotPasswordCommandHandler>>();
            _mockUserLoginRepository = new Mock<IUserLoginRepository>();
            _mockAlertRepository = new Mock<IAlertRepository>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new ForgotPasswordCommandHandler(
                _mockMapper.Object,
                _mockEmailService.Object,
                _mockUserRepository.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockPublisher.Object,
                _mockConfiguration.Object,
                _mockUserInfoRepository.Object,
                _mockGlobalSettingRepository.Object,
                _mockLogger.Object,
                _mockUserLoginRepository.Object,
                _mockAlertRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldSendEmail_WhenValidRequest()
        {
            var command = new ForgotPasswordCommand
            {
                LoginName = "testuser",
                Password = "newpassword",
                NewPassword = "newpassword"
            };

            var user = new Domain.Entities.User
            {
                LoginName = "testuser",
                ReferenceId = "123",
                LoginType = "basic"
            };

            var userInfo = new Domain.Entities.UserInfo
            {
                Email = "<EMAIL>"
            };

            var smtpConfig = new Domain.Entities.SmtpConfiguration
            {
                SmtpHost = "smtp.example.com"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync(user);

            _mockGlobalSettingRepository.Setup(repo => repo.GlobalSettingBySettingKey("Email Notification"))
                .ReturnsAsync(new GlobalSetting { GlobalSettingValue = "true" });

            _mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(userInfo);

            _mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.SmtpConfiguration> { smtpConfig });

            _mockConfiguration.Setup(config => config.GetValue<string>("CP:Version"))
                .Returns("1.0");

            _mockMapper.Setup(m => m.Map<EmailDto>(smtpConfig))
                .Returns(new EmailDto());

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("Email sent successfully!.", result.Message);

            _mockEmailService.Verify(service => service.SendEmail(It.IsAny<EmailDto>()), Times.Once);
            _mockUserRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.User>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidException_WhenEmailNotificationsDisabled()
        {
            var command = new ForgotPasswordCommand
            {
                LoginName = "testuser"
            };

            _mockGlobalSettingRepository.Setup(repo => repo.GlobalSettingBySettingKey("Email Notification"))
                .ReturnsAsync(new GlobalSetting { GlobalSettingValue = "false" });

            var exception = await Assert.ThrowsAsync<InvalidException>(
                () => _handler.Handle(command, CancellationToken.None));

            Assert.Equal("The email notification feature is not enabled in the global settings.", exception.Message);

            _mockEmailService.Verify(service => service.SendEmail(It.IsAny<EmailDto>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidException_WhenSmtpNotConfigured()
        {
            var command = new ForgotPasswordCommand
            {
                LoginName = "testuser"
            };

            var user = new Domain.Entities.User
            {
                LoginName = "testuser",
                ReferenceId = "123",
                LoginType = "basic"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync(user);

            _mockGlobalSettingRepository.Setup(repo => repo.GlobalSettingBySettingKey("Email Notification"))
                .ReturnsAsync(new GlobalSetting { GlobalSettingValue = "true" });

            _mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.SmtpConfiguration>());

            var exception = await Assert.ThrowsAsync<InvalidException>(
                () => _handler.Handle(command, CancellationToken.None));

            Assert.Equal("please configure smtp.", exception.Message);

            _mockEmailService.Verify(service => service.SendEmail(It.IsAny<EmailDto>()), Times.Never);
        }
    }
}
