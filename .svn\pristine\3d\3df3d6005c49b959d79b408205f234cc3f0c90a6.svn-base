﻿using ContinuityPatrol.Application.Features.RsyncOption.Events.PaginatedView;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncOption.Events
{
    public class PaginatedRsyncOptionEventTests
    {
        private readonly Mock<ILogger<RsyncOptionPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly RsyncOptionPaginatedEventHandler _handler;

        public PaginatedRsyncOptionEventTests()
        {
            _mockLogger = new Mock<ILogger<RsyncOptionPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _mockUserService.Setup(s => s.UserId).Returns("TestUserId");
            _mockUserService.Setup(s => s.LoginName).Returns("TestLoginName");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/rsyncOption/paginated");
            _mockUserService.Setup(s => s.CompanyId).Returns("TestCompanyId");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _handler = new RsyncOptionPaginatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenEventIsHandled()
        {
            var paginatedEvent = new RsyncOptionPaginatedEvent();

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockLogger.Verify(
                logger => logger.LogInformation("Rsync Options viewed"),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldAddUserActivity_WhenEventIsHandled()
        {
            var paginatedEvent = new RsyncOptionPaginatedEvent();

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                    activity.UserId == "TestUserId" &&
                    activity.LoginName == "TestLoginName" &&
                    activity.RequestUrl == "/rsyncOption/paginated" &&
                    activity.CompanyId == "TestCompanyId" &&
                    activity.HostAddress == "127.0.0.1" &&
                    activity.Action == "View RsyncOption" &&
                    activity.Entity == "RsyncOption" &&
                    activity.ActivityType == "View" &&
                    activity.ActivityDetails == "Rsync Options viewed"
                )),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenUserServiceReturnsNullValues()
        {
            _mockUserService.Setup(s => s.UserId).Returns((string)null);
            _mockUserService.Setup(s => s.LoginName).Returns((string)null);

            var paginatedEvent = new RsyncOptionPaginatedEvent();

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldHandleEmptyRequestUrl()
        {
            _mockUserService.Setup(s => s.RequestedUrl).Returns(string.Empty);

            var paginatedEvent = new RsyncOptionPaginatedEvent();

            await _handler.Handle(paginatedEvent, CancellationToken.None);

            _mockLogger.Verify(
                logger => logger.LogInformation("Rsync Options viewed"),
                Times.Once
            );
            _mockUserActivityRepository.Verify(
                repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()),
                Times.Once
            );
        }
    }
}
