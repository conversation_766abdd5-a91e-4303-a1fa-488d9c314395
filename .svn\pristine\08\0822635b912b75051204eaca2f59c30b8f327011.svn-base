﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Events.SendTestEmail;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Events
{
    public class SmtpConfigurationSendTestEmailEventTests
    {
        private readonly Mock<ILogger<SmtpConfigurationSendTestEmailEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SmtpConfigurationSendTestEmailEventHandler _handler;

        public SmtpConfigurationSendTestEmailEventTests()
        {
            _loggerMock = new Mock<ILogger<SmtpConfigurationSendTestEmailEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SmtpConfigurationSendTestEmailEventHandler(
                _loggerMock.Object,
                _userActivityRepositoryMock.Object,
                _userServiceMock.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveUserActivity_WhenNotificationIsHandled()
        {
            var notification = new SmtpConfigurationSendTestEmailEvent();
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userServiceMock.Setup(us => us.LoginName).Returns("testUser");
            _userServiceMock.Setup(us => us.RequestedUrl).Returns("/api/smtp/test-email");
            _userServiceMock.Setup(us => us.CompanyId).Returns("456");
            _userServiceMock.Setup(us => us.IpAddress).Returns("***********");

            await _handler.Handle(notification, cancellationToken);

            _userActivityRepositoryMock.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == "123" &&
                    ua.LoginName == "testUser" &&
                    ua.RequestUrl == "/api/smtp/test-email" &&
                    ua.CompanyId == "456" &&
                    ua.HostAddress == "***********" &&
                    ua.Action == "TestEmail SmtpConfiguration" &&
                    ua.Entity == "SmtpConfiguration" &&
                    ua.ActivityType == "TestEmail" &&
                    ua.ActivityDetails == "Test Email sent successfully"
                )),
                Times.Once
            );

            _loggerMock.Verify(
                logger => logger.LogInformation("Test Email sent successfully"),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldNotThrow_WhenRepositoryFails()
        {
            var notification = new SmtpConfigurationSendTestEmailEvent();
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(us => us.UserId).Returns("123");
            _userActivityRepositoryMock
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new System.Exception("Database error"));

            var exception = await Record.ExceptionAsync(() => _handler.Handle(notification, cancellationToken));

            Assert.Null(exception);

            _loggerMock.Verify(
                logger => logger.LogInformation("Test Email sent successfully"),
                Times.Once
            );
        }
    }
}
