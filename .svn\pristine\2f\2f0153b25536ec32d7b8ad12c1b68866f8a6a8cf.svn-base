﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Delete;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetList;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNames;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class GlobalSettingsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GlobalSettingListVm>>> GetGlobalSettings()
    {
        Logger.LogDebug("Get All GlobalSettings");

        return Ok(await Mediator.Send(new GetGlobalSettingListQuery()));
    }

    [HttpGet("{id}", Name = "GetGlobalSetting")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GlobalSettingDetailVm>> GetGlobalSettingById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "GlobalSetting Id");

        Logger.LogDebug($"Get GlobalSetting Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetGlobalSettingDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateGlobalSettingResponse>> CreateGlobalSetting([FromBody] CreateGlobalSettingCommand createGlobalSettingCommand)
    {
        Logger.LogDebug($"Create GlobalSetting '{createGlobalSettingCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateGlobalSetting), await Mediator.Send(createGlobalSettingCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateGlobalSettingResponse>> UpdateGlobalSetting([FromBody] UpdateGlobalSettingCommand updateGlobalSettingCommand)
    {
        Logger.LogDebug($"Update GlobalSetting '{updateGlobalSettingCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateGlobalSettingCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteGlobalSettingResponse>> DeleteGlobalSetting(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "GlobalSetting Id");

        Logger.LogDebug($"Delete GlobalSetting Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteGlobalSettingCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<GlobalSettingListVm>>> GetPaginatedGlobalSettings([FromQuery] GetGlobalSettingPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in GlobalSetting Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<Application.Features.GlobalSetting.Queries.GetNames.GlobalSettingNameVm>>> GetGlobalSettingNames()
    {
        Logger.LogDebug("Get All GlobalSetting Names");

        return Ok(await Mediator.Send(new GetGlobalSettingNameQuery()));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsGlobalSettingNameExist(string key, string? id)
    {
        Guard.Against.NullOrWhiteSpace(key, "GlobalSetting Key");

        Logger.LogDebug($"Check Key Exists Detail by GlobalSetting Key '{key}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetGlobalSettingNameUniqueQuery { GlobalSettingKey = key, Id = id }));
    }

    [Route("authentication"),HttpPost]
    public async Task<ActionResult> Authentication([FromBody] AuthenticationCommand authenticationCommand)
    {
        Logger.LogDebug($"Check GlobalSetting AccessKey Key");

        return Ok(await Mediator.Send(authenticationCommand));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = new string[] { ApplicationConstants.Cache.AllGlobalSettingCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}