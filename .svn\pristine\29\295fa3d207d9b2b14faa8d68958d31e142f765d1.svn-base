﻿namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetDetail;

public class
    GetSmtpConfigurationDetailQueryHandler : IRequestHandler<GetSmtpConfigurationDetailQuery, SmtpConfigurationDetailVm>
{
    private readonly IMapper _mapper;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;

    public GetSmtpConfigurationDetailQueryHandler(IMapper mapper,
        ISmtpConfigurationRepository smtpConfigurationRepository)
    {
        _mapper = mapper;
        _smtpConfigurationRepository = smtpConfigurationRepository;
    }

    public async Task<SmtpConfigurationDetailVm> Handle(GetSmtpConfigurationDetailQuery request,
        CancellationToken cancellationToken)
    {
        var smtpConfiguration = await _smtpConfigurationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(smtpConfiguration, nameof(Domain.Entities.SmtpConfiguration),
            new NotFoundException(nameof(Domain.Entities.SmtpConfiguration), request.Id));

        var smtpConfigurationDetailDto = _mapper.Map<SmtpConfigurationDetailVm>(smtpConfiguration);

        smtpConfigurationDetailDto.UserName = SecurityHelper.Decrypt(smtpConfigurationDetailDto.UserName);

        return smtpConfigurationDetailDto;
    }
}