﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoWorkflowCategoryDataAttribute : AutoDataAttribute
{
    public AutoWorkflowCategoryDataAttribute()
        : base(() =>

        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowCategoryCommand>(p => p.Name, 10));
            fixture.Customize<CreateWorkflowCategoryCommand>(c => c.With(b => b.Name, 10.ToString()));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowCategoryCommand>(p => p.Name, 10));
            fixture.Customize<UpdateWorkflowCategoryCommand>(c => c.With(b => b.Id, 0.ToString()));

            return fixture;
        })
    {

    }
}