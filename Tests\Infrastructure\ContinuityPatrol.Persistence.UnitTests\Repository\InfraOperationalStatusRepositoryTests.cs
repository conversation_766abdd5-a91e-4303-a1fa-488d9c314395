using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraOperationalStatusRepositoryTests : IClassFixture<InfraOperationalStatusFixture>
{
    private readonly InfraOperationalStatusFixture _infraOperationalStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraOperationalStatusRepository _repository;

    public InfraOperationalStatusRepositoryTests(InfraOperationalStatusFixture infraOperationalStatusFixture)
    {
        _infraOperationalStatusFixture = infraOperationalStatusFixture;
        _dbContext = _infraOperationalStatusFixture.DbContext;
        _repository = new InfraOperationalStatusRepository(_dbContext);
    }

    //public void Dispose()
    //{
    //    _infraOperationalStatusFixture?.Dispose();
    //}

    private async Task ClearDatabase()
    {
        _dbContext.InfraOperationalStatus.RemoveRange(_dbContext.InfraOperationalStatus);
        await _dbContext.SaveChangesAsync();
    }

    #region GetInfraOperationalStatusByInfraId Tests

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_ReturnsMatchingStatus_WhenInfraIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraId = "INFRA_123";

        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraId,
            IsActive = true
        };

        await _dbContext.InfraOperationalStatus.AddAsync(infraOperationalStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId(infraId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_ReturnsNull_WhenInfraIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentInfraId = "INFRA_999";

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId(nonExistentInfraId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_ReturnsFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraId = "INFRA_123";

        var infraOperationalStatuses = new List<InfraOperationalStatus>
        {
            new InfraOperationalStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraId,
 
                IsActive = true
            },
            new InfraOperationalStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraId,
            
                IsActive = true
            }
        };

        await _dbContext.InfraOperationalStatus.AddRangeAsync(infraOperationalStatuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId(infraId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraId, result.InfraObjectId);
        // Should return the first match found

    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraId = "INFRA_123";

        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraId,
     
            IsActive = true
        };

        await _dbContext.InfraOperationalStatus.AddAsync(infraOperationalStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.GetInfraOperationalStatusByInfraId("INFRA_123");
        var resultDifferentCase = await _repository.GetInfraOperationalStatusByInfraId("infra_123");

        // Assert
        Assert.NotNull(resultExactCase); // Exact case should match
        Assert.Null(resultDifferentCase); // Different case should not match (case sensitive)
    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_HandlesEmptyString()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId("");

        // Assert
        Assert.Null(result); // Empty string should return null
    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_HandlesNullString()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId(null);

        // Assert
        Assert.Null(result); // Null string should return null
    }

    [Fact]
    public async Task GetInfraOperationalStatusByInfraId_UsesAsNoTracking()
    {
        // Arrange
        await ClearDatabase();
        var infraId = "INFRA_123";

        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraId,
  
            IsActive = true
        };

        await _dbContext.InfraOperationalStatus.AddAsync(infraOperationalStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraOperationalStatusByInfraId(infraId);

        // Assert
        Assert.NotNull(result);
        // Verify that the entity is not being tracked by the context
        var entry = _dbContext.Entry(result);
        Assert.Equal(EntityState.Detached, entry.State);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_AddsInfraOperationalStatusSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_ADD_TEST",
     
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraOperationalStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("INFRA_ADD_TEST", result.InfraObjectId);
        
        var savedEntity = await _dbContext.InfraOperationalStatus
            .FirstOrDefaultAsync(x => x.InfraObjectId == "INFRA_ADD_TEST");
        Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task UpdateAsync_UpdatesInfraOperationalStatusSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_UPDATE_TEST",
     
            IsActive = true
        };

        await _dbContext.InfraOperationalStatus.AddAsync(infraOperationalStatus);
        await _dbContext.SaveChangesAsync();

        // Modify the entity

        // Act
        var result = await _repository.UpdateAsync(infraOperationalStatus);

        // Assert
        Assert.NotNull(result);

    }

    [Fact]
    public async Task DeleteAsync_DeletesInfraOperationalStatusSuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraOperationalStatus = new InfraOperationalStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_DELETE_TEST",

            IsActive = true
        };

        await _dbContext.InfraOperationalStatus.AddAsync(infraOperationalStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.DeleteAsync(infraOperationalStatus);

        // Assert
        Assert.NotNull(result);
        
        var deletedEntity = await _dbContext.InfraOperationalStatus
            .FirstOrDefaultAsync(x => x.InfraObjectId == "INFRA_DELETE_TEST");
        Assert.Null(deletedEntity);
    }

    #endregion
}
