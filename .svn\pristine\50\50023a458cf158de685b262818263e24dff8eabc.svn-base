using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaCost.Events.Delete;

public class FiaCostDeletedEventHandler : INotificationHandler<FiaCostDeletedEvent>
{
    private readonly ILogger<FiaCostDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaCostDeletedEventHandler(ILoggedInUserService userService, ILogger<FiaCostDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FiaCostDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} FiaCost",
            Entity = "FiaCost",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"FiaCost '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaCost '{deletedEvent.Name}' deleted successfully.");
    }
}
