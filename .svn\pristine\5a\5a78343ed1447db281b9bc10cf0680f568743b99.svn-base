namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetNameUnique;

public class
    GetCyberComponentMappingNameUniqueQueryHandler : IRequestHandler<GetCyberComponentMappingNameUniqueQuery, bool>
{
    private readonly ICyberComponentMappingRepository _cyberComponentMappingRepository;

    public GetCyberComponentMappingNameUniqueQueryHandler(
        ICyberComponentMappingRepository cyberComponentMappingRepository)
    {
        _cyberComponentMappingRepository = cyberComponentMappingRepository;
    }

    public async Task<bool> Handle(GetCyberComponentMappingNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _cyberComponentMappingRepository.IsNameExist(request.Name, request.Id);
    }
}