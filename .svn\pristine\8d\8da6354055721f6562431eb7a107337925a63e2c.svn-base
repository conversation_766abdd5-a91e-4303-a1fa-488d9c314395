﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class AirGapReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public  AirGapLogReportVm AirGapReports = new AirGapLogReportVm();
        public AirGapReport(string data)
        {
            try
            {
                AirGapReports = JsonConvert.DeserializeObject<AirGapLogReportVm>(data);
                _logger = PreBuildReportController._logger;
                InitializeComponent();
                ClientCompanyLogo();
                var report = AirGapReports;
                this.DataSource = report.CyberAirGapLogLists;
                _username.Text = "Report Generated By : " + AirGapReports.ReportGenerated;
                xrTableCell7.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch(Exception ex) {

                _logger.LogError("An error occured in design page : " + ex.Message.ToString());
            }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in DrDrill Summary Report" + ex.Message.ToString());
            }
        }

        private int serialNumber = 1;
        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the DrDrill summary Report's CP Version. The error message : " + ex.Message); throw; }
        }
    }
}