﻿using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Validators;

public class UpdateServerValidatorTests
{
    private readonly Mock<IServerRepository> _mockServerRepository;
    private readonly Mock<ISiteRepository> _mockSiteRepository;
    private readonly Mock<ISiteTypeRepository> _mockSiteTypeRepository;
    private readonly Mock<ILicenseManagerRepository> _licenseManagerRepository;
    private readonly Mock<ILicenseValidationService> _licenseValidationService;
    private readonly Mock<IDatabaseViewRepository> _mockDatabaseViewRepository;

    private readonly UpdateServerCommand _updateServerCommand;
    public UpdateServerValidatorTests()
    {
        var servers = new Fixture().Create<List<Domain.Entities.Server>>();
        var sites = new Fixture().Create<List<Domain.Entities.Site>>();
        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();

        _licenseValidationService = new Mock<ILicenseValidationService>();
        _mockSiteRepository = SiteRepositoryMocks.UpdateSiteRepository(sites);
        _mockServerRepository = ServerRepositoryMocks.UpdateServerRepository(servers);
        _licenseManagerRepository = LicenseManagerRepositoryMocks.UpdateBaseLicenseRepository(licenseManagers);
        _mockSiteTypeRepository = new Mock<ISiteTypeRepository>();
        _mockDatabaseViewRepository = new Mock<IDatabaseViewRepository>();


        var db = new AutoFixture.Fixture().Create<UpdateServerCommand>();
        var prefixes = new List<string> { "Name", "Type", "TypeIdId", "CompanyId", "SiteId", "SiteName", "Logo", "Properties", "LicenseId", "LicenseKey", "BusinessServiceId", "BusinessServiceName", "FormVersion", "OS", "Role", "Server", "Id" };
        var pattern = $"^{string.Join("|", prefixes.Select(p => Regex.Escape(p)))}";
        var jsonPrefix = "Properties";
        _updateServerCommand = RemovePrefixesFromObject(db, pattern, jsonPrefix);
        _updateServerCommand = RemovePrefixesFromObject(_updateServerCommand, pattern, jsonPrefix);
    }

    static T RemovePrefixesFromObject<T>(T obj, string prefixPattern, string jsonPrefix)
    {

        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                      .Where(p => p.PropertyType == typeof(string));

        foreach (var property in properties)
        {
            var currentValue = (string)property.GetValue(obj);
            if (currentValue != null)
            {
                if (property.Name == jsonPrefix)
                {
                    // Convert to JSON if property name matches the specific prefix
                    var jsonValue = JsonSerializer.Serialize(new { Properties = currentValue });
                    property.SetValue(obj, jsonValue);
                }
                else
                {
                    // Remove prefixes for other properties
                    var newValue = Regex.Replace(currentValue, prefixPattern, "");
                    property.SetValue(obj, newValue);
                }
            }
        }
        return obj;
    }

    [Fact]
    public async Task Verify_Update_Name_InServer_With_Empty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);

        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp=>dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);

        _updateServerCommand.Name = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Name_InServer_Isnull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameNotNullRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Name_InServer_MinimumRange()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "AR";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameRangeRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Name_InServer_MaximumRange()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameRangeRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "   PTS  ";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_DoubleSpace_InFront()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "  PTSIndia";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_DoubleSpace_InBack()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "PTSIndia  ";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_TripleSpace_InBetween()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "PTS   India";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_SpecialCharacters_InFront()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "#$%PTSIndia";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_SpecialCharacters_InBetween()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "PTS#@$%India";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_SpecialCharacters_Only()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "!@#$%%^&*><";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_UnderScore_InFront()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "_PTS";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_UnderScore_InFront_AndBack()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "_PTS_";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_Numbers_InFront()   
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "345PTS";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "_345PTS_";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_UnderScore_InFront_AndNumbers_InBack()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "_PTS345";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Valid_Name_InServer_With_Numbers_Only()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Name = "12345678990";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerNameValidRequired, validateResult.Errors[3].ErrorMessage);
    }

    //SiteName

    [Fact]
    public async Task Verify_Update_SiteName_InServer_WithEmpty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.SiteName = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerSiteNameRequired, validateResult.Errors[4].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_SiteName_InServer_IsNull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.SiteName = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerSiteNameNotNullRequired, validateResult.Errors[5].ErrorMessage);
    }

    //ServerType

    [Fact]
    public async Task Verify_Update_ServerType_InServer_WithEmpty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.ServerType = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerTypeRequired, validateResult.Errors[5].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_ServerType_InServer_IsNull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.ServerType = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerTypeNotNullRequired, validateResult.Errors[6].ErrorMessage);
    }

    //OSType

    [Fact]
    public async Task Verify_Update_OSType_InServer_WithEmpty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.OSType = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerOsTypeRequired, validateResult.Errors[6].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_OSType_InServer_IsNull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.OSType = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerOsTypeNotNullRequired, validateResult.Errors[7].ErrorMessage);
    }

    //RoleType

    [Fact]
    public async Task Verify_Update_RoleType_InServer_WithEmpty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.RoleType = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerRoleTypeRequired, validateResult.Errors[6].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_RoleType_InServer_IsNull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.RoleType = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerRoleTypeNotNullRequired, validateResult.Errors[7].ErrorMessage);
    }

    //Properties

    [Fact]
    public async Task Verify_Update_Properties_InServer_WithEmpty()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Properties = "";
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerPropertiesRequired, validateResult.Errors[7].ErrorMessage);
    }

    [Fact]
    public async Task Verify_Update_Properties_InServer_IsNull()
    {
        var validator = new UpdateServerCommandValidator(_mockServerRepository.Object, _licenseManagerRepository.Object, _mockSiteRepository.Object, _licenseValidationService.Object,_mockSiteTypeRepository.Object, _mockDatabaseViewRepository.Object);
        var server = new Domain.Entities.Server();
        _mockServerRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.Id)).ReturnsAsync(server);
        var sites = new Domain.Entities.Site();
        _mockSiteRepository.Setup(dp => dp.GetByReferenceIdAsync(_updateServerCommand.SiteId)).ReturnsAsync(sites);
        var siteType = new Domain.Entities.SiteType();
        _mockSiteTypeRepository.Setup(dp => dp.GetByReferenceIdAsync(sites.TypeId)).ReturnsAsync(siteType);
        int Index = 0;
        _mockSiteTypeRepository.Setup(dp => dp.GetSiteTypeIndexByIdAsync(siteType.ReferenceId)).ReturnsAsync(Index);
        var license = new Domain.Entities.LicenseManager();
        _licenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_updateServerCommand.LicenseId)).ReturnsAsync(license);
        int dbcount = 0;
        _mockServerRepository.Setup(dp => dp.GetServerCountByLicenseKey(_updateServerCommand.LicenseId, _updateServerCommand.RoleType, _updateServerCommand.SiteId)).ReturnsAsync(dbcount);
        bool dbLicenseCount = false;
        _licenseValidationService.Setup(dp => dp.IsDatabaseLicenseCountExitMaxLimit(license, siteType, dbcount, Index)).ReturnsAsync(dbLicenseCount);
        _updateServerCommand.Properties = null;
        

        var validateResult = await validator.ValidateAsync(_updateServerCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.Server.ServerPropertiesNotNullRequired, validateResult.Errors[8].ErrorMessage);
    }
}