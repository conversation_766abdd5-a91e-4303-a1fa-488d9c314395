using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple specification for testing
public class ReportScheduleFilterSpecification : Specification<ReportSchedule>
{
    public ReportScheduleFilterSpecification(string? searchString = null, bool? isActive = null)
    {
        if (isActive.HasValue)
        {
            Criteria = p => p.IsActive == isActive.Value;
        }
        else
        {
            Criteria = p => p.ReportName != null;
        }

        if (!string.IsNullOrEmpty(searchString))
        {
            And(p => p.ReportName.Contains(searchString) ||
                     p.ReportType.Contains(searchString) ||
                     p.NodeName.Contains(searchString));
        }
    }
}

public class ReportScheduleRepositoryTests : IClassFixture<ReportScheduleFixture>, IDisposable
{
    private readonly ReportScheduleFixture _reportScheduleFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReportScheduleRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ReportScheduleRepositoryTests(ReportScheduleFixture reportScheduleFixture)
    {
        _reportScheduleFixture = reportScheduleFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReportScheduleFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _repository = new ReportScheduleRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        var existingReportSchedules = await _dbContext.Set<ReportSchedule>().ToListAsync();
        _dbContext.Set<ReportSchedule>().RemoveRange(existingReportSchedules);
        await _dbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnReportSchedulesByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "Schedule1", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "Schedule2", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "DifferentCompany", 
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true 
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(ReportScheduleFixture.CompanyId, r.CompanyId));
        Assert.Contains(result, r => r.ReportName == "Schedule1");
        Assert.Contains(result, r => r.ReportName == "Schedule2");
        Assert.DoesNotContain(result, r => r.ReportName == "DifferentCompany");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoMatchingCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            CompanyId = "DIFFERENT_COMPANY",
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnReportSchedulesByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "Schedule1", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "Schedule2", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "DifferentCompany", 
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true 
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var query = _repository.GetPaginatedQuery();
        var result = await query.ToListAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(ReportScheduleFixture.CompanyId, r.CompanyId));
        Assert.Contains(result, r => r.ReportName == "Schedule1");
        Assert.Contains(result, r => r.ReportName == "Schedule2");
        Assert.DoesNotContain(result, r => r.ReportName == "DifferentCompany");
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnOrderedByIdDescending()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "First", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "Second", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var query = _repository.GetPaginatedQuery();
        var result = await query.ToListAsync();

        // Assert
        Assert.Equal(2, result.Count);
        // Should be ordered by Id descending (newer records first)
        Assert.True(result[0].Id > result[1].Id);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedules = new List<ReportSchedule>();
        for (int i = 1; i <= 15; i++)
        {
            reportSchedules.Add(new ReportSchedule
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = $"Schedule{i}",
                ReportType = "TestType",
                NodeName = "TestNode",
                CompanyId = i % 2 == 0 ? ReportScheduleFixture.CompanyId : "DIFFERENT_COMPANY",
                IsActive = true
            });
        }

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        var specification = new ReportScheduleFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(15, result.TotalCount); // IsParent=true should return all records
        Assert.Equal(10, result.Data.Count); // Page size
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.TotalPages); // 15 records / 10 per page = 2 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterByCompany_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule1",
                ReportType = "Type1",
                NodeName = "Node1",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule2",
                ReportType = "Type2",
                NodeName = "Node2",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "DifferentCompany",
                ReportType = "Type3",
                NodeName = "Node3",
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        var specification = new ReportScheduleFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.TotalCount); // Only records from user's company
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, r => Assert.Equal(ReportScheduleFixture.CompanyId, r.CompanyId));
        Assert.Contains(result.Data, r => r.ReportName == "Schedule1");
        Assert.Contains(result.Data, r => r.ReportName == "Schedule2");
        Assert.DoesNotContain(result.Data, r => r.ReportName == "DifferentCompany");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnSecondPage()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedules = new List<ReportSchedule>();
        for (int i = 1; i <= 25; i++)
        {
            reportSchedules.Add(new ReportSchedule
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = $"Schedule{i:D2}",
                ReportType = "TestType",
                NodeName = "TestNode",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            });
        }

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        var specification = new ReportScheduleFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(2, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(25, result.TotalCount);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(3, result.TotalPages); // 25 records / 10 per page = 3 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterBySpecification()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "ActiveSchedule1",
                ReportType = "Type1",
                NodeName = "Node1",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "ActiveSchedule2",
                ReportType = "Type2",
                NodeName = "Node2",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "InactiveSchedule",
                ReportType = "Type3",
                NodeName = "Node3",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = false
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _dbContext.ReportSchedules.AddAsync(schedule);
            _dbContext.SaveChanges();
        }

        var specification = new ReportScheduleFilterSpecification(isActive: true);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.TotalCount); // Only active records
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, r => Assert.True(r.IsActive));
        Assert.Contains(result.Data, r => r.ReportName == "ActiveSchedule1");
        Assert.Contains(result.Data, r => r.ReportName == "ActiveSchedule2");
        Assert.DoesNotContain(result.Data, r => r.ReportName == "InactiveSchedule");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterBySearchString()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "DailyReport",
                ReportType = "Daily",
                NodeName = "Node1",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "WeeklyReport",
                ReportType = "Weekly",
                NodeName = "Node2",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "MonthlyReport",
                ReportType = "Monthly",
                NodeName = "Node3",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        var specification = new ReportScheduleFilterSpecification("Daily");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.TotalCount); // Only records matching search
        Assert.Single(result.Data);
        Assert.Contains(result.Data, r => r.ReportName == "DailyReport");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldSortDescending()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "AReport",
                ReportType = "Type1",
                NodeName = "Node1",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "BReport",
                ReportType = "Type2",
                NodeName = "Node2",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "CReport",
                ReportType = "Type3",
                NodeName = "Node3",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        var specification = new ReportScheduleFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(3, result.Data.Count);
        // Should be sorted by ReportName descending
        Assert.Equal("CReport", result.Data[0].ReportName);
        Assert.Equal("BReport", result.Data[1].ReportName);
        Assert.Equal("AReport", result.Data[2].ReportName);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResult_WhenNoMatchingRecords()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            ReportType = "TestType",
            NodeName = "TestNode",
            CompanyId = "DIFFERENT_COMPANY",
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        var specification = new ReportScheduleFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(0, result.TotalCount);
        Assert.Empty(result.Data);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(0, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleNullSpecification()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            ReportType = "TestType",
            NodeName = "TestNode",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null!, "ReportName", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Data);
        Assert.Equal("TestSchedule", result.Data[0].ReportName);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnReportSchedule_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.GetByReferenceIdAsync(reportSchedule.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(reportSchedule.ReferenceId, result.ReferenceId);
        Assert.Equal(reportSchedule.ReportName, result.ReportName);
        Assert.Equal(reportSchedule.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldFilterByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "SameCompany", 
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                ReportName = "DifferentCompany", 
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true 
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var result1 = await _repository.GetByReferenceIdAsync(reportSchedules[0].ReferenceId);
        var result2 = await _repository.GetByReferenceIdAsync(reportSchedules[1].ReferenceId);

        // Assert
        Assert.NotNull(result1);
        Assert.Equal("SameCompany", result1.ReportName);
        Assert.Null(result2); // Should be null because it's from different company
    }

    #endregion

    #region IsReportScheduleNameExist Tests

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "ExistingSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameExist("ExistingSchedule", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportScheduleNameExist("NonExistentSchedule", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldReturnFalse_WhenNameExistsButIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "ExistingSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameExist("ExistingSchedule", reportSchedule.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldReturnTrue_WhenNameExistsAndIdDoesNotMatch()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "ExistingSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameExist("ExistingSchedule", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldHandleMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "DuplicateName",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "DuplicateName",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var result = await _repository.IsReportScheduleNameExist("DuplicateName", reportSchedules[0].ReferenceId);

        // Assert
        Assert.True(result); // Should return true because there's another entity with the same name
    }

    [Fact]
    public async Task IsReportScheduleNameExist_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "CaseSensitive",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameExist("casesensitive", "invalid-guid");

        // Assert
        Assert.False(result); // Should be case-sensitive
    }

    #endregion

    #region IsReportScheduleNameUnique Tests

    [Fact]
    public async Task IsReportScheduleNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "ExistingSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameUnique("ExistingSchedule");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReportScheduleNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportScheduleNameUnique("NonExistentSchedule");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReportScheduleNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "CaseSensitive",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.IsReportScheduleNameUnique("casesensitive");

        // Assert
        Assert.False(result); // Should be case-sensitive
    }

    [Fact]
    public async Task IsReportScheduleNameUnique_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReportScheduleNameUnique(null);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetReportScheduleNames Tests

    [Fact]
    public async Task GetReportScheduleNames_ShouldReturnReportSchedulesByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReportScheduleFixture.CompanyId);

        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule1",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule2",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "DifferentCompany",
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var result = await _repository.GetReportScheduleNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, r => r.ReportName == "Schedule1");
        Assert.Contains(result, r => r.ReportName == "Schedule2");
        Assert.DoesNotContain(result, r => r.ReportName == "DifferentCompany");
        // CompanyId should be null as it's not selected in the projection
        Assert.All(result, r => Assert.Null(r.CompanyId));
    }

    [Fact]
    public async Task GetReportScheduleNames_ShouldReturnOnlySpecificProperties()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            CompanyId = ReportScheduleFixture.CompanyId,
            ReportType = "SomeType",
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.GetReportScheduleNames();

        // Assert
        Assert.Single(result);
        var returnedSchedule = result[0];
        Assert.Equal(reportSchedule.ReferenceId, returnedSchedule.ReferenceId);
        Assert.Equal(reportSchedule.ReportName, returnedSchedule.ReportName);
        // ReportType should be null as it's not selected
        Assert.Null(returnedSchedule.ReportType);
    }

    [Fact]
    public async Task GetReportScheduleNames_ShouldReturnOrderedByReportName()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "ZSchedule",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "ASchedule",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "MSchedule",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _repository.AddAsync(schedule);
        }

        // Act
        var result = await _repository.GetReportScheduleNames();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Equal("ASchedule", result[0].ReportName);
        Assert.Equal("MSchedule", result[1].ReportName);
        Assert.Equal("ZSchedule", result[2].ReportName);
    }

    [Fact]
    public async Task GetReportScheduleNames_ShouldReturnEmptyList_WhenNoMatchingCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            CompanyId = "DIFFERENT_COMPANY",
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.GetReportScheduleNames();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetReportSchedulerByUserGroupId Tests

    [Fact]
    public async Task GetReportSchedulerByUserGroupId_ShouldReturnActiveReportSchedules_WhenUserGroupIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var userGroupId = "USER_GROUP_123";
        var reportSchedules = new List<ReportSchedule>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule1",
                UserProperties = $"['{userGroupId}','OTHER_GROUP']",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule2",
                UserProperties = $"['DIFFERENT_GROUP','{userGroupId}']",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "Schedule3",
                UserProperties = "['DIFFERENT_GROUP','ANOTHER_GROUP']",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReportName = "InactiveSchedule",
                UserProperties = $"['{userGroupId}']",
                CompanyId = ReportScheduleFixture.CompanyId,
                IsActive = false
            }
        };

        foreach (var schedule in reportSchedules)
        {
            await _dbContext.ReportSchedules.AddAsync(schedule);
            _dbContext.SaveChanges();
        }

        // Act
        var result = await _repository.GetReportSchedulerByUserGroupId(userGroupId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.All(result, r => Assert.Contains(userGroupId, r.UserProperties));
        Assert.Contains(result, r => r.ReportName == "Schedule1");
        Assert.Contains(result, r => r.ReportName == "Schedule2");
        Assert.DoesNotContain(result, r => r.ReportName == "Schedule3");
        Assert.DoesNotContain(result, r => r.ReportName == "InactiveSchedule");
    }

    [Fact]
    public async Task GetReportSchedulerByUserGroupId_ShouldReturnEmptyList_WhenNoMatchingUserGroupId()
    {
        // Arrange
        await ClearDatabase();
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "TestSchedule",
            UserProperties = "['DIFFERENT_GROUP']",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(reportSchedule);

        // Act
        var result = await _repository.GetReportSchedulerByUserGroupId("NON_EXISTENT_GROUP");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReportSchedulerByUserGroupId_ShouldReturnEmptyList_WhenOnlyInactiveSchedules()
    {
        // Arrange
        await ClearDatabase();
        var userGroupId = "USER_GROUP_123";
        var reportSchedule = new ReportSchedule
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ReportName = "InactiveSchedule",
            UserProperties = $"['{userGroupId}']",
            CompanyId = ReportScheduleFixture.CompanyId,
            IsActive = false
        };

        await _dbContext.ReportSchedules.AddAsync(reportSchedule);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetReportSchedulerByUserGroupId(userGroupId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReportSchedulerByUserGroupId_ShouldHandleNullUserGroupId()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetReportSchedulerByUserGroupId(null);

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
