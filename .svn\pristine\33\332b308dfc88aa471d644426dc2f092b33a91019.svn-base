﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Queries;

public class GetWorkflowCategoryPaginatedListQueryHandlerTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    private readonly GetWorkflowCategoryPaginatedListQueryHandler _handler;

    public GetWorkflowCategoryPaginatedListQueryHandlerTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        var workflowCategoryNewFixture = workflowCategoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        workflowCategoryNewFixture.WorkflowCategories[0].Name = "WFA_Test";
        workflowCategoryNewFixture.WorkflowCategories[0].Properties = "WFA_Properties";
        workflowCategoryNewFixture.WorkflowCategories[0].Version = "0.1";

        workflowCategoryNewFixture.WorkflowCategories[1].Name = "WFA_Test1";
        workflowCategoryNewFixture.WorkflowCategories[1].Properties = "WFA_Properties1";
        workflowCategoryNewFixture.WorkflowCategories[1].Version = "0.2";

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.GetPaginatedWorkflowCategoryRepository(workflowCategoryNewFixture.WorkflowCategories);

        _handler = new GetWorkflowCategoryPaginatedListQueryHandler(workflowCategoryNewFixture.Mapper, _mockWorkflowCategoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowCategoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowCategoryListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowCategories_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowCategoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "WFA" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowCategoryListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<WorkflowCategoryListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("WFA_Test");

        result.Data[0].Properties.ShouldBe("WFA_Properties");

        result.Data[0].Version.ShouldBe("0.1");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowCategoryPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowCategoryListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowCategories_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowCategoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Name=Test;Properties=WFA;Version=0.1" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowCategoryListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<WorkflowCategoryListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("WFA_Test");

        result.Data[0].Properties.ShouldBe("WFA_Properties");

        result.Data[0].Version.ShouldBe("0.1");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowCategoryPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowCategoryRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}