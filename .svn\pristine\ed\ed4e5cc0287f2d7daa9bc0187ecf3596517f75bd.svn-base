﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Events.PaginatedView;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;
using DocumentFormat.OpenXml.Bibliography;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class VeritasClusterController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<VeritasClusterController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public VeritasClusterController(IPublisher publisher,ILogger<VeritasClusterController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in VeritasCluster");
        await _publisher.Publish(new VeritasClusterPaginatedEvent());
        return View();
    }

    [HttpGet]
    public async Task<JsonResult> GetPaginated(GetVeritasClusterPaginatedListQuery query)
    {
        _logger.LogDebug("Entering List method in VeritasCluster");
        try
        {
            _logger.LogDebug("Successfully retrieved veritas cluster paginated list on VeritasCluster page");
            var result = await _dataProvider.VeritasCluster.GetPaginatedVeritasClusters(query);
            return Json(new { success = true, data = result });
        } 
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on veritas cluster page while processing the pagination request.",ex);
            return ex.GetJsonException();
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> CreateOrUpdate(VeritasClusterViewModel veritasCluster)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in VeritasCluster");
        var userId = Request.Form["id"].ToString();
        try
        {
            if (userId.IsNullOrWhiteSpace())
            {       
                var createCommand = _mapper.Map<CreateVeritasClusterCommand>(veritasCluster);
               var response = await _dataProvider.VeritasCluster.CreateAsync(createCommand);
               _logger.LogDebug($"Creating VeritasCluster '{createCommand.ClusterName}'");
               _logger.LogDebug("CreateOrUpdate operation completed successfully in VeritasCluster, returning view.");
                return Json(new { success = true, data = response });
            }
            else
            {
             
                var updateCommand = _mapper.Map<UpdateVeritasClusterCommand>(veritasCluster);
                var response = await _dataProvider.VeritasCluster.UpdateAsync(updateCommand);
                _logger.LogDebug($"Updating VeritasCluster '{updateCommand.ClusterName}'");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in VeritasCluster, returning view.");
                return Json(new { success = true, data = response });    
            }
        }
        catch (Exception ex)
        {  
            _logger.Exception("An error occurred on veritas cluster page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }


    [HttpGet]
    public async Task<JsonResult> GetServerList()
    {
        _logger.LogDebug("Entering GetServerList method in VeritasCluster");
        try
        {
            _logger.LogDebug("Successfully retrieved server list on VeritasCluster page");
            var result = await _dataProvider.Server.GetServerNames();
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on veritas cluster page while retrieving server list",ex);
            return ex.GetJsonException();
        }
    }

    [HttpDelete]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in VeritasCluster");
        try
        {
            var result = await _dataProvider.VeritasCluster.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in veritas cluster");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on veritas cluster.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> IsVeritasClusterNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsVeritasClusterNameExist method in VeritasCluster");
        try
        {
            var nameExist = await _dataProvider.VeritasCluster.IsVeritasClusterNameExist(name, id);
            _logger.LogDebug("Returning result for IsVeritasClusterNameExist on VeritasCluster");
            return Json(new { success = true, data = nameExist });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on veritas cluster page while checking if veritas clusterName exists for : {name}.", ex);
            return ex.GetJsonException(); ;
        }
    }
}

