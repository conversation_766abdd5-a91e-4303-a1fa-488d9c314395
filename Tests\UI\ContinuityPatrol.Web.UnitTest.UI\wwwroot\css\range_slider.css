.range-slider {
    width: 100%;
    margin: 0 auto;
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
}

#range {
    -webkit-appearance: none;
    width: 100%;
}

    #range:focus {
        outline: none;
    }

/*    #range::before, #range::after {
        position: absolute;
        top: 2rem;
        color: #333;
        font-size: 12px;
        line-height: 1;
        padding: 3px 5px;
        background-color: rgba(0,0,0,.1);
        border-radius: 4px;
    }

    #range::before {
        left: 0;
        content: attr(data-min);
    }

    #range::after {
        right: 0;
        content: attr(data-max);
    }
*/
    #range::-webkit-slider-runnable-track {
        width: 100%;
        height: 0.3rem;
        cursor: pointer;
        animate: 0.2s;
        background: linear-gradient(90deg, #0d6efd var(--range-progress), #dee4ec var(--range-progress));
        border-radius: 1rem;
    }

    #range::-webkit-slider-thumb {
        -webkit-appearance: none;
        border: 0.25rem solid #0d6efd;
        box-shadow: 0 1px 3px rgba(0,0,255,.3);
        border-radius: 50%;
        background: #fff;
        cursor: pointer;
        height: 16px;
        width: 16px;
        transform: translateY(calc(-50% + 2px));
    }

#tooltip {
    position: absolute;
    top: -0.99rem;
}

    #tooltip span {
        position: absolute;
        text-align: center;
        display: block;
        line-height: 1;
        padding: 0.125rem 0.25rem;
        color: #fff;
        border-radius: 0.125rem;
        background: #0d6efd;
        font-size: 0.6rem;
        left: 50%;
        transform: translate(-50%, 0);
    }

        #tooltip span:before {
            position: absolute;
            content: "";
            left: 50%;
            bottom: -8px;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border: 4px solid transparent;
            border-top-color: #0d6efd;
        }
