﻿namespace ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetByInfraObjectId;

public class DashboardViewLogByInfraObjectId
{
    public string Id { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string DataLagValue { get; set; }
    public string Status { get; set; }
    public string CurrentRTO { get; set; }
    public string CurrentRPO { get; set; }
    public string ConfiguredRPO { get; set; }
}

public class InfraObjectHealthScore
{
    public int GoodCount { get; set; }
    public int MediumCount { get; set; }
    public int BadCount { get; set; }

    public List<DashboardViewLogByInfraObjectId> DashboardViewLogByInfraObjectIds { get; set; } = new();
}