using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Events.Delete;

public class WorkflowApprovalMappingDeletedEventHandler : INotificationHandler<WorkflowApprovalMappingDeletedEvent>
{
    private readonly ILogger<WorkflowApprovalMappingDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowApprovalMappingDeletedEventHandler(ILoggedInUserService userService, ILogger<WorkflowApprovalMappingDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowApprovalMappingDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} WorkflowApprovalMapping",
            Entity = "WorkflowApprovalMapping",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"WorkflowApprovalMapping '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowApprovalMapping '{deletedEvent.Name}' deleted successfully.");
    }
}
