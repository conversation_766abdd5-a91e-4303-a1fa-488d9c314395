var createPermission = $("#manageCreate").data("create-permission").toLowerCase();
var deletePermission = $("#manageDelete").data("delete-permission").toLowerCase();
const exceptThisSymbols = ["e", "E", "+", "-", "."];
if (createPermission == 'false') {
    $(".btn-create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}
let selectedValues = [], infra_name = "", GroupPolicy = '', textupdate = "", dataTable
function drreadydebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
    dataTable = $('#tblDrReadiness').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""

            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            Sortable: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/ResiliencyReadiness/ManageResilienceReadiness/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 2 ? "infraObjectName" : sortIndex === 3 ? "scheduleTime" : sortIndex === 4 ? "beforeSwitchOverWorkflowName" : 
                        sortIndex === 5 ? "afterSwitchOverWorkflowName" : sortIndex === 6 ? "nodeName" : sortIndex === 7 ? "lastExecutionTime" : sortIndex === 8 ? "status" : sortIndex === 9 ? "state" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        infra_name = json.data.data
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    orderable: false
                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true,
                    "render": function (data, type, row, meta) {
                        return '<input type="checkbox" name="rowCheckbox" statename="' + row.state + '"  class=" ' + data.state + ' form-check-input custom-cursor-default-hover  " checkid=' + data.id + ' id="' + data.state + '">';
                    }, orderable: false
                },
                {
                    "data": "infraObjectName", "name": "InfraObject Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${data == null ? "NA" : data}" > ${data == null ? "NA" : data}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Scheduled Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `
                              <td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "beforeSwitchOverWorkflowName", "name": "Before SwitchOver Workflow ", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data == null ? "NA" : data + '">' + data == null ? "NA" : data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "afterSwitchOverWorkflowName", "name": "After SwitchOver Workflow ", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data == null ? "NA" : data + '">' + data == null ? "NA" : data + '</span>';
                        }
                        return data;
                    }
                }, {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data || "NA"}">${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {

                    "data": "lastExecutionTime","name": "Last Executed Time","autoWidth": true,
                    "render": (data) => {
                        if (!data || isNaN(Date.parse(data))) return `<td><span title="NA">NA</span></td>`;
                        let dateObj = new Date(data);
                        let formattedDate = dateObj.toLocaleString("en-GB").replace(/\//g, "-").replace(",", "");
                        return `<td><span title="${formattedDate}">${formattedDate}</span></td>`;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}" title="${data == null ? "NA" : data}" id="icon" ></i></td>
                              <td><span> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-success me-1";
                        }
                        return `<td ><i class="${iconClass}" title="${data}"  id="icon"  ></i></td>
                              <td><span  id="state"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        let errorVisible = row?.exceptionMessage === null || row?.exceptionMessage === undefined || row?.exceptionMessage === '';
                        let errmsg = row?.exceptionMessage
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="edit-button ${row?.status == "Running" ? 'form-delete-disable' : ''}" data-drready='${JSON.stringify(row)}' title="Edit">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" ? 'form-delete-disable' : ''}" data-drready-id="${row.id}" data-drready-name="${row.infraObjectName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>  
                                                  <span  id="reset" title="Reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span> 
                                                     <span title="Error Message" dr-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>     
                                </div>`;
                        }
                        else if (createPermission == "true" && deletePermission == "false") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                                            <span  role="button" class="edit-button ${row?.status == "Running" ? 'form-delete-disable' : ''}" data-drready='${JSON.stringify(row)}' title="Edit">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete"  class="btn-disabled ${row?.status == "Running" ? 'form-delete-disable' : ''}"><i class="cp-Delete"></i>
                                                </span>  
                                                  <span  id="reset" title="Reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span> 
                                                     <span title="Error Message" dr-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>     
                                </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "true") {
                            return `
                       <div class="d-flex align-items-center gap-2" title="Edit">
                                            <span  role="button" class="btn-disabled">
                                                <i class="cp-edit ${row?.status == "Running" ? 'form-delete-disable' : ''}"></i>
                                            </span>
                                            <span role="button"  class="delete-button ${row?.status == "Running" ? 'form-delete-disable' : ''}" title="Delete" data-drready-id="${row.id}" data-drready-name="${row.infraObjectName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>  
                                                  <span  id="reset" title="Reset" class="btn-disabled" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span> 
                                                     <span title="Error Message" dr-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>     
                                </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "false") {
                            return `
                       <div class="d-flex align-items-center gap-2" title="Edit">
                                            <span  role="button" class="btn-disabled ${row?.status == "Running" ? 'form-delete-disable' : ''}">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button"  class="btn-disabled ${row?.status == "Running" ? 'form-delete-disable' : ''}" title="Delete"><i class="cp-Delete"></i>
                                                </span>  
                                                  <span  id="reset" title="Reset" class="btn-disabled" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span> 
                                                     <span title="Error Message" dr-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>     
                                </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', drreadydebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
            const CompanyNameCheckbox = $("#CompanyName");
            const DisplayNameCheckbox = $("#DisplayName");
            const DisplayNameCheckbox1 = $("#DisplayName1");
            const inputValue = $('#search-inp').val();
            if (CompanyNameCheckbox.is(':checked')) {
                selectedValues.push(CompanyNameCheckbox.val() + inputValue);
            }
            if (DisplayNameCheckbox.is(':checked')) {
                selectedValues.push(DisplayNameCheckbox.val() + inputValue);
            }
            if (DisplayNameCheckbox1.is(':checked')) {
                selectedValues.push(DisplayNameCheckbox1.val() + inputValue);
            }
        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
     }, 500));
   })

$("#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").on("click", function () {
    clearCronExpressionData()
    $("#CronMin-error,#CronHourMin-error,#CroneveryMin-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error,#CronMonthMins-error,#CronHourly-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error,#CronMonthly-error,#CronMon-error,#MonthlyHours-error").text('').removeClass('field-validation-error');
})
$('#GroupPolicy').on('change', function () {
    const value = $(this).val();
    if (value == '1') {
        $('#ExecutionPolicy').show();
    }else {
        $('#ExecutionPolicy').hide();
    }
})
$('#tblDrReadiness').on('click', '.Error-button', function () {
    let noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">'
    let dr_error_message = $(this).attr('dr-error_message');
    dr_error_message = atob(dr_error_message)
    if (!dr_error_message || dr_error_message == 'null') {
        $("#error_message").css('text-align', 'center')
            .html(noData);
    } else {
        $('#error_message').text(dr_error_message);
    }
});
$('#tblDrReadiness').on('click', '.reset-button',async function () {
    var drData = $(this).data('job');
    drData.__RequestVerificationToken = gettoken()
    await $.ajax({
        url: RootUrl + 'ResiliencyReadiness/ManageResilienceReadiness/ResetManageResilienceReadinessStatus',
        type: 'POST',
        data: drData,
        success: function (result) {
            if (result.success) {
                notificationAlert("success", result.data.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                errorNotification(result)
            }
        }
    });
});

const DrReadinessService = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + 'ITAutomation/WorkflowExecution/CheckWindowsService',
        data: { type: 'ResiliencyReadyService', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result && result.success) {
                    let html = DrReadinessMessage(result)
                    notificationAlert("success", html, 'execution')
                } else {
                    notificationAlert("warning", response.message);
                }

            } else {
                errorNotification(result)
            }
        }
    })
}

const DrReadinessMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}

$(async function () {
    await DrReadinessService()
})


//InfraObjectName 
$("#selectInfraObject").on('change', async function () {
        checkinfra()
        validateDropDown($(this).val(), "Select infraobject name", $('#infraName-error'));
        let selectInfra = $("#selectInfraObject option:selected").attr('id');
        $('#infraobjectname').val(selectInfra)
        if ($('#infraobjectname').val() != "select") {
            await $.ajax({
                type: "GET",
                async: false,
                url: RootUrl + 'ResiliencyReadiness/ManageResilienceReadiness/GetWorkflowNameByInfraId',
                dataType: "json",
                data: { infraId: selectInfra },
                success: function (result) {
                    if (result.success) {
                        drBeforeWorkflowDropdown(result?.data);
                        drAfterWorkflowDropdown(result?.data);
                    } else {
                        errorNotification(result);
                    }
                },
            });
        }
    }
);
function checkinfra() {
    setTimeout(() => {
        if (textupdate != "Update") {
            let select_infraname = $("#selectInfraObject option:selected").val()
            infra_name.forEach(function (values, index) {
                let inf_name = values.infraObjectName
                if (inf_name == select_infraname) {
                    setTimeout(() => {
                        $('#infraName-error').text("Already scheduled the infraobject").addClass('field-validation-error');
                    }, 250)
                    $("#selectInfraObject").val("").trigger("change")
                    $("#drAfterWorkflowDrop,#drbeforeWorkflowDrop").empty()
                    $("#infraName-error,#afterworkflowdrope-error,#beforeswitchover-error").text('').removeClass('field-validation-error')
                    return false;
                } else {
                    $('#infraName-error').text('').removeClass('field-validation-error');
                    return true;
                }
            });
        }
    }, 200)
}
function drBeforeWorkflowDropdown(data) {
    var beforeWorkflowDrop = $('#drbeforeWorkflowDrop');
    beforeWorkflowDrop.empty().append($('<option>').val("").text(""));
    data.forEach(function (item, index) {
        if ((item.actionType == "Resiliency Ready") || (item.actionType == "Resilience Ready") || (item.actionType == "Resiliency Readiness") || item?.actionType?.toLowerCase().includes('cyber') ) {
            beforeWorkflowDrop.append($('<option>').val(item.workflowName).text(item.workflowName).attr('class', item.workflowId).attr("type", item.actionType));
        }
    });
}
function drAfterWorkflowDropdown(data) {
    var afterWorkflowDrop = $('#drAfterWorkflowDrop');
    afterWorkflowDrop.empty().append($('<option>').val("").text(""));
    data.forEach(function (item, index) {
        if ((item.actionType == "Resiliency Ready") || (item.actionType == "Resilience Ready") || (item.actionType == "Resiliency Readiness") || item?.actionType?.toLowerCase().includes('cyber')) {
            afterWorkflowDrop.append($('<option>').val(item.workflowName).text(item.workflowName).attr('class', item.workflowId).attr("type", item.actionType));
        }
    });
}
// BeforeSwitchover
$("#drbeforeWorkflowDrop").on('change',function () {
        let selectInfraObjecttext = $('#selectInfraObject').val()
        if (selectInfraObjecttext != "") {
            let selectBeforeWorkflowName = $('#drbeforeWorkflowDrop option:selected').attr('class');
            $('#workflowdrope').val(selectBeforeWorkflowName);
            //hideSelectedOption($('#drbeforeWorkflowDrop option:selected').val(), 'drAfterWorkflowDrop');
        } else {
            $('#workflowdrope').val("");
        }
    }
);
// AfterSwitchover 
$("#drAfterWorkflowDrop").on('change',function () {
        let selectInfraObjecttext = $('#selectInfraObject').val()
        if (selectInfraObjecttext != "") {
            let selectAfterWorkflowName = $('#drAfterWorkflowDrop option:selected').attr('class');
            $('#Afterworkflowdrope').val(selectAfterWorkflowName);
        } else {
            $('#Afterworkflowdrope').val("");
        }
    }
);
$('input[name=weekDays]').on('click', function (event) {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day(s)", $('#CronDay-error'));
});


$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});
//Validation
function validateDropDown(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
$('#drbeforeWorkflowDrop').on('change', function () {
    if ($(this).val() === "Select Workflow Name") {
        $(this).val() = ""
    }
    validateDropDown($(this).val(), "Select workflow name", $('#beforeswitchover-error'));
});
$('#drAfterWorkflowDrop').on('change', function () {
    if ($(this).val() === "Select Workflow Name") {
        $(this).val() = ""
    }
    validateDropDown($(this).val(), "Select workflow name", $('#afterworkflowdrope-error'));
});

$('#GroupPolicy').on('change', function () {
    GroupPolicy = $(this).val()
    $("#selectGroupPolicy").val('').trigger('change');
    $('#Policy-error').text('').removeClass('field-validation-error');
    validateDropDown($(this).val(), "Select execution policy", $('#GroupPolicy-error'));
});
$('#selectGroupPolicy').on('change', function () {
    var selectgrouppolicyid = $("#selectGroupPolicy option:selected").attr('id')
    $('#selectGroupPolicyId').val(selectgrouppolicyid);
    validateDropDown($(this).val(), "Select group node policy", $('#Policy-error'));
})
$('#txtHourss').on('input keypress', function (event) {
    if (exceptThisSymbols.includes(event.key) || $(this).val().length >= 2) {
        event.preventDefault();
    }
    validateHourNumber($(this).val(), "Enter hours", $('#CronMonthHrs-error'));
});

// Minites
$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});


// Hourly 

$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 23) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 59) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});

$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})

$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});
$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#MonthlyHours-error'));
});

$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});
$('#txtMins').on('input', function (event) {
    if (exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
    }
    if ($(this).val() == 0) {
        $('#txtMins').val("")
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});
$('#everyMinutes').on('input keypress', function (event) {
    if (exceptThisSymbols.includes(event.key) || $(this).val().length >= 2) {
        event.preventDefault();
    }
    validateMinNumber($(this).val(), "Enter minutes", $('#CroneveryMin-error'));
});
$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});
$('#ddlMinutes').on('change ', function (event) {
    if (exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
    }
    validateMinNumber($(this).val(), "Select minutes", $('#CronddlMin-error'));
});
function srvTime() {
    try {
        //FF, Opera, Safari, Chrome
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        //IE
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {
                //AJAX not supported, use CPU time.
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}

$('#datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val())
    let currentdate = new Date(srvTime())
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});
//  Month And Year
$('#lblMonth').on("change", function () {

    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    let selectedDate = new Date($(this).val()), currentDate = new Date()
    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        let data = ""
        data = i + 1
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel").each(function () {
            let checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop({ disabled: true, checked: false });
    } else {
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});

$('input[name=Monthyday]').on('click', function () {

    let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
});

function validateMonthInput() {
    let inputMonth = document.getElementById("lblMonth").value;
    let selectedDate = new Date(inputMonth);
    let currentYear = new Date().getFullYear();
    if (selectedDate.getFullYear() < currentYear) {
        $("#CronMonth-error").text("Scheduled time is greater than present time").addClass("field-validation-error")
        return false
    } else {
        $("#CronMonth-error").text("").removeClass("field-validation-error")
        return true
    }
}
$('#btnDrReadysave').on("click", function () {
    GetIsSchedule();
    Get_ScheduleTypes();
    let cyberId = 'f3fd73c4-8a61-4b16-9be4-a40d87a74c3d';
    let resiliencyId = '60256798-ae32-4bf3-84c0-2e1a6a26e196';
    let isName = validateDropDown($("#selectInfraObject").val(), "Select infraobject name", $('#infraName-error'));
    let isbefore = validateDropDown($("#drbeforeWorkflowDrop").val(), "Select workflow name", $('#beforeswitchover-error'));
    let isafter = validateDropDown($("#drAfterWorkflowDrop").val(), "Select workflow name", $('#afterworkflowdrope-error'));
    let isGroupPolicy = validateDropDown($('#GroupPolicy').val(), "Select execution policy", $('#GroupPolicy-error'));
    let isPolicy = validateDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#Policy-error'));
    let beforeOption = $('#drbeforeWorkflowDrop option:selected');
    let afterOption = $('#drAfterWorkflowDrop option:selected');
    let workflowTypeBefore = beforeOption.attr('type') || '';
    let workflowTypeAfter = afterOption.attr('type') || '';
    if (workflowTypeAfter !== workflowTypeBefore) {
        notificationAlert('warning', 'Workflow type mismatched');
        return false;
    } else {
        $("#workflowType").val(workflowTypeBefore);

        if (workflowTypeBefore.includes('Cyber')) {
            $("#workflowTypeId").val(cyberId);
        } else {
            $("#workflowTypeId").val(resiliencyId);
        }
    }
    let isScheduler = CronValidation();
    let { CronExpression, listcron } = JobCronExpression();
    $('#textCronExpression').val(CronExpression);
    $('#txtCronViewList').val(listcron);
    if (isName && isbefore && isGroupPolicy && isafter && isScheduler && ($('#GroupPolicy').val() == '1' ? isPolicy : true)) {
        $('#drReadysaveform').trigger("submit");
    }
});

//Save
//$('#btnDrReadysave').on("click", function () {
//    GetIsSchedule();
//    Get_ScheduleTypes();
//    let cyberId = 'f3fd73c4-8a61-4b16-9be4-a40d87a74c3d';
//    let resiliencyId = '60256798-ae32-4bf3-84c0-2e1a6a26e196';

//    let isName = validateDropDown($("#selectInfraObject").val(), "Select infraobject name", $('#infraName-error'));

//    let isbefore = validateDropDown($("#drbeforeWorkflowDrop").val(), "Select workflow name", $('#beforeswitchover-error'));

//    let isafter = validateDropDown($("#drAfterWorkflowDrop").val(), "Select workflow name", $('#afterworkflowdrope-error'));

//    let isGroupPolicy = validateDropDown($('#GroupPolicy').val(), "Select execution policy", $('#GroupPolicy-error'));

//    let isPolicy = validateDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#Policy-error'));
//    let workflowTypeBefore = $('#drbeforeWorkflowDrop option:selected').attr('type')
//    let workflowTypeAfter = $('#drAfterWorkflowDrop option:selected').attr('type')
  
    
//    if (workflowTypeAfter !== workflowTypeBefore) {
//        notificationAlert('warning', 'Workflow type mismatched')
//        return false;
//    } else {
//        $("#workflowType").val(workflowTypeBefore)
//        if (workflowTypeBefore.includes('Cyber')) {
//            $("#workflowTypeId").val(cyberId)
//        } else {
//            $("#workflowTypeId").val(resiliencyId)
//        }
//    }

//    let isScheduler = CronValidation();
//    let { cronExpression, cronListView } = GetCronExpression();
//    $('#textCronExpression').val(cronExpression);
//    $('#txtCronViewList').val(cronListView);
//    if (isName && isbefore && isGroupPolicy && isafter && isScheduler && (GroupPolicy == '1' ? isPolicy : true) ) {
//        $('#drReadysaveform').trigger("submit");
//    }
//});
function GetIsSchedule() {
    let schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#textIsSchedule').val(1);
    } else {
        $('#textIsSchedule').val(2);
    }
}
//Update
$('#tblDrReadiness').on('click', '.edit-button',function () {
    let DrReadinessdata = $(this).data("drready");
        populateModalFieldss(DrReadinessdata);
        Tabselection(DrReadinessdata);
        Tab_schedule_type(DrReadinessdata);
        $('#btnDrReadysave').text("Update");
        ClearErrorElements();
        $('#CreateModal').modal('show');
        textupdate = $("#btnDrReadysave").text()
    });
//Delete
$('#tblDrReadiness').on('click', '.delete-button', function () {
    let drReadyinessId = $(this).data("drready-id");
    let drReadinessName = $(this).data("drready-name")
    $("#deleteData").attr("title", drReadinessName).text(drReadinessName);
    $('#textDeleteId').val(drReadyinessId);
});
function populateModalFieldss(DrReadinessdata) {
    $("#txtlastexecutetime").val(DrReadinessdata.lastExecutionTime)
    $('#infraobjectname').val(DrReadinessdata.infraObjectId);
    $('#text_companyid').val(DrReadinessdata.companyId);
    $('#selectInfraObject').val(DrReadinessdata.infraObjectName);
    $('#selectInfraObject').trigger('change')
    $('#textDrReadyId').val(DrReadinessdata.id);
    $('#drbeforeWorkflowDrop').val(DrReadinessdata.beforeSwitchOverWorkflowName).trigger('change');
    $('#Afterworkflowdrope').val(DrReadinessdata.afterSwitchOverWorkflowId);
    $('#drAfterWorkflowDrop').val(DrReadinessdata.afterSwitchOverWorkflowName).trigger('change');
    $('#workflowdrope').val(DrReadinessdata.beforeSwitchOverWorkflowId);
    $('#textStatus').val("Pending");
    $('#selectGroupPolicy').val(DrReadinessdata.groupPolicyName).trigger('change')
    $('#selectGroupPolicyId').val(DrReadinessdata.groupPolicyId)
    $('#txtNodeId').val(DrReadinessdata.nodeId)
    $('#txtNodeName').val(DrReadinessdata.nodeName)
    $('#GroupPolicy').val(DrReadinessdata.executionPolicy)
    if (DrReadinessdata.executionPolicy == '1') {
        $('#ExecutionPolicy').show()
        $('#selectGroupPolicy').val(DrReadinessdata.groupPolicyName)
    }
    else {
        $('#ExecutionPolicy').hide()
    }
    let scheduleTime = DrReadinessdata?.scheduleTime?.split(" ")
    setTimeout(() => {

        if (DrReadinessdata.scheduleTime?.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }
        if (DrReadinessdata.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }

        if (scheduleTime.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }

        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (DrReadinessdata.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (DrReadinessdata.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
        }


        if (scheduleTime.length >= 12) {
            var year = parseInt(scheduleTime[12])
            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;

            $("#lblMonth").val(newdate).trigger('change')
            scheduleTime[5]?.split(",").forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)
}
//Cron Tab selection
function Tabselection(DrReadinessdata) {
    if (DrReadinessdata.isSchedule == 2) {
        Drready_SM1 = document.getElementById("switchMonthly");
        Drready_SM1.checked = true;
        var elementToHide1 = document.getElementById("monthgroup");
        elementToHide1.style.display = "block";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "none";
    } else {
        Drready_SM2 = document.getElementById("switchYearly");
        Drready_SM2.checked = true;
        var elementToHide11 = document.getElementById("monthgroup");
        elementToHide11.style.display = "none";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "block";
    }
}
//once Cron Build
function DateTimeCronBuilder(datetime) {
    let splitDate = datetime.split("T");
    let cronDate = splitDate[0].split("-");
    let cronTime = splitDate[1].split(":");
    let cronYear = cronDate[0];
    let cronMonth = cronDate[1];
    let cronDay = cronDate[2];
    let cronHours = cronTime[0];
    let cronMin = cronTime[1];
    let cronmonthexp = cronDate[1] == "01" ? "JAN" : cronDate[1] == "02" ? "FEB" : cronDate[1] == "03" ? "MAR" : cronDate[1] == "04" ? "APR" :
        cronDate[1] == "05" ? "MAY" : cronDate[1] == "06" ? "JUN" : cronDate[1] == "07" ? "JUL" : cronDate[1] == "08" ? "AUG" : cronDate[1] == "09" ? "SEP" :
            cronDate[1] == "10" ? "OCT" : cronDate[1] == "11" ? "NOV" : cronDate[1] == "12" ? "DEC" : ""
    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear
    cronListView = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronmonthexp + ", only in " + cronYear;
    return { CronExpression, cronListView };
}
//Cron Validation
function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    var everyHours = $('#everyHours').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var isScheduler = '';

    $('#datetimeCron').val('');
    var Scheduler_types = $('.nav-tabs .active').text().trim();
    switch (Scheduler_types) {
        case "Minutes":
            isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
            break;
        case "Hourly":
            isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
            isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
            break;
        case "Daily":
            isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
            isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
            if (isSchedulerHour && isSchedulerDay) {
                isScheduler = true;
            }
            break;
        case "Weekly":
            isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
            isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
            if (isSchedulerHour && isSchedulerDay) {
                isScheduler = true;
            }
            break;
        case "Monthly":
            isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
            isSchedulerDay = validateDayNumber(txtmonthday, "Select date", $('#CronMon-error'));
            isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
            if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                isScheduler = true;
            }
            break;
    }

    return isScheduler;
}
//Clear data
$("#drbtn_create").on('click', function () {
    $('#btnDrReadysave').text("Save");
    $('#ExecutionPolicy').hide();
    DrreadyOnce()
    $("#drbeforeWorkflowDrop,#drAfterWorkflowDrop").empty()
});
$(".drbtn_cancel").on("click", function () {
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
    clearDrReadyField();
})
$("#nav-Monthly-tab").on("click", function () {
    if ($("#btnDrReadysave").text() == "Save") {
        $('input[name=Days]').attr('disabled', 'disabled');
    }
})
function DrreadyOnce() {
    Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    var elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    var elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
$("#switchMonthly,#switchYearly").on("click", function () {
    $('#datetimeCron').val('');
    ClearErrorElements();
    clearCronExpressionData();
})
const clearDrReadyField = () => {
    $("#textDrReadyId,#txtNodeId,#txtNodeName,#datetimeCron,#GroupPolicy").val('');
    $('#selectInfraObject option:first').prop('selected', 'selected');
    $('#drbeforeWorkflowDrop option:first').prop('selected', 'selected');
    $('#drAfterWorkflowDrop option:first').prop('selected', 'selected');
    $('#selectGroupPolicy option:first').prop('selected', 'selected');
    $('#txtMins').prop('selected', 'selected');
    $('#btnDrReadysave').text("Save");
    ClearErrorElements();
    clearCronExpressionData();
};
const clearCronExpressionData = () => {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#ddlMinutes,#everyHours,#everyMinutes,#datetimeCron,#textCronExpression,#txtHourss,#txtMinss,#MonthlyHours").val('');
    $('#lblMonth').val("").trigger("change")
    $('input[name=weekDays]').prop("checked", false)
    $('input[name=Monthyday]').prop("checked", false)
    $('input[name=daysevery]').prop("checked", false)
};
function ClearErrorElements() {
    $("#infraName-error,#workflowtype-error,#beforeswitchover-error,#afterworkflowdrope-error,#CronHourly-error,#CronHourMin-error,#CroneveryHour-error, #CroneveryMin-error,#CronddlMin-error,#CronddlHour-error,#CronDay-error,#CronMin-error,#CronMonth-error,#CronMonthlyDay-error,#CronMonthHrs-error, #CronMonthMins-error,#GroupPolicy-error,#Policy-error,#CronExpression-error,#MonthlyHours-error").text('').removeClass('field-validation-error');
}
let monthInput = document.getElementById("lblMonth");
var today = new Date();
let currentYear = today.getFullYear();
let currentMonth = today.getMonth() + 1;
let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth);
$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        if (e.target.checked) {
            $('input[name="rowCheckbox"]').prop("checked", true);
        } else {
            $('input[name="rowCheckbox"]').prop("checked", false)
        }
    }, 100)
})
$("#tblDrReadiness").on('change', 'input[name="rowCheckbox"]', function () {
    $('input[name="checkboxAll"]').prop("checked", false)
})
$('#Activebtn').on('click',async  function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind.forEach((obj, idx) => {
        if (obj.checked && obj.id != "Active") {
            datas.push({
                Id: obj.getAttribute("checkid"),
                State: "Active",
            })
        }
    })
    if (datas.length) {
       await $.ajax({
            url: "/ResiliencyReadiness/ManageResilienceReadiness/UpdateManageResilienceReadinessState",
            type: 'PUT',
            data: { "updateInfraObjectSchedulerStates": datas },
            success: function (result) {
                if (result.success) {
                    var data = result.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            }
        });
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Manage Resilience Readiness states has already updated to 'Active' state")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
$('#Inactivebtn').on('click',async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind.forEach((obj, idx) => {
        if (obj.checked && obj.id != "InActive") {
            datas.push({
                Id: obj.getAttribute("checkid"),
                State: "InActive",
            })
        }
    })
    if (datas.length) {
       await $.ajax({
            url: "/ResiliencyReadiness/ManageResilienceReadiness/UpdateManageResilienceReadinessState",
            type: 'PUT',
            data: {
                "updateInfraObjectSchedulerStates": datas
            },
            success: function (result) {
                if (result.success) {
                    var data = result.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            }
        })
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Manage Resilience Readiness states has already updated to 'InActive' state")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
var datetimeInput = $('#datetimeCron');
var minYear = new Date().getFullYear()
var maxYear = new Date().getFullYear() + 76;
var minDateString = minYear + "-01-01T00:00";
var maxDateString = maxYear + "-12-31T23:59";
datetimeInput.attr('min', minDateString);
datetimeInput.attr('max', maxDateString);
var today = new Date().toISOString().slice(0, 16);
document.getElementsByName("datetime_currentdate")[0].min = today;



//function hideSelectedOption(selectedValue, workflowDropdown) {
//    if (selectedValue !== "Select Workflow Name") {
//        let WorkflowName = selectedValue;
//        let select2Options = document.getElementById(workflowDropdown).options;
//        for (let i = 1; i < select2Options.length; i++) {
//            if (select2Options[i].value === WorkflowName) {
//                select2Options[i].disabled = true;
//            } else {
//                select2Options[i].disabled = false;
//            }
//        }
//    } else {
//        let select2Options = document.getElementById(workflowDropdown).options;
//        for (let i = 1; i < select2Options.length; i++) {
//            select2Options[i].disabled = false;
//        }
//    }
//    if ($('#drbeforeWorkflowDrop option:selected').val() == $('#drAfterWorkflowDrop option:selected').val()) {
//        $('#drAfterWorkflowDrop ').val("").trigger("change")
//        $("#afterworkflowdrope-error").text("Should not same as before switchover").addClass('field-validation-error')
//        return false
//    } else {
//        $("#afterworkflowdrope-error").text("").removeClass('field-validation-error');
//    }
//}
//function ValidateCronRadioButton(errorElement) {
//    if ($('input[name=daysevery1]:checked').length > 0) {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//    else {
//        errorElement.text("Select day type").addClass('field-validation-error');;
//        return false;
//    }
//}
//function validateDayNumber(value, errorMsg, errorElement) {
//    if (!value || value == 0) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    }
//    else {
//        errorElement.text('').removeClass('field-validation-error')
//        return true;
//    }
//}

// Cron builder
//function GetCronExpression() {
//    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
//    let txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
//    let checkedDaysCheckboxes = document.querySelectorAll('[name="Days"]:checked');
//    let txtDays = Array.from(checkedDaysCheckboxes).map(checkbox => checkbox.value);

//    let cronExpression = "";
//    let cronListView = "";

//    let Minutes = $('#txtMins').val();

//    let txtHours = $('#txtHours').val();
//    txtHours = txtHours === "00" ? "0" : txtHours === "01" ? "1" : txtHours === "03" ? "3" : txtHours === "04" ? "4" :
//        txtHours === "05" ? "5" : txtHours === "06" ? "6" : txtHours === "07" ? "7" : txtHours === "08" ? "8" :
//            txtHours === "09" ? "9" : txtHours;

//    let txtHourMinutes = $('#txtMinutes').val();
//    txtHourMinutes = txtHourMinutes === "00" ? "0" : txtHourMinutes === "01" ? "1" : txtHourMinutes === "03" ? "3" :
//        txtHourMinutes === "04" ? "4" : txtHourMinutes === "05" ? "5" : txtHourMinutes === "06" ? "6" :
//            txtHourMinutes === "07" ? "7" : txtHourMinutes === "08" ? "8" : txtHourMinutes === "09" ? "9" : txtHourMinutes;

//    let weekhm = $('#ddlHours').val().split(":");
//    let ddlHours = weekhm[0];
//    let ddlMinutes = weekhm[1];

//    let everyHours = $('#everyHours').val();
//    let everyMinutes = $('#everyMinutes').val();
//    let weekDay = $('#defaultCheck-MON-FRI').val();
//    let datetime = $('#datetimeCron').val();
//    let month = $('#lblMonth').val();
//    let monthYear = month.split("-");
//    let monthHrs = $('#txtHourss').val();
//    let monthMints = $('#txtMinss').val();
//    let schedule_model = document.querySelector('input[name="daysevery1"]:checked');

//    if (datetime !== '') {
//        let result = DateTimeCronBuilder(datetime);
//        cronExpression = result.CronExpression;
//        cronListView = result.cronListView;
//    } else {
//        if (Minutes !== '') {
//            cronExpression = "0 0/" + Minutes + " * * * ?";
//            cronListView = "Every " + Minutes + " minutes";
//        } else if (txtHours !== '') {
//            cronExpression = "0 " + txtHourMinutes + " 0/" + txtHours + " * * ?";
//            cronListView = "Every " + txtHours + " hours, every " + txtHourMinutes + " minutes";
//        } else if (txtDay.length > 0) {
//            cronExpression = "0 " + ddlMinutes + " 0/" + ddlHours + " ? * " + txtDay.join(',') + " *";
//            cronListView = txtDay.join(', ') + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
//        } else if (txtDays.length > 0) {
//            let monthName = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"][parseInt(monthYear[1], 10) - 1];
//            cronExpression = "0 " + monthMints + " " + monthHrs + " " + txtDays.join(',') + " " + monthYear[1] + " ? " + monthYear[0];
//            cronListView = monthHrs + " hours " + monthMints + " minutes for " + txtDays.join(', ') + " day(s) on " + monthName + " in the year " + monthYear[0];
//        } else if (schedule_model?.value === "everyday") {
//            cronExpression = "0 " + everyMinutes + " 0/" + everyHours + " * * ?";
//            cronListView = "Every day at " + everyHours + " hours " + everyMinutes + " minutes";
//        } else if (schedule_model?.value === "MON-FRI") {
//            cronExpression = "0 " + everyMinutes + " 0/" + everyHours + " ? * " + weekDay;
//            cronListView = "MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes";
//        }
//    }

//    return { cronExpression, cronListView };
//}

//function parseMinCronExpression(expression) {
//    const parts = expression.split(' ');
//    const minutes = parseInt(parts[1].substring(2));
//    const hours = parseInt(parts[2].substring(2));
//    const day = parseInt(parts[3].substring(2));
//    return { hours, minutes, day };
//}
//function dayconventor(day) {
//    const daysMap = {
//        MON: 1,
//        TUE: 2,
//        WED: 3,
//        THU: 4,
//        FRI: 5,
//        SAT: 6,
//        SUN: 0
//    };
//    const days = day.split(',');
//    days.forEach(day => {
//        const checkboxId = `#defaultCheck-${daysMap[day]}`;
//        $(checkboxId).prop("checked", true);
//    });
//}
//function monthDayconventor(days) {
//    const day = days;
//    let checkboxes = document.querySelectorAll('input[name="Days"]');
//    checkboxes.forEach(function (checkbox) {
//        if (day.includes(checkbox.value)) {
//            checkbox.checked = true;
//        }
//    });
//};
//function Get_ScheduleTypes() {
//    var Scheduler_types = $('.nav-tabs .active').text().trim();
//    switch (Scheduler_types) {
//        case "Minutes":
//            $('#textScheduleType').val(1);
//            break;
//        case "Hourly":
//            $('#textScheduleType').val(2);
//            break;
//        case "Daily":
//            $('#textScheduleType').val(3);
//            break;
//        case "Weekly":
//            $('#textScheduleType').val(4);
//            break;
//        case "Monthly":
//            $('#textScheduleType').val(5);
//            break;
//    }
//}
//function Tab_schedule_type(DrReadinessdata) {
//    let types = DrReadinessdata.scheduleType;
//    let clickedLink = "";
//    let linkId = "";
//    if (DrReadinessdata.isSchedule == 1) {
//        let datetime = DateTimeCronConventor(DrReadinessdata.cronExpression)
//        $('#datetimeCron').val(datetime)
//    }
//    else {
//        switch (types) {
//            case 1:
//                linkId = "nav-Minutes-tab";
//                setTimeout(() => {
//                    clickedLink = document.getElementById(linkId);
//                    clickedLink.click();
//                    const { minutes } = parseMinCronExpression(DrReadinessdata.scheduleTime);
//                    document.getElementById("txtMins").value = minutes;
//                }, 150)
//                break;
//            case 2:
//                linkId = "nav-Hourly-tab";
//                setTimeout(() => {
//                    clickedLink = document.getElementById(linkId);
//                    clickedLink.click();
//                    const { hours, minutes } = parseCronExpression(DrReadinessdata.scheduleTime);
//                    document.getElementById("txtHours").value = hours;
//                    document.getElementById("txtMinutes").value = minutes;
//                }, 150)
//                break;
//            case 3:
//                linkId = "nav-Daily-tab";
//                setTimeout(() => {
//                    clickedLink = document.getElementById(linkId);
//                    clickedLink.click();
//                    const { hours, minutes, day } = parseCronExpression(DrReadinessdata.scheduleTime);
//                    document.getElementById("everyHours").value = hours;
//                    document.getElementById("everyMinutes").value = minutes;
//                    if (day == "?") {
//                        $("#defaultCheck-everyday").prop("checked", true);
//                    }
//                    else {
//                        $("#defaultCheck-MON-FRI").prop("checked", true);
//                    }
//                }, 150)
//                break;
//            case 4:
//                linkId = "nav-Weekly-tab";
//                setTimeout(() => {
//                    clickedLink = document.getElementById(linkId);
//                    clickedLink.click();
//                    const { hours, minutes, day } = parseCronExpression(DrReadinessdata.scheduleTime);
//                    document.getElementById("ddlHours").value = hours;
//                    dayconventor(day);
//                }, 150)
//                break;
//            case 5:
//                linkId = "nav-Monthly-tab";
//                setTimeout(() => {
//                    clickedLink = document.getElementById(linkId);
//                    clickedLink.click();
//                    const { minutes, hours, month, days } = parseCronMonthExpression(DrReadinessdata.scheduleTime);
//                    document.getElementById("txtHourss").value = hours;
//                    document.getElementById("txtMinss").value = minutes;
//                    document.getElementById("lblMonth").value = month;
//                    monthDayconventor(days);
//                }, 150)
//                break;
//        }
//    }
//}
//function parseCronExpression(expression) {
//    const parts = expression.split(' ');
//    const minutes = parseInt(parts[1]);
//    const hours = parseInt(parts[2].substring(2));
//    const day = parts[5];
//    return { hours, minutes, day };
//}
//function parseCronMonthExpression(expression) {
//    const parts = expression.split(' ');
//    const minutes = parseInt(parts[1]);
//    const hours = parseInt(parts[2]);
//    const month = parts[6] + "-" + parts[4];
//    const days = parts[3];
//    return { minutes, hours, month, days };
//}
//function DateTimeCronConventor(cron) {
//    let splitcron = cron.split(" ");
//    let cronYear = splitcron[6];
//    let yearPart;
//    if (cronYear && cronYear.length >= 5) {
//        yearPart = cronYear.slice(0, 4);
//    }
//    else if (cronYear) {
//        yearPart = cronYear;
//    }
//    else {
//        yearPart = "";
//    }
//    let cronMonth = splitcron[4];
//    let cronDay = splitcron[3];
//    let cronHours = splitcron[2];
//    let cronMin = splitcron[1];
//    let cronDate = yearPart + "-" + cronMonth + "-" + cronDay + "T" + cronHours + ":" + cronMin
//    return cronDate
//}
//function validateMiniteJobNumber(value, errorMsg, errorElement) {
//    if (!value) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    }else if ((Number(value) < 0) || (Number(value) > 59)) {
//        errorElement.text("Enter value between 1 to 59").addClass('field-validation-error');
//        return false;
//    } else if (Number(value) == 0) {
//        errorElement.text("Enter the value more than 0").addClass('field-validation-error');
//        return false;
//    }else {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//}
//function validateMinNumber(value, errorMsg, errorElement) {
//    if (!value) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    } else if (value.length > 2) {
//        errorElement.text("Value should be 2 digit").addClass('field-validation-error');
//        return false;
//    } else if ((Number(value) < 0) || (Number(value) >= 60)) {
//        errorElement.text("Enter value between 0 to 59").addClass('field-validation-error');
//        return false;
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//}
//function validateHourNumber(value, errorMsg, errorElement) {
//    if (!value) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    } else if (value.length > 2) {
//        errorElement.text("Value should be 2 digit").addClass('field-validation-error');
//        return false;
//    } else if ((Number(value) == 0)) {
//        errorElement.text("Enter value greater than zero").addClass('field-validation-error');
//        return false;
//    }else if ((Number(value) < 1) || (Number(value) >= 24)) {
//        errorElement.text("Enter value between 1 to 23").addClass('field-validation-error');
//        return false;
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//}
//function validationhourweek(value, errorMsg, errorElement) {
//    if (!value && value.length == 0) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//}
//const exceptThisSymbols = ["e", "E", "+", "-", "."];
//function validateDayNumber(value, errorMsg, errorElement) {
//    if (!value || value.length == 0) {
//        errorElement.text(errorMsg).addClass('field-validation-error');
//        return false;
//    }
//    else {
//        errorElement.text('').removeClass('field-validation-error');
//        return true;
//    }
//}
//function validateprevNumber(value, errorMsg, errorElement) {
//    let selectdate = new Date(value)
//    let currentdate = new Date()
//    if (selectdate > currentdate) {
//        $('#CronExpression-error').text('').removeClass('field-validation-error');
//        return true;
//    } else if (selectdate < currentdate) {
//        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
//        return false;
//    }

//}






