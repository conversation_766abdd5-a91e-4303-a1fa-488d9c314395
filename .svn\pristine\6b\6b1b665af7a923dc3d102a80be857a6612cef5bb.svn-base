﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;

public class GetWorkflowOperationGroupPaginatedListQueryHandler : IRequestHandler<
    GetWorkflowOperationGroupPaginatedListQuery, PaginatedResult<WorkflowOperationGroupListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationGroupRepository _workflowOperationInfraRepository;

    public GetWorkflowOperationGroupPaginatedListQueryHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationInfraRepository)
    {
        _mapper = mapper;
        _workflowOperationInfraRepository = workflowOperationInfraRepository;
    }

    public async Task<PaginatedResult<WorkflowOperationGroupListVm>> Handle(
        GetWorkflowOperationGroupPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _workflowOperationInfraRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowOperationGroupFilterSpecification(request.SearchString);

        var workflowOperationInfraList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowOperationGroupListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowOperationInfraList;
    }
}