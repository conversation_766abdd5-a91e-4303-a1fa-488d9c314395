﻿const approverURL = {
    nameExist: "Manage/Approval/IsApprovalMatrixUsersNameExist",
    getPaginatedlist: "/Manage/Approval/GetPaginatedlist",
    getUserByID: "Manage/Approval/GetUserByID",
    create: "Manage/Approval/Create",
    getUsersList: "Manage/Approval/GetApprovalMatrixUsersList",
    getBusinessServiceList: "Manage/Approval/GetBusinessServiceList",
    delete: "Manage/Approval/Delete",
}
let approverButtonDisable = false;
let dataTable = "";
let newApproverData = "";
let addedApproverData = [];
let noDataFount = `<div class="d-flex flex-column align-items-center justify-content-center NoDataContainer mt-5">
                       <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
                       <span>No matching records found</span>
                   </div>`;
const errorElements = ['#UserName-error', '#Email-error', '#Mobile-error', '#BusinessService-error', "#MobilePre-error"];
let approverdata = {
    ApprovalMatrixUsers: []
}
let countrycode;

$(function () {
    approverPreventSpecialKeys('#search-inp');
    let selectedValues = [];
    dataTable = $('#ApproverTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "fixedColumns": {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": approverURL.getPaginatedlist,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "userName" : sortIndex === 2 ? "userType" : sortIndex === 3 ?
                        "acceptType" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    const isEmpty = json?.data?.length === 0;
                    $(".pagination-column").toggleClass("disabled", isEmpty);
                    return json?.data;
                }
            },
            //"columnDefs": [
            //    {
            //        "targets": [0,1, 2, 3,4],
            //        "className": "truncate"
            //    }
            //],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "userName", "name": "User Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || "NA"}'>  ${data || "NA"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "userType", "name": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span>  ${data || "NA"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "Description", "name": "Deligates to", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span>  ${data || "-"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        return `<div class="d-flex align-items-center gap-2">
                                        <span role="button" title="${row.userType.toLowerCase() === "cp-user" ? '' : "Edit"}" class="${row.userType.toLowerCase() === "cp-user" ? 'icon-disabled' : "edit-button"}" data-site='${JSON.stringify(row)}'>
                                            <i class="cp-edit"></i>
                                        </span>
                                        <span role="button" title="Delete" class="delete-button" data-site-id="${row.id}" data-site-name="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>                                        
                                       <span role="button" title="Deligate to" class="deligate-button" data-site-id="${row.id}" data-site-name="${row.userName}">
                                            <i class="cp-deligate"></i>
                                        </span>                                      
                              </div>`;
                    },
                    "orderable": false
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('input', commonDebounce(function (e) {
        const NameCheckbox = $("#Name");

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $(document).on("click", ".ADAddUser", commonDebounce(async function () {
        let $this = $(this);
        let className = $this.attr("class");
        let dataID = $this.attr("data-id");
        let dataName = $this.attr("data-name").replaceAll(" ", "").toLowerCase();

        if (className.includes("btn-primary")) {
            $this.removeClass("btn-primary").addClass("btn-danger").text("Remove");
            await $.ajax({
                type: "GET",
                url: RootUrl + approverURL.getUserByID,
                data: { id: dataID },
                dataType: "json",
                success: function (response) {
                    if (response.success) {
                        let result = response.data;
                        let approval = {
                            "Id": "",
                            "UserName": result.loginName,
                            "Email": result.userInfo.email || "NA",
                            "MobileNumber": result.userInfo.mobile || "NA",
                            "BusinessServiceProperties": "NA",
                            "UserType": "CP-User",
                            "AcceptType": "NA",
                            "IsLink": true,
                        }
                        approverdata.ApprovalMatrixUsers.push(approval);
                    }
                }
            });
        }
        else {
            $this.removeClass("btn-danger").addClass("btn-primary").text("Add");
            if (approverdata.ApprovalMatrixUsers.length) {
                approverdata.ApprovalMatrixUsers = approverdata.ApprovalMatrixUsers.filter(user => user.UserName.replaceAll(" ", "").toLowerCase() !== dataName);
            }
        }
    }));

    $("#profile-tab").on("click", function () {
        $("#AMUserType").val("Anonymous");
        $("#searchElement").addClass("d-none")
    });

    $("#SaveFunction").on('click', async function () {
        if ($("#AMUserType").val() === "CP-User") {
            if (approverdata.ApprovalMatrixUsers.length && !approverButtonDisable) {
                approverButtonDisable = true;
                await $.ajax({
                    type: 'Post',
                    url: RootUrl + approverURL.create,
                    dataType: "json",
                    data: approverdata,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            $("#adduserModal").modal("hide");
                            dataTableCreateAndUpdate($("#SaveFunction"), dataTable);
                        } else {
                            errorNotification(result);                           
                        }
                        approverButtonDisable = false;
                    },
                });
            }
        } else {
            let name = $("#UserName").val();
            let eMail = $("#mail").val();
            let businessService = $("#selectBusinessService").val();
            let countryCode = $('#mobilepre').val();
            let mobileNumber = $("#mobilenum").val();
            let mobileNumberWithCountryCode = countryCode + ' ' + mobileNumber;
            let isChecked = $("input[name='flexCheckDefault']").prop("checked") ? "true" : "false";
            let SiteSanitizeArray = ['UserName', 'mail', 'mobilenum', 'mobilepre,selectBusinessService'];

            const isName = await validateUserName(name, $('#userNameId').val(), $('#UserName-error'));
            const isEmail = await validateEmail(eMail, 'Enter email', $('#Email-error'));
            const isCountryCode = validateMobilePre(countryCode)
            const isMobileNumber = validateMobile(mobileNumber, 'Enter mobile number', $('#Mobile-error'));
            const isBusinessService = validateDropDown(businessService, 'Select operational service', $('#BusinessService-error'));

            sanitizeContainer(SiteSanitizeArray);

            $("#AMMobileNumber").val(mobileNumberWithCountryCode);
            $("#AMEMail").val(eMail);
            $("#AMUsername").val(name);
            $("#AMIsLink").val(isChecked);

            if (isName && isEmail && isMobileNumber && isBusinessService && isCountryCode && !approverButtonDisable) {
                approverButtonDisable = true;
                let anonymous = {
                    "Id": $("#userNameId").val(),
                    "MobileNumber": mobileNumberWithCountryCode,
                    "Email": eMail,
                    "UserName": name,
                    "IsLink": isChecked,
                    "UserType": $("#AMUserType").val(),
                    "AcceptType": $("#AMAccessType").val(),
                    "BusinessServiceProperties": businessService,
                }
                approverdata.ApprovalMatrixUsers.push(anonymous);
                let createOrUpdate = $("#userNameId").val() ? "Update" : "Create";
                let createOrUpdateData = createOrUpdate === "Update" ? anonymous : approverdata;
                await $.ajax({
                    type: 'Post',
                    url: RootUrl + `Manage/Approval/${createOrUpdate}`,
                    dataType: "json",
                    data: createOrUpdateData,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            $("#adduserModal").modal("hide");                           
                            dataTableCreateAndUpdate($("#SaveFunction"), dataTable);
                        } else {
                            errorNotification(result);                           
                        }
                        approverButtonDisable = false;
                    },
                });
            }
        }
    });

    // Edit
    $('#ApproverTable').on('click', '.edit-button', function () {
        updateSiteType($(this).data('site'));
        $('#SaveFunction').text('Update');
        $('#adduserModal').modal('show');
        $('#home-tab').removeClass('active').addClass('disabled');
    });

    //Delete
    $('#ApproverTable').on('click', '.delete-button', function () {
        const ApprovalName = $(this).data('site-name');
        $("#deleteData").attr("title", ApprovalName);
        $('#deleteData').text(ApprovalName);
        $('#textDeleteId').val($(this).data('site-id'));
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#approvalDelete')[0];
        const formData = new FormData(form);

        if (!approverButtonDisable) {
            approverButtonDisable = true;
            const response = await $.ajax({
                type: "DELETE",
                url: RootUrl + approverURL.delete,
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });           
            $("#DeleteModal").modal("hide");

            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                errorNotification(response);
            }
            approverButtonDisable = false;
        }
    });

    $('#Company-CreateButton').on('click', function () {
        $('#UnaddedTitle').addClass('disabled');
        $('#home-tab').removeClass('disabled');
        $('#addserApprovalTag').hide();
        $('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');
        GetBusinessServiceList()
        clearInputFields('CreateForm', errorElements);
        $('#SaveFunction').text('Save');
        $("#AMUserType").val('CP-User');
        $("#home-tab").trigger("click");
        addCPUser();
    });

    $('#UserName').on('input', function () {
        siteId = $('#textSiteTypeId').val();
        validateUserName($(this).val(), $('#userNameId').val(), '#UserName-error');
    });

    $('#mobilenum').on('keypress keyup', function (event) {
        const value = $(this).val();

        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        if (!/^[0-9]+$/.test(event.key)) {
            event?.preventDefault();
        }
        validateMobile(value);
    });

    $('#mobilepre').on('change', function (event) {
        validateMobilePre($(this).val());
    });

    $('#mail').on('keyup', async function () {
        let value = $(this).val();
        $(this).val(value.replace(/\s{2,}/g, ' '));
        await validateEmail(value, $('#textLoginId').val());
    });

    $('#selectBusinessService').on('change', function (event) {
        validateDropDown($(this).val(), "Select operational service", $('#BusinessService-error'));
    });

    $("#home-tab").on("click", function () {
        clearInputFields('CreateForm', errorElements);
        $("#AMUserType").val('CP-User');
        $("#searchElement").removeClass("d-none");
    });

    $("#approverSearchInput").on("keyup input", function (e) {
        let value = $(this).val();
        const $addserApproval = $("#addserApproval");

        if (!value) {
            $(".AddedList").hide()
            approvarlists(newApproverData);
            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }
        if (e.type === "keyup") {
            searchedApproverData();
        }
    });

    $("#searchApprovar").on("click", function () {
        searchedApproverData();
    });
    addCPUser();
})

$(document).on("click", ".ADAddUser", function () {
    const $addserApproval = $("#addserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.data("name");
    const userRole = $this.data("role");
    const index = $this.data("index");
    let addedData = {
        id: userId,
        loginName: userName,
        roleName: userRole
    };

    if (!addedApproverData.some(item => item.id === addedData.id)) {
        addedApproverData.push(addedData);
    }
    $(".addedUsersList").remove();
    addNewApprover($addserApproval, userId, userName, userRole, index);
});

$(document).on("click", ".removeUser", function () {
    const $addserApproval = $("#addserApproval");
    const $addUserApproval = $("#addUserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.closest('div').find('.fw-semibold').text();
    const userRole = $this.closest('div').find('.badge').text();
    const index = $(`#user-${userId}`).data("index");
    addedApproverData = addedApproverData.filter(user => user.id !== userId);
    $(".usersList").remove();
    removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index);
});
