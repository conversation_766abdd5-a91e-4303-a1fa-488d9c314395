using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DynamicDashboardsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<DynamicDashboardListVm>>> GetDynamicDashboards()
    {
        Logger.LogDebug("Get All DynamicDashboards");

        return Ok(await Mediator.Send(new GetDynamicDashboardListQuery()));
    }

    [HttpGet("{id}", Name = "GetDynamicDashboard")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<DynamicDashboardDetailVm>> GetDynamicDashboardById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboard Id");

        Logger.LogDebug($"Get DynamicDashboard Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDynamicDashboardDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<DynamicDashboardListVm>>> GetPaginatedDynamicDashboards([FromQuery] GetDynamicDashboardPaginatedListQuery query)
 {
     if (query == null)
         throw new ArgumentNullException(nameof(query));

     Logger.LogDebug("Get Searching Details in DynamicDashboard Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [AllowAnonymous]
   // [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateDynamicDashboardResponse>> CreateDynamicDashboard([FromBody] CreateDynamicDashboardCommand createDynamicDashboardCommand)
    {
        if (createDynamicDashboardCommand == null)
            throw new ArgumentNullException(nameof(createDynamicDashboardCommand));

        Logger.LogDebug($"Create DynamicDashboard '{createDynamicDashboardCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDynamicDashboard), await Mediator.Send(createDynamicDashboardCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateDynamicDashboardResponse>> UpdateDynamicDashboard([FromBody] UpdateDynamicDashboardCommand updateDynamicDashboardCommand)
    {
        if (updateDynamicDashboardCommand == null)
            throw new ArgumentNullException(nameof(updateDynamicDashboardCommand));

        Logger.LogDebug($"Update DynamicDashboard '{updateDynamicDashboardCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDynamicDashboardCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteDynamicDashboardResponse>> DeleteDynamicDashboard(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboard Id");

        Logger.LogDebug($"Delete DynamicDashboard Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDynamicDashboardCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDynamicDashboardNameExist(string dynamicDashboardName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(dynamicDashboardName, "DynamicDashboard Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicDashboard Name '{dynamicDashboardName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDynamicDashboardNameUniqueQuery { Name = dynamicDashboardName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


