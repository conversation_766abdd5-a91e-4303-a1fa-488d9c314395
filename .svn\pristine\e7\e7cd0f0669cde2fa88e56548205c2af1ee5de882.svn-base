let disableParents = ['infraDiv', 'rpoDateDiv', "airgapid", 'jobDiv', 'operationalServiceDiv', 'operationalFunctionDiv', 'startDiv', 'endDiv', 'snapDiv','infralogdiv']
let isEdit = false
let createReportObj = {
    userProperties: [],
    reportProperties: []
}
const optionsMap = {
    daily: [
        { value: 'current date', text: 'Current Date' },
        { value: 'previous date', text: 'Previous Date' }
    ],
    weekly: [
        { value: 'current week', text: 'Current Week' },
        { value: 'past week', text: 'Past Week' }
    ],
    monthly: [
        { value: 'current month', text: 'Current Month' },
        { value: 'past month', text: 'Past Month' }
    ]
};

////----pagination--->
$(function () {

    let createPermission = $("#reportCreate").data("create-permission")?.toLowerCase();
    let deletePermission = $("#reportDelete").data("delete-permission")?.toLowerCase();

    if (createPermission == 'false') {
        $("#scheduleButton").removeClass('#scheduleButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false').removeAttr('id');
    }

    btnCrudEnable('confirmDeleteButton');
    ReportScheduleService();

    let selectedValues = [];
    let dataTable = $('#reportSchedule').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Report/ReportScheduler/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;
                    if (json?.data && json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }

                    return json?.data?.data;
                },
                "error": function (xhr, status, error) {
                    if (error.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },                   
                },
                {
                    "data": "reportName", "name": "Report Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "reportType", "name": "Report Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "userProperties", "name": "Receiver List", "autoWidth": true,
                    "render": function (data, type, row) {

                        let dataVal = data ? JSON.parse(data) : [];
                        let userNameList = dataVal?.map(user => user?.userName).join(', ') || 'NA';

                        if (type === 'display') {
                            return '<span title="' + (userNameList || 'NA') + '">' + (userNameList || 'NA') + '</span>';
                        }

                        return userNameList;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Schedule Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        let getScheduleTime = convertCronToOriginalValue(data, '', row)

                        if (type === 'display') {
                            return '<span title="' + (getScheduleTime || 'NA') + '">' + (getScheduleTime || 'NA') + '</span>';
                        }
                        return getScheduleTime;
                    }
                },

                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                       <span role="button" title="Executed Details" id="report_detail" data-bs-toggle="modal" data-report-id="${row?.id}" data-bs-target="#ReportScedulerModal">
                                                <i class="cp-time"></i>
                                            </span>
                                            <span role="button" title="Edit" class="edit-button" data-report='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="delete-button" data-Schedule-id="${row?.id}" data-Schedule-name="${row?.reportName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>     
                                </div>`;

                        }

                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                               <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit" class="edit-button" data-report='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="icon-disabled">
                                            <i class="cp-Delete"></i>
                                                </span>     
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit" class="edit-button" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="delete-button" data-Schedule-id="${row?.id}" data-Schedule-name="${row?.reportName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>     
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>     
                                </div>`;
                        }
                    }
                }
            ],

            "columnDefs": [
                {
                    "targets": [1, 3, 4],
                    "className": "truncate"
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this?.api();
                let startIndex = api?.context[0]?._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {

                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {

        if (e?.key === '=' || e?.key === 'Enter') {
            e?.preventDefault();
            return false;
        }

        const inputValue = $('#search-inp').val();
        const JobNameCheckbox = $("#Namecheckbox");
        const TemplatenameCheckbox = $("#Typecheckbox");
     
        if (JobNameCheckbox.is(':checked')) {
            selectedValues.push(JobNameCheckbox.val() + inputValue);
        }

        if (TemplatenameCheckbox.is(':checked')) {
            selectedValues.push(TemplatenameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {

            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
            if ((inputValue == "" || inputValue == undefined) && json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No Data Found');
            }
        })

    }, 500));

    $('#search-inp').attr('autocomplete', 'off');

    // Save Form    

    $('#btnSave').on('click', commonDebounce(async () => {
        let isScheduler = CronValidation();
        const reportName = $("#reportName").val();
        const id = createReportObj?.id ?? ''
        const getNameValidation = await ReportNameValidate(reportName, id, 'reportNameError');

        if (formValidation() && isScheduler && getNameValidation) {
            btnCrudDiasable('btnSave');

            let { cronExpression } = GetCronExpression();
            createReportObj['scheduleTime'] = cronExpression

            for (let index = createReportObj.reportProperties.length - 1; index >= 0; index--) {
                const key = createReportObj?.reportProperties[index];
                Object.keys(key).forEach((keyData) => {
                    let element = document.querySelector(`[name="${keyData}"]`);

                    if (element && element?.parentElement?.parentElement?.classList?.contains('d-none')) {
                        element.value = '';
                        createReportObj?.reportProperties?.splice(index, 1);
                    }
                });
            }

            if (typeof createReportObj?.reportProperties !== 'string') {
                createReportObj.reportProperties = JSON.stringify(createReportObj?.reportProperties)
            }

            if (typeof createReportObj?.userProperties !== 'string') {
                createReportObj.userProperties = JSON.stringify(createReportObj?.userProperties)
            }

            if (!isEdit) createReportObj['companyId'] = $('#CompanyId').val()
            createReportObj['fromDate'] = $('#clndrStart').val();
            createReportObj['toDate'] = $('#clndrEnd').val();
            createReportObj.type = $('#nav-tab .nav-link.active')?.attr('name')
            createReportObj.__RequestVerificationToken = gettoken()

            $.ajax({
                type: "POST",
                url: '/Report/ReportScheduler/SaveOrUpdate',
                data: createReportObj,
                dataType: "json",
                traditional: true,
                success: function (result) {

                    if (result?.success) {

                        btnCrudEnable('btnSave');
                        $('#CreateModal').modal('hide')
                        notificationAlert("success", result?.data?.message)
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000)

                    } else {
                        btnCrudEnable('btnSave');
                        errorNotification(result)
                    }
                },

            })

        }
    }, 800))

    //// report Name Validation
    $('#reportName').on('input', commonDebounce(async function () {
        const value = $("#reportName").val();
        const id = createReportObj?.id || ''

        let sanitizedValue = value?.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);

        await ReportNameValidate(value, id, 'reportNameError');
    }, 500))

})

$('#confirmDeleteButton').on('click', function () {
    btnCrudDiasable('confirmDeleteButton');
})

const ReportScheduleService = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + 'ITAutomation/WorkflowExecution/CheckWindowsService',
        data: { type: 'monitor', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                if (result && result?.success) {
                    let html = ReportScheduleStatusMessage(result)
                    notificationAlert("success", html, 'execution')
                } else {
                    notificationAlert("warning", response?.message);
                }

            } else {
                errorNotification(result)
            }
        }
    })
}

const ReportScheduleStatusMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}

const getUserList = async () => {
    $('#users_list').empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + 'Report/ReportScheduler/GetUsers',
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#users_list').append('<option value="">Select Users List</option>');

                    result?.data?.forEach((item) => {
                        let userInfo = item?.userInfo
                        $('#users_list').append('<option userName="' + item?.loginName + '" value="' + userInfo?.userId + '"  data-email="' + userInfo?.email + '"> ' + item?.loginName + ' (' + userInfo?.userName + ')' + '</option > ')
                    })
                }

                if (isEdit) {
                    let userList = []
                    Array.isArray(createReportObj?.userProperties) && createReportObj?.userProperties?.length && createReportObj?.userProperties.forEach((user) => {
                        if (user?.type == 'user') userList.push(user?.userId)
                    })
                    updateUserSelection(userList, '#user', '#users_list');
                }

            } else {
                errorNotification(result)
            }
        }
    })
}

const getUserGroupList = async () => {
    $('#user_groupList').empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + 'Report/ReportScheduler/GetUserGroup',
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#user_groupList').append('<option value="">Select User Group List</option>');

                    result?.data?.forEach((item) => {
                        $('#user_groupList').append('<option userName="' + item?.groupName + '" value="' + item?.id + '"> ' + item?.groupName + '</option>')
                    })
                }

                if (isEdit) {
                    let userGroup = []

                    Array.isArray(createReportObj?.userProperties) && createReportObj?.userProperties.length && createReportObj?.userProperties.forEach((user) => {
                        if (user?.type == 'userGroup') userGroup.push(user?.userId)
                    })
                    updateUserSelection(userGroup, '#user_group', '#user_groupList');
                }

            } else {
                errorNotification(result)
            }
        }
    })

}

const getInfraList = async (id = '') => {
    $('#infraList').empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + (id ? 'Report/ReportScheduler/GetInfraBusinessFunction' : 'Report/ReportScheduler/GetMonitorTypeByInfraObject'),
        data: { id: id },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#infraList').append('<option value="">Select InfraObject Name</option>');

                    result?.data?.forEach((item) => {
                        $('#infraList').append('<option value="' + (item.id ? item.id : item?.infraObjectId) + '" data-type="' + item?.monitorType + '">' + (item.name ? item.name : item?.infraObjectName) + '</option>');

                    })
                }

                if (isEdit) {
                    let infraObj = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties?.find(s => s.hasOwnProperty('infraObjectId'))
                    if (infraObj) $('#infraList').val(infraObj?.infraObjectId)
                }

            } else {
                errorNotification(result)
            }
        }
    })

}

const getJobList = async (id) => {

    if (id) {
        $('#job').empty();

        await $.ajax({
            type: "GET",
            url: RootUrl + 'Report/ReportScheduler/GetInfraJob',
            data: { id: id },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                        $('#job').append('<option value="">Select Job Name</option>');

                        result?.data?.forEach((item) => {
                            $('#job').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                        })
                    }

                    if (isEdit) {
                        let jobObj = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties?.find(s => s.hasOwnProperty('jobId'))

                        if (jobObj) $('#job').val(jobObj?.jobId)
                    }

                } else {
                    errorNotification(result)
                }
            }
        })
    }

}

const getOperationalFunction = async (id) => {
    $('#operationFunction').empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + 'Report/ReportScheduler/GetBusinessFunction',
        data: { id: id },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                    $('#operationFunction').append('<option value="">Select Operational Function</option>');

                    result?.data?.forEach((item) => {
                        $('#operationFunction').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                }

                if (isEdit) {
                    let operationServiceObj = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties?.find(s => s.hasOwnProperty('operationFunctionId'))

                    if (operationServiceObj) $('#operationFunction').val(operationServiceObj?.operationFunctionId).trigger('change')
                }
            } else {
                errorNotification(result)
            }
        }
    })

}
function getAirgap() {

    $("#airgaplist").val('');

    $.ajax({
        url: "/Report/PreBuildReport/GetAirgaplists",
        type: 'GET',
        success: function (response) {

            if (response?.success) {

                if (response?.data && Array.isArray(response?.data) && response?.data?.length) {
                    response?.data?.forEach((item) => {
                        $('#airgaplist').append('<option value="' + item?.airGapId + '">' + item?.airGapName + '</option>')
                    })
                }

                if (isEdit) {
                    let airGap = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties?.find(s => s?.hasOwnProperty('airgapids'))
                    if (airGap) $('#airgaplist').val(airGap?.airgapids).trigger('change')
                }
            } else {
                errorNotification(response)
            }
        },
        error: function (error) {
            /*console.error(error);*/
        }
    })
}

$('#infralogStart').on('change', function () {
    
    const startDate = $(this).val();
    const today = new Date()?.toISOString()?.split('T')[0];
    const endDate = (startDate === today) ? startDate : '';

    $('#infralogEnd').val(endDate).attr('min', startDate).trigger('change');

    if (!endDate) actionInfoValidation(endDate, 'infralogEndError', 'Select end date');

    actionInfoValidation(startDate, 'infralogStartError', 'Select start date');    
});

$('#infralogEnd').on('change', function () {
    
    const startDate = $("#infralogStart").val();
    const endDate = $("#infralogEnd").val();

    actionInfoValidation(endDate, 'infralogEndError', 'Select end date'); 

    if (!startDate) actionInfoValidation(startDate, 'infralogStartError', 'Select start date');

});

const handleOnChange = (e, type = '', keyName = '') => {
    let getId = e?.target?.id;

    if (e?.target?.name === 'reportType') {
        const getType = e?.target?.selectedOptions[0]?.getAttribute('typeId');
        const disableDivAttr = e?.target?.selectedOptions[0]?.getAttribute("disableDiv");

        const disableDivArray = disableDivAttr && JSON.parse(disableDivAttr);
        if (getType == 'snap') { $("#snapList").val("").trigger('change'); $('#snapListError').text('').removeClass('field-validation-error'); }
        if (getType == 'rpo' || getType == 'datasync') {
            getInfraList();
        }
        if (getType == 'infralog') {
            const currentDate = new Date();
            const formattedDate = currentDate?.toISOString()?.split('T')[0];
            $('#infralogStart, #infralogEnd').val('');
            $('#infralogStart').attr('max', formattedDate);
            $('#infralogEnd').attr('max', formattedDate);
            clearerror("infralogStartError");
            clearerror("infralogEndError");
        }
        if (getType == 'airgap') {

            $('#airgaplist, #clndrStart, #clndrEnd').val();
            $('#airgaplist option').remove();
            const currentDate = new Date();
            const formattedDate = currentDate.toISOString().split('T')[0];
            $('#clndrStart').attr('max', formattedDate);
            $('#clndrEnd').attr('max', formattedDate);
            clearerror("airgapidError");
            clearerror("errorStart");
            clearerror("errorEnd");
            getAirgap();
        }

        disableFunction(disableDivArray)

    }else if (e?.target?.name === 'dateFormat') {

        let id = e?.target?.value;
        const dateSelect = document?.getElementById('date');
        $("#date").val("");
        while (dateSelect?.options?.length > 1) {
            dateSelect.remove(1);
        }

        const options = optionsMap[id] || [];
        options.forEach(option => {
            const opt = document?.createElement('option');
            opt.value = option?.value;
            opt.textContent = option?.text;
            dateSelect.appendChild(opt);
        });
    }
    else if (e?.target?.name === 'infraObjectId') {

        let id = e?.target?.value
        if (createReportObj?.reportType?.toLowerCase()?.trim() === 'datasyncmonitoringreport' && id) {
            getJobList(id);
            $('#jobDiv').removeClass('d-none')
        }

    }
    else if (e?.target?.name === 'operationServiceId') {

        let id = e?.target?.value
        if (createReportObj?.reportType?.toLowerCase()?.replace(/\s+/g, '').trim() === 'resiliencyreadinessexecutionlogreport' && id) {
            //getOperationalFunction(id);
            $('#operationalFunctionDiv').addClass('d-none')
        }
    }
    else if (e?.target?.name === 'operationFunctionId') {

        let id = e?.target?.value;
        if (createReportObj?.reportType?.toLowerCase()?.replace(/\s+/g, '').trim() === 'resiliencyreadinessexecutionlogreport' && id) {
           // getInfraList(id);
            $('#infraDiv').addClass('d-none')
        }

    }
    else if (e?.target?.type == 'checkbox') {
        const divId = (e?.target?.value === 'user') ? '#userDiv' : '#userGroupDiv';
        $(divId).toggleClass('d-none', !e?.target?.checked);

        if (e?.target?.value === 'user_group') {
            $('#user_groupListError').text('').removeClass('field-validation-error');
            $('#userCategoryDivError').text('').removeClass('field-validation-error');
        }
        if (e?.target?.value === 'user') {
            $('#users_listError').text('').removeClass('field-validation-error');
            $('#userCategoryDivError').text('').removeClass('field-validation-error');
        }

        const typeToRemove = (e?.target?.value === 'user' && !e?.target?.checked) ? 'user' :
            (e?.target?.value === 'user_group' && !e?.target?.checked) ? 'userGroup' :
                null;

        if (typeToRemove !== null) {
            createReportObj.userProperties = Array.isArray(createReportObj?.userProperties) && createReportObj?.userProperties.length ? createReportObj?.userProperties.filter(item => item.type !== typeToRemove) : [];
        }

    }
  
    // save data to create obj
    if (type == 'report_properties' && e.target.value) {

        const filteredData = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties.length ? createReportObj?.reportProperties.filter(item => !item.hasOwnProperty(e.target.name)) : [];
        let getMonitor = (createReportObj?.reportType?.toLowerCase()?.replace(/\s+/g, '').trim() === 'rposlareport' && e.target.name === 'infraObjectId') && e.target?.selectedOptions[0].getAttribute('data-type')

        let obj = {}
        if (e.target?.selectedOptions?.length && keyName) {
            obj = {
                [e.target.name]: e?.target?.value,
                [keyName]: e?.target?.selectedOptions[0]?.text
            };

            if (createReportObj?.reportType?.toLowerCase()?.replace(/\s+/g, '').trim() === 'rposlareport' && e.target.name === 'infraObjectId') obj.monitorType = getMonitor

        } else {
            obj = {
                [e.target.name]: e?.target?.value,
            };
        }

        filteredData.push(obj);
        createReportObj.reportProperties = filteredData;

    } else if (type == 'user_properties') {

        const selectedOptions = Array.from(e?.target?.selectedOptions);

        if (e.target.name == 'user') {
            const filteredData = Array.isArray(createReportObj?.userProperties) && createReportObj?.userProperties.length ? createReportObj?.userProperties.filter(item => item.type !== 'user') : [];

            Array.isArray(selectedOptions) && selectedOptions?.length && selectedOptions.map(option => {
                if (option?.value) {
                    const getEmail = option?.getAttribute('data-email')

                    const obj = {
                        type: 'user',
                        userName: option?.getAttribute('username'),
                        userId: option?.value
                    };
                    if (getEmail) obj.email = getEmail
                    filteredData.push(obj);
                }
            });
            createReportObj.userProperties = filteredData;

            if (Array.isArray(createReportObj?.userProperties) &&
                !createReportObj?.userProperties.some(item => item?.type === 'user') &&
                $('#user').prop('checked') && !$('#users_list')?.val()?.length) {
                actionInfoValidation('', 'users_listError', 'Select user list');
            } else {
                const value = (type === 'user_properties') ? e?.target?.selectedOptions : e?.target?.value;
                actionInfoValidation(value, `${getId}Error`, '');
            }
        } else if (e.target.name == 'userGroup') {

            const filteredData = Array.isArray(createReportObj?.userProperties) && createReportObj?.userProperties?.length ? createReportObj?.userProperties.filter(item => item?.type !== 'userGroup') : [];

            Array.isArray(selectedOptions) && selectedOptions?.length && selectedOptions.map(option => {
                if (option?.value) {
                    const getEmail = option?.getAttribute('data-email')

                    const obj = {
                        type: 'userGroup',
                        userName: option?.getAttribute('username'),
                        userId: option?.value
                    };
                    if (getEmail) obj.email = getEmail
                    filteredData.push(obj);
                }
            });

            createReportObj.userProperties = filteredData;
            if (Array.isArray(createReportObj?.userProperties) &&
                !createReportObj?.userProperties.some(item => item?.type === 'userGroup') &&
                $('#user_group').prop('checked') && !$('#user_groupList')?.val()?.length) {
                actionInfoValidation('', 'user_groupListError', 'Select user group');
            } else {
                actionInfoValidation(type === 'user_properties' ? e.target?.selectedOptions : e?.target?.value, `${getId}Error`, '');
            }
        }
    } else {
        if (e?.target?.type != 'checkbox' && e?.target?.value) createReportObj[e.target.name] = e?.target?.value
    }

    if (e?.target?.name != 'user' && e?.target?.name != 'userGroup') {
        actionInfoValidation(type == 'user_properties' ? e?.target?.selectedOptions : e?.target?.value, `${getId}Error`, '')
    }
}

const disableFunction = (disableDivArray) => {

    if (disableDivArray?.length) {
        disableParents.forEach((disable) => {
            disableDivArray.includes(disable) ? $('#' + disable).removeClass('d-none') : $('#' + disable).addClass('d-none')
        })
    } else {
        disableParents.forEach((disable) => $('#' + disable).addClass('d-none'))
    }

    $('#operationService, #date_format, #date').val('').trigger('change');
    $('#operationServiceError, #date_formatError, #dateError').text('').removeClass('field-validation-error')
}

// Create Modal Open
$('#scheduleButton').on('click', () => {
    isEdit = false;
    $('#btnSave').text('Save');
    clearReportFields();
    $('#CreateModal').modal('show');
})

// Report Schedule Execution Details
$('#reportSchedule').on('click', '#report_detail', async function () {
   $('#reportScheduleDetails').empty();
   let reportId = $(this).data("reportId");
    let html = '';

    if (reportId) {
        await $.ajax({
            type: "GET",
            url: RootUrl + 'Report/ReportScheduler/GetReportScheduleExecutionById',
            data: { reportId: reportId },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {

                    if (result?.data && result?.data?.length) {
                        $('#reportScheduleNodata').hide();
                        result?.data?.forEach((item, index) => {
                            html += `<tr>                   
                       <td title="${index + 1}">${index + 1}</td>
                       <td class="truncate" title="${item?.reportName || 'NA'}">${item?.reportName || 'NA'}</td>
                       <td class="truncate" title="${item?.reportType || 'NA'}">${item?.reportType || 'NA'}</td>
                       <td class="truncate"><span title="${item?.users}">${item?.users}</span></td>
                       <td class="truncate" title="${item?.scheduleDateTime || 'NA'}">${item?.scheduleDateTime || 'NA'}</td>
                    </tr >`;
                        });
                    } else {
                        $('#reportScheduleNodata').show();
                    }

                    if ($.fn.DataTable.isDataTable('#reportScheduleTable')) {
                        let table = $('#reportScheduleTable').DataTable();
                        table.clear();
                        table.rows.add($(html));
                        table.draw();
                        table.page.len(10).draw();
                    } else {
                        $('#reportScheduleDetails').append(html);
                        let dataTable = $('#reportScheduleTable').DataTable({
                            paging: true,
                            searching: false,
                            ordering: true,
                            pageLength: 10,
                            lengthMenu: [5, 10],
                            lengthChange: true,
                            info: true,
                            language: {
                                paginate: {
                                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                                }
                            },
                            dom: 't<"d-flex justify-content-between align-items-center"<"left-section"l><"center-section"i><"right-section"p>>',
                            initComplete: function () {

                                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                                $('.paginate_button.page-item.next').attr('title', 'Next');
                            }
                        });
                        dataTable.on('draw.dt', function () {
                            $('.paginate_button.page-item.previous').attr('title', 'Previous');
                            $('.paginate_button.page-item.next').attr('title', 'Next');
                        });
                    }
                } else {
                    $('#reportScheduleNodata').show();
                    errorNotification(result);
                }
            },
        });
    }

});

// Edit Report
$('#reportSchedule').on('click', '.edit-button', function () {
    let reportObj = $(this).data("report");
    $('#btnSave').text('Update');
    isEdit = true
    populateReport(reportObj)

});

// Delete Report List
$('#reportSchedule').on('click', '.delete-button', function () {
    let scheduleId = $(this)?.data("scheduleId");
    let scheduleName = $(this)?.data("scheduleName")

    $('#textDeleteId').val(scheduleId);
    $('#deleteData').text(scheduleName);
});

const populateReport = (reportObj) => {

    createReportObj = {...reportObj,
        reportProperties: JSON.parse(reportObj?.reportProperties || '{}'),
        userProperties: JSON.parse(reportObj?.userProperties || '{}')
    };

    getUserList();
    getUserGroupList();

    let getReportData = generateReportType(reportObj?.reportType)
    let date = "";

    $('#reportName').val(reportObj?.reportName)
    $('#reportType').val(getReportData ? getReportData : reportObj?.reportType).trigger('change')
    
    Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties.forEach((report) => {

        if (report?.dateFormat !== undefined) {
            $('#date_format').val(report?.dateFormat);
            optionsMap[report?.dateFormat]?.forEach(option => {
                $('#date').append(new Option(option?.text, option?.value));
            });
        }

        if (report?.date) date = report?.date;  
        if (report?.operationServiceId) $('#operationService').val(report?.operationServiceId).trigger('change');
        if (report?.infralogStart) $('#infralogStart').val(report?.infralogStart)
        if (report?.infralogEnd) $('#infralogEnd').val(report?.infralogEnd);       
    })

    if (date) $('#date').val(date)
    
    if (isEdit) {
        let cyberSnap = Array.isArray(createReportObj?.reportProperties) && createReportObj?.reportProperties?.length && createReportObj?.reportProperties?.find(s => s?.hasOwnProperty('snapId'))
        if (cyberSnap) $('#snapList').val(cyberSnap?.snapId).trigger('change');
    }

    convertCronToOriginalValue(reportObj?.scheduleTime, isEdit, reportObj)
    clearErrorElements();
    $('#btnSave').prop('disabled', false)
    $('#CreateModal').modal('show')
}

function updateUserSelection(users, checkboxId, dropdownId) {
    const divId = (dropdownId === '#users_list') ? '#userDiv' : '#userGroupDiv';

    if (users.length) {
        $(checkboxId).prop('checked', true);
        $(divId).toggleClass('d-none', false);
        $(dropdownId).val(users);
    } else {
        $(checkboxId).prop('checked', false);
        $(divId).toggleClass('d-none', true);
    }
}

const generateReportType = (data) => {
    let type = '';

    switch (data) {
        case "rpo_sla_report":
            type = "RPO SLA Report";
            break;
        case "datalag_status_report":
            type = "Datalag Status Report";
            break;
        case "infra_object":
            type = "InfraObject Summary";
            break;
        case "dr_readiness":
            type = "Resiliency Readiness Execution Log Report";
            break;
        case "dr_ready":
            type = "Resilience Readiness Report";
            break;
        case "AirGap Report":
            type = "AirGap Report";
            break;
        default:
            type = "";
            break;
    }

    return type;
}

const clearReportFields = () => {
    disableParents.forEach(disable => $('#' + disable).addClass('d-none'))
    getUserList();
    getUserGroupList();
    $('#user_group, #user').prop('checked', false)
    $('#userGroupDiv, #userDiv').addClass('d-none')
    $('.form-clear').each(function () {
        $(this)?.val('');
    });

    createReportObj = {
        userProperties: [],
        reportProperties: []
    }
  
    $('#everyHours, #ddlHours,#hourly, #txtHours,#txtMinutes,#lblMonth, #monthlyHours').val('');    
    $("input[name='Monthyday']").prop("checked", false).prop("disabled", true);
    $("#defaultCheck-everyday, #defaultCheck-MON-FRI").prop("checked", false)
    $('input[name=weekDays], input[name=Monthyday]').prop("checked", false)
    $('#nav-Hourly-tab').addClass('active')
    $('#nav-Hourly').addClass('active show')
    $('#nav-Daily-tab, #nav-Daily, #nav-Monthly-tab, #nav-Monthly, #nav-Weekly-tab, #nav-Weekly').removeClass('active')
    clearErrorElements();
    $('#btnSave').prop('disabled', false)
}

const clearErrorElements = () => {
    let cronError = ['CronHourly','CronHourMin', 'Crondaysevery', 'CroneveryHour', 'CronDay', 'CronddlHour', 'CronMonth', 'CronMonthlyDay', 'CronMonthHrs']
    $('.form-clear').each(function () {
        let getId = $(this).attr('id')
        $(`#${getId}Error`).removeClass('field-validation-error').text('');

    });
    cronError.forEach((id) => {
        $(`#${id}Error`).removeClass('field-validation-error').text('');
    })
}

const formValidation = () => {
    let formIsValid = true
    const reportType = $('#reportType').val();

    if (reportType === "Infra Object Scheduler Log Report") {
        ['#infralogStart', '#infralogEnd'].forEach((field) => {
            if (!$(field).val()) {
                const errorId = field === '#infralogStart' ? 'infralogStartError' : 'infralogEndError';
                const message = field === '#infralogStart' ? 'Select start date' : 'Select end date';
                actionInfoValidation('', errorId, message);
                formIsValid = false;
            }
        });
    }

    const validationConditions = [
        { prop: '#reportName', errorId: 'reportNameError', message: 'Enter report name' },
        { prop: '#reportType', errorId: 'reportTypeError', message: 'Select report type' },
        { prop: '#user', errorId: 'users_listError', message: 'Select user list', altProp: '#users_list' },
        { prop: '#user_group', errorId: 'user_groupListError', message: 'Select user group', altProp: '#user_groupList' },
        { prop: '#userCategoryDiv', errorId: 'userCategoryDivError', message: 'Select user category' },
        { prop: '#operationService', errorId: 'operationServiceError', message: 'Select operational service', parent: true },
        { prop: '#operationFunction', errorId: 'operationFunctionError', message: 'Select operation function', parent: true },
        { prop: '#infraList', errorId: 'infraListError', message: 'Select infraobject name', parent: true },
        { prop: '#snapList', errorId: 'snapListError', message: 'Select snapTag', parent: true },
        { prop: '#job', errorId: 'jobError', message: 'Select job', parent: true },      
        { prop: '#airgaplist', errorId: 'airgapidError', message: 'Select airgap name', parent: true },
        { prop: '#date_format', errorId: 'date_formatError', message: 'Select report cycle' },
        { prop: '#date', errorId: 'dateError', message: 'Select report duration' }
    ];   

    for (const condition of validationConditions) {   
        
        const propElement = $(condition?.prop);
        const altPropElement = condition?.altProp ? $(condition?.altProp) : null;       

        if (condition?.prop == '#date_format' || condition?.prop == '#date') {
            if (!$('#rpoDateDiv').hasClass('d-none') && !propElement.val()) {
                actionInfoValidation('', condition?.errorId, condition?.message);
                formIsValid = false;
            }
        } else if (condition?.prop === '#userCategoryDiv') {
            const anyCheckboxChecked = $('#userCategoryDiv input[type="checkbox"]').is(':checked');
            if (!anyCheckboxChecked) {
                actionInfoValidation('', condition?.errorId, condition?.message);
                formIsValid = false;
            }
        } else if (!propElement?.val() && !altPropElement && !condition?.parent) {
            actionInfoValidation('', condition?.errorId, condition?.message);
            formIsValid = false;
        } else if (!propElement?.val() && (condition?.parent && !propElement?.parent()?.parent()?.hasClass('d-none'))) {
            actionInfoValidation('', condition?.errorId, condition?.message);
            formIsValid = false;
        } else if (altPropElement && condition?.prop === '#user' && propElement?.prop('checked') && !altPropElement?.val()?.length) {
            actionInfoValidation('', condition?.errorId, condition?.message);
            formIsValid = false;
        } else if (altPropElement && condition?.prop === '#user_group' && propElement.prop('checked') && !altPropElement?.val()?.length) {
            actionInfoValidation('', condition?.errorId, condition?.message);
            formIsValid = false;
        }

    }

    return formIsValid;
}

function actionInfoValidation(value, errorId, text = '') {
    const errorElement = $('#' + errorId);
    if (!value || !value?.length) errorElement.text(text).addClass('field-validation-error');
    else errorElement.removeClass('field-validation-error').text('');  
}
function clearerror(errorid) {
    let errorId = $('#' + errorid);
    errorId.removeClass('field-validation-error').text('');
};

$('.validateInput').on('input', function (e) {
    let value = e?.target?.value
    let errorId = e?.target?.getAttribute('errorid')
    if (value) $(`#${errorId}Error`).text('').removeClass('field-validation-error')
})

$('#txtMinutes').on('input', function () {
    let value = $(this).val();
    let numericValue = value?.replace(/[^0-9]/g, '');
    let errorElement = $('#CronHourMinError');

    if (numericValue?.length > 2) numericValue = numericValue?.substring(0, 2);
    
    if (numericValue === '' || parseInt(numericValue, 10) < 1 || parseInt(numericValue, 10) > 59) $(this).val('');
    else $(this).val(numericValue);
    
    validateMinNumber(numericValue, "Enter minutes", errorElement);
});

// Hourly Button
$('#txtHours').on('input', function () {
    const value = $(this).val();
    let numericValue = value?.replace(/[^0-9]/g, '');
    let errorElement = $('#CronHourlyError');

    if (numericValue?.length > 2) numericValue = numericValue?.substring(0, 2);
    
    if (numericValue === '' || parseInt(numericValue, 10) < 0 || parseInt(numericValue, 10) > 23) $(this).val('');
    else $(this).val(numericValue);
    
    validateHourNumber(numericValue, "Enter hours", errorElement);
});

$('input[name=weekDays]').on('click', function (e) {
    if (e.target.checked) $(`#CronDayError`).text('').removeClass('field-validation-error')
});

$('input[name=Monthyday]').on('click', function (e) {
    if (e.target.checked) $(`#CronMonthlyDayError`).text('').removeClass('field-validation-error')
});

async function ReportNameValidate(value, id = null, errorId) {

    const errorElement = $('#' + errorId);
    if (!value) {
        errorElement.text('Enter report name').addClass('field-validation-error')
        return false;
    } else {
        let url = '/Report/ReportScheduler/IsReportNameExist';
        let data = {};
        data.reportName = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await secondChar(value),
        ];
        const allValid = validationResults.every(result => result === true);

        if (allValid) {
            validationResults.push(await IsNameExist(url, data.reportName, data, OnError));
        } 

        return await CommonValidation(errorElement, validationResults);
    }
}

async function IsNameExist(url, name, data, errorFunc) {
    return !name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}