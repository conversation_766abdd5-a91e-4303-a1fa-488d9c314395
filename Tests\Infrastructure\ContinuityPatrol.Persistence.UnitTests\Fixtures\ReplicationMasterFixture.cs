using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReplicationMasterFixture : IDisposable
{
    public List<ReplicationMaster> ReplicationMasterPaginationList { get; set; }
    public List<ReplicationMaster> ReplicationMasterList { get; set; }
    public ReplicationMaster ReplicationMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReplicationMasterFixture()
    {
        var fixture = new Fixture();

        ReplicationMasterList = fixture.Create<List<ReplicationMaster>>();

        ReplicationMasterPaginationList = fixture.CreateMany<ReplicationMaster>(20).ToList();

        ReplicationMasterDto = fixture.Create<ReplicationMaster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
