﻿using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoUserDataAttribute : AutoDataAttribute
{
    public AutoUserDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateUserCommand>(p => p.CompanyName, 10));
            fixture.Customize<CreateUserCommand>(c => c.With(b => b.CompanyId, Guid.NewGuid().ToString()));
  
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateUserCommand>(p => p.CompanyName, 10));
            fixture.Customize<UpdateUserCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            return fixture;
        })
    {

    }
}


public class InlineUserDataAttribute : InlineAutoDataAttribute
{
    public InlineUserDataAttribute(params object[] objects) : base(new AutoUserDataAttribute(), objects) { }
}