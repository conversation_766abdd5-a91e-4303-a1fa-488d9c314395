﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'RoboCopy';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { robocopymonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
setTimeout(() => { robocopyServer(infraObjectId) }, 250)

$('#mssqlserver').hide();
async function robocopyServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindRobocopyServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindRobocopyServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function robocopymonitorstatus(id, type) {
    
    $.ajax({
        url: "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType",
        method: 'GET',

        data: {
            monitorId: id,
            type: type
        },
        dataType: 'json',
        async: true,


        success: function (res) {
            
            const value = res?.data;
            
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }
            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                let data = JSON?.parse(value?.properties);
                let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : value?.prServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';
                let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : value?.drServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : '<i class="cp-pending text-warning"></i>';

                $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
                $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
               
                $("#PR_Server_IpAddress").text(checkAndReplace(data?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_IpAddress)).prepend(prStatus);
                
                $("#PR_Server_HostName").text(checkAndReplace(data?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_HostName)).prepend('<i class="cp-host-name text-primary"></i>')
                
              
                let customSite = data?.RoboCopyMonitoringModels?.length > 1;
                if (customSite) {
                    $("#Sitediv").show();
                } else {
                    $("#Sitediv").hide();
                }


                $(".siteContainer").empty();


                data?.RoboCopyMonitoringModels?.forEach((a, index) => {
                    let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
                    $(".siteContainer").append(selectTab);
                });


                if (data?.RoboCopyMonitoringModels?.length > 0) {
                    $("#siteName0 .nav-link").addClass("active");
                    displaySiteData(data?.RoboCopyMonitoringModels[0]);
                }


                $(document).on('click', '.siteListChange', function () {
                    $(".siteListChange .nav-link").removeClass("active");
                    $(this).find(".nav-link").addClass("active");
                    let siteId = $(this)[0]?.id
                    let getSiteName = $(`#${siteId} .siteName`).text()

                    let MonitoringModel = data?.RoboCopyMonitoringModels?.find(d => d?.Type === getSiteName);
                    if (MonitoringModel) {
                        displaySiteData(MonitoringModel);
                    }
                });

                function displaySiteData(siteData) {
                   
                    let obj = {};
                  
                    for (let key in siteData?.MonitoringModel) {
                        obj[`DR_` + key] = siteData?.MonitoringModel[key];
                    }
                    $("#DR_Server_IpAddress").text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                    $("#DR_Server_HostName").text(checkAndReplace(siteData?.MonitoringModel?.Server_HostName)).prepend('<i class="cp-host-name text-primary"></i>')
                    
                    const Joblength = siteData?.MonitoringModel?.RoboCopyMonitorModel?.length ?? 0
                    $("#PR_Server_Job").text(Joblength)
                    //$("#PR_Server_Job").text(checkAndReplace(Joblength))
                      $('#jobtable').empty();
                   siteData?.MonitoringModel?.RoboCopyMonitorModel && siteData?.MonitoringModel?.RoboCopyMonitorModel?.length && siteData?.MonitoringModel?.RoboCopyMonitorModel?.forEach((list, i) => {
                        
                        let jobData = ''
                       
                        let SelectedOptions = checkAndReplace(list?.SelectedOptions)
                        let Drive = checkAndReplace(list?.SourcePath)
                        let DirectoryCopy = checkAndReplace(list?.DestinationPath)
                        let StartTime = checkAndReplace(list?.RepStartTime)
                        let EndTime = checkAndReplace(list?.RepEndTime)
                        let TotalDirCount = checkAndReplace(list?.TotalDirCount)
                        let TotalDirCopiedCount = checkAndReplace(list?.TotalDirCopiedCount)
                        let TotalSkippedDirCount = checkAndReplace(list?.TotalSkippedDirCount)
                        let TotalMisMatchedDirCount = checkAndReplace(list?.TotalMisMatchedDirCount)
                        let TotalFailedDirCount = checkAndReplace(list?.TotalFailedDirCount)
                        let TotalExtrasDirCount = checkAndReplace(list?.TotalExtrasDirCount)
                        let TotalFilesCount = checkAndReplace(list?.TotalFilesCount)
                        let TotalFilesCopiedCount = checkAndReplace(list?.TotalFilesCopiedCount)
                        let TotalSkippedFilesCount = checkAndReplace(list?.TotalSkippedFilesCount)
                        let TotalMisMatchedFilesCount = checkAndReplace(list?.TotalMisMatchedFilesCount)
                        let TotalFailedFilesCount = checkAndReplace(list?.TotalFailedFilesCount)
                        let TotalExtrasFilesCount = checkAndReplace(list?.TotalExtrasFilesCount)
                        let TotalBytesCount = checkAndReplace(list?.TotalBytesCount)
                        let TotalBytesCopiedCount = checkAndReplace(list?.TotalBytesCopiedCount)
                        let TotalSkippedBytesCount = checkAndReplace(list?.TotalSkippedBytesCount)
                        let TotalMisMatchedBytesCount = checkAndReplace(list?.TotalMisMatchedBytesCount)
                        let TotalFailedBytesCount = checkAndReplace(list?.TotalFailedBytesCount)
                        let TotalExtrasBytesCount = checkAndReplace(list?.TotalExtrasBytesCount)
                        let TotalTimesCount = checkAndReplace(list?.TotalTimesCount)
                        let TotalTimesCopiedCount = checkAndReplace(list?.TotalTimesCopiedCount)
                        let TotalSkippedTimesCount = checkAndReplace(list?.TotalSkippedTimesCount)
                        let TotalMisMatchedTimesCount = checkAndReplace(list?.TotalMisMatchedTimesCount)
                        let TotalFailedTimesCount = checkAndReplace(list?.TotalFailedTimesCount)
                        let TotalExtrasTimesCount = checkAndReplace(list?.TotalExtrasTimesCount)
                        let SpeedBytesPerSeconds = checkAndReplace(list?.SpeedBytesPerSeconds)
                        let SpeedMBPerMinute = checkAndReplace(list?.SpeedMBPerMinute)

                        jobData += `  <table class="table mb-4" >
                            
                       <thead>
                                <tr>
                                    <th>Job : ${i + 1}</th>
                                    <th>Source Server</th>
                                    <th>Destination Server</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-folder-file me-1"></i>Drive / Directory for Copy</td>
                                    <td>${Drive}</td>
                                    <td>${DirectoryCopy}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-select me-1"></i>Selected Options</td>
                                    <td>${SelectedOptions}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-table-clock me-1"></i>Started Date, Time</td>
                                    <td>${StartTime}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-table-clock me-1"></i>End Date, Time</td>
                                    <td>${EndTime}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="p-0">
                                        <table class="table mb-4">
                                            <thead>
                                                <tr>
                                                    <td></td>
                                                    <th>Total</th>
                                                    <th>Copied</th>
                                                    <th>Skipped</th>
                                                    <th>Mismatch</th>
                                                    <th>Failed</th>
                                                    <th>Extras</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><i class="text-secondary cp-file-c me-1"></i>Dirs</td>
                                                    <td>${TotalDirCount}</td>
                                                    <td>${TotalDirCopiedCount}</td>
                                                    <td>${TotalSkippedDirCount}</td>
                                                    <td>${TotalMisMatchedDirCount}</td>
                                                    <td>${TotalFailedDirCount}</td>
                                                    <td>${TotalExtrasDirCount}</td>
                                                </tr>
                                                <tr>
                                                    <td><i class="text-secondary cp-control-file-type me-1"></i>Files</td>
                                                    <td>${TotalFilesCount}</td>
                                                    <td>${TotalFilesCopiedCount}</td>
                                                    <td>${TotalSkippedFilesCount}</td>
                                                    <td>${TotalMisMatchedFilesCount}</td>
                                                    <td>${TotalFailedFilesCount}</td>
                                                    <td>${TotalExtrasFilesCount}</td>
                                                </tr>
                                                <tr>
                                                    <td><i class="text-secondary cp-freeze-time me-1"></i>Bytes</td>
                                                    <td>${TotalBytesCount}</td>
                                                    <td>${TotalBytesCopiedCount}</td>
                                                    <td>${TotalSkippedBytesCount}</td>
                                                    <td>${TotalMisMatchedBytesCount}</td>
                                                    <td>${TotalFailedBytesCount}</td>
                                                    <td>${TotalExtrasBytesCount}</td>
                                                </tr>
                                                <tr>
                                                    <td class="border-bottom-0"><i class="text-secondary cp-apply-finish-time me-1"></i>Times</td>
                                                    <td class="border-bottom-0">${TotalTimesCount}</td>
                                                    <td class="border-bottom-0">${TotalTimesCopiedCount}</td>
                                                    <td class="border-bottom-0">${TotalSkippedTimesCount}</td>
                                                    <td class="border-bottom-0">${TotalMisMatchedTimesCount}</td>
                                                    <td class="border-bottom-0">${TotalFailedTimesCount}</td>
                                                    <td class="border-bottom-0">${TotalExtrasTimesCount}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-speed-meter me-1"></i>Speed(Bytes/Min)</td>
                                    <td>${SpeedBytesPerSeconds}</td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-secondary cp-speed-meter me-1"></i>Speed(MegaBytes/Min)</td>
                                    <td>${SpeedMBPerMinute}</td>
                                    <td></td>
                                </tr>
                            </tbody>
                             </table>`
                     
                        $('#jobtable').append(jobData);
                    });
                   
                }
               

            }
        }
    });
}