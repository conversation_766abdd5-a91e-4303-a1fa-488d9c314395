﻿using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using System.Text.Json;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Queries;

public class GetAvailableCountQueryHandlerTests : IClassFixture<LicenseInfoFixture>, IClassFixture<LicenseManagerFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    
    private readonly GetAvailableCountQueryHandler _handler;
    
    public GetAvailableCountQueryHandlerTests(LicenseInfoFixture licenseInfoFixture, LicenseManagerFixture licenseManagerFixture) 
    {
        _licenseInfoFixture = licenseInfoFixture;

         var licenseManagerFixture1 = licenseManagerFixture;

        _mockLicenseInfoRepository = new Mock<ILicenseInfoRepository>();

        _mockLicenseManagerRepository = new();

        //_mockLicenseInfoRepository = LicenseInfoRepositoryMocks.GetAvailableCountRepository(_licenseInfoFixture.LicenseInfos);

        //_mockLicenseManagerRepository = LicenseManagerRepositoryMocks.GetLicenseManagerRepository(licenseManagerFixture1.LicenseManagers);

        _handler = new GetAvailableCountQueryHandler(_mockLicenseInfoRepository.Object, _mockLicenseManagerRepository.Object);

        
        _licenseInfoFixture.LicenseInfos[0].LicenseId = "c8b99ecc-01b7-423c-bba1-f06602a4ba27";
        _licenseInfoFixture.LicenseInfos[1].LicenseId = "c8b99ecc-01b7-423c-bba1-f06602a4ba27";
        _licenseInfoFixture.LicenseInfos[2].LicenseId = "c8b99ecc-01b7-423c-bba1-f06602a4ba27";
        _licenseInfoFixture.LicenseInfos[0].Type = "Database";
        _licenseInfoFixture.LicenseInfos[1].Type = "Server";
        _licenseInfoFixture.LicenseInfos[2].Type = "Replication";
        licenseManagerFixture1.LicenseManagers[0].ReferenceId = "c8b99ecc-01b7-423c-bba1-f06602a4ba27";
    }
    
    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        //var licenseInfo=new List<Domain.Entities.LicenseInfo>();
        //var licenseManager=new Domain.Entities.LicenseManager { 
        //  AmcEndDate = JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  AmcStartDate = JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  AmcPlan =JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  IpAddress= JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  MacAddress= JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  CompanyId= JsonSerializer.Serialize(new { AmcPlan = "plan" }),
        //  CompanyName= JsonSerializer.Serialize(new { AmcPlan = "plan" }),
          
        //    //IsActive = JsonSerializer.Serialize(new { IsActive = true })
        //};
        //_mockLicenseInfoRepository.Setup(dp => dp.GetLicenseInfoDetailByLicenseId(_licenseInfoFixture.LicenseInfos[0].LicenseId)).ReturnsAsync(licenseInfo);
        //_mockLicenseManagerRepository.Setup(dp => dp.GetLicenseDetailByIdAsync(_licenseInfoFixture.LicenseInfos[0].LicenseId)).ReturnsAsync(licenseManager);
        //var result= await _handler.Handle(new GetAvailableCountQuery{LicenseId = _licenseInfoFixture.LicenseInfos[0].LicenseId}, CancellationToken.None);

        

        //_mockLicenseManagerRepository.Verify(x => x.GetLicenseDetailByIdAsync(It.IsAny<string>()), Times.Once);

    }
}