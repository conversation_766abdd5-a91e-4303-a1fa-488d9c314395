﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Queries;

public class GetSmtpConfigurationDetailQueryHandlerTests : IClassFixture<SmtpConfigurationFixture>
{
    private readonly SmtpConfigurationFixture _smtpConfigurationFixture;

    private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;

    private readonly GetSmtpConfigurationDetailQueryHandler _handler;

    public GetSmtpConfigurationDetailQueryHandlerTests(SmtpConfigurationFixture smtpConfigurationFixture)
    {
        _smtpConfigurationFixture = smtpConfigurationFixture;

        _mockSmtpConfigurationRepository = SmtpConfigurationRepositoryMocks.GetSmtpConfigurationRepository(_smtpConfigurationFixture.SmtpConfigurations);

        _handler = new GetSmtpConfigurationDetailQueryHandler(_smtpConfigurationFixture.Mapper, _mockSmtpConfigurationRepository.Object);

        _smtpConfigurationFixture.SmtpConfigurations[0].UserName = "rD7N1eVaZPFrr8tsoKJ8m7vKdUuASxHnnZ6ytacs+KU=$nbTPa0AMdFK/ccxYst2rkrlOt3xm3B9hFayBtPxE2zvaE5b5XnS+WbkA7jE=";
    }

    [Fact]
    public async Task Handle_Return_SmtpConfigurationDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetSmtpConfigurationDetailQuery { Id = _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<SmtpConfigurationDetailVm>();
        result.Id.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId);
        result.CompanyId.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].CompanyId);
        result.SmtpHost.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].SmtpHost);
        result.UserName.ShouldBe("<EMAIL>");
        result.Password.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].Password);
        result.Port.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].Port);
        result.EnableSSL.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].EnableSSL);
        result.IsBodyHTML.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[0].IsBodyHTML);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSmtpConfigurationId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetSmtpConfigurationDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetSmtpConfigurationDetailQuery { Id = _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId }, CancellationToken.None);

        _mockSmtpConfigurationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}