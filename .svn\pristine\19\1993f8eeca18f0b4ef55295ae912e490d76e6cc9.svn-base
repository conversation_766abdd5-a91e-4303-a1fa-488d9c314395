﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Update;
using ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.SmsConfigurationModel;

namespace ContinuityPatrol.Application.Mappings;

public class SmsConfigurationProfile : Profile
{
    public SmsConfigurationProfile()
    {
        CreateMap<CreateSmsConfigurationCommand, SmsConfigurationViewModel>().ReverseMap();
        CreateMap<UpdateSmsConfigurationCommand, SmsConfigurationViewModel>().ReverseMap();

        CreateMap<SmsConfiguration, CreateSmsConfigurationCommand>().ReverseMap();
        CreateMap<UpdateSmsConfigurationCommand, SmsConfiguration>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<SmsConfiguration, SmsConfigurationDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SmsConfiguration, SmsConfigurationListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}