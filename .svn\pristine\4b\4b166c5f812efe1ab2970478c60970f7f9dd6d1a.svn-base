﻿using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class MsSqlNativeLogShippingMonitorStatusService : BaseService, IMsSqlNativeLogShippingMonitorStatusService
{
    public MsSqlNativeLogShippingMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(
        CreateMsSqlNativeLogShippingMonitorStatusCommand createMsSqlNativeLogShippingMonitorStatusCommand)
    {
        Logger.LogDebug(
            $"Create MsSqlNativeLogShippingMonitorStatus '{createMsSqlNativeLogShippingMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createMsSqlNativeLogShippingMonitorStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(
        UpdateMssqlNativeLogShippingMonitorStatusCommand updateMsSqlNativeLogShippingMonitorStatusCommand)
    {
        Logger.LogDebug(
            $"Update MsSqlNativeLogShippingMonitorStatus '{updateMsSqlNativeLogShippingMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(updateMsSqlNativeLogShippingMonitorStatusCommand);
    }

    public async Task<List<MsSqlNativeLogShippingMonitorStatusListVm>> GetAllMsSqlNativeLogShippingMonitorStatus()
    {
        Logger.LogDebug("Get All MsSqlNativeLogShippingMonitorStatus");

        return await Mediator.Send(new MsSqlNativeLogShippingMonitorStatusListQuery());
    }

    public async Task<MsSqlNativeLogShippingMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MsSqlNativeLogShippingMonitorStatusById");

        Logger.LogDebug($"Get MsSqlNativeLogShippingMonitorStatus Detail By Id '{id}' ");

        return await Mediator.Send(new MsSqlNativeLogShippingMonitorStatusDetailQuery { Id = id });
    }

    public async Task<List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>>
        GetMsSqlNativeLogShippingMonitorStatusByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "MsSqlNativeLogShippingMonitorStatus Type");

        Logger.LogDebug($"Get MsSqlNativeLogShippingMonitorStatus Detail by Type '{type}'");

        return await Mediator.Send(new GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQuery { Type = type });
    }

    public async Task<PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>>
        GetPaginatedMsSqlNativeLogShippingMonitorStatus(GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MsSqlNativeLogShippingMonitorStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllMsSqlNativeLogShippingMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }
}