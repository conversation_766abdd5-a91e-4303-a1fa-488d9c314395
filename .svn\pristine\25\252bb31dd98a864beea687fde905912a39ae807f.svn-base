using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Delete;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Delete;

public class DeleteApprovalMatrixRequestCommandHandler : IRequestHandler<DeleteApprovalMatrixRequestCommand,
    DeleteApprovalMatrixRequestResponse>
{
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IPublisher _publisher;

    public DeleteApprovalMatrixRequestCommandHandler(IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IPublisher publisher)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;

        _publisher = publisher;
    }

    public async Task<DeleteApprovalMatrixRequestResponse> Handle(DeleteApprovalMatrixRequestCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _approvalMatrixRequestRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.ApprovalMatrixRequest),
            new NotFoundException(nameof(Domain.Entities.ApprovalMatrixRequest), request.Id));

        eventToDelete.IsActive = false;

        await _approvalMatrixRequestRepository.UpdateAsync(eventToDelete);

        var response = new DeleteApprovalMatrixRequestResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.ApprovalMatrixRequest), eventToDelete.ProcessName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new ApprovalMatrixRequestDeletedEvent { Name = eventToDelete.ProcessName },
            cancellationToken);

        return response;
    }
}