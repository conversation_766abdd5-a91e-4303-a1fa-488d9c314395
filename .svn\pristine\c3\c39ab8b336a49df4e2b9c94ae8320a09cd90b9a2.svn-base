using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftManagementMonitorStatusFixture : IDisposable
{
    public List<DriftManagementMonitorStatus> DriftManagementMonitorStatusPaginationList { get; set; }
    public List<DriftManagementMonitorStatus> DriftManagementMonitorStatusList { get; set; }
    public DriftManagementMonitorStatus DriftManagementMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string busnessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
    public const string InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
    public const string Type = "TestType";
    public const string Status = "Active";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftManagementMonitorStatusFixture()
    {
        var fixture = new Fixture();

        DriftManagementMonitorStatusList = fixture.Create<List<DriftManagementMonitorStatus>>();

        DriftManagementMonitorStatusPaginationList = fixture.CreateMany<DriftManagementMonitorStatus>(20).ToList();

        DriftManagementMonitorStatusPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftManagementMonitorStatusPaginationList.ForEach(x => x.IsActive = true);
        DriftManagementMonitorStatusPaginationList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());
      
        DriftManagementMonitorStatusList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftManagementMonitorStatusList.ForEach(x => x.IsActive = true);
        DriftManagementMonitorStatusList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());

        DriftManagementMonitorStatusDto = fixture.Create<DriftManagementMonitorStatus>();
        DriftManagementMonitorStatusDto.ReferenceId = Guid.NewGuid().ToString();
        DriftManagementMonitorStatusDto.IsActive = true;
        DriftManagementMonitorStatusDto.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
