﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public  class OneViewRiskMitigationFailedDrillViewRepository: IOneViewRiskMitigationFailedDrillViewRepository
{
    private readonly ApplicationDbContext _dbContext;

    public OneViewRiskMitigationFailedDrillViewRepository(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
        
    }

    public async Task<List<OneViewRiskMitigationFailedDrillView>> ListAllAsync()
    {
        try
        {
            return await _dbContext?.OneViewRiskMitigationFailedDrills?.AsNoTracking().ToListAsync();

        }
        catch (Exception ex)
        {
            throw new Exception("An Error Occure On OneViewRiskMitigationFailedDrillViewRepository while get List detail.");
        }
    }
}
