﻿namespace ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetDetail;

public class LicenseHistoryDetailVm
{
    public string Id { get; set; }

    public string LicenseId { get; set; }

    public string PONumber { get; set; }

    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string CPHostName { get; set; }

    public string Properties { get; set; }

    public string IPAddress { get; set; }

    public string MACAddress { get; set; }

    public string LicenseKey { get; set; }

    public string Validity { get; set; }

    public string ExpiryDate { get; set; }

    public string UpdaterId { get; set; }

    public string ParentPONumber { get; set; }
    public bool IsState { get; set; }
}