﻿using ContinuityPatrol.Application.Features.LogViewer.Events.Create;

namespace ContinuityPatrol.Application.Features.LogViewer.Commands.Create;

public class CreateLogViewerCommandHandler : IRequestHandler<CreateLogViewerCommand, CreateLogViewerResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ILogViewerRepository _logViewerRepository;

    public CreateLogViewerCommandHandler(IMapper mapper, IPublisher publisher, ILogViewerRepository logViewerRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _logViewerRepository = logViewerRepository;
    }

    public async Task<CreateLogViewerResponse> Handle(CreateLogViewerCommand request, CancellationToken cancellationToken)
    {
        var serverLog = _mapper.Map<Domain.Entities.LogViewer>(request);
        serverLog = await _logViewerRepository.AddAsync(serverLog);
        var response = new CreateLogViewerResponse
        {
            Message = Message.Create(nameof(Domain.Entities.LogViewer), serverLog.Name),
            Id = serverLog.ReferenceId
        };
        await _publisher.Publish(new LogViewerCreatedEvent { Name = serverLog.Name}, cancellationToken);
        return response;
    }
}
