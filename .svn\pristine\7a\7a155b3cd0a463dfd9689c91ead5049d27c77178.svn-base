using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class VeritasClusterRepository : BaseRepository<VeritasCluster>, IVeritasClusterRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public VeritasClusterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
   
    public async Task<bool> IsNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await Entities.AnyAsync(e => e.ClusterProfileName.Equals(name))
            : (await Entities.Where(e => e.ClusterProfileName.Equals(name)).ToListAsync()).Unique(id);
    }

    public async Task<bool> IsVeritasClusterNameUnique(string name)
    {
        return await _dbContext.VeritasClusters.AnyAsync(e => e.ClusterProfileName.Equals(name));
    }

    public override async Task<PaginatedResult<VeritasCluster>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<VeritasCluster> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(productFilterSpec).DescOrderById()
            .Select(x => new VeritasCluster
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                ClusterProfileName=x.ClusterProfileName,
                ClusterServerId=x.ClusterServerId,
                ClusterServerName=x.ClusterServerName,
                ClusterName=x.ClusterName,
                ClusterBinPath=x.ClusterBinPath
            }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}
