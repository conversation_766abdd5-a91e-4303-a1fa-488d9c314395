using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ApprovalMatrixUsersRepositoryTests : IClassFixture<ApprovalMatrixUsersFixture>
{
    private readonly ApprovalMatrixUsersFixture _approvalMatrixUsersFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ApprovalMatrixUsersRepository _repository;

    public ApprovalMatrixUsersRepositoryTests(ApprovalMatrixUsersFixture approvalMatrixUsersFixture)
    {
        _approvalMatrixUsersFixture = approvalMatrixUsersFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ApprovalMatrixUsersRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;

        // Act
        var result = await _repository.AddAsync(approvalMatrixUsers);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixUsers.UserName, result.UserName);
        Assert.Equal(approvalMatrixUsers.AcceptType, result.AcceptType);
        Assert.Single(_dbContext.ApprovalMatrixUsers);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        await _repository.AddAsync(approvalMatrixUsers);

        approvalMatrixUsers.UserName = "UpdatedUserName";
        approvalMatrixUsers.Email = "<EMAIL>";
        approvalMatrixUsers.MobileNumber = "123456879"
        ;

        // Act
        var result = await _repository.UpdateAsync(approvalMatrixUsers);

        // Assert
        Assert.Equal("UpdatedUserName", result.UserName);
        Assert.Equal("<EMAIL>", result.Email);
        Assert.Equal("123456879",result.MobileNumber);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        await _repository.AddAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.DeleteAsync(approvalMatrixUsers);

        // Assert
        Assert.Equal(approvalMatrixUsers.UserName, result.UserName);
        Assert.Empty(_dbContext.ApprovalMatrixUsers);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        var addedEntity = await _repository.AddAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.UserName, result.UserName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        await _repository.AddAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.GetByReferenceIdAsync(approvalMatrixUsers.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixUsers.ReferenceId, result.ReferenceId);
        Assert.Equal(approvalMatrixUsers.UserName, result.UserName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        await _repository.AddRangeAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixUsers.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;

        // Act
        var result = await _repository.AddRangeAsync(approvalMatrixUsers);

        // Assert
        Assert.Equal(approvalMatrixUsers.Count, result.Count());
        Assert.Equal(approvalMatrixUsers.Count, _dbContext.ApprovalMatrixUsers.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        await _repository.AddRangeAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.RemoveRangeAsync(approvalMatrixUsers);

        // Assert
        Assert.Equal(approvalMatrixUsers.Count, result.Count());
        Assert.Empty(_dbContext.ApprovalMatrixUsers);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        var targetUserName = "TEST_USER";
        approvalMatrixUsers.First().UserName = targetUserName;
        await _repository.AddRangeAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.UserName == targetUserName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetUserName, result.First().UserName);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        await _repository.AddRangeAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.UserName == "NON_EXISTENT_USER");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenUserNameExistsAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        approvalMatrixUsers.UserName = "ExistingUserName";
        await _repository.AddAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.IsNameExist("ExistingUserName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenUserNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        await _repository.AddRangeAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.IsNameExist("NonExistentUserName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenUserNameExistsForSameEntity()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        approvalMatrixUsers.UserName = "SameUserName";
        await _repository.AddAsync(approvalMatrixUsers);

        // Act
        var result = await _repository.IsNameExist("SameUserName", approvalMatrixUsers.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestUserName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var users = _approvalMatrixUsersFixture.ApprovalMatrixUsersList;
        var user1 = users[0];
        var user2 = users[1];

        // Act
        var task1 = _repository.AddAsync(user1);
        var task2 = _repository.AddAsync(user2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ApprovalMatrixUsers.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var users = _approvalMatrixUsersFixture.ApprovalMatrixUsersList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(users);
        var initialCount = users.Count;

        var toUpdate = users.Take(2).ToList();
        toUpdate.ForEach(x => x.UserName = "UpdatedUserName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = users.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.UserName == "UpdatedUserName").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInUserNames()
    {
        // Arrange
        var approvalMatrixUsers = _approvalMatrixUsersFixture.ApprovalMatrixUsersDto;
        approvalMatrixUsers.UserName = "Test@User#123$%";

        // Act
        var addedUser = await _repository.AddAsync(approvalMatrixUsers);
        var nameExists = await _repository.IsNameExist("Test@User#123$%", "invalid-guid");

        // Assert
        Assert.NotNull(addedUser);
        Assert.Equal("Test@User#123$%", addedUser.UserName);
        Assert.True(nameExists);
    }

  
    #endregion
}
