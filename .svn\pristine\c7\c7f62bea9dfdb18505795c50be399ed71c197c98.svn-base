﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class JobRepositoryMocks
{
    public static Mock<IJobRepository> CreateJobRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(jobs);

        jobRepository.Setup(repo => repo.AddAsync(It.IsAny<Job>())).ReturnsAsync((Job job) =>
        {
            job.Id = new Fixture().Create<int>();

            job.ReferenceId = new Fixture().Create<Guid>().ToString();

            jobs.Add(job);

            return job;
        });

        return jobRepository;
    }

    public static Mock<IJobRepository> UpdateJobRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(jobs);

        jobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => jobs.SingleOrDefault(x => x.ReferenceId == i));

        jobRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Job>())).ReturnsAsync((Job job) =>
        {

            var index = jobs.FindIndex(item => item.Id == job.Id);

            jobs[index] = job;

            return job;
        });

        return jobRepository;
    }

    public static Mock<IJobRepository> DeleteJobRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(jobs);

        jobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => jobs.SingleOrDefault(x => x.ReferenceId == i));

        jobRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Job>())).ReturnsAsync((Job job) =>
        {
            var index = jobs.FindIndex(item => item.Id == job.Id);

            job.IsActive = false;

            jobs[index] = job;

            return job;
        });

        return jobRepository;
    }

    public static Mock<IJobRepository> GetJobRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(jobs);

        jobRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => jobs.SingleOrDefault(x => x.ReferenceId == i));

        return jobRepository;
    }

    public static Mock<IJobRepository> GetJobEmptyRepository()
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.GetJobsByInfraObjectId(It.IsAny<string>())).ReturnsAsync(new List<Job>());

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Job>());

        return jobRepository;
    }

    public static Mock<IJobRepository> GetJobsByInfraObjectId(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.GetJobsByInfraObjectId(It.IsAny<string>())).ReturnsAsync(jobs);

        return jobRepository;
    }

    public static Mock<IJobRepository> GetJobByTemplateIdRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.GetJobByTemplateId(It.IsAny<string>())).ReturnsAsync((string i) => jobs.SingleOrDefault(x => x.TemplateId == i));

        return jobRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateJobEventRepository(List<UserActivity> userActivities)
    {
        var jobEventRepository = new Mock<IUserActivityRepository>();

        jobEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        jobEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);

                return userActivity;
            });

        return jobEventRepository;
    }



    public static Mock<IJobRepository> GetJobNamesRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.GetJobNames()).ReturnsAsync(jobs);

        jobRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(jobs);

        return jobRepository;
    }

    public static Mock<IJobRepository> GetJobNameUniqueRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        jobRepository.Setup(repo => repo.IsJobNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => jobs.Exists(x => x.Name == i && x.ReferenceId == j));

        return jobRepository;
    }

    public static Mock<IJobRepository> GetPaginatedJobRepository(List<Job> jobs)
    {
        var jobRepository = new Mock<IJobRepository>();

        var queryableJobScheduler = jobs.BuildMock();

        jobRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableJobScheduler);

        return jobRepository;
    }

}
