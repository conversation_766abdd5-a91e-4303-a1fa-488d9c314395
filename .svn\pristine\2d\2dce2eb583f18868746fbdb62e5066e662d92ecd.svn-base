using ContinuityPatrol.Application.Features.PageWidget.Events.Delete;

namespace ContinuityPatrol.Application.Features.PageWidget.Commands.Delete;

public class DeletePageWidgetCommandHandler : IRequestHandler<DeletePageWidgetCommand, DeletePageWidgetResponse>
{
    private readonly IPageWidgetRepository _pageWidgetRepository;
    private readonly IPublisher _publisher;

    public DeletePageWidgetCommandHandler(IPageWidgetRepository pageWidgetRepository, IPublisher publisher)
    {
        _pageWidgetRepository = pageWidgetRepository;

        _publisher = publisher;
    }

    public async Task<DeletePageWidgetResponse> Handle(DeletePageWidgetCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _pageWidgetRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.PageWidget),
            new NotFoundException(nameof(Domain.Entities.PageWidget), request.Id));

        eventToDelete.IsActive = false;

        await _pageWidgetRepository.UpdateAsync(eventToDelete);

        var response = new DeletePageWidgetResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.PageWidget), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new PageWidgetDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}