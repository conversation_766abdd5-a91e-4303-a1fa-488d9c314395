﻿
let mId = sessionStorage.getItem("monitorId")
let monitortype = 'OracleRac';
let infraObjectId = sessionStorage.getItem("infraobjectId");
let mode = ""
setTimeout(() => { oracleracmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { oracleRacServer(infraObjectId) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId, mode) }, 250)
$('#mssqlserver').hide();
async function oracleRacServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);
    
    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        $('#mssqlserver').show();
        
        bindRacServer(mssqlServerData)
    } else {
        $('#mssqlserver').hide();
    }

}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

function bindRacServer(mssqlServerData) {
    console.log(mssqlServerData, 'mssqlServerData');

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData.forEach(item => {
        let parsedServices = [];
        try {
            parsedServices = JSON.parse(item?.isServiceUpdate || '[]');
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }
        console.log(parsedServices, 'parsedServices')
        parsedServices.forEach(serviceGroup => {
            if (serviceGroup.Type === 'PR') {
                prType = {
                    IpAddress: serviceGroup.IpAddress || '--',
                    Services: serviceGroup.Services || []
                };
            } else if (serviceGroup.Type === 'DR') {
                drType = {
                    IpAddress: serviceGroup.IpAddress || '--',
                    Services: serviceGroup.Services || []
                };
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType.IpAddress + ')');
    $('#drIp').text('DR (' + drType.IpAddress + ')');

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType.Services.map(s => s.ServiceName),
        ...drType.Services.map(s => s.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames.forEach(serviceName => {
        let prService = prType.Services.find(s => s.ServiceName === serviceName);
        let drService = drType.Services.find(s => s.ServiceName === serviceName);

        let prStatus = prService ? prService.Status : '--';
        let drStatus = drService ? drService.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

async function oracleracmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData, monitoringData?.type);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noImage);
    }
}

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

let noImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
let asmNoData = '<div class="asmData"><img src="/img/isomatric/nodatalag.svg" class="mx-auto"> <br><span class="text-danger">No data available</span></div>'
function propertiesData(value, type) {
    if (!value) {
        $("#noDataimg").css('text-align', 'center').html(noImage);
    }
    else {

        let data1 = JSON?.parse(value?.properties);
        let data2 = data1?.MonitoringOracleRacModel
       
        if (data2[0]?.DrModels?.length >= 2) $("#Sitediv").show();
        else $("#Sitediv").hide();

        Array.isArray(data2) && data2?.length && data2[0]?.DrModels && data2[0]?.DrModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });

        if (data2.length > 0) {
            $("#siteName0 .nav-link").addClass("active"); 
            displayNodeBasedDetails(data2)  
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()

            $('.dynamicSite_header').empty().append(getSiteName).attr('title', getSiteName)

            let MonitoringModel = Array.isArray(data2) && data2?.length && data2?.filter((d) => {
                return d?.DrModels && d?.DrModels?.length && d?.DrModels?.some((datas) => {
                    return datas?.Type === getSiteName;
                });
            });

            if (Array.isArray(MonitoringModel) && MonitoringModel?.length) {
                displayNodeBasedDetails(MonitoringModel, 'siteChange');
            }
        });
 

    }
}

//Display Details based on Nodename
function displayNodeBasedDetails(nodeBasedData, siteMode = '') {
    
    let Cluster = nodeBasedData;

    let InstanceDetails = nodeBasedData;

    let ServerDetails = nodeBasedData;

    SelectnodeName(Cluster, siteMode);
    Selectinstancenode(InstanceDetails, siteMode);
    Selectservernode(ServerDetails, siteMode);
    monitoringSolution(infraObjectId, mode);
    getASMDetails(nodeBasedData, siteMode)

    $('#clusterDetails').on('change', function () {
        
        let selectedNodeName = $(this).find('option:selected').text();
        let selectedNodeData = Array.isArray(Cluster) && Cluster?.length ? Cluster?.find(obj => `${obj?.Node}` === selectedNodeName) : {};

        const clusterChange = Array.isArray(Cluster) && Cluster?.length ? Cluster?.filter(t => t?.Node === selectedNodeData.Node) : [];
        
        let selectedNode = Array.isArray(InstanceDetails) && InstanceDetails?.length ? InstanceDetails?.find(obj => `${obj?.Node}` === selectedNodeName) : {};
        const instanceChange = Array.isArray(InstanceDetails) && InstanceDetails?.length ? InstanceDetails?.filter(t => t?.Node === selectedNode?.Node) : [];

        let selectedserverNode = Array.isArray(ServerDetails) && ServerDetails?.length ? ServerDetails?.find(obj => `${obj?.Node}` === selectedNodeName) : {};
        const serverChange = Array.isArray(ServerDetails) && ServerDetails?.length ? ServerDetails?.filter(t => t?.Node === selectedserverNode?.Node) : [];

        clusterDetails(clusterChange);
        instanceDetails(instanceChange);
        serverDetails(serverChange);
        getASMDetails(clusterChange)
    });

}
// Node dropdown
function SelectnodeName(data2, siteMode) {

    if (!siteMode) {
        $('#clusterDetails').empty();

        Array.isArray(data2) && data2?.length && data2?.forEach((d, index) => {

            const option = document.createElement('option');
            option.value = d?.Node;
            option.textContent = `${d?.Node}`;
            $('#clusterDetails').append(option);
            if (index === 0) {

                const oraclecluster = data2?.filter(a => a?.Node === d?.Node);
                clusterDetails(oraclecluster);
            }
        });
    } else {

        const selectedNodeName = $('#clusterDetails').val();
        const oraclecluster = Array.isArray(data2) && data2?.length && data2?.filter(a => a?.Node === selectedNodeName);
        clusterDetails(oraclecluster);
    }
}
function Selectinstancenode(data2, siteMode) {

    //if (!siteMode) {
    //    Array.isArray(data2) && data2.length && data2.forEach((d, index) => {

    //        if (index === 0) {
    //            const oracleinstance = data2.filter(a => a.Node === d.Node);
    //            instanceDetails(oracleinstance);
    //        }
    //    });
    //} else {
        const selectedNodeName = $('#clusterDetails').val();
        const oracleinstance = Array.isArray(data2) && data2?.length && data2?.filter(a => a?.Node === selectedNodeName);
        instanceDetails(oracleinstance);
   /* }*/
}
function Selectservernode(data2, siteMode) {

    //if (!siteMode) {
    //    Array.isArray(data2) && data2.length && data2.forEach((d, index) => {

    //        if (index === 0) {
    //            const oracleserver = data2.filter(a => a.Node === d.Node);
    //            serverDetails(oracleserver);
    //        }
    //    });
    //} else {
        const selectedNodeName = $('#clusterDetails').val();
        const oracleserver = Array.isArray(data2) && data2?.length && data2?.filter(a => a?.Node === selectedNodeName);
        serverDetails(oracleserver);
   /* }*/
}

const getDynamicSiteData = (data2) => {
    let particularTypeData = $(".siteListChange .nav-link.active .siteName").text();

    let filteredData = {};

    if (Array.isArray(data2) && data2?.length) {
        filteredData = data2[0]?.DrModels && data2[0]?.DrModels?.length && data2[0]?.DrModels?.find((data) => {
            return data?.Type === particularTypeData
        })
    }

    return filteredData
}
function clusterDetails(data2) {

   if (Array.isArray(data2) && data2?.length) {

       let getDynamicData = getDynamicSiteData(data2)

    if (data2[0]?.PrModel && data2[0]?.PrModel?.PrMonitoringModel) { 
        const cluster_PR = data2[0]?.PrModel?.PrMonitoringModel?.PRClusterDetails;

        const prop_PR = ["PR_Cluster_Name", "PR_ClusterWare_Active_Version", "PR_OHAS_Status", "PR_CRS_Status", "PR_CSS_Status", "PR_EVM_Status", "PR_Scan_Listener_Status", "PR_Cluster_Listener", "PR_Scan_Status"];

        if (cluster_PR) {
            bindProperties(cluster_PR, prop_PR);
        }
    }

    const cluster_DynamicSite = getDynamicData?.MonitoringModel?.ClusterDetails;
    const prop_DR = ["Cluster_Name", "ClusterWare_Active_Version", "OHAS_Status", "CRS_Status", "CSS_Status", "EVM_Status", "Scan_Listener_Status", "Cluster_Listener", "Scan_Status"];

       if (getDynamicData) {
           bindProperties(cluster_DynamicSite, prop_DR);
       }

   }
}
function instanceDetails(data2) {

    if (Array.isArray(data2) && data2?.length) {

        let getDynamicData = getDynamicSiteData(data2)

        if (data2[0]?.PrModel && data2[0]?.PrModel?.PrMonitoringModel) {

            const instance_PR = data2[0]?.PrModel?.PrMonitoringModel?.PROdgInstanceDetails;
            const prop_PR = ["PR_InstanceName", "PR_InstanceId", "PR_InstanceStartUpTime", "PR_OpenMode", "PR_IsClusterDatabase", "PR_ControlfileName", "PR_ParameterFile", "PR_Platform_name"];

            if (instance_PR) {
                bindProperties(instance_PR, prop_PR);
            }

        }
        const instance_DynamicSite = getDynamicData?.MonitoringModel?.OdgInstanceDetails;
        const prop_DR = ["InstanceName", "InstanceId", "InstanceStartUpTime", "OpenMode", "IsClusterDatabase", "ControlfileName", "ParameterFile", "Platform_name"];

        if (instance_DynamicSite) {
            bindProperties(instance_DynamicSite, prop_DR);
        }
    }
}
function serverDetails(data2) {
    debugger
    // server Details
    const server_PR = data2[0]?.PrModel?.PrMonitoringModel;
    const server_PR_Log = data2[0]?.PrModel?.PrMonitoringModel;
    const PrServerDetails = data2[0]?.PrModel?.PrMonitoringModel?.PrServerDetails;   
    const prop_PR = ["PR_Unique_Name"]
    const prop_PR_Log = ["PR_Log_sequence"]

    let getDynamicData = getDynamicSiteData(data2)
     
    const server_DynamicSite = getDynamicData?.MonitoringModel;
    const server_DynamicSite_Log = getDynamicData?.MonitoringModel?.ServerDetails;    
    const prop_DR = ["Unique_Name"];
    const prop_DR_Log = ["Log_sequence"]

    if (server_PR) bindProperties(server_PR, prop_PR);

    if (server_PR_Log) bindProperties(PrServerDetails, prop_PR_Log);

    if (server_DynamicSite) bindProperties(server_DynamicSite, prop_DR);

    if (server_DynamicSite_Log) bindProperties(server_DynamicSite_Log, prop_DR_Log);


    // Database Details

    const dbDetails_PR = server_PR?.PrDatabaseMonitoring;
    const dbDetailsProp_PR = ["PR_Database_name", "PR_Database_role", "PR_Openmode", "PR_Database_createdtime", "PR_Control_filetype", "PR_Currentscn", "PR_Flashback_on",
        "PR_Database_version", "PR_Database_incarnation", "PR_DB_Reset_logschange", "PR_Reset_logsmode", "PR_Archive_mode", "PR_Dbsize", "PR_Db_create_file_dest",
        "PR_Db_file_name_convert", "PR_Db_create_online_log_dest1", "PR_Log_file_name_convert", "PR_Db_recovery_file_dest", "PR_Db_recovery_file_dest_size", "PR_Db_flashback_retention_target"];

    if (dbDetails_PR) bindProperties(dbDetails_PR, dbDetailsProp_PR);

    const dbDetails_DynamicSite = getDynamicData?.MonitoringModel?.DatabaseMonitoring;
    const dbDetailsProp_DR = ["Database_name", "Database_role", "Openmode", "Database_createdtime", "Control_filetype", "Currentscn", "Flashback_on",
       "Database_version", "Database_incarnation", "DB_Reset_logschange", "Reset_logsmode", "Archive_mode", "Dbsize", "Db_create_file_dest",
        "Db_file_name_convert", "Db_create_online_log_dest1", "Log_file_name_convert", "Db_recovery_file_dest", "Db_recovery_file_dest_size", "Db_flashback_retention_target"];


    if (dbDetails_DynamicSite) bindProperties(dbDetails_DynamicSite, dbDetailsProp_DR);

    // Replication Details

    const replica_PR = server_PR?.PrReplicationMonitoring;
    const replicaDetailsProp_PR = ["PR_Active_DG_Enabled", "PR_Dg_broker_status", "PR_Dataguard_status", "PR_Recovery_Status", "PR_Switchover_status", "PR_Log_archive_config", "PR_Force_logging", "PR_Fal_server", "PR_Fal_client", "PR_Standby_redo_logs", "PR_Apply_lag",
        "PR_Transport_lag", "PR_Apply_finish_time", "PR_Standby_file_management", "PR_Archive_Dest_Location",
        "PR_Protection_mode", "PR_Transmit_mode", "PR_Estimated_startup_time", "PR_Recovery_mode", "PR_Affirm", "PR_Archiver", "PR_Archivelog_compression", "PR_Delay_mins"];

    if (replica_PR) bindProperties(replica_PR, replicaDetailsProp_PR);
    
    const replica_DynamicSite = getDynamicData?.MonitoringModel?.ReplicationMonitoring;
    const replicaDetailsProp_DR = ["Active_DG_Enabled", "Dg_broker_status", "Dataguard_status", "Recovery_Status", "Switchover_status", "Log_archive_config", "Force_logging", "Fal_server", "Fal_client", "Standby_redo_logs", "Apply_lag",
        "Transport_lag", "Apply_finish_time", "Standby_file_management", "Archive_Dest_Location",
        "Protection_mode", "Transmit_mode", "Estimated_startup_time", "Recovery_mode", "Affirm", "Archiver", "Archivelog_compression", "Delay_mins"];

    if (replica_DynamicSite) bindProperties(replica_DynamicSite, replicaDetailsProp_DR);

    //TNSService
    const prTNSRaw = checkAndReplace(server_PR?.PrTnsServiceDetails?.PR_TNSServiceName)?.trim() || "";
    const prTNSList = prTNSRaw ? prTNSRaw.split('\n').filter(item => item.trim() !== "") : [];
    const prTNSHtml = prTNSList.map(item =>
        `<li class="list-group-item p-1 text-truncate" title="${item}">${addIcon(item)}${item}</li>`
    ).join('');
    $('#PR_TNSServiceName').empty().append(prTNSHtml);

    const drTNSRaw = checkAndReplace(getDynamicData?.MonitoringModel?.TnsServiceDetails?.TNSServiceName)?.trim() || "";
    const drTNSList = drTNSRaw ? drTNSRaw.split('\n').filter(item => item.trim() !== "") : [];
    const drTNSHtml = drTNSList.map(item =>
        `<li class="list-group-item p-1 text-truncate" title="${item}">${addIcon(item)}${item}</li>`
    ).join('');
    $('#TNSServiceName').empty().append(drTNSHtml);
    //const tnsService_PR = server_PR?.PrTnsServiceDetails;
    //consttnsServiceProp_PR = ["PR_TNSServiceName"]
    //if (tnsService_PR) bindProperties(tnsService_PR, consttnsServiceProp_PR);

    //const tnsService_DynamicSite = getDynamicData?.MonitoringModel?.TnsServiceDetails;
    //const tnsServiceProp_DR = ["TNSServiceName"];

    //if (tnsService_DynamicSite) bindProperties( tnsService_DynamicSite, tnsServiceProp_DR);

    //Multitenancy
    const multiTenancy_PR = server_PR?.PrMultiTenanceDetails;
    const multiTenancyProp_PR = ["PR_CDB", "PR_Containers", "PR_Pdbs"];

    if (multiTenancy_PR) bindProperties(multiTenancy_PR, multiTenancyProp_PR);    

    const multiTenancy_DynamicSite = getDynamicData?.MonitoringModel?.OdgMultiTenanceDetails;
    const multiTenancyProp_DR = ["CDB", "Containers", "Pdbs"];

    if (multiTenancy_DynamicSite) bindProperties(multiTenancy_DynamicSite, multiTenancyProp_DR);

    const ODG = server_PR?.PrReplicationMonitoring;
    const hourly = ODG?.Archieve_Log_Genearion_Hourly && ODG?.Archieve_Log_Genearion_Hourly !== "NA" ? JSON.parse(ODG?.Archieve_Log_Genearion_Hourly) : {};
    const daily = ODG?.Archieve_Log_Genearion_Day && ODG?.Archieve_Log_Genearion_Day !== "NA" ? JSON.parse(ODG?.Archieve_Log_Genearion_Day) : {};
    const weekly = ODG?.Archieve_Log_Genearion_Weekly && ODG?.Archieve_Log_Genearion_Weekly !== "NA" ? JSON.parse(ODG?.Archieve_Log_Genearion_Weekly) : {};

    archiveLogDaily(daily);
    archiveLogHour(hourly);
    archiveLogWeek(weekly);
    
}

const getASMDetails = (nodeData) => {

    const selectedNodeName = $('#clusterDetails').val();
    const getSelectedData = Array.isArray(nodeData) && nodeData?.length && nodeData?.filter(a => a?.Node === selectedNodeName);
    const getDynamicData = getDynamicSiteData(nodeData)

    if (Array.isArray(getSelectedData) && getSelectedData?.length) displayASM(getSelectedData[0]?.PrModel?.PrMonitoringModel?.PrAsmDetails?.PR_Asm_Details, '#prASM', '#asmPrimary');
    if (getDynamicData) displayASM(getDynamicData?.MonitoringModel?.AsmDetails?.Asm_Details, '#drASM', '#asmDR');
}

//ASM Details
function displayASM(val, target, element) {

    $(target).empty();

    try {
        if (!val) {
            $(target).hide();
            $(element).find('thead').hide();
            $(element).find('.asmData').remove();

            $(element)
                .css('text-align', 'center')
                .append(asmNoData);
        } else {
            let data = val.replace(/,(?=\s*[\]}])/, '');
            let asmVal = JSON?.parse(data);

            if (asmVal?.length > 0) {
                $(target).show();
                $(element).find('thead').show();
                $(element).find('.asmData').remove();

                const asmRows = asmVal.map((list, i) => `<tr><td>${i + 1}</td><td class="text-truncate" title="${list?.NAME}">${list?.NAME}</td><td>${list?.STATE}</td><td>${list?.TYPE}</td><td>${list?.TOTAL_MB}</td><td>${list?.FREE_MB}</td><td>${list['USED(%)']}</td></tr>`).join('');
                $(target).append(asmRows);
            }
            else {

                $(target).hide();
                $(element).find('thead').hide();
                $(element).find('.asmData').remove();

                $(element)
                    .css('text-align', 'center')
                    .append(asmNoData);
            }
        }
    } catch (error) {

        $(target).hide();
        $(element).find('thead').hide();
        $(element).find('.asmData').remove();

        $(element)
            .css('text-align', 'center')
            .append(asmNoData);

    }
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function bindProperties(data, properties, value) {

    properties?.forEach((item) => {
        const value = data[item];
        const displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        const iconHtml = addIcon(displayedValue, item, data, value);
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${item}`).html(mergeValue).attr('title', displayedValue);
        // Displayed value with icon
        if (item !== 'PR_Database_createdtime' && item !== 'Database_createdtime' && item !== 'PR_Scan_Listener_Status' && item !== 'Scan_Listener_Status' && item !== 'PR_Cluster_Listener' && item !== 'Cluster_Listener' && item !== 'PR_Scan_Status' && item !== 'Scan_Status') {
            //const iconHtml = addIcon(displayedValue);
            const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';  
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${item}`).html(mergeValue).attr('title', displayedValue);
                     
        }
        else if (item === 'PR_Database_createdtime' || item === 'Database_createdtime' || item === 'PR_Scan_Listener_Status' || item === 'Scan_Listener_Status' || item === 'PR_Cluster_Listener' || item === 'Cluster_Listener' || item === 'PR_Scan_Status' || item === 'Scan_Status') {
            if (displayedValue !== "NA") {
                if (item === 'PR_Scan_Listener_Status' || item === 'Scan_Listener_Status' || item === 'PR_Cluster_Listener' || item === 'Cluster_Listener' || item === 'PR_Scan_Status' || item === 'Scan_Status') {
                    const inputString = displayedValue.split(/\r?\n/);
                    const mergeValue = inputString.join("<br>");
                    $(`#${item}`).html(mergeValue).attr('title', displayedValue);
                }
                else {
                    const [date, time] = displayedValue?.split(' ');
                    const formattedValue = `${date}<br>${time}`;
                    const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : ''; /*addIcon(displayedValue, item)*/
                    const mergeValue = `${iconHtml}${formattedValue}`;
                    $(`#${item}`).html(mergeValue).attr('title', `${date} ${time}`);
                }
            }
            else {
                const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : ''; /*addIcon(displayedValue);*/
                const mergeValue = `${iconHtml}${displayedValue}`;
                $(`#${item}`).html(mergeValue).attr('title', displayedValue);
            }
        }
        else {
            const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : ''; /* addIcon(displayedValue);*/
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${item}`).html(mergeValue).attr('title', displayedValue);
        }
    });
}

function addIcon(displayedValue, property, data) {
    
    let prUnique = data?.PR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let drUnique = data?.DR_Unique_Name ? "text-primary cp-database-unique-name me-1 fs-6" : "text-danger cp-disable"
    let prVersion = data?.PR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let drVersion = data?.DR_Database_version ? "text-primary cp-version me-1 fs-6" : "text-danger cp-disable"
    let prDB = data?.PR_Database_incarnation ? "text-primary cp-refresh me-1 fs-6" : "text-danger cp-disable"
    let drDB = data?.DR_Database_incarnation ? "text-primary cp-refresh me-1 fs-6" : "text-danger cp-disable"
    let prArchieve = data?.PR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let drArchieve = data?.DR_Archive_mode ? "text-success cp-archive-mode me-1 fs-6" : "text-danger cp-disable"
    let prFile = data?.PR_Db_create_file_dest ? "text-primary cp-db-create-online me-1 fs-6" : "text-danger cp-disable"
    let drFile = data?.DR_Db_create_file_dest ? "text-primary cp-db-create-online me-1 fs-6" : "text-danger cp-disable"
    let prdbFile = data?.PR_Db_file_name_convert ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"
    let drdbFile = data?.DR_Db_file_name_convert ? "text-primary cp-database me-1 fs-6" : "text-danger cp-disable"
    let prLogFile = data?.PR_Log_file_name_convert ? "text-success cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let drLogFile = data?.DR_Log_file_name_convert ? "text-success cp-log-file-name me-1 fs-6" : "text-danger cp-disable"
    let prFileDest = data?.PR_Db_recovery_file_dest ? "text-success cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let drFileDest = data?.DR_Db_recovery_file_dest ? "text-success cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let prFileSize = data?.PR_Db_recovery_file_dest_size ? "text-success cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let drFileSize = data?.DR_Db_recovery_file_dest_size ? "text-success cp-roate-settings me-1 fs-6" : "text-danger cp-disable"
    let prFlash = data?.PR_Db_flashback_retention_target ? "text-primary cp-warning me-1 fs-6" : "text-danger cp-disable"
    let drFlash = data?.DR_Db_flashback_retention_target ? "text-primary cp-warning me-1 fs-6" : "text-danger cp-disable"
    let prInstance = data?.PR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let drInstance = data?.DR_InstanceName ? "text-primary cp-instance-name me-1 fs-6" : "text-danger cp-disable"
    let prInstanceId = data?.PR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let drInstanceId = data?.DR_InstanceId ? "text-primary cp-instance-id me-1 fs-6" : "text-danger cp-disable"
    let prInstancestart = data?.PR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let drInstancestart = data?.DR_InstanceStartUpTime ? "text-primary cp-timer-meter me-1 fs-6" : "text-danger cp-disable"
    let prControl = data?.PR_ControlfileName ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let drControl = data?.DR_ControlfileName ? "text-primary cp-control-file-type me-1 fs-6" : "text-danger cp-disable"
    let prParamFile = data?.PR_ParameterFile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let drParamFile = data?.DR_ParameterFile ? "text-primary cp-parameter-file me-1 fs-6" : "text-danger cp-disable"
    let prPlatform = data?.PR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    let drPlatform = data?.DR_Platform_name ? "text-primary cp-platform-name me-1 fs-6" : "text-danger cp-disable"
    let prFal = data?.PR_Fal_server ? "text-primary cp-stand-server me-1 fs-6" : "text-danger cp-disable"
    let drFal = data?.DR_Fal_server ? "text-primary cp-stand-server me-1 fs-6" : "text-danger cp-disable"
    let prFalclient = data?.PR_Fal_client ? "text-success cp-fal-client me-1 fs-6" : "text-danger cp-disable"
    let drFalclient = data?.DR_Fal_client ? "text-success cp-fal-client me-1 fs-6" : "text-danger cp-disable"
    let prDest = data?.PR_Archive_Dest_Location ? "text-success cp-site-location me-1 fs-6" : "text-danger cp-disable"
    let drDest = data?.DR_Archive_Dest_Location ? "text-success cp-site-location me-1 fs-6" : "text-danger cp-disable"
    let prMode = data?.PR_Protection_mode ? "text-primary cp-protection-mode me-1 fs-6" : "text-danger cp-disable"
    let drMode = data?.DR_Protection_mode ? "text-primary cp-protection-mode me-1 fs-6" : "text-danger cp-disable"
    let prArchiever = data?.PR_Archiver ? "text-primary cp-files me-1 fs-6" : "text-danger cp-disable"
    let drArchiever = data?.DR_Archiver ? "text-primary cp-files me-1 fs-6" : "text-danger cp-disable"
    let prDelay = data?.PR_Delay_mins ? "text-success cp-time-one me-1 fs-6" : "text-danger cp-disable"
    let drDelay = data?.PR_Delay_mins ? "text-success cp-time-one me-1 fs-6" : "text-danger cp-disable"

    const iconMapping = {
        'PR_Unique_Name': prUnique,
        'DR_Unique_Name': drUnique,
        'PR_Database_version': prVersion,
        'DR_Database_version': drVersion,
        'PR_Database_incarnation': prDB,
        'DR_Database_incarnation': drDB,
        'PR_Archive_mode': prArchieve,
        'DR_Archive_mode': drArchieve,
        'PR_Db_create_file_dest': prFile,
        'DR_Db_create_file_dest': drFile,
        'PR_Db_file_name_convert': prdbFile,
        'DR_Db_file_name_convert': drdbFile,
        'PR_Log_file_name_convert': prLogFile,
        'DR_Log_file_name_convert': drLogFile,
        'PR_Db_recovery_file_dest': prFileDest,
        'DR_Db_recovery_file_dest': drFileDest,
        'PR_Db_recovery_file_dest_size': prFileSize,
        'DR_Db_recovery_file_dest_size': drFileSize,
        'PR_Db_flashback_retention_target': prFlash,
        'DR_Db_flashback_retention_target': drFlash,
        'PR_InstanceName': prInstance,
        'DR_InstanceName': drInstance,
        'PR_InstanceId': prInstanceId,
        'DR_InstanceId': drInstanceId,
        'PR_InstanceStartUpTime': prInstancestart,
        'DR_InstanceStartUpTime': drInstancestart,
        'PR_ControlfileName': prControl,
        'DR_ControlfileName': drControl,
        'PR_ParameterFile': prParamFile,
        'DR_ParameterFile': drParamFile,
        'PR_Platform_name': prPlatform,
        'DR_Platform_name': drPlatform,
        'PR_Fal_server': prFal,
        'DR_Fal_server': drFal,
        'PR_Fal_client': prFalclient,
        'DR_Fal_client': drFalclient,
        'PR_Archive_Dest_Location': prDest,
        'DR_Archive_Dest_Location': drDest,
        'PR_Protection_mode': prMode,
        'DR_Protection_mode': drMode,
        'PR_Archiver': prArchiever,
        'DR_Archiver': drArchiever,
        'PR_Delay_mins': prDelay,
        'DR_Delay_mins': drDelay,
    }
    iconClass = iconMapping[property] || ''; 

    switch (displayedValue?.toLowerCase()) {
        case 'na':
            iconClass = 'text-danger cp-disable';
            break;
        case 'primary':
            iconClass = 'text-primary cp-list-prsite';
            break;
        case 'physical standby':
            iconClass = 'text-info cp-physical-drsite';
            break;
        case 'standby':
        case 'to standby':
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'current':
        case 'read write':
            iconClass = 'text-success cp-file-edits';
            break;
        case 'mounted':
            iconClass = 'text-warning cp-control-file-type';
            break;
        case 'false':
        case 'defer':
        case 'deferred':
            iconClass = 'text-danger cp-error';
            break;
        case 'true':
            iconClass = 'text-success cp-success';
            break;
        case 'disabled':
        case 'disable':
            iconClass = 'text-danger cp-disables';
            break;
        case 'enabled':
        case 'enable':
            iconClass = 'text-success cp-enables';
            break;
        case 'not allowed':
        case 'no':
            iconClass = 'text-danger cp-disagree';
            break;      
        case 'yes':
            iconClass = 'text-success cp-agree';
            break;
        case 'valid':
            iconClass = 'text-success cp-success';
            break;
        case 'required':
            iconClass = 'text-warning cp-warning';
            break;
        case 'pending':
            iconClass = 'text-warning cp-pending';
            break;
        default:
            if (displayedValue?.toLowerCase()?.substring(0, 4) === 'asyn') {
                iconClass = 'text-danger cp-refresh';
            } else if (displayedValue?.toLowerCase()?.substring(0, 4) === 'sync') {
                iconClass = 'text-success cp-refresh';
            }
            else if (data === 'PR_Database_createdtime' || data === 'Database_createdtime') {
                if (data !== 'NA') {
                    iconClass = 'text-success cp-time';
                }
            } else if (data === 'Database_name' || data === 'Database_name') {
                if (data != 'NA') {
                    iconClass = 'cp-database me-1 text-primary'
                }
            }
            break;
    }

    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
}
