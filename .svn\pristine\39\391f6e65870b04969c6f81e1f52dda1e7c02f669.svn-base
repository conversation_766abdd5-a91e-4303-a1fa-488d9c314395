﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.LogViewerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.LogViewer.Queries.GetPaginatedList;

public class GetLogViewerPaginatedListQueryHandler : IRequestHandler<GetLogViewerPaginatedListQuery,
    PaginatedResult<LogViewerListVm>>
{
    private readonly IMapper _mapper;
    private readonly ILogViewerRepository _logViewerRepository;

    public GetLogViewerPaginatedListQueryHandler(IMapper mapper,
        ILogViewerRepository logViewerRepository)
    {
        _mapper = mapper;
        _logViewerRepository = logViewerRepository;
    }

    public async Task<PaginatedResult<LogViewerListVm>> Handle(GetLogViewerPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new LogViewerFilterSpecification(request.SearchString);

        var queryable =await _logViewerRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var serverLogList = _mapper.Map<PaginatedResult<LogViewerListVm>>(queryable);

        return serverLogList;
    }
}