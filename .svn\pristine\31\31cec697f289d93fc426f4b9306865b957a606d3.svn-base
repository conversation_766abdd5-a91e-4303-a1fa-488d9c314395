using ContinuityPatrol.Application.Features.WorkflowTemp.Events.Delete;

namespace ContinuityPatrol.Application.Features.WorkflowTemp.Commands.Delete;

public class DeleteWorkflowTempCommandHandler : IRequestHandler<DeleteWorkflowTempCommand, DeleteWorkflowTempResponse>
{
    private readonly IWorkflowTempRepository _workflowTempRepository;
    private readonly IPublisher _publisher;

    public DeleteWorkflowTempCommandHandler(IWorkflowTempRepository workflowTempRepository, IPublisher publisher)
    {
        _workflowTempRepository = workflowTempRepository;

        _publisher = publisher;
    }

    public async Task<DeleteWorkflowTempResponse> Handle(DeleteWorkflowTempCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _workflowTempRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.WorkflowTemp),
            new NotFoundException(nameof(Domain.Entities.WorkflowTemp), request.Id));

        eventToDelete.IsActive = false;

        await _workflowTempRepository.UpdateAsync(eventToDelete);

        var response = new DeleteWorkflowTempResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.WorkflowTemp), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new WorkflowTempDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}
