﻿using ContinuityPatrol.Application.Features.RsyncJob.Commands.Delete;
using ContinuityPatrol.Application.Features.RsyncJob.Events.Delete;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Commands
{
    public class DeleteRsyncJobTests
    {
        private readonly Mock<IRsyncJobRepository> _mockRsyncJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly DeleteRsyncJobCommandHandler _handler;

        public DeleteRsyncJobTests()
        {
            _mockRsyncJobRepository = new Mock<IRsyncJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new DeleteRsyncJobCommandHandler(
                _mockRsyncJobRepository.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_DeletesRsyncJob_Successfully()
        {
            var command = new DeleteRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "TestJob",
                IsActive = true
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(rsyncJob)).Returns(ToString);
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.False(result.IsActive);
            Assert.Contains(rsyncJob.ReplicationName, result.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenJobDoesNotExist()
        {
            var command = new DeleteRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.RsyncJob)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Contains(nameof(Domain.Entities.RsyncJob), exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsInvalidArgumentException_WhenIdIsInvalid()
        {
            var command = new DeleteRsyncJobCommand
            {
                Id = Guid.Empty.ToString(),
            };

            var exception = await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Contains("RsyncJob Id", exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryUpdateFails()
        {
            var command = new DeleteRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "TestJob",
                IsActive = true
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(rsyncJob))
                .Throws(new Exception("Update failed"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Update failed", exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenPublisherFails()
        {
            var command = new DeleteRsyncJobCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = command.Id,
                ReplicationName = "TestJob",
                IsActive = true
            };

            _mockRsyncJobRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.UpdateAsync(rsyncJob)).Returns(ToString);
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()))
                .Throws(new Exception("Publisher failed"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Publisher failed", exception.Message);
            _mockRsyncJobRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.UpdateAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
