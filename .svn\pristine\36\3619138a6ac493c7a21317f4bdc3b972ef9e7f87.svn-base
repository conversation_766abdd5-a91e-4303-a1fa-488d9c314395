const typeURL = {
    NameExist: "Admin/FormType/FormTypeNameExist",
    Pagination: "/Admin/FormType/GetPagination"
};

let btnDisableFormType = false;
let selectedValues = [];
let dataTable = "";

$(function () {
    $('#formTypeName').on('keypress', function (e) {
        if (!this.value && e.key === 'Enter') e.preventDefault();
    });

    $('#search-inp').on('keypress', function (e) {
        if (e.key === '=' || e.key === 'Enter') e.preventDefault();
    });

    dataTable = $('#formTypeTableData').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            retrieve: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": typeURL.Pagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "formTypeName" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        const { data } = json;
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        $(".pagination-column").toggleClass("disabled", !data?.data?.length);
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                },
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta?.row + 1;
                        }
                        return meta?.row + 1;
                    }
                },
                {
                    "data": "formTypeName", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            let editedData = data ? data : 'NA';
                            return `<span title='${editedData}'>${editedData} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "isDelete", "orderable": false, "width": '100px',
                    "render": function (data, type, row) {
                        const disableButtonStyles = data ? '' : 'pointer-events: none; cursor: not-allowed; opacity: 0.5;';
                        return `
                                <div class="d-flex align-items-center gap-2">                              
                                    <span role="button"  title="Edit" id="editButton" style="${disableButtonStyles}" data-form='${JSON.stringify(row)}'>
                                      <i class="cp-edit"></i>
                                    </span>                                      
                                    <span role="button" id="deleteButton" title="Delete" style="${disableButtonStyles}" data-form-id="${row.id}" data-form-name="${row.formTypeName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                      <i class="cp-Delete"></i>
                                    </span>                                                                               
                                </div>`;
                    }
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart; // Get the start index of displayed data
                let counter = startIndex + index + 1; // Calculate the serial number based on start index and index
                $('td:eq(0)', row).html(counter); // Update the cell in the first column with the serial number
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    $('#search-inp').on('keyup input', commonDebounce(async function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const NameCheckbox = $("#Name");
            let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');
            if (sanitizedValue.trim() === "") {
                $(this).val("");
                sanitizedValue = "";
            } else {
                $(this).val(sanitizedValue);
            }
            if (NameCheckbox.is(':checked')) {
                selectedValues.push(NameCheckbox.val() + sanitizedValue);
            }
            dataTable.ajax.reload(function (json) {
                const dataTableEmptyMessage = $('.dataTables_empty');

                if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                    dataTableEmptyMessage.text('No Data Found');
                } else if (json?.recordsFiltered === 0) {
                    dataTableEmptyMessage.text('No matching records found');
                }
            })
        }
    }));

    $('#formTypeName').on('keyup', commonDebounce(async function () {
        const $formTypeName = $("#formTypeName");
        const sanitizedValue = $formTypeName.val().replace(/\s{2,}/g, ' ');
        $formTypeName.val(sanitizedValue);
        await formTypeNameValidation(sanitizedValue, $('#formTypeID').val(), typeURL.NameExist);
    }));

    $("#SaveFunction").on("click", async function () {
        const formTypeValidation = await formTypeNameValidation($("#formTypeName").val(), $('#formTypeID').val(), typeURL.NameExist);
        if (!btnDisableFormType) {
            if (formTypeValidation) {
                btnDisableFormType = true;
                //$("#CreateForm").trigger('submit');

                const form = $('#CreateForm')[0];
                const formData = new FormData(form);

                async function saveFormType() {
                    let response = await $.ajax({
                        type: "POST",
                        url: RootUrl + "Admin/FormType/CreateOrUpdate",
                        headers: {
                            'RequestVerificationToken': await gettoken()
                        },
                        data: formData,
                        contentType: false,
                        processData: false,
                    });

                    if (response?.success) {
                        $('#CreateModal').modal('hide');
                        btnDisableFormType = false;
                        notificationAlert("success", response?.data?.message);
                        setTimeout(() => {
                            //dataTable.ajax.reload();
                            dataTableCreateAndUpdate($("#SaveFunction"), dataTable);
                        }, 2000)
                    } else {
                        $('#CreateModal').modal('hide');
                        errorNotification(response);
                        btnDisableFormType = false;
                    }
                }

                await saveFormType();
            }
        }
    });

    $("#createModalButton").on("click", function () {
        clearFormTypeInputAndErrorMessage();
        $('#SaveFunction').text('Save');
        $('#formTypeID').val('');
    });

    //delete
    $('#formTypeTableData').on('click', '#deleteButton', function () {
        $("#deleteData").text($(this).data("form-name")).attr("title", $(this).data('form-name'));
        $("#textDeleteId").val($(this).data("form-id"));
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#deleteFormType')[0];
        const formData = new FormData(form);

        if (!btnDisableFormType) {
            btnDisableFormType = true;
            let response = await $.ajax({
                type: "POST",
                url: RootUrl + "Admin/FormType/Delete",
                headers: {
                    'RequestVerificationToken': await gettoken()
                },
                data: formData,
                contentType: false,
                processData: false,
            });

            if (response?.success) {
                $("#DeleteModal").modal("hide");
                notificationAlert("success", response?.data?.message);
                btnDisableFormType = false;
                setTimeout(() => {
                    //dataTable.ajax.reload();
                    dataTableDelete(dataTable);
                }, 2000)
            } else {
                $("#DeleteModal").modal("hide");
                errorNotification(response);
                btnDisableFormType = false;
            }
        }
    });

    //Update
    $('#formTypeTableData').on('click', '#editButton', function () {
        clearFormTypeInputAndErrorMessage();
        populateModalFields($(this).data("form"));
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
    });
});

//functions

async function FormTypeIsNameExist(url, data, errorFunc) {
    return !data.formTypeName.trim() ? true : (await FormTypeGetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function FormTypeGetAsync(url, data, errorFunc) {
    return await $.get(url, data)
        .fail(errorFunc);
}

async function formTypeNameValidation(value, id = null, formTypeNameExistURL) {
    let errorElement = $("#FormTypeName-error");
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (!value) {
        errorElement.text("Enter form type name").addClass('field-validation-error');
        return false;
    }

    const url = RootUrl + formTypeNameExistURL;
    const data = {
        id: id,
        formTypeName: value
    };
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await ShouldNotAllowMultipleSpace(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await FormTypeIsNameExist(url, data, OnError),
    ];
    return await CommonValidation(errorElement, validationResults);
}

function clearFormTypeInputAndErrorMessage() {
    clearInputFields('CreateForm', ['#FormTypeName-error']);
}

function populateModalFields(formTypeData) {
    $('#formTypeID').val(formTypeData.id);
    $('#formTypeName').val(formTypeData.formTypeName);
}