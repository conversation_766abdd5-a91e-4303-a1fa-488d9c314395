using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RoboCopyMonitorStatusFixture : IDisposable
{
    public List<RoboCopyMonitorStatus> RoboCopyMonitorStatusPaginationList { get; set; }
    public List<RoboCopyMonitorStatus> RoboCopyMonitorStatusList { get; set; }
    public RoboCopyMonitorStatus RoboCopyMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string WorkflowId = "WORKFLOW_123";
    public const string Type = "RoboCopy";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RoboCopyMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RoboCopyMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RoboCopyMonitorStatusList = _fixture.CreateMany<RoboCopyMonitorStatus>(5).ToList();
        RoboCopyMonitorStatusPaginationList = _fixture.CreateMany<RoboCopyMonitorStatus>(20).ToList();
        RoboCopyMonitorStatusDto = _fixture.Create<RoboCopyMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RoboCopyMonitorStatus CreateRoboCopyMonitorStatusWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RoboCopyMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, Type)
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.InfraObjectName, _fixture.Create<string>())
            .With(x => x.WorkflowId, WorkflowId)
            .With(x => x.WorkflowName, _fixture.Create<string>())
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, _fixture.Create<string>())
            .With(x => x.DataLagValue, _fixture.Create<string>())
            .With(x => x.Threshold, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
