using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FastCopyMonitorFixture : IDisposable
{
    public List<FastCopyMonitor> FastCopyMonitorPaginationList { get; set; }
    public List<FastCopyMonitor> FastCopyMonitorList { get; set; }
    public FastCopyMonitor FastCopyMonitorDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public FastCopyMonitorFixture()
    {
        var fixture = new Fixture();

        FastCopyMonitorList = fixture.Create<List<FastCopyMonitor>>();

        FastCopyMonitorPaginationList = fixture.CreateMany<FastCopyMonitor>(20).ToList();

        FastCopyMonitorDto = fixture.Create<FastCopyMonitor>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
