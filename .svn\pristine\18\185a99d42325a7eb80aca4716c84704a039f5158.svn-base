﻿using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

namespace ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;

public class GetBulkImportReportVm
{
    public string ReportGeneratedBy { get; set; }
    public string Date { get; set; }
    public int SuccessInfra { get; set; }
    public int RunningInfra { get; set; }
    public int FailureInfra { get; set; }
    public int TotalInfra { get; set; }
    public List<BulkImportOperationGroupList> BulkImportOperationGroupListVms { get; set; } = new();
}