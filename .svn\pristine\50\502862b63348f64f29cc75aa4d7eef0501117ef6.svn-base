﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowInfraObject.Events;

public class WorkflowInfraObjectDeletedEventHandlerTests : IClassFixture<WorkflowInfraObjectFixture>
{
    private readonly WorkflowInfraObjectFixture _workflowInfraObjectFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowInfraObjectDeletedEventHandler _handler;

    public WorkflowInfraObjectDeletedEventHandlerTests(WorkflowInfraObjectFixture workflowInfraObjectFixture)
    {
        _workflowInfraObjectFixture = workflowInfraObjectFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowInfraObjectEventLogger = new Mock<ILogger<WorkflowInfraObjectDeletedEventHandler>>();

        _mockUserActivityRepository = WorkflowInfraObjectRepositoryMocks.CreateWorkflowInfraObjectEventRepository(_workflowInfraObjectFixture.UserActivities);

        _handler = new WorkflowInfraObjectDeletedEventHandler(mockLoggedInUserService.Object, mockWorkflowInfraObjectEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteWorkflowInfraObjectEventDeleted()
    {
        _workflowInfraObjectFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowInfraObjectFixture.WorkflowInfraObjectDeletedEvent, CancellationToken.None);

        result.Equals(_workflowInfraObjectFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowInfraObjectFixture.WorkflowInfraObjectDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_DeleteWorkflowInfraObjectEventDeleted()
    {
        _workflowInfraObjectFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowInfraObjectFixture.WorkflowInfraObjectDeletedEvent, CancellationToken.None);

        result.Equals(_workflowInfraObjectFixture.UserActivities[0].Id);

        result.Equals(_workflowInfraObjectFixture.WorkflowInfraObjectDeletedEvent.WorkflowName);

        await Task.CompletedTask;
    }
}
