﻿@model ContinuityPatrol.Domain.ViewModels.CyberAirGapModel.CyberAirGapViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title">
                <i class="cp-air-gap"></i><span>
                    Airgap List
                </span>
            </h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="Name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="airGapCreateButton" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
                @* <button type="button" class="btn btn-primary btn-sm " data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button> *@
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover dataTable no-footer align-middle" id="airGap_table" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>
                            <div class="d-flex align-items-center gap-1">
                                @* <input class="form-check-input m-0" type="checkbox" value="" id="flexCheckDefault"> *@
                                <span for="flexCheckDefault">
                                    Airgap Name
                                </span>

                            </div>

                        </th>
                        @*  <th>Description</th> *@
                        <th>Port</th> 
                        <th>Status</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

</div>

<!-- Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">

    <div class="modal-dialog modal-dialog-scrollabel modal-lg">
        <form class="modal-content" id="CreateForm" asp-area="CyberResiliency" asp-controller="AirGap" asp-action="CreateOrUpdate" method="post">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-air-gap"></i><span>Airgap Configuration</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="min-height:500px">

                <div class="form-group">
                    <label class="form-label">
                        Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input asp-for="Name" id="airGapName" type="text" class="form-control form-clear" placeholder="Enter Airgap Name" maxlength="100" autocomplete="off">
                    </div>
                    <span id="airGapName-error"></span>
                </div>

                <div class="form-group">
                    <label class="form-label">Description <small class="text-secondary">( Optional )</small></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-description"></i></span>
                        <input asp-for="Description" id="Description" type="text" class="form-control" maxlength="250" placeholder="Enter Description" />
                    </div>
                    <span id="Description-error"></span>
                </div>
                <div class="form-group">
                    <label class="form-label">Port or Channel</label>
                    <div class="d-flex flex-row">
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-port"></i></span>
                            <input id="airGapPort" type="text" value="1" class="form-control" maxlength="6" placeholder="Enter Port or Channel" />
                        </div>
                        <div class="ms-2 mt-1" id="btnAddPort" style="display:none;"><i class="cp-circle-plus text-primary fs-5 mb-0 "></i></div>
                    </div>
                    <span id="Port-error"></span>
                </div>
                <div class="ms-2 mb-2 fs-7" id="portContainer">
                </div>
                <input asp-for="Port" id="Port" type="text" hidden />
                <div class="form-group">
                    <label class="form-label">
                        Source Zone
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-component-list"></i></span>
                        <select asp-for="SourceComponentId" class="form-select-modal form-clear" id="sourceComponent" aria-label="Default select example" data-live-search="true" data-placeholder="Select Source Zone">
                        </select>
                    </div>
                    <input asp-for="SourceComponentName" id="sourceComponentName" type="hidden" />
                    <span id="sourceComponent-error"></span>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        Source Host
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server"></i></span>
                        <select class="form-select-modal form-clear" id="sourceServer" aria-label="Default select example" multiple
                                data-live-search="true" data-placeholder="Select Source Host">
                        </select>
                    </div>
                    <span id="sourceServer-error"></span>
                    <input asp-for="Source" type="hidden" id="sourceServer_details" />
                </div>

                <div class="form-group">
                    <label class="form-label">
                        Target Zone
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-component-list"></i></span>
                        <select asp-for="TargetComponentId" class="form-select-modal form-clear" id="targetComponent" aria-label="Default select example" data-live-search="true" data-placeholder="Select Target Zone">
                        </select>
                    </div>
                    <input asp-for="TargetComponentName" id="targetComponentName" type="hidden" />
                    <span id="targetComponent-error"></span>
                </div>

                <div class="form-group d-none">
                    <label class="form-label">
                        Target Host
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server"></i></span>
                        <select class="form-select-modal form-clear" id="targetServer" aria-label="Default select example" multiple
                                data-live-search="true" data-placeholder="Select Target Host">
                        </select>
                    </div>
                    <span id="targetServer-error"></span>
                    <input asp-for="Target" type="hidden" id="targetServer_details" />

                </div>

                <div class="form-group d-none">
                    <label class="form-label">
                        Enable Workflow
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-workflow-configuration"></i></span>
                        <select asp-for="EnableWorkflowId" class="form-select-modal form-clear" id="enable-workflow" aria-label="Default select example" data-live-search="true" data-placeholder="Select Enable Workflow">
                        </select>
                    </div>
                    <span id="enable-workflow-error"></span>
                </div>

                <div class="form-group d-none">
                    <label class="form-label">
                        Disable Workflow
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-workflow-configuration"></i></span>
                        <select asp-for="DisableWorkflowId" class="form-select-modal form-clear" id="disable-workflow" aria-label="Default select example" data-live-search="true" data-placeholder="Select Disable Workflow">
                        </select>
                    </div>
                    <span id="disable-workflow-error"></span>
                </div>

                <input asp-for="Id" id="airGapId" type="hidden" />
                <input asp-for="Status" id="status" value="Disable" type="hidden" />
                <input asp-for="StartTime" id="start_time" value="Disable" type="hidden" />
                <input asp-for="EndTime" id="end_time" value="Disable" type="hidden" />
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                    <button type="button" id="btnSave" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </form>


    </div>
</div>

<div id="airGapCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Cyber.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Delete-->
<div id="airGapDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Cyber.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>


<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>

<script src="~/js/CyberResiliency/Configuration/Airgap/AirGap.js"></script>