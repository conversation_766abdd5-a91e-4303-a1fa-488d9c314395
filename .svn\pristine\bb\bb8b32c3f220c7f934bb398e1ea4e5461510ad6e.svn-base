using ContinuityPatrol.Application.Features.FiaCost.Commands.Create;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Update;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IFiaCostService
{
    Task<List<FiaCostListVm>> GetFiaCostList();
    Task<BaseResponse> CreateAsync(CreateFiaCostCommand createFiaCostCommand);
    Task<BaseResponse> UpdateAsync(UpdateFiaCostCommand updateFiaCostCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<FiaCostDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsFiaCostNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<FiaCostListVm>> GetPaginatedFiaCosts(GetFiaCostPaginatedListQuery query);
    #endregion
}
