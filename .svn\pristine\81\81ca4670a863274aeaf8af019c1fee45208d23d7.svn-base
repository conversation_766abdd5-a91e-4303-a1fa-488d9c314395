﻿using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.RiskMitigation.Queries;

public class GetRiskMitigationPaginatedListQueryHandlerTests : IClassFixture<RiskMitigationFixture>
{
    private readonly Mock<IRiskMitigationRepository> _mockRiskMitigationRepository;

    private readonly GetRiskMitigationPaginatedListQueryHandler _handler;

    public GetRiskMitigationPaginatedListQueryHandlerTests(RiskMitigationFixture riskMitigationFixture)
    {
        var riskMitigationNewFixture = riskMitigationFixture;

        _mockRiskMitigationRepository = RiskMitigationRepositoryMocks.GetPaginatedRiskMitigationRepository(riskMitigationNewFixture.RiskMitigations);

        _handler = new GetRiskMitigationPaginatedListQueryHandler(riskMitigationNewFixture.Mapper, _mockRiskMitigationRepository.Object);

        riskMitigationNewFixture.RiskMitigations[0].BusinessServiceName = "Business_Service";
        riskMitigationNewFixture.RiskMitigations[0].InfraObjectName = "Infra_Test";



        riskMitigationNewFixture.RiskMitigations[1].BusinessServiceName = "Service_Function";
        riskMitigationNewFixture.RiskMitigations[1].InfraObjectName = "Infra_Reference";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetRiskMitigationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<RiskMitigationListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedRiskMitigations_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetRiskMitigationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Infra" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<RiskMitigationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].ShouldBeOfType<RiskMitigationListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("Infra_Test");

        result.Data[0].BusinessServiceName.ShouldBe("Business_Service");

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetRiskMitigationPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<RiskMitigationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_RiskMitigations_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetRiskMitigationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "infraObjectName=Infra_Test;workflowName=Work_Infra;businessservice=Business_Service;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<RiskMitigationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("Infra_Test");

        result.Data[0].BusinessServiceName.ShouldBe("Business_Service");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetRiskMitigationPaginatedListQuery(), CancellationToken.None);

        _mockRiskMitigationRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}