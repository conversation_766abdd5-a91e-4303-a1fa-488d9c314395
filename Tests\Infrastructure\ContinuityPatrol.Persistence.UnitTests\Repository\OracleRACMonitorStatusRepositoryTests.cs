using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class OracleRACMonitorStatusRepositoryTests : IClassFixture<OracleRACMonitorStatusFixture>
{
    private readonly OracleRACMonitorStatusFixture _oracleRACMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly OracleRACMonitorStatusRepository _repository;

    public OracleRACMonitorStatusRepositoryTests(OracleRACMonitorStatusFixture oracleRACMonitorStatusFixture)
    {
        _oracleRACMonitorStatusFixture = oracleRACMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new OracleRACMonitorStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.OracleRacStatuses.RemoveRange(_dbContext.OracleRacStatuses);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }



    #region GetOracleRacStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldReturnStatus_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status = _oracleRACMonitorStatusFixture.CreateOracleRACMonitorStatusWithProperties(infraObjectId: infraObjectId);
        
        await _dbContext.OracleRacStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetOracleRacStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentInfraObjectId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetOracleRacStatusByInfraObjectIdAsync(nonExistentInfraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => 
            _repository.GetOracleRacStatusByInfraObjectIdAsync("invalid-guid"));
    }

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => 
            _repository.GetOracleRacStatusByInfraObjectIdAsync(string.Empty));
    }

  

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status1 = _oracleRACMonitorStatusFixture.CreateOracleRACMonitorStatusWithProperties(infraObjectId: infraObjectId);
        var status2 = _oracleRACMonitorStatusFixture.CreateOracleRACMonitorStatusWithProperties(infraObjectId: infraObjectId);
        
        await _dbContext.OracleRacStatuses.AddRangeAsync(new[] { status1, status2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetOracleRacStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return one of the matching records
        Assert.True(result.ReferenceId == status1.ReferenceId || result.ReferenceId == status2.ReferenceId);
    }

    [Fact]
    public async Task GetOracleRacStatusByInfraObjectIdAsync_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status = _oracleRACMonitorStatusFixture.CreateOracleRACMonitorStatusWithProperties(infraObjectId: infraObjectId);
        status.InfraObjectName = "<EMAIL>";
        
        await _dbContext.OracleRacStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetOracleRacStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("<EMAIL>", result.InfraObjectName);
    }

    #endregion


}
