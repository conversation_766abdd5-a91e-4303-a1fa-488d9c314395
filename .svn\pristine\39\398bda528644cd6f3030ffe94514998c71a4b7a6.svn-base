﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using Microsoft.AspNetCore.Http;

namespace ContinuityPatrol.Services.Api.Impl.Manage
{
    public  class ApprovalMatrixService :  BaseService , IApprovalMatrixService
    {

        public ApprovalMatrixService(IHttpContextAccessor accessor) : base(accessor)
        {
        }

        public Task<string> ApproveApprovalRequest(string id, string status, string userId)
        {
            throw new NotImplementedException();
        }

        public Task<BaseResponse> DeleteAsync(string id)
        {
            throw new NotImplementedException();
        }

        public Task<List<ApprovalMatrixListVm>> GetApprovalMatrixList()
        {
            throw new NotImplementedException();
        }

        public Task<List<ApprovalMatrixListVm>> GetApprovalMatrixListByUser(string loggedInUserId)
        {
            throw new NotImplementedException();
        }

        public Task<PaginatedResult<ApprovalMatrixListVm>> GetPaginatedApprovalMatrices(GetApprovalMatrixPaginatedListQuery query)
        {
            throw new NotImplementedException();
        }

        public Task<PaginatedResult<ApprovalMatrixListVm>> GetPaginatedApprovalMatricesByUserId(string loggedInUserId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsMatrixNameExist(string matrixName, string id)
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsTemplateAttached(string templateName)
        {
            throw new NotImplementedException();
        }

        public Task<string> WithdrawApprovalMatrix(string matrixId)
        {
            throw new NotImplementedException();
        }

        Task<BaseResponse> IApprovalMatrixService.CreateAsync(CreateApprovalMatrixCommand company)
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<ApprovalMatrixListVm>> IApprovalMatrixService.GetApprovalMatrixListByUser(string loggedInUserId)
        {
            throw new NotImplementedException();
        }

        Task<BaseResponse> IApprovalMatrixService.UpdateAsync(UpdateApprovalMatrixCommand company)
        {
            throw new NotImplementedException();
        }
    }
}
