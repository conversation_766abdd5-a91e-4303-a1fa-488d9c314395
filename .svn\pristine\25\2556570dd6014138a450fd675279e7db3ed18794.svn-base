﻿using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;

using static ContinuityPatrol.Web.Areas.Report.ReportTemplate.LicenseUtlizationReport;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class LicenseUtlizationXlsReport : DevExpress.XtraReports.UI.XtraReport
    {
        public List<LicenseReportDetail> License { get; set; }
        public List<DatabaseReportVm> Database { get; set; }
        public List<ReplicationReportVm> Replication { get; set; }
        public List<ApplicationReportVm> Application { get; set; }
        public List<AvailableCountViewVm> Available { get; set; }
        public List<UsedCountViewVm> Used { get; set; }
        public  LicenseReport LicenseReports = new LicenseReport();
        private readonly ILogger<PreBuildReportController> _logger;

        public static Int64 totals;
        public static Int64 usedcounts;
        public static Int64 remaincounts;

        public class CombinedCount
        {
            public int DatabaseCount { get; set; }
            public int ServerDbCount { get; set; }
            public int ReplicationCount { get; set; }
            public int StorageCount { get; set; }
            public int VirtualizationCount { get; set; }
            public int ApplicationCount { get; set; }
            public int DnsCount { get; set; }
            public int NetworkCount { get; set; }
            public int ThirdPartyCount { get; set; }
        }

        public LicenseUtlizationXlsReport(string data)
        {
            try
            {
                LicenseReports = JsonConvert.DeserializeObject<LicenseReport>(data);
                _logger = PreBuildReportController._logger;
                InitializeComponent();
                ClientCompanyLogo();
                this.DisplayName = "LicenseUtilization_PONumber_Report_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                string GetValueOrNA(string values)
                {
                    if (string.IsNullOrEmpty(values)) return "-";
                    return string.Join(", ", values.Split(',')
                                                    .Select(value => string.IsNullOrEmpty(value.Trim()) || value.Trim() == "NA" ? "-" : value.Trim()));
                }
                var Licencereport = LicenseReports.LicenseReportVms.Where(x => x.LicenseReportDetail.Any(y => y.PONumber != null)).ToList();
                var childPOID = LicenseReports.ChildLicensePOIds.ToString();
                var report = Licencereport.SelectMany(x => x.LicenseReportDetail).ToList();
                foreach (var values in report)
                {
                    var decrypt = SecurityHelper.Decrypt(values.PONumber);
                    values.PONumber = decrypt != "" ? decrypt : values.PONumber;
                }
                HashSet<string> ServerpoName = new HashSet<string>();
                HashSet<string> childServerpoName = new HashSet<string>();
                var imagePaths = new Dictionary<string, string>
                        {
                            {"mysql", "wwwroot/img/Drdrill_Icons/MySql.png"},
                            {"postgres", "wwwroot/img/Drdrill_Icons/postgres.png"},
                            {"mac", "wwwroot/img/Drdrill_Icons/mac_os.png"},
                            {"openshift", "wwwroot/img/License_Report/Linux-Openshift.png"},
                            {"linux", "wwwroot/img/Drdrill_Icons/linux.png"},
                            {"oraclerac", "wwwroot/img/Drdrill_Icons/oracle_rac.png"},
                            {"oracle", "wwwroot/img/Drdrill_Icons/oracle.png"},
                            {"sqlite", "wwwroot/img/Drdrill_Icons/sqlite.png"},
                            {"windows 2016", "wwwroot/img/Drdrill_Icons/Windows 2017.png"},
                            {"windows 2019", "wwwroot/img/Drdrill_Icons/windows-2019.png"},
                            {"windows", "wwwroot/img/Drdrill_Icons/windows-2019.png"},
                            {"solaris", "wwwroot/img/Drdrill_Icons/solaris.png"},
                            {"aix", "wwwroot/img/License_Report/AIX.png"},
                            {"azurestorageserver", "wwwroot/img/License_Report/azure.png"},
                            {"cloudazure", "wwwroot/img/License_Report/CloudAzure.png"},
                            {"azure", "wwwroot/img/License_Report/AzureDatabase_MSSQL_PaaS.png"},
                            {"as400", "wwwroot/img/License_Report/as400.png"},
                            {"redisdb", "wwwroot/img/License_Report/RedisDB.png"},
                            {"maxdb", "wwwroot/img/License_Report/MaxDB.png"},
                            {"mongodb", "wwwroot/img/License_Report/mongo-db.png"},
                            {"ibm", "wwwroot/img/License_Report/IBM-DB2.png"},
                            {"sql", "wwwroot/img/Drdrill_Icons/mssql.png"}
                        };
                var Default = "wwwroot/img/Drdrill_Icons/abot.png";
                int j = 0, k = 0, l = 0, m = 0, rowIndex = 0;

                xrTable2.BeforePrint += (sender, e) =>
                {
                    if (report.Count != 0)
                    {
                        if (ServerpoName.Contains(report[j].PONumber)) { return; }
                        else { ServerpoName.Add(report[j].PONumber); }

                        var table = (XRTable)sender; table.Rows.Clear();

                        var server2 = report[j].ApplicationReportVms;
                        if (server2.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Application License " + " (" + server2.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Application_Lic_Black.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel appName = new XRLabel(); appName.Text = "  Server Name"; appName.WidthF = 229.36F; appName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { appName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var serverName in server2)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var serverdb = report[j].DatabaseReportVms;
                        if (serverdb.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Database License " + " (" + serverdb.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/database.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            XRLabel database = new XRLabel(); database.Text = "  Database Name"; database.WidthF = 229.36F; database.HeightF = 32F;
                            var serverHead1 = (XRTable)sender;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { database }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Database Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Database SID", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var serverName in serverdb)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.Type) ?? "-", WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.EntityField.Split(",").ElementAtOrDefault(2)) ?? "-", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(serverName.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var server3 = report[j].ReplicationReportVms;
                        if (server3.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Replication License " + " (" + server3.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/replication_Lic_Black.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            XRLabel repName = new XRLabel(); repName.Text = "  Replication Name"; repName.WidthF = 229.36F; repName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { repName }, WidthF = 231.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Replication Type", WidthF = 288.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Site Name", WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });

                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var serverName in server3)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => serverName.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                                   new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                   new XRTableCell { Text = GetValueOrNA(serverName.EntityName), WidthF = 222.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                   new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                   new XRTableCell { Text = GetValueOrNA(serverName.Type), WidthF = 268.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                   new XRTableCell { Text = GetValueOrNA(serverName.Category), WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                   new XRTableCell { Text = GetValueOrNA(serverName.BusinessServiceName), WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                    // new XRTableCell { Text = serverName.Category ?? "-", WidthF = 100.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                   });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var storage = report[j].StorageReportVms;
                        if (storage.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Storage License " + " (" + storage.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/Storage.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel storageName = new XRLabel(); storageName.Text = "  Server Name"; storageName.WidthF = 229.36F; storageName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { storageName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var storageServer in storage)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => storageServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(storageServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(storageServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(storageServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(storageServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(storageServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var network = report[j].NetworkReportVms;
                        if (network.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Network License " + " (" + network.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/network.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel networkName = new XRLabel(); networkName.Text = "  Server Name"; networkName.WidthF = 229.36F; networkName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { networkName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var networkServer in network)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => networkServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(networkServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(networkServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(networkServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(networkServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(networkServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var thirdParty = report[j].ThirdPartyReportVms;
                        if (thirdParty.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Third Party License " + " (" + thirdParty.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Thirdparty_Lic_Black.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel ThirdPartyName = new XRLabel(); ThirdPartyName.Text = "  Server Name"; ThirdPartyName.WidthF = 229.36F; ThirdPartyName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { ThirdPartyName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 166.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var thirdPartyServer in thirdParty)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => thirdPartyServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(thirdPartyServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(thirdPartyServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(thirdPartyServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(thirdPartyServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(thirdPartyServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var vmServer = report[j].VirtualizationReportVms;
                        if (vmServer.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "Virtualization License " + " (" + vmServer.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/VM_Lic_Black.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel VMName = new XRLabel(); VMName.Text = "  Server Name"; VMName.WidthF = 229.36F; VMName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { VMName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var vmServerValue in vmServer)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => vmServerValue.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(vmServerValue.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(vmServerValue.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(vmServerValue.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(vmServerValue.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(vmServerValue.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        var dns = report[j].DNSReportVms;
                        if (dns.Count > 0)
                        {
                            //Add Label
                            XRTableRow headerRow = new XRTableRow();
                            XRLabel label = new XRLabel();
                            label.Text = "DNS License " + " (" + dns.Count + ")";
                            label.WidthF = 300F;
                            label.HeightF = 50F;
                            label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                            // Add picture box
                            XRPictureBox pictureBox1 = new XRPictureBox();
                            pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/dns.png");
                            pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                            pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                            //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                            headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                            table.Rows.Add(headerRow);


                            XRTableRow serverHeader = new XRTableRow();
                            serverHeader.HeightF = 32F;
                            var serverHead2 = (XRTable)sender;
                            XRLabel DNSName = new XRLabel(); DNSName.Text = "  Server Name"; DNSName.WidthF = 229.36F; DNSName.HeightF = 32F;
                            serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                            serverHeader.Cells.Add(new XRTableCell { Controls = { DNSName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                            table.Rows.Add(serverHeader);

                            rowIndex = 0;

                            foreach (var dnsServer in dns)
                            {
                                string imagePath = imagePaths.FirstOrDefault(pair => dnsServer.Type?.ToLower().Contains(pair.Key) ?? false).Value ?? Default;

                                XRTableRow dataRow = new XRTableRow();
                                XRPictureBox pictureBox = new XRPictureBox();
                                pictureBox.Image = Image.FromFile(imagePath);
                                pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                dataRow.Cells.AddRange(new[]
                                {
                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(dnsServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(dnsServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(dnsServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(dnsServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                            new XRTableCell { Text = GetValueOrNA(dnsServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                            });
                                bool isOddRow = rowIndex % 2 != 0;
                                dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                table.Rows.Add(dataRow);
                                rowIndex++;
                            }
                        }
                        j++;
                        return;
                    }
                };

                xrTable1.BeforePrint += (sender, e) =>
                {
                    if (report.Count != 0)
                    {
                        int a = 0, b = j - 1;

                        if (report[b].ChildLicenseReportVms.Count > 0)
                        {
                            var table2 = (XRTable)sender; table2.Rows.Clear();

                            foreach (var po in report[b].ChildLicenseReportVms)
                            {
                                var decrypt = SecurityHelper.Decrypt(po.PONumber);
                                po.PONumber = decrypt != "" ? decrypt : po.PONumber;

                                var child = report[b].ChildLicenseReportVms[a];
                                if ((child.PONumber != null) && (child.DatabaseReportVms.Count != 0 || child.ApplicationReportVms.Count != 0 || child.ReplicationReportVms.Count != 0))
                                {
                                    var table = (XRTable)sender; table.Rows.Clear();
                                    if ((!childPOID.Contains(child.LicenseId) && childPOID != "All") || childServerpoName.Equals(child.PONumber)) { return; }
                                    else { childServerpoName.Add(child.PONumber); }

                                    XRTableRow headerRow0 = new XRTableRow();
                                    var table0 = (XRTable)sender;
                                    headerRow0.SizeF = new System.Drawing.SizeF(821F, 32F);
                                    headerRow0.BackColor = ColorTranslator.FromHtml("#edf5ff");
                                    headerRow0.Cells.Add(new XRTableCell { Text = " Derived_" + report[b].PONumber + " (" + child.PONumber + ")", WidthF = 251.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 10, 10), TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold), ForeColor = ColorTranslator.FromHtml("#0479FF") });
                                    table.Rows.Add(headerRow0);

                                    if (child.ApplicationReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Application License " + " (" + child.ApplicationReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Application_Lic_Black.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel appName = new XRLabel(); appName.Text = "  Server Name"; appName.WidthF = 229.36F; appName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { appName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var Appserver in child.ApplicationReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && Appserver.Type != null && Appserver.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(Appserver.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(Appserver.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(Appserver.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(Appserver.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(Appserver.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                    if (child.DatabaseReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Database License " + " (" + child.DatabaseReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/database.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        XRLabel database = new XRLabel(); database.Text = "  Database Name"; database.WidthF = 229.36F; database.HeightF = 32F;
                                        var serverHead1 = (XRTable)sender;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { database }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Database Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Database SID", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);

                                        rowIndex = 0;

                                        foreach (var dbserver in child.DatabaseReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && dbserver.Type != null && dbserver.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(dbserver.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(dbserver.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(dbserver.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(dbserver.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(dbserver.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                            });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;

                                        }

                                    }

                                    if (child.ReplicationReportVms.Count > 0)
                                    {

                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Replication License " + " (" + child.ReplicationReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/replication_Lic_Black.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        XRLabel repName = new XRLabel(); repName.Text = "  Replication Name"; repName.WidthF = 229.36F; repName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { repName }, WidthF = 231.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Replication Type", WidthF = 288.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Site Name", WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });

                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;
                                        foreach (var Repserver in child.ReplicationReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && Repserver.Type != null && Repserver.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                            new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(Repserver.EntityName), WidthF = 222.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Controls = { pictureBox }, WidthF = 40F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(Repserver.Type), WidthF = 258.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(Repserver.EntityField), WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            new XRTableCell { Text = GetValueOrNA(Repserver.BusinessServiceName), WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                            });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                        XRTableRow dataRow1 = new XRTableRow();
                                        XRLabel repName2 = new XRLabel(); repName2.Text = "   "; repName2.WidthF = 229.36F; repName2.HeightF = 32F;
                                        dataRow1.Cells.AddRange(new[]
                                        {
                                        new XRTableCell { Controls = {repName2}, WidthF = 231.15F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 40F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text =  "", WidthF = 258.61F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text =  "", WidthF = 231.32F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text =  "", WidthF = 253.08F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        });
                                        table.Rows.Add(dataRow1);

                                    }
                                    if (child.StorageReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Storage License " + " (" + child.StorageReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/Storage.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel storeName = new XRLabel(); storeName.Text = "  Server Name"; storeName.WidthF = 229.36F; storeName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { storeName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var storageServer in child.StorageReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && storageServer.Type != null && storageServer.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(storageServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(storageServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(storageServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(storageServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(storageServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                    if (child.NetworkReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Network License " + " (" + child.NetworkReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/network.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel networkName = new XRLabel(); networkName.Text = "  Server Name"; networkName.WidthF = 229.36F; networkName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { networkName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var networkServer in child.NetworkReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && networkServer.Type != null && networkServer.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell {Text = GetValueOrNA(networkServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(networkServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(networkServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(networkServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(networkServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                    if (child.ThirdPartyReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Third Party License " + " (" + child.ThirdPartyReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/Thirdparty_Lic_Black.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel ThirdPartyName = new XRLabel(); ThirdPartyName.Text = "  Server Name"; ThirdPartyName.WidthF = 229.36F; ThirdPartyName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { ThirdPartyName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var ThirdPartyServer in child.ThirdPartyReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && ThirdPartyServer.Type != null && ThirdPartyServer.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }


                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(ThirdPartyServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(ThirdPartyServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(ThirdPartyServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(ThirdPartyServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(ThirdPartyServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                    if (child.VirtualizationReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "Virtualization License " + " (" + child.VirtualizationReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/License_Report/VM_Lic_Black.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel VMName = new XRLabel(); VMName.Text = "  Server Name"; VMName.WidthF = 229.36F; VMName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { VMName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var vmServer in child.VirtualizationReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && vmServer.Type != null && vmServer.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }

                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(vmServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(vmServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(vmServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(vmServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(vmServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                    if (child.DNSReportVms.Count > 0)
                                    {
                                        //Add Label
                                        XRTableRow headerRow = new XRTableRow();
                                        XRLabel label = new XRLabel();
                                        label.Text = "DNS License " + " (" + child.DNSReportVms.Count + ")";
                                        label.WidthF = 300F;
                                        label.HeightF = 50F;
                                        label.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);

                                        // Add picture box
                                        XRPictureBox pictureBox1 = new XRPictureBox();
                                        pictureBox1.Image = Image.FromFile("wwwroot/img/Drdrill_Icons/dns.png");
                                        pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                        pictureBox1.SizeF = new System.Drawing.SizeF(18F, 50F);

                                        //  headerRow.Cells.AddRange(new XRTableCell[] { headerCell1, headerCell });
                                        headerRow.Cells.AddRange(new[] { new XRTableCell { Controls = { pictureBox1 }, WidthF = 3F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) }, new XRTableCell { Controls = { label }, TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0) } });
                                        table.Rows.Add(headerRow);


                                        XRTableRow serverHeader = new XRTableRow();
                                        serverHeader.HeightF = 32F;
                                        var serverHead2 = (XRTable)sender;
                                        XRLabel DNSName = new XRLabel(); DNSName.Text = "  Server Name"; DNSName.WidthF = 229.36F; DNSName.HeightF = 32F;
                                        serverHeader.BackColor = ColorTranslator.FromHtml("#EDF5FF");
                                        serverHeader.Cells.Add(new XRTableCell { Controls = { DNSName }, WidthF = 229.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "OS Type", WidthF = 168.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Category", WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "IP Address/HostName", WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        serverHeader.Cells.Add(new XRTableCell { Text = "Operational Service Name", WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 4, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold) });
                                        table.Rows.Add(serverHeader);
                                        rowIndex = 0;

                                        foreach (var dnsServer in child.DNSReportVms)
                                        {
                                            var serverTable = (XRTable)sender;
                                            string imagePath = imagePaths.FirstOrDefault(pair =>
                                            pair.Key != null && dnsServer.Type != null && dnsServer.Type.ToLower().Contains(pair.Key)).Value;

                                            if (imagePath == null)
                                            {
                                                imagePath = Default;
                                            }

                                            XRTableRow dataRow = new XRTableRow();
                                            dataRow.HeightF = 42;
                                            var appTab = (XRTable)sender;
                                            XRPictureBox pictureBox = new XRPictureBox();
                                            pictureBox.Image = Image.FromFile(imagePath);
                                            pictureBox.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
                                            //  pictureBox.LocationF = new PointF(-10, 0);
                                            pictureBox.SizeF = new System.Drawing.SizeF(21F, 32F);
                                            dataRow.Cells.AddRange(new[]
                                            {
                                        new XRTableCell { Text = "   ", WidthF = 9F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(dnsServer.EntityName), WidthF = 220.36F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Controls = { pictureBox }, WidthF = 30F, HeightF = 32F, TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(dnsServer.Type), WidthF = 148.72F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(dnsServer.Category), WidthF = 196.11F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(dnsServer.EntityField), WidthF = 201.91F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { WidthF = 10F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) },
                                        new XRTableCell { Text = GetValueOrNA(dnsServer.BusinessServiceName), WidthF = 218.05F, HeightF = 32F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 8, 3), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) }
                                        });
                                            bool isOddRow = rowIndex % 2 != 0;
                                            dataRow.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                                            table.Rows.Add(dataRow);
                                            rowIndex++;
                                        }

                                    }
                                }
                                a++;

                            }

                        }
                    }
                };

                this.Detail.Visible = report.Count != 0;
                this.DataSource = report;


                Available = LicenseReports.LicenseReportVms.Select(x => x.AvailableCountVm).ToList();
                Used = LicenseReports.LicenseReportVms.Select(x => x.UsedCountVm).ToList();

                int Usedcount = Used.Sum(u =>
                u.DatabaseUsedCount +
                u.ReplicationUsedCount +
                u.StorageUsedCount +
                u.VirtualizationUsedCount +
                u.ApplicationUsedCount +
                u.DnsUsedCount +
                u.NetworkUsedCount +
                u.ThirdPartyUsedCount
            );

                CombinedCount combinedCount = new CombinedCount
                {
                    DatabaseCount = Available.Sum(a => a.DatabaseAvailableCount) + Used.Sum(u => u.DatabaseUsedCount),
                    ReplicationCount = Available.Sum(a => a.ReplicationAvailableCount) + Used.Sum(u => u.ReplicationUsedCount),
                    StorageCount = Available.Sum(a => a.StorageAvailableCount) + Used.Sum(u => u.StorageUsedCount),
                    VirtualizationCount = Available.Sum(a => a.VirtualizationAvailableCount) + Used.Sum(u => u.VirtualizationUsedCount),
                    ApplicationCount = Available.Sum(a => a.ApplicationAvailableCount) + Used.Sum(u => u.ApplicationUsedCount),
                    DnsCount = Available.Sum(a => a.DnsAvailableCount) + Used.Sum(u => u.DnsUsedCount),
                    NetworkCount = Available.Sum(a => a.NetworkAvailableCount) + Used.Sum(u => u.NetworkUsedCount),
                    ThirdPartyCount = Available.Sum(a => a.ThirdPartyAvailableCount) + Used.Sum(u => u.ThirdPartyUsedCount),
                };

                int totalcount = LicenseReports.LicenseReportVms.Sum(total => total.TotalCount);
                totals = combinedCount.DatabaseCount + combinedCount.ReplicationCount + combinedCount.StorageCount + combinedCount.VirtualizationCount + combinedCount.ApplicationCount + combinedCount.DnsCount + combinedCount.ThirdPartyCount + combinedCount.NetworkCount;
                long totalcount64 = totals;
                int remain = (int)(totalcount64 - Usedcount);
                xrLabel67.Text = totals.ToString();
                xrLabel68.Text = Usedcount.ToString();
                xrLabel69.Text = remain.ToString();
                // totals = totalcount;
                usedcounts = Usedcount;
                remaincounts = remain;

                xrLabel6.Text = combinedCount.DatabaseCount.ToString();

                xrLabel5.Text = combinedCount.DnsCount.ToString();
                xrLabel4.Text = combinedCount.ReplicationCount.ToString();
                xrLabel10.Text = combinedCount.ApplicationCount.ToString();
                xrLabel14.Text = combinedCount.NetworkCount.ToString();
                xrLabel36.Text = combinedCount.StorageCount.ToString();
                xrLabel37.Text = combinedCount.ThirdPartyCount.ToString();
                xrLabel38.Text = combinedCount.VirtualizationCount.ToString();


                if (Used.Count != 0)
                {
                    xrLabel16.Text = Used[0].DnsUsedCount.ToString();
                    xrLabel41.Text = Used[0].ReplicationUsedCount.ToString();
                    xrLabel42.Text = Used[0].DatabaseUsedCount.ToString();
                    xrLabel43.Text = Used[0].ApplicationUsedCount.ToString();
                    xrLabel44.Text = Used[0].NetworkUsedCount.ToString();
                    xrLabel47.Text = Used[0].StorageUsedCount.ToString();
                    xrLabel48.Text = Used[0].ThirdPartyUsedCount.ToString();
                    xrLabel52.Text = Used[0].VirtualizationUsedCount.ToString();
                }

                xrLabel51.Text = totals.ToString();
                if (Available.Count != 0)
                {
                    xrLabel1.Text = Available[0].DnsAvailableCount.ToString();
                    xrLabel55.Text = Available[0].ReplicationAvailableCount.ToString();
                    xrLabel56.Text = Available[0].DatabaseAvailableCount.ToString();
                    xrLabel57.Text = Available[0].ApplicationAvailableCount.ToString();
                    xrLabel58.Text = Available[0].NetworkAvailableCount.ToString();
                    xrLabel61.Text = Available[0].StorageAvailableCount.ToString();
                    xrLabel62.Text = Available[0].ThirdPartyAvailableCount.ToString();
                    xrLabel63.Text = Available[0].VirtualizationAvailableCount.ToString();
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_PO_excel report. The error message : " + ex.Message); throw; }
        }
        //Charts
        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                Int64 DatabaseCount = 0, DNSCount = 0, ReplicationCount = 0, ApplicationCount = 0,
                        NetworkCount = 0, Storage = 0, ThirdPartyTool = 0, VM = 0, NodataFound = 0;
                if (Used.Count != 0)
                {
                    DatabaseCount = Used[0].DatabaseUsedCount;
                    DNSCount = Used[0].DnsUsedCount;
                    ReplicationCount = Used[0].ReplicationUsedCount;
                    ApplicationCount = Used[0].ApplicationUsedCount;
                    NetworkCount = Used[0].NetworkUsedCount;
                    Storage = Used[0].StorageUsedCount;
                    ThirdPartyTool = Used[0].ThirdPartyUsedCount;
                    VM = Used[0].VirtualizationUsedCount;
                }
                bool noData = (DatabaseCount + DNSCount + ReplicationCount + ApplicationCount +
                   NetworkCount + Storage + ThirdPartyTool + VM == 0);

                NodataFound = noData ? 1 : 0;
                string textPattern = noData ? "{A}" : "{A}\n{V}";
                bool detailVisible = !noData;

                Series series = new Series("Series1", ViewType.Doughnut);
                xrChart1.Series.Add(series);

                series.DataSource = CreateChartData(ApplicationCount, ReplicationCount, DNSCount, DatabaseCount, NetworkCount, Storage, ThirdPartyTool, VM, NodataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series.View = doughnutSeriesView;
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = textPattern;
                ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                //this.Detail.Visible = detailVisible;

            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_PO_excel_report's detail Chart. The error message : " + ex.Message); throw; }

        }
        private DataTable CreateChartData(Int64 ApplicationCount, Int64 ReplicationCount, Int64 DNSCount, Int64 DatabaseCount, Int64 NetworkCount, Int64 Storage, Int64 ThirdPartyTool, Int64 VM, Int64 NodataFound)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Network", NetworkCount);
            table.Rows.Add("Database", DatabaseCount);
            table.Rows.Add("DNS", DNSCount);
            table.Rows.Add("Replication", ReplicationCount);
            table.Rows.Add("Application", ApplicationCount);
            table.Rows.Add("Storage", Storage);
            table.Rows.Add("ThirdPartyTool", ThirdPartyTool);
            table.Rows.Add("VM", VM);
            table.Rows.Add("Not  Utilized", NodataFound);

            return table;
        }

        private void xrChart4_BeforePrint(object sender, System.EventArgs e)
        {
            try
            {
                Series series = new Series("Series1", ViewType.Bar);
                xrChart4.Series.Add(series);
                series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.True;
                series.DataSource = CreateChartDatabar(totals, usedcounts, remaincounts);
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{V}";
                series.View.Colorizer = new ColorObjectColorizer();
                BarSeriesView view = (BarSeriesView)series.View;
                view.BarWidth = 0.4;
                view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                view.Pane.BorderVisible = false;
                xrChart4.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_PO_excel's total bar Chart. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartDatabar(Int64 totalCount, Int64 usedCount, Int64 remainCount)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Assigned", totalCount);
            table.Rows.Add("Utilized", usedCount);
            table.Rows.Add("Remaining", remainCount);
            return table;
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + LicenseReports.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the License_PO_excel's User Name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex)
            { _logger.LogError("Error occured while display the License_PO_excel's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the License_PO_excel's customer logo" + ex.Message.ToString());
            }
        }
    }
}