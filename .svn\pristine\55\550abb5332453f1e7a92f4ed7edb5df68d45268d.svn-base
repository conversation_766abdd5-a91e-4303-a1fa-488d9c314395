﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;

public class WithdrawApprovalMatrixRequestCommandValidator : AbstractValidator<WithdrawApprovalMatrixRequestCommand>
{
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;

    public WithdrawApprovalMatrixRequestCommandValidator(IApprovalMatrixRequestRepository approvalMatrixRequestRepository)
    {
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;

        RuleFor(y => y)
            .NotNull()
            .MustAsync(WithdrawValidUser).WithMessage("You are not authorized to withdraw this request");

    }


    private async Task<bool> WithdrawValidUser(WithdrawApprovalMatrixRequestCommand command,
        CancellationToken token)
    {
        return await _approvalMatrixRequestRepository.IsValidWithdrawUser(command.Id);
    }

}