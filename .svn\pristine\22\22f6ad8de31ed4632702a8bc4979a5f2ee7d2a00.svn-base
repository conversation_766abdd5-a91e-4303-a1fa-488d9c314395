﻿using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Delete;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormType.Queries.GetList;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class FormTypeService : BaseService, IFormTypeService
{
    public FormTypeService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<FormTypeNameVm>> GetFormTypeNames()
    {
        Logger.LogDebug("Get All FormType Names");

        return await Mediator.Send(new GetFormTypeNameQuery());
    }

    public async Task<bool> IsFormTypeNameExist(string formTypeName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(formTypeName, "FormType Name");

        Logger.LogDebug($"Check Name Exists Detail by FormType Name '{formTypeName}' and Id '{id}'");

        return await Mediator.Send(new GetFormTypeNameUniqueQuery { FormTypeName = formTypeName, FormTypeId = id });
    }

    public async Task<List<FormTypeListVm>> GetFormTypeList()
    {
        Logger.LogDebug("Get All FormTypes");

        return await Mediator.Send(new GetFormTypeListQuery());
    }

    public async Task<BaseResponse> CreateAsync(CreateFormTypeCommand createFormTypeCommand)
    {
        Logger.LogDebug($"Create FormType '{createFormTypeCommand.FormTypeName}'");

        return await Mediator.Send(createFormTypeCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateFormTypeCommand updateFormTypeCommand)
    {
        Logger.LogDebug($"Update FormType '{updateFormTypeCommand.FormTypeName}'");

        return await Mediator.Send(updateFormTypeCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FormType Id");

        Logger.LogDebug($"Delete FormType Details by Id '{id}'");

        return await Mediator.Send(new DeleteFormTypeCommand { Id = id });
    }

    public async Task<FormTypeDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FormType Id");

        Logger.LogDebug($"Get FormType Detail by Id '{id}'");

        return await Mediator.Send(new GetFormTypeDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<FormTypeListVm>> GetPaginatedFormTypes(GetFormTypePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in FormType Paginated List");

        return await Mediator.Send(query);
    }
}