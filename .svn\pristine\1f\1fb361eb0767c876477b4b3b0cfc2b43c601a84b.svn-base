using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class ApprovalMatrixRequestService : BaseService,IApprovalMatrixRequestService
{
    public ApprovalMatrixRequestService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<WithdrawApprovalMatrixRequestResponse> WithdrawApprovalMatrixRequest(WithdrawApprovalMatrixRequestCommand command)
    {
        Logger.LogDebug($"Withdraw ApprovalMatrixRequest '{command}'");

        return await Mediator.Send(command);
    }

    public async Task<List<ApprovalMatrixRequestListVm>> GetApprovalMatrixRequestList()
    {
        Logger.LogInformation("Get All ApprovalMatrixRequests");

        return await Mediator.Send(new GetApprovalMatrixRequestListQuery());
    }

    public async Task<ApprovalMatrixRequestDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixRequest Id");

        Logger.LogInformation($"Get ApprovalMatrixRequest Detail by Id '{id}'");

        return await Mediator.Send(new GetApprovalMatrixRequestDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixRequestCommand createApprovalMatrixRequestCommand)
    {
        Logger.LogInformation($"Create ApprovalMatrixRequest '{createApprovalMatrixRequestCommand}'");

        return await Mediator.Send(createApprovalMatrixRequestCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixRequestCommand updateApprovalMatrixRequestCommand)
    {
        Logger.LogInformation($"Update ApprovalMatrixRequest '{updateApprovalMatrixRequestCommand}'");

        return await Mediator.Send(updateApprovalMatrixRequestCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixRequest Id");

        Logger.LogInformation($"Delete ApprovalMatrixRequest Details by Id '{id}'");

        return await Mediator.Send(new DeleteApprovalMatrixRequestCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsApprovalMatrixRequestNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "ApprovalMatrixRequest Name");

     Logger.LogInformation($"Check Name Exists Detail by ApprovalMatrixRequest Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetApprovalMatrixRequestNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<ApprovalMatrixRequestListVm>> GetPaginatedApprovalMatrixRequests(GetApprovalMatrixRequestPaginatedListQuery query)
{
    Logger.LogInformation("Get Searching Details in ApprovalMatrixRequest Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
