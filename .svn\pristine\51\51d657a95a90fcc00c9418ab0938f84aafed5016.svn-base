﻿@using ContinuityPatrol.Shared.Core.Domain
@using ContinuityPatrol.Shared.Core.Extensions
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Web.TagHelpers
@using Microsoft.Extensions.Configuration

@inject IConfiguration Configuration

@model ContinuityPatrol.Domain.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<style>
    .background_diabled {
        pointer-events: none;
        opacity: 0.9;
    }
</style>
<div class="container-fluid h-100" id="loginContainer">
    <div class="row h-100">
        <div class="col-md-7 col-lg-7 bg-light d-grid justify-content-center align-items-center">
            <div class="px-4 py-1">
                <img class="mb-4" src="~/img/logo/pts_logo.png" alt="Customer Logo" title="Customer Logo" height="40" width="200" />
                <div class="">
                    <h4>Compliance Management & Operational Resilience</h4>
                    <h6 class="fw-normal my-3">Simple workflows to perform actions & failovers in few clicks.</h6>
                </div>
                <img src="~/img/login/login_iso.png" style="width:100%;" alt="Operational Resilience" alt="Operational Resilience" />
            </div>
        </div>
        <div class="col-md-5 col-lg-5 right-img position-relative">
            <div class="row justify-content-center">
                <div class="col-12 col-md-10 col-lg-9 col-xl-8 col-xxl-6 pt-5 mt-xxl-5">
                    <div class="d-grid h-100 align-items-end">
                        <div>
                            <div class="card-header  mt-xxl-5 pt-xxl-5">
                                <img class="mb-3" src="~/img/logo/cplogo.svg" title="CP Logo" alt="Logo" width="320" />
                                <h6 class="mt-3">Sign in to your account</h6>
                            </div>
                            <div class="card-body">
                                <form method="post" id="loginForm" autocomplete="off" autocorrect="off">
                                    @Html.AntiForgeryToken()
                                    <div class="form-group">
                                        <label class="form-label" for="txtLoginName">Login Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-user"></i></span>
                                            <input type="text" id="txtLoginName" placeholder="Enter Login Name" maxlength="100" autocomplete="off" autocorrect="off" class="form-control" autofocus>
                                            <input asp-for="LoginName" type="hidden" id="hiddenTxtLogin" />
                                        </div>
                                        <span id="loginName_error"></span>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="txtPassword">Password</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-lock"></i></span>
                                            <input asp-for="Password" type="password" id="txtPassword" placeholder="Enter Password" autocomplete="off" autocorrect="off" class="form-control" maxlength="100">
                                        </div>
                                        <span id="password_error"></span>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="ddlCompanyId">Company</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-company"></i></span>
                                            <select asp-for="CompanyId" asp-items="@Model.Companies" id="ddlCompanyId" class="form-select" data-placeholder="Select Company">
                                            </select>
                                        </div>
                                        <span asp-validation-for="CompanyId"></span>
                                    </div>
                                    <div class="d-flex gap-3">
                                        <div class="d-flex gap-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="AD_check" id="ADCheckBox" tabindex="-1">
                                                <label class="form-check-label" for="inlineRadio1">AD</label>
                                            </div>
                                            <div class="form-check" style="opacity: 0.5; pointer-events: none;">
                                                <input class="form-check-input" type="checkbox" name="inlineRadioOptions" id="SSOCheckBox" tabindex="-1">
                                                <label class="form-check-label" for="inlineRadio2">SSO</label>
                                            </div>
                                        </div>
                                        <div class="ms-auto text-end">
                                            <a asp-action="ForgotPassword" asp-controller="Account" class="form-check-label" id="forgotPasswordModel">Forgot password?</a>
                                        </div>
                                    </div>
                                    <div id="domainName" class="mt-2">
                                        <div class="form-group d-flex" style="margin-bottom:0.5rem !important;">
                                            <div class="form-check form-check-inline">
                                                <input type="radio" class="form-check-input ADLoginCheck" id="WFInfraNameList" name="individual" value="individual" checked />
                                                <label class='form-check-label'>Individual</label>

                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input type="radio" class="form-check-input ADLoginCheck" name="group" value="group" />
                                                <label class='form-check-label'>Group</label>

                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-12" id="ADDomainContainer">
                                                <div class="form-label">Domain</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-web"></i></span>
                                                    <select asp-for="Domains" id="ddlADLogin" class="form-select" aria-label="Default select example" data-placeholder="Select Domain">
                                                    </select>
                                                    <input asp-for="AuthenticationType" type="hidden" id="authentication" />
                                                </div>
                                                <span id="Domain-error"></span>
                                            </div>
                                            <div class="form-group col-6" id="ADGroupContainer" style="display:none;">
                                                <div class="form-label">AD Group</div>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="cp-group"></i></span>
                                                    <select id="ddlADGroup" asp-for="GroupName" class="form-select" aria-label="Default select example" data-placeholder="Select AD Group">
                                                        <option value=""></option>
                                                    </select>
                                                    <input asp-for="IsGroup" type="hidden" id="isGroupLogin" />
                                                </div>
                                                <span id="ADGroup-error"></span>
                                            </div>
                                        </div>
                                       
                                    </div>
                                    <div class="wrapper d-none">
                                        <div class="form-label mb-0">Captcha</div>
                                        <div class="captcha-area d-flex align-items-center gap-2 mb-2">
                                            <div class="captcha-img rounded-2 bg-light border px-2">
                                                <span class="captcha fs-5"></span>
                                            </div>
                                            <button class="btn btn-sm btn-secondary reload-btn"><i class="cp-reload align-middle"></i></button>
                                        </div>
                                        <form action="#">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="cp-images"></i>
                                                </span>
                                                <input type="text" class="form-control" placeholder="Enter captcha" maxlength="6" spellcheck="false" required>
                                                <button class="btn btn-sm btn-secondary rounded-2 check-btn">Check</button>
                                            </div>
                                        </form>
                                        <div class="status-text"></div>
                                    </div>
                                    <div class="my-3">
                                        <button type="submit" class="btn btn-primary btn-lg w-100" id="btnLogin">
                                            <span role="status">Login</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="text-center p-1 position-absolute bottom-0 end-0 start-0">
                @{
                    var version = Configuration.GetValue<string>("CP:Version");
                    var isCOE = Configuration.GetValue<string>("Release:isCOE");
                }
                @if (@isCOE != null)
                {
                    <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
                else
                {
                    <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
            </footer>
        </div>
    </div>
</div>
<script nonce="randomnonce" src="~/js/hash.js"></script>
<script nonce="randomnonce" src="~/js/login.js"></script>
<script nonce="randomnonce" src="~/lib/dompurify/dist/purify.min.js"></script>
@{
    var message1 = TempData.Get<MultiLoginSessionMessage>("MultiLoginMessage");
    @if (message1 is not null)
    {
        <multilogin user-name=@message1.UserName></multilogin>
        <script>
            $(document).ready(function () {
                $("#DeleteAccountModal").modal('show')
            });
        </script>
        TempData.Remove("MultiLoginMessage");
    }
}
@section Scripts
{
    <partial name="_ValidationScriptsPartial" />
}
<script type="text/javascript">
    const captcha = document.querySelector(".captcha"),
        reloadBtn = document.querySelector(".reload-btn"),
        inputField = document.querySelector(".input-area input"),
        checkBtn = document.querySelector(".check-btn"),
        statusTxt = document.querySelector(".status-text");
    //storing all captcha characters in array
    let allCharacters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O',
        'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd',
        'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's',
        't', 'u', 'v', 'w', 'x', 'y', 'z', 0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    function getCaptcha() {
        for (let i = 0; i < 6; i++) { //getting 6 random characters from the array
            let randomCharacter = allCharacters[Math.floor(Math.random() * allCharacters.length)];
            captcha.innerText += ` ${randomCharacter}`; //passing 6 random characters inside captcha innerText
        }
    }
    getCaptcha();
    reloadBtn.addEventListener("click", () => {
        removeContent();
        getCaptcha();
    });
    checkBtn.addEventListener("click", e => {
        e.preventDefault();
        statusTxt.style.display = "block";
        let inputVal = inputField.value.split('').join(' ');
        if (inputVal == captcha.innerText) {
            statusTxt.style.color = "#4db2ec";
            statusTxt.innerText = "Nice! Redirect Approved.";
            setTimeout(() => {
                removeContent();
                getCaptcha();
            }, 2000);
        } else {
            statusTxt.style.color = "#ff0000";
            statusTxt.innerText = "Captcha not matched. Please try again!";
        }
    });
    function removeContent() {
        inputField.value = "";
        captcha.innerText = "";
        statusTxt.style.display = "none";
    }
</script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>




