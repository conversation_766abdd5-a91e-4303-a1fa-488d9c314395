﻿$('#btnSetting').removeClass('active')

let globalCustomDashboardDetails;
$(function () {
    if (location.pathname !== '/Admin/Settings/List') {
        localStorage.removeItem('id')
    }

    const element = document.getElementById("DashboardNavbar");
    if (element != null) {
        element.addEventListener("mouseenter", () => {

            GetDynamicDashboardList() // <PERSON> enters
        });

        element.addEventListener("click", () => {

            GetDynamicDashboardList() // <PERSON> enters
        });
    }

    $('.nav-item, .nav-link.dropdown-item.dropdown-toggle, .nav-link.Menu_Icon').removeClass('active')
    $('.nav-link.dropdown-item').filter(`[href='${window.location.pathname}']`).addClass('active')

    if ($('.nav-link.dropdown-item').filter(`[href='${window.location.pathname}']`).parents('.nav-item.dropend').length) {
        $('.nav-link.dropdown-item').filter(`[href='${window.location.pathname}']`).parents('.nav-item.dropend').find('.nav-link.dropdown-item.dropdown-toggle').addClass('active')
    }
    //$('.nav-link.dropdown-item').filter(`[href='${window.location.pathname}']`).parents('.dropdown-menu').addClass('active')

    $('.nav-link.dropdown-item').filter(`[href='${window.location.pathname}']`).parents('.nav-item.dropdown').find('.nav-link.Menu_Icon').addClass('active')

    //$(".nav-link.Menu_Icon").each(function () {
    //    if (localStorage.getItem("selectedmenu") == $(this).text().trim()) {
    //        $(".nav-link.Menu_Icon").removeClass('active')
    //        if (this.id !== 'btnSetting' && !window.location.href.includes('Monitor')) {
    //            $(this).addClass('active')
    //        }

    //    }       
    //})

    //$(".nav-link.dropdown-item").each(function () {
    //    if (localStorage.getItem("selecteddropdown") == $(this).text().trim()) {
    //        $(".nav-link.dropdown-item").removeClass('active')
    //        $(".nav-link.Menu_Icon").removeClass('active')

    //        if (!window.location.href.includes('Monitor')) {
    //            $(this).addClass('active')
    //        }

    //        if ($(this).closest('.dropdown-menu').siblings('.dropdown-toggle')) {
    //            $(this).closest('.dropdown-menu').siblings('.dropdown-toggle').addClass('active')

    //            var parentMenuIcon = $(this).closest('.dropdown-menu').siblings('.Menu_Icon');
    //            if (parentMenuIcon.length != 0) {
    //                if (parentMenuIcon[0]?.id !== 'btnSetting' && !window.location.href.includes('Monitor')) {
    //                    parentMenuIcon.addClass('active');
    //                }

    //            } else {
    //                $(this).closest('.dropdown-menu').siblings('.dropdown-toggle').parent().parent().siblings('.Menu_Icon').addClass('active')
    //            }
    //        }
    //    } else {
    //        //currentModule = window.location.href
    //        //if (currentmodule.includes('')) {

    //        //}
    //    }        
    //})

    
    //$(".nav-link.Menu_Icon").on('click', function () {
    //    localStorage.removeItem('selecteddropdown')
    //    localStorage.removeItem('selectedmenu')
    //    $(".nav-link.menu_Icon").removeClass('active')
    //    $(this).addClass('active')
    //    // $(".nav-link.menu_icon").removeclass('active')
    //    if (this.id !== 'btnsetting' && !window.location.href.includes('Monitor')) {
    //        if ($(this).text().trim() == 'Drift' || $(this).text().trim() == 'Cyber Resiliency' || $(this).text().trim() == 'Cyber Resiliency' || $(this).text().trim() == 'Cloud Connect') {
    //            $(".nav-link.menu_icon").removeclass('active')
    //            $(this).addclass('active')
    //        }
    //    }
    //    localStorage.setItem('selectedmenu', $(this).text().trim());
    //})

    //$(".nav-link.dropdown-item").on('click', function () {
    //    localStorage.removeItem('selecteddropdown')
    //    localStorage.removeItem('selectedmenu')
    //    $(".nav-link.dropdown-item").removeClass('active')
    //    if (!window.location.href.includes('Monitor')) {
    //        $(this).addClass('active')
    //        if ($(this).parents('.nav-item.dropdown').length > 0) {
    //            $(this).parents('.nav-item.dropdown').find('.nav-link.Menu_Icon').addClass('active')
    //        }
    //    }
    //    localStorage.setItem('selecteddropdown', $(this).text().trim());
    //});

    const checkAlertCount = async () => {
        $("#alertpage").addClass('d-none')
        if (window.location.pathname != '/Alert/AlertDashboard/List') {
            await $.ajax({
                type: "GET",
                url: RootUrl + "Alert/AlertDashboard/LastAlertCount",
                dataType: "json",
                success: function (result) {
                    if (result.success) {
                        let data = result.data
                        if (data.alertId && data.alertCount != 0) {
                            $(".alertcount").text(data.alertCount < 99 ? data.alertCount : "99+")
                            $("#alertpage").attr('alertId', data.alertId).removeClass('d-none')
                            document.getElementById("alertcontextMenu").style.display = "none";
                        } else {
                            $("#alertpage").addClass('d-none')
                        }
                    }
                    else {
                        notificationAlert("warning", result.message)
                    }
                },
            })
            setTimeout(() => {
                checkAlertCount()
            }, 90000)
        }
    }
    //document.getElementById("alertcontextMenu").style.display = "none";
    //document.getElementById("alertpage").addEventListener("contextmenu", function (event) {
    //    event.preventDefault(); 
    //    let menu = document.getElementById("alertcontextMenu");
    //    menu.style.position = "absolute";
    //    menu.style.display = "block";
    //    menu.style.background = "white";
    //    menu.style.border = "1px solid #ccc";
    //    menu.style.padding = "7px";
    //    menu.style.zIndex = "1000";
    //    menu.style.top="30px"
    //});
    //$(document).on("click", '#alertopenNewTab', function () {
    //    document.getElementById("alertcontextMenu").style.display = "none";
    //    let alertId = $("#alertpage").attr('alertId')
    //    localStorage.setItem('ID', alertId);
    //    let link = document.getElementById("alertpage").href; 
    //    window.open(link, "_blank"); 
    //})
    $(document).on("click", '#alertpage', function () {
        let alertId = $(this).attr('alertId')
        localStorage.setItem('ID', alertId);
        window.location.href = '/Alert/AlertDashboard/List'
    });

    const getAlert = async () => {
        let companyId = $("#companyId").data("companyid");
        await $.ajax({
            type: 'GET',
            url: RootUrl + 'Admin/LicenseManager/GetLicenseExpiresByCompanyId',
            dataType: "json",
            traditional: true,
            data: {
                'companyId': companyId
            },
            success: function (response) {
                if (response && response.length > 0) {
                    let html = ''
                    response.forEach(item => {
                        html += `<li>${item.message}</li>`
                    })
                    $("#licenseText").html(`<ul>${html}</ul>`);
                    setTimeout(() => {
                        $("#licenseToast").toast("show");
                    }, 1500)
                }
            },
        });
    };
 
    const getProfileImage = () => {
        let userId = $('#LoginId').text().trim();
        let data = { 'userId': userId };
        $.ajax({
            type: 'GET',
            url: RootUrl + "Admin/User/UserProfiles",
            data: data,
            dataType: "json",
            success: function (result) {
                if (result && result.logoName) {
                    let profileLogo = result.logoName;
                    $('#userProfileImage').prop('src', profileLogo);
                    localStorage.setItem('profileLogo', profileLogo)
                } else {
                    localStorage.removeItem('profileLogo',)
                    $('#userProfileImage').prop('src', '/img/input_Icons/user-profile.svg');                  
                }
            },
        });
    };
    //  getProfileImage();
    if (sessionStorage.getItem('IsFirst')) {
        getAlert();
        getProfileImage();
        //getGlobalSettingConfig();
        sessionStorage.removeItem('IsFirst')
    }
    if (!window.location.href.includes('Account/UserProfile')) {
        let getProfileLogo = localStorage.getItem('profileLogo');
        let isImageDeleted = localStorage.getItem('isImageDeleted');

        if (isImageDeleted === 'true') {
            $('#userProfileImage').prop('src', '/img/input_Icons/user-profile.svg');
        } else if (getProfileLogo) {
            $('#userProfileImage').prop('src', getProfileLogo);
        } 
    }
 
    async function GetDynamicDashboardList() {  
        await $.ajax({
            type: "GET",
            url: RootUrl + "Dashboard/ServiceAvailability/GetDynamicDashboardList",
            dataType: "json",
            success: function (result) {
                if (result.success) {
                    let Html = ""
                    result.message.reverse()
                    result.message.forEach((data) => {                       
                        $(".customDashboard").empty()
                        if (data.name == "Custom Dashboard") {

                            sessionStorage.setItem("CustomDashboardDetails", JSON.stringify(data))

                            if (data?.dynamicSubDashboardListVms?.length != 0) {
                                $(".customDashboard").removeClass("d-none")
                                Html += "<a class='nav-link dropdown-item dropdown-toggle' href='#' role='button' data-bs-toggle='dropdown' aria-expanded='false'>" + data.name + "</a>"
                                Html += "<ul class='dropdown-menu btnDropDown'>"

                                data.dynamicSubDashboardListVms.forEach((subdata) => {
                                    Html += "<li><a  class='nav-link dropdown-item' href='/Dashboard/CustomDashboard/List?dashboardSubId=" + subdata.id + "' role='button'>" + subdata.name + "</a></li>"

                                })
                                Html += '</ul>'
                                $(".customDashboard").append(Html)
                            }
                            else {
                                $(".customDashboard").addClass("d-none")
                            }
                        }

                    })

                }
                //else {
                //    //$("#dashboard-dropdown").empty();
                //    errorNotification(result)
                //}
            },

        })

    }
   
    const getGlobalSettingConfig = async () => {
        let matrix = "Approval Matrix"
        let escalation = "Escalation Matrix"
        let bulk = "Bulk Import"
        let DrCal = "DR Calendar"
        await $.ajax({
            type: 'GET',
            url: RootUrl + "Admin/GlobalSettings/GetGlobalSettingList",
            dataType: "json",
            success: function (result) {
                if (result) {
                    let Matrixvalue = result?.filter((val) => val?.globalSettingKey === matrix)
                    const filter = Matrixvalue[0];
                    let escalationValue = result?.filter((val) => val?.globalSettingKey === escalation)
                    const filteredValue = escalationValue[0]
                    let bulkValue = result?.filter((val) => val?.globalSettingKey === bulk)
                    const filterBulk = bulkValue[0];
                    let DrCalValue = result?.filter((val) => val?.globalSettingKey === DrCal)
                    const filterDrCal = DrCalValue[0];
                    
                    if (filter.globalSettingValue === 'false') {
                        $(".nav-item.dropend").each(function () {
                            let dropdownText = $(this).find('.nav-link').text().trim();
                            if (dropdownText.includes(matrix)) {
                                $(this).addClass('disabled');
                                $(this).find('.dropdown-menu').addClass('disabled');
                                $(this).find('.dropdown-item').addClass('disabled');
                            }

                        });
                    }
                    if (filteredValue.globalSettingValue === 'false') {
                        $(".nav-item.dropend").each(function () {
                            let dropdownText = $(this).find('.nav-link').text().trim();
                            if (dropdownText.includes(escalation)) {
                                $(this).addClass('disabled');
                                $(this).find('.dropdown-menu').addClass('disabled');
                                $(this).find('.dropdown-item').addClass('disabled');
                            }
                        });
                    }

                    if (filterBulk.globalSettingValue === 'false') {
                        $(".nav-item").each(function () {
                            const dropdownItems = document.querySelectorAll('.nav-link.dropdown-item');
                            dropdownItems.forEach(item => {
                                if (item.textContent.trim() === bulk) {
                                    //item.onclick = function (event) {
                                    //    event.preventDefault(); 
                                    //};
                                    item.classList.add('disabled');
                                }
                            });
                        });
                    }
                    if (filterDrCal.globalSettingValue === 'false') {
                        $(".nav-item").each(function () {
                            const dropdownItems = document.querySelectorAll('.nav-link.dropdown-item');
                            dropdownItems.forEach(item => {
                                if (item.textContent.trim() === DrCal) {
                                    //item.onclick = function (event) {
                                    //    event.preventDefault(); 
                                    //};
                                    item.classList.add('disabled');
                                }
                            });
                        });
                    }
                }
            }
        })
    }
   
    getGlobalSettingConfig()
    //GetDynamicDashboardList();
    checkAlertCount()

    //$('#businessLogoNavigate').on('click', function (e) {
    //    e.preventDefault()
    //    if (!$('#dashboard-link').hasClass('disabled')) {
    //        localStorage.setItem('selecteddropdown', 'Service Availability');
    //        window.location.assign('/Dashboard/ServiceAvailability/List')
    //    }
    //})
})
if (sessionStorage.getItem("CustomDashboardDetails")) {
    GlobalCustomDashboardDetails()
}
function GlobalCustomDashboardDetails() {
    $(".customDashboard").empty()
    let CustomDashboardDetails = sessionStorage.getItem("CustomDashboardDetails")
    let jsonCustomData = JSON.parse(CustomDashboardDetails)
    if (jsonCustomData) {
        if (jsonCustomData?.dynamicSubDashboardListVms?.length != 0) {
            let Html = ""
            $(".customDashboard").removeClass("d-none")
            Html += "<a class='nav-link dropdown-item dropdown-toggle' href='#' role='button' data-bs-toggle='dropdown' aria-expanded='false'>" + jsonCustomData?.name + "</a>"
            Html += "<ul class='dropdown-menu btnDropDown'>"

            jsonCustomData.dynamicSubDashboardListVms.forEach((subdata) => {
                Html += "<li><a  class='nav-link dropdown-item' href='/Dashboard/CustomDashboard/List?dashboardSubId=" + subdata?.id + "' role='button'>" + subdata?.name + "</a></li>"
            })
            Html += '</ul>'
            $(".customDashboard").append(Html)
        }
        else {
            $(".customDashboard").addClass("d-none")
        }
    }
}
$(document).on('click', '#btnSetting', function () {
    // $('#btnSetting').removeClass('show')
    if ($('.dropdown-menu.btnDropDown1').is(':visible')) {
        setTimeout(() => {
            $('#btnSetting').removeClass('show')
        }, 200)
        $('#btnSetting').trigger('blur')
        $('.dropdown-menu.btnDropDown1').css('display', 'none')
    } else {
        // $('#btnSetting').addClass('active').addClass('show')
        // $('#btnSetting').addClass('active').addClass('show')
        $('.dropdown-menu.btnDropDown1').css('display', 'block')
    }
})
let mouse_is_inside = false;
$('.btnDropDown1').on('hover', function (e) {
    mouse_is_inside = true;
}, function () {
    mouse_is_inside = false;
});

$("body").on('mouseup', function (e) {

    let isRightMB = false;

    if ("which" in e)  // Gecko (Firefox), WebKit (Safari/Chrome) & Opera
        isRightMB = e.which == 3;
    else if ("button" in e)  // IE, Opera 
        isRightMB = e.button == 2;

    if (!mouse_is_inside && !isRightMB) {
        if (!e.target.classList.contains('cp-settings')) {
            $('.btnDropDown1').hide();
            $('#btnSetting').removeClass('show')
        }
    }
});
//$(function () {
//    let sessionTimer = Number($("#sessionExpirationTime").text())* 60

//    let modalTimer = 3 * 60
//    let sessionInterval = 0
//    let modalInterval=0

//    function formatTime(seconds) {
//        const mins = Math.floor(seconds / 60);
//        const secs = seconds % 60;
//        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
//    }
//    function updateModalTimer() {
//        document.getElementById('timeRemainingText').textContent = formatTime(modalTimer);
//        if (modalTimer === 0) {
//            clearInterval(modalInterval);
//            localStorage.setItem('force_logout', Date.now());
//            window.location.assign('/Account/Logout');
//        }
//        modalTimer--;
//    }

//    function openModalAndStartTimer() {
//        $('#sessionNotifyModal').modal('show');
//        $('#sessionExpiredIcon').show('100');
//        modalInterval = setInterval(updateModalTimer, 1000);
//    }
//    function startTimer() {
//         sessionInterval = setInterval(() => {
//         /*   console.log('Session timer:', sessionTimer);*/
//            if (sessionTimer === 0) {
//                clearInterval(sessionInterval);
//                openModalAndStartTimer();
//            }
//            sessionTimer--;
//        }, 1000);

//    }
//    startTimer()

//    $('#confirmStayLogedButton').on('click', function () {
//        $('#sessionNotifyModal').modal('hide');
//        clearInterval(sessionInterval);
//        clearInterval(modalInterval);
//        startTimer()
       
//    });
//    $('#btnSessionExpireLogout').on('click', function () {
//        window.location.assign('/Account/Logout');
//    });
//        $(document).on('mousemove keydown click scroll', function () {
//            if (!window.location.href.includes('Logout') && window.location.pathname !== '/') {
//                startTimer();
//                $('#sessionNotifyModal').modal('hide');
//                clearInterval(sessionInterval);
//                clearInterval(modalInterval);
//            }
//        });
//    window.addEventListener('storage', function (e) {
//        if (e.key === 'force_logout') {
//            // Another tab triggered logout
//            window.location.href = LOGOUT_URL;
//        }
//    });

//})

//var inactivityCount = 0;

//$(function () {
//    let inactivityTimer;
//    let sessionNotificationTimer;

//    const sessionNotification = () => {
//        let timeMin = 2;
//        let timeSec = 59;

//        sessionNotificationTimer = setInterval(() => {
//            $('#timeRemainingText').text(`0${timeMin}:${timeSec > 9 ? '' : '0'}${timeSec}`);
//            $('#sessionExpiredIcon').show('100');
//            timeSec--;
//            if (timeSec === 0 && timeMin === 0) {
//                clearInterval(sessionNotificationTimer);
//                window.location.assign('/Account/Logout');
//            } else if (timeSec === 0) {
//                timeSec = 59;
//                timeMin--;
//            }
//        }, 1000);
//    };
//    function resetInactivityTimer() {
//        inactivityCount = 0;
//        const sessionValidity = Number($('#sessionExpirationTime').text());

//        clearInterval(inactivityTimer);
//        clearInterval(sessionNotificationTimer);

//        inactivityTimer = setInterval(() => {
//            inactivityCount += 3;
//            const convertToMin = inactivityCount / 60;
//            if (convertToMin > (sessionValidity - 3)) {
//                $('#sessionNotifyModal').modal('show');
//                sessionNotification();
//               clearInterval(inactivityTimer);
//            } else if (convertToMin > sessionValidity) {
//                window.location.assign('/Account/Logout');
//            }
//        }, 3000);
//    }
//    // Start timer once
//    resetInactivityTimer();
//    // Restart on activity
//    $(document).on('mousemove keydown click scroll', function () {
//        if (!window.location.href.includes('Logout') && window.location.pathname !== '/') {
//            resetInactivityTimer();
//        }
//    });
//    // Handle modal buttons
//    $('#confirmStayLogedButton').on('click', function () {
//        $('#sessionNotifyModal').modal('hide');
//        resetInactivityTimer();
//    });

//    $('#btnSessionExpireLogout').on('click', function () {
//        window.location.assign('/Account/Logout');
//    });

//    // Tab visibility check
//    $(document).on("visibilitychange", function () {
//        inactivityCount=0
//        if (!document.hidden) {
//            setTimeout(() => {
//                const sessionValidity = Number($('#sessionExpirationTime').text());
//                const convertToMin = inactivityCount / 60;
//                if (convertToMin >= sessionValidity - 3) {
//                    window.location.assign('/Account/Logout');
//                }
//            }, 600);
//        }
//    });

//    // Optional: handle internal navigation (like SPA routes)
//    window.DashboardView = function (data) {
//        window.location = '/Dashboard/' + data.dataset.controller + '/' + data.dataset.action;
//    };
//});
