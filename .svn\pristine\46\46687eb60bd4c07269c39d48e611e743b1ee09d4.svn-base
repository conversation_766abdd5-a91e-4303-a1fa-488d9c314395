﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendTestEmail;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using Microsoft.AspNetCore.Authorization;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SmtpConfigurationControllerShould
    {
        private readonly Mock<ILogger<SmtpConfigurationController>> _mockLogger =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  SmtpConfigurationController _controller;

        public SmtpConfigurationControllerShould()
        {

            Initialize();

        }
        internal void Initialize()
        {
            _controller = new SmtpConfigurationController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public void List_ReturnsView()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetSmtpList_ReturnsJsonResultWithSmtpConfigurations()
        {
            
            var smtpConfigurations = new SmtpConfigurationListVm();
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ReturnsAsync(smtpConfigurations);

            
            var result = await _controller.GetSmtpList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"IsPasswordLess\":false", json);
            Assert.NotNull(result); 
        }

        [Fact]
        public async Task GetSmtpList_HandlesException()
        {
            // Arrange
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ThrowsAsync(new Exception("Test Exception"));

            // Act
            var result = await _controller.GetSmtpList() as JsonResult;
            var message = result?.Value as string;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Test Exception", message);
        }

        [Fact]
        public async Task SendTestMail_Success()
        {
            
            var model = new AutoFixture.Fixture().Create<SmtpConfigurationViewModel>();
            var command = new SendTestEmailCommand();
            var response = new BaseResponse { Success = true, Message = "Email sent successfully" };

            _mockMapper.Setup(m => m.Map<SendTestEmailCommand>(model)).Returns(command);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.SendTestMail(command))
                .ReturnsAsync(response);

            
            var result = await _controller.SendTestMail(model) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);

            
        }

        [Fact]
        public async Task SendTestMail_HandlesException()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create <SmtpConfigurationViewModel>();

            // Act
            var result = await _controller.SendTestMail(model) as JsonResult;
            var exception = result?.Value as JsonResult;

            // Assert
            Assert.NotNull(result);
            
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesSmtpConfigurationSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create<SmtpConfigurationViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id","");
            var collection = new FormCollection(dic);
            
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            
            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
            var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
            Assert.Contains("\\\"Message\\\":\\\"Created successfully\\\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesSmtpConfigurationSuccessfully()
        {
            
            var model = new AutoFixture.Fixture().Create<SmtpConfigurationViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id","22");
            var collection = new FormCollection(dic);
            var updateCommand = new UpdateSmtpConfigurationCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateSmtpConfigurationCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
            var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
            Assert.Contains("\\\"Message\\\":\\\"Updated successfully\\\"", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException()
        {

            
            var model = new AutoFixture.Fixture().Create<SmtpConfigurationViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","");
            var collection = new FormCollection(dic);
            
            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(new CreateSmtpConfigurationCommand());
            _mockDataProvider.Setup(m => m.SmtpConfiguration.CreateAsync(It.IsAny<CreateSmtpConfigurationCommand>())).ReturnsAsync(new BaseResponse {Success=true,Message="created success" });
            
            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
            var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
            Assert.Contains("\\\"Message\\\":\\\"created success\\\"", json);

        }

        [Fact]
        public void DecryptPassword_ReturnsDecryptedPassword()
        {

            var encryptedPassword = "encryptedPassword";
            var decryptedPassword = "decryptedPassword";
            SecurityHelper.Encrypt(encryptedPassword);
            SecurityHelper.Decrypt(encryptedPassword);


            var result = _controller.DecryptPassword(encryptedPassword) as JsonResult;



            Assert.NotNull(result);

        }

        // ===== ADDITIONAL COMPREHENSIVE TESTS FOR 100% COVERAGE =====

        [Fact]
        public void DecryptPassword_WithNullPassword_ReturnsEmptyDecrypt()
        {
            // Act
            var result = _controller.DecryptPassword(null) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"decrypt\":\"\"", resultValue);
        }

        [Fact]
        public void DecryptPassword_WithEmptyPassword_ReturnsEmptyDecrypt()
        {
            // Act
            var result = _controller.DecryptPassword("") as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"decrypt\":\"\"", resultValue);
        }

        [Fact]
        public void DecryptPassword_WithWhitespacePassword_ReturnsEmptyDecrypt()
        {
            // Act
            var result = _controller.DecryptPassword("   ") as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"decrypt\":\"\"", resultValue);
        }

        [Fact]
        public void DecryptPassword_LogsDebugMessage()
        {
            // Act
            _controller.DecryptPassword("testPassword");

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering DecryptPassword method in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public void DecryptPassword_LogsNullPasswordMessage()
        {
            // Act
            _controller.DecryptPassword(null);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Password is null or empty in DecryptPassword method on SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public void DecryptPassword_LogsSuccessMessage()
        {
            // Act
            _controller.DecryptPassword("testPassword");

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully decrypted password in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public void DecryptPassword_WithInvalidBase64Key_HandlesExceptionAndReturnsEmptyJson()
        {
            
            var invalidEncryptedPassword = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa$not_base64@!";

            // Act
            var result = _controller.DecryptPassword(invalidEncryptedPassword) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value as string;
            Assert.Equal("", resultValue);
        }

        [Fact]
        public async Task GetSmtpList_LogsDebugMessage()
        {
            // Arrange
            var smtpConfiguration = new SmtpConfigurationListVm();
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ReturnsAsync(smtpConfiguration);

            // Act
            await _controller.GetSmtpList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetSmtpList method in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetSmtpList_LogsSuccessMessage()
        {
            // Arrange
            var smtpConfiguration = new SmtpConfigurationListVm();
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ReturnsAsync(smtpConfiguration);

            // Act
            await _controller.GetSmtpList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully retrieved smtp list in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetSmtpList_LogsExceptionMessage()
        {
            // Arrange
            var exception = new Exception("Test Exception");
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ThrowsAsync(exception);

            // Act
            await _controller.GetSmtpList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred on smtp configuration while retrieving the smtp list.")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task SendTestMail_WithPasswordLess_SetsEmptyPassword()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = true
            };
            var command = new SendTestEmailCommand();
            var response = new BaseResponse { Success = true, Message = "Email sent successfully" };

            _mockMapper.Setup(m => m.Map<SendTestEmailCommand>(model)).Returns(command);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.SendTestMail(command))
                .ReturnsAsync(response);

            // Act
            await _controller.SendTestMail(model);

            // Assert
            Assert.Equal(string.Empty, model.Password);
        }

        [Fact]
        public async Task SendTestMail_WithPassword_EncryptsPassword()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "short", // Short password to trigger encryption
                IsPasswordLess = false
            };
            var command = new SendTestEmailCommand();
            var response = new BaseResponse { Success = true, Message = "Email sent successfully" };

            _mockMapper.Setup(m => m.Map<SendTestEmailCommand>(model)).Returns(command);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.SendTestMail(command))
                .ReturnsAsync(response);

            // Act
            await _controller.SendTestMail(model);

            // Assert
            Assert.NotEqual("short", model.Password); // Password should be encrypted
        }

        [Fact]
        public async Task SendTestMail_LogsDebugMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var command = new SendTestEmailCommand();
            var response = new BaseResponse { Success = true, Message = "Email sent successfully" };

            _mockMapper.Setup(m => m.Map<SendTestEmailCommand>(model)).Returns(command);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.SendTestMail(command))
                .ReturnsAsync(response);

            // Act
            await _controller.SendTestMail(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering SendTestMail method in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task SendTestMail_LogsSuccessMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var command = new SendTestEmailCommand();
            var response = new BaseResponse { Success = true, Message = "Email sent successfully" };

            _mockMapper.Setup(m => m.Map<SendTestEmailCommand>(model)).Returns(command);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.SendTestMail(command))
                .ReturnsAsync(response);

            // Act
            await _controller.SendTestMail(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Test email sent successfully using SMTP configuration with Username:")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_WithPasswordLess_SetsEmptyPassword()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = true
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            Assert.Equal(string.Empty, model.Password);
        }

        [Fact]
        public async Task CreateOrUpdate_WithPassword_EncryptsPassword()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "short", // Short username to trigger encryption
                Password = "short", // Short password to trigger encryption
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            Assert.NotEqual("short", model.UserName); // Username should be encrypted
            Assert.NotEqual("short", model.Password); // Password should be encrypted
        }

        [Fact]
        public async Task CreateOrUpdate_LogsDebugMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering CreateOrUpdate method in SmtpConfiguration")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesActualValidationException()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("UserName", "UserName is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(new CreateSmtpConfigurationCommand());
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(It.IsAny<CreateSmtpConfigurationCommand>()))
                .ThrowsAsync(validationException);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);

            var exception = new Exception("General error");

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(new CreateSmtpConfigurationCommand());
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(It.IsAny<CreateSmtpConfigurationCommand>()))
                .ThrowsAsync(exception);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsCreateDebugMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand { UserName = "TestUser" };
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Creating SmtpConfiguration 'TestUser'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsUpdateDebugMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            var updateCommand = new UpdateSmtpConfigurationCommand { UserName = "UpdatedUser" };
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateSmtpConfigurationCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.UpdateAsync(updateCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Updating SmtpConfiguration 'UpdatedUser'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_LogsCompletionMessage()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            await _controller.CreateOrUpdate(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("CreateOrUpdate operation completed successfully in SmtpConfiguration, returning view.")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== CONSTRUCTOR AND SETUP TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new SmtpConfigurationController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(SmtpConfigurationController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldImplementControllerBase()
        {
            // Act
            var controller = new SmtpConfigurationController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        // ===== METHOD ATTRIBUTE TESTS =====

        [Fact]
        public void GetSmtpList_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var controllerType = typeof(SmtpConfigurationController);
            var method = controllerType.GetMethod("GetSmtpList");

            // Act
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void SendTestMail_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(SmtpConfigurationController);
            var method = controllerType.GetMethod("SendTestMail");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(SmtpConfigurationController);
            var method = controllerType.GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void DecryptPassword_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(SmtpConfigurationController);
            var method = controllerType.GetMethod("DecryptPassword");

            // Act
            var allowAnonymousAttribute = method.GetCustomAttributes(typeof(AllowAnonymousAttribute), false).FirstOrDefault();
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(allowAnonymousAttribute);
            Assert.NotNull(httpGetAttribute);
        }

        // ===== EDGE CASE AND ERROR HANDLING TESTS =====

        [Fact]
        public void List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public async Task GetSmtpList_ShouldNotThrowException()
        {
            // Arrange
            var smtpConfiguration = new SmtpConfigurationListVm();
            _mockDataProvider.Setup(p => p.SmtpConfiguration.GetSmtpConfigurationList())
                .ReturnsAsync(smtpConfiguration);

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.GetSmtpList());
            Assert.Null(exception);
        }

        [Fact]
        public async Task SendTestMail_WithNullModel_ReturnsJsonException()
        {
            // Arrange
            SmtpConfigurationViewModel model = null;

            // Act
            var result = await _controller.SendTestMail(model) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // The method catches the NullReferenceException and returns ex.GetJsonException()
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullModel_ReturnsRedirectToList()
        {
            // Arrange
            SmtpConfigurationViewModel model = null;
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            // The method catches the NullReferenceException and redirects to List with a warning message
        }

        [Fact]
        public async Task CreateOrUpdate_RedirectsToCorrectAction()
        {
            // Arrange
            var model = new SmtpConfigurationViewModel
            {
                UserName = "testuser",
                Password = "testpassword",
                IsPasswordLess = false
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            var createCommand = new CreateSmtpConfigurationCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateSmtpConfigurationCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.SmtpConfiguration.CreateAsync(createCommand)).ReturnsAsync(response);

            _controller.ControllerContext = new ControllerContextMocks().Default();
            var companyList = new List<SelectListItem> { new SelectListItem { Text = "Company1", Value = "1" } };
            WebHelper.CurrentSession.Set("CompanyProfiles", companyList);
            _controller.Request.Form = collection;

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("Settings", result.ControllerName);
            Assert.Equal("Admin", result.RouteValues["Area"]);
        }
    }
}
