using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Delete;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetUpCompingDrillList;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DrCalendarControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DrCalendarController _controller;
    private readonly DrCalendarFixture _drCalendarFixture;

    public DrCalendarControllerTests()
    {
        _drCalendarFixture = new DrCalendarFixture();

        var testBuilder = new ControllerTestBuilder<DrCalendarController>();
        _controller = testBuilder.CreateController(
            _ => new DrCalendarController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDrCalender_WithValidCommand_ReturnsBaseResponse()
    {
        // Arrange
        var command = _drCalendarFixture.CreateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrCalender(command);

        // Assert
        var returnedResponse = Assert.IsType<CreateDrCalendarResponse>(result);
        Assert.Equal("Enterprise DR Calendar Activity", command.ActivityName);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDrCalender_WithValidCommand_ReturnsBaseResponse()
    {
        // Arrange
        var command = _drCalendarFixture.UpdateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.UpdateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrCalender(command);

        // Assert
        var returnedResponse = Assert.IsType<UpdateDrCalendarResponse>(result);
        Assert.Equal("Enterprise DR Calendar Activity Update", command.ActivityName);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDrCalender_WithValidId_ReturnsBaseResponse()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var expectedResponse = _drCalendarFixture.DeleteDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrCalendarCommand>(c => c.Id == calendarId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrCalender(calendarId);

        // Assert
        var returnedResponse = Assert.IsType<DeleteDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetDrCalendarById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var expectedDetail = _drCalendarFixture.DrCalendarDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarDetailsQuery>(q => q.Id == calendarId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDrCalendarById(calendarId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrCalendarDetailVm>(okResult.Value);
        Assert.Equal("Enterprise DR Calendar Detail Activity", returnedDetail.ActivityName);
        Assert.Equal("Detailed enterprise disaster recovery calendar activity", returnedDetail.Description);
        Assert.Equal("DR_DRILL", returnedDetail.ActivityType);
        Assert.Equal("SCHEDULED", returnedDetail.ActivityStatus);
    }

    [Fact]
    public async Task GetDrCalenderList_ReturnsActivityList()
    {
        // Arrange
        var calendarList = _drCalendarFixture.DrCalendarListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDrCalendarListQuery>(), default))
            .ReturnsAsync(calendarList);

        // Act
        var result = await _controller.GetDrCalenderList();

        // Assert
        var returnedList = Assert.IsType<List<DrCalendarActivityListVm>>(result);
        Assert.Equal(5, returnedList.Count);
        Assert.All(returnedList, calendar => Assert.Contains("Enterprise", calendar.ActivityName));
    }

    [Fact]
    public async Task GetPaginatedDrCalendar_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _drCalendarFixture.GetDrCalendarPaginatedListQuery;
        var paginatedResult = _drCalendarFixture.DrCalendarPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDrCalendar(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DrCalendarActivityListVm>>(okResult.Value);
        Assert.Equal(8, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, calendar => Assert.Contains("Enterprise", calendar.ActivityName));
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDrCalendar_CallsClearDataCache()
    {
        // Arrange
        var command = _drCalendarFixture.CreateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDrCalender(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDrCalendar_CallsClearDataCache()
    {
        // Arrange
        var command = _drCalendarFixture.UpdateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.UpdateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDrCalender(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task DeleteDrCalendar_CallsClearDataCache()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var expectedResponse = _drCalendarFixture.DeleteDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrCalendarCommand>(c => c.Id == calendarId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.DeleteDrCalender(calendarId);

        // Assert
        _mediatorMock.Verify(m => m.Send(It.Is<DeleteDrCalendarCommand>(c => c.Id == calendarId), default), Times.Once);
    }

    #endregion

    #region Additional Enterprise Test Cases

    [Fact]
    public async Task CreateDrCalender_HandlesFullDrDrillConfiguration()
    {
        // Arrange
        var drillCommand = _drCalendarFixture.CreateDrCalendarCommand;
        drillCommand.ActivityName = "Enterprise Full DR Drill Q4 2024";
        drillCommand.Description = "Comprehensive enterprise-wide disaster recovery drill";
        drillCommand.ActivityType = "FULL_DR_DRILL";
        drillCommand.ActivityStatus = "SCHEDULED";
        drillCommand.ScheduledStartDate = DateTime.UtcNow.AddDays(30);
        drillCommand.ScheduledEndDate = DateTime.UtcNow.AddDays(31);
        drillCommand.Location = "Enterprise Data Center";
        drillCommand.Responsibility = "IT Operations Team";
        drillCommand.SetReminders = "24_hours";
        drillCommand.WorkflowProfiles = "Enterprise_DR_Profile";

        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(drillCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrCalender(drillCommand);

        // Assert
        var returnedResponse = Assert.IsType<CreateDrCalendarResponse>(result);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Full DR Drill Q4 2024", drillCommand.ActivityName);
        Assert.Equal("FULL_DR_DRILL", drillCommand.ActivityType);
        Assert.Equal("SCHEDULED", drillCommand.ActivityStatus);
        Assert.Equal("Enterprise Data Center", drillCommand.Location);
        Assert.Equal("IT Operations Team", drillCommand.Responsibility);
        Assert.Equal("24_hours", drillCommand.SetReminders);
    }

    [Fact]
    public async Task UpdateDrCalender_HandlesScheduleRescheduling()
    {
        // Arrange
        var rescheduleCommand = _drCalendarFixture.UpdateDrCalendarCommand;
        rescheduleCommand.ActivityName = "Enterprise DR Drill - Rescheduled";
        rescheduleCommand.Description = "Rescheduled due to critical business operations";
        rescheduleCommand.ScheduledStartDate = DateTime.UtcNow.AddDays(45);
        rescheduleCommand.ScheduledEndDate = DateTime.UtcNow.AddDays(46);
        rescheduleCommand.ActivityStatus = "RESCHEDULED";
        rescheduleCommand.Location = "Enterprise Backup Data Center";
        rescheduleCommand.Responsibility = "CTO Office";
        rescheduleCommand.SetReminders = "48_hours";

        var expectedResponse = _drCalendarFixture.UpdateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(rescheduleCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrCalender(rescheduleCommand);

        // Assert
        var returnedResponse = Assert.IsType<UpdateDrCalendarResponse>(result);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise DR Drill - Rescheduled", rescheduleCommand.ActivityName);
        Assert.Equal("RESCHEDULED", rescheduleCommand.ActivityStatus);
        Assert.Equal("Enterprise Backup Data Center", rescheduleCommand.Location);
        Assert.Equal("CTO Office", rescheduleCommand.Responsibility);
        Assert.True(rescheduleCommand.ScheduledStartDate > DateTime.UtcNow.AddDays(40));
    }

    [Fact]
    public async Task GetPaginatedDrCalendar_HandlesDateRangeFiltering()
    {
        // Arrange
        var query = _drCalendarFixture.GetDrCalendarPaginatedListQuery;
        query.SearchString = "Enterprise Q1 2025";
        query.PageSize = 20;
        query.SortColumn = "Priority";
        query.SortOrder = "DESC";

        var dateFilteredResult = new PaginatedResult<DrCalendarActivityListVm>
        {
            Data = Enumerable.Range(1, 20).Select(i => new DrCalendarActivityListVm()
            {
                Id = Guid.NewGuid().ToString(),
                ActivityName = $"Enterprise Q1 2025 DR Event {i}",
                Description = $"Q1 2025 enterprise DR event {i}",
                ScheduledStartDate = DateTime.UtcNow.AddDays(60 + i),
                ScheduledEndDate = DateTime.UtcNow.AddDays(61 + i),
                ActivityType = i % 3 == 0 ? "FULL_DR_DRILL" : i % 2 == 0 ? "DR_TEST" : "DR_DRILL",
                ActivityStatus = i % 4 == 0 ? "CRITICAL" : i % 3 == 0 ? "SCHEDULED" : "PLANNED",
                BusinessServiceId = Guid.NewGuid().ToString(),
                Location = $"Enterprise Q1 Data Center {i}",
                Responsibility = $"Enterprise Q1 Team {i}"
            }).ToList(),
            TotalCount = 20,
            PageSize = 20,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(dateFilteredResult);

        // Act
        var result = await _controller.GetPaginatedDrCalendar(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DrCalendarActivityListVm>>(okResult.Value);

        Assert.Equal(20, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, calendar => Assert.Contains("Enterprise Q1 2025", calendar.ActivityName));
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityStatus == "CRITICAL");
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityStatus == "SCHEDULED");
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityStatus == "PLANNED");
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityType == "FULL_DR_DRILL");
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityType == "DR_TEST");
        Assert.Contains(returnedResult.Data, calendar => calendar.ActivityType == "DR_DRILL");
    }

    [Fact]
    public async Task CreateDrCalender_HandlesComplianceRequiredEvents()
    {
        // Arrange
        var complianceCommand = _drCalendarFixture.CreateDrCalendarCommand;
        complianceCommand.ActivityName = "Enterprise SOX Compliance DR Test";
        complianceCommand.Description = "Mandatory SOX compliance disaster recovery test";
        complianceCommand.ActivityType = "COMPLIANCE_DR_TEST";
        complianceCommand.ActivityStatus = "SCHEDULED";
        complianceCommand.Location = "Enterprise Compliance Data Center";
        complianceCommand.Responsibility = "Compliance Officer";
        complianceCommand.SetReminders = "72_hours";
        complianceCommand.WorkflowProfiles = "Enterprise_Compliance_Profile";

        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(complianceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrCalender(complianceCommand);

        // Assert
        var returnedResponse = Assert.IsType<CreateDrCalendarResponse>(result);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise SOX Compliance DR Test", complianceCommand.ActivityName);
        Assert.Equal("COMPLIANCE_DR_TEST", complianceCommand.ActivityType);
        Assert.Equal("SCHEDULED", complianceCommand.ActivityStatus);
        Assert.Equal("Enterprise Compliance Data Center", complianceCommand.Location);
        Assert.Equal("Compliance Officer", complianceCommand.Responsibility);
        Assert.Equal("72_hours", complianceCommand.SetReminders);
    }

    [Fact]
    public async Task GetDrCalendarById_HandlesComplexEventDetails()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var complexDetail = _drCalendarFixture.DrCalendarDetailVm;
        complexDetail.ActivityName = "Enterprise Multi-Site DR Coordination Event";
        complexDetail.Description = "Complex multi-site disaster recovery coordination event";
        complexDetail.ActivityType = "MULTI_SITE_DR_DRILL";
        complexDetail.ActivityStatus = "CRITICAL";
        complexDetail.WorkflowProfiles = "Enterprise_Operations_Profile";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarDetailsQuery>(q => q.Id == calendarId), default))
            .ReturnsAsync(complexDetail);

        // Act
        var result = await _controller.GetDrCalendarById(calendarId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrCalendarDetailVm>(okResult.Value);

        Assert.Equal("Enterprise Multi-Site DR Coordination Event", returnedDetail.ActivityName);
        Assert.Equal("MULTI_SITE_DR_DRILL", returnedDetail.ActivityType);
        Assert.Equal("CRITICAL", returnedDetail.ActivityStatus);
        Assert.Contains("Operations_Profile", returnedDetail.WorkflowProfiles);
     
    }

    #endregion

    #region Additional Enterprise Scenario Test Cases

    [Fact]
    public async Task CreateDrCalender_WithQuarterlyDrDrill_ReturnsBaseResponse()
    {
        // Arrange
        var quarterlyCommand = _drCalendarFixture.CreateDrCalendarCommand;
        quarterlyCommand.ActivityName = "Enterprise Q1 2025 Quarterly DR Drill";
        quarterlyCommand.Description = "Comprehensive quarterly disaster recovery drill for all business units";
        quarterlyCommand.ActivityType = "QUARTERLY_DR_DRILL";
        quarterlyCommand.ActivityStatus = "SCHEDULED";
        quarterlyCommand.ScheduledStartDate = DateTime.UtcNow.AddDays(90);
        quarterlyCommand.ScheduledEndDate = DateTime.UtcNow.AddDays(91);
        quarterlyCommand.Location = "Enterprise Primary and Secondary Data Centers";
        quarterlyCommand.Responsibility = "Enterprise DR Team";
        quarterlyCommand.SetReminders = "168_hours";
        quarterlyCommand.WorkflowProfiles = "Enterprise_Quarterly_DR_Profile";

        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(quarterlyCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrCalender(quarterlyCommand);

        // Assert
        var returnedResponse = Assert.IsType<CreateDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Q1 2025 Quarterly DR Drill", quarterlyCommand.ActivityName);
        Assert.Equal("QUARTERLY_DR_DRILL", quarterlyCommand.ActivityType);
        Assert.Equal("SCHEDULED", quarterlyCommand.ActivityStatus);
        Assert.Contains("Primary and Secondary", quarterlyCommand.Location);
        Assert.Equal("168_hours", quarterlyCommand.SetReminders);
    }

    [Fact]
    public async Task UpdateDrCalender_WithEmergencyRescheduling_ReturnsBaseResponse()
    {
        // Arrange
        var emergencyCommand = _drCalendarFixture.UpdateDrCalendarCommand;
        emergencyCommand.ActivityName = "Enterprise Emergency DR Test - Rescheduled";
        emergencyCommand.Description = "Emergency rescheduling due to critical system maintenance";
        emergencyCommand.ScheduledStartDate = DateTime.UtcNow.AddDays(7);
        emergencyCommand.ScheduledEndDate = DateTime.UtcNow.AddDays(7).AddHours(4);
        emergencyCommand.ActivityStatus = "EMERGENCY_RESCHEDULED";
        emergencyCommand.Location = "Enterprise Emergency Operations Center";
        emergencyCommand.Responsibility = "Emergency Response Team";
        emergencyCommand.SetReminders = "24_hours";

        var expectedResponse = _drCalendarFixture.UpdateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(emergencyCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrCalender(emergencyCommand);

        // Assert
        var returnedResponse = Assert.IsType<UpdateDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Emergency DR Test - Rescheduled", emergencyCommand.ActivityName);
        Assert.Equal("EMERGENCY_RESCHEDULED", emergencyCommand.ActivityStatus);
        Assert.Contains("Emergency", emergencyCommand.Location);
        Assert.Contains("Emergency", emergencyCommand.Responsibility);
    }

    [Fact]
    public async Task GetPaginatedDrCalendar_WithAnnualPlanningView_ReturnsOkResult()
    {
        // Arrange
        var query = _drCalendarFixture.GetDrCalendarPaginatedListQuery;
        query.SearchString = "Annual Planning";
        query.PageSize = 12;

        var annualPlanningResult = new PaginatedResult<DrCalendarActivityListVm>
        {
            Data = Enumerable.Range(1, 12).Select(i => new DrCalendarActivityListVm()
            {
                Id = Guid.NewGuid().ToString(),
                ActivityName = $"Enterprise Annual Planning Activity {i:D2}",
                Description = $"Annual DR planning activity for month {i}",
                ScheduledStartDate = DateTime.UtcNow.AddDays(30 * i),
                ScheduledEndDate = DateTime.UtcNow.AddDays(30 * i + 1),
                ActivityType = i % 4 == 0 ? "ANNUAL_REVIEW" : i % 3 == 0 ? "QUARTERLY_DRILL" : "MONTHLY_TEST",
                ActivityStatus = i % 6 == 0 ? "COMPLETED" : i % 4 == 0 ? "IN_PROGRESS" : "SCHEDULED",
                BusinessServiceId = Guid.NewGuid().ToString(),
                Location = $"Enterprise Planning Center {i}",
                Responsibility = $"Enterprise Planning Team {i}"
            }).ToList(),
            TotalCount = 12,
            PageSize = 12,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(annualPlanningResult);

        // Act
        var result = await _controller.GetPaginatedDrCalendar(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DrCalendarActivityListVm>>(okResult.Value);
        Assert.Equal(12, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, activity => Assert.Contains("Enterprise Annual Planning", activity.ActivityName));
        Assert.Contains(returnedResult.Data, activity => activity.ActivityType == "ANNUAL_REVIEW");
        Assert.Contains(returnedResult.Data, activity => activity.ActivityType == "QUARTERLY_DRILL");
        Assert.Contains(returnedResult.Data, activity => activity.ActivityType == "MONTHLY_TEST");
        Assert.Contains(returnedResult.Data, activity => activity.ActivityStatus == "COMPLETED");
        Assert.Contains(returnedResult.Data, activity => activity.ActivityStatus == "IN_PROGRESS");
        Assert.Contains(returnedResult.Data, activity => activity.ActivityStatus == "SCHEDULED");
    }

    [Fact]
    public async Task GetDrCalendarById_WithDetailedComplianceActivity_ReturnsOkResult()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var complianceDetail = new DrCalendarDetailVm
        {
            Id = calendarId,
            ActivityName = "Enterprise SOX Compliance DR Validation",
            Description = "Annual SOX compliance disaster recovery validation and documentation",
            ScheduledStartDate = DateTime.UtcNow.AddDays(60),
            ScheduledEndDate = DateTime.UtcNow.AddDays(62),
            ActivityType = "COMPLIANCE_VALIDATION",
            ActivityStatus = "SCHEDULED",
            BusinessServiceId = Guid.NewGuid().ToString(),
            Location = "Enterprise Compliance Validation Center",
            Responsibility = "Compliance and DR Teams",
            InvitationNo = 5,
            RecipientTwo = "<EMAIL>",
            SetReminders = "336_hours",
            WorkflowProfiles = "Enterprise_SOX_Compliance_Profile"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarDetailsQuery>(q => q.Id == calendarId), default))
            .ReturnsAsync(complianceDetail);

        // Act
        var result = await _controller.GetDrCalendarById(calendarId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DrCalendarDetailVm>(okResult.Value);
        Assert.Equal("Enterprise SOX Compliance DR Validation", returnedDetail.ActivityName);
        Assert.Equal("COMPLIANCE_VALIDATION", returnedDetail.ActivityType);
        Assert.Equal("SCHEDULED", returnedDetail.ActivityStatus);
        Assert.Contains("SOX", returnedDetail.Description);
        Assert.Contains("Compliance", returnedDetail.Location);
        Assert.Equal("<EMAIL>", returnedDetail.RecipientTwo);
        Assert.Equal("336_hours", returnedDetail.SetReminders);
    }

    [Fact]
    public async Task DeleteDrCalender_WithCancelledDrillScenario_ReturnsBaseResponse()
    {
        // Arrange
        var cancelledDrillId = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteDrCalendarResponse
        {
            Message = "Enterprise DR drill cancelled and removed from calendar successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDrCalendarCommand>(c => c.Id == cancelledDrillId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrCalender(cancelledDrillId);

        // Assert
        var returnedResponse = Assert.IsType<DeleteDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
        Assert.Contains("cancelled", returnedResponse.Message.ToLower());
        Assert.Contains("Enterprise", returnedResponse.Message);
    }

    [Fact]
    public async Task GetDrCalenderList_WithMultipleActivityTypes_ReturnsActivityList()
    {
        // Arrange
        var diverseActivityList = Enumerable.Range(1, 25).Select(i => new DrCalendarActivityListVm
        {
            Id = Guid.NewGuid().ToString(),
            ActivityName = $"Enterprise Diverse Activity {i:D2}",
            Description = $"Enterprise diverse DR activity type {i}",
            ScheduledStartDate = DateTime.UtcNow.AddDays(i * 7),
            ScheduledEndDate = DateTime.UtcNow.AddDays(i * 7 + 1),
            ActivityType = i % 5 == 0 ? "BUSINESS_CONTINUITY_TEST" :
                          i % 4 == 0 ? "INFRASTRUCTURE_VALIDATION" :
                          i % 3 == 0 ? "APPLICATION_RECOVERY_TEST" :
                          i % 2 == 0 ? "DATA_BACKUP_VERIFICATION" : "COMMUNICATION_DRILL",
            ActivityStatus = i % 7 == 0 ? "COMPLETED" :
                            i % 5 == 0 ? "IN_PROGRESS" :
                            i % 3 == 0 ? "CANCELLED" : "SCHEDULED",
            BusinessServiceId = Guid.NewGuid().ToString(),
            Location = $"Enterprise Location {i % 5 + 1}",
            Responsibility = $"Enterprise Team {i % 3 + 1}"
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDrCalendarListQuery>(), default))
            .ReturnsAsync(diverseActivityList);

        // Act
        var result = await _controller.GetDrCalenderList();

        // Assert
        var returnedList = Assert.IsType<List<DrCalendarActivityListVm>>(result);
        Assert.Equal(25, returnedList.Count);
        Assert.All(returnedList, activity => Assert.Contains("Enterprise Diverse Activity", activity.ActivityName));
        Assert.Contains(returnedList, activity => activity.ActivityType == "BUSINESS_CONTINUITY_TEST");
        Assert.Contains(returnedList, activity => activity.ActivityType == "INFRASTRUCTURE_VALIDATION");
        Assert.Contains(returnedList, activity => activity.ActivityType == "APPLICATION_RECOVERY_TEST");
        Assert.Contains(returnedList, activity => activity.ActivityType == "DATA_BACKUP_VERIFICATION");
        Assert.Contains(returnedList, activity => activity.ActivityType == "COMMUNICATION_DRILL");
        Assert.Contains(returnedList, activity => activity.ActivityStatus == "COMPLETED");
        Assert.Contains(returnedList, activity => activity.ActivityStatus == "IN_PROGRESS");
        Assert.Contains(returnedList, activity => activity.ActivityStatus == "CANCELLED");
        Assert.Contains(returnedList, activity => activity.ActivityStatus == "SCHEDULED");
    }

    #endregion

    #region Missing Method Tests

    [Fact]
    public async Task GetDrCalendarDrillEvents_ReturnsUpcomingDrillCountVm()
    {
        // Arrange
        var expectedDrillEvents = new GetUpcomingDrillCountVm
        {
            Total = 10,
            Success = 7,
            Failure = 3,
            GetUpcomingDrillDetailVm = new List<GetDrCalendarDrillEventsVm>
            {
                new GetDrCalendarDrillEventsVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ActivityName = "Enterprise Q1 DR Drill",
                    ActivityType = "DR_DRILL",
                    Description = "Quarterly disaster recovery drill",
                    BusinessServiceId = Guid.NewGuid().ToString(),
                    BusinessServiceName = "Core Banking System",
                    InvitationNo = 1,
                    CompanyId = Guid.NewGuid().ToString(),
                    Location = "Primary Data Center",
                    ScheduledStartDate = DateTime.UtcNow.AddDays(5),
                    ScheduledEndDate = DateTime.UtcNow.AddDays(5).AddHours(8),
                    Responsibility = "IT Operations",
                    ResponsibilityName = new List<string> { "John Doe", "Jane Smith" },
                    ActivityStatus = "SCHEDULED",
                    RecipientTwo = "<EMAIL>",
                    RecipientTwoName = new List<string> { "Backup Team" }
                },
                new GetDrCalendarDrillEventsVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ActivityName = "Enterprise Security DR Test",
                    ActivityType = "DR_TEST",
                    Description = "Security focused disaster recovery test",
                    BusinessServiceId = Guid.NewGuid().ToString(),
                    BusinessServiceName = "Security Management System",
                    InvitationNo = 2,
                    CompanyId = Guid.NewGuid().ToString(),
                    Location = "Secondary Data Center",
                    ScheduledStartDate = DateTime.UtcNow.AddDays(10),
                    ScheduledEndDate = DateTime.UtcNow.AddDays(10).AddHours(4),
                    Responsibility = "Security Team",
                    ResponsibilityName = new List<string> { "Security Manager" },
                    ActivityStatus = "SCHEDULED",
                    RecipientTwo = "<EMAIL>",
                    RecipientTwoName = new List<string> { "Security Team" }
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDrCalendarDrillEventsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDrillEvents);

        // Act
        var result = await _controller.GetDrCalendarDrillEvents();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Total);
        Assert.Equal(7, result.Success);
        Assert.Equal(3, result.Failure);
        Assert.Equal(2, result.GetUpcomingDrillDetailVm.Count);
        Assert.Contains(result.GetUpcomingDrillDetailVm, drill => drill.ActivityName == "Enterprise Q1 DR Drill");
        Assert.Contains(result.GetUpcomingDrillDetailVm, drill => drill.ActivityType == "DR_TEST");
    }

    [Fact]
    public async Task GetDrCalendarDrillEvents_WithNoEvents_ReturnsEmptyResult()
    {
        // Arrange
        var emptyDrillEvents = new GetUpcomingDrillCountVm
        {
            Total = 0,
            Success = 0,
            Failure = 0,
            GetUpcomingDrillDetailVm = new List<GetDrCalendarDrillEventsVm>()
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDrCalendarDrillEventsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyDrillEvents);

        // Act
        var result = await _controller.GetDrCalendarDrillEvents();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.Total);
        Assert.Equal(0, result.Success);
        Assert.Equal(0, result.Failure);
        Assert.Empty(result.GetUpcomingDrillDetailVm);
    }

    [Fact]
    public async Task GetDrCalendarDrillEvents_WithLargeDataSet_ReturnsCorrectCounts()
    {
        // Arrange
        var largeDrillEvents = new GetUpcomingDrillCountVm
        {
            Total = 100,
            Success = 85,
            Failure = 15,
            GetUpcomingDrillDetailVm = Enumerable.Range(1, 100).Select(i => new GetDrCalendarDrillEventsVm
            {
                Id = Guid.NewGuid().ToString(),
                ActivityName = $"Enterprise DR Event {i}",
                ActivityType = i % 2 == 0 ? "DR_DRILL" : "DR_TEST",
                Description = $"Enterprise disaster recovery event {i}",
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = $"Business Service {i}",
                InvitationNo = i,
                CompanyId = Guid.NewGuid().ToString(),
                Location = $"Data Center {i % 3 + 1}",
                ScheduledStartDate = DateTime.UtcNow.AddDays(i),
                ScheduledEndDate = DateTime.UtcNow.AddDays(i).AddHours(4),
                Responsibility = $"Team {i % 5 + 1}",
                ResponsibilityName = new List<string> { $"Manager {i}" },
                ActivityStatus = i % 10 == 0 ? "COMPLETED" : "SCHEDULED",
                RecipientTwo = $"team{i}@enterprise.com",
                RecipientTwoName = new List<string> { $"Team {i}" }
            }).ToList()
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDrCalendarDrillEventsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeDrillEvents);

        // Act
        var result = await _controller.GetDrCalendarDrillEvents();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Total);
        Assert.Equal(85, result.Success);
        Assert.Equal(15, result.Failure);
        Assert.Equal(100, result.GetUpcomingDrillDetailVm.Count);
        Assert.Contains(result.GetUpcomingDrillDetailVm, drill => drill.ActivityType == "DR_DRILL");
        Assert.Contains(result.GetUpcomingDrillDetailVm, drill => drill.ActivityType == "DR_TEST");
    }

    [Fact]
    public async Task GetUpComingDrillList_ReturnsUpcomingDrillList()
    {
        // Arrange
        var expectedDrillList = new List<UpComingDrillListVm>
        {
            new UpComingDrillListVm
            {
                Message = "The drill 'Enterprise Q1 DR Drill' is scheduled to start on 3/15/2025."
            },
            new UpComingDrillListVm
            {
                Message = "The drill 'Enterprise Security Test' is scheduled to start on 3/18/2025."
            },
            new UpComingDrillListVm
            {
                Message = "The drill 'Enterprise Backup Validation' is scheduled to start on 3/20/2025."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUpComingDrillListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDrillList);

        // Act
        var result = await _controller.GetUpComingDrillList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.Contains(result, drill => drill.Message.Contains("Enterprise Q1 DR Drill"));
        Assert.Contains(result, drill => drill.Message.Contains("Enterprise Security Test"));
        Assert.Contains(result, drill => drill.Message.Contains("Enterprise Backup Validation"));
        Assert.All(result, drill => Assert.Contains("scheduled to start", drill.Message));
    }

    [Fact]
    public async Task GetUpComingDrillList_WithNoDrills_ReturnsEmptyList()
    {
        // Arrange
        var emptyDrillList = new List<UpComingDrillListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUpComingDrillListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(emptyDrillList);

        // Act
        var result = await _controller.GetUpComingDrillList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetUpComingDrillList_WithMultipleDrills_ReturnsCorrectCount()
    {
        // Arrange
        var multipleDrillList = Enumerable.Range(1, 10).Select(i => new UpComingDrillListVm
        {
            Message = $"The drill 'Enterprise DR Event {i}' is scheduled to start on {DateTime.Now.AddDays(i).ToShortDateString()}."
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUpComingDrillListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(multipleDrillList);

        // Act
        var result = await _controller.GetUpComingDrillList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Count);
        Assert.All(result, drill => Assert.Contains("Enterprise DR Event", drill.Message));
        Assert.All(result, drill => Assert.Contains("scheduled to start", drill.Message));
    }

    [Fact]
    public async Task IsDrCalendarNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var activityName = "Enterprise Q1 DR Drill";
        var activityId = Guid.NewGuid().ToString();
        var scheduleStartTime = DateTime.UtcNow.AddDays(7);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarNameUniqueQuery>(q =>
                q.ActivityName == activityName &&
                q.ActivityId == activityId &&
                q.ScheduleStartTime == scheduleStartTime), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDrCalendarNameExist(activityName, activityId, scheduleStartTime);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDrCalendarNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var activityName = "Non-Existing DR Activity";
        var activityId = Guid.NewGuid().ToString();
        var scheduleStartTime = DateTime.UtcNow.AddDays(7);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarNameUniqueQuery>(q =>
                q.ActivityName == activityName &&
                q.ActivityId == activityId &&
                q.ScheduleStartTime == scheduleStartTime), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDrCalendarNameExist(activityName, activityId, scheduleStartTime);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDrCalendarNameExist_WithNullId_ReturnsCorrectResult()
    {
        // Arrange
        var activityName = "Enterprise New DR Activity";
        string? activityId = null;
        var scheduleStartTime = DateTime.UtcNow.AddDays(14);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarNameUniqueQuery>(q =>
                q.ActivityName == activityName &&
                q.ActivityId == activityId &&
                q.ScheduleStartTime == scheduleStartTime), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDrCalendarNameExist(activityName, activityId, scheduleStartTime);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDrCalendarNameExist_WithDifferentScheduleTimes_ReturnsCorrectResult()
    {
        // Arrange
        var activityName = "Enterprise Scheduled DR Test";
        var activityId = Guid.NewGuid().ToString();
        var scheduleStartTime = DateTime.UtcNow.AddDays(30);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrCalendarNameUniqueQuery>(q =>
                q.ActivityName == activityName &&
                q.ActivityId == activityId &&
                q.ScheduleStartTime == scheduleStartTime), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDrCalendarNameExist(activityName, activityId, scheduleStartTime);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Arrange
        // The ClearDataCache method is a NonAction method that clears specific cache keys
        // We can test that the method exists and can be called without exceptions

        // Act & Assert
        // Since ClearDataCache is a NonAction method and calls protected ClearCache method,
        // we verify it doesn't throw any exceptions when called
        Assert.NotNull(_controller);

        // The method should exist and be callable
        var method = typeof(DrCalendarController).GetMethod("ClearDataCache");
        Assert.NotNull(method);
        Assert.True(method.IsPublic);

        // Verify it has the NonAction attribute
        var nonActionAttribute = method.GetCustomAttributes(typeof(Microsoft.AspNetCore.Mvc.NonActionAttribute), false);
        Assert.NotEmpty(nonActionAttribute);
    }

    [Fact]
    public async Task ClearDataCache_IsCalledAfterCreateOperation()
    {
        // Arrange
        var command = _drCalendarFixture.CreateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.CreateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDrCalender(command);

        // Assert
        // Verify that the mediator was called (which would trigger cache clearing in real scenario)
        _mediatorMock.Verify(m => m.Send(command, It.IsAny<CancellationToken>()), Times.Once);

        var returnedResponse = Assert.IsType<CreateDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task ClearDataCache_IsCalledAfterUpdateOperation()
    {
        // Arrange
        var command = _drCalendarFixture.UpdateDrCalendarCommand;
        var expectedResponse = _drCalendarFixture.UpdateDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDrCalender(command);

        // Assert
        // Verify that the mediator was called (which would trigger cache clearing in real scenario)
        _mediatorMock.Verify(m => m.Send(command, It.IsAny<CancellationToken>()), Times.Once);

        var returnedResponse = Assert.IsType<UpdateDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task ClearDataCache_IsCalledAfterDeleteOperation()
    {
        // Arrange
        var calendarId = Guid.NewGuid().ToString();
        var expectedResponse = _drCalendarFixture.DeleteDrCalendarResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDrCalendarCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDrCalender(calendarId);

        // Assert
        // Verify that the mediator was called (which would trigger cache clearing in real scenario)
        _mediatorMock.Verify(m => m.Send(It.IsAny<DeleteDrCalendarCommand>(), It.IsAny<CancellationToken>()), Times.Once);

        var returnedResponse = Assert.IsType<DeleteDrCalendarResponse>(result);
        Assert.True(returnedResponse.Success);
    }

    #endregion
}
