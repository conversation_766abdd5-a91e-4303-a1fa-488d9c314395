﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ReplicationMasterRepositoryMocks
{
    public static Mock<IReplicationMasterRepository> CreateReplicationMasterRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replicationMasters);

        replicationMasterRepository.Setup(repo => repo.AddAsync(It.IsAny<ReplicationMaster>())).ReturnsAsync(
            (ReplicationMaster replicationMaster) =>
            {
                replicationMaster.Id = new Fixture().Create<int>();

                replicationMaster.ReferenceId = new Fixture().Create<Guid>().ToString();

                replicationMasters.Add(replicationMaster);

                return replicationMaster;
            });

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> UpdateReplicationMasterRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replicationMasters);

        replicationMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replicationMasters.SingleOrDefault(x => x.ReferenceId == i));

        replicationMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ReplicationMaster>())).ReturnsAsync((ReplicationMaster replicationMaster) =>
        {
            var index = replicationMasters.FindIndex(item => item.ReferenceId == replicationMaster.ReferenceId);

            replicationMasters[index] = replicationMaster;

            return replicationMaster;

        });
        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> DeleteReplicationMasterRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replicationMasters);

        replicationMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replicationMasters.SingleOrDefault(x => x.ReferenceId == i));

        replicationMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ReplicationMaster>())).ReturnsAsync((ReplicationMaster replicationMaster) =>
        {
            var index = replicationMasters.FindIndex(item => item.ReferenceId == replicationMaster.ReferenceId);

            replicationMaster.IsActive = false;

            replicationMasters[index] = replicationMaster;

            return replicationMaster;
        });

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> GetReplicationMasterRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(replicationMasters);

        replicationMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => replicationMasters.SingleOrDefault(x => x.ReferenceId == i));

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> GetReplicationMasterEmptyRepository()
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ReplicationMaster>());

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> GetReplicationMasterNameUniqueRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.IsReplicationMasterNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => replicationMasters.Exists(x => x.Name == i && x.ReferenceId == j));

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> GetReplicationMasterNamesRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        replicationMasterRepository.Setup(repo => repo.GetReplicationNames()).ReturnsAsync(replicationMasters);

        return replicationMasterRepository;
    }

    public static Mock<IReplicationMasterRepository> GetPaginatedReplicationMasterRepository(List<ReplicationMaster> replicationMasters)
    {
        var replicationMasterRepository = new Mock<IReplicationMasterRepository>();

        var queryableReplicationMaster = replicationMasters.BuildMock();

        replicationMasterRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableReplicationMaster);

        return replicationMasterRepository;
    }
}