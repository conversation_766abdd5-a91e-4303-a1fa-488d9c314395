using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftImpactTypeMasterFixture : IDisposable
{
    public CreateDriftImpactTypeMasterCommand CreateDriftImpactTypeMasterCommand { get; }
    public CreateDriftImpactTypeMasterResponse CreateDriftImpactTypeMasterResponse { get; }
    public UpdateDriftImpactTypeMasterCommand UpdateDriftImpactTypeMasterCommand { get; }
    public UpdateDriftImpactTypeMasterResponse UpdateDriftImpactTypeMasterResponse { get; }
    public DeleteDriftImpactTypeMasterCommand DeleteDriftImpactTypeMasterCommand { get; }
    public DeleteDriftImpactTypeMasterResponse DeleteDriftImpactTypeMasterResponse { get; }
    public DriftImpactTypeMasterDetailVm DriftImpactTypeMasterDetailVm { get; }
    public List<DriftImpactTypeMasterListVm> DriftImpactTypeMasterListVm { get; }
    public GetDriftImpactTypeMasterPaginatedListQuery GetDriftImpactTypeMasterPaginatedListQuery { get; }
    public PaginatedResult<DriftImpactTypeMasterListVm> DriftImpactTypeMasterPaginatedResult { get; }
    public GetDriftImpactTypeMasterNameUniqueQuery GetDriftImpactTypeMasterNameUniqueQuery { get; }

    public DriftImpactTypeMasterFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise drift impact type scenarios
        fixture.Customize<CreateDriftImpactTypeMasterCommand>(c => c
            .With(x => x.ImpactType, "Enterprise Critical Impact")
            );

        fixture.Customize<CreateDriftImpactTypeMasterResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Impact Type Master created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDriftImpactTypeMasterCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ImpactType, "Enterprise Updated Critical Impact")
            );

        fixture.Customize<UpdateDriftImpactTypeMasterResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Impact Type Master updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDriftImpactTypeMasterResponse>(c => c
            .With(x => x.IsActive, false)
            .With(x => x.Message, "Enterprise Drift Impact Type Master deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DriftImpactTypeMasterDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ImpactType, "Enterprise Detail Critical Impact")
         );

        fixture.Customize<DriftImpactTypeMasterListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ImpactType, "Enterprise List Critical Impact")
            );

        // Initialize properties
        CreateDriftImpactTypeMasterCommand = fixture.Create<CreateDriftImpactTypeMasterCommand>();
        CreateDriftImpactTypeMasterResponse = fixture.Create<CreateDriftImpactTypeMasterResponse>();
        UpdateDriftImpactTypeMasterCommand = fixture.Create<UpdateDriftImpactTypeMasterCommand>();
        UpdateDriftImpactTypeMasterResponse = fixture.Create<UpdateDriftImpactTypeMasterResponse>();
        DeleteDriftImpactTypeMasterCommand = new DeleteDriftImpactTypeMasterCommand { Id = Guid.NewGuid().ToString() };
        DeleteDriftImpactTypeMasterResponse = fixture.Create<DeleteDriftImpactTypeMasterResponse>();
        DriftImpactTypeMasterDetailVm = fixture.Create<DriftImpactTypeMasterDetailVm>();
        DriftImpactTypeMasterListVm = fixture.CreateMany<DriftImpactTypeMasterListVm>(7).ToList();
        
        GetDriftImpactTypeMasterPaginatedListQuery = fixture.Create<GetDriftImpactTypeMasterPaginatedListQuery>();
        DriftImpactTypeMasterPaginatedResult = new PaginatedResult<DriftImpactTypeMasterListVm>
        {
            Data = fixture.CreateMany<DriftImpactTypeMasterListVm>(14).ToList(),
            TotalCount = 14,
            PageSize = 14,
            Succeeded = true
        };
        
        GetDriftImpactTypeMasterNameUniqueQuery = new GetDriftImpactTypeMasterNameUniqueQuery { Name = "Enterprise Critical Impact" };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
