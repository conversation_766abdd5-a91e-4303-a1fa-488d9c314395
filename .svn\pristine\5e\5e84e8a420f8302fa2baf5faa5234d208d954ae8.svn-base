﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class WorkflowActionResult : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public static List<WorkflowOperationDrDrillReportVm> DrDrillReportActionlist = new List<WorkflowOperationDrDrillReportVm>();
        public static List<WorkflowActionResultDrDrillReportVm> WorkflowActionResultDrDrillReportVms = new List<WorkflowActionResultDrDrillReportVm>();
        public DrDrillReport drillReports = new DrDrillReport();
        public WorkflowActionResult(string data)
        {
            try
            {
                _logger = PreBuildReportController._logger;
                DrDrillReportActionlist.Clear();
                drillReports = JsonConvert.DeserializeObject<DrDrillReport>(data);
                InitializeComponent();

                WorkflowActionResultDrDrillReportVms.Clear();

                DrDrillReportActionlist.Add(drillReports.WorkflowOperationDrDrillReportVm);

                foreach (var grpReport in DrDrillReportActionlist)
                {
                    foreach (var resultReport in grpReport.WorkflowOperationGroupDrDrillDetailVms)
                    {
                        foreach (var item in resultReport.WorkflowActionResultDrDrillReportVms)
                        {
                            WorkflowActionResultDrDrillReportVms.Add(item);
                        }
                    }
                }
                if (WorkflowActionResultDrDrillReportVms.Count > 0)
                {
                    xrTableCell18.BeforePrint += tableCell_SerialNumber_BeforePrint;

                    this.DetailReport.DataSource = WorkflowActionResultDrDrillReportVms;
                }
                else
                {
                    throw new NotImplementedException();
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the WorKflowAction summary Report. The error message : " + ex.Message); throw; }
        }

        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
    }
}