﻿@model ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel.InfraReplicationMappingViewModel

<div class="modal-dialog modal-dialog-centered modal-lg">
    <form class="modal-content" id="CreateForm" asp-controller="InfraReplicationMapping" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-infra-replication-mapping"></i><span>Infra-Replication Mapping Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body" style="height: 30em;">

            @Html.AntiForgeryToken()
          
            <div class="mb-3 form-group">
                <div class="form-label">Activity Type</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                    <select asp-for='Type' class="form-select-modal" data-placeholder="Select Activity Type"  id="Activetype">
                        <option value=""></option>
                        <option value="Application">Application</option>
                        <option value="Database">DB</option>
                        <option value="Virtual">Virtual</option>
                    </select>
                </div>
                <span id="ActiveType-error"></span>

            </div>
           <div class=" mb-3 form-group" id="databaseType">
            <label class="form-label">Database Type</label>
            <div class="input-group">
                    <span class="input-group-text"><i class="cp-database"></i></span>
                    <select asp-for="DatabaseName" id="databaseName" class="form-select-modal" data-live-search="true" data-placeholder="Select Database Type">
                
                </select>
            </div>
            <span asp-validation-for="DatabaseName" id="Database-error"></span>
            <input asp-for="DatabaseId" id="DatabaseNameId" type="hidden" class="form-control"  />
                
           </div>
            <div class="mb-3 form-group">
                <div class="form-label">Replication Category</div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-replication-on"></i></span>
                    <select asp-for="ReplicationMasterName" id="replicationCategory" class="form-select-modal" data-live-search="true" data-placeholder="Select Replication Category">
                     
                    </select>

                </div>
                <span  id="ReplicationCategory-error"></span>
                <input asp-for="ReplicationMasterId" id="ReplicationCategoryId" type="hidden" class="form-control" />
            </div>

            <div class="mb-3 form-group" id="Replication">
                <div class="form-label">Replication Type</div>             
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-replication-rotate"></i></span>
                    <select class="form-select-modal" id="replicationType" data-live-search="true" multiple="multiple" data-placeholder="Select Replication Type" aria-label="Default select example" required>

                    </select>
                </div>
                <span  id="ReplicationType-error"></span>
             
            </div>
            <input asp-for="Properties" id="replicationtypeid" type="hidden" class="form-control"  />
            <input asp-for="Id" id="replicationMapId" type="hidden" class="form-control" hidden placeholder="Enter Infraobject Name" />
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
            </div>
        </div>
    </form>
</div>


