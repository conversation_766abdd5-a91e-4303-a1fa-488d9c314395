﻿using AutoFixture;
using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class SiteTypeControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher =new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IDataProvider> _mockProvider = new();
    private readonly Mock<ILogger<SiteTypeController>> _mockLogger = new();
    private SiteTypeController _controller;

    public SiteTypeControllerShould()
    {
            
        _controller = new SiteTypeController(
            _mockPublisher.Object,
            _mockMapper.Object,
            _mockProvider.Object,
            _mockLogger.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_ReturnsViewResult_AndPublishesEvent()
    {
        // Arrange
        _mockPublisher.Setup(p => p.Publish(It.IsAny<SiteTypePaginatedEvent>(), default))
                     .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.List();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.Model); // List() returns View() without model
        _mockPublisher.Verify(p => p.Publish(It.IsAny<SiteTypePaginatedEvent>(), default), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_CreatesSiteType_WhenIdIsEmpty()
    {
            
        var siteType = new AutoFixture.Fixture().Create<SiteTypeListModel> ();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var siteTypeCreateCommand = new CreateSiteTypeCommand ();
        var response = new BaseResponse { Success = true, Message = "Created" };

        _mockMapper.Setup(m => m.Map<CreateSiteTypeCommand>(siteType)).Returns(siteTypeCreateCommand);
        _mockProvider.Setup(p => p.SiteType.CreateAsync(siteTypeCreateCommand)).ReturnsAsync(response);

            
        var result = await _controller.CreateOrUpdate(siteType);

            
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
        Assert.Contains("\\\"Message\\\":\\\"Created\\\"", json);
    }

    [Fact]
    public async Task CreateOrUpdate_UpdatesSiteType_WhenIdIsNotEmpty()
    {
        var siteType = new AutoFixture.Fixture().Create<SiteTypeListModel>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var siteTypeUpdateCommand = new UpdateSiteTypeCommand ();
        var response = new BaseResponse { Success = true, Message = "Updated" };
        _mockMapper.Setup(m => m.Map<UpdateSiteTypeCommand>(siteType)).Returns(siteTypeUpdateCommand);
        _mockProvider.Setup(p => p.SiteType.UpdateAsync(siteTypeUpdateCommand)).ReturnsAsync(response);
        var result = await _controller.CreateOrUpdate(siteType);
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
        Assert.Contains("\\\"Message\\\":\\\"Updated\\\"", json);
    }

        

    [Fact]
    public async Task CreateOrUpdate_HandlesValidationException()
    {
        // Arrange
        var siteType = new SiteTypeListModel();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var validationResult = new FluentValidation.Results.ValidationResult();
        validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Type", "Type is required"));
        var validationException = new ContinuityPatrol.Shared.Core.Exceptions.ValidationException(validationResult);

        _mockProvider.Setup(p => p.SiteType.CreateAsync(It.IsAny<CreateSiteTypeCommand>()))
                    .ThrowsAsync(validationException);

        // Act
        var result = await _controller.CreateOrUpdate(siteType);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_HandlesGeneralException()
    {
        // Arrange
        var siteType = new SiteTypeListModel();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var exception = new Exception("An error occurred");
        _mockProvider.Setup(p => p.SiteType.CreateAsync(It.IsAny<CreateSiteTypeCommand>()))
                    .ThrowsAsync(exception);

        // Act
        var result = await _controller.CreateOrUpdate(siteType);

        // Assert
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
    }

    [Fact]
    public async Task GetSiteTypeList_ReturnsJsonResult()
    {
        // Arrange
        var siteTypeList = new List<SiteTypeListVm>();
        _mockProvider.Setup(p => p.SiteType.GetSiteTypeList()).ReturnsAsync(siteTypeList);

        // Act
        var result = await _controller.GetSiteTypeList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.Equal(siteTypeList, jsonResult.Value);
    }

    [Fact]
    public async Task GetSiteTypeList_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.SiteType.GetSiteTypeList()).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetSiteTypeList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }

    [Fact]
    public async Task Delete_ReturnsRedirectToAction_WhenSuccessful()
    {
            
        var id = "1";
        var name = "test";
        var response = new BaseResponse { Success = true, Message = "Deleted" };

        _mockProvider.Setup(p => p.SiteType.DeleteAsync(id, name)).ReturnsAsync(response);

            
        var result = await _controller.Delete(id, name);

            
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
            
    }

    [Fact]
    public async Task Delete_HandlesException()
    {
            
        var id = "1";
        var name = "test";
        var exception = new Exception("An error occurred");

        _mockProvider.Setup(p => p.SiteType.DeleteAsync(id, name)).ThrowsAsync(exception);

            
        var result = await _controller.Delete(id, name);

            
        var redirectResult = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("List", redirectResult.ActionName);
        var json = JsonConvert.SerializeObject(_controller.TempData["Message"]);
        Assert.Contains("Message\\\":\\\"An error occurred\\", json);
    }

    [Fact]
    public async Task IsSiteTypeExist_ReturnsTrue()
    {
        // Arrange
        var type = "Type1";
        var id = "1";
        _mockProvider.Setup(p => p.SiteType.IsSiteTypeExist(type, id)).ReturnsAsync(true);

        // Act
        var result = await _controller.IsSiteTypeExist(type, id);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsSiteTypeExist_ReturnsFalse()
    {
        // Arrange
        var type = "Type1";
        var id = "1";
        _mockProvider.Setup(p => p.SiteType.IsSiteTypeExist(type, id)).ReturnsAsync(false);

        // Act
        var result = await _controller.IsSiteTypeExist(type, id);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsSiteTypeExist_HandlesException_ReturnsFalse()
    {
        // Arrange
        var type = "Type1";
        var id = "1";
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.SiteType.IsSiteTypeExist(type, id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.IsSiteTypeExist(type, id);

        // Assert
        Assert.False(result); // Should return false on exception
    }

    [Fact]
    public async Task GetPagination_ReturnsJsonResult()
    {
        // Arrange
        var query = new GetSiteTypePaginatedListQuery();
        var paginatedList = new PaginatedResult<SiteTypeListVm>();
        _mockProvider.Setup(p => p.SiteType.GetSiteTypePaginatedList(query)).ReturnsAsync(paginatedList);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.Equal(paginatedList, jsonResult.Value);
    }

    [Fact]
    public async Task GetPagination_HandlesException_ReturnsJsonException()
    {
        // Arrange
        var query = new GetSiteTypePaginatedListQuery();
        var exception = new Exception("Database error");
        _mockProvider.Setup(p => p.SiteType.GetSiteTypePaginatedList(query)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        Assert.NotNull(jsonResult.Value);
    }
}