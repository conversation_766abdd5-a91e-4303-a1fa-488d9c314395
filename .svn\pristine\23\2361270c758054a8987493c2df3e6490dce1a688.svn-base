let selectedValues = [], replicationProperties = [], Archivedata = '', tableName = '', ArchivedataId = '', tableArray = [];
let Radiobutton = 'Count'
const exceptThisSymbols = ["e", "E", "+", "-", "."];
const errorElements = ['#profileName-error,#tableName-error,#TableMax-error,#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronMonthMins-error,#CronddlHour-error,#CronMonthly-error,#CronMonth-error,#CronMon-error,#MonthlyHours - error'];

const archiveURL = {
    nameExistUrl: "Admin/Archive/IsArchiveNameExist",
    archivePagination: "/Admin/Archive/GetPaginated",   
}

$(function () {  
    const createPermission = $("#AdminArCreate").data("create-permission").toLowerCase();
    const deletePermission = $("#AdminArDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#btnCreate").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    let dataTable = $('#archiveTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": archiveURL.archivePagination ,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = (d?.order && d.order[0]) ? d.order[0].column : ''; //
                    let sortValue = sortIndex === 1 ? "archiveProfileName" : sortIndex === 2 ? "status" : ""; "";
                    let orderValue = (d?.order && d.order[0]) ? d.order[0].dir : 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }                     
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "archiveProfileName", "name": "Profile Name", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<td><span title="${row.archiveProfileName}" > ${row.archiveProfileName}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "tableNameProperties", "name": "Table Name", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (typeof data === 'string' && data.trim().startsWith('[') && data.trim().endsWith(']')) {
                            let optionValues = JSON.parse(data);
                            tableNames = Array.isArray(optionValues) && optionValues?.length
                                ? optionValues.map(o => o.tableName).join(', ')
                                : 'NA';
                        }
                        if (type === 'display') {
                            return `<td title="${tableNames}"><span title="${tableNames}" > ${tableNames}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Type", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "scheduleTime", "name": "Schedule Time", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {
                        return `
                              <td><span title="${data}"> ${data}</span></td>`;
                    }
                },
                {
                    "data": "backUpType", "name": "Table Count/Backup Type", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {

                        if (type === 'display' && data != null) {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;                        
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                       <div class="d-flex align-items-center gap-2">
                                            <span role="button" class="btnEdit" title='Edit' data-Archive='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                           </span>
                                            <span role="button"  title='Delete' class="btnDelete" data-Archive-id="${row.id}" data-Archive-name="${row.archiveProfileName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                               </span>     
                                               
                               </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                               
                                            <span role="button" title="Edit" class="btn-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                 
                                       <span role="button"  title='Delete' class="btnDelete" data-Archive-id="${row.id}" data-Archive-name="${row.archiveProfileName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                               </span>
                                
                            </div>`;
                        }
                        else if (createPermission == "true" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                               
                                              <span role="button" class="btnEdit" title='Edit' data-Archive='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                           </span>
                                        <span role="button" title="Delete" class="btn-disabled">
                                            <i class="cp-Delete"></i>
                                        </span>
                                
                            </div>`;
                        }
                        else if (createPermission == "false" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                               
                                            <span role="button" title="Edit" class="btn-disabled">
                                                <i class="cp-edit"></i>
                                            </span>
                                 
                                        <span role="button" title="Delete" class="btn-disabled">
                                            <i class="cp-Delete"></i>
                                        </span>
                                
                            </div>`;
                        }
                    }
                }
                //{
                //    "render": function (data, type, row) {

                //        return `
                //       <div class="d-flex align-items-center gap-2">
                //                            <span role="button" class="edit-button" title='Edit' data-Archive='${JSON.stringify(row)}'>
                //                                <i class="cp-edit"></i>
                //                            </span>
                //                            <span role="button"  title='Delete' class="delete-button" data-Archive-id="${row.id}" data-Archive-name="${row.archiveProfileName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                //                                </span>     
                                               
                //                </div>`;
                                    
                //    },
                //    "orderable": false

                //}
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    $('#search-inp').on('keydown input', commonDebounce(function (e) {

        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const ProfileNameCheckbox = $("#ProfileName");
        const TableNameCheckbox = $("#archiveTableName");
        const TypeCheckbox = $("#Type");
        const BackupCheckbox = $("#BackupType");

        const inputValue = $('#search-inp').val();
        if (ProfileNameCheckbox.is(':checked')) {
            selectedValues.push(ProfileNameCheckbox.val() + inputValue);
        }
        if (TableNameCheckbox.is(':checked')) {
            selectedValues.push(TableNameCheckbox.val() + inputValue);
        }
        if (TypeCheckbox.is(':checked')) {
            selectedValues.push(TypeCheckbox.val() + inputValue);
        }
        if (BackupCheckbox.is(':checked')) {
            selectedValues.push(BackupCheckbox.val() + inputValue);
        }

        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {

                if (e.target.value && json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }

    }, 500)); 


$('#GroupPolicy').on('change', function () {
    const value = $(this).val();
    if (value == '1') {
        $('#ExecutionPolicy').show();
    }
    else {
        $('#ExecutionPolicy').hide();
    }
})

async function SetTableName() {
    
    $('#archiveTableName').empty();
    let url = RootUrl + "Admin/Archive/GetPagination";
        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            datatype: "json",   
            data: {},
            success: function (result) {
                

                if (result.succeeded) {                 
                    if (result?.data && Array.isArray(result?.data) && result?.data?.length > 0) {
                        $('#archiveTableName').append('<option value="all" data-id="uniqueValue" data-name="selectAll" >Select all</option>');
                        let checkedItems = result.data.filter(item => item.isChecked);
                        checkedItems.forEach(item => {
                            $('#archiveTableName').append('<option value="' + item.id + '">' + item.tableName + '</option>');
                        });        
                    } else {
                        $("#archiveTableName").append($('<option>', {
                            value: "No Data found, Select Table Access",
                            text: "No Data found, Select Table Access",
                            disabled: true
                        }));
                        return false;
                    }     
                    if (Object.keys(Archivedata).length) {  
                       
                        if (Archivedata?.tableNameProperties && Archivedata?.tableNameProperties?.length) {
                            let selectedValues = JSON.parse(Archivedata?.tableNameProperties);
                            let tableValues = selectedValues.map(t => t.tableId);
                            
                            $('#archiveTableName').val(tableValues).trigger('change');                                     
                        }
                    }                   
                }
                else {
                    errornotification(result);
                }
            },
        });   
}

//Update
    $('#archiveTable').on('click', '.btnEdit', function () {     
        archiveClearField();
        Archivedata = $(this).data("archive");
        Tab_schedule_type(Archivedata);
        $('#CreateModal').modal('show');
        archiveEdit(Archivedata);
        $('#btnArchiveSave').text("Update");     
    });

//Delete
    $('#archiveTable').on('click', '.btnDelete', function () {     
    let ArchivesId = $(this).data("archiveId");
    let ArchivesName = $(this).data("archiveName")
    $("#deleteData").attr("title", ArchivesName).text(ArchivesName);
    $('#textDeleteId').val(ArchivesId);   
});
// Create
    $('#btnCreate').on('click', function () {
    SetTableName()
    clearInputFields();
    archiveClearField();
    $('#btnArchiveSave').text("Save");
});

 //  ArchiveProfileName
    $('#archiveName').on('keyup', commonDebounce (async function () {   
    const value = $(this).val();
    let name = await sanitizeInput($(this).val());
    $(this).val(name);
    await validateName(value, ArchivedataId, IsNameExist);
  },400));

 //  Table Name
    $('#archiveTableName').on('change', function () {
      /*  let selectedOptions = $(this).find('option:selected');*/
        const value = $(this).val();
        if (value.includes('all')) {
            $('#archiveTableName option').prop('selected', true);
            $('#archiveTableName').find('[value="all"]').prop('selected', false);
            $('#archiveTableName').trigger('change');
        } else {
            $('#archiveTableName').find('[value="all"]').prop('selected', false);
        }

        const arrayOfSelectedOptions = $('#archiveTableName option:selected').map(function () {
            return $(this).text();
        }).get();

        const id = $(this).val();
        validateDropDown(id, 'Select table name', 'tableName-error');
        tableArray = [];

        arrayOfSelectedOptions.forEach(function (optionText) {
            let option = $('#archiveTableName').find('option').filter(function () {
                return $(this).text() === optionText;
            });

            let id = option.val();
            let value = option.text();
            let obj = { tableName: value, tableId: id };
            tableArray.push(obj);
        });
        replicationProperties = tableArray;
    });

// Backup Data Count
    $('#archiveBackupDataCount').on('input', function () {
        const value = $(this).val();
        const numericValue = value.replace(/[^0-9]/g, '');  
        $(this).val(numericValue);
        if (numericValue === '' || parseInt(numericValue, 10) < 1) {
            $(this).val('');
        }
        validateDropDown(numericValue, 'Enter backup data count', 'TableMax-error');
    });

   
// Minites
    $('#txtMins').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
            event.preventDefault();
            $('#txtMins').val('');
        }
        if ($(this).val() == 0 || $(this).val() > 59) {
            $('#txtMins').val("")
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
 });

 
// Hourly 

    $('#txtHours').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 23) {
            event.preventDefault();
            $('#txtHours').val('');
        }
        if ($(this).val() == 0) {
            $('#txtHours').val("")
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
    });

    $('#txtMinutes').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 59) {
            event.preventDefault();
            $('#txtMinutes').val('');
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
    });

    $("#txtMinutes,#txtHours").on("input", function () {
        if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
            $("#txtMinutes").val("")
            setTimeout(() => {
                $('#CronHourMin-error').text("Enter the proper hours and minites")
            }, 200)
        }
    })


 // Daily Button

  $('input[name=daysevery]').on('click', function () {
        ValidateCronRadioButton($('#Crondaysevery-error'));
  });

  $('#everyHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
            event.preventDefault();
            $('#everyHours').val('');
        }
        validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
  });

  $('#everyMinutes').on('click', function (event) {
      if (event.key == 109 || event.key == 107 || event.key == 69) {
            event.preventDefault();
            $('#everyMinutes').val('');
       }
      validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
  });

   
// weekly Button 

  $('input[name=weekDays]').on('click', function () {

      let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
      let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
      validateDayNumber(Dayvalue, "Select day", $('#CronDay-error'));
  });

    $('#ddlHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
            $('#ddlHours').val('');
        }
        validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
    });

 //  Month And Year
    $('#lblMonth').on("change", function () {
        
        $('input[name="Monthyday"]').prop("checked", false)
        validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
        let selectedDate = new Date($(this).val()), currentDate = new Date()
        const getDays = (year, month) => {
            return new Date(year, month, 0).getDate();
        };
        const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
        for (let i = 0; i < daysInmonth; i++) {
            let data = ""
            data = i + 1
            $('input[name="Monthyday"]').each(function () {
                let checkboxValue = parseInt($(this).val());
                if (checkboxValue > data) {
                    $(this).css("display", "none")
                } else {
                    $(this).css("display", "block")
                }
            })
            $(".checklabel").each(function () {
                let checkboxValue = parseInt($(this).text());
                if (checkboxValue > data) {
                    $(this).css("display", "none")
                } else {
                    $(this).css("display", "block")
                }
            })
        }
        if ($(this).val() == "") {
            $('input[name="Monthyday"]').prop({ disabled: true, checked: false });
        } else {
            $('input[name="Monthyday"]').each(function () {
                let checkboxValue = parseInt($(this).val());
                if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                    (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                    $(this).prop('disabled', true);
                } else {
                    $(this).prop('disabled', false);
                }
            })
        }
    });

    $('input[name=Monthyday]').on('click', function () {
        
        let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
    });

// Radio Button

$(function () {
  
    $("#BackupTablesClm,#monthgroup").hide();
    Radiobutton = $("#Countclick").val();
    $("input[name='inlineRadioOptions']").on('change',function () {
        Radiobutton = $("input[name='inlineRadioOptions']:checked").val();
        $("#countClm").toggle(Radiobutton === 'Count');
        $("#BackupTablesClm").toggle(Radiobutton === 'Period');
        if (Radiobutton === 'Count') {
            $('#TableMax-error').text('').removeClass('field-validation-error');
            $("#week,#month,#month2,#month6").show();
            $("#option1").prop("checked", true);
            // $('#tableMax').val('')
        } else {
            $('#archiveBackupDataCount').val('')
            $("#oneweek").prop("checked", true);
            $('#userroleerror').text('').removeClass('field-validation-error');

        }
    });
});

    $("input[name='oneweek']").on('change', function () {
        let chkvalue1 = $(this).val();
        RedioVal(chkvalue1);
    });
    function RedioVal(chkvalue1) {
        switch (chkvalue1) {
            case 'One Week':
                $("#option1").prop("checked", true);
                $("#week,#month,#month2,#month6").show();
                break;
            case 'One Month':
                $("#week").hide();
                $("#month,#month2,#month6").show();
                $("#option2").prop("checked", true);
                break;
            case 'Three Month':
                $("#month2,#month6").show();
                $("#week,#month").hide();
                $("#option3").prop("checked", true);
                break;
            case 'Six Month':
                $("#month6").show();
                $("#week,#month,#month2").hide();
                $("#option4").prop("checked", true);
                break;
            case 'One Year':
                $("#week,#month,#month2,#month6").hide();
                $("#option5").prop("checked", true);
                break;
            default:
                // Handle default case if necessary
                break;
        }
    }

//  save
    $("#btnArchiveSave").on('click', async function () {
    
    Get_ScheduleTypes()
    let form = $('#ArchiveForm');
    let profilename = $("#archiveName").val();
    let name = $("#archiveTableName").val().length
    let tableMax = $("#archiveBackupDataCount").val();
    let chkvalue = $('input[name="options"]:checked').val();
    let selectedradioValue = $('input[name="oneweek"]:checked').val();
    let isprofileName = await validateName(profilename, ArchivedataId, IsNameExist);
    let istableName = await validateDropDown(name, ' Select table name', 'tableName-error');
    let istableMax = await validateDropDown(tableMax, ' Enter backup data count', 'TableMax-error');
    let isScheduler = CronValidation();
    let { CronExpression, listcron } = JobCronExpression();
    $('#archiveCronExpression').val(CronExpression);
    $('#archiveCronViewList').val(listcron);
    
    if (istableName && isprofileName && ($("#countClm").is(":visible") ? istableMax : true) && isScheduler) {       
             $("#radioVal").val(Radiobutton);
             $("#ClearBackupId").val(chkvalue);
        if (Radiobutton == "Period") {
            $("#ChkValue").val(selectedradioValue);
        } else {
            $("#ChkValue").val(tableMax);
        }
        let s = JSON.stringify(replicationProperties)
        $('#TableNameProperties').val(s);

        form.trigger('submit');
    }
});


// Validations

async function validateName(value, id = null) {

    const errorElement = $('#profileName-error');

    if (!value) {
        errorElement.text('Enter archive profile name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + archiveURL.nameExistUrl;
    let data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}
async function IsNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
function validateDropDown(value, errorMessage, errorElement) {

    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}
 //function ValidateCronRadioButton(errorElement) {
 //       if ($('input[name=daysevery]:checked').length > 0) {
 //           errorElement.text('').removeClass('field-validation-error');
 //           return true;
 //       }
 //       else {
 //           errorElement.text("Select day type").addClass('field-validation-error');;
 //           return false;
 //       }
 //}
 //function validateMiniteJobNumber(value, errorMsg, errorElement) {
 //       if (!value) {
 //           errorElement.text(errorMsg).addClass('field-validation-error')
 //           return false;
 //       }
 //       else if ((Number(value) < 0) || (Number(value) >= 60)) {
 //           errorElement.text("Enter value between 0 to 59").addClass('field-validation-error')
 //           return false;
 //       }
 //       else {
 //           errorElement.text('').removeClass('field-validation-error')
 //           return true;
 //       }
 // }
 //function validateMinJobNumber(value, errorMsg, errorElement) {
 //       if (!value) {
 //           errorElement.text(errorMsg).addClass('field-validation-error')
 //           return false;
 //       }
 //       else if ((Number(value) < 0) || (Number(value) > 59)) {
 //           errorElement.text("Enter value between 1 to 59").addClass('field-validation-error')
 //           return false;
 //       } else if (Number(value) == "0") {
 //           errorElement.text("Enter the value more than 0").addClass('field-validation-error')
 //           return false;
 //       }
 //       else {
 //           errorElement.text('').removeClass('field-validation-error')
 //           return true;
 //       }
 // }
 //function validateHourJobNumber(value, errorMsg, errorElement) {
 //       if (!value) {
 //           errorElement.text(errorMsg).addClass('field-validation-error')
 //           return false;
 //       }
 //       else if ((Number(value) == 0)) {
 //           errorElement.text("Enter value greater than zero").addClass('field-validation-error')
 //           return false;
 //       }
 //       else if ((Number(value) < 1) || (Number(value) >= 24)) {
 //           errorElement.text("Enter value between 1 to 23").addClass('field-validation-error')
 //           return false;
 //       }
 //       else {
 //           errorElement.text('').removeClass('field-validation-error')
 //           return true;
 //       }
 //  }
 // function validateDayNumber(value, errorMsg, errorElement) {
 //       if (!value || value.length == 0) {
 //           errorElement.text(errorMsg).addClass('field-validation-error');
 //           return false;
 //       } else {
 //           errorElement.text('').removeClass('field-validation-error');
 //           return true;
 //       }
 //   }

  //function Get_ScheduleTypes() {
  //      var Scheduler_types = $('.nav-tabs .active').text().trim();
  //      switch (Scheduler_types) {
  //          case "Minutes":
  //              IsScheduletype = 1
  //              $('#textScheduleType').val(1);
  //              break;
  //          case "Hourly":
  //              IsScheduletype = 2
  //              $('#textScheduleType').val(2);
  //              break;
  //          case "Daily":
  //              IsScheduletype = 3
  //              $('#textScheduleType').val(3);
  //              break;
  //          case "Weekly":
  //              IsScheduletype = 4
  //              $('#textScheduleType').val(4);
  //              break;
  //          case "Monthly":
  //              IsScheduletype = 5
  //              $('#textScheduleType').val(5);
  //              break;
  //      }
  //  }
    //function JobCronExpression() {

    //    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    //    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    //    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    //    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    //    var monthlymonth = $('#lblMonth').val();
    //    var CronExpression = "";
    //    var Minutes = $('#txtMins').val();
    //    var txtHours = $('#txtHours').val() == "00" ? "0" : $('#txtHours').val() == "01" ? "1" : $('#txtHours').val() == "03" ? "3" : $('#txtHours').val() == "04" ? "4" : $('#txtHours').val() == "05" ? "5" : $('#txtHours').val() == "06" ? "6" : $('#txtHours').val() == "07" ? "7" : $('#txtHours').val() == "08" ? "8" : $('#txtHours').val() == "09" ? "9" : $('#txtHours').val()
    //    var txtHourMinutes = $('#txtMinutes').val() == "00" ? "0" : $('#txtMinutes').val() == "01" ? "1" : $('#txtMinutes').val() == "03" ? "3" : $('#txtMinutes').val() == "04" ? "4" : $('#txtMinutes').val() == "05" ? "5" : $('#txtMinutes').val() == "06" ? "6" : $('#txtMinutes').val() == "07" ? "7" : $('#txtMinutes').val() == "08" ? "8" : $('#txtMinutes').val() == "09" ? "9" : $('#txtMinutes').val()
    //    var day = $('#ddlHours').val().split(":")
    //    var ddlHours = day[0]
    //    var ddlMinutes = day[1] //$('#ddlMinutes').val();
    //    var Daily = $('#everyHours').val().split(":")
    //    var everyHours = Daily[0]
    //    var everyMinutes = Daily[1]
    //    var month = $('#MonthlyHours').val().split(":")
    //    var MonthlyHours = month[0]
    //    var MonthlyMins = month[1] //$('#MonthlyMins').val()
    //    var weekDay = $('#defaultCheck-MON-FRI').val();
    //    var datetime = $('#datetimeCron').val()

    //    let schedule_model = document.querySelector('input[name="daysevery"]:checked');

    //    var listcron = '';
    //    if (datetime = '') {
    //        var { CronExpression, listCron } = DateTimeCronBuilder(datetime)
    //        CronExpression = CronExpression
    //        listcron = listCron;
    //    }
    //    else {
    //        if (Minutes != '') {
    //            CronExpression = "0" + " 0/" + Minutes + " * * * ?";
    //            listcron = "Every " + Minutes + " minutes"
    //        }
    //        else if (txtHours != '') {

    //            CronExpression = "0 " + txtHourMinutes + " 0/" + txtHours + " * * ?"
    //            listcron = "Every " + txtHours + " hours " + txtHourMinutes + " minutes";
    //        }
    //        else if (txtDay != '') {
    //            CronExpression = "0 " + ddlMinutes + " " + ddlHours + " ? * " + txtDay + " *"
    //            listcron = txtDay + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
    //        }
    //        else if (txtmonthday != '') {
    //            if (monthlymonth != '') {
    //                monthlymonth = monthlymonth.split('-');
    //                var txtmonth = monthlymonth[1] == "01" ? "JAN" : monthlymonth[1] == "02" ? "FEB" : monthlymonth[1] == "03" ? "MAR" : monthlymonth[1] == "04" ? "APR" :
    //                    monthlymonth[1] == "05" ? "MAY" : monthlymonth[1] == "06" ? "JUN" : monthlymonth[1] == "07" ? "JUL" : monthlymonth[1] == "08" ? "AUG" : monthlymonth[1] == "09" ? "SEP" :
    //                        monthlymonth[1] == "10" ? "OCT" : monthlymonth[1] == "11" ? "NOV" : monthlymonth[1] == "12" ? "DEC" : ""

    //                var txtyear = monthlymonth[0];
    //            }
    //            CronExpression = "0 " + MonthlyMins + " 0/" + MonthlyHours + " " + txtmonthday + " " + txtmonth + " ? " + txtyear
    //            listcron = MonthlyHours + " hours " + MonthlyMins + " minutes for " + txtmonthday + " day(s) on " + txtmonth + " in the year " + txtyear;
    //        }
    //        else if (schedule_model != null) {
    //            if (schedule_model.value == "everyday") {
    //                CronExpression = "0 " + everyMinutes + " " + everyHours + " * * ?"
    //                listcron = " Every day at " + everyHours + " hours " + everyMinutes + " minutes ";
    //            }
    //            else if (schedule_model.value == "MON-FRI") {
    //                CronExpression = "0 " + everyMinutes + " " + everyHours + " ? * " + weekDay + " * ";
    //                listcron = " MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes ";
    //            }
    //        }
    //    }
    //    return { CronExpression, listcron };
    //}
    //function DateTimeCronBuilder(datetime) {
    //    var splitDate = datetime.split("T");
    //    var cronDate = splitDate[0].split("-");
    //    var cronTime = splitDate[1].split(":");

    //    var cronYear = cronDate[0];
    //    var cronMonth = cronDate[1]
    //    var cronDay = cronDate[2];
    //    var cronHours = cronTime[0];
    //    var cronMin = cronTime[1];
    //    var cronmonthexp = cronDate[1] == "01" ? "JAN" : cronDate[1] == "02" ? "FEB" : cronDate[1] == "03" ? "MAR" : cronDate[1] == "04" ? "APR" :
    //        cronDate[1] == "05" ? "MAY" : cronDate[1] == "06" ? "JUN" : cronDate[1] == "07" ? "JUL" : cronDate[1] == "08" ? "AUG" : cronDate[1] == "09" ? "SEP" :
    //            cronDate[1] == "10" ? "OCT" : cronDate[1] == "11" ? "NOV" : cronDate[1] == "12" ? "DEC" : ""
    //    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
    //    // monthname
    //    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronmonthexp + ", only in " + cronYear;
    //    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024
    //    return { CronExpression, listCron }
    //}

    //function Tab_schedule_type(jobData) {

    //    let types = jobData.scheduleType, clickedLink = "", linkId = "";
    //    if (jobData.isSchedule == 1) {
    //        var datetime = DateTimeCronConventor(jobData.cronExpression)
    //        $('#datetimeCron').val(datetime)
    //    }
    //    else {
    //        switch (types) {
    //            case 1:
    //                linkId = "nav-Minutes-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { minutes } = parseMinCronExpression(jobData.cronExpression);
    //                    document.getElementById("txtMins").value = minutes;
    //                }, 150)
    //                break;
    //            case 2:
    //                linkId = "nav-Hourly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, minutes } = parseHoursCronExpression(jobData.cronExpression);
    //                    document.getElementById("txtHours").value = hours;
    //                    document.getElementById("txtMinutes").value = minutes;
    //                }, 150)
    //                break;
    //            case 3:
    //                linkId = "nav-Daily-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, day } = parseDailyCronExpression(jobData.cronExpression);
    //                    document.getElementById("everyHours").value = hours;
    //                    if (day == "?") {
    //                        $("#defaultCheck-everyday").prop("checked", true);
    //                    }
    //                    else {
    //                        $("#defaultCheck-MON-FRI").prop("checked", true);
    //                    }
    //                }, 150)
    //                break;
    //            case 4:
    //                linkId = "nav-Weekly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, day } = parseWeeklyCronExpression(jobData.cronExpression);
    //                    document.getElementById("ddlHours").value = hours;
    //                    dayconventor(day);
    //                }, 150)
    //                break;
    //            case 5:
    //                linkId = "nav-Monthly-tab";
    //                setTimeout(() => {
    //                    clickedLink = document.getElementById(linkId);
    //                    clickedLink.click();
    //                    const { hours, month, days } = parseCronMonthExpression(jobData.cronExpression);
    //                    document.getElementById("MonthlyHours").value = hours;
    //                    document.getElementById("lblMonth").value = month;
    //                    monthDayconventor(days);
    //                }, 150)
    //                break;
    //        }
    //    }
    //}
    //function dayconventor(day) {
    //    const daysMap = {
    //        MON: 1,
    //        TUE: 2,
    //        WED: 3,
    //        THU: 4,
    //        FRI: 5,
    //        SAT: 6,
    //        SUN: 0
    //    };
    //    const days = day.split(',');
    //    days.forEach(day => {
    //        const checkboxId = `#defaultCheck-${daysMap[day]}`;
    //        $(checkboxId).prop("checked", true);
    //    });
    //}
    //function parseMinCronExpression(expression) {
    //    const parts = expression.split(' '),
    //        minutes = parseInt(parts[1].substring(2)),
    //        hours = parseInt(parts[2].substring(2)),
    //        day = parseInt(parts[3].substring(2))
    //    return { hours, minutes, day };
    //}
    //function parseHoursCronExpression(expression) {
    //    const parts = expression.split(' '),
    //        minutes = parseInt(parts[1]),
    //        hours = parseInt(parts[2].substring(2)),
    //        day = parts[5]
    //    return { hours, minutes, day };
    //}
    //function parseDailyCronExpression(expression) {
    //    const parts = expression.split(' '),
    //        minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)),
    //        day = parts[5]
    //    return { hours, minutes, day };
    //}
    //function parseWeeklyCronExpression(expression) {
    //    const parts = expression.split(' '),
    //        minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)),
    //        day = parts[5]
    //    return { hours, minutes, day };
    //}
    //function parseCronMonthExpression(expression) {
    //    const parts = expression.split(' '),
    //        minutes = parseInt(parts[1]), hours = parseInt(parts[2].substring(2)),
    //        month = parts[6] + "-" + parts[4],
    //        days = parts[3]
    //    return { minutes, hours, month, days };
    //};
    //function monthDayconventor(days) {
    //    const day = days.split(" ")
    //    let checkboxes = document.querySelectorAll('input[name="Monthyday"]');
    //    checkboxes.forEach(function (checkbox) {
    //        if (day.includes(checkbox.value)) {
    //            checkbox.checked = true;
    //        }
    //    });
    //};

    function CronValidation() {
        var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
        var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
        var monthlymonth = $('#lblMonth').val();
        var Minutes = $('#txtMins').val();
        var txtHours = $('#txtHours').val();
        var txtHourMinutes = $('#txtMinutes').val();
        var everyHours = $('#everyHours').val();      
        var MonthlyHours = $('#MonthlyHours').val();
        var isScheduler = '';

            $('#datetimeCron').val('');
            var Scheduler_types = $('.nav-tabs .active').text().trim();
            switch (Scheduler_types) {
                case "Minutes":
                    isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                    break;
                case "Hourly":
                    isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                    isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                    break;
                case "Daily":
                    isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                    isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                    if (isSchedulerHour && isSchedulerDay) {
                        isScheduler = true;
                    }
                    break;
                case "Weekly":
                    isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                    isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
                    if (isSchedulerHour && isSchedulerDay) {
                        isScheduler = true;
                    }
                    break;
                case "Monthly":
                    isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                    isSchedulerDay = validateDayNumber(txtmonthday, "Select date", $('#CronMon-error'));
                    isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                    if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                        isScheduler = true;
                    }
                    break;
            }
       
        return isScheduler;
    }


    $('.nav-link').on("click", function () {
        ClearCroneElements();
    });
    function ClearCroneElements() {
        $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours").val('');
        $("input[name=weekDays],input[name=daysevery],input[name=Monthyday]").prop("checked", false);
    }

    $(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab,#Next_profile_data").on("click", function () {
        $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
    })

    $("#nav-Monthly-tab").on("click", function () {
        if ($("btnArchiveSave").text() == "Save") {
            $('input[name=Monthyday]').attr('disabled', 'disabled');
        }
    })
    const archiveClearField = () => {
        replicationProperties = []
        ArchivedataId = ''
        $('#archiveTableName').val([]).trigger('change');
        $("#archiveTableName option:selected").prop("selected", false).change()
        $('#archiveName, #archiveBackupDataCount, #ArchiveId').val('');    
        $('#btnArchiveSave').text("Save");
        $("#Countclick").trigger('click');
        ClearErrorElements(errorElements);
        $('#txtMins, #txtHours, #txtMinutes, #ddlHours, #ddlMinutes, #everyHours, #MonthlyHours, #datetimeCron, #archiveCronExpression, #lblMonth').val('');       
        $("#onemonth,#threemonth,#sixmonth,#oneyear,#option3,#option2,#option4,#option5").prop("checked", false);
        $('#option1, #oneweek').prop('checked', true);      
        $('.nav-link').removeClass('active')
        $('.tab-pane.fade').removeClass('active show')
        $('#nav-Minutes, #nav-Minutes-tab').addClass('active show');
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"], input[name="weekDays"], input[name="Days"], input[name="daysevery1"]').prop('checked', false);
    };

    let monthInput = document.getElementById("lblMonth");
    let today = new Date();
    let currentYear = today.getFullYear();
    let currentMonth = today.getMonth() + 1;
    let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    monthInput.setAttribute("min", minMonth);
    monthInput.setAttribute("max", maxMonth)

    function ClearErrorElements(errorElements) {
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });

    }

   

    function archiveEdit(Archivedata) {
    $('#archiveCronExpression').val(Archivedata?.cronExpression);
    $('#archiveName').val(Archivedata?.archiveProfileName);
    $('#ArchiveId').val(Archivedata?.id);
    ArchivedataId = Archivedata?.id
    let scheduleTime = Archivedata?.scheduleTime.split(" ")
    SetTableName()
    setTimeout(() => {
        
        if (Archivedata.scheduleTime.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }
        if (Archivedata.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }

        if (scheduleTime.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }

        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (Archivedata.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (Archivedata.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
        }


        if (scheduleTime.length >= 12) {       
            var year = parseInt(scheduleTime[12])
            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;

            $("#lblMonth").val(newdate).trigger('change')
            scheduleTime[5]?.split(",").forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)
    if (Archivedata?.type == "Count") {
        $('#archiveBackupDataCount').val(Archivedata?.count);
        Radiobutton = Archivedata.type;
        $("#Countclick").trigger('click');
    } else {

        let backUpTypedata = Archivedata?.backUpType;
        Radiobutton = Archivedata?.type;
        $("#Periodclick").trigger('click');
        switch (backUpTypedata) {
            case 'One Week':
                $("#oneweek").prop("checked", true);
                $("#week,#month,#month2,#month6").show();
                break;
            case 'One Month':
                $("#onemonth").prop("checked", true);
                $("#week").hide();
                break;
            case 'Three Month':
                $("#threemonth").prop("checked", true);
                $("#week,#month").hide();
                break;
            case 'Six Month':
                $("#sixmonth").prop("checked", true);
                $("#week,#month,#month2").hide();
                break;
            case 'One Year':
                $("#oneyear").prop("checked", true);
                $("#week,#month,#month2,#month6").hide();
                break;
        }
    
    }   

    switch (Archivedata.clearBackup) {
    
        case 'One Week':           
            $("#option1").prop("checked", true);
            break;
        case 'One Month':
            $("#option2").prop("checked", true);
            break;
        case 'Three Month':
            $("#option3").prop("checked", true);
            break;
        case 'Six Month':
            $("#option4").prop("checked", true);
            break;
        case 'One Year':
            $("#option5").prop("checked", true);
            break;
        default:           
            break;
    }
    }
})