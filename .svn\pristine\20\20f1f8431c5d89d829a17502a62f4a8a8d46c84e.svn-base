﻿
using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
namespace ContinuityPatrol.Services.Api.Impl.Manage
{
    public class MssqlDBMirroringMonitorLogService: BaseClient,IMSSQLDbMirroringMonitorLogsService
    {
       
        public MssqlDBMirroringMonitorLogService(IConfiguration config, IAppCache cache, ILogger<MssqlDBMirroringMonitorLogService> logger)  : base(config, cache, logger)
        {
        }

       

       public  async Task<BaseResponse> CreateAsync(CreateSQLDBMirroringLogsCommand createSqldbMirroringLogCommand)
        {
            var request = new RestRequest("api/v6/sqldbmirroringmonitorlog", Method.Post);

            request.AddJsonBody(createSqldbMirroringLogCommand);

            return await Post<BaseResponse>(request);
        }

        

        public async Task<List<MSSQLDBMirroringLogListVm>> GetAllSqlDbMirroringLogs()
        {
            var request = new RestRequest("api/v6/sqldbmirroringmonitorlog");

              return await GetFromCache<List<MSSQLDBMirroringLogListVm>>(request, "GetAllSqlDbMirroringLogs");
        }

        public async Task<List<SQLDBMirroringMonitorLogsDetailByTypeVm>> GetSqlDbMirroringLogsByType(string type)
        {
            var request = new RestRequest($"api/v6/sqldbmirroringmonitorlog/type?type={type}");

            return await Get<List<SQLDBMirroringMonitorLogsDetailByTypeVm>>(request);
        }

        public async Task<PaginatedResult<MSSQLDBMirroringLogListVm>> GetPaginatedMSSQLDBMirroringMonitorLogs(GetMSSQLDbMonitorLogsPaginatedListQuery query)
        {
            var request = new RestRequest("api/v6/sqldbmirroringmonitorlog/paginated-list");

            return await Get<PaginatedResult<MSSQLDBMirroringLogListVm>>(request);
        }
    }
}
