namespace ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;

public class IncidentManagementSummaryViewModel
{
    public string Id { get; set; }
    public string IncidentId { get; set; }
    public string ParentBusinessServiceId { get; set; }
    public string ChildBusinessServiceId { get; set; }
    public string ParentBusinessServiceImpactId { get; set; }
    public string ChildBusinessServiceImpactId { get; set; }
    public string ParentBusinessServiceCost { get; set; }
    public string ParentBusinessFunctionId { get; set; }
    public string ChildBusinessFunctionId { get; set; }
    public string ParentBusinessFunctionImpactId { get; set; }
    public string ChildBusinessFunctionImpactId { get; set; }
    public string ParentBusinessFunctionCost { get; set; }
}