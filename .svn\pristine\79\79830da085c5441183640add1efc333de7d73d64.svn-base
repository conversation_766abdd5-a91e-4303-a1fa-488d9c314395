﻿using ClosedXML.Excel;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.BulkImportModel;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using ExcelDataReader;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;
[Area("Configuration")]
public class BulkImportController : BaseController
{
    public readonly ILogger<BulkImportController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IConfiguration _config;
    public BulkImportController(IConfiguration config, IMapper mapper, ILogger<BulkImportController> logger, IDataProvider dataProvider)
    {
        _config = config;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }


    [HttpGet]
    public IActionResult List()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public JsonResult UploadFile(BulkImportViewModel bulkImportModel)
    {
        if (bulkImportModel.uploadedFile != null && bulkImportModel.uploadedFile == null && bulkImportModel.uploadedFile.Count <= 0)
        {
            bulkImportModel.FileUploadStatus = "No File Selected";
            bulkImportModel.servers.Clear();
            bulkImportModel.databases.Clear();
        }
        else
        {
            try
            {
                using var stream = new MemoryStream();
                var sites = _dataProvider.Site.GetSitePaginatedList(new GetSitePaginatedListQuery()).Result.Data.ToList();
                var businessServiceList = _dataProvider.BusinessService.GetBusinessServiceList().Result.ToList();
                var nameVersionList = _dataProvider.ComponentType.GetComponentTypeList().Result;
                var osVersionLists = nameVersionList.Where(x => x.FormTypeName.ToLower() == "server").ToList();

                if (businessServiceList.Count <= 0 || sites.Count <= 0 || osVersionLists.Count <= 0)
                {
                    bulkImportModel.FileUploadStatus = "There is No Records in Sites / Business Services / OS Versions to Save Server Details.";
                    if (bulkImportModel.servers != null)
                    {
                        bulkImportModel.servers.Clear();
                    }
                }
                else
                {
                    if (bulkImportModel.uploadedFile != null) bulkImportModel.uploadedFile[0].CopyTo(stream);
                    stream.Position = 0;
                    using IExcelDataReader reader = ExcelReaderFactory.CreateReader(stream);
                    var excelData = new ExcelDataSetConfiguration
                    {
                        ConfigureDataTable = _ => new ExcelDataTableConfiguration
                        {
                            UseHeaderRow = true
                        }
                    };

                    var dataSet = reader.AsDataSet(excelData);

                    var dataTable = dataSet.Tables[0];

                    var servers = new List<Server>();

                    foreach (DataRow item in dataTable.Rows)
                    {
                        string siteName, siteId;
                        var siteTemp = sites.Where(a => a.Name.Equals(item["SiteName"].ToString())).ToList();
                        if (siteTemp.Any())
                        {
                            siteName = siteTemp.First().Name;
                            if (string.IsNullOrEmpty(siteName))
                            {
                                continue;
                            }

                            siteId = siteTemp.First().Id;
                        }
                        else
                        {
                            continue;
                        }


                        string businessServiceName, businessServiceId;
                        var bsTemp = businessServiceList.Where(a => a.Name.Equals(item["BusinessServiceName"].ToString())).ToList();
                        if (bsTemp.Any())
                        {
                            businessServiceName = bsTemp.First().Name;
                            if (string.IsNullOrEmpty(businessServiceName))
                            {
                                continue;
                            }

                            businessServiceId = bsTemp.First().Id;
                        }
                        else
                        {
                            continue;
                        }


                        string osTypeId;
                        var osTypeTemp = new List<ComponentTypeListVm>();

                        foreach (ComponentTypeListVm comType in osVersionLists)
                        {
                            var jsonObject = JObject.Parse(comType.Properties);

                            if (jsonObject["name"]?.ToString() == item["OSType"].ToString())
                            {
                                if (jsonObject["version"]!.ToString().Contains(item["Version"].ToString()!))
                                {
                                    osTypeTemp.Add(comType);
                                }
                            }

                        }

                        if (osTypeTemp.Any())
                        {
                            osTypeId = osTypeTemp.First().Id;
                        }
                        else
                        {
                            continue;
                        }

                        servers.Add(new Server
                        {

                            Name = item["Name"].ToString(),

                            SiteId = siteId,
                            SiteName = siteName,

                            BusinessServiceId = businessServiceId,
                            BusinessServiceName = businessServiceName,
                            CompanyId = LoggedInUserCompanyId,
                            RoleType = item["RoleType"].ToString(),
                            ServerType = item["ServerType"].ToString(),
                            OSTypeId = osTypeId,
                            OSType = item["OSType"].ToString(),
                            Properties = "",
                            LicenseKey = item["LicenseKey"].ToString(),
                            Version = item["Version"].ToString()

                        });
                    }

                    bulkImportModel.servers = servers;
                    bulkImportModel.FileUploadStatus = "File Uploaded Successfully";
                }
            }
            catch (Exception)
            {
                bulkImportModel.FileUploadStatus = "Error Occurred While Reading File, Make sure that Uploaded File is Filled Correct Data.";
                if (bulkImportModel.servers != null)
                {
                    bulkImportModel.servers.Clear();
                }
            }
        }
        return Json(bulkImportModel.servers);

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public JsonResult UploadFileDatabase(BulkImportViewModel bulkImportModel)
    {
        //ModelState.Clear();
        if (bulkImportModel.uploadedFile != null && bulkImportModel.uploadedFile == null && bulkImportModel.uploadedFile.Count <= 0)
        {
            bulkImportModel.FileUploadStatus = "No File Selected";
            bulkImportModel.databases.Clear();
            bulkImportModel.servers.Clear();
        }
        else
        {
            try
            {
                using var stream = new MemoryStream();
                var servers = _dataProvider.Server.GetServerList().Result.ToList();
                var businessServiceList = _dataProvider.BusinessService.GetBusinessServiceList().Result.ToList();
                var formTypeCategoryLists = _dataProvider.FormMapping.GetFormMappingList().Result.Where(a => a.Name == "Database").ToList();

                if (businessServiceList.Count <= 0 || servers.Count <= 0 || formTypeCategoryLists.Count <= 0)
                {
                    bulkImportModel.FileUploadStatus = "There is No Records in Sites / Business Services / Database types to Save Server Details.";
                    if (bulkImportModel.servers != null)
                    {
                        bulkImportModel.servers.Clear();
                    }
                }
                else
                {
                    if (bulkImportModel.uploadedFile != null) bulkImportModel.uploadedFile[0].CopyTo(stream);
                    stream.Position = 0;
                    using IExcelDataReader reader = ExcelReaderFactory.CreateReader(stream);
                    var excelData = new ExcelDataSetConfiguration
                    {
                        ConfigureDataTable = _ => new ExcelDataTableConfiguration
                        {
                            UseHeaderRow = true
                        }
                    };

                    var dataSet = reader.AsDataSet(excelData);

                    var dataTable = dataSet.Tables[0];

                    var databases = new List<Database>();

                    foreach (DataRow item in dataTable.Rows)
                    {

                        string serverName, serverId;
                        var serverTemp = servers.Where(a => a.Name.Equals(item["ServerName"].ToString())).ToList();
                        if (serverTemp.Any())
                        {
                            serverName = serverTemp.First().Name;
                            if (string.IsNullOrEmpty(serverName))
                            {
                                continue;
                            }

                            serverId = serverTemp.First().Id;
                        }
                        else
                        {
                            continue;
                        }


                        string businessServiceName, businessServiceId;
                        var bsTemp = businessServiceList.Where(a => a.Name.Equals(item["BusinessServiceName"].ToString())).ToList();
                        if (bsTemp.Any())
                        {
                            businessServiceName = bsTemp.First().Name;
                            if (string.IsNullOrEmpty(businessServiceName))
                            {
                                continue;
                            }

                            businessServiceId = bsTemp.First().Id;
                        }
                        else
                        {
                            continue;
                        }


                        string formTypeId;
                        var dbTypeTemp = new FormTypeCategoryListVm();
                        foreach (FormTypeCategoryListVm formType in formTypeCategoryLists)
                        {
                            if (formType.FormTypeName == item["DatabaseType"].ToString())
                            {
                                dbTypeTemp = formType;
                            }
                        }

                        if (!string.IsNullOrEmpty(dbTypeTemp.FormTypeName))
                        {
                            formTypeId = dbTypeTemp.FormTypeId;
                        }
                        else
                        {
                            continue;
                        }
                        databases.Add(new Database
                        {

                            Name = item["Name"].ToString(),

                            ServerId = serverId,
                            ServerName = serverName,
                            BusinessServiceId = businessServiceId,
                            BusinessServiceName = businessServiceName,
                            CompanyId = LoggedInUserCompanyId,
                            DatabaseType = item["DatabaseType"] + "|" + formTypeId,
                            Properties = "",
                            LicenseKey = item["LicenseKey"].ToString(),

                        });
                    }

                    bulkImportModel.databases = databases;
                    bulkImportModel.FileUploadStatus = "File Uploaded Successfully";
                }
            }
            catch (Exception)
            {
                bulkImportModel.FileUploadStatus = "Error Occurred While Reading File, Make sure that Uploaded File is Filled Correct Data.";
                if (bulkImportModel.databases != null)
                {
                    bulkImportModel.servers.Clear();
                    bulkImportModel.databases.Clear();
                }
            }
        }

        return Json(bulkImportModel.databases);

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Download_Template(string templateType)
    {
        string name = "Server";
        try
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("Servers");

            int column = 0;
            string[] serverColumns = { "Name", "SiteName", "BusinessServiceName", "RoleType", "ServerType", "OSType", "Version", "LicenseKey" };

            if (serverColumns.Any())
            {
                foreach (var rp in serverColumns)
                {
                    worksheet.Cell(1, column + 1).Value = rp;
                    worksheet.Cell(1, column + 1).Style.Font.FontSize = 11;
                    worksheet.Cell(1, column + 1).Style.Font.Bold = true;
                    worksheet.Cell(1, column + 1).Style.Font.FontColor = XLColor.White;
                    worksheet.Cell(1, column + 1).WorksheetColumn().Width = 25;
                    worksheet.Cell(1, column + 1).Style.Fill.BackgroundColor = XLColor.Blue;
                    worksheet.Cell(1, column + 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
                    worksheet.Cell(1, column + 1).Style.Alignment.Vertical = XLAlignmentVerticalValues.Top;
                    worksheet.Cell(1, column + 1).Style.Alignment.WrapText = true;
                    worksheet.Range(1, column + 1, 200, column + 1).Style.Protection.Locked = true; // Lock the column
                    column++;
                }
            }

            var site = _dataProvider.Site.GetSiteByCompanyId(LoggedInUserCompanyId);
            var sbSiteNames = new List<string>();
            if (site != null)
            {
                sbSiteNames = site.Result.Select(s => s.Name).ToList();
            }

            var businessServiceNames = _dataProvider.BusinessService.GetBusinessServiceList();
            var sbBusinessServiceNames = new List<string>();
            if (businessServiceNames != null)
            {
                sbBusinessServiceNames = businessServiceNames.Result
                    .Where(a => a.CompanyId == LoggedInUserCompanyId)
                    .Select(bs => bs.Name)
                    .ToList();
            }

            var serverType = _dataProvider.ServerSubType.GetServerSubTypeList();
            var sbServerType = new List<string>();
            if (serverType != null)
            {
                sbServerType = serverType.Result.Select(st => st.Name).ToList();
            }

            var sbOSType = new List<string>();
            var sbVersion = new List<string>();
            var componentType = _dataProvider.ComponentType.GetComponentTypeListByName(name);

            if (componentType != null)
            {
                foreach (var st in componentType.Result)
                {
                    string prop = st.Properties;

                    if (prop != null)
                    {
                        var osInfo = JsonConvert.DeserializeObject<OperatingSystemInfo>(prop);

                        sbOSType.Add(osInfo.Name);
                        if (!string.IsNullOrEmpty(osInfo.Version))
                        {
                            sbVersion.AddRange(osInfo.Version.Replace("[", "").Replace("]", "").Replace("\\", "").Split(',').Select(version => version.Replace("\"", "")));
                        }
                    }
                }

                sbOSType = sbOSType.Distinct().ToList();
                sbVersion = sbVersion.Distinct().ToList();
            }

            var licenceKeys = _dataProvider.LicenseManager.GetAllPoNumbers();
            var sbLicenceKeys = new List<string>();
            if (licenceKeys != null)
            {
                sbLicenceKeys = licenceKeys.Result.Select(s => s.PoNumber).ToList();
            }

            // Create a hidden worksheet
            var hiddenWorksheet = workbook.AddWorksheet("HiddenSheet1");
            hiddenWorksheet.Visibility = XLWorksheetVisibility.VeryHidden;

            for (int i = 1; i < sbSiteNames.Count; i++)
            {
                hiddenWorksheet.Cell(i, 1).Value = sbSiteNames[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation1 = worksheet.Range("B1:B1000").CreateDataValidation();
            dataValidation1.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 1), hiddenWorksheet.Cell(sbSiteNames.Count, 1)));



            // Fill the hidden worksheet with values
            for (int i = 1; i < sbBusinessServiceNames.Count; i++)
            {
                hiddenWorksheet.Cell(i, 2).Value = sbBusinessServiceNames[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation2 = worksheet.Range("C1:C1000").CreateDataValidation();
            dataValidation2.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 2), hiddenWorksheet.Cell(sbBusinessServiceNames.Count, 2)));

            // Add data validation for the "RoleType" column (D column)
            var roleTypeValidation = worksheet.Range("D2:D1000").CreateDataValidation();
            roleTypeValidation.List("Application, Database");
            roleTypeValidation.ShowErrorMessage = true;
            roleTypeValidation.ErrorTitle = "Invalid Entry";
            roleTypeValidation.ErrorMessage = "Please select RoleType.";


            // Fill the hidden worksheet with values
            for (int i = 1; i < sbServerType.Count; i++)
            {
                hiddenWorksheet.Cell(i, 3).Value = sbServerType[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation3 = worksheet.Range("E1:E1000").CreateDataValidation();
            dataValidation3.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 3), hiddenWorksheet.Cell(sbServerType.Count, 3)));


            // Fill the hidden worksheet with values
            for (int i = 1; i < sbOSType.Count; i++)
            {
                hiddenWorksheet.Cell(i, 4).Value = sbOSType[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation4 = worksheet.Range("F1:F1000").CreateDataValidation();
            dataValidation4.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 4), hiddenWorksheet.Cell(sbOSType.Count, 4)));


            // Fill the hidden worksheet with values
            for (int i = 1; i < sbVersion.Count; i++)
            {
                hiddenWorksheet.Cell(i, 5).Value = sbVersion[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation5 = worksheet.Range("G1:G1000").CreateDataValidation();
            dataValidation5.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 5), hiddenWorksheet.Cell(sbVersion.Count, 5)));



            // Fill the hidden worksheet with values
            for (int i = 1; i < sbLicenceKeys.Count; i++)
            {
                hiddenWorksheet.Cell(i, 6).Value = sbLicenceKeys[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation6 = worksheet.Range("H1:H1000").CreateDataValidation();
            dataValidation6.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 6), hiddenWorksheet.Cell(sbLicenceKeys.Count, 6)));


            // Save the Excel file to a memory stream
            using MemoryStream stream = new MemoryStream();

            workbook.SaveAs(stream);

            // Reset the position of the stream to the beginning
            stream.Position = 0;

            // Optionally, save the file to a physical path
            var reportPath = _config.GetSection("ExcelBlankTemplatePath").Value;
            var tempFileName = $"{name}_{DateTime.Now.ToString("ddMMyyyy_HH_mm_ss")}";
            if (reportPath != null)
            {
                string filePath = Path.Combine(reportPath, tempFileName + ".xlsx");
                await System.IO.File.WriteAllBytesAsync(filePath, stream.ToArray());
            }

            // Return the file name as a JSON response
            return Json(new { FileName = tempFileName });
        }


        catch (Exception)
        {
            return Content("<script>function(){alert('Failed To Download Template...');}</script>", "text/html");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> download_TemplateDatabase(string templateType)
    {
        string name = "Database";
        try
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("DataBases");

            int column = 0;
            string[] serverColumns = { "Name", "DatabaseType", "BusinessServiceName", "ServerName", "LicenseKey" };

            if (serverColumns.Any())
            {
                foreach (var rp in serverColumns)
                {
                    worksheet.Cell(1, column + 1).Value = rp;
                    worksheet.Cell(1, column + 1).Style.Font.FontSize = 11;
                    worksheet.Cell(1, column + 1).Style.Font.Bold = true;
                    worksheet.Cell(1, column + 1).Style.Font.FontColor = XLColor.White;
                    worksheet.Cell(1, column + 1).WorksheetColumn().Width = 25;
                    worksheet.Cell(1, column + 1).Style.Fill.BackgroundColor = XLColor.MediumBlue;
                    worksheet.Cell(1, column + 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;
                    worksheet.Cell(1, column + 1).Style.Alignment.Vertical = XLAlignmentVerticalValues.Top;
                    worksheet.Cell(1, column + 1).Style.Alignment.WrapText = true;
                    worksheet.Range(1, column + 1, 200, column + 1).Style.Protection.Locked = true; // Lock the column
                    column++;
                }
            }

            var dbTypeNames = _dataProvider.FormMapping.GetFormMappingList();
            //  var versionLists = nameVersionList.Where(x => x.Name == "Database").ToList();
            var sbDBTypeNames = new List<string>();
            if (dbTypeNames != null)
            {
                sbDBTypeNames = dbTypeNames.Result.Where(x => x.Name == "Database").Select(dbt => dbt.FormTypeName).ToList();
                sbDBTypeNames = sbDBTypeNames.Distinct().ToList();
            }


            var serverNames = _dataProvider.Server.GetServerList();
            var sbServerNamesNames = new List<string>();
            if (serverNames != null)
            {
                sbServerNamesNames = serverNames.Result
                    .Where(a => a.CompanyId == LoggedInUserCompanyId)
                    .Select(bs => bs.Name)
                    .ToList();
            }

            var businessServiceNames = _dataProvider.BusinessService.GetBusinessServiceList();
            var sbBusinessServiceNames = new List<string>();
            if (businessServiceNames != null)
            {
                sbBusinessServiceNames = businessServiceNames.Result
                    .Where(a => a.CompanyId == LoggedInUserCompanyId)
                    .Select(bs => bs.Name)
                    .ToList();
            }



            var licenceKeys = _dataProvider.LicenseManager.GetAllPoNumbers();
            var sbLicenceKeys = new List<string>();
            if (licenceKeys != null)
            {
                sbLicenceKeys = licenceKeys.Result.Select(s => s.PoNumber).ToList();
            }


            var hiddenWorksheet = workbook.AddWorksheet("HiddenSheet1");
            hiddenWorksheet.Visibility = XLWorksheetVisibility.VeryHidden;

            for (int i = 1; i < sbDBTypeNames.Count; i++)
            {
                hiddenWorksheet.Cell(i, 1).Value = sbDBTypeNames[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation1 = worksheet.Range("B1:B1000").CreateDataValidation();
            dataValidation1.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 1), hiddenWorksheet.Cell(sbDBTypeNames.Count, 1)));



            // Fill the hidden worksheet with values
            for (int i = 1; i < sbBusinessServiceNames.Count; i++)
            {
                hiddenWorksheet.Cell(i, 2).Value = sbBusinessServiceNames[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation2 = worksheet.Range("C1:C1000").CreateDataValidation();
            dataValidation2.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 2), hiddenWorksheet.Cell(sbBusinessServiceNames.Count, 2)));


            // Fill the hidden worksheet with values
            for (int i = 1; i < sbServerNamesNames.Count; i++)
            {
                hiddenWorksheet.Cell(i, 3).Value = sbServerNamesNames[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation3 = worksheet.Range("D1:D1000").CreateDataValidation();
            dataValidation3.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 3), hiddenWorksheet.Cell(sbServerNamesNames.Count, 3)));



            // Fill the hidden worksheet with values
            for (int i = 1; i < sbLicenceKeys.Count; i++)
            {
                hiddenWorksheet.Cell(i, 4).Value = sbLicenceKeys[i - 1];
            }
            // Apply data validation using the range of the hidden worksheet column
            var dataValidation6 = worksheet.Range("E1:E1000").CreateDataValidation();
            dataValidation6.List(hiddenWorksheet.Range(hiddenWorksheet.Cell(1, 4), hiddenWorksheet.Cell(sbLicenceKeys.Count, 4)));

            // Save the Excel file to a memory stream
            using MemoryStream stream = new MemoryStream();
            workbook.SaveAs(stream);

            // Reset the position of the stream to the beginning
            stream.Position = 0;

            // Optionally, save the file to a physical path
            var reportPath = _config.GetSection("ExcelBlankTemplatePath").Value;
            var tempFileName = $"{name}_{DateTime.Now.ToString("ddMMyyyy_HH_mm_ss")}";
            if (reportPath == null) return Json(new { FileName = tempFileName });
            var filePath = Path.Combine(reportPath, tempFileName + ".xlsx");
            await System.IO.File.WriteAllBytesAsync(filePath, stream.ToArray());

            // Return the file name as a JSON response
            return Json(new { FileName = tempFileName });
        }


        catch (Exception)
        {
            return Content("<script>function(){alert('Failed To Download Template...');}</script>", "text/html");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public JsonResult Save_ServerRecord([FromBody] Server record)
    {
        var success = false;
        var message = "Record saved successfully.";

        try
        {
            var formModel = _mapper.Map<CreateServerCommand>(record);
            var response = _dataProvider.Server.CreateAsync(formModel);
            TempData.NotifySuccess(response.Result.Message);

            success = true;
        }
        catch (Exception ex)
        {
            message = "Error occurred while saving server. error : " + ex.Message;
        }

        return Json(new { Success = success, Message = message });
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public JsonResult Save_DatabaseRecord([FromBody] Database record)
    {
        var database = record;

        var success = false;
        var message = "Record saved successfully.";

        try
        {
            var formModel = _mapper.Map<CreateDatabaseCommand>(database);
            var response = _dataProvider.Database.CreateAsync(formModel);
            TempData.NotifySuccess(response.Result.Message);

            success = true;
        }
        catch (Exception ex)
        {
            message = "Error occurred while saving database. Error : " + ex.Message;
        }


        return Json(new { Success = success, Message = message });
    }

    [HttpGet]
    public IActionResult DownloadFile(string fileName)
    {
        var filePath = Path.Combine(_config.GetSection("ExcelBlankTemplatePath").Value!, fileName);

        var fileBytes = System.IO.File.ReadAllBytes(filePath);

        return File(fileBytes, "application/vnd.ms-excel", fileName);
    }

    [HttpGet]
    public async Task<JsonResult> GetBulkImportOperationsrunningStatus()
    {
        try
        {
            var GetBulkImportOperationsRunningStatus = await _dataProvider.BulkImportOperation.GetBulkImportOperationsrunningStatus();
            return Json(new { success = true, data = GetBulkImportOperationsRunningStatus });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on replication page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    public class OperatingSystemInfo
    {
        public string Name { get; set; }
        public string Version { get; set; }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> SaveBulkImport([FromBody] CreateBulkImportOperationCommand bulkImportCommand)
    {
        try
        {
            foreach (var bulkImportOperation in bulkImportCommand.BulkImportOperationList)
            {
                await ProcessBulkItems(bulkImportOperation.ServerList, ProcessServer);
                await ProcessBulkItems(bulkImportOperation.DatabaseList, ProcessDatabase);
                await ProcessBulkItems(bulkImportOperation.ReplicationList, ProcessReplication);

                await ProcessItems(bulkImportOperation.InfraObject, ProcessInfraObject);
            }

            var response = await _dataProvider.BulkImportOperation.CreateAsync(bulkImportCommand);


            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [AntiXss]
    public async Task<JsonResult> GetBulkImportOperation(string id)
    {
        try
        {
            var response = await _dataProvider.BulkImportOperation.GetByReferenceId(id);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateBulkImportOperation(UpdateBulkImportOperationCommand updateBulkImportOperationCommand)
    {
        try
        {
            var response = await _dataProvider.BulkImportOperation.UpdateAsync(updateBulkImportOperationCommand);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [AntiXss]
    public async Task<JsonResult> GetBulkImportOperationGroupByOperationId(string id)
    {
        try
        {
            var response = await _dataProvider.BulkImportOperationGroup.GetBulkImportOperationGroupByOperationId(id);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [AntiXss]
    public async Task<JsonResult> GetBulkImportOperationGroup(string id)
    {
        try
        {
            var response = await _dataProvider.BulkImportOperationGroup.GetByReferenceId(id);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateBulkImportGroup(UpdateBulkImportOperationGroupCommand updateBulkImportOperationGroupCommand)
    {
        try
        {
            var response = await _dataProvider.BulkImportOperationGroup.UpdateAsync(updateBulkImportOperationGroupCommand);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateBulkImportOperationGroup(List<UpdateBulkImportOperationGroupCommand> updateBulkImportOperationGroupCommand)
    {
        try
        {
            var response = new BaseResponse();
            foreach (var bulkImportOperation in updateBulkImportOperationGroupCommand)
            {
                response = await _dataProvider.BulkImportOperationGroup.UpdateAsync(bulkImportOperation);
            }
            
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }       
    }

    [HttpGet]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> GetBulkImportActionResult(string operationId, string operationGroupId)
    {
        try
        {
            var response = await _dataProvider.BulkImportActionResult.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [AntiXss]
    public async Task<JsonResult> RollBackBulkImportAction(RollBackBulkImportCommand rollBackBulkImportCommand)
    {
        try
        {
            var response = await _dataProvider.BulkImport.RollBackBulkImportAction(rollBackBulkImportCommand);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [AntiXss]
    public async Task<JsonResult> GetSiteByName(string name)
    {
        try
        {
            if (name.IsNullOrWhiteSpace())
            {
                return Json(new { success = true, data = "Site name null or empty." });
            }

            var response = (await _dataProvider.Site.GetSites())
                .Where(x => x.Name.Trim().ToLower().Equals(name.Trim().ToLower()));

            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateBulkImportActionResult(UpdateBulkImportActionResultCommand updateBulkImportActionResultCommand)
    {
        try
        {
            var response = await _dataProvider.BulkImportActionResult.UpdateAsync(updateBulkImportActionResultCommand);
            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> BulkImportValidation([FromBody] CreateBulkImportValidatorCommand bulkImportCommand)
    {
        try
        {
            foreach (var bulkImportOperation in bulkImportCommand.BulkImportOperationList)
            {
                await ProcessBulkItems(bulkImportOperation.ServerList, ProcessServer);
                await ProcessBulkItems(bulkImportOperation.DatabaseList, ProcessDatabase);
                await ProcessBulkItems(bulkImportOperation.ReplicationList, ProcessReplication);
                await ProcessItems(bulkImportOperation.InfraObject, ProcessInfraObject);

            }

            var response = await _dataProvider.BulkImportOperation.CreateBulkImportValidator(bulkImportCommand);


            return Json(new { success = true, data = response });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on bulk import page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }



    #region Private Methods

    private async Task ProcessItems<T>(T items, Func<T, Task> processItem)
    {
        await processItem(items);
    }


    private async Task ProcessBulkItems<T>(List<T> items, Func<T, Task> processItem)
    {
        foreach (var item in items)
        {
            await processItem(item);
        }
    }

    private async Task ProcessInfraObject(CreateBulkDataInfraObjectListCommand infraObject)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var businessFunctionList = await _dataProvider.BusinessFunction.GetBusinessFunctionNames();

        var databaseComponents = infraObject.TypeName.ToLower().Equals("db")
            ? await _dataProvider.ComponentType.GetComponentTypeListByName("DataBase")
            : null;

        var replicationComponents =
            await _dataProvider.ComponentType.GetComponentTypeListByName("Replication");

        var replicationMaster = await _dataProvider.ReplicationMaster.GetReplicationMasterList();


        infraObject.CompanyId = companyId;
        infraObject.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(infraObject.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        infraObject.BusinessFunctionId = businessFunctionList.FirstOrDefault(x => x.Name.Equals(infraObject.BusinessFunctionName, StringComparison.OrdinalIgnoreCase))?.Id;
        var dbType = databaseComponents?.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(infraObject.SubType, StringComparison.OrdinalIgnoreCase));
        infraObject.SubTypeId = dbType?.Id ?? string.Empty;
        infraObject.SubType = dbType?.ComponentName ?? string.Empty;
        var replicationType = replicationComponents?.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(infraObject.ReplicationTypeName, StringComparison.OrdinalIgnoreCase));
        infraObject.ReplicationTypeId = replicationType?.Id ?? string.Empty;
        var replicationCategory = replicationMaster?.FirstOrDefault(x => x.Name.Equals(infraObject.ReplicationCategoryType, StringComparison.OrdinalIgnoreCase));
        infraObject.ReplicationCategoryTypeId = replicationCategory?.Id ?? string.Empty;
    }

    private async Task ProcessServer(CreateBulkDataServerListCommand server)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var sites = await _dataProvider.Site.GetSites();
        var serverSubtypes = await _dataProvider.ServerSubType.GetServerSubTypeList();
        var serverComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("Server");

        server.CompanyId = companyId;
        server.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(server.LicenseKey)).Id;
        server.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(server.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        server.SiteId = sites.FirstOrDefault(x => x.Name.Equals(server.SiteName, StringComparison.OrdinalIgnoreCase))?.Id;

        var roleType = await _dataProvider.ServerType.GetServerTypeListByName(server.RoleType);
        server.RoleType = roleType.FirstOrDefault()?.Name;
        server.RoleTypeId = roleType.FirstOrDefault()?.Id;

        var subtypeDtl = serverSubtypes.FirstOrDefault(x => x.Name.Equals(server.ServerType, StringComparison.OrdinalIgnoreCase));
        server.ServerType = subtypeDtl?.Name ?? server.ServerType;
        server.ServerTypeId = subtypeDtl?.Id ?? string.Empty;

        var osDtl = serverComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(server.OSType, StringComparison.OrdinalIgnoreCase));
        server.OSType = osDtl?.ComponentName ?? server.OSType;
        server.OSTypeId = osDtl?.Id ?? string.Empty;
    }

    private async Task ProcessDatabase(CreateBulkDataDataBaseListCommand database)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var databaseComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("DataBase");

        database.CompanyId = companyId;
        database.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(database.LicenseKey)).Id;
        database.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(database.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        database.ServerId = Guid.NewGuid().ToString();

        var dbType = databaseComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(database.DatabaseType, StringComparison.OrdinalIgnoreCase));
        database.DatabaseType = dbType?.ComponentName ?? database.DatabaseType;
        database.DatabaseTypeId = dbType?.Id ?? string.Empty;
    }

    private async Task ProcessReplication(CreateBulkDataReplicationListCommand replication)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var sites = await _dataProvider.Site.GetSites();
        var replicationComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("Replication");

        replication.CompanyId = companyId;
        replication.SiteId = sites.FirstOrDefault(x => x.Name.Equals(replication.SiteName, StringComparison.OrdinalIgnoreCase))?.Id;
        replication.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(replication.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;

        var dbType = replicationComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(replication.Type, StringComparison.OrdinalIgnoreCase));
        replication.Type = dbType?.ComponentName ?? replication.Type;
        replication.TypeId = dbType?.Id ?? string.Empty;

        if (!replication.LicenseKey.ToLower().Equals("na"))
        {
            replication.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(replication.LicenseKey)).Id;
        }
    }

    #endregion
    #region Validation
    private async Task ProcessServer(CreateBulkImportComponentServerCommand server)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var sites = await _dataProvider.Site.GetSites();
        var serverSubtypes = await _dataProvider.ServerSubType.GetServerSubTypeList();
        var serverComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("Server");

        server.CompanyId = companyId;
        server.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(server.LicenseKey)).Id;
        server.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(server.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        server.SiteId = sites.FirstOrDefault(x => x.Name.Equals(server.SiteName, StringComparison.OrdinalIgnoreCase))?.Id;

        var roleType = await _dataProvider.ServerType.GetServerTypeListByName(server.RoleType);
        server.RoleType = roleType.FirstOrDefault()?.Name;
        server.RoleTypeId = roleType.FirstOrDefault()?.Id;

        var subtypeDtl = serverSubtypes.FirstOrDefault(x => x.Name.Equals(server.ServerType, StringComparison.OrdinalIgnoreCase));
        server.ServerType = subtypeDtl?.Name ?? server.ServerType;
        server.ServerTypeId = subtypeDtl?.Id ?? string.Empty;

        var osDtl = serverComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(server.OSType, StringComparison.OrdinalIgnoreCase));
        server.OSType = osDtl?.ComponentName ?? server.OSType;
        server.OSTypeId = osDtl?.Id ?? string.Empty;
    }

    private async Task ProcessDatabase(CreateBulkImportComponentDataBaseCommand database)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var databaseComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("DataBase");

        database.CompanyId = companyId;
        database.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(database.LicenseKey)).Id;
        database.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(database.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        database.ServerId = Guid.NewGuid().ToString();

        var dbType = databaseComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(database.DatabaseType, StringComparison.OrdinalIgnoreCase));
        database.DatabaseType = dbType?.ComponentName ?? database.DatabaseType;
        database.DatabaseTypeId = dbType?.Id ?? string.Empty;
    }

    private async Task ProcessReplication(CreateBulkImportComponentReplicationCommand replication)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var sites = await _dataProvider.Site.GetSites();
        var replicationComponents = await _dataProvider.ComponentType.GetComponentTypeListByName("Replication");

        replication.CompanyId = companyId;
        replication.SiteId = sites.FirstOrDefault(x => x.Name.Equals(replication.SiteName, StringComparison.OrdinalIgnoreCase))?.Id;
        replication.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(replication.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;

        var dbType = replicationComponents.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(replication.Type, StringComparison.OrdinalIgnoreCase));
        replication.Type = dbType?.ComponentName ?? replication.Type;
        replication.TypeId = dbType?.Id ?? string.Empty;

        if (!replication.LicenseKey.ToLower().Equals("na"))
        {
            replication.LicenseId = (await _dataProvider.LicenseManager.GetLicenseDetailsByPoNumber(replication.LicenseKey)).Id;
        }
    }

    private async Task ProcessInfraObject(CreateBulkImportComponentInfraObjectCommand infraObject)
    {
        var companyId = WebHelper.UserSession.CompanyId;
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
        var businessFunctionList = await _dataProvider.BusinessFunction.GetBusinessFunctionNames();

        var databaseComponents = infraObject.TypeName.ToLower().Equals("db")
            ? await _dataProvider.ComponentType.GetComponentTypeListByName("DataBase")
            : null;

        var replicationComponents =
            await _dataProvider.ComponentType.GetComponentTypeListByName("Replication");

        var replicationMaster = await _dataProvider.ReplicationMaster.GetReplicationMasterList();


        infraObject.CompanyId = companyId;
        infraObject.BusinessServiceId = businessServiceList.FirstOrDefault(x => x.Name.Equals(infraObject.BusinessServiceName, StringComparison.OrdinalIgnoreCase))?.Id;
        infraObject.BusinessFunctionId = businessFunctionList.FirstOrDefault(x => x.Name.Equals(infraObject.BusinessFunctionName, StringComparison.OrdinalIgnoreCase))?.Id;
        var dbType = databaseComponents?.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(infraObject.SubType, StringComparison.OrdinalIgnoreCase));
        infraObject.SubTypeId = dbType?.Id ?? string.Empty;
        infraObject.SubType = dbType?.ComponentName ?? string.Empty;
        var replicationType = replicationComponents?.FirstOrDefault(x => x.ComponentName.IsNotNullOrWhiteSpace() && x.ComponentName.Equals(infraObject.ReplicationTypeName, StringComparison.OrdinalIgnoreCase));
        infraObject.ReplicationTypeId = replicationType?.Id ?? string.Empty;
        var replicationCategory = replicationMaster?.FirstOrDefault(x => x.Name.Equals(infraObject.ReplicationCategoryType, StringComparison.OrdinalIgnoreCase));
        infraObject.ReplicationCategoryTypeId = replicationCategory?.Id ?? string.Empty;
    }


    #endregion


}