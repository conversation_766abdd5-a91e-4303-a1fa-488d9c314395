﻿namespace ContinuityPatrol.Application.Features.MssqlNativeLogShippingMonitorLog.Queries.GetList;

public class GetMssqlNativeLogShippingMonitorLogListQueryHandler : IRequestHandler<
    GetMssqlNativeLogShippingMonitorLogListQuery, List<MssqlNativeLogShippingMonitorLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlNativeLogShippingMonitorLogRepository _mssqlNativeLogShippingMonitorLogRepository;

    public GetMssqlNativeLogShippingMonitorLogListQueryHandler(
        IMssqlNativeLogShippingMonitorLogRepository mssqlNativeLogShippingMonitorLogRepository, IMapper mapper)
    {
        _mssqlNativeLogShippingMonitorLogRepository = mssqlNativeLogShippingMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<MssqlNativeLogShippingMonitorLogListVm>> Handle(
        GetMssqlNativeLogShippingMonitorLogListQuery request, CancellationToken cancellationToken)
    {
        var mssqlNativeLogShippingMonitorLogList = await _mssqlNativeLogShippingMonitorLogRepository.ListAllAsync();

        return mssqlNativeLogShippingMonitorLogList.Count <= 0
            ? new List<MssqlNativeLogShippingMonitorLogListVm>()
            : _mapper.Map<List<MssqlNativeLogShippingMonitorLogListVm>>(mssqlNativeLogShippingMonitorLogList);
    }
}