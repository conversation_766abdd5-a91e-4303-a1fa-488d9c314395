using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessServiceHealthStatusControllerTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly BusinessServiceHealthStatusFixture _businessServiceHealthStatusFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessServiceHealthStatusController _controller;

    public BusinessServiceHealthStatusControllerTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusFixture = businessServiceHealthStatusFixture;

        var testBuilder = new ControllerTestBuilder<BusinessServiceHealthStatusController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessServiceHealthStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GeBusinessServiceHealthStatusList_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceHealthStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusListVm);

        // Act
        var result = await _controller.GeBusinessServiceHealthStatusList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var healthStatuses = Assert.IsAssignableFrom<List<BusinessServiceHealthStatusListVm>>(okResult.Value);
        Assert.Equal(3, healthStatuses.Count);
    }

    [Fact]
    public async Task GetBusinessServiceHealthStatusById_ReturnsExpectedDetail()
    {
        // Arrange
        var healthStatusId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceHealthStatusDetailQuery>(q => q.Id == healthStatusId), default))
            .ReturnsAsync(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusDetailVm);

        // Act
        var result = await _controller.GetBusinessServiceHealthStatusById(healthStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var healthStatus = Assert.IsType<BusinessServiceHealthStatusDetailVm>(okResult.Value);
        Assert.NotNull(healthStatus);
    }

    [Fact]
    public async Task CreateBusinessServiceHealthStatus_Returns201Created()
    {
        // Arrange
        var command = _businessServiceHealthStatusFixture.CreateBusinessServiceHealthStatusCommand;
        var expectedMessage = "BusinessServiceHealthStatus has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceHealthStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceHealthStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceHealthStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthStatus_ReturnsOk()
    {
        // Arrange
        var command = _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand;
        var expectedMessage = "BusinessServiceHealthStatus has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceHealthStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceHealthStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceHealthStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessServiceHealthStatus_ReturnsOk()
    {
        // Arrange
        var healthStatusId = Guid.NewGuid().ToString();
        var expectedMessage = "BusinessServiceHealthStatus has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessServiceHealthStatusCommand>(c => c.Id == healthStatusId), default))
            .ReturnsAsync(new DeleteBusinessServiceHealthStatusResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBusinessServiceHealthStatus(healthStatusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessServiceHealthStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetPaginatedBusinessServiceHealthStatus_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetBusinessServiceHealthStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusListVm.Take(2).ToList();
        var expectedResults = PaginatedResult<BusinessServiceHealthStatusListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessServiceHealthStatusPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedBusinessServiceHealthStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<BusinessServiceHealthStatusListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(2, paginatedResult.TotalCount);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task GetBusinessServiceHealthStatusById_HandlesInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessServiceHealthStatusById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessServiceHealthStatus_ValidatesConfiguredCount()
    {
        // Arrange
        var command = new CreateBusinessServiceHealthStatusCommand
        {
            ConfiguredCount = -1, // Negative count should cause validation error
            DRReadyCount = 10,
            DRNotReadyCount = 5,
            ProblemState = "Test State"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("ConfiguredCount must be non-negative"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceHealthStatus(command));
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthStatus_ValidatesHealthStatusExists()
    {
        // Arrange
        var command = new UpdateBusinessServiceHealthStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            ConfiguredCount = 50,
            DRReadyCount = 45,
            DRNotReadyCount = 5,
            ProblemState = "Updated State"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BusinessServiceHealthStatus not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateBusinessServiceHealthStatus(command));
    }

    [Fact]
    public async Task CreateBusinessServiceHealthStatus_HandlesComplexHealthMetrics()
    {
        // Arrange
        var command = new CreateBusinessServiceHealthStatusCommand
        {
            ConfiguredCount = 250,
            DRReadyCount = 220,
            DRNotReadyCount = 30,
            ProblemState = "Enterprise Infrastructure - Multiple DR sites configured with automated failover capabilities. Minor issues detected in secondary site connectivity."
        };

        var expectedMessage = "BusinessServiceHealthStatus has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessServiceHealthStatusResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBusinessServiceHealthStatus(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessServiceHealthStatusResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessServiceHealthStatus_HandlesStatusImprovement()
    {
        // Arrange
        var command = new UpdateBusinessServiceHealthStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            ConfiguredCount = 250,
            DRReadyCount = 248,
            DRNotReadyCount = 2,
            ProblemState = "All systems operational - DR readiness at 99.2% with only minor maintenance items remaining"
        };

        var expectedMessage = "BusinessServiceHealthStatus has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateBusinessServiceHealthStatusResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateBusinessServiceHealthStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessServiceHealthStatusResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task CreateBusinessServiceHealthStatus_ValidatesDRReadyCount()
    {
        // Arrange
        var command = new CreateBusinessServiceHealthStatusCommand
        {
            ConfiguredCount = 50,
            DRReadyCount = 60, // DR Ready count cannot exceed configured count
            DRNotReadyCount = 5,
            ProblemState = "Test State"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("DRReadyCount cannot exceed ConfiguredCount"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBusinessServiceHealthStatus(command));
    }
}
