﻿using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class WorkflowProfileInfoViewFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<WorkflowProfileInfoView> WorkflowProfileInfoViews { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    public WorkflowProfileInfoViewFixture()
    {
        WorkflowProfileInfoViews = AutoWorkflowProfileInfoViewFixture.Create<List<WorkflowProfileInfoView>>();
        UserActivities = AutoWorkflowProfileInfoViewFixture.Create<List<UserActivity>>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowProfileInfoProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowProfileInfoViewFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<WorkflowProfileInfoView>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }
    public void Dispose()
    {

    }
}