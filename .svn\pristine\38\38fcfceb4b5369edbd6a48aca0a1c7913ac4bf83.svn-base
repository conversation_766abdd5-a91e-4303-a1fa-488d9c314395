﻿using ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.UpdateStatus;

namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;

public class UpdateAirGapStatusCommandHanlder : IRequestHandler<UpdateAirGapStatusCommand, UpdateAirGapStatusResponse>
{
    private readonly ICyberAirGapStatusRepository _cyberAirGapStatusRepository;
    private readonly IPublisher _publisher;

    public UpdateAirGapStatusCommandHanlder(IPublisher publisher,
        ICyberAirGapStatusRepository cyberAirGapStatusRepository)
    {
        _publisher = publisher;
        _cyberAirGapStatusRepository = cyberAirGapStatusRepository;
    }

    public async Task<UpdateAirGapStatusResponse> Handle(UpdateAirGapStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _cyberAirGapStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.CyberAirGapStatus), request.Id);

        eventToUpdate.Status = request.Status;

        await _cyberAirGapStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateAirGapStatusResponse
        {
            Message = $"Cyber air gap status has been updated to '{eventToUpdate.Status}' successfully",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(
            new AirGapStatusUpdatedEvent { AirGap = eventToUpdate.AirGapName, status = eventToUpdate.Status },
            cancellationToken);

        return response;
    }
}