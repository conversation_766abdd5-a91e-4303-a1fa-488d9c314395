﻿//using ContinuityPatrol.Application.Contracts.Persistence;
//using ContinuityPatrol.Domain.Entities;
//using ContinuityPatrol.Domain.Extensions;
//using ContinuityPatrol.Persistence.Persistence;
//using ContinuityPatrol.Shared.Core.Contracts.Identity;
//using ContinuityPatrol.Shared.Core.Specifications;
//using ContinuityPatrol.Shared.Core.Wrapper;

//namespace ContinuityPatrol.Persistence.Repositories;

//public class HeatMapLogRepository : BaseRepository<HeatMapLog>, IHeatMapLogRepository
//{
//    private readonly ApplicationDbContext _dbContext;
//    private readonly ILoggedInUserService _loggedInUserService;

//    public HeatMapLogRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
//        dbContext, loggedInUserService)
//    {
//        _dbContext = dbContext;
//        _loggedInUserService = loggedInUserService;
//    }

//    public override async Task<IReadOnlyList<HeatMapLog>> ListAllAsync()
//    {
//        var businessServices = base.ListAllAsync(businessService => businessService.IsActive);

//        var businessServiceDto = MapHeatMapLog(businessServices);

//        return _loggedInUserService.IsAllInfra
//            ? await businessServiceDto.ToListAsync()
//            : AssignedBusinessServices(businessServiceDto);
//    }

//    public override async Task<HeatMapLog> GetByReferenceIdAsync(string id)
//    {
//        var businessServices = base.GetByReferenceIdAsync(id,
//                    dataLags => dataLags.ReferenceId.Equals(id));

//        var businessServiceDto = MapHeatMapLog(businessServices);

//        return _loggedInUserService.IsAllInfra
//            ? await businessServiceDto.FirstOrDefaultAsync()
//            : AssignedBusinessServices(businessServiceDto).FirstOrDefault();
//    }
//    public async Task<PaginatedResult<HeatMapLog>> GetHeatMapLogType(string type, int pageNumber, int pageSize, Specification<HeatMapLog> productFilterSpec,string sortColumn,string sortOrder)
//    {
//        return await (_loggedInUserService.IsAllInfra
//            ? MapHeatMapLog(Entities.Specify(productFilterSpec).Where(x=>x.HeatmapType.Equals(type)).DescOrderById())
//            : MapHeatMapLog(GetPaginatedAssignedBusinessServices(Entities.Specify(productFilterSpec).Where(x => x.HeatmapType.Equals(type)).DescOrderById()))
//            ).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
//    }

//    public IQueryable<HeatMapLog> GetHeatMapLogType(string type)
//    {
//        var heatMap = base.FilterBy(x => x.HeatmapType.Equals(type));

//        var heatMapDto = MapHeatMapLog(heatMap);

//        return _loggedInUserService.IsAllInfra
//            ? heatMapDto
//            : GetPaginatedAssignedBusinessServices(heatMapDto);
//    }


//    public IReadOnlyList<HeatMapLog> AssignedBusinessServices(IQueryable<HeatMapLog> businessServices)
//    {
//        var services = new List<HeatMapLog>();

//        foreach (var businessService in businessServices)
//            if (AssignedEntity.AssignedBusinessServices.Count > 0)
//                services.AddRangeAsync(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
//                                  where businessService.BusinessServiceId == assignedBusinessService.Id
//                                  select businessService);
//        return services;
//    }
//    private IQueryable<HeatMapStatus> GetPaginatedAssignedBusinessServices(IQueryable<HeatMapStatus> businessServices)
//    {
//        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

//        return businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));
//    }
//    private IQueryable<HeatMapLog> MapHeatMapLog(IQueryable<HeatMapLog> heatMapLog)
//    {
//        var mapHeatMapLog = heatMapLog.Select(data => new
//        {
//            HeatMapStatus = data,
//            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessServiceId)),
//            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessFunctionId)),
//            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId)),
//        });

//        var mapHeatMapLogQuery = mapHeatMapLog.Select(result => new HeatMapLog
//        {
//            Id = result.HeatMapStatus.Id,
//            ReferenceId = result.HeatMapStatus.ReferenceId,
//            BusinessServiceId = result.BusinessService.ReferenceId,
//            BusinessServiceName = result.BusinessService.Name,
//            BusinessFunctionId = result.BusinessFunction.ReferenceId,
//            BusinessFunctionName = result.BusinessFunction.Name,
//            InfraObjectId = result.InfraObject.ReferenceId,
//            InfraObjectName = result.InfraObject.Name,
//            EntityId = result.HeatMapStatus.EntityId,
//            HeatmapType = result.HeatMapStatus.HeatmapType,
//            HeatmapStatus = result.HeatMapStatus.HeatmapStatus,
//            IsAffected = result.HeatMapStatus.IsAffected,
//            ErrorMessage = result.HeatMapStatus.ErrorMessage,
//            Properties = result.HeatMapStatus.Properties,
//            IsActive = result.HeatMapStatus.IsActive,
//            CreatedBy = result.HeatMapStatus.CreatedBy,
//            LastModifiedBy = result.HeatMapStatus.LastModifiedBy,
//            LastModifiedDate = result.HeatMapStatus.LastModifiedDate,
//        });

//        return mapHeatMapLogQuery;
//    }
//}