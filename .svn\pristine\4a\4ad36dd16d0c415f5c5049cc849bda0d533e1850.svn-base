﻿namespace ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;

public class CyberJobWorkflowSchedulerListVm
{
    public string Id { get; set; }
    public string JobId { get; set; }
    public string Name { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string CurrentActionId { get; set; }
    [Column(TypeName = "NCLOB")] public string CurrentActionName { get; set; }
    public int IsSchedule { get; set; }
    public int ScheduleType { get; set; }
    public string ScheduleTime { get; set; }
    public string CronExpression { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; }
    public int ConditionActionId { get; set; }
    public string State { get; set; }
    public string Mode { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    [Column(TypeName = "NCLOB")] public string ExceptionMessage { get; set; }
    public string SuccessCount { get; set; }
}
