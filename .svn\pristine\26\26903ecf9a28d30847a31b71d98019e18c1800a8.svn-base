﻿using ContinuityPatrol.Application.Features.Workflow.Events.Lock;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Lock;

public class UpdateWorkflowLockCommandHandler : IRequestHandler<UpdateWorkflowLockCommand, UpdateWorkflowLockResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public UpdateWorkflowLockCommandHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowLockResponse> Handle(UpdateWorkflowLockCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToWorkflowUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        var eventToWorkflowProfileInfoUpdate =
            await _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(request.Id);

        //if (!eventToWorkflowUpdate.CreatedBy.Equals(_loggedInUserService.UserId))
        //{
        //    throw new InvalidException("User Access denied.");
        //}

        if (eventToWorkflowProfileInfoUpdate != null)
        {
            eventToWorkflowProfileInfoUpdate.IsLock = request.IsLock;

            await _workflowProfileInfoRepository.UpdateAsync(eventToWorkflowProfileInfoUpdate);
        }

        if (eventToWorkflowUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        eventToWorkflowUpdate.IsLock = request.IsLock;

        _mapper.Map(request, eventToWorkflowUpdate, typeof(UpdateWorkflowLockCommand),
            typeof(Domain.Entities.Workflow));

        await _workflowRepository.UpdateAsync(eventToWorkflowUpdate);

        await _publisher.Publish(new WorkflowLockEvent
        {
            WorkflowName = eventToWorkflowUpdate.Name,
            IsLocked = request.IsLock
        }, cancellationToken);

        return new UpdateWorkflowLockResponse
        {
            Message = request.IsLock
                ? $"Workflow '{eventToWorkflowUpdate.Name}' locked successfully."
                : $"Workflow '{eventToWorkflowUpdate.Name}' unlocked successfully.",

            WorkflowId = eventToWorkflowUpdate.ReferenceId
        };
    }
}