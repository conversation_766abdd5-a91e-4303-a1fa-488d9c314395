﻿using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Commands
{
    public class UpdateRoboCopyTests
    {
        private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateRoboCopyCommandHandler _handler;

        public UpdateRoboCopyTests()
        {
            _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new UpdateRoboCopyCommandHandler(
                _mockMapper.Object,
                _mockRoboCopyRepository.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldUpdateRoboCopySuccessfully()
        {
            var command = new UpdateRoboCopyCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "UpdatedRoboCopyName"
            };

            var existingEntity = new Domain.Entities.RoboCopy
            {
                ReferenceId = command.Id,
                Name = "OldRoboCopyName"
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingEntity);

            _mockRoboCopyRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns(ToString);

            _mockMapper
                .Setup(mapper => mapper.Map(command, existingEntity, typeof(UpdateRoboCopyCommand), typeof(Domain.Entities.RoboCopy)))
                .Callback(() =>
                {
                    existingEntity.Name = command.Name;
                });

            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<RoboCopyUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.Id, result.Id);
            Assert.Contains("UpdatedRoboCopyName", result.Message);

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.RoboCopy>(e => e.Name == command.Name)), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRoboCopyDoesNotExist()
        {
            var command = new UpdateRoboCopyCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "UpdatedRoboCopyName"
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.RoboCopy)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentException_WhenIdIsInvalid()
        {
            var command = new UpdateRoboCopyCommand
            {
                Id = Guid.Empty.ToString(),
                Name = "UpdatedRoboCopyName"
            };

            await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
