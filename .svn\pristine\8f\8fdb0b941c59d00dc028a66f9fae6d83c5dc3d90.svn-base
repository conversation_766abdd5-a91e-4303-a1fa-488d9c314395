﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Queries;

public class GetServerTypePaginatedListQueryHandlerTests : IClassFixture<ServerTypeFixture>
{
    private readonly ServerTypeFixture _serverTypeFixture;

    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;
    private readonly GetServerTypePaginatedListQueryHandler _handler;

    public GetServerTypePaginatedListQueryHandlerTests(ServerTypeFixture serverTypeFixture)
    {
        _serverTypeFixture = serverTypeFixture;

        _serverTypeFixture.ServerTypes[0].Name = "Server_Type";

        _serverTypeFixture.ServerTypes[1].Name = "Testing_Server";

        _mockServerTypeRepository = ServerTypeRepositoryMocks.GetPaginatedServerTypeRepository(_serverTypeFixture.ServerTypes);

        _handler = new GetServerTypePaginatedListQueryHandler(_serverTypeFixture.Mapper, _mockServerTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetServerTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerTypeListVm>>();

        result.TotalCount.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetServerTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_ServerTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetServerTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Server_Type" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBe(_serverTypeFixture.ServerTypes[0].ReferenceId);

        result.Data[0].Name.ShouldBe("Server_Type");
    }

    [Fact]
    public async Task Handle_Return_PaginatedServerTypes_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetServerTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ServerTypeListVm>();

        result.Data[0].Name.ShouldBe("Testing_Server");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetServerTypePaginatedListQuery(), CancellationToken.None);

        _mockServerTypeRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
