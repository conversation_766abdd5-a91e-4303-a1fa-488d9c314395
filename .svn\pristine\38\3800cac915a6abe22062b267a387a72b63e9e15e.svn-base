using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class BackUpLogProfile : Profile
{
    public BackUpLogProfile()
    {
        CreateMap<BackUpLog, BackUpLogListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BackUpLog, BackUpLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<BackUpLog, CreateBackUpLogCommand>().ReverseMap();
        CreateMap<BackUpLog, BackUpLogViewModel>().ReverseMap();

        CreateMap<CreateBackUpLogCommand, BackUpLogViewModel>().ReverseMap();
        CreateMap<UpdateBackUpLogCommand, BackUpLogViewModel>().ReverseMap();

        CreateMap<UpdateBackUpLogCommand, BackUpLog>().ForMember(x => x.Id, y => y.Ignore());
            
       CreateMap< PaginatedResult<BackUpLog>,PaginatedResult<BackUpLogListVm>>()
            .ForMember(dest=>dest.Data,opt=>opt.MapFrom(src=>src.Data));
    }
}