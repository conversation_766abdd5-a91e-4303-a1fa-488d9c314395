using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FiaImpactTypeRepositoryTests : IClassFixture<FiaImpactTypeFixture>, IDisposable
{
    private readonly FiaImpactTypeFixture _fiaImpactTypeFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FiaImpactTypeRepository _repository;

    public FiaImpactTypeRepositoryTests(FiaImpactTypeFixture fiaImpactTypeFixture)
    {
        _fiaImpactTypeFixture = fiaImpactTypeFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FiaImpactTypeRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var impactTypeName = "Revenue Loss";
        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Impact Type";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var impactTypeName = "Customer Dissatisfaction";

        _fiaImpactTypeFixture.FiaImpactTypeDto.ReferenceId = existingId;
        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, existingId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var differentId = Guid.NewGuid().ToString();
        var impactTypeName = "Regulatory Penalties";

        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, differentId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        // Arrange
        var invalidId = "not-a-valid-guid";
        var impactTypeName = "Data Loss";

        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, invalidId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_IsCaseSensitive()
    {
        // Arrange
        var impactTypeName = "Service Disruption";
        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("SERVICE DISRUPTION", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyString()
    {
        // Act
        var result = await _repository.IsNameExist("", null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_HandlesEmptyId()
    {
        // Arrange
        var impactTypeName = "Test Impact Type";
        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, "");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_IncludesInactiveRecords()
    {
        // Arrange
        var impactTypeName = "Inactive Impact Type";
        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = impactTypeName;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Manually set as inactive after saving
        _fiaImpactTypeFixture.FiaImpactTypeDto.IsActive = false;
        _dbContext.FiaImpactTypes.Update(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(impactTypeName, null);

        // Assert
        Assert.True(result); // Should still find inactive records
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFiaImpactType_WhenValidImpactType()
    {
        // Arrange
        var impactType = _fiaImpactTypeFixture.FiaImpactTypeDto;
        impactType.Name = "Test Impact Type";
        impactType.Description = "Test Description";
        impactType.FiaImpactCategoryId = "CAT_001";
        impactType.FiaImpactCategoryName = "Financial";

        // Act
        var result = await _repository.AddAsync(impactType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(impactType.Name, result.Name);
        Assert.Equal(impactType.Description, result.Description);
        Assert.Equal(impactType.FiaImpactCategoryId, result.FiaImpactCategoryId);
        Assert.Single(_dbContext.FiaImpactTypes);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenImpactTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsImpactType_WhenExists()
    {
        // Arrange
        _fiaImpactTypeFixture.FiaImpactTypeDto.Id = 1;
        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fiaImpactTypeFixture.FiaImpactTypeDto.Id, result.Id);
        Assert.Equal(_fiaImpactTypeFixture.FiaImpactTypeDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsImpactType_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _fiaImpactTypeFixture.FiaImpactTypeDto.ReferenceId = referenceId;

        await _dbContext.FiaImpactTypes.AddAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateImpactType_WhenValidImpactType()
    {
        // Arrange
        _dbContext.FiaImpactTypes.Add(_fiaImpactTypeFixture.FiaImpactTypeDto);
        await _dbContext.SaveChangesAsync();

        _fiaImpactTypeFixture.FiaImpactTypeDto.Name = "Updated Impact Type Name";
        _fiaImpactTypeFixture.FiaImpactTypeDto.Description = "Updated Description";

        // Act
        var result = await _repository.UpdateAsync(_fiaImpactTypeFixture.FiaImpactTypeDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Impact Type Name", result.Name);
        Assert.Equal("Updated Description", result.Description);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenImpactTypeIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveImpactTypes_WhenImpactTypesExist()
    {
        // Arrange
        await _dbContext.FiaImpactTypes.AddRangeAsync(_fiaImpactTypeFixture.FiaImpactTypeList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion
}
