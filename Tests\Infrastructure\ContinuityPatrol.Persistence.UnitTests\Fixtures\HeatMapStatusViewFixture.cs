using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class HeatMapStatusViewFixture : IDisposable
{
    public ApplicationDbContext DbContext { get; }

    public List<HeatMapStatusView> HeatMapStatusViewPaginationList { get; set; }

    public List<HeatMapStatusView> HeatMapStatusViewList { get; set; }

    public HeatMapStatusView HeatMapStatusViewDto { get; set; }

    public HeatMapStatusViewFixture()
    {
        DbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());

        var fixture = new Fixture();

        HeatMapStatusViewPaginationList = fixture.CreateMany<HeatMapStatusView>(20).ToList();
        HeatMapStatusViewList = fixture.CreateMany<HeatMapStatusView>(5).ToList();

        // Setup proper test data for HeatMapStatusViewPaginationList
        HeatMapStatusViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        HeatMapStatusViewPaginationList.ForEach(x => x.IsActive = true);
        HeatMapStatusViewPaginationList.ForEach(x => x.BusinessServiceId = Guid.NewGuid().ToString());
        HeatMapStatusViewPaginationList.ForEach(x => x.BusinessFunctionId = Guid.NewGuid().ToString());
        HeatMapStatusViewPaginationList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());
        HeatMapStatusViewPaginationList.ForEach(x => x.EntityId = Guid.NewGuid().ToString());

        // Setup proper test data for HeatMapStatusViewList
        HeatMapStatusViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        HeatMapStatusViewList.ForEach(x => x.IsActive = true);
        HeatMapStatusViewList.ForEach(x => x.BusinessServiceId = Guid.NewGuid().ToString());
        HeatMapStatusViewList.ForEach(x => x.BusinessFunctionId = Guid.NewGuid().ToString());
        HeatMapStatusViewList.ForEach(x => x.InfraObjectId = Guid.NewGuid().ToString());
        HeatMapStatusViewList.ForEach(x => x.EntityId = Guid.NewGuid().ToString());

        HeatMapStatusViewDto = fixture.Create<HeatMapStatusView>();
        HeatMapStatusViewDto.ReferenceId = Guid.NewGuid().ToString();
        HeatMapStatusViewDto.IsActive = true;
        HeatMapStatusViewDto.BusinessServiceId = Guid.NewGuid().ToString();
        HeatMapStatusViewDto.BusinessFunctionId = Guid.NewGuid().ToString();
        HeatMapStatusViewDto.InfraObjectId = Guid.NewGuid().ToString();
        HeatMapStatusViewDto.EntityId = Guid.NewGuid().ToString();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
