﻿using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Commands;

public class UpdateServerTypeTests : IClassFixture<ServerTypeFixture>
{
    private readonly ServerTypeFixture _serverTypeFixture;

    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;

    private readonly UpdateServerTypeCommandHandler _handler;

    public UpdateServerTypeTests(ServerTypeFixture serverTypeFixture)
    {
        _serverTypeFixture = serverTypeFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockServerTypeRepository = ServerTypeRepositoryMocks.UpdateServerTypeRepository(_serverTypeFixture.ServerTypes);

        _handler = new UpdateServerTypeCommandHandler(_serverTypeFixture.Mapper, _mockServerTypeRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidServer_UpdateToServerTypesRepo()
    {
        _serverTypeFixture.UpdateServerTypeCommand.Id = _serverTypeFixture.ServerTypes[0].ReferenceId;

        var result = await _handler.Handle(_serverTypeFixture.UpdateServerTypeCommand, CancellationToken.None);

        var serverType = await _mockServerTypeRepository.Object.GetByReferenceIdAsync(result.ServerTypeId);

        Assert.Equal(_serverTypeFixture.UpdateServerTypeCommand.Name, serverType.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidServerTypeResponse_WhenUpdate_ServerType()
    {
        _serverTypeFixture.UpdateServerTypeCommand.Id = _serverTypeFixture.ServerTypes[0].ReferenceId;

        var result = await _handler.Handle(_serverTypeFixture.UpdateServerTypeCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateServerTypeResponse));

        result.ServerTypeId.ShouldBeGreaterThan(0.ToString());

        result.ServerTypeId.ShouldBe(_serverTypeFixture.UpdateServerTypeCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidServerTypeId()
    {
        _serverTypeFixture.UpdateServerTypeCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_serverTypeFixture.UpdateServerTypeCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _serverTypeFixture.UpdateServerTypeCommand.Id = _serverTypeFixture.ServerTypes[0].ReferenceId;

        await _handler.Handle(_serverTypeFixture.UpdateServerTypeCommand, CancellationToken.None);

        _mockServerTypeRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockServerTypeRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.ServerType>()), Times.Once);
    }

}
