﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DataSetRepositoryMocks
{
    public static Mock<IDataSetRepository> CreateDataSetRepository(List<DataSet> dataSets)
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dataSets);

        mockDataSetRepository.Setup(repo => repo.AddAsync(It.IsAny<DataSet>())).ReturnsAsync(
            (DataSet dataSet) =>
            {
                dataSet.Id = new Fixture().Create<int>();

                dataSet.ReferenceId = new Fixture().Create<Guid>().ToString();

                dataSets.Add(dataSet);

                return dataSet;
            });

        return mockDataSetRepository;
    }

    public static Mock<IDataSetRepository> UpdateDataSetRepository(List<DataSet> dataSets)
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dataSets);

        mockDataSetRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dataSets.SingleOrDefault(x => x.ReferenceId == i));

        mockDataSetRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DataSet>())).ReturnsAsync((DataSet dataSet) =>
        {
            var index = dataSets.FindIndex(item => item.Id == dataSet.Id);

            dataSets[index] = dataSet;

            return dataSet;
        });

        return mockDataSetRepository;
    }

    public static Mock<IDataSetRepository> DeleteDataSetRepository(List<DataSet> dataSets)
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dataSets);

        mockDataSetRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dataSets.SingleOrDefault(x => x.ReferenceId == i));

        mockDataSetRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DataSet>())).ReturnsAsync((DataSet dataSet) =>
        {
            var index = dataSets.FindIndex(item => item.Id == dataSet.Id);

            dataSet.IsActive = false;

            dataSets[index] = dataSet;

            return dataSet;
        });

        return mockDataSetRepository;
    }

    public static Mock<IDataSetRepository> GetDataSetRepository(List<DataSet> dataSets)
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(dataSets);

        mockDataSetRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => dataSets.SingleOrDefault(x => x.ReferenceId == i));

        return mockDataSetRepository;
    }

    public static Mock<IDataSetRepository> GetDataSetNamesRepository(List<DataSet> dataSets)
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.GetDataSetNames()).ReturnsAsync(dataSets);

        return mockDataSetRepository;
    }

    public static Mock<IDataSetRepository> GetPaginatedDataSetRepository(List<DataSet> dataSets)
    {
        var dataSetRepository = new Mock<IDataSetRepository>();

        var queryableDataSet = dataSets.BuildMock();

        dataSetRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableDataSet);

        return dataSetRepository;
    }

    public static Mock<IDataSetRepository> GetDataSetEmptyRepository()
    {
        var mockDataSetRepository = new Mock<IDataSetRepository>();

        mockDataSetRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<DataSet>());

        return mockDataSetRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateDataSetEventRepository(List<UserActivity> userActivityModels)
    {
        var dataSetEventRepository = new Mock<IUserActivityRepository>();

        dataSetEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivityModels.Add(userActivity);

              return userActivity;
          });

        return dataSetEventRepository;
    }
}