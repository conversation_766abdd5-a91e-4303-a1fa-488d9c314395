using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ApprovalMatrixRequestFilterSpecification : Specification<ApprovalMatrixRequest>
{
    public ApprovalMatrixRequestFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ProcessName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("processname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ProcessName.Contains(stringItem.Replace("processname=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("description=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Description.Contains(stringItem.Replace("description=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("username=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.UserName.Contains(stringItem.Replace("username=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("status=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Status.Contains(stringItem.Replace("status=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("approvers=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Approvers.Contains(stringItem.Replace("approvers=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ProcessName.Contains(searchString) || p.Description.Contains(searchString) ||
                    p.UserName.Contains(searchString) || p.Status.Contains(searchString) ||
                    p.Approvers.Contains(searchString);
            }
        }
    }
}