﻿using ContinuityPatrol.Application.Features.InfraObject.Events.DashboardViewEvent.Update;
using ContinuityPatrol.Application.Features.InfraObject.Events.Update;

namespace ContinuityPatrol.Application.Features.InfraObject.Commands.Update;

public class UpdateInfraObjectCommandHandler : IRequestHandler<UpdateInfraObjectCommand, UpdateInfraObjectResponse>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateInfraObjectCommandHandler(IMapper mapper, IInfraObjectRepository infraObjectRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<UpdateInfraObjectResponse> Handle(UpdateInfraObjectCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _infraObjectRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.InfraObject), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateInfraObjectCommand), typeof(Domain.Entities.InfraObject));

        await _infraObjectRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateInfraObjectResponse
        {
            Message = Message.Update(nameof(Domain.Entities.InfraObject), eventToUpdate.Name),

            InfraObjectId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new InfraObjectDashboardViewUpdatedEvent
        {
            InfraObjectId = eventToUpdate.ReferenceId,
            InfraObjectName = eventToUpdate.Name,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            BusinessFunctionId = eventToUpdate.BusinessFunctionId,
            BusinessFunctionName = eventToUpdate.BusinessFunctionName,
            ReplicationStatus = eventToUpdate.ReplicationStatus,
            DROperationStatus = eventToUpdate.DROperationStatus,
            Type = eventToUpdate.Type,
            State = eventToUpdate.State
        }, CancellationToken.None);

        await _publisher.Publish(new InfraObjectUpdatedEvent { InfraObjectName = eventToUpdate.Name },
            cancellationToken);

        return response;
    }
}