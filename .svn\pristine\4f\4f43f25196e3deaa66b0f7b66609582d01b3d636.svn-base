using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaInterval.Events.Update;

public class FiaIntervalUpdatedEventHandler : INotificationHandler<FiaIntervalUpdatedEvent>
{
    private readonly ILogger<FiaIntervalUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaIntervalUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<FiaIntervalUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(FiaIntervalUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} FiaInterval",
            Entity = "FiaInterval",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"FiaInterval '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaInterval '{updatedEvent.Name}' updated successfully.");
    }
}