﻿@model ContinuityPatrol.Domain.ViewModels.SingleSignOnModel.SingleSignOnViewModel;

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

<style>
    .f-checkbox {
        display: flex;
        align-items: center;
    }

    .wizard > .content > .body label {
        margin-bottom: -1px;
    }
</style>

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-single-sign_on"></i><span>Single Sign-On</span></h6>
            <form class="d-flex">
                <span class="input-group-text form-label mb-0 me-2" for="basic-url">Sign-On Type</span>
                <div class="input-group ">                   
                    <span class="input-group-text"><i class="cp-sign-on-type"></i></span>
                    <div class="input-group border-0 me-2" style="width: 250px !important;">
                        <select class="form-select w-100" id="selectType">
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
                <div class="input-group mx-2 w-100">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="profileName=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="signOnType=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm create-model" id="SingleSignOn-CreateButton" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="singleSignOnList" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th"> Sr. No.</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Create-->
<div class="modal fade " id="CreateModal" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>

<!--Version Restore-->
@* <div class="modal fade" data-bs-backdrop="static" id="RestoreSSOModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/confirmation.svg" alt="confirmation Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                 <p class="d-inline-block align-bottom gap-2 ">
                    You want to continue <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="SSOVersionName"></span>
                    data with new version <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="newVersion"></span> ?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id ="cancelSSOFormRestore" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmSSOFormRestore">Yes</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmSSOFormRestoreAll">Yes to all</button>
            </div>
        </div>
    </div>
</div> *@

<script src="~/js/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/lib/formeo/formeo.min.js"></script>

<script src="~/js/configuration/infra components/commonfunctions.js"></script>
<script src="~/js/configuration/infra components/single sign-on/singlesignonfunctions.js"></script>
<script src="~/js/configuration/infra components/single sign-on/singlesignon.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidation.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildercommonfunctions.js"></script>
