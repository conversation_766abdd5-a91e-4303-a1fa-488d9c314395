﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IJobRepository : IRepository<Domain.Entities.Job>
{
    Task<bool> IsJobNameUnique(string name);
    Task<bool> IsJobNameExist(string name, string id);
    Task<List<Domain.Entities.Job>> GetJobsByInfraObjectId(string infraObjectId);
    Task<List<Domain.Entities.Job>> GetJobNames();

    Task<Domain.Entities.Job> GetJobByTemplateId(string templateId);

    //Task<List<Job>> GetJobsListByInfraObjectId(string infraObjectId);
    Task<List<Domain.Entities.Job>> GetJobsListByTemplateId(string templateId);
    Task<List<Domain.Entities.Job>> GetJobByIds(List<string> templateId);

    Task<List<Domain.Entities.Job>> GetBySolutionTypeId(string solutiontypeId);
    Task<List<Domain.Entities.Job>> GetJobByGroupNodePolicyId(string groupPolicyId);
    Task<List<string>> GetInfraObjectPropertyBySolutiontypeId(string solutiontypeId);
    Task<List<Domain.Entities.Job>> GetByPolicy(string policy);
}