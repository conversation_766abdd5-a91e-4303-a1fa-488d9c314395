﻿//namespace ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetType;

//public class GetHeatMapLogTypeQueryHandler : IRequestHandler<GetHeatMapLogTypeQuery, List<HeatMapLogTypeVm>>
//{
//    private readonly IHeatMapLogRepository _heatMapLogRepository;
//    private readonly IMapper _mapper;

//    public GetHeatMapLogTypeQueryHandler(IMapper mapper, IHeatMapLogRepository heatMapLogRepository)
//    {
//        _mapper = mapper;
//        _heatMapLogRepository = heatMapLogRepository;
//    }

//    public Task<List<HeatMapLogTypeVm>> Handle(GetHeatMapLogTypeQuery request,
//        CancellationToken cancellationToken)
//    {
//        var heatMapLogType = _heatMapLogRepository.GetHeatMapLogType(request.Type).ToList();

//        return Task.FromResult(heatMapLogType.Count <= 0
//            ? new List<HeatMapLogTypeVm>()
//            : _mapper.Map<List<HeatMapLogTypeVm>>(heatMapLogType));
//    }
//}