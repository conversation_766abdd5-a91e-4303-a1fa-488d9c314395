﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowActionType.Events.Update;

public class WorkflowActionTypeUpdatedEventHandler : INotificationHandler<WorkflowActionTypeUpdatedEvent>
{
    private readonly ILogger<WorkflowActionTypeUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowActionTypeUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowActionTypeUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowActionTypeUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.WorkflowActionType}",
            Entity = Modules.WorkflowActionType.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"WorkflowActionType '{updatedEvent.ActionType}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowActionType '{updatedEvent.ActionType}' updated successfully.");
    }
}