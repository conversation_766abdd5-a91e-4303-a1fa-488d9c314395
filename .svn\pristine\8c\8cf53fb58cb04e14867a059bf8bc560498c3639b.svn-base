using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ActiveDirectoryMonitorLogFixture : IDisposable
{
    public List<ActiveDirectoryMonitorLog> ActiveDirectoryMonitorLogPaginationList { get; set; }
    public List<ActiveDirectoryMonitorLog> ActiveDirectoryMonitorLogList { get; set; }
    public ActiveDirectoryMonitorLog ActiveDirectoryMonitorLogDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ActiveDirectoryMonitorLogFixture()
    {
        var fixture = new Fixture();

        ActiveDirectoryMonitorLogList = fixture.Create<List<ActiveDirectoryMonitorLog>>();

        ActiveDirectoryMonitorLogPaginationList = fixture.CreateMany<ActiveDirectoryMonitorLog>(20).ToList();

        ActiveDirectoryMonitorLogDto = fixture.Create<ActiveDirectoryMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
