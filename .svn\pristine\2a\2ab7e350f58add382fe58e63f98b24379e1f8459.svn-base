﻿using ContinuityPatrol.Application.Features.Company.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Events;

public class PaginatedCompanyEventTests : IClassFixture<CompanyFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CompanyFixture _companyFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly CompanyPaginatedEventHandler _handler;

    public PaginatedCompanyEventTests(CompanyFixture companyFixture, UserActivityFixture userActivityFixture)
    {
        _companyFixture = companyFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Admin");

        var mockCompanyEventLogger = new Mock<ILogger<CompanyPaginatedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new CompanyPaginatedEventHandler(mockLoggedInUserService.Object, mockCompanyEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_ViewCompanyEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Admin";

        var result = _handler.Handle(_companyFixture.CompanyPaginatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_companyFixture.CompanyPaginatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

}