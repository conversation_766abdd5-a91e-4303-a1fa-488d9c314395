<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Notification Manager Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.19.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <style>
        .field-validation-error {
            color: red;
        }

        .tree {
            margin-left: 20px;
        }

        #qunit-fixture {
            position: static !important;
            width: 100%;
        }

        .modal {
            display: none !important;
        }

        .btn-disabled {
            opacity: 0.5;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <!-- Form elements for testing -->
        <div id="ConfigurationCreate" data-create-permission="true"></div>
        <div id="ConfigurationDelete" data-delete-permission="true"></div>
        <button id="CreteButton" class="btn" data-bs-toggle="modal" data-bs-target="#CreateModal">Create</button>

        <input type="text" id="notifName">
        <span id="notifNameError" class="field-validation-error"></span>

        <input type="text" id="notifEmail">
        <span id="notifEmailError" class="field-validation-error"></span>

        <input type="text" id="notifMobileNum">
        <span id="notifMobileError" class="field-validation-error"></span>

        <select id="notifMobilePre"></select>
        <span id="notifMobilePreError" class="field-validation-error"></span>

        <input type="checkbox" id="notifIsMobile">
        <div id="notifMob" style="display:none;"></div>
        <div id="notifMobPre" style="display:none;"></div>

        <div id="notifyTreeView"></div>
        <span id="notifTreeError"></span>

        <input type="checkbox" id="notifSelectAll">
        <details id="notifExpand"></details>

        <input type="hidden" id="notifyId">
        <input type="hidden" id="textProperties">
        <input type="hidden" id="comMobile">

        <button id="notifSave">Save</button>
        <button id="notifCreateButton">Create</button>
        <button id="notifyBtnCancel">Cancel</button>

        <!-- DataTable container -->
        <table id="notificationManager" class="display" style="width:100%"></table>

        <!-- Search elements -->
        <input type="text" id="notifSearchInp">
        <input type="checkbox" id="Name" value="name">
        <input type="checkbox" id="Email" value="email">
        <input type="checkbox" id="Mobile" value="mobile">

        <!-- Modal elements -->
        <div id="CreateModal" class="modal fade" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form id="CreateForm"></form>
                </div>
            </div>
        </div>

        <div id="DeleteModal" class="modal fade" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <span id="deleteData"></span>
                    <input type="hidden" id="textDeleteId">
                </div>
            </div>
        </div>
    </div>

    <!-- Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.19.1.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Mock common.js functions -->
    <script>
        // Mock common.js functions used in NotificationManager.js
        window.errorNotification = function (message) {
            console.error("Mock errorNotification:", message);
        };

        window.notificationAlert = function (title, message) {
            console.log("Mock notificationAlert:", title, message);
        };

        window.CommonValidation = function (element, results) {
            for (let i = 0; i < results.length; i++) {
                if (results[i] !== true) {
                    element.text(results[i]).addClass('field-validation-error');
                    return false;
                }
            }
            element.text('').removeClass('field-validation-error');
            return true;
        };

        window.GetAsync = function (url, data) {
            console.log("Mock GetAsync called with:", url, data);
            return Promise.resolve(false);
        };

        window.PostAsync = function (url, data) {
            console.log("Mock PostAsync called with:", url, data);
            return Promise.resolve({ success: true });
        };

        window.sanitizeInput = function (value) {
            return Promise.resolve(value.replace(/</g, '&lt;'));
        };

        // Mock validation functions
        window.SpecialCharValidateCustom = function () { return true; };
        window.ShouldNotBeginWithUnderScore = function () { return true; };
        window.ShouldNotBeginWithSpace = function () { return true; };
        window.OnlyNumericsValidate = function () { return true; };
        window.ShouldNotBeginWithNumber = function () { return true; };
        window.SpaceWithUnderScore = function () { return true; };
        window.ShouldNotEndWithUnderScore = function () { return true; };
        window.ShouldNotEndWithSpace = function () { return true; };
        window.MultiUnderScoreRegex = function () { return true; };
        window.SpaceAndUnderScoreRegex = function () { return true; };
        window.minMaxlength = function () { return true; };
        window.secondChar = function () { return true; };
        window.emailRegex = function () { return true; };
    </script>

    <!-- The actual NotificationManager.js file we're testing -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Manage/Notification Manager/NotificationManager.js"></script>
    <script src="/js/Manage/Notification Manager/NotificationManagerTest.js"></script>

</body>
</html>