// Timer functionality
let timeLeft = 600; // 10 minutes in seconds
const timerElement = document.getElementById('timer');
const progressBar = document.getElementById('progress-bar');

function updateTimer() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

    const progressWidth = (timeLeft / 600) * 100;
    progressBar.style.width = `${progressWidth}%`;

    if (timeLeft <= 0) {
        clearInterval(timerInterval);
        timerElement.textContent = 'Expired';
        progressBar.style.width = '0%';
    } else {
        timeLeft--;
    }
}

const timerInterval = setInterval(updateTimer, 1000);
updateTimer();

// OTP input handling
const inputs = document.querySelectorAll('.otp-input');
inputs.forEach((input, index) => {
    input.addEventListener('input', (e) => {
        if (e.target.value.length === 1 && index < inputs.length - 1) {
            inputs[index + 1].focus();
        }
    });
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Backspace' && !e.target.value && index > 0) {
            inputs[index - 1].focus();
        }
    });
});