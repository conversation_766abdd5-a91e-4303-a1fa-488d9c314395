﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetPaginatedList;

public class GetCredentialProfilePaginatedListQueryHandler : IRequestHandler<GetCredentialProfilePaginatedListQuery,
    PaginatedResult<CredentialProfileListVm>>
{
    private readonly ICredentialProfileRepository _credentialProfileRepository;
    private readonly IMapper _mapper;

    public GetCredentialProfilePaginatedListQueryHandler(IMapper mapper,
        ICredentialProfileRepository credentialProfileRepository)
    {
        _mapper = mapper;
        _credentialProfileRepository = credentialProfileRepository;
    }

    public async Task<PaginatedResult<CredentialProfileListVm>> Handle(
        GetCredentialProfilePaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new CredentialProfileFilterSpecification(request.SearchString);

        var queryable = request.CredentialType != null
            ? await _credentialProfileRepository.GetCredentialProfileByType(request.PageNumber,request.PageSize,productFilterSpec,request.CredentialType, request.SortColumn, request.SortOrder)
            : await _credentialProfileRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var credentialProfileList = _mapper.Map <PaginatedResult<CredentialProfileListVm>>(queryable);
       
        return credentialProfileList;
        //var queryable = request.CredentialType != null
        //    ? _credentialProfileRepository.GetCredentialProfileByType(request.CredentialType)
        //    : _credentialProfileRepository.PaginatedListAllAsync();

        //var productFilterSpec = new CredentialProfileFilterSpecification(request.SearchString);

        //var credentialProfileList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CredentialProfileListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        ////await _publisher.Publish(new CredentialProfilePaginatedEvent(), cancellationToken);

        //return credentialProfileList;
    }
}