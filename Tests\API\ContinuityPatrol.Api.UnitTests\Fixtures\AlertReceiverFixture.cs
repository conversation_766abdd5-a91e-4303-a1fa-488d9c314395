using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AlertReceiverFixture
{
    public List<AlertReceiverListVm> AlertReceiverListVm { get; }
    public AlertReceiverDetailVm AlertReceiverDetailVm { get; }
    public CreateAlertReceiverCommand CreateAlertReceiverCommand { get; }
    public UpdateAlertReceiverCommand UpdateAlertReceiverCommand { get; }

    public AlertReceiverFixture()
    {
        var fixture = new Fixture();

        // Create sample AlertReceiver list data
        AlertReceiverListVm = new List<AlertReceiverListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = "<EMAIL>",
                MobileNumber = "******-0123",
                Name = "John Doe",
                Properties = "{\"department\":\"IT\",\"role\":\"Administrator\"}",
                IsMail = true,
                IsActiveUser = true,
                IsSendReport = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = "<EMAIL>",
                MobileNumber = "******-0456",
                Name = "Jane Smith",
                Properties = "{\"department\":\"Operations\",\"role\":\"Manager\"}",
                IsMail = true,
                IsActiveUser = true,
                IsSendReport = false
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                EmailAddress = "<EMAIL>",
                MobileNumber = "******-0789",
                Name = "Bob Wilson",
                Properties = "{\"department\":\"Security\",\"role\":\"Analyst\"}",
                IsMail = false,
                IsActiveUser = true,
                IsSendReport = true
            }
        };

        // Create detailed AlertReceiver data
        AlertReceiverDetailVm = new AlertReceiverDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            EmailAddress = "<EMAIL>",
            MobileNumber = "******-0001",
            Name = "System Administrator",
            Properties = "{\"department\":\"IT\",\"role\":\"Administrator\",\"priority\":\"high\"}",
            IsMail = true,
            IsActiveUser = true,
            IsSendReport = true
        };

        // Create command for creating AlertReceiver
        CreateAlertReceiverCommand = new CreateAlertReceiverCommand
        {
            EmailAddress = "<EMAIL>",
            MobileNumber = "******-9999",
            Name = "New User",
            Properties = "{\"department\":\"Testing\",\"role\":\"Tester\"}",
            IsMail = true,
            IsActiveUser = true,
            IsSendReport = false
        };

        // Create command for updating AlertReceiver
        UpdateAlertReceiverCommand = new UpdateAlertReceiverCommand
        {
            Id = Guid.NewGuid().ToString(),
            EmailAddress = "<EMAIL>",
            MobileNumber = "******-8888",
            Name = "Updated User",
            Properties = "{\"department\":\"Updated\",\"role\":\"Updated Role\"}",
            IsMail = false,
            IsActiveUser = false,
            IsSendReport = true
        };
    }
}
