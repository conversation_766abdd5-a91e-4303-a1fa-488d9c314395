﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class FormTypeCategoryProfile : Profile
{
    public FormTypeCategoryProfile()
    {
        CreateMap<CreateFormTypeCategoryCommand, FormMappingViewModel>().ReverseMap();
        CreateMap<UpdateFormTypeCategoryCommand, FormMappingViewModel>().ReverseMap();

        CreateMap<FormTypeCategory, CreateFormTypeCategoryCommand>().ReverseMap();
        CreateMap<UpdateFormTypeCategoryCommand, FormTypeCategory>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<FormTypeCategory, FormTypeCategoryDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormTypeCategory, FormTypeCategoryListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormTypeCategory, FormTypeCategoryByFormTypeIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormTypeCategory, FormTypeCategoryNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FormTypeCategory, FormTypeCategoryByNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        //CreateMap<ImportFormTypeCategoryCommands, CreateFormTypeCategoryCommand>();
        //CreateMap<ImportFormTypeCategoryCommands, UpdateFormTypeCategoryCommand>();

        CreateMap<PaginatedResult<FormTypeCategory>, PaginatedResult<FormTypeCategoryListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}