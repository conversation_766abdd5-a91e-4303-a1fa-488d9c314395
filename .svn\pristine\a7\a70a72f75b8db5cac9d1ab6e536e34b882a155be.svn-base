﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;

public class GetFormTypeCategoryPaginatedListQueryHandler : IRequestHandler<GetFormTypeCategoryPaginatedListQuery,
    PaginatedResult<FormTypeCategoryListVm>>
{
     private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly ISingleSignOnRepository _singleSignOnRepository;

    public GetFormTypeCategoryPaginatedListQueryHandler(IFormTypeCategoryRepository formTypeCategoryRepository,
        IMapper mapper,
        IServerViewRepository serverViewRepository,
        IDatabaseViewRepository databaseViewRepository,
        IReplicationRepository replicationRepository,
        INodeRepository nodeRepository,
        ISingleSignOnRepository singleSignOnRepository)
    {
        _formTypeCategoryRepository = formTypeCategoryRepository;
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _replicationRepository = replicationRepository;
        _nodeRepository = nodeRepository;
        _singleSignOnRepository = singleSignOnRepository;
    }

    public async Task<PaginatedResult<FormTypeCategoryListVm>> Handle(GetFormTypeCategoryPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new FormTypeCategoryFilterSpecification(request.SearchString);

        var queryable =await _formTypeCategoryRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec,request.SortColumn,request.SortOrder);

        var formCategory = _mapper.Map<PaginatedResult<FormTypeCategoryListVm>>(queryable);

        // **New Code Change on 31.12.2024*********

        var formTypeIds = formCategory.Data.Where(x=>x.FormTypeId.IsNotNullOrWhiteSpace()).Select(fy => fy.FormTypeId).ToList();

        if (formTypeIds.Count == 0) return formCategory;

        var servers = await _serverViewRepository.GetServersByOsTypeIds(formTypeIds);
        var databases = await _databaseViewRepository.GetDatabaseByDatabaseTypeIds(formTypeIds);
        var replications = await _replicationRepository.GetReplicationByTypeIds(formTypeIds);
        var nodes = await _nodeRepository.GetNodeByTypeIds(formTypeIds);
        var signOns = await _singleSignOnRepository.GetSingleSignOnByTypeIds(formTypeIds);


        formCategory.Data.ForEach(fy =>
        {
            var isMapped = servers.Any(s => s.OSTypeId .Equals(fy.FormTypeId)) ||
                           databases.Any(d => d.DatabaseTypeId.Equals(fy.FormTypeId)) ||
                           replications.Any(r => r.TypeId.Equals(fy.FormTypeId)) ||
                           nodes.Any(n => n.TypeId.Equals(fy.FormTypeId)) ||
                           signOns.Any(sso => sso.SignOnTypeId.Equals(fy.FormTypeId));

            fy.IsMapped = isMapped;
        });

        return formCategory;
    }
}