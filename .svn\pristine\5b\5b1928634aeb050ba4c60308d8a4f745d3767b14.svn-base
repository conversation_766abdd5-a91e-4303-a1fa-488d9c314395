﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowExecutionEventLogRepositoryMocks
{
    public static Mock<IWorkflowExecutionEventLogRepository> CreateWorkflowExecutionEventLogRepository(List<WorkflowExecutionEventLog> workflowExecutionEventLogs)
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();
        workflowExecutionEventLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowExecutionEventLogs);
        workflowExecutionEventLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowExecutionEventLogs.SingleOrDefault(x => x.ReferenceId == i));
        workflowExecutionEventLogRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowExecutionEventLog>())).ReturnsAsync(
            (WorkflowExecutionEventLog workflowExecutionEventLog) =>
            {
                workflowExecutionEventLog.Id = new Fixture().Create<int>();
                workflowExecutionEventLog.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowExecutionEventLogs.Add(workflowExecutionEventLog);
                return workflowExecutionEventLog;

            });

        return workflowExecutionEventLogRepository;
    }

    public static Mock<IWorkflowExecutionEventLogRepository> UpdateWorkflowExecutionEventLogRepository(List<WorkflowExecutionEventLog> workflowExecutionEventLogs)
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();

        workflowExecutionEventLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowExecutionEventLogs);

        workflowExecutionEventLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowExecutionEventLogs.SingleOrDefault(x => x.ReferenceId == i));

        workflowExecutionEventLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowExecutionEventLog>())).ReturnsAsync((WorkflowExecutionEventLog workflowExecutionEventLog) =>
        {
            var index = workflowExecutionEventLogs.FindIndex(item => item.ReferenceId == workflowExecutionEventLog.ReferenceId);
            workflowExecutionEventLogs[index] = workflowExecutionEventLog;
            return workflowExecutionEventLog;
        });

        return workflowExecutionEventLogRepository;
    }

    public static Mock<IWorkflowExecutionEventLogRepository> DeleteWorkflowExecutionEventLogRepository(List<WorkflowExecutionEventLog> workflowExecutionEventLogs)
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();
        workflowExecutionEventLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowExecutionEventLogs);

        workflowExecutionEventLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowExecutionEventLogs.SingleOrDefault(x => x.ReferenceId == i));

        workflowExecutionEventLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowExecutionEventLog>())).ReturnsAsync((WorkflowExecutionEventLog workflowExecutionEventLog) =>
        {
            var index = workflowExecutionEventLogs.FindIndex(item => item.ReferenceId == workflowExecutionEventLog.ReferenceId);
            workflowExecutionEventLog.IsActive = false;
            workflowExecutionEventLogs[index] = workflowExecutionEventLog;

            return workflowExecutionEventLog;
        });

        return workflowExecutionEventLogRepository;
    }

    public static Mock<IWorkflowExecutionEventLogRepository> GetWorkflowExecutionEventLogRepository(List<WorkflowExecutionEventLog> workflowExecutionEventLogs)
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();

        workflowExecutionEventLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowExecutionEventLogs);

        workflowExecutionEventLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowExecutionEventLogs.SingleOrDefault(x => x.ReferenceId == i));

        return workflowExecutionEventLogRepository;
    }

    public static Mock<IWorkflowExecutionEventLogRepository> GetWorkflowExecutionEventLogEmptyRepository()
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();

        workflowExecutionEventLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowExecutionEventLog>());

        return workflowExecutionEventLogRepository;
    }

    public static Mock<IWorkflowExecutionEventLogRepository> GetPaginatedWorkflowExecutionEventLogRepository(List<WorkflowExecutionEventLog> workflowExecutionEventLogs)
    {
        var workflowExecutionEventLogRepository = new Mock<IWorkflowExecutionEventLogRepository>();

        var queryableWorkflowExecutionEventLog = workflowExecutionEventLogs.BuildMock();

        workflowExecutionEventLogRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableWorkflowExecutionEventLog);

        return workflowExecutionEventLogRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowExecutionEventLogEventRepository(List<UserActivity> userActivities)
    {
        var workflowExecutionUserActivityRepository = new Mock<IUserActivityRepository>();

        workflowExecutionUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
        {
            userActivity.LoginName = new Fixture().Create<string>();

            userActivities.Add(userActivity);

            return userActivity;
        });

        return workflowExecutionUserActivityRepository;
    }
}
