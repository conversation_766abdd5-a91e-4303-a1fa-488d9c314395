﻿namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;

public class UpdateAirGapStatusCommandValidator : AbstractValidator<UpdateAirGapStatusCommand>
{
    private readonly ICyberAirGapStatusRepository _cyberAirGapStatusRepository;

    public UpdateAirGapStatusCommandValidator(ICyberAirGapStatusRepository cyberAirGapStatusRepository)
    {
        _cyberAirGapStatusRepository = cyberAirGapStatusRepository;

        RuleFor(p => p).MustAsync(ValidateIsRPOExceet)
            .WithMessage("the file transfer is in process ,so status cannot be updated.");
    }

    private async Task<bool> ValidateIsRPOExceet(UpdateAirGapStatusCommand p, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _cyberAirGapStatusRepository.GetByReferenceIdAsync(p.Id);

        if (p.Status is not null && p.Status.ToLower().Equals("closed"))
        {
            //var differenceInMin = Math.Round((DateTime.Now - eventToUpdate.StartTime).TotalMinutes);

            //if (differenceInMin > eventToUpdate.RPO)
            //{
            //    return  true;
            //}
        }

        return false;
    }
}