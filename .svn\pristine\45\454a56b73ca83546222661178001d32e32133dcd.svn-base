﻿namespace ContinuityPatrol.Application.Features.CyberMappingHistory.Queries.GetCyberMappingHistoryById;

public class
    GetCyberMappingHistoryByIdQueryHandler : IRequestHandler<GetCyberMappingHistoryByIdQuery,
        List<CyberMappingHistoryIdVm>>
{
    private readonly ICyberMappingHistoryRepository _cyberMappingHistory;
    private readonly IMapper _mapper;

    public GetCyberMappingHistoryByIdQueryHandler(ICyberMappingHistoryRepository cyberMappingHistory, IMapper mapper)
    {
        _cyberMappingHistory = cyberMappingHistory;
        _mapper = mapper;
    }

    public async Task<List<CyberMappingHistoryIdVm>> Handle(GetCyberMappingHistoryByIdQuery request,
        CancellationToken cancellationToken)
    {
        var cyberMappingHistory =
            await _cyberMappingHistory.GetCyberComponentMappingHistoryById(request.CyberComponentMappingId);

        var cyberMappingHistoryDto = _mapper.Map<List<CyberMappingHistoryIdVm>>(cyberMappingHistory);

        return cyberMappingHistoryDto == null
            ? throw new NotFoundException(nameof(Domain.Entities.CyberMappingHistory), request.CyberComponentMappingId)
            : cyberMappingHistoryDto;
    }
}