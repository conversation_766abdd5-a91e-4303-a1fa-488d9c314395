﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberJobManagementLogsRepository : BaseRepository<CyberJobManagementLogs>, ICyberJobManagementLogsRepository
{
    private readonly ApplicationDbContext _dbContext;

    public CyberJobManagementLogsRepository(ApplicationDbContext dbContext)
        : base(dbContext)
    {
        _dbContext = dbContext;        
    }
}
