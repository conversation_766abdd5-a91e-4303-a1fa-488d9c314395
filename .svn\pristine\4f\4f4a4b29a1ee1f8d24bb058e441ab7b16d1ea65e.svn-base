﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDashboardViewService
{
    Task<ItViewByInfraObjectIdVm> GetITViewByInfraObjectId(string infraObjectId);
    Task<DashboardViewDetailVm> GetDashboardViewById(string id);
    Task<List<BusinessViewPaginatedList>> GetBusinessViews();
    Task<List<DashboardViewByBusinessServiceIdVm>> GetDashboardViewListByBusinessServiceId(string businessServiceId);
    Task<List<DashboardViewByBusinessFunctionIdVm>> GetDashboardViewListByBusinessFunctionId(string businessFunction);
    Task<GetDashboardViewByInfraObjectIdVm> GetDashboardViewListByInfraObjectId(string infraObjectId);
    Task<List<DashboardViewListVm>> GetDashboardViews();
    Task<List<GetDcMappingListVm>> GetDcMappingDetails(string siteId);
    Task<List<DataLagStatusbyLast7DaysVm>> GetDatalagStatusByLast7DaysList();
    Task<List<GetItViewListVm>> GetItViewList();
    Task<List<ItViewByBusinessServiceIdVm>> GetItViewByBusinessServiceId(string businessServiceId);
    Task<BaseResponse> CreateAsync(CreateDashboardViewCommand createDashboardViewCommand);
    Task<BaseResponse> UpdateAsync(UpdateDashboardViewCommand updateDashboardViewCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<GetByEntityIdVm> GetMonitorServiceStatusByIdAndType(string monitorId, string type);
    Task<List<DashboardViewNameVm>> GetDashboardNames();
    Task<List<GetServiceTopologyListVm>> GetBusinessServiceTopologyByBusinessServiceId(string businessServiceId);
    Task<RTOByBusinessServiceIdVm> GetRTOByBusinessServiceId(string businessServiceId);
    Task<DrReadyByBusinessServiceIdVm> GetDrReadyByBusinessServiceId(string businessServiceId);
    Task<ImpactAvailabilityDetailVm> GetImpactAvailabilityByBusinessServiceId(string businessServiceId);
    Task<DataLagListVm> GetDataLagByBusinessServiceId(string businessServiceId);
    Task<SitePropertiesByBusinessServiceIdVm> GetSitePropertiesByBusinessServiceId(string businessServiceId);
    Task<GetDcMappingSitesVm> GetDcMappingSiteDetails();
    Task<DrillAnalyticsDetailVm> GetDrillAnalytics();
    Task<ComponentFailureAnalyticsDetailVm> GetComponentFailureAnalytics();
    Task<GetSlaBreachListVm> GetSlaBreach();
    Task<GetOperationalAvailabilityAnalyticsDetailVm> GetOperationalAvailabilityAnalytics();
    Task<GetWorkflowAnalyticsDetailVm> GetWorkflowAnalytics();
    Task<ResilienceHealthStatusDetailVm> GetResilienceHealthStatusByInfraObjectId(string infraObjectId);
    Task<List<OperationalHealthSummaryDetailVm>> GetOperationalServiceHealthSummary();
    Task<List<BusinessImpactAnalysisVm>> GetBusinessImpactAnalysisAsync();
    Task<List<SiteCountListVm>> GetSiteCountList();
    Task<VerifyWorkflowDetailVm> GetVerifiedWorkflowList();
    Task<BreachDetailVm> GetBreachDetails();
    Task<LastDrillDetailVm> GetLastDrillDetails();

    Task<List<OneViewEntitiesEventView>> GetOneViewEntitiesEventViewList();
    Task<List<OneViewRiskMitigationCyberSecurityView>> GetOneViewRiskmitigationCyberSecurityList();
    Task<List<OneViewRiskMitigationFailedDrillView>> GetOneViewRiskmitigationFailedDrillList();
    Task<TotalSiteDetailForOneViewListVm> GetTotalSiteDetailsForOneView(List<string> siteId ,string categoryType);
}