﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.TeamMasterModel.TeamMasterViewModel;

<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
    <form class="modal-content" id="CreateTeamForm" asp-controller="TeamMaster" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-teams"></i><span>Team Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body">

            <div class="form-group">
                <label class="form-label">Team Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input asp-for="GroupName" type="text" id="textTeamName" class="form-control" placeholder="Enter Team Name" autocomplete="off" />

                </div>

                <span asp-validation-for="GroupName" id="grpname-error"></span>

            </div>
            <div class="form-group">
                <label class="form-label">Team Description</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-description"></i></span>
                    <input asp-for="Description" type="text" id="txtTeamDescription" class="form-control" placeholder="Enter Description Name" autocomplete="off" />
                    <span asp-validation-for="Description" id="grpdescription-error"></span>
                </div>

            </div>
   

            <input asp-for="Id" type="hidden" id="textTeamId" class="form-control" />
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveTeamGroup">Save</button>
            </div>
        </div>
    </form>
</div>


@* <p>@ViewBag.SelectUser</p> *@
@section Scripts
    {

}