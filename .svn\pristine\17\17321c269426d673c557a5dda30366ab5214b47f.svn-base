using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DynamicDashboardMapRepository : BaseRepository<DynamicDashboardMap>, IDynamicDashboardMapRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DynamicDashboardMapRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.DashBoardSubName.Equals(name))
            : Entities.Where(e => e.DashBoardSubName.Equals(name)).ToList().Unique(id));
    }

    public Task<DynamicDashboardMap> IsDefaultDashboardByUserId(string userId)
    {
        var dynamicDashboardMapDto = _dbContext.DynamicDashboardMaps
            .Active().FirstOrDefault(x => x.UserId.Equals(userId) && x.IsDefault && x.IsView);

        return Task.FromResult(dynamicDashboardMapDto);
    }

    public Task<DynamicDashboardMap> IsDefaultDashboardByRoleId(string roleId)
    {
        var dynamicDashboardMapDto = _dbContext.DynamicDashboardMaps
            .Active().FirstOrDefault(x => x.RoleId.Equals(roleId) && x.IsDefault && x.IsView);

        return Task.FromResult(dynamicDashboardMapDto);
    }

    public override async Task<IReadOnlyList<DynamicDashboardMap>> ListAllAsync()
    {
        return await MapDynamicDashboardMap(base.ListAllAsync(x => x.IsActive).Select(d => new DynamicDashboardMap
        {
            Id = d.Id,
            UserId = d.UserId,
            RoleId = d.RoleId,
            DashBoardSubId = d.DashBoardSubId,
            DashBoardSubName = d.DashBoardSubName,
            IsDefault = d.IsDefault,
            IsView = d.IsView,
        })).ToListAsync();
    }


    private IQueryable<DynamicDashboardMap> MapDynamicDashboardMap(IQueryable<DynamicDashboardMap> query)
    { 
        var dynamicDashboardMap = query.Select(data => new
        {
            DynamicDashboardMap = data,
            DynamicSubDashboard = _dbContext.DynamicSubDashboards.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.DashBoardSubId))
        });
        return dynamicDashboardMap.Select(x => new DynamicDashboardMap
        {
            Id = x.DynamicDashboardMap.Id,
            UserId = x.DynamicDashboardMap.UserId,
            RoleId = x.DynamicDashboardMap.RoleId,
            DashBoardSubId = x.DynamicDashboardMap.DashBoardSubId,
            DashBoardSubName =x.DynamicSubDashboard.Name??x.DynamicDashboardMap.DashBoardSubName,
            IsDefault = x.DynamicDashboardMap.IsDefault,
            IsView = x.DynamicDashboardMap.IsView,
            IsActive = x.DynamicDashboardMap.IsActive,
            CreatedBy = x.DynamicDashboardMap.CreatedBy,
            CreatedDate = x.DynamicDashboardMap.CreatedDate,
            LastModifiedDate = x.DynamicDashboardMap.LastModifiedDate,
            LastModifiedBy = x.DynamicDashboardMap.LastModifiedBy,
        });
    }

}
