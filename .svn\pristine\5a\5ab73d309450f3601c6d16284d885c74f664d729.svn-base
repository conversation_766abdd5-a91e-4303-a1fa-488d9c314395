﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;

public class
    GetBusinessFunctionAvailabilityQueryHandler : IRequestHandler<GetBusinessFunctionAvailabilityQuery,
        BusinessFunctionAvailabilityVm>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;

    public GetBusinessFunctionAvailabilityQueryHandler(IMapper mapper,
        IBusinessFunctionRepository businessFunctionRepository, IInfraObjectRepository infraObjectRepository,
        IHeatMapStatusRepository heatMapStatusRepository)
    {
        _mapper = mapper;
        _businessFunctionRepository = businessFunctionRepository;
        _infraObjectRepository = infraObjectRepository;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task<BusinessFunctionAvailabilityVm> Handle(GetBusinessFunctionAvailabilityQuery request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = new BusinessFunctionAvailabilityVm();

        var businessFunction = await _businessFunctionRepository.ListAllAsync();

        var bfAvailable = false;

        foreach(var bf in businessFunction)
        {
            heatMapStatus.BFConfiguredCount += 1;

            var bfMap = _mapper.Map<FunctionAvailability>(bf);

            var infraObjects = await _infraObjectRepository.GetInfraObjectByBusinessFunctionId(bf.ReferenceId);

            if (infraObjects.Count == 0) heatMapStatus.BFNotAvailableCount += 1;

            bfMap.FunctionRemark.ConfiguredInfraObjectCount += infraObjects.Count;

            foreach(var infra in infraObjects)
            {
                var heatMapStatsInfra =await _heatMapStatusRepository.GetHeatMapByInfraObjectId(infra.ReferenceId);

                var affectedCount = heatMapStatsInfra.Count(x => x.IsAffected);

                bfMap.FunctionRemark.AffectedInfraObjectCount += affectedCount;

                if (affectedCount > 0)
                {
                    bfMap.IsAffected = heatMapStatsInfra.Any(x => x.IsAffected);

                    var affectedList = heatMapStatsInfra.Where(x => x.IsAffected).ToList();

                    var heatMapsList = _mapper.Map<List<BFHeatmapStatusDto>>(affectedList);

                    bfMap.FunctionRemark.BFHeatmapStatusesDto.AddRange(heatMapsList);
                }
                else
                {
                    bfAvailable = true;
                    bfMap.IsAffected = false;
                }
            };

            if (bfAvailable) heatMapStatus.BFAvailableCount += 1;
            heatMapStatus.FunctionAvailability.AddRange(bfMap);
        };

        return heatMapStatus;
    }
}