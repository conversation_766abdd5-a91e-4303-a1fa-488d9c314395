﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using UserRole = ContinuityPatrol.Shared.Core.Enums.UserRole;

namespace ContinuityPatrol.Persistence.Repositories;

public class UserRepository : BaseRepository<User>, IUserRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IQueryable<User> _baseQuery;
    public UserRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext,
        loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
        _baseQuery = BuildBaseQuery();
    }
    public override async Task<IReadOnlyList<User>> ListAllAsync()
    {
        return await MapUsers(_baseQuery).ToListAsync();

        //var users = _loggedInUserService.IsSiteAdmin
        //    ? base.ListAllAsync(user => user.IsActive)
        //    : _loggedInUserService.IsParent
        //        ? base.FilterBy(user => !user.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //        : base.FilterBy(user => user.CompanyId.Equals(_loggedInUserService.CompanyId) && !user.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]));

        //var userDto = MapUsers(users);
        //return await userDto.ToListAsync();
    }

    public override IQueryable<User> GetPaginatedQuery()
    {
        return MapUsers(_baseQuery.OrderByDescending(user => user.Id));

        //if (_loggedInUserService.IsParent)
        //{
        //    var user = _loggedInUserService.IsSiteAdmin
        //        ? Entities.Where(x => x.IsActive)
        //            .AsNoTracking()
        //            .OrderByDescending(x => x.Id)
        //        : Entities.Where(x => x.IsActive && !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //            .AsNoTracking()
        //            .OrderByDescending(x => x.Id);

        //    var userDto = MapUsers(user);

        //    return userDto;
        //}

        //var users = _loggedInUserService.IsSiteAdmin
        //    ? Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
        //        .AsNoTracking()
        //        .OrderByDescending(x => x.Id)
        //    : Entities.Where(x =>
        //            x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
        //            !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //        .AsNoTracking()
        //        .OrderByDescending(x => x.Id);

        //var userDtos = MapUsers(users);

        //return userDtos;
    }

    public async Task<List<User>> GetUserNames()
    {
        //return await _baseQuery.OrderBy(user => user.LoginName)
        //    .Select(user => new User
        //    {
        //        ReferenceId = user.ReferenceId,
        //        LoginName = user.LoginName,
        //        Role = user.Role,
        //        RoleName = user.RoleName
        //    })
        //    .ToListAsync();


        var usersQuery = _loggedInUserService.IsParent
            ? await _dbContext.Users.AsNoTracking().Active()
                .Select(x => new User
                    { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
                .OrderBy(x => x.LoginName).ToListAsync()
            : await _dbContext.Users.AsNoTracking().Active()
                .Where(x=>x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new User
                    { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
                .OrderBy(x => x.LoginName).ToListAsync();

        return !_loggedInUserService.IsSiteAdmin
            ? usersQuery.Where(x => !x.RoleName.Equals(UserRole.SiteAdmin.ToString())).ToList()
            : usersQuery;


        //if (!_loggedInUserService.IsParent)
        //    return _loggedInUserService.IsSiteAdmin
        //        ? _dbContext.Users.Active()
        //            .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
        //            .Select(x => new User { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
        //            .OrderBy(x => x.LoginName)
        //            .ToListAsync()
        //        : _dbContext.Users.Active()
        //            .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
        //                        !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //            .Select(x => new User { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
        //            .OrderBy(x => x.LoginName)
        //            .ToListAsync();
        //return _loggedInUserService.IsSiteAdmin
        //    ? _dbContext.Users
        //        .Active()
        //        .Select(x => new User { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
        //        .OrderBy(x => x.LoginName)
        //        .ToListAsync()
        //    : _dbContext.Users
        //        .Active()
        //        .Where(x => !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //        .Select(x => new User { ReferenceId = x.ReferenceId, LoginName = x.LoginName, Role = x.Role, RoleName = x.RoleName })
        //        .OrderBy(x => x.LoginName)
        //        .ToListAsync();
    }

    public async Task<bool> IsLoginRoleUnique(string role)
    {
        return await Task.FromResult(!_loggedInUserService.IsAdministrator || !role.Equals(UserRole.SuperAdmin.ToString()));
        
        //if (_loggedInUserService.IsAdministrator && role.Equals(_keyValuePairs[UserRole.SuperAdmin.ToString()]))
        //    return false;

        //return true;
    }

    public async Task<bool> HasUserAsync()
    {
        return await _dbContext.Users
            .AsNoTracking()
            .Active()
            .Take(1)
            .AnyAsync();
    }
     
    public async Task<bool> IsUserNameExist(string loginName, string id)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.Users.AnyAsync(e => e.LoginName.Equals(loginName));
        }
        return await _dbContext.Users.AnyAsync(e => e.LoginName.Equals(loginName) && e.ReferenceId != id);

        //return !id.IsValidGuid()

        //    ? Task.FromResult(_dbContext.Users.Any(e => e.LoginName.Equals(loginName)))

        //    : Task.FromResult(_dbContext.Users.Where(e => e.LoginName.Equals(loginName)).ToList().Unique(id));
    }

    public override async Task<User> GetByReferenceIdAsync(string id)
    {
        return await MapUsers(_baseQuery.Where(u => u.ReferenceId == id)).SingleOrDefaultAsync();

        //var result = _loggedInUserService.IsSiteAdmin
        //    ? base.GetByReferenceIdAsync(id, x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReferenceId.Equals(id))
        //    : _loggedInUserService.IsParent
        //        ? base.FilterBy(x => x.ReferenceId.Equals(id) && !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //        : base.FilterBy(x => x.Equals(id) && x.CompanyId.Equals(_loggedInUserService.CompanyId) && !x.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]));

        //var mapUser = MapUsers(result);

        //return Task.FromResult(mapUser.SingleOrDefault());
    }

    public async Task<List<User>> GetUsersByCompanyId(string companyId)
    {
      return await _dbContext.Users.Active().Where(user => user.CompanyId.Equals(companyId)).ToListAsync();
    }

    public async Task<IReadOnlyList<User>> GetUsersByRole(string role)
    {
        return await MapUsers(_baseQuery.Where(u => u.Role == role)).ToListAsync();

        //var user = _loggedInUserService.IsSiteAdmin
        //    ? _loggedInUserService.IsParent
        //        ? await _dbContext.Users.Active().Where(user => user.Role.Equals(role)).ToListAsync()
        //        : await _dbContext.Users.Active().Where(user =>
        //            user.Role.Equals(role) && user.CompanyId.Equals(_loggedInUserService.CompanyId)).ToListAsync()
        //    : _loggedInUserService.IsParent
        //        ? await _dbContext.Users.Active().Where(user =>
        //                user.Role.Equals(role) && !user.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]))
        //            .ToListAsync()
        //        : await _dbContext.Users.Active().Where(user =>
        //            user.Role.Equals(role) && user.CompanyId.Equals(_loggedInUserService.CompanyId) &&
        //            !user.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()])).ToListAsync();

        //var userDto = MapUsers(user.AsQueryable());

        //return userDto.ToList();
    }

    public async Task<List<User>> GetUsersByRoleId(string roleId)
    {
        return await _dbContext.Users.Active().Where(user => user.Role.Equals(roleId)).ToListAsync();
    }

    public async Task<User> FindByLoginNameAsync(string loginName)
    {
        return await _dbContext.Users
            .AsNoTracking()
            .Select(e => new User
            {
                Id = e.Id,
                ReferenceId = e.ReferenceId,
                LoginName = e.LoginName,
                LoginPassword = e.LoginPassword,
                CompanyId = e.CompanyId,
                CompanyName = e.CompanyName,
                Role = e.Role,
                RoleName = e.RoleName,
                LoginType = e.LoginType,
                IsLock = e.IsLock,
                InfraObjectAllFlag = e.InfraObjectAllFlag,
                IsDefaultDashboard = e.IsDefaultDashboard,
                IsGroup = e.IsGroup,
                IsReset = e.IsReset,
                IsVerify = e.IsVerify,
                SessionTimeout = e.SessionTimeout,
                TwoFactorAuthentication = e.TwoFactorAuthentication,
                Url = e.Url
            })
            .FirstOrDefaultAsync(e => e.LoginName.Equals(loginName));
    }

    public async Task<User> FindByReferenceIdAsync(string id)
    {
        return await _dbContext.Users.Active().FirstOrDefaultAsync(e => e.ReferenceId.Equals(id));
    }

    public async Task<bool> IsUserNameUnique(string name)
    {
        return await _dbContext.Users.AnyAsync(e => e.LoginName.Equals(name));
    }

    #region AD

    [SupportedOSPlatform("windows")]
    public async Task<List<string>> GetDomains()
    {
        var domains = new List<string>();
        foreach (System.DirectoryServices.ActiveDirectory.Domain d in Forest.GetCurrentForest().Domains)
            domains.Add(d.Name);
        return await Task.FromResult(domains);
    }

    [SupportedOSPlatform("windows")]
    public async Task<List<string>> GetDomainUsers(string domainName)
    {
        var searcher = new UserPrincipal(new PrincipalContext(ContextType.Domain, domainName));
        var pS = new PrincipalSearcher(searcher);

        var domainUserName = pS.FindAll()
            .Select(u => ((UserPrincipal)u).SamAccountName)
            .ToList();

        return await Task.FromResult(domainUserName);
    }

    [SupportedOSPlatform("windows")]
    public async Task<Dictionary<string, string>> GetDomainUsersAndEmails(string domainName)
    {
        // Set up a searcher to look up users from the specified domain
        var searcher = new UserPrincipal(new PrincipalContext(ContextType.Domain, domainName));
        var pS = new PrincipalSearcher(searcher);

        // Use the PrincipalSearcher to find all users and return a dictionary with SamAccountName and Email Address
        var userEmailDictionary = pS.FindAll()
            .OfType<UserPrincipal>()
            .ToDictionary(u => u.SamAccountName, u => u.EmailAddress);

        return await Task.FromResult(userEmailDictionary);
    }

    [SupportedOSPlatform("windows")]
    public async Task<List<string>> GetDomainGroups(string domainName)
    {
        using var principalContext = new PrincipalContext(ContextType.Domain, domainName);
        using var groupPrincipal = new GroupPrincipal(principalContext);
        using var principalSearcher = new PrincipalSearcher(groupPrincipal);

        var groupList = principalSearcher.FindAll()
            .Select(g => ((GroupPrincipal)g).Name)
            .ToList();

        return await Task.FromResult(groupList);
    }

    [SupportedOSPlatform("windows")]
    public async Task<List<string>> SearchDomainGroups(string domainName, string groupName)
    {
        using var principalContext = new PrincipalContext(ContextType.Domain, domainName);
        using var groupPrincipal = new GroupPrincipal(principalContext);
        using var principalSearcher = new PrincipalSearcher(groupPrincipal);

        var groupList = principalSearcher.FindAll()
            .Select(g => (GroupPrincipal)g)
            .Where(g => g.Name.StartsWith(groupName, StringComparison.OrdinalIgnoreCase))
            .Select(g => g.Name)
            .ToList();

        return await Task.FromResult(groupList);
    }

    [SupportedOSPlatform("windows")]
    public async Task<bool> IsUserMemberOfGroup(string loginName, string groupName)
    {
        var splitLoginName = Regex.Split(loginName, @"\\");

        var domainName = splitLoginName[0];
        var domainLoginName = splitLoginName[1];

        using var principalContext = new PrincipalContext(ContextType.Domain, domainName);
        using var groupPrincipal = GroupPrincipal.FindByIdentity(principalContext, groupName);
        if (groupPrincipal != null)
        {
            using var userPrincipal = UserPrincipal.FindByIdentity(principalContext, domainLoginName);
            if (userPrincipal != null)
            {
                return userPrincipal.IsMemberOf(groupPrincipal);
            }
        }
        return await Task.FromResult(false);
    }


    [SupportedOSPlatform("windows")]
    public async Task<List<string>> GetDomainUsersByUserName(string domainName, string userName)
    {
        using var principalContext = new PrincipalContext(ContextType.Domain, domainName);
        var searcher = new PrincipalSearcher(new UserPrincipal(principalContext));
        var users = searcher.FindAll()
            .Select(u => (UserPrincipal)u)
            .Where(u => u.SamAccountName.ToLower().StartsWith(userName.ToLower()))
            .Select(u => u.SamAccountName)
            .ToList();

        return await Task.FromResult(users);
    }


    [SupportedOSPlatform("windows")]
    public Task<bool> IsValidActiveDirectoryUser(string loginName, string password)
    {
        using var context = new PrincipalContext(ContextType.Domain);

        var isValid = context.ValidateCredentials(loginName, password);

        return Task.FromResult(isValid);
    }
    #endregion

    private IQueryable<User> MapUsers(IQueryable<User> users)
    {
        return from user in users
            join company in _dbContext.Companies.Active() on user.CompanyId equals company.ReferenceId into companyGroup
            from company in companyGroup.DefaultIfEmpty()
            join userRole in _dbContext.UserRoles.Active() on user.Role equals userRole.ReferenceId into userRoleGroup
            from userRole in userRoleGroup.DefaultIfEmpty()
            select new User
            {
                Id = user.Id,
                ReferenceId = user.ReferenceId,
                LoginName = user.LoginName,
                LoginPassword = user.LoginPassword,
                CompanyId = company.ReferenceId,
                CompanyName = company.DisplayName,
                Role = userRole.ReferenceId,
                RoleName = userRole.Role,
                LoginType = user.LoginType,
                IsLock = user.IsLock,
                IsReset = user.IsReset,
                IsGroup = user.IsGroup,
                IsDefaultDashboard = user.IsDefaultDashboard,
                Url = user.Url,
                InfraObjectAllFlag = user.InfraObjectAllFlag,
                SessionTimeout = user.SessionTimeout,
                IsVerify = user.IsVerify,
                TwoFactorAuthentication = user.TwoFactorAuthentication,
                IsActive = user.IsActive,
                CreatedBy = user.CreatedBy,
                CreatedDate = user.CreatedDate,
                LastModifiedBy = user.LastModifiedBy,
                LastModifiedDate = user.LastModifiedDate,
            };

        //var mappedUser = users.Select(data => new
        //{
        //    User = data,
        //    Company = _dbContext.Companies.Active().FirstOrDefault(x => x.ReferenceId.Equals(data.CompanyId)),
        //    UserRole = _dbContext.UserRoles.Active().FirstOrDefault(x => x.ReferenceId.Equals(data.Role)),

        //});

        //var mappedUserQuery = mappedUser.Select(result => new User
        //{
        //    Id = result!.User!.Id,
        //    ReferenceId = result!.User!.ReferenceId,
        //    LoginName = result!.User!.LoginName,
        //    LoginPassword = result!.User!.LoginPassword,
        //    CompanyId = result!.Company!.ReferenceId,
        //    CompanyName = result!.Company!.DisplayName,
        //    Role = result!.UserRole!.ReferenceId,
        //    RoleName = result!.UserRole!.Role,
        //    LoginType = result!.User!.LoginType,
        //    IsLock = result!.User!.IsLock,
        //    IsReset = result!.User!.IsReset,
        //    IsGroup = result!.User!.IsGroup,
        //    IsDefaultDashboard = result!.User!.IsDefaultDashboard,
        //    Url = result!.User!.Url,
        //    InfraObjectAllFlag = result!.User!.InfraObjectAllFlag,
        //    SessionTimeout = result!.User!.SessionTimeout,
        //    IsVerify = result!.User!.IsVerify,
        //    TwoFactorAuthentication = result!.User!.TwoFactorAuthentication,
        //    IsActive = result!.User!.IsActive,
        //    CreatedBy = result!.User!.CreatedBy,
        //    CreatedDate = result!.User!.CreatedDate,
        //    LastModifiedBy = result!.User!.LastModifiedBy,
        //    LastModifiedDate = result!.User!.LastModifiedDate,
        //});

        //return mappedUserQuery;
    }

    private IQueryable<User> BuildBaseQuery()
    {
        var query = _dbContext.Users.AsNoTracking().Active();
        if (!_loggedInUserService.IsParent)
        {
            query = query.Where(user => user.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                                        (_loggedInUserService.IsSiteAdmin || !user.Role.Equals(UserRole.SiteAdmin.ToString())));
        }
        else if (!_loggedInUserService.IsSiteAdmin)
        {
            query = query.Where(user => !user.Role.Equals(UserRole.SiteAdmin.ToString()));
        }
        //if (!_loggedInUserService.IsParent)
        //{
        //    query = query.Where(user => user.CompanyId.Equals(_loggedInUserService.CompanyId));

        //    if (!_loggedInUserService.IsSiteAdmin)
        //    {
        //        query = query.Where(user => !user.Role.Equals(_keyValuePairs[UserRole.SiteAdmin.ToString()]));
        //    }
        //}
        return query;
    }

}