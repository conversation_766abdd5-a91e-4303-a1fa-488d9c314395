﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.PaginatedView;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using System.Reflection;


namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SolutionMappingControllerShould
    {
        
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IPageSolutionMappingService> _mockPageSolutionMappingService = new();
        private readonly Mock<ILogger<SolutionMappingController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new(); 
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private  SolutionMappingController _controller;

        public SolutionMappingControllerShould()
        {
            Initialize();

        }
        internal void Initialize()
        {
            _controller = new SolutionMappingController(
                 _mockPublisher.Object,
                 _mockDataProvider.Object,
                 _mockLogger.Object,
                 _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsViewResult()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPageWidgetList_ReturnsJsonResult_OnSuccess()
        {
            // Arrange
            var pageSolutionList = new List< PageSolutionMappingListVm>();
			_mockDataProvider.Setup(s => s.PageSolutionMapping.GetPageSolutionMappingList())
                .ReturnsAsync(pageSolutionList);

            
            // Act
            var result = await _controller.GetPageWidgetList() as JsonResult;

            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);

        }
        [Fact]
        public async Task GetPageWidgetList_ReturnsJsonResult_OnFailure()
        {
            // Arrange
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPageSolutionMappingList())
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.GetPageWidgetList() as JsonResult;
            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);

        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesEntity_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreatePageSolutionMappingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
			_mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Value is BaseResponse { Success: true, Message: "Created" });
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesEntity_WhenIdIsProvided()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdatePageSolutionMappingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockMapper.Setup(m => m.Map<UpdatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
			_mockDataProvider.Setup(s => s.PageSolutionMapping.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Value is BaseResponse { Success: true, Message: "Updated" });
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted" };

            _mockDataProvider.Setup(s => s.PageSolutionMapping.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);

        }

        [Fact]
        public async Task GetPageBuilderList_ReturnsJsonResult_OnSuccess()
        {
            // Arrange
            var builderList = new List<PageBuilderListVm>();
            _mockDataProvider.Setup(d => d.PageBuilder.GetPageBuilderList())
                .ReturnsAsync(builderList);

            // Act
            var result = await _controller.GetPageBuilderList() as JsonResult;
            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetPageBuilderList_ReturnsJsonResult_OnFailure()
        {
            // Arrange
            _mockDataProvider.Setup(d => d.PageBuilder.GetPageBuilderList())
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.GetPageBuilderList() as JsonResult;
            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_OnSuccess()
        {
            // Arrange
            var query = new GetPageSolutionMappingPaginatedListQuery();
            var paginatedList = new PaginatedResult<PageSolutionMappingListVm>();
			_mockDataProvider.Setup(s => s.PageSolutionMapping.GetPaginatedPageSolutionMapping(query))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;
            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult_OnFailure()
        {
            // Arrange
            var query = new GetPageSolutionMappingPaginatedListQuery();
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPaginatedPageSolutionMapping(query))
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsJsonResult_OnSuccess()
        {
            // Arrange
            var databaseId = "db1";
            var replicationMasterId = "rm1";
            var type = "type1";
            var replicationNames = new List<InfraReplicationMappingListVm>();

            _mockDataProvider.Setup(d => d.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
                .ReturnsAsync(replicationNames);

            // Act
            var result = await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type) as JsonResult;
            dynamic jsonResponse = result.Value;
            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsJsonResult_OnFailure()
        {
            // Arrange
            var databaseId = "db1";
            var replicationMasterId = "rm1";
            var type = "type1";

            _mockDataProvider.Setup(d => d.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
                .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== MISSING TEST CASES FOR 100% COVERAGE =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new SolutionMappingController(
                _mockPublisher.Object,
                _mockDataProvider.Object,
                _mockLogger.Object,
                _mockMapper.Object);

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public async Task List_ShouldPublishEvent()
        {
            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageSolutionMappingPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task List_ShouldLogDebugMessage()
        {
            // Act
            await _controller.List();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering List method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var command = new CreatePageSolutionMappingCommand();
            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleGeneralException()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreatePageSolutionMappingCommand();
            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(s => s.PageSolutionMapping.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete error"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(SolutionMappingController);

            // Act
            var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void List_ShouldHaveAntiXssAttribute()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("List");

            // Act
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetPageWidgetList_ShouldHaveAntiXssAttribute()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("GetPageWidgetList");

            // Act
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("CreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void Delete_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("Delete");

            // Act
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetPageBuilderList_ShouldHaveAntiXssAttribute()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("GetPageBuilderList");

            // Act
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetPagination_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("GetPagination");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetTypeByDatabaseIdAndReplicationMasterId_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(SolutionMappingController).GetMethod("GetTypeByDatabaseIdAndReplicationMasterId");

            // Act
            var httpGetAttribute = method.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        // ===== ADDITIONAL COMPREHENSIVE TESTS FOR 100% COVERAGE =====

        [Fact]
        public async Task GetPageWidgetList_ShouldLogDebugMessages()
        {
            // Arrange
            var pageSolutionList = new List<PageSolutionMappingListVm>();
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPageSolutionMappingList())
                .ReturnsAsync(pageSolutionList);

            // Act
            await _controller.GetPageWidgetList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetPageWidgetList method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully retrieved page widget list in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldLogDebugForCreate()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreatePageSolutionMappingCommand { Name = "TestName" };
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(viewModel);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Creating solution mapping 'TestName'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldLogDebugForUpdate()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdatePageSolutionMappingCommand { Name = "UpdatedName" };
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockMapper.Setup(m => m.Map<UpdatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            await _controller.CreateOrUpdate(viewModel);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Updating solution mapping 'UpdatedName'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Delete_ShouldLogDebugMessages()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted" };

            _mockDataProvider.Setup(s => s.PageSolutionMapping.DeleteAsync(id))
                .ReturnsAsync(response);

            // Act
            await _controller.Delete(id);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering Delete method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully deleted record in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPageBuilderList_ShouldLogDebugMessages()
        {
            // Arrange
            var builderList = new List<PageBuilderListVm>();
            _mockDataProvider.Setup(d => d.PageBuilder.GetPageBuilderList())
                .ReturnsAsync(builderList);

            // Act
            await _controller.GetPageBuilderList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetPageBuilderList method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully retrieved page builder list in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPagination_ShouldLogDebugMessages()
        {
            // Arrange
            var query = new GetPageSolutionMappingPaginatedListQuery();
            var paginatedList = new PaginatedResult<PageSolutionMappingListVm>();
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPaginatedPageSolutionMapping(query))
                .ReturnsAsync(paginatedList);

            // Act
            await _controller.GetPagination(query);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetPagination method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully retrieved solution mapping paginated list on SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_ShouldLogDebugMessages()
        {
            // Arrange
            var databaseId = "db1";
            var replicationMasterId = "rm1";
            var type = "type1";
            var replicationNames = new List<InfraReplicationMappingListVm>();

            _mockDataProvider.Setup(d => d.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
                .ReturnsAsync(replicationNames);

            // Act
            await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetTypeByDatabaseIdAndReplicationMasterId method in SolutionMapping")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Successfully retrieved infra replication mapping by databaseId '{databaseId}' and replicationMasterId '{replicationMasterId}'")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== EXCEPTION HANDLING AND EDGE CASES =====

        [Fact]
        public async Task CreateOrUpdate_ValidationException_ShouldLogError()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var command = new CreatePageSolutionMappingCommand();
            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            await _controller.CreateOrUpdate(viewModel);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Validation error on solution mapping page: Name is required")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPageWidgetList_Exception_ShouldLogError()
        {
            // Arrange
            var exception = new Exception("Test error");
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPageSolutionMappingList())
                .ThrowsAsync(exception);

            // Act
            await _controller.GetPageWidgetList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occured on solution mapping page while retrieving the page widget list. Exception : Test error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPageBuilderList_Exception_ShouldLogError()
        {
            // Arrange
            var exception = new Exception("Test error");
            _mockDataProvider.Setup(d => d.PageBuilder.GetPageBuilderList())
                .ThrowsAsync(exception);

            // Act
            await _controller.GetPageBuilderList();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occured on solution mapping page while retrieving the page builder list. Exception : Test error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPagination_Exception_ShouldLogError()
        {
            // Arrange
            var query = new GetPageSolutionMappingPaginatedListQuery();
            var exception = new Exception("Test error");
            _mockDataProvider.Setup(s => s.PageSolutionMapping.GetPaginatedPageSolutionMapping(query))
                .ThrowsAsync(exception);

            // Act
            await _controller.GetPagination(query);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred on solution mapping page while processing the pagination request. Exception : Test error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetTypeByDatabaseIdAndReplicationMasterId_Exception_ShouldLogError()
        {
            // Arrange
            var databaseId = "db1";
            var replicationMasterId = "rm1";
            var type = "type1";
            var exception = new Exception("Test error");

            _mockDataProvider.Setup(d => d.InfraReplicationMapping.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type))
                .ThrowsAsync(exception);

            // Act
            await _controller.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred on solution mapping page while retrieving infra replication mapping by databaseId and replicationMasterId. Exception : Test error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Delete_Exception_ShouldLogError()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Delete error");
            _mockDataProvider.Setup(s => s.PageSolutionMapping.DeleteAsync(id))
                .ThrowsAsync(exception);

            // Act
            await _controller.Delete(id);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred while deleting record on solution mapping. Exception : Delete error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_GeneralException_ShouldLogError()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var exception = new Exception("General error");
            var command = new CreatePageSolutionMappingCommand();
            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ThrowsAsync(exception);

            // Act
            await _controller.CreateOrUpdate(viewModel);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("An error occurred on solution mapping page while processing the request for create or update. Exception : General error")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== EDGE CASES AND BOUNDARY CONDITIONS =====

        [Fact]
        public async Task CreateOrUpdate_WithWhitespaceId_ShouldTreatAsCreate()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "   "); // Whitespace only
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreatePageSolutionMappingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(s => s.PageSolutionMapping.CreateAsync(command), Times.Once);
            _mockDataProvider.Verify(s => s.PageSolutionMapping.UpdateAsync(It.IsAny<UpdatePageSolutionMappingCommand>()), Times.Never);
        }

        [Fact]
        public async Task CreateOrUpdate_WithNullId_ShouldTreatAsCreate()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<PageSolutionMappingViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", (string)null); // Null value
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreatePageSolutionMappingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(m => m.Map<CreatePageSolutionMappingCommand>(viewModel))
                .Returns(command);
            _mockDataProvider.Setup(s => s.PageSolutionMapping.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            _mockDataProvider.Verify(s => s.PageSolutionMapping.CreateAsync(command), Times.Once);
            _mockDataProvider.Verify(s => s.PageSolutionMapping.UpdateAsync(It.IsAny<UpdatePageSolutionMappingCommand>()), Times.Never);
        }
    }
}
