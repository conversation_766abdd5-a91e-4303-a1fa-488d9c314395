﻿let infraObject = sessionStorage.getItem("infraobjectId");
let moniterType = sessionStorage.getItem("moniterType");

//let moniterStatus = sessionStorage.getItem("moniterStatus")
let databaseImage;

$(document).on('change', '#clusterDetails', function () {
    mode = $('#clusterDetails').find(':selected').text();
    monitoringSolution(infraObjectId, mode)

});
async function monitoringSolution(infraObject,mode) {
    
    $.ajax({
        url: "/Monitor/Monitoring/GetInfraObjectDetailsById",
        method: 'GET',
        data: {
            infraObjectId: infraObject,
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res?.data;
            
            if (value != null) {
                
                let modeValue = mode;
                let prServer = "";
                let prDB = "";
                let drServer = "";
                let drDB = "";
                let type = value?.typeName
                
                let moniterStatus = checkValue(value?.drOperationStatus)
                let replicationType = checkValue(value?.replicationTypeName)                
                let replicationTypemonitor = checkValue(value?.replicationTypeName)
                let infrastate = checkValue(value?.state)

                am4core.useTheme(am4themes_animated);
                am4core.options.autoSetClassName = true;
                // Create chart
                var chart = am4core.create("Solution_Diagram", am4plugins_forceDirected.ForceDirectedTree);

                if (chart.logo) {
                    chart.logo.disabled = true;
                }
                // Create series
                var series = chart.series.push(
                    new am4plugins_forceDirected.ForceDirectedSeries()
                );

                if (replicationType?.toLowerCase() === "application - no replication" || replicationType?.toLowerCase() === "application - no - replication" || replicationType?.toLowerCase() === "application-no replication" || replicationType?.toLowerCase() === "application-no-replication") {
                    xValueParent = 10;
                    xValueChild = 30;
                    xValueSubChild = 70;
                    xValueLastChild = 90;

                }
                if (moniterType === null) {
                    xValueParent = 10;
                    xValueChild = 30;
                    xValueSubChild = 70;
                    xValueLastChild = 90;

                }

                switch (true) {
                    
                    case moniterType?.toLowerCase()?.includes("goldengate") && value?.subType?.toLowerCase()?.includes("oracle"):
                        image = "/img/charts_img/DataCenter/oracle golden gate.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("azurestorage"):
                        image = "/img/charts_img/DataCenter/Azure_storage.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("azuremssqlpaas"):
                        image = "/img/charts_img/DataCenter/Azure_MSSQL.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("azuremysqlpaas"):
                        image = "/img/charts_img/DataCenter/Azure_Mysql.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("recoverpointforvm"):
                        image = "/img/charts_img/DataCenter/Azure_RecoverPointVm.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("oracle") || (value?.subType)?.toLowerCase()?.includes("oracle"):
                    case moniterType?.toLowerCase()?.includes("oracle_dataguard") || (value?.subType)?.toLowerCase()?.includes("oracle_dataguard"):
                    case moniterType?.toLowerCase()?.includes("rac") || (value?.subType)?.toLowerCase()?.includes("oraclerac") || (value?.subType)?.toLowerCase()?.includes("rac"):
                        image = "/img/charts_img/DataCenter/oracle.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("mysql") || (value?.subType)?.toLowerCase()?.includes("mysql"):
                        image = "/img/charts_img/DataCenter/my_sql.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("mssql") || moniterType?.toLowerCase()?.includes("nls") || replicationType?.toLowerCase()?.includes('2kx') || (value?.subType)?.toLowerCase()?.includes("2kx") ||  (value?.subType)?.toLowerCase()?.includes("mssqlalwayson") || (value?.subType)?.toLowerCase()?.includes("always") || (value?.subType)?.toLowerCase()?.includes("mssql"):
                        image = "/img/charts_img/DataCenter/MSSQL.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("postgres") || (value?.subType)?.toLowerCase()?.includes("postgres"):
                        image = "/img/charts_img/DataCenter/Postgresql.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("db2hadr") || (value?.subType)?.toLowerCase()?.includes("db2hadr") || (value?.subType)?.toLowerCase()?.includes("ibm"):
                        image = "/img/charts_img/DataCenter/IBM.svg";
                        break;
                    case moniterType?.toLowerCase() === "mongodb" || (value?.subType)?.toLowerCase()?.includes("mongodb"):
                        image = "/img/Database_Icon/cp_mongo_db.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("hyperv") || (value?.subType)?.toLowerCase()?.includes("hyperv"):
                        image = "/img/charts_img/DataCenter/windows-1.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("rsync") || (value?.subType)?.toLowerCase()?.includes("rsync"):
                        image = "/img/charts_img/DataCenter/rsync.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("robocopy") || (value?.subType)?.toLowerCase()?.includes("robocopy"):
                        image = "/img/charts_img/DataCenter/RoboCopynew.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("activedirectory"):
                        image = "/img/charts_img/DataCenter/windows-activedirectory.svg";
                        break;
                    case moniterType?.toLowerCase() === "mssqldbmirroring" || (value?.subType)?.toLowerCase()?.includes("mirror"):
                        image = "/img/charts_img/DataCenter/MSSQL.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("as400") || (value?.subType)?.toLowerCase()?.includes("as400"):
                        image = "/img/charts_img/DataCenter/IBM-AIX.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("netapp") || (value?.subType)?.toLowerCase()?.includes("netapp"):
                        image = "/img/DB-Logo/cp_netapp.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("hp3par") || (value?.subType)?.toLowerCase()?.includes("hp3par"):
                        image = "/img/charts_img/DataCenter/3PAR_logo.svg";
                        break;
                    case moniterType?.toLowerCase()?.includes("srm") || (value?.subType)?.toLowerCase()?.includes("srm"):
                        image = "/img/charts_img/DataCenter/srm_vmware.svg";
                        break;
                    case moniterType?.toLowerCase() === "applicationnoreplication":
                    case replicationType?.toLowerCase() === "application - no replication":
                    case replicationType?.toLowerCase() === "application - no - replication":
                    case replicationType?.toLowerCase() === "application-no replication":
                    case replicationType?.toLowerCase() === "application-no-replication":
                        image = "/img/charts_img/DataCenter/replication-off.svg";
                        break;
                    default:
                        image = "/img/charts_img/DataCenter/defaultapplication.svg";
                        break;

                }

                let isSRM = moniterType?.toLowerCase() === 'srm';


                let SRMArray = value?.serverDto?.filter((d) => d?.type === 'SRMServer');
                //let nodePro = value?.nodeProperties ? JSON.parse(value?.nodeProperties) : value.nodeProperties;
                //console.log(nodePro,'res')
                if (moniterType === "MssqlAlwaysOn" || moniterType === "Postgres" || moniterType === "Mysql" || moniterType === "Oracle" || moniterType === "MssqlNLS" || moniterType === "HyperV" || moniterType === "DB2HADR" || moniterType === "MongoDB" || moniterType === "RSyncAppReplication" || moniterType === "NetAppSnapMirror" || moniterType === "RoboCopy" || moniterType === "CyberRecover" || moniterType?.toLowerCase() === "mssqldbmirroring" || moniterType === "AS400" || moniterType?.toLowerCase() === "azuremysqlpaas" || moniterType?.toLowerCase() === "azurepostgrespaas" || moniterType?.toLowerCase() === "azuremssqlpaas" || moniterType?.toLowerCase() === "azurestorageaccount" || moniterType?.toLowerCase() === "goldengatereplication" || value?.subType?.toLowerCase()?.includes("ibm") || value?.subType?.toLowerCase()?.includes("mirror") || replicationType?.toLowerCase() === "rpfovm replication" || value?.subType?.toLowerCase()?.includes("mssqlnls") || replicationType?.toLowerCase() === "application no-replication" 
                    || moniterType === "Hp3par" || (moniterType === "Oracle" && !value?.replicationTypeName?.toLowerCase()?.includes('oraclerac') || !value?.replicationTypeName?.toLowerCase()?.includes('oracle-rac')) || value?.subType?.toLowerCase()?.includes("mysql") || value?.subType?.toLowerCase()?.includes("mssqlalwayson") || value?.subType?.toLowerCase()?.includes("postgres") || value?.subType?.toLowerCase()?.includes("cyber") || value?.subType?.toLowerCase()?.includes("hyperv") || value?.subType?.toLowerCase()?.includes("mongodb") || value?.subType?.toLowerCase()?.includes("as400")) {

                    let PRServerArray = value?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'))
                    let DRServerArray = value?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'))
                    let PRDBArray = value?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('pr'))
                    let DRDBArray = value?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('dr'))
                    let ipsolution = PRServerArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray[0]?.hostName : PRServerArray[0]?.ipAddress
                    var pripaddress = checkValue(ipsolution)
                    var prOstype = checkValue(PRServerArray[0]?.osType)
                    var prOslocation = checkValue(PRServerArray[0]?.location)
                    var prhost = checkValue(PRServerArray[0]?.hostName)
                    var prStatus = checkValue(PRServerArray[0]?.status)                  
                   
                    var prdbStatus = checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status)
                    
                    var prroleType = checkValue(PRServerArray[0]?.roleType)
                    prServer = checkValue(PRServerArray[0]?.status);
                    prDB = checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status);
                   
                    var dripaddress = checkValue(DRServerArray[0]?.ipAddress)
                    var drOstype = checkValue(DRServerArray[0]?.osType)
                    var drOslocation = checkValue(DRServerArray[0]?.location)
                    var drhost = checkValue(DRServerArray[0]?.hostName)
                    var drStatus = checkValue(DRServerArray[0]?.status)
                    var drdbStatus = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status)
                    var drroleType = checkValue(value?.serverDto[1]?.roleType)
                    drServer = checkValue(DRServerArray[0]?.status)
                    drDB = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status);

                    var prdatabase = checkValue(PRDBArray[0]?.sid)
                   
                    var prversion = checkValue(PRDBArray[0]?.version)
                    var prdatabasetype = checkValue(PRDBArray[0]?.databaseType)

                    var drdatabase = checkValue(DRDBArray[0]?.sid)
                    var drversion = checkValue(DRDBArray[0]?.version)
                    var drdatabasetype = checkValue(DRDBArray[0]?.databaseType)
                }
                if (moniterType === "SRM") {

                    PRSRMServer = value?.serverDto?.filter((d) => d?.serverType == 'PRESXIServer')
                    DRSRMServer = value?.serverDto?.filter((d) => d?.serverType == 'DRESXIServer')
                    var srmip = value?.serverDto[0]?.connectViaHostName?.toLowerCase() === "true" ? value?.serverDto[0]?.hostName : value?.serverDto[0]?.ipAddress
                    var srmip1 = SRMArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRSRMServer[0]?.hostName : PRSRMServer[0]?.ipAddress
                    var pripaddress = checkValue(srmip)
                    var dripaddress = checkValue(value?.serverDto[1]?.ipAddress)
                    var prdatabase = checkValue(SRMArray[0]?.ipAddress)
                    var drdatabase = checkValue(SRMArray[1]?.ipAddress)
                    var prOstype = checkValue(PRSRMServer[0]?.osType)
                    var drOstype = checkValue(DRSRMServer[0]?.osType)
                    var prOslocation = checkValue(PRSRMServer[0]?.location)
                    var drOslocation = checkValue(DRSRMServer[0]?.location)
                    var prhost = checkValue(PRSRMServer[0]?.hostName)
                    var drhost = checkValue(DRSRMServer[0]?.hostName)
                    var prStatus = checkValue(PRSRMServer[0]?.status)
                    var drStatus = checkValue(DRSRMServer[0]?.status)
                    var prdbStatus = checkValue(SRMArray[0]?.status)
                    var drdbStatus = checkValue(SRMArray[1]?.status)
                    var prversion = checkValue(SRMArray[0]?.version)
                    var drversion = checkValue(SRMArray[1]?.version)
                    var prdatabasetype = checkValue(value?.databaseDto[0]?.databaseType)
                    var drdatabasetype = checkValue(value?.databaseDto[1]?.databaseType)
                    var prroleType = checkValue(PRSRMServer[0]?.roleType)
                    var drroleType = checkValue(DRSRMServer[0]?.roleType)
                    prServer = checkValue(PRSRMServer[0]?.status);
                    prDB = checkValue(SRMArray[0]?.status)
                    drServer = checkValue(DRSRMServer[0]?.status)
                    drDB = checkValue(SRMArray[1]?.status)

                    var prsrmHost = checkValue(PRSRMServer[0]?.hostName)
                    var drsrmHost = checkValue(DRSRMServer[0]?.hostName)
                    var prsrmLocation = checkValue(PRSRMServer[0]?.location)
                    var drsrmLocation = checkValue(DRSRMServer[0]?.location)
                    var prsrmOS = checkValue(PRSRMServer[0]?.osType)
                    var drsrmOS = checkValue(DRSRMServer[0]?.osType)


                }

                else if ((moniterType?.toLowerCase() === "oraclerac")) {


                    if ((moniterType?.toLowerCase() === "oraclerac")) {
                        let nodeArray = [];
                        let nodeArray1 = [];
                        let nodeName = $('#clusterDetails option:selected')?.text() === "" ? "Node1" : $('#clusterDetails option:selected')?.text()
                        if (nodeName?.toLowerCase()?.includes('1')) {
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('drdb_1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    nodeArray.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('drdb_1') ||item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                    nodeArray1.push(item);
                                }
                            });
                        } else if (nodeName?.toLowerCase()?.includes('2')) {
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    nodeArray.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') ||item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                    nodeArray1.push(item);
                                }
                            });
                        } else if (nodeName?.toLowerCase()?.includes('3')) {
                            value?.serverDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    nodeArray.push(item);
                                }
                            });
                            value?.databaseDto?.forEach((item) => {
                                if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                    nodeArray1.push(item);
                                }
                            });
                        }
                        value.serverDto = nodeArray
                        value.databaseDto = nodeArray1
                    }

                    let PRServerArray = value?.serverDto?.filter((d) => d.serverType?.toLowerCase()?.includes('pr'))
                    var PRserverType = PRServerArray[0]?.serverType 
                    let DRServerArray = value?.serverDto.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'))
                    let PRDBArray = value?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('pr'))
                    let DRDBArray = value?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('dr'))
                    let ipadvalue = PRServerArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray[0]?.hostName : PRServerArray[0]?.ipAddress
                    var pripaddress = checkValue(ipadvalue)
                    var prOstype = checkValue(PRServerArray[0]?.osType)
                    var prOslocation = checkValue(PRServerArray[0]?.location)
                    var prhost = checkValue(PRServerArray[0]?.hostName)
                    var prStatus = checkValue(PRServerArray[0]?.status)
                    var prdbStatus = checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status)
                   
                    var prroleType = checkValue(PRServerArray[0]?.roleType)
                    prServer = checkValue(PRServerArray[0]?.status);
                    prDB = checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status);

                    var dripaddress = checkValue(DRServerArray[0]?.ipAddress)
                    var drOstype = checkValue(DRServerArray[0]?.osType)
                    var drOslocation = checkValue(DRServerArray[0]?.location)
                    var drhost = checkValue(DRServerArray[0]?.hostName)
                    var drStatus = checkValue(DRServerArray[0]?.status)
                    var drdbStatus = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status)
                    var drroleType = checkValue(value.serverDto[1]?.roleType)
                    drServer = checkValue(DRServerArray[0]?.status)
                    drDB = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status);

                    var prdatabase = checkValue(PRDBArray[0]?.sid)
                    var prversion = checkValue(PRDBArray[0]?.version)
                    var prdatabasetype = checkValue(PRDBArray[0]?.databaseType)

                    var drdatabase = checkValue(DRDBArray[0]?.sid)
                    var drversion = checkValue(DRDBArray[0]?.version)
                    var drdatabasetype = checkValue(DRDBArray[0]?.databaseType)


                }
                else {
                    var PRServerArray = value?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'))
                    var PRserverType = PRServerArray[0]?.serverType 
                    let DRServerArray = value?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'))
                    let PRDBArray = value?.databaseDto.filter((d) => d?.type?.toLowerCase()?.includes('pr'))
                    let DRDBArray = value?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('dr'))
                    let ipprvalue = PRServerArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray[0]?.hostName : PRServerArray[0]?.ipAddress
                    var pripaddress = checkValue(ipprvalue)
                    var prOstype = checkValue(PRServerArray[0]?.osType)
                    var prOslocation = checkValue(PRServerArray[0]?.location)
                    var prhost = checkValue(PRServerArray[0]?.hostName)
                    var prStatus = checkValue(PRServerArray[0]?.status)
                    var prdbStatus = (type?.toLowerCase() !== "application" && type?.toLowerCase() !== "virtual") ? (checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status)) : 'NA'
                   /* var prdbStatus =  checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status)*/
                    var prroleType = checkValue(PRServerArray[0]?.roleType)
                    prServer = checkValue(PRServerArray[0]?.status);
                    prDB = checkValue(PRServerArray[0]?.status?.toLowerCase() != "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status);

                    var dripaddress = checkValue(DRServerArray[0]?.ipAddress)
                    var drOstype = checkValue(DRServerArray[0]?.osType)
                    var drOslocation = checkValue(DRServerArray[0]?.location)
                    var drhost = checkValue(DRServerArray[0]?.hostName)
                    var drStatus = checkValue(DRServerArray[0]?.status)
                    var drdbStatus = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status)
                    var drroleType = checkValue(value?.serverDto[1]?.roleType)
                    drServer = checkValue(DRServerArray[0]?.status)
                    drDB = checkValue(DRServerArray[0]?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : DRServerArray[0]?.status);

                    var prdatabase = PRDBArray?.length ? checkValue(PRDBArray[0]?.sid) : ''
                    var prversion = checkValue(PRDBArray[0]?.version)
                    var prdatabasetype = checkValue(PRDBArray[0]?.databaseType)

                    var drdatabase = checkValue(DRDBArray[0]?.sid) 
                    var drversion = checkValue(DRDBArray[0]?.version)
                    var drdatabasetype = checkValue(DRDBArray[0]?.databaseType)
                }
                setTimeout(() => {
                    if (moniterType?.toLowerCase()?.includes("srm") || moniterType?.toLowerCase()?.includes("openshift")) {

                        if (moniterType === "" || moniterType === undefined || value?.state?.toLowerCase() === "maintenance" || value?.serverDto[0]?.status?.toLowerCase() !== "up" || value?.serverDto[1]?.status?.toLowerCase() !== "up") {
                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')

                        }
                        else if (moniterStatus == 2) {

                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                        }
                        else {
                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
                        }
                    }
                    else {
                        if (moniterType === "" || moniterType === undefined || value?.state?.toLowerCase() === "maintenance" || value?.serverDto[0]?.status?.toLowerCase() !== "up" || value?.serverDto[1]?.status?.toLowerCase() !== "up" || value?.databaseDto[0]?.status?.toLowerCase() !== "up" || value?.databaseDto[1]?.status?.toLowerCase() !== "up") {
                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-dashesIn 1s  linear infinite')

                        }
                        else if (moniterStatus == 2) {

                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                        }
                        else {
                            $('.amcharts-ForceDirectedLink-group').css('animation', 'am-moving-dashesIn 1s linear infinite')
                        }
                    }

                }, 300)



                if (prdatabasetype || drdatabasetype) {
                    const databaseType = (prdatabasetype || drdatabasetype)?.toLowerCase();

                    if (databaseType?.includes("oracle")) {
                        databaseImage = "/img/charts_img/DataCenter/oracle.svg";
                    } else if (databaseType?.includes("mysql")) {
                        databaseImage = "/img/charts_img/DataCenter/my_sql.svg";
                    } else if (databaseType?.includes("mssql") || databaseType?.includes("always") || databaseType?.includes("ms-sql")) {
                        databaseImage = "/img/charts_img/DataCenter/MSSQL.svg";
                    } else if (databaseType?.includes("postgres")) {
                        databaseImage = "/img/charts_img/DataCenter/Postgresql.svg";
                    } else if (databaseType?.includes("ibm")) {
                        databaseImage = "/img/charts_img/DataCenter/IBM.svg";
                    } else if (databaseType?.includes("mongodb")) {
                        databaseImage = "/img/DB-Logo/cp_mongo_db.svg"
                    }
                }
                chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];

                series.data = [{

                    fixed: true,
                    x: am4core.percent(12),
                    y: am4core.percent(50),
                    tagimage: prroleType?.toLowerCase() === "database" ? (prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
                    ip: prdatabase,
                    status: prdbStatus,
                    type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
                    version: prversion,
                    value: 10,
                    serverimage: databaseImage,
                    dbimage: prroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application") ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg",
                    children: [{
                        name: "Primary",
                        fixed: true,
                        x: am4core.percent(30),
                        y: am4core.percent(50),
                        tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                        ip: pripaddress,
                        hostname: prhost,
                        os: prOstype,
                        location: prOslocation,
                        prstatus: prdbStatus,
                        prostatus: prStatus,
                        value: 10,
                        activeicon: moniterStatus == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
                        //image: "/img/charts_img/DataCenter/PR.svg",
                        image: moniterType?.toLowerCase() !== 'srm' && (PRserverType === 'PRDBServer' || PRserverType === 'PRAPPServer' || PRserverType === 'PRVsphereServer') ? "/img/charts_img/DataCenter/PR.svg" : "/img/charts_img/DataCenter/far dr.svg",
                        serverimage: prOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : prOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                        children: [
                            {
                                dbname: replicationType,                               
                                status: prServer,
                                fixed: true,
                                x: am4core.percent(51),
                                y: am4core.percent(50),
                                mainimage: (value?.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                                value: 15,
                                image: image,
                                children: [

                                ],
                            }
                        ]
                    }]

                },];

                if (moniterType?.toLowerCase() === 'srm') {
                    series.data = [{

                        fixed: true,
                        x: am4core.percent(12),
                        y: am4core.percent(40),
                        tagimage: prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg",
                        ip: prdatabase,
                        status: prdbStatus,
                        type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
                        version: prversion,
                        value: 10,
                        activeicon: moniterStatus == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
                        serverimage: databaseImage,
                        image: "/img/charts_img/DataCenter/PR.svg",
                        children: [{
                            name: "Primary",
                            fixed: true,
                            x: am4core.percent(30),
                            y: am4core.percent(40),
                            tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                            ip: pripaddress,
                            hostname: prhost,
                            os: prOstype,
                            location: prOslocation,
                            prstatus: prdbStatus,
                            prostatus: prStatus,
                            value: 10,

                            image: "/img/charts_img/DataCenter/esxi_server_icon.svg",
                            serverimage: prOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : prOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                            children: [
                                {
                                    dbname: replicationType,
                                    status: prServer,
                                    fixed: true,
                                    x: am4core.percent(51),
                                    y: am4core.percent(40),
                                    mainimage: (value.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                                    value: 15,
                                    image: image,
                                    children: [
                                        {
                                            name: "DR",
                                            fixed: true,
                                            x: am4core.percent(70),
                                            y: am4core.percent(40),
                                            tagimage: (drServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                                            ip: dripaddress,
                                            hostname: drhost,
                                            os: drOstype,
                                            location: drOslocation,
                                            status: drStatus,
                                            value: 10,

                                            serverimage: drOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : drOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                                            image: "/img/charts_img/DataCenter/esxi_server_icon.svg",
                                            children: [{

                                                fixed: true,
                                                x: am4core.percent(85),
                                                y: am4core.percent(40),
                                                tagimage: drDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg",
                                                ip: drdatabase,
                                                status: drdbStatus,
                                                type: drroleType?.toLowerCase() === "database" ? drdatabasetype : replicationType,
                                                version: drversion,
                                                value: 10,
                                                activeicon: moniterStatus == 2 ? "/img/charts_img/DataCenter/crown_1.svg" : "",
                                                serverimage: databaseImage,
                                                image: "/img/charts_img/DataCenter/DR.svg",
                                            }],
                                        }
                                    ],
                                }
                            ]
                        }]

                    },];
                }
                if (moniterType?.toLowerCase()?.includes('openshift')) {
                    series.data = [{

                        fixed: true,
                        x: am4core.percent(32),
                        y: am4core.percent(45),
                        tagimage: prroleType?.toLowerCase() === "database" || isSRM ? (prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",

                        location: prOslocation,
                        status: prdbStatus,
                        type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
                        version: prversion,
                        hostname: isSRM ? prsrmHost : '',
                        location: isSRM ? prsrmLocation : '',
                        os: isSRM ? prsrmOS : '',
                        value: 10,
                        serverimage: databaseImage,
                        dbimage: /*"/img/charts_img/DataCenter/ApplicationRe.svg"*/ prroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application") ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg", 
                        children: [{
                            name: "Primary",
                            fixed: true,
                            x: am4core.percent(50),
                            y: am4core.percent(45),
                            tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                            ip: value?.serverDto[0]?.hostName,
                            hostname: value?.serverDto[0]?.hostName,
                            os: value?.serverDto[0]?.osType,
                            location: value?.serverDto[0]?.location,
                            prstatus: value?.serverDto[0]?.status,
                            prostatus: prStatus,
                            value: 10,
                            // activeicon: drOperation == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
                            image: "/img/charts_img/DataCenter/far dr.svg",
                            serverimage: "/img/charts_img/DataCenter/linux.svg",
                            children: [
                                {
                                    dbname: replicationType,
                                    status: prServer,
                                    fixed: true,
                                    x: am4core.percent(71),
                                    y: am4core.percent(45),
                                    mainimage: (value?.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                                    value: 15,
                                    image: "/img/charts_img/DataCenter/replication-off.svg",

                                }
                            ]
                        }]

                    },];
                }

                if ((moniterType?.toLowerCase() === "oraclerac")) {
                    let nodeArray = [];
                    let nodeArray1 = [];
                    let nodeName = $('#clusterDetails option:selected')?.text() === "" ? "Node1" : $('#clusterDetails option:selected')?.text()
                    if (nodeName?.toLowerCase()?.includes('1')) {
                        value?.serverDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('drdb_1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                nodeArray.push(item);
                            }
                        });
                        value?.databaseDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('drdb_1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                                nodeArray1.push(item);
                            }
                        });
                    } else if (nodeName?.toLowerCase()?.includes('2')) {
                        value?.serverDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                nodeArray.push(item);
                            }
                        });
                        value?.databaseDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') ||item?.nodeName?.toLowerCase()?.includes('dr2')) {
                                nodeArray1.push(item);
                            }
                        });
                    } else if (nodeName?.toLowerCase()?.includes('3')) {
                        value?.serverDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                nodeArray.push(item);
                            }
                        });
                        value?.databaseDto?.forEach((item) => {
                            if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                                nodeArray1.push(item);
                            }
                        });
                    }
                    value.serverDto = nodeArray
                    value.databaseDto = nodeArray1
                }


                let customDRServerArray = value?.serverDto?.filter((d) => !d.serverType?.toLowerCase()?.includes('pr'))                
                let customDRDBArray = value?.databaseDto?.filter((d) => !d.type?.toLowerCase()?.includes('pr'))
                
                if (!moniterType?.toLowerCase()?.includes('srm')) {
                    if (customDRServerArray.length > 0) {
                        customDRServerArray?.forEach((d, index) => {
                            let ipvalue = d?.connectViaHostName?.toLowerCase() === "true" ? d?.hostName : d?.ipAddress
                            var dripaddress = checkValue(ipvalue)
                            var drOstype = checkValue(d?.osType)
                            var drOslocation = checkValue(d?.location)
                            var drhost = checkValue(d?.hostName)
                            var drStatus = checkValue(d?.status)
                            /* var drdbStatus = replicationType?.toLowerCase() !== "application-no-replication" ? (checkValue(d?.status?.toLowerCase() != "down" ? value?.databaseDto[1]?.status : d?.status)) : 'NA'*/
                            var drdbStatus = (type?.toLowerCase() !== "application" && type?.toLowerCase() !== "virtual") ? (checkValue(d?.status?.toLowerCase() != "down" ? value?.databaseDto[0]?.status : d?.status)) : 'NA'
                            var drroleType = checkValue(value?.serverDto[1]?.roleType)

                            var serverType = d?.serverType                            
                            let drArrayLength = customDRServerArray?.length
                            let yValue = []
                            if (drArrayLength === 1) {
                                yValue = [50]
                            } else if (drArrayLength === 2) {
                                yValue = [25, 65]
                            } else if (drArrayLength === 3) {
                                yValue = [20, 50, 80]
                            } else if (drArrayLength === 4) {
                                yValue = [20, 35, 65, 80]
                            } else if (drArrayLength === 5) {
                                yValue = [20, 35, 50, 65, 80]
                            } else if (drArrayLength === 6) {
                                yValue = [10, 25, 40, 60, 75, 90]
                            }
                            let filterdTypeArray = []
                            if (moniterType?.toLowerCase()?.includes('rac')) {
                                filterdTypeArray = customDRDBArray?.filter((x) => serverType?.toLowerCase()?.includes('dr'));
                            } else {
                                filterdTypeArray = customDRDBArray?.filter((x) => x?.type?.toLowerCase()?.includes('dr') && serverType?.toLowerCase()?.includes('dr'));
                            }

                            var drdatabase = customDRDBArray?.length ? checkValue(filterdTypeArray[0]?.sid) : ''
                            var drversion = checkValue(filterdTypeArray[0]?.version)
                            var drdatabasetype = checkValue(filterdTypeArray[0]?.databaseType)

                            let obj = {
                                name: d?.serverType,
                                fixed: true,
                                x: am4core.percent(70),
                                y: am4core.percent(yValue[index]),
                                tagimage: (drStatus?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drStatus?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drStatus?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                                ip: dripaddress,
                                hostname: drhost,
                                os: drOstype,
                                location: drOslocation,
                                prostatus: drStatus,
                                status: drStatus,
                                value: 10,
                                activeicon: (moniterStatus == 2 && serverType?.toLowerCase()?.includes('dr')) || (moniterStatus == 15 && !serverType?.toLowerCase()?.includes('dr')) ? "/img/charts_img/DataCenter/crown_1.svg" : "",
                                serverimage: drOstype?.toLowerCase() === "windows" ? "/img/charts_img/DataCenter/windows-1.svg" : drOstype?.toLowerCase() === "linux" ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                                image: (serverType === "DR" || serverType === "DRDBServer" || serverType === "DRAPPServer" || serverType === 'DRVsphereServer') ? "/img/charts_img/DataCenter/DR.svg" : "/img/charts_img/DataCenter/far dr.svg",
                                children: [{

                                    fixed: true,
                                    x: am4core.percent(85),
                                    y: am4core.percent(yValue[index]),
                                    tagimage: drroleType?.toLowerCase() === "database" ? (drDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
                                    ip: drdatabase,
                                    status: drdbStatus,
                                    type: drroleType?.toLowerCase() === "database" ? drdatabasetype : replicationType,
                                    version: drversion,
                                    value: 10,
                                    serverimage: databaseImage,
                                    dbimage: drroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (drroleType?.toLowerCase() === "virtualization" || drroleType?.toLowerCase() === "application" ) ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg",
                                }],
                            }
                            series.data[0].children[0].children[0].children.push(obj)
                        })
                    }
                }
                //   }

                setTimeout(() => {

                    if (customDRServerArray?.length === 2) {
                        // $('.amcharts-ForceDirectedLink-group').eq(1).css('animation', 'am-moving-dashesIn 1s linear infinite')
                        let stringArray
                        if (moniterStatus === 2) {
                            stringArray = [0, 1, 2, 3]
                        } else if (moniterStatus === 15) {
                            stringArray = [0, 1, 4, 5]
                        }
                        for (let i = 0; i < 6; i++) {
                            if (stringArray?.includes(i)) {
                                $('.amcharts-ForceDirectedLink-group').eq(i).css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                            }
                        }

                    } else if (customDRServerArray?.length === 3) {
                        // $('.amcharts-ForceDirectedLink-group').eq(1).css('animation', 'am-moving-dashesIn 1s linear infinite')
                        let stringArray
                        if (moniterStatus === 2) {
                            stringArray = [0, 1, 2, 3]
                        } else if (moniterStatus === 15) {
                            stringArray = [0, 1, 4, 5]
                        }
                        for (let i = 0; i < 8; i++) {
                            if (stringArray.includes(i)) {
                                $('.amcharts-ForceDirectedLink-group').eq(i).css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                            }
                        }

                    }
                }, 500)

                // Set up data fields
                series.dataFields.value = "value";
                series.dataFields.fixed = "fixed";
                series.dataFields.dbname = "dbname";
                series.dataFields.activeicon = "activeicon";
                series.dataFields.serverimage = "serverimage";
                series.dataFields.name = "name";
                series.dataFields.ip = "ip";
                series.dataFields.Connection = "Connection";
                (series.dataFields.Service = "Service"),
                    (series.dataFields.host = "host"),
                    (series.dataFields.Port = "Port"),
                    (series.dataFields.id = "id");
                series.dataFields.children = "children";
                series.dataFields.tag = "tag";
                series.dataFields.linkWith = "link";
                series.links.template.propertyFields.userClassName = "lineClass"
                series.links.template.adapter.add("stroke", function (stroke, target) {
                    if (target.dataItem && target.dataItem.dataContext) {
                        const sourceNode = target.dataItem.dataContext;

                        const sourceStatus = sourceNode.status;
                        const prStatusSource = sourceNode.prstatus;
                       
                        // Set individual stroke colors 
                        if (sourceStatus) {
                                                                         
                            if (moniterType === '' || moniterType === undefined) {
                                return am4core.color("grey");
                            } else {
                                if (sourceStatus?.toLowerCase() === "up") {
                                    return am4core.color("#41c200");
                                } else if (sourceStatus?.toLowerCase() === "down") {
                                    return am4core.color("red");
                                } else if (sourceStatus?.toLowerCase() === "pending") {
                                    return am4core.color("grey");
                                }
                                else if (drroleType?.toLowerCase() === "virtualization" || drroleType?.toLowerCase() === "application") {
                                    return am4core.color("#FFFFFF");
                                }
                                else {
                                    return am4core.color("grey");
                                }
                            }

                        }
                        
                        if (prStatusSource) {
                            
                            if (moniterType === '' || moniterType === undefined) {
                                return am4core.color("grey");
                            } else {
                                if (prStatusSource?.toLowerCase() === "up") {
                                    return am4core.color("#41c200");
                                } else if (prStatusSource?.toLowerCase() === "down") {
                                    return am4core.color("red");
                                } else if (prStatusSource?.toLowerCase() === "pending") {
                                    return am4core.color("grey");
                                }
                                else if (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application" ) {
                                    return am4core.color("#FFFFFF");
                                }
                                else {
                                    return am4core.color("grey");
                                }
                            }

                        }
                    }
                    return stroke;
                });



                series.dataFields.id = "name";
                series.manyBodyStrength = -18;
                // Add labels
                series.nodes.template.label.text = "{dbname}\n{ip}";
                series.nodes.template.label.valign = "bottom";
                series.nodes.template.label.wrap = true;  // Enable wrapping
                series.nodes.template.label.hideOversized = false;  // Prevents hiding wrapped text
                series.nodes.template.label.truncate = false; // Disable truncation
                series.nodes.template.label.maxWidth = 110; // Set max width before wrapping
                series.nodes.template.label.fill = am4core.color("#000");
                series.nodes.template.label.dy = -2;
               
                series.nodes.template.adapter.add("tooltipText", function (text, target) {
                    if (!target.dataItem) return "";
                    const dataItem = target.dataItem.dataContext;
                    
                    if (target?.dataItem?.name === "Primary" || target?.dataItem?.name === "DR" || target?.dataItem?.name) {
                        let hostText = dataItem?.hostname ?? "NA";
                        let osText = dataItem?.os ?? "NA";
                        let osLocation = dataItem?.location ?? "NA";
                        let statusText = dataItem?.prostatus ?? "NA";

                        return `[bold; #0479ff;]Hostname: [/]${hostText}\n[/] [bold; #0479ff;]Location: [/]${osLocation}\n[/] [bold; #0479ff;]OS: [/]${osText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;
                    }
                    else if (dataItem?.type) {
                        //if (isSRM) {
                        //    let hostText = dataItem?.hostname ?? "NA";
                        //    let osText = dataItem?.os ?? "NA";
                        //    let osLocation = dataItem?.location ?? "NA";

                        //    return `[bold; #0479ff;]Hostname: [/]${hostText}\n[/] [bold; #0479ff;]Location: [/]${osLocation}\n[/] [bold; #0479ff;]OS: [/]${osText}\n[/] [bold; #0479ff;]`;

                        //}

                        let typeText = dataItem?.type ?? "NA";
                        let versionText = dataItem?.version ?? "NA";
                        let statusText = dataItem?.status ?? "NA";
                        return `[bold; #0479ff;]Type: [/]${typeText}\n[/] [bold; #0479ff;]Version: [/]${versionText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;


                    } else if (replicationTypemonitor) {

                        const replicaText = dataItem?.dbname ?? "NA";
                        return `[bold; #0479ff;]${replicaText}`;
                    }

                    return "";
                });

                series.fontSize = 11;
                series.minRadius = 35;
                series.maxRadius = 35;

                series.tooltip.autoTextColor = false;
                series.tooltip.getFillFromObject = false;
                series.tooltip.label.fill = am4core.color("#1A1A1A");
                series.tooltip.label.background.fill = am4core.color("#fff");

                series.links.template.strokeWidth = 2;
                //series.links.template.strokeDasharray = "5,3";
                series.nodes.template.circle.strokeWidth = 0;
                series.nodes.template.circle.disabled = true;
                series.nodes.template.outerCircle.disabled = true;

                series.dataFields.fixed = "fixed";
                series.nodes.template.propertyFields.x = "x";
                series.nodes.template.propertyFields.y = "y";

                // Add tag
                var tag = series.nodes.template.createChild(am4core.Label);
                tag.text = "{tag}";
                tag.strokeWidth = 0;
                // tag = am4core.percent(50)
                tag.fill = am4core.color("#fff");
                tag.background = new am4core.RoundedRectangle();
                tag.background.cornerRadius(10, 10, 10, 10);
                tag.background.fill = am4core.color("#41c200");
                tag.padding(2, 4, 2, 4);
                tag.zIndex = 10;
                tag.width = '8px';
                tag.height = '10px';
                tag.fontSize = 8;
                tag.verticalCenter = "top";
                tag.textAlign = 'middle'
                tag.horizontalCenter = "left";

                tag.adapter.add("dy", function (dy, target) {
                    return -target.parent.circle.radius + 40;
                });
                tag.adapter.add("dx", function (dy, target) {
                    return target.parent.circle.radius - 65;
                });
                tag.adapter.add("textOutput", function (text, target) {
                    if (text === "") {
                        target.disabled = true;
                    }
                    return text;
                });

                tag.adapter.add("fill", function (fill, target) {
                    if (target.dataItem && target.dataItem.tag == "✔") {
                        return am4core.color("#fff");
                    } else {
                        return fill;
                    }
                });
                tag.background.adapter.add("fill", function (fill, target) {
                    if (target.dataItem && target.dataItem.tag == "✖") {
                        return am4core.color("red");
                    } else {
                        return fill;
                    }
                });
                tag.background.adapter.add("fill", function (fill, target) {
                    if (target.dataItem && target.dataItem.tag == "⟳") {
                        return am4core.color("#FF9632");
                    } else {
                        return fill;
                    }
                });
                // Change the padding values
                chart.padding(-15, -15, -15, -15)

                // Configure icons
                var icon = series.nodes.template.createChild(am4core.Image);
                icon.propertyFields.href = "image";
                icon.horizontalCenter = "middle";
                icon.verticalCenter = "middle";
                icon.width = 55;
                icon.height = 55;


                // Configure icons
                var dbicon = series.nodes.template.createChild(am4core.Image);
                dbicon.propertyFields.href = "dbimage";
                dbicon.horizontalCenter = "middle";
                dbicon.verticalCenter = "middle";
                dbicon.width = 35;
                dbicon.height = 35;
                series.centerStrength = 0.5;
                var tagicon = series.nodes.template.createChild(am4core.Image);
                tagicon.strokeWidth = 0;
                tagicon.propertyFields.href = "tagimage";
                tagicon.dy = 0;
                tagicon.dx = -30
                tagicon.zIndex = 10;
                tagicon.width = '15px';
                tagicon.height = '15px';
                tagicon.verticalCenter = "top";
                tagicon.textAlign = 'center'
                tagicon.horizontalCenter = "left";

                var tagicon = series.nodes.template.createChild(am4core.Image);
                tagicon.text = "{tagicon}";
                tagicon.strokeWidth = 0;
                tagicon.propertyFields.href = "mainimage";
                tagicon.dy = 0;
                tagicon.dx = -30
                tagicon.zIndex = 10;
                tagicon.width = '15px';
                tagicon.height = '15px';
                tagicon.verticalCenter = "top";
                tagicon.textAlign = 'center'
                tagicon.horizontalCenter = "left";

                var activetag = series.nodes.template.createChild(am4core.Label);
                //activetag.text = "{activetag}";
                activetag.strokeWidth = 0;
                // tag = am4core.percent(50)
                //activetag.fill = am4core.color("blue");
                activetag.background = new am4core.RoundedRectangle();
                activetag.background.cornerRadius(10, 10, 10, 10);
                activetag.background.fill = am4core.color("#fff");
                activetag.padding(2, 5, 2, 5);
                activetag.zIndex = 10;
                activetag.width = '10px';
                activetag.height = '10px';
                activetag.verticalCenter = "top";
                activetag.textAlign = 'center'
                activetag.horizontalCenter = "left";
                activetag.adapter.add("dy", function (dy, target) {
                    return -target.parent.circle.radius + -8;
                });
                activetag.adapter.add("dx", function (dy, target) {
                    return target.parent.circle.radius - 40;
                });

                activetag.background.adapter.add("fill", function (fill, target) {
                    if (target.dataItem && target.dataItem.activetag == true) {
                        return am4core.color("#41c200");
                    } else {
                        return fill;
                    }
                });

                var icons = series.nodes.template.createChild(am4core.Image);
                icons.propertyFields.href = "serverimage";
                icons.horizontalCenter = "middle";
                icons.verticalCenter = "middle";
                icons.width = 35;
                icons.height = 35;
                icons.dy = 20;
                icons.dx = 15
                var icons = series.nodes.template.createChild(am4core.Image);
                icons.propertyFields.href = "activeicon";
                icons.horizontalCenter = "middle";
                icons.verticalCenter = "middle";
                icons.width = 15;
                icons.height = 15;
                icons.dy = -40;
                icons.dx = 0
            }
        }
    });
    function checkValue(value) {
        return (value !== null && value !== '' && value !== undefined) ? value : "NA";
    }
   
}



    