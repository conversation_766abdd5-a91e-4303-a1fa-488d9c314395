using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftParameter.Events.Update;

public class DriftParameterUpdatedEventHandler : INotificationHandler<DriftParameterUpdatedEvent>
{
    private readonly ILogger<DriftParameterUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftParameterUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DriftParameterUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DriftParameterUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.DriftParameter}",
            Entity = Modules.DriftParameter.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Drift Parameter '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Drift Parameter '{updatedEvent.Name}' updated successfully.");
    }
}