﻿function Get_ScheduleTypes() {

    var Scheduler_types = $('.nav-tabs .active').text().trim();

    switch (Scheduler_types) {
        case "Minutes":
            $('#textScheduleType').val(1);
            break;
        case "Hourly":
            $('#textScheduleType').val(2);
            break;
        case "Daily":
            $('#textScheduleType').val(3);
            break;
        case "Weekly":
            $('#textScheduleType').val(4);
            break;
        case "Monthly":
            $('#textScheduleType').val(5);
            break;
    }
}
//function DateTimeCronBuilder(datetime) {

//    var splitDate = datetime?.split("T");
//    var cronDate = splitDate?.[0]?.split("-");
//    var cronTime = splitDate?.[1]?.split(":");

//    var cronYear = cronDate?.[0];
//    var cronMonth = cronDate?.[1]
//    var cronDay = cronDate?.[2];
//    var cronHours = cronTime?.[0];
//    var cronMin = cronTime?.[1];
//    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
//    // monthname
//    //var month = datetime.split("T");
//    //var datemonth = month[0].split("-");
//    //var cronmonth_listcron = datemonth[1]

//    //var cronname = ["January", "February", "March", "April", "May", "June", "July",
//    //    "August", "September", "October", "November", "December"];
//    //var cronmonthname = cronname[cronmonth_listcron - 1]
//    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronMonth + ", only in " + cronYear;

//    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024

//    return { CronExpression, listCron }

//}

function DateTimeCronBuilder(datetime) {
    var splitDate = datetime.split("T");
    var cronDate = splitDate[0].split("-");
    var cronTime = splitDate[1].split(":");

    var cronYear = cronDate[0];
    var cronMonth = cronDate[1]
    var cronDay = cronDate[2];
    var cronHours = cronTime[0];
    var cronMin = cronTime[1];
    var cronmonthexp = cronDate[1] == "01" ? "JAN" : cronDate[1] == "02" ? "FEB" : cronDate[1] == "03" ? "MAR" : cronDate[1] == "04" ? "APR" :
        cronDate[1] == "05" ? "MAY" : cronDate[1] == "06" ? "JUN" : cronDate[1] == "07" ? "JUL" : cronDate[1] == "08" ? "AUG" : cronDate[1] == "09" ? "SEP" :
            cronDate[1] == "10" ? "OCT" : cronDate[1] == "11" ? "NOV" : cronDate[1] == "12" ? "DEC" : ""
    CronExpression = "0 " + cronMin + " " + cronHours + " " + cronDay + " " + cronMonth + " ? " + cronYear;
    // monthname
    listCron = "At " + cronHours + ":" + cronMin + ", on day  " + cronDay + " of the month, only in " + cronmonthexp + ", only in " + cronYear;
    //At 12: 51 PM, on day 14 of the month, only in February, only in 2024
    return { CronExpression, listCron }
}

function DateTimeCronConventor(cron) {

    var splitcron = cron.split(" ");
    var cronYear = splitcron[6];
    var cronMonth = splitcron[4];
    var cronDay = splitcron[3];
    var cronHours = splitcron[2];
    var cronMin = splitcron[1];
    var cronDate = cronYear + "-" + cronMonth + "-" + cronDay + "T" + cronHours + ":" + cronMin

    //At 06:05:00am, on the 8th day, in May, in 2023
    return cronDate

}

function dayconventor(day) {
    const daysMap = {
        MON: 1,
        TUE: 2,
        WED: 3,
        THU: 4,
        FRI: 5,
        SAT: 6,
        SUN: 0
    };
    const days = day.split(',');
    days.forEach(day => {
        const checkboxId = `#defaultCheck-${daysMap[day]}`;
        $(checkboxId).prop("checked", true);
    });
}

function monthDayconventor(days) {
    const day = days.split(" ")
    var checkboxes = document.querySelectorAll('input[name="Monthyday"]');
    checkboxes.forEach(function (checkbox) {
        if (day.includes(checkbox.value)) {
            checkbox.checked = true;
        }
    });
};

function Tab_selection(jobData) {
    if (jobData.isSchedule == 2) {
        Drready_SM1 = document.getElementById("switchMonthly");
        Drready_SM1.checked = true;
        var elementToHide1 = document.getElementById("monthgroup");
        elementToHide1.style.display = "block";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "none";
    } else {
        Drready_SM2 = document.getElementById("switchYearly");
        Drready_SM2.checked = true;
        var elementToHide11 = document.getElementById("monthgroup");
        elementToHide11.style.display = "none";
        var elementToHide22 = document.getElementById("yeargroup");
        elementToHide22.style.display = "block";
    }
}
// Create  Cron Expression
function JobCronExpression() {

    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var CronExpression = "";
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val() == "00" ? "0" : $('#txtHours').val() == "01" ? "1" : $('#txtHours').val() == "03" ? "3" : $('#txtHours').val() == "04" ? "4" : $('#txtHours').val() == "05" ? "5" : $('#txtHours').val() == "06" ? "6" : $('#txtHours').val() == "07" ? "7" : $('#txtHours').val() == "08" ? "8" : $('#txtHours').val() == "09" ? "9" : $('#txtHours').val()
    var txtHourMinutes = $('#txtMinutes').val() == "00" ? "0" : $('#txtMinutes').val() == "01" ? "1" : $('#txtMinutes').val() == "03" ? "3" : $('#txtMinutes').val() == "04" ? "4" : $('#txtMinutes').val() == "05" ? "5" : $('#txtMinutes').val() == "06" ? "6" : $('#txtMinutes').val() == "07" ? "7" : $('#txtMinutes').val() == "08" ? "8" : $('#txtMinutes').val() == "09" ? "9" : $('#txtMinutes').val()
    var day = $('#ddlHours').val().split(":")
    var ddlHours = day[0]
    var ddlMinutes = day[1]
    var Daily = $('#everyHours').val().split(":")
    var everyHours = Daily[0]
    var everyMinutes = Daily[1]
    var month = $('#MonthlyHours').val().split(":")
    var MonthlyHours = month[0]
    var MonthlyMins = month[1]
    var weekDay = $('#defaultCheck-MON-FRI').val();
    var datetime = $('#datetimeCron').val()

    let schedule_model = document.querySelector('input[name="daysevery"]:checked');

    var listcron = '';

    if (datetime != '' && datetime != undefined) {
        var { CronExpression, listCron } = DateTimeCronBuilder(datetime)
        CronExpression = CronExpression
        listcron = listCron;
    }
    else {
        if (Minutes != '') {
            CronExpression = "0" + " 0/" + Minutes + " * * * ?";
            listcron = "Every " + Minutes + " minutes"
        }

        else if (txtHours != '') {

            CronExpression = "0 " + txtHourMinutes + " 0/" + txtHours + " * * ?"
            listcron = "Every " + txtHours + " hours " + txtHourMinutes + " minutes";
        }
        else if (txtDay != '') {
            CronExpression = "0 " + ddlMinutes + " " + ddlHours + " ? * " + txtDay + " *"
            listcron = txtDay + " at " + ddlHours + " hours " + ddlMinutes + " minutes";
        }
        else if (txtmonthday != '') {
            if (monthlymonth != '') {
                monthlymonth = monthlymonth.split('-');
                var txtmonth = monthlymonth[1] == "01" ? "JAN" : monthlymonth[1] == "02" ? "FEB" : monthlymonth[1] == "03" ? "MAR" : monthlymonth[1] == "04" ? "APR" :
                    monthlymonth[1] == "05" ? "MAY" : monthlymonth[1] == "06" ? "JUN" : monthlymonth[1] == "07" ? "JUL" : monthlymonth[1] == "08" ? "AUG" : monthlymonth[1] == "09" ? "SEP" :
                        monthlymonth[1] == "10" ? "OCT" : monthlymonth[1] == "11" ? "NOV" : monthlymonth[1] == "12" ? "DEC" : ""

                var txtyear = monthlymonth[0];
            }
            CronExpression = "0 " + MonthlyMins + " 0/" + MonthlyHours + " " + txtmonthday + " " + txtmonth + " ? " + txtyear
            listcron = MonthlyHours + " hours " + MonthlyMins + " minutes for " + txtmonthday + " day(s) on " + txtmonth + " in the year " + txtyear;
        }
        else if (schedule_model != null) {
            if (schedule_model.value == "everyday") {
                CronExpression = "0 " + everyMinutes + " " + everyHours + " * * ?"
                listcron = " Every day at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
            else if (schedule_model.value == "MON-FRI") {
                CronExpression = "0 " + everyMinutes + " " + everyHours + " ? * " + weekDay + " * ";
                listcron = " MON-FRI at " + everyHours + " hours " + everyMinutes + " minutes ";
            }
        }
    }
    return { CronExpression, listcron };
}

function Tab_schedule_type(jobData) {
    var types = jobData.scheduleType;
    var clickedLink = "";
    var linkId = "";


    if (jobData.isSchedule == 1) {
        var datetime = DateTimeCronConventor(jobData.cronExpression)
        $('#datetimeCron').val(datetime)
    }
    else {
        switch (types) {
            case 1:
                linkId = "nav-Minutes-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { minutes } = parseMinCronExpression(jobData.cronExpression);
                    document.getElementById("txtMins").value = minutes;
                }, 150)
                break;
                debugger
            case 2:
                linkId = "nav-Hourly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, minutes } = parseHoursCronExpression(jobData.cronExpression);
                    document.getElementById("txtHours").value = hours;
                    document.getElementById("txtMinutes").value = minutes;
                }, 150)
                break;
            case 3:
                linkId = "nav-Daily-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseDailyCronExpression(jobData.cronExpression);
                    document.getElementById("everyHours").value = hours;
                    if (day == "?") {
                        $("#defaultCheck-everyday").prop("checked", true);
                    }
                    else {
                        $("#defaultCheck-MON-FRI").prop("checked", true);
                    }
                }, 150)
                break;
            case 4:
                linkId = "nav-Weekly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, day } = parseWeeklyCronExpression(jobData.cronExpression);
                    document.getElementById("ddlHours").value = hours;
                    dayconventor(day);
                }, 150)
                break;
            case 5:
                linkId = "nav-Monthly-tab";
                setTimeout(() => {
                    clickedLink = document.getElementById(linkId);
                    clickedLink.click();
                    const { hours, month, days } = parseCronMonthExpression(jobData.cronExpression);
                    document.getElementById("MonthlyHours").value = hours;
                    document.getElementById("lblMonth").value = month;
                    monthDayconventor(days);
                }, 150)
                break;
        }
    }
}


function parseCronMonthExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const month = parts[6] + "-" + parts[4];
    const days = parts[3];
    return { minutes, hours, month, days };
};

function parseMinCronExpression(expression) {
    const parts = expression.split(' ');
    const minutes = parseInt(parts[1].substring(2));
    const hours = parseInt(parts[2].substring(2));
    const day = parseInt(parts[3].substring(2));
    return { hours, minutes, day };
}


function parseHoursCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function parseDailyCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}
function parseWeeklyCronExpression(expression) {
    const parts = expression.split(' ')
    const minutes = parseInt(parts[1]);
    const hours = parseInt(parts[2].substring(2));
    const day = parts[5];
    return { hours, minutes, day };
}


//////  Validation /////////////////////
function ValidateCronRadioButton(errorElement) {
    if ($('input[name=daysevery]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select day type").addClass('field-validation-error');;
        return false;
    }
}
function validateMiniteJobNumber(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 0) || (Number(value) >= 60)) {
        errorElement.text("Enter value between 0 to 59").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateMinJobNumber(value, errorMsg, errorElement) {

    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 0) || (Number(value) > 59)) {
        errorElement.text("Enter value between 1 to 59").addClass('field-validation-error');
        return false;
    } else if (Number(value) == "0") {
        errorElement.text("Enter the value more than 0").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateHourJobNumber(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) == 0)) {
        errorElement.text("Enter value greater than zero").addClass('field-validation-error');
        return false;
    }
    else if ((Number(value) < 1) || (Number(value) >= 24)) {
        errorElement.text("Enter value between 1 to 23").addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function validateprevNumber(value, errorMsg, errorElement) {
    let selectdate = new Date(value)
    let currentdate = new Date()
    if (selectdate > currentdate) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        errorElement.text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
}

function validateDayNumber(value, errorMsg, errorElement) {

    if (!value || value.length == 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}


