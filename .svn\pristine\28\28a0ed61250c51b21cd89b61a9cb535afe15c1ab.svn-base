﻿using ContinuityPatrol.Application.Features.Alert.Commands.Create;
using ContinuityPatrol.Application.Features.Alert.Commands.Update;
using ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListByStartOfWeek;
using ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListFilterByDate;
using ContinuityPatrol.Application.Features.Alert.Queries.GetClientAlertId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetInfraObjectId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetLastAlertId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Alert;
public class AlertService : BaseClient, IAlertService
{
    public AlertService(IConfiguration config, IAppCache cacheService, ILogger<AlertService> logger)
        : base(config, cacheService, logger)
    {
            
    }
    public async  Task<BaseResponse> CreateAsync(CreateAlertCommand createAlertCommand)
    {
        var request = new RestRequest("api/v6/alerts", Method.Post);

        ClearCache("GetAlertList");

        request.AddJsonBody(createAlertCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string alertId)
    {
        var request = new RestRequest($"api/v6/alerts/{alertId}", Method.Delete);

        ClearCache("GetAlertList");

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<AlertListVm>> GetAlertList()
    {
        var request = new RestRequest("api/v6/alerts");

        return await GetFromCache<List<AlertListVm>>(request, "GetAlertList");
    }

    public async Task<List<AlertListByStartOfWeekVm>> GetAlertListByStartOfWeek(string startDate, string endDate)
    {
        var request = startDate.IsNotNullOrWhiteSpace()  && endDate.IsNullOrWhiteSpace()?
            new RestRequest($"api/v6/alerts/by/startofweek?startDate={startDate}&endDate={endDate}")
           : new RestRequest($"api/v6/alerts/by/startofweek");

        return await Get<List<AlertListByStartOfWeekVm>>(request);
    }

    public async Task<List<AlertListFilterByDateVm>> GetAlertListFilterByDate(string startDate, string endDate)
    {
        var request = new RestRequest($"api/v6/alerts/filterbydate?startDate={startDate}&endDate={endDate}");

        return await Get<List<AlertListFilterByDateVm>>(request);
    }

    public async Task<(PaginatedResult<AlertListVm>, Dictionary<string, int>)> GetAlertPaginatedList(GetAlertPaginatedListQuery query)
    {
       var request = new RestRequest("api/v6/alerts/paginated-list");

        return await Get<(PaginatedResult<AlertListVm>, Dictionary<string, int>)>(request);
    }

    public async Task<List<AlertListVm>> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/alerts/{id}");

        return await Get<List<AlertListVm>>(request);
    }

    public async  Task<List<AlertByClientAlertIdVm>> GetClientAlertId(string clientAlertId)
    {
        var request = new RestRequest($"api/v6/alerts/clientalertid?clientAlertId={clientAlertId}");

        return await Get<List<AlertByClientAlertIdVm>>(request);
    }

    public async Task<List<AlertByInfraObjectIdVm>> GetInfraObjectId(string infraObjectId, string entityId)
    {
        var request = new RestRequest($"api/v6/alerts/infraobjectid/entityid?infraObjectId={infraObjectId}&entityId={entityId}");

        return await Get<List<AlertByInfraObjectIdVm>>(request);
    }

    public async Task<GetLastAlertDetailVm> GetLastAlertCount()
    {
        var request = new RestRequest("api/v6/alerts/alert-count");

        return await Get<GetLastAlertDetailVm>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAlertCommand updateAlertCommand)
    {
        var request = new RestRequest("api/v6/alerts", Method.Put);

        ClearCache("GetAlertList");

        request.AddJsonBody(updateAlertCommand);

        return await Put<BaseResponse>(request);
    }
}