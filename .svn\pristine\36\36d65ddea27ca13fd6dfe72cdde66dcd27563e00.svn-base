﻿using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;

namespace ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNames;

public class
    GetBusinessFunctionNameQueryHandler : IRequestHandler<BusinessFunctionNameQuery, List<BusinessFunctionNameVm>>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IMapper _mapper;

    public GetBusinessFunctionNameQueryHandler(IMapper mapper, IBusinessFunctionRepository businessFunctionRepository)
    {
        _businessFunctionRepository = businessFunctionRepository;
        _mapper = mapper;
    }

    public async Task<List<BusinessFunctionNameVm>> Handle(BusinessFunctionNameQuery request,
        CancellationToken cancellationToken)
    {
        var businessFunctions = await _businessFunctionRepository.GetBusinessFunctionNames();

        var businessFunctionDto = _mapper.Map<List<BusinessFunctionNameVm>>(businessFunctions);

        return businessFunctionDto;
    }
}