﻿using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;
using ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class CGExecutionReport : DevExpress.XtraReports.UI.XtraReport
    {
        private ILogger<CGExecutionReportController> _logger;
        public CGExecutionReportResultVm ReportData = new CGExecutionReportResultVm();
        public CGExecutionReport(string reportData)
        {
            try
            {
                _logger = CGExecutionReportController._logger;

                InitializeComponent();
                ClientCompanyLogo();
                ReportData = JsonConvert.DeserializeObject<CGExecutionReportResultVm>(reportData);

                DetailReport.DataSource = ReportData.CGExecutionReportVm;
                _username.Text = "Report Generated By : " + ReportData.ReportGeneratedBy;
                //lblWorkflowName.Text = ReportData.ReportGeneratedBy.FirstOrDefault().WorkflowName;

                //var success = ReportData.ScheduleWorkflowActionResultsReportVms.Where(x => x.Status.ToLower().Equals("success"));
                //var error = ReportData.ScheduleWorkflowActionResultsReportVms.Where(x => x.Status.ToLower().Equals("error"));

                //lblSuccessCount.Text = success.Count().ToString();
                //lblErrorCount.Text = error.Count().ToString();
                //lblTotalCount.Text = ReportData.ScheduleWorkflowActionResultsReportVms.Count().ToString();
                _logger.LogInformation("CG Execution Report loaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogInformation($"CG Execution Report throw exception {ex.Message}");
            }
        }
        private int serialNumber = 1;

        private void xrSerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {

                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the CG Execution Report's CP Version. The error message : " + ex.Message); throw;
            }
        }

        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(CGExecutionReportController.CompanyLogo) ? "NA" : CGExecutionReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the CG Execution Report's customer logo" + ex.Message.ToString());
            }
        }
    }
}
