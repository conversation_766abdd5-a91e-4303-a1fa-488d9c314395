﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftJob.Events.RescheduleJob;

public class RescheduleDriftJobEventHandler : INotificationHandler<RescheduleDriftJobEvent>
{
    private readonly ILogger<RescheduleDriftJobEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RescheduleDriftJobEventHandler(ILogger<RescheduleDriftJobEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService loggedInUserService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(RescheduleDriftJobEvent rescheduleJobEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.DriftJob.ToString(),
            Action = $"{ActivityType.Update} {Modules.DriftJob}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Drift Job '{rescheduleJobEvent.Name}' Rescheduled successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Drift Job '{rescheduleJobEvent.Name}' Rescheduled successfully.");
    }
}