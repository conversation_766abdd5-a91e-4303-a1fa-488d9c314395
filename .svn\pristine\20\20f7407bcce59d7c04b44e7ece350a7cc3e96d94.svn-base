﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Services;

public class BaseAuthenticationService
{
    private readonly IConfiguration _config;
    private readonly ILogger _logger;
    private readonly IUserCredentialRepository _userCredentialRepository;
    private readonly IUserLoginRepository _userLoginRepository;
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly IAlertRepository _alertRepository;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    public BaseAuthenticationService(IConfiguration config, IUserRepository userRepository, IUserLoginRepository userLoginRepository,
        IUserCredentialRepository userCredentialRepository, ILogger logger, IEmailService emailService, ISmtpConfigurationRepository smtpConfigurationRepository, IUserInfoRepository userInfoRepository, IAlertRepository alertRepository, IGlobalSettingRepository globalSettingRepository)
    {
        _config = config;
        _userRepository = userRepository;
        _userLoginRepository = userLoginRepository;
        _userCredentialRepository = userCredentialRepository;
        _logger = logger;
        _emailService = emailService;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _userInfoRepository = userInfoRepository;
        _alertRepository = alertRepository;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task VerifyLoginAttempt(User user)
    {
        switch (await IsExceedMaximumLoginAttempt(user))
        {
            case (int)ErrorCode.InvalidAuthentication:

                throw new AuthenticationException(Authentication.InvalidLogin, (int)ErrorCode.InvalidAuthentication);

            //case (int)ErrorCode.AccountLocked:
            //    throw new AuthenticationException(
            //        string.Format(Authentication.LoginExceedMaximumAttempt, user.LoginName),
            //        (int)ErrorCode.AccountLocked);

            case (int)ErrorCode.AccountLocked:
                throw new AuthenticationException(
                    string.Format(Authentication.UserAccountLocked, user.LoginName),
                    (int)ErrorCode.AccountLocked);

            case (int)ErrorCode.InvalidLoginAttempt:
                var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);
                throw new AuthenticationException(string.Format(Authentication.InvalidLoginAttempt,
                    5 - userLogin.InvalidLoginAttempt, user.LoginName), (int)ErrorCode.InvalidLoginAttempt);

        }
    }

    public async Task<int> IsExceedMaximumLoginAttempt(User user)
    {
        const int configuredAttempts = 4;

        var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);

        if (userLogin.IsNull())
        {
            await UpdateUserInvalidLoginAttempt(user);

            return (int)ErrorCode.InvalidAuthentication;
        }

        if (userLogin.InvalidLoginAttempt is >= 2 and <= 3)
        {
            userLogin.InvalidLoginAttempt += 1;

            await _userLoginRepository.UpdateAsync(userLogin);

            return (int)ErrorCode.InvalidLoginAttempt;
        }

        if (userLogin.InvalidLoginAttempt >= configuredAttempts)
        {
            if (!user.IsLock)
            {
                try
                {
                    await SendEmail(user);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error sending email:{ex.Message}");
                }
            }
            user.IsLock = true;
            await _userRepository.UpdateAsync(user);
            userLogin.InvalidLoginAttempt += 1;
            await _userLoginRepository.UpdateAsync(userLogin);

            return (int)ErrorCode.AccountLocked;
        }

        userLogin.InvalidLoginAttempt += 1;

        await _userLoginRepository.UpdateAsync(userLogin);

        _logger.LogInformation(string.Format(Authentication.LoginAttemptUpdated, user.LoginName,
            userLogin.InvalidLoginAttempt));

        return (int)ErrorCode.InvalidAuthentication;
    }

    public async Task VerifyAdLoginAttempt(User user)
    {
        var loginAttempt = await IsExceedMaximumLoginAttempt(user);

        switch (loginAttempt)
        {
            case (int)ErrorCode.InvalidAuthentication:

                throw new AuthenticationException(Authentication.InvalidLogin, (int)ErrorCode.InvalidAuthentication);

            case (int)ErrorCode.AccountLocked:
                throw new AuthenticationException(
                    string.Format(Authentication.LoginExceedMaximumAttempt, user.LoginName),
                    (int)ErrorCode.AccountLocked);

            case (int)ErrorCode.InvalidLoginAttempt:
                var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);
                throw new AuthenticationException(string.Format(Authentication.InvalidLoginAttempt,
                    5 - userLogin.InvalidLoginAttempt, user.LoginName), (int)ErrorCode.InvalidLoginAttempt);
        }
    }

    public async Task UpdateAuthenticateUserCredential(User user)
    {
        var userCredential = await _userCredentialRepository.GetUserCredentialByUserId(user.ReferenceId);

        if (userCredential.Count == 0)
            await _userCredentialRepository.AddAsync(new UserCredential
            {
                UserId = user.ReferenceId,
                LoginPassword = user.LoginPassword,
                IsActive = true
            });
    }

    public async Task UpdateAuthenticateUserLogin(UserLogin userLogin, string userName, string userId, string ipAddress, int sessionTimeout)
    {
        if (userLogin.IsNull())
        {
            await AddNewUserLogin(userId, ipAddress);
        }
        else
        {
            //if (!userLogin.IsProperLoggedOut)
            //{
            if (userLogin.SessionId.IsNotNullOrEmpty() && !string.IsNullOrEmpty(userLogin.LastLoginDate.ToString()))
            {
                //if (DateTime.TryParse(userLogin.LastLoginDate.ToString(), out DateTime lastLoginDate))
                //{
                    var currentTime = DateTime.Now;

                    var sessionTimeout1 = sessionTimeout;

                    var expirationTime = userLogin.LastLoginDate?.AddMinutes(sessionTimeout1);

                    if (expirationTime > currentTime)
                    {
                        throw new AuthenticationException($"User {userName} is already logged in from another session/computer", userId, (int)LogInStatus.MultipleLoginSessionFound);
                    }
                //}
            }
            //}

            var maxAlertId = await _alertRepository.GetAlertByMaxId();

            userLogin.InvalidLoginAttempt = 0;
            userLogin.LastLoginDate = DateTime.Now;
            userLogin.LastLoginIp = ipAddress;
            userLogin.LastAlertId = userLogin.LastAlertId == 0 || userLogin.LastAlertId == 1 ? maxAlertId : userLogin.LastAlertId;
            await _userLoginRepository.UpdateAsync(userLogin);
        }
    }

    public async Task AddNewUserLogin(string userId, string ipAddress)
    {
        var maxAlertId = await _alertRepository.GetAlertByMaxId();

        var userLogin = await _userLoginRepository.GetUserLoginByUserId(userId);
        if (userLogin is null)
        {
            await _userLoginRepository.AddAsync(new UserLogin
            {
                UserId = userId,
                InvalidLoginAttempt = 0,
                LastLoginDate = DateTime.Now,
                LastPasswordChanged = DateTime.Now,
                LastLoginIp = ipAddress,
                LastAlertId = maxAlertId == 0 ? 1 : maxAlertId,
                IsActive = true
            });
        }
    }

    public async Task UpdateUserInvalidLoginAttempt(User user)
    {
        await _userLoginRepository.AddAsync(new UserLogin
        {
            UserId = user.ReferenceId,
            InvalidLoginAttempt = 1,
            LastLoginDate = DateTime.Now,
            LastPasswordChanged = DateTime.Now,
            LastAlertId = 0,
            IsActive = true
        });

        _logger.LogInformation(string.Format(Authentication.LoginAttemptUpdated, user.LoginName, "1"));
    }

    public void VerifyCompany(User user, string requestCompanyId)
    {
        if (!requestCompanyId.IsValidGuid())
            throw new AuthenticationException(Authentication.InvalidLogin, (int)ErrorCode.InvalidAuthentication);

        if (!user.CompanyId.IsValidGuid())
            throw new AuthenticationException($"User Associate with Company ({user.CompanyId}) is invalid",
                (int)ErrorCode.IncorrectCompanyAssociate);

        _logger.LogError($"Requested Company ({requestCompanyId}) cannot be match with user",
            (int)ErrorCode.InvalidCompany);

        throw new AuthenticationException(Authentication.InvalidLogin, (int)ErrorCode.InvalidAuthentication);
    }

    public async Task VerifyPasswordReset(User user)
    {
        if (!user.IsVerify) return;


        _logger.LogInformation("Initiating password reset verification for user {UserName}. Current verification status: {IsVerified}.",
            user.LoginName, user.IsVerify);

        var userLogin = await _userLoginRepository.GetUserLoginByUserId(user.ReferenceId);
        user.IsVerify = false;
        await _userRepository.UpdateAsync(user);

        var isHours = userLogin.LastPasswordChanged != null && (DateTime.Now - userLogin.LastPasswordChanged.Value).TotalHours <= 1;

        if (!isHours)
        {

            _logger.LogError("Login verification attempt for user {UserName}. Password last changed {PasswordChangeTime}. Time difference: {TimeDifference} hours.",
                    user.LoginName, userLogin.LastPasswordChanged, (DateTime.Now - userLogin.LastPasswordChanged.Value).TotalHours);

            await VerifyLoginAttempt(user);
        }
        
    }

    public async Task SendEmail(User user)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Email Notification");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
        {
            _logger.LogWarning("The email notification feature is not enabled in the global settings.");

           return;
        }

        var smtpConfigurations = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfigurations is not null)
        {
            var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(user.ReferenceId);

            var version = _config.GetValue<string>("CP:Version");

            var body = EmailTemplateHelper.GetAccountLockedEmailBody(user.LoginName, version);

            var imageNames = new List<string>
            {
              "abstract.png",
              "cp_logo.png",
              "account_locked.png"
            };

            var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, "AccountLock");

            await _emailService.SendEmail(new EmailDto
            {
                From = smtpConfigurations.UserName,
                To = userInfo.Email,
                Subject = "User Account Locked.",
                HtmlBody = htmlView,
                SmtpHost = smtpConfigurations.SmtpHost,
                Port = smtpConfigurations.Port,
                EnableSSL = smtpConfigurations.EnableSSL,
                Password = smtpConfigurations.Password
            });

            _logger.LogInformation($"User '{user.LoginName}' account locked email send successfully!.");
        }
    }
}