﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetFailurePrediction;

public class GetWorkflowFailurePredictionQueryHandler : IRequestHandler<GetWorkflowFailurePredictionQuery, List<WorkflowFailurePredictionListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;
    public GetWorkflowFailurePredictionQueryHandler(IMapper mapper,
        IWorkflowPredictionRepository workflowPredictionRepository)
    {
        _mapper = mapper;
        _workflowPredictionRepository = workflowPredictionRepository;
    }
    public async Task<List<WorkflowFailurePredictionListVm>> Handle(GetWorkflowFailurePredictionQuery request,
        CancellationToken cancellationToken)
    {
        var failureList = await _workflowPredictionRepository.GetWorkflowFailurePredictionById(request.WorkflowId);


        return failureList.Count > 0 ? failureList
            : new List<WorkflowFailurePredictionListVm>();
    }
}
