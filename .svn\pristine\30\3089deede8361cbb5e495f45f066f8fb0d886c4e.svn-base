﻿using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.GroupPolicy.Queries;

public class GetGroupPolicyPaginatedListQueryHandlerTests : IClassFixture<GroupPolicyFixture>
{
    private readonly GetGroupPolicyPaginatedListQueryHandler _handler;
    private readonly Mock<IGroupPolicyRepository> _mockGroupPolicyRepository;

    public GetGroupPolicyPaginatedListQueryHandlerTests(GroupPolicyFixture groupPolicyFixture)
    {
        var groupPolicyNewFixture = groupPolicyFixture;

        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.GetPaginatedGroupPolicyRepository(groupPolicyNewFixture.GroupPolicies);

        _handler = new GetGroupPolicyPaginatedListQueryHandler(_mockGroupPolicyRepository.Object, groupPolicyNewFixture.Mapper);

        groupPolicyNewFixture.GroupPolicies[0].GroupName = "Group_Test";
        groupPolicyNewFixture.GroupPolicies[0].Properties = "{\"Name\": \"cpadmin\", \"state\": \"Admin@123\"}";

        groupPolicyNewFixture.GroupPolicies[1].GroupName = "Policy_Site";
        groupPolicyNewFixture.GroupPolicies[1].Properties = "{\"Name\": \"admin\", \"password\": \"cpadmin@1234\"}";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetGroupPolicyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<GroupPolicyListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedGroupPolicies_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetGroupPolicyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Group_Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<GroupPolicyListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<GroupPolicyListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].GroupName.ShouldBe("Group_Test");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"cpadmin\", \"state\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetGroupPolicyPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<GroupPolicyListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_FormTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetGroupPolicyPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "groupname=Policy_Site; properties={\"Name\": \"admin\", \"password\": \"cpadmin@1234\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<GroupPolicyListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].GroupName.ShouldBe("Policy_Site");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"cpadmin@1234\"}");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetGroupPolicyPaginatedListQuery(), CancellationToken.None);

        _mockGroupPolicyRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}