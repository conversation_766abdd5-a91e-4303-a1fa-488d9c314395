﻿namespace ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Commands.Create;

public class CreateSvcMsSqlMonitorStatusCommandHandler : IRequestHandler<CreateSvcMsSqlMonitorStatusCommand,
    CreateSvcMsSqlMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly ISvcMsSqlMonitorStatusRepository _svcMsSqlMonitorStatusRepository;

    public CreateSvcMsSqlMonitorStatusCommandHandler(IMapper mapper,
        ISvcMsSqlMonitorStatusRepository svcMsSqlMonitorStatusRepository)
    {
        _mapper = mapper;
        _svcMsSqlMonitorStatusRepository = svcMsSqlMonitorStatusRepository;
    }

    public async Task<CreateSvcMsSqlMonitorStatusResponse> Handle(CreateSvcMsSqlMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var svcMsSqlMonitorStatus = _mapper.Map<Domain.Entities.SvcMsSqlMonitorStatus>(request);

        svcMsSqlMonitorStatus = await _svcMsSqlMonitorStatusRepository.AddAsync(svcMsSqlMonitorStatus);

        var response = new CreateSvcMsSqlMonitorStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.SvcMsSqlMonitorStatus), svcMsSqlMonitorStatus.ReferenceId),

            Id = svcMsSqlMonitorStatus.ReferenceId
        };

        return response;
    }
}