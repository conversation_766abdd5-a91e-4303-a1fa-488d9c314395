﻿using ContinuityPatrol.Application.Features.Server.Events.InfraSummaryEvents.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events
{
    public class ServerInfraSummaryDeletedEventTests
    {
        private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
        private readonly Mock<ILogger<ServerInfraSummaryDeletedEventHandler>> _mockLogger;
        private readonly ServerInfraSummaryDeletedEventHandler _handler;

        public ServerInfraSummaryDeletedEventTests()
        {
            _mockInfraSummaryRepository = new Mock<IInfraSummaryRepository>();
            _mockLogger = new Mock<ILogger<ServerInfraSummaryDeletedEventHandler>>();

            _handler = new ServerInfraSummaryDeletedEventHandler(
                _mockLogger.Object,
                _mockInfraSummaryRepository.Object);
        }

        [Fact]
        public async Task Handle_InfraSummaryCountIsOne_DeletesInfraSummary()
        {
            var deletedEvent = new ServerInfraSummaryDeletedEvent
            {
                Type = "Server",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            var infraSummary = new Domain.Entities.InfraSummary
            {
                Count = 1,
                Type = "Server",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    deletedEvent.Type,
                    deletedEvent.BusinessServiceId,
                    deletedEvent.CompanyId))
                .ReturnsAsync(infraSummary);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.DeleteAsync(infraSummary), Times.Once);
            _mockLogger.Verify(logger => logger.LogInformation($"InfraSummary '{deletedEvent.Type}' deleted successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_InfraSummaryCountIsGreaterThanOne_UpdatesInfraSummary()
        {
            var deletedEvent = new ServerInfraSummaryDeletedEvent
            {
                Type = "Server",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            var infraSummary = new Domain.Entities.InfraSummary
            {
                Count = 5,
                Type = "Server",
                BusinessServiceId = "service-id-456",
                CompanyId = "company-id-789"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    deletedEvent.Type,
                    deletedEvent.BusinessServiceId,
                    deletedEvent.CompanyId))
                .ReturnsAsync(infraSummary);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.InfraSummary>(summary =>
                summary.Count == 4 &&
                summary.Type == "Server" &&
                summary.BusinessServiceId == deletedEvent.BusinessServiceId &&
                summary.CompanyId == deletedEvent.CompanyId
            )), Times.Once);
            _mockLogger.Verify(logger => logger.LogInformation($"InfraSummary '{deletedEvent.Type}' deleted successfully."), Times.Once);
        }
    }
}
