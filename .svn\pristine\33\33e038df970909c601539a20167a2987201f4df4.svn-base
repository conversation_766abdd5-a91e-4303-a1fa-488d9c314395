﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DashboardView.Commands;

public class DeleteDashboardViewTests : IClassFixture<DashboardViewFixture>
{
    private readonly DashboardViewFixture _dashboardViewFixture;

    private readonly Mock<IDashboardViewRepository> _dashboardViewRepositoryMock;

    private readonly DeleteDashboardViewCommandHandler _handler;

    public DeleteDashboardViewTests(DashboardViewFixture dashboardViewFixture)
    {
        _dashboardViewFixture = dashboardViewFixture;
    
        _dashboardViewRepositoryMock = DashboardViewRepositoryMocks.DeleteDashboardViewRepository(_dashboardViewFixture.DashboardViews);
        
        _handler = new DeleteDashboardViewCommandHandler(_dashboardViewRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_DashboardView_Deleted()
    {
        var result = await _handler.Handle(new DeleteDashboardViewCommand { BusinessViewId = _dashboardViewFixture.DashboardViews[0].ReferenceId }, CancellationToken.None);
        
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteDashboardViewLogResponse_When_DashboardViewLogDeleted()
    {
        var result = await _handler.Handle(new DeleteDashboardViewCommand { BusinessViewId = _dashboardViewFixture.DashboardViews[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteDashboardViewResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_DashboardViewDeleted()
    {
        await _handler.Handle(new DeleteDashboardViewCommand { BusinessViewId = _dashboardViewFixture.DashboardViews[0].ReferenceId }, CancellationToken.None);

        var dashboardViewLog = await _dashboardViewRepositoryMock.Object.GetByReferenceIdAsync(_dashboardViewFixture.DashboardViews[0].ReferenceId);

        dashboardViewLog.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDashboardViewId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteDashboardViewCommand { BusinessViewId = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteDashboardViewCommand { BusinessViewId = _dashboardViewFixture.DashboardViews[0].ReferenceId }, CancellationToken.None);

        _dashboardViewRepositoryMock.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _dashboardViewRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.DashboardView>()), Times.Once);
    }
}