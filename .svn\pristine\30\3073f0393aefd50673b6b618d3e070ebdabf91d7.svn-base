using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectSchedulerFixture : IDisposable
{
    public List<InfraObjectScheduler> InfraObjectSchedulerPaginationList { get; set; }
    public List<InfraObjectScheduler> InfraObjectSchedulerList { get; set; }
    public InfraObjectScheduler InfraObjectSchedulerDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WORKFLOW_123";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectSchedulerFixture()
    {
        var fixture = new Fixture();

        InfraObjectSchedulerList = fixture.Create<List<InfraObjectScheduler>>();

        InfraObjectSchedulerPaginationList = fixture.CreateMany<InfraObjectScheduler>(20).ToList();

        // Setup proper test data for InfraObjectSchedulerPaginationList
        InfraObjectSchedulerPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);

        // Setup proper test data for InfraObjectSchedulerList
        InfraObjectSchedulerList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerList.ForEach(x => x.InfraObjectId = InfraObjectId);

        InfraObjectSchedulerDto = fixture.Create<InfraObjectScheduler>();
        InfraObjectSchedulerDto.ReferenceId = Guid.NewGuid().ToString();
        InfraObjectSchedulerDto.IsActive = true;
        InfraObjectSchedulerDto.CompanyId = CompanyId;
        InfraObjectSchedulerDto.InfraObjectId = InfraObjectId;
        InfraObjectSchedulerDto.InfraObjectName = "Test Infrastructure Object";
        InfraObjectSchedulerDto.WorkflowTypeId = "WT_123";
        InfraObjectSchedulerDto.WorkflowType = "Disaster Recovery";
        InfraObjectSchedulerDto.BeforeSwitchOverWorkflowId = WorkflowId;
        InfraObjectSchedulerDto.BeforeSwitchOverWorkflowName = "Before Switchover Workflow";
        InfraObjectSchedulerDto.AfterSwitchOverWorkflowId = "WORKFLOW_456";
        InfraObjectSchedulerDto.AfterSwitchOverWorkflowName = "After Switchover Workflow";
        InfraObjectSchedulerDto.ScheduleType = 1;
        InfraObjectSchedulerDto.CronExpression = "0 0 12 * * ?";
        InfraObjectSchedulerDto.ScheduleTime = "12:00:00";
        InfraObjectSchedulerDto.Status = "Active";
        InfraObjectSchedulerDto.NodeId = "NODE_123";
        InfraObjectSchedulerDto.NodeName = "Test Node";
        InfraObjectSchedulerDto.State = "Running";
        InfraObjectSchedulerDto.IsSchedule = 1;
        InfraObjectSchedulerDto.WorkflowVersion = "1.0";
        InfraObjectSchedulerDto.GroupPolicyId = "GP_123";
        InfraObjectSchedulerDto.GroupPolicyName = "Test Group Policy";
        InfraObjectSchedulerDto.ExecutionPolicy = "Sequential";
        InfraObjectSchedulerDto.IsEnable = true;
        InfraObjectSchedulerDto.LastExecutionTime = DateTime.Now.AddHours(-1).ToString();
        InfraObjectSchedulerDto.ExceptionMessage = null;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
