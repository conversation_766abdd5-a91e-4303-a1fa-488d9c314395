﻿let globalAllLoadBalancer = [], globalType, globalTypeCategory, page = 0, ip_port_data = "", data = {};

const loadBalancerURL = {
    nameExistUrl : "Admin/LoadBalancer/IsLoadBalancerNameExist",
    portandIpExistUrl: "Admin/LoadBalancer/IsLoadBalancerIpandPortExist",
    pagination: "/Admin/LoadBalancer/GetPagination",
    nodeStatus: "Admin/LoadBalancer/UpdateNodeStatus",
    testConfiguration: "Admin/LoadBalancer/TestConfiguration",
    idDefault: "Admin/LoadBalancer/IsDefault",
    createOrUpdate: "Admin/LoadBalancer/CreateOrUpdate",
    delete: "Admin/LoadBalancer/Delete",
}

$('#nodeDeleteModal').attr('data-bs-backdrop', 'static');

$(function () {

    const loadBalancerPermission = {
        createPermission: $("#adminCreate").data("create-permission").toLowerCase(),
        deletePermission: $("#adminDelete").data("delete-permission").toLowerCase()
    }

    if (loadBalancerPermission.createPermission == 'false') {
        $(".createBtn").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }

    btnCrudEnable('confirmDeleteButton')

    let selectedValues = [];

    let dataTable = $('#loadBalancerTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3 align-items-center"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            retrieve: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": loadBalancerURL.pagination,
                "dataType": "json",
                "data": function (d) {

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#nodeSearch').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "error": function (xhr, err) {
                    if (xhr.status === 401) {
                        window.location.assign('/Account/Logout');
                    }
                },
                "dataSrc": function (json) {

                    globalAllLoadBalancer = [];
                    const allLoadBalancer = json?.data && json?.data?.length && json.data.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer");
                    setTimeout(function () {
                        globalAllLoadBalancer = allLoadBalancer;
                    }, 100);
                    ip_port_data = json?.data
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    return json?.data;
                }
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "typeCategory", "name": "Configuration Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (data) {
                            data = data.replace('LoadBalancer', 'Load Balancer');
                            data = data.replace('CPNode', 'CP Node');
                        }
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "ipAddress", "name": "IP Address", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "connectionType", "name": "Connection Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Service Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        data = data?.replace(/([a-z])([A-Z])/g, '$1 $2');

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "hostName", "name": "Host Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "port", "name": "Port", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "healthStatus", "name": "Health State", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass = '';
                        switch (data?.toLowerCase()) {
                            case 'active':
                                iconClass = "cp-active-inactive text-success me-1";
                                break;
                            case 'inactive':
                                iconClass = "cp-active-inactive text-danger me-1";
                                break;
                            case 'pending':
                                iconClass = "cp-pending text-warning me-1";
                                break;
                            default:
                                iconClass = "cp-default-class";
                        }

                        return `<i title="${data || 'NA'}" class="${iconClass}"></i>`;
                    }

                },

                {
                    "data": "isNodeStatus", "name": "Node State", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `
                            <div class="me-3 d-flex align-items-center gap-2 form-switch">
                                <input type="checkbox" id="chk-activity-${row.id}"
                                        class="form-check-input custom-cursor-default-hover toggle-switch"
                                        data-id="${row.id}" data-name="${row.name}" ${row.isNodeStatus ? 'checked' : ''} title="${row.isNodeStatus ? 'Enable' : 'Disable'}">
                            </div>`;
                    }
                },

                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        const isEditAllowed = loadBalancerPermission.createPermission === "true";
                        const isDeleteAllowed = loadBalancerPermission.deletePermission === "true";
                        const isStatusActive = row?.status === 'Active';

                        const nodeType = row?.type?.trim()?.toLowerCase();
                        const iconClass = nodeType === "workflowservice" ? "cp-Workflow-service" : nodeType === "monitorservice" ? "cp-moitoring-service" : nodeType === "resiliencyreadyservice" ? "cp-resielncy-service" : "";

                        const isDefault = `<span role="button" style="${row?.isDefault ? 'pointer-events: none; opacity: 0.5;' : ''}" 
                          title="${row?.isDefault ? "Active" : "Set As Default"}" 
                          class="default-button" 
                          data-name="${row?.name}" 
                          data-default="${row?.isDefault}" 
                          data-id="${row?.id}">
                          <i class="${row?.typeCategory?.toLowerCase() === 'cpnode'
                                ? row?.isDefault
                                    ? `${iconClass} text-success`
                                    : `${iconClass} text-secondary`
                                : ''}"></i>
                      </span>`;

                        const testConnectionBtn = `<span role="button" title="Test Connection" 
                                  class="${isEditAllowed ? '' : 'btn-disabled'}" id="btnTestConnection" data-test-connection='${JSON.stringify(row)}'> <i class="cp-test-connection"></i> </span>`;

                        const editBtn = isEditAllowed
                            ? `<span role="button" title="Edit" class="btnNodeEdit" data-loadbalancer='${JSON.stringify(row)}'> <i class="cp-edit"></i> </span>`
                            : `<span role="button" title="Edit" class="btn-disabled"> <i class="cp-edit"></i></span>`;

                        const deleteBtn = isDeleteAllowed ? `<span role="button" title="Delete" ${isStatusActive ? 'class="btnNodeDelete icon-disabled"' : `class="btnNodeDelete" data-loadbalancer-id="${row.id}" data-loadbalancer-name="${row.name}" data-bs-toggle="modal" data-bs-target="#nodeDeleteModal"`}> <i class="cp-Delete"></i></span>`
                            : `<span role="button" title="Delete" class="btn-disabled"> <i class="cp-Delete"></i> </span>`;

                        const exceptionBtn = row?.exceptionMessage ? `<span title="Exception" role="button" id="load-exception-button" data-bs-toggle="modal" data-bs-target="#nodeErrorModal" data-loadException='${row.exceptionMessage}'> <i class="cp-error-message"></i> </span>`
                            : '';

                        return `<div class="d-flex align-items-center gap-2">${testConnectionBtn}${editBtn}${deleteBtn}${isDefault}${exceptionBtn}</div>`;
                    }
              
                }
            ],

            "columnDefs": [
                {
                    "targets": [2, 6],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },


        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    dataTable.on('order', function () {
        if (dataTable.page() !== page) {
            dataTable.page(page).draw('page');
        }
    });

    dataTable.on('page', function () {
        page = dataTable.page();
    });

    ////search

    $('#nodeSearch').on('keydown input', commonDebounce(function (e) {
        const blockedKeys = ['=', 'Enter'];
        if (blockedKeys.includes(e.key)) {
            e.preventDefault();
            return false;
        }

        const inputValue = $('#nodeSearch').val().trim();
        selectedValues.length = 0;
        const fieldCheckboxes = [
            "#configuration_type",
            "#Name",
            "#ip_address",
            "#connection_type",
            "#host_name",
            "#service_type",
            "#ports"
        ];

        fieldCheckboxes.forEach(selector => {
            const $checkbox = $(selector);
            if ($checkbox.is(':checked')) {
                selectedValues.push($checkbox.val() + inputValue);
            }
        });

        const currentPage = dataTable?.page?.info()?.page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (inputValue && json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false);
        }
    }, 500));

    //$(document).on("click", ".toggle-switch", function (event) {
    //    event.preventDefault();
    //    let nodeId = $(this).data("id");
    //    let nodeName = $(this).data('name')
    //    let isChecked = $(this).is(":checked");
    //    let statusValue = isChecked ? "true" : "false";
    //    $(this).val(statusValue);
    //    data = { id: nodeId, isNodeStatus: statusValue, name: nodeName }
    //    $("#statusData").text(isChecked ? "Enable" : "Disable");
    //    $("#statusPreviousData").text(!isChecked ? "Enable" : "Disable");
    //    $('#nodeDuplicateActionsModal').modal('show');
    //    $("#duplicateConfirmation").data("checkbox", $(this));
    //});

    //$("#duplicateConfirmation").on('click', function () {
    //    let checkbox = $(this).data("checkbox");
    //    let isChecked = !checkbox.prop("checked");
    //    checkbox.prop("checked", isChecked);

    //    $.ajax({
    //        type: "POST",
    //        url: RootUrl + loadBalancerURL.nodeStatus,
    //        data: data,
    //        traditional: true,
    //        headers: {
    //            'RequestVerificationToken': gettoken()
    //        },
    //        dataType: 'json',
    //        success: function (result) {
    //            let dataTable = $('#loadBalancerTable').DataTable();
    //            if (result && result?.data?.success) {
    //                let currentPage = dataTable.page();
    //                dataTable.ajax.reload(function () {
    //                    dataTable.page(currentPage).draw(false);
    //                }, false);
    //                notificationAlert("success", result?.data?.message)
    //            } else {
    //                errorNotification(result);

    //            }
    //        }
    //    })
    //    $('#nodeDuplicateActionsModal').modal('hide');
    //});
   
    // TestConnection
    $("#loadBalancerTable").on("click", "#btnTestConnection", commonDebounce(async function (data) {

        const value = $(this).data("test-connection");
        const nodeData = {};
        nodeData.nodeId = value?.id;

        $.ajax({
            type: "GET",
            url: RootUrl + loadBalancerURL.testConfiguration,
            data: nodeData,
            dataType: 'json',
            success: function (data) {
                if (data && data?.success) {
                    dataTable.ajax.reload();
                    notificationAlert("success", data?.data?.message);
                }
                else {
                    errorNotification(data)
                }
            }
        })
    }, 800));

    //Update
    $('#loadBalancerTable').on('click', '.btnNodeEdit', function () {
        const loadBalancerData = $(this).data('loadbalancer');
        $('#save').text('Update');
        btnCrudEnable('save');
        populateModalFields(loadBalancerData);
        $('#nodeCreateModal').modal('show');
    });

    //delete
    $("#loadBalancerTable").on('click', '.btnNodeDelete', function () {
        const loadBalancerId = $(this).data('loadbalancer-id');
        const loadBalancerName = $(this).data('loadbalancer-name');
        $('#deleteData').text(loadBalancerName);
        $('#textDeleteId').val(loadBalancerId);
    });

    // Name
    $('#nameLB').on('keyup', commonDebounce(async function () {
        const loadBalancerId = $('#loadBalancerId').val();
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateName(value, loadBalancerId, loadBalancerURL.nameExistUrl);
    }, 500));

    $('#portLB').on('input', commonDebounce(async function () {
        const ip_value = $("#IpAddressLB").val();
        const port_value = $("#portLB").val();
        if (port_value) {
            await check_port(ip_value, port_value)
            if ($("#save").text() == "Update") {
                await validateportExists(port_value, ip_value, $('#loadBalancerId').val(), loadBalancerURL.portandIpExistUrl);
            }
        }       
    }, 500));

    $(document).on('change', '.Checkboxs', function () {
        handleCheckboxChange('#chkLBTestConnection');
    });

    handleCheckboxChange('#chkLBTestConnection');

    //Create
    $("#save").on('click', commonDebounce(async function () {
        $("#nodeStatus").prop("checked", true);
        $('#status').val('Pending');
        let name = $("#nameLB").val();
        let ipAddress = $("#IpAddressLB").val();
        let hostName = $("#hostNameLB").val();
        let port = $("#portLB").val();
        let loadBalancerId = $("#loadBalancerId").val();
        let connectionType = document.querySelector('input[name="connectionType"]:checked').value;
        let serviceType = $('input[name="type"]:checked').val();
        let nodestatus = $("#nodeStatus").val();
        let healthStatus = $("#status").val();
        let typeCategory = $('input[name="typecategory"]:checked').val();
        let defaultValue = $("#default").val();
        let Connection = $("#chkLBTestConnection").val();
        
        let isName = await validateName(name, loadBalancerId, loadBalancerURL.nameExistUrl)
        let isIpAddress = await validateIpAddress(ipAddress)
        let isHostName = await validateHostName(hostName)
        let isPort = await validatePort(port)
        if (!isPort) { return false }
        let checkportdata = await check_port(ipAddress, port)
        let portcheck = true
        if ($("#save").text() == "Update") {
            portcheck = await validateportExists($("#portLB").val(), $("#IpAddressLB").val(), $('#loadBalancerId').val(), loadBalancerURL.portandIpExistUrl);

        }

        if (isName && isIpAddress && isHostName && isPort && (checkportdata == undefined ? true : checkportdata) && portcheck) {

            let commonData = {
                Id: loadBalancerId,
                Name : name,
                IPAddress: ipAddress,
                HostName: hostName,
                Port: port,
                ConnectionType: connectionType,
                Type: serviceType,
                IsNodeStatus: nodestatus,
                HealthStatus: healthStatus,
                TypeCategory: typeCategory,
                IsDefault: defaultValue,
                IsConnection: Connection,
                __RequestVerificationToken: gettoken()
            };

            await $.ajax({
                url: RootUrl +loadBalancerURL.createOrUpdate,
                type: "POST",               
                dataType: "json",         
                data: commonData,
                traditional: true,
                success: function (result) {
                    if (result && typeof result === 'object' && result?.success) {
                        notificationAlert("success", result?.data);
                        $('#nodeCreateModal').modal('hide');
                        setTimeout(() => dataTable.ajax.reload(), 1500);
                    } else {
                        errorNotification(result);
                    }
                }
            });
            $(this).prop('disabled', true);
            btnCrudDiasable('save')
        }
    }, 800));

    //Is Default
    $('#loadBalancerTable').on('click', '.default-button', commonDebounce(function () {
        const id = $(this).data('id');
        const isDefault = true; // $(this).data('default') 
        const name = $(this).data('name');
        $.ajax({
            type: "POST",
            url: RootUrl + loadBalancerURL.idDefault,
            dataType: "json",
            data: { "Id": id, "Name": name, "IsDefault": isDefault, __RequestVerificationToken: gettoken() },
            success: function (response) {
                if (response.success) {
                    notificationAlert("success", response?.data?.message);
                    dataTable.ajax.reload();
                } else {
                    errorNotification(response);
                }
            }
        });
    }));

    $("#all").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#chkLBTestConnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });

    $("#monitorService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#chkLBTestConnection").prop('checked', true);
            $("#defaultConnection").removeClass("d-none");
        }
    });

    $("#workflowService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#chkLBTestConnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });

    $("#bothService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#chkLBTestConnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });

    // load exception message

$('#loadBalancerTable').on('click', '#load-exception-button', function () {

    let getExceptionData = $(this).data('loadexception')

    if (getExceptionData) {
        $('#loadExceptionNA').addClass('d-none');
        $('#loadException').text(getExceptionData);
    } else {
        $('#loadException').text("")
        $('#loadExceptionNA').removeClass('d-none');
    }

});

 $('#confirmDeleteButton').on('click',async function () {  
     btnCrudDiasable('confirmDeleteButton');
     let loadBalancerDeleteId = $('#textDeleteId').val();
     await $.ajax({
         url: RootUrl + loadBalancerURL.delete,
         type: "POST",
         dataType: "json",
         data: { id: loadBalancerDeleteId },
         success: function (result) {
             if (result && result?.success) {
                 notificationAlert("success", result?.data);
                 $('#nodeDeleteModal').modal('hide');
                 setTimeout(() => dataTable.ajax.reload(), 1500);
             } else {
                 errorNotification(result);
             }
         }
     })
})

    $('#IpAddressLB').on('keypress keyup', async function (event) {
    const value = $(this).val();
    if (!/^[0-9.]+$/.test(event.key)) {
        event.preventDefault();
    }
    await validateIpAddress(value);
});

$('.pair').on('change', function () {
    typeCategoryBased();
})

    $('#hostNameLB').on('keyup', async function () {
    const value = $(this).val();
    if (value !== undefined) {
        $(this).val(value.replace(/  +/g, " "));
    }
    await validateHostName(value);
});

    $('#portLB').on('keypress keyup', async function (event) {
    const value = $(this).val();
    if (!/[0-9]/.test(event.key)) {
        event.preventDefault();
    }
    await validatePort(value);
});

    $('#create').on('click', function () {

    const errorElements = ['#loadBalNameError', '#loadBalIPAddressError', '#loadBalHostNameError', '#loadBalPortError'];
    clearInputFields('loadBalancerConfigureForm', errorElements);

    // Set default values
    $('#chkLBTestConnection').prop('checked', true);
    $('#defaultConnection').removeClass('d-none');
    $('#loadBalancerId, #nameLB, #IpAddressLB, #hostNameLB, #status, #portLB').val('');
    $('#monitorService, #workflowService, #bothService').prop('disabled', false);
    btnCrudEnable('save');
    $('#save').text('Save').prop('disabled', false);

    const allItems = globalAllLoadBalancer?.filter(item =>
        item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type === "All"
    );

    const togglePRDRIcons = (prEnabled, prChecked, drChecked, showTypeAll, showNodeHttp) => {
        toggleDisableEnable($('#prIcon'), prEnabled);
        $('#prIcon').prop('checked', prChecked);
        $('#drIcon').prop('checked', drChecked);
        $('#typeAll').toggle(showTypeAll);
        $('#node-http').toggleClass('d-none', !showNodeHttp);
    };

    if (allItems?.length > 0) {
        togglePRDRIcons(true, false, true, false, true);
        $('input[name="connectionType"][value="http"]').prop("checked", true);
        $('input[name="type"][value="MonitorService"]').prop("checked", true).prop('disabled', false);
    } else {
        $("#defaultConnection").addClass("d-none");
        $('#chkLBTestConnection,#default').prop('checked', false);

        const loadBalancerTypes = ["MonitorService", "WorkflowService", "ResiliencyReadyService"];
        const typeExists = type =>
            globalAllLoadBalancer?.some(item =>
                item?.typeCategory?.toLowerCase() === "loadbalancer" &&
                item?.type?.toLowerCase() === type.toLowerCase()
            );
        const isAnyTypePresent = loadBalancerTypes.some(type => typeExists(type));
        const monitorType = globalAllLoadBalancer?.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "monitorservice"
        );
        const workflowType = globalAllLoadBalancer?.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "workflowservice"
        );
        const drReadyType = globalAllLoadBalancer?.filter(item =>
            item?.typeCategory?.toLowerCase() === "loadbalancer" &&
            item?.type?.toLowerCase() === "resiliencyreadyservice"
        );
        if (isAnyTypePresent) {
            togglePRDRIcons(true, false, true, false, true);
            $('#monitorService').prop('disabled', false);
        } else {
            togglePRDRIcons(false, true, false, true, false);
            $('input[name="connectionType"][value="https"]').prop("checked", true);
        }
        serviceTypeToggle(monitorType, workflowType, drReadyType, isAnyTypePresent);
        if (!monitorType?.length && !workflowType?.length && !drReadyType?.length) {
            togglePRDRIcons(false, true, false, true, false);
        }
    }
    $('#nodeCreateModal').modal('show');
});

});

