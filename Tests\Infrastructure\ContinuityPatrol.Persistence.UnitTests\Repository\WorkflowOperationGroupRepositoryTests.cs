using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowOperationGroupRepositoryTests : IClassFixture<WorkflowOperationGroupFixture>
    {
        private readonly WorkflowOperationGroupFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowOperationGroupRepository _repoParent;
        private readonly WorkflowOperationGroupRepository _repoNotParent;


        public WorkflowOperationGroupRepositoryTests(WorkflowOperationGroupFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repoParent = new WorkflowOperationGroupRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoNotParent = new WorkflowOperationGroupRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
           
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsAllInfra()
        {
            await _dbContext.WorkflowOperationGroups.AddRangeAsync(_fixture.WorkflowOperationGroupList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowOperationGroupList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsyncForDashBoardView_ReturnsAll_WhenIsAllInfra()
        {
            await _dbContext.WorkflowOperationGroups.AddRangeAsync(_fixture.WorkflowOperationGroupList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ListAllAsyncForDashBoardView();

            Assert.Equal(_fixture.WorkflowOperationGroupList.Count, result.Count);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetWorkflowOperationGroupNames_ReturnsNames_WhenIsAllInfra()
        {
            await _dbContext.WorkflowOperationGroups.AddRangeAsync(_fixture.WorkflowOperationGroupList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupNames();

            Assert.All(result, x => Assert.Contains(_fixture.WorkflowOperationGroupList, y => y.ReferenceId == x.ReferenceId));
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered_WhenIsAllInfra()
        {
            _dbContext.WorkflowOperationGroups.AddRange(_fixture.WorkflowOperationGroupPaginationList);
            _dbContext.SaveChanges();

            var result = _repoParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByWorkflowOperationId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByWorkflowOperationId(entity.WorkflowOperationId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByWorkflowOperationIds_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByWorkflowOperationIds(new List<string> { entity.WorkflowOperationId });

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetOperationGroupByWorkflowOperationIds_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetOperationGroupByWorkflowOperationIds(new List<string> { entity.WorkflowOperationId });

            Assert.All(result, x => Assert.Equal(entity.WorkflowOperationId, x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByWorkflowOperationIdAndNodeId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.NodeId = "Node1";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByWorkflowOperationIdAndNodeId(entity.WorkflowOperationId, "Node1");

            Assert.All(result, x => Assert.Equal("Node1", x.NodeId));
        }

        [Fact]
        public async Task IsWorkflowOperationGroupNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.CurrentActionName = "TestName";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowOperationGroupNameExist("TestName", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowOperationGroupNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repoParent.IsWorkflowOperationGroupNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowOperationGroupNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.ReferenceId = id;
            entity.CurrentActionName = "UniqueName";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowOperationGroupNameExist("UniqueName", id);

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.InfraObjectId = "Infra1";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByInfraObjectId("Infra1");

            Assert.All(result, x => Assert.Equal("Infra1", x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByBusinessServiceId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.BusinessServiceId = "BS1";
            entity.Status = "Completed";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByBusinessServiceId("BS1");

            Assert.All(result, x => Assert.Equal("BS1", x.BusinessServiceId));
        }

        [Fact]
        public async Task GetDrillProfileCountByBusinessServiceId_ReturnsVm()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.BusinessServiceId = "BS2";
            entity.Status = "Completed";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetDrillProfileCountByBusinessServiceId("BS2");

            Assert.NotNull(result);
            Assert.True(result.DrDrillCount >= 0);
        }

        [Fact]
        public async Task GetWorkflowOperationGroupListByWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.WorkflowId = "WID1";
            entity.WorkflowOperationId = "WOID1";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupListByWorkflowId("WID1", "WOID1");

            Assert.All(result, x => Assert.Equal("WID1", x.WorkflowId));
            Assert.All(result, x => Assert.Equal("WOID1", x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowOperationByWorkflowOperationId_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.WorkflowOperationId = "WOID2";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByWorkflowOperationId("WOID2");

            Assert.All(result, x => Assert.Equal("WOID2", x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByInfraObjectIds_ReturnsDictionary_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.InfraObjectId = "Infra2";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByInfraObjectIds(new List<string> { "Infra2" });

            Assert.Contains("Infra2", result.Keys);
        }

        [Fact]
        public async Task IsWorkflowIdExist_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.WorkflowId = "WID3";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowIdExist("WID3");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowIdExist_ReturnsFalse_WhenNotExists()
        {
            var result = await _repoParent.IsWorkflowIdExist("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.WorkflowId = "WID4";
            entity.WorkflowOperationId = "WOID4";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds(new List<string> { "WID4" }, "WOID4");

            Assert.All(result, x => Assert.Equal("WID4", x.WorkflowId));
            Assert.All(result, x => Assert.Equal("WOID4", x.WorkflowOperationId));
        }

        [Fact]
        public async Task GetWorkflowOperationGroupByWorkflowOperationIdByReport_ReturnsList_WhenIsAllInfra()
        {
            var entity = _fixture.WorkflowOperationGroupDto;
            entity.WorkflowOperationId = "WOID5";
            await _dbContext.WorkflowOperationGroups.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationGroupByWorkflowOperationIdByReport("WOID5");

            Assert.All(result, x => Assert.Equal("WOID5", x.WorkflowOperationId));
        }
    }
}