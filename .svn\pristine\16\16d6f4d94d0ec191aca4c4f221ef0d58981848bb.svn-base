﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Events.Create;

public class CredentialProfileCreatedEventHandler : INotificationHandler<CredentialProfileCreatedEvent>
{
    private readonly ILogger<CredentialProfileCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CredentialProfileCreatedEventHandler(ILoggedInUserService userService,
        ILogger<CredentialProfileCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CredentialProfileCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.CredentialProfile.ToString(),
            Action = $"{ActivityType.Create} {Modules.CredentialProfile}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $" CredentialProfile '{createdEvent.CredentialProfileName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CredentialProfile '{createdEvent.CredentialProfileName}' created successfully.");
    }
}