﻿using ContinuityPatrol.Application.Features.UserGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.UserGroupModel;

namespace ContinuityPatrol.Application.UnitTests.Features.UserGroup.Queries
{
    public class GetUserGroupPaginatedListQueryHandlerTests
    {
        private readonly Mock<IUserGroupRepository> _mockUserGroupRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetUserGroupPaginatedListQueryHandler _handler;

        public GetUserGroupPaginatedListQueryHandlerTests()
        {
            _mockUserGroupRepository = new Mock<IUserGroupRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetUserGroupPaginatedListQueryHandler(_mockMapper.Object, _mockUserGroupRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPaginatedResult_WhenValidRequest()
        {
            var query = new GetUserGroupPaginatedListQuery
            {
                SearchString = "Test",
                PageNumber = 1,
                PageSize = 10
            };

            var userGroups = new List<Domain.Entities.UserGroup>
            {
                new Domain.Entities.UserGroup { UserId = "1", UserNames = "Group1" },
                new Domain.Entities.UserGroup { UserId = "2", UserNames = "Group2" }
            };

            var userGroupListVm = new List<UserGroupListVm>
            {
                new UserGroupListVm { Id = "1", GroupName = "Group1" },
                new UserGroupListVm { Id = "2", GroupName = "Group2" }
            };

            _mockUserGroupRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(userGroups.AsQueryable());

            _mockMapper.Setup(m => m.Map<UserGroupListVm>(It.IsAny<Domain.Entities.UserGroup>()))
                .Returns((Domain.Entities.UserGroup userGroup) =>
                    new UserGroupListVm { Id = Guid.NewGuid().ToString(), GroupName = userGroup.GroupName });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("Group1", result.Data[0].GroupName);
            Assert.Equal("Group2", result.Data[1].GroupName);

            _mockUserGroupRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<UserGroupListVm>(It.IsAny<Domain.Entities.UserGroup>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ReturnsEmptyPaginatedResult_WhenNoResults()
        {
            var query = new GetUserGroupPaginatedListQuery
            {
                SearchString = "NoMatch",
                PageNumber = 1,
                PageSize = 10
            };

            var userGroups = new List<Domain.Entities.UserGroup>();

            _mockUserGroupRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(userGroups.AsQueryable());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);

            _mockUserGroupRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<UserGroupListVm>(It.IsAny<Domain.Entities.UserGroup>()), Times.Never);
        }

        [Fact]
        public async Task Handle_MapsCorrectly_WhenDataExists()
        {
            var query = new GetUserGroupPaginatedListQuery
            {
                SearchString = "Group",
                PageNumber = 1,
                PageSize = 10
            };

            var userGroups = new List<Domain.Entities.UserGroup>
            {
                new Domain.Entities.UserGroup { UserId = "1", UserNames = "Group1" }
            };

            var userGroupListVm = new UserGroupListVm { Id = "1", GroupName = "Group1" };

            _mockUserGroupRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(userGroups.AsQueryable());

            _mockMapper.Setup(m => m.Map<UserGroupListVm>(userGroups[0]))
                .Returns(userGroupListVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Group1", result.Data.First().GroupName);

            _mockMapper.Verify(m => m.Map<UserGroupListVm>(userGroups[0]), Times.Once);
        }
    }
}
