﻿namespace ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;

public class GetByConfigDetailQueryHandler : IRequestHandler<GetByConfigDetailQuery, GetByConfigDetailVm>
{
    private readonly IConfiguration _config;

    public GetByConfigDetailQueryHandler(IConfiguration configuration)
    {
        _config = configuration;
    }

    public Task<GetByConfigDetailVm> Handle(GetByConfigDetailQuery request, CancellationToken cancellationToken)
    {
        var config = _config.GetConnectionString("Default");

        var dbProvider = _config.GetConnectionString("DBProvider");

        var decryptString = CryptographyHelper.Decrypt(config);

        var dbProviderString = CryptographyHelper.Decrypt(dbProvider);
       
        var schema = GetConnectionFieldsFromConnectionString(decryptString, dbProviderString);

        var configData = new GetByConfigDetailVm
        {
            ServerName = dbProviderString.ToLower().Equals("oracle")? GetOracleConnectionDetail(schema["data source"],"host")
            : schema.ContainsKey("server") ? schema["server"] + "," + schema["port"] 
            : schema.ContainsKey("data source") ? schema["data source"] : null,
            DatabaseName = dbProviderString.ToLower().Equals("oracle") ? GetOracleConnectionDetail(schema["data source"], "service")
            :schema.ContainsKey("database") ? schema["database"] 
            :schema.ContainsKey("initial catalog") ? schema["initial catalog"] : null,
            UserName = schema.ContainsKey("user id") ? schema["user id"] :
          schema.ContainsKey("user") ? schema["user"] : null,
            Password = schema.ContainsKey("password") ? schema["password"] : null
        };
        //var configData = new GetByConfigDetailVm
        //{
        //    ServerName = schema.ContainsKey("Server") ? schema["Server"] + "," + schema["Port"] :
        //        schema.ContainsKey("Data Source") ? schema["Data Source"] : null,
        //    DatabaseName = schema.ContainsKey("Database") ? schema["Database"] :
        //        schema.ContainsKey("Initial Catalog") ? schema["Initial Catalog"] : null,
        //    UserName = schema.ContainsKey("User ID") ? schema["User ID"] :
        //        schema.ContainsKey("User") ? schema["User"] : null,
        //    Password = schema.ContainsKey("Password") ? schema["Password"] : null
        //};

        configData.Password=configData.Password.IsNotNullOrWhiteSpace()?SecurityHelper.Encrypt(configData.Password) : null;
        return Task.FromResult(configData);
    }

    private Dictionary<string, string> GetConnectionFieldsFromConnectionString(string connectionString, string provider)
    {
        DbConnectionStringBuilder builder = provider.ToLower() switch
        {
            "mysql" => new MySqlConnectionStringBuilder(connectionString),
            "oracle" => new OracleConnectionStringBuilder(connectionString),
            "mssql" => new SqlConnectionStringBuilder(connectionString),
            "npgsql" => new NpgsqlConnectionStringBuilder(connectionString),
            _ => throw new ArgumentException("Unsupported provider name.")
        };

        var connectionFields = new Dictionary<string, string>();

        foreach (string key in builder.Keys) connectionFields[key.ToLower()] = builder[key]?.ToString();

        return connectionFields;
    }

    private string GetOracleConnectionDetail(string dataSource, string patternType)
    {
        var pattern = @"HOST=([\d\.]+).*?PORT=(\d+).*?SERVICE_NAME=([\w\d]+)";
        var match = Regex.Match(dataSource, pattern, RegexOptions.Singleline);

        if (match.Success)
        {
            return patternType.ToLower() switch
            {
                "host" => match.Groups[1].Value,
                "port" => match.Groups[2].Value,
                "service" => match.Groups[3].Value,
                _ => string.Empty
            };
        }
        return string.Empty;
    }


}