using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class RsyncOptionService : BaseClient, IRsyncOptionService
{
    public RsyncOptionService(IConfiguration config, IAppCache cache, ILogger<RsyncOptionService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<RsyncOptionListVm>> GetRsyncOptionList()
    {
        var request = new RestRequest("api/v6/rsyncoptions");

        return await GetFromCache<List<RsyncOptionListVm>>(request, "GetRsyncOptionList");
    }

    public async Task<BaseResponse> CreateAsync(CreateRsyncOptionCommand createRsyncOptionCommand)
    {
        var request = new RestRequest("api/v6/rsyncoptions", Method.Post);

        request.AddJsonBody(createRsyncOptionCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateRsyncOptionCommand updateRsyncOptionCommand)
    {
        var request = new RestRequest("api/v6/rsyncoptions", Method.Put);

        request.AddJsonBody(updateRsyncOptionCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/rsyncoptions/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<RsyncOptionDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/rsyncoptions/{id}");

        return await Get<RsyncOptionDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsRsyncOptionNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/rsyncoptions/name-exist?rsyncoptionName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<RsyncOptionListVm>> GetPaginatedRsyncOptions(GetRsyncOptionPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/rsyncoptions/paginated-list");

      return await Get<PaginatedResult<RsyncOptionListVm>>(request);
  }
   #endregion
}
