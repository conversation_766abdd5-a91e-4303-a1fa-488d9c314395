$(function () {
    // Hide all lists except the outermost.
    $('ul.tree ul').show();

    $('.tree li > ul').each(function (i) {
        var $subUl = $(this);
        var $parentLi = $subUl.parent('li');
        var $toggleIcon = '<i class="js-toggle-icon">-</i>';

        $parentLi.addClass('has-children');

        $parentLi.prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
            $(this).text($(this).text() == '-' ? '+' : '-');
            $subUl.slideToggle('fast');
        });
    });
});



const data = {
    "name": "Core Banking_BS",
    "subname": "(Partial Impact)",
    "fill": "#FFC107",
    "error": true,
    "level": "green",
    "image": "/img/charts_img/incident_icons/business_service.svg",
    "children": [
        {
            "name": "Data Power_BF",
            "subname": "(Major Impact)",
            "fill": "#F28A2E",
            "error": false,
            "level": "green",
            "image": "/img/charts_img/incident_icons/business_function.svg",
            "children": [
                {
                    "name": "Oracle_RAC_DEMO..",
                    "subname": "(Major Impact)",
                    "fill": "#F28A2E",
                    "error": false,
                    "level": "green",
                    "image": "/img/charts_img/incident_icons/infraobject.svg",
                    "children": [
                        {
                            "name": "Oraclehost_Dataguard_d1",
                            "subname": "",
                            "fill": "#FF294D",
                            "error": false,
                            "level": "green",
                            "image": "/img/charts_img/incident_icons/infracomponent.svg",
                        },
                        {
                            "name": "Oraclehost_Dataguard_d3",
                            "subname": "(Impact)",
                            "fill": "#FF294D",
                            "error": false,
                            "level": "green",
                            "image": "/img/charts_img/incident_icons/infracomponent.svg",
                        },

                    ]
                }
            ]
        },


    ]
};
var margin = { top: 20, right: 0, bottom: 30, left: 90 },
    width = 600 - margin.left - margin.right,
    height = 200 - margin.top - margin.bottom;


const svg = d3.select("#treewrappers").append("svg")
// .attr("width", width + margin.right + margin.left)
// .attr("height", height + margin.top + margin.bottom)
g = svg.append("g").attr("transform", "translate(40,0)");


const zoom = d3.zoom()
    .scaleExtent([0.1, 10])
    .on("zoom", zoomed);

svg.call(zoom);
// Zoom out by 20% when the page renders
svg.transition().duration(750).call(zoom.scaleTo, 0.5);
function zoomed(event) {
    g.attr("transform", event.transform);
}

const tree = d3.tree().size([500, width - 160]);

const root = d3.hierarchy(data);
root.x0 = height / 2;
root.y0 = 0;

const update = (source) => {
    const duration = 250;
    const nodes = root.descendants().reverse();
    const links = root.links();

    // Compute the new tree layout
    tree(root);

    let i = 0;

    // Normalize for fixed-depth
    nodes.forEach(d => d.y = d.depth * 180);

    // Update the nodes
    const node = g.selectAll(".node")
        .data(nodes, d => d.id || (d.id = ++i));

    // Enter any new nodes at the parent's previous position
    const nodeEnter = node.enter().append("g")
        .attr("class", "node")
        .attr("transform", d => `translate(${source.y0},${source.x0})`)
        .on("click", (event, d) => {
            d.children = d.children ? null : d._children;
            update(d);
        });
    nodeEnter.on("click", (event, d) => {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        update(d);
    });



    nodeEnter.append("circle")

        .attr("r", "20")
        .style("fill", function (d) {
            return d.data.fill;
        }).attr("stroke", function (d) {
            return d.data.fill
        }).attr("stroke-width", "1.5px").style("stroke-dasharray", "3,3");


    nodeEnter.append("text")
        .attr("dy", "3em")
        .attr("x", 10)
        .style("fill", function (d) {
            return d.data.fill
        })
        .attr("text-anchor", "middle")
        .text(d => d.data.name).append("title") // Add a title element for the tooltip
        .text(d => d.data.name); // Display the tooltip text;



    nodeEnter.append("text")
        .attr("dy", "5em") // Offset the second name below the first
        // .attr("x", 0)
        .attr("text-anchor", "middle").style("fill", function (d) {
            return d.data.fill
        })
        .text(d => d.data.subname); // Display the second name


    // Add images to the nodes
    nodeEnter.filter(d => d.data.image)
        .append("image")
        .attr("xlink:href", d => d.data.image)
        .attr("x", -10)
        .attr("y", -10)
        .attr("width", 20)
        .attr("height", 20);


    // Transition nodes to their new position
    const nodeUpdate = node.merge(nodeEnter).transition()
        .duration(duration)
        .attr("transform", d => `translate(${d.y},${d.x})`);

    // Transition exiting nodes to the parent's new position
    const nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", d => `translate(${source.y},${source.x})`)
        .remove();

    // Update the links
    const link = g.selectAll(".link")
        .data(links, d => d.target.id).style("stroke", d => {
            // Change the link color based on a condition
            return d.target.data.level;
        });

    // Enter any new links at the parent's previous position
    const linkEnter = link.enter().insert("path", "g")
        .attr("class", "link")
        .attr("d", d => {
            const o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        }).style("stroke", d => {
            // Change the link color based on a condition
            return d.target.data.level;
        });

    // Transition links to their new position
    link.merge(linkEnter).transition()
        .duration(duration)
        .attr("d", diagonal);

    // Transition exiting nodes to the parent's new position
    link.exit().transition()
        .duration(duration)
        .attr("d", d => {
            const o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

    // Stash the old positions for transition
    nodes.forEach(d => {
        d.x0 = d.x;
        d.y0 = d.y;
    });
};

const diagonal = d3.linkHorizontal()
    .x(d => d.y)
    .y(d => d.x);

update(root);
