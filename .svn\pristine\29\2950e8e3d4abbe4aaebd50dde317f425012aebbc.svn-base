using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;

public class
    GetHacmpClusterPaginatedListQueryHandler : IRequestHandler<GetHacmpClusterPaginatedListQuery,
        PaginatedResult<HacmpClusterListVm>>
{
    private readonly IHacmpClusterRepository _hacmpClusterRepository;
    private readonly IMapper _mapper;

    public GetHacmpClusterPaginatedListQueryHandler(IMapper mapper, IHacmpClusterRepository hacmpClusterRepository)
    {
        _mapper = mapper;
        _hacmpClusterRepository = hacmpClusterRepository;
    }

    public async Task<PaginatedResult<HacmpClusterListVm>> Handle(GetHacmpClusterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new HacmpClusterFilterSpecification(request.SearchString);
      
        var queryable =await _hacmpClusterRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var hacmpClusterList = _mapper.Map<PaginatedResult<HacmpClusterListVm>>(queryable);

        return hacmpClusterList;
    }
}