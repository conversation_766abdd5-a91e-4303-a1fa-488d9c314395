﻿/*Cyber Resiliency Style*/

.accordion-button {
    padding: 6px;
    font-size: var(--bs-nav-menu-font-size);
    font-weight: var(--bs-menu-font-weight);
    background-color: transparent !important;
    box-shadow: none !important;
}

.accordion-body {
    padding: 3px 6px;
}

.dot-border-bg {
    background-image: radial-gradient(#ebebeb 1px, #fff 1px) !important;
    background-size: 15px 15px;
    border-radius: var(--bs-card-border-radius);
}

.air-gap-section .form-check-input.open:checked {
    background-color: #DAFFC8;
    border-color: #41C200;
}

.air-gap-section .form-switch .form-check-input.open:checked {
    background-position: right center;
    --bs-form-switch-bg: url(/img/layouts_img/green.svg) !important;
    box-shadow: none;
}

.air-gap-section .form-check-input.close:checked {
    background-color: #ffc8c8;
    border-color: red;
}

.air-gap-section .form-switch .form-check-input.close:checked {
    background-position: right center;
    --bs-form-switch-bg: url(/img/layouts_img/red.svg) !important;
    box-shadow: none;
}

.form-check-input:focus {
    border-color: #none;
    outline: 0;
    box-shadow: none;
}

.accordion-button:not(.collapsed)::after {
    background-position: bottom;
}

.accordion-button::after {
    display: none;
}


.dot-border-bg .drag-option-card ul li {
    cursor: move;
}

.dot-border-bg {
    background-image: radial-gradient(#ebebeb 1px, #fff 1px) !important;
    background-size: 15px 15px;
    border-radius: var(--bs-card-border-radius);
}

    .dot-border-bg .drag-option-card ul li a, .zoom-list li a {
        text-decoration: none;
        color: #333;
    }

    .dot-border-bg .drag-option-card ul li a {
        cursor: move;
    }

.drag-card, .drag-card-inside {
    border: 1px solid #959595;
    background: transparent;
}

    .zone-card {
        display: flex;
        align-items: center;
        justify-content: center;
    }

.drag-btn {
    background-color: #C7E1FF;
    color: #067AFF;
    border-color: #C7E1FF;
}

.drag-option-card {
    width: 65px;
    border-radius: 50px;
    box-shadow: 0 .12rem 1rem rgb(0 0 0 / 7%) !important;
}

.zoom-list li {
    margin: 15px 0px;
    padding: 10px 8px;
    border-radius: 10px;
}

    .zoom-list li a:hover i, .drag-option-card ul li:hover a {
        color: var(--bs-primary)
    }

.circle-img {
    position: absolute;
    top: 35%;
    left: 50%;
    width: 35px;
}

.activeMenu {
    color: var(--bs-primary) !important;
}

.Dashboard-diagram #cyberDashboardContainer {
    width: 100% !important;
    zoom: 85%;
    justify-items: center;
}

#cyberDashboardContainer .siteDraggableContainer:nth-last-child(-n+3) {
    width: 50% !important;
    float: left;
}

/*End Cyber Resiliency Style*/