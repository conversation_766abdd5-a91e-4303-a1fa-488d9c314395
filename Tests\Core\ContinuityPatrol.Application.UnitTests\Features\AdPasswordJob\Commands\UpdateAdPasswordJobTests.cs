using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Commands;

public class UpdateAdPasswordJobTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;
    private readonly Mock<IJobScheduler> _mockJobScheduler;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateAdPasswordJobCommandHandler _handler;

    public UpdateAdPasswordJobTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateUpdateAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        _mockLoadBalancerRepository = AdPasswordJobRepositoryMocks.CreateLoadBalancerRepository(_adPasswordJobFixture.LoadBalancers);
        _mockJobScheduler = new Mock<IJobScheduler>();
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((source, destination, srcType, destType) =>
            {
                if (source is UpdateAdPasswordJobCommand cmd && destination is Domain.Entities.AdPasswordJob entity)
                {
                    entity.DomainServerId = cmd.DomainServerId;
                    entity.DomainServer = cmd.DomainServer;
                    entity.State = cmd.State;
                    entity.IsSchedule = cmd.IsSchedule;
                    entity.ScheduleType = cmd.ScheduleType;
                    entity.CronExpression = cmd.CronExpression;
                    entity.ScheduleTime = cmd.ScheduleTime;
                    entity.NodeId = cmd.NodeId;
                    entity.NodeName = cmd.NodeName;
                    entity.ExceptionMessage = cmd.ExceptionMessage;
                }
            });

        
        _handler = new UpdateAdPasswordJobCommandHandler(
            _mockJobScheduler.Object,
            _mockMapper.Object,
            _mockAdPasswordJobRepository.Object,
            _mockPublisher.Object,
            _mockLoadBalancerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_UpdateAdPasswordJobResponse_When_AdPasswordJobUpdated()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand 
        { 
            Id = existingJob.ReferenceId,
            DomainServer = "UpdatedDomainServer"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(UpdateAdPasswordJobResponse));
        result.Id.ShouldBe(existingJob.ReferenceId);
        result.Message.ShouldContain("AdPasswordJob");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordJob>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishEvent_OnlyOnce()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordJobUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map(It.IsAny<object>(), It.IsAny<object>(),
            It.IsAny<Type>(), It.IsAny<Type>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_ScheduleJob_When_NodeConfigurationExists()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };

        // Act
       var result = await _handler.Handle(command, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateAdPasswordJobResponse));

    }

    [Fact]
    public async Task Handle_Not_Call_ScheduleJob_When_NodeConfigurationNotExists()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };
        
        _mockLoadBalancerRepository.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockJobScheduler.Verify(x => x.ScheduleJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordJobNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateAdPasswordJobCommand { Id = nonExistentId };

        _mockAdPasswordJobRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordJob)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_PublishEventWithCorrectName_When_AdPasswordJobUpdated()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand 
        { 
            Id = existingJob.ReferenceId,
            DomainServer = "UpdatedDomainServer"
        };

        AdPasswordJobUpdatedEvent capturedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<AdPasswordJobUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<AdPasswordJobUpdatedEvent, CancellationToken>((evt, ct) => capturedEvent = evt)
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEvent.ShouldNotBeNull();
        capturedEvent.Name.ShouldBe(existingJob.DomainServer);
    }

    [Fact]
    public async Task Handle_Call_GetNodeConfiguration_Twice_When_FirstReturnsNull()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var command = new UpdateAdPasswordJobCommand { Id = existingJob.ReferenceId };
        
        _mockLoadBalancerRepository.SetupSequence(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null)
            .ReturnsAsync(_adPasswordJobFixture.LoadBalancers.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockLoadBalancerRepository.Verify(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()), Times.Exactly(2));
    }
}
