using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Delete;

namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Delete;

public class DeleteAdPasswordExpireCommandHandler : IRequestHandler<DeleteAdPasswordExpireCommand, DeleteAdPasswordExpireResponse>
{
    private readonly IAdPasswordExpireRepository _adPasswordExpireRepository;
    private readonly IPublisher _publisher;

    public DeleteAdPasswordExpireCommandHandler(IAdPasswordExpireRepository adPasswordExpireRepository, IPublisher publisher)
    {
        _adPasswordExpireRepository = adPasswordExpireRepository;

        _publisher = publisher;
    }

    public async Task<DeleteAdPasswordExpireResponse> Handle(DeleteAdPasswordExpireCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _adPasswordExpireRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.AdPasswordExpire),
            new NotFoundException(nameof(Domain.Entities.AdPasswordExpire), request.Id));

        eventToDelete.IsActive = false;

        await _adPasswordExpireRepository.UpdateAsync(eventToDelete);

        var response = new DeleteAdPasswordExpireResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.AdPasswordExpire), eventToDelete.UserName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new AdPasswordExpireDeletedEvent { Name = eventToDelete.UserName }, cancellationToken);

        return response;
    }
}
