using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;

namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetList;

public class GetAdPasswordExpireListQueryHandler : IRequestHandler<GetAdPasswordExpireListQuery, List<AdPasswordExpireListVm>>
{
    private readonly IAdPasswordExpireRepository _adPasswordExpireRepository;
    private readonly IMapper _mapper;

    public GetAdPasswordExpireListQueryHandler(IMapper mapper, IAdPasswordExpireRepository adPasswordExpireRepository)
    {
        _mapper = mapper;
        _adPasswordExpireRepository = adPasswordExpireRepository;
    }

    public async Task<List<AdPasswordExpireListVm>> Handle(GetAdPasswordExpireListQuery request, CancellationToken cancellationToken)
    {
        var adPasswordExpires = await _adPasswordExpireRepository.ListAllAsync();

        if (adPasswordExpires.Count <= 0) return new List<AdPasswordExpireListVm>();

        return _mapper.Map<List<AdPasswordExpireListVm>>(adPasswordExpires);
    }
}
