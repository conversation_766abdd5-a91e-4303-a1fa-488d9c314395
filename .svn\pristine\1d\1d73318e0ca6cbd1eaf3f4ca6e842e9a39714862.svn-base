﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetByType;

public class GetMYSQLMonitorLogsDetailByTypeQueryHandler : IRequestHandler<GetMYSQLMonitorLogsDetailByTypeQuery,
    List<MYSQLMonitorLogsDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorLogsRepository _mysqlMonitorLogsRepository;

    public GetMYSQLMonitorLogsDetailByTypeQueryHandler(IMysqlMonitorLogsRepository mysqlMonitorLogsRepository,
        IMapper mapper)
    {
        _mysqlMonitorLogsRepository = mysqlMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<List<MYSQLMonitorLogsDetailByTypeVm>> Handle(GetMYSQLMonitorLogsDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorLogs = await _mysqlMonitorLogsRepository.GetDetailByType(request.Type);

        return mysqlMonitorLogs.Count <= 0
            ? new List<MYSQLMonitorLogsDetailByTypeVm>()
            : _mapper.Map<List<MYSQLMonitorLogsDetailByTypeVm>>(mysqlMonitorLogs);
    }
}