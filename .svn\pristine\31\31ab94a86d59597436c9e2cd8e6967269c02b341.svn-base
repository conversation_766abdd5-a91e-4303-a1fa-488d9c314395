﻿using ContinuityPatrol.Application.Features.DRCalendar.Events.SendEmail;

namespace ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;

public class UpdateDrCalenderCommandHandler : IRequestHandler<UpdateDrCalenderCommand, UpdateDrCalenderResponse>
{
    private readonly IDrCalenderRepository _drCalenderRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateDrCalenderCommandHandler(IMapper mapper, IDrCalenderRepository drCalenderRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _drCalenderRepository = drCalenderRepository;
        _publisher = publisher;
    }

    public async Task<UpdateDrCalenderResponse> Handle(UpdateDrCalenderCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _drCalenderRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(DrCalenderActivity), request.Id);

        request.InvitationNo = eventToUpdate.InvitationNo + 1;

        _mapper.Map(request, eventToUpdate, typeof(UpdateDrCalenderCommand), typeof(DrCalenderActivity));

        await _drCalenderRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDrCalenderResponse
        {
            Message = Message.Update("DR Calender Activity", eventToUpdate.ActivityName),

            Id = eventToUpdate.ReferenceId
        };


        await _publisher.Publish(new DrCalendarSendEmailEvent
        {
            Id = eventToUpdate.ReferenceId,
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            Description = eventToUpdate.Description,
            ActivityName = eventToUpdate.ActivityName,
            InvitationNo = eventToUpdate.InvitationNo,
            ScheduledStartDate = eventToUpdate.ScheduledStartDate,
            ScheduledEndDate = eventToUpdate.ScheduledEndDate,
            Responsibility = eventToUpdate.Responsibility,
            RecipientTwo = eventToUpdate.RecipientTwo,
            WorkflowProfiles = eventToUpdate.WorkflowProfiles,
            File = request.File,
            Location = eventToUpdate.Location,
            MailSentActivity = "UPDATE",
            SetReminder = request.SetReminders
        }, cancellationToken);


        return response;
    }
}