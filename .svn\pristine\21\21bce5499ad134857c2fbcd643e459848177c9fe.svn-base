﻿using ContinuityPatrol.Application.Features.UserGroup.Queries.GetAssignedUserGroups;

namespace ContinuityPatrol.Application.UnitTests.Features.UserGroup.Queries
{
    public class GetAllUserWithGroupListQueryHandlerTests
    {
        private readonly Mock<IUserGroupRepository> _mockUserGroupRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetAllUserWithGroupListQueryHandler _handler;

        public GetAllUserWithGroupListQueryHandlerTests()
        {
            _mockUserGroupRepository = new Mock<IUserGroupRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetAllUserWithGroupListQueryHandler(
                _mockUserGroupRepository.Object,
                _mockMapper.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsUsersWithUserGroup_WhenRepositoryReturnsData()
        {
            var userGroups = new List<Domain.Entities.UserGroup>
            {
                new Domain.Entities.UserGroup
                {
                    ReferenceId = "1",
                    GroupName = "Admin",
                    UserProperties = "AdminUser,OtherUser"
                },
                new Domain.Entities.UserGroup
                {
                   ReferenceId = "2",
                   GroupName = "User",
                   UserProperties = "RegularUser,GuestUser"
                }
            };

            var userGroupDtos = new List<AssignedUsers>
            {
                new AssignedUsers
                {
                    Id = "1",
                    Properties = "AdminUser,OtherUser"
                },
                new AssignedUsers
                {
                    Id = "2",
                    Properties = "RegularUser,GuestUser"
                }
            };

            _mockUserGroupRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userGroups);

            _mockMapper.Setup(mapper => mapper.Map<List<AssignedUsers>>(userGroups))
                .Returns(userGroupDtos);

            var query = new GetAllUserWithGroupListQuery();
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.NotEmpty(result.AssignedUsersWithGroup);
            Assert.Equal(2, result.AssignedUsersWithGroup.Count);

            Assert.Equal("AdminUser", result.AssignedUsersWithGroup[0].Name);
            Assert.Equal("1", result.AssignedUsersWithGroup[0].Id);
            Assert.Equal("RegularUser", result.AssignedUsersWithGroup[1].Name);
            Assert.Equal("2", result.AssignedUsersWithGroup[1].Id);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyUsersWithUserGroup_WhenRepositoryReturnsNoData()
        {
            _mockUserGroupRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.UserGroup>());

            _mockMapper.Setup(mapper => mapper.Map<List<AssignedUsers>>(It.IsAny<List<Domain.Entities.UserGroup>>()))
                .Returns(new List<AssignedUsers>());

            var query = new GetAllUserWithGroupListQuery();
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Empty(result.AssignedUsersWithGroup);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryFails()
        {
            _mockUserGroupRepository.Setup(repo => repo.ListAllAsync())
                .ThrowsAsync(new System.Exception("Database error"));

            var query = new GetAllUserWithGroupListQuery();
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<System.Exception>(() =>
                _handler.Handle(query, cancellationToken));
        }
    }
}
