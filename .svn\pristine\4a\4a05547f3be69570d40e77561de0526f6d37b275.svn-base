﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Template.Events.Update;

public class TemplateUpdatedEventHandler : INotificationHandler<TemplateUpdatedEvent>
{
    private readonly ILogger<TemplateUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public TemplateUpdatedEventHandler(ILoggedInUserService userService, ILogger<TemplateUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(TemplateUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var result = await _userActivityRepository.AddAsync(
            new Domain.Entities.UserActivity
            {
                UserId = _userService.UserId,
                LoginName = _userService.LoginName,
                RequestUrl = _userService.RequestedUrl,
                CompanyId = _userService.CompanyId,
                HostAddress = _userService.IpAddress,
                Action = $"{ActivityType.Update} {Modules.Template}",
                Entity = Modules.Template.ToString(),
                ActivityType = ActivityType.Update.ToString(),
                ActivityDetails = $"Template '{updatedEvent.TemplateName}' updated successfully."
            });

        _logger.LogInformation($"Template '{updatedEvent.TemplateName}' updated successfully.");
    }
}