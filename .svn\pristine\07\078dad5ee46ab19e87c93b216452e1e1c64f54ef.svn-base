﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels.ForgotPasswordViewModel

@using Microsoft.Extensions.Configuration

@inject IConfiguration Configuration

@{
    ViewData["Title"] = "ForgotPassword";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
@Html.AntiForgeryToken()

<div class="container-fluid">
    <div class="align-items-center vh-100 row">
       
        <div class="d-grid justify-content-center align-items-center col vh-100 bg-light">
            <div class="px-4 py-1">
                <img class="mb-4" src="~/img/logo/pts_logo.png" alt="Customer Logo" height="40" />
                <div class="">
                    <h4>Compliance Management & Operational Resilience</h4>
                    <h6 class="fw-normal my-3">Simple workflows to perform actions & failovers in few clicks.</h6>
                </div>
                <img src="~/img/isomatric/forget_password_iso.svg" style="width:100%;" />
                @* <button class="btn btn-primary btn-sm" type="button" data-bs-toggle="modal" data-bs-target="#DeleteAccountModal">Delete Account</button> *@
            </div>
        </div> 
        <div class="col-xl-5 col-lg-6 col-md-9 col-sm-8 position-relative h-100">
            <div class="justify-content-center row">
                <div class="col-xxl-7 col-xl-9 col-lg-8 col-md-9 col-sm-8 pt-5 mt-xxl-5">
                    <div class="card login_card">
                        <div class="d-flex align-items-end p-3 card-header">
                            <img src="~/img/logo/cplogo.svg" title="CP Logo" alt="Logo" width="320" />
                        </div>
                        <div class="card-body">
                            <div class="font-weight-bold mb-4">
                              
                                <h6 class="mt-3">Forgot Password?</h6>
                                <span class="text-muted">Enter your login name below and we’ll send you a password on registered email</span>

                            </div>
                            <form id="ForgotPasswordModel" asp-action="ForgotPassword">
                                <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                                <div class="mb-3">
                                    <div class="form-group">
                                        <label class="form-label">Login Name</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-user"></i>
                                            </span>
                                            <input asp-for="LoginName" type="text" id="txtForgotLoginName" placeholder="Enter Login Name" autocomplete="off" class="form-control">
                                        </div>
                                        <span asp-validation-for="LoginName" id="error-loginName"></span>
                                        <input asp-for="NewPassword" type="hidden" id="textNewPassword" />
                                        <input asp-for="Password" type="hidden" id="textPassword" />
                                    </div>
                                </div>
                                @* <div class="mb-3">
                                    <div class="form-group">
                                        <label class="form-label">Email Address</label>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="cp-email"></i>
                                            </span>
                                            <input asp-for="Email" type="email" id="txtEmail" placeholder="Enter Email Address" class="form-control" readonly="readonly">
                                        </div>
                                        <span asp-validation-for="Email" id="error-Email"></span>
                                        <input asp-for="NewPassword" type="hidden" id="textNewPassword" />
                                        <input asp-for="Password" type="hidden" id="textPassword" />
                                    </div>
                                </div> *@
                                <div class="text-end">
                                    <a asp-action="Login" class="form-check-label">Back to Login?</a>
                                </div>

                                <form method="post" class="mb-3 mt-4 gap-2 d-flex">
                                    <div class="form-group" style="display: flex">
                                        <button type="button" class="btn btn-primary login-btn sendmail" style="margin-right:5px" id="sendMailButton">Send Mail</button>
                                        <button class="btn btn-secondary login-btn" id="cancel" type="button">Reset</button>
                                    </div>
                                </form>


                            </form>
                        </div>
                      
                    </div>
                </div>
             
            </div>
            <footer class="text-center p-1 position-absolute bottom-0 end-0 start-0">

                @{
                    var version = Configuration.GetValue<string>("CP:Version");
                    var isCOE = Configuration.GetValue<string>("Release:isCOE");
                }

                @if (@isCOE != null)
                {
                    <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }
                else
                {
                    <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
                }

                @* <small>Continuity Patrol Version <span class="cpVersionData"></span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small> *@
            </footer>
        </div>
    </div>
   
</div>


@* Notification *@
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='success-toast'>
                    <i id="icon_Detail" class='cp-check toast_icon'></i>
                </span>
                <span id="message">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>

@section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}
<script>
    let version = sessionStorage.getItem('cpVersion')
    $('.cpVersionData').text(version)
</script>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/ForgotPassword.js"></script>

