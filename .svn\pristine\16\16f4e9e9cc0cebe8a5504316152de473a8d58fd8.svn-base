﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Update;

namespace ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Commands.Update;

public class UpdateWorkflowExecutionEventLogCommandHandler : IRequestHandler<UpdateWorkflowExecutionEventLogCommand,
    UpdateWorkflowExecutionEventLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowExecutionEventLogRepository _workflowExecutionEventLogRepository;

    public UpdateWorkflowExecutionEventLogCommandHandler(IMapper mapper,
        IWorkflowExecutionEventLogRepository workflowExecutionEventLogRepository)
    {
        _mapper = mapper;
        _workflowExecutionEventLogRepository = workflowExecutionEventLogRepository;
    }

    public async Task<UpdateWorkflowExecutionEventLogResponse> Handle(UpdateWorkflowExecutionEventLogCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowExecutionEventLogRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.WorkflowActionResult), request.Id);
        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowActionResultCommand),
            typeof(Domain.Entities.WorkflowActionResult));

        await _workflowExecutionEventLogRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowExecutionEventLogResponse
        {
            Message = Message.Update(nameof(Domain.Entities.WorkflowActionResult), eventToUpdate.WorkflowActionName),

            WorkflowExecutionEventLogId = eventToUpdate.ReferenceId
        };
        return response;
    }
}