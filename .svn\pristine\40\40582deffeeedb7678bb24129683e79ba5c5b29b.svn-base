﻿
$(function () {
    let mId = sessionStorage.getItem("monitorId");
    let monitortype = 'mssqldbmirroring';
    let infraObjectId = sessionStorage.getItem("infraobjectId");
    let replicaType = sessionStorage.getItem('replicationType');
    setTimeout(() => { MSSQLDBMIRROINGmonitorstatus(mId, monitortype) }, 250)
    setTimeout(() => { dbMirrorServer(infraObjectId) }, 250)

    setTimeout(() => { monitoringSolution(infraObjectId) }, 250)

    function MSSQLDBMIRROINGmonitorstatus(id, type) {
        $.ajax({
            url: "/Monitor/MSSQLMirror/GetMonitorServiceStatusByIdAndType",
            method: 'GET',
            data: {
                monitorId: id,
                type: type,
            },
            dataType: 'json',
            async: true,
            success: function (data) {

                infraDataa(data);
                propertiesDataa(data);
                let dataValue = JSON?.parse(data?.properties);
            },
            error: function (error) {
                console.error('Error:', error);
            }
        });
    }
    $('#mssqlserver').hide();
    async function dbMirrorServer(id) {

        let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
        let data = {}
        data.infraObjectId = id;
        let mssqlServerData = await getAysncWithHandler(url, data);

        if (mssqlServerData != null && mssqlServerData?.length > 0) {
            mssqlServerData?.forEach(data => {
                let value = data?.isServiceUpdate
                let parsed = []
                if (value && value !== 'NA') parsed = JSON?.parse(value)
                if (parsed) {
                    $('#mssqlserver').show();
                    bindDBMirrorServer(mssqlServerData)
                }
            })
           
        } else {
            $('#mssqlserver').hide();
        }

    }
    function bindDBMirrorServer(mssqlServerData) {
        
        let prType = { IpAddress: '--', Services: [] };
        let drType = { IpAddress: '--', Services: [] };

        // Loop through each item to find PR and DR entries
        mssqlServerData?.forEach(item => {
            let parsedServices = [];
            try {
                const value = item?.isServiceUpdate
                if (value && value !== 'NA') {
                    parsedServices = JSON.parse(item?.isServiceUpdate)
                }
            } catch (e) {
                console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
            }
           
            parsedServices?.forEach(serviceGroup => {
                if (serviceGroup?.Type === 'PR') {
                    prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                    prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
                } else if (serviceGroup?.Type === 'DR') {
                    prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                    prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
                }
            });
        });

        // Set header IPs
        $('#prIp').text('Primary (' + prType?.IpAddress + ')');
        $('#drIp').text('DR (' + drType?.IpAddress + ')');
        
        const prStatusSummary = getStatusSummary(prType.Services?.map(s => s.Status).filter(Boolean));
        const drStatusSummary = getStatusSummary(drType.Services?.map(s => s.Status).filter(Boolean));
        $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
        $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

        // Unique list of all service names from both PR and DR
        let allServiceNames = [...new Set([
            ...prType?.Services?.map(s => s?.ServiceName),
            ...drType?.Services?.map(s => s?.ServiceName)
        ])];

        // Build table rows
        let tbody = $('#mssqlserverbody');
        tbody.empty();

        allServiceNames?.forEach(serviceName => {
            let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
            let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

            let prStatus = prService ? prService?.Status : '--';
            let drStatus = drService ? drService?.Status : '--';
            let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
            let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
            const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

            let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
            tbody.append(row);
        });
    }
    function getStatusSummary(arr) {
        let countMap = {};
        arr?.forEach(status => {
            countMap[status] = (countMap[status] || 0) + 1;
        });
        let total = arr?.length;
        let statusSummary = Object.entries(countMap)
            .map(([status, count]) => `${count} ${status}`)
            .join(', ');
        return statusSummary ? `${statusSummary} of ${total}` : '--';
    }
    function getStatusIconClass(status) {
        if (!status) return "text-danger cp-disable";

        const lowerStatus = status.toLowerCase();
        if (lowerStatus === "running") {
            return "text-success cp-reload cp-animate";
        } else if (lowerStatus === "error" || lowerStatus === "stopped") {
            return "text-danger cp-fail-back";
        } else {
            return "text-danger cp-disable";
        }
    }
    $("#backtoITview").on('click', function () {
        window.location.assign('/Dashboard/ITResiliencyView/List');
    })
    function infraDataa(value) {
        $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
        $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
    }
    $("#replica").text(replicaType).prepend('<i class="cp-replication-type me-1 text-primary"></i>'); 
    $("#DR_replica").text(replicaType).prepend('<i class="cp-replication-type me-1 text-primary"></i>');
    function checkAndReplace(value) {
        return (value === null || value === '' || value === undefined) ? 'NA' : value;
    }
    let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">'
    let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
    function propertiesDataa(value) {
        let ipprdata;
        if (value === undefined || value === null || value === '') {
            $("#noDataimg").css('text-align', 'center').html(noDataImage);
        }
        else {
            let data = JSON?.parse(value?.properties);

            let customSite = data?.MSSQLDBMirroringModel?.length > 1;
            if (customSite) {
                $("#Sitediv").show();
            } else {
                $("#Sitediv").hide();
            }

            $(".siteContainer").empty();


            data?.MSSQLDBMirroringModel?.forEach((a, index) => {
                let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
                $(".siteContainer").append(selectTab);
            });


            if (data?.MSSQLDBMirroringModel?.length > 0) {
                $("#siteName0 .nav-link").addClass("active");
                displaySiteData(data?.MSSQLDBMirroringModel[0]);
            }

            let defaultSite = data?.MSSQLDBMirroringModel?.find(d => d?.Type === 'DR') || data?.MSSQLDBMirroringModel[0];
            if (defaultSite) {
                displaySiteData(defaultSite);
            }
            $(document).on('click', '.siteListChange', function () {
                $(".siteListChange .nav-link").removeClass("active");
                $(this).find(".nav-link").addClass("active");
                let siteId = $(this)[0].id
                let getSiteName = $(`#${siteId} .siteName`).text()

                let MonitoringModel = data?.MSSQLDBMirroringModel?.find(d => d?.Type === getSiteName);
                if (MonitoringModel) {
                    $('#PR_Server_IpAddress').find('i').remove();
                    $('#DR_Server_IpAddress').find('i').remove();
                    displaySiteData(MonitoringModel);
                }
            });
            function displaySiteData(siteData) {
                let obj = {};
                $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

                for (let key in siteData) {
                    obj[`DR_` + key] = siteData[key];
                }
                ipprdata = obj?.DR_connectViaHostName?.toLowerCase() === "true" ? obj?.DR_Server_HostName : obj?.DR_Server_IpAddress

                $("#DR_Server_IpAddress").text(ipprdata)
                let MonitoringModelMirrior = [
                    "DR_ServerName", "DR_Database", "DR_LSN_Logs", "DR_Server_NetworkAddress", "DR_PR_Datalag"
                ];

                if (Object.keys(obj)?.length > 0) {
                    bindProperties(obj, MonitoringModelMirrior, value, ipprdata);
                }


                let objmirrior = {};

                for (let key in siteData?.sqlDBMirroring) {
                    objmirrior[`DR_` + key] = siteData?.sqlDBMirroring[key];
                }

                let MonitoringModelSqlmirrior = [
                    "DR_OpreationMode", "DR_DBRole", 
                    "DR_MirroringState", "DR_LogGenerateRate", "DR_UnsentLog", "DR_SentRate",
                    "DR_UnrestoredLog", "DR_RecoveryRate", "DR_TransactionDelay", "DR_TransactionPerSecond",
                    "DR_AverageDelay", "DR_TimeRecorded", "DR_TimeBehind", "DR_LocalTime",
                    "InfraObjectId", "MSSQL_Database_State_DR", "MSSQL_Database_Recovery_Model_DR",
                    
                ]; 

                if (Object.keys(objmirrior)?.length > 0) {
                    bindProperties(objmirrior, MonitoringModelSqlmirrior, value);
                }
            }

            let sqldbmirormonitor = data?.PrMSSQLDBMirroringModel;
            ipprdata = sqldbmirormonitor?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? sqldbmirormonitor?.PR_Server_HostName : sqldbmirormonitor?.PR_Server_IpAddress

            $('#PR_Server_IpAddress').text(ipprdata)
            // const sqldbmirormonitorProp = ["PR_Server_IpAddress", "DR_Server_IpAddress", "PRServerName", "DRServerName", "PR_LSN_Logs", "DR_LSN_Logs", "PR_Datalag", "PR_Server_NetworkAddress","DR_Server_NetworkAddress"];
            const sqldbmirormonitorProp = ["PRServerName", "PR_Database", "PR_LSN_Logs","PR_Server_NetworkAddress"];
            if (sqldbmirormonitor !== '' && sqldbmirormonitor !== null && sqldbmirormonitor !== undefined) {
                bindProperties(sqldbmirormonitor, sqldbmirormonitorProp, value, ipprdata);
            } else {
                $("#db2monitor").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>DB2 Monitoring Details is not available</span>");
            }

            //Replication Monitoring
            let sqlDBMirroringrepli = checkAndReplace(data?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr);

            const sqlDBMirroringMonitor = ["PROpreationMode", "PRDBRole", 
                "PRMirroringState", "PRLogGenerateRate", "PRUnsentLog", "PRSentRate", "PRUnrestoredLog",
                "PRRecoveryRate", "PRTransactionDelay", "PRTransactionPerSecond", "PRAverageDelay",
                "PRTimeRecorded", "PRTimeBehind", "PRLocalTime",
                "InfraObjectId", "MSSQL_Database_State_PR", "MSSQL_Database_Recovery_Model_PR",
               /* "DataLag"*/
            ];
            if (sqlDBMirroringrepli !== '' && sqlDBMirroringrepli !== null && sqlDBMirroringrepli !== undefined) {
                bindProperties(sqlDBMirroringrepli, sqlDBMirroringMonitor, value);
            } else {
                $("#mongorepli").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>DB2 Replication Monitoring Details is not available</span>");
            }

            //DB Size
            //$('#PR_Dbsize').text(checkAndReplace(sqlDBMirroringrepli.PRDatabaseSize)).attr('title', checkAndReplace(sqlDBMirroringrepli.PRDatabaseSize));
            //$('#DR_Dbsize').text(checkAndReplace(sqlDBMirroringrepli.DRDatabaseSize)).attr('title', checkAndReplace(sqlDBMirroringrepli.DRDatabaseSize));
            var pSqlValue = checkAndReplace(sqlDBMirroringrepli?.PrDatabaseSize);
            var drSqlValue
            data?.MSSQLDBMirroringModel.forEach((x, i) => {
                drSqlValue = checkAndReplace(x?.sqlDBMirroring?.DatabaseSize);
            })

            if (pSqlValue === 'NA') {
                $('#PR_Dbsize').text(pSqlValue).attr('title', pSqlValue);
            } else if (pSqlValue?.includes("MB")) {
                $('#PR_Dbsize').text(pSqlValue).attr('title', pSqlValue);
            } else {
                $('#PR_Dbsize').text(pSqlValue + " MB").attr('title', pSqlValue + " MB");
            }
            if (drSqlValue === 'NA') {
                $('#DR_Dbsize').text(drSqlValue).attr('title', drSqlValue);
            } else if (drSqlValue?.includes("MB")) {
                $('#DR_Dbsize').text(drSqlValue).attr('title', drSqlValue);
            } else {
                $('#DR_Dbsize').text(drSqlValue + " MB").attr('title', drSqlValue + " MB");
            }
            // setPropData(data, [db2monitorProp, db2replicaMonitor]);
            const datalag = checkAndReplace(defaultSite?.PR_Datalag);
           
            let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

            var result = "";

            if (dataLagValue?.includes(".")) {
                
                var value = dataLagValue?.split(".");
                var hours = value[0] * 24;
                var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
                var min = minutes?.split(':');
                var firstValue = parseInt(min[0]) + parseInt(hours);
                result = firstValue + ":" + min[1];
                const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
                minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
            }
            else if (dataLagValue?.includes("+")) {
                
                const value = dataLagValue.split(" ");
                result = value[1]?.split(':')?.slice(0, 2)?.join(':');
                const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
                const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
                minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
            }
            else {
                
                result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
                const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
                const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
                minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
            }
        }
    }
    function setPropData(data, propSets, value) {
        propSets?.forEach(properties => {
            bindProperties(data, properties, value);
        });
    }

    function bindProperties(data, properties, value) {

       // let prStatus = value?.prServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.prServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.prServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";
       // let drStatus = value?.drServerStatus?.toLowerCase() === "up" ? 'cp-up-linearrow text-success' : value.drServerStatus.toLowerCase() === "down" ? "cp-down-linearrow text-danger" : value?.drServerStatus?.toLowerCase() === "pending" ? "cp-pending text-warning" : "cp-pending text-warning";

        const iconMapping = {
           // 'PR_Server_IpAddress': prStatus,
           // 'DR_Server_IpAddress': drStatus,
            'PRServerName': 'text-primary cp-server',
            'DR_ServerName': 'text-primary cp-server',
            'PR_Database': 'text-primary cp-database-unique-name',
            'DR_Database': 'text-primary cp-database-unique-name',
            'PROpreationMode': 'cp-cluster-database text-primary',
            'DR_OpreationMode': 'cp-cluster-database text-primary',
            'PRDBRole': 'text-primary cp-database-role',
            'DR_DBRole': 'text-primary cp-database-role',
            'PRMirroringState': 'text-success cp-refresh',
            'DR_MirroringState': 'text-success cp-refresh',
            'PRLogGenerateRate': 'text-primary cp-file-edits me-1 fs-6',
            'DR_LogGenerateRate': 'text-primary cp-file-edits me-1 fs-6',
            'PRUnsentLog': 'text-warning cp-control-file-type me-1 fs-6',
            'DR_UnsentLog': 'text-warning cp-control-file-type me-1 fs-6',
            'PRSentRate': 'text-primary cp-file-edits me-1 fs-6',
            'DR_SentRate': 'text-primary cp-file-edits me-1 fs-6',
            'PRUnrestoredLog': 'text-primary cp-datalog',
            'DR_UnrestoredLog': 'text-primary cp-datalog',
            'PRRecoveryRate': 'text-success cp-success-rate',
            'DR_RecoveryRate': 'text-success cp-success-rate',
            'PRTransactionDelay': 'text-primary cp-delay',
            'DR_TransactionDelay': 'text-primary cp-delay',
            'PRTransactionPerSecond': 'text-primary cp-timer-meter',
            'DR_TransactionPerSecond': 'text-primary cp-timer-meter',
            'DR_PR_Datalag': 'text-primary cp-time me-1 fs-6',
            //'DR_Datalag': 'text-success cp-data-lag me-1 fs-6',
            'PR_Server_NetworkAddress': 'text-primary cp-fal-server',
            'DR_Server_NetworkAddress': 'text-primary cp-fal-server'
        };
        $('#PR_Server_IpAddress').find('i').remove();
        $('#DR_Server_IpAddress').find('i').remove();

        const prIconHtml = value?.prServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
            value?.prServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
                value?.prServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
        $('#PR_Server_IpAddress').prepend(prIconHtml);

        const drIconHtml = value?.drServerStatus?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success me-1 fs-6"></i>' :
            value?.drServerStatus?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger me-1 fs-6"></i>' :
                value?.drServerStatus?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning me-1 fs-6"></i>' : '';
        $('#DR_Server_IpAddress').prepend(drIconHtml);
        properties?.forEach(property => {
            const value = data[property];
            let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
            let iconClass = iconMapping[property] || '';

            if (displayedValue !== 0) {
                // Add icons based on conditions
                if (displayedValue === 'NA') {
                    iconClass = 'text-danger cp-disable';
                }
                else if (displayedValue === 'Streaming') {
                    iconClass = 'text-success cp-refresh';
                }
                //else if (displayedValue?.includes('running')) {
                //    iconClass = 'text-primary cp-thunder';
                //}
                
                else if (displayedValue?.includes('running')) {
                    iconClass = 'text-primary cp-thunder';
                }
                else if (displayedValue?.includes('stopped') || displayedValue?.includes('stop')) {
                    iconClass = 'text-danger cp-Stopped';
                }
                else if (displayedValue?.includes('production')) {
                    iconClass = 'text-warning cp-log-archive-config';
                }
                else if (displayedValue?.includes('archive recovery')) {
                    iconClass = 'text-warning cp-log-archive-config';
                }
                else if (displayedValue === 'f' || displayedValue === 'false') {
                    iconClass = 'text-danger cp-error';
                }
                else if (displayedValue === 't' || displayedValue === 'true') {
                    iconClass = 'text-success cp-success';
                }
            }

            if (property === 'DR_UnsentLog') {
                console.log(property)
            }

            // Displayed value with icon
            const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
            const mergeValue = `${iconHtml}${displayedValue}`;
            $(`#${property}`).html(mergeValue).attr('title', displayedValue);
        });
    }
})