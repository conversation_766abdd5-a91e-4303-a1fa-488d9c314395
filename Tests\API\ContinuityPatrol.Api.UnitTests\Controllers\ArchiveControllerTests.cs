using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Delete;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Features.Archive.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Archive.Queries.GetList;
using ContinuityPatrol.Application.Features.Archive.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Archive.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Archive.Queries.GetTableNameUnique;
using ContinuityPatrol.Domain.ViewModels.ArchiveModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ArchiveControllerTests : IClassFixture<ArchiveFixture>
{
    private readonly ArchiveFixture _archiveFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ArchivesController _controller;

    public ArchiveControllerTests(ArchiveFixture archiveFixture)
    {
        _archiveFixture = archiveFixture;

        var testBuilder = new ControllerTestBuilder<ArchivesController>();
        _controller = testBuilder.CreateController(
            _ => new ArchivesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetArchives_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
      

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchiveListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_archiveFixture.ArchiveListVm);

        // Act
        var result = await _controller.GetArchives();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var archives = Assert.IsAssignableFrom<List<ArchiveListVm>>(okResult.Value);
        Assert.Equal(3, archives.Count);
    }

    [Fact]
    public async Task GetArchives_ReturnsEmptyList_WhenNoArchivesExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
   

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchiveListQuery>(), default))
            .ReturnsAsync(new List<ArchiveListVm>());

        // Act
        var result = await _controller.GetArchives();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var archives = Assert.IsAssignableFrom<List<ArchiveListVm>>(okResult.Value);
        Assert.Empty(archives);
    }

    [Fact]
    public async Task GetArchiveById_ReturnsArchive_WhenIdIsValid()
    {
        // Arrange
        var archiveId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetArchiveDetailQuery>(q => q.Id == archiveId), default))
            .ReturnsAsync(_archiveFixture.ArchiveDetailVm);

        // Act
        var result = await _controller.GetArchiveById(archiveId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var archive = Assert.IsType<ArchiveDetailVm>(okResult.Value);
        Assert.NotNull(archive);
    }

    [Fact]
    public async Task GetArchiveById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetArchiveById("invalid-guid"));
    }

    [Fact]
    public async Task CreateArchive_Returns201Created()
    {
        // Arrange
        var command = _archiveFixture.CreateArchiveCommand;
        var expectedMessage = $"Archive '{command.ArchiveProfileName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateArchiveResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateArchive(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateArchiveResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateArchive_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"Archive '{_archiveFixture.UpdateArchiveCommand.ArchiveProfileName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateArchiveCommand>(), default))
            .ReturnsAsync(new UpdateArchiveResponse
            {
                Message = expectedMessage,
                Id = _archiveFixture.UpdateArchiveCommand.Id
            });

        // Act
        var result = await _controller.UpdateArchive(_archiveFixture.UpdateArchiveCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateArchiveResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteArchive_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "Archive 'Test Archive' has been deleted successfully!.";
        var archiveId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteArchiveCommand>(c => c.Id == archiveId), default))
            .ReturnsAsync(new DeleteArchiveResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteArchive(archiveId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteArchiveResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedArchives_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetArchivePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _archiveFixture.ArchiveListVm;
        var expectedPaginatedResult = PaginatedResult<ArchiveListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchivePaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedArchives(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ArchiveListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ArchiveListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task IsArchiveNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchiveNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsArchiveNameExist("ExistingArchive", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsArchiveNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetArchiveNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsArchiveNameExist("NewArchive", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsArchiveTableNameExist_ReturnsTrue_WhenTableNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetTableNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsTableNameExist("ExistingTable", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsArchiveTableNameExist_ReturnsFalse_WhenTableNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetTableNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsTableNameExist("NewTable", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateArchive_ValidatesScheduleType()
    {
        // Arrange
        var command = new CreateArchiveCommand
        {
            ArchiveProfileName = "Test Archive",
            ScheduleType = -1, // Invalid schedule type
            Count = 1000
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Invalid schedule type"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateArchive(command));
    }

    [Fact]
    public async Task UpdateArchive_ValidatesArchiveExists()
    {
        // Arrange
        var command = new UpdateArchiveCommand
        {
            Id = Guid.NewGuid().ToString(),
            ArchiveProfileName = "Updated Archive",
            Count = 2000
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Archive not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateArchive(command));
    }

    [Fact]
    public async Task GetPaginatedArchives_HandlesFilteringByBackUpType()
    {
        // Arrange
        var query = new GetArchivePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var fullBackupArchives = new List<ArchiveListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ArchiveProfileName = "Full Backup Archive 1",
                BackUpType = "Full",
                Count = 10000,
                Type = "Database"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                ArchiveProfileName = "Full Backup Archive 2",
                BackUpType = "Full",
                Count = 15000,
                Type = "Log"
            }
        };

        var expectedPaginatedResult = PaginatedResult<ArchiveListVm>.Success(
            data: fullBackupArchives,
            count: fullBackupArchives.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchivePaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedArchives(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ArchiveListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ArchiveListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, archive => Assert.Equal("Full", archive.BackUpType));
    }

    [Fact]
    public async Task CreateArchive_HandlesComplexTableNameProperties()
    {
        // Arrange
        var command = new CreateArchiveCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            TableNameProperties = "{\"tables\":[\"Users\",\"Orders\",\"Products\",\"Inventory\"],\"relationships\":[\"FK_Orders_Users\",\"FK_OrderItems_Products\"],\"constraints\":[\"PK_Users\",\"UK_Users_Email\"]}",
            ArchiveProfileName = "Complex Enterprise Archive",
            Count = 500000,
            CronExpression = "0 0 1 * *",
            ScheduleTime = "01:00:00",
            ScheduleType = 1,
            BackUpType = "Full",
            Type = "Enterprise",
            ClearBackup = "Yes",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "Enterprise-Archive-Node"
        };

        var expectedMessage = $"Archive '{command.ArchiveProfileName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateArchiveResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateArchive(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateArchiveResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteArchive_VerifiesArchiveIsDeactivated()
    {
        // Arrange
        var archiveId = Guid.NewGuid().ToString();
        var expectedMessage = "Archive 'Test Archive' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteArchiveCommand>(c => c.Id == archiveId), default))
            .ReturnsAsync(new DeleteArchiveResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteArchive(archiveId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteArchiveResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetArchives_HandlesLargeDataSets()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        

        var largeDataSet = new List<ArchiveListVm>();
        for (int i = 0; i < 500; i++)
        {
            largeDataSet.Add(new ArchiveListVm
            {
                Id = Guid.NewGuid().ToString(),
                ArchiveProfileName = $"Archive Profile {i}",
                Count = i * 1000,
                BackUpType = i % 2 == 0 ? "Full" : "Incremental",
                Type = i % 3 == 0 ? "Database" : "Log"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetArchiveListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeDataSet);

        // Act
        var result = await _controller.GetArchives();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var archives = Assert.IsAssignableFrom<List<ArchiveListVm>>(okResult.Value);
        Assert.Equal(500, archives.Count);
    }
}
