﻿
// ontoggle button fuction

$(function () {    
    $("#chart,#hide").hide();
   
    $("#show").on('click', function () {
        let value = $(this).attr("state");
        if (value === "true") {
            // $('#LoadBalanceChart').empty();        
            $('#parentLoader').removeClass('d-none')
            initializeNetworkDiagram();
            $("#chart").show();
            $('#loadTitle').text('State Monitoring')
            $('#changeIcon').removeClass('cp-load-balancer').addClass('cp-Monitor');
            $("#table, #search-container, #create").hide();
            $(this).attr("title", "Load Balancer");
            $(this).attr("state", "false");
            $(".cp-circle-workflow").removeClass("cp-circle-workflow").addClass("cp-table");
        } else {
            $("#table, #search-container, #create").show();
            $('#loadTitle').text('Load Balancer')
            $('#changeIcon').removeClass('cp-Monitor').addClass('cp-load-balancer');
            $("#chart").hide();
            $(this).attr("title", "State Monitoring");
            $(this).attr("state", "true");
            $(".cp-table").removeClass("cp-table").addClass("cp-circle-workflow");
        }
    })

    //$("#hide").on('click',function () {
    //    $("#table, #search-container, #create").show();
    //    $('#loadTitle').text('Load Balancer')
    //    $('#changeIcon').removeClass('cp-Monitor')
    //    $('#changeIcon').addClass('cp-load-balancer')
    //    $("#chart").hide();
    //    $("#hide").hide();
    //    $("#show").show();
    //});
    //$("#show").on('click',function () { 
    //    // $('#LoadBalanceChart').empty();        
    //    $('#parentLoader').removeClass('d-none')
    //    initializeNetworkDiagram();
    //    $("#chart").show();
    //    $('#loadTitle').text('State Monitoring')
    //    $('#changeIcon').removeClass('cp-load-balancer')
    //    $('#changeIcon').addClass('cp-Monitor')
    //    $("#table, #search-container, #create").hide();
    //    $("#hide").show();
    //    $("#show").hide();
    //});
});


// data Map in the table

let counts = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20].map((count, index) => {
    return `
        <tr>
                                    <td>${count}</td>
                                    <td>Testing_PRSite</td>
                                    <td>122.22.332</td>
                                    <td>http</td>
                                    <td>MonitorService</td>
                                    <td>GoDaddy</td>
                                    <td>4000</td>
                                    <td>InActive</td>
                                    <td class="Action-th">
                                        <div class="d-flex align-items-center gap-2">

                                            <span role="button" title="Test Connection"><i class="cp-test-connection"></i></span>
                                            <span role="button" title="Edit"><i class="cp-edit"></i></span>
                                            <span  role="button" title="Delete" data-bs-toggle="modal"
                                                        data-bs-target="#DeleteModal"><i
                                                                class="cp-Delete"></i></span>

                                        </div>
                                    </td>
                                </tr>
        `
}).join("");


//document.getElementById("tableData").innerHTML = counts;


const loadStateMonitoring = async () => {
    let stateData = [];

    await $.ajax({
        type: "GET",
        url: RootUrl + "Admin/LoadBalancer/LoadStateMonitoring",
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result?.data) stateData = result.data
            } else {
                errorNotification(result)
            }
        }
    })

    return stateData;
}

async function initializeNetworkDiagram() {

    am4core.useTheme(am4themes_animated);

    // Create chart
    var chart = am4core.create(
        "LoadBalanceChart",
        am4plugins_forceDirected.ForceDirectedTree
    );
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Create series
    var series = chart.series.push(
        new am4plugins_forceDirected.ForceDirectedSeries()
    );
    chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];

    let data = await loadStateMonitoring();

    if (data && data.length) {

        let originalObject = data[0]?.properties && JSON.parse(data[0].properties);

        series.data = [{
            value: "40",
            image:
                "/img/charts_img/center_cp_logo.svg",
            color: "#789D4A",
            name: originalObject.UINodeName,
            fullname: originalObject.UINodeName,
            fixed: true,
            x: am4core.percent(50),
            y: am4core.percent(10),
            tag: originalObject?.UINode?.State && originalObject?.UINode?.State?.toLowerCase() == 'up' ? "✔" : "✖",
            ip: originalObject.UINode.IpAddress,
            Connection: originalObject.UINode.ConnectionType,
            //Service: originalObject.UINode.ServiceType,
            host: originalObject.UINode.HostName ?? 'NA',
            Port: String(originalObject.UINode.Port),
            value: 15,
            image:
                "/img/charts_img/ui.svg",

            children: [
                {
                    fixed: true,
                    x: am4core.percent(30),
                    y: am4core.percent(30),
                    name: originalObject.LoadBalancerName,
                    fullname: originalObject.LoadBalancerName,
                    tag: originalObject?.LoadBalancer?.State && originalObject?.LoadBalancer?.State?.toLowerCase() == 'up' ? "✔" : "✖",
                    ip: originalObject.LoadBalancer.IpAddress,
                    Connection: originalObject.LoadBalancer.ConnectionType,
                    Service: originalObject.LoadBalancer.ServiceType,
                    host: originalObject.LoadBalancer.HostName,
                    Port: String(originalObject.LoadBalancer.Port),
                    value: 10,
                    //down: originalObject?.UINode?.State && originalObject?.UINode?.State?.toLowerCase() == 'down' ? "red" : "",
                    image:
                        "/img/charts_img/Load_balancer.svg",
                    children: [],
                },
                {
                    fixed: true,
                    x: am4core.percent(70),
                    y: am4core.percent(30),
                    name: originalObject.DatabaseName,
                    fullname: originalObject.DatabaseName,
                    databasetype: originalObject?.Database?.DatabaseType,
                    tag: originalObject?.Database?.State && originalObject?.Database?.State?.toLowerCase() == 'up' ? "✔" : "✖",
                    ip: originalObject.Database.IpAddress,
                    //Connection: originalObject.Database.ConnectionType,
                    Service: originalObject.Database.ServiceType,
                    //host: originalObject.Database.HostName ?? 'NA',
                    Port: String(originalObject.Database.Port),
                    value: 15,
                    //down: originalObject?.UINode?.State && originalObject?.UINode?.State?.toLowerCase() == 'down' ? "red" : "",
                    image:
                        "/img/charts_img/database.svg",
                    link: [originalObject.LoadBalancerName],
                }
            ],

        },
        ];

        let parentX = originalObject.LoadBalancer.ParallelNodes.length == 1 ? 13 : 10;
        let parentY = 60;
        const parentXIncrement = 10;
        let childOffsetX = 5; // Offset for positioning children to the sides of the parent
        let childY = 80; // Initial Y position for children

        originalObject.LoadBalancer.ParallelNodes.forEach((data) => {
            let obj = {};

            obj["fixed"] = true;
            obj["x"] = am4core.percent(parentX);
            obj["y"] = am4core.percent(parentY);
            obj["name"] = data.NodeName;
            obj["fullname"] = data.NodeName;
            obj["tag"] = data?.Node?.State && data?.Node?.State?.toLowerCase() == 'up' ? "✔" : "✖";
            obj["ip"] = data.Node.IpAddress;
            obj["Connection"] = data.Node.ConnectionType;
            //obj["Service"] = data.Node.ServiceType;
            obj["host"] = data.Node.HostName ?? 'NA';
            //obj["Port"] = data.Node.Port;
            obj["value"] = 10;
            obj["image"] = "/img/charts_img/node.svg";
            //obj["down"] = originalObject?.LoadBalancer?.State && originalObject?.LoadBalancer?.State?.toLowerCase() == 'down' ? "red" : "";
            obj["children"] = [];

            const numChildren = data.Services.length;

            data.Services.forEach((service, index) => {
                let serviceObj = {};
                let truncatedName = service?.ServiceName?.split(' ');

                serviceObj["fixed"] = false;
                serviceObj["x"] = am4core.percent(parentX);
                serviceObj["y"] = am4core.percent(childY);
                serviceObj["name"] = truncatedName.length && truncatedName[0];
                serviceObj["fullname"] = service.ServiceName;
                //serviceObj["down"] = data?.Node?.State && data?.Node?.State?.toLowerCase() == 'down' ? "red" : "";
                serviceObj["tag"] = service?.ServiceNode?.State && service?.ServiceNode?.State?.toLowerCase() == 'up' ? "✔" : "✖";
                serviceObj["ip"] = service.ServiceNode.IpAddress;
                serviceObj["Connection"] = service.ServiceNode.ConnectionType;
                serviceObj["Service"] = service.ServiceNode.ServiceType;
                serviceObj["host"] = service.ServiceNode.HostName ?? 'NA';
                serviceObj["Port"] = String(service.ServiceNode.Port);
                serviceObj["value"] = 10;
                serviceObj["image"] = "/img/charts_img/Workflow_Service.svg";

                obj["children"].push(serviceObj);

                if (numChildren == 2) {

                    if (index === 0) {
                        serviceObj["x"] = am4core.percent(parentX - childOffsetX);
                    } else {
                        serviceObj["x"] = am4core.percent(parentX + childOffsetX);
                    }

                } else if (numChildren == 3) {
                    if (index === 0) {
                        serviceObj["x"] = am4core.percent(parentX - childOffsetX);
                    } else if (index === 1) {
                        serviceObj["x"] = am4core.percent((parentX - childOffsetX + parentX + childOffsetX) / 2);
                    } else {
                        serviceObj["x"] = am4core.percent(parentX + childOffsetX);
                    }
                }
            });

            series.data[0].children[0].children.push(obj);

            parentX += parentXIncrement;

            series.data[0].children[1].link.push(obj.fullname);
            //series.data[0].children[1].linkColor = series.data[0].children[1].down === "red" ? "red" : "black";
        });

        // Set up data fields
        series.dataFields.value = "value";
        series.dataFields.fixed = "fixed";
        series.dataFields.name = "name";
        series.dataFields.ip = "ip";
        series.dataFields.Connection = "Connection";
        (series.dataFields.Service = "Service"),
            (series.dataFields.host = "host"),
            (series.dataFields.Port = "Port"),
            (series.dataFields.id = "id");
        (series.dataFields.databasetype = "databasetype");
        series.dataFields.children = "children";
        //series.dataFields.down = "down";
        series.dataFields.tag = "tag";
        series.dataFields.linkWith = "link";
        series.dataFields.id = "fullname";
        series.manyBodyStrength = -18;
        // Add labels
        series.nodes.template.label.text = "{name}";
        series.nodes.template.label.valign = "bottom";

        series.nodes.template.label.fill = am4core.color("#000");
        series.nodes.template.label.dy = -20;
       // series.nodes.template.tooltipText =
         //   "[font-size: 15px; #0479ff; ]{fullname}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Service Type : [bold]{Service}[/]\n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
        series.fontSize = 11;
        series.minRadius = 35;
        series.maxRadius = 35;

        series.tooltip.autoTextColor = false;
        series.tooltip.getFillFromObject = false;
        series.tooltip.label.fill = am4core.color("#1A1A1A");
        series.tooltip.label.background.fill = am4core.color("#fff");

        series.links.template.strokeWidth = 1;

        series.links.template.strokeDasharray = "5,3";
        series.nodes.template.circle.strokeWidth = 0;
        series.nodes.template.circle.disabled = true;
        series.nodes.template.outerCircle.disabled = true;
        series.nodes.template.propertyFields.x = "x";
        series.nodes.template.propertyFields.y = "y";

        // Add tag
        var tag = series.nodes.template.createChild(am4core.Label);
        tag.text = "{tag}";
        tag.strokeWidth = 0;

        // tag = am4core.percent(50)
        tag.fill = am4core.color("#fff");
        tag.background = new am4core.RoundedRectangle();
        tag.background.cornerRadius(10, 10, 10, 10);
        tag.background.fill = am4core.color("#41c200");
        tag.padding(2, 5, 2, 5);
        tag.zIndex = 10;
        tag.width = '20px';
        tag.height = '20px';
        tag.verticalCenter = "top";
        //tag.textAlign = 'middle'
        tag.horizontalCenter = "left";

        series.nodes.template.adapter.add("tooltipText", function (text, target) {

            if (!target?.dataItem?.dataContext?.hasOwnProperty('Service') && !target?.dataItem?.dataContext?.hasOwnProperty('Port')) {
                return "[font-size: 15px; #0479ff; ]{fullname}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Host Name : [bold]{host}[/]\n";
            } else if (!target?.dataItem?.dataContext?.hasOwnProperty('Connection') && !target?.dataItem?.dataContext?.hasOwnProperty('host')) {
                return "[font-size: 15px; #0479ff; ]{fullname}\n[/] IP Address : [bold]{ip}[/]\n Database Type : [bold]{databasetype}[/]\n Service Type : [bold]{Service}[/]\n Port : [bold]{Port}[/]\n";
            } else if (!target?.dataItem?.dataContext?.hasOwnProperty('Service')) {
                return "[font-size: 15px; #0479ff; ]{fullname}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
            } else {
                return "[font-size: 15px; #0479ff; ]{fullname}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Service Type : [bold]{Service}[/]\n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
            }

        });
        tag.adapter.add("dy", function (dy, target) {
            return -target.parent.circle.radius + 40;
        });
        tag.adapter.add("dx", function (dy, target) {
            return target.parent.circle.radius - 15;
        });
        tag.adapter.add("textOutput", function (text, target) {
            if (text === "") {
                target.disabled = true;
            }
            return text;
        });

        //series.links.template.adapter.add("stroke", function (stroke, target) {
        //    // Check if the target dataItem has a color property
        //    if (series.dataFields.hasOwnProperty('down')) {
        //        if (target.dataItem.down) return am4core.color(target.dataItem.down)
        //        else return stroke;
        //    } else {
        //        return stroke; // Otherwise, return default stroke color
        //    }
        //});
        tag.adapter.add("fill", function (fill, target) {
            if (target.dataItem && target.dataItem.tag == "✔") {
                return am4core.color("#fff");
            } else {
                return fill;
            }
        });

        tag.background.adapter.add("fill", function (fill, target) {
            if (target.dataItem && target.dataItem.tag == "✖") {
                return am4core.color("red");
            } else {
                return fill;
            }
        });

        // Configure icons
        var icon = series.nodes.template.createChild(am4core.Image);
        icon.propertyFields.href = "image";
        icon.horizontalCenter = "middle";
        icon.verticalCenter = "middle";
        icon.width = 60;
        icon.height = 60;
        series.centerStrength = 0.5;

        $('#parentLoader').addClass('d-none')
    }
}

//initializeNetworkDiagram();



// ontoggle button fuction

//$(document).ready(function () {
//    $("#hide").hide();
//    $("#chart").hide();
//    $("#hide").click(function () {
//        $("#table").show();
//        $('#loadTitle').text('Load Balancer')
//        $("#chart").hide();
//        $("#hide").hide();
//        $("#show").show();
//    });
//    $("#show").click(function () {
//        $("#chart").show();
//        $('#loadTitle').text('State Monitoring')
//        $("#table").hide();
//        $("#hide").show();
//        $("#show").hide();
//    });
//});


//// data Map in the table

//let counts = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20].map((count, index) => {
//    return `
//        <tr>
//                                    <td>${count}</td>
//                                    <td>Testing_PRSite</td>
//                                    <td>122.22.332</td>
//                                    <td>http</td>
//                                    <td>MonitorService</td>
//                                    <td>GoDaddy</td>
//                                    <td>4000</td>
//                                    <td>InActive</td>
//                                    <td class="Action-th">
//                                        <div class="d-flex align-items-center gap-2">

//                                            <span role="button" title="Test Connection"><i class="cp-test-connection"></i></span>
//                                            <span role="button" title="Edit"><i class="cp-edit"></i></span>
//                                            <span  role="button" title="Delete" data-bs-toggle="modal"
//                                                        data-bs-target="#DeleteModal"><i
//                                                                class="cp-Delete"></i></span>

//                                        </div>
//                                    </td>
//                                </tr>
//        `
//}).join("");


////document.getElementById("tableData").innerHTML = counts;

//am4core.useTheme(am4themes_animated);

//// Create chart
//var chart = am4core.create(
//    "LoadBalanceChart",
//    am4plugins_forceDirected.ForceDirectedTree
//);
//if (chart.logo) {
//    chart.logo.disabled = true;
//}
//// Create series
//var series = chart.series.push(
//    new am4plugins_forceDirected.ForceDirectedSeries()
//);
//chart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];
//series.data = [{
//    value: "40",
//    image:
//        "/img/charts_img/center_cp_logo.svg",
//    color: "#789D4A",
//    name: "UI",
//    fixed: true,
//    x: am4core.percent(50),
//    y: am4core.percent(10),
//    tag: "✖",
//    ip: "***********",
//    Connection: "HTTP",
//    Service: "Monitor Service",
//    host: "CD030",
//    Port: "6002",
//    value: 15,
//    image:
//        "/img/charts_img/ui.svg",

//    children: [
//        {
//            fixed: true,
//            x: am4core.percent(30),
//            y: am4core.percent(30),
//            name: "Load Balancer",
//            tag: "✔",
//            ip: "***********",
//            Connection: "HTTP",
//            Service: "Monitor Service",
//            host: "CD030",
//            Port: "6002",
//            value: 10,
//            image:
//                "/img/charts_img/Load_balancer.svg",
//            children: [
//                {
//                    fixed: true,
//                    x: am4core.percent(30),
//                    y: am4core.percent(60),
//                    name: "node1",
//                    tag: "✖",
//                    ip: "***********",
//                    Connection: "HTTP",
//                    Service: "Monitor Service",
//                    host: "CD030",
//                    Port: "6002",
//                    value: 10,
//                    image:
//                        "/img/charts_img/node.svg",
//                    children: [
//                        {
//                            name: "n1_Workflow windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/Workflow_Service.svg",
//                        },
//                        {
//                            name: "n1_DR Ready windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/DR_ready_Service.svg",
//                        },
//                        {
//                            name: "n1_Monitoring windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/Monitor_service.svg",
//                        },
//                    ],
//                },
//                {
//                    fixed: true,
//                    x: am4core.percent(70),
//                    y: am4core.percent(60),
//                    name: "node2",
//                    tag: "✔",
//                    ip: "***********",
//                    Connection: "HTTP",
//                    Service: "Monitor Service",
//                    host: "CD030",
//                    Port: "6002",
//                    value: 10,
//                    image:
//                        "/img/charts_img/node.svg",
//                    children: [
//                        {
//                            name: "n2_Workflow windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/Workflow_Service.svg",
//                        },
//                        {
//                            name: "n2_DR Ready windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/DR_ready_Service.svg",
//                        },
//                        {
//                            name: "n2_Monitoring windowservice",
//                            tag: "✔",
//                            ip: "***********",
//                            Connection: "HTTP",
//                            Service: "Monitor Service",
//                            host: "CD030",
//                            Port: "6002",
//                            value: 10,
//                            image:
//                                "/img/charts_img/Monitor_service.svg",
//                        },
//                    ],
//                },
//            ],
//        },
//        {
//            fixed: true,
//            x: am4core.percent(70),
//            y: am4core.percent(30),
//            name: "Database",
//            tag: "✖",
//            ip: "***********",
//            Connection: "HTTP",
//            Service: "Monitor Service",
//            host: "CD030",
//            Port: "6002",
//            value: 15,
//            image:
//                "/img/charts_img/database.svg",
//            link: ["node1", "node2", "Load Balancer", "UI"],
//        }
//    ],

//},
//];

//// Set up data fields
//series.dataFields.value = "value";
//series.dataFields.fixed = "fixed";
//series.dataFields.name = "name";
//series.dataFields.ip = "ip";
//series.dataFields.Connection = "Connection";
//(series.dataFields.Service = "Service"),
//    (series.dataFields.host = "host"),
//    (series.dataFields.Port = "Port"),
//    (series.dataFields.id = "id");
//series.dataFields.children = "children";
//series.dataFields.tag = "tag";
//series.dataFields.linkWith = "link";
//series.dataFields.id = "name";
//series.manyBodyStrength = -18;
//// Add labels
//series.nodes.template.label.text = "{name}";
//series.nodes.template.label.valign = "bottom";

//series.nodes.template.label.fill = am4core.color("#000");
//series.nodes.template.label.dy = -20;
//series.nodes.template.tooltipText =
//    "[font-size: 15px; #0479ff; ]{name}\n[/] IP Address : [bold]{ip} [/]\n Connection Type : [bold]{Connection} [/] \n Service Type : [bold]{Service}[/]\n Host Name : [bold]{host}[/]\n Port : [bold]{Port}[/]\n";
//series.fontSize = 11;
//series.minRadius = 35;
//series.maxRadius = 35;

//series.tooltip.autoTextColor = false;
//series.tooltip.getFillFromObject = false;
//series.tooltip.label.fill = am4core.color("#1A1A1A");
//series.tooltip.label.background.fill = am4core.color("#fff");

//series.links.template.strokeWidth = 1;
//series.links.template.strokeDasharray = "5,3";
//series.nodes.template.circle.strokeWidth = 0;
//series.nodes.template.circle.disabled = true;
//series.nodes.template.outerCircle.disabled = true;
//series.nodes.template.propertyFields.x = "x";
//series.nodes.template.propertyFields.y = "y";
//// Add tag
//var tag = series.nodes.template.createChild(am4core.Label);
//tag.text = "{tag}";
//tag.strokeWidth = 0;
//// tag = am4core.percent(50)
//tag.fill = am4core.color("#fff");
//tag.background = new am4core.RoundedRectangle();
//tag.background.cornerRadius(10, 10, 10, 10);
//tag.background.fill = am4core.color("#41c200");
//tag.padding(2, 5, 2, 5);
//tag.zIndex = 10;
//tag.width = '20px';
//tag.height = '20px';
//tag.verticalCenter = "top";
////tag.textAlign = 'middle'
//tag.horizontalCenter = "left";
//tag.adapter.add("dy", function (dy, target) {
//    return -target.parent.circle.radius + 40;
//});
//tag.adapter.add("dx", function (dy, target) {
//    return target.parent.circle.radius - 15;
//});
//tag.adapter.add("textOutput", function (text, target) {
//    if (text === "") {
//        target.disabled = true;
//    }
//    return text;
//});

//tag.adapter.add("fill", function (fill, target) {
//    if (target.dataItem && target.dataItem.tag == "✔") {
//        return am4core.color("#fff");
//    } else {
//        return fill;
//    }
//});
//tag.background.adapter.add("fill", function (fill, target) {
//    if (target.dataItem && target.dataItem.tag == "✖") {
//        return am4core.color("red");
//    } else {
//        return fill;
//    }
//});

//// Configure icons
//var icon = series.nodes.template.createChild(am4core.Image);
//icon.propertyFields.href = "image";
//icon.horizontalCenter = "middle";
//icon.verticalCenter = "middle";
//icon.width = 60;
//icon.height = 60;
//series.centerStrength = 0.5;
