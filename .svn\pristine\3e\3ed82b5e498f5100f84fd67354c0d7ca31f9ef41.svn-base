﻿using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

[SupportedOSPlatform("windows")]
public partial class ExecutedDRDrillReport : DevExpress.XtraReports.UI.XtraReport
{
    private readonly ILogger<PreBuildReportController> _logger;
    public DrDrillReport drDrillReport = new DrDrillReport();
    public List<WorkflowOperationDrDrillReportVm> DrDrillReports = new List<WorkflowOperationDrDrillReportVm>();
    public List<WorkflowActionResultDrDrillReportVm> DrDrillReportresults = new List<WorkflowActionResultDrDrillReportVm>();
    public List<WorkflowOperationGroupDrDrillReportVm> Groupreport = new List<WorkflowOperationGroupDrDrillReportVm>();
    public List<List<WorkflowActionResultDrDrillReportVm>> WorkflowActionLists = new List<List<WorkflowActionResultDrDrillReportVm>>();
    public int WorkflowActionRowCounts;
    public int SerialNumberWorkflowActions = 0;

    public ExecutedDRDrillReport(string data)
    {
        try
        {
            _logger = PreBuildReportController._logger;
            WorkflowActionRowCounts = 0;
            WorkflowActionLists.Clear();
            DrDrillReportresults.Clear();
            DrDrillReports.Clear();
            InitializeComponent();
            ClientCompanyLogo();
            drDrillReport = JsonConvert.DeserializeObject<DrDrillReport>(data);
            DrDrillReports.Add(drDrillReport.WorkflowOperationDrDrillReportVm);
            xrTableCell4.BeforePrint += tableCell_SerialNumber_BeforePrint;
            xrTableCell18.BeforePrint += tableCell_SerialNumber_BeforePrintDR;
            xrPictureBox9.BeforePrint += xrPictureBox9_BeforePrint;

            foreach (var grpReport in DrDrillReports)
            {
                Groupreport = grpReport.WorkflowOperationGroupDrDrillDetailVms;
            }

            foreach (var resultReport in Groupreport)
            {
                WorkflowActionLists.Add(resultReport.WorkflowActionResultDrDrillReportVms);
                foreach (var item in resultReport.WorkflowActionResultDrDrillReportVms)
                {
                    DrDrillReportresults.Add(item);
                }
            }
            xrLabel26.Text = string.IsNullOrEmpty(drDrillReport.WorkflowOperationDrDrillReportVm.Description.ToString()) ? "NA" : drDrillReport.WorkflowOperationDrDrillReportVm.Description.ToString();

            //Data Source Assigned
            this.DataSource = DrDrillReports;
            this.DetailReport.DataSource = Groupreport;
            this.DetailReport3.DataSource = DrDrillReportresults;
            this.DetailReport2.DataSource = Groupreport;

            if (Groupreport.Count == 0)
            {
                DetailReport.Visible = false;
            }
            else
            {
                DetailReport.Visible = true;
            }

            if (DrDrillReportresults.Count == 0)
            {
                DetailReport3.Visible = false;
                xrPictureBox13.Visible = false;
                xrLabel9.Visible = false;
                xrLabel55.Visible = false;
            }
            else
            {
                DetailReport3.Visible = true;
                xrPictureBox13.Visible = true;
                xrLabel9.Visible = true;
                xrLabel55.Visible = true;
            }

            TimeSpan SavedRTO = new TimeSpan();
            TimeSpan ActualRTO = new TimeSpan();
            TimeSpan ConfigRTO = new TimeSpan();
            foreach (var GrReport in DrDrillReports)
            {
                ConfigRTO = TimeSpan.Parse(GrReport.ConfiguredRTO);
                ActualRTO = TimeSpan.Parse(GrReport.ActualRTO.ToString());
                if (ConfigRTO > ActualRTO)
                {
                    SavedRTO = ConfigRTO - ActualRTO;
                }
                else
                {
                    SavedRTO = new TimeSpan();
                }

                DateTime starttime = GrReport.StartTime.ToDateTime();
                DateTime endtime = GrReport.EndTime.ToDateTime();

                // Format the time in 12-hour format
                xrLabel123.Text = string.IsNullOrEmpty(GrReport.StartTime) ? "NA" : GrReport.StartTime;
                xrLabel125.Text = string.IsNullOrEmpty(GrReport.EndTime) ? "NA" : GrReport.EndTime;
                TimeSpan timeDifference = endtime.Subtract(starttime);
                xrLabel32.Text = Convert.ToString(timeDifference);

                xrLabel65.Text = string.IsNullOrEmpty(GrReport.TotalWorkflowExecutedCount.ToString()) ? "NA" : GrReport.TotalWorkflowExecutedCount.ToString();
                xrLabel3.Text = string.IsNullOrEmpty(GrReport.TotalActionExecutedCount.ToString()) ? "NA" : GrReport.TotalActionExecutedCount.ToString();
                xrLabel63.Text = string.IsNullOrEmpty(GrReport.TotalActionExecutedCount.ToString()) ? "NA" : GrReport.TotalActionExecutedCount.ToString();
                xrLabel40.Text = string.IsNullOrEmpty(GrReport.ActualRTOStartTime) ? "NA" : GrReport.ActualRTOStartTime;
                xrLabel59.Text = string.IsNullOrEmpty(GrReport.ActualRTOEndTime) ? "NA" : GrReport.ActualRTOEndTime;
                xrLabel131.Text = string.IsNullOrEmpty(GrReport.RunMode) ? "NA" : GrReport.RunMode.ToTitleCase();
                this.DisplayName = GrReport.ProfileName + "_DRDrillSummaryReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                var configuredRTO = (int)ConfigRTO.TotalMinutes.ToInt64();
                xrLabel109.Text = string.IsNullOrEmpty(configuredRTO.ToString()) ? "NA" : configuredRTO.ToString();
            }

            foreach (var output in Groupreport)
            {
                //Workflow Execution Details
                try
                {
                    string test = "";
                    var testing = output.ConfiguredRTOLessActualRTO.ToString();
                    if (testing.StartsWith('-'))
                    {
                        test = testing.TrimStart('-');
                        output.ConfiguredRTOLessActualRTO = TimeSpan.Parse(test);
                    }
                    else
                    {
                        test = testing.ToString();
                        output.ConfiguredRTOLessActualRTO = TimeSpan.Parse(test);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"An error occurred while processing the request. {ex.GetMessage()}");
                }

                string pair = "";
                var trim = output.ConfiguredRTOLessActualRTO.ToString();
                if (trim.StartsWith('-'))
                {
                    pair = trim.TrimStart('-');
                }
                else
                {
                    pair = trim.ToString();
                }
                string[] panel = pair.Split(':');
                string value = panel[0] + "h " + panel[1] + "m " + panel[2] + "s";
                var formattedTime = value.ToString();

                string Times = ConfigRTO.ToString();
                string[] panels = Times.Split(':');
                string values = panels[0] + "h " + panels[1] + "m " + panels[2] + "s";
                var Time = values.ToString();

                string Mute = ActualRTO.ToString();
                TimeSpan formattedDateTime = TimeSpan.Parse(pair);
                TimeSpan timeDateTime = TimeSpan.Parse(Times);
                TimeSpan date = TimeSpan.Parse(Mute);

                if (timeDateTime > date)
                {
                    this.xrLabel105.ForeColor = System.Drawing.Color.FromArgb(106, 192, 86);
                    this.xrPictureBox73.Visible = true;
                    this.xrLabel104.Text = "Current RTO is Less Than Configured RTO";
                    this.xrPictureBox72.Visible = false;
                }
                else
                {
                    this.xrLabel105.ForeColor = System.Drawing.Color.FromArgb(255, 0, 0);
                    this.xrPictureBox73.Visible = false;
                    this.xrLabel104.Text = "Current RTO is More Than Configured RTO";
                    this.xrPictureBox72.Visible = true;
                }
            }

            //Hours, Minutes and Seconds Unit...
            foreach (var rto in DrDrillReports)
            {
                var trimFirst = rto.ConfiguredRTO.ToString();
                string[] Assign1 = trimFirst.Split(':');
                xrLabel79.Text = Assign1[0];
                xrLabel92.Text = Assign1[1];
                xrLabel72.Text = Assign1[2];

                var trimSecond = rto.ActualRTO.ToString();
                string[] Assign2 = trimSecond.Split(':');
                xrLabel81.Text = Assign2[0];
                xrLabel98.Text = Assign2[1];
                xrLabel73.Text = Assign2[2];

                var trimThird = SavedRTO.ToString();
                string[] Assign3 = trimThird.Split(':');
                xrLabel83.Text = Assign3[0];
                xrLabel95.Text = Assign3[1];
                xrLabel74.Text = Assign3[2];
            }

            //Configured RTO Less Actual RTO (color code changes and Up,Down icon)
            foreach (var output in DrDrillReports)
            {
                //ConfiguredRTO Less ActualRTO Trim minus symbol
                string pair = "";
                var trim = output.ConfiguredRTOLessActualRTO.ToString();
                if (trim.StartsWith('-'))
                {
                    pair = trim.TrimStart('-');
                }
                else
                {
                    pair = trim.ToString();
                }
                string[] panel = pair.Split(':');
                string value = panel[0] + "h " + panel[1] + "m " + panel[2] + "s";
                var formattedTime = value.ToString();
                xrLabel61.Text = string.IsNullOrEmpty(formattedTime) ? "NA" : formattedTime;

                string Times = ConfigRTO.ToString();
                string[] panels = Times.Split(':');
                string values = panels[0] + "h " + panels[1] + "m " + panels[2] + "s";
                var Time = values.ToString();
                xrLabel118.Text = string.IsNullOrEmpty(Time) ? "NA" : Time;

                string Mute = ActualRTO.ToString();
                TimeSpan formattedDateTime = TimeSpan.Parse(pair);
                TimeSpan timeDateTime = TimeSpan.Parse(Times);
                TimeSpan date = TimeSpan.Parse(Mute);

                if (timeDateTime > date)
                {
                    this.xrLabel61.ForeColor = System.Drawing.Color.FromArgb(106, 192, 86);
                    this.xrPictureBox67.Visible = true;
                    this.xrLabel60.Text = "Current RTO isLess Than Configured RTO";
                    this.xrPictureBox63.Visible = false;
                }
                else
                {
                    this.xrLabel61.ForeColor = System.Drawing.Color.FromArgb(255, 0, 0);
                    this.xrPictureBox67.Visible = false;
                    this.xrLabel60.Text = "Current RTO isMore Than Configured RTO";
                    this.xrPictureBox63.Visible = true;
                }
            }

            // DRDrillReportResult Success & Skip Counts
            Int64 succeedaction = 0;
            Int64 skippedaction = 0;
            Int64 bypassedAction = 0;
            Int64 abortedAction = 0;

            foreach (var ActStatus in DrDrillReportresults)
            {
                if (ActStatus.Status.Contains("Success"))
                {
                    succeedaction = succeedaction + 1;

                }
                else if (ActStatus.Status.Contains("Skip"))
                {
                    skippedaction = skippedaction + 1;
                }
                else if (ActStatus.Status.Contains("Bypass"))
                {
                    bypassedAction = bypassedAction + 1;
                }
                else
                {
                    abortedAction = abortedAction + 1;
                }
            }
            xrLabel113.Text = string.IsNullOrEmpty(skippedaction.ToString()) ? "NA" : skippedaction.ToString();
            xrLabel112.Text = string.IsNullOrEmpty(succeedaction.ToString()) ? "NA" : succeedaction.ToString();
            xrLabel133.Text = string.IsNullOrEmpty(bypassedAction.ToString()) ? "NA" : bypassedAction.ToString();
            xrLabel66.Text = string.IsNullOrEmpty(abortedAction.ToString()) ? "NA" : abortedAction.ToString();

            if (Groupreport.Count > 0 && DrDrillReportresults.Count > 1)
            {
                foreach (var item in Groupreport)
                {
                    item.WorkflowActionResultDrDrillReportVms.First().StartRto = 1;
                    item.WorkflowActionResultDrDrillReportVms.Last().StartRto = 2;
                }
            }
            else
            {
                this.xrTableCell3.Visible = false;
                this.xrTableCell5.Visible = false;
            }
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DRDrill Report. The error message : " + ex.Message); throw; }
    }
    private void xrTableCell2_BeforePrint(object sender, System.EventArgs e)
    {

    }
    private void xrPictureBox9_BeforePrint(object sender, System.EventArgs e)
    {
        WorkflowActionRowCounts += 1;
        if (WorkflowActionLists.Count > 0)
        {
            this.DetailReport3.DataSource = WorkflowActionLists[WorkflowActionRowCounts - 1];
        }
        SerialNumberWorkflowActions = 1;
    }

    private int serialNumber = 1;

    private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = serialNumber.ToString();
        serialNumber++;
    }
    private void tableCell_SerialNumber_BeforePrintDR(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;
        cell.Text = SerialNumberWorkflowActions.ToString();
        SerialNumberWorkflowActions++;
    }

    private void xrChart4_BeforePrint(object sender, System.EventArgs e)
    {
        try
        {
            List<WorkflowOperationGroupDrDrillReportVm> Groupreport = new List<WorkflowOperationGroupDrDrillReportVm>();
            List<WorkflowActionResultDrDrillReportVm> ActionResult = new List<WorkflowActionResultDrDrillReportVm>();
            Int64 succeedaction = 0;
            Int64 skippedaction = 0;
            Int64 abortedAction = 0;
            Int64 bypassedAction = 0;

            foreach (var GrReport in DrDrillReports)
            {
                Groupreport = GrReport.WorkflowOperationGroupDrDrillDetailVms;

            }
            foreach (var ActResult in Groupreport)
            {
                ActionResult.AddRange(ActResult.WorkflowActionResultDrDrillReportVms);
            }
            foreach (var ActStatus in ActionResult)
            {
                if (ActStatus.Status == "Success")
                {
                    succeedaction = succeedaction + 1;
                }
                else if (ActStatus.Status == "Skipped" || ActStatus.Status == "Skip")
                {
                    skippedaction = skippedaction + 1;
                }
                else if (ActStatus.Status.ToLower() == "bypassed")
                {
                    bypassedAction = bypassedAction + 1;
                }
                else
                {
                    abortedAction = abortedAction + 1;
                }
            }
            xrLabel66.Text = string.IsNullOrEmpty(abortedAction.ToString()) ? "NA" : abortedAction.ToString();

            // Customize the area view appearance.  

            Series series = new Series("Series1", ViewType.Bar);
            xrChart4.Series.Add(series);
            xrChart4.WidthF = 225f;
            xrChart4.HeightF = 200f;
            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateChartData(succeedaction, skippedaction, abortedAction, bypassedAction);
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "Value" });
            series.View.Colorizer = new ColorObjectColorizer();
            BarSeriesView view = (BarSeriesView)series.View;
            view.BarWidth = 0.4;
            view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view.Pane.BorderVisible = false;
            xrChart4.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            // X Axis Font Size
            AxisBase axisX = ((XYDiagram)xrChart4.Diagram).AxisX;
            axisX.Label.Font = new Font(axisX.Label.Font.FontFamily, 6f);
            //((XYDiagram)xrChart4.Diagram).AxisY.GridLines.Visible = false;
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DR Drill Report's Barchart. The error message : " + ex.Message); throw; }
    }
    private void xrChart3_BeforePrint(object sender, System.EventArgs e)
    {
        try
        {
            Int64 SavedRTO = 0;
            Int64 ActualRTO = 0;
            Int64 ConfigRTO = 0;
            Int64 NodataFound = 0;
            foreach (var GrReport in DrDrillReports)
            {
                ActualRTO = GrReport.ActualRTO.TotalMinutes.ToInt64();
                string configuredRTOString = GrReport.ConfiguredRTO;
                TimeSpan output = TimeSpan.Parse(configuredRTOString);
                ConfigRTO = (int)output.TotalMinutes.ToInt64();

                if (ConfigRTO > ActualRTO)
                {
                    SavedRTO = ConfigRTO - ActualRTO;
                }
                else
                {
                    SavedRTO = 0;
                }
            }
            if (SavedRTO == 0 && ActualRTO == 0)
            {
                Series series1 = new Series("Series1", ViewType.Doughnut);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series1.View = doughnutSeriesView;
                xrChart3.Series.Add(series1);
                NodataFound = 1;
                series1.DataSource = CreateChartDataDr(ActualRTO, SavedRTO, NodataFound);
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.Label.TextPattern = "{A}";
                series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart3.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            else
            {
                Series series = new Series("Series1", ViewType.Doughnut);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series.View = doughnutSeriesView;
                xrChart3.Series.Add(series);
                series.DataSource = CreateChartDataDr(ActualRTO, SavedRTO, NodataFound);
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{A}\n{V} Mins";
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart3.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DR Drill Report's Doughnut Chart. The error message : " + ex.Message); throw; }
    }
    private DataTable CreateChartDataDr(Int64 ActualRTO, Int64 SavedRTO, Int64 NodataFound)
    {
        DataTable table = new DataTable("Table1");

        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Value", typeof(Int64));

        Random rnd = new Random();

        table.Rows.Add("Current", ActualRTO);
        table.Rows.Add("Saved", SavedRTO);
        table.Rows.Add("No Data Found", NodataFound);


        return table;
    }
    private DataTable CreateChartData(Int64 Success, Int64 Skip, Int64 Abort, Int64 byPass)
    {
        DataTable table = new DataTable("Table1");

        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Value", typeof(Int64));

        Random rnd = new Random();

        table.Rows.Add("Success", Success);
        table.Rows.Add("Skipped", Skip);
        table.Rows.Add("Aborted", Abort);
        table.Rows.Add("ByPassed", byPass);

        return table;
    }
    private void _userName_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            xrLabel16.Text = "Report Generated By: " + drDrillReport.ReportGeneratedBy.ToString();
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DR Drill Report's User name. The error message : " + ex.Message); throw; }
    }

    private static string username_session(LoginViewModel model)
    {
        return model.LoginName;
    }
    private void _version_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DR Drill Report's CP Version. The error message : " + ex.Message); throw; }
    }
    public void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(ServiceAvailabilityController.CompanyLogo) ? "NA" : ServiceAvailabilityController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    Image image = Image.FromStream(ms);
                    prClientLogo.Image = image;
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex) { _logger.LogError("Error occured while display the Executed DR Drill Report's customer logo. The error message : " + ex.Message); throw; }
    }
}
