﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LoadBalancerControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILoadBalancerService> _mockLoadBalancerService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILogger<LoadBalancerController>> _mockLogger = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private  LoadBalancerController _controller;

        public LoadBalancerControllerShould()
        {

            Initialize();

        }
        internal void Initialize()
        {
            _controller = new LoadBalancerController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockDataProvider.Object,
                _mockLogger.Object,
                _mockLoggedInUserService.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewWithNodeConfigurationViewModel()
        {
            // Arrange
            var loadBalancerList = new List<LoadBalancerListVm>();

            _mockLoadBalancerService.Setup(s => s.GetLoadBalancerList()).ReturnsAsync(loadBalancerList);

            // Act
            var result = await _controller.List() as ViewResult;
            var model = result?.Model as LoadBalancerListVm;

            // Assert
            Assert.NotNull(result);
            
           
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesNodeConfigurationSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create <LoadBalancerViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateLoadBalancerCommand ();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateLoadBalancerCommand>(model)).Returns(createCommand);
            _mockDataProvider.Setup(p => p.LoadBalancer.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesNodeConfigurationSuccessfully()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<LoadBalancerViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateLoadBalancerCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateLoadBalancerCommand>(model)).Returns(updateCommand);
            _mockDataProvider.Setup(p => p.LoadBalancer.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<LoadBalancerViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createdCommmand = new CreateLoadBalancerCommand();
            _mockMapper.Setup(m => m.Map<CreateLoadBalancerCommand>(model)).Returns(createdCommmand);
            _mockDataProvider.Setup(m=>m.LoadBalancer.CreateAsync(createdCommmand)).ReturnsAsync(new BaseResponse {Success=true,Message="Created Success"});

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
            
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            // Arrange
            var model = new AutoFixture.Fixture().Create<LoadBalancerViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<CreateLoadBalancerCommand>(model)).Returns(new CreateLoadBalancerCommand());
            _mockDataProvider.Setup(p => p.LoadBalancer.CreateAsync(It.IsAny<CreateLoadBalancerCommand>()))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(model) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToPostView()
        {
            // Arrange
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(p => p.LoadBalancer.DeleteAsync("some-id")).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete("some-id") as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResultWithPaginatedList()
        {
            // Arrange
            var query = new AutoFixture.Fixture().Create<GetLoadBalancerPaginatedListQuery>();
            _mockDataProvider.Setup(m=>m.LoadBalancer.GetPaginatedNodeConfigurations(query)).ReturnsAsync(It.IsAny<PaginatedResult<LoadBalancerListVm>>);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;
            var data = result?.Value as List<LoadBalancerViewModel>;

            // Assert
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task IsLoadBalancerNameExist_ReturnsBoolean()
        {
            // Arrange
            _mockDataProvider.Setup(p => p.LoadBalancer.IsLoadBalancerNameExist("some-name", "some-id")).ReturnsAsync(true);

            // Act
            var result = await _controller.IsLoadBalancerNameExist("some-name", "some-id");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task LoadStateMonitoring_ReturnsJsonResultWithStateDetails()
        {
            // Arrange
            var stateDetails = new List<StateMonitorStatusListVm> ();
            _mockDataProvider.Setup(p => p.LoadBalancer.GetStateMonitorStatusList()).ReturnsAsync(stateDetails);

            // Act
            var result = await _controller.LoadStateMonitoring() as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);

            // Assert
            
           
        }

        [Fact]
        public async Task TestConfiguration_ReturnsJsonResultWithTestConnectionResult()
        {
            // Arrange
            var testResult = new BaseResponse ();
            _mockDataProvider.Setup(p => p.LoadBalancer.TestConnection(It.IsAny<LoadBalancerTestConnectionCommand>())).ReturnsAsync(testResult);

            // Act
            var result = await _controller.TestConfiguration("some-id") as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
            Assert.NotNull(result);
            
        }
    }
}
