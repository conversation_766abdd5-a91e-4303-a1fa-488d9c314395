﻿using ContinuityPatrol.Application.Features.WorkflowAction.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;

public class
    CreateWorkflowActionCommandHandler : IRequestHandler<CreateWorkflowActionCommand, CreateWorkflowActionResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISolutionHistoryRepository _solutionRepository;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public CreateWorkflowActionCommandHandler(IMapper mapper, IWorkflowActionRepository workflowActionRepository,
        IPublisher publisher, ILoggedInUserService loggedInUserService, ISolutionHistoryRepository solutionRepository,
        IVersionManager versionManager)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
        _solutionRepository = solutionRepository;
        _versionManager = versionManager;
    }

    public async Task<CreateWorkflowActionResponse> Handle(CreateWorkflowActionCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var workflowAction = _mapper.Map<Domain.Entities.WorkflowAction>(request);

        var version = await _versionManager.GetVersion(request.Version);

        workflowAction.Version = version;

        workflowAction = await _workflowActionRepository.AddAsync(workflowAction);

        var solutionHistory = new Domain.Entities.SolutionHistory
        {
            LoginName = _loggedInUserService.LoginName,
            CompanyId = _loggedInUserService.CompanyId,
            NodeId = workflowAction.NodeId,
            ActionId = workflowAction.ReferenceId,
            ActionName = workflowAction.ActionName,
            Version = workflowAction.Version,
            Properties = workflowAction.Properties,
            UpdaterId = _loggedInUserService.UserId
        };
        await _solutionRepository.AddAsync(solutionHistory);

        var response = new CreateWorkflowActionResponse
        {
            Message = Message.Create("Workflow Action", workflowAction.ActionName),
            WorkflowActionId = workflowAction.ReferenceId,
            NodeId = workflowAction.NodeId
        };
        await _publisher.Publish(new WorkflowActionCreatedEvent { ActionName = workflowAction.ActionName },
            cancellationToken);

        return response;
    }
}