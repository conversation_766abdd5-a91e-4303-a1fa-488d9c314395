﻿using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Delete;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetList;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class AccessManagersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AccessManagerListVm>>> GetAccessManagers()
    {
        Logger.LogDebug("Get All AccessManagers ");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + LoggedInUserService.CompanyId,
            () => Mediator.Send(new GetAccessManagerListQuery()), CacheExpiry));
    }

    [HttpGet("{id}", Name = "GetAccessManager")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<AccessManagerDetailVm>> GetAccessManagerById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AccessManager Id");

        Logger.LogDebug($"Get AccessManager Details by Id '{id}'");

        return Ok(await Mediator.Send(new GetAccessManagerDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateAccessManagerResponse>> CreateAccessManager([FromBody] CreateAccessManagerCommand createAccessManagerCommand)
    {
        Logger.LogDebug($"Create AccessManager '{createAccessManagerCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateAccessManager), await Mediator.Send(createAccessManagerCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateAccessManagerResponse>> UpdateAccessManager([FromBody] UpdateAccessManagerCommand updateAccessManagerCommand)
    {
        Logger.LogDebug($"Update AccessManager '{updateAccessManagerCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateAccessManagerCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteAccessManagerResponse>> DeleteAccessManager(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AccessManager Id");

        Logger.LogDebug($"Delete AccessManager Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteAccessManagerCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<AccessManagerListVm>>> GetPaginatedAccessManagers([FromQuery] GetAccessManagerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in AccessManager Paginated List");
         
        return Ok(await Mediator.Send(query));
    }

    //[HttpGet, Route("by/{userId}")]
    //[Authorize(Policy = Permissions.Configuration.View)]
    //public async Task<ActionResult<List<GetAccessManagerByUserIdVm>>> GetAccessManagerByUserId(string userId)
    //{
    //    Guard.Against.InvalidGuidOrEmpty(userId, "User Id");

    //    Logger.LogDebug($"Get AccessManager Detail by User Id '{userId}'");

    //    return Ok(await Mediator.Send(new GetAccessManagerByUserIdQuery { Id = userId }));
    //}

    [HttpGet, Route("userRole")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetByRoleIdVm>>GetByRole(string roleId)
    {
        Logger.LogDebug($"Get AccessManager Detail by RoleId '{roleId}'");

        return Ok(await Mediator.Send(new GetByRoleQuery { Role = roleId }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllAccessManagersCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllAccessManagersRoleCacheKey };

        ClearCache(cacheKeys);
    }
}