﻿using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller;

public class MSSQLAlwaysOnControllerShould
{
    private readonly MSSQLAlwaysOnController _controller;
    private readonly Mock<IDashboardViewService> _mockDashboardViewService;
    private readonly Mock<ILogger<MSSQLAlwaysOnController>> _mockLogger;

    public MSSQLAlwaysOnControllerShould()
    {
        _mockDashboardViewService = new Mock<IDashboardViewService>();
        _mockLogger = new Mock<ILogger<MSSQLAlwaysOnController>>();
        _controller = new MSSQLAlwaysOnController(_mockDashboardViewService.Object, _mockLogger.Object);
    }

    [Fact]
    public void List_ReturnsViewResult()
    {
            
        var result = _controller.List();

            
        var viewResult = Assert.IsType<ViewResult>(result);
          
    }

    [Fact]
    public async Task GetMonitorServiceStatusByIdAndType_ReturnsCorrectData()
    {
            
        var monitorId = "testMonitorId";
        var type = "testType";
        var expectedResult = new GetByEntityIdVm(); // Mock or set up the expected result
        _mockDashboardViewService
            .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
            .ReturnsAsync(expectedResult);

            
        var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
        Assert.NotNull(result);
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task GetMonitorServiceStatusByIdAndType_LogsErrorOnException()
    {
            
        var monitorId = "testMonitorId";
        var type = "testType";
        var exception = new Exception("Test exception");
        _mockDashboardViewService
            .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
            .ThrowsAsync(exception);

            
        var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

           
            
        Assert.Null(result);
    }
}