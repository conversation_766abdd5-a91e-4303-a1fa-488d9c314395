﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Delete;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetList;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class SiteTypesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<IActionResult> GetSiteTypes()
    {
        Logger.LogDebug("Get All SiteTypes");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllSiteTypeCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetSiteTypeListQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetSiteTypeListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateSiteTypeResponse>> CreateSiteType([FromBody] CreateSiteTypeCommand createSiteTypeCommand)
    {
        Logger.LogDebug($"Create Site Type '{createSiteTypeCommand}'");

        ClearDataCache();
        return CreatedAtAction(nameof(CreateSiteType), await Mediator.Send(createSiteTypeCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateSiteTypeResponse>> UpdateSiteType([FromBody] UpdateSiteTypeCommand updateSiteTypeCommand)
    {
        Logger.LogDebug($"Update Site Type'{updateSiteTypeCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(UpdateSiteType), await Mediator.Send(updateSiteTypeCommand));
    }

    [HttpDelete]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteSiteTypeResponse>> DeleteSiteType(string id,string name)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SiteType Id");

        Logger.LogDebug($"Delete Site Type Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteSiteTypeCommand { Id = id ,Name = name }));
    }

    [HttpGet("{id}", Name = "GetSiteType")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<SiteTypeDetailVm>> GetSiteTypeById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SiteType Id");

        Logger.LogDebug($"Get Site Type Details by Id '{id}'");

        return Ok(await Mediator.Send(new GetSiteTypeDetailQuery { Id = id }));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsSiteTypeNameExist(string type, string? id)
    {
        Guard.Against.NullOrWhiteSpace(type, "Site Type");

        Logger.LogDebug($"Check Type Exists Detail by Site Type '{type}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetSiteTypeNameUniqueQuery { Type = type, Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<SiteTypeListVm>>> GetPaginatedSites([FromQuery] GetSiteTypePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Site Type Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        var cacheKeys = new[] { ApplicationConstants.Cache.AllSiteTypeCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllSiteTypeTypeCacheKey };

        ClearCache(cacheKeys);
    }
}