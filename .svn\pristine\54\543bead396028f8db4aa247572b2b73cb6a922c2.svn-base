﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence
{
    public interface ISybaseRSHADRMonitorLogsRepository : IRepository<SybaseRSHADRMonitorLog>
    {
        Task<List<SybaseRSHADRMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
        Task<List<SybaseRSHADRMonitorLog>> GetDetailByType(string type);
    }
}
