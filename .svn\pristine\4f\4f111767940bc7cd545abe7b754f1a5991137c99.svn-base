using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Persistence.Repositories;

public class PageBuilderRepository : BaseRepository<PageBuilder>, IPageBuilderRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public PageBuilderRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public override async Task<IReadOnlyList<PageBuilder>> ListAllAsync()
    {
        return await Entities.AsNoTracking().DescOrderById().Select(x=>new PageBuilder {
          Id=x.Id,ReferenceId=x.ReferenceId,
          Name=x.Name,
          Type=x.Type,
          Properties=x.Properties,
          IsLock=x.IsLock,
          IsPublish=x.IsPublish}).ToListAsync();
    }
}