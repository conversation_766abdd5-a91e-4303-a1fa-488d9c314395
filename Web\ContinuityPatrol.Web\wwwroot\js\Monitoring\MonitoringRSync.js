﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'RSyncAppReplication';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { RsyncmonitorStatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
//setTimeout(() => { rsyncServer(infraObjectId) }, 250)

//$('#mssqlserver').hide();
//async function rsyncServer(id) {

//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);

//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        mssqlServerData?.forEach(data => {
//            let value = data?.isServiceUpdate
//            let parsed = []
//            if (value && value !== 'NA') parsed = JSON?.parse(value)
//            if (Array.isArray(parsed)) {
//                parsed?.forEach(s => {
//                    if (s?.Services?.length) {
//                        $('#mssqlserver').show();
//                        bindRsyncServer(mssqlServerData)
//                    }
//                })
//            }
//        })

//    } else {
//        $('#mssqlserver').hide();
//    }

//}
//function bindRsyncServer(mssqlServerData) {

//    let prType = { IpAddress: '--', Services: [] };
//    let drType = { IpAddress: '--', Services: [] };

//    // Loop through each item to find PR and DR entries
//    mssqlServerData?.forEach(item => {
//        let parsedServices = [];
//        try {
//            const value = item?.isServiceUpdate
//            if (value && value !== 'NA') {
//                parsedServices = JSON.parse(item?.isServiceUpdate)
//            }
//        } catch (e) {
//            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
//        }

//        parsedServices?.forEach(serviceGroup => {
//            if (serviceGroup?.Type === 'PR') {
//                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
//                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
//            } else if (serviceGroup?.Type === 'DR') {
//                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
//                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
//            }
//        });
//    });

//    // Set header IPs
//    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
//    $('#drIp').text('DR (' + drType?.IpAddress + ')');

//    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
//    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
//    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
//    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

//    // Unique list of all service names from both PR and DR
//    let allServiceNames = [...new Set([
//        ...prType?.Services?.map(s => s?.ServiceName),
//        ...drType?.Services?.map(s => s?.ServiceName)
//    ])];

//    // Build table rows
//    let tbody = $('#mssqlserverbody');
//    tbody.empty();

//    allServiceNames?.forEach(serviceName => {
//        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
//        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

//        let prStatus = prService ? prService?.Status : '--';
//        let drStatus = drService ? drService?.Status : '--';
//        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
//        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
//        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

//        let row = `
//            <tr>
//                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
//                <td>${prIcon}${prStatus}</td>
//                <td>${drIcon}${drStatus}</td>
//            </tr>
//        `;
//        tbody.append(row);
//    });
//}
//function getStatusSummary(arr) {
//    let countMap = {};
//    arr?.forEach(status => {
//        countMap[status] = (countMap[status] || 0) + 1;
//    });
//    let total = arr?.length;
//    let statusSummary = Object.entries(countMap)
//        .map(([status, count]) => `${count} ${status}`)
//        .join(', ');
//    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
//}
//function getStatusIconClass(status) {
//    if (!status) return "text-danger cp-disable";

//    const lowerStatus = status.toLowerCase();
//    if (lowerStatus === "running") {
//        return "text-success cp-reload cp-animate";
//    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
//        return "text-danger cp-fail-back";
//    } else {
//        return "text-danger cp-disable";
//    }
//}

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function RsyncmonitorStatus(id, type) {
    
    $.ajax({
        url: "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType",
        method: 'GET',
        data: {
            monitorId: id,
            type: type
        },
        dataType: 'json',
        async: true,
        success: function (res) {
            const value = res.data;
           
            function checkAndReplace(value) {
                return (value === null || value === '' || value === undefined) ? 'NA' : value;
            }

            $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
            $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));

            if (value === undefined || value === null || value === '') {
                $("#noDataimg").css('text-align', 'center').html(noDataImage);
            }
            else {

                let data = JSON?.parse(value?.properties);                
                let prStatus
                let drStatus
                value?.serverStatus.forEach(server => {

                    Object.keys(server).forEach(key => {
                        let data = server[key]
                       
                        prStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : data?.status?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning"></i>' : '<i class="cp-pending text-warning"></i>';
                        drStatus = data?.status?.toLowerCase() === "up" ? '<i class="cp-up-linearrow text-success"></i>' : data?.status?.toLowerCase() === "down" ? '<i class="cp-down-linearrow text-danger"></i>' : data?.status?.toLowerCase() === "pending" ? '<i class="cp-pending text-warning"></i>' : '<i class="cp-pending text-warning"></i>';
                    })
                })
                let SecondaryServer;
               
                $('#PR_Server_Name').text(checkAndReplace(data?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_Name))
              
                $('#PR_Server_IpAddress1').text(checkAndReplace(data?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress)).prepend(prStatus)
               

                const PrimaryServer = prStatus + checkAndReplace(data?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress)
               

                let customSite = data?.RSyncReplicationModels?.length > 1;
                if (customSite) {
                    $("#Sitediv").show();
                } else {
                    $("#Sitediv").hide();
                }


                $(".siteContainer").empty();


                data?.RSyncReplicationModels?.forEach((a, index) => {
                    let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
                    $(".siteContainer").append(selectTab);
                });


                if (data?.RSyncReplicationModels?.length > 0) {
                    $("#siteName0 .nav-link").addClass("active");
                    displaySiteData(data?.RSyncReplicationModels[0]);
                }


                $(document).on('click', '.siteListChange', function () {
                    $(".siteListChange .nav-link").removeClass("active");
                    $(this).find(".nav-link").addClass("active");
                    let siteId = $(this)[0].id
                    let getSiteName = $(`#${siteId} .siteName`).text()

                    let MonitoringModel = data?.RSyncReplicationModels?.find(d => d?.Type === getSiteName);
                    if (MonitoringModel) {
                        displaySiteData(MonitoringModel, getSiteName);
                    }
                    getSiteNamebindMonitoringServices(globalMSSQLServerData, getSiteName);
                });

                function displaySiteData(siteData) {
                    
                    let obj = {};

                    for (let key in siteData?.MonitoringModel) {
                        obj[`DR_` + key] = siteData?.MonitoringModel[key];
                    }
                    $("#DR_Server_IpAddress").text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                    $("#DR_Server_HostName").text(checkAndReplace(siteData?.MonitoringModel?.Server_HostName)).prepend('<i class="cp-host-name text-primary"></i>')

                    $('#DR_Server_Name').text(checkAndReplace(siteData?.MonitoringModel?.Server_Name))
                    $('#DR_Server_IpAddress1').text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                    SecondaryServer = drStatus + checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)
                    let SourcePath = [];
                    let DestinationPath = [];
                    let TotalNumberoffiles = [];
                    let TotalFilesSize = [];
                    let NumberOfRegFilesTransfer = [];
                    let TotalTransferfileSize = [];
                    $('#rsynctable').empty();
                    siteData?.MonitoringModel?.RSyncMonitorModel && siteData?.MonitoringModel?.RSyncMonitorModel?.length && siteData?.MonitoringModel?.RSyncMonitorModel?.forEach((list, i) => {
                        
                        //$('#DR_Server_Name').text(checkAndReplace(siteData?.MonitoringModel?.Server_Name))
                        //$('#DR_Server_IpAddress1').text(checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)).prepend(drStatus)
                        /*SecondaryServer = drStatus + checkAndReplace(siteData?.MonitoringModel?.Server_IpAddress)*/
                         SourcePath.push(checkAndReplace(list?.SourcePath)) 
                         DestinationPath.push(checkAndReplace(list?.DestinationPath));
                         TotalNumberoffiles.push(checkAndReplace(list?.TotalNumberoffiles));
                         TotalFilesSize.push(checkAndReplace(list?.TotalFilesSize));
                         NumberOfRegFilesTransfer.push(checkAndReplace(list?.NumberOfRegFilesTransfer));
                         TotalTransferfileSize.push(checkAndReplace(list?.TotalTransferfileSize))
                    });
                    let jobData = ''
                    let iconClass = TotalNumberoffiles !== 'NA' ? 'cp-files me-1 fs-6 text-primary' : TotalFilesSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : NumberOfRegFilesTransfer != "NA" ? "cp-files me-1 fs-6 text-primary" : TotalTransferfileSize !== 'NA' ? "cp-file-size me-1 fs-6 text-primary" : 'text-danger me-1 fs-6 cp-disable';
                  
                        jobData += `
                        <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th></th>

                                </tr>
                            </thead>
                            <tbody>
                        <tr>
                           <td class="fw-semibold text-truncate "><i class="text-secondary cp-custom-server-4 me-1"></i>Primary Server</td>
                            <td class="text-truncate">${PrimaryServer}</td>
                        </tr>
                         <tr>
                             <td class="fw-semibold text-truncate "><i class="text-secondary cp-custom-server-3 me-1"></i>Secondary Server</td>
                             <td class="text-truncate">${SecondaryServer}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-replication-source me-1"></i>Source Replication Path</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 text-primary"></i>${SourcePath}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-location me-1"></i>Destination Path</td>
                            <td class="text-truncate"><i class="cp-report-path me-1 text-primary"></i>${DestinationPath}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-copied-transaction me-1"></i>Number of files</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${TotalNumberoffiles}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-file-size me-1"></i>Total file size</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${TotalFilesSize}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-backup me-1"></i>Number of regular files transferred</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${NumberOfRegFilesTransfer}</td>
                        </tr>
                        <tr>
                            <td class="fw-semibold text-truncate"><i class="text-secondary cp-last-backup me-1"></i>Total transferred file size</td>
                            <td class="text-truncate"><i class="${iconClass}"></i>${TotalTransferfileSize}</td>
                        </tr>
                        </tbody>
                            `
                  
                    $('#rsynctable').append(jobData);
                   
                }
            }

            }

        });
    
}
