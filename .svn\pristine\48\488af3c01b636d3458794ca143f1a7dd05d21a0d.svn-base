﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;

public class
    GetDataLagStatusbyLast7DaysQueryHandler : IRequestHandler<GetDataLagStatusbyLast7DaysQuery,
        List<DataLagStatusbyLast7DaysVm>>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly IMapper _mapper;

    public GetDataLagStatusbyLast7DaysQueryHandler(IMapper mapper, IDashboardViewRepository dashboardViewRepository)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<List<DataLagStatusbyLast7DaysVm>> Handle(GetDataLagStatusbyLast7DaysQuery request,
        CancellationToken cancellationToken)
    {
        var dataLagStatus = await _dashboardViewRepository.GetBusinessViewByLast7Days();

        return dataLagStatus.Count > 0
            ? _mapper.Map<List<DataLagStatusbyLast7DaysVm>>(dataLagStatus)
            : new List<DataLagStatusbyLast7DaysVm>();
    }
}