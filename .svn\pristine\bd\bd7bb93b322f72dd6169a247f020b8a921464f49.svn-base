﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;

public class CreateBulkDataInfraObjectListCommand
{
    public string Name { get; set; }
    public string Description { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public int Type { get; set; }
    public string SubType { get; set; }
    public bool DRReady { get; set; }
    public bool NearDR { get; set; }
    public int RecoveryType { get; set; }
    public string ServerProperties { get; set; }
    public string DatabaseProperties { get; set; }
    public string ReplicationProperties { get; set; }
    public string PRServerId { get; set; }
    public string PRServerName { get; set; }
    public string DRServerId { get; set; }
    public string DRServerName { get; set; }
    public string NearDRServerId { get; set; }
    public string NearDRServerName { get; set; }
    public string PRDatabaseId { get; set; }
    public string PRDatabaseName { get; set; }
    public string DRDatabaseId { get; set; }
    public string DRDatabaseName { get; set; }
    public string NearDRDatabaseId { get; set; }
    public string NearDRDatabaseName { get; set; }
    public string PRReplicationId { get; set; }
    public string PRReplicationName { get; set; }
    public string DRReplicationId { get; set; }
    public string DRReplicationName { get; set; }
    public string NearDRReplicationId { get; set; }
    public string NearDRReplicationName { get; set; }
    public string Priority { get; set; }
    public string State { get; set; }
    public bool IsPair { get; set; }
    public string PairInfraObjectId { get; set; }
    public string PairInfraObjectName { get; set; }
    public bool IsAssociate { get; set; }
    public string IsAssociateInfraObjectId { get; set; }
    public string IsAssociateInfraObjectName { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryTypeId { get; set; }
    public string ReplicationCategoryType { get; set; }
    public string SubTypeId { get; set; }
    public string TypeName { get; set; }
    public string PRNodeId { get; set; }
    public string PRNodeName { get; set; }
    public string DRNodeId { get; set; }
    public string DRNodeName { get; set; }
    public string NodeProperties { get; set; }
    public string Reason { get; set; }

    public string SiteProperties { get; set; }
    //[JsonIgnore]
    //public string BulkImportOperationId { get; set; }
    //[JsonIgnore]
    //public string BulkImportOperationGroupId { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }

    //public void Sanitize()
    //{
    //    var props = GetType().GetProperties()
    //        .Where(p => p.PropertyType == typeof(string) && p.CanRead && p.CanWrite);

    //    foreach (var prop in props)
    //    {
    //        if (prop.GetValue(this) is string value)
    //        {
    //            prop.SetValue(this, value.Trim());
    //        }
    //    }
    //}
}