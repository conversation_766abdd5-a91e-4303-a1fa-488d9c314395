﻿namespace ContinuityPatrol.Domain.Entities;

public class UserLogin : BaseEntity
{
    public string UserId { get; set; }

    [NotMapped] public new string ReferenceId { get; set; }
    public int InvalidLoginAttempt { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public string LastLoginIp { get; set; }
    public int LastAlertId { get; set; }
    public DateTime? LastPasswordChanged { get; set; }
    public string SessionId { get; set; }
    public bool IsProperLoggedOut { get; set; }
    public DateTime LastLoggedOutDate { get; set; }
}