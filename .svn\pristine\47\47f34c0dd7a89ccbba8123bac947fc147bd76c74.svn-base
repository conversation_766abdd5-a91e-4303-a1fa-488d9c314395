﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetImpactAvailabilityByBusinessServiceId;

public class GetImpactAvailabilityByBusinessServiceIdQueryHandler:IRequestHandler<GetImpactAvailabilityByBusinessServiceIdQuery, DashboardImpactAvailabilityDetailVm>
{
    private readonly IDatalagImpactAvailabilityViewRepository _datalagImpactAvailabilityView;
    private readonly IMapper _mapper;
    public GetImpactAvailabilityByBusinessServiceIdQueryHandler(IDatalagImpactAvailabilityViewRepository datalagImpactAvailabilityView, IMapper mapper  )
    {
        _datalagImpactAvailabilityView = datalagImpactAvailabilityView;
        _mapper = mapper;
    }

    public async Task<DashboardImpactAvailabilityDetailVm> Handle(GetImpactAvailabilityByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var impactByBusinessServiceId = await _datalagImpactAvailabilityView.GetByBusinessServiceId(request.BusinessServiceId);

        var impactAvailabilityDto = new DashboardImpactAvailabilityDetailVm
        {
            Id = impactByBusinessServiceId?.ReferenceId ?? request.BusinessServiceId,
            BusinessServiceId = impactByBusinessServiceId?.ReferenceId ?? request.BusinessServiceId,
            BusinessServiceName = impactByBusinessServiceId?.BusinessServiceName ?? null,
            TotalBusinessFunctionCount = impactByBusinessServiceId?.TotalBusinessFunctionCount ?? 0,
            BusinessFunctionTotalImpacted = impactByBusinessServiceId?.BusinessFunctionNotAvailable ?? 0,
            BusinessFunctionMajorImpactCount = impactByBusinessServiceId?.BusinessFunctionImpactCount ?? 0,
            BusinessFunctionPartialImpactCount = impactByBusinessServiceId?.BusinessFunctionPartialImpactCount ?? 0,
            BusinessFunctionUnderRPOCount = impactByBusinessServiceId?.BusinessFunctionAvailableCount ?? 0,
            TotalInfraObjectCount = impactByBusinessServiceId?.TotalInfraObjectCount ?? 0,
            InfraTotalImpactCount = impactByBusinessServiceId?.InfraNotAvailableCount ?? 0,
            InfraMajorImpactCount = impactByBusinessServiceId?.InfraMajorImpactCount ?? 0,
            InfraPartialImpactCount = impactByBusinessServiceId?.InfraPartialImpactCount ?? 0,
            InfraUnderRPOCount = impactByBusinessServiceId?.InfraAvailableCount ?? 0
        };

        //var impactAvailabilityDto = _mapper.Map<ImpactAvailabilityDetailVm>(impactByBusinessServiceId);

        return impactAvailabilityDto;
    }
}
