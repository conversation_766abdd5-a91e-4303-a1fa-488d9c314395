﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;

public class UpdateReportScheduleCommandValidator : AbstractValidator<UpdateReportScheduleCommand>
{
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public UpdateReportScheduleCommandValidator(IReportScheduleRepository reportScheduleRepository)
    {
        _reportScheduleRepository = reportScheduleRepository;
        RuleFor(p => p.ReportName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.ReportType)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .Matches(@"^[a-zA-Z0-9_\s-]+$")
            .WithMessage("Please enter valid {PropertyName}");

        RuleFor(p => p.NodeName)
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.")
            .When(p => p.NodeName.IsNotNullOrWhiteSpace());

        RuleFor(p => p.ReportProperties)
            .Must(IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.")
            .When(p => p.ReportProperties.IsNotNullOrWhiteSpace());

        RuleFor(p => p.UserProperties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .Must(IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.")
            .NotNull();

        RuleFor(e => e)
            .MustAsync(IsValidGuidId).WithMessage("Id is invalid")
            .MustAsync(ReportScheduleNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> ReportScheduleNameUnique(UpdateReportScheduleCommand e,
        CancellationToken cancellationToken)
    {
        return !await _reportScheduleRepository.IsReportScheduleNameExist(e.ReportName, e.Id);
    }

    private Task<bool> IsValidGuidId(UpdateReportScheduleCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "ReportScheduler Id");

        if (!string.IsNullOrEmpty(p.NodeId))
            Guard.Against.InvalidGuidOrEmpty(p.NodeId, "Node Id");

        return Task.FromResult(true);
    }

    private static bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                return JsonConvert.DeserializeObject(properties) != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}