﻿using ContinuityPatrol.Application.Features.DashboardView.Event.ITResiliencyView;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.Job.Commands.RescheduleJob;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Dashboard.Controllers;

[Area("Dashboard")]
public class ITResiliencyViewController : BaseController
{
    private readonly IDataProvider _provider;
    private readonly ILogger<ITResiliencyViewController> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    public ITResiliencyViewController(IDataProvider provider, IMapper mapper, ILogger<ITResiliencyViewController> logger, IPublisher publisher)
    {
        _mapper = mapper;
        _provider = provider;
        _logger = logger;
        _publisher = publisher;
    }

    public async Task<JsonResult> GetITViewByInfraObjectId(string infraObjectId)
    {
        if (string.IsNullOrWhiteSpace(infraObjectId))
        {
            return Json("InfraObject is Not valid format");
        }
        else
        {
            try
            {
                var Infra = await _provider.DashboardView.GetITViewByInfraObjectId(infraObjectId);
                // Infra.Success = true;
                return Json(new { Success = true, data = Infra });
            }
            catch (Exception ex)
            {
                return ex.GetJsonException();
            }
        }
    }
    public async Task<JsonResult> GetItViewByBusinessServiceId(string businessServiceId)
    {
        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json("businessService is Not valid format");
        }
        else
        {
            try
            {
                var businessService = await _provider.DashboardView.GetItViewByBusinessServiceId(businessServiceId);
                // Infra.Success = true;
                return Json(new { Success = true, data = businessService });
            }
            catch (Exception ex)
            {
                return ex.GetJsonException();
            }
        }
    }
    public async Task<JsonResult> GetMonitoringDetailsByInfraObjectId(string infraObjectId)
    {
        if (string.IsNullOrWhiteSpace(infraObjectId))
        {
            return Json("InfraObject is Not valid format");
        }
        else
        {
            try
            {
                var Infra = await _provider.DashboardView.GetITViewByInfraObjectId(infraObjectId);
                // Infra.Success = true;
                return Json(new { Success = true, data = Infra });
            }
            catch (Exception ex)
            {
                return ex.GetJsonException();
            }
        }
    }
    public async Task<JsonResult> GetITViewLogByInfraObjectId(string infraObjectId)
    {
        if (string.IsNullOrWhiteSpace(infraObjectId))
        {
            return Json("InfraObject is Not valid format");
        }
        else
        {
            try
            {
                var InfraObject = await _provider.DashboardView.GetResilienceHealthStatusByInfraObjectId(infraObjectId);
                // Infra.Success = true;
                return Json(new { Success = true, data = InfraObject });
            }
            catch (Exception ex)
            {
                return ex.GetJsonException();
            }
        }
    }
  
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new ITResiliencyEvent());
        return View();
    }
    public async Task<JsonResult> GetBusinessServiceList()
    {
        try
        {
            var ItView = await _provider.DashboardView.GetItViewList();
            return Json(new { Success = true, data = ItView });

        }
        catch (Exception ex)
        {
            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetAllInfraSummaries()
    {
        try
        {
            var summaries = await _provider.InfraSummary.GetInfraSummaries();

            return Json(new { Success = true, data = summaries });
            //return Json(summaries);
        }
        catch (Exception ex)
        {
            return ex.GetJsonException();
        }

    }


    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> UpdateInfraObjectState([FromBody] UpdateInfraObjectStateCommand updateInfraObjectStateCommand)
    {
        try
        {
            var state = await _provider.InfraObject.UpdateInfraObjectState(updateInfraObjectStateCommand);

            if (state.Success)
            {
                return Json(new { Success = true, data = state });
            }

            return Json(new { Success = false, data = state });

            //return Json(state);

        }

        catch (Exception ex)
        {
            return ex.GetJsonException();
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> RescheduleJob([FromBody] RescheduleJobCommand jobModel)
    {
        try
        {
            //var jobCommand = _mapper.Map<RescheduleJobCommand>(jobModel);
            var result = await _provider.JobService.RescheduleJob(jobModel);
            return Json(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.ValidationErrors.FirstOrDefault()}");
            return (Json(new { success = false, message = ex.ValidationErrors.FirstOrDefault() }));
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.GetMessage()}");
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    [HttpGet]
    public async Task<JsonResult> GetInfraObjectDetailsById(string infraObjectId)
    {
        if (string.IsNullOrWhiteSpace(infraObjectId))
        {
            return Json("InfraObject is Not valid format");
        }
        else
        {
            try
            {
                var solutionInfraobject = await _provider.InfraObject.GetInfraObjectDetailsById(infraObjectId);
                // Infra.Success = true;
                return Json(new { Success = true, data = solutionInfraobject });
            }
            catch (Exception ex)
            {
                return ex.GetJsonException();
            }
        }
    }

    //private IActionResult RouteToPostView(BaseResponse result)
    //{
    //    try
    //    {
    //        TempData.Set(result.Success
    //        ? new NotificationMessage(NotificationType.Success, result.Message)
    //        : new NotificationMessage(NotificationType.Error, result.Message)
    //        );

    //        return RedirectToAction("List", "ITView", new { Area = "Dashboard" });
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError($"An error occurred while processing the request. {ex.GetMessage()}");
    //        return Json(new { success = false, message = ex.GetMessage() });
    //    }
    //}

}