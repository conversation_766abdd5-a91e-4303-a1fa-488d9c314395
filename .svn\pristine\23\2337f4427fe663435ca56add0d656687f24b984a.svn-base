﻿using ContinuityPatrol.Web.Areas.Report.Controllers;
using System.Drawing;
using Newtonsoft.Json;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class RPOSLARoboCopyReport : DevExpress.XtraReports.UI.XtraReport
    {
        public static string username;
        public static TimeSpan ConfigRPO;
        private static TimeSpan Threshold;
        private static Int64 ConfiguredRPO;
        private static Int64 ConfiguredThreshold;
        private string RportDateOption;
        private List<GetRPOSLARoboCopyReportVm> ReportList = new List<GetRPOSLARoboCopyReportVm>();
        public  GetRPOSLARoboCopyBusinessServiceDetails ReportData = new GetRPOSLARoboCopyBusinessServiceDetails();
        private readonly ILogger<PreBuildReportController> _logger;
        
        private class SpDatalag
        {
            public TimeSpan Datalag { get; set; }
            public int TimeStamp { get; set; }
            public string Date { get; set; }
        }
        public RPOSLARoboCopyReport(string data)
        {
            try
            {
                _logger = PreBuildReportController._logger;

                InitializeComponent();
                ClientCompanyLogo();
                
                ReportData = JsonConvert.DeserializeObject<GetRPOSLARoboCopyBusinessServiceDetails>(data);
                
                ReportList = ReportData.GetRPOSLARoboCopyReportVms;

                RportDateOption = ReportData.DateOption;

                this.DataSource = ReportList;

                var infraName = ReportData.InfraObjectName;
                this.DisplayName = "RPOSLAReport_" + infraName + "_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");
                lblInfra.Text = infraName;

                lblBusiness.Text = !string.IsNullOrEmpty(ReportData.BusinessServiceName)
                    ? ReportData.BusinessServiceName
                    : "-";

                lblPRIP.Text = !string.IsNullOrEmpty(ReportData.PRIPAddress)
                    ? ReportData.PRIPAddress
                    : "-";
                //lblPRDB.Text = jsonObject.SelectToken("PRDatabaseName")?.ToString();
                lblDRIP.Text = !string.IsNullOrEmpty(ReportData.DRIPAddress)
                    ? ReportData.DRIPAddress
                    : "-";
                //lblDRDB.Text = jsonObject.SelectToken("DRDatabaseName")?.ToString();

                username = ReportData.ReportGeneratedBy;

                ConfiguredRPO = ReportList.LastOrDefault().ConfigureRPO.ToInt64();
                ConfiguredThreshold = ReportList.LastOrDefault().Threshold.ToInt64();

                ConfigRPO = TimeSpan.FromMinutes(ConfiguredRPO);
                Threshold = TimeSpan.FromMinutes(ConfiguredThreshold);

                //lblConfiguredRPO.Text = "Configured RPO(" + ConfiguredRPO.ToString() + " Mins)";
                //lblThreshold.Text = "Threshold Exceeded(" + ConfiguredThreshold.ToString() + " Mins)";

                lblUnderThreshold.Text = "DataLag ≤ " + Threshold + " (Threshold)";
                lblDatalagExceed.Text = "DataLag > " + ConfigRPO + " (Configured RPO)";
                lblThresholdExceed.Text = "(Threshold)" + Threshold + " < Threshold Exceeded ≤ " + ConfigRPO + "(Configured RPO)";

                var fromdate = ReportData.FromDate;
                var todate = ReportData.ToDate;
                
                DateTime reportFromdate = DateTime.Parse(fromdate);
                DateTime reportTodate = DateTime.Parse(todate);

                lblfromdate.Text = reportFromdate.ToString("dd/MM/yyyy");
                lbltodate.Text = reportTodate.ToString("dd/MM/yyyy");
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLARoboCopy Report. The error message : " + ex.Message); throw; }
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + username;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLARoboCopy Report's User Name. The error message : " + ex.Message); throw; }
        }

        //private void xrChart2_BeforePrint(object sender, CancelEventArgs e)
        //{

        //    List<SpDatalag> chart = GetChart();
        //    Series series1 = new Series("Series1", ViewType.Spline);
        //    xrChart2.Series.Add(series1);

        //    series1.DataSource = CreateChartDatasp(chart);
        //    series1.ArgumentScaleType = ScaleType.Auto;
        //    series1.ArgumentDataMember = "Argument";
        //    series1.ValueScaleType = ScaleType.Numerical;
        //    series1.ValueDataMembers.AddRange(new string[] { "Value" });
        //    series1.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;

        //    xrChart2.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
        //    SplineSeriesView view1 = series1.View as SplineSeriesView;
        //    view1.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
        //    view1.Pane.BorderVisible = false;

        //    Series series2 = new Series("Series2", ViewType.Spline);
        //    xrChart2.Series.Add(series2);

        //    series2.DataSource = CreateChartDataline(chart);
        //    series2.ArgumentScaleType = ScaleType.Auto;
        //    series2.ArgumentDataMember = "Argument";
        //    series2.ValueScaleType = ScaleType.Numerical;
        //    series2.ValueDataMembers.AddRange(new string[] { "Value" });
        //    series2.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
        //    SplineSeriesView view2 = series2.View as SplineSeriesView;
        //    view2.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
        //    view2.Pane.BorderVisible = false;

        //    Series series3 = new Series("Series3", ViewType.Spline);
        //    xrChart2.Series.Add(series3);

        //    series3.DataSource = CreateChartThresholdline(chart);
        //    series3.ArgumentScaleType = ScaleType.Auto;
        //    series3.ArgumentDataMember = "Argument";
        //    series3.ValueScaleType = ScaleType.Numerical;
        //    series3.ValueDataMembers.AddRange(new string[] { "Value" });
        //    series3.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
        //    SplineSeriesView view3 = series3.View as SplineSeriesView;
        //    view3.MarkerVisibility = DevExpress.Utils.DefaultBoolean.False;
        //    view3.Pane.BorderVisible = false;

        //    ((XYDiagram)xrChart2.Diagram).AxisY.GridLines.Visible = true;
        //    int red = 138;
        //    int green = 144;
        //    int blue = 154;
        //    Color customColor = Color.FromArgb(red, green, blue);
        //    int red1 = 243;
        //    int green1 = 243;
        //    int blue1 = 243;
        //    Color customColor1 = Color.FromArgb(red1, green1, blue1);

        //    ((XYDiagram)xrChart2.Diagram).AxisY.GridLines.Color = customColor1;
        //    ((XYDiagram)xrChart2.Diagram).AxisX.Label.TextColor = customColor;
        //    ((XYDiagram)xrChart2.Diagram).AxisY.Label.TextColor = customColor;
        //    ((XYDiagram)xrChart2.Diagram).AxisX.WholeRange.SideMarginsValue = 0;
        //    NumericScaleOptions numericScaleOptions = ((XYDiagram)xrChart2.Diagram).AxisX.NumericScaleOptions;
        //    numericScaleOptions.GridOffset = 0;
        //    numericScaleOptions.AutoGrid = false;
        //    numericScaleOptions.GridAlignment = NumericGridAlignment.Ones;
        //    //numericScaleOptions.GridSpacing = 0;
        //}

        //private List<SpDatalag> GetChart()
        //{
        //    TimeSpan timeSpan = TimeSpan.Zero;
        //    TimeSpan datalag = TimeSpan.Zero;
        //    List<SpDatalag> chart = new List<SpDatalag>();
        //    List<SpDatalag> finalchart = new List<SpDatalag>();
        //    if (RportDateOption == "Daily")
        //    {
        //        foreach (var check in ReportList)
        //        {
        //            if (!string.IsNullOrEmpty(check.DataLag))
        //            {
        //                if (check.DataLag.Contains("+"))
        //                {
        //                    string datalagvalue = check.DataLag;
        //                    datalagvalue = datalagvalue.TrimStart('+');
        //                    datalagvalue = datalagvalue.Replace(" ", ".");
        //                    datalag = TimeSpan.Parse(datalagvalue);
        //                }
        //                else
        //                {
        //                    string datalagvalues = string.IsNullOrEmpty(check.DataLag) || check.DataLag == "NA" ? "00:00:00" : check.DataLag;
        //                    datalag = TimeSpan.Parse(datalagvalues);
        //                }
        //                DateTime validDateTime = DateTime.ParseExact(check.TimeStamp, "dd-MM-yyyy hh:mm:ss tt", CultureInfo.InvariantCulture);
        //                int hours = validDateTime.Hour;
        //                chart.Add(new SpDatalag { Datalag = datalag, TimeStamp = hours });
        //            }

        //        }
        //        var groupedData = chart.GroupBy(entry => entry.TimeStamp);

        //        foreach (var group in groupedData)
        //        {

        //            TimeSpan maxDatalag = group.Max(entry => entry.Datalag);
        //            finalchart.Add(new SpDatalag { Datalag = maxDatalag, TimeStamp = group.Key });

        //        }
        //    }
        //    else
        //    {
        //        foreach (var check in ReportList)
        //        {
        //            if (!string.IsNullOrEmpty(check.DataLag))
        //            {
        //                if (check.DataLag.Contains("+"))
        //                {
        //                    string datalagvalue = check.DataLag;
        //                    datalagvalue = datalagvalue.TrimStart('+');
        //                    datalagvalue = datalagvalue.Replace(" ", ".");
        //                    datalag = TimeSpan.Parse(datalagvalue);
        //                }
        //                else
        //                {
        //                    string datalagvalue = string.IsNullOrEmpty(check.DataLag) || check.DataLag == "NA" ? "00:00:00" : check.DataLag;
        //                    datalag = TimeSpan.Parse(datalagvalue);
        //                }
        //                DateTime validDateTime = DateTime.ParseExact(check.TimeStamp, "dd-MM-yyyy hh:mm:ss tt", CultureInfo.InvariantCulture);
        //                int day = validDateTime.Day;
        //                string formattedDate = validDateTime.ToString("MMM-dd");
        //                chart.Add(new SpDatalag { Datalag = datalag, TimeStamp = day, Date = formattedDate });
        //            }

        //        }
        //        var groupedData = chart.GroupBy(entry => entry.TimeStamp);

        //        foreach (var group in groupedData)
        //        {
        //            string date = string.Empty;
        //            foreach (var entry in group)
        //            {
        //                if (!string.IsNullOrEmpty(entry.Date))
        //                {
        //                    date = entry.Date;
        //                    break;
        //                }
        //            }
        //            TimeSpan maxDatalag = group.Max(entry => entry.Datalag);
        //            finalchart.Add(new SpDatalag { Datalag = maxDatalag, Date = date });

        //        }
        //    }
        //    return finalchart;
        //}

        //private DataTable CreateChartDatasp(List<SpDatalag> spDatalags)
        //{
        //    DataTable table = new DataTable("Table1");
        //    table.Columns.Add("Argument", typeof(string));
        //    table.Columns.Add("Value", typeof(Int64));
        //    Random rnd = new Random();
        //    int i = 0;
        //    foreach (var inline in spDatalags)
        //    {
        //        if (i == 0) { table.Rows.Add(0, 0); }
        //        if (RportDateOption == "Daily")
        //        {
        //            table.Rows.Add(inline.TimeStamp.ToString(), inline.Datalag.TotalMinutes);
        //        }
        //        else
        //        {
        //            table.Rows.Add(inline.Date.ToString(), inline.Datalag.TotalMinutes);
        //        }
        //        i++;
        //    }
        //    return table;
        //}
        //private DataTable CreateChartDataline(List<SpDatalag> spDatalags)
        //{
        //    DataTable table = new DataTable("Table1");
        //    table.Columns.Add("Argument", typeof(string));
        //    table.Columns.Add("Value", typeof(Int64));
        //    Random rnd = new Random();
        //    int i = 0;
        //    foreach (var inline in spDatalags)
        //    {
        //        if (i == 0) { table.Rows.Add(0, ConfiguredRPO); }
        //        if (RportDateOption == "Daily")
        //        {
        //            table.Rows.Add(inline.TimeStamp.ToString(), ConfiguredRPO);
        //        }
        //        else
        //        {
        //            table.Rows.Add(inline.Date.ToString(), ConfiguredRPO);
        //        }
        //        i++;
        //    }
        //    return table;
        //}
        //private DataTable CreateChartThresholdline(List<SpDatalag> spDatalags)
        //{
        //    DataTable table = new DataTable("Table1");
        //    table.Columns.Add("Argument", typeof(string));
        //    table.Columns.Add("Value", typeof(Int64));
        //    Random rnd = new Random();
        //    int i = 0;
        //    foreach (var inline in spDatalags)
        //    {
        //        if (i == 0) { table.Rows.Add(0, ConfiguredThreshold); }
        //        if (RportDateOption == "Daily")
        //        {
        //            table.Rows.Add(inline.TimeStamp.ToString(), ConfiguredThreshold);
        //        }
        //        else
        //        {
        //            table.Rows.Add(inline.Date.ToString(), ConfiguredThreshold);
        //        }
        //        i++;
        //    }
        //    return table;
        //}
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLARoboCopy Report's CP Version. The error message : " + ex.Message); throw; }
        }

        [SupportedOSPlatform("windows")]
        private static Image LoadImageFromFile(string path)
        {
            return Image.FromFile(path);
        }
        private void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        if (OperatingSystem.IsWindows())
                        {
                            prClientLogo.Image = LoadImageFromFile(ms.ToString());
                        }
                        else
                        {
                            throw new PlatformNotSupportedException("Image loading only works on Windows in this context.");
                        }
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RPOSLARoboCopy Report's customer logo" + ex.Message.ToString());
            }
        }
    }

}
