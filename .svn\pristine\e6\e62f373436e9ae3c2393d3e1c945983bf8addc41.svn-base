﻿using ContinuityPatrol.Application.Features.Server.Events.UpdateBulkPassword;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;

public class UpdateBulkPasswordCommandHandler : IRequestHandler<UpdateBulkPasswordCommand, UpdateBulkPasswordResponse>
{
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly ILogger<UpdateBulkPasswordCommandHandler> _logger;

    public UpdateBulkPasswordCommandHandler(IServerRepository serverRepository, IPublisher publisher, ILogger<UpdateBulkPasswordCommandHandler> logger)
    {
        _serverRepository = serverRepository;
        _publisher = publisher;
        _logger = logger;
    }

    public async Task<UpdateBulkPasswordResponse> Handle(UpdateBulkPasswordCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            var serverIds = request.UpdateBulkPasswordLists
                .Where(x=>x.Id.IsNotNullOrWhiteSpace())
                .Select(x => x.Id)
                .ToList();

            var eventToUpdate = await _serverRepository.GetByServerIdsAsync(serverIds);

            if (eventToUpdate.Count > 0)
            {
                if (request.IsSubstituteAuthentication)
                {
                    var req = request.UpdateBulkPasswordLists.ToList();

                    eventToUpdate = eventToUpdate.Select(ser =>
                    {

                        var toChangeSubAuthUsers = req.FirstOrDefault(bu => bu.Id.Equals(ser.ReferenceId));

                        if (toChangeSubAuthUsers == null && toChangeSubAuthUsers.SubAuthUserNames.Count == 0) return ser;

                        var jsonProperties = GetJsonProperties.GetJsonObjectByKeyValue(ser.Properties, "SubstituteAuthenticationUser", toChangeSubAuthUsers.SubAuthUserNames, request.Password);

                        var modifiedJson = jsonProperties.ToString();
                        var sanitizedJson = modifiedJson.Replace("\r\n", "");

                        ser.Properties = sanitizedJson;
                        ser.Status = "Pending";

                        return ser;
                    }).ToList();
                }
                else
                {


                    eventToUpdate = eventToUpdate.Select(ser =>
                    {
                        var jsonProperties = JsonConvert.DeserializeObject<dynamic>(ser.Properties);

                        jsonProperties = GetJsonProperties.ReplacePasswords(jsonProperties, request.Password);

                        var modifiedJson = jsonProperties.ToString();
                        var sanitizedJson = modifiedJson.Replace("\r\n", "");

                        ser.Properties = sanitizedJson;
                        ser.Status = "Pending";

                        return ser;
                    }).ToList();

                }
                await _serverRepository.UpdateRangeAsync(eventToUpdate);

                foreach (var serverDto in request.UpdateBulkPasswordLists)
                {
                    await _publisher.Publish(
                        new ServerBulkPasswordUpdatedEvent
                        {
                            ServerName = serverDto.Name,
                            IpAddress = serverDto.IpAddress,
                            UserName = serverDto.UserName,
                            OSType = serverDto.OsType,
                            ActivityType = ActivityType.Update.ToString(),
                            IsSubAuthentication=request.IsSubstituteAuthentication,
                            
                        }, cancellationToken);
                }
            }

            return new UpdateBulkPasswordResponse
            {
                Message = request.IsSubstituteAuthentication ? $"{eventToUpdate.Count} Server Sub Authentication password updated successfully."
                : $"{eventToUpdate.Count} Server password updated successfully."
            };
        }
        catch (Exception ex)
        {
            _logger.Exception("Error in updating server password", ex);

            throw new InvalidException($"Error in updating server password,{ex.GetMessage()}");
        }
    }
}