﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Event.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class GlobalSettingsController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<GlobalSettingsController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;

    public GlobalSettingsController(IPublisher publisher, ILogger<GlobalSettingsController> logger, IDataProvider dataProvider, ILoggedInUserService loggedInUserService, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in GlobalSetting");
        await _publisher.Publish(new GlobalSettingPaginatedViewEvent());
        return View();
    }
    [HttpGet]
    public async Task<IActionResult> GetGlobalSettingList()
    {
        _logger.LogDebug("Entering GetList method in GlobalSetting");

        try
        {
            var globalSettingsList = await _dataProvider.GlobalSettings.GetGlobalSettingsList();
            _logger.LogDebug("Successfully retrieved global settings list in GlobalSetting");
            return Json(globalSettingsList);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on global settings page while retrieving the globalSettings list", ex);
            return Json("");
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(GlobalSettingModel globalSettingModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in GlobalSetting");

        var id = Request.Form["Id"].ToString();
        try
        {
            BaseResponse result;
            globalSettingModel.LoginUserId = _loggedInUserService.UserId;
            if (globalSettingModel.GlobalSettingKey == "SecurityKey")
            {
                _logger.LogDebug("Processing 'SecurityKey' global setting.");
                globalSettingModel.PasswordProtection = globalSettingModel.PasswordProtection.Length < 44 ? CryptographyHelper.Encrypt(globalSettingModel.PasswordProtection) + "$" + GetRandomString(10) : globalSettingModel.PasswordProtection;
            }
            else
            {
                _logger.LogDebug("Non-security setting, clearing PasswordProtection value on GlobalSetting.");
                globalSettingModel.PasswordProtection = "";
            }
            if (id.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateGlobalSettingCommand>(globalSettingModel);
                _logger.LogDebug($"Creating GlobalSetting '{createCommand.GlobalSettingKey}'.");
                result = await _dataProvider.GlobalSettings.CreateAsync(createCommand);
            }
            else
            {
                var updateCommand = _mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel);
                _logger.LogDebug($"Updating GlobalSetting '{updateCommand.GlobalSettingKey}'.");
                result = await _dataProvider.GlobalSettings.UpdateAsync(updateCommand);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in GlobalSetting.");
            return Json(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception on global setting page: {ex.ValidationErrors.FirstOrDefault()}");
            return (Json(new { success = false, message = ex.ValidationErrors.FirstOrDefault() }));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on global setting page while processing the request for create or update.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public string GetRandomString(int length)
    {
        _logger.LogDebug("Entering GetRandomString method in GlobalSetting");

        try
        {
            const string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            _logger.LogDebug($"Successfully generated random string of length '{length}' in GlobalSetting.");
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on global setting page while generating random string.",ex);
            return string.Empty;
        }
    }
}