﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetPaginated;

public class
    GetDataSyncJobPaginatedQueryHandler : IRequestHandler<GetDataSyncJobPaginatedQuery,
        PaginatedResult<DataSyncJobListVm>>
{
    private readonly IDataSyncJobRepository _dataSyncJobRepository;
    private readonly IMapper _mapper;

    public GetDataSyncJobPaginatedQueryHandler(IDataSyncJobRepository dataSyncJobRepository, IMapper mapper)
    {
        _dataSyncJobRepository = dataSyncJobRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<DataSyncJobListVm>> Handle(GetDataSyncJobPaginatedQuery request,
        CancellationToken cancellationToken)
    {
        var specification = new DataSyncJobFilterSpecification(request.SearchString);

        var queriable = await _dataSyncJobRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,specification, request.SortColumn, request.SortOrder);

        var list = _mapper.Map<PaginatedResult<DataSyncJobListVm>>(queriable);

        return list;
        //var queriable = _dataSyncJobRepository.GetPaginatedQuery();

        //var specification = new DataSyncJobFilterSpecification(request.SearchString);

        //var list = await queriable
        //    .Specify(specification)
        //    .Select(p => _mapper.Map<DataSyncJobListVm>(p))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return list;
    }
}