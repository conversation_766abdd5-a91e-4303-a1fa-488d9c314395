﻿namespace ContinuityPatrol.Application.Features.TableAccess.Queries.GetNames;

public class GetTableAccessNameQueryHandler : IRequestHandler<GetTableAccessNameQuery, List<TableAccessNameVm>>
{
    private readonly IMapper _mapper;

    private readonly ITableAccessRepository _tableAccessRepository;

    public GetTableAccessNameQueryHandler(IMapper mapper, ITableAccessRepository tableAccessRepository)
    {
        _mapper = mapper;

        _tableAccessRepository = tableAccessRepository;
    }

    public async Task<List<TableAccessNameVm>> Handle(GetTableAccessNameQuery request,
        CancellationToken cancellationToken)
    {
        var tableAccesses = (await _tableAccessRepository.GetTableAccessNames()).ToList();

        var tableAccessDto = _mapper.Map<List<TableAccessNameVm>>(tableAccesses);

        return tableAccessDto;
    }
}