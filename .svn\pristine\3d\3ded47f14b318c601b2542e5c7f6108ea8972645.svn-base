﻿namespace ContinuityPatrol.Application.Features.PluginManager.Queries.GetNameUnique;

public class GetPluginManagerNameUniqueQueryHandler : IRequestHandler<GetPluginManagerNameUniqueQuery, bool>
{
    private readonly IPluginManagerRepository _pluginManagerRepository;

    public GetPluginManagerNameUniqueQueryHandler(IPluginManagerRepository pluginManagerRepository)
    {
        _pluginManagerRepository = pluginManagerRepository;
    }

    public async Task<bool> Handle(GetPluginManagerNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _pluginManagerRepository.IsPluginNameExist(request.Name, request.PluginId);
    }
}