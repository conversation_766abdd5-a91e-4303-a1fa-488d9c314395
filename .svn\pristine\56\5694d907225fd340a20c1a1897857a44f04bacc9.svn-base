using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraObjectRepositoryTests : IClassFixture<InfraObjectFixture>
{
    private readonly InfraObjectFixture _infraObjectFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraObjectRepository _repository;
    private readonly InfraObjectRepository _repositoryNotParent;

    public InfraObjectRepositoryTests(InfraObjectFixture infraObjectFixture)
    {
        _infraObjectFixture = infraObjectFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new InfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new InfraObjectRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;

        // Act
        var result = await _repository.AddAsync(infraObject);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObject.Name, result.Name);
        Assert.Equal(infraObject.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.InfraObjects);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _repository.AddAsync(infraObject);

        infraObject.Name = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(infraObject);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedInfraObjectName", result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _repository.AddAsync(infraObject);

        // Act
        var result = await _repository.DeleteAsync(infraObject);

        // Assert
        Assert.Equal(infraObject.Name, result.Name);
        Assert.Empty(_dbContext.InfraObjects);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        var addedEntity = await _repository.AddAsync(infraObject);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        var addedEntity = await _repositoryNotParent.AddAsync(infraObject);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _repository.AddAsync(infraObject);

        // Act
        var result = await _repository.GetByReferenceIdAsync(infraObject.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObject.ReferenceId, result.ReferenceId);
        Assert.Equal(infraObject.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        await _repository.AddRangeAsync(infraObjects);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjects.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        await _repositoryNotParent.AddRangeAsync(infraObjects);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _repository.AddAsync(infraObject);

        // Act
        var result = await _repository.IsInfraObjectNameExist(InfraObjectFixture.InfraObjectName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsInfraObjectNameExist("NonExistentInfraObjectName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var infraObject = _infraObjectFixture.InfraObjectDto;
        await _repository.AddAsync(infraObject);

        // Act
        var result = await _repository.IsInfraObjectNameExist(infraObject.Name, infraObject.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetInfraObjectByBusinessServiceId Tests

    [Fact]
    public async Task GetInfraObjectByBusinessServiceId_ShouldReturnEntitiesWithMatchingBusinessServiceId()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        var businessServiceId = "BUSINESS_SERVICE_123";
        infraObjects.ForEach(x => x.BusinessServiceId = businessServiceId);
        await _repository.AddRangeAsync(infraObjects);

        // Act
        var result = await _repository.GetInfraObjectByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjects.Count, result.Count);
        Assert.All(result, x => Assert.Equal(businessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetInfraObjectByBusinessServiceId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        await _repository.AddRangeAsync(infraObjects);

        // Act
        var result = await _repository.GetInfraObjectByBusinessServiceId("non-existent-business-service-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectListByReplicationTypeId Tests

    [Fact]
    public async Task GetInfraObjectListByReplicationTypeId_ShouldReturnEntitiesWithMatchingReplicationTypeId()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        var replicationTypeId = "REPLICATION_TYPE_123";
        infraObjects.ForEach(x => x.ReplicationTypeId = replicationTypeId);
        await _repository.AddRangeAsync(infraObjects);

        // Act
        var result = await _repository.GetInfraObjectListByReplicationTypeId(replicationTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjects.Count, result.Count);
        Assert.All(result, x => Assert.Equal(replicationTypeId, x.ReplicationTypeId));
    }

    [Fact]
    public async Task GetInfraObjectListByReplicationTypeId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList;
        await _repository.AddRangeAsync(infraObjects);

        // Act
        var result = await _repository.GetInfraObjectListByReplicationTypeId("non-existent-replication-type-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var infraObjects = _infraObjectFixture.InfraObjectList.Take(3).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(infraObjects);
        var initialCount = infraObjects.Count;
        
        var toUpdate = infraObjects.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedInfraObjectName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = infraObjects.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Name == "UpdatedInfraObjectName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
