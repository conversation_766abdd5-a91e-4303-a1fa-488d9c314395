﻿using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;

public record GetRpForVmCGMonitorStatusPaginatedQuery:PaginatedBase, IRequest<(PaginatedResult<RpForVmCGMonitorStatusListVm>, Dictionary<string, int>)>
{
    public string InfraObjectId { get; set; }
    public string ConsistencyGroupName { get; set; }
    public string State { get; set; }
    public string AvailabilityStatus { get;set; }
}
