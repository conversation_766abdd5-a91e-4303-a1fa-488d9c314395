﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class IncidentDetailsControllerTests
    {
        [Fact]
        public void List_ReturnsViewResult()
        {
            // Arrange
            var controller = new IncidentDetailsController();

            // Act
            var result = controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }
    }
}
