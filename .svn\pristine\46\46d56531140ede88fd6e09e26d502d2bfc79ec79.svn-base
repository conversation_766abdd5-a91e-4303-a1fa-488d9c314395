using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordExpire.Commands;

public class UpdateAdPasswordExpireTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly Mock<IAdPasswordExpireRepository> _mockAdPasswordExpireRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateAdPasswordExpireCommandHandler _handler;

    public UpdateAdPasswordExpireTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;

        _mockAdPasswordExpireRepository = AdPasswordExpireRepositoryMocks.CreateUpdateAdPasswordExpireRepository(_adPasswordExpireFixture.AdPasswordExpires);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((source, destination, srcType, destType) =>
            {
                if (source is UpdateAdPasswordExpireCommand cmd && destination is Domain.Entities.AdPasswordExpire entity)
                {
                    entity.DomainServerId = cmd.DomainServerId;
                    entity.DomainServer = cmd.DomainServer;
                    entity.UserName = cmd.UserName;
                    entity.Email = cmd.Email;
                    entity.ServerList = cmd.ServerList;
                    entity.NotificationDays = cmd.NotificationDays;
                    entity.IsPassword = cmd.IsPassword;
                }
            });

        // Setup default repository behaviors
        //_mockAdPasswordExpireRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()))
        //    .Returns(Task.CompletedTask);

        _handler = new UpdateAdPasswordExpireCommandHandler(
            _mockMapper.Object,
            _mockAdPasswordExpireRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_UpdateAdPasswordExpireResponse_When_AdPasswordExpireUpdated()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand 
        { 
            Id = existingExpire.ReferenceId,
            UserName = "UpdatedUser"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(UpdateAdPasswordExpireResponse));
        result.Id.ShouldBe(existingExpire.ReferenceId);
        result.Message.ShouldContain("AdPasswordExpire");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockAdPasswordExpireRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AdPasswordExpire>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_PublishEvent_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<AdPasswordExpireUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand { Id = existingExpire.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map(It.IsAny<object>(), It.IsAny<object>(),
            It.IsAny<Type>(), It.IsAny<Type>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_AdPasswordExpireNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateAdPasswordExpireCommand { Id = nonExistentId };

        _mockAdPasswordExpireRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.AdPasswordExpire)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_PublishEventWithCorrectName_When_AdPasswordExpireUpdated()
    {
        // Arrange
        var existingExpire = _adPasswordExpireFixture.AdPasswordExpires.First();
        var command = new UpdateAdPasswordExpireCommand 
        { 
            Id = existingExpire.ReferenceId,
            UserName = "UpdatedUser"
        };

        AdPasswordExpireUpdatedEvent capturedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<AdPasswordExpireUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<AdPasswordExpireUpdatedEvent, CancellationToken>((evt, ct) => capturedEvent = evt)
            .Returns(Task.CompletedTask);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEvent.ShouldNotBeNull();
        capturedEvent.Name.ShouldBe(existingExpire.UserName);
    }
}
