﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class LicenseInfoService : BaseClient, ILicenseInfoService
{
    public LicenseInfoService(IConfiguration config, IAppCache cacheService, ILogger<LicenseInfoService> logger)
        : base(config, cacheService, logger)
    {
    }

    public async Task<List<LicenseInfoListVm>> GetLicenseInfo()
    {

        var request = new RestRequest("api/v6/licenseinfo");

        return await Get<List<LicenseInfoListVm>>(request);
    }

    public async Task<LicenseInfoDetailViewVm> GetLicenseInfoById(string licenseId)
    {
        var request = new RestRequest($"api/v6/licenseinfo/detailview?licenseId={licenseId}");

        return await Get<LicenseInfoDetailViewVm>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateLicenseInfoCommand createLicenseInfoCommand)
    {
        var request = new RestRequest("api/v6/licenseinfo", Method.Post);

        request.AddJsonBody(createLicenseInfoCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateLicenseInfoCommand updateLicenseInfoCommand)
    {
        var request = new RestRequest("api/v6/licenseinfo", Method.Put);

        request.AddJsonBody(updateLicenseInfoCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/licenseinfo/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<LicenseInfoDetailVm>> GetLicenseInfoByLicenseId(string licenseId)
    {
        var request = new RestRequest($"api/v6/licenseinfo/{licenseId}");

        return await Get<List<LicenseInfoDetailVm>>(request);
    }

    public async Task<AvailableCountVm> GetAvailableCountByLicenseId(string licenseId)
    {
        var request = new RestRequest($"api/v6/licenseinfo/availablecountby/{licenseId}");

        return await Get<AvailableCountVm>(request);
    }

    public async Task<List<LicenseInfoByBusinessServiceIdListVm>> GetLicenseByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/licenseinfo/businessserviceid?businessServiceId={businessServiceId}");

        return await Get<List<LicenseInfoByBusinessServiceIdListVm>>(request);
    }

    public async Task<PaginatedResult<LicenseInfoListVm>> GetPaginatedLicenseInfo(GetLicenseInfoPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/licenseinfo/paginated-list?Entity={query}");

        return await Get<PaginatedResult<LicenseInfoListVm>>(request);
    }

    public async Task<List<LicenseInfoByEntityListVm>> GetLicenseInfoByEntity(string licenseId, string entity,string? entityType)
    {
        var request = new RestRequest($"api/v6/licenseinfo/entity?licenseId={licenseId}&entity= {entity} &entityType ={entityType}");

        return await Get<List<LicenseInfoByEntityListVm>>(request);
    }

    public async Task<List<LicenseInfoTypeListVm>> GetLicenseInfoByType(string licenseId, string type,string? entityType)
    {
        var request = new RestRequest($"api/v6/licenseinfo/type?licenseId{licenseId} & type = {type} &entityType={entityType}");

        return await Get<List<LicenseInfoTypeListVm>>(request);
    }
}