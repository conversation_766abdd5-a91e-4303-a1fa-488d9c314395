namespace ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetNameUnique;

public class
    GetWorkflowActionFieldMasterNameUniqueQueryHandler : IRequestHandler<GetWorkflowActionFieldMasterNameUniqueQuery,
        bool>
{
    private readonly IWorkflowActionFieldMasterRepository _workflowActionFieldMasterRepository;

    public GetWorkflowActionFieldMasterNameUniqueQueryHandler(
        IWorkflowActionFieldMasterRepository workflowActionFieldMasterRepository)
    {
        _workflowActionFieldMasterRepository = workflowActionFieldMasterRepository;
    }

    public async Task<bool> Handle(GetWorkflowActionFieldMasterNameUniqueQuery request,
        CancellationToken cancellationToken)
    {
        return await _workflowActionFieldMasterRepository.IsNameExist(request.Name, request.Id);
    }
}