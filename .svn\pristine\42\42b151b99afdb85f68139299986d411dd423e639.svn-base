﻿using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Events.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionFieldMaster.Events
{
    public class DeleteWorkflowActionFieldMasterEventTests
    {
        private readonly Mock<ILogger<WorkflowActionFieldMasterDeletedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly WorkflowActionFieldMasterDeletedEventHandler _handler;

        public DeleteWorkflowActionFieldMasterEventTests()
        {
            _mockLogger = new Mock<ILogger<WorkflowActionFieldMasterDeletedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();
            _handler = new WorkflowActionFieldMasterDeletedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenWorkflowActionFieldMasterDeletedSuccessfully()
        {
            var deletedEvent = new WorkflowActionFieldMasterDeletedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            _mockUserService.Setup(us => us.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.LoginName).Returns("testuser");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/delete");
            _mockUserService.Setup(us => us.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockLogger.Verify(logger => logger.LogInformation(It.Is<string>(s => s.Contains("deleted successfully"))), Times.Once);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua => ua.ActivityDetails.Contains("deleted successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldRecordUserActivity_WhenWorkflowActionFieldMasterDeleted()
        {
            var deletedEvent = new WorkflowActionFieldMasterDeletedEvent
            {
                Name = "Test Workflow Action Field Master"
            };

            _mockUserService.Setup(us => us.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.LoginName).Returns("testuser");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/delete");
            _mockUserService.Setup(us => us.CompanyId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(us => us.IpAddress).Returns("***********");

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.Action == "Delete WorkflowActionFieldMaster" &&
                ua.Entity == "WorkflowActionFieldMaster" &&
                ua.ActivityDetails.Contains("deleted successfully") &&
                ua.UserId == _mockUserService.Object.UserId)), Times.Once);
        }
    }
}
