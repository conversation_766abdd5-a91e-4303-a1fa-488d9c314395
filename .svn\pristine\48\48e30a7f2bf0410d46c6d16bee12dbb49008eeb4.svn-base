﻿function monitorTypeMssqlDBMirroring(value, infraObjectName, moniterType, parsedData) {
    let prWfName = [], drWfName = [];
    let prStatusArr = [], drStatusArr = [];
    let prWfDisplay = '--', drWfDisplay = '--';
    let prStatusDisplay = '--', drStatusDisplay = '--';
    let iconWF = '', iconStatus = '';
    let monitor = value?.monitorServiceDetails;

    if (value?.monitorServiceDetails?.length > 0) {
        value?.monitorServiceDetails?.forEach(list => {
            let parsed = [];
            const isValidJson = list?.isServiceUpdate && Array.isArray(list?.isServiceUpdate)

            if (isValidJson) {
                try {
                    parsed = JSON?.parse(list?.isServiceUpdate);
                } catch (err) {
                    console.warn('Invalid JSON in isServiceUpdate:', list?.isServiceUpdate);
                    parsed = [];
                }
            }
            parsed?.forEach(entry => {
                entry?.Services?.forEach(service => {
                    if (entry?.Type?.toLowerCase() === 'pr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            prWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            prStatusArr.push(service?.Status);
                        }
                    } else if (entry?.Type?.toLowerCase() === 'dr') {
                        if (service?.ServiceName && service?.ServiceName !== 'NA') {
                            drWfName.push(service?.ServiceName);
                        }
                        if (service?.Status && service?.Status !== 'NA') {
                            drStatusArr.push(service?.Status);
                        }
                    }
                });
            });
        });

        // Unique workflow names
        prWfDisplay = prWfName?.length > 0 ? [...new Set(prWfName)].join(', ') : '--';
        drWfDisplay = drWfName?.length > 0 ? [...new Set(drWfName)].join(', ') : '--';

        // Status summary
        function getStatusSummary(arr) {
            let countMap = {};
            arr?.forEach(status => {
                countMap[status] = (countMap[status] || 0) + 1;
            });
            let total = arr?.length;
            let statusSummary = Object.entries(countMap)
                .map(([status, count]) => `${count} ${status}`)
                .join(', ');
            return statusSummary ? `${statusSummary} / ${total}` : '--';
        }

        prStatusDisplay = getStatusSummary(prStatusArr);
        drStatusDisplay = getStatusSummary(drStatusArr);

        iconWF = (prWfDisplay !== '--' || drWfDisplay !== '--') ? '<i class="text-primary cp-monitoring-services me-1 fs-6"></i>' : '';

        iconStatus = (prStatusDisplay !== '--' || drStatusDisplay !== '--') ? '<i class="text-primary cp-Job-status me-1 fs-6"></i>' : '';

    }
    const getDRDetails = (data, value, obj = null) => {
        
        let tdHtml = '';
        data?.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item[obj][value] : item[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData !== 0 ? tableData || 'NA' : tableData}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        
        let iconClass = '';
        
        if (value == 'ServerName') {
            iconClass = 'cp-stand-server text-primary'

        } else if (value === 'Server_IpAddress' || value === 'Server_HostName') {
            let text = monitoringData?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'DatabaseName') {
            
            iconClass = monitoringData?.sqlDBMirroring?.DatabaseName ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'Server_NetworkAddress') {
            
            iconClass = monitoringData?.Server_NetworkAddress || monitoringData?.Server_NetworkAddress === 0 ? "text-primary me-1 cp-fal-server" : "cp-disable me-1 text-danger";

        } else if (value === 'OpreationMode') {
            iconClass = monitoringData?.sqlDBMirroring?.OpreationMode ? "cp-cluster-database me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'DBRole') {
            iconClass = monitoringData?.sqlDBMirroring?.DBRole ? "cp-database-role me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'MirroringState') {
            iconClass = monitoringData?.sqlDBMirroring?.MirroringState ? "cp-refresh me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LogGenerateRate') {
            iconClass = monitoringData?.sqlDBMirroring?.LogGenerateRate || monitoringData?.sqlDBMirroring?.LogGenerateRate === 0 ? "text-primary cp-file-edits me-1" : "cp-disable me-1 text-danger";

        } else if (value === 'UnsentLog') {
            iconClass = monitoringData?.sqlDBMirroring?.UnsentLog || monitoringData?.sqlDBMirroring?.UnsentLog === 0 ? "text-warning cp-control-file-type me-1" : "cp-disable me-1 text-danger";

        } else if (value === 'SentRate') {
            iconClass = monitoringData?.sqlDBMirroring?.SentRate || monitoringData?.sqlDBMirroring?.SentRate === 0 ? "text-primary cp-file-edits me-1" : "cp-disable me-1 text-danger";

        } else if (value === 'UnrestoredLog') {
            iconClass = monitoringData?.sqlDBMirroring?.UnrestoredLog || monitoringData?.sqlDBMirroring?.UnrestoredLog === 0 ? "text-primary cp-datalog" : "cp-disable me-1 text-danger";

        } else if (value === 'RecoveryRate') {
            iconClass = monitoringData?.sqlDBMirroring?.RecoveryRate || monitoringData?.sqlDBMirroring?.RecoveryRate === 0? "text-success cp-success-rate" : "cp-disable me-1 text-danger";

        } else if (value === 'TransactionDelay') {
            iconClass = monitoringData?.sqlDBMirroring?.TransactionDelay || monitoringData?.sqlDBMirroring?.TransactionDelay === 0 ? "cp-time text-primary mt-2" : "cp-disable me-1 text-danger";

        } else if (value === 'TransactionPerSecond') {
            iconClass = monitoringData?.sqlDBMirroring?.TransactionPerSecond || monitoringData?.sqlDBMirroring?.TransactionPerSecond === 0 ? "cp-timer-meter me-1 text-primary" : "cp-disable me-1 text-danger";

        }

        return iconClass;
    }

    const getDynamicHeader = (MSSQLDBMirroringModel) => {

        let dynamicHeader = '';

        MSSQLDBMirroringModel?.length && MSSQLDBMirroringModel?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "mssqldbmirroring") {
        let repType = value?.replicationType ? 'cp-replication-type me-1 text-primary' : 'cp-disable me-1 text-danger'
        let rep = value?.replicationType !== null && value?.replicationType !== "" ? value?.replicationType : 'NA'
        let ipOrHostName;

        let prdatabase = parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PrDatabaseName ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger"
        let prmode = parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PROpreationMode ? "cp-cluster-database me-1 text-primary" : "cp-disable me-1 text-danger"
        let prrole = parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRDBRole ? "cp-database-role me-1 text-primary" : "cp-disable me-1 text-danger"
        let prnetwork = parsedData?.PrMSSQLDBMirroringModel?.PR_Server_NetworkAddress ? "text-primary me-1 cp-fal-server" : "cp-disable me-1 text-danger"
        
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let pripaddress = value?.prServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" : (value?.prServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";        
        let dripaddress = value?.drServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" : (value?.drServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";
        let ipprdata = parsedData?.PrMSSQLDBMirroringModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PrMSSQLDBMirroringModel?.PR_Server_HostName : parsedData?.PrMSSQLDBMirroringModel?.PR_Server_IpAddress
        let drdata = parsedData?.MSSQLDBMirroringModel?.map((ip) => ip?.connectViaHostName);

        parsedData?.MSSQLDBMirroringModel?.forEach((ip, index) => {
            
            let isHostName = drdata[index]?.toLowerCase() === "true";
            value = isHostName ? 'Server_HostName' : 'Server_IpAddress';
             ipOrHostName = isHostName
                ? getDRDetails(parsedData?.MSSQLDBMirroringModel, 'Server_HostName')
                : getDRDetails(parsedData?.MSSQLDBMirroringModel, 'Server_IpAddress');
        });
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MSSQLDBMirroringModel)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'Server Name' + '</td>' +
            '<td>' + '<i class="text-primary me-1 cp-server"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.PRServerName !== undefined && parsedData?.PrMSSQLDBMirroringModel?.PRServerName !== null && parsedData?.PrMSSQLDBMirroringModel?.PRServerName !== "" ? parsedData?.PrMSSQLDBMirroringModel?.PRServerName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'ServerName')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address/Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (ipprdata || 'NA') + '</td>' +         
             `${ipOrHostName}`+
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="' + prdatabase + '"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PrDatabaseName !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PrDatabaseName !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PrDatabaseName !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PrDatabaseName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'DatabaseName', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Server Network Address' + '</td>' +
            '<td>' + '<i class="' + prnetwork + '"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.PR_Server_NetworkAddress !== undefined && parsedData?.PrMSSQLDBMirroringModel?.PR_Server_NetworkAddress !== null && parsedData?.PrMSSQLDBMirroringModel?.PR_Server_NetworkAddress !== "" ? parsedData?.PrMSSQLDBMirroringModel?.PR_Server_NetworkAddress : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'Server_NetworkAddress',)}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Operation Mode" + '</td>' +
            '<td>' + '<i class="' + prmode + '"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PROpreationMode !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PROpreationMode !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PROpreationMode !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PROpreationMode : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'OpreationMode', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Role Of DB" + '</td>' +
            '<td>' + '<i class="' + prrole + '"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRDBRole !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRDBRole !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRDBRole !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRDBRole : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'DBRole', 'sqlDBMirroring')}` +
            '</tr>';
        if (Array.isArray(monitor) && monitor?.length > 0) {
            infraobjectdata +=
                '<tr id="prWorkflow">' +
                '<td>Monitoring workflow</td>' +
                '<td>' + iconWF + prWfDisplay + '</td>' +
                '<td>' + iconWF + drWfDisplay + '</td>' +
                '</tr>' +
                '<tr id="prStatus">' +
                '<td>Application Status</td>' +
                '<td>' + iconStatus + prStatusDisplay + '</td>' +
                '<td>' + iconStatus + drStatusDisplay + '</td>' +
                '</tr>';
        }
        infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value?.monitorServiceDetails);
        infraobjectdata += '</tbody>' +
           
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MSSQLDBMirroringModel)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "Replication Type" + '</td>' +
            '<td>' + '<i class="' + repType + '"></i>' + rep + '</td>' +
            /*'<td>' + '<i class="cp-replication-type me-1 text-primary"></i>' + (parsedData?.Monitor_Type !== undefined && parsedData?.Monitor_Type !== null && parsedData?.Monitor_Type !== "" ? parsedData?.Monitor_Type : 'NA') + '</td>' +*/
             '</tr>' +
            '<tr>' +
            '<td>' + "Monitoring State" + '</td>' +
            '<td>' + '<i class="cp-refresh me-1 text-primary"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRMirroringState !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRMirroringState !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRMirroringState !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRMirroringState : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'MirroringState', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Log Generate Rate (Kbytes/Sec)" + '</td>' +
            '<td>' + '<i class="text-primary cp-file-edits me-1"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRLogGenerateRate !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRLogGenerateRate !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRLogGenerateRate !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRLogGenerateRate : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'LogGenerateRate', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "UnSent Log (Kbytes)" + '</td>' +
            '<td>' + '<i class="text-warning cp-control-file-type me-1"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnsentLog !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnsentLog !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnsentLog !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnsentLog : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'UnsentLog', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Log Sent Rate (Kb/Sec)" + '</td>' +
            '<td>' + '<i class="text-primary cp-file-edits me-1"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRSentRate !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRSentRate !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRSentRate !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRSentRate : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'SentRate', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Unrestored Queue Log Value (Kbytes)" + '</td>' +
            '<td>' + '<i class="text-primary cp-datalog"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnrestoredLog !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnrestoredLog !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnrestoredLog !== "" ? parsedData ?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRUnrestoredLog : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'UnrestoredLog', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Log Recovery Rate (Kbytes/Sec)" + '</td>' +
            '<td>' + '<i class="text-success cp-success-rate"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRRecoveryRate !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRRecoveryRate !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRRecoveryRate !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRRecoveryRate : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'RecoveryRate', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Transaction Delay (Milisec)" + '</td>' +
            '<td>' + '<i class="cp-time me-1 text-primary"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionDelay !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionDelay !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionDelay !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionDelay : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'TransactionDelay', 'sqlDBMirroring')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Transaction PerSecond (Trans/Sec)" + '</td>' +
            '<td>' + '<i class="cp-timer-meter me-1 text-primary"></i>' + (parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionPerSecond !== undefined && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionPerSecond !== null && parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionPerSecond !== "" ? parsedData?.PrMSSQLDBMirroringModel?.sqlDBMirroringPr?.PRTransactionPerSecond : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MSSQLDBMirroringModel, 'TransactionPerSecond', 'sqlDBMirroring')}` +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>' 
            


        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
}
