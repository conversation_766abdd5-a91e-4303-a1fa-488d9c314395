﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BusinessFunction.Events.Delete;

public class BusinessFunctionDeletedEventHandler : INotificationHandler<BusinessFunctionDeletedEvent>
{
    private readonly ILogger<BusinessFunctionDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BusinessFunctionDeletedEventHandler(ILoggedInUserService userService,
        ILogger<BusinessFunctionDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(BusinessFunctionDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.OperationalFunction}",
            Entity = Modules.OperationalFunction.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"OperationalFunction '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"OperationalFunction '{deletedEvent.Name}' deleted successfully.");
    }
}