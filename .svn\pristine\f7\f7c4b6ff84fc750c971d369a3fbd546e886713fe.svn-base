﻿using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetWorkflowActionById;

public class GetWorkflowActionByIdQueryHandler : IRequestHandler<GetWorkflowActionByIdQuery, List<GetWorkflowActionByIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowRunningActionRepository _workflowRunningActionRepository;
    private readonly IDatabaseViewRepository _databaseViewRepository;
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IReplicationViewRepository _replicationViewRepository;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;

    public GetWorkflowActionByIdQueryHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IWorkflowActionResultRepository workflowActionResultRepository,
        IWorkflowExecutionTempRepository workflowExecutionTempRepository,
        IWorkflowRunningActionRepository workflowRunningActionRepository, IServerViewRepository serverViewRepository,
        IDatabaseViewRepository databaseViewRepository, IReplicationViewRepository replicationViewRepository, 
        IInfraObjectViewRepository infraObjectViewRepository)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
        _workflowRunningActionRepository = workflowRunningActionRepository;
        _serverViewRepository = serverViewRepository;
        _databaseViewRepository = databaseViewRepository;
        _replicationViewRepository = replicationViewRepository;
        _infraObjectViewRepository = infraObjectViewRepository;
    }

    public async Task<List<GetWorkflowActionByIdVm>> Handle(GetWorkflowActionByIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowRunning = await _workflowRunningActionRepository.GetWorkflowRunningActionsByOperationGroupId(request.WorkflowOperationGroupId);

        if (workflowRunning.Any())
        {
            var actionMap = _mapper.Map<List<GetWorkflowActionByIdVm>>(workflowRunning);

            var workflowRunningActions = await _workflowActionResultRepository.GetWorkflowActionResultByGroupId(request.WorkflowOperationGroupId);

            var actionResultDictionary = workflowRunningActions.DistinctBy(x=>x.StepId).ToDictionary(x => x.StepId);

            actionMap = actionMap.Select(jsonObject =>
            {
                if (actionResultDictionary.TryGetValue(jsonObject.StepId, out var actionResult))
                {
                    jsonObject.WorkflowOperationGroupId = actionResult.WorkflowOperationGroupId;
                    jsonObject.ActionId = actionResult.ReferenceId;
                    jsonObject.WorkflowActionName = actionResult.WorkflowActionName;
                    jsonObject.Status = actionResult.Status;
                    jsonObject.StartTime = actionResult.StartTime;
                    jsonObject.EndTime = actionResult.EndTime;
                    jsonObject.Message = actionResult.Message;
                }

                return jsonObject;
            }).ToList();

            return actionMap;
        }


        var workflow = await _workflowRepository.GetByReferenceIdAsync(request.WorkflowId);

        Guard.Against.NullOrDeactive(workflow, nameof(Domain.Entities.Workflow),
            new NotFoundException(nameof(Domain.Entities.Workflow), request.WorkflowId));

        var workflowActionResult =
            await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationGroupId(
                request.WorkflowOperationGroupId);

        var workflowActionList = new List<GetWorkflowActionByIdVm>();

        var customProperties = await _workflowExecutionTempRepository.GetByWorkflowIdAsync(request.WorkflowId);

        var properties = customProperties?.Properties ?? workflow.Properties;

        //var properties = workflowOperationGroup?.IsCustom == true
        //    ? (await _workflowExecutionTempRepository.GetByWorkflowIdAsync(request.WorkflowId))?.Properties ?? workflow.Properties
        //    : workflow.Properties;

        if (properties.IsNullOrWhiteSpace())
            throw new InvalidException("Workflow properties is empty");

        var deserializedNodes = JsonConvert.DeserializeObject<dynamic>(properties)?.SelectToken("nodes");

        if (deserializedNodes == null)
            return new List<GetWorkflowActionByIdVm>();

        foreach (var node in deserializedNodes)
        {
            await ProcessNode(node, workflowActionList, workflowActionResult);
        }

        var matchedStepServerMap = await ExtractMatchedMap("Server", workflowActionList);

        var serverIdList = ExtractDistinctIds(matchedStepServerMap);

        var serverListVm = await _serverViewRepository.GetServerTypeByIds(serverIdList);

        var serverDict = serverListVm.ToDictionary(s => s.ReferenceId, s => s);


        var matchedStepDatabaseMap = await ExtractMatchedMap("Database", workflowActionList);

        var databaseIdList = ExtractDistinctIds(matchedStepDatabaseMap);

        var databaseListVm = await _databaseViewRepository.GetByDatabaseIdsAsync(databaseIdList);

        var databaseDict = databaseListVm.ToDictionary(s => s.ReferenceId, s => s);


        var matchedStepReplicationMap = await ExtractMatchedMap("Replication", workflowActionList);

        var replicationIdList = ExtractDistinctIds(matchedStepReplicationMap);

        var replicationListVm = await _replicationViewRepository.GetByReplicationIdsAsync(replicationIdList);

        var replicationDict = replicationListVm.ToDictionary(s => s.ReferenceId, s => s);


        var matchedStepInfraObjectMap = await ExtractMatchedMap("InfraObject", workflowActionList);

        var infraObjectIdList = ExtractDistinctIds(matchedStepInfraObjectMap);

        var infraObjectListVm = await _infraObjectViewRepository.GetInfraStateByReferenceIds(infraObjectIdList);

        var infraObjectDict = infraObjectListVm.ToDictionary(s => s.ReferenceId, s => s);


        foreach (var action in workflowActionList)
        {
            var props = JObject.Parse(action.Properties);

           await UpdatePropertyValuesAsync(props, matchedStepServerMap, action.StepId, serverDict);
           await UpdatePropertyValuesAsync(props, matchedStepDatabaseMap, action.StepId, databaseDict);
           await UpdatePropertyValuesAsync(props, matchedStepReplicationMap, action.StepId, replicationDict);
           await UpdatePropertyValuesAsync(props, matchedStepInfraObjectMap, action.StepId, infraObjectDict);

            action.Properties = props.ToString(Formatting.None);
        }

        if (request.WorkflowOperationGroupId.IsNotNullOrWhiteSpace())
        {
            var actionMap = _mapper.Map<List<WorkflowRunningAction>>(workflowActionList);

            actionMap.ForEach(x => x.WorkflowId = request.WorkflowId);
            actionMap.ForEach(x => x.WorkflowOperationGroupId = request.WorkflowOperationGroupId);

            _ = await _workflowRunningActionRepository.AddRangeAsync(actionMap) as List<WorkflowRunningAction>;
        }

        return workflowActionList;
    }

    public async Task ProcessNode(dynamic node, List<GetWorkflowActionByIdVm> actionList, List<Domain.Entities.WorkflowActionResult> results, string groupId = null, string groupName = null)
    {
        // Create action
        var action = await CreateActionVm(node, groupId, groupName);

        // Apply result mapping
        var mappedAction = await ApplyResultMapping(action, results);
        if (!string.IsNullOrWhiteSpace(mappedAction.WorkflowActionName))
            actionList.Add(mappedAction);

        // Process children
        var children = node?.SelectToken("children");
        if (children != null)
        {
            foreach (var child in children)
            {
                await ProcessNode(child, actionList, results, mappedAction.GroupId, mappedAction.GroupName);
            }
        }

        // Process group actions
        var groupActions = node?.SelectToken("groupActions");
        if (groupActions != null)
        {
            foreach (var groupAction in groupActions)
            {
                var groupIdStr = node.SelectToken("groupId")?.ToString() ?? string.Empty;
                var groupNameStr = node.SelectToken("groupName")?.ToString() ?? string.Empty;

                await ProcessNode(groupAction, actionList, results, groupIdStr, groupNameStr);
            }
        }
    }

    public Task<GetWorkflowActionByIdVm> CreateActionVm(dynamic node, string groupId, string groupName)
    {
        var actionInfo = node?.SelectToken("actionInfo");

        var properties = actionInfo?.SelectToken("properties");

        var formInputToken = actionInfo?.SelectToken("formInput");

        string json = properties is not null 
            ? JsonConvert.SerializeObject(properties!)
            : string.Empty;

        string formJson = properties is not null
            ? JsonConvert.SerializeObject(formInputToken!)
            : string.Empty;

        var combined = new JObject
        {
            ["properties"] = json,
            ["formInput"] = formJson
        };

        var combinedJson = JsonConvert.SerializeObject(combined);


        var result = new GetWorkflowActionByIdVm
        {
            StepId = node!.SelectToken("stepId"),
            WorkflowActionName = actionInfo!.SelectToken("actionName"),
            Icon = actionInfo.SelectToken("icon"),
            IsParallel = actionInfo.SelectToken("IsParallel") ?? false,
            IsGroup = actionInfo.SelectToken("IsGroup") ?? false,
            IsCustom = actionInfo.SelectToken("isCustom") ?? false,
            GroupId = groupId,
            GroupName = groupName,
            Type = actionInfo.SelectToken("type"),
            Properties = combinedJson
        };

        return Task.FromResult(result);
    }

    public Task<GetWorkflowActionByIdVm> ApplyResultMapping(GetWorkflowActionByIdVm action, List<Domain.Entities.WorkflowActionResult> results)
    {
        var result = results.FirstOrDefault(r => r.StepId == action.StepId);
        if (result == null) return Task.FromResult(action);
        var mappedAction = _mapper.Map<GetWorkflowActionByIdVm>(result);
        mappedAction.Icon ??= action.Icon;
        mappedAction.IsParallel = action.IsParallel || mappedAction.IsParallel;
        mappedAction.IsGroup = action.IsGroup || mappedAction.IsGroup;
        mappedAction.IsCustom = action.IsCustom || mappedAction.IsCustom;
        mappedAction.GroupId = action.GroupId;
        mappedAction.GroupName = action.GroupName;
        mappedAction.Type = action.Type;
        return Task.FromResult(mappedAction);
    }


    private List<string> ExtractDistinctIds(Dictionary<string, List<string>> map) =>
        map.Values.SelectMany(x => x).Distinct().ToList();


    private  Task<Dictionary<string, List<string>>> ExtractMatchedMap(string type, List<GetWorkflowActionByIdVm> workflowActionList)
    {
       
            return Task.FromResult(workflowActionList
                .Where(x => x.Properties.IsNotNullOrWhiteSpace())
                .ToDictionary(
                    x => x.StepId,
                    x =>
                    {
                        var jObj = JObject.Parse(x.Properties);

                        var rawProperties = jObj["properties"]?.ToString();

                        if (string.IsNullOrWhiteSpace(rawProperties))
                            return new List<string>();

                        var innerProps = JObject.Parse(rawProperties);

                        return innerProps.Properties()
                            .Where(p => p.Name.Contains(type, StringComparison.OrdinalIgnoreCase))
                            .Select(p => p.Value.Type == JTokenType.String ? p.Value.ToString() : null)
                            .Where(v => v != null)
                            .ToList();
                    }));
    }


    private Task UpdatePropertyValuesAsync<T>(JObject props, Dictionary<string, List<string>> matchedMap, string stepId, Dictionary<string, T> lookupDict) where T : class
    {
        if (!matchedMap.TryGetValue(stepId, out var ids))
            return Task.CompletedTask;

        var rawProperties = props["properties"]?.ToString();
        var innerProps = JObject.Parse(rawProperties);
        if (innerProps == null)
            return Task.CompletedTask;

        foreach (var id in ids)
        {
            var prop = innerProps.Properties().FirstOrDefault(p => p.Value.ToString() == id);
            if (prop != null && lookupDict.TryGetValue(id, out var item))
            {
                var nameProp = item?.GetType().GetProperty("Name")?.GetValue(item)?.ToString();
                var ipAddressProp = item?.GetType().GetProperty("IpAddress")?.GetValue(item)?.ToString();
                var sidProp = item?.GetType().GetProperty("SID")?.GetValue(item)?.ToString();

                if (!string.IsNullOrEmpty(nameProp))
                    prop.Value = nameProp;

                if (!string.IsNullOrEmpty(ipAddressProp))
                    props[$"{prop.Name}IpAddress"] = ipAddressProp;

                if (!string.IsNullOrEmpty(sidProp))
                    props[$"{prop.Name}SID"] = sidProp;
            }
        }

        return Task.CompletedTask;
    }
}