﻿$(function () {
    var selectedValues = [];
    var dataTable = $('#tblmanageworkflow').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "columnDefs": [
                {
                    "targets": [ 2, 3],
                    "className": "truncate"
                }
            ],
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }


    );

  
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });
    $('#search-inp').on('keyup input', function () {
        $('input[type="checkbox"]').each(function () {
            if ($(this).is(':checked')) {
                var checkboxValue = $(this).val();
                var inputValue = $('#search-inp').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });
        dataTable.search($(this).val()).draw();
    });

});



