using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BulkImportActionResultRepositoryMocks
{
    public static Mock<IBulkImportActionResultRepository> CreateBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.AddAsync(It.IsAny<BulkImportActionResult>())).ReturnsAsync(
            (BulkImportActionResult bulkImportActionResult) =>
            {
                bulkImportActionResult.Id = new Fixture().Create<int>();
                bulkImportActionResult.ReferenceId = new Fixture().Create<Guid>().ToString();
                bulkImportActionResults.Add(bulkImportActionResult);
                return bulkImportActionResult;
            });

        mockBulkImportActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportActionResult>()));
            //.Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.DeleteAsync(It.IsAny<BulkImportActionResult>()));
            //.Returns(Task.CompletedTask);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByEntityIdAndBulkImportOperationId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string entityId, string operationId) => 
                bulkImportActionResults.FirstOrDefault(x => x.EntityId == entityId && x.BulkImportOperationId == operationId));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationId, string operationGroupId) => 
                bulkImportActionResults.Where(x => x.BulkImportOperationId == operationId && x.BulkImportOperationGroupId == operationGroupId).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdsAndOperationGroupIds(It.IsAny<List<string>>(), It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds, List<string> operationGroupIds) => 
                bulkImportActionResults.Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId)).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetActionByOperationGroupIdAndEntityType(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationGroupId, string entityType) => 
                bulkImportActionResults.FirstOrDefault(x => x.BulkImportOperationGroupId == operationGroupId && x.EntityType == entityType));

        return mockBulkImportActionResultRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateUpdateBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportActionResult>()));
           // .Returns(Task.CompletedTask);

        return mockBulkImportActionResultRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateDeleteBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BulkImportActionResult>()));
           // .Returns(Task.CompletedTask);

        return mockBulkImportActionResultRepository;
    }

    public static Mock<IBulkImportActionResultRepository> CreateQueryBulkImportActionResultRepository(List<BulkImportActionResult> bulkImportActionResults)
    {
        var mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();

        mockBulkImportActionResultRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(bulkImportActionResults);

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => bulkImportActionResults.FirstOrDefault(x => x.ReferenceId == id));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByEntityIdAndBulkImportOperationId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string entityId, string operationId) => 
                bulkImportActionResults.FirstOrDefault(x => x.EntityId == entityId && x.BulkImportOperationId == operationId));

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationId, string operationGroupId) => 
                bulkImportActionResults.Where(x => x.BulkImportOperationId == operationId && x.BulkImportOperationGroupId == operationGroupId).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetByOperationIdsAndOperationGroupIds(It.IsAny<List<string>>(), It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> operationIds, List<string> operationGroupIds) => 
                bulkImportActionResults.Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId)).ToList());

        mockBulkImportActionResultRepository.Setup(repo => repo.GetActionByOperationGroupIdAndEntityType(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string operationGroupId, string entityType) => 
                bulkImportActionResults.FirstOrDefault(x => x.BulkImportOperationGroupId == operationGroupId && x.EntityType == entityType));

        return mockBulkImportActionResultRepository;
    }

    public static Mock<IUserActivityRepository> CreateBulkImportActionResultEventRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        return mockUserActivityRepository.As<IUserActivityRepository>();
    }
}
