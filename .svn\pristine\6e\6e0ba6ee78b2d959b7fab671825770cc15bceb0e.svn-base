using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IEmployeeService
{
    Task<List<EmployeeListVm>> GetEmployeeList();
    Task<BaseResponse> CreateAsync(CreateEmployeeCommand createEmployeeCommand);
    Task<BaseResponse> UpdateAsync(UpdateEmployeeCommand updateEmployeeCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<EmployeeDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsEmployeeNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<EmployeeListVm>> GetPaginatedEmployees(GetEmployeePaginatedListQuery query);
    #endregion
}
