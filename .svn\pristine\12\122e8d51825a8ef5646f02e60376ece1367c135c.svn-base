using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDescriptionBulkImportStartAndEndTime;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetList;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetRunningList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;


//using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class BulkImportOperationService : BaseService,IBulkImportOperationService
{
    public BulkImportOperationService(IHttpContextAccessor accessor) : base(accessor)
    {
    }
    public async Task<CreateBulkImportValidatorCommandResponse> CreateBulkImportValidator(CreateBulkImportValidatorCommand createBulkImportValidatorCommand)
    {
        Logger.LogDebug($"Create BulkImportOperation '{createBulkImportValidatorCommand}'");

        return await Mediator.Send(createBulkImportValidatorCommand);
    }

    public async Task<List<BulkImportOperationListVm>> GetBulkImportOperationList()
    {
        Logger.LogDebug("Get All BulkImportOperations");

        return await Mediator.Send(new GetBulkImportOperationListQuery());
    }

    public async Task<BulkImportOperationDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportOperation Id");

        Logger.LogDebug($"Get BulkImportOperation Detail by Id '{id}'");

        return await Mediator.Send(new GetBulkImportOperationDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateBulkImportOperationCommand createBulkImportOperationCommand)
    {
        Logger.LogDebug($"Create BulkImportOperation '{createBulkImportOperationCommand}'");

        return await Mediator.Send(createBulkImportOperationCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBulkImportOperationCommand updateBulkImportOperationCommand)
    {
        Logger.LogDebug($"Update BulkImportOperation '{updateBulkImportOperationCommand}'");

        return await Mediator.Send(updateBulkImportOperationCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BulkImportOperation Id");

        Logger.LogDebug($"Delete BulkImportOperation Details by Id '{id}'");

        return await Mediator.Send(new DeleteBulkImportOperationCommand { Id = id });
    }

    public async  Task<List<BulkImportOperationRunningListVm>> GetBulkImportOperationsrunningStatus()
    {
        Logger.LogDebug($"Get BulkImportOperation RunningList");

        return await Mediator.Send(new GetBulkImportOperationRunningListQuery());
    }

    public async Task<List<GetDescriptionBulkImportStartAndEndTimeVm>> GetDescriptionBulkImportStartAndEndTime(string startDate, string endDate)
    {
        Logger.LogDebug($"Get Profile Executor By BusinessServiceId '{startDate}' and Id '{endDate}'");

        return await Mediator.Send(new GetDescriptionBulkImportStartAndEndTimeQuery { StartTime = startDate, EndTime = endDate });
    }


    #region NameExist
    // public async Task<bool> IsBulkImportOperationNameExist(string name, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(name, "BulkImportOperation Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by BulkImportOperation Name '{name}' and Id '{id}'");
    //
    //     return await Mediator.Send(new GetBulkImportOperationNameUniqueQuery { Name = name, Id = id });
    // }
    #endregion

    #region Paginated
    //public async Task<PaginatedResult<BulkImportOperationListVm>> GetPaginatedBulkImportOperations(GetBulkImportOperationPaginatedListQuery query)
    //{
    //    Logger.LogDebug("Get Searching Details in BulkImportOperation Paginated List");
    //
    //    return await Mediator.Send(query);
    //}
    #endregion
}
