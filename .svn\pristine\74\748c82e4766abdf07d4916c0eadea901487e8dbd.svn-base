﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetPaginatedList;

public class
    GetDRReadyLogPaginatedListQueryHandler : IRequestHandler<GetDRReadyLogPaginatedListQuery,
        PaginatedResult<DRReadyLogListVm>>
{
    private readonly IDrReadyLogRepository _dRReadyLogRepository;
    private readonly IMapper _mapper;

    public GetDRReadyLogPaginatedListQueryHandler(IMapper mapper, IDrReadyLogRepository dRReadyLogRepository)
    {
        _mapper = mapper;
        _dRReadyLogRepository = dRReadyLogRepository;
    }

    public async Task<PaginatedResult<DRReadyLogListVm>> Handle(GetDRReadyLogPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _dRReadyLogRepository.GetPaginatedQuery();

        var productFilterSpec = new DrReadyLogFilterSpecification(request.SearchString);

        var dRReadyLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<DRReadyLogListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return dRReadyLog;
    }
}