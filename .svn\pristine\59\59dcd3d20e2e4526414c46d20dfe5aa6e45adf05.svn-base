﻿namespace ContinuityPatrol.Application.Features.FormType.Queries.GetNames;

public class GetFormTypeNameQueryHandler : IRequestHandler<GetFormTypeNameQuery, List<FormTypeNameVm>>
{
    private readonly IFormTypeRepository _formTypeRepository;
    private readonly IMapper _mapper;

    public GetFormTypeNameQueryHandler(IMapper mapper, IFormTypeRepository formTypeRepository)
    {
        _mapper = mapper;
        _formTypeRepository = formTypeRepository;
    }

    public async Task<List<FormTypeNameVm>> Handle(GetFormTypeNameQuery request, CancellationToken cancellationToken)
    {
        var formType = await _formTypeRepository.GetFormTypeNames();

        return _mapper.Map<List<FormTypeNameVm>>(formType);
    }
}