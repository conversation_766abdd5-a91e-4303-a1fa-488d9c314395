﻿using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Shared.Core.Extensions;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class DashboardViewService : BaseClient, IDashboardViewService
{
    public DashboardViewService(IConfiguration config, IAppCache cache, ILogger<DashboardViewService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateDashboardViewCommand createDashboardViewCommand)
    {
        var request = new RestRequest("api/v6/dashboardview", Method.Post);

        request.AddJsonBody(createDashboardViewCommand);

        return await Post<BaseResponse>(request);
    }
    public async Task<List<DashboardViewNameVm>> GetDashboardNames()
    {
        var request = new RestRequest("api/v6/dashboardview/names");

        return await GetFromCache<List<DashboardViewNameVm>>(request, "GetDashboardNames");
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDashboardViewCommand updateDashboardViewCommand)
    {
        var request = new RestRequest("api/v6/dashboardview", Method.Put);

        request.AddJsonBody(updateDashboardViewCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/dashboardview/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<BusinessViewPaginatedList>> GetBusinessViews()
    {
        var request = new RestRequest($"/api/v6/dashboardview/business-views");

        return await Get<List<BusinessViewPaginatedList>>(request);
    }

    public async Task<List<DashboardViewListVm>> GetDashboardViews()
    {
        var request = new RestRequest($"api/v6/dashboardview");

        return await GetFromCache<List<DashboardViewListVm>>(request, "GetDashboardViews");
    }

    public async Task<DashboardViewDetailVm> GetDashboardViewById(string id)
    {
        var request = new RestRequest($"api/v6/dashboardview/{id}");

        return await Get<DashboardViewDetailVm>(request);
    }

    public async Task<ItViewByInfraObjectIdVm> GetITViewByInfraObjectId(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/dashboardview/{infraObjectId}");

        return await Get<ItViewByInfraObjectIdVm>(request);
    }

    public async Task<List<DashboardViewByBusinessServiceIdVm>> GetDashboardViewListByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/dashboardview/businessserviceid?businessServiceId={businessServiceId}");

        return await Get<List<DashboardViewByBusinessServiceIdVm>>(request);
    }

    public async Task<List<DashboardViewByBusinessFunctionIdVm>> GetDashboardViewListByBusinessFunctionId(string businessFunction)
    {
        var request = new RestRequest($"api/v6/dashboardview/businessfunctionid?businessFunction={businessFunction}");

        return await Get<List<DashboardViewByBusinessFunctionIdVm>>(request);
    }

    public async Task<GetDashboardViewByInfraObjectIdVm> GetDashboardViewListByInfraObjectId(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/dashboardview/infraobjectid?infraObjectId={infraObjectId}");

        return await Get<GetDashboardViewByInfraObjectIdVm>(request);
    }

    public async Task<List<GetDcMappingListVm>> GetDcMappingDetails(string? siteId)
    {
        var request = new RestRequest($"api/v6/dashboardview/dcmapping?siteId={siteId}");

        return await GetFromCache<List<GetDcMappingListVm>>(request, "GetDcMappingDetails");
    }

    public async Task<List<DataLagStatusbyLast7DaysVm>> GetDatalagStatusByLast7DaysList()
    {
        var request = new RestRequest($"api/v6/dashboardview/lastlist");

        return await GetFromCache<List<DataLagStatusbyLast7DaysVm>>(request, "GetDatalagStatusByLast7DaysList");
    }

    public async Task<List<ItViewByBusinessServiceIdVm>> GetItViewByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/dashboardview/it-view-businessserviceid?businessServiceId={businessServiceId}");

        return await Get<List<ItViewByBusinessServiceIdVm>>(request);
    }

    public async Task<List<GetItViewListVm>> GetItViewList()
    {
        var request = new RestRequest($"api/v6/dashboardview/it-view");

        return await GetFromCache<List<GetItViewListVm>>(request, "GetItViewList");
    }

    public async Task<GetByEntityIdVm> GetMonitorServiceStatusByIdAndType(string monitorId, string type)
    {
        var request = new RestRequest($"api/v6/dashboardview/by/monitorid?monitorId={monitorId}&type={type}");

        return await Get<GetByEntityIdVm>(request);
    }

    public async Task<List<GetServiceTopologyListVm>> GetBusinessServiceTopologyByBusinessServiceId(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/dashboardview/business-service-topology?businessServiceId={businessServiceId}");

        return await Get<List<GetServiceTopologyListVm>>(request);
    }

    public async Task<RTOByBusinessServiceIdVm> GetRTOByBusinessServiceId(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/rtos/businessserviceid?businessServiceId={businessServiceId}");

        return await Get<RTOByBusinessServiceIdVm>(request);
    }
    public async Task<DrReadyByBusinessServiceIdVm> GetDrReadyByBusinessServiceId(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadys/businessserviceid?businessServiceId={businessServiceId}");

        return await Get<DrReadyByBusinessServiceIdVm>(request);
    }
    public async Task<ImpactAvailabilityDetailVm> GetImpactAvailabilityByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/impactavailability/infraobjectid?businessServiceId={businessServiceId}");

        return await Get<ImpactAvailabilityDetailVm>(request);
    }
    public async Task<DataLagListVm> GetDataLagByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/datalags/bybusinessserviceid?businessServiceId={businessServiceId}");

        return await Get<DataLagListVm>(request);
    }
    public async Task<SitePropertiesByBusinessServiceIdVm> GetSitePropertiesByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/dashboardview/datacenter?businessServiceId={businessServiceId}");

        return await Get<SitePropertiesByBusinessServiceIdVm>(request);
    }

    public async Task<ResilienceHealthStatusDetailVm> GetResilienceHealthStatusByInfraObjectId(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/dashboardview/resilience-health-status?infraObjectId={infraObjectId}");

        return await Get<ResilienceHealthStatusDetailVm>(request);
    }



    public async Task<GetDcMappingSitesVm> GetDcMappingSiteDetails()
    {
        var request = new RestRequest($"api/v6/dashboardview/dcmapping-sites");

        return await Get<GetDcMappingSitesVm>(request);
    }

    // Operational Analytics

    public async Task<DrillAnalyticsDetailVm> GetDrillAnalytics()
    {
        var request = new RestRequest("api/v6/dashboardview/drill-analytic");

        return await Get<DrillAnalyticsDetailVm>(request);
    }

    public async Task<ComponentFailureAnalyticsDetailVm> GetComponentFailureAnalytics()
    {
        var request = new RestRequest("api/v6/dashboardview/component-failure-analytics");

        return await Get<ComponentFailureAnalyticsDetailVm>(request);
    }

    public async Task<GetSlaBreachListVm> GetSlaBreach()
    {
        var request = new RestRequest($"api/v6/dashboardview/sla-breach");

        return await Get<GetSlaBreachListVm>(request);
    }

    public async Task<GetOperationalAvailabilityAnalyticsDetailVm> GetOperationalAvailabilityAnalytics()
    {
        var request = new RestRequest($"api/v6/dashboardview/operational-availability-analytics");

        return await Get<GetOperationalAvailabilityAnalyticsDetailVm>(request);
    }

    public async Task<GetWorkflowAnalyticsDetailVm> GetWorkflowAnalytics()
    {
        var request = new RestRequest("api/v6/dashboardview/workflow-analytics");

        return await Get<GetWorkflowAnalyticsDetailVm>(request);
    }

    public async Task<List<OperationalHealthSummaryDetailVm>> GetOperationalServiceHealthSummary()
    {
        var request = new RestRequest($"api/v6/dashboardview/operational-service-health-summary");

        return await Get<List<OperationalHealthSummaryDetailVm>>(request);
    }

    public async Task<List<BusinessImpactAnalysisVm>> GetBusinessImpactAnalysisAsync()
    {
        var request = new RestRequest("api/v6/dashboardview/business-impact-analysis");

        return await Get<List<BusinessImpactAnalysisVm>>(request);
    }

    #region OneView

    public async Task<List<SiteCountListVm>> GetSiteCountList()
    {
        var request = new RestRequest("api/v6/dashboardview/one-view-sites");

        return await Get<List<SiteCountListVm>>(request);
    }

    public async Task<VerifyWorkflowDetailVm> GetVerifiedWorkflowList()
    {
        var request = new RestRequest("/api/v6/dashboardview/verified-workflow");

        return await Get<VerifyWorkflowDetailVm>(request);
    }

    public async Task<BreachDetailVm> GetBreachDetails()
    {
        var request = new RestRequest("/api/v6/dashboardview/rto-rpo-breach");

        return await Get<BreachDetailVm>(request);
    }

    public async Task<LastDrillDetailVm> GetLastDrillDetails()
    {
        var request = new RestRequest("/api/v6/dashboardview/last-drill");

        return await Get<LastDrillDetailVm>(request);
    }

    public async Task<List<OneViewEntitiesEventView>> GetOneViewEntitiesEventViewList()
    {
        var request = new RestRequest($"api/v6/OneViewEntitiesEvent");

        return await Get<List<OneViewEntitiesEventView>>(request);
    }    
    public async Task<TotalSiteDetailForOneViewListVm>GetTotalSiteDetailsForOneView(string? siteId, string? categorytype)
    {
        var apiRequets = $"siteid={siteId}&&categorytype={categorytype}";
        var request = (siteId.IsNotNullOrEmpty() && categorytype.IsNullOrEmpty()) ? new RestRequest($"api/v6/totalsite-for-oneview")
            : new RestRequest($"api/v6/totalsite-for-oneview/{apiRequets}");

        return await Get<TotalSiteDetailForOneViewListVm>(request);
    }
    public async Task<List<OneViewRiskMitigationCyberSecurityView>> GetOneViewRiskmitigationCyberSecurityList()
    {
        var request = new RestRequest($"api/v6/dashboardview/one-view-riskmitigation-cybersecurity");

        return await Get<List<OneViewRiskMitigationCyberSecurityView>>(request);
    }
    public async Task<List<OneViewRiskMitigationFailedDrillView>> GetOneViewRiskmitigationFailedDrillList()
    {
        var request = new RestRequest($"api/v6/dashboardview/one-view-riskmitigation-failed-drill");

        return await Get<List<OneViewRiskMitigationFailedDrillView>>(request);
    }

    #endregion

}