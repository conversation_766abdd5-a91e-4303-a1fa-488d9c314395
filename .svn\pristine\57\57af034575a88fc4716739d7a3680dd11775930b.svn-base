﻿

function AutomatonOrchestrationChart(data) {
    am4core.useTheme(am4themes_animated);

    var chart = am4core.create("AutomatonOrchestrationChart", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data
    chart.data = data

    // Set inner radius
    chart.innerRadius = am4core.percent(60);
    chart.padding(0, 0, 0, 0)
    // Add and configure Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;

    // This creates initial animation
    pieSeries.hiddenState.properties.opacity = 1;
    pieSeries.hiddenState.properties.endAngle = -90;
    pieSeries.hiddenState.properties.startAngle = -90;
    pieSeries.ticks.template.disabled = true;
    pieSeries.labels.template.disabled = true;
    pieSeries.colors.list = [
        am4core.color("#41c100"),
        am4core.color("#ff284d")
    ];
    // This creates initial animation
    pieSeries.hiddenState.properties.opacity = 1;
    pieSeries.hiddenState.properties.endAngle = -90;
    pieSeries.hiddenState.properties.startAngle = -90;

    let label2 = pieSeries.createChild(am4core.Label);
    label2.text = "Verified";
    label2.horizontalCenter = "middle";
    label2.verticalCenter = "middle";
    label2.fontSize = 13;

    // Add a legend
    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";

    var markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 12;
    markerTemplate.height = 12;


}

