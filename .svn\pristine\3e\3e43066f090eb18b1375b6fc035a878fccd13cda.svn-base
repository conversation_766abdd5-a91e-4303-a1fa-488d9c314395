﻿using ContinuityPatrol.Application.Features.UserRole.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Events;

public class CreateUserRoleEventTests : IClassFixture<UserRoleFixture>, IClassFixture<UserActivityFixture>
{
    private readonly UserRoleFixture _userRoleFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly UserRoleCreatedEventHandler _handler;

    public CreateUserRoleEventTests(UserRoleFixture userRoleFixture, UserActivityFixture userActivityFixture)
    {
        _userRoleFixture = userRoleFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockUserRoleEventLogger = new Mock<ILogger<UserRoleCreatedEventHandler>>();

        _mockUserActivityRepository = UserRoleRepositoryMocks.CreateUserRoleEventRepository(_userActivityFixture.UserActivities);

        _handler = new UserRoleCreatedEventHandler(mockLoggedInUserService.Object, mockUserRoleEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateUserRoleEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_userRoleFixture.UserRoleCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_userRoleFixture.UserRoleCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}