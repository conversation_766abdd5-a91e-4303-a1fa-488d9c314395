﻿using ContinuityPatrol.Domain.ViewModels.ServerModel;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;

public class TotalSiteDetailForOneViewListVm
{
   //public List<TotalSiteTypeDeatil> SiteTypeDeatils { get; set; }

    public List<ServerTypeWithListVm>  ServerTypeWithListVms { get; set; }

}

public class ServerTypeWithListVm
{
     public string RoleTypeId { get; set; }
    public string RoleType { get; set; }
    public int ServerUpCount { get; set; }
    public int ServerDownCount { get; set; }
    public List<ServerListVm> ServerListVms { get; set; }
}

//public class TotalSiteTypeDeatil
//{
//    public string SiteTypeId { get;set; }
//    public string SiteType {  get; set; }
//    public List<SiteListVm> SiteListVms { get; set; }
//}