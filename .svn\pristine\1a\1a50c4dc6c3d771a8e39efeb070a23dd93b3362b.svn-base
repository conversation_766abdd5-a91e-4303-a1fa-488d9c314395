﻿const nameExistUrl = "Admin/UserGroup/IsGroupNameExist";
var postactionurl = "Admin/UserGroup/UpdateList";
var cnt = 0;
var GroupName_update = "";
let Userlist = [];
let usergroupData = '';
let UsersNameList = [];
let userGroupId = '';
const exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

var createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
var deletePermission = $("#AdminDelete").data("delete-permission").toLowerCase();

if (createPermission == 'false') {
    $(".btn-Usergroup-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}
$(function () {

    var selectedValues = [];
    var dataTable = $('#tblusrgroup').DataTable(
        {

            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],

            "ajax": {
                "type": "GET",
                "url": "/Admin/UserGroup/GetPaginationList",
                "dataType": "json",
                "async": true,
                "data": function (d) {
                    
                    let sortIndex = (d?.order && d?.order?.length > 0) ? d?.order[0].column : '';
                    let sortValue = sortIndex === 1 ? "groupName" : sortIndex === 2 ? "groupDescription"  :
                       sortIndex === 3 ? "status" : "";
                    let orderValue = (d?.order && d?.order?.length > 0)? d.order[0]?.dir : 'asc';
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                        /*  $(".TableThead").addClass("d-none")*/

                    }
                    else {
                        $(".pagination-column").removeClass("disabled")

                    }
                    return json?.data;
                },
                "error": function (xhr, status, error) {
                    if (error?.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },

            },

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },

                },
                {
                    data: "groupName",
                    name: 'Group Name',
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "groupDescription", "name": "Group Description", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span title='${data ?? 'NA'}'>${data ?? 'NA'} </span>`
                        }
                        return data;
                    }
                },

                {
                    "data": "userProperties", "name": "userProperties", "autoWidth": true,
                    "render": function (data, type, row) {

                        let optionValues = JSON.parse(data)
                        let tableNames = optionValues?.length ? optionValues.map(o => o.loginname).join() : 'NA'
                        if (type === 'display') {
                            return '<span title="' + tableNames + '">' + tableNames + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            const isParent = row.isParent;
                            return `<div class="d-flex align-items-center  gap-2">                                       
                                <span role="button" title="Edit"  class="edit-button" data-usergroup='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title=""  opacity:0.50;" class="delete-button ">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                    ` <span role="button" title="Delete" class="delete-button" data-usergroup-id="${row.id}" data-usergroup-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`
                                }
                            </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit-button" data-usergroup='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                       
                                            
                       
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                  
                                            
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>



                                <span role="button" title="Delete" class="delete-button" data-usergroup-id="${row.id}" data-usergroup-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
            columnDefs: [           
                {
                    "targets": [1, 2, 3,],
                    "className": "truncate"
                },

            ],
            error: function (xhr, error, code) {
                console.log(xhr, code, error);
            },
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const nameCheckbox = $("#groupname");
        const userCheckbox = $("#User");
        const inputValue = $('#search-inp').val();
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + inputValue);
        }
        if (userCheckbox.is(':checked')) {
            selectedValues.push(userCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (e.target.value && json?.recordsFiltered === 0) {

                $('.dataTables_empty').text('No matching records found');

            }
        })
    }, 500))
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });


    const getUserList = async (dataArray) => {
        await $.ajax({
            type: "GET",
            url: RootUrl + "Admin/UserGroup/GetUserNames",
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (result?.data && Array.isArray(result.data) && result?.data?.length > 0) {
                        userList = result.data;
                        $('#UsersList').empty();
                        let html = '';
                        userList.map((item) => {
                            html += '<option value="' + item.id + '" data-id="' + item.id + '" data-name="' + item.loginName + '">' + item.loginName + '</option>';
                        });
                        $('#UsersList').append(html);
                    }
                    if (dataArray?.length > 0) {

                        $('#UsersList').val(dataArray).trigger('change');
                    }
                } else {
                    errorNotification(result);
                    $('#UsersList').append('<option value="No Data"></option>');
                }
            },
        });
    };

    $('#tblusrgroup').on('click', '.delete-button', function () {
        var usergroupId = $(this).data('usergroup-id');
        var usergroupName = $(this).data('usergroup-name');
        $("#deleteData").attr("title", usergroupName);
        $('#deleteData').text(usergroupName);
        $('#textDeleteId').val(usergroupId);
    });


    $('#tblusrgroup').on('click', '.edit-button', function () {
        usergroupData = $(this).data('usergroup');
        populateusergroupModalFields(usergroupData);
        $('#SaveUserGroup').text('Update')
        $('#CreateModal').modal('show');
    });

    $('#textgroupName').on('input', commonDebounce(async function () {
        userGroupId = $('#textgroupId').val();
        const value = $(this).val();
        var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateNames(value, userGroupId, nameExistUrl);
    }, 400));


    $('#txtGroupDescription').on('input', async function (event) {
        const value = $(this).val();
        const sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        if (exceptThisSymbol.includes(event.key)) {
            event.preventDefault();
        }
        let errorElement = $('#grpdescription-error');
        await validateDescription(sanitizedValue, "Should not allow more than 250 characters", errorElement);
    });

    $("#UsersList").on('change', function () {
        UsersNameList = [];
        const value = $(this).val();
        const selectedOptions = $(this).find('option:selected');
        selectedOptions.each(function () {
            let id = $(this).data('id');
            let name = $(this).data('name');
            UsersNameList.push({ id: id, loginname: name });
        });
        validateDropDown(value, 'Select users', 'username-error');
    });


    $("#SaveUserGroup").on('click', async function () {
        let form = $("#CreateForm");
        let name = $('#textgroupName').val();
        elementDescription = $("#txtGroupDescription").val()
        errorElement = $('#grpdescription-error');
        let isDescription = await validateDescription(elementDescription, "Should not allow more than 250 characters", errorElement);
        let user = $('#UsersList').val();
        let IsName = await validateNames(name, userGroupId, nameExistUrl);
        let IsUser = await validateDropDown(user, 'Select users', 'username-error');
        let userGroupSanitizeArray = ['textgroupName', 'txtGroupDescription', 'textgroupId', 'datroperties']
        sanitizeContainer(userGroupSanitizeArray)
        setTimeout(() => {
            if (IsName && isDescription && IsUser) {
                let s = JSON.stringify(UsersNameList);
                $('#datroperties').val(s)
                form.trigger('submit');
            }
        }, 200)
        //if (IsName && isDescription && IsUser) {
        //    let s = JSON.stringify(UsersNameList);     
        //    $('#datroperties').val(s)       
        //    form.trigger('submit');
        //}

    });

    async function validateNames(value, id = null) {

        const errorElement = $('#grpname-error');
        if (!value) {
            errorElement.text('Enter group name ')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + nameExistUrl;
        var data = {};
        data.Groupname = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsGroupNameExist(url, data, OnError)
        ];

        return await CommonValidation(errorElement, validationResults);
    }

    async function IsGroupNameExist(url, data, errorFunc) {

        return !data.Groupname.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    async function validateDescription(value, errorMessage) {
        const errorElement = $('#grpdescription-error');
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        if (!value) {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
            return true;

        } else if (value.length < 0) {
            errorElement.text(errorMessage);
            errorElement.addClass('field-validation-error');
            return false;
        } else if (value.length > 250) {
            errorElement.text(errorMessage);
            errorElement.addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('');
            errorElement.removeClass('field-validation-error');
            return true;
        }
    }

    function validateDropDown(value, errormessage, errorElements) {
        if (value.length === 0) {
            $('#' + errorElements).text(errormessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElements).text('').removeClass('field-validation-error');
            return true;
        }
    }

    $('#Create').on('click', async function () {
        getUserList()
        clearInputs();

    });

    function clearInputs() {
        const errorElements = ['#username-error', '#grpdescription-error', '#grpname-error'];
        clearInputFields('usergroupData', errorElements);
        $('#SaveUserGroup').text('Save');
        $('#UsersList,#textgroupName,#txtGroupDescription').val('');
        $('#textgroupName').text('').removeClass('field-validation-error');
        $('#txtGroupDescription').text('').removeClass('field-validation-error');
        $('#UsersList').text('').removeClass('field-validation-error');

    }

    $('#Cancel').on('click', async function () {
        clearInputs();
    })

    function populateusergroupModalFields(usergroupData) {

        let userArray = []
        let userPropertie = JSON.parse(usergroupData.userProperties);
        for (let i = 0; i < userPropertie.length; i++) {
            userArray.push(userPropertie[i].id);
        }
        getUserList(userArray)
        $('#textgroupName').val(usergroupData.groupName);
        $('#txtGroupDescription').val(usergroupData.groupDescription);
        $('#textgroupId').val(usergroupData.id);

        userGroupId = usergroupData.id
        var errorElement = ['#grpname-error', '#username-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }


});

