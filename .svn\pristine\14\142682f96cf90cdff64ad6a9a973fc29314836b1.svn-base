﻿using ContinuityPatrol.Application.Features.WorkflowAction.Events.ImportWorkflowAction;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Events;

public class ImportWorkflowActionEventHandlerTests : IClassFixture<WorkflowActionFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ImportWorkflowActionEventHandler _handler;

    public ImportWorkflowActionEventHandlerTests(WorkflowActionFixture workflowActionFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockImportWorkflowActionEventLogger = new Mock<ILogger<ImportWorkflowActionEventHandler>>();

        _mockUserActivityRepository = WorkflowActionRepositoryMocks.CreateWorkflowActionEventRepository(_workflowActionFixture.UserActivities);

        _handler = new ImportWorkflowActionEventHandler(mockLoggedInUserService.Object, mockImportWorkflowActionEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_ImportWorkflowActionEvent()
    {
        _workflowActionFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionFixture.ImportWorkflowActionEvent, CancellationToken.None);

        result.Equals(_workflowActionFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowActionFixture.ImportWorkflowActionEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}