using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class BackUpLogService : BaseClient, IBackUpLogService
{
    public BackUpLogService(IConfiguration config, IAppCache cache, ILogger<BackUpLogService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<BackUpLogListVm>> GetBackUpLogList()
    {
        var request = new RestRequest("api/v6/backuplogs");

        return await GetFromCache<List<BackUpLogListVm>>(request, "GetBackUpLogList");
    }

    public async Task<BaseResponse> CreateAsync(CreateBackUpLogCommand createBackUpLogCommand)
    {
        var request = new RestRequest("api/v6/backuplogs", Method.Post);

        request.AddJsonBody(createBackUpLogCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBackUpLogCommand updateBackUpLogCommand)
    {
        var request = new RestRequest("api/v6/backuplogs", Method.Put);

        request.AddJsonBody(updateBackUpLogCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/backuplogs/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BackUpLogDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/backuplogs/{id}");

        return await Get<BackUpLogDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsBackUpLogNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/backuplogs/name-exist?backuplogName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<BackUpLogListVm>> GetPaginatedBackUpLogs(GetBackUpLogPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/backuplogs/paginated-list");

      return await Get<PaginatedResult<BackUpLogListVm>>(request);
  }
   #endregion
}
