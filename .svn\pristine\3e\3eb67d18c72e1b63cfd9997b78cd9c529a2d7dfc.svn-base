﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;

public class GetHeatMapStatusByInfraObjectandEntityIdQueryHandler : IRequestHandler<
    GetHeatMapStatusByInfraObjectandEntityIdQuery, HeatMapStatusByInfraObjectandEntityIdVm>
{
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IMapper _mapper;

    public GetHeatMapStatusByInfraObjectandEntityIdQueryHandler(IHeatMapStatusRepository heatMapStatusRepository,
        IMapper mapper)
    {
        _heatMapStatusRepository = heatMapStatusRepository;
        _mapper = mapper;
    }

    public async Task<HeatMapStatusByInfraObjectandEntityIdVm> Handle(
        GetHeatMapStatusByInfraObjectandEntityIdQuery request, CancellationToken cancellationToken)
    {
        var heatMapStatus =
            await _heatMapStatusRepository.GetHeatMapDetailByInfraObjectAndEntityId(request.InfraObjectId,
                request.EntityId);

        Guard.Against.NullOrDeactive(heatMapStatus, nameof(Domain.Entities.HeatMapStatus),
            new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.EntityId));

        var heatMapStatusDetailDto = _mapper.Map<HeatMapStatusByInfraObjectandEntityIdVm>(heatMapStatus);

        return heatMapStatusDetailDto ??
               throw new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.EntityId);
    }
}