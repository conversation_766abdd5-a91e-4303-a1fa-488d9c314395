using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MSSQLMonitorStatusRepositoryTests : IClassFixture<MSSQLMonitorStatusFixture>
{
    private readonly MSSQLMonitorStatusFixture _mssqlMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MSSQLMonitorStatusRepository _repository;

    public MSSQLMonitorStatusRepositoryTests(MSSQLMonitorStatusFixture mssqlMonitorStatusFixture)
    {
        _mssqlMonitorStatusFixture = mssqlMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new MSSQLMonitorStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MssqlMonitorStatuses.RemoveRange(_dbContext.MssqlMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

   

    #region GetDetailByType Tests

    //[Fact]
    //public async Task GetDetailByType_ShouldReturnActiveStatuses_WhenTypeExists()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var testType = "TestType";
    //    var statuses = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithMixedActiveStatus(testType);
        
    //    await _dbContext.MssqlMonitorStatuses.AddRangeAsync(statuses);
    //    await _dbContext.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.GetDetailByType(testType);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(2, result.Count); // Only active statuses should be returned
    //    Assert.All(result, status => Assert.True(status.IsActive));
    //    Assert.All(result, status => Assert.Equal(testType, status.Type));
    //}

    //[Fact]
    //public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act
    //    var result = await _repository.GetDetailByType("NonExistentType");

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Empty(result);
    //}

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var activeStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: testType, isActive: true);
        var inactiveStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: testType, isActive: false);
        
        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(new[] { activeStatus, inactiveStatus });
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal(testType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithWhitespace();
        whitespaceStatus.IsActive = true;
        await _dbContext.MssqlMonitorStatuses.AddAsync(whitespaceStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithLongType(1000);
        longTypeStatus.IsActive = true;
        await _dbContext.MssqlMonitorStatuses.AddAsync(longTypeStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = MSSQLMonitorStatusFixture.TestData.SpecialCharacterTypes;
        var statuses = new List<MSSQLMonitorStatus>();

        foreach (var type in specialTypes)
        {
            var status = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: type, isActive: true);
            statuses.Add(status);
        }

        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnMultipleStatuses_WhenMultipleActiveStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _mssqlMonitorStatusFixture.CreateMultipleMSSQLMonitorStatusWithSameType(testType, 5);
        
        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count);
        Assert.All(result, status => Assert.Equal(testType, status.Type));
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseType = "testtype";
        var upperCaseType = "TESTTYPE";
        
        var lowerCaseStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: lowerCaseType, isActive: true);
        var upperCaseStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: upperCaseType, isActive: true);
        
        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetDetailByType(lowerCaseType);
        var upperResult = await _repository.GetDetailByType(upperCaseType);

        // Assert
        Assert.Single(lowerResult);
        Assert.Single(upperResult);
        Assert.Equal(lowerCaseType, lowerResult.First().Type);
        Assert.Equal(upperCaseType, upperResult.First().Type);
    }

    #endregion

    #region GetMssqlMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        await _dbContext.MssqlMonitorStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync("9a73e815-ab81-4d54-a468-72b4b9632866");

        // Assert
        Assert.Null(result);
    }

   

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status1 = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);
        var status2 = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        // Add status1 first, then status2
        await _dbContext.MssqlMonitorStatuses.AddAsync(status1);
        _dbContext.SaveChanges();
        await _dbContext.MssqlMonitorStatuses.AddAsync(status2);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one added (FirstOrDefaultAsync behavior)
        Assert.Equal(status1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldReturnActiveAndInactiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var inactiveStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(
            infraObjectId: infraObjectId,
            isActive: false);

        await _dbContext.MssqlMonitorStatuses.AddAsync(inactiveStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.True(result.IsActive); // Should return inactive status as well
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldHandleValidGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var validGuids = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString("D"),
            Guid.NewGuid().ToString("N"),
            Guid.NewGuid().ToString("B"),
            Guid.NewGuid().ToString("P")
        };

        var statuses = new List<MSSQLMonitorStatus>();
        foreach (var guid in validGuids)
        {
            var status = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(guid);
            statuses.Add(status);
        }

        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var guid in validGuids)
        {
            var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(guid);
            Assert.NotNull(result);
            Assert.Equal(guid, result.InfraObjectId);
        }
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseGuid = Guid.NewGuid().ToString().ToLower();
        var upperCaseGuid = lowerCaseGuid.ToUpper();

        var lowerCaseStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(lowerCaseGuid);
        var upperCaseStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(upperCaseGuid);

        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(lowerCaseGuid);
        var upperResult = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(upperCaseGuid);

        // Assert
        Assert.NotNull(lowerResult);
        Assert.NotNull(upperResult);
        Assert.Equal(lowerCaseGuid, lowerResult.InfraObjectId);
        Assert.Equal(upperCaseGuid, upperResult.InfraObjectId);
        Assert.NotEqual(lowerResult.ReferenceId, upperResult.ReferenceId);
    }

    #endregion

   

   

    #region Edge Case Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var status = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties();
            tasks.Add(_repository.AddAsync(status));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Equal(10, allStatuses.Count);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeType = "测试类型_тест_テスト";
        var status = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: unicodeType, isActive: true);
        await _dbContext.MssqlMonitorStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(unicodeType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(unicodeType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldUseEqualsMethod()
    {
        // Arrange
        await ClearDatabase();
        var exactType = "ExactType";
        var similarType = "ExactTypeExtra"; // Contains the exact type but is longer

        var exactStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: exactType, isActive: true);
        var similarStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithProperties(type: similarType, isActive: true);

        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(new[] { exactStatus, similarStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(exactType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should only return exact match, not partial match
        Assert.Equal(exactType, result[0].Type);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_ShouldUseEqualsMethod()
    {
        // Arrange
        await ClearDatabase();
        var exactInfraObjectId = "12345678-1234-1234-1234-123456789012";
        var similarInfraObjectId = "12345678-1234-1234-1234-123456789013"; // Similar but different

        var exactStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(exactInfraObjectId);
        var similarStatus = _mssqlMonitorStatusFixture.CreateMSSQLMonitorStatusWithSpecificInfraObjectId(similarInfraObjectId);

        await _dbContext.MssqlMonitorStatuses.AddRangeAsync(new[] { exactStatus, similarStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMssqlMonitorStatusByInfraObjectIdAsync(exactInfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(exactInfraObjectId, result.InfraObjectId);
        Assert.Equal(exactStatus.ReferenceId, result.ReferenceId);
    }

    #endregion
}
