﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">

    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span title="Open Shift">Open Shift Detail Monitoring: <span id="infraName"></span></span></h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row g-2">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Cluster Monitoring">Cluster Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table  mb-0 align-middle">
                            <thead class="position-sticky top-0">
                                <tr>

                                    <th title="Cluster Monitoring">Cluster Monitoring</th>
                                    <th  title="Status">Status</th>
                                
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <i class="cp-ip-address text-secondary me-1"></i>Openshift Ip Address/Hostname
                                    </td>
                                    <td id="OpenShiftClusterFQDN">
                                       
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-cluster-database text-secondary me-1"></i>Cluster Resource Name
                                    </td>
                                    <td id="ClusterResourceName">
                                       
                                    </td>
                                </tr>
                                @* <tr>
                                    <td>
                                        <i class="cp-control-file-type text-secondary me-1"></i>Control Plane Type
                                    </td>
                                    <td>
                                        <i class="cp-activity-type text-success me-1"></i> local-cluster
                                    </td>
                                </tr> *@
                                <tr>
                                    <td>
                                        <i class="cp-ohas-status text-secondary me-1"></i>Status
                                    </td>
                                    <td id="ProjectsStatus">
                                       @* <i class="cp-success text-success me-1"></i>Ready*@
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-infra-replication-mapping text-secondary me-1"></i>Infrastructure
                                    </td>
                                    <td id="ClusterInfrastructure">
                                        
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-version text-secondary me-1"></i>Distribution Version
                                    </td>
                                    <td id="DistributionVersion">
                                     
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-api text-secondary me-1"></i>Cluster API address
                                    </td>
                                    <td id="ClusterAPIAddress">
                                       
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-folder-file text-secondary me-1"></i>Projects(Namespace) Name
                                    </td>
                                    <td id="ProjectsName">
                                      
                                    </td>
                                </tr>
                                @*<tr>
                                    <td>
                                        <i class="cp-folder-file text-secondary me-1"></i>Projects(Namespace) Name
                                    </td>
                                    <td>
                                        <i class="text-success cp-active-inactive me-1"></i>  Active
                                    </td>
                                </tr>*@
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2" id="virtual">
                    <div class="card-header card-title" title="Virtual Machine Monitoring">Virtual Machine Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table  mb-0 align-middle">
                            <thead class="position-sticky top-0">
                                <tr>

                                    <th title="Name">Name</th>
                                    <th title="Status">Status</th>

                                </tr>
                            </thead>
                            <tbody id="virtualData">
                               
                            </tbody>
                        </table>

                    </div>
                </div>
                <div class="card Card_Design_None mb-0" id="Machine">
                    <div class="card-header card-title" title="MachineSet Monitoring">MachineSet Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table  mb-0 align-middle">
                            <thead class="position-sticky top-0">
                                <tr>

                                    <th title="MachineSet Name">MachineSet Name</th>
                                    <th title="Machine count">Machine count</th>

                                </tr>
                            </thead>
                            <tbody id="MachineSet">
                              
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
            <div class="col-12 d-grid" id="Workload">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Database Details">Workload Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table table-bordered mb-2 align-middle" style="table-layout:fixed" id="stateful">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th></th>
                                    <th title="Name">Name</th>
                                    <th class="text-primary" title="Pod Names">Pod Names</th>
                                    <th class="text-info" title="Pod Names Ready Status (Pod count)">Pod Names Ready Status (Pod count)</th>
                                </tr>
                            </thead>
                            <tbody id="statefulSets">
                              @*  <tr>
                                    <td rowspan="2">
                                        StatefulSets Pods
                                    </td>
                                    <td rowspan="2">
                                        nginxstatefulset
                                    </td>
                                    <td>nginxstatefulset-0</td>
                                    <td>2/2</td>
                                </tr>
                                <tr>
                                    <td>nginxstatefulset-0</td>
                                    <td>2/2</td>
                                </tr>*@
                            </tbody>
                        </table>
                        <table class="table table-bordered mb-2 align-middle" style="table-layout:fixed" id="Replica">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th></th>
                                    <th title="Name">Name</th>
                                    <th class="text-primary" title="Pod Names">Pod Names</th>
                                    <th class="text-info" title="Pod Names Ready Status (Pod count)">Pod Names Ready Status (Pod count)</th>
                                </tr>
                            </thead>
                            <tbody id="ReplicaSet">
                               @* <tr>
                                    <td rowspan="2">
                                        ReplicaSets Pods
                                    </td>
                                    <td rowspan="2">
                                        nginxstatefulset
                                    </td>
                                    <td>multi-nginxreplicaset-79lff</td>
                                    <td>2/2</td>
                                </tr>
                                <tr>
                                    <td>multi-nginxreplicaset-zvsdv</td>
                                    <td>2/2</td>
                                </tr>*@
                            </tbody>
                        </table>
                        <table class="table table-bordered mb-2 align-middle" style="table-layout:fixed" id="podset">
                            <thead class="position-sticky top-0">
                                <tr>
                                    <th></th>
                                    <th title="Name">Name</th>
                                    <th class="text-primary" title="Pod Names">Pod Names</th>
                                    <th class="text-info" title="Pod Names Ready Status (Pod count)">Pod Names Ready Status (Pod count)</th>
                                </tr>
                            </thead>
                            <tbody id="ReplicaSetsPods">
                               @* <tr>
                                    <td rowspan="2">
                                        ReplicaSets Pods
                                    </td>
                                    <td rowspan="2">
                                        nginxstatefulset
                                    </td>
                                    <td>deploy-multicontainer-5b44dcbc67-m24zq</td>
                                    <td>2/2</td>
                                </tr>
                                <tr>
                                    <td>deploy-multicontainer-5b44dcbc67-txjr6</td>
                                    <td>2/2</td>
                                </tr>*@
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-0">
                    <div class="card-header card-title" title="Node Monitoring">Node Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table table-bordered mb-0 align-middle">
                            <thead class="position-sticky top-0">
                                <tr>
                                   
                                    <th title="Name">Name</th>
                                    <th class="text-primary" title="Status">Status</th>
                                    <th class="text-info" title="Role">Role</th>
                                </tr>
                            </thead>
                            <tbody id="NodeMonitor">
                                @*<tr>
                                    <td >
                                        <i class="cp-network text-primary me-1"></i>00-50-56 8f-0d-ee
                                    </td>
                                    <td>
                                        <i class="cp-success text-success me-1"></i>Ready
                                    </td>
                                    <td>control-plane, master, worker</td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-network text-primary me-1"></i>00-50-56 8f-0d-ee
                                    </td>
                                    <td>
                                        <i class="cp-success text-success me-1"></i>Ready
                                    </td>
                                    <td>control-plane, master, worker</td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="cp-network text-primary me-1"></i>00-50-56 8f-0d-ee
                                    </td>
                                    <td>
                                        <i class="cp-success text-success me-1"></i>Ready
                                    </td>
                                    <td>control-plane, master, worker</td>
                                </tr>*@
                            </tbody>
                        </table>
                      
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/js/Monitoring/OCPMonitoring.js"></script>