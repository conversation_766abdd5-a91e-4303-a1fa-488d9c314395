﻿using ContinuityPatrol.Domain.ViewModels.AboutCpModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.AboutCp.Queries.GetList;

public class GetAboutCpListQueryHandler : IRequestHandler<GetAboutCpListQuery, List<GetAboutCpListVm>>
{
    private readonly IAboutCpRepository _aboutCpRepository;
    private readonly IConfiguration _config;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ILogger<GetAboutCpListQueryHandler> _logger;

    public GetAboutCpListQueryHandler(IAboutCpRepository aboutCpRepository, IMapper mapper, IConfiguration config,
        ILicenseManagerRepository licenseManagerRepository, ILoggedInUserService loggedInUserService, ILogger<GetAboutCpListQueryHandler> logger)
    {
        _aboutCpRepository = aboutCpRepository;
        _mapper = mapper;
        _config = config;
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;
        _logger = logger;
    }

    public async Task<List<GetAboutCpListVm>> Handle(GetAboutCpListQuery request, CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting to handle GetAboutCpListQuery.");

        var cpVersion = _config.GetValue<string>("CP:Version");

        _logger.LogDebug("CP Version retrieved: {CpVersion}", cpVersion);

        var license = await _licenseManagerRepository.GetLicenseDetailByCompanyId(_loggedInUserService.CompanyId);

        _logger.LogDebug("Retrieved {LicenseCount} license(s) for CompanyId {CompanyId}.", license.Count, _loggedInUserService.CompanyId);

        var remainingDay = string.Empty;
        string licenseType;

        var createDate = license.MinBy(x => x.Id)?.CreatedDate;

        if (license.Any(x => x.Validity.Trim().ToLower().Equals("enterprise-unlimited")))
        {
            remainingDay = "Unlimited";
            licenseType = "Enterprise";

            _logger.LogDebug("License type set to Enterprise with Unlimited validity.");
        }
        else
        {
            var baseLicense = license.FirstOrDefault(x =>
                x.Validity.Trim().ToLower().Contains("subscription") || x.Validity.Trim().ToLower().Contains("poc"));

            licenseType = baseLicense!.Validity.Trim().ToLower().Contains("subscription")
                ? "Subscription"
                : baseLicense.Validity.Trim().ToLower().Contains("poc")
                    ? "POC"
                    : "UIT";

            _logger.LogDebug("License type determined: {LicenseType}.", licenseType);

            if (DateTime.TryParseExact(baseLicense.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate))
            {
                var currentDate = DateTime.Now.Date;

                _logger.LogDebug("Current date: {CurrentDate}, License expiry date: {ExpiryDate}.", currentDate, expiryDate);

                if (expiryDate >= currentDate)
                {
                    var remainingDays = (expiryDate - currentDate).TotalDays;

                    remainingDay = remainingDays >= 0 ? ((int)remainingDays).ToString() : "Expired";

                    _logger.LogDebug("License is active. Remaining days: {RemainingDays}.", remainingDay);
                }
                else
                {
                    remainingDay = "Expired";

                    _logger.LogWarning("License has expired.");
                }
            }
        }

        var aboutCp = await _aboutCpRepository.ListAllAsync();

        _logger.LogDebug("Retrieved {AboutCount} AboutCP records.", aboutCp.Count);

        if (aboutCp.Count > 0) {
           
            var aboutList = aboutCp.Select(x => new Domain.Entities.AboutCp
            {
                Id=x.Id,
                ReferenceId=x.ReferenceId,
                ProductId=x.ProductId,
                ProductActivatedDate=x.ProductActivatedDate,
                ProductVersion = cpVersion,
                RemainingDays = remainingDay,
                LicenseType = licenseType,
                AboutProduct=x.AboutProduct,
                IsActive=x.IsActive,
                CreatedBy=x.CreatedBy,
                CreatedDate=x.CreatedDate,
                LastModifiedBy=x.LastModifiedBy,
                LastModifiedDate=x.LastModifiedDate
            }).ToList();

            if (aboutList.Any())
                await _aboutCpRepository.UpdateRangeAsync(aboutList);


        }
        else
            await _aboutCpRepository.AddAsync(new Domain.Entities.AboutCp
            {
                ProductId = Guid.NewGuid().ToString(),
                LicenseType = licenseType,
                ProductVersion = cpVersion,
                RemainingDays = remainingDay,
                ProductActivatedDate = createDate,
                AboutProduct =
                    "Continuity Patrol™, part of Perpetuuiti's Resiliency Automation platform, empowers organizations to swiftly recover from disruptions or outages, boasting the industry's lowest Recovery Point Objectives (RPOs) and fastest Recovery Time Objectives (RTOs). This effectively minimizes data loss and downtime, ensuring seamless operational continuity."
            });

        var aboutCpListVm = await _aboutCpRepository.ListAllAsync();

        _logger.LogDebug("Returning {AboutCpCount} AboutCP records.", aboutCpListVm.Count);

        _logger.LogDebug("Finished handling GetAboutCpListQuery.");
        return aboutCp.Count <= 0
            ? new List<GetAboutCpListVm>()
            : _mapper.Map<List<GetAboutCpListVm>>(aboutCpListVm);
    }
}