using ContinuityPatrol.Application.Features.PageWidget.Events.Update;

namespace ContinuityPatrol.Application.Features.PageWidget.Commands.Update;

public class UpdatePageWidgetCommandHandler : IRequestHandler<UpdatePageWidgetCommand, UpdatePageWidgetResponse>
{
    private readonly IMapper _mapper;
    private readonly IPageWidgetRepository _pageWidgetRepository;
    private readonly IPublisher _publisher;

    public UpdatePageWidgetCommandHandler(IMapper mapper, IPageWidgetRepository pageWidgetRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _pageWidgetRepository = pageWidgetRepository;
        _publisher = publisher;
    }

    public async Task<UpdatePageWidgetResponse> Handle(UpdatePageWidgetCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _pageWidgetRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.PageWidget), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdatePageWidgetCommand), typeof(Domain.Entities.PageWidget));

        await _pageWidgetRepository.UpdateAsync(eventToUpdate);

        var response = new UpdatePageWidgetResponse
        {
            Message = Message.Update(nameof(Domain.Entities.PageWidget), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new PageWidgetUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}