﻿using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobStatus;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract
{
    public interface IReplicationJobService
    {
        Task<BaseResponse> UpdateReplicationJob(UpdateReplicationJobCommand updateReplicationJobCommand);
        Task<List<ReplicationJobListVm>> GetReplicationJobList();
        Task<BaseResponse> CreateReplicationJob(CreateReplicationJobCommand createReplicationJobCommand);
        Task<BaseResponse> DeleteReplicationJob(string id);
        Task<ReplicationJobListVm> GetReplicationJobById(string id);
        Task<PaginatedResult<ReplicationJobListVm>> GetPaginated(GetReplicationJobPaginatedListQuery query);
        Task<BaseResponse> UpdateReplicationJobState( UpdateReplicationJobStateCommand updateReplicationJobStateCommand);
        Task<bool> IsReplicationJobNameExist(string name, string id);
        Task<BaseResponse> UpdateReplicationJobStatus(UpdateReplicationJobStatusCommand updateReplicationJobStatus);
    }
}
