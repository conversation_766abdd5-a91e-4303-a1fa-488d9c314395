﻿using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.UnitTests.Fixtures;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseManager.Queries;

public class GetByPoNumberQueryHandlerTests : IClassFixture<LicenseManagerFixture>
{
    private readonly LicenseManagerFixture _licenseManagerFixture;

    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;

    private readonly GetLicenseManagerByPoNumberQueryHandler _handler;

    //public GetByPoNumberQueryHandlerTests(LicenseManagerFixture licenseManagerFixture)
    //{
    //    _licenseManagerFixture = licenseManagerFixture;

    //    _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.GetByPoNumberRepository(_licenseManagerFixture.LicenseManagers);

    //    _handler = new GetLicenseManagerByPoNumberQueryHandler(_mockLicenseManagerRepository.Object, _licenseManagerFixture.Mapper);
    //}

    [Fact]
    public async Task Handle_Return_Valid_LicenseManagersList()
    {
        //var result = await _handler.Handle(new GetLicenseManagerByPoNumberQuery { PONumber = _licenseManagerFixture.LicenseManagers[0].PONumber }, CancellationToken.None);

        //result.ShouldBeOfType<LicenseManagerByPoNumberVm>();

        //result.Id.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ReferenceId);
        //result.PONumber.ShouldBe(_licenseManagerFixture.LicenseManagers[0].PONumber);
        //result.Validity.ShouldBe(_licenseManagerFixture.LicenseManagers[0].Validity);
        //result.ExpiryDate.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ExpiryDate);
    }

    //[Fact]
    //public async Task Handle_Throw_NotFoundException_When_InvalidLicenseManagerId()
    //{
    //    var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetLicenseManagerByPoNumberQuery { PONumber = int.MaxValue.ToString() }, CancellationToken.None));

    //    exceptionDetails.Message.ShouldContain("Not Found");
    //}

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        //await _handler.Handle(new GetLicenseManagerByPoNumberQuery { PONumber = _licenseManagerFixture.LicenseManagers[0].PONumber }, CancellationToken.None);

        //_mockLicenseManagerRepository.Verify(x => x.GetLicenseDetailByPoNumber(It.IsAny<string>()), Times.Once);
    }
}