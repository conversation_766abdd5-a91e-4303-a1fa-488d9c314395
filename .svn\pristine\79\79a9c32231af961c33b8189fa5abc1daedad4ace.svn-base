﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;

public class GetTableAccessPaginatedListQueryHandler : IRequestHandler<GetTableAccessPaginatedListQuery,
    PaginatedResult<TableAccessListVm>>
{
    private readonly IMapper _mapper;

    private readonly ITableAccessRepository _tableAccessRepository;

    public GetTableAccessPaginatedListQueryHandler(IMapper mapper, ITableAccessRepository tableAccessRepository)
    {
        _mapper = mapper;

        _tableAccessRepository = tableAccessRepository;
    }

    public async Task<PaginatedResult<TableAccessListVm>> Handle(GetTableAccessPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _tableAccessRepository.GetPaginatedQuery();

        var productFilterSpec = new TableAccessFilterSpecification(request.SearchString);

        var tableAccessesList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<TableAccessListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return tableAccessesList;
    }
}