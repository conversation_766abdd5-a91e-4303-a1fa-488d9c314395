using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Queries;

public class GetBiaRulesListTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IBiaRulesRepository> _mockBiaRulesRepository;
    private readonly GetBiaRulesListQueryHandler _handler;

    public GetBiaRulesListTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockBiaRulesRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(_biaRulesFixture.BiaRules);

        _handler = new GetBiaRulesListQueryHandler(
            _biaRulesFixture.Mapper,
            _mockBiaRulesRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnBiaRulesList_When_BiaRulesExist()
    {
        // Arrange
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<BiaRulesListVm>>();
        result.Count.ShouldBeGreaterThan(0);

        _mockBiaRulesRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoBiaRulesExist()
    {
        // Arrange
        var emptyBiaRules = new List<Domain.Entities.BiaRules>();
        var mockEmptyRepository = BiaRulesRepositoryMocks.CreateBiaRulesRepository(emptyBiaRules);
        var handler = new GetBiaRulesListQueryHandler(_biaRulesFixture.Mapper, mockEmptyRepository.Object);
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<BiaRulesListVm>>();
        result.Count.ShouldBe(0);

        mockEmptyRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectMappedData_When_BiaRulesExist()
    {
        // Arrange
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        var firstResult = result.First();
        var firstBiaRule = _biaRulesFixture.BiaRules.First();

        firstResult.ShouldNotBeNull();
       
        firstResult.Description.ShouldBe(firstBiaRule.Description);
        firstResult.Type.ShouldBe(firstBiaRule.Type);
        firstResult.EntityId.ShouldBe(firstBiaRule.EntityId);
        firstResult.IsEffective.ShouldBe(firstBiaRule.IsEffective);
        firstResult.RuleCode.ShouldBe(firstBiaRule.RuleCode);
    }

    [Fact]
    public async Task Handle_ReturnAllActiveBiaRules_When_MultipleRulesExist()
    {
        // Arrange
        var activeBiaRules = _biaRulesFixture.BiaRules.Where(x => x.IsActive).ToList();
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(activeBiaRules.Count);

        foreach (var biaRule in activeBiaRules)
        {
            var mappedRule = result.FirstOrDefault(x => x.Id == biaRule.ReferenceId);
            mappedRule.ShouldNotBeNull();
            mappedRule.Description.ShouldBe(biaRule.Description);
            mappedRule.Type.ShouldBe(biaRule.Type);
        }
    }

    [Fact]
    public async Task Handle_ReturnRTORules_When_RTORulesExist()
    {
        // Arrange
        var rtoRules = _biaRulesFixture.BiaRules.Where(x => x.Type == "RTO" && x.IsActive).ToList();
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var rtoResults = result.Where(x => x.Type == "RTO").ToList();
        rtoResults.Count.ShouldBe(rtoRules.Count);

        foreach (var rtoRule in rtoRules)
        {
            var mappedRule = rtoResults.FirstOrDefault(x => x.Id == rtoRule.ReferenceId);
            mappedRule.ShouldNotBeNull();
            mappedRule.Type.ShouldBe("RTO");
        }
    }

    [Fact]
    public async Task Handle_ReturnRPORules_When_RPORulesExist()
    {
        // Arrange
        // Add RPO rules to the fixture
        var rpoRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "RPO Rule for Testing",
            Type = "RPO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"1\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-10).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(20).ToString("yyyy-MM-dd"),
            IsEffective = true,
            RuleCode = "BIA_RPO_LIST_TEST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(rpoRule);

        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var rpoResults = result.Where(x => x.Type == "RPO").ToList();
        rpoResults.Count.ShouldBeGreaterThan(0);

        var mappedRpoRule = rpoResults.FirstOrDefault(x => x.Id == rpoRule.ReferenceId);
        mappedRpoRule.ShouldNotBeNull();
        mappedRpoRule.Type.ShouldBe("RPO");
        mappedRpoRule.Description.ShouldBe("RPO Rule for Testing");
    }

    [Fact]
    public async Task Handle_ReturnEffectiveRules_When_EffectiveRulesExist()
    {
        // Arrange
        var effectiveRules = _biaRulesFixture.BiaRules.Where(x => x.IsEffective && x.IsActive).ToList();
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var effectiveResults = result.Where(x => x.IsEffective).ToList();
        effectiveResults.Count.ShouldBe(effectiveRules.Count);

        foreach (var effectiveRule in effectiveRules)
        {
            var mappedRule = effectiveResults.FirstOrDefault(x => x.Id == effectiveRule.ReferenceId);
            mappedRule.ShouldNotBeNull();
            mappedRule.IsEffective.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Handle_ReturnInactiveRules_When_InactiveRulesExist()
    {
        // Arrange
        // Add inactive rule to the fixture
        var inactiveRule = new Domain.Entities.BiaRules
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Inactive Rule for Testing",
            Type = "RTO",
            EntityId = Guid.NewGuid().ToString(),
            Properties = "{\"threshold\":\"8\",\"unit\":\"hours\"}",
            EffectiveDateFrom = DateTime.Now.AddDays(-5).ToString("yyyy-MM-dd"),
            EffectiveDateTo = DateTime.Now.AddDays(5).ToString("yyyy-MM-dd"),
            IsEffective = false,
            RuleCode = "BIA_RTO_INACTIVE_LIST",
            IsActive = true
        };
        _biaRulesFixture.BiaRules.Add(inactiveRule);

        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var inactiveResults = result.Where(x => !x.IsEffective).ToList();
        inactiveResults.Count.ShouldBeGreaterThan(0);

        var mappedInactiveRule = inactiveResults.FirstOrDefault(x => x.Id == inactiveRule.ReferenceId);
        mappedInactiveRule.ShouldNotBeNull();
        mappedInactiveRule.IsEffective.ShouldBeFalse();
        mappedInactiveRule.Description.ShouldBe("Inactive Rule for Testing");
    }

    [Fact]
    public async Task Handle_ReturnRulesWithProperties_When_RulesHaveProperties()
    {
        // Arrange
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        var rulesWithProperties = result.Where(x => !string.IsNullOrEmpty(x.Properties)).ToList();
        rulesWithProperties.Count.ShouldBeGreaterThan(0);

        foreach (var rule in rulesWithProperties)
        {
            rule.Properties.ShouldNotBeNullOrEmpty();
            // Verify it's valid JSON format
            rule.Properties.ShouldStartWith("{");
            rule.Properties.ShouldEndWith("}");
        }
    }

    [Fact]
    public async Task Handle_ReturnRulesWithDateRanges_When_RulesHaveDateRanges()
    {
        // Arrange
        var query = new GetBiaRulesListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        foreach (var rule in result)
        {
            rule.EffectiveDateFrom.ShouldNotBeNullOrEmpty();
            rule.EffectiveDateTo.ShouldNotBeNullOrEmpty();
            
            // Verify date format
            DateTime.TryParse(rule.EffectiveDateFrom, out _).ShouldBeTrue();
            DateTime.TryParse(rule.EffectiveDateTo, out _).ShouldBeTrue();
        }
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var query = new GetBiaRulesListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBiaRulesRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockBiaRulesRepository.VerifyNoOtherCalls();
    }
}
