namespace ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;

public class GetCyberSnapsDetailsQueryHandler : IRequestHandler<GetCyberSnapsDetailQuery, CyberSnapsDetailVm>
{
    private readonly ICyberSnapsRepository _cyberSnapsRepository;
    private readonly IMapper _mapper;

    public GetCyberSnapsDetailsQueryHandler(IMapper mapper, ICyberSnapsRepository cyberSnapsRepository)
    {
        _mapper = mapper;
        _cyberSnapsRepository = cyberSnapsRepository;
    }

    public async Task<CyberSnapsDetailVm> Handle(GetCyberSnapsDetailQuery request, CancellationToken cancellationToken)
    {
        var cyberSnaps = await _cyberSnapsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(cyberSnaps, nameof(Domain.Entities.CyberSnaps),
            new NotFoundException(nameof(Domain.Entities.CyberSnaps), request.Id));

        var cyberSnapsDetailDto = _mapper.Map<CyberSnapsDetailVm>(cyberSnaps);

        return cyberSnapsDetailDto;
    }
}