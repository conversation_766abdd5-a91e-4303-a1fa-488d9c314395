﻿using ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Events.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByUserName;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class BulkServerCredentialControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IDataProvider> _mockDataProvider;
        private readonly Mock<ILogger<BulkServerCredentialController>> _mockLogger;
        private readonly BulkServerCredentialController _controller;

        public BulkServerCredentialControllerShould()
        {
            _mockPublisher = new Mock<IPublisher>();
            _mockDataProvider = new Mock<IDataProvider>();
            _mockLogger = new Mock<ILogger<BulkServerCredentialController>>();

            _controller = new BulkServerCredentialController(_mockPublisher.Object, _mockDataProvider.Object, _mockLogger.Object);

            // Setup HttpContext and TempData
            var httpContext = new DefaultHttpContext();
            httpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "testuser"),
                new Claim(ClaimTypes.NameIdentifier, "123")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext()
            {
                HttpContext = httpContext
            };

            var tempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>());
            _controller.TempData = tempData;
        }

        #region List Tests

        [Fact]
        public async Task List_WithSuccessfulPublish_ReturnsViewResult()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<ServerBulkPasswordUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.Is<ServerBulkPasswordUpdatedEvent>(e =>
                e.ActivityType == ActivityType.View.ToString()), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task List_WithPublishException_ReturnsViewResultAndLogsError()
        {
            // Arrange
            var exception = new Exception("Publisher error");
            _mockPublisher.Setup(p => p.Publish(It.IsAny<ServerBulkPasswordUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ServerBulkPasswordUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region GetServerByUserName Tests

        [Fact]
        public async Task GetServerByUserName_WithValidParameters_ReturnsJsonWithServerList()
        {
            // Arrange
            var userName = "testuser";
            var osType = "Windows";
            var substituteAuthentication = false;

            var serverList = new List<ServerByUserNameVm>
            {
                new ServerByUserNameVm
                {
                    Id = "1",
                    Name = "Test Server 1",
                    OSType = "Windows",
                    SubAuthUsers = new List<string> { "user1", "user2" }
                },
                new ServerByUserNameVm
                {
                    Id = "2",
                    Name = "Test Server 2",
                    OSType = "Linux",
                    SubAuthUsers = new List<string> { "user3" }
                }
            };

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        [Fact]
        public async Task GetServerByUserName_WithEmptyUserName_ReturnsJsonWithServerList()
        {
            // Arrange
            var userName = "";
            var osType = "Windows";
            var substituteAuthentication = true;

            var serverList = new List<ServerByUserNameVm>();

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        [Fact]
        public async Task GetServerByUserName_WithNullUserName_ReturnsJsonWithServerList()
        {
            // Arrange
            string userName = null;
            var osType = "Linux";
            var substituteAuthentication = false;

            var serverList = new List<ServerByUserNameVm>();

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        [Fact]
        public async Task GetServerByUserName_WithNullOsType_ReturnsJsonWithServerList()
        {
            // Arrange
            var userName = "testuser";
            string osType = null;
            var substituteAuthentication = true;

            var serverList = new List<ServerByUserNameVm>
            {
                new ServerByUserNameVm
                {
                    Id = "1",
                    Name = "Test Server",
                    OSType = "Windows"
                }
            };

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        [Fact]
        public async Task GetServerByUserName_WithSubstituteAuthenticationTrue_ReturnsJsonWithServerList()
        {
            // Arrange
            var userName = "testuser";
            var osType = "Windows";
            var substituteAuthentication = true;

            var serverList = new List<ServerByUserNameVm>
            {
                new ServerByUserNameVm
                {
                    Id = "1",
                    Name = "Test Server",
                    OSType = "Windows",
                    SubAuthUsers = new List<string> { "subuser1", "subuser2" }
                }
            };

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ReturnsAsync(serverList);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(serverList, jsonResult.Value);
            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        [Fact]
        public async Task GetServerByUserName_WithException_ReturnsJsonWithErrorMessage()
        {
            // Arrange
            var userName = "testuser";
            var osType = "Windows";
            var substituteAuthentication = false;
            var exception = new Exception("Database connection error");

            _mockDataProvider.Setup(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetServerByUserName(userName, osType, substituteAuthentication);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var messageProperty = resultValue.GetType().GetProperty("message");

            Assert.NotNull(successProperty);
            Assert.NotNull(messageProperty);
            Assert.False((bool)successProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.GetServerByUserName(userName, osType, substituteAuthentication), Times.Once);
        }

        #endregion

        #region UpdateBulkPassword Tests

        [Fact]
        public async Task UpdateBulkPassword_WithValidCommand_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "newpassword123",
                IsSubstituteAuthentication = false,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>
                {
                    new UpdateBulkPasswordList
                    {
                        Id = "1",
                        Name = "Server1",
                        UserName = "user1",
                        IpAddress = "***********",
                        OsType = "Windows",
                        SubAuthUserNames = new List<string> { "subuser1" }
                    },
                    new UpdateBulkPasswordList
                    {
                        Id = "2",
                        Name = "Server2",
                        UserName = "user2",
                        IpAddress = "***********",
                        OsType = "Linux",
                        SubAuthUserNames = new List<string> { "subuser2" }
                    }
                }
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "2 Server password updated successfully."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithSubstituteAuthentication_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "newpassword123",
                IsSubstituteAuthentication = true,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>
                {
                    new UpdateBulkPasswordList
                    {
                        Id = "1",
                        Name = "Server1",
                        UserName = "user1",
                        IpAddress = "***********",
                        OsType = "Windows",
                        SubAuthUserNames = new List<string> { "subuser1", "subuser2" }
                    }
                }
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "1 Server Sub Authentication password updated successfully."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithEmptyPasswordList_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "newpassword123",
                IsSubstituteAuthentication = false,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>()
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "0 Server password updated successfully."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithNullPasswordList_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "newpassword123",
                IsSubstituteAuthentication = false,
                UpdateBulkPasswordLists = null
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "Server password update completed."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithEmptyPassword_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "",
                IsSubstituteAuthentication = false,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>
                {
                    new UpdateBulkPasswordList
                    {
                        Id = "1",
                        Name = "Server1",
                        UserName = "user1",
                        IpAddress = "***********",
                        OsType = "Windows"
                    }
                }
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "1 Server password updated successfully."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithNullPassword_ReturnsJsonWithSuccessAndData()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = null,
                IsSubstituteAuthentication = true,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>
                {
                    new UpdateBulkPasswordList
                    {
                        Id = "1",
                        Name = "Server1",
                        UserName = "user1",
                        IpAddress = "***********",
                        OsType = "Linux",
                        SubAuthUserNames = new List<string> { "subuser1" }
                    }
                }
            };

            var response = new UpdateBulkPasswordResponse
            {
                Message = "1 Server Sub Authentication password updated successfully."
            };

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var dataProperty = resultValue.GetType().GetProperty("data");

            Assert.NotNull(successProperty);
            Assert.NotNull(dataProperty);
            Assert.True((bool)successProperty.GetValue(resultValue));
            Assert.Equal(response, dataProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithException_ReturnsJsonWithErrorMessage()
        {
            // Arrange
            var updateCommand = new UpdateBulkPasswordCommand
            {
                Password = "newpassword123",
                IsSubstituteAuthentication = false,
                UpdateBulkPasswordLists = new List<UpdateBulkPasswordList>
                {
                    new UpdateBulkPasswordList
                    {
                        Id = "1",
                        Name = "Server1",
                        UserName = "user1",
                        IpAddress = "***********",
                        OsType = "Windows"
                    }
                }
            };

            var exception = new Exception("Database connection failed");

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var messageProperty = resultValue.GetType().GetProperty("message");

            Assert.NotNull(successProperty);
            Assert.NotNull(messageProperty);
            Assert.False((bool)successProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        [Fact]
        public async Task UpdateBulkPassword_WithNullCommand_HandlesGracefully()
        {
            // Arrange
            UpdateBulkPasswordCommand updateCommand = null;
            var exception = new ArgumentNullException("updateBulkPassword");

            _mockDataProvider.Setup(dp => dp.Server.UpdateServerPassword(updateCommand))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.UpdateBulkPassword(updateCommand);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var resultValue = jsonResult.Value;
            Assert.NotNull(resultValue);

            var successProperty = resultValue.GetType().GetProperty("success");
            var messageProperty = resultValue.GetType().GetProperty("message");

            Assert.NotNull(successProperty);
            Assert.NotNull(messageProperty);
            Assert.False((bool)successProperty.GetValue(resultValue));

            _mockDataProvider.Verify(dp => dp.Server.UpdateServerPassword(updateCommand), Times.Once);
        }

        #endregion
    }
}
