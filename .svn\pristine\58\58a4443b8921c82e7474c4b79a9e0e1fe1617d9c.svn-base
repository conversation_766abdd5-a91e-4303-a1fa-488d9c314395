﻿using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Commands;

public class UpdateSingleSignOnTests : IClassFixture<SingleSignOnFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;
    private readonly Mock<ISingleSignOnRepository> _mockSingleSignOnRepository;
    private readonly UpdateSingleSignOnCommandHandler _handler;

    public UpdateSingleSignOnTests(SingleSignOnFixture singleSignOnFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.UpdateSingleSignOnRepository(_singleSignOnFixture.SingleSignOns);

        _handler = new UpdateSingleSignOnCommandHandler(_singleSignOnFixture.Mapper, _mockSingleSignOnRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidSingleSignOn_UpdatedTo_SingleSignOnsRepo()
    {
        _singleSignOnFixture.UpdateSingleSignOnCommand.Id = _singleSignOnFixture.SingleSignOns[0].ReferenceId;

        var result = await _handler.Handle(_singleSignOnFixture.UpdateSingleSignOnCommand, CancellationToken.None);

        var singleSignOn = await _mockSingleSignOnRepository.Object.GetByReferenceIdAsync(result.SingleSignOnId);

        Assert.Equal(_singleSignOnFixture.UpdateSingleSignOnCommand.ProfileName, singleSignOn.ProfileName);
    }

    [Fact]
    public async Task Handle_Return_ValidSingleSignOnResponse()
    {
        _singleSignOnFixture.UpdateSingleSignOnCommand.Id = _singleSignOnFixture.SingleSignOns[0].ReferenceId;

        var result = await _handler.Handle(_singleSignOnFixture.UpdateSingleSignOnCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateSingleSignOnResponse));

        result.SingleSignOnId.ShouldBeGreaterThan(0.ToString());

        result.SingleSignOnId.ShouldBe(_singleSignOnFixture.UpdateSingleSignOnCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidSingleSignOnId()
    {
        _singleSignOnFixture.UpdateSingleSignOnCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_singleSignOnFixture.UpdateSingleSignOnCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _singleSignOnFixture.UpdateSingleSignOnCommand.Id = _singleSignOnFixture.SingleSignOns[0].ReferenceId;

        await _handler.Handle(_singleSignOnFixture.UpdateSingleSignOnCommand, CancellationToken.None);

        _mockSingleSignOnRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSingleSignOnRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.SingleSignOn>()), Times.Once);
    }
}