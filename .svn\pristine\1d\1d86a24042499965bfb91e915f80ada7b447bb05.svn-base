﻿using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseManager.Queries;

public class GetLicenseManagerListQueryHandlerTests : IClassFixture<LicenseManagerFixture>, IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseManagerFixture _licenseManagerFixture;

    private readonly LicenseInfoFixture _licenseInfoFixture;

    private Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;

    private Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly GetLicenseManagerListQueryHandler _handler;

    public GetLicenseManagerListQueryHandlerTests(LicenseManagerFixture licenseManagerFixture, LicenseInfoFixture licenseInfoFixture)
    {
        _licenseManagerFixture = licenseManagerFixture;

        _licenseInfoFixture = licenseInfoFixture;

        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.GetLicenseManagerRepository(_licenseManagerFixture.LicenseManagers);

        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.GetLicenseInfoRepository(_licenseInfoFixture.LicenseInfos);

        _handler = new GetLicenseManagerListQueryHandler(_licenseManagerFixture.Mapper, _mockLicenseManagerRepository.Object,_mockLicenseInfoRepository.Object);
       
    }

    //[Fact]
    //public async Task Handle_Return_Valid_LicenseManagersList()
    //{
    //    var result = await _handler.Handle(new GetLicenseManagerListQuery(), CancellationToken.None);

    //    result.ShouldBeOfType<List<LicenseManagerListVm>>();

    //    result[0].Id.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ReferenceId);
    //    result[0].PONumber.ShouldBe(_licenseManagerFixture.LicenseManagers[0].PONumber);
    //    //result[0].CompanyId.ShouldBe(_licenseManagerFixture.LicenseManagers[0].CompanyId);
    //    //result[0].CPHostName.ShouldBe(_licenseManagerFixture.LicenseManagers[0].CPHostName);
    //    result[0].ServerCount.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ServerCount);
    //    result[0].DatabaseCount.ShouldBe(_licenseManagerFixture.LicenseManagers[0].DatabaseCount);
    //    result[0].ReplicationCount.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ReplicationCount);
    //    //result[0].IPAddress.ShouldBe(_licenseManagerFixture.LicenseManagers[0].IPAddress);
    //    result[0].Validity.ShouldBe(_licenseManagerFixture.LicenseManagers[0].Validity);
    //    result[0].ExpiryDate.ShouldBe(_licenseManagerFixture.LicenseManagers[0].ExpiryDate);
    //}

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.GetLicenseManagerEmptyRepository();

        var handler = new GetLicenseManagerListQueryHandler(_licenseManagerFixture.Mapper, _mockLicenseManagerRepository.Object,_mockLicenseInfoRepository.Object);

        var result = await handler.Handle(new GetLicenseManagerListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    //[Fact]
    //public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    //{
    //    await _handler.Handle(new GetLicenseManagerListQuery(), CancellationToken.None);

    //    _mockLicenseManagerRepository.Verify(repo => repo.ListAllLicense(), Times.Once);
    //}
}