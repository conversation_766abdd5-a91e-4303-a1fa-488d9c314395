﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.Update;

public class InfraObjectSchedulerUpdatedEventHandler : INotificationHandler<InfraObjectSchedulerUpdatedEvent>
{
    private readonly ILogger<InfraObjectSchedulerUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraObjectSchedulerUpdatedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<InfraObjectSchedulerUpdatedEventHandler> logger)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(InfraObjectSchedulerUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = "Manage Resilience Readiness",
            Action = $"{ActivityType.Update} Manage Resilience Readiness",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Manage Resilience Readiness '{updatedEvent.InfraObjectName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Manage Resilience Readiness '{updatedEvent.InfraObjectName}' updated successfully.");
    }
}