﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Events.PaginatedView;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LoadBalancerControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<LoadBalancerController>> _mockLogger = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private LoadBalancerController _controller;

        public LoadBalancerControllerShould()
        {
            Initialize();
        }

        public void Initialize()
        {
            _controller = new LoadBalancerController(
                _mockPublisher.Object,
                _mockMapper.Object,
                _mockProvider.Object,
                _mockLogger.Object,
                _mockLoggedInUserService.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

            // Setup common logged in user service properties
            _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("test-company-123");
            _mockLoggedInUserService.Setup(x => x.UserId).Returns("test-user-123");
            _mockLoggedInUserService.Setup(x => x.LoginName).Returns("testuser");
            _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/test-url");
            _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("***********");
        }

        [Fact]
        public async Task List_Success_ReturnsView()
        {
            // Arrange
            // No specific setup needed as the method just publishes an event and returns a view

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.Model); // The successful path returns View() with no model
            _mockPublisher.Verify(p => p.Publish(It.IsAny<LoadBalancerPaginatedEvent>(), default), Times.Once);
        }

        [Fact]
        public async Task List_Exception_ReturnsViewWithEmptyModel()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<LoadBalancerPaginatedEvent>(), default))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<LoadBalancerViewModel>(viewResult.Model);
            Assert.NotNull(model.NodesVm);
            Assert.Empty(model.NodesVm);
        }
        
        [Fact]
        public async Task CreateOrUpdate_Create_Success_ReturnsJsonResult()
        {
            // Arrange
            var model = new LoadBalancerViewModel { Id = null }; // Null/empty ID indicates create
            var createCommand = new CreateLoadBalancerCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mockMapper.Setup(m => m.Map<CreateLoadBalancerCommand>(model)).Returns(createCommand);
            _mockProvider.Setup(p => p.LoadBalancer.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":\"Created successfully\"", json);

            _mockMapper.Verify(m => m.Map<CreateLoadBalancerCommand>(model), Times.Once);
            _mockProvider.Verify(p => p.LoadBalancer.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_Update_Success_ReturnsJsonResult()
        {
            // Arrange
            var model = new LoadBalancerViewModel { Id = "test-id-123" }; // Non-empty ID indicates update
            var updateCommand = new UpdateLoadBalancerCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mockMapper.Setup(m => m.Map<UpdateLoadBalancerCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LoadBalancer.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":\"Updated successfully\"", json);

            _mockMapper.Verify(m => m.Map<UpdateLoadBalancerCommand>(model), Times.Once);
            _mockProvider.Verify(p => p.LoadBalancer.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_Create_Exception_ReturnsJsonException()
        {
            // Arrange
            var model = new LoadBalancerViewModel { Id = null };
            var createCommand = new CreateLoadBalancerCommand();

            _mockMapper.Setup(m => m.Map<CreateLoadBalancerCommand>(model)).Returns(createCommand);
            _mockProvider.Setup(p => p.LoadBalancer.CreateAsync(createCommand))
                .ThrowsAsync(new Exception("Create failed"));

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            // The result should be from ex.GetJsonException() which returns error information
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_Update_Exception_ReturnsJsonException()
        {
            // Arrange
            var model = new LoadBalancerViewModel { Id = "test-id-123" };
            var updateCommand = new UpdateLoadBalancerCommand();

            _mockMapper.Setup(m => m.Map<UpdateLoadBalancerCommand>(model)).Returns(updateCommand);
            _mockProvider.Setup(p => p.LoadBalancer.UpdateAsync(updateCommand))
                .ThrowsAsync(new Exception("Update failed"));

            // Act
            var result = await _controller.CreateOrUpdate(model);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            // The result should be from ex.GetJsonException() which returns error information
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task Delete_Success_ReturnsJsonResult()
        {
            // Arrange
            var id = "test-id-123";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockProvider.Setup(p => p.LoadBalancer.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":\"Deleted successfully\"", json);

            _mockProvider.Verify(p => p.LoadBalancer.DeleteAsync(id), Times.Once);
        }

        [Fact]
        public async Task Delete_Exception_ReturnsJsonException()
        {
            // Arrange
            var id = "test-id-123";
            _mockProvider.Setup(p => p.LoadBalancer.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete failed"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_Success_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetLoadBalancerPaginatedListQuery();
            var paginatedResult = new PaginatedResult<LoadBalancerListVm>
            {
                Data = new List<LoadBalancerListVm>(),
                TotalCount = 0
            };
            _mockProvider.Setup(p => p.LoadBalancer.GetPaginatedNodeConfigurations(query))
                .ReturnsAsync(paginatedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedResult, jsonResult.Value);
            _mockProvider.Verify(p => p.LoadBalancer.GetPaginatedNodeConfigurations(query), Times.Once);
        }

        [Fact]
        public async Task GetPagination_Exception_ReturnsEmptyJson()
        {
            // Arrange
            var query = new GetLoadBalancerPaginatedListQuery();
            _mockProvider.Setup(p => p.LoadBalancer.GetPaginatedNodeConfigurations(query))
                .ThrowsAsync(new Exception("Pagination failed"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task IsLoadBalancerNameExist_Success_ReturnsTrue()
        {
            // Arrange
            var loadBalancerName = "test-name";
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsLoadBalancerNameExist(loadBalancerName, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsLoadBalancerNameExist(loadBalancerName, id);

            // Assert
            Assert.True(result);
            _mockProvider.Verify(p => p.LoadBalancer.IsLoadBalancerNameExist(loadBalancerName, id), Times.Once);
        }

        [Fact]
        public async Task IsLoadBalancerNameExist_Success_ReturnsFalse()
        {
            // Arrange
            var loadBalancerName = "test-name";
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsLoadBalancerNameExist(loadBalancerName, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsLoadBalancerNameExist(loadBalancerName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsLoadBalancerNameExist_Exception_ReturnsFalse()
        {
            // Arrange
            var loadBalancerName = "test-name";
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsLoadBalancerNameExist(loadBalancerName, id))
                .ThrowsAsync(new Exception("Check failed"));

            // Act
            var result = await _controller.IsLoadBalancerNameExist(loadBalancerName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsLoadBalancerIpandPortExist_Success_ReturnsTrue()
        {
            // Arrange
            var ipAddress = "***********00";
            var port = 8080;
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsIpAddressAndPortExist(ipAddress, port, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsLoadBalancerIpandPortExist(ipAddress, port, id);

            // Assert
            Assert.True(result);
            _mockProvider.Verify(p => p.LoadBalancer.IsIpAddressAndPortExist(ipAddress, port, id), Times.Once);
        }

        [Fact]
        public async Task IsLoadBalancerIpandPortExist_Success_ReturnsFalse()
        {
            // Arrange
            var ipAddress = "***********00";
            var port = 8080;
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsIpAddressAndPortExist(ipAddress, port, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsLoadBalancerIpandPortExist(ipAddress, port, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsLoadBalancerIpandPortExist_Exception_ReturnsFalse()
        {
            // Arrange
            var ipAddress = "***********00";
            var port = 8080;
            var id = "test-id";
            _mockProvider.Setup(p => p.LoadBalancer.IsIpAddressAndPortExist(ipAddress, port, id))
                .ThrowsAsync(new Exception("Check failed"));

            // Act
            var result = await _controller.IsLoadBalancerIpandPortExist(ipAddress, port, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void StateMonitoring_ReturnsView()
        {
            // Act
            var result = _controller.StateMonitoring();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.Model); // Method returns View() with no model
        }

        [Fact]
        public async Task LoadStateMonitoring_Success_ReturnsJsonResult()
        {
            // Arrange
            var stateDetails = new List<StateMonitorStatusListVm>();
            _mockProvider.Setup(p => p.LoadBalancer.GetStateMonitorStatusList()).ReturnsAsync(stateDetails);
            _mockProvider.Setup(p => p.UserActivity.CreateAsync(It.IsAny<CreateUserActivityCommand>()))
                .ReturnsAsync(new CreateUserActivityResponse());

            // Act
            var result = await _controller.LoadStateMonitoring();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);

            _mockProvider.Verify(p => p.LoadBalancer.GetStateMonitorStatusList(), Times.Once);
            _mockProvider.Verify(p => p.UserActivity.CreateAsync(It.IsAny<CreateUserActivityCommand>()), Times.Once);
        }

        [Fact]
        public async Task LoadStateMonitoring_Exception_ReturnsJsonException()
        {
            // Arrange
            _mockProvider.Setup(p => p.LoadBalancer.GetStateMonitorStatusList())
                .ThrowsAsync(new Exception("Load failed"));
            _mockProvider.Setup(p => p.UserActivity.CreateAsync(It.IsAny<CreateUserActivityCommand>()))
                .ReturnsAsync(new CreateUserActivityResponse());

            // Act
            var result = await _controller.LoadStateMonitoring();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task TestConfiguration_Success_ReturnsJsonResult()
        {
            // Arrange
            var nodeId = "test-node-123";
            var testResult = new BaseResponse { Success = true, Message = "Connection successful" };
            _mockProvider.Setup(p => p.LoadBalancer.TestConnection(It.IsAny<LoadBalancerTestConnectionCommand>()))
                .ReturnsAsync(testResult);

            // Act
            var result = await _controller.TestConfiguration(nodeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"data\":", json);

            _mockProvider.Verify(p => p.LoadBalancer.TestConnection(It.Is<LoadBalancerTestConnectionCommand>(cmd => cmd.Id == nodeId)), Times.Once);
        }

        [Fact]
        public async Task TestConfiguration_Exception_ReturnsJsonException()
        {
            // Arrange
            var nodeId = "test-node-123";
            _mockProvider.Setup(p => p.LoadBalancer.TestConnection(It.IsAny<LoadBalancerTestConnectionCommand>()))
                .ThrowsAsync(new Exception("Test failed"));

            // Act
            var result = await _controller.TestConfiguration(nodeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task UpdateNodeStatus_Success_ReturnsJsonResult()
        {
            // Arrange
            var command = new UpdateNodeStatusCommand
            {
                Id = "test-id",
                Name = "test-node",
                IsNodeStatus = true
            };
            var response = new UpdateNodeStatusResponse { Success = true, Message = "Status updated" };
            _mockProvider.Setup(p => p.LoadBalancer.UpdateNodeStatus(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.UpdateNodeStatus(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);

            _mockProvider.Verify(p => p.LoadBalancer.UpdateNodeStatus(command), Times.Once);
        }

        [Fact]
        public async Task UpdateNodeStatus_Exception_ReturnsJsonException()
        {
            // Arrange
            var command = new UpdateNodeStatusCommand();
            _mockProvider.Setup(p => p.LoadBalancer.UpdateNodeStatus(command))
                .ThrowsAsync(new Exception("Update failed"));

            // Act
            var result = await _controller.UpdateNodeStatus(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task IsDefault_Success_ReturnsJsonResult()
        {
            // Arrange
            var command = new UpdateLoadBalancerDefaultCommand
            {
                Id = "test-id",
                Name = "test-node",
                IsDefault = true
            };
            var response = new UpdateLoadBalancerDefaultResponse { Success = true, Message = "Default updated" };
            _mockProvider.Setup(p => p.LoadBalancer.UpdateDefault(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.IsDefault(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);

            _mockProvider.Verify(p => p.LoadBalancer.UpdateDefault(command), Times.Once);
        }

        [Fact]
        public async Task IsDefault_Exception_ReturnsJsonException()
        {
            // Arrange
            var command = new UpdateLoadBalancerDefaultCommand();
            _mockProvider.Setup(p => p.LoadBalancer.UpdateDefault(command))
                .ThrowsAsync(new Exception("Update failed"));

            // Act
            var result = await _controller.IsDefault(command);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task CreateUserActivity_Success_CreatesActivity()
        {
            // This tests the private CreateUserActivity method indirectly through LoadStateMonitoring
            // Arrange
            var stateDetails = new List<StateMonitorStatusListVm>();
            _mockProvider.Setup(p => p.LoadBalancer.GetStateMonitorStatusList()).ReturnsAsync(stateDetails);
            _mockProvider.Setup(p => p.UserActivity.CreateAsync(It.IsAny<CreateUserActivityCommand>()))
                .ReturnsAsync(new CreateUserActivityResponse());

            // Act
            await _controller.LoadStateMonitoring();

            // Assert
            _mockProvider.Verify(p => p.UserActivity.CreateAsync(It.Is<CreateUserActivityCommand>(cmd =>
                cmd.CompanyId == "test-company-123" &&
                cmd.UserId == "test-user-123" &&
                cmd.LoginName == "testuser" &&
                cmd.RequestUrl == "/test-url" &&
                cmd.HostAddress == "***********" &&
                cmd.Action == $"{ActivityType.View} StateMonitoring" &&
                cmd.Entity == "StateMonitoring" &&
                cmd.ActivityType == ActivityType.View.ToString() &&
                cmd.ActivityDetails == "State Monitoring viewed"
            )), Times.Once);
        }

        [Fact]
        public async Task CreateUserActivity_Exception_DoesNotThrow()
        {
            // This tests the private CreateUserActivity method exception handling indirectly
            // Arrange
            var stateDetails = new List<StateMonitorStatusListVm>();
            _mockProvider.Setup(p => p.LoadBalancer.GetStateMonitorStatusList()).ReturnsAsync(stateDetails);
            _mockProvider.Setup(p => p.UserActivity.CreateAsync(It.IsAny<CreateUserActivityCommand>()))
                .ThrowsAsync(new Exception("Activity creation failed"));

            // Act & Assert - Should not throw exception
            var result = await _controller.LoadStateMonitoring();

            // The method should still return success even if user activity creation fails
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }
    }
}
