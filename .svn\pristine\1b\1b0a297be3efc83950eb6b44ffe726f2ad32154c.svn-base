﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Commands;

public class UpdateWorkflowCategoryTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private readonly Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    private readonly UpdateWorkflowCategoryCommandHandler _handler;

    public UpdateWorkflowCategoryTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.UpdateWorkflowCategoryRepository(_workflowCategoryFixture.WorkflowCategories);

        _handler = new UpdateWorkflowCategoryCommandHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidWorkflowCategory_UpdateToWorkflowCategoriesRepo()
    {
        _workflowCategoryFixture.UpdateWorkflowCategoryCommand.Id = _workflowCategoryFixture.WorkflowCategories[0].ReferenceId;

        var result = await _handler.Handle(_workflowCategoryFixture.UpdateWorkflowCategoryCommand, CancellationToken.None);

        var workflowCategory = await _mockWorkflowCategoryRepository.Object.GetByReferenceIdAsync(result.WorkflowCategoryId);

        Assert.Equal(_workflowCategoryFixture.UpdateWorkflowCategoryCommand.Name, workflowCategory.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidWorkflowCategoryResponse_When_WorkflowCategoryUpdated()
    {
        _workflowCategoryFixture.UpdateWorkflowCategoryCommand.Id = _workflowCategoryFixture.WorkflowCategories[0].ReferenceId;

        var result = await _handler.Handle(_workflowCategoryFixture.UpdateWorkflowCategoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateWorkflowCategoryResponse));

        result.WorkflowCategoryId.ShouldBeGreaterThan(0.ToString());

        result.WorkflowCategoryId.ShouldBe(_workflowCategoryFixture.UpdateWorkflowCategoryCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _workflowCategoryFixture.UpdateWorkflowCategoryCommand.Id = _workflowCategoryFixture.WorkflowCategories[0].ReferenceId;

        await _handler.Handle(_workflowCategoryFixture.UpdateWorkflowCategoryCommand, CancellationToken.None);

        _mockWorkflowCategoryRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowCategoryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowCategory>()), Times.Once);
    }

    [Fact]

    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowCategoryId()
    {
        _workflowCategoryFixture.UpdateWorkflowCategoryCommand.Id = int.MaxValue.ToString();
        await Assert.ThrowsAsync<NotFoundException>(() =>
        _handler.Handle(_workflowCategoryFixture.UpdateWorkflowCategoryCommand, CancellationToken.None));
    }
}