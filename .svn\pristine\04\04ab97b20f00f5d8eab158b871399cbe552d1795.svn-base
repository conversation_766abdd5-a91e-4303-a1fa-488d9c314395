﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/css/dashboard.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div>
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-BIA-rules"></i>
                        <span>BIA Rules</span>
                    </h6>
                </div>
            </div>
        </div>
        <div class="card-body pt-0 px-1">
            <nav>
                <div class="bia nav nav-tabs" id="nav-tab" role="tablist">
                    <button class="nav-link active"  id="nav-home-tab" data-bs-toggle="tab" data-bs-target="#nav-home" type="button" role="tab" aria-controls="nav-home" aria-selected="true">InfraObject to Operational  Function</button>
                    <button class="nav-link" id="nav-profile-tab" data-bs-toggle="tab" data-bs-target="#nav-profile" type="button" role="tab" aria-controls="nav-profile" aria-selected="false"> Operational Function To Operational Function</button>
                    <button class="nav-link" id="nav-contact-tab" data-bs-toggle="tab" data-bs-target="#nav-contact" type="button" role="tab" aria-controls="nav-contact" aria-selected="false">Operational Service to Operational Service</button>
                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <div class="tab-pane fade show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                    <form>
                        <div class="d-flex align-items-center justify-content-end pb-3">
                            <div class="input-group me-2" style="width:350px !important">
                                <span class="input-group-text"><i class="cp-toggle"></i></span>
                                <select class="form-select" style="width:300px" data-placeholder="Select Operational Service to view Rules" id="treeBS">
                                    <option></option>
                                </select>
                            </div>
                            <button class="btn btn-sm" type="button" title="Export All BIA Rules"><i class="cp-export text-primary"></i></button>
                            <button class="btn btn-sm" type="button" title="Import All BIA Rules"><i class="cp-import text-primary"></i></button>
                            <button class="btn btn-sm btn_cancel" type="button" title="Create" id="createbiamodal" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-circle-plus text-primary"></i></button>
                        </div>
                    </form>
                    <div id="treewrapper" class="treewrapper" style="width:100%;height:250px"></div>

                    <div class="d-flex align-items-center gap-3 justify-content-end">
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-warning p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Partial Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge p-2 rounded-circle" style="background:#E76802"><span class="d-hidden"></span></span> <span>Major Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-danger p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Total Impact</span>
                        </div>
                    </div>
                    @* table start *@
                    <div class="bg-white my-3">
                        <table class="table" style="table-layout:fixed" id="InfratoBfTable">
                            <thead>
                                <tr>
                                    <th class="text-truncate" style="width:40px">#</th>
                                    <th class="text-truncate">InfraObject</th>
                                    <th class="text-truncate">JobName</th>
                                    <th class="text-truncate">Process Name</th>
                                    <th class="text-truncate">If InfraObject Component</th>
                                    <th class="text-truncate">Operational Function</th>
                                    <th class="text-truncate">Impact</th>
                                    <th class="text-truncate">Operational Service</th>
                                    <th class="text-truncate">Impact</th>
                                    <th class="text-truncate">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    @* table end *@
                </div>
                <div class="tab-pane fade" id="nav-profile" role="tabpanel" aria-labelledby="nav-profile-tab" tabindex="0">
                    <form>
                        <div class="d-flex align-items-center justify-content-end pb-3">
                            <button class="btn btn-sm" type="button" title="Import All BIA Rules"><i class="cp-import text-primary"></i></button>
                            <button class="btn btn-sm btn_cancel" type="button" title="Create" id="createbfmodal" data-bs-toggle="modal" data-bs-target="#BusinessFunctionModal"><i class="cp-circle-plus text-primary"></i></button>
                        </div>
                    </form>
                    <div id="treewrapper1" class="treewrapper1" style="width:100%;height:250px"></div>
                    <div class="d-flex align-items-center gap-3 justify-content-end">
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-warning p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Partial Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge p-2 rounded-circle" style="background:#E76802"><span class="d-hidden"></span></span> <span>Major Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-danger p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Total Impact</span>
                        </div>
                    </div>
                    @* table start *@
                    <div class="bg-white my-3">
                        <table class="table" style="table-layout:fixed" id="BftoBfTable">
                            <thead>
                                <tr>
                                    <th class="text-truncate" style="width:40px">#</th>
                                    @* <th class="text-truncate">Rule Code</th> *@
                                    <th class="text-truncate">If Operational Function</th>
                                    <th class="text-truncate">Impact</th>
                                    <th class="text-truncate">Operational Function</th>
                                    <th class="text-truncate">Impact</th>
                                    <th class="text-truncate">Effective Date</th>
                                    <th class="text-truncate">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    @* table end *@


                </div>
                <div class="tab-pane fade" id="nav-contact" role="tabpanel" aria-labelledby="nav-contact-tab" tabindex="0">
                    <form>
                        <div class="d-flex align-items-center justify-content-end pb-3">
                            <button class="btn btn-sm btn_cancel" type="button" title="Create" id="createsecondmodal" data-bs-toggle="modal" data-bs-target="#BusinessServiceModal"><i class="cp-circle-plus text-primary"></i></button>
                        </div>
                    </form>
                    <div id="treewrapper2" class="treewrapper2" style="width:100%;height:250px"></div>
                    <div class="d-flex align-items-center gap-3 justify-content-end">
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-warning p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Partial Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge p-2 rounded-circle" style="background:#E76802"><span class="d-hidden"></span></span> <span>Major Impact</span>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-danger p-2 rounded-circle"><span class="d-hidden"></span></span> <span>Total Impact</span>
                        </div>
                    </div>
                    @* table start *@
                    <div class="bg-white my-3">
                        <table class="table" style="table-layout:fixed" id="Bs_Bs-table">
                            <thead>
                                <tr>
                                    <th class="text-truncate" style="width:40px">#</th>
                                    @* <th class="text-truncate">Rule Code</th> *@
                                    <th class="text-truncate">If Operational Service</th>
                                    <th class="text-truncate">Impact</th>
                                    <th class="text-truncate">Operational Service</th>

                                    <th class="text-truncate">Effective Date</th>
                                    <th class="text-truncate">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    @* table end *@
                </div>
            </div>
        </div>
    </div>
    <div id="ConfigurationBIACreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true"></div>
    <div id="ConfigurationBIADelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true"></div>
    <!--InfraObject to Business Function Modal Start -->
    <div class="modal fade" id="CreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" autocomplete="off">
                <div class="modal-header">
                    <h6 class="page_title" title=""><i class="cp-infraobjects"></i><span> InfraObject to Operational Function</span></h6>
                    <button type="button" title="Close" class="btn-close btn_cancel" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">

                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <div class="form-group w-100">
                            <div class="form-label">Operational Service</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-business-service"></i></span>
                                <select class="form-select-modal" id="bia-infra-bs" data-placeholder="Select Operational Service">
                                    <option></option>
                                </select>
                            </div>
                            <span id="bia-infra-bs-error"></span>
                        </div>

                        <div class="form-group w-100">
                            <div class="form-label" title="">InfraObject</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                <select class="form-select-modal mb-0" id="bia-infra-infraobject" data-placeholder="Select InfraObject">
                                    <option></option>

                                </select>
                            </div>
                            <span id="bia-infra-infraobject-error"></span>
                        </div>

                        <div class="form-group w-100">
                            <div class="form-label" title="">Infra Component</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-component"></i></span>
                                <select class="form-select-modal" id="bia-infra-infracomponent" data-placeholder="Select Infra Component">
                                    <option></option>
                                </select>
                            </div>
                            <span id="bia-infra-infracomponent-error"></span>
                        </div>
                    </div>
                    @*       <div class="form-group d-flex gap-3 align-items-center">
                    <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="Apply_Process_Rule">
                    <label class="form-check-label" for="Apply_Process_Rule">
                    Apply Process Rule
                    </label>
                    </div>
                    <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="Apply_Queue_Rule">
                    <label class="form-check-label" for="Apply_Queue_Rule">
                    Apply Queue Rule
                    </label>
                    </div>
                    </div> *@
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-2 w-100">
                            <div class="form-group w-100">
                                <div class="form-label" title="">Is not available then</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-business-function"></i></span>
                                    <select class="form-select-modal" id="bia-infra-businessfunction" data-placeholder="Select Operational Function">
                                        <option></option>

                                    </select>
                                </div>
                                <span id="bia-infra-businessfunction-error"></span>
                            </div>
                            <div>will be</div>
                        </div>

                        <div class="d-flex align-items-center gap-2 w-100">
                            <div class="form-group w-100">
                                <div class="form-label" title="">Impact</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-Impact"></i></span>
                                    <select class="form-select-modal" id="bia-infra-impact" data-placeholder="Select Impact">
                                        <option></option>
                                        <option value="Partial">Partial</option>
                                        <option value="Major">Major</option>
                                        <option value="Total">Total</option>
                                    </select>
                                </div>
                                <span id="bia-infra-impact-error"></span>
                            </div>
                            <div>Then</div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-2 w-100">
                            <div class="form-group w-100">
                                <div class="form-label" title="">Operational Service</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-business-service"></i></span>
                                    <select class="form-select-modal" id="bia-infra-bs1" data-placeholder="Select Operational Service">
                                        <option></option>

                                    </select>
                                </div>
                                <span id="bia-infra-bs1-error"></span>
                            </div>
                            <div>will be</div>
                        </div>

                        <div class="d-flex align-items-center gap-2 w-100">
                            <div class="form-group w-100">
                                <div class="form-label" title="">Impact</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-Impact"></i></span>
                                    <select class="form-select-modal" id="bia-infra-impact1" data-placeholder="Select Impact">
                                        <option></option>
                                        <option value="Partial">Partial</option>
                                        <option value="Major">Major</option>
                                        <option value="Total">Total</option>
                                    </select>
                                </div>
                                <span id="bia-infra-impact1-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-label" title="">Effective Date</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                            <input autocomplete="off" class="form-control" id="bia-infra-date" max="23" min="0" name="ddlHours" ondragstart="return false" ondrop="return false" onkeydown="return true" onpaste="return false" placeholder="Hour(s)" type="datetime-local" cursorshover="true">
                        </div>
                        <span id="bia-infra-date-error"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="">Description <small class="text-secondary">( Optional )</small></div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-description"></i></span>
                            <input type="text" class="form-control" id="infra_BF_description" placeholder="Enter Description" maxlength="500" />
                        </div>
                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm btn_cancel" data-bs-dismiss="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" id="infra-bf-save" class="btn btn-primary btn-sm" title="">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--InfraObject to Business Function Modal End -->
    <!-- Business Function To Business Function Modal Start -->
    <div class="modal fade" id="BusinessFunctionModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" autocomplete="off">
                <div class="modal-header">
                    <h6 class="page_title" title="Operational Function To Operational Function"><i class="cp-business-function"></i><span>Operational Function To Operational Function</span></h6>
                    <button type="button" title="Close" class="btn-close btn_cancel" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">

                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <div class="form-group w-100">
                            <div class="form-label" title="">IF Operational Function</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-business-function"></i></span>
                                <select class="form-select-modal" id="bia-bf-businessfunction" data-placeholder="Select Operational Function">
                                    <option value=""></option>
                                </select>
                            </div>
                            <span id="bia-bf-businessfunction-error"></span>
                        </div>

                        <div class="form-group w-100">
                            <div class="form-label" title="">Is</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal mb-0" id="bia-bf-impact" data-placeholder="Select Impact">
                                    <option></option>
                                    <option value="Partial">Partial</option>
                                    <option value="Major">Major</option>
                                    <option value="Total">Total</option>
                                </select>
                            </div>
                            <span id="bia-bf-impact-error"></span>
                        </div>


                        <div class="form-group w-100">
                            <div class="form-label" title="">Then</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-business-function"></i></span>
                                <select class="form-select-modal" id="bia-bf-businessfunction1" data-placeholder="Select Operational Function">
                                    <option></option>

                                </select>
                            </div>
                            <span id="bia-bf-businessfunction1-error"></span>
                        </div>
                        <div class="form-group w-100">
                            <div class="form-label" title="">Will be</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal" id="bia-bf-impact1" data-placeholder="Select Impact">
                                    <option></option>
                                    <option value="Partial">Partial</option>
                                    <option value="Major">Major</option>
                                    <option value="Total">Total</option>
                                </select>
                            </div>
                            <span id="bia-bf-impact1-error"></span>
                        </div>
                    </div>


                    <div class="form-group">
                        <div class="form-label" title="">Effective Date</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                            <input autocomplete="off" class="form-control" id="bia-bf-date" max="23" min="0" name="ddlHours" ondragstart="return false" ondrop="return false" onkeydown="return true" onpaste="return false" placeholder="Hour(s)" type="datetime-local" cursorshover="true">
                        </div>
                        <span id="bia-bf-date-error"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="">Description <small class="text-secondary">( Optional )</small></div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-description"></i></span>
                            <input type="text" class="form-control" id="BF_BF_description" placeholder="Enter Description" maxlength="500" />
                        </div>

                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm btn_cancel" data-bs-dismiss="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="bf_bf_save" title="">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Business Function To Business Function Modal End -->
    <!--Business Service To Business Service Modal Start -->
    <div class="modal fade" id="BusinessServiceModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" autocomplete="off">
                <div class="modal-header">
                    <h6 class="page_title" title="Operational Service To Operational Service"><i class="cp-business-service"></i><span>Operational Service To Operational Service</span></h6>
                    <button type="button" title="Close" class="btn-close btn_cancel" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">

                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <div class="form-group w-100">
                            <div class="form-label" title="">Operational Service</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-business-service"></i></span>
                                <select class="form-select-modal" id="bia-bs-businessservice" data-placeholder=" Select Operational Service" >
                                    <option></option>

                                </select>
                            </div>
                            <span id="bia-bs-businessservice-error"></span>
                        </div>

                        <div class="form-group w-100">
                            <div class="form-label" title="">Is</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal mb-0" id="bia-bs-impact" data-placeholder="Select Impact">
                                    <option></option>
                                    <option value="Partial">Partial</option>
                                    <option value="Major">Major</option>
                                    <option value="Total">Total</option>
                                </select>
                            </div>
                            <span id="bia-bs-impact-error"></span>
                        </div>


                        <div class="form-group w-100">
                            <div class="form-label" title="">By Service</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-infra-omponent"></i></span>
                                <select class="form-select-modal" id="bia-bs-infracomponent" data-placeholder="Select Operational Service" multiple>
                                    <option></option>

                                </select>
                            </div>
                            <span id="bia-bs-infracomponent-error"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-label" title="">Effective Date</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                            <input autocomplete="off" class="form-control" id="bia-bs-date" max="23" min="0" name="ddlHours" ondragstart="return false" ondrop="return false" onkeydown="return true" onpaste="return false" placeholder="Hour(s)" type="datetime-local" cursorshover="true">
                        </div>
                        <span id="bia-bs-date-error"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label" title="">Description <small class="text-secondary">( Optional )</small></div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-description"></i></span>
                            <input type="text" class="form-control" id="BS_BS_description" placeholder="Enter Description" maxlength="500" />
                        </div>

                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm btn_cancel" data-bs-dismiss="modal" title="" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="bs_bs_save" title="">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--Business Service To Business Service Modal End -->
    <!--Modal Reject-->
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="InfratoBf_deleted_id"></span>
                            data?
                        </p>

                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="infratoBf_confirmDeleteButton">Yes</button>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal1" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="BftoBf_deleted_id"></span>
                            data?
                        </p>

                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="BftoBf_confirmDeleteButton">Yes</button>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal2" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body  pt-0">
                    <div class="text-center">
                        <h4>Are you sure?</h4>
                        <p class="d-flex align-items-center justify-content-center gap-1">
                            You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="BstoBs_deleted_id"></span>
                            data?
                        </p>
                    </div>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="BstoBs_confirmDeleteButton">Yes</button>
                </div>

            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/lib/d3charts/d3.min.js"></script>

<script src="~/js/Configuration/FIA-BIA/BIA Rules/biaRules.js"></script>