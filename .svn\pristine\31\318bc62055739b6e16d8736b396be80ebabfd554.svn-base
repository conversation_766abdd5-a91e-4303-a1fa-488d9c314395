﻿using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
//using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetOracleMonitorStatusByInfraObjectId;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class OracleMonitorStatusService : BaseService, IOracleMonitorStatusService
{
    public OracleMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateOracleMonitorStatusCommand createOracleMonitorStatusCommand)
    {
        Logger.LogDebug($"Create Oracle Monitor Status '{createOracleMonitorStatusCommand}'");

        return await Mediator.Send(createOracleMonitorStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateOracleMonitorStatusCommand updateOracleMonitorStatusCommand)
    {
        Logger.LogDebug($"Update Oracle Monitor Status '{updateOracleMonitorStatusCommand}'");

        return await Mediator.Send(updateOracleMonitorStatusCommand);
    }

    public async Task<List<OracleMonitorStatusListVm>> GetOracleMonitorStatusList()
    {
        Logger.LogDebug("Get All  Oracle Monitor Status");

        return await Mediator.Send(new GetOracleMonitorStatusListQuery());
    }

    public async Task<OracleMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Oracle MonitorStatus Detail By Id");

        Logger.LogDebug($"Get Oracle Monitor Status Detail by Id'{id}' ");

        return await Mediator.Send(new GetOracleMonitorStatusDetailQuery { Id = id });
    }

    public async Task<List<OracleMonitorStatusDetailByTypeVm>> GetOracleMonitorStatusDetailByTypeVm(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "Oracle Monitor Status Detail By Type");

        Logger.LogDebug($"Get Oracle Monitor Status Detail by Id'{type}' ");

        return await Mediator.Send(new GetOracleMonitorStatusDetailByTypeQuery { Type = type });
    }


    public async Task<PaginatedResult<OracleMonitorStatusListVm>> GetPaginatedOracleMonitorStatus(
        GetOracleMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Oracle MonitorStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllOracleMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }

    //public async Task<List<OracleMonitorStatusByInfraObjectIdVm>> GetOracleMonitorStatusByInfraObjectIdVm(string infraObjectId)
    //{
    //    Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId");

    //    Logger.LogDebug($"Get Oracle Monitor Status Detail by InfraObjectId'{infraObjectId}' ");

    //    return await Mediator.Send(new GetOracleMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    //}
}