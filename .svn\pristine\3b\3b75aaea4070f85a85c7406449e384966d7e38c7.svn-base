﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-parameter-name"></i><span>Prerequisite Profile List</span></h6>
            <form class="d-flex gap-2 align-items-center">
                <div class="input-group w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off">
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="displayName=" id="DisplayName">
                                        <label class="form-check-label" for="DisplayName">
                                            Display Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#ProfileModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Profile Name</th>
                        <th>Solution Name</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>MySQL Profile</td>
                        <td>PowerMax_Monitoring_Template</td>
                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td>MSSQL_AO</td>
                        <td>ZertoVPG_Monitoring_Template</td>
                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td>Mssql_AO_Server_Database</td>
                        <td>Azure_Storage_Monitor_Template</td>
                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>4</td>
                        <td>Hard Disk</td>
                        <td>AzureDatabase_MySQL_PaaS</td>
                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>5</td>
                        <td>OS version</td>
                        <td>PowerMax_Monitoring_Template</td>
                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <!-- Prerequisite Profile Configuration - Modal Start -->
    <div class="modal fade" id="ProfileModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="Prerequisite Profile Configuration"><i class="cp-parameter-name"></i><span>Prerequisite Profile Configuration</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">
                                    Profile Name
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-profile-name"></i></span>
                                    <input type="text" class="form-control" placeholder="Enter Profile Name" maxlength="100" autocomplete="off">
                                </div>
                            </div>
                            <div class="card Card_Design_None mb-0" style="box-shadow:0 .125rem .25rem rgb(0 0 0 / 15%)!important">
                                <div class="card-header header">
                                    <h6 class="sub-title">Parameters List</h6>
                                </div>
                                <div class="card-body p-0 pe-2" style="height:calc(100vh - 320px);overflow:auto">
                                    <div class="form-control">
                                        <div class="input-group">
                                            <input class="form-control" placeholder="Search" type="text" id="parameter_list_search" autocomplete="off">
                                            <div class="input-group-text" role="button">
                                                <div class="dropdown">
                                                    <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                                                    <ul class="dropdown-menu filter-dropdown">
                                                        <li>
                                                            <h6 class="dropdown-header">Filter Search</h6>
                                                        </li>
                                                        <li class="dropdown-item">
                                                            <div>
                                                                <input class="form-check-input" type="checkbox" value="" id="Name">
                                                                <label class="form-check-label" for="Name">
                                                                    Name
                                                                </label>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="nodata_parameter">

                                        <details class="filterTitle">
                                            <summary class=" categorysummary text-truncate" title=""><i class="cp-database me-1"></i>Database</summary>
                                            <div class="d-flex align-items-center justify-content-between ms-3">
                                                <span role="button"><i class="cp-os-type me-1"></i>OS Version</span>
                                                <span>
                                                    <input class="form-check" type="checkbox">
                                                </span>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between ms-3">
                                                <span role="button"><i class="cp-Xeon me-1"></i>CPU</span>
                                                <span>
                                                    <input class="form-check" type="checkbox">
                                                </span>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between ms-3">
                                                <span role="button"><i class="cp-ram me-1"></i>RAM</span>
                                                <span>
                                                    <input class="form-check" type="checkbox">
                                                </span>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-between ms-3">
                                                <span role="button"><i class="cp-disk-controller me-1"></i>Hard Disk</span>
                                                <span>
                                                    <input class="form-check" type="checkbox">
                                                </span>
                                            </div>
                                        </details>
                                        <details class="filterTitle">
                                            <summary class=" categorysummary text-truncate" title=""><i class="cp-network me-1"></i>Network</summary>
                                            <div class="d-flex align-items-center justify-content-between ms-3">
                                                <span role="button">
                                                    <i class="cp-aborted me-1"></i>StartUpConfigurationMode
                                                </span>
                                                <span><input class="form-check" type="checkbox" name=""></span>
                                            </div>

                                        </details>
                                    </div>

                                </div>
                                <div class="card-footer text-center">
                                    <button class="btn btn-sm" style="background:#41C100;color:#fff">Move<i class="ms-1 cp-right-linearrow align-middle"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="col-8">
                            <div class="card Card_Design_None mb-0" style="box-shadow:0 .125rem .25rem rgb(0 0 0 / 15%)!important">
                                <div class="card-header header">
                                    <h6 class="sub-title">Profile Parameters</h6>
                                </div>
                                <div class="card-body p-0 px-2" style="height:calc(100vh - 205px);overflow:auto">
                                    <div>
                                        <div>
                                            <details>
                                                <summary class="categorysummary d-flex align-items-center text-truncate">
                                                    <span class="w-100 d-flex align-items-center justify-content-between">
                                                        <span><i class="cp-database me-1"></i>Database</span>
                                                        <span class="cp-Delete"></span>
                                                    </span>
                                                </summary>
                                                <div>
                                                    <div class="d-flex align-items-center justify-content-between ms-4 mb-2">
                                                        <span role="button" class="text-truncate w-75"><i class="cp-os-type me-1"></i>OS Version</span>

                                                        <div class="d-flex gap-2 ">
                                                            <span style="font-size: 10px;" class="badge text-bg-warning py-1 d-none">Thershold<span class=""></span></span>
                                                            <div class="dropdown">
                                                                <i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>
                                                                <ul class="dropdown-menu" style="">
                                                                    <li><a class="dropdown-item">Disabled</a></li>
                                                                    <li><a class="dropdown-item">Delete</a></li>
                                                                    <li><a class="dropdown-item d-flex align-items-center justify-content-between">Notification<input type="checkbox" class="form-check"></a></li>
                                                                    <li><a class="dropdown-item">Threshold</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between ms-4 mb-2">
                                                        <span role="button" class="text-truncate w-75"><i class="cp-Xeon me-1"></i>CPU</span>
                                                        <div class="d-flex gap-2 ">
                                                            <span style="font-size: 10px;" class="badge text-bg-warning py-1 d-none">Thershold<span class=""></span></span>
                                                            <div class="dropdown">
                                                                <i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>
                                                                <ul class="dropdown-menu" style="">
                                                                    <li><a class="dropdown-item">Disabled</a></li>
                                                                    <li><a class="dropdown-item">Delete</a></li>
                                                                    <li><a class="dropdown-item d-flex align-items-center justify-content-between">Notification<input type="checkbox" class="form-check"></a></li>
                                                                    <li><a class="dropdown-item">Threshold</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between ms-4 mb-2">
                                                        <span role="button" class="text-truncate w-75"><i class="cp-ram me-1"></i>RAM</span>
                                                        <div class="d-flex gap-2 ">
                                                            <span style="font-size: 10px;" class="badge text-bg-warning py-1 d-none">Thershold<span class=""></span></span>
                                                            <div class="dropdown">
                                                                <i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>
                                                                <ul class="dropdown-menu" style="">
                                                                    <li><a class="dropdown-item">Disabled</a></li>
                                                                    <li><a class="dropdown-item">Delete</a></li>
                                                                    <li><a class="dropdown-item d-flex align-items-center justify-content-between">Notification<input type="checkbox" class="form-check"></a></li>
                                                                    <li><a class="dropdown-item">Threshold</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between ms-4 mb-2">
                                                        <span role="button" class="text-truncate w-75"><i class="cp-disk-controller me-1"></i>Hard Disk</span>
                                                        <div class="d-flex gap-2 ">
                                                            <span style="font-size: 10px;" class="badge text-bg-warning py-1 d-none">Thershold<span class=""></span></span>
                                                            <div class="dropdown">
                                                                <i class="cp-vertical-dots" data-bs-toggle="dropdown" aria-expanded="false" role="button"></i>
                                                                <ul class="dropdown-menu" style="">
                                                                    <li><a class="dropdown-item">Disabled</a></li>
                                                                    <li><a class="dropdown-item">Delete</a></li>
                                                                    <li><a class="dropdown-item d-flex align-items-center justify-content-between">Notification<input type="checkbox" class="form-check"></a></li>
                                                                    <li><a class="dropdown-item">Threshold</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </details>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save" data-bs-toggle="modal" data-bs-target="#SolutionModal">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Prerequisite Profile Configuration - Modal End -->

    <div class="modal fade" id="SolutionModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="Prerequisite Profile Configuration"><i class="cp-solution-name"></i><span>Solution</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">
                            Solution Name
                        </label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input type="text" class="form-control" placeholder="Enter Solution Name" maxlength="100" autocomplete="off">
                        </div>
                    </div>

                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>



    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <form>
                    <div class="modal-header p-0">
                        <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
                    </div>
                    <div class="modal-body text-center pt-0">
                        <h5 class="fw-bold">Are you sure?</h5>
                        <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span> data?</p>
                    </div>
                    <div class="modal-footer gap-2 justify-content-center">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                        <button type="submit" class="btn btn-primary btn-sm">Yes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
