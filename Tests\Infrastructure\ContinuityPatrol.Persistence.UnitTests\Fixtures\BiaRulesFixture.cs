using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BiaRulesFixture : IDisposable
{
    public List<BiaRules> BiaRulesPaginationList { get; set; }
    public List<BiaRules> BiaRulesList { get; set; }
    public BiaRules BiaRulesDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BiaRulesFixture()
    {
        var fixture = new Fixture();

        BiaRulesList = fixture.Create<List<BiaRules>>();

        BiaRulesPaginationList = fixture.CreateMany<BiaRules>(20).ToList();

        BiaRulesPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BiaRulesPaginationList.ForEach(x => x.IsActive = true);

        BiaRulesList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BiaRulesList.ForEach(x => x.IsActive = true);

        BiaRulesDto = fixture.Create<BiaRules>();
        BiaRulesDto.ReferenceId = Guid.NewGuid().ToString();
        BiaRulesDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
