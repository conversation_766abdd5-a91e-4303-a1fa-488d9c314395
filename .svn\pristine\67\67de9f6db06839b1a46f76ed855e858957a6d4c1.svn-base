using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberComponentGroupRepositoryTests : IClassFixture<CyberComponentGroupFixture>
{
    private readonly CyberComponentGroupFixture _cyberComponentGroupFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberComponentGroupRepository _repository;

    public CyberComponentGroupRepositoryTests(CyberComponentGroupFixture cyberComponentGroupFixture)
    {
        _cyberComponentGroupFixture = cyberComponentGroupFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberComponentGroupRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetCyberComponentGroupsBySiteId Tests

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldReturnGroupsForSite()
    {
        // Arrange
        var siteId = "bb6d2c67-f512-4292-93cc-0bc87043b18b";
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        componentGroups[0].SiteId = siteId; 
        componentGroups[2].SiteId = siteId; 
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(siteId, x.SiteId));
    }

    [Fact]
    public async Task GetCyberComponentGroupsBySiteId_ShouldReturnEmpty_WhenNoGroupsForSite()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetCyberComponentGroupsBySiteId("NON_EXISTENT_SITE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetComponentGroupsByComponentId Tests

    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldReturnGroupsContainingComponent()
    {
        // Arrange
        var componentId = "351da211-cb11-4bf8-86c3-afce9328ecaa";
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        componentGroups[0].ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp2\"},{\"id\":\"291aa018-3e80-4f75-89ae-77f38326e16c\",\"name\":\"test43\"}]";
        componentGroups[1].ComponentProperties = "[{\"id\":\"351da211-cb11-4bf8-86c3-afce9328ecaa\",\"name\":\"comp2\"},{\"id\":\"291aa018-3e80-4f75-89ae-77f38326e16c\",\"name\":\"test43\"}]";

        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId(componentId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(componentId, x.ComponentProperties));
    }

    [Fact]
    public async Task GetComponentGroupsByComponentId_ShouldReturnEmpty_WhenNoGroupsContainComponent()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.GetComponentGroupsByComponentId("NON_EXISTENT_COMPONENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "ExistingGroupName";
        await _repository.AddAsync(componentGroup);

        // Act
        var result = await _repository.IsNameExist("ExistingGroupName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;
        await _repository.AddRangeAsync(componentGroups);

        // Act
        var result = await _repository.IsNameExist("NonExistentGroupName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var componentGroup = _cyberComponentGroupFixture.CyberComponentGroupDto;
        componentGroup.GroupName = "SameGroupName";
        await _repository.AddAsync(componentGroup);

        // Act
        var result = !await _repository.IsNameExist("SameGroupName", componentGroup.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion



    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleSiteAndComponentAssignments()
    {
        // Arrange
        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;

        componentGroups[0].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd963";
        componentGroups[0].GroupName = "DatabaseGroup";
        componentGroups[1].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd965";
        componentGroups[1].GroupName = "WebGroup";
        componentGroups[2].SiteId = "39cd8f83-2490-43c7-9b47-41bebfcfd964";
        componentGroups[2].GroupName = "ApplicationGroup";


        await _repository.AddRangeAsync(componentGroups);

        // Act
        var dbsite= await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd963"));
        var website = await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd965"));
        var appsite = await _repository.FindByFilterAsync(x => x.SiteId.Contains("39cd8f83-2490-43c7-9b47-41bebfcfd964"));

        // Assert
        Assert.Single(dbsite);
        Assert.Single(website);
        Assert.Single(appsite);
        Assert.Contains("Database", dbsite.First().GroupName);
        Assert.Contains("App", appsite.First().GroupName);
        Assert.Contains("Web", website.First().GroupName);
    }

    #endregion

    #region Group Name and Component Properties Tests

    [Fact]
    public async Task Repository_ShouldFilterByGroupName()
    {
        // Arrange

        var componentGroups = _cyberComponentGroupFixture.CyberComponentGroupList;

        componentGroups[0].GroupName = "ApplicationGroup";
 
        componentGroups[1].GroupName = "DatabaseGroup";

        componentGroups[2].GroupName = "WebGroup";
   
        await _repository.AddRangeAsync(componentGroups);
        // Act
        var databaseGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Database"));
        var webGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Web"));
        var applicationGroups = await _repository.FindByFilterAsync(x => x.GroupName.Contains("Application"));

        // Assert
        Assert.Single(databaseGroups);
        Assert.Single(webGroups);
        Assert.Single(applicationGroups);
        Assert.Contains("Database", databaseGroups.First().GroupName);
        Assert.Contains("Web", webGroups.First().GroupName);
        Assert.Contains("Application", applicationGroups.First().GroupName);
    }

    #endregion
}
