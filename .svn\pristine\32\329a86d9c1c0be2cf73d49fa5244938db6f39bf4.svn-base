using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Events.Update;

namespace ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Update;

public class UpdateWorkflowActionFieldMasterCommandHandler : IRequestHandler<UpdateWorkflowActionFieldMasterCommand,
    UpdateWorkflowActionFieldMasterResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowActionFieldMasterRepository _workflowActionFieldMasterRepository;

    public UpdateWorkflowActionFieldMasterCommandHandler(IMapper mapper,
        IWorkflowActionFieldMasterRepository workflowActionFieldMasterRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _workflowActionFieldMasterRepository = workflowActionFieldMasterRepository;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowActionFieldMasterResponse> Handle(UpdateWorkflowActionFieldMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowActionFieldMasterRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.WorkflowActionFieldMaster), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowActionFieldMasterCommand),
            typeof(Domain.Entities.WorkflowActionFieldMaster));

        await _workflowActionFieldMasterRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowActionFieldMasterResponse
        {
            Message = Message.Update(nameof(Domain.Entities.WorkflowActionFieldMaster), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new WorkflowActionFieldMasterUpdatedEvent { Name = eventToUpdate.Name },
            cancellationToken);

        return response;
    }
}