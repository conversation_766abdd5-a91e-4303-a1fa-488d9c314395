﻿using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Create;
using ContinuityPatrol.Application.Features.HacmpCluster.Commands.Update;
using ContinuityPatrol.Application.Features.HacmpCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel;
using ContinuityPatrol.Application.Features.HacmpCluster.Events.PaginatedView;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class HACMPClusterController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<HACMPClusterController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    public HACMPClusterController(IPublisher publisher, ILogger<HACMPClusterController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }


    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in HACMPCluster");

        await _publisher.Publish(new HacmpClusterPaginatedViewEvent());

        var clusterList = await _dataProvider.HacmpCluster.GetHacmpClusterList();
        var serverList = await _dataProvider.Server.GetServerNames();

        var cluster = new HacmpClusterViewModel
        {
            Cluster = clusterList,
            Servers = serverList
        };

        return View(cluster);
    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetHacmpClusterPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in HACMPCluster");

        try
        {
            _logger.LogDebug("Successfully retrieved hacmpCluster paginated list on HACMPCluster page");

            return Json(await _dataProvider.HacmpCluster.GetPaginatedHacmpClusters(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on HacmpCluster page while processing the pagination request",ex);

            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(HacmpClusterViewModel clusterModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in HACMPCluster");

        var clusterModelId = Request.Form["id"].ToString();

        try
        {
            if (clusterModelId.IsNullOrWhiteSpace())
            {
                var createCommand = _mapper.Map<CreateHacmpClusterCommand>(clusterModel);

                var response = await _dataProvider.HacmpCluster.CreateAsync(createCommand);

                _logger.LogDebug($"Creating HACMPCluster '{createCommand.Name}'");

                TempData.NotifySuccess(response.Message);

            }
            else
            {
                var updateCommand = _mapper.Map<UpdateHacmpClusterCommand>(clusterModel);

                var response = await _dataProvider.HacmpCluster.UpdateAsync(updateCommand);

                _logger.LogDebug($"Updating HACMPCluster '{updateCommand.Name}'");

                TempData.NotifySuccess(response.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in HACMPCluster, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on HacmpCluster page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on HacmpCluster page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in HACMPCluster");

        try
        {
            var result = await _dataProvider.HacmpCluster.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in HACMPCluster");

            TempData.NotifySuccess(result.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting record on HACMPCluster.", ex);

            return RedirectToAction("List");
        }

    }
   
    public async Task<bool> IsClusterNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsClusterNameExist method in HACMPCluster");

        try
        {
            var nameExist = await _dataProvider.HacmpCluster.IsHacmpClusterNameExist(name, id);

            _logger.LogDebug("Returning result for IsClusterNameExist on HACMPCluster");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on HACMPCluster page while checking if HACMPCluster name exists for : {name}.", ex);

            return false;
        }
    }

}

