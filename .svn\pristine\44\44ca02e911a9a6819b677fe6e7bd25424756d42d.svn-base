using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.RoboCopy.Events.Delete;

public class RoboCopyDeletedEventHandler : INotificationHandler<RoboCopyDeletedEvent>
{
    private readonly ILogger<RoboCopyDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RoboCopyDeletedEventHandler(ILoggedInUserService userService, ILogger<RoboCopyDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(RoboCopyDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} RoboCopyOptions",
            Entity = "RoboCopyOptions",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"RoboCopy '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"RoboCopy '{deletedEvent.Name}' deleted successfully.");
    }
}