using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CompanyRepositoryTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CompanyRepository _repository;

    public CompanyRepositoryTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CompanyRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;

        // Act
        var result = await _repository.AddAsync(company);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(company.Name, result.Name);
        Assert.Equal(company.DisplayName, result.DisplayName);
        Assert.Single(_dbContext.Companies);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        await _repository.AddAsync(company);

        company.Name = "UpdatedName";
        company.DisplayName = "UpdatedDisplayName";
        company.WebAddress = "https://updated.com";

        // Act
        var result = await _repository.UpdateAsync(company);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDisplayName", result.DisplayName);
        Assert.Equal("https://updated.com", result.WebAddress);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.DeleteAsync(company);

        // Assert
        Assert.Equal(company.Name, result.Name);
        Assert.Empty(_dbContext.Companies);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        var addedEntity = await _repository.AddAsync(company);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.GetByReferenceIdAsync(company.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(company.ReferenceId, result.ReferenceId);
        Assert.Equal(company.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var companies = _companyFixture.CompanyList;
        await _repository.AddRangeAsync(companies);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(companies.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAllCompanyNames Tests

    [Fact]
    public async Task GetAllCompanyNames_ShouldReturnCompanyNames()
    {
        // Arrange
        var companies = new List<Company>
        {
            new Company 
            { 
                Name = "Company1",
                DisplayName = "Company One",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsParent = true
            },
            new Company 
            { 
                Name = "Company2",
                DisplayName = "Company Two",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true,
                IsParent = false
            }
        };

        await _repository.AddRangeAsync(companies);

        // Act
        var result = await _repository.GetAllCompanyNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.DisplayName));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetAllCompanyNames_ShouldReturnEmpty_WhenNoCompanies()
    {
        // Act
        var result = await _repository.GetAllCompanyNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCompanyByLoginCompanyId Tests

    [Fact]
    public async Task GetCompanyByLoginCompanyId_ShouldReturnCompany_WhenExists()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.GetCompanyByLoginCompanyId(company.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(company.ReferenceId, result.ReferenceId);
        Assert.Equal(company.Name, result.Name);
    }

    [Fact]
    public async Task GetCompanyByLoginCompanyId_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetCompanyByLoginCompanyId("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetParentCompanyByLoginCompanyId Tests

    [Fact]
    public async Task GetParentCompanyByLoginCompanyId_ShouldReturnParentCompany_WhenExists()
    {
        // Arrange
        var parentCompany = new Company 
        { 
            Name = "ParentCompany",
            DisplayName = "Parent Company",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            IsParent = true
        };

        await _repository.AddAsync(parentCompany);

        // Act
        var result = await _repository.GetParentCompanyByLoginCompanyId(parentCompany.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(parentCompany.ReferenceId, result.ReferenceId);
        Assert.Equal(parentCompany.DisplayName, result.DisplayName);
        Assert.True(result.IsParent);
    }

    [Fact]
    public async Task GetParentCompanyByLoginCompanyId_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetParentCompanyByLoginCompanyId("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region IsCompanyAndDisplayNameUnique Tests

    [Fact]
    public async Task IsCompanyAndDisplayNameUnique_ShouldReturnTrue_WhenNameOrDisplayNameExists()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        company.Name = "ExistingName";
        company.DisplayName = "ExistingDisplayName";
        await _repository.AddAsync(company);

        // Act
        var result1 = await _repository.IsCompanyAndDisplayNameUnique("ExistingName", "DifferentDisplayName");
        var result2 = await _repository.IsCompanyAndDisplayNameUnique("DifferentName", "ExistingDisplayName");

        // Assert
        Assert.True(result1);
        Assert.True(result2);
    }

    [Fact]
    public async Task IsCompanyAndDisplayNameUnique_ShouldReturnFalse_WhenNeitherExists()
    {
        // Arrange
        var companies = _companyFixture.CompanyList;
        await _repository.AddRangeAsync(companies);

        // Act
        var result = await _repository.IsCompanyAndDisplayNameUnique("NonExistentName", "NonExistentDisplayName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsDisplayNameExist Tests

    [Fact]
    public async Task IsDisplayNameExist_ShouldReturnTrue_WhenDisplayNameExistsAndIdIsInvalid()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        company.DisplayName = "ExistingDisplayName";
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.IsDisplayNameExist("ExistingDisplayName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDisplayNameExist_ShouldReturnFalse_WhenDisplayNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var companies = _companyFixture.CompanyList;
        await _repository.AddRangeAsync(companies);

        // Act
        var result = await _repository.IsDisplayNameExist("NonExistentDisplayName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsDisplayNameExist_ShouldReturnFalse_WhenDisplayNameExistsForSameEntity()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        company.DisplayName = "SameDisplayName";
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.IsDisplayNameExist("SameDisplayName", company.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        company.Name = "ExistingName";
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.IsNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var companies = _companyFixture.CompanyList;
        await _repository.AddRangeAsync(companies);

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var company = _companyFixture.CompanyDto;
        company.Name = "SameName";
        await _repository.AddAsync(company);

        // Act
        var result = await _repository.IsNameExist("SameName", company.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var company = _companyFixture.CompanyList;
        var company1 = company[0];
        var company2 = company[2];
        company2.ReferenceId = Guid.NewGuid().ToString();
        company2.Name = "DifferentName";
 

        // Act
        var task1 = _repository.AddAsync(company1);
        var task2 = _repository.AddAsync(company2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.Companies.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var companies = _companyFixture.CompanyList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(companies);
        var initialCount = companies.Count;

        var toUpdate = companies.Take(2).ToList();
        toUpdate.ForEach(x => x.WebAddress = "https://updated.com");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = companies.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.WebAddress == "https://updated.com").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
