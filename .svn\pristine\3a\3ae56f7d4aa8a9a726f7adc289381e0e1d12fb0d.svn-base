﻿namespace ContinuityPatrol.Application.Features.Workflow.Commands.Draft;

public class UpdateWorkflowIsDraftCommandValidator:AbstractValidator<UpdateWorkflowIsDraftCommand>
{
    private readonly IWorkflowRepository _workflowRepository;

    public UpdateWorkflowIsDraftCommandValidator(IWorkflowRepository workflowRepository)
    {
        _workflowRepository = workflowRepository;

        RuleFor(x => x).NotEmpty()
            .MustAsync(IsValidGuid)
            .WithMessage("Invalid Id");

        RuleFor(x => x).NotEmpty()
            .MustAsync(IsWorkflowVerified)
            .WithMessage("Workflow not yet verified.");
    }

    public Task<bool> IsValidGuid(UpdateWorkflowIsDraftCommand command,CancellationToken cancellationToken)
    {
        var isValid= Guid.TryParse(command.Id, out _);

        return Task.FromResult(isValid);
    } 
    public async Task<bool> IsWorkflowVerified(UpdateWorkflowIsDraftCommand command,CancellationToken cancellationToken)
    {
        var workflow = await _workflowRepository.GetByReferenceIdAsync(command?.Id);

        if (workflow == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), command!.Name);

        return workflow.IsVerify;
    }
}
