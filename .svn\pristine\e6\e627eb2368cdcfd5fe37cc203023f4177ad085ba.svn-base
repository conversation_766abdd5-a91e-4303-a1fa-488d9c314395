using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SchedulerWorkflowActionResultsRepositoryTests : IClassFixture<SchedulerWorkflowActionResultsFixture>, IDisposable
{
    private readonly SchedulerWorkflowActionResultsFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly SchedulerWorkflowActionResultsRepository _repository;

    public SchedulerWorkflowActionResultsRepositoryTests(SchedulerWorkflowActionResultsFixture fixture)
    {
        _fixture = fixture;
        _dbContext = _fixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(SchedulerWorkflowActionResultsFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _repository = new SchedulerWorkflowActionResultsRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        ClearDatabase().Wait();
    }

    #region GetSchedulerWorkflowActionResultListByWorkflowId Tests

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnMatchingResults_WhenWorkflowIdAndInfraReferenceIdMatch()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        var schedulerWorkflowActionResults = new List<SchedulerWorkflowActionResults>
        {
            new()
            {
                WorkflowId = workflowId,
                InfraObjectSchedulerLogsId = infraReferenceId,
                NodeId = "NODE_001",
                NodeName = "Node 1",
                WorkflowName = "Test Workflow",
                InfraObjectId = "INFRA_OBJ_001",
                InfraObjectName = "Test Infra Object",
                StepId = "STEP_001",
                WorkflowActionId = "ACTION_001",
                WorkflowActionName = "Test Action",
                Status = "Success",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1),
                Message = "Test message 1",
                IsActive = true
            },
            new()
            {
                WorkflowId = workflowId,
                InfraObjectSchedulerLogsId = infraReferenceId,
                NodeId = "NODE_002",
                NodeName = "Node 2",
                WorkflowName = "Test Workflow",
                InfraObjectId = "INFRA_OBJ_002",
                InfraObjectName = "Test Infra Object 2",
                StepId = "STEP_002",
                WorkflowActionId = "ACTION_002",
                WorkflowActionName = "Test Action 2",
                Status = "Failed",
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now,
                Message = "Test message 2",
                IsActive = true
            },
            new()
            {
                WorkflowId = "DIFFERENT_WORKFLOW",
                InfraObjectSchedulerLogsId = infraReferenceId,
                NodeId = "NODE_003",
                NodeName = "Node 3",
                WorkflowName = "Different Workflow",
                InfraObjectId = "INFRA_OBJ_003",
                InfraObjectName = "Test Infra Object 3",
                StepId = "STEP_003",
                WorkflowActionId = "ACTION_003",
                WorkflowActionName = "Test Action 3",
                Status = "Success",
                StartTime = DateTime.Now.AddHours(-3),
                EndTime = DateTime.Now.AddHours(-2),
                Message = "Test message 3",
                IsActive = true
            }
        };

        foreach (var result in schedulerWorkflowActionResults)
        {
            await _repository.AddAsync(result);
        }

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(2, results.Count);
        Assert.All(results, r => Assert.Equal(workflowId, r.WorkflowId));
        Assert.All(results, r => Assert.Equal(infraReferenceId, r.InfraObjectSchedulerLogsId));
        Assert.Contains(results, r => r.NodeName == "Node 1");
        Assert.Contains(results, r => r.NodeName == "Node 2");
        Assert.DoesNotContain(results, r => r.NodeName == "Node 3");
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnEmpty_WhenWorkflowIdDoesNotMatch()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        var schedulerWorkflowActionResult = new SchedulerWorkflowActionResults
        {
            WorkflowId = "DIFFERENT_WORKFLOW",
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_001",
            NodeName = "Node 1",
            WorkflowName = "Different Workflow",
            InfraObjectId = "INFRA_OBJ_001",
            InfraObjectName = "Test Infra Object",
            StepId = "STEP_001",
            WorkflowActionId = "ACTION_001",
            WorkflowActionName = "Test Action",
            Status = "Success",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(-1),
            Message = "Test message",
            IsActive = true
        };

        await _repository.AddAsync(schedulerWorkflowActionResult);

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Empty(results);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnEmpty_WhenInfraReferenceIdDoesNotMatch()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        var schedulerWorkflowActionResult = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = "DIFFERENT_INFRA",
            NodeId = "NODE_001",
            NodeName = "Node 1",
            WorkflowName = "Test Workflow",
            InfraObjectId = "INFRA_OBJ_001",
            InfraObjectName = "Test Infra Object",
            StepId = "STEP_001",
            WorkflowActionId = "ACTION_001",
            WorkflowActionName = "Test Action",
            Status = "Success",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(-1),
            Message = "Test message",
            IsActive = true
        };

        await _repository.AddAsync(schedulerWorkflowActionResult);

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Empty(results);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnEmpty_WhenNoActiveRecords()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        var schedulerWorkflowActionResult = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_001",
            NodeName = "Node 1",
            WorkflowName = "Test Workflow",
            InfraObjectId = "INFRA_OBJ_001",
            InfraObjectName = "Test Infra Object",
            StepId = "STEP_001",
            WorkflowActionId = "ACTION_001",
            WorkflowActionName = "Test Action",
            Status = "Success",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(-1),
            Message = "Test message",
            IsActive = false // Inactive record
        };

        await _dbContext.SchedulerWorkflowActionResults.AddAsync(schedulerWorkflowActionResult);
        _dbContext.SaveChanges();

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Empty(results);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnEmpty_WhenNoRecordsExist()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Empty(results);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnCorrectFields()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";
        var startTime = DateTime.Now.AddHours(-2);
        var endTime = DateTime.Now.AddHours(-1);

        var schedulerWorkflowActionResult = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_001",
            NodeName = "Test Node",
            WorkflowName = "Test Workflow",
            InfraObjectId = "INFRA_OBJ_001",
            InfraObjectName = "Test Infra Object",
            StepId = "STEP_001",
            WorkflowActionId = "ACTION_001",
            WorkflowActionName = "Test Action",
            Status = "Success",
            StartTime = startTime,
            EndTime = endTime,
            Message = "Test message",
            IsActive = true
        };

        await _repository.AddAsync(schedulerWorkflowActionResult);

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Single(results);

        var result = results[0];
        Assert.Equal(workflowId, result.WorkflowId);
        Assert.Equal(infraReferenceId, result.InfraObjectSchedulerLogsId);
        Assert.Equal("NODE_001", result.NodeId);
        Assert.Equal("Test Node", result.NodeName);
        Assert.Equal("Test Workflow", result.WorkflowName);
        Assert.Equal("INFRA_OBJ_001", result.InfraObjectId);
        Assert.Equal("Test Infra Object", result.InfraObjectName);
        Assert.Equal("STEP_001", result.StepId);
        Assert.Equal("ACTION_001", result.WorkflowActionId);
        Assert.Equal("Test Action", result.WorkflowActionName);
        Assert.Equal("Success", result.Status);
        Assert.Equal(startTime, result.StartTime);
        Assert.Equal(endTime, result.EndTime);
        Assert.Equal("Test message", result.Message);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldHandleNullParameters()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var results1 = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(null, "INFRA_001");
        Assert.NotNull(results1);
        Assert.Empty(results1);

        var results2 = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId("WORKFLOW_001", null);
        Assert.NotNull(results2);
        Assert.Empty(results2);

        var results3 = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(null, null);
        Assert.NotNull(results3);
        Assert.Empty(results3);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldHandleEmptyParameters()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var results1 = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(string.Empty, "INFRA_001");
        Assert.NotNull(results1);
        Assert.Empty(results1);

        var results2 = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId("WORKFLOW_001", string.Empty);
        Assert.NotNull(results2);
        Assert.Empty(results2);
    }

    [Fact]
    public async Task GetSchedulerWorkflowActionResultListByWorkflowId_ShouldReturnMultipleResults_InCorrectOrder()
    {
        // Arrange
        await ClearDatabase();

        var workflowId = "WORKFLOW_001";
        var infraReferenceId = "INFRA_001";

        var result1 = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_001",
            NodeName = "Node 1",
            WorkflowActionName = "Action 1",
            Status = "Success",
            StartTime = DateTime.Now.AddHours(-3),
            EndTime = DateTime.Now.AddHours(-2),
            IsActive = true
        };

        var result2 = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_002",
            NodeName = "Node 2",
            WorkflowActionName = "Action 2",
            Status = "Failed",
            StartTime = DateTime.Now.AddHours(-2),
            EndTime = DateTime.Now.AddHours(-1),
            IsActive = true
        };

        var result3 = new SchedulerWorkflowActionResults
        {
            WorkflowId = workflowId,
            InfraObjectSchedulerLogsId = infraReferenceId,
            NodeId = "NODE_003",
            NodeName = "Node 3",
            WorkflowActionName = "Action 3",
            Status = "Running",
            StartTime = DateTime.Now.AddHours(-1),
            EndTime = DateTime.Now,
            IsActive = true
        };

        await _repository.AddAsync(result1);
        await _repository.AddAsync(result2);
        await _repository.AddAsync(result3);

        // Act
        var results = await _repository.GetSchedulerWorkflowActionResultListByWorkflowId(workflowId, infraReferenceId);

        // Assert
        Assert.NotNull(results);
        Assert.Equal(3, results.Count);
        Assert.Contains(results, r => r.NodeName == "Node 1");
        Assert.Contains(results, r => r.NodeName == "Node 2");
        Assert.Contains(results, r => r.NodeName == "Node 3");
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SchedulerWorkflowActionResults.RemoveRange(_dbContext.SchedulerWorkflowActionResults);
        await _dbContext.SaveChangesAsync();
    }
}
