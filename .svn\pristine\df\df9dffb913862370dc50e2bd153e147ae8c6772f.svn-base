﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessServiceRepository : BaseRepository<BusinessService>, IBusinessServiceRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public BusinessServiceRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<BusinessService>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId));

        var bsService = MapBusinessService(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await bsService.ToListAsync()
            : GetAssignedBusinessServices(bsService);
    }
    public async Task<IReadOnlyList<BusinessService>> GetByReferenceIdsAsync(List<string> ids)
    {
        var businessService = _loggedInUserService.IsParent
           ? Entities.AsNoTracking().DescOrderById().Where(x => ids.Contains(x.ReferenceId))
           : Entities.AsNoTracking().DescOrderById().Where(bs => ids.Contains(bs.ReferenceId) && bs.CompanyId.Equals(_loggedInUserService.CompanyId));


        var businessServiceDto = MapBusinessService(businessService);

        return _loggedInUserService.IsAllInfra
            ? await businessServiceDto.ToListAsync()
            : GetAssignedBusinessServices(businessServiceDto);
    }
    public override async Task<BusinessService> GetByReferenceIdAsync(string id)
    {
        var businessService = base.GetByReferenceId(id,
            businessService => businessService.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                               businessService.ReferenceId.Equals(id));

        var businessServiceDto = MapBusinessService(businessService);

        return _loggedInUserService.IsAllInfra
            ? await businessServiceDto.FirstOrDefaultAsync()
            : GetBusinessServiceByReferenceId(businessServiceDto.FirstOrDefault());
    }

    public async Task<BusinessService> GetFilterByReferenceIdAsync(string id)
    {
        var businessService = _loggedInUserService.IsParent
            ? await Entities.AsNoTracking().Active().Select(x => new BusinessService
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                Name = x.Name,
            }).FirstOrDefaultAsync(x => x.ReferenceId == id)
            : await Entities.AsNoTracking().Active()
                .Select(x => new BusinessService
                {
                    Id = x.Id,
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                })
                .FirstOrDefaultAsync(x => x.ReferenceId == id && x.CompanyId == _loggedInUserService.CompanyId);

        return _loggedInUserService.IsAllInfra
            ? businessService
            : GetBusinessServiceByReferenceId(businessService);
    }


    public async Task<List<BusinessService>> GetBusinessServiceNames()
    {
        var businessServices = base
            .QueryAll(businessService => businessService.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new BusinessService { ReferenceId = x.ReferenceId, Name = x.Name, CompanyId = x.CompanyId ,Priority = x.Priority,SiteProperties=x.SiteProperties});

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : GetAssignedBusinessServices(businessServices).ToList();
    }
    public override async Task<PaginatedResult<BusinessService>>PaginatedListAllAsync(int pageNumber,int pageSize,Specification<BusinessService> specification, string sortColumn, string sortOrder)
    {
        if(_loggedInUserService.IsParent)
        {
            return await SelectBusinessService(_loggedInUserService.IsAllInfra ?
            MapBusinessService(Entities.Specify(specification).DescOrderById())           
           :  GetPaginatedAssignedBusinessServices(MapBusinessService(Entities.Specify(specification).DescOrderById())))          
           .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }

        return await SelectBusinessService(_loggedInUserService.IsAllInfra?
             MapBusinessService(Entities.Specify(specification).Where(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())           
            :  GetPaginatedAssignedBusinessServices(MapBusinessService(Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())))
           .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<BusinessService> GetPaginatedQuery()
    {
        var businessServices = base.QueryAll(businessService =>
            businessService.CompanyId.Equals(_loggedInUserService.CompanyId));

        var bsService = MapBusinessService(businessServices);

        return _loggedInUserService.IsAllInfra
            ? bsService.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedAssignedBusinessServices(bsService).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public Task<bool> IsBusinessServiceNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.BusinessServices.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.BusinessServices.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsBusinessServiceNameUnique(string name)
    {
        var matches = _dbContext.BusinessServices.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<IReadOnlyList<BusinessService>> GetBySiteIds(List<string> siteIds)
    {
        //var businessServices = _loggedInUserService.IsParent
        //    ? base.FilterBy(x => siteIds.Any(id => x.SiteProperties.Contains(id)))
        //    : base.FilterBy(x => siteIds.Any(id => x.SiteProperties.Contains(id)
        //                                           && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        
        var businessServices = _loggedInUserService.IsParent
            ? _dbContext.BusinessServices.AsNoTracking()
                .Select(x => new BusinessService
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    SiteProperties = x.SiteProperties
                })
            : _dbContext.BusinessServices.AsNoTracking()
                .Where(x => x.CompanyId == _loggedInUserService.CompanyId) // Ensure filtering happens first
                .Select(x => new BusinessService
                {
                    ReferenceId = x.ReferenceId,
                    Name = x.Name,
                    SiteProperties = x.SiteProperties
                });

        var assignBs = _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : GetAssignedBusinessServices(businessServices).ToList();


        var rr = assignBs.Where(x => siteIds.Any(id => x.SiteProperties.Contains(id))).ToList();

        return rr;

    }


    public async Task<IReadOnlyList<BusinessService>> GetBusinessServicesBySiteId(string siteId)
    {
        var businessServices =  _loggedInUserService.IsParent
            ? base.FilterBy(x=>x.SiteProperties.Contains(siteId))
            : base.FilterBy(x => x.SiteProperties.Contains(siteId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var bsService = MapBusinessService(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await bsService.ToListAsync()
            : GetAssignedBusinessServices(bsService);
    }


    private IQueryable<BusinessService> MapBusinessService(IQueryable<BusinessService> bsServices)
    {
        return bsServices.Select(x => new
        {
            Company = _dbContext.Companies.FirstOrDefault(c => c.ReferenceId.Equals(x.CompanyId)),
            BusinessServices = x
        })
        .Select(res => new BusinessService
        {
            Id = res.BusinessServices.Id,
            ReferenceId = res.BusinessServices.ReferenceId,
            Name = res.BusinessServices.Name,
            Description = res.BusinessServices.Description,
            CompanyId = res.Company.ReferenceId,
            CompanyName = res.Company.DisplayName,
            SiteProperties = res.BusinessServices.SiteProperties,
            Priority = res.BusinessServices.Priority,
            IsActive = res.BusinessServices.IsActive,
            CreatedBy = res.BusinessServices.CreatedBy,
            CreatedDate = res.BusinessServices.CreatedDate,
            LastModifiedBy = res.BusinessServices.LastModifiedBy,
            LastModifiedDate = res.BusinessServices.LastModifiedDate,
        });
    }
    private IQueryable<BusinessService> SelectBusinessService(IQueryable<BusinessService> query)
    {
        return query.Select(x => new BusinessService
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name=x.Name,
            CompanyId = x.CompanyId,
            CompanyName = x.CompanyName,
            Description = x.Description,
            SiteProperties = x.SiteProperties,
            Priority = x.Priority
        });
    }
}