﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.RefreshToken;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Settings;

namespace ContinuityPatrol.Shared.Infrastructure.Identity;

public class TokenManager : ITokenManager
{
    private readonly IDistributedCache _cache;
    private readonly CacheSettings _cacheSettings;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly JwtSettings _jwtSettings;
    private readonly ILogger<TokenManager> _logger;

    public TokenManager(IHttpContextAccessor httpContextAccessor, IDistributedCache cache,
        IOptions<JwtSettings> jwtSettings, IOptions<CacheSettings> cacheSettings, ILogger<TokenManager> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _cache = cache;
        _jwtSettings = jwtSettings.Value;
        _cacheSettings = cacheSettings.Value;
        _logger = logger;
    }

    public HttpContext HttpContext => _httpContextAccessor.HttpContext;
    public string IpAddress => HttpContext?.Connection.RemoteIpAddress?.ToString();
    public string Agent => HttpContext?.Request.Headers["User-Agent"];


    public async Task<string> GenerateToken(AuthenticationServiceResponse request)
    {
        JwtSecurityToken jwtSecurityToken = null;

        await Task.Run(() => { jwtSecurityToken = CreateJwtSecurityToken(request); });
        var token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
        var dataToCache = Encoding.UTF8.GetBytes(token);

        // Setting up the cache options
        var options = new DistributedCacheEntryOptions()
            .SetAbsoluteExpiration(DateTime.UtcNow.AddMinutes(request.SessionTimeout))
            .SetSlidingExpiration(TimeSpan.FromMinutes(_cacheSettings.SlidingExpiration));

        var cacheKey = request.LoginName;

        await _cache.SetAsync(cacheKey, dataToCache, options);

        if (string.IsNullOrWhiteSpace(token))
            throw new InvalidTokenException(
                $"Exception occurred generate authentication token for the request {request.LoginName}");

        return token;
    }

    public Task<RefreshTokenRequest> GenerateRefreshToken()
    {
        var refreshToken = new RefreshTokenRequest
        {
            Token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(64)),
            Expires = DateTime.UtcNow.AddDays(1),
            Created = DateTime.UtcNow
        };

        return Task.FromResult(refreshToken);
    }

    public Task<bool> IsCurrentActiveToken()
    {
        var token = CurrentToken().Result;

        return Task.FromResult(!string.IsNullOrWhiteSpace(token) && IsValid(token));
    }

    public Task<bool> Clear(string loginName)
    {
        if (string.IsNullOrWhiteSpace(loginName)) return Task.FromResult(false);

        _cache.Remove(loginName);

        return Task.FromResult(true);
    }

    public Task<string> CurrentToken()
    {
        if (HttpContext == null) return Task.FromResult(string.Empty);

        var authorizationHeader = HttpContext.Request.Headers["authorization"];

        return Task.FromResult(authorizationHeader == StringValues.Empty
            ? string.Empty
            : authorizationHeader.Single().Split(" ").Last());
    }

    private JwtSecurityToken CreateJwtSecurityToken(AuthenticationServiceResponse request)
    {
        var permissions = string.Empty;

        if (request.Permissions.Any()) permissions = JsonConvert.SerializeObject(request.Permissions);
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.UniqueName, request.LoginName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim("companyId", request.CompanyId),
            new Claim("companyName", request.CompanyName),
            new Claim(ClaimTypes.Role, request.Role),
            new Claim("permissions", permissions),
            new Claim("uid", request.UserId),
            new Claim("ipAddress", IpAddress ?? string.Empty),
            new Claim("userAgent", Agent ?? string.Empty),
            new Claim("isParent", request.IsParent.ToString()),
            new Claim("isAllInfra", request.IsAllInfra.ToString()),
            new Claim("AssignedInfras", request.AssignedInfras)
        };

        var symmetricSecurityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
        var signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);

        var jwtSecurityToken = new JwtSecurityToken(
            _jwtSettings.Issuer,
            _jwtSettings.Audience,
            claims,
            expires: DateTime.UtcNow.AddMinutes(request.SessionTimeout),
            signingCredentials: signingCredentials);
        return jwtSecurityToken;
    }

    public bool IsValid(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();

        var key = Encoding.ASCII.GetBytes(_jwtSettings.Key);

        ClaimsPrincipal claimsPrincipal;

        try
        {
            claimsPrincipal = tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            }, out _);
        }
        catch (Exception e)
        {
            _logger.LogError($"Token Exception :{e.Message}");

            throw new TokenExpiredException("Token is expired.");
        }

        if (IpAddress == null) throw new AuthenticationException("Invalid Token", (int)ErrorCode.InvalidAuthentication);

        var loginName = claimsPrincipal.Identity?.Name;

        var bytes = _cache.Get(loginName);

        if (bytes == null)
        {
            _logger.LogInformation($"'{loginName}' Session logged out, no action taken too long...");

            throw new AuthenticationException("Session logged out, no action taken too long",
                (int)ErrorCode.InvalidAuthentication);
        }

        var cacheToken = Encoding.UTF8.GetString(bytes, 0, bytes.Length);

        if (cacheToken != token)
            throw new AuthenticationException("Invalid Token", (int)ErrorCode.InvalidAuthentication);
        if (Agent.Contains("CP")) return true;
        var userAgent = claimsPrincipal.Claims.FirstOrDefault(x => x.Type == "userAgent")?.Value;

        return userAgent == Agent;
    }
}