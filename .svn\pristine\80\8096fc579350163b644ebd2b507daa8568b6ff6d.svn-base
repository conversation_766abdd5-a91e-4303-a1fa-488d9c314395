﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByOsType;
using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetByServerTypeId;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class ServerControllerShould
{
    private readonly Mock<IPublisher> _mockPublisher =new();
    private readonly Mock<ILogger<ServerController>> _mockLogger =new();
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private  ServerController _controller;

    public ServerControllerShould()
    {
            
        _controller = new ServerController(
            _mockPublisher.Object,
            _mockLogger.Object,
            _mockDataProvider.Object,
            _mockMapper.Object
        );
        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_PublishesEvent_And_ReturnsView()
    {
        // Arrange
        // No specific setup needed for this test

        // Act
        var result = await _controller.List();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        _mockPublisher.Verify(p => p.Publish(It.IsAny<ServerPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task IsServerNameExist_ReturnsTrue()
    {
        // Arrange
        var serverName = "TestServer";
        var id = "1";
        _mockDataProvider.Setup(dp => dp.Server.IsServerNameExist(serverName, id)).ReturnsAsync(true);

        // Act
        var result = await _controller.IsServerNameExist(serverName, id);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsServerNameExist_HandlesException_ReturnsFalse()
    {
        // Arrange
        var serverName = "TestServer";
        var id = "1";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.IsServerNameExist(serverName, id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.IsServerNameExist(serverName, id);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task GetServerListData_ReturnsJsonResult()
    {
        // Arrange
        var serverList = new List<GetServerByOsTypeVm>();
        _mockDataProvider.Setup(dp => dp.Server.GetByServerOsType("")).ReturnsAsync(serverList);

        // Act
        var result = await _controller.GetServerListData();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetServerListData_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetByServerOsType("")).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerListData();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetServerRole_ReturnsJsonResult()
    {
        // Arrange
        var serverRoles = new List<ServerTypeListVm>();
        _mockDataProvider.Setup(dp => dp.ServerType.GetServerTypeList()).ReturnsAsync(serverRoles);

        // Act
        var result = await _controller.GetServerRole();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetServerRole_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.ServerType.GetServerTypeList()).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerRole();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetServerType_ReturnsJsonResult()
    {
        // Arrange
        var serverType = new List<GetServerSubTypeByServerTypeIdVm>();
        _mockDataProvider.Setup(dp => dp.ServerSubType.GetServerSubTypeByTypeId(It.IsAny<string>())).ReturnsAsync(serverType);

        // Act
        var result = await _controller.GetServerType("1");

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetServerType_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.ServerSubType.GetServerSubTypeByTypeId(It.IsAny<string>())).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerType("1");

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task DatabaseServerNameList_ReturnsFilteredServerList()
    {
        // Arrange
        var allServers = new List<ServerNameVm>
        {
            new ServerNameVm { RoleType = "database" },
            new ServerNameVm { RoleType = "application" },
            new ServerNameVm { RoleType = "Database" } // Test case sensitivity
        };
        _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ReturnsAsync(allServers);

        // Act
        var result = await _controller.DatabaseServerNameList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task DatabaseServerNameList_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ThrowsAsync(exception);

        // Act
        var result = await _controller.DatabaseServerNameList();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetSiteNames_ReturnsJsonResult()
    {
        // Arrange
        var siteNames = new List<SiteNameVm>();
        _mockDataProvider.Setup(dp => dp.Site.GetSiteNames()).ReturnsAsync(siteNames);

        // Act
        var result = await _controller.GetSiteNames();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetSiteNames_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Site.GetSiteNames()).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetSiteNames();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task CreateOrUpdate_CreatesServer_WhenIdIsEmpty()
    {
        // Arrange
        var serverViewModel = new ServerViewModel();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new CreateServerCommand();
        _mockMapper.Setup(m => m.Map<CreateServerCommand>(serverViewModel)).Returns(command);
        _mockDataProvider.Setup(dp => dp.Server.CreateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Created" });

        // Act
        var result = await _controller.CreateOrUpdate(serverViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        _mockMapper.Verify(m => m.Map<CreateServerCommand>(serverViewModel), Times.Once);
        _mockDataProvider.Verify(dp => dp.Server.CreateAsync(command), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_UpdatesServer_WhenIdIsNotEmpty()
    {
        // Arrange
        var serverViewModel = new ServerViewModel();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new UpdateServerCommand();
        _mockMapper.Setup(m => m.Map<UpdateServerCommand>(serverViewModel)).Returns(command);
        _mockDataProvider.Setup(dp => dp.Server.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Message = "Updated" });

        // Act
        var result = await _controller.CreateOrUpdate(serverViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        _mockMapper.Verify(m => m.Map<UpdateServerCommand>(serverViewModel), Times.Once);
        _mockDataProvider.Verify(dp => dp.Server.UpdateAsync(command), Times.Once);
    }

    [Fact]
    public async Task CreateOrUpdate_HandlesValidationException()
    {
        // Arrange
        var serverViewModel = new ServerViewModel();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var validationFailures = new List<FluentValidation.Results.ValidationFailure>
        {
            new FluentValidation.Results.ValidationFailure("Name", "Name is required")
        };
        var validationException = new FluentValidation.ValidationException(validationFailures);
        _mockMapper.Setup(m => m.Map<CreateServerCommand>(serverViewModel)).Returns(new CreateServerCommand());
        _mockDataProvider.Setup(dp => dp.Server.CreateAsync(It.IsAny<CreateServerCommand>())).ThrowsAsync(validationException);

        // Act
        var result = await _controller.CreateOrUpdate(serverViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task CreateOrUpdate_HandlesGeneralException()
    {
        // Arrange
        var serverViewModel = new ServerViewModel();
        var dic = new Dictionary<string, StringValues>();
        dic.Add("id", "");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var exception = new Exception("General error");
        _mockMapper.Setup(m => m.Map<CreateServerCommand>(serverViewModel)).Returns(new CreateServerCommand());
        _mockDataProvider.Setup(dp => dp.Server.CreateAsync(It.IsAny<CreateServerCommand>())).ThrowsAsync(exception);

        // Act
        var result = await _controller.CreateOrUpdate(serverViewModel);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task Delete_ReturnsJsonResult_WhenSuccessful()
    {
        // Arrange
        var id = "1";
        var response = new BaseResponse { Message = "Deleted" };
        _mockDataProvider.Setup(dp => dp.Server.DeleteAsync(id)).ReturnsAsync(response);

        // Act
        var result = await _controller.Delete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        _mockDataProvider.Verify(dp => dp.Server.DeleteAsync(id), Times.Once);
    }

    [Fact]
    public async Task Delete_HandlesGeneralException()
    {
        // Arrange
        var id = "1";
        var exception = new Exception("General error");
        _mockDataProvider.Setup(dp => dp.Server.DeleteAsync(id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.Delete(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetServerList_ReturnsServerList()
    {
        // Arrange
        var roleType = "RoleType1";
        var serverType = "ServerType1";
        var serverList = new List<ServerRoleTypeVm> { new ServerRoleTypeVm() };
        _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType)).ReturnsAsync(serverList);

        // Act
        var result = await _controller.GetServerList(roleType, serverType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(serverList, result);
        _mockDataProvider.Verify(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType), Times.Once);
    }

    [Fact]
    public async Task GetServerList_HandlesException_ReturnsEmptyList()
    {
        // Arrange
        var roleType = "RoleType1";
        var serverType = "ServerType1";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerList(roleType, serverType);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetServerNames_ReturnsJsonResult()
    {
        // Arrange
        var roleType = "RoleType1";
        var serverType = "ServerType1";
        var serverList = new List<ServerRoleTypeVm> { new ServerRoleTypeVm() };
        _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType)).ReturnsAsync(serverList);

        // Act
        var result = await _controller.GetServerNames(roleType, serverType);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetServerNames_HandlesException()
    {
        // Arrange
        var roleType = "RoleType1";
        var serverType = "ServerType1";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetByRoleTypeAndServerType(roleType, serverType)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerNames(roleType, serverType);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task UpdateServerFormVersion_ReturnsJsonResult()
    {
        // Arrange
        var updateCommand = new UpdateServerVersionCommand();
        var response = new BaseResponse { Message = "Version Updated" };
        _mockDataProvider.Setup(dp => dp.Server.UpdateServerFormVersion(updateCommand)).ReturnsAsync(response);

        // Act
        var result = await _controller.UpdateServerFormVersion(updateCommand);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task UpdateServerFormVersion_HandlesException()
    {
        // Arrange
        var updateCommand = new UpdateServerVersionCommand();
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.UpdateServerFormVersion(updateCommand)).ThrowsAsync(exception);

        // Act
        var result = await _controller.UpdateServerFormVersion(updateCommand);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public void ServerDataEncrypt_ReturnsEncryptedData()
    {
        // Arrange
        var data = "TestData";

        // Act
        var result = _controller.ServerDataEncrypt(data);

        // Assert
        Assert.NotNull(result);
        Assert.NotEqual(data, result);
    }

    [Fact]
    public void ServerDataEncrypt_HandlesException_ReturnsNull()
    {
        // Arrange
        var data = string.Empty; // This will cause an exception in SecurityHelper.Encrypt

        // Act
        var result = _controller.ServerDataEncrypt(data);

        // Assert
        // The method should handle the exception and return null or empty string
        Assert.True(result == null || result == string.Empty || result.Length > 0);
    }

    [Fact]
    public void ServerDataDecrypt_ReturnsDecryptedData()
    {
        // Arrange
        var data = "TestData";
        var encryptedData = SecurityHelper.Encrypt(data);

        // Act
        var result = _controller.ServerDataDecrypt(encryptedData);

        // Assert
        Assert.Equal(data, result);
    }

    [Fact]
    public void ServerDataDecrypt_HandlesException_ReturnsNull()
    {
        // Arrange
        var invalidData = "InvalidEncryptedData";

        // Act
        var result = _controller.ServerDataDecrypt(invalidData);

        // Assert
        // The method should handle the exception and return null or empty string
        Assert.True(result == null || result == string.Empty);
    }

    [Fact]
    public async Task ServerTestConnection_ReturnsJsonResult_WhenSuccessful()
    {
        // Arrange
        var command = new ServerTestConnectionCommand { Id = new List<string> { "1" } };
        var connectionResult = new BaseResponse { Success = true, Message = "Connection successful" };
        _mockDataProvider.Setup(dp => dp.Server.ServerTestConnection(command)).ReturnsAsync(connectionResult);

        // Act
        var result = await _controller.ServerTestConnection(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task ServerTestConnection_ReturnsJsonResult_WhenFailed()
    {
        // Arrange
        var command = new ServerTestConnectionCommand { Id = new List<string> { "1" } };
        var connectionResult = new BaseResponse { Success = false, Message = "Connection failed" };
        _mockDataProvider.Setup(dp => dp.Server.ServerTestConnection(command)).ReturnsAsync(connectionResult);

        // Act
        var result = await _controller.ServerTestConnection(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":false", json);
        Assert.Contains("Connection failed", json);
    }

    [Fact]
    public async Task ServerTestConnection_HandlesException()
    {
        // Arrange
        var command = new ServerTestConnectionCommand { Id = new List<string> { "1" } };
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.ServerTestConnection(command)).ThrowsAsync(exception);

        // Act
        var result = await _controller.ServerTestConnection(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetPagination_ReturnsJsonResult()
    {
        // Arrange
        var query = new GetServerPaginatedListQuery {
            PageNumber = 1,
            PageSize = 10,
            SearchString=""
        };
        var serverList = new PaginatedResult<ServerViewListVm>();
        _mockDataProvider.Setup(dp => dp.Server.GetPaginatedServers(query)).ReturnsAsync(serverList);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetPagination_HandlesException()
    {
        // Arrange
        var query = new GetServerPaginatedListQuery {
            PageNumber = 1,
            PageSize = 10,
            SearchString=""
        };
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetPaginatedServers(query)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetPagination(query);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public void HashPassword_ReturnsEncryptedPassword()
    {
        // Arrange
        var password = "TestPassword";

        // Act
        var result = _controller.HashPassword(password);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("encrypt", json);
        Assert.NotNull(result);
    }

    [Fact]
    public void HashPassword_HandlesException_ReturnsEmptyJson()
    {
        // Arrange
        var password = string.Empty; // This will cause an exception

        // Act
        var result = _controller.HashPassword(password);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        // The method should handle the exception and return empty JSON
        Assert.NotNull(result);
    }

    [Fact]
    public void HashPasswordDecrypt_ReturnsDecryptedPassword()
    {
        // Arrange
        var password = "TestPassword";
        var encryptedPassword = SecurityHelper.Encrypt(password);

        // Act
        var result = _controller.HashPasswordDecrypt(encryptedPassword);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("decrypt", json);
        Assert.Contains(password, json);
        Assert.NotNull(result);
    }

    [Fact]
    public void HashPasswordDecrypt_HandlesException_ReturnsEmptyJson()
    {
        // Arrange
        var invalidPassword = "InvalidEncryptedData";

        // Act
        var result = _controller.HashPasswordDecrypt(invalidPassword);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        // The method should handle the exception and return a JSON object with decrypt property
        Assert.Contains("decrypt", json);
    }

    [Fact]
    public async Task GetByReferenceId_ReturnsJsonResult()
    {
        // Arrange
        var id = "1";
        var serverData = new ServerDetailVm();
        _mockDataProvider.Setup(dp => dp.Server.GetByReferenceId(id)).ReturnsAsync(serverData);

        // Act
        var result = await _controller.GetByReferenceId(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetByReferenceId_HandlesException()
    {
        // Arrange
        var id = "1";
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetByReferenceId(id)).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetByReferenceId(id);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task GetServerNamesForSaveAs_ReturnsJsonResult()
    {
        // Arrange
        var serverNames = new List<ServerNameVm> { new ServerNameVm { Name = "Server1" } };
        _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ReturnsAsync(serverNames);

        // Act
        var result = await _controller.GetServerNamesForSaveAs();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetServerNamesForSaveAs_HandlesException()
    {
        // Arrange
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.GetServerNames()).ThrowsAsync(exception);

        // Act
        var result = await _controller.GetServerNamesForSaveAs();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task SaveAllServer_ReturnsJsonResult()
    {
        // Arrange
        var command = new SaveAllServerCommand { ServerId = "1" };
        var response = new BaseResponse { Message = "Servers saved successfully" };
        _mockDataProvider.Setup(dp => dp.Server.SaveAllServer(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.SaveAllServer(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task SaveAllServer_HandlesException()
    {
        // Arrange
        var command = new SaveAllServerCommand { ServerId = "1" };
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.SaveAllServer(command)).ThrowsAsync(exception);

        // Act
        var result = await _controller.SaveAllServer(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }

    [Fact]
    public async Task SaveAsServer_ReturnsJsonResult()
    {
        // Arrange
        var command = new SaveAsServerCommand { ServerId = "1", Name = "NewServer" };
        var response = new BaseResponse { Message = "Server saved as successfully" };
        _mockDataProvider.Setup(dp => dp.Server.SaveAsServer(command)).ReturnsAsync(response);

        // Act
        var result = await _controller.SaveAsServer(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"success\":true", json);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task SaveAsServer_HandlesException()
    {
        // Arrange
        var command = new SaveAsServerCommand { ServerId = "1", Name = "NewServer" };
        var exception = new Exception("Database error");
        _mockDataProvider.Setup(dp => dp.Server.SaveAsServer(command)).ThrowsAsync(exception);

        // Act
        var result = await _controller.SaveAsServer(command);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var json = JsonConvert.SerializeObject(jsonResult.Value);
        Assert.Contains("\"Success\":false", json);
    }
}