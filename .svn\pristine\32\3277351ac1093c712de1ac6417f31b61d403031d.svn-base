using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowActionFieldMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionFieldMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowActionFieldMasterService : BaseClient, IWorkflowActionFieldMasterService
{
    public WorkflowActionFieldMasterService(IConfiguration config, IAppCache cache, ILogger<WorkflowActionFieldMasterService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<WorkflowActionFieldMasterListVm>> GetWorkflowActionFieldMasterList()
    {
        var request = new RestRequest("api/v6/workflowactionfieldmasters");

        return await GetFromCache<List<WorkflowActionFieldMasterListVm>>(request, "GetWorkflowActionFieldMasterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowActionFieldMasterCommand createWorkflowActionFieldMasterCommand)
    {
        var request = new RestRequest("api/v6/workflowactionfieldmasters", Method.Post);

        request.AddJsonBody(createWorkflowActionFieldMasterCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowActionFieldMasterCommand updateWorkflowActionFieldMasterCommand)
    {
        var request = new RestRequest("api/v6/workflowactionfieldmasters", Method.Put);

        request.AddJsonBody(updateWorkflowActionFieldMasterCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/workflowactionfieldmasters/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<WorkflowActionFieldMasterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/workflowactionfieldmasters/{id}");

        return await Get<WorkflowActionFieldMasterDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsWorkflowActionFieldMasterNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/workflowactionfieldmasters/name-exist?workflowactionfieldmasterName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<WorkflowActionFieldMasterListVm>> GetPaginatedWorkflowActionFieldMasters(GetWorkflowActionFieldMasterPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/workflowactionfieldmasters/paginated-list");

      return await Get<PaginatedResult<WorkflowActionFieldMasterListVm>>(request);
  }
   #endregion
}
