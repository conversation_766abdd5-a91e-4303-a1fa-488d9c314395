﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowHistoryRepository : IRepository<WorkflowHistory>
{
    Task<List<WorkflowHistory>> GetWorkflowHistoryByWorkflowId(string workflowId);
    Task<List<WorkflowHistory>> GetWorkflowHistoryNames();
    Task<bool> IsWorkflowHistoryNameExist(string name, string id);
    Task<bool> IsWorkflowHistoryNameUnique(string name);
    Task<WorkflowHistory> GetWorkflowHistoryByWorkflowIdAndVersion(string workflowId, string version);
}