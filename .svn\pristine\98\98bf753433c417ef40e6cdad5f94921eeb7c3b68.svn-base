﻿//using ContinuityPatrol.Domain.Entities;

//namespace ContinuityPatrol.Application.UnitTests.Mocks;

//public class HeatMapLogRepositoryMocks
//{
//    public static Mock<IHeatMapLogRepository> CreateHeatMapLogRepository(List<HeatMapLog> heatMapLogs)
//    {
//        var createHeatMapLogRepository = new Mock<IHeatMapLogRepository>();

//        createHeatMapLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapLogs);

//        createHeatMapLogRepository.Setup(repo => repo.AddAsync(It.IsAny<HeatMapLog>())).ReturnsAsync(
//            (HeatMapLog heatMapLog) =>
//            {
//                heatMapLog.Id = new Fixture().Create<int>();

//                heatMapLog.ReferenceId = new Fixture().Create<Guid>().ToString();

//                heatMapLogs.Add(heatMapLog);

//                return heatMapLog;
//            });

//        return createHeatMapLogRepository;
//    }

//    public static Mock<IHeatMapLogRepository> UpdateHeatMapLogRepository(List<HeatMapLog> heatMapLogs)
//    {
//        var updateHeatMapLogRepository = new Mock<IHeatMapLogRepository>();

//        updateHeatMapLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapLogs);

//        updateHeatMapLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapLogs.SingleOrDefault(x => x.ReferenceId == i));

//        updateHeatMapLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HeatMapLog>())).ReturnsAsync((HeatMapLog heatMapLog) =>
//        {
//            var index = heatMapLogs.FindIndex(item => item.ReferenceId == heatMapLog.ReferenceId);

//            heatMapLogs[index] = heatMapLog;

//            return heatMapLog;
//        });

//        return updateHeatMapLogRepository;
//    }

//    public static Mock<IHeatMapLogRepository> DeleteHeatMapLogRepository(List<HeatMapLog> heatMapLogs)
//    {
//        var deleteHeatMapLogRepository = new Mock<IHeatMapLogRepository>();

//        deleteHeatMapLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapLogs);

//        deleteHeatMapLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapLogs.SingleOrDefault(x => x.ReferenceId == i));

//        deleteHeatMapLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HeatMapLog>())).ReturnsAsync((HeatMapLog heatMapLog) =>
//        {
//            var index = heatMapLogs.FindIndex(item => item.ReferenceId == heatMapLog.ReferenceId);

//            heatMapLog.IsActive = false;

//            heatMapLogs[index] = heatMapLog;

//            return heatMapLog;
//        });

//        return deleteHeatMapLogRepository;
//    }

//    public static Mock<IHeatMapLogRepository> GetHeatMapLogRepository(List<HeatMapLog> heatMapLogList)
//    {
//        var heatMapLogRepository = new Mock<IHeatMapLogRepository>();

//        heatMapLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapLogList);

//        heatMapLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapLogList.SingleOrDefault(x => x.ReferenceId == i));

//        return heatMapLogRepository;
//    }

//    public static Mock<IHeatMapLogRepository> GetHeatMapLogEmptyRepository()
//    {
//        var heatMapLogEmptyRepository = new Mock<IHeatMapLogRepository>();

//        heatMapLogEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<HeatMapLog>());

//        return heatMapLogEmptyRepository;
//    }

//    public static Mock<IHeatMapLogRepository> GetPaginatedHeatMapLogRepository(List<HeatMapLog> heatMapLogList)
//    {
//        var paginatedHeatMapLogRepository = new Mock<IHeatMapLogRepository>();

//        var queryableHeatMapLog = heatMapLogList.BuildMock();

//        paginatedHeatMapLogRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableHeatMapLog);

//        return paginatedHeatMapLogRepository;
//    }

//    public static Mock<IHeatMapLogRepository> GetHeatMapLogTypeRepository(List<HeatMapLog> heatMapLogList)
//    {
//        var heatMapLogTypeRepository = new Mock<IHeatMapLogRepository>();

//        var queryableHeatMapLog = heatMapLogList.BuildMock();

//        heatMapLogTypeRepository.Setup(repo => repo.GetHeatMapLogType(It.IsAny<string>())).Returns(queryableHeatMapLog);

//        return heatMapLogTypeRepository;
//    }
//}