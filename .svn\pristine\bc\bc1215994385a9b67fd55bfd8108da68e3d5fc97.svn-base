using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SiteLocationFixture : IDisposable
{
    public List<SiteLocation> SiteLocationPaginationList { get; set; }
    public List<SiteLocation> SiteLocationList { get; set; }
    public SiteLocation SiteLocationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SiteLocationFixture()
    {
        var fixture = new Fixture();

        SiteLocationList = fixture.Create<List<SiteLocation>>();

        SiteLocationPaginationList = fixture.CreateMany<SiteLocation>(20).ToList();

        SiteLocationDto = fixture.Create<SiteLocation>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
