﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                Golden Gate Component:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div  class="monitor_pages mb-2">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-3 mt-0">
            <div class="col-6">
                <div class="card Card_Design_None">
                    @* <div class="card-header card-title d-flex align-items-center justify-content-between siteContainer"> *@
                    <div class="card-header card-title">
                            Golden Gate Component Monitoring
                        </div>
                        @* <span title=" NetAPP_SnapMirror ">
                            NetAPP_SnapMirror
                        </span> *@

                    @* </div> *@
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Component Monitor</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-secondary" title="DR" id="customSite">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="text-secondary cp-list-prsite me-1"></i>Server Name</td>
                                    <td class="text-truncate"><span id="Server_Name"></span></td>
                                    <td class="text-truncate"><span id="DR_Server_Name"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-server-ip me-1"></i>IPAddress</td>
                                    <td class="text-truncate"><span id="Server_IpAddress"></span></td>
                                    <td class="text-truncate"><span id="DR_Server_IpAddress"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-version me-1"></i>Golden Gate Version</td>
                                    <td class="text-truncate"><span id="GGVersion"></span></td>
                                    <td class="text-truncate"><span id="DR_GGVersion"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-home me-1"></i>Golden Gate Home</td>
                                    <td class="text-truncate"><span id="GGHomePath"></span></td>
                                    <td class="text-truncate"><span id="DR_GGHomePath"></span></td>
                                </tr> 
                                <tr>
                                    <td><i class="text-secondary cp-schema me-1"></i>Golden Gate Status</td>
                                    <td class="text-truncate"><span id="GGMgrStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_GGMgrStatus"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-schema me-1"></i>Golden Gate Schema</td>
                                    <td><span id="GGSchema"></span></td>
                                    <td><span id="DR_GGSchema"></span>
                                    <td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-ADM-settings me-1"></i>Checkpoint Table</td>
                                    <td><span id="CheckPointTable"></span></td>
                                    <td><span id="DR_CheckPointTable"></span>
                                    <td>
                                </tr>
                            </tbody>
                          
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                    <div class="d-grid card-body">
                        <div id="Solution_Diagram"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-3 mt-0">
            <div class="col-12 mt-0">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Golden Gate Replication Details ">Golden Gate Replication Details</span>
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0 align-middle" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Server Type</th>
                                    <th>Program</th>
                                    <th>Group</th>
                                    <th>Status</th>
                                    <th>Lag at checkpoint</th>
                                    <th>Time Since checkpoint</th>
                                    <th>
                                        Log Read 
                                    </th> 
                                    <th>
                                        Logfile Read 
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="tblgoldenGate">
                               
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
      
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
    </div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringGoldengate.js"></script>