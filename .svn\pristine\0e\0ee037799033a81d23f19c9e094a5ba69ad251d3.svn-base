﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByLicenseKey;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByUserName;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class DatabaseService : BaseClient, IDatabaseService
{
    public DatabaseService(IConfiguration config, IAppCache cache, ILogger<DatabaseService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> SaveAllDatabase(SaveAllDatabaseCommand command)
    {
        if (command.DatabaseId.IsNullOrWhiteSpace()) throw new Exception("Database Id is required");

        var serialize = JsonConvert.SerializeObject(command);

        var dynamicData = new Dictionary<string, object>
        {
            {"Type", Modules.Database.ToString()},
            { "Command", serialize}
        };

        //await _jobExecutionService.ScheduleSaveAllJob<SaveAllJobService>
        //    (command.ServerId, Modules.Server.ToString(), dynamicData);

        return new BaseResponse { Message = "SaveAll scheduled successfully" };
    }

    public async Task<List<DatabaseNameVm>> GetDatabaseNames()
    {
        var request = new RestRequest("api/v6/databases/names");

        return await GetFromCache<List<DatabaseNameVm>>(request, "GetDatabaseNames");
    }

    public async Task<bool> IsDatabaseNameExist(string databaseName, string? id)
    {
        var request = new RestRequest($"api/v6/databases/name-exist?databaseName={databaseName}&id={id}");

        return await Get<bool>(request);
    }

    public async  Task<PaginatedResult<DatabaseListVm>> GetDatabasePaginatedList(GetDatabasePaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/databases/paginated-list{query}");

        return await Get<PaginatedResult<DatabaseListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateDatabaseCommand createDatabaseCommand)
    {
        var request = new RestRequest("api/v6/databases", Method.Post);

        request.AddJsonBody(createDatabaseCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDatabaseCommand updateDatabaseCommand)
    {

        var request = new RestRequest("api/v6/databases", Method.Put);

        request.AddJsonBody(updateDatabaseCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string databaseId)
    {
        var request = new RestRequest($"api/v6/databases/{databaseId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DatabaseDetailVm> GetByReferenceId(string databaseId)
    {
        var request = new RestRequest($"api/v6/databases/{databaseId}");

        return await Get<DatabaseDetailVm>(request);
    }

    public async Task<List<GetDatabaseByLicenseKeyListVm>> GetByLicenseKey(string licenseId)
    {
        var request = new RestRequest($"api/v6/databases/by/licenseid?licenseId={licenseId}");

        return await Get<List<GetDatabaseByLicenseKeyListVm>>(request);
    }

    public async Task<List<GetDatabaseByServerIdVm>>  GetByServerId(string serverId)
    {
        var request = new RestRequest($"api/v6/databases/by/{serverId}");

        return await Get<List<GetDatabaseByServerIdVm>>(request);
    }

    public async Task<BaseResponse> DatabaseTestConnection(DatabaseTestConnectionCommand command)
    {
        var request = new RestRequest($"api/v6/databases/databasetestconnection", Method.Post);

        request.AddJsonBody(command);

        return await Post<BaseResponse>(request);
    }

    public async  Task<List<DatabaseListVm>> GetDatabaseList()
    {
        var request = new RestRequest("api/v6/databases");

        return await GetFromCache<List<DatabaseListVm>>(request, "DatabaseList");
    }

    public async Task<BaseResponse> SaveAsDatabase(SaveAsDatabaseCommand saveAsDatabaseCommand)
    {
        var request = new RestRequest($"/api/v6/databases/save-as", Method.Post);

        request.AddJsonBody(saveAsDatabaseCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<List<GetDatabaseByTypeVm>> GetByType(string type)
    {
        var request = new RestRequest($"api/v6/databases/by/type?type={type}");

        return await Get<List<GetDatabaseByTypeVm>>(request);
    }

    public async  Task<List<DatabaseTypeVm>> GetByDatabaseType(string databaseTypeId)
    {
        var request = new RestRequest($"api/v6/databases/by/databasetypeid?databaseTypeId={databaseTypeId}");

        return await Get<List<DatabaseTypeVm>>(request);
    }

    public async Task<List<GetDatabaseByUserNameVm>> GetDatabaseByUserName(string userName, string? databaseTypeId)
    {
        var request = new RestRequest($"api/v6/databases/username?userName={userName}&databaseType={databaseTypeId}");

        return await Get<List<GetDatabaseByUserNameVm>>(request);
    }

    public async Task<UpdateDatabasePasswordResponse> UpdateDatabasePassword(UpdateDatabasePasswordCommand command)
    {
        var request = new RestRequest($"api/v6/databases/bulk-password", Method.Put);

        request.AddJsonBody(command);

        return await Put<UpdateDatabasePasswordResponse>(request);
    }

    public async Task<BaseResponse> UpdateDatabaseFormVersion(UpdateDatabaseVersionCommand updateDatabaseVersionCommand)
    {
        var request = new RestRequest($"api/v6/databases/update-formversion", Method.Put);

        request.AddJsonBody(updateDatabaseVersionCommand);

        return await Put<UpdateDatabasePasswordResponse>(request);
    }
}