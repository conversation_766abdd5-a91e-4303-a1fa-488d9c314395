using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class StateMonitorStatusFixture : IDisposable
{
    public List<StateMonitorStatus> StateMonitorStatusPaginationList { get; set; }
    public List<StateMonitorStatus> StateMonitorStatusList { get; set; }
    public StateMonitorStatus StateMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public StateMonitorStatusFixture()
    {
        var fixture = new Fixture();

        StateMonitorStatusList = fixture.Create<List<StateMonitorStatus>>();

        StateMonitorStatusPaginationList = fixture.CreateMany<StateMonitorStatus>(20).ToList();

        StateMonitorStatusDto = fixture.Create<StateMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
