using System.Linq.Expressions;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RpForVmCGMonitorStatusRepositoryTests : IClassFixture<RpForVmCGMonitorStatusFixture>
{
    private readonly RpForVmCGMonitorStatusFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RpForVmCGMonitorStatusRepository _repository;

    public RpForVmCGMonitorStatusRepositoryTests(RpForVmCGMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RpForVmCGMonitorStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetDetailByInfraObjectId Tests

    [Fact]
    public async Task GetDetailByInfraObjectId_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST_001";
        var matchingStatuses = new List<RpForVmCGMonitorStatus>
        {
            _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId(infraObjectId),
            _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId(infraObjectId)
        };
        var nonMatchingStatus = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId("DIFFERENT_INFRA");

        _dbContext.RpForVmCGMonitorStatuses.AddRange(matchingStatuses);
        _dbContext.RpForVmCGMonitorStatuses.Add(nonMatchingStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, status => Assert.Equal(infraObjectId, status.InfraObjectId));
    }

    [Fact]
    public async Task GetDetailByInfraObjectId_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var status = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId("DIFFERENT_INFRA");
        _dbContext.RpForVmCGMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByInfraObjectId("NON_EXISTENT_INFRA");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetDetailByInfraObjectId_ShouldReturnEmptyList_WhenInfraObjectIdIsNullOrEmpty(string infraObjectId)
    {
        // Arrange
        var status = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId("VALID_INFRA");
        _dbContext.RpForVmCGMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetStatusCountAsync Tests

    [Fact]
    public async Task GetStatusCountAsync_ShouldReturnCorrectCounts_WhenRecordsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST_COUNT";
        var expectedCounts = new Dictionary<string, int>
        {
            { "Available", 3 },
            { "Unavailable", 2 },
            { "Degraded", 1 }
        };

        var statuses = _fixture.CreateMultipleRpForVmCGMonitorStatusWithSameInfraObjectId(infraObjectId, expectedCounts);
        _dbContext.RpForVmCGMonitorStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetStatusCountAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedCounts.Count, result.Count);
        
        foreach (var expectedCount in expectedCounts)
        {
            Assert.True(result.ContainsKey(expectedCount.Key));
            Assert.Equal(expectedCount.Value, result[expectedCount.Key]);
        }
    }

    [Fact]
    public async Task GetStatusCountAsync_ShouldReturnEmptyDictionary_WhenNoMatchingRecords()
    {
        // Arrange
        var status = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId("DIFFERENT_INFRA");
        _dbContext.RpForVmCGMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetStatusCountAsync("NON_EXISTENT_INFRA");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetStatusCountAsync_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var infraObjectId = "INFRA_ACTIVE_TEST";
        var activeStatus = _fixture.CreateRpForVmCGMonitorStatusWithAvailabilityStatus(infraObjectId, "Available");
        activeStatus.IsActive = true;

        var inactiveStatus = _fixture.CreateRpForVmCGMonitorStatusWithAvailabilityStatus(infraObjectId, "Available");
        inactiveStatus.IsActive = false;

        _dbContext.RpForVmCGMonitorStatuses.AddRange(activeStatus, inactiveStatus);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetStatusCountAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.ContainsKey("Available"));
        Assert.Equal(1, result["Available"]); // Only the active record should be counted
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetStatusCountAsync_ShouldReturnEmptyDictionary_WhenInfraObjectIdIsNullOrEmpty(string infraObjectId)
    {
        // Arrange
        var status = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId("VALID_INFRA");
        _dbContext.RpForVmCGMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetStatusCountAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResult_WhenRecordsExist()
    {
        // Arrange
        var statuses = _fixture.RpForVmCGMonitorStatusPaginationList.Take(15).ToList();
        _dbContext.RpForVmCGMonitorStatuses.AddRange(statuses);
        await _dbContext.SaveChangesAsync();
    string? searchString = null;
        var specification = new RpForVmCGMonitorStatusSpecification(searchString);
        Expression<Func<RpForVmCGMonitorStatus, bool>> expression = x => x.IsActive;
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, expression, "Id", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationAndExpression()
    {
        // Arrange
        var activeStatuses = new List<RpForVmCGMonitorStatus>();
        var inactiveStatuses = new List<RpForVmCGMonitorStatus>();

        for (int i = 0; i < 5; i++)
        {
            var activeStatus = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId($"ACTIVE_INFRA_{i}");
            activeStatus.IsActive = true;
            activeStatuses.Add(activeStatus);

            var inactiveStatus = _fixture.CreateRpForVmCGMonitorStatusWithInfraObjectId($"INACTIVE_INFRA_{i}");
            inactiveStatus.IsActive = false;
            inactiveStatuses.Add(inactiveStatus);
        }

        _dbContext.RpForVmCGMonitorStatuses.AddRange(activeStatuses);
        _dbContext.RpForVmCGMonitorStatuses.AddRange(inactiveStatuses);
        await _dbContext.SaveChangesAsync();
        string? searchString = null;
        var specification = new RpForVmCGMonitorStatusSpecification(searchString);
        Expression<Func<RpForVmCGMonitorStatus, bool>> expression = x => x.IsActive;

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, expression, "Id", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleEmptyResult()
    {
        // Arrange
        string? searchString = null;
        var specification = new RpForVmCGMonitorStatusSpecification(searchString);
        Expression<Func<RpForVmCGMonitorStatus, bool>> expression = x => x.IsActive;

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, expression, "Id", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRpForVmCGMonitorStatus_WhenValidEntity()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmCGMonitorStatusDto;
        monitorStatus.InfraObjectId = "INFRA_TEST_001";
        monitorStatus.ConsistencyGroupId = "CG_TEST_001";
        monitorStatus.ConsistencyGroupName = "Test Consistency Group";
        monitorStatus.State = "Active";
        monitorStatus.TransferStatus = "Completed";
        monitorStatus.ActivityType = "Replication";
        monitorStatus.ActivityStatus = "Success";
        monitorStatus.AvailabilityStatus = "Available";
        monitorStatus.ProtectedSize = "500GB";
        monitorStatus.DataLag = "2 minutes";

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.ConsistencyGroupId, result.ConsistencyGroupId);
        Assert.Equal(monitorStatus.ConsistencyGroupName, result.ConsistencyGroupName);
        Assert.Equal(monitorStatus.State, result.State);
        Assert.Equal(monitorStatus.TransferStatus, result.TransferStatus);
        Assert.Equal(monitorStatus.ActivityType, result.ActivityType);
        Assert.Equal(monitorStatus.ActivityStatus, result.ActivityStatus);
        Assert.Equal(monitorStatus.AvailabilityStatus, result.AvailabilityStatus);
        Assert.Equal(monitorStatus.ProtectedSize, result.ProtectedSize);
        Assert.Equal(monitorStatus.DataLag, result.DataLag);
        Assert.Single(_dbContext.RpForVmCGMonitorStatuses);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmCGMonitorStatusDto;
        _dbContext.RpForVmCGMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(monitorStatus.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Id, result.Id);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmCGMonitorStatusDto;
        _dbContext.RpForVmCGMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        var updatedState = "Updated State";
        var updatedAvailabilityStatus = "Updated Availability Status";
        monitorStatus.State = updatedState;
        monitorStatus.AvailabilityStatus = updatedAvailabilityStatus;

        // Act
        var result = await _repository.UpdateAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedState, result.State);
        Assert.Equal(updatedAvailabilityStatus, result.AvailabilityStatus);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmCGMonitorStatusDto;
        _dbContext.RpForVmCGMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(monitorStatus);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(monitorStatus.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RpForVmCGMonitorStatuses.RemoveRange(_dbContext.RpForVmCGMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }
}
