﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;

public class GetDataLagStatusReportQueryHandler : IRequestHandler<GetDataLagStatusReportQuery, DataLagStatusReport>
{
    private readonly IDashboardViewLogRepository _dashboardViewLogRepository;
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public GetDataLagStatusReportQueryHandler(IMapper mapper, IDashboardViewRepository dashboardViewRepository,
        IDashboardViewLogRepository dashboardViewLogRepository, ILoggedInUserService loggedInUserService,
        IPublisher publisher)
    {
        _mapper = mapper;
        _dashboardViewRepository = dashboardViewRepository;
        _dashboardViewLogRepository = dashboardViewLogRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<DataLagStatusReport> Handle(GetDataLagStatusReportQuery request,
        CancellationToken cancellationToken)
    {
        var dataLagStatusReport = new List<DataLagStatusReportVm>();

        var dashboardViewLogList = await _dashboardViewLogRepository.ListAllAsync();

        var dashboardView = await _dashboardViewRepository.ListAllAsync();

        var uniqueBusinessServiceIds = dashboardViewLogList
            .Select(x => new { x.BusinessServiceId, x.BusinessServiceName })
            .Union(dashboardView.Select(x => new { x.BusinessServiceId, x.BusinessServiceName }))
            .DistinctBy(x => x.BusinessServiceId)
            .ToList();

        var uniqueBusinessServices = uniqueBusinessServiceIds
            .Select(x => new Domain.Entities.DashboardViewLog
                { BusinessServiceId = x.BusinessServiceId, BusinessServiceName = x.BusinessServiceName })
            .ToList();

        var businessServiceMap = _mapper.Map<List<DataLagStatusReportVm>>(uniqueBusinessServices);

        foreach (var businessService in businessServiceMap)
        {
            if (businessService.BusinessServiceId == "") continue;

            var businessFunctionVm =
                await _dashboardViewRepository.GetBusinessViewListByBusinessServiceIdDatalag(
                    businessService?.BusinessServiceId);

            var infraObject = businessFunctionVm
                ?.Where(x => x.InfraObjectId.IsNotNullOrWhiteSpace())
                .DistinctBy(x => x?.InfraObjectId)
                .ToList();


            businessService.InfraObjectDataLagStatusReports =
                _mapper.Map<List<InfraObjectDataLagStatusReport>>(infraObject);

            foreach (var infra in businessService.InfraObjectDataLagStatusReports)
            {
                var dataLagOneDayReport =
                    await _dashboardViewLogRepository.GetDataLagByOneDayReport(infra?.InfraObjectId);

                if (dataLagOneDayReport.Count > 0)
                {
                    var groupedByHour = dataLagOneDayReport.GroupBy(e => e.LastModifiedDate.Hour).ToList();

                    var dataLagValueGroup = groupedByHour
                        .SelectMany(group => group).ToList();

                    var dataLagKeyAndValue = await MaxDataLagValue(dataLagValueGroup);

                    var maxDataLagValueMinutes = dataLagKeyAndValue.Count > 0 ? dataLagKeyAndValue.Values.Max() : 0;

                    foreach (var group in groupedByHour)
                    {
                        var infraObjectDto =
                            await _dashboardViewRepository.GetBusinessViewByInfraObjectId(infra.InfraObjectId);

                        if (infraObjectDto is not null)
                        {
                            var configuredRPOMins =
                                TimeSpan.FromMinutes(Convert.ToInt32(infraObjectDto?.ConfiguredRPO));

                            var configuredRPOMinutes = (int)Math.Round(configuredRPOMins.TotalMinutes);

                            var thresholdPercentage = Convert.ToInt32(infraObjectDto.ConfiguredRPO) *
                                Convert.ToInt32(infraObjectDto.RPOThreshold) / 100;

                            var thresholdValue =
                                (int)Math.Round(TimeSpan.Parse("00:" + thresholdPercentage + ":00").TotalMinutes);

                            if (maxDataLagValueMinutes < thresholdValue)
                            {
                                var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
                                {
                                    Hour = group.Key,
                                    IsDataLagValue = true,
                                    IsDataLagValueExist = false,
                                    IsThreshold = false,
                                    IsNotAvailable = false
                                };

                                infra.InfraObjectHoursDataLagDetails.Add(infraObjectHoursDetails);
                            }
                            else if (maxDataLagValueMinutes > thresholdValue &&
                                     maxDataLagValueMinutes < configuredRPOMinutes)
                            {
                                var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
                                {
                                    Hour = group.Key,
                                    IsDataLagValue = false,
                                    IsDataLagValueExist = false,
                                    IsThreshold = true,
                                    IsNotAvailable = false
                                };

                                infra.InfraObjectHoursDataLagDetails.Add(infraObjectHoursDetails);
                            }

                            else if (maxDataLagValueMinutes > configuredRPOMinutes)
                            {
                                var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
                                {
                                    Hour = group.Key,
                                    IsDataLagValue = false,
                                    IsDataLagValueExist = true,
                                    IsThreshold = false,
                                    IsNotAvailable = false
                                };

                                infra.InfraObjectHoursDataLagDetails.Add(infraObjectHoursDetails);
                            }

                            else
                            {
                                var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
                                {
                                    Hour = group.Key,
                                    IsDataLagValue = false,
                                    IsDataLagValueExist = false,
                                    IsThreshold = false,
                                    IsNotAvailable = true
                                };

                                infra.InfraObjectHoursDataLagDetails.Add(infraObjectHoursDetails);
                            }
                        }
                    }
                }
                else
                {
                    var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
                    {
                        Hour = 0,
                        IsDataLagValue = false,
                        IsDataLagValueExist = false,
                        IsThreshold = false,
                        IsNotAvailable = true
                    };

                    infra.InfraObjectHoursDataLagDetails.AddRange(infraObjectHoursDetails);
                }
            }
        }

        businessServiceMap.RemoveAll(bs => bs.InfraObjectDataLagStatusReports.Count == 0);

        await _publisher.Publish(
            new ReportViewedEvent { ReportName = "DataLag Status Report", ActivityType = ActivityType.View.ToString() },
            CancellationToken.None);

        return new DataLagStatusReport
        {
            ReportGeneratedBy = _loggedInUserService.LoginName,
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            DataLagStatusReportVms = businessServiceMap
        };
    }

    private Task<Dictionary<string, int>> MaxDataLagValue(List<Domain.Entities.DashboardViewLog> dataLagLogsDto)
    {
        var dataLagKeyAndValue = new Dictionary<string, int>();

        foreach (var dataLagLog in dataLagLogsDto.Where(dataLagLog => !dataLagLog.DataLagValue.Equals("NA")))
            try
            {
                if (dataLagLog?.DataLagValue.Length is > 8 and 12)
                {
                    var dataLagValueSplit = dataLagLog.DataLagValue.Split(' ');
                    if (dataLagValueSplit.Length == 1) continue;
                    var dataLagValueSplitMinutes = Convert.ToInt32(dataLagValueSplit[0].Substring(1, 2)) * 1440
                                                   + (int)Math.Round(TimeSpan.Parse(dataLagValueSplit[1]).TotalMinutes);

                    dataLagKeyAndValue.TryAdd(dataLagLog.DataLagValue, dataLagValueSplitMinutes);
                }

                else if (dataLagLog?.DataLagValue.Length is > 8)
                {
                    var dataLagValueSplit = dataLagLog.DataLagValue.Split('.');

                    var dataLagValueSplitMinutes = Convert.ToInt32(dataLagValueSplit[0].Substring(0, 1)) * 1440
                                                   + (int)Math.Round(TimeSpan.Parse(dataLagValueSplit[1]).TotalMinutes);

                    dataLagKeyAndValue.TryAdd(dataLagLog.DataLagValue, dataLagValueSplitMinutes);
                }

                else
                {
                    var dataLagValueSplitMinutes =
                        (int)Math.Round(TimeSpan.Parse(dataLagLog.DataLagValue).TotalMinutes);

                    dataLagKeyAndValue.TryAdd(dataLagLog.DataLagValue, dataLagValueSplitMinutes);
                }
            }

            catch
            {
            }

        return Task.FromResult(dataLagKeyAndValue);
    }
}

//foreach(var dashboardViewLog in dashboardViewLogs)
//{
//    var dataLagOneDayReport =await _dashboardViewLogRepository.GetDataLagByOneDayReport(dashboardViewLog.BusinessServiceId);

//    if (dataLagOneDayReport.Count > 0)
//    {
//        var dateTime = DateTime.Now;

//        var dataLagDto = new DataLagStatusReportVm
//        {
//            BusinessServiceId = dashboardViewLog.BusinessServiceId,
//            BusinessServiceName = dashboardViewLog.BusinessServiceName
//        };

//        if (!dataLagStatusReport.Any(x => x.BusinessServiceId.Equals(dashboardViewLog.BusinessServiceId))) dataLagStatusReport.Add(dataLagDto);

//        var groupedEntities = dataLagOneDayReport.GroupBy(e => e.LastModifiedDate.Hour).ToList();

//        groupedEntities.ForEach(group =>
//        {
//            var maxDataLagValueList = new List<Domain.Entities.DashboardViewLog>();

//            var skipFlag = true;

//            var hour = group.Key;

//            var dataLagLogsDto = group.ToList();

//            var maxDataLagValueMinutes = 0;

//            var maxDataLagValue = string.Empty;

//            if (group.Key.Equals(hour))
//            {
//                var dataLagKeyAndValue = MaxDataLagValue(dataLagLogsDto);

//                maxDataLagValueMinutes = dataLagKeyAndValue.Values.Max();

//                foreach (var keyValuePair in dataLagKeyAndValue)
//                {
//                    var result = keyValuePair.Value.Equals(maxDataLagValueMinutes);

//                    if (!result) continue;

//                    maxDataLagValue = keyValuePair.Key;

//                    //maxDataLagValueList.AddRangeAsync(dataLagOneDayReport.Where(dataLagLog =>
//                    //    dataLagLog.DataLagValue.Equals(maxDataLagValue)));

//                    maxDataLagValueList.AddRangeAsync(dataLagOneDayReport.DistinctBy(x=>x.DataLagValue).Where(dataLagLog =>
//                        dataLagLog.DataLagValue.Equals(maxDataLagValue)));
//                }
//            }

//            var infraDataLag = new List<InfraObjectDataLagStatusReport>();

//            var infraDataLagDto = new List<InfraObjectHoursDataLagDetails>();

//            foreach(var dataLagLog in maxDataLagValueList)
//            {
//                var infraDataLagReport = new InfraObjectDataLagStatusReport
//                {
//                    InfraObjectId = dataLagLog.InfraObjectId,
//                    InfraObjectName = dataLagLog.InfraObjectName,
//                    BusinessServiceId = dataLagLog.BusinessServiceId
//                };

//                infraDataLag.Add(infraDataLagReport);

//                var infraObjectDto =await _dashboardViewRepository.GetBusinessViewByInfraObjectId(dataLagLog.InfraObjectId);

//                if (infraObjectDto != null)
//                {
//                    var configuredRPOMins = TimeSpan.FromMinutes(Convert.ToInt32(infraObjectDto.ConfiguredRPO));

//                    var configuredRPOMinutes = (int)Math.Round(configuredRPOMins.TotalMinutes);

//                    var thresholdPercentage = Convert.ToInt32(infraObjectDto.ConfiguredRPO) * Convert.ToInt32(infraObjectDto.RPOThreshold) / 100;

//                    var thresholdValue = (int)Math.Round(TimeSpan.Parse("00:" + thresholdPercentage + ":00").TotalMinutes);

//                    if (maxDataLagValueMinutes < thresholdValue)
//                    {
//                        var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
//                        {
//                            InfraObjectId = dataLagLog.InfraObjectId,
//                            InfraObjectName = dashboardViewLog.InfraObjectName,
//                            Hour = hour,
//                            IsDataLagValue = true,
//                            IsDataLagValueExist = false,
//                            IsThreshold = false,
//                            IsNotAvailable = false
//                        };

//                        infraDataLagDto.Add(infraObjectHoursDetails);
//                    }

//                    else if (maxDataLagValueMinutes > thresholdValue && maxDataLagValueMinutes < configuredRPOMinutes)
//                    {
//                        var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
//                        {
//                            InfraObjectId = dataLagLog.InfraObjectId,
//                            InfraObjectName = dashboardViewLog.InfraObjectName,
//                            Hour = hour,
//                            IsDataLagValue = false,
//                            IsDataLagValueExist = false,
//                            IsThreshold = true,
//                            IsNotAvailable = false
//                        };

//                        infraDataLagDto.Add(infraObjectHoursDetails);
//                    }

//                    else if (maxDataLagValueMinutes > configuredRPOMinutes)
//                    {
//                        var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
//                        {
//                            InfraObjectId = dataLagLog.InfraObjectId,
//                            InfraObjectName = dashboardViewLog.InfraObjectName,
//                            Hour = hour,
//                            IsDataLagValue = false,
//                            IsDataLagValueExist = true,
//                            IsThreshold = false,
//                            IsNotAvailable = false
//                        };

//                        infraDataLagDto.Add(infraObjectHoursDetails);
//                    }

//                    else
//                    {
//                        var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
//                        {
//                            InfraObjectId = dataLagLog.InfraObjectId,
//                            InfraObjectName = dashboardViewLog.InfraObjectName,
//                            Hour = hour,
//                            IsDataLagValue = false,
//                            IsDataLagValueExist = false,
//                            IsThreshold = false,
//                            IsNotAvailable = true
//                        };

//                        infraDataLagDto.Add(infraObjectHoursDetails);
//                    }
//                }

//                if (!dataLagStatusReport.Any(x => x.InfraObjectDataLagStatusReports.Any(infra => infra.InfraObjectId.Equals(dataLagLog.InfraObjectId))))

//                    dataLagStatusReport.ForEach(x => x.InfraObjectDataLagStatusReports.AddRangeAsync(infraDataLag.Where(y => y.BusinessServiceId.Equals(x.BusinessServiceId))));
//            };

//            if (maxDataLagValueList.Any(x => x.DataLagValue.Equals(maxDataLagValue)) && skipFlag)
//                if (!infraDataLag.Any(x => x.InfraObjectId.Equals(infraDataLagDto.Select(y => y.InfraObjectId))))
//                    dataLagStatusReport.ForEach(x => x.InfraObjectDataLagStatusReports.ForEach(y =>
//                        y.InfraObjectHoursDataLagDetails.AddRangeAsync(infraDataLagDto.Where(z => z.InfraObjectId.Equals(y.InfraObjectId)))));

//            skipFlag = false;

//        };
//    }
//    else
//    {
//        var dateTime = DateTime.Now;

//        var dataLagDto = new DataLagStatusReportVm
//        {
//            BusinessServiceId = dashboardViewLog.BusinessServiceId,
//            BusinessServiceName = dashboardViewLog.BusinessServiceName,
//        };

//        if (!dataLagStatusReport.Any(x => x.BusinessServiceId.Equals(dashboardViewLog.BusinessServiceId))) dataLagStatusReport.Add(dataLagDto);

//        var infraDataLag = new List<InfraObjectDataLagStatusReport>();

//        var infraDataLagDto = new List<InfraObjectHoursDataLagDetails>();

//        var infraDataLagReport = new InfraObjectDataLagStatusReport
//        {
//            InfraObjectId = dashboardViewLog.InfraObjectId,
//            InfraObjectName = dashboardViewLog.InfraObjectName,
//            BusinessServiceId = dashboardViewLog.BusinessServiceId
//        };

//        infraDataLag.Add(infraDataLagReport);

//        var infraObjectHoursDetails = new InfraObjectHoursDataLagDetails
//        {
//            InfraObjectId = dashboardViewLog.InfraObjectId,
//            InfraObjectName = dashboardViewLog.InfraObjectName,
//            //Hour = hour,
//            IsDataLagValue = false,
//            IsDataLagValueExist = false,
//            IsThreshold = false,
//            IsNotAvailable = true
//        };

//        infraDataLagDto.Add(infraObjectHoursDetails);

//        dataLagStatusReport.ForEach(x => x.InfraObjectDataLagStatusReports
//            .AddRangeAsync(infraDataLag.Where(y => y.BusinessServiceId.Equals(x.BusinessServiceId))));

//        dataLagStatusReport.ForEach(x => x.InfraObjectDataLagStatusReports.ForEach(y =>
//            y.InfraObjectHoursDataLagDetails.AddRangeAsync(infraDataLagDto
//                .Where(z => z.InfraObjectId.Equals(y.InfraObjectId)))));
//    }
//});