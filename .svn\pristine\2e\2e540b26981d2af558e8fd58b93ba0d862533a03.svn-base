using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Cyber;

public class CyberAirGapService : BaseService,ICyberAirGapService
{
    public CyberAirGapService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<CyberAirGapListVm>> GetCyberAirGapList()
    {
        Logger.LogDebug("Get All CyberAirGaps");

        return await Mediator.Send(new GetCyberAirGapListQuery());
    }

    public async Task<CyberAirGapDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGap Id");

        Logger.LogDebug($"Get CyberAirGap Detail by Id '{id}'");

        return await Mediator.Send(new GetCyberAirGapDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateCyberAirGapCommand createCyberAirGapCommand)
    {
        Logger.LogDebug($"Create CyberAirGap '{createCyberAirGapCommand}'");

        return await Mediator.Send(createCyberAirGapCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberAirGapCommand updateCyberAirGapCommand)
    {
        Logger.LogDebug($"Update CyberAirGap '{updateCyberAirGapCommand}'");

        return await Mediator.Send(updateCyberAirGapCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGap Id");

        Logger.LogDebug($"Delete CyberAirGap Details by Id '{id}'");

        return await Mediator.Send(new DeleteCyberAirGapCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsCyberAirGapNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "CyberAirGap Name");

     Logger.LogDebug($"Check Name Exists Detail by CyberAirGap Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetCyberAirGapNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<CyberAirGapListVm>> GetPaginatedCyberAirGaps(GetCyberAirGapPaginatedListQuery query)
{
    Logger.LogDebug("Get Searching Details in CyberAirGap Paginated List");

    return await Mediator.Send(query);
}


public async Task<BaseResponse> UpdateStatus(AirGapStatusUpdateCommand airGapStatusCommand)
{
    Logger.LogDebug($"Update CyberAirGap Status'{airGapStatusCommand}'");

    return await Mediator.Send(airGapStatusCommand);
    }

public async Task<BaseResponse> AirGapIsAttached(AirGapAttachedCommand airGapAttachedCommand)
{
    Logger.LogDebug($"CyberAirGap IsAttached '{airGapAttachedCommand}'");

    return await Mediator.Send(airGapAttachedCommand);
    }
public async Task<List<GetAirGapsStatusListVm>> GetAirGapsStatus()
{
    Logger.LogDebug("Get AirGaps Status");

    return await Mediator.Send(new GetAirGapsStatusListQuery());
}
    #endregion
}
