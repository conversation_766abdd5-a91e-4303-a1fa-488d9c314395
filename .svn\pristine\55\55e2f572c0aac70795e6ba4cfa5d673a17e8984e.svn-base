﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;

public class
    DrReadyStatusForDrReadyReportQueryHandler : IRequestHandler<DRReadyStatusForDRReadyReportQuery, DrReadyStatusReport>
{
    private readonly IDrReadyStatusRepository _dRReadyStatusRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public DrReadyStatusForDrReadyReportQueryHandler(IMapper mapper, IDrReadyStatusRepository dRReadyStatusRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher)
    {
        _mapper = mapper;
        _dRReadyStatusRepository = dRReadyStatusRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<DrReadyStatusReport> Handle(DRReadyStatusForDRReadyReportQuery request,
        CancellationToken cancellationToken)
    {
        List<InfraObjectCountList> InfraList = new List<InfraObjectCountList>();
        var drReadyStatusList = request.BusinessServiceId.IsNotNullOrWhiteSpace()
            ? await _dRReadyStatusRepository.GetDrReadyStatusByBusinessServiceId(request.BusinessServiceId)
            : await _dRReadyStatusRepository.ListAllAsync();

        var removeDuplicateInfra = drReadyStatusList?.DistinctBy(x => x?.InfraObjectId).ToList();

        var drReadyReport = _mapper.Map<List<DrReadyStatusForDrReadyReportVm>>(removeDuplicateInfra);

        drReadyReport.ForEach(infra =>
        {
            if (string.IsNullOrEmpty(infra.BusinessFunctionId)) return;
            var drReadyFunction = _dRReadyStatusRepository
                .GetDrReadyStatusListByBusinessFunctionId(infra.BusinessFunctionId).Result;

            var infraCount = drReadyFunction.DistinctBy(x => x?.InfraObjectId).ToList();

            var infraUpCount = infraCount.Count(bf => bf.DRReady == "1");
            var infraDownCount = infraCount.Count(bf => bf.DRReady != "1");

            var infraDetails = new InfraObjectCountList
            {
                BusinessServiceId = infra?.BusinessServiceId,
                BusinessFunctionId = infra?.BusinessFunctionId,
                BusinessFunctionName = infra?.BusinessFunctionName,
                UpCount = infraUpCount,
                DownCount = infraDownCount,
                TotalCount = infraCount.Count
            };
            infra.InfraObjectCountLists.Add(infraDetails);
        });
        var processedBusinessServiceIds = new HashSet<string>();
        foreach (var report in drReadyReport)
        {
            if (!processedBusinessServiceIds.Contains(report.BusinessServiceId))
            {
                processedBusinessServiceIds.Add(report.BusinessServiceId);
                if (report.InfraObjectCountLists != null)
                {
                    foreach (var item in report.InfraObjectCountLists)
                    {
                        InfraList.Add(item);
                    }
                }
            }
        }

        await _publisher.Publish(
            new ReportViewedEvent
                { ReportName = "Resiliency Readiness Report", ActivityType = ActivityType.View.ToString() },
            CancellationToken.None);

        return new DrReadyStatusReport
        {
            ReportGeneratedBy = _loggedInUserService.LoginName,
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            DrReadyStatusForDrReadyReportVms = drReadyReport,
            InfraCountList = InfraList.ToList(),
            BusinessServiceName = request.BusinessServiceId == "" ? "All Operational Service" : drReadyReport[0].BusinessServiceName
        };
    }
}