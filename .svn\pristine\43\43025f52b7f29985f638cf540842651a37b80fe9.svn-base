﻿using ContinuityPatrol.Application.Features.Setting.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Setting.Events;

public class CreateSettingEventTests : IClassFixture<SettingFixture>, IClassFixture<UserActivityFixture>
{
    private readonly SettingFixture _settingFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly SettingCreatedEventHandler _handler;

    public CreateSettingEventTests(SettingFixture settingFixture, UserActivityFixture userActivityFixture)
    {
        _settingFixture = settingFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockSettingEventLogger = new Mock<ILogger<SettingCreatedEventHandler>>();

        _mockUserActivityRepository = SettingRepositoryMocks.CreateSettingEventRepository(_userActivityFixture.UserActivities);

        _handler = new SettingCreatedEventHandler(mockLoggedInUserService.Object, mockSettingEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateSettingEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_settingFixture.SettingCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_settingFixture.SettingCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}