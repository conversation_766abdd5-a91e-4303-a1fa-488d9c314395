using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;

public class
    GetBackUpPaginatedListQueryHandler : IRequestHandler<GetBackUpPaginatedListQuery, PaginatedResult<BackUpListVm>>
{
    private readonly IBackUpRepository _backUpRepository;
    private readonly IMapper _mapper;

    public GetBackUpPaginatedListQueryHandler(IMapper mapper, IBackUpRepository backUpRepository)
    {
        _mapper = mapper;
        _backUpRepository = backUpRepository;
    }

    public async Task<PaginatedResult<BackUpListVm>> Handle(GetBackUpPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new BackUpFilterSpecification(request.SearchString);

        var queryable =await _backUpRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);
        
        var backUpList = _mapper.Map<PaginatedResult<BackUpListVm>>(queryable);

        return backUpList;
        //    var queryable = _backUpRepository.GetPaginatedQuery();

        //    var productFilterSpec = new BackUpFilterSpecification(request.SearchString);

        //    var backUpList = await queryable
        //        .Specify(productFilterSpec)
        //        .Select(m => _mapper.Map<BackUpListVm>(m))
        //        .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //    //await _publisher.Publish(new BackUpPaginatedEvent(), cancellationToken);

        //    return backUpList;
    }
}