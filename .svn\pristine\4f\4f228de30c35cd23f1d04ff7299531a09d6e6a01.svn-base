﻿using ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowExecutionTemp.Commands;

public class DeleteWorkflowExecutionTempTests : IClassFixture<WorkflowExecutionTempFixture>
{
    private readonly WorkflowExecutionTempFixture _workflowExecutionTempFixture;

    private readonly Mock<IWorkflowExecutionTempRepository> _mockWorkflowExecutionTempRepository;

    private readonly DeleteWorkflowExecutionTempCommandHandler _handler;

    public DeleteWorkflowExecutionTempTests(WorkflowExecutionTempFixture workflowExecutionTempFixture)
    {
        _workflowExecutionTempFixture = workflowExecutionTempFixture;

        //var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowExecutionTempRepository = WorkflowExecutionTempRepositoryMocks.DeleteWorkflowExecutionTempRepository(_workflowExecutionTempFixture.WorkflowExecutionTemps);

        _handler = new DeleteWorkflowExecutionTempCommandHandler(_mockWorkflowExecutionTempRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateReferenceIdAsyncIsActiveFalse_When_WorkflowExecutionTempDeleted()
    {
        var result = await _handler.Handle(new DeleteWorkflowExecutionTempCommand { Id = _workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId }, CancellationToken.None);

        Assert.True(result.Success);

        var workflowExecutionTemp = await _mockWorkflowExecutionTempRepository.Object.GetByReferenceIdAsync(_workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId);
        Assert.False(workflowExecutionTemp.IsActive);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulWorkflowExecutionTempResponse_When_WorkflowExecutionTempDeleted()
    {
        var result = await _handler.Handle(new DeleteWorkflowExecutionTempCommand { Id = _workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowExecutionTempResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_WorkflowExecutionTemp()
    {
        await _handler.Handle(new DeleteWorkflowExecutionTempCommand { Id = _workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId }, CancellationToken.None);

        var workflowExecutionTemp = await _mockWorkflowExecutionTempRepository.Object.GetByReferenceIdAsync(_workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId);

        workflowExecutionTemp.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowExecutionTempId()
    {
        var handler = new DeleteWorkflowExecutionTempCommandHandler(_mockWorkflowExecutionTempRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new DeleteWorkflowExecutionTempCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteWorkflowExecutionTempCommand { Id = _workflowExecutionTempFixture.WorkflowExecutionTemps[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowExecutionTempRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockWorkflowExecutionTempRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowExecutionTemp>()), Times.Once);
    }
}