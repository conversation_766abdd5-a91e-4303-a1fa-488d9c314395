﻿using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Import;
using ContinuityPatrol.Application.Features.Form.Commands.Lock;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Application.Features.Form.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class FormProfile : Profile
{
    public FormProfile()
    {
        CreateMap<Form, FormListCommand>().ReverseMap();

        CreateMap<FormTypeCategory, FormTypeCategoryListCommand>().ReverseMap();
        CreateMap<ComponentType, ComponentTypeListCommand>().ReverseMap();

        CreateMap<Form, CreateFormCommand>().ReverseMap();

        CreateMap<FormListCommand, Form>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<FormTypeCategoryListCommand, FormTypeCategory>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<ComponentTypeListCommand, ComponentType>()
            .ForMember(x => x.Id, y => y.Ignore());


        CreateMap<Form, SaveAsFormCommand>().ReverseMap();
        CreateMap<UpdateFormCommand, Form>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<FormViewModel, CreateFormCommand>().ReverseMap();
        CreateMap<FormViewModel, UpdateFormCommand>().ReverseMap();

        CreateMap<Form, FormDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Form, FormListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Form, FormNameVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Form, FormTypeVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<PaginatedResult<Form>, PaginatedResult<FormListVm>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
        #region Publish

        CreateMap<UpdateFormPublishCommand, Form>().ForMember(x => x.Id, y => y.Ignore());

        #endregion

        #region Lock

        CreateMap<UpdateFormLockCommand, Form>().ForMember(x => x.Id, y => y.Ignore());

        #endregion
    }
}