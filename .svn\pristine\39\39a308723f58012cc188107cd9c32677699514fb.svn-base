using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDefaultDashboardByRoleId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DynamicDashboardMapsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<DynamicDashboardMapListVm>>> GetDynamicDashboardMaps()
    {
        Logger.LogDebug("Get All DynamicDashboardMaps");

        return Ok(await Mediator.Send(new GetDynamicDashboardMapListQuery()));
    }

    [HttpGet("{id}", Name = "GetDynamicDashboardMap")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<DynamicDashboardMapDetailVm>> GetDynamicDashboardMapById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardMap Id");

        Logger.LogDebug($"Get DynamicDashboardMap Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDynamicDashboardMapDetailQuery { Id = id }));
    }
    [Route("roleId"), HttpGet]
    public async Task<ActionResult<DynamicDashboardMapListVm>> GetDefaultDashboardByRoleId(string roleId)
    {
       // Guard.Against.NullOrWhiteSpace(dynamicDashboardMapName, "DynamicDashboardMap Name");

        Logger.LogDebug($"Get DynamicDashboardMap Detail by Role Id '{roleId}'");

        return Ok(await Mediator.Send(new GetDefaultDashboardByRoleIdQuery { RoleId = roleId}));
    }

    #region Paginated
    [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<DynamicDashboardMapListVm>>> GetPaginatedDynamicDashboardMaps([FromQuery] GetDynamicDashboardMapPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in DynamicDashboardMap Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateDynamicDashboardMapResponse>> CreateDynamicDashboardMap([FromBody] CreateDynamicDashboardMapCommand createDynamicDashboardMapCommand)
    {
        Logger.LogDebug($"Create DynamicDashboardMap '{createDynamicDashboardMapCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDynamicDashboardMap), await Mediator.Send(createDynamicDashboardMapCommand));
    }


    [ HttpGet, Route("userid")]
    public async Task<ActionResult<DynamicDashboardMapListVm>> GetDefaultDashboardByUserId(string userId)
    {

        Logger.LogDebug($"Get DynamicDashboardMap Detail by user Id '{userId}'");

        return Ok(await Mediator.Send(new GetDynamicDashboardMapByUserIdQuery { UserId = userId }));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateDynamicDashboardMapResponse>> UpdateDynamicDashboardMap([FromBody] UpdateDynamicDashboardMapCommand updateDynamicDashboardMapCommand)
    {
        Logger.LogDebug($"Update DynamicDashboardMap '{updateDynamicDashboardMapCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDynamicDashboardMapCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteDynamicDashboardMapResponse>> DeleteDynamicDashboardMap(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardMap Id");

        Logger.LogDebug($"Delete DynamicDashboardMap Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDynamicDashboardMapCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDynamicDashboardMapNameExist(string dynamicDashboardMapName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(dynamicDashboardMapName, "DynamicDashboardMap Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicDashboardMap Name '{dynamicDashboardMapName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDynamicDashboardMapNameUniqueQuery { Name = dynamicDashboardMapName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


