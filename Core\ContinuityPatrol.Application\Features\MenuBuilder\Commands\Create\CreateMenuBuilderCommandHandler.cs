using ContinuityPatrol.Application.Features.MenuBuilder.Events.Create;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;

public class CreateMenuBuilderCommandHandler : IRequestHandler<CreateMenuBuilderCommand, CreateMenuBuilderResponse>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateMenuBuilderCommandHandler(IMapper mapper, IMenuBuilderRepository menuBuilderRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _menuBuilderRepository = menuBuilderRepository;
    }

    public async Task<CreateMenuBuilderResponse> Handle(CreateMenuBuilderCommand request, CancellationToken cancellationToken)
    {
        var menuBuilder = _mapper.Map<Domain.Entities.MenuBuilder>(request);

        menuBuilder = await _menuBuilderRepository.AddAsync(menuBuilder);

        var response = new CreateMenuBuilderResponse
        {
            Message = Message.Create(nameof(Domain.Entities.MenuBuilder), menuBuilder.Name),

            Id = menuBuilder.ReferenceId
        };

        await _publisher.Publish(new MenuBuilderCreatedEvent { Name = menuBuilder.Name }, cancellationToken);

        return response;
    }
}
