using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class FiaImpactCategoryRepository : BaseRepository<FiaImpactCategory>, IFiaImpactCategoryRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public FiaImpactCategoryRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? await Entities.AnyAsync(e => e.Name.Equals(name))
            : (await Entities.Where(e => e.Name.Equals(name)).ToListAsync()).Unique(id);
    }

}
