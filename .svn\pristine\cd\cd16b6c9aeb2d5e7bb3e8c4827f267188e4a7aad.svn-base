using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowProfileRepositoryTests : IClassFixture<WorkflowProfileFixture>
    {
        private readonly WorkflowProfileFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowProfileRepository _repoParent;
        private readonly WorkflowProfileRepository _repoNotParent;
        private readonly WorkFlowRepository _workflowrepository;

        public WorkflowProfileRepositoryTests(WorkflowProfileFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _workflowrepository = new WorkFlowRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoParent = new WorkflowProfileRepository(_dbContext, DbContextFactory.GetMockUserService(), _workflowrepository);
            _repoNotParent = new WorkflowProfileRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent(), _workflowrepository);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenNoPermissions()
        {
            await _dbContext.WorkflowProfiles.AddRangeAsync(_fixture.WorkflowProfileList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowProfileList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsWithPermissions()
        {
            var extraProfile = _fixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddAsync(extraProfile);
            await _dbContext.SaveChangesAsync();
          //  _mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string> { extraProfile.ReferenceId });

            var result = await _repoParent.ListAllAsync();

            Assert.Contains(result, x => x.ReferenceId == extraProfile.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity()
        {
            var entity = _fixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByProfileIdAsync_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.ReferenceId };
            var result = await _repoParent.GetByProfileIdAsync(ids);

            Assert.Single(result);
            Assert.Equal(entity.ReferenceId, result[0].ReferenceId);
        }

        [Fact]
        public async Task GetByProfileIdAsync_ReturnsList_WhenNotParent()
        {
            var entity = _fixture.WorkflowProfileDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var ids = new List<string> { entity.ReferenceId };
            var result = await _repoNotParent.GetByProfileIdAsync(ids);

            Assert.Single(result);
            Assert.Equal(entity.ReferenceId, result[0].ReferenceId);
        }

        [Fact]
        public async Task GetWorkflowProfileNames_ReturnsNames_WhenNoPermissions()
        {
            //_mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string>());
            await _dbContext.WorkflowProfiles.AddRangeAsync(_fixture.WorkflowProfileList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowProfileNames();

            Assert.All(result, x => Assert.NotNull(x.Name));
        }

        [Fact]
        public async Task GetWorkflowProfileNames_ReturnsWithPermissions()
        {
            var extraProfile = _fixture.WorkflowProfileDto;
            await _dbContext.WorkflowProfiles.AddAsync(extraProfile);
            await _dbContext.SaveChangesAsync();
           // _mockWorkflowRepo.Setup(x => x.GetWorkflowPermissions("profile")).ReturnsAsync(new List<string> { extraProfile.ReferenceId });

            var result = await _repoParent.GetWorkflowProfileNames();

            Assert.Contains(result, x => x.ReferenceId == extraProfile.ReferenceId);
        }

        [Fact]
        public async Task PaginatedListAllAsync_ReturnsPaginatedResult_WhenIsParent()
        {
            await _dbContext.WorkflowProfiles.AddRangeAsync(_fixture.WorkflowProfileList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.PaginatedListAllAsync(1, 2, null, "ReferenceId", "desc");

            Assert.NotNull(result);
            Assert.True(result.Data.Count <= 2);
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered()
        {
            _dbContext.WorkflowProfiles.AddRange(_fixture.WorkflowProfilePaginationList);
            _dbContext.SaveChanges();

            var result = _repoParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task IsWorkflowProfileNameExist_ReturnsTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            var entity = _fixture.WorkflowProfileDto;
            entity.Name = "TestName";
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowProfileNameExist("TestName", "invalid-guid");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowProfileNameExist_ReturnsFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            var result = await _repoParent.IsWorkflowProfileNameExist("NonExistent", "invalid-guid");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowProfileNameExist_ReturnsExpected_WhenIdIsValidGuid()
        {
            var id = Guid.NewGuid().ToString();
            var entity = _fixture.WorkflowProfileDto;
            entity.ReferenceId = id;
            entity.Name = "UniqueName";
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowProfileNameExist("UniqueName", id);

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowProfileNameUnique_ReturnsTrue_WhenNameExists()
        {
            var entity = _fixture.WorkflowProfileDto;
            entity.Name = "UniqueName";
            await _dbContext.WorkflowProfiles.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.IsWorkflowProfileNameUnique("UniqueName");

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowProfileNameUnique_ReturnsFalse_WhenNameDoesNotExist()
        {
            var result = await _repoParent.IsWorkflowProfileNameUnique("NonExistent");

            Assert.False(result);
        }
    }
}