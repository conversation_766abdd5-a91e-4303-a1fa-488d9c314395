﻿using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ImpactActivity.Commands;

public class CreateImpactActivityTests : IClassFixture<ImpactActivityFixture>
{
    private readonly ImpactActivityFixture _impactActivityFixture;

    private readonly Mock<IImpactActivityRepository> _mockImpactActivityRepository;

    private readonly CreateImpactActivityCommandHandler _handler;

    public CreateImpactActivityTests(ImpactActivityFixture impactActivityFixture)
    {
        _impactActivityFixture = impactActivityFixture;

        Mock<IPublisher> mockPublisher = new();

        _mockImpactActivityRepository = new Mock<IImpactActivityRepository>();

        _mockImpactActivityRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(_impactActivityFixture.ImpactActivities);
        _mockImpactActivityRepository
            .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.ImpactActivity>()))
            .ReturnsAsync(new Domain.Entities.ImpactActivity()); 

        _mockImpactActivityRepository = ImpactActivityRepositoryMocks.CreateImpactActivityRepository(_impactActivityFixture.ImpactActivities);

        _handler = new CreateImpactActivityCommandHandler(_impactActivityFixture.Mapper,
            _mockImpactActivityRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_ImpactActivity()
    {
        var initialCount = _impactActivityFixture.ImpactActivities.Count;

        await _handler.Handle(_impactActivityFixture.CreateImpactActivityCommand, CancellationToken.None);

        var result = await _mockImpactActivityRepository.Object.ListAllAsync();

        result.Count.ShouldBe(initialCount + 1);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulImpactActivityResponse_When_AddValidImpactActivity()
    {
        var result = await _handler.Handle(_impactActivityFixture.CreateImpactActivityCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateImpactActivityResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_impactActivityFixture.CreateImpactActivityCommand, CancellationToken.None);

        _mockImpactActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.ImpactActivity>()), Times.Once);
    }
}