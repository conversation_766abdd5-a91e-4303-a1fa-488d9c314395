﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Commands;

public class DeleteCredentialProfileTests : IClassFixture<CredentialProfileFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;
    private readonly Mock<ICredentialProfileRepository> _mockCredentialProfileRepository;
    private readonly DeleteCredentialProfileCommandHandler _handler;

    public DeleteCredentialProfileTests(CredentialProfileFixture credentialProfileFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.DeleteCredentialProfileRepository(_credentialProfileFixture.CredentialProfiles);

        _handler = new DeleteCredentialProfileCommandHandler(_mockCredentialProfileRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_CredentialProfileDeleted()
    {
        var result = await _handler.Handle(new DeleteCredentialProfileCommand { Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId }, CancellationToken.None);

        Assert.True(result.Success);

        var credentialProfile = await _mockCredentialProfileRepository.Object.GetByReferenceIdAsync(_credentialProfileFixture.CredentialProfiles[0].ReferenceId);
        Assert.False(credentialProfile.IsActive);
    }

    [Fact]
    public async Task Handle_ReturnSuccess_When_DeleteCredentialProfile()
    {
        var result = await _handler.Handle(new DeleteCredentialProfileCommand() { Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteCredentialProfileResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_Delete_CredentialProfile()
    {
        await _handler.Handle(new DeleteCredentialProfileCommand() { Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId }, CancellationToken.None);

        var credentialProfile = await _mockCredentialProfileRepository.Object.GetByReferenceIdAsync(_credentialProfileFixture.CredentialProfiles[0].ReferenceId);

        credentialProfile.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task ThrowNotFoundException_When_InvalidCredentialProfileId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteCredentialProfileCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteCredentialProfileCommand { Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId }, CancellationToken.None);

        _mockCredentialProfileRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockCredentialProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CredentialProfile>()), Times.Once);
    }
}