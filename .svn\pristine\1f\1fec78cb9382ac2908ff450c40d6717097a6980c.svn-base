﻿using ContinuityPatrol.Application.Features.Company.Events.Delete;

namespace ContinuityPatrol.Application.Features.Company.Commands.Delete;

public class DeleteCompanyCommandHandler : IRequestHandler<DeleteCompanyCommand, DeleteCompanyResponse>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IPublisher _publisher;
    private readonly ISiteRepository _siteRepository;
    private readonly IUserRepository _userRepository;

    public DeleteCompanyCommandHandler(ICompanyRepository companyRepository, IPublisher publisher,
        ISiteRepository siteRepository, IUserRepository userRepository)
    {
        _companyRepository = companyRepository;
        _publisher = publisher;
        _siteRepository = siteRepository;
        _userRepository = userRepository;
    }

    public async Task<DeleteCompanyResponse> Handle(DeleteCompanyCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Company Id");
        var sites = await _siteRepository.GetSiteByCompanyId(request.Id);

        var users = await _userRepository.GetUsersByCompanyId(request.Id);

        if (sites.Count > 0 || users.Count > 0)
            throw new EntityAssociatedException(
                "The company could not be delete because of an association with site or user entity.");

        var eventToDelete = await _companyRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Company),
            new NotFoundException(nameof(Domain.Entities.Company), request.Id));

        eventToDelete.IsActive = false;

        await _companyRepository.UpdateAsync(eventToDelete);

        var response = new DeleteCompanyResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Company), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new CompanyDeletedEvent { CompanyName = eventToDelete.Name }, cancellationToken);

        return response;
    }
}