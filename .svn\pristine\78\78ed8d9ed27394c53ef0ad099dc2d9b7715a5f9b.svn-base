namespace ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;

public class UpdateCyberComponentCommandValidator : AbstractValidator<UpdateCyberComponentCommand>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;

    public UpdateCyberComponentCommandValidator(ICyberComponentRepository cyberComponentRepository)
    {
        _cyberComponentRepository = cyberComponentRepository;
        RuleFor(x => x.Name)
          .NotNull().WithMessage("{PropertyName} is required.")
          .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
          .WithMessage("Please Enter Valid {PropertyName}")
          .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(x => x)
           .MustAsync(CyberComponentNameUnique)
           .WithMessage("A same name already exists.");

        RuleFor(x => x)
         .MustAsync(IsValidGuid)
         .WithMessage("Invalid Id.");

        RuleFor(x => x.SiteName).NotNull().WithMessage("{PropertyName} is required.")
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.");

        RuleFor(x => x.ServerType).NotNull().WithMessage("{PropertyName} is required.")
           .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
           .WithMessage("Please Enter Valid {PropertyName}")
           .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(x => x.Type).NotNull().WithMessage("{PropertyName} is required.");


    }
    private async Task<bool> CyberComponentNameUnique(UpdateCyberComponentCommand c, CancellationToken token)
    {
        return !await _cyberComponentRepository.IsNameExist(c.Name, c.Id);
    }

    private Task<bool> IsValidGuid(UpdateCyberComponentCommand c, CancellationToken cancellationToken)
    {
        try
        {
            Guard.Against.InvalidGuidOrEmpty(c.Id, "Id");
            Guard.Against.InvalidGuidOrEmpty(c.SiteId, "SiteId");
            Guard.Against.InvalidGuidOrEmpty(c.SiteId, "ServerTypeId");
            return Task.FromResult(true);
        }
        catch (Exception)
        {
            return Task.FromResult(false);
        }
    }
}