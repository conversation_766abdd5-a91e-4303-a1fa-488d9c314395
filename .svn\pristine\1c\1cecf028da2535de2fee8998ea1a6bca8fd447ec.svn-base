﻿using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;

public class GetWorkflowOperationGroupRunningStatusQueryHandler : IRequestHandler<
    GetWorkflowOperationGroupRunningStatusQuery, List<WorkflowOperationGroupRunningStatusVm>>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;

    public GetWorkflowOperationGroupRunningStatusQueryHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowActionResultRepository workflowActionResultRepository, 
        IInfraObjectRepository infraObjectRepository, ILoadBalancerRepository loadBalancerRepository)
    {
        _mapper = mapper;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _loadBalancerRepository = loadBalancerRepository;
    }

    public async Task<List<WorkflowOperationGroupRunningStatusVm>> Handle(
        GetWorkflowOperationGroupRunningStatusQuery request, CancellationToken cancellationToken)
    {

        #region OldCode

        //var workflowOperationRunningStatus = await _workflowOperationRepository.GetWorkflowOperationByRunningStatus();

        //var workflowOperationGroup = new List<Domain.Entities.WorkflowOperationGroup>();

        //foreach (var workflowOperation in workflowOperationRunningStatus)
        //{
        //    var workflowOperationGroupRunningStatus = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(workflowOperation.ReferenceId);
        //    workflowOperationGroup.AddRange(workflowOperationGroupRunningStatus);
        //}

        ////workflowOperationRunningStatus.ForEach(x =>
        ////{
        ////    var workflowOperationGroupRunningStatus = _workflowOperationGroupRepository
        ////        .GetWorkflowOperationGroupByWorkflowOperationId(x.ReferenceId).Result;
        ////    workflowOperationGroup.AddRange(workflowOperationGroupRunningStatus);
        ////});

        //var workflowOperationListVm =
        //    _mapper.Map<List<WorkflowOperationGroupRunningStatusVm>>(workflowOperationRunningStatus);

        //var workflowOperationGroupListVm = _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroup);

        //foreach (var groupVm in workflowOperationGroupListVm.Where(groupVm => groupVm.InfraObjectId.IsNotNullOrWhiteSpace()))
        //{
        //    var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(groupVm.InfraObjectId);
        //    groupVm.State = infraObject?.State ?? string.Empty;

        //    if(groupVm.NodeId.IsNotNullOrWhiteSpace())
        //    {
        //        var loadBalancer = await _loadBalancerRepository.GetByReferenceIdAsync(groupVm.NodeId);
        //        groupVm.NodeName = loadBalancer?.Name ?? string.Empty;
        //    }
        //}


        ////workflowOperationGroupListVm.ForEach(x =>
        ////{
        ////    if (x.InfraObjectId.IsNotNullOrWhiteSpace())
        ////    {
        ////        var infraObject = _infraObjectRepository.GetByReferenceIdAsync(x.InfraObjectId).Result;

        ////        x.State = infraObject?.State ?? "";
        ////    }
        ////});

        //workflowOperationListVm.ForEach(x =>
        //    x.WorkflowOperationGroupListVm.AddRange(
        //        workflowOperationGroupListVm.Where(y => y.WorkflowOperationId == x.Id)));

        //foreach (var opVm in workflowOperationListVm)
        //{
        //    var workflowActionResultStatusCount = await _workflowActionResultRepository
        //        .GetWorkflowActionResultByWorkflowOperationId(opVm.Id);
        //    opVm.WorkflowActionStatusCount = _mapper.Map<WorkflowActionStatusCount>(workflowActionResultStatusCount);
        //}


        ////workflowOperationListVm.ForEach(actionCount =>
        ////{
        ////    var workflowActionResultStatusCount = _workflowActionResultRepository
        ////        .GetWorkflowActionResultByWorkflowOperationId(actionCount.Id).Result;

        ////    actionCount.WorkflowActionStatusCount =
        ////        _mapper.Map<WorkflowActionStatusCount>(workflowActionResultStatusCount);
        ////});

        #endregion

        var workflowOperationRunningStatus = await _workflowOperationRepository.GetWorkflowOperationByRunningStatus();

        if(workflowOperationRunningStatus.Count == 0)
            return new List<WorkflowOperationGroupRunningStatusVm>();

        var operationIds = workflowOperationRunningStatus
            .Where(x => x.ReferenceId.IsNotNullOrWhiteSpace())
            .Select(x => x.ReferenceId)
            .ToList();

        var workflowOperationGroup = await _workflowOperationGroupRepository
            .GetOperationGroupByWorkflowOperationIds(operationIds);

        var workflowOperationListVm =
            _mapper.Map<List<WorkflowOperationGroupRunningStatusVm>>(workflowOperationRunningStatus);

        var workflowOperationGroupListVm = _mapper.Map<List<WorkflowOperationGroupListVm>>(workflowOperationGroup);

        var result = workflowOperationGroupListVm
            .Where(groupVm => groupVm.InfraObjectId.IsNotNullOrWhiteSpace() || groupVm.NodeId.IsNotNullOrWhiteSpace())
            .Aggregate(new { InfraIds = new HashSet<string>(), NodeIds = new HashSet<string>() }, (acc, groupVm) =>
            {
                if (groupVm.InfraObjectId.IsNotNullOrWhiteSpace())
                    acc.InfraIds.Add(groupVm.InfraObjectId);

                if (groupVm.NodeId.IsNotNullOrWhiteSpace())
                    acc.NodeIds.Add(groupVm.NodeId);

                return acc;
            });

        var infraObjects = result.InfraIds.Count > 0 
            ? await _infraObjectRepository.GetInfraStateByReferenceIds(result.InfraIds.ToList())
            : new List<Domain.Entities.InfraObject>();

        var loadBalancers = result.NodeIds.Count > 0 
            ? await _loadBalancerRepository.GetNodeNameByIdAsync(result.NodeIds.ToList())
            : new List<Domain.Entities.LoadBalancer>();

        workflowOperationGroupListVm = workflowOperationGroupListVm.Select(groupVm =>
        {
            groupVm.State = infraObjects.FirstOrDefault(x => x.ReferenceId == groupVm.InfraObjectId)?.State ?? string.Empty;
            groupVm.NodeName = loadBalancers.FirstOrDefault(x => x.ReferenceId == groupVm.NodeId)?.Name ?? string.Empty;
            return groupVm;
        }).ToList();

        workflowOperationListVm.ForEach(x =>
            x.WorkflowOperationGroupListVm.AddRange(
                workflowOperationGroupListVm.Where(y => y.WorkflowOperationId == x.Id)));

        var workflowActionResultStatusCount = await _workflowActionResultRepository.GetByWorkflowOperationIds(operationIds);

        // var workflowActionCounts = _mapper.Map<List<WorkflowActionStatusCount>>(workflowActionResultStatusCount);

        var workflowActionCounts = workflowActionResultStatusCount
            .GroupBy(x => x.WorkflowOperationId)
            .Select(group => new WorkflowActionStatusCount
            {
                WorkflowOperationId = group.Key ?? "NA",
                SkipCount = group.Count(x => x.Status?.Trim().ToLower() == "skip" || x.Status?.Trim().ToLower() == "skipped"),
                SuccessCount = group.Count(x => x.Status?.Trim().ToLower() == "success"),
                BypassedCount = group.Count(x => x.Status?.Trim().ToLower() == "bypassed"),
                ErrorCount = group.Count(x => x.Status?.Trim().ToLower() == "error"),
                RunningCount = group.Count(x => x.Status?.Trim().ToLower() == "running")
            }).ToList();



        workflowOperationListVm.ForEach(actionCount =>
        {
            actionCount.WorkflowActionStatusCount = workflowActionCounts.FirstOrDefault(x => x.WorkflowOperationId == actionCount.Id) ?? new WorkflowActionStatusCount();
        });


        return workflowOperationListVm;
    }
}