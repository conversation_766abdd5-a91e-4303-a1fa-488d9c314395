﻿using ContinuityPatrol.Application.Features.User.Events.SendEmail;
using ContinuityPatrol.Application.Features.User.Events.UpdateSendEmail;
using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;

namespace ContinuityPatrol.Application.Helper;

public class EmailTemplateHelper
{
    public static string WebAddress = "<a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a>";
    public static string Year => $"{DateTime.Now.Year}-{DateTime.Now.Year + 1}";

    public static string ApprovalMatrixAcceptEmailBody(string userName,string workflowName,string approverName)
    {
       return $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>ApprovalMatrix Approval</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #41c200;text-align: center;\">\r\n                    <h2>Approval</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:approval_matrix_approved alt=\"Unlocked_Image\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{userName},</b></p>\r\n\t\t\t\t\t\t<p>Your request to modify the workflow \"<b>{workflowName}</b>\" has been \r\n\t\t\t            <b style=\"color: #41c200;\">approved</b> by <b>{approverName}</b>.\r\n\t\t               </p>\r\n                </td>\r\n            </tr>\r\n          \r\n            </tr>\r\n\t\t  \r\n            <tr>\r\n                <td>\r\n                    <p><b>Note :</b> If you have any questions or concerns, please feel free to contact us. We will be happy to assist \r\n                        you.Thank you for choosing our services.</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version 6.0 © 2023-2024\r\n                        <a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>"; 
    }

    public static string ApprovalMatrixRejectEmailBody(string userName, string workflowName, string approverName)
    {
        return $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>ApprovalMatrix Rejection</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\"font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px; margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px; width: 100%;\" /></th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #dc3545; text-align: center;\">\r\n                    <h2>Rejection</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:approval_matrix_rejected alt=\"Rejected_Image\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p>Dear <b style=\"text-transform: uppercase;\">{userName},</b></p>\r\n                    <p>\r\n                        Your request to modify the workflow \"<b>{workflowName}</b>\" has been \r\n                        <b style=\"color: #dc3545;\">rejected</b> by <b>{approverName}</b>.\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Note :</b> If you have any questions or concerns, please feel free to contact us. We will be happy to assist you. Thank you for choosing our services.</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray; font-size: 10px; text-align: center;\">\r\n                        Continuity Patrol Version 6.0 © 2023-2024\r\n                        <a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a> - All Rights Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</body>\r\n\r\n</html>\r\n";
    }

    public static string DeleteIsVerifyWorkflow(string userName,string otp,string version)
    {
        return $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>Confirm Deletion of Workflow</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #FFAF00;text-align: center;\">\r\n                    <h2>Confirm Deletion of Workflow</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:confim_delete alt=\"confim_delete.png\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p>Hello <b style=\" text-transform: uppercase;\">{userName}</b></p>\r\n                    <p>We have received your request to delete the Frozen Workflow. To ensure this action, please verify your \r\n                        confimation by entering the below one-time password (OTP)\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <table style=\"margin: auto;   border-radius: 1.25rem;\r\n                    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px; background-color: #fff;padding: 5px 10px;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td><img src=cid:password alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">one-time password (OTP) </p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{otp}</p>\r\n                                </td>\r\n                            </tr>\r\n                           \r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Note :</b>If you have any questions or concerns, please feel free to contact us. We will be happy to assist \r\n                        you.Thank you for choosing our services</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version 6.0 © 2023-2024\r\n                        <a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";
    }


    public static string GetAccountLockedEmailBody(string loginName, string version)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Account Locked</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #d82121;text-align: center;\">\r\n                    <h2>Account Locked</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:account_locked alt=\"Locked_Image\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{loginName},</b></p>\r\n                    <p>User Account has been Locked, To Unlock contact Continuity Patrol Admin\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n          \r\n            <tr>\r\n                <td>\r\n                    <p><b>Note :</b> If you have any questions or concerns, please feel free to contact us. We will be happy to assist \r\n                        you.Thank you for choosing our services.</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year}\r\n                        {WebAddress} - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";
    }

    public static string GetAccountUnlockedEmailBody(User eventToUpdate, string version)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Account Unlocked</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #41c200;text-align: center;\">\r\n                    <h2>Account Unlocked</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:unlocked_image alt=\"Unlocked_Image\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{eventToUpdate.LoginName},</b></p>\r\n                    <p>User Account has been Unlocked! Thank you for your understanding. Dive into the enhanced experience \r\n                        and explore without limits.\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n          \r\n            <tr>\r\n                <td>\r\n                    <p><b>Note :</b> If you have any questions or concerns, please feel free to contact us. We will be happy to assist \r\n                        you.Thank you for choosing our services.</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year}\r\n                        {WebAddress} - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";
    }

    public static string GetAccountCreatedEmailBody(CreateSendEmailEvent request, string version, string loginUrl)
    {
        var requestPassword = SecurityHelper.Decrypt(request.EncryptPassword);

        if (request.LoginType.ToLower().Equals("ad"))
            return
                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Account Created</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 750px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.png\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #007bff;text-align: center;\">\r\n                    <h2>User Account Created</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:User_created alt=\"Isometric_Orange_Warning\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <br/>\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{request.UserName},</b></p>\r\n                        <p>We are pleased to inform you that your account has been created successfully. You can now access\r\n                        <b>Continuity Patrol {version}</b>\r\n                        by using the following credentials :\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <table style=\"margin: auto;   border-radius: 1.25rem;\r\n                    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px; background-color: #fff;padding: 20px;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td><img src=cid:username alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Login Name</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{request.LoginName}</p>\r\n                                </td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td><img src=cid:password alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Password</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{requestPassword}</p>\r\n                                </td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td><img src=cid:company_name alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Company Name</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{request.CompanyName}</p>\r\n                                </td>\r\n\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p> If you have any\r\n                        questions or concerns, please feel free to contact us. We will be happy to assist you. Thank you\r\n                        for choosing our services. <a href=\"{loginUrl}\" style=\"color: #007bff;\">Click here to login</a></p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year}\r\n                        {WebAddress} - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";

        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Account Created</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 750px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.png\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #007bff;text-align: center;\">\r\n                    <h2>User Account Created</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:User_created alt=\"Isometric_Orange_Warning\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <br/>\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{request.UserName},</b></p>\r\n                        <p>We are pleased to inform you that your account has been created successfully. You can now access\r\n                        <b>Continuity Patrol {version}</b>\r\n                        by using the following credentials :\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <table style=\"margin: auto;   border-radius: 1.25rem;\r\n                    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px; background-color: #fff;padding: 20px;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td><img src=cid:username alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Login Name</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{request.LoginName}</p>\r\n                                </td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td><img src=cid:password alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Password</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{requestPassword}</p>\r\n                                </td>\r\n                            </tr>\r\n                            <tr>\r\n                                <td><img src=cid:company_name alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Company Name</p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{request.CompanyName}</p>\r\n                                </td>\r\n\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p>We recommend that you change your password as soon as you log in to your account for the first\r\n                        time. This will help you protect your account from unauthorized access. If you have any\r\n                        questions or concerns, please feel free to contact us. We will be happy to assist you. Thank you\r\n                        for choosing our services. <a href=\"{loginUrl}\" style=\"color: #007bff;\">Click here to login</a></p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year}\r\n                        {WebAddress} - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";
    }
    public static string GetAccountUpdateRoleEmailBody(UpdateSendEmailEvent request, string version, string loginUrl)
    {

        //return
        //$"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Role Updated</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n<body style=\"font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 750px; margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.png\" style=\"margin: 10px 0px; width: 100%;\" /></th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #007bff; text-align: center;\">\r\n                    <h2>User Role Updated</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:User_updated alt=\"Isometric_Orange_Warning\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <br />\r\n                    <p>Dear <b style=\"text-transform: uppercase;\">{request.UserName},</b></p>\r\n    <p>We are pleased to inform you that your user role has been successfully updated in <b>Continuity Patrol {version}</b>. The role has changed from <b>{request.PreviouseRole}</b> to <b>{request.CurrentRole}</b>. This change was made by <b>{request.ChangedByUserName}</b> (Role: <b>{request.ChangedByUserRole}</b>).</p>\r\n                </td>\r\n            \r\n                    <p>If you have any questions or require assistance regarding your updated role, please contact your <b>{request.ChangedByUserRole}</b>.  Thank you for using our services. <a href=\"{loginUrl}\" style=\"color: #007bff;\">Click here to login</a></p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray; font-size: 10px; text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year} {WebAddress} - All Rights Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</body>\r\n</html>\r\n";
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>User Role Updated</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n<body style=\"font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 750px; margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.png\" style=\"margin: 10px 0px; width: 100%;\" /></th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #007bff; text-align: center;\">\r\n                    <h2>User Role Updated</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:User_created alt=\"Isometric_Orange_Warning\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <br />\r\n                    <p>Dear <b style=\"text-transform: uppercase;\">{request.UserName},</b></p>\r\n                    <p>We are pleased to inform you that your user role has been successfully updated in <b>Continuity Patrol {version}</b>. The role has changed from <b>{request.PreviouseRole}</b> to <b>{request.CurrentRole}</b>. This change was made by <b>{request.ChangedByUserName}</b> (Role: <b>{request.ChangedByUserRole}</b>).</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p>If you have any questions or require assistance regarding your updated role, please contact your <b>{request.ChangedByUserRole}</b>. Thank you for using our services. <a href=\"{loginUrl}\" style=\"color: #007bff;\">Click here to login</a></p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray; font-size: 10px; text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year} {WebAddress} - All Rights Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</body>\r\n</html>\r\n";
    }

    public static string GetForgotPasswordEmailBody(string loginName, string newPassword, string version)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <title>Forgot Password</title>\r\n    <style>\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color: #007bff;text-align: center;\">\r\n                    <h2>Forgot Password!</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:forgot_password1 alt=\"Isometric_Orange_Warning\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p>Dear <b style=\" text-transform: uppercase;\">{loginName},</b></p>\r\n                    <p>We are pleased to inform you that your account  Password has been modified successfully. You can now access\r\n                        <b>Continuity Patrol {version}</b>\r\n                        by using the following credentials :\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <table style=\"margin: auto;   border-radius: 1.25rem;\r\n                    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px; background-color: #fff;padding: 20px;\">\r\n                        <tbody>\r\n                           \r\n                            <tr>\r\n                                <td><img src=cid:password alt=\"username\" /></td>\r\n                                <td style=\"color: gray;font-weight: bold;padding: 0px 10px 0px 5px;\">\r\n                                    <p style=\"line-height:0px;\">Temporary Password </p>\r\n                                </td>\r\n                                <td style=\"padding: 10px 0px;\">:</td>\r\n                                <td style=\"padding: 10px 5pxpx;font-weight: bold;\">\r\n                                    <p style=\"line-height:0px;\">{newPassword}\t</p>\r\n                                </td>\r\n                            </tr>\r\n               <tr>\r\n                               <td colspan=\"4\">\r\n                <span style=\"color: red; font-size:10px; margin-top: 0px;\">Note : This password is valid for 1 hour only.</span> \r\n                               </td>\r\n                           </tr>            \r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p>We recommend that you change your password as soon as you log in to your account for the first\r\n                        time. This will help you protect your account from unauthorized access. If you have any\r\n                        questions or concerns, please feel free to contact us. We will be happy to assist you. Thank you\r\n                        for choosing our services.</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {Year}\r\n                        {WebAddress} - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n\r\n</body>\r\n\r\n</html>";
    }

    public static string GetSendTestEmailBody(string userName, string version)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n  <title>Test Mail</title>\r\n    <style>\r\n        * {{\r\n            margin: 0;\r\n            padding: 0;\r\n            box-sizing: border-box;\r\n            font-family: \"Poppins\", sans-serif;\r\n        }}\r\n\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 26px;\r\n        }}\r\n\r\n        .logo {{\r\n            height: 40px;\r\n        }}\r\n\r\n        .max_width {{\r\n            max-width: 600px;\r\n            margin: auto;\r\n        }}\r\n\r\n        .main_content {{\r\n            margin: 10px 0px;\r\n        }}\r\n\r\n        .top_img {{\r\n            width: 100%;\r\n        }}\r\n\r\n        .container {{\r\n            margin: 0px 15px;\r\n        }}\r\n\r\n        .my-2 {{\r\n            margin: 15px 0px;\r\n        }}\r\n\r\n        .text-center {{\r\n            text-align: center;\r\n        }}\r\n\r\n        .text-dark {{\r\n            color: #000;\r\n            font-size: 2rem;\r\n            margin: 5px 0px;\r\n        }}\r\n\r\n        .user_name {{\r\n            font-weight: bold;\r\n            text-transform: uppercase;\r\n        }}\r\n\r\n\r\n        .btn-primary {{\r\n            color: #fff;\r\n            background-color: #007bff;\r\n            border-color: #007bff;\r\n        }}\r\n\r\n        a {{\r\n            color: gray;\r\n        }}\r\n\r\n        .server_down_img {{\r\n            height: 270px;\r\n        }}\r\n\r\n        .footer {{\r\n            color: gray;\r\n            font-size: 10px;\r\n        }}\r\n\r\n        .bg-gray {{\r\n            background-color: #ededed;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body>\r\n    <div class=\"max_width\">\r\n        <img src=cid:abstract alt=\"Abstract.png\" class=\"top_img\" />\r\n        <div class=\"container\">\r\n            <div class=\"main_content\">\r\n                <img src=cid:cp_logo alt=\"CP_Logo\" class=\"logo\" />\r\n                <div class=\"text-center\">\r\n                    <h1 class=\"text-dark\">Test Mail</h1>\r\n                    <img src=cid:Test_Mail alt=\"Isometric_Orange_Warning\"\r\n                        class=\"server_down_img\" />\r\n                    <p>Dear <span class=\"user_name\">{userName},</span></p>\r\n                    <p>This is an Email Message sent while Testing the SM<span style=\"font-weight: 600;\">TP\r\n                            Configuration</span></p>\r\n                    <br />\r\n                </div>\r\n            </div>\r\n            <p style=\"font-weight: bold;\">\r\n                Best regards,\r\n            </p>\r\n            <p style=\"margin-bottom: 10px;\">Continuity Patrol </p>\r\n        </div>\r\n        <div class=\"bg-gray text-center\">\r\n            <p class=\"footer\">\r\n                Continuity Patrol Version {version} © {Year} {WebAddress} -\r\n                All Rights Reserved\r\n            </p>\r\n        </div>\r\n\r\n    </div>\r\n</body>\r\n\r\n</html>";
    }

    public static string DrCalendarActivityEmailBody(DateTime scheduledStartDate, DateTime scheduledEndDate,
        string profileNamesString, string businessServiceName)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n      <title>Drill Activity Notification</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <!--  -->\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"abstract.png\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo  alt=\"cp_logo.png\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color:green;text-align: center;\">\r\n                    <h2>Drill Activity\r\n                    </h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:Drill-Activity alt=\"Drill-Activity.png\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                 <td>\r\n    <p>Hi <b>Team,</b></p>\r\n    <p>DR Activity for Business Service: {businessServiceName}</p>\r\n    <p>Profile Name: {profileNamesString}</p>\r\n    <p>Start Date: {scheduledStartDate}</p>\r\n    <p>End Date:{scheduledEndDate}</p>\r\n </td>\r\n            </tr>\r\n\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version 6.0 © 2024-2025\r\n                        <a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</body>\r\n\r\n</html>";
    }

    public static string DrCalendarActivityUpdateEmailBody(DateTime scheduledStartDate, DateTime scheduledEndDate,
        string profileNamesString, string businessServiceName)
    {
        return
            $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n      <title>Drill Activity Notification</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 13px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <!--  -->\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"abstract.png\" style=\"margin: 10px 0px;width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo  alt=\"cp_logo.png\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"color:green;text-align: center;\">\r\n                    <h2>Drill Activity\r\n                    </h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:Drill-Activity alt=\"Drill-Activity.png\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                 <td>\r\n    <p>Hi <b>Team,</b></p>\r\n  <p><b>The following DR Activity  has been modified:,</b></p>\r\n   <p>DR Activity for Business Service: {businessServiceName}</p>\r\n    <p>Profile Name: {profileNamesString}</p>\r\n    <p>Start Date: {scheduledStartDate}</p>\r\n    <p>End Date:{scheduledEndDate}</p>\r\n </td>\r\n            </tr>\r\n\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol Team</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version 6.0 © 2024-2025\r\n                        <a href=\"https://www.ptechnosoft.com/\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</body>\r\n\r\n</html>";
    }

    public static string WorkflowDrCalenderEmailBody(
    List<WorkflowDrCalenderSendEmailDataTableVm> tableRows,
    DateTime date,
    DateTime startTime,
    DateTime endTime,
    string location,
    string organizer,
    List<string> attendees,
    string activityDetails)
    {
        var attendeesList = string.Join("", attendees.Select(a => $"<li>{a}</li>"));

        var actionItemsTable = string.Join("", tableRows.Select(row =>
      $@"
    <tr>
        <td>{row.WorkflowName}</td>
        <td>{row.ActionType}</td>
        <td>{row.StartTime.ToString("yyyy-MM-dd HH:mm") ?? "N/A"}</td>
        <td>{row.EndTime.ToString("yyyy-MM-dd HH:mm") ?? "N/A"}</td>
        <td>{row.Status}</td>
    </tr>"));

        return $"<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <style>\r\n        body {{\r\n            font-family: Arial, sans-serif;\r\n            line-height: 1.6;\r\n        }}\r\n        table {{\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin: 20px 0;\r\n        }}\r\n        table, th, td {{\r\n            border: 1px solid #ddd;\r\n        }}\r\n        th, td {{\r\n            padding: 8px;\r\n            text-align: left;\r\n        }}\r\n        th {{\r\n            background-color: #f4f4f4;\r\n        }}\r\n    </style>\r\n</head>\r\n<body>\r\n    <h2>Disaster Recovery (DR) Activity</h2>\r\n    <p><strong>Date:</strong> {date.Date}</p>\r\n    <p><strong>Time:</strong> {startTime.TimeOfDay} - {endTime.TimeOfDay}</p>\r\n    <p><strong>Location:</strong> {location}</p>\r\n    <p><strong>Organizer:</strong> {organizer}</p>\r\n\r\n    <h3>Attendees</h3>\r\n    <ul>\r\n        {attendeesList}\r\n    </ul>\r\n\r\n    <h3>Action Items</h3>\r\n    <table>\r\n        <thead>\r\n            <tr>\r\n                <th>Workflow Name</th>\r\n                <th>Activity Type</th>\r\n                <th>Start Time</th>\r\n                <th>End Time</th>\r\n                <th>Status</th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            {actionItemsTable}\r\n        </tbody>\r\n    </table>\r\n\r\n    <h3>Activity Details</h3>\r\n    <p>{activityDetails}</p>\r\n\r\n    <p>Best regards,<br/>\r\n    {organizer}<br/>\r\n    DR Manager<br/>\r\n  <EMAIL></p>\r\n</body>\r\n</html>";
    }
}