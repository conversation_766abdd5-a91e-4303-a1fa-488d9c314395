using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DRReadyLogFixture : IDisposable
{
    public List<DRReadyLog> DRReadyLogPaginationList { get; set; }
    public List<DRReadyLog> DRReadyLogList { get; set; }
    public DRReadyLog DRReadyLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string BusinessFunctionId = "BF_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WF_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DRReadyLogFixture()
    {
        var fixture = new Fixture();

        DRReadyLogList = fixture.Create<List<DRReadyLog>>();

        DRReadyLogPaginationList = fixture.CreateMany<DRReadyLog>(20).ToList();

        DRReadyLogPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DRReadyLogPaginationList.ForEach(x => x.IsActive = true);
        DRReadyLogPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DRReadyLogPaginationList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DRReadyLogPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DRReadyLogPaginationList.ForEach(x => x.WorkflowId = WorkflowId);

        DRReadyLogList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DRReadyLogList.ForEach(x => x.IsActive = true);
        DRReadyLogList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DRReadyLogList.ForEach(x => x.BusinessFunctionId = BusinessFunctionId);
        DRReadyLogList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DRReadyLogList.ForEach(x => x.WorkflowId = WorkflowId);

        DRReadyLogDto = fixture.Create<DRReadyLog>();
        DRReadyLogDto.ReferenceId = Guid.NewGuid().ToString();
        DRReadyLogDto.IsActive = true;
        DRReadyLogDto.BusinessServiceId = BusinessServiceId;
        DRReadyLogDto.BusinessFunctionId = BusinessFunctionId;
        DRReadyLogDto.InfraObjectId = InfraObjectId;
        DRReadyLogDto.WorkflowId = WorkflowId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
