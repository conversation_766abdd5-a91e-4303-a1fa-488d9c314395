using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ApprovalMatrixApprovalsControllerTests : IClassFixture<ApprovalMatrixApprovalFixture>
{
    private readonly ApprovalMatrixApprovalFixture _approvalMatrixApprovalFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ApprovalMatrixApprovalsController _controller;

    public ApprovalMatrixApprovalsControllerTests(ApprovalMatrixApprovalFixture approvalMatrixApprovalFixture)
    {
        _approvalMatrixApprovalFixture = approvalMatrixApprovalFixture;

        var testBuilder = new ControllerTestBuilder<ApprovalMatrixApprovalsController>();
        _controller = testBuilder.CreateController(
            _ => new ApprovalMatrixApprovalsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetApprovalMatrixApprovals_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_approvalMatrixApprovalFixture.ApprovalMatrixApprovalListVm);

        // Act
        var result = await _controller.GetApprovalMatrixApprovals();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvals = Assert.IsAssignableFrom<List<ApprovalMatrixApprovalListVm>>(okResult.Value);
        Assert.Equal(3, approvals.Count);
    }

    [Fact]
    public async Task GetApprovalMatrixApprovals_ReturnsEmptyList_WhenNoApprovalsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalListQuery>(), default))
            .ReturnsAsync(new List<ApprovalMatrixApprovalListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixApprovals();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvals = Assert.IsAssignableFrom<List<ApprovalMatrixApprovalListVm>>(okResult.Value);
        Assert.Empty(approvals);
    }

    [Fact]
    public async Task GetApprovalMatrixApprovalById_ReturnsApproval_WhenIdIsValid()
    {
        // Arrange
        var approvalId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixApprovalDetailQuery>(q => q.Id == approvalId), default))
            .ReturnsAsync(_approvalMatrixApprovalFixture.ApprovalMatrixApprovalDetailVm);

        // Act
        var result = await _controller.GetApprovalMatrixApprovalById(approvalId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approval = Assert.IsType<ApprovalMatrixApprovalDetailVm>(okResult.Value);
        Assert.NotNull(approval);
    }

    [Fact]
    public async Task GetApprovalMatrixApprovalById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetApprovalMatrixApprovalById("invalid-guid"));
    }

    [Fact]
    public async Task CreateApprovalMatrixApproval_Returns201Created()
    {
        // Arrange
        var command = _approvalMatrixApprovalFixture.CreateApprovalMatrixApprovalCommand;
        var expectedMessage = $"ApprovalMatrixApproval '{command.ProcessName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixApprovalResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrixApproval(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixApprovalResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateApprovalMatrixApproval_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"ApprovalMatrixApproval '{_approvalMatrixApprovalFixture.UpdateApprovalMatrixApprovalCommand.ProcessName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateApprovalMatrixApprovalCommand>(), default))
            .ReturnsAsync(new UpdateApprovalMatrixApprovalResponse
            {
                Message = expectedMessage,
                Id = _approvalMatrixApprovalFixture.UpdateApprovalMatrixApprovalCommand.Id
            });

        // Act
        var result = await _controller.UpdateApprovalMatrixApproval(_approvalMatrixApprovalFixture.UpdateApprovalMatrixApprovalCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrixApproval_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "ApprovalMatrixApproval 'Test Approval' has been deleted successfully!.";
        var approvalId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixApprovalCommand>(c => c.Id == approvalId), default))
            .ReturnsAsync(new DeleteApprovalMatrixApprovalResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixApproval(approvalId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrixApprovals_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetApprovalMatrixApprovalPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _approvalMatrixApprovalFixture.ApprovalMatrixApprovalListVm;
        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixApprovalListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixApprovals(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixApprovalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixApprovalListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }


    [Fact]
    public async Task IsApprovalMatrixApprovalNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsApprovalMatrixApprovalNameExist("ExistingApproval", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixApprovalNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixApprovalNameExist("NewApproval", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateApprovalMatrixApproval_ValidatesRequiredFields()
    {
        // Arrange
        var command = new CreateApprovalMatrixApprovalCommand
        {
            ProcessName = "", // Empty process name should cause validation error
            Description = "Test description",
            UserName = "test.user"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("ProcessName is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateApprovalMatrixApproval(command));
    }

    [Fact]
    public async Task UpdateApprovalMatrixApproval_ValidatesApprovalExists()
    {
        // Arrange
        var command = new UpdateApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Updated Process",
            Description = "Updated description"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrixApproval not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateApprovalMatrixApproval(command));
    }


    [Fact]
    public async Task GetPaginatedApprovalMatrixApprovals_HandlesFilteringByStatus()
    {
        // Arrange
        var query = new GetApprovalMatrixApprovalPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var pendingApprovals = new List<ApprovalMatrixApprovalListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ProcessName = "Pending Process 1",
                Status = "Pending",
                UserName = "user1",
                ApproverName = "approver1"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ProcessName = "Pending Process 2",
                Status = "Pending",
                UserName = "user2",
                ApproverName = "approver2"
            }
        };

        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixApprovalListVm>.Success(
            data: pendingApprovals,
            count: pendingApprovals.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixApprovals(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixApprovalListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixApprovalListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, approval => Assert.Equal("Pending", approval.Status));
    }

    [Fact]
    public async Task CreateApprovalMatrixApproval_HandlesComplexApprovalWorkflow()
    {
        // Arrange
        var command = new CreateApprovalMatrixApprovalCommand
        {
        
            ProcessName = "Multi-Level Security Approval",
            Description = "Complex security approval requiring multiple levels of authorization with detailed audit trail",
            UserName = "security.analyst",
            Status = "Pending Level 1",
            Approver = "security.manager"
        };

        var expectedMessage = $"ApprovalMatrixApproval '{command.ProcessName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixApprovalResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateApprovalMatrixApproval(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixApprovalResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrixApproval_VerifiesApprovalIsDeactivated()
    {
        // Arrange
        var approvalId = Guid.NewGuid().ToString();
        var expectedMessage = "ApprovalMatrixApproval 'Test Approval' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixApprovalCommand>(c => c.Id == approvalId), default))
            .ReturnsAsync(new DeleteApprovalMatrixApprovalResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixApproval(approvalId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetApprovalMatrixApprovals_HandlesEmptyDatabase()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixApprovalListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ApprovalMatrixApprovalListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixApprovals();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var approvals = Assert.IsAssignableFrom<List<ApprovalMatrixApprovalListVm>>(okResult.Value);
        Assert.Empty(approvals);
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Test Process",
            Status = "Approved"
        };

        var expectedResponse = new ApprovalMatrixApprovalResponse
        {
            Message = "Approval matrix status updated successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateApprovalMatrixStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.Equal(expectedResponse.Id, response.Id);
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithApprovedStatus_ProcessesCorrectly()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Budget Approval Process",
            Status = "Approved"
        };

        var expectedResponse = new ApprovalMatrixApprovalResponse
        {
            Message = "Approval matrix status updated to Approved successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
                c.Status == "Approved"), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateApprovalMatrixStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);

        // Verify the mediator was called with the correct status
        _mediatorMock.Verify(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
            c.Status == "Approved"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithRejectedStatus_ProcessesCorrectly()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Security Access Request",
            Status = "Rejected"
        };

        var expectedResponse = new ApprovalMatrixApprovalResponse
        {
            Message = "Approval matrix status updated to Rejected successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
                c.Status == "Rejected"), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateApprovalMatrixStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);

        // Verify the mediator was called with the correct status
        _mediatorMock.Verify(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
            c.Status == "Rejected"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithInvalidId_ThrowsNotFoundException()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Invalid Process",
            Status = "Approved"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrixApproval not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.UpdateApprovalMatrixStatus(command));
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithPendingStatus_ProcessesCorrectly()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "HR Process Request",
            Status = "Pending"
        };

        var expectedResponse = new ApprovalMatrixApprovalResponse
        {
            Message = "Approval matrix status updated to Pending successfully.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
                c.Status == "Pending"), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateApprovalMatrixStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);

        // Verify the mediator was called with the correct status
        _mediatorMock.Verify(m => m.Send(It.Is<ApprovalMatrixApprovalCommand>(c =>
            c.Status == "Pending"), default), Times.Once);
    }

    [Fact]
    public async Task UpdateApprovalMatrixStatus_WithComplexProcessName_ProcessesCorrectly()
    {
        // Arrange
        var command = new ApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Multi-Level Enterprise Security Approval Process with Escalation",
            Status = "Approved"
        };

        var expectedResponse = new ApprovalMatrixApprovalResponse
        {
            Message = "Approval matrix status updated successfully for complex process.",
            Id = command.Id
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateApprovalMatrixStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<ApprovalMatrixApprovalResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.Equal(expectedResponse.Id, response.Id);
    }
}
