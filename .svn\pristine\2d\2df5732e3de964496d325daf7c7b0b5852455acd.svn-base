namespace ContinuityPatrol.Application.Features.FiaInterval.Queries.GetDetail;

public class GetFiaIntervalDetailsQueryHandler : IRequestHandler<GetFiaIntervalDetailQuery, FiaIntervalDetailVm>
{
    private readonly IFiaIntervalRepository _fiaIntervalRepository;
    private readonly IMapper _mapper;

    public GetFiaIntervalDetailsQueryHandler(IMapper mapper, IFiaIntervalRepository fiaIntervalRepository)
    {
        _mapper = mapper;
        _fiaIntervalRepository = fiaIntervalRepository;
    }

    public async Task<FiaIntervalDetailVm> Handle(GetFiaIntervalDetailQuery request,
        CancellationToken cancellationToken)
    {
        var fiaInterval = await _fiaIntervalRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(fiaInterval, nameof(Domain.Entities.FiaInterval),
            new NotFoundException(nameof(Domain.Entities.FiaInterval), request.Id));

        var fiaIntervalDetailDto = _mapper.Map<FiaIntervalDetailVm>(fiaInterval);

        return fiaIntervalDetailDto;
    }
}