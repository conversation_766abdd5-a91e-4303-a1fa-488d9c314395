using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SRMMonitorLogFixture
{
    public SRMMonitorLog CreateSRMMonitorLog(
        string type = "SRM_REPLICATION",
        string infraObjectId = "INFRA_001",
        string infraObjectName = "Default SRM Object",
        string workflowId = "WF_001",
        string workflowName = "Default Workflow",
        string properties = null,
        string configuredRPO = "15",
        string dataLagValue = "5",
        string threshold = "10",
        DateTime? createdDate = null,
        DateTime? lastModifiedDate = null,
        bool isActive = true,
        bool isDelete = false)
    {
        var now = DateTime.UtcNow;
        return new SRMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            InfraObjectId = infraObjectId,
            InfraObjectName = infraObjectName,
            WorkflowId = workflowId,
            WorkflowName = workflowName,
            Properties = properties ?? "{\"rpo\": \"15\", \"status\": \"active\", \"lastSync\": \"2024-01-01T10:00:00Z\"}",
            ConfiguredRPO = configuredRPO,
            DataLagValue = dataLagValue,
            Threshold = threshold,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = createdDate ?? now,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = lastModifiedDate ?? now
        };
    }

    public List<SRMMonitorLog> CreateMultipleSRMMonitorLogs(int count, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SRMMonitorLog>();
        for (int i = 1; i <= count; i++)
        {
            logs.Add(CreateSRMMonitorLog(
                type: $"SRM_TYPE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"SRM Object {i}",
                workflowId: $"WF_{i:D3}",
                workflowName: $"Workflow {i}",
                configuredRPO: (15 + i).ToString(),
                dataLagValue: i.ToString(),
                threshold: (10 + i).ToString(),
                createdDate: DateTime.UtcNow.AddDays(-i)
            ));
        }
        return logs;
    }

    public SRMMonitorLog CreateSRMMonitorLogWithSpecificId(string referenceId, string type = "SRM_REPLICATION")
    {
        return new SRMMonitorLog
        {
            ReferenceId = referenceId,
            Type = type,
            InfraObjectId = "INFRA_TEST",
            InfraObjectName = "Test SRM Object",
            WorkflowId = "WF_TEST",
            WorkflowName = "Test Workflow",
            Properties = "{\"test\": true}",
            ConfiguredRPO = "15",
            DataLagValue = "5",
            Threshold = "10",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SRMMonitorLog CreateSRMMonitorLogForType(string type, string infraObjectId = "INFRA_001")
    {
        return CreateSRMMonitorLog(
            type: type,
            infraObjectId: infraObjectId,
            infraObjectName: $"SRM Object for {type}",
            workflowName: $"Workflow for {type}"
        );
    }

    public List<SRMMonitorLog> CreateSRMMonitorLogsForTypes(List<string> types, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SRMMonitorLog>();
        foreach (var type in types)
        {
            logs.Add(CreateSRMMonitorLogForType(type, infraObjectId));
        }
        return logs;
    }

    public List<SRMMonitorLog> CreateSRMMonitorLogsWithStatus(int activeCount, int inactiveCount, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SRMMonitorLog>();
        
        for (int i = 1; i <= activeCount; i++)
        {
            logs.Add(CreateSRMMonitorLog(
                type: $"SRM_ACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Active SRM {i}",
                isActive: true
            ));
        }
        
        for (int i = 1; i <= inactiveCount; i++)
        {
            logs.Add(CreateSRMMonitorLog(
                type: $"SRM_INACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Inactive SRM {i}",
                isActive: false
            ));
        }
        
        return logs;
    }

    public SRMMonitorLog CreateSRMMonitorLogForInfraObject(string infraObjectId, string infraObjectName = null)
    {
        return CreateSRMMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: infraObjectName ?? $"SRM Object for {infraObjectId}",
            workflowName: $"Workflow for {infraObjectId}"
        );
    }

    public List<SRMMonitorLog> CreateSRMMonitorLogsForInfraObjects(List<string> infraObjectIds)
    {
        var logs = new List<SRMMonitorLog>();
        foreach (var infraObjectId in infraObjectIds)
        {
            logs.Add(CreateSRMMonitorLogForInfraObject(infraObjectId));
        }
        return logs;
    }

    public SRMMonitorLog CreateSRMMonitorLogWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSRMMonitorLog(properties: propertiesJson);
    }

    public SRMMonitorLog CreateSRMMonitorLogWithComplexProperties()
    {
        var complexProperties = new Dictionary<string, object>
        {
            {"rpo", "15"},
            {"status", "running"},
            {"lastSync", "2024-01-01T10:00:00Z"},
            {"replicationDetails", new Dictionary<string, object>
                {
                    {"sourceDatastore", "DS001"},
                    {"targetDatastore", "DS002"},
                    {"replicationMode", "async"},
                    {"compressionEnabled", true}
                }
            },
            {"performance", new Dictionary<string, object>
                {
                    {"throughput", "100MB/s"},
                    {"latency", "5ms"},
                    {"errorRate", "0.01%"}
                }
            }
        };

        return CreateSRMMonitorLogWithProperties(complexProperties);
    }

    public List<SRMMonitorLog> CreateSRMMonitorLogsForDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<SRMMonitorLog>();
        var dateRange = (endDate - startDate).TotalDays;
        var interval = dateRange / count;

        for (int i = 0; i < count; i++)
        {
            var logDate = startDate.AddDays(i * interval);
            logs.Add(CreateSRMMonitorLog(
                infraObjectId: infraObjectId,
                infraObjectName: $"SRM Object {i + 1}",
                workflowName: $"Workflow {i + 1}",
                createdDate: logDate,
                lastModifiedDate: logDate
            ));
        }
        
        return logs;
    }

    public SRMMonitorLog CreateSRMMonitorLogForWorkflow(string workflowId, string workflowName = null, string infraObjectId = "INFRA_001")
    {
        return CreateSRMMonitorLog(
            infraObjectId: infraObjectId,
            workflowId: workflowId,
            workflowName: workflowName ?? $"Workflow {workflowId}",
            infraObjectName: $"SRM Object for {workflowId}"
        );
    }

    public SRMMonitorLog CreateSRMMonitorLogWithRPOSettings(string configuredRPO, string dataLagValue, string threshold)
    {
        return CreateSRMMonitorLog(
            configuredRPO: configuredRPO,
            dataLagValue: dataLagValue,
            threshold: threshold,
            properties: $"{{\"rpo\": \"{configuredRPO}\", \"dataLag\": \"{dataLagValue}\", \"threshold\": \"{threshold}\"}}"
        );
    }

    public List<SRMMonitorLog> CreateSRMMonitorLogsForRPOTesting()
    {
        return new List<SRMMonitorLog>
        {
            CreateSRMMonitorLogWithRPOSettings("15", "5", "10"),
            CreateSRMMonitorLogWithRPOSettings("30", "10", "20"),
            CreateSRMMonitorLogWithRPOSettings("60", "25", "40"),
            CreateSRMMonitorLogWithRPOSettings("120", "50", "80")
        };
    }

    public SRMMonitorLog CreateMinimalSRMMonitorLog()
    {
        return new SRMMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "SRM_MINIMAL",
            InfraObjectId = "MINIMAL_INFRA",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SRMMonitorLog CreateSRMMonitorLogForTesting(
        string testName,
        string type = null,
        string infraObjectId = null)
    {
        return CreateSRMMonitorLog(
            type: type ?? $"SRM_{testName.ToUpper()}",
            infraObjectId: infraObjectId ?? $"INFRA_{testName.ToUpper()}",
            infraObjectName: $"Test SRM for {testName}",
            workflowName: $"Test Workflow for {testName}"
        );
    }
}
