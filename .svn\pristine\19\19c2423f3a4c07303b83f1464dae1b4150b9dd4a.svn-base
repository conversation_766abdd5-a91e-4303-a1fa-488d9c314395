﻿using ContinuityPatrol.Application.Features.FormHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormHistory.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.FormHistoryModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class FormHistoryService : BaseService, IFormHistoryService
{
    public FormHistoryService(IHttpContextAccessor accessor) : base(accessor) { }

    public async Task<List<FormHistoryDetailVm>> GetFormHistoryById(string formId, string? formVersion)
    {
        Guard.Against.InvalidGuidOrEmpty(formId, "Form Id");

        Logger.LogDebug($"Get FormHistory Detail by Action Id '{formId}' and '{formVersion}'");

        return await Mediator.Send(new GetFormHistoryDetailQuery { FormId = formId,Version = formVersion});
    }
    public async Task<List<FormHistoryListVm>> GetFormHistories()
    {
        Logger.LogDebug("Get All FormHistories");

        return await Mediator.Send(new GetFormHistoryListQuery());
    }
}
