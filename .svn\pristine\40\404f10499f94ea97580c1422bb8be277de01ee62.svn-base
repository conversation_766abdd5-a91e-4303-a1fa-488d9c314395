﻿@model ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel.FormMappingViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-form-mapping"></i><span>Form Mapping</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li> 
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="formName=" id="formName">
                                        <label class="form-check-label" for="formName">
                                            Form Name
                                        </label>
                                    </div> 
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="MappingType=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Form Type Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off"  class="form-check-input" type="checkbox" value="database=" id="FormTypeName">
                                        <label class="form-check-label" for="FormTypeName">
                                            Type
                                        </label>
                                    </div>
                                </li>                              
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm create-model-btn" data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblFormMapping" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Form Name</th>
                        <th>Form Type Name</th>
                        <th>Type</th>
                        <th>Version</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                   
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Create-->
<div class="modal fade" id="CreateModal" tabindex="-1" data-bs-backdrop="static"  aria-labelledby="exampleModalLabel">
    <partial name="Configuration" />
</div>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel">
    <partial name="Delete" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<script src="~/js/siteadmin/form/commonfunctions.js"></script>
<script src="~/js/siteadmin/form/formmapping/formmappingfunctions.js"></script>
<script src="~/js/siteadmin/form/formmapping/formmapping.js"></script>
