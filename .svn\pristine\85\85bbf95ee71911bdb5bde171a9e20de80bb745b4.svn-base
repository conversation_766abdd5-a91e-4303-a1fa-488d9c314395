﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Queries
{
    public class GetRoboCopyJobPaginatedQueryHandlerTests
    {
        private readonly Mock<IRoboCopyJobRepository> _mockRoboCopyJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRoboCopyJobPaginatedQueryHandler _handler;

        public GetRoboCopyJobPaginatedQueryHandlerTests()
        {
            _mockRoboCopyJobRepository = new Mock<IRoboCopyJobRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetRoboCopyJobPaginatedQueryHandler(
                _mockRoboCopyJobRepository.Object,
                _mockMapper.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedRoboCopyJobList_WhenRoboCopyJobsExist()
        {
            var query = new GetRoboCopyJobPaginatedQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "Test"
            };

            var roboCopyJobList = new List<Domain.Entities.RoboCopyJob>
            {
                new Domain.Entities.RoboCopyJob
                {
                    Id = 1,
                    ReplicationName = "TestReplication1"
                },
                new Domain.Entities.RoboCopyJob
                {
                    Id = 2,
                    ReplicationName = "TestReplication2"
                }
            };

            var expectedResult = new List<RoboCopyJobListVm>
            {
                new RoboCopyJobListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReplicationName = roboCopyJobList[0].ReplicationName
                },
                new RoboCopyJobListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    ReplicationName = roboCopyJobList[1].ReplicationName
                }
            };

            var paginatedResult = new PaginatedResult<RoboCopyJobListVm>
            {
                Data = expectedResult,
                TotalCount = roboCopyJobList.Count
            };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopyJobList.AsQueryable());

            _mockMapper.Setup(mapper => mapper.Map<RoboCopyJobListVm>(It.IsAny<Domain.Entities.RoboCopyJob>()))
                .Returns((Domain.Entities.RoboCopyJob job) => new RoboCopyJobListVm
                {
                    Id = job.SiteId,
                    ReplicationName = job.ReplicationName
                });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(paginatedResult.TotalCount, result.TotalCount);
            Assert.Equal(expectedResult.Count, result.Data.Count);
            Assert.Equal(expectedResult[0].Id, result.Data[0].Id);
            Assert.Equal(expectedResult[1].ReplicationName, result.Data[1].ReplicationName);

            _mockRoboCopyJobRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);

            _mockMapper.Verify(mapper => mapper.Map<RoboCopyJobListVm>(It.IsAny<Domain.Entities.RoboCopyJob>()), Times.Exactly(roboCopyJobList.Count));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoRoboCopyJobsExist()
        {
            var query = new GetRoboCopyJobPaginatedQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "Test"
            };

            var roboCopyJobList = new List<Domain.Entities.RoboCopyJob>();
            var expectedResult = new PaginatedResult<RoboCopyJobListVm>
            {
                Data = new List<RoboCopyJobListVm>(),
                TotalCount = 0
            };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopyJobList.AsQueryable());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(expectedResult.TotalCount, result.TotalCount);

            _mockRoboCopyJobRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldFilterBySearchString_WhenSearchStringIsProvided()
        {
            var query = new GetRoboCopyJobPaginatedQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "TestReplication"
            };

            var roboCopyJobList = new List<Domain.Entities.RoboCopyJob>
            {
                new Domain.Entities.RoboCopyJob
                {
                    Id = 1,
                    ReplicationName = "TestReplication1"
                },
                new Domain.Entities.RoboCopyJob
                {
                    Id = 2,
                    ReplicationName = "AnotherReplication"
                }
            };

            var filteredList = roboCopyJobList.Where(r => r.ReplicationName.Contains(query.SearchString)).ToList();
            var expectedResult = new PaginatedResult<RoboCopyJobListVm>
            {
                Data = filteredList.Select(r => new RoboCopyJobListVm
                {
                    Id = r.SiteId,
                    ReplicationName = r.ReplicationName
                }).ToList(),
                TotalCount = filteredList.Count
            };

            _mockRoboCopyJobRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopyJobList.AsQueryable());

            _mockMapper.Setup(mapper => mapper.Map<RoboCopyJobListVm>(It.IsAny<Domain.Entities.RoboCopyJob>()))
                .Returns((Domain.Entities.RoboCopyJob job) => new RoboCopyJobListVm
                {
                    Id = job.SiteId,
                    ReplicationName = job.ReplicationName
                });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal(expectedResult.TotalCount, result.TotalCount);
            Assert.Equal("TestReplication1", result.Data[0].ReplicationName);

            _mockRoboCopyJobRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);

            _mockMapper.Verify(mapper => mapper.Map<RoboCopyJobListVm>(It.IsAny<Domain.Entities.RoboCopyJob>()), Times.Once);
        }
    }
}
