﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorLogsModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringLogs.Queries
{
    public class GetSVCGMMonitorLogPaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorLogRepository> _mockSVCGMMonitorLogRepository;
        private readonly GetSVCGMMonitorLogPaginatedListQueryHandler _handler;

        public GetSVCGMMonitorLogPaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorLogRepository = new Mock<ISVCGMMonitorLogRepository>();
            _handler = new GetSVCGMMonitorLogPaginatedListQueryHandler(_mockSVCGMMonitorLogRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenDataExists()
        {
            var query = new GetSVCGMMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "ref"
            };

            var mockEntities = new List<SVCGMMonitorLog>
            {
                new SVCGMMonitorLog { Id = 1, ReferenceId = "ref1", IsActive = true },
                new SVCGMMonitorLog { Id = 2, ReferenceId = "ref2", IsActive = true }
            };

            var mockViewModels = mockEntities.Select(m => new SVCGMMonitorLogsListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = m.ReferenceId
            }).ToList();

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockEntities.AsQueryable());

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLogsListVm>(It.IsAny<SVCGMMonitorLog>()))
                .Returns((SVCGMMonitorLog log) => new SVCGMMonitorLogsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    InfraObjectId = log.ReferenceId
                });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal(1, result.TotalCount);
            Assert.Equal(2, result.TotalPages);
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoDataExists()
        {
            var query = new GetSVCGMMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "nonexistent"
            };

            var emptyList = new List<SVCGMMonitorLog>();

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(emptyList.AsQueryable());

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLogsListVm>(It.IsAny<SVCGMMonitorLog>()))
                .Returns((SVCGMMonitorLog log) => new SVCGMMonitorLogsListVm());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldApplySearchFilter()
        {
            var query = new GetSVCGMMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "ref1"
            };

            var mockEntities = new List<SVCGMMonitorLog>
            {
                new SVCGMMonitorLog { Id = 1, ReferenceId = "ref1", IsActive = true },
                new SVCGMMonitorLog { Id = 2, ReferenceId = "ref2", IsActive = true }
            };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(mockEntities.AsQueryable());

            var spec = new SvcgmMonitorLogFilterSpecification(query.SearchString);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("ref1", result.Data[0].Id);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryPaginatedListAllAsyncOnce()
        {
            var query = new GetSVCGMMonitorLogPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "any"
            };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetPaginatedQuery())
                .Returns(Enumerable.Empty<SVCGMMonitorLog>().AsQueryable());

            await _handler.Handle(query, CancellationToken.None);

            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
