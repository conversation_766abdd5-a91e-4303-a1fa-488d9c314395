﻿
let jsonData = "", inputValue, selectedValues = [], datas = [], dataTable
let jsonData1 = datas;
const updateInfraObjectStates = [];
let updateStateToUnlocks = []
$(".clsArrow").hide();
var createPermission = $("#ManageCreate").data("create-permission").toLowerCase();
if (createPermission == 'false') {
    $("#infraActive").removeAttr('data-bs-toggle').attr('disabled', createPermission == 'false') &&
        $("#maintenance").removeAttr('data-bs-toggle').attr('disabled', createPermission == 'false') &&
        $("#btnReset").removeAttr('data-bs-toggle').attr('disabled', createPermission == 'false');
}
$('#selectAllOpen').attr('open', true);
$(function () {
    dataTable = $('#manageBusinessService').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col-5 text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Manage/ManageOperationalService/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "businessFunctionName" : sortIndex === 3 ? "businessServiceName" :
                        sortIndex === 4 ? "typeName" : sortIndex === 5 ? "replicationCategoryType" : sortIndex === 6 ? "state" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.businessServiceId = $('#businessService').find("option:selected").val() === "All" ? '' : $('#businessService').find("option:selected").val();
                    d.BusniessFunctionId = $('#businessFunction').find("option:selected").val() === "All" ? '' : $('#businessFunction').find("option:selected").val()
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        json?.data?.data.forEach(function (item) {
                            datas.push(item);
                            if (item.businessServiceName != undefined) {
                                $('#businessService').append(' <option value=""></option>');
                                $('#businessService').append(' <option value="' + item.businessServiceId + '">' + item.businessServiceName + '</option>');
                            }
                            $("#businessService option").each(function () {
                                $(this).siblings('[value="' + this.value + '"]').remove();
                            });
                        });
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        return json?.data?.data;
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 5, 6],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "InfraObject Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "businessFunctionName", "name": "Operational Function", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "Operational Service", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "typeName", "name": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "replicationCategoryType", "name": "Replication Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "reason", "name": "Reason", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span title="${data ?? "NA"}">${data ?? "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "state", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            var statusIcon = '';
                            if (data === "Maintenance" || data == null) {
                                statusIcon = '<i class="cp-maintenance text-primary"></i>';
                            } else if (data === "Active") {
                                statusIcon = '<i class="cp-active-inactive text-success"></i>';
                            } else if (data === "Locked") {
                                statusIcon = '<i class="cp-lock text-warning me-1"></i>';
                            }
                            return '<span title="' + data + '">' + statusIcon + '</span>';
                        }

                        return data;
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });


    // Events

    $("#businessService").on("change", function () {
        $('#businessFunction').empty();
        if ($("#businessService option:selected").val() == "All") {
            $('#BF_change').css("display", "none");
        } else {
            $('#BF_change').css("display", "");
        }
        let checkOF = []
        $('#businessFunction').append('<option value="All">All</option>');
        datas.forEach(function (value) {
            if ($('#businessService').find(':selected').val() === value.businessServiceId && !checkOF.includes(value.businessFunctionName))
                $('#businessFunction').append('<option value="' + value.businessFunctionId + '">' + value.businessFunctionName + '</option>');
            checkOF.push(value.businessFunctionName)
        });
        dataTable.ajax.reload()
    });

    $("#businessFunction").on("change", function () {
        dataTable.ajax.reload()
    });

    if ($("#businessService option:selected").val() == "All") {
        $('#BF_change').css("display", "none");
    }

    const jsonArray = {
        "updateInfraObjectStates": [],
        "updateStateToUnlocks": []
    };

    treeListView();
    $(".box").hide();
    $('input[type="radio"]').on("click", function () {
        inputValue = $(this).attr("value");
        if (inputValue == "Active") {
            $("#SaveFunction").text("Active");
            $('#updateStatus').val(inputValue);
            $("#saveTrigger").show();
            $("#maintananceBox").empty().hide();
            $("#btnCancle").show();
            $("#textArea").empty();
        }
        else if (inputValue == "Maintenance") {
            $("#SaveFunction").text("Maintenance");
            $('#updateStatus').val(inputValue);
            $("#saveTrigger").show();
            $("#maintananceBox").show();
            $("#btnCancle").show();
        }
    });
    //$('.selecttree').on('change', function (e) {
    //    let s = this.checked;
    //    let id = $(this).attr('businessid');
    //    let funcId = $(this).attr('functionId');
    //    let infraId = $(this).attr('infraId');

    //    JsonTreeView(s, id, funcId, infraId);
    //});
    $(document).on('change', '.selecttree', function () {
        const checkbox = this;
        const check = checkbox.checked;
        const businessId = $(checkbox).attr("businessid");
        const functionId = $(checkbox).attr("functionid");
        const infraId = $(checkbox).attr("infraid");
        JsonTreeView(check, businessId, functionId, infraId);
    });


    $('#lockSelect').on('change', function () {
        treeViewToggle(this, "Locked", { disableInfra: true, disableMaintenance: true });
    });
    $('#maintenanceSelect').on('change', function () {
        treeViewToggle(this, "Maintenance", { disableInfra: false, disableMaintenance: true });
    });
    $('#selectAll').on('change', function () {
        treeViewToggle(this, "Active", { disableInfra: true, disableMaintenance: false });
    });
    $('#selectAllOpen').on('toggle', function () {
        let open = this.open;
        open ? SelectAllTreeViewExpended(true) : SelectAllTreeViewExpended(false)
    });

    $("#btnSave").on("click", async function () {
        const form = $("#CreateForm");
        const stateText = $("#stateText").text();
        const textAreaValue = $("#textArea").val();
        updateInfraObjectStates.length = 0;
        updateStateToUnlocks.length = 0;
        filterInfraObjects(textAreaValue, stateText);
        if (updateInfraObjectStates.length === 0) {
            return;
        }
        $("#reasonFor").val(textAreaValue);
        let updateInfraObjectStateCommand = { updateInfraObjectStates };
        let UpdateStateToUnlockCommand = { updateStateToUnlocks };
        form.trigger("submit");

        if (stateText !== "Unlock") {
            if (stateText === "Active") {
                updateState("Active", "Maintenance");
            } else if (stateText === "Maintenance") {
                updateState("Maintenance", "Active");
            }
            updateInfraObjectStateCommand.updateInfraObjectStates = updateStateToUnlocks;
            $("#textProperties").val(JSON.stringify(updateInfraObjectStateCommand));
            $.ajax({
                url: RootUrl + "Manage/ManageOperationalService/UpdateInfraObjectState",
                method: "POST",
                dataType: "json",
                data: {
                    updateInfraObjectStateCommand,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    let data = result.data;
                    $('#ConfimationModal').modal('hide');
                    notificationAlert("success", data.message);
                    setTimeout(() => location.reload(), 2000);
                },
                error: function (error) {
                    errorNotification(error);
                }
            });
        } else {
            $("#textProperties").val(JSON.stringify(UpdateStateToUnlockCommand));

            $.ajax({
                url: RootUrl + "Manage/ManageOperationalService/UpdateInfraObjectUnlock",
                method: "POST",
                dataType: "json",
                data: {
                    UpdateStateToUnlockCommand,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    let data = result.data;
                    $('#ConfimationModal').modal('hide');
                    notificationAlert("success", data.message);
                    setTimeout(() => location.reload(), 2000);
                },
                error: function (error) {
                    errorNotification(error);
                }
            });
        }
        function updateState(newState, oldState) {
            updateStateToUnlocks = updateInfraObjectStateCommand.updateInfraObjectStates.map(obj => ({
                ...obj,
                state: obj.state === oldState ? newState : obj.state
            }));
        }
    });

    ///Events
    $("#btnReset").on("click", function () {
        let check = false
        check ? SelectAllTreeView(true) : SelectAllTreeView(false);
        let open = false;
        open ? SelectAllTreeViewExpended(true) : SelectAllTreeViewExpended(false)
        $("#error-message").hide();
    })

    let isTreeExpanded = false;

    $("#expandTreeAll").on("click", function () {
        isTreeExpanded = !isTreeExpanded;
        $("details").prop("open", isTreeExpanded);
        $(".expArrow").toggle(!isTreeExpanded);
        $(".clsArrow").toggle(isTreeExpanded);
        $(".cp-circle-rightarrow, .cp-circle-downarrow").each(function () {
            if (isTreeExpanded) {
                $(this).removeClass("cp-circle-rightarrow").addClass("cp-circle-downarrow");
            } else {
                $(this).removeClass("cp-circle-downarrow").addClass("cp-circle-rightarrow");
            }
        });
    });

    $("#infraActive").on("click", function () {
        var buttonValue = $(this).val();
        $("#updateStatus").val(buttonValue);
        $("#stateText,#infraText,#statests").text("Active");
        $("#boxContainer").hide();
        $("#activestatein").text("Maintenance to Active")
        updateInfraObjectStates.length = 0;
        filterInfraObjects();
        if (!Array.isArray(updateInfraObjectStates) || !updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "maintenance")) {
            notificationAlert("warning", "Select at least one Maintenance InfraObject");
        } else if (updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "maintenance")) {
            $('#stateConfirmationModal').modal('show');
        }
    })

    $("#maintenance").on("click", function () {
        var buttonValue = $(this).val();
        $("#updateStatus").val(buttonValue);
        $("#stateText").text("Maintenance");
        $("#boxContainer").show();
        $("#infraText").text("Maintenance");
        $("#activestatein").text("Active to Maintenance")
        $("#statests").text("Maintenance");
        updateInfraObjectStates.length = 0;
        filterInfraObjects();
        if (!Array.isArray(updateInfraObjectStates) || !updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "active")) {
            notificationAlert("warning", "Select at least one Active InfraObject");
        }
        else if (Array.isArray(updateInfraObjectStates) && updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "maintenance")) {
            $('#stateConfirmationModal').modal('show');
        }
        else {
            if (updateInfraObjectStates.length === 0) {
                $('#alertClass').removeClass();
                $('#alertClass').addClass("cp-exclamation")
                notificationAlert("warning", "Select at least one InfraObject")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $("#ConfimationModal").modal("hide");
            }
            else {
                $("#error-message").hide();
                $("#ConfimationModal").modal("show");
                $("#textArea").val("")
            }
        }
    })

    $("#Unlock").on("click", function () {
        var buttonValue = $(this).val();
        $("#updateStatus").val(buttonValue);
        $("#stateText").text("Unlock");
        $("#boxContainer").hide();
        $("#infraText").text("Unlock");
        $("#activestatein").text("Unlock to Maintenance")
        $("#statests").text("Unlock");
        updateStateToUnlocks.length = 0;
        filterInfraObjects();
        if (!Array.isArray(updateInfraObjectStates) || !updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "locked")) {
            notificationAlert("warning", "Select at least one Locked InfraObject");
        }
        else if (Array.isArray(updateInfraObjectStates) && updateInfraObjectStates.some(x => { let state = x?.state?.trim().toLowerCase(); return state === "maintenance" || state === "active"; })) {
            $('#stateConfirmationModal').modal('show');
        }
        else {
            if (updateStateToUnlocks.length === 0) {
                $('#alertClass').removeClass();
                $('#alertClass').addClass("cp-exclamation")
                notificationAlert("warning", "Select at least one InfraObject")
                $('#mytoastrdata').toast({ delay: 3000 });
                $('#mytoastrdata').toast('show');
                $("#ConfimationModal").modal("hide");
            }
            else {
                $("#error-message").hide();
                $("#ConfimationModal").modal("show");
                $("#textArea").val("")
            }
        }
    })

    $('#search_category').on('keydown input', function () {
        $('#btn_search').trigger('click');
    });

    $('#btn_search').on('click', function () {
        let filter = $('#search_category').val().toLowerCase().trim();
        $('details').removeAttr('open');
        $('.infraListContainer').prop('checked', false).closest('ul').hide();
        $("#workflowTreeNoData").hide().html('');
        if (filter === '') {
            SelectAllTreeViewExpended(false);
            return false;
        }
        let hasMatch = false;

        $('.infraListContainer').each(function () {
            const $checkbox = $(this);
            const name = ($checkbox.data('name') || '').toLowerCase();

            if (name.includes(filter)) {
                $checkbox.closest('ul').show();
                $checkbox.parents('details').attr('open', true);
                hasMatch = true;
            }
        });

        if (!hasMatch) {
            $("#workflowTreeNoData").show().html('<p>No matching infraobject found</p>');
        }
        SelectAllTreeViewExpended(hasMatch);
        return false;
    });

    $("#stateConfirmation").on("click", function () {
        $("#btnSave").trigger("click");
    })
    function filterInfraObjects(reason, state) {
        jsonData.assignedBusinessServices.forEach(service =>
            service.assignedBusinessFunctions.forEach(func =>
                func.assignedInfraObjects.forEach(infra => {
                    if (infra.isSelected) {
                        let obj = { id: infra.id, state: infra.state, reason };
                        updateInfraObjectStates.push(obj);
                        updateStateToUnlocks.push(obj);
                    }
                })
            )
        );
    }
    function updateTreeView(stateFilter, check) {
        jsonData.assignedBusinessServices.forEach(service =>
            service.assignedBusinessFunctions.forEach(bf =>
                bf.assignedInfraObjects.forEach(infra => {
                    if (infra.state === stateFilter) infra.isSelected = check;
                })
            )
        );
        refreshTreeView();
        setTimeout(() => $(`.infra-checkbox[data-state="${stateFilter}"]`).prop('checked', check), 0);
        manageUnlockButton();
    }
    function uncheckAllCheckboxes() {
        $('.infra-checkbox').prop('checked', false);
        jsonData.assignedBusinessServices.forEach(service =>
            service.assignedBusinessFunctions.forEach(bf =>
                bf.assignedInfraObjects.forEach(infra => infra.isSelected = false)
            )
        );
        refreshTreeView();
        manageUnlockButton();
    }
    function manageUnlockButton() {
        const hasLocked = jsonData.assignedBusinessServices.some(service =>
            service.assignedBusinessFunctions.some(bf =>
                bf.assignedInfraObjects.some(infra => infra.isSelected && infra.state === "Locked")
            )
        );
        $('#Unlock').toggleClass('d-flex', hasLocked).toggleClass('d-none', !hasLocked).toggle(hasLocked);
    }

    function refreshTreeView() {
        $("#treeview").empty();
        $('#textProperties').val(JSON.stringify(jsonData));
        createTreeView($("#treeview"), jsonData);
    }

    function treeViewToggle(source, state, options = {}) {
        const check = source.checked;
        if (check) {
            uncheckAllCheckboxes();
            $('#lockSelect, #maintenanceSelect, #selectAll').not(source).prop('checked', false);
            $('#infraActive').prop('disabled', options.disableInfra ?? false);
            $('#maintenance').prop('disabled', options.disableMaintenance ?? false);
        } else {
            $('#infraActive, #maintenance').prop('disabled', false);
        }
        updateTreeView(state, check);
        SelectAllTreeViewExpended(check);
        $('#selectAllOpen').prop('open', check);
    }

    function SelectAllTreeView(check) {
        $('#selectAll').prop('checked', check);
        jsonData.assignedBusinessServices.forEach(service => {
            service.isAll = service.isPartial = check;
            service.assignedBusinessFunctions.forEach(func => {
                func.isAll = func.isPartial = check;
                func.assignedInfraObjects.forEach(infra => infra.isSelected = check);
            });
        });
        let lockedSelected = jsonData.assignedBusinessServices.some(service =>
            service.assignedBusinessFunctions.some(bf =>
                bf.assignedInfraObjects.some(infra => infra.isSelected && infra.state === "Locked")
            )
        );
        $('#Unlock').toggle(lockedSelected).toggleClass('d-flex', lockedSelected).toggleClass('d-none', !lockedSelected);
        refreshTreeView();
        $("#treeview").enable = false;
        $("#maintenance,#infraActive").prop('disabled', false);
        $('#lockSelect,#maintenanceSelect').prop('checked', false);
    }
    function SelectAllTreeViewExpended(open) {
        $('#selectAllOpen').attr('open', true);
    }
    function JsonTreeView(s, id, funcId, infraId) {
        jsonData.assignedBusinessServices.forEach(service => {
            if (service.id !== id) return;

            if (!funcId && !infraId) {
                service.isAll = service.isPartial = s;
                service.assignedBusinessFunctions.forEach(bf => {
                    if (bf) {
                        bf.isAll = bf.isPartial = s;
                        bf.assignedInfraObjects.forEach(infra => {
                            if (infra.id) infra.isSelected = s;
                        });
                    }
                });
                $("#containerId details").removeAttr("open");
            } else if (funcId && !infraId) {
                service.assignedBusinessFunctions.forEach(bf => {
                    if (bf.id === funcId) {
                        bf.isAll = bf.isPartial = s;
                        bf.assignedInfraObjects.forEach(infra => {
                            if (infra.id) infra.isSelected = s;
                        });

                        const selectedCount = service.assignedBusinessFunctions.filter(f => f.isAll).length;
                        service.isAll = selectedCount === service.assignedBusinessFunctions.length;
                        service.isPartial = selectedCount > 0;
                    }
                });
            } else if (funcId && infraId != null) {
                service.assignedBusinessFunctions.forEach(bf => {
                    if (!bf) return;
                    bf.assignedInfraObjects.forEach(infra => {
                        if (infra.id !== infraId) return;
                        infra.isSelected = s;

                        const selectedInfraCount = bf.assignedInfraObjects.filter(i => i.isSelected).length;
                        const totalInfra = bf.assignedInfraObjects.length;

                        bf.isAll = selectedInfraCount === totalInfra;
                        bf.isPartial = selectedInfraCount > 0 && selectedInfraCount < totalInfra;

                        const selectedBFCount = service.assignedBusinessFunctions.filter(f => f.isAll).length;
                        service.isAll = selectedBFCount === service.assignedBusinessFunctions.length;
                        service.isPartial = selectedBFCount > 0 && selectedBFCount < service.assignedBusinessFunctions.length;
                    });
                });
            }
        });

        const isAnySelectedLocked = () => {
            return jsonData.assignedBusinessServices.some(service =>
                service.assignedBusinessFunctions.some(bf =>
                    bf.assignedInfraObjects.some(infra =>
                        infra.isSelected && infra.state === "Locked"
                    )
                )
            );
        };

        const showUnlock = isAnySelectedLocked();
        $('#Unlock').toggle(showUnlock).toggleClass('d-flex', showUnlock).toggleClass('d-none', !showUnlock);

        const allChecked = jsonData.assignedBusinessServices.every(service => service.isAll);
        $("#selectAll").prop("checked", allChecked);
        $("#maintenanceSelect").prop("checked", allChecked);
        $("#lockSelect").prop("checked", allChecked);
        jsonData.isAll = allChecked;

        $("#treeview").empty();
        $('#textProperties').val(JSON.stringify(jsonData));
        createTreeView($("#treeview"), jsonData, s, id, funcId, infraId);
    }
    function treeListView() {
        $("#treeview").empty();
        $.ajax({
            url: RootUrl + "Manage/ManageOperationalService/GetUserInfraObjectList",
            method: 'GET',
            dataType: 'json',
            success: function (data) {
                jsonData = data;
                populateTreeView(jsonData);
                let bs = jsonData.assignedBusinessServices
                $.each(bs, function (index, value) {
                    datas.push(value.name);
                });
                $('#selectAllOpen details').removeAttr('open');
            },
            error: function (error) {
                notificationAlert('Error:', error);
            }
        });
    }
    function populateTreeView(jsonData) {
        var container = $("#treeview");
        container.empty();
        if (jsonData && jsonData.assignedBusinessServices) {
            createTreeView(container, jsonData);
        } else {
            container.text("No data available.");
        }
    }
    function createTreeView(container, userInfraObject, s, id, funcId, infraId) {
        if (userInfraObject.assignedBusinessServices.length !== 0) {
            userInfraObject.assignedBusinessServices.forEach((service) => {
                let serviceDetails
                if (service.isAll) {
                    serviceDetails = $('<details open></details>');
                } else if (service.isPartial) {
                    serviceDetails = $('<details open></details>');
                } else if (service.id == id) {
                    serviceDetails = $('<details open></details>');
                } else {
                    serviceDetails = $('<details></details>');
                }
                service.assignedBusinessFunctions.forEach((f) => {
                    if (f.id == funcId) {
                        serviceDetails = $('<details open></details>');
                    }
                    f.assignedInfraObjects.forEach((infra) => {
                        if (infra.isSelected == true) {
                            serviceDetails = $('<details open></details>');
                        } else if (infra.id == infraId) {
                            serviceDetails = $('<details open></details>');
                        }
                    });
                })
                const serviceSummary = $('<summary open></summary>');
                const serviceCheckbox = $('<input type="checkbox" class="form-check-input selecttree">');
                serviceCheckbox.attr('businessId', service.id);
                serviceCheckbox.prop('checked', service.isAll);
                serviceSummary.append(serviceCheckbox);
                serviceSummary.append(' ' + service.name);
                if (service.assignedBusinessFunctions.length > 0) {
                    const functionList = $('<ul class="tree ps-0"></ul>');
                    createFunctions(functionList, service.assignedBusinessFunctions, service.id);
                    serviceDetails.append(functionList);
                }
                serviceDetails.append(serviceSummary);
                container.append(serviceDetails[0]);
            });
        }
    }
    function createFunctions(container, functions, id) {
        functions.forEach((func) => {
            const functionDetails = $('<details open></details>');
            const functionSummary = $('<summary></summary>');
            const functionCheckbox = $('<input type="checkbox" class="form-check-input selecttree">');
            functionCheckbox.prop('checked', func.isAll);
            functionCheckbox.attr('businessId', id);
            functionCheckbox.attr('functionId', func.id);
            functionSummary.append(functionCheckbox);
            functionSummary.append(' ' + func.name);
            if (func.assignedInfraObjects.length > 0) {
                const objectList = $('<ul class="tree ps-0"></ul>');
                createObjects(objectList, func.assignedInfraObjects, id, func.id);
                functionDetails.append(objectList);
                functionDetails.attr('open', 'open');
            }
            functionDetails.append(functionSummary);
            container.append(functionDetails[0]);
        });
    }
    function createObjects(container, objects, parentId, functionId) {
        const sortedObjects = objects.length && objects.sort((a, b) => {
            const customCompare = (strA, strB) => {
                if (strA && strB) return strA.localeCompare(strB);
            };
            return customCompare(a?.name, b?.name);
        });

        sortedObjects.length && sortedObjects.forEach((obj) => {
            let state = obj.state.toLowerCase() === "active"
                ? '<i class="cp-active-inactive text-success me-1 fs-7"></i>'
                : obj.state.toLowerCase() === "maintenance"
                    ? '<i class="cp-maintenance text-primary me-1 fs-7"></i>'
                    : obj.state.toLowerCase() === "locked"
                        ? '<i class="cp-lock text-warning me-1"></i>'
                        : "";

            const objectDetails = $('<ul open></ul>');
            const objectSummary = $('<summary style="list-style-type: none;"></summary>');

            const objectCheckbox = $(`<input type="checkbox" 
            id="${obj.id}" 
            class="form-check-input selecttree infraListContainer" 
            data-name="${obj.name.toLowerCase()}">`);

            objectCheckbox.prop('checked', obj.isSelected);
            objectCheckbox.attr('businessId', parentId);
            objectCheckbox.attr('functionId', functionId);
            objectCheckbox.attr('infraId', obj.id);
            objectCheckbox.attr('state', obj.state);

            objectSummary.append(objectCheckbox);
            objectSummary.append(state + ' ' + obj.name);
            objectDetails.append(objectSummary);
            container.append(objectDetails[0]);
        });
    }
});
//function createObjects(container, objects, parentId, functionId) {
//    const sortedObjects = objects.length && objects.sort((a, b) => {
//        const customCompare = (strA, strB) => {
//            if (strA && strB) return strA.localeCompare(strB);
//        };
//        return customCompare(a?.name, b?.name);
//    });
//    sortedObjects.length && sortedObjects.forEach((obj) => {
//        let state = obj.state.toLowerCase() == "active" ? '<i class="cp-active-inactive text-success me-1 fs-7"></i>' : obj.state.toLowerCase() == "maintenance" ? '<i class="cp-maintenance text-primary me-1 fs-7"></i>' : obj.state.toLowerCase() == "locked" ? '<i class="cp-lock text-warning me-1"></i>' : ""

//        const objectDetails = $('<ul open></ul>');
//        const objectSummary =
//            $('<summary style="list-style-type: none;" ></summary>');
//        const objectCheckbox = $('<input type="checkbox"  class="form-check-input selecttree infraListContainer" onchange="selectTree(this)" >');
//        objectCheckbox.prop('checked', obj.isSelected);
//        objectCheckbox.attr('businessId', parentId);
//        objectCheckbox.attr('functionId', functionId);
//        objectCheckbox.attr('infraId', obj.id);
//        objectCheckbox.attr('state', obj.state);
//        objectSummary.append(objectCheckbox);
//        objectSummary.append(state + ' ' + obj.name);
//        objectDetails.append(objectSummary);
//        container.append(objectDetails[0]);
//    });
//   }






//$('#btn_search').on('click', function () {
//    let filter = $('#search_category').val().toLowerCase().trim();
//    if (filter === '') {
//        $('details').removeAttr('open');
//        $('.infraListContainer').hide();
//        $("#workflowTreeNoData").hide().html('');
//        SelectAllTreeViewExpended(false);
//        return false;
//    }
//    let hasMatch = false;
//    $('details').removeAttr('open');
//    $('.infraListContainer').hide();
//    jsonData.assignedBusinessServices.forEach(service => {
//        service.assignedBusinessFunctions.forEach(bf => {
//            bf.assignedInfraObjects.forEach(infra => {
//                let infraName = infra.name.toLowerCase();
//                if (infraName.startsWith(filter)) {
//                    let $item = $(`.infraListContainer[id='${CSS.escape(infra.id)}']`);
//                    $item.show();
//                    $('.infraListContainer').prop('checked', false);
//                    $item.parents('details').attr('open', true);
//                    hasMatch = true;
//                }
//            });
//        });
//    });
//    if (!hasMatch) {
//        $("#workflowTreeNoData")
//            .show()
//            .css({ 'text-align': 'center' })
//            .html('<p>No matching infraobject found</p>');
//    } else {
//        $("#workflowTreeNoData").hide().html('');
//    }
//    SelectAllTreeViewExpended(hasMatch);
//    return false;
//});




//$('#selectAll').on('change', function () {
//    let check = this.checked;
//    if (check == true) {
//        $("#selectAllOpen").attr('open', 'open');
//    }
//    if (check == false) {
//        $("#selectAllOpen").removeAttr('open');
//    }
//    check ? SelectAllTreeView(true) : SelectAllTreeView(false);
//    check ? SelectAllTreeViewExpended(true) : SelectAllTreeViewExpended(false)
//});


//$("#infraActive").on("click",function () {
//    var buttonValue = $(this).val();
//    $("#updateStatus").val(buttonValue);
//    $("#stateText").text("Active");
//    $("#boxContainer").hide();
//    $("#infraText").text("Active");
//    $("#activestatein").text("Maintenance to Active")
//    $("#statests").text("Active");
//    updateInfraObjectStates.length = 0;
//    filterInfraObjects();
//    if (!Array.isArray(updateInfraObjectStates) || !updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "maintenance")) {
//        notificationAlert("warning", "Select at least one Maintenance InfraObject");
//    } else if (updateInfraObjectStates.some(x => x?.state?.trim().toLowerCase() === "active")) {
//        $('#stateConfirmationModal').modal('show');
//    }

//    else {
//        if (updateInfraObjectStates.length === 0) {
//            $('#alertClass').removeClass();
//            $('#alertClass').addClass("cp-exclamation")
//            notificationAlert("warning", "Select at least one InfraObject")
//            $('#mytoastrdata').toast({ delay: 3000 });
//            $('#mytoastrdata').toast('show');
//            $("#ConfimationModal").modal("hide");
//        }
//        else {
//            $("#error-message").hide();
//            $("#ConfimationModal").modal("show");
//            $("#textArea").val("")
//        }
//    }
//})


//function JsonTreeView(s, id, funcId, infraId) {

//    jsonData.assignedBusinessServices.forEach((d) => {
//        if (id && funcId && !infraId) {
//            if (d.id === id) {
//                d.assignedBusinessFunctions.forEach((f) => {
//                    if (f.id === funcId) {
//                        f.isAll = s;
//                        f.isPartial = s;
//                        if (infraId || s === false)
//                            if (infraId) m
//                        {
//                            f.assignedInfraObjects.forEach((infra) => {
//                                if (infra.id != null) {
//                                    infra.isSelected = s;
//                                }
//                            });
//                        }
//                        var selectedBFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
//                            return count + (busimessFunction.isAll ? 1 : 0);
//                        },
//                            0); if (selectedBFCount === d.assignedBusinessFunctions.length) {
//                                d.isAll = true;
//                                d.isPartial = true;
//                            } else if (selectedBFCount > 0) {
//                                d.isAll = false;
//                                d.isPartial = false;
//                            } else {
//                            d.isAll = false;
//                            d.isPartial = false;
//                        }
//                    }
//                });
//            }
//        } else if (d.id === id && !funcId && !infraId) {
//            d.isAll = s;
//            d.isPartial = s;
//            d.assignedBusinessFunctions.forEach((bf) => {
//                if (bf != null) {

//                    d.isAll = s;
//                    d.isPartial = s;
//                    bf.isAll = s;
//                    bf.isPartial = s;
//                    bf.assignedInfraObjects.forEach((infra) => {
//                        if (infra.id != null) {
//                            infra.isSelected = s;
//                        }
//                    });
//                }
//            });

//            $("#containerId details").removeAttr("open");
//        }
//        else if (d.id === id && funcId && infraId != null) {

//            if (s === false) {
//                d.assignedBusinessFunctions.forEach((bf) => {
//                    if (bf != null) {
//                        bf.assignedInfraObjects.forEach((infra) => {
//                            if (infra.id === infraId) {
//                                infra.isSelected = s;
//                                d.assignedBusinessFunctions.forEach((bf) => {
//                                    if (bf.id === funcId) {
//                                        d.isAll = s;
//                                        d.isPartial = s;
//                                        bf.isAll = s;
//                                        bf.isPartial = s;
//                                        //bf.isSelected = s;
//                                    }
//                                });
//                            }
//                        });
//                    }
//                });
//            }
//            else if (s === true) {
//                d.assignedBusinessFunctions.forEach((bf) => {
//                    if (bf != null) {
//                        bf.assignedInfraObjects.forEach((infra) => {

//                            if (s === true) {
//                                if (infra.id === infraId) {

//                                    infra.isSelected = s;
//                                }
//                            } else {
//                                d.isAll = false;
//                                d.isPartial = false;
//                                bf.isAll = false;
//                                bf.isPartial = false;
//                            }
//                            var selectedCount = bf.assignedInfraObjects.reduce(function (count, infra) {
//                                return count + (infra.isSelected ? 1 : 0);
//                            }, 0);
//                            if (selectedCount === bf.assignedInfraObjects.length) {
//                                //d.isAll = true;
//                                //d.isPartial = false
//                                bf.isAll = true;
//                                bf.isPartial = false;
//                            } else if (selectedCount > 0) {
//                                d.isAll = false;
//                                d.isPartial = true;
//                                bf.isAll = false;
//                                bf.isPartial = true;
//                            } else {
//                                bf.isAll = false;
//                                bf.isPartial = false;
//                            }
//                            var selectedBFCount = d.assignedBusinessFunctions.reduce(function (count, busimessFunction) {
//                                return count + (busimessFunction.isAll ? 1 : 0);
//                            }, 0);
//                            if (selectedBFCount === d.assignedBusinessFunctions.length) {
//                                d.isAll = true;
//                                d.isPartial = true;
//                            } else if (selectedBFCount > 0) {
//                                d.isAll = false;
//                                d.isPartial = false;
//                            } else {
//                                d.isAll = false;
//                                d.isPartial = false;
//                            }
//                        });
//                    }
//                });
//            }
//            else {
//                d.isAll = s;
//                d.isPartial = s;
//                d.assignedBusinessFunctions.forEach((bf) => {
//                    if (bf != null) {
//                        d.isAll = s;
//                        d.isPartial = s;
//                        bf.isAll = s;
//                        bf.isPartial = s;
//                        bf.assignedInfraObjects.forEach((infra) => {

//                            if (infra.id == infraId) {
//                                infra.isSelected = s;
//                                d.assignedBusinessFunctions.forEach((bf) => {
//                                    if (bf.id === funcId) {
//                                        bf.isSelected = s;
//                                    }
//                                });
//                            }
//                        });
//                    }
//                });
//            }
//        }
//    });
//    const allBusinessServicesChecked = jsonData.assignedBusinessServices.every(service => service.isAll);
//    $("#selectAll").prop("checked", allBusinessServicesChecked);
//    if (allBusinessServicesChecked) {
//        jsonData.isAll = true;
//    } else {
//        jsonData.isAll = false;
//    }
//    $("#treeview").empty();
//    $('#textProperties').val(JSON.stringify(jsonData));
//    createTreeView($("#treeview"), jsonData, s, id, funcId, infraId);
//}
