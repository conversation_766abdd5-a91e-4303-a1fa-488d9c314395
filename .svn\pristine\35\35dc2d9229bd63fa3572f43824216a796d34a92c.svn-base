﻿<!--Modal Delete-->
@model ContinuityPatrol.Domain.ViewModels.CyberAirGapModel.CyberAirGapViewModel

@Html.AntiForgeryToken()
<div class="modal-dialog modal-sm modal-dialog-centered">
    <form asp-controller="AirGap" asp-action="Delete" asp-route-id="textDeleteId" method="post" enctype="multipart/form-data" class="w-100">
        <div class="modal-content">

            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body   text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to delete <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData"></span> data?</p>
                <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
            </div>
        </div>
    </form>
</div>
