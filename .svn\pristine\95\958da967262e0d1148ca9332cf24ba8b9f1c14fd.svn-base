using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberSnapsFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string StorageGroupName = "STORAGE_GROUP_001";
    public const string Tag = "SNAP_TAG_001";

    public List<CyberSnaps> CyberSnapsPaginationList { get; set; }
    public List<CyberSnaps> CyberSnapsList { get; set; }
    public CyberSnaps CyberSnapsDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberSnapsFixture()
    {
        var fixture = new Fixture();

        CyberSnapsList = fixture.Create<List<CyberSnaps>>();
        CyberSnapsPaginationList = fixture.CreateMany<CyberSnaps>(20).ToList();

        CyberSnapsDto = fixture.Create<CyberSnaps>();
       
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
