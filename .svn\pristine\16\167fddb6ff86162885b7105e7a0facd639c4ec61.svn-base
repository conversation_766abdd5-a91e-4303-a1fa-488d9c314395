﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Domain.ViewModels.DRCalender;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class DrCalenderService : BaseClient, IDrCalenderService
{

    public DrCalenderService(IConfiguration config, IAppCache cache, ILogger<DrCalenderService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateDrCalenderCommand createDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/drcalender", Method.Post);

        request.AddJsonBody(createDrCalenderCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string calId)
    {
        var request = new RestRequest($"api/v6/businessfunctions/{calId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DrCalenderDetailVm> GetDrCalendarById(string id)
    {
        var request = new RestRequest($"api/v6/drcalender/{id}");

        return await Get<DrCalenderDetailVm>(request);
    }

    public async Task<List<DrCalenderActivityListVm>> GetDrCalenderList()
    {
        var request = new RestRequest("api/v6/drcalender");

        return await Get<List<DrCalenderActivityListVm>>(request);
    }

    public async Task<PaginatedResult<DrCalenderActivityListVm>> GetPaginatedDrCalendar(GetDrCalendarPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/drcalender/paginated-list{query}");

        return await Get<PaginatedResult<DrCalenderActivityListVm>>(request);
    }

    public async Task<GetUpcomingDrillCountVm> GetDrCalendarDrillEvents()
    {
        var request = new RestRequest("api/v6/drcalender/drill-events");

        return await Get<GetUpcomingDrillCountVm>(request);
    }

    public async Task<bool> IsDrCalendarNameExist(string activityName, string? id, DateTime scheduleStartTime)
    {
        var request = new RestRequest($"api/v6/drcalender/name-exist?activityName={activityName}&id={id}&scheduleStartTime={scheduleStartTime}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDrCalenderCommand updateDrCalenderCommand)
    {
        var request = new RestRequest("api/v6/drcalender", Method.Put);

        request.AddJsonBody(updateDrCalenderCommand);

        return await Put<BaseResponse>(request);
    }
}