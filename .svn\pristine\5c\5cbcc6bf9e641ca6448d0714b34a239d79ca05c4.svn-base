﻿using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseManager.Validators;

public class UpdateBaseLicenseValidatorTests
{
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;

    private Mock<ILoggedInUserService> _mockLoggedinUserService;

    //private readonly Mock<ILogger<UpdateBaseLicenseCommandValidator>> _logger;
    public List<Domain.Entities.LicenseManager> LicenseManagers { get; set; }

    public UpdateBaseLicenseValidatorTests()
    {
        LicenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();

        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.UpdateBaseLicenseRepository(LicenseManagers);

        //_logger = new Mock<ILogger<UpdateBaseLicenseCommandValidator>>();

        _mockLoggedinUserService = LicenseManagerRepositoryMocks.LoggedInUserServiceMock();
    }

    //LicenseKey

    //[Theory]
    //[AutoLicenseManagerData]
    //public async Task Verify_Update_LicenseKey_InLicenseManager_WithEmpty(UpdateBaseLicenseCommand updateLicenseManagerCommand)
    //{
    //    var validator = new UpdateBaseLicenseCommandValidator(_mockLicenseManagerRepository.Object,_mockLoggedinUserService.Object);

    //    updateLicenseManagerCommand.LicenseKey = "";

    //    var validateResult = await validator.ValidateAsync(updateLicenseManagerCommand, CancellationToken.None);

    //    Assert.Equal(ValidatorConstants.LicenseManager.LicenseManagerLicenseKeyRequired,validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoLicenseManagerData]
    //public async Task Verify_Update_LicenseKey_InLicenseManager_IsNull(UpdateBaseLicenseCommand updateLicenseManagerCommand)
    //{
    //    var validator = new UpdateBaseLicenseCommandValidator(_mockLicenseManagerRepository.Object, _mockLoggedinUserService.Object);

    //    updateLicenseManagerCommand.LicenseKey = null;

    //    var validateResult = await validator.ValidateAsync(updateLicenseManagerCommand, CancellationToken.None);

    //    Assert.Equal(ValidatorConstants.LicenseManager.LicenseManagerLicenseKeyNotNullRequired,validateResult.Errors[2].ErrorMessage);
    //}
}