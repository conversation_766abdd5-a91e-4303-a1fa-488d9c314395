﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class CyberJobWorkflowSchedulerFilterSpecification :Specification<CyberJobWorkflowScheduler>
{
    public CyberJobWorkflowSchedulerFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("workflow=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WorkflowName.Contains(stringItem.Replace("workflow=", "",
                            StringComparison.OrdinalIgnoreCase)));
                   
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.WorkflowName.Contains(searchString) ;
            }
        }
    }
}
