﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.UserGroup.Events.Delete;

namespace ContinuityPatrol.Application.Features.UserGroup.Commands.Delete;

public class DeleteUserGroupCommandHandler : IRequestHandler<DeleteUserGroupCommand, DeleteUserGroupCommandResponse>
{
    private readonly IPublisher _publisher;
    private readonly IUserGroupRepository _userGroupRepository;
    private readonly IReportScheduleRepository _reportScheduleRepository;
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;
    private readonly ILogger<DeleteUserGroupCommandResponse> _logger;

    public DeleteUserGroupCommandHandler(IUserGroupRepository userGroupRepository, IPublisher publisher,
        IReportScheduleRepository reportScheduleRepository, IWorkflowPermissionRepository workflowPermissionRepository,ILogger<DeleteUserGroupCommandResponse> logger)
    {
        _userGroupRepository = userGroupRepository;
        _publisher = publisher;
        _reportScheduleRepository = reportScheduleRepository;
        _workflowPermissionRepository = workflowPermissionRepository;
        _logger = logger;
    }

    public async Task<DeleteUserGroupCommandResponse> Handle(DeleteUserGroupCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _userGroupRepository.GetByReferenceIdAsync(request.Id);

        var reportScheduler = await _reportScheduleRepository.GetReportSchedulerByUserGroupId(request.Id);

        var workflowPermission = await _workflowPermissionRepository.GetWorkflowPermissionByUserIdAsync(request.Id);

        if (reportScheduler.Count > 0 || workflowPermission.Count > 0)
        {
            var loggerDtl = reportScheduler.Count > 0 && workflowPermission.Count > 0 ? "Scheduler report and Workflow user privileges"
                : reportScheduler.Count > 0 ? "Scheduler report"
                : "Workflow user privileges";

            _logger.LogInformation($"The user '{eventToDelete.GroupName}' is currently being used in {loggerDtl}.");

            throw new InvalidException($"The User Group'{eventToDelete.GroupName}' is currently in use");
        }

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.UserGroup),
            new NotFoundException(nameof(Domain.Entities.UserGroup), request.Id));

        eventToDelete.IsActive = false;

        await _userGroupRepository.UpdateAsync(eventToDelete);

        var response = new DeleteUserGroupCommandResponse
        {
            Message = Message.Delete("User Group", eventToDelete.GroupName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new UserGroupDeleteEvent { GroupName = eventToDelete.GroupName }, cancellationToken);

        return response;
    }
}