using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowOperationGroupFixture : IDisposable
{
    public List<WorkflowOperationGroup> WorkflowOperationGroupPaginationList { get; set; }
    public List<WorkflowOperationGroup> WorkflowOperationGroupList { get; set; }
    public WorkflowOperationGroup WorkflowOperationGroupDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowOperationGroupFixture()
    {
        var fixture = new Fixture();

        WorkflowOperationGroupList = fixture.Create<List<WorkflowOperationGroup>>();

        WorkflowOperationGroupPaginationList = fixture.CreateMany<WorkflowOperationGroup>(20).ToList();

        WorkflowOperationGroupPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowOperationGroupList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowOperationGroupDto = fixture.Create<WorkflowOperationGroup>();

        WorkflowOperationGroupDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
