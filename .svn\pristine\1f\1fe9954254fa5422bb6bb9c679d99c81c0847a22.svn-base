﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AccessManagerRepositoryMocks
{
    public static Mock<IAccessManagerRepository> CreateAccessManagerRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(accessManagers);

        accessManagerRepository.Setup(repo => repo.AddAsync(It.IsAny<AccessManager>())).ReturnsAsync(
            (AccessManager accessManager) =>
            {
                accessManager.Id = new Fixture().Create<int>();

                accessManager.ReferenceId = new Fixture().Create<Guid>().ToString();

                accessManagers.Add(accessManager);

                return accessManager;
            });

        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> UpdateAccessManagerRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(accessManagers);

        accessManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => accessManagers.SingleOrDefault(x => x.ReferenceId == i));

        accessManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AccessManager>())).ReturnsAsync((AccessManager accessManager) =>
        {
            var index = accessManagers.FindIndex(item => item.ReferenceId == accessManager.ReferenceId);

            accessManagers[index] = accessManager;

            return accessManager;

        });
        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> DeleteAccessManagerRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(accessManagers);

        accessManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => accessManagers.SingleOrDefault(x => x.ReferenceId == i));

        accessManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AccessManager>())).ReturnsAsync((AccessManager accessManager) =>
        {
            var index = accessManagers.FindIndex(item => item.ReferenceId == accessManager.ReferenceId);

            accessManager.IsActive = false;

            accessManagers[index] = accessManager;

            return accessManager;
        });

        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> GetPaginatedAccessManagerRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        //var queryableAccessManager = accessManagers.BuildMock();

        //accessManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableAccessManager);

        accessManagerRepository.Setup(repo => repo.PaginatedListAllAsync(
              It.IsAny<int>(),
              It.IsAny<int>(),
              It.IsAny<Specification<AccessManager>>(),
              It.IsAny<string>(),
              It.IsAny<string>()))
          .ReturnsAsync((int pageNumber, int pageSize, Specification<AccessManager> spec, string sortColumn, string sortOrder) =>
          {
              var sortedAccessdetail = accessManagers.AsQueryable();

              if (spec.Criteria != null)
              {
                  sortedAccessdetail = sortedAccessdetail.Where(spec.Criteria);
              }

              if (!string.IsNullOrWhiteSpace(sortColumn))
              {
                  sortedAccessdetail = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                      ? sortedAccessdetail.OrderByDescending(c => c.RoleName)
                      : sortedAccessdetail.OrderBy(c => c.RoleName);
              }

              var totalCount = sortedAccessdetail.Count();
              var paginated = sortedAccessdetail
                  .Skip((pageNumber - 1) * pageSize)
                  .Take(pageSize)
                  .ToList();

              return PaginatedResult<AccessManager>.Success(paginated, totalCount, pageNumber, pageSize);
          });

        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> GetAccessManagerRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(accessManagers);

        accessManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => accessManagers.SingleOrDefault(x => x.ReferenceId == i));

        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> GetAccessManagerEmptyRepository()
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AccessManager>());

        return accessManagerRepository;
    }

    public static Mock<IAccessManagerRepository> GetAccessManagerByRoleIdRepository(List<AccessManager> accessManagers)
    {
        var accessManagerRepository = new Mock<IAccessManagerRepository>();

        accessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(It.IsAny<string>()))
            .ReturnsAsync((string i) => accessManagers.FirstOrDefault(x => x.RoleId == i));

        return accessManagerRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateAccessManagerEventRepository(List<UserActivity> userActivities)
    {
        var accessManagerEventRepository = new Mock<IUserActivityRepository>();

        accessManagerEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        accessManagerEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return accessManagerEventRepository;
    }

}