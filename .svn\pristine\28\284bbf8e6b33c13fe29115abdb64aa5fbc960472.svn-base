﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                Azure Database for MySQL (5.7- SingleServer) Replication-PaaS-Monitoirng:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
       
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        MySQL Replication Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th>Replication Monitoring</th>
                                    <th class="text-primary">Source</th>
                                    <th>Replica</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>MySQL Server Name </td>
                                    <td class="text-truncate"><span id="ServerName"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerName"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-roate-settings me-1"></i>MySQL Server Version</td>
                                    <td class="text-truncate"><span id="ServerVersion"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerVersion"></span></td>
                                    
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-ip-address me-1"></i>MySQL Server FQDN
                                    </td>
                                    <td class="text-truncate"><span id="ServerFQDN"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerFQDN"></span></td>
                                 

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-database-success me-1"></i>MySQL Server Resource Group Name
                                    </td>
                                    <td class="text-truncate"><span id="ServerResourceGroupName"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerResourceGroupName"></span></td>
                                 

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-ip-address me-1"></i>MySQL Server Location (Region)
                                    </td>
                                    <td class="text-truncate"><span id="ServerLocation"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerLocation"></span></td>
                                    

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-database-success me-1"></i>MySQL Server Status
                                    </td>
                                    <td class="text-truncate"><span id="ServerStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_ServerStatus"></span></td>
                                   

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>MySQL Replication Role
                                    </td>
                                    <td class="text-truncate"><span id="ReplicationRole"></span></td>
                                    <td class="text-truncate"><span id="DR_ReplicationRole"></span></td>
                                   
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>DataLag (sec)
                                    </td>
                                    <td class="text-truncate"><span id="Datalag"></span></td>
                                    <td class="text-truncate"><span id="DR_Datalag"></span></td>
                                    
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="w-100 h-100"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 d-grid">
            <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                        <thead class="align-middle">
                            <tr>
                                <th rowspan="2">Service / Process / Workflow Name</th>
                                <th colspan="2" class="text-center">Server IP/HostName</th>
                            </tr>
                            <tr>
                                <th id="prIp"></th>
                                <th id="drIp"></th>
                            </tr>
                        </thead>
                        <tbody id="mssqlserverbody">
                        </tbody>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringMySqlAzure.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>
