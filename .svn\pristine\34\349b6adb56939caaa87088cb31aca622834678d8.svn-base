﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.Features.Template.Commands.Create;

public class CreateTemplateCommandHandler : IRequestHandler<CreateTemplateCommand, CreateTemplateResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ITemplateHistoryRepository _templateHistoryRepository;
    private readonly ITemplateRepository _templateRepository;
    private readonly IVersionManager _versionManager;

    public CreateTemplateCommandHandler(IMapper mapper, ITemplateRepository templateRepository,
        ILoggedInUserService loggedInUserService, IVersionManager versionManager,
        ITemplateHistoryRepository templateHistoryRepository)
    {
        _mapper = mapper;
        _templateRepository = templateRepository;
        _loggedInUserService = loggedInUserService;
        _versionManager = versionManager;
        _templateHistoryRepository = templateHistoryRepository;
    }

    public async Task<CreateTemplateResponse> Handle(CreateTemplateCommand request, CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var template = _mapper.Map<Domain.Entities.Template>(request);

        var version = await _versionManager.GetVersion(request.Version);

        template.Version = version;

        await _templateRepository.AddAsync(template);

        var templateHistory = new Domain.Entities.TemplateHistory
        {
            LoginName = _loggedInUserService.LoginName,
            CompanyId = _loggedInUserService.CompanyId,
            TemplateId = template.ReferenceId,
            TemplateName = template.Name,
            Version = template.Version,
            Properties = template.Properties,
            UpdaterId = _loggedInUserService.UserId,
            Comments = request.Comments
        };
        await _templateHistoryRepository.AddAsync(templateHistory);

        var response = new CreateTemplateResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Template), template.Name),

            TemplateId = template.ReferenceId
        };

        return response;
    }
}