﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;

public class
    UpdateHeatMapStatusCommandHandler : IRequestHandler<UpdateHeatMapStatusCommand, UpdateHeatMapStatusResponse>
{
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IMapper _mapper;

    public UpdateHeatMapStatusCommandHandler(IMapper mapper, IHeatMapStatusRepository heatMapStatusRepository)
    {
        _mapper = mapper;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task<UpdateHeatMapStatusResponse> Handle(UpdateHeatMapStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate =
            await _heatMapStatusRepository.GetHeatMapDetailByInfraObjectAndEntityId(request.InfraObjectId,
                request.EntityId);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateHeatMapStatusCommand), typeof(Domain.Entities.HeatMapStatus));

        await _heatMapStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateHeatMapStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.HeatMapStatus), eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}