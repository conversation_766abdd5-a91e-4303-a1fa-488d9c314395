using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Commands.Delete;
using ContinuityPatrol.Application.Features.Archive.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ArchiveFixture : IDisposable
{
    public List<Archive> Archives { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateArchiveCommand CreateArchiveCommand { get; set; }
    public UpdateArchiveCommand UpdateArchiveCommand { get; set; }
    public DeleteArchiveCommand DeleteArchiveCommand { get; set; }
    public IMapper Mapper { get; set; }

    public ArchiveFixture()
    {
        Archives = new List<Archive>
        {
            new Archive
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                TableNameProperties = "{\"tables\":[\"Users\",\"Orders\",\"Products\"],\"retention\":\"365\"}",
                ArchiveProfileName = "TestArchiveProfile",
                Count = 1000,
                CronExpression = "0 0 2 * * ?",
                ScheduleTime = "02:00",
                ScheduleType = 1, // Daily
                BackUpType = "Full",
                Type = "Database",
                ClearBackup = "{\"enabled\":\"true\",\"retention\":\"30\"}",
                NodeId = Guid.NewGuid().ToString(),
                NodeName = "ArchiveNode01",
                IsActive = true
            }
        };

        Archives = AutoArchiveFixture.Create<List<Archive>>();
        UserActivities = AutoArchiveFixture.Create<List<UserActivity>>();
        CreateArchiveCommand = AutoArchiveFixture.Create<CreateArchiveCommand>();
        UpdateArchiveCommand = AutoArchiveFixture.Create<UpdateArchiveCommand>();
        DeleteArchiveCommand = AutoArchiveFixture.Create<DeleteArchiveCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ArchiveProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoArchiveFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateArchiveCommand>(p => p.ArchiveProfileName, 100));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.TableNameProperties, "{\"tables\":[\"Users\",\"Orders\"],\"retention\":\"365\"}"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.ArchiveProfileName, "TestArchiveProfile"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.Count, 1000));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.CronExpression, "0 0 2 * * ?"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.ScheduleTime, "02:00"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.ScheduleType, 1));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.BackUpType, "Full"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.Type, "Database"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.ClearBackup, "{\"enabled\":\"true\",\"retention\":\"30\"}"));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<CreateArchiveCommand>(c => c.With(a => a.NodeName, "ArchiveNode01"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateArchiveCommand>(p => p.ArchiveProfileName, 100));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.TableNameProperties, "{\"tables\":[\"UpdatedUsers\",\"UpdatedOrders\"],\"retention\":\"180\"}"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.ArchiveProfileName, "UpdatedArchiveProfile"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.Count, 2000));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.CronExpression, "0 0 3 * * ?"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.ScheduleTime, "03:00"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.ScheduleType, 2));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.BackUpType, "Incremental"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.Type, "File"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.ClearBackup, "{\"enabled\":\"false\",\"retention\":\"60\"}"));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateArchiveCommand>(c => c.With(a => a.NodeName, "UpdatedArchiveNode"));

            fixture.Customize<DeleteArchiveCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));

            fixture.Customize<Archive>(c => c.With(a => a.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<Archive>(c => c.With(a => a.IsActive, true));
            fixture.Customize<Archive>(c => c.With(a => a.CompanyId, Guid.NewGuid().ToString()));
            fixture.Customize<Archive>(c => c.With(a => a.TableNameProperties, "{\"tables\":[\"Users\",\"Orders\",\"Products\"],\"retention\":\"365\"}"));
            fixture.Customize<Archive>(c => c.With(a => a.ArchiveProfileName, "TestArchiveProfile"));
            fixture.Customize<Archive>(c => c.With(a => a.Count, 1000));
            fixture.Customize<Archive>(c => c.With(a => a.CronExpression, "0 0 2 * * ?"));
            fixture.Customize<Archive>(c => c.With(a => a.ScheduleTime, "02:00"));
            fixture.Customize<Archive>(c => c.With(a => a.ScheduleType, 1));
            fixture.Customize<Archive>(c => c.With(a => a.BackUpType, "Full"));
            fixture.Customize<Archive>(c => c.With(a => a.Type, "Database"));
            fixture.Customize<Archive>(c => c.With(a => a.ClearBackup, "{\"enabled\":\"true\",\"retention\":\"30\"}"));
            fixture.Customize<Archive>(c => c.With(a => a.NodeId, Guid.NewGuid().ToString()));
            fixture.Customize<Archive>(c => c.With(a => a.NodeName, "ArchiveNode01"));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
