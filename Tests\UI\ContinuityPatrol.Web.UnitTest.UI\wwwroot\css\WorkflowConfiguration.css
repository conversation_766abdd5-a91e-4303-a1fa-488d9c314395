﻿.primary-suggestion {
    width: fit-content;
}

.primary-suggestion-bg {
    width: 0px;
    height: 0px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #0d6efd;
    position: absolute;
    right: 18px;
    top: -6px;
}

.workflowTableTooltip {
    position: absolute;
    width: 19rem;
    word-wrap: break-word !important;
    word-break: break-word !important;
    /*    background-color: ghostwhite;
    padding: 2px;
    border: 1px solid black;
    min-width: 10rem;
    */
    /* Adjust styles as needed */
}

    .workflowTableTooltip tr td:first-child {
        width: 7rem;
    }

.cyberAirgapTooltip {
    position: absolute;
    width: 19rem;
    word-wrap: break-word !important;
    word-break: break-word !important;
    /*    background-color: ghostwhite;
    padding: 2px;
    border: 1px solid black;
    min-width: 10rem;
    */
    /* Adjust styles as needed */
}

    .cyberAirgapTooltip tr td:first-child {
        width: 7rem;
    }

.primary-suggestion-circle {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 50%;
    margin: 2px 4px;
}


#workflowActionCreate .steps {
    pointer-events: none;
}

.Workflow-Tree {
    height: calc(100vh - 156px);
    overflow-y: auto;
}

.Workflow-Digram-Accordion .accordion-button {
    padding: 9px 4px;
    border-radius: 3rem !important;
    font-size: var(--bs-font-family-Light);
}

    .Workflow-Digram-Accordion .accordion-button i {
        /*        padding: 5px;
        border-radius: 0.3rem 0rem 0rem 0.3rem;*/
        padding: 5px;
        border-radius: 1rem;
        color: #fff;
    }

.workflow_toolbar .list-group-item {
    padding: 5px 10px;
    border-width: 0;
    border: none;
}

.collapse-tools {
    position: absolute;
    right: 15px;
    width: 16rem !important;
    border: none;
}

.Category_Icon i {
    cursor: pointer;
    font-size: var(--bs-icon-font-size);
}

.workflow_body_scroll {
    height: calc(100vh - 200px);
    overflow-y: auto;
    background-image: radial-gradient(#ebebeb 1px, #fff 1px) !important;
    background-size: 15px 15px;
    border-radius: var(--bs-card-border-radius);
}

.next-disabled {
    pointer-events: none;
    cursor: not-allowed;
    opacity: 0.5;
}

.ActionBuilder_body_scroll {
    height: calc(100vh - 141px);
    overflow-y: auto;
    /*    background-image: radial-gradient(#e0e0e0 1px, #fff 1px) !important;
    background-size: 20px 20px;*/
    border-radius: var(--bs-card-border-radius);
}


.Workflow-Digram-Accordion .accordion-item {
    border: 0px;
    background-color: transparent;
}

.Workflow-Digram-Accordion .accordion-button::after {
    content: "\eaa1" !important;
    font-family: 'cp-icon' !important;
    background-image: none;
    font-size: 16px;
    --bs-accordion-btn-icon-width: auto !important;
    line-height: 0;
    margin-right: 4px;
    display: none;
}

.Workflow-Digram-Accordion .accordion-button:focus {
    border-color: transparent;
    box-shadow: none;
}

.Workflow-Execution .btn-outline-secondary:hover {
    background-color: var(--bs-primary-bg-subtle);
    color: var(--bs-primary);
}

.accordion-button:not(.collapsed) {
    background-color: transparent;
    box-shadow: none;
}

.highlight {
    padding: 2px;
    background-color: #ffff00;
}

.scroll-highlight {
    padding: 2px;
    background-color: #ff9632;
}
/*Workflow Execution Style*/
.Workflow-Execution {
    height: calc(100vh - 141px);
    overflow-y: auto;
}

    .Workflow-Execution .accordion-button {
        padding: 3px 6px;
        font-size: var(--bs-nav-menu-font-size);
        font-weight: var(--bs-menu-font-weight)
    }

        .Workflow-Execution .accordion-button::after {
            margin-left: 0;
        }

    .Workflow-Execution .accordion-body {
        padding: 0px;
    }

.Profile-Select .Success_Running {
    background-color: var(--bs-primary);
    padding: 5px;
    border-radius: 50%;
    color: #fff;
}

.Profile-Select .Skipped_Paused {
    background-color: var(--bs-gray-300);
    padding: 5px;
    border-radius: 50%;
    color: var(--bs-dark);
}
/*End Workflow Execution Style*/

/*Template Store Style */

.filter-item .filter-card {
    float: left;
}

    .filter-item .filter-card.active {
        transition: all 0.5s ease;
    }

    .filter-item .filter-card.delete {
        transition: all 0.5s ease;
        display: none;
    }

        .filter-item .filter-card.delete .card {
            display: none;
        }


.star-rating {
    direction: rtl;
    display: inline-block;
    cursor: default;
}

.star-input {
    display: none;
}

.star {
    color: #bbb;
    cursor: pointer;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

    .star:hover,
    .star:hover ~ .star,
    .star-input:checked ~ .star {
        color: #f2b600;
    }

/*End Template Store Style */

.select_actions {
    width: 100%
}

/*#workflowContainer {
    position: relative;
}*/

.workflowCanvas {
    position: absolute;
    pointer-events: none;
    height: 100%;
    width: 100%;
    left: 0;
}

.cyberMappingCanvas {
    position: absolute;
    pointer-events: auto;
    height: 100%;
    width: 100%;
    left: 0;
}

.draggablePoint {
    height: 10px;
    width: 10px;
    background-color: black;
    border-radius: 50%;
    display: inline-block;
    margin-top: 29px;
}


/*.svgContainer {
    position: absolute;
    z-index: 1;
    pointer-events: none;
    left: 0;
    width: 100%;
    height: 100%;
}*/
#workflowActions {
    position: relative;
}

.workflowActions {
    /*
    border: 1px solid #707e87;
    padding: 3px 4px;
    margin: 0px 3px;
    width: 15rem;
    align-items: center;
    height: 28px;
    text-align: left;
    display: flex;
    border-radius: 0.3rem;
    */
    background-color: var(--bs-white);
    border: 1px solid var(--bs-gray-300);
    padding: 3px 4px;
    /*margin: 3px 3px;*/
    width: 20rem;
    align-items: center;
    height: 30px;
    text-align: left;
    display: flex;
    border-radius: 3rem;
    box-shadow: 0 .125rem .5rem rgba(0,0,0,.075) !important;
    font-size: var(--bs-body-font-size-small);
}

.AISuggestionActions {
    background-color: var(--bs-white);
    border: 1px solid var(--bs-gray-300);
    padding: 3px 4px;
    /*margin: 3px 3px;*/
    width: 12rem;
    align-items: center;
    height: 30px;
    text-align: left;
    display: flex;
    border-radius: 3rem;
    box-shadow: 0 .125rem .5rem rgba(0,0,0,.075) !important;
    font-size: var(--bs-body-font-size-small);
}



.workflowIndex {
    /*    padding-right: 1px;*/
    width: 22px;
}

.parentGroup {
    width: 25rem;
}


.groupClass {
    border: 1px solid var(--bs-gray-300);
    width: 100%;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0,0,0,.075) !important;
    background-color: var(--bs-white);
}

.contextMenu {
    display: none;
    position: absolute;
    /*  width: 150px;
    height: auto;
    background: #fff;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19);
    z-index: 999;
    font-family: 'Roboto', sans-serif;*/
}

.UlContextBtn {
    margin: 0;
    padding: 0;
}



.conditionalDot {
    height: 4px;
    width: 4px; 
    border-radius: 50%;   
}

.contextBtn {
    list-style: none;
    font-size: 14px;
}

    .contextBtn:hover {
        background: #f4f4f4;
    }

.selectedWfContainer, .selected {
    /* background-color: cornflowerblue;*/
    border: 1px dashed var(--bs-primary) !important;
    box-shadow: 0 .125rem 0.5rem var(--bs-focus-ring-color) !important;
    margin-left: -1px;
}

.selected-error {
    /* background-color: cornflowerblue;*/
    border: 1px dashed var(--bs-danger) !important;
    box-shadow: 0 .125rem 0.5rem var(--bs-focus-ring-color) !important;
}

#parentAction .ui-sortable-handle {
    width: 92%;
    line-height: 0;
}

/*.ui-sortable-handle{
    width : 48rem;
    line-height:0;
}*/

span.actionSpan {
    line-height: 1;
}

.parallelCont {
    padding: 12px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border: 1px solid var(--bs-gray-300);
    position: relative;
    border-radius: 0.5rem;
    background-color: var(--bs-white);
}


.parallel-icon::after {
    content: "\eb2a";
    font-family: 'cp-icon' !important;
    color: var(--bs-white);
    position: absolute;
    right: -14px;
    top: 14px;
    font-size: var(--bs-input-icon-font-size);
    background-color: var(--bs-primary);
    width: 24px;
    height: 24px;
    border-radius: 1rem;
    line-height: 1.7;
}

.btnGroup {
    /*    height: 2.5rem;*/
}

.actionCheckBox {
    margin-right: 5px;
    width: 11px;
}


.template_store_switch_back_bg {
    background-color: #f5a00d;
}

.template_store_switch_over_bg {
    background-color: #59574c;
}

.template_store_fail_back_bg {
    background-color: #034159;
}

.template_store_fail_over_bg {
    background-color: #ff643b;
}

.template_store_custom_bg {
    background-color: #4551bf;
}

.template_store_drready_bg {
    background-color: var(--bs-success);
}

.template_store_monitoring_bg {
    background-color: #8c3232;
}

.template_store_switch_backroll_bg {
    background-color: #0099CC;
}

.Workflow-Execution .btn-outline-primary {
    background-color: transparent;
    color: #1a1a1a;
}

    .Workflow-Execution .btn-outline-primary:hover {
        background-color: var(--bs-primary-bg-subtle);
        color: var(--bs-primary);
    }

.Workflow-Execution .active {
    background-color: var(--bs-primary-bg-subtle) !important;
    color: var(--bs-primary) !important;
}

.accordion-after-icon-none a {
    text-decoration:none !important;
}

.accordion-after-icon-none::after{
    display:none;
}



/*Cyber Resiliency*/

/*.componentContainer{
    min-width : auto;
}*/

/*.zoneContainer{
    min-width : 330px;
}*/

#cyberContainer, #detailDiagramContainer {
    position: relative;
}

/*#cyberContainer{
    max-width: 70%;
}*/



.donut-chart {
    position: relative;
}

    .donut-chart svg {
        transform: rotate(-90deg);
    }

.circle-bg {
    fill: none;
    stroke: #d9d9d9;
    stroke-width: 10;
}

.circle-progress {
    fill: none;
    stroke: #3dced3;
    stroke-width: 10;
    stroke-linecap: round;
    stroke-dasharray: 314;
    stroke-dashoffset: calc(314 - (314 * var(--progress)) / 100);
    transition: stroke-dashoffset 0.6s ease;
}

.percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}