﻿using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IPostgresMonitorStatusService
{
    Task<List<PostgresMonitorStatusListVm>> GetPostgresMonitorStatusList();
    Task<BaseResponse> CreateAsync(CreatePostgresMonitorStatusCommand createPostgresMonitorStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdatePostgresMonitorStatusCommand updatePostgresMonitorStatusCommand);
    Task<PostgresMonitorStatusDetailVm> GetByReferenceId(string id);
    Task<List<PostgresMonitorStatusDetailByTypeVm>> GetPostgresMonitorStatusDetailByTypeVm(string type);
    //Task<List<OracleMonitorStatusByInfraObjectIdVm>> GetOracleMonitorStatusByInfraObjectIdVm(string infraObjectId);
    Task<PaginatedResult<PostgresMonitorStatusListVm>> GetPaginatedPostgresMonitorStatus(GetPostgresMonitorStatusPaginatedListQuery query);
}