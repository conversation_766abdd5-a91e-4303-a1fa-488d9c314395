using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataSetRepositoryTests : IClassFixture<DataSetFixture>
{
    private readonly DataSetFixture _dataSetFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataSetRepository _repository;
    private readonly DataSetRepository _repositoryNotParent;
    private readonly Mock<IConfiguration> _configMock;

    public DataSetRepositoryTests(DataSetFixture dataSetFixture)
    {
        _dataSetFixture = dataSetFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _configMock = ConfigurationRepositoryMocks.GetConnectionString();

        // Setup configuration mock
        //var mockSection = new Mock<IConfigurationSection>();
        //mockSection.Setup(x => x.Value).Returns("mysql");
        //_configMock.Setup(x => x.GetSection("ConnectionStrings").GetSection("DBProvider")).Returns(mockSection.Object);
        //_mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("Server=localhost;Database=test;");
        
        _repository = new DataSetRepository(_dbContext, _configMock.Object, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DataSetRepository(_dbContext, _configMock.Object, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;

        // Act
        await _dbContext.DataSets.AddAsync(dataSet);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSet.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSet.DataSetName, result.DataSetName);
        Assert.Equal(dataSet.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DataSets);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        await _dbContext.DataSets.AddAsync(dataSet);
        await _dbContext.SaveChangesAsync();

        dataSet.DataSetName = "UpdatedDataSetName";

        // Act
        _dbContext.DataSets.Update(dataSet);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dataSet.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedDataSetName", result.DataSetName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        await _dbContext.DataSets.AddAsync(dataSet);
        await _dbContext.SaveChangesAsync();

        // Act
        dataSet.IsActive = false;

        _dbContext.DataSets.Update(dataSet);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        var addedEntity = await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.DataSetName, result.DataSetName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataSet.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSet.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSet.DataSetName, result.DataSetName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsParent()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSets.Count, result.Count);
        Assert.All(result, x => Assert.Equal(DataSetFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsNotParent()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repositoryNotParent.AddRangeAsync(dataSets);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DataSetFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ShouldReturnQueryableOrderedByIdDescending()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        _repository.AddRangeAsync(dataSets).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Any());
        
        // Verify ordering by checking if the first item has a higher ID than the last
        var resultList = result.ToList();
        if (resultList.Count > 1)
        {
            Assert.True(resultList.First().Id >= resultList.Last().Id);
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnEmptyQueryable_WhenNoEntities()
    {
        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Any());
    }

    #endregion

    #region GetDataSetById Tests

    [Fact]
    public async Task GetDataSetById_ShouldReturnEntity_WhenExists_AndIsParent()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.GetDataSetById(dataSet.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSet.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSet.DataSetName, result.DataSetName);
    }

    [Fact]
    public async Task GetDataSetById_ShouldReturnEntity_WhenExists_AndIsNotParent()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        dataSet.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(dataSet);

        // Act
        var result = await _repositoryNotParent.GetDataSetById(dataSet.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSet.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetDataSetById_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetDataSetById("non-existent-id");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetDataSetNames Tests

    [Fact]
    public async Task GetDataSetNames_ShouldReturnDataSetNames()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.GetDataSetNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSets.Count, result.Count);
        Assert.All(result, x => Assert.NotNull(x.DataSetName));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetDataSetNames_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.GetDataSetNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsDataSetNameUnique Tests

    [Fact]
    public async Task IsDataSetNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        dataSet.DataSetName = "ExistingDataSetName";
        await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.IsDataSetNameUnique("ExistingDataSetName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDataSetNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.IsDataSetNameUnique("NonExistentDataSetName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsDataSetNameExist Tests

    [Fact]
    public async Task IsDataSetNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        dataSet.DataSetName = "ExistingDataSetName";
        await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.IsDataSetNameExist("ExistingDataSetName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsDataSetNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var dataSet = _dataSetFixture.DataSetDto;
        dataSet.DataSetName = "SameDataSetName";
        await _repository.AddAsync(dataSet);

        // Act
        var result = await _repository.IsDataSetNameExist("SameDataSetName", dataSet.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetDataSetByTableAccessId Tests

    [Fact]
    public async Task GetDataSetByTableAccessId_ShouldReturnEntitiesWithMatchingTableAccessId()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.GetDataSetByTableAccessId(DataSetFixture.TableAccessId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DataSetFixture.TableAccessId, x.TableAccessId));
    }

    [Fact]
    public async Task GetDataSetByTableAccessId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.GetDataSetByTableAccessId("non-existent-table-access-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;

        // Act
        var result = await _repository.AddRangeAsync(dataSets);

        // Assert
        Assert.Equal(dataSets.Count, result.Count());
        Assert.Equal(dataSets.Count, _dbContext.DataSets.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataSets = _dataSetFixture.DataSetList;
        await _repository.AddRangeAsync(dataSets);

        // Act
        var result = await _repository.RemoveRangeAsync(dataSets);

        // Assert
        Assert.Equal(dataSets.Count, result.Count());
        Assert.Empty(_dbContext.DataSets);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion
}
