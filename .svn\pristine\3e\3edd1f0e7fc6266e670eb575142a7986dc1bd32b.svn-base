﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowprofileByInfraobjectId;

public class GetWorkflowprofileByInfraobjectIdQueryHandler : IRequestHandler<GetWorkflowprofileByInfraobjectIdQuery,
    List<WorkflowProfileInfoNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowProfileRepository _workflowProfileRepository;

    public GetWorkflowprofileByInfraobjectIdQueryHandler(IWorkflowProfileRepository workflowProfileRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, IMapper mapper)
    {
        _workflowProfileRepository = workflowProfileRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _mapper = mapper;
    }

    public Task<List<WorkflowProfileInfoNameVm>> Handle(GetWorkflowprofileByInfraobjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowProfile = _workflowProfileInfoRepository.GetWorkflowProfileByInfraId(request.infraobjectId);

        var workflowProfileInfoName = _mapper.Map<List<WorkflowProfileInfoNameVm>>(workflowProfile);


        return Task.FromResult(workflowProfileInfoName);
    }
}