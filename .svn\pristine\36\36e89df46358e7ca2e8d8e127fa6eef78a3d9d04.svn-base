﻿namespace ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetByType;

public class GetSVCGMMonitorLogDetailByTypeQueryHandler : IRequestHandler<GetSVCGMMonitorLogDetailByTypeQuery,
    List<SVCGMMonitorLogDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCGMMonitorLogRepository _svcGMMonitorLogRepository;

    public GetSVCGMMonitorLogDetailByTypeQueryHandler(ISVCGMMonitorLogRepository svcGMMonitorLogRepository,
        IMapper mapper)
    {
        _svcGMMonitorLogRepository = svcGMMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<SVCGMMonitorLogDetailByTypeVm>> Handle(GetSVCGMMonitorLogDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var svcGMMonitorLogList = await _svcGMMonitorLogRepository.GetDetailByType(request.Type);

        return svcGMMonitorLogList.Count <= 0
            ? new List<SVCGMMonitorLogDetailByTypeVm>()
            : _mapper.Map<List<SVCGMMonitorLogDetailByTypeVm>>(svcGMMonitorLogList);
    }
}