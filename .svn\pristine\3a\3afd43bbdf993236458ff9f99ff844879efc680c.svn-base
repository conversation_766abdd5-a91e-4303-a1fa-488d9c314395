﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowOperation.Events.Create;

public class WorkflowOperationCreatedEventHandler : INotificationHandler<WorkflowOperationCreatedEvent>
{
    private readonly ILogger<WorkflowOperationCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowOperationCreatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowOperationCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowOperationCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{createdEvent.Description}' Created successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.WorkflowOperation}",
            Entity = Modules.WorkflowOperation.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"WorkflowOperation '{createdEvent.Description}' Created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}