﻿//using ContinuityPatrol.Configuration.Core.Contracts;
//using ContinuityPatrol.Configuration.Core.Features.Job.Commands.Update;
//using ContinuityPatrol.Configuration.Core.UnitTests.Fixtures;
//using ContinuityPatrol.Configuration.Core.UnitTests.Mocks;
//using ContinuityPatrol.Shared.Core.Exceptions;
//using ContinuityPatrol.Shared.Services.Contract;
//using Microsoft.Extensions.Configuration;

//namespace ContinuityPatrol.Configuration.Core.UnitTests.Domains.Job.Commands;


//public class UpdateJobTests : IClassFixture<JobFixture>
//{
//    private readonly Mock<IJobRepository> _mockJobRepository;

//    private readonly JobFixture _jobFixture;

//    private readonly UpdateJobCommandHandler _handler;

//    public UpdateJobTests(JobFixture jobFixture)
//    {
//        _jobFixture = jobFixture;

//        var config= new Mock<IConfiguration>();

//        var workflowOperationService = new Mock<IWorkflowOperationService>();

//        var logger = new Mock<ILogger<UpdateJobCommandHandler>>();

//        _mockJobRepository = JobRepositoryMocks.UpdateJobRepository(_jobFixture.Jobs);

//        _handler = new UpdateJobCommandHandler(_jobFixture.Mapper, _mockJobRepository.Object, config.Object, workflowOperationService.Object, logger.Object);
//    }

//    [Fact]
//    public async Task Handle_ValidJob_UpdateReferenceIdAsyncMethodToJobsRepo()
//    {
//        _jobFixture.UpdateJobCommand.Id = _jobFixture.Jobs[0].ReferenceId;

//        var result = await _handler.Handle(_jobFixture.UpdateJobCommand, CancellationToken.None);

//        var job = await _mockJobRepository.Object.GetByReferenceIdAsync(result.JobId);

//        Assert.Equal(_jobFixture.UpdateJobCommand.Name, job.Name);
//    }

//    [Fact]
//    public async Task Handle_Return_ValidJobResponse()
//    {
//        _jobFixture.UpdateJobCommand.Id = _jobFixture.Jobs[0].ReferenceId;

//        var result = await _handler.Handle(_jobFixture.UpdateJobCommand, CancellationToken.None);

//        result.ShouldBeOfType(typeof(UpdateJobResponse));

//        result.JobId.ShouldBeGreaterThan(0.ToString());

//        result.JobId.ShouldBe(_jobFixture.UpdateJobCommand.Id);

//        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_Throw_NotFoundException_When_InvalidJobId()
//    {
//        _jobFixture.UpdateJobCommand.Id = int.MaxValue.ToString();

//        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_jobFixture.UpdateJobCommand, CancellationToken.None));
//    }

//    [Fact]
//    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
//    {
//        _jobFixture.UpdateJobCommand.Id = _jobFixture.Jobs[0].ReferenceId;

//        await _handler.Handle(_jobFixture.UpdateJobCommand, CancellationToken.None);

//        _mockJobRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

//        _mockJobRepository.Verify(x => x.UpdateAsync(It.IsAny<Job>()), Times.Once);
//    }
//}
