﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetailByDatabaseId;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetType;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetTypeByDatabaseIdAndReplicationId;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class InfraReplicationMappingService : BaseService, IInfraReplicationMappingService
{
    public InfraReplicationMappingService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<InfraReplicationMappingListVm>> GetInfraReplicationMappingList()
    {
        Logger.LogDebug("Get InfraReplicationMapping List");

        return await Mediator.Send(new GetInfraReplicationMappingListQuery());
    }

    public async Task<InfraReplicationMappingDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraReplicationMapping Id");

        Logger.LogDebug($"Get InfraReplicationMapping Detail by Id '{id}'");

        return await Mediator.Send(new GetInfraReplicationMappingDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<InfraReplicationMappingListVm>> GetPaginatedInfraReplicationMapping(
        GetInfraReplicationMappingPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in InfraReplicationMapping Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<InfraReplicationMappingByDatabaseIdVm>> GetInfraReplicationMappingByDatabaseId(
        string databaseId, string replicationMasterId)
    {
        Guard.Against.InvalidGuidOrEmpty(databaseId, "Database Id");

        Logger.LogDebug($"Get InfraReplicationMapping Details by Id '{databaseId}', '{replicationMasterId}'");

        return await Mediator.Send(new InfraReplicationMappingByDatabaseIdQuery
            { DatabaseId = databaseId, ReplicationMasterId = replicationMasterId });
    }

    public async Task<BaseResponse> CreateAsync(
        CreateInfraReplicationMappingCommand createInfraReplicationMappingCommand)
    {
        Logger.LogDebug($"Create InfraReplicationMapping '{createInfraReplicationMappingCommand}'");

        return await Mediator.Send(createInfraReplicationMappingCommand);
    }

    public async Task<BaseResponse> UpdateAsync(
        UpdateInfraReplicationMappingCommand updateInfraReplicationMappingCommand)
    {
        Logger.LogDebug($"Update InfraReplicationMapping '{updateInfraReplicationMappingCommand}'");

        return await Mediator.Send(updateInfraReplicationMappingCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraReplicationMapping Id");

        Logger.LogDebug($"Delete InfraReplicationMapping Details by Id '{infraObjectId}'");

        return await Mediator.Send(new DeleteInfraReplicationMappingCommand { Id = infraObjectId });
    }

    public async Task<List<InfraReplicationMappingListVm>> GetInfraReplicationMappingByType(string type)
    {
        Logger.LogDebug($"Get InfraReplicationMapping Details by Type '{type}'");

        return await Mediator.Send(new InfraReplicationMappingTypeQuery { Type = type });
    }

    public async Task<List<InfraReplicationMappingListVm>> GetTypeByDatabaseIdAndReplicationMasterId(string? databaseId,
        string replicationMasterId, string type)
    {
        Logger.LogDebug(
            $"Get InfraReplicationMapping Details by Type '{databaseId}','{replicationMasterId}','{type}'");

        return await Mediator.Send(new GetTypeByDatabaseIdAndReplicationIdQuery
            { DatabaseId = databaseId, ReplicationMasterId = replicationMasterId, Type = type });
    }
}