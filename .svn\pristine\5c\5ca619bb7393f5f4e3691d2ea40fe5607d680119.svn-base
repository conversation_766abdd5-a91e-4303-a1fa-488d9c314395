﻿namespace ContinuityPatrol.Application.Features.Setting.Queries.GetNames;

public class GetSettingNameQueryHandler : IRequestHandler<GetSettingNameQuery, List<SettingNameVm>>
{
    private readonly IMapper _mapper;
    private readonly ISettingRepository _settingRepository;

    public GetSettingNameQueryHandler(ISettingRepository settingRepository, IMapper mapper)
    {
        _settingRepository = settingRepository;
        _mapper = mapper;
    }

    public async Task<List<SettingNameVm>> Handle(GetSettingNameQuery request, CancellationToken cancellationToken)
    {
        var settings = (await _settingRepository.ListAllAsync()).ToList();

        var settingDto = _mapper.Map<List<SettingNameVm>>(settings);

        return settingDto;
    }
}