﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class StorageDB2EMCSRDFSGControllerShould
    {
        [Fact]
        public void List_Returns_ViewResult()
        {
            
            var controller = new StorageDB2EMCSRDFSGController();

            
            var result = controller.List();

           
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }
    }
}
