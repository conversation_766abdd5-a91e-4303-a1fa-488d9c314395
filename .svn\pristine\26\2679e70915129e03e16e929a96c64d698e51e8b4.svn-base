﻿using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertId;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertName;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterIdExist;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterNameUnique;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Alert;

public class AlertMasterService : BaseService, IAlertMasterService
{
    public AlertMasterService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateAlertMasterCommand createAlertMasterCommand)
    {
        Logger.LogDebug($"Create Alert Master '{createAlertMasterCommand}'");

        return await Mediator.Send(createAlertMasterCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Alert Master Id");

        Logger.LogDebug($"Delete Alert Master Details by Id '{id}'");

        return await Mediator.Send(new DeleteAlertMasterCommand { AlertId = id });
    }

    public async Task<List<AlertMasterByAlertIdVm>> GetAlertMasterByAlertId(string alertId)
    {
        Logger.LogDebug($"Get AlertMaster Detail by AlertId '{alertId}'");

        return await Mediator.Send(new GetAlertMasterByAlertIdQuery { AlertId = alertId });
    }

    public async Task<List<AlertMasterByAlertNameVm>> GetAlertMasterByAlertName(string alertName)
    {
        Logger.LogDebug($"Get AlertMaster Detail by Id '{alertName}'");

        return await Mediator.Send(new GetAlertMasterByAlertNameQuery { AlertName = alertName });
    }

    public async Task<AlertMasterDetailVm> GetAlertMasterById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Alert Master Id");

        Logger.LogDebug($"Get Alert Master Detail by Id '{id}'");

        return await Mediator.Send(new GetAlertMasterDetailQuery { Id = id });
    }

    public async Task<List<AlertMasterListVm>> GetAlertMastersList()
    {
        Logger.LogDebug("Get All AlertMasters");

        return await Cache.GetOrAddAsync(
            ApplicationConstants.Cache.AllAlertMasterCacheKey + LoggedInUserService.CompanyId,
            () => Mediator.Send(new GetAlertMasterListQuery()));
    }

    public async Task<PaginatedResult<AlertMasterListVm>> GetPaginatedAlertMasters(
        GetAlertMasterPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Alert Master Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<bool> IsAlertMasterIdExist(string alertId)
    {
        Logger.LogDebug($"Check Alert Master Id Exists '{alertId}'");

        return await Mediator.Send(new GetAlertMasterIdExistQuery { AlertId = alertId });
    }

    public async Task<bool> IsAlertMasterNameExist(string alertName, string alertId)
    {
        Guard.Against.NullOrWhiteSpace(alertName, "Alert Master Name");

        Logger.LogDebug($"Check Name Exists Detail by Alert Master Name '{alertName}' and Id '{alertId}'");

        return await Mediator.Send(new GetAlertMasterNameUniqueQuery { AlertName = alertName, AlertId = alertId });

    }

    public async Task<BaseResponse> UpdateAsync(UpdateAlertMasterCommand updateAlertMasterCommand)
    {
        Logger.LogDebug($"Update Alert Master'{updateAlertMasterCommand}'");

        return await Mediator.Send(updateAlertMasterCommand);
    }
}