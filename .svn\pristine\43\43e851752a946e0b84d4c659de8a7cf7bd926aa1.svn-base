namespace ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;

public class
    GetCyberComponentDetailsQueryHandler : IRequestHandler<GetCyberComponentDetailQuery, CyberComponentDetailVm>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentDetailsQueryHandler(IMapper mapper, ICyberComponentRepository cyberComponentRepository)
    {
        _mapper = mapper;
        _cyberComponentRepository = cyberComponentRepository;
    }

    public async Task<CyberComponentDetailVm> Handle(GetCyberComponentDetailQuery request,
        CancellationToken cancellationToken)
    {
        var cyberComponent = await _cyberComponentRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(cyberComponent, nameof(Domain.Entities.CyberComponent),
            new NotFoundException(nameof(Domain.Entities.CyberComponent), request.Id));

        var cyberComponentDetailDto = _mapper.Map<CyberComponentDetailVm>(cyberComponent);

        return cyberComponentDetailDto;
    }
}