﻿function monitordebouncedebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}

async function solutiontypeData(val, editedGroupPolicyId = null, editedSolutionTypeId = null) {
    $("#selectInfraObjectName,#selectTemplateName").empty();

    if (val == 1) {
        await $.ajax({
            url: monitoringJobUrl.getGroupNodeList,
            type: 'GET',
            success: function (result) {

                if (result?.success) {
                    let data = result?.data;
                    let $selectGroupPolicy = $('#selectGroupPolicy');
                    $selectGroupPolicy.select2({ dropdownParent: $('#CreateModal') });
                    $selectGroupPolicy.empty();
                    let html = "";
                    html += '<option value=""></option>';

                    data?.forEach((x, i) => {
                        if (x.type.toLowerCase().replace(/\s+/g, '') === "monitorservice") {
                            html += `<option id="${x.id}" value="${x.id}">${x.groupName}</option>`
                        }
                    })
                    $selectGroupPolicy.append(html);

                    if (editedGroupPolicyId) {
                        $selectGroupPolicy.val(editedGroupPolicyId).trigger("change");
                    }
                } else {
                    errorNotification(result)
                }
            }
        });
    }

    await $.ajax({
        url: monitoringJobUrl.getSolutionTypeByPolicy,
        type: 'GET',
        data: { policy: val },
        success: function (result) {

            if (result?.success) {
                let data = result?.data
                const seen = new Set();
                const uniqueData = data.filter(item => {
                    if (seen.has(item.componentName)) {
                        return false;
                    }
                    seen.add(item.componentName);
                    return true;
                });
                let $selectSolutionType = $('#selectSolutionType');
                $selectSolutionType.select2({ dropdownParent: $('#CreateModal') });
                $selectSolutionType.empty();
                let html = "";
                html += '<option value=""></option>';

                uniqueData.forEach((x, i) => {
                    html += `<option id="${x.id}" value="${x.id}" ${(x.isConfigured && x.id !== solutionTypeID) ? 'disabled' : ''}>${x.componentName}</option>`;
                });
                $selectSolutionType.append(html);

                if (editedSolutionTypeId) {
                    $selectSolutionType.val(editedSolutionTypeId).trigger("change");
                }
            } else {
                errorNotification(result);
            }
        }
    });
    return true;
}

const getInfraObjectDetails = async (id, editedInfraobjectNames = null, editedTemplateName = null) => {
    $('#selectInfraObjectName,#selectTemplateName').val(null)

    if ($("#selectExecutionPolicy").val() != "") {
        $("#solutionTypeId").val(id);
        let data = {}
        data.replicationTypeId = id

        if ($("#selectExecutionPolicy").val() == "1") {
            await $.ajax({
                type: "GET",
                url: RootUrl + monitoringJobUrl.getInfraObjectListByReplicationTypeId,
                data: data,
                dataType: "json",
                traditional: true,
                success: function (result) {

                    if (result?.success) {
                        let $infraobjectNames = $('#selectInfraObjectName');
                        $infraobjectNames.select2({ dropdownParent: $('#CreateModal') });
                        $infraobjectNames.empty();
                        let html = ''
                        let data = result?.data;
                        html += '<option value=""></option>';

                        data?.forEach((item) => {
                            html += `<option id='${item.id}' value='${item.id}' ${item.isConfigured == true ? 'disabled' : ''}>${item.name}</option>`;
                        });
                        $infraobjectNames.append(html)

                        if (editedInfraobjectNames?.length) {
                            $infraobjectNames.val(editedInfraobjectNames).trigger('change')
                        }
                    } else {
                        errorNotification(result)
                    }
                }
            })
        }

        await $.ajax({
            type: "GET",
            url: RootUrl + monitoringJobUrl.getTemplateByReplicationTypeId,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {

                if (result?.success) {
                    let $templateName = $('#selectTemplateName');
                    $templateName.select2({ dropdownParent: $('#CreateModal') });
                    $templateName.empty();
                    let html = ''
                    let data = result?.data;
                    html += '<option value=""></option>';

                    data?.forEach((item) => {
                        if (item.actionType == "Monitoring") {
                            html += `<option id='${item.id}' value='${item.id}'>${item.name}</option>`;
                        }
                    });
                    $templateName.append(html);

                    if (editedTemplateName) {
                        $templateName.val(editedTemplateName).trigger("change")
                    }
                } else {
                    errorNotification(result)
                }
            }
        })
    }
}

async function populateModalFields(jobData) {
    $('#selectExecutionPolicy').val(jobData?.executionPolicy);
    solutionTypeID = jobData?.solutionTypeId;
    //$("#solutionTypeId").val(jobData?.solutionTypeId);
    // $("#solutionType").val(jobData?.solutionType);
    //$('#policyid').val(jobData?.groupPolicyId);

    let result = await solutiontypeData(jobData?.executionPolicy, jobData?.groupPolicyId, jobData?.solutionTypeId);

    if (result) {
        //$('#selectExecutionPolicy').val(jobData?.executionPolicy);
        //setTimeout(() => {
        //$('#selectSolutionType').val(jobData?.solutionTypeId).trigger("change")
        $("#solutionTypeId").val(jobData?.solutionTypeId);
        $("#solutionType").val(jobData?.solutionType);
        $('#policyid').val(jobData?.groupPolicyId)
        //$("#selectGroupPolicy").val(jobData?.groupPolicyId).trigger("change")
        //setTimeout(() => {

        //$('#selectTemplateName').val(jobData?.templateId).trigger("change")
        let Arr = [];
        if (jobData?.infraObjectProperties != null) {
            JSON.parse(jobData?.infraObjectProperties).forEach((d) => {
                Arr.push(d?.InfraObjectId)
            })
            //setTimeout(() => {
            $('#selectInfraObjectName').val(Arr).trigger('change')
            //}, 500)
        }
        getInfraObjectDetails(jobData?.solutionTypeId, Arr, jobData?.templateId)
        //}, 500)
        //}, 500)
    }
    $("#txtlastexecutetime").val(jobData?.lastExecutionTime)
    $("#selectInfraObjectName,#selectTemplateName").empty();
    $("#datetimeCronlist").val(jobData?.type)
    $('#textJobId').val(jobData?.id);
    $('#textJobName').val(jobData?.name);
    $('#textTemplateId').val(jobData?.templateId);
    $('#textNodeId').val(jobData?.nodeId);
    $('#textNodeName').val(jobData?.nodeName);

    if ([null, "Running", "Success"].includes(jobData?.status)) {
        $('#textStatus').val("Pending");
    } else {
        $('#textStatus').val(jobData?.status);
    }
    if (jobData?.executionPolicy == '1') {
        $("#InfraObject_Name").css("display", "block")
        $('#groupPolicyDiv').show();
        //$('#selectGroupPolicy').val(jobData?.groupPolicyName);
    }
    else {
        $('#groupPolicyDiv').hide();
        $("#InfraObject_Name").css("display", "none")
    }
    let scheduleTime = jobData?.scheduleTime.split(" ");

    setTimeout(() => {
        if (jobData?.scheduleTime?.includes("Every day")) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }
        if (jobData?.scheduleTime?.includes("MON-FRI")) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }
        if (scheduleTime?.length === 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }
        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            const days = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

            days.forEach((day, index) => {

                if (jobData?.scheduleTime.includes(day)) {
                    $(`#defaultCheck-${index}`).prop("checked", true);
                }
            });
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change");
        }
        if (scheduleTime?.length >= 12) {
            let year = parseInt(scheduleTime[12])
            let month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" :
                scheduleTime[8] == "APR" ? "04" : scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" :
                    scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                        scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            let newdate = year + "-" + month;
            $("#lblMonth").val(newdate).trigger("change");

            scheduleTime[5]?.split(",").forEach(function (i) {
                $("#inlineCheckbox" + i).prop("checked", !!i);
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)

    if (jobData?.state == "Active") {
        $("#textStateActive").prop("checked", true);
    }
    else {
        $("#textStateInactive").prop("checked", true);
    }
}

function srvTime() {
    try {

        //FF, Opera, Safari, Chrome
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {

        //IE
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {

                //AJAX not supported, use CPU time.
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}

const clearJobFields = () => {
    $("#textJobId,#textJobName,#textTemplateId,#selectExecutionPolicy,#textInfraObjectId,#textNodeId,#textNodeName,#textCronExpression,#textStatus,#textState,#selectGroupPolicy,#selectExecution,#textIsSchedule,#textScheduleType").val('');
    $("#selectInfraObjectName,#selectSolutionType,#selectTemplateName").val('').trigger("change").empty()
    $("#textStateInactive").prop("checked", false)
    $('#SaveFunction').text("Save");
    $("#SolutionType-error,#TemplateName-error").text("").removeClass('field-validation-error')
    ClearJobErrorElements();
    ClearCroneElements();
}

async function validateJobName(value, id = null, url) {
    if (!value) {
        $('#Name-error').text('Enter job name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        $('#Name-error').text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];
    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations?.length > 0) {
        $('#Name-error').text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        $('#Name-error').text('').removeClass('field-validation-error')
        return true;
    }
}

async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? " Job name already exists" : true;
}

async function SetGroupPolicy() {
    var url = RootUrl + monitoringJobUrl.getGroupPolicies;
    var data = {};
    var result = await GetAsync(url, data, OnError);

    for (var index = 0; index <= result.length; index++) {
        $('#selectGroupPolicy').append('<option value="' + result[index].groupName + '">' + result[index].groupName + '</option>');
    }
}

function ClearJobErrorElements() {
    $("#Name-error,#TemplateName-error,#SolutionType-error,#GroupPolicy-error,#state-error,#CronMin-error,#CronHourly-error,#CroneveryHour-error,#ExecutionPolicy-error,#CronddlMin-error,#CronddlHour-error,#CronDay-error, #CronExpression-error,#Crondaysevery-error,#InfraObjectName-error,#MonthlyHours-error,#CronMon-error,#CronMonthly-error,#CronExpression-error").text('').removeClass('field-validation-error');
}

function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours,#datetimeCron").val('');
    $('input[name="weekDays"], input[name="daysevery"], input[name="Monthyday"]').prop("checked", false);
    $('input[name="Monthyday"]').prop("disabled", false);
}

function CronValidation() {
    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    let Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    let monthlymonth = $('#lblMonth').val();
    let Minutes = $('#txtMins').val();
    let txtHours = $('#txtHours').val();
    let txtHourMinutes = $('#txtMinutes').val();
    let everyHours = $('#everyHours').val();
    let datetime = $('#datetimeCron').val();
    let MonthlyHours = $('#MonthlyHours').val();
    let isScheduler = '';

    if (document.getElementById('switchMonthly').checked == true) {
        $('#datetimeCron').val('');
        let Scheduler_types = $('.nav-tabs .active').text().trim();

        switch (Scheduler_types) {

            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;

            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;

            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;

            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day(s)", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;

            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select time", $('#MonthlyHours-error'));
                if (monthlymonth != "") {
                    isSchedulerDay = validateDayNumber(txtmonthday, "Select date(s)", $('#CronMon-error'));
                }
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        isScheduler = validateDayNumber(datetime, "Select schedule time", $('#CronExpression-error')) && validateprevNumber(datetime, "", $('#CronExpression-error'));
    }
    return isScheduler;
}

function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value?.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error')
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function ValidateRadioButton(errorElement) {
    if ($('input[name=state]:checked')?.length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}

function GetIsSchedule() {
    let schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#textIsSchedule').val(1);
    } else {
        $('#textIsSchedule').val(2);
    }
}

function jobOnce() {
    let Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    let elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    let elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
