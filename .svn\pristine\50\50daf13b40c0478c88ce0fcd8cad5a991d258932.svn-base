﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.TwoStepAuthentication.Commands.SendOtp;

public class SendOtpCommandHandler : IRequestHandler<SendOtpCommand, SendOtpResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IUserInfoRepository _userInfoRepository;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IEmailService _emailService;
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly ILogger<SendOtpCommandHandler> _logger;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _config;

    public SendOtpCommandHandler(ILoggedInUserService loggedInUserService, 
        IUserInfoRepository userInfoRepository, ISmtpConfigurationRepository smtpConfigurationRepository,
        IEmailService emailService, IGlobalSettingRepository globalSettingRepository, 
        ILogger<SendOtpCommandHandler> logger, IMemoryCache cache, IConfiguration config)
    {
        _loggedInUserService = loggedInUserService;
        _userInfoRepository = userInfoRepository;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _emailService = emailService;
        _globalSettingRepository = globalSettingRepository;
        _logger = logger;
        _cache = cache;
        _config = config;
    }

    public async Task<SendOtpResponse> Handle(SendOtpCommand request, CancellationToken cancellationToken)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Email Notification");

        if (globalSetting is null || globalSetting.GlobalSettingValue.Equals("false"))
        {
            _logger.LogWarning("The email notification feature is not enabled in the global settings.");

            return new SendOtpResponse
            {
                Success = false,
                Message = "The email notification feature is not enabled in the global settings."
            };
        }

        var version = _config.GetValue<string>("CP:Version");

        var otp = GenerateOtp(6);

        var body = EmailTemplateHelper.DeleteIsVerifyWorkflow(_loggedInUserService.LoginName, otp, version);

        var imageNames = new List<string>
        {
            "abstract.png",
            "confim_delete.png",
            "cp_logo.png",
            "username.png",
            "password.png"
        };

        var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, "Verify");

        var smtpConfigurations = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (smtpConfigurations is null)
            return new SendOtpResponse
            {
                Success = false,
                Message = "SMTP not Configure!."
            };

        var userInfo = await _userInfoRepository.GetUserInfoByUserIdAsync(_loggedInUserService.UserId);

        await _emailService.SendEmail(new EmailDto
        {
            From = smtpConfigurations.UserName,
            To = userInfo.Email,
            Subject = "Delete workflow Conformation!.",
            HtmlBody = htmlView,
            SmtpHost = smtpConfigurations.SmtpHost,
            Port = smtpConfigurations.Port,
            EnableSSL = smtpConfigurations!.EnableSSL,
            Password = smtpConfigurations.Password
        });


        var loginNameWithRole = $"{_loggedInUserService.LoginName}{_loggedInUserService.Role}";

        _cache.Set(loginNameWithRole, otp, TimeSpan.FromMinutes(2));

        return new SendOtpResponse
        {
            Success = true,
            Message = "Verification code email sent successfully!"
        };

    }
    public static string GenerateOtp(int length)
    {
        const string validCharacters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        var random = new Random();
        return new string(Enumerable.Repeat(validCharacters, length)
            .Select(s => s[random.Next(s.Length)])
            .ToArray());
    }
}