﻿using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using AutoMapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using MediatR;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ComponentTypeControllerShould
    {
        private readonly Mock<ILogger<ComponentTypeController>> _loggerMock =new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<IPublisher> _publisherMock = new();
        private ComponentTypeController _controller;

        public ComponentTypeControllerShould()
        {

            Initialize();

        }
        internal void Initialize()
        {
            _controller = new ComponentTypeController(
               _loggerMock.Object,
               _publisherMock.Object,
               _mapperMock.Object,
               _dataProviderMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_ComponentTypes()
        {

            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var componentTypes = new List<ComponentTypeViewModel>();
            
            
            var result = await _controller.List() as ViewResult;

            
            Assert.NotNull(result);
            
        }

        [Fact]
        public async Task ServerList_Returns_Filtered_ComponentTypes()
        {
            
            string serverName = "Server";
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);

            
            var result = await _controller.ServerList(serverName) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
            
        }

        [Fact]
        public async Task DatabaseList_Returns_Database_ComponentTypes()
        {
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.DatabaseList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ReplicationList_Returns_Replication_ComponentTypes()
        {
            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.ReplicationList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task SingleSignOnList_Returns_SingleSignOn_ComponentTypes()
        {


            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.SingleSignOnList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task ServerList_Returns_ServerList_Type_ComponentTypes()
        {

            _dataProviderMock.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(It.IsAny<List<ComponentTypeListVm>>);
            var result = await _controller.ServerList() as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":false", json);
        }
        
        [Fact]
        public async Task CreateOrUpdate_Creates_New_ComponentType()
        {
            
            var viewModel = new AutoFixture.Fixture().Create<ComponentTypeViewModel> ();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateComponentTypeCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_Existing_ComponentType()
        {
            
            var viewModel = new AutoFixture.Fixture().Create<ComponentTypeViewModel> ();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateComponentTypeCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.UpdateAsync(command))
                .ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

       
        [Fact]
        public async Task CreateOrUpdate_Handles_General_Exception()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<ComponentTypeViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateComponentTypeCommand();

            _mapperMock.Setup(m => m.Map<CreateComponentTypeCommand>(viewModel))
                .Returns(command);
            _dataProviderMock.Setup(dp => dp.ComponentType.CreateAsync(command))
                .ThrowsAsync(new Exception("General error"));

            
            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task Delete_Calls_Delete_Method()
        {
            
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(dp => dp.ComponentType.DeleteAsync(id))
                .ReturnsAsync(response);

            
            var result = await _controller.Delete(id) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            
        }

        [Fact]
        public async Task GetPagination_Returns_Paginated_List()
        {
            
            var query = new AutoFixture.Fixture().Create<GetComponentTypePaginatedListQuery>();
            _dataProviderMock.Setup(m => m.ComponentType.GetPaginatedComponentTypes(query)).ReturnsAsync(It.IsAny<PaginatedResult<ComponentTypeListVm>>);

            
            var result = await _controller.GetPagination(query) as JsonResult;
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task ComponentTypeNameExist_Returns_True_When_Exists()
        {
            // Arrange
            var name = "ExistingName";
            var id = "1";

            _dataProviderMock.Setup(dp => dp.ComponentType.IsComponentTypeExist(name, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.ComponentTypeNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ComponentTypeNameExist_Returns_False_When_Not_Exists()
        {
            // Arrange
            var name = "NonExistingName";
            var id = "1";

            _dataProviderMock.Setup(dp => dp.ComponentType.IsComponentTypeExist(name, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.ComponentTypeNameExist(name, id);

            // Assert
            Assert.False(result);
        }
    }
}
