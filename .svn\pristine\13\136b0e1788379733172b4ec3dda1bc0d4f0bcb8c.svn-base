﻿using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;

public class GetServerRoleTypeQueryHandler : IRequestHandler<GetServerRoleTypeQuery, List<ServerRoleTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public GetServerRoleTypeQueryHandler(IMapper mapper, IServerViewRepository serverViewRepository)
    {
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<ServerRoleTypeVm>> Handle(GetServerRoleTypeQuery request,
        CancellationToken cancellationToken)
    {
        var servers = await _serverViewRepository.GetRoleType(request.RoleTypeId);
        servers = (List<ServerView>)(request.RoleTypeId is not null && request.ServerTypeId is not null
            ? servers?.Where(x => x.ServerTypeId != null && x.ServerTypeId.Equals(request.ServerTypeId)).ToList()
            : request.RoleTypeId is not null
                ? servers
                : await _serverViewRepository.ListAllAsync());

        return servers.Count <= 0 ? new List<ServerRoleTypeVm>() : _mapper.Map<List<ServerRoleTypeVm>>(servers);
    }
}