﻿let selectedValues = [];
let createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();
let globalUserName = $('#userLoginName').text().replace(/ /g, '')
const workflowList = {
    getPagination: "/ITAutomation/WorkflowList/GetPagination",
    workflowConfiguration: '/ITAutomation/WorkflowConfiguration/List',
    getReport: "ITAutomation/WorkflowConfiguration/GetRunBookReport"
}
$(function () {
    function applyColResizable() {
        const $table = $('#WorkFlowList');
        $table.colResizable({ disable: true });
        $table.colResizable({
            liveDrag: true,
            fixed: false,
            partialRefresh: true,
            resizeMode: 'fit',
            headerOnly: false,
            minWidth: 50
        });
    }

    let dataTable = $('#WorkFlowList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        deferRender: true,
        processing: true,
        serverSide: false,
        filter: true,
        rowReorder: true,
        order: [],
        ajax: {
            type: "GET",
            url: workflowList.getPagination,
            dataType: "json",
            data(d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length ? selectedValues.join(';') : $('#wfListSearch').val();
                selectedValues.length = 0;
            },
            dataSrc(json) {
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json.data.length === 0);
                return json?.data;
            }
        },
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "orderable": false,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        return meta.row + 1;
                    }
                    return data;
                },
            },
            {
                data: "workflowName",
                name: "Workflow Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "businessServiceName",
                name: "Business Servic",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "profileName",
                name: "Profile Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "infraObjectName",
                name: "InfraObject Name",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "workflowType",
                name: "Type",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "lastExecutionDate",
                name: "LastExecution Date",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
                data: "createdName",
                name: "createdBy",
                autoWidth: true,
                render: function (data, type) {
                    const value = data || 'NA';
                    return type === 'display' ? `<span title="${value}">${value}</span>` : value;
                }
            },
            {
     data: null,
     name: "Action",
     autoWidth: true,
     orderable: false,
     render: function (data, type, row) {
         if (!row) return data;

         const isEditable = createPermission === 'true';
         const currentUser = $('#userLoginName').text().replace(/ /g, '');
         const createdName = row.createdName || '';
         const isCreator = (createdName.includes('\\') ? createdName.split('\\')[1] : createdName) === currentUser;
         const canView = row.isPublish || isCreator;

         const lockStatus = row.isLock? `<span><i class="cp-lock text-danger me-1 ${!isEditable ? 'icon-disabled' : ''}" title="Lock" data-Workflow-id="${row.workflowId}" data-workflowName="${row.workflowName}" data-Workflow-isLock="${row.isLock}"></i></span>`: '';
        /* const publishStatus = `<span><i class="${row.isPublish ? 'cp-publish text-warning me-1' : 'cp-non-publish text-info me-1'} ${!isEditable ? 'icon-disabled' : ''}" title="${row.isPublish ? 'Publish' : 'Unpublish'}" data-workflowName="${row.workflowName}" data-Workflow-id="${row.workflowId}" data-Workflow-isPublish="${row.isPublish}"></i></span>`;*/
         const reportButton = `<span role='button' class="downloadReportButton me-1" title="Report" data-workflowId='${row.id}' data-workflowName='${row.workflowName}'><i class="cp-custom-reports" type="button"></i></span>`;
         const viewButton = canView
             ? `<span role='button'><i class="cp-password-visible viewModeUnique text-primary" type="button" title="View" data-Workflow-id="${row.id}"></i></span>`
             : `<span role='button'><i class="cp-password-visible text-muted icon-disabled" type="button" title="View" style="pointer-events:none;"></i></span>`;

         return `<div class="d-flex align-items-center gap-2">${viewButton}${reportButton}${lockStatus}</div>`;
     }
 }
        ],
        rowCallback(row, data, index) {
            const startIndex = this.api().context[0]._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete() {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
            applyColResizable();
        },
        drawCallback() {
            applyColResizable();
        },
        columnDefs: [
            { orderable: false, targets: [-2, -1] },
            { targets: [0], width: "50px" },
            { targets: [1, 2, 3, 4, 5, 6], className: "truncate" }
        ]
    });

    //$('#WorkFlowList').colResizable({
    //    liveDrag: true,
    //    fixed: false,
    //    resizeMode: 'fit',
    //    partialRefresh: true,
    //    headerOnly: false,
    //    disableColumns: [0, 5]
    //});

    $('#wfListSearch').on('keyup input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        selectedValues = [];
        var inputValue = $('#wfListSearch').val();
        let searchArray = [{ key: 'name', value: 'workflow' }, { key: 'infraname', value: 'infraobject' }, { key: 'profilename', value: 'profile' },
        { key: 'bsName', value: 'businessservice' }, { key: 'bfName', value: 'businessfunction' }, { key: 'wfType', value: 'workflowtype' }]
        searchArray.forEach((x) => {
            if ($(`#${x.key}`).is(':checked')) {
                selectedValues.push(x.value + '=' + inputValue)
            }
        })
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })

    }, 200))
    $(document).on('click', '.viewModeUnique', function () {
        let id = $(this).data("workflowId");
        sessionStorage.setItem('WorkflowId', id)
        setTimeout(() => {
            window.location.assign(workflowList.workflowConfiguration)
        }, 200)
    })

    // Report Download
    $('#WorkFlowList').on('click', '.downloadReportButton', async function () {
        let workflowId = $(this).data("workflowid");
        let workflowName = $(this).data("workflowname");
        try {
            const url = `${RootUrl + workflowList.getReport}?WorkflowId=${workflowId}`;
            const method = 'POST';
            const fetchResponse = await fetch(url, {
                method: method,
                headers: {
                    'RequestVerificationToken': await gettoken(),
                },
            });
            if (fetchResponse?.ok) {
                const response = await fetchResponse?.blob();
                if (response?.type == "application/pdf") {
                    const DateTime = new Date().toLocaleString('en-US', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                        hour12: false,
                    })?.replace(/[^0-9]/g, '');
                    const formattedDateTime = DateTime?.replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1$2$3_$4$5$6');
                    await downloadFileXls(response, workflowName + "_RunBook_" + formattedDateTime + ".xls", "application/xls");
                    message = workflowName + " RunBook downloaded successfully";
                    notificationAlert("success", message);
                }
                else {
                    message = workflowName + " RunBook downloaded failed!";
                    notificationAlert("error", message);
                }
            } else {
                message = workflowName + " RunBook downloaded failed!";
                notificationAlert("error", message);
            }
        } catch (error) {
            message = workflowName + " RunBook downloaded failed!";
            notificationAlert("error", message);
        }
    });

    async function downloadFileXls(blob, fileName, contentType) {
        try {
            const link = document.createElement("a");
            link.download = fileName;
            link.href = window.URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error("Error downloading file: " + error?.message);
        }
    }
})