﻿using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;

namespace ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetList;

public class
    SQLDbMirroingStatusListQueryHandler : IRequestHandler<SQLDbMirroingStatusListQuery,
        List<MSSQLDBMirroingStatuslistVM>>
{
    private readonly IMsSqlDbMirroringStatusRepository _dbmirroringMonitorStatusRepository;
    private readonly IMapper _mapper;

    public SQLDbMirroingStatusListQueryHandler(IMapper mapper,
        IMsSqlDbMirroringStatusRepository dbmirroringMonitorStatusRepository)
    {
        _mapper = mapper;
        _dbmirroringMonitorStatusRepository = dbmirroringMonitorStatusRepository;
    }

    public async Task<List<MSSQLDBMirroingStatuslistVM>> Handle(SQLDbMirroingStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var dbmirroringMonitorStatus = await _dbmirroringMonitorStatusRepository.ListAllAsync();

        return dbmirroringMonitorStatus.Count <= 0
            ? new List<MSSQLDBMirroingStatuslistVM>()
            : _mapper.Map<List<MSSQLDBMirroingStatuslistVM>>(dbmirroringMonitorStatus);
    }
}