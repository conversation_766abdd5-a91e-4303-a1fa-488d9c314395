using ContinuityPatrol.Application.Features.Archive.Events.Update;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Archive.Commands.Update;

public class UpdateArchiveCommandHandler : IRequestHandler<UpdateArchiveCommand, UpdateArchiveResponse>
{
    private readonly IArchiveRepository _archiveRepository;
    private readonly IJobScheduler _client;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    
    public UpdateArchiveCommandHandler(IMapper mapper, IArchiveRepository archiveRepository, IPublisher publisher,
        ILoadBalancerRepository nodeConfigurationRepository, IJobScheduler client)
    {
        _mapper = mapper;
        _archiveRepository = archiveRepository;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _client = client;
        _publisher = publisher;
    }

    public async Task<UpdateArchiveResponse> Handle(UpdateArchiveCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _archiveRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Archive), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateArchiveCommand), typeof(Domain.Entities.Archive));

        await _archiveRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(new ArchiveUpdatedEvent { Name = eventToUpdate.ArchiveProfileName },
            cancellationToken);

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL",ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(), ServiceType.LoadBalancer.ToString());

        if (nodeConfig is not null)
        {
            var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

            var url = $"{baseUrl}/LoadBalancer/ArchiveLog/{eventToUpdate.ReferenceId}";

           await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });
        }

        var response = new UpdateArchiveResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Archive), eventToUpdate.ArchiveProfileName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}