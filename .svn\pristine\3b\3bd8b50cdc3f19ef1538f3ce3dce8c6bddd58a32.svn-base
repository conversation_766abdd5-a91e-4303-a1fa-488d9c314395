﻿using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class UserActivityService : BaseClient, IUserActivityService
{
    public UserActivityService(IConfiguration config, IAppCache cache, ILogger<UserActivityService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateUserActivityCommand createUserActivityCommand)
    {
        var request = new RestRequest("api/v6/useractivities");

        request.AddJsonBody(createUserActivityCommand);

        return await Post<BaseResponse>(request);
    }
    public async Task<List<UserActivityListVm>> GetUserActivityList()
    {
        var request = new RestRequest("api/v6/useractivities");

        return await Get<List<UserActivityListVm>>(request);
    }
    public async Task<List<UserActivityLoginNameVm>> GetUserActivityLoginName(string loginName)
    {
        var request = new RestRequest("api/v6/useractivityloginname");

        return await Get<List<UserActivityLoginNameVm>>(request);
    }

    public async Task<List<UserActivityListVm>> GetStartTimeEndTimeByUser(string? loginName, string startDate, string endDate)
    {
        var request = new RestRequest($"api/v6/useractivities/starttime-endtime?loginName={loginName}&createDate={startDate}&lastModifiedDate={endDate}");

        return await Get<List<UserActivityListVm>>(request);
    }
}