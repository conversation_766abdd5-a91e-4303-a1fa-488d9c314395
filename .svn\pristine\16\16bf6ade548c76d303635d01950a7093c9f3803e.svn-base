﻿using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.DashboardViewLog.Queries;

public class GetDashboardViewLogPaginatedListQueryHandlerTests : IClassFixture<DashboardViewLogFixture>
{
    private readonly DashboardViewLogFixture _dashboardViewLogFixture;

    private readonly Mock<IDashboardViewLogRepository> _dashboardViewLogRepositoryMock;

    private readonly GetDashboardViewLogPaginatedListQueryHandler _handler;

    public GetDashboardViewLogPaginatedListQueryHandlerTests(DashboardViewLogFixture dashboardViewLogFixture)
    {
        _dashboardViewLogFixture = dashboardViewLogFixture;

        _dashboardViewLogRepositoryMock = DashboardViewLogRepositoryMocks.GetPaginatedDashboardViewLogRepository(_dashboardViewLogFixture.DashboardViewLogs);

        _handler = new GetDashboardViewLogPaginatedListQueryHandler(_dashboardViewLogFixture.Mapper, _dashboardViewLogRepositoryMock.Object);

        _dashboardViewLogFixture.DashboardViewLogs[0].BusinessServiceName = "BS_Test_01";
        _dashboardViewLogFixture.DashboardViewLogs[0].BusinessFunctionName = "BF_Test_01";
        _dashboardViewLogFixture.DashboardViewLogs[0].InfraObjectName = "IO_Test_01";
        _dashboardViewLogFixture.DashboardViewLogs[0].MonitorType = "Oracle";
        _dashboardViewLogFixture.DashboardViewLogs[0].State = "Maintenance";

        _dashboardViewLogFixture.DashboardViewLogs[1].BusinessServiceName = "BS_Test_02";
        _dashboardViewLogFixture.DashboardViewLogs[1].BusinessFunctionName = "BF_Test_02";
        _dashboardViewLogFixture.DashboardViewLogs[1].InfraObjectName = "IO_Test_02";
        _dashboardViewLogFixture.DashboardViewLogs[1].MonitorType = "MSSQL";
        _dashboardViewLogFixture.DashboardViewLogs[1].State = "Active";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetDashboardViewLogPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DashboardViewLogListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_DashboardViewLogs_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetDashboardViewLogPaginatedListQuery { PageNumber = 1, PageSize = 10, 
            SearchString = "BusinessServiceName=BS_Test_01; businessFunctionName=BF_Test_01; infraObjectName=IO_Test_01; monitorType=Oracle; state=Maintenance"
        }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DashboardViewLogListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].BusinessServiceName.ShouldBe("BS_Test_01");
        result.Data[0].BusinessFunctionName.ShouldBe("BF_Test_01");
        result.Data[0].InfraObjectName.ShouldBe("IO_Test_01");
        result.Data[0].MonitorType.ShouldBe("Oracle");
        result.Data[0].State.ShouldBe("Maintenance");
    }

    [Fact]
    public async Task Handle_Return_PaginatedDashboardViewLogs_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetDashboardViewLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Orac" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DashboardViewLogListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<DashboardViewLogListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Id.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].ReferenceId);
        result.Data[0].BusinessServiceId.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].BusinessServiceId);
        result.Data[0].BusinessServiceName.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].BusinessServiceName);
        result.Data[0].BusinessFunctionId.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].BusinessFunctionId);
        result.Data[0].BusinessFunctionName.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].BusinessFunctionName);
        result.Data[0].InfraObjectId.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].InfraObjectId);
        result.Data[0].InfraObjectName.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].InfraObjectName);
        result.Data[0].CompanyId.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].CompanyId);
        result.Data[0].EntityId.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].EntityId);
        result.Data[0].MonitorType.ShouldBe("Oracle");
        result.Data[0].Type.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].Type);
        result.Data[0].DataLagValue.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].DataLagValue);
        result.Data[0].Status.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].Status);
        result.Data[0].Properties.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].Properties);
        result.Data[0].ReplicationStatus.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].ReplicationStatus);
        result.Data[0].DROperationStatus.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].DROperationStatus);
        result.Data[0].CurrentRTO.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].CurrentRTO);
        result.Data[0].State.ShouldBe(_dashboardViewLogFixture.DashboardViewLogs[0].State);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetDashboardViewLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<DashboardViewLogListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDashboardViewLogPaginatedListQuery(), CancellationToken.None);

        _dashboardViewLogRepositoryMock.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}