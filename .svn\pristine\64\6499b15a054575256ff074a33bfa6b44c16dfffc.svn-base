using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;

public class GetDynamicDashboardWidgetPaginatedListQueryHandler : IRequestHandler<
    GetDynamicDashboardWidgetPaginatedListQuery, PaginatedResult<DynamicDashboardWidgetListVm>>
{
    private readonly IDynamicDashboardWidgetRepository _dynamicDashboardWidgetRepository;
    private readonly IMapper _mapper;

    public GetDynamicDashboardWidgetPaginatedListQueryHandler(IMapper mapper,
        IDynamicDashboardWidgetRepository dynamicDashboardWidgetRepository)
    {
        _mapper = mapper;
        _dynamicDashboardWidgetRepository = dynamicDashboardWidgetRepository;
    }

    public async Task<PaginatedResult<DynamicDashboardWidgetListVm>> Handle(
        GetDynamicDashboardWidgetPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DynamicDashboardWidgetFilterSpecification(request.SearchString);

        var queryable = await _dynamicDashboardWidgetRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var dynamicDashboardWidgetList = _mapper.Map<PaginatedResult<DynamicDashboardWidgetListVm>>(queryable);

        return dynamicDashboardWidgetList;
        //var queryable = _dynamicDashboardWidgetRepository.GetPaginatedQuery();

        //var productFilterSpec = new DynamicDashboardWidgetFilterSpecification(request.SearchString);

        //var dynamicDashboardWidgetList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DynamicDashboardWidgetListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return dynamicDashboardWidgetList;
    }
}