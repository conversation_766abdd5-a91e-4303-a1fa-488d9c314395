using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixRequestFixture : IDisposable
{
    public List<ApprovalMatrixRequest> ApprovalMatrixRequestPaginationList { get; set; }
    public List<ApprovalMatrixRequest> ApprovalMatrixRequestList { get; set; }
    public ApprovalMatrixRequest ApprovalMatrixRequestDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixRequestFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixRequestList = fixture.Create<List<ApprovalMatrixRequest>>();

        ApprovalMatrixRequestPaginationList = fixture.CreateMany<ApprovalMatrixRequest>(20).ToList();

        ApprovalMatrixRequestPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixRequestPaginationList.ForEach(x => x.IsActive = true);
        ApprovalMatrixRequestPaginationList.ForEach(x => x.CreatedBy = "TEST_USER_ID");

        ApprovalMatrixRequestList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixRequestList.ForEach(x => x.IsActive = true);
        ApprovalMatrixRequestList.ForEach(x => x.CreatedBy = "TEST_USER_ID");

        ApprovalMatrixRequestDto = fixture.Create<ApprovalMatrixRequest>();
        ApprovalMatrixRequestDto.ReferenceId = Guid.NewGuid().ToString();
        ApprovalMatrixRequestDto.IsActive = true;
        ApprovalMatrixRequestDto.CreatedBy = "TEST_USER_ID";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
