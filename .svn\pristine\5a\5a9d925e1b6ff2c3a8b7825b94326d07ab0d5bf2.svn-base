﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class TeamMasterRepository : BaseRepository<TeamMaster>, ITeamMasterRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public TeamMasterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<TeamMaster>> GetAllTeamNames()
    {
        return _GetAllTeamNames();
    }

    Task<TeamMaster> ITeamMasterRepository.GetTeamNameById(string id)
    {
        return base.GetByReferenceIdAsync(id);
    }

    public async Task<TeamMaster> GetTeamNameByName(string name)
    {
        return await _dbContext.TeamMasters.Active().Where(job => job.GroupName.Equals(name)).FirstOrDefaultAsync();
    }


    /* public override Task<IReadOnlyList<TeamMaster>> ListAllAsync()
     {
         return _loggedInUserService.IsParent
             ? base.ListAllAsync()
             : FindByFilterAsync(teamMaster => teamMaster.Description.Equals(_loggedInUserService.CompanyId));
     }*/

    /*  public override Task<TeamMaster> GetByReferenceIdAsync(string id)   
      {
          return _loggedInUserService.IsParent
              ? base.GetByReferenceIdAsync(id)
              : Task.FromResult(FindByFilterAsync(teamMaster =>
                      teamMaster.ReferenceId.Equals(id) && teamMaster.Description.Equals(_loggedInUserService.CompanyId))
                  .Result.SingleOrDefault());
      }*/

    /* public override IQueryable<TeamMaster> GetPaginatedQuery()
     {
         if (_loggedInUserService.IsParent)
             return Entities.Where(x => x.IsActive)
                 .AsNoTracking()
                 .OrderByDescending(x => x.Id);
 
         return Entities.Where(x => x.IsActive && x.Description.Equals(_loggedInUserService.CompanyId))
             .AsNoTracking()
             .OrderByDescending(x => x.Id);
     }*/

    public Task<List<TeamMaster>> GetTeamMasterNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.TeamMasters.Active()
                .Where(x => x.Description.Equals(_loggedInUserService.CompanyId))
                .Select(x => new TeamMaster { ReferenceId = x.ReferenceId, GroupName = x.GroupName })
                .OrderBy(x => x.GroupName)
                .ToListAsync();
        return _dbContext.TeamMasters
            .Active()
            .Select(x => new TeamMaster { ReferenceId = x.ReferenceId, GroupName = x.GroupName })
            .OrderBy(x => x.GroupName)
            .ToListAsync();
    }

    public Task<bool> IsTeamMasterNameExist(string groupName, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.TeamMasters.Any(e => e.GroupName.Equals(groupName)))
            : Task.FromResult(_dbContext.TeamMasters.Where(e => e.GroupName.Equals(groupName)).ToList().Unique(id));
    }

    public Task<bool> IsTeamMasterNameUnique(string groupName)
    {
        var matches = _dbContext.TeamMasters.Any(e => e.GroupName.Equals(groupName));

        return Task.FromResult(matches);
    }

    public Task<bool> IsTeamMasterNameExist(string name)
    {
        var matches = _dbContext.TeamMasters.Any(e => e.GroupName.Equals(name));

        return Task.FromResult(matches);
    }

    private Task<List<TeamMaster>> _GetAllTeamNames()
    {
        return Entities
            .Active()
            .Select(x => new TeamMaster
                { ReferenceId = x.ReferenceId, GroupName = x.GroupName })
            .OrderBy(x => x.GroupName)
            .ToListAsync();
    }
}