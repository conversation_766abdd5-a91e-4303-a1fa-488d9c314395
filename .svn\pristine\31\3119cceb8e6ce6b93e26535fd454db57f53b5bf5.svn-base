﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowHistory.Queries;

public class GetWorkflowHistoryNameQueryHandlerTests : IClassFixture<WorkflowHistoryFixture>
{
    private readonly WorkflowHistoryFixture _workflowHistoryFixture;
    private Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;
    private readonly GetWorkflowHistoryNameQueryHandler _handler;

    public GetWorkflowHistoryNameQueryHandlerTests(WorkflowHistoryFixture workflowHistoryFixture)
    {
        _workflowHistoryFixture = workflowHistoryFixture;
        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.GetWorkflowHistoryNamesRepository(_workflowHistoryFixture.WorkflowHistories);
        _handler = new GetWorkflowHistoryNameQueryHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowHistory_Name()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowHistoryNameVm>>();

        result.Count.ShouldBe(_workflowHistoryFixture.WorkflowHistories.Count);
        result[0].Id.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].ReferenceId);
        result[0].WorkflowName.ShouldBe(_workflowHistoryFixture.WorkflowHistories[0].WorkflowName);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowHistoryNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowHistoryNameVm>>();

        result.Count.ShouldBe(_workflowHistoryFixture.WorkflowHistories.Count);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.GetWorkflowHistoryEmptyRepository();

        var handler = new GetWorkflowHistoryNameQueryHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object);

        var result = await handler.Handle(new GetWorkflowHistoryNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowHistoryNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowHistoryNameQuery(), CancellationToken.None);

        _mockWorkflowHistoryRepository.Verify(x => x.GetWorkflowHistoryNames(), Times.Once);
    }
}
