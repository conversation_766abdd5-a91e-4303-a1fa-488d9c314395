﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Delete;

public class
    DeleteWorkflowPredictionCommandHandler : IRequestHandler<DeleteWorkflowPredictionCommand,
        DeleteWorkflowPredictionResponse>
{
    private readonly IPublisher _publisher;
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;

    public DeleteWorkflowPredictionCommandHandler(IWorkflowPredictionRepository workflowPredictionRepository,
        IPublisher publisher)
    {
        _workflowPredictionRepository = workflowPredictionRepository;
        _publisher = publisher;
    }

    public async Task<DeleteWorkflowPredictionResponse> Handle(DeleteWorkflowPredictionCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _workflowPredictionRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.WorkflowPrediction),
            new NotFoundException(nameof(Domain.Entities.WorkflowPrediction), request.Id));

        eventToDelete.IsActive = false;

        await _workflowPredictionRepository.UpdateAsync(eventToDelete);

        var response = new DeleteWorkflowPredictionResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.WorkflowPrediction), eventToDelete.ActionId),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}