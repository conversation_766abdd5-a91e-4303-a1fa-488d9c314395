using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftImpactTypeMasterRepositoryTests
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftImpactTypeMasterRepository _repository;

    public DriftImpactTypeMasterRepositoryTests()
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftImpactTypeMasterRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftImpactTypeMaster = new DriftImpactTypeMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ImpactType = "TestImpactType",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(driftImpactTypeMaster);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftImpactTypeMaster.ImpactType, result.ImpactType);
        Assert.Single(_dbContext.DriftImpactTypeMasters);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftImpactTypeMaster = new DriftImpactTypeMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ImpactType = "ExistingImpactType",
            IsActive = true
        };
        await _repository.AddAsync(driftImpactTypeMaster);

        // Act
        var result = await _repository.IsNameExist("ExistingImpactType", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentImpactType", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftImpactTypeMaster = new DriftImpactTypeMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ImpactType = "SameImpactType",
            IsActive = true
        };
        await _repository.AddAsync(driftImpactTypeMaster);

        // Act
        var result = await _repository.IsNameExist("SameImpactType", driftImpactTypeMaster.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var impactTypes = new List<DriftImpactTypeMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType3", IsActive = false }
        };
        _dbContext.DriftImpactTypeMasters.AddRange(impactTypes);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftImpactTypeMaster = new DriftImpactTypeMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ImpactType = "TestImpactType",
            IsActive = true
        };
        await _repository.AddAsync(driftImpactTypeMaster);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftImpactTypeMaster.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftImpactTypeMaster.ReferenceId, result.ReferenceId);
        Assert.Equal(driftImpactTypeMaster.ImpactType, result.ImpactType);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var impactTypes = new List<DriftImpactTypeMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), ImpactType = "ImpactType3", IsActive = true }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(impactTypes);
        var initialCount = impactTypes.Count;
        
        var toUpdate = impactTypes.Take(2).ToList();
        toUpdate.ForEach(x => x.ImpactType = "UpdatedImpactType");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = impactTypes.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.ImpactType == "UpdatedImpactType").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
