using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetList;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class MenuBuildersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<MenuBuilderListVm>>> GetMenuBuilders()
    {
        Logger.LogDebug("Get All MenuBuilders");

        return Ok(await Mediator.Send(new GetMenuBuilderListQuery()));
    }

    [HttpGet("{id}", Name = "GetMenuBuilder")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<MenuBuilderDetailVm>> GetMenuBuilderById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MenuBuilder Id");

        Logger.LogDebug($"Get MenuBuilder Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetMenuBuilderDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<MenuBuilderListVm>>> GetPaginatedMenuBuilders([FromQuery] GetMenuBuilderPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in MenuBuilder Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateMenuBuilderResponse>> CreateMenuBuilder([FromBody] CreateMenuBuilderCommand createMenuBuilderCommand)
    {
        Logger.LogDebug($"Create MenuBuilder '{createMenuBuilderCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateMenuBuilder), await Mediator.Send(createMenuBuilderCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateMenuBuilderResponse>> UpdateMenuBuilder([FromBody] UpdateMenuBuilderCommand updateMenuBuilderCommand)
    {
        Logger.LogDebug($"Update MenuBuilder '{updateMenuBuilderCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateMenuBuilderCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteMenuBuilderResponse>> DeleteMenuBuilder(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MenuBuilder Id");

        Logger.LogDebug($"Delete MenuBuilder Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteMenuBuilderCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsMenuBuilderNameExist(string menuBuilderName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(menuBuilderName, "MenuBuilder Name");

     Logger.LogDebug($"Check Name Exists Detail by MenuBuilder Name '{menuBuilderName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetMenuBuilderNameUniqueQuery { Name = menuBuilderName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


