﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Queries;

public class GetServerTypeDetailQueryHandlerTests : IClassFixture<ServerTypeFixture>
{
    private readonly ServerTypeFixture _serverTypeFixture;

    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;

    private readonly GetServerTypeDetailQueryHandler _handler;

    public GetServerTypeDetailQueryHandlerTests(ServerTypeFixture serverTypeFixture)
    {
        _serverTypeFixture = serverTypeFixture;

        _mockServerTypeRepository = ServerTypeRepositoryMocks.GetServerTypeRepository(_serverTypeFixture.ServerTypes);

        _handler = new GetServerTypeDetailQueryHandler(_serverTypeFixture.Mapper, _mockServerTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_ServerTypeDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetServerTypeDetailQuery { Id = _serverTypeFixture.ServerTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<ServerTypeDetailVm>();

        result.Id.ShouldBe(_serverTypeFixture.ServerTypes[0].ReferenceId);

        result.Name.ShouldBe(_serverTypeFixture.ServerTypes[0].Name);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidServerTypeId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetServerTypeDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetServerTypeDetailQuery { Id = _serverTypeFixture.ServerTypes[0].ReferenceId }, CancellationToken.None);

        _mockServerTypeRepository.Verify(x => x.GetServerTypeById(It.IsAny<string>()), Times.Once);
    }

}
