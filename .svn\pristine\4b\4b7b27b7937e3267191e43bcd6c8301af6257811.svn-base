using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Create;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;

public class CreateApprovalMatrixRequestCommandHandler : IRequestHandler<CreateApprovalMatrixRequestCommand,
    CreateApprovalMatrixRequestResponse>
{
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateApprovalMatrixRequestCommandHandler(IMapper mapper,
        IApprovalMatrixRequestRepository approvalMatrixRequestRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
    }

    public async Task<CreateApprovalMatrixRequestResponse> Handle(CreateApprovalMatrixRequestCommand request,
        CancellationToken cancellationToken)
    {
        var approvalMatrixRequest = _mapper.Map<Domain.Entities.ApprovalMatrixRequest>(request);

        approvalMatrixRequest = await _approvalMatrixRequestRepository.AddAsync(approvalMatrixRequest);

        var response = new CreateApprovalMatrixRequestResponse
        {
            Message = Message.Create(nameof(Domain.Entities.ApprovalMatrixRequest), approvalMatrixRequest.ProcessName),

            Id = approvalMatrixRequest.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixRequestCreatedEvent { Name = approvalMatrixRequest.ProcessName },
            cancellationToken);

        return response;
    }
}