using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MYSQLMonitorLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MYSQLMonitor";

    public List<MYSQLMonitorLogs> MYSQLMonitorLogsPaginationList { get; set; }
    public List<MYSQLMonitorLogs> MYSQLMonitorLogsList { get; set; }
    public MYSQLMonitorLogs MYSQLMonitorLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MYSQLMonitorLogsFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MYSQLMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
  );

        MYSQLMonitorLogsPaginationList = _fixture.CreateMany<MYSQLMonitorLogs>(20).ToList();
        MYSQLMonitorLogsList = _fixture.CreateMany<MYSQLMonitorLogs>(5).ToList();
        MYSQLMonitorLogsDto = _fixture.Create<MYSQLMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MYSQLMonitorLogs CreateMYSQLMonitorLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MYSQLMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MYSQLMonitorLogs CreateMYSQLMonitorLogsWithWhitespace()
    {
        return CreateMYSQLMonitorLogsWithProperties(type: "  MYSQLMonitor  ");
    }

    public MYSQLMonitorLogs CreateMYSQLMonitorLogsWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMYSQLMonitorLogsWithProperties(type: longType);
    }

    public MYSQLMonitorLogs CreateMYSQLMonitorLogsWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMYSQLMonitorLogsWithProperties(infraObjectId: infraObjectId);
    }

    public List<MYSQLMonitorLogs> CreateMultipleMYSQLMonitorLogsWithSameType(string type, int count)
    {
        var logs = new List<MYSQLMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateMYSQLMonitorLogsWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public List<MYSQLMonitorLogs> CreateMYSQLMonitorLogsWithMixedActiveStatus(string type)
    {
        return new List<MYSQLMonitorLogs>
        {
            CreateMYSQLMonitorLogsWithProperties(type: type, isActive: true),
            CreateMYSQLMonitorLogsWithProperties(type: type, isActive: false),
            CreateMYSQLMonitorLogsWithProperties(type: type, isActive: true)
        };
    }

    public List<MYSQLMonitorLogs> CreateMYSQLMonitorLogsWithDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<MYSQLMonitorLogs>();
        var dateRange = (endDate - startDate).TotalDays;
        
        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            logs.Add(CreateMYSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: randomDate));
        }
        return logs;
    }

    public List<MYSQLMonitorLogs> CreateMYSQLMonitorLogsOutsideDateRange(string infraObjectId, DateTime startDate, DateTime endDate)
    {
        return new List<MYSQLMonitorLogs>
        {
            CreateMYSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: startDate.AddDays(-5)), // Before range
            CreateMYSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: endDate.AddDays(5)) // After range
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MYSQLMonitor", "MySQL", "MySQLServer", "DatabaseMonitor" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
