﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Events.Delete;

public class WorkflowPermissionDeletedEventHandler : INotificationHandler<WorkflowPermissionDeletedEvent>
{
    private readonly ILogger<WorkflowPermissionDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowPermissionDeletedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowPermissionDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowPermissionDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.WorkflowPermission}",
            Entity = Modules.WorkflowPermission.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"WorkflowPermission '{deletedEvent.AccessProperties}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowPermission '{deletedEvent.AccessProperties}' deleted successfully.");
    }
}