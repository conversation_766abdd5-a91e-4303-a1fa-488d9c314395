﻿@model ContinuityPatrol.Domain.ViewModels.VeritasClusterModel.VeritasClusterViewModel
@using ContinuityPatrol.Shared.Services.Helper

<div id="createModal" class="modal-dialog modal-dialog-centered">
	<form class="modal-content" enctype="multipart/form-data" autocomplete="off">
		<div class="modal-header">
			<h6 class="page_title"><i class="cp-web"></i><span>Site Configuration</span></h6>
			<button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
		</div>
		<div class="modal-body">
			<div class="row">
				<div class="col-12">
					<div class="form-group">
						<div class="form-label">Cluster Profile Name</div>
						<div class="input-group">
							<span class="input-group-text"><i class="cp-user-configuration"></i></span>
							<input maxlength="100" id="clusterProfileName" autocomplete="off" class="form-control" placeholder="Enter Cluster Profile Name" />
						</div>
						<span id="clusterProfileNameError"></span>
					</div>
				</div>
				<div class="col-12">
					<div class="form-group">
						<div class="form-label">Cluster Server</div>
						<div class="input-group">
							<span class="input-group-text"><i class="cp-fal-server"></i></span>
							<select id="clusterServerName" class="form-select-modal" data-live-search="true" data-placeholder="Select Cluster Server">
							</select>
						</div>
						<span id="clusterServerNameError"></span>
					</div>
				</div>
				<div class="col-12">
					<div class="form-group">
						<div class="form-label">Cluster Name</div>
						<div class="input-group">
							<span class="input-group-text"><i class="cp-auxiliary-cluster-name"></i></span>
							<input id="clusterName" class="form-control" autocomplete="off" placeholder="Enter Cluster Name" maxlength="100" />
						</div>
						<span id="clusterNameError"></span>
					</div>
				</div>
				<div class="col-12">
					<div class="form-group">
						<div class="form-label">Cluster Bin Path</div>
						<div class="input-group">
							<span class="input-group-text"><i class="cp-endpoint-port-number"></i></span>
							<input id="clusterBinPath" autocomplete="off" class="form-control" placeholder="Enter Cluster Bin Path" maxlength="200" />
						</div>
						<span id="clusterBinPathError"></span>
					</div>
				</div>
			</div>
		</div>
		<div class="modal-footer d-flex justify-content-between">
			<small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
			<div class="gap-2 d-flex">
				<button type="button" class="btn btn-secondary btn-sm" tabindex="-1" data-bs-dismiss="modal">Cancel</button>
				<button type="button" class="btn btn-primary btn-sm" tabindex="-1" id="veritasSaveBtn">Save</button>
			</div>
		</div>
	</form>
</div>
@section Scripts
{
	@{
		<partial name="_ValidationScriptsPartial" />
	}
}