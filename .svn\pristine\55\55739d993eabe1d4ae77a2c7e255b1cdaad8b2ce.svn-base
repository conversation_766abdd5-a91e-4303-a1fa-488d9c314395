﻿using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs
{
    public class GetInfraObjectSchedulerLogsReportQuery : IRequest<GetResiliencyReadinessSchedulerLogReportVm>
    {
        public string StartDate { get; set; }
        public string EndDate { get; set; }
    }
}
