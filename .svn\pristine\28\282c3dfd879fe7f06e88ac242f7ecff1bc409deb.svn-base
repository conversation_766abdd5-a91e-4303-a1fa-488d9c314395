﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Shared.Services.Helper
@model ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel.CyberJobManagementViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-job-management"></i><span>Cyber Resiliency Job Management</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Job Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="airgap=" id="airgapname">
                                        <label class="form-check-label" for="airgapname">
                                            Airgap Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="workflow=" id="workflowname">
                                        <label class="form-check-label" for="workflowname">
                                            Workflow Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="Activebtn" class="btn btn-transparent border border-success-subtle me-2 d-flex align-items-center text-primary-emphasis"
                        data-bs-toggle="modal"
                        data-bs-target="" style="background-color:#fafff8">
                    <i class="cp-active-inactive text-success" title="Active"></i> &nbsp; Active
                </button>
                <button type="button" id="Inactivebtn" class="btn btn-transparent border border-danger-subtle me-2 d-flex align-items-center" data-bs-toggle="collapse"
                        href="#collapseExample" data-clicked="false" role="button" aria-expanded="false"
                        aria-controls="collapseExample" style="background-color:#fff0f2">
                    <i class="cp-active-inactive text-danger" title="InActive"></i> &nbsp; Inactive
                </button>
                <button type="button" id="create" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table class="datatable table table-hover dataTable no-footer" id="tblJobManage" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>
                            <div class="">
                                <input name="checkboxAll" type="checkbox" id="flexCheckDefault"
                                       class="form-check-input custom-cursor-default-hover">
                            </div>
                        </th>
                        <th>Job Name</th>
                        <th>Airgap Name</th>
                        <th>Workflow Name</th>
                        <th>Scheduled Time</th>
                        <th>Last Executed Time</th>
                        <th>Status</th>
                        <th>State</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>


<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="ZoneModal" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-lg ">
        <form class="modal-content" asp-controller="JobManagement" id="CreateForm" asp-action="CreateOrUpdate" method="post">
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-job-management"></i><span>
                        Cyber Resiliency Job Management Configuration
                    </span>
                </h6>
                <button type="button" title="Close" class="btn-close monitorbtn-cancel" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">
                        Job Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input asp-for="Name" type="text" maxlength="100" class="form-control" id="jobName" placeholder="Enter Job Name" autocomplete="off" />
                    </div>
                    <input asp-for="Id" type="hidden" id="NameId" />
                    <span asp-validation-for="Name" id="Name-error"></span>
                </div>
                <div class="form-group">
                    <label class="form-label">
                        Airgap Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-air-gap"></i></span>

                        <select class="form-select-modal" asp-for="AirgapName" id="airgapName" data-placeholder="Enter Airgap Name">
                        </select>
                    </div>
                    <input asp-for="AirgapId" type="hidden" id="airgapId" />
                    <span asp-validation-for="AirgapName" id="AirgapName-error"></span>
                </div>
                <div class="form-group">
                    <label class="form-label">Workflow Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-workflow"></i></span>
                        <select class="form-select-modal" asp-for="WorkflowName" id="workflowName" data-placeholder="Select Workflow Name">
                        </select>

                    </div>
                    <input asp-for="WorkflowId" type="hidden" id="workflowId" />
                    <span asp-validation-for="WorkflowName" id="WorkflowName-error"></span>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="switches-container mb-3">
                            <input type="radio" id="switchMonthly" name="switchPlan" value="Cycle" checked="checked" />
                            <input type="radio" id="switchYearly" name="switchPlan" value="Once" />
                            <label for="switchMonthly" class="nav-link">Cycle</label>
                            <label for="switchYearly" class="nav-link">Once</label>
                            <div class="switch-wrapper">
                                <div class="switch">
                                    <div>Cycle</div>
                                    <div>Once</div>
                                </div>
                            </div>
                        </div>
                        <div class="month" id="yeargroup">
                            <div class="mb-3">
                                <div class="form-group ">
                                    <div class="form-label">Schedule Time</div>
                                    <div class="input-group">
                                        <span class="input-group-text">  <i class="cp-time"></i></span>

                                        <input id="datetimeCron" class="form-control datetimeCron" name="datetime_currentdate" placeholder="Schedule Time" type="datetime-local" />
                                    </div>
                                    <span asp-validation-for="CronExpression" id="CronExpression-error"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pe-0">
                        <div class="row mt-2 w-100 ">
                            <div class="year" id="monthgroup">
                                <div class="mb-3">
                                    <div class="form-label">Scheduler</div>
                                    <div>
                                        <nav>
                                            <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                                <button class="nav-link active" id="nav-Minutes-tab" data-bs-toggle="tab" name="Scheduler"
                                                        data-bs-target="#nav-Minutes" type="button" role="tab"
                                                        aria-controls="nav-Minutes" aria-selected="true">
                                                    Minutes
                                                </button>
                                                <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab" name="Scheduler"
                                                        data-bs-target="#nav-Hourly" type="button" role="tab"
                                                        aria-controls="nav-Hourly" aria-selected="false">
                                                    Hourly
                                                </button>
                                                <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="Scheduler"
                                                        data-bs-target="#nav-Daily" type="button" role="tab"
                                                        aria-controls="nav-Daily" aria-selected="false">
                                                    Daily
                                                </button>
                                                <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="Scheduler"
                                                        data-bs-target="#nav-Weekly" type="button" role="tab"
                                                        aria-controls="nav-Weekly" aria-selected="false">
                                                    Weekly
                                                </button>
                                                <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="Scheduler"
                                                        data-bs-target="#nav-Monthly" type="button" role="tab"
                                                        aria-controls="nav-Monthly" aria-selected="false">
                                                    Monthly
                                                </button>
                                            </div>
                                        </nav>
                                        <div class="tab-content" id="nav-tabContent">
                                            <div class="tab-pane fade show active" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                                <div class="row mt-2 align-items-end">
                                                    <div class="col-3">
                                                        <div class="form-group">
                                                            <div class="form-label">Every</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                                @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", maxlength = "2", min = "0", max = "59", pattern = "d{2}", @class = "form-control", @placeholder = "Enter Mins", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                <span class="input-group-text small">Mins</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-3 d-none">
                                                        <div class="form-group">
                                                            <div class="form-label"></div>
                                                            <div class="input-group">
                                                                <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                                <input type="number" class="form-control" placeholder="Select Minute" />
                                                                <span class="input-group-text small pe-0">Minute</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronMin-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                                <div class="row mt-2">
                                                    <div class="col-4">
                                                        <div class="form-group">
                                                            <div class="form-label">Every</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text">
                                                                    <i class="cp-calendar"></i>
                                                                </span>
                                                                @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                <span class="input-group-text fs-8 ms-1">
                                                                    hrs
                                                                </span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronHourly-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-xl-6">
                                                        <div class="form-group">
                                                            <div class="form-label">Minutes</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text">
                                                                    <i class="cp-calendar"></i>
                                                                </span>
                                                                @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                                <span class="input-group-text form-label mb-0 text-secondary">Mins</span>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronHourMin-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                                <div class="row mt-2 align-items-center">
                                                    <div class="col-4">
                                                        <div class="form-group flex-fill ">
                                                            <label class="animation-label form-label">
                                                                Select Day Type
                                                            </label>
                                                            <div class="">
                                                                <span class="input-group-text"></span>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                                    <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="MON-FRI">
                                                                    <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                                </div>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="Crondaysevery-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="form-group">
                                                            <div class="form-label">Starts at</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CroneveryHour-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                                <div class="row row-cols-2 mt-2">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label class="form-label custom-cursor-default-hover">Select Day</label>
                                                            <div class="bg-transparent input-group">
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="MON"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="TUE"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="WED"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="THU"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="FRI" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="SAT"><labelfor ="defaultCheck-6" class="form-check-label">Saturday</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="SUN"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                                </div>
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronDay-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="form-group">
                                                            <div class="form-label">Starts at</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                                @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronddlHour-error"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                                <div class="row row-cols-2 mt-2">
                                                    <div class="col-4">
                                                        <div class="mb-3 form-group">
                                                            <div class="form-label">Select Month And Year</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text">
                                                                    <i class="cp-calendar"></i>
                                                                </span>
                                                                <input name="month" autocomplete="off" type="month"
                                                                       id="lblMonth"
                                                                       class="form-control custom-cursor-default-hover"
                                                                       cursorshover="true" min="2025-02" max="2101-02" />
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="CronMonthly-error"></span>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 pe-0">
                                                        <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                            <div class="form-label mb-2">Select Date(s)</div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                                <label class="form-check-label checklabel">1</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                                <label class="form-check-label checklabel">2</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                                <label class="form-check-label checklabel">3</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                                <label class="form-check-label checklabel">4</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                                <label class="form-check-label checklabel">5</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                                <label class="form-check-label checklabel">6</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                                <label class="form-check-label checklabel">7</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                                <label class="form-check-label checklabel">8</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                                <label class="form-check-label checklabel">9</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                                <label class="form-check-label checklabel">10</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                                <label class="form-check-label checklabel">11</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                                <label class="form-check-label checklabel">12</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                                <label class="form-check-label checklabel">13</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                                <label class="form-check-label checklabel">14</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                                <label class="form-check-label checklabel">15</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                                <label class="form-check-label checklabel">16</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                                <label class="form-check-label checklabel">17</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                                <label class="form-check-label checklabel">18</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                                <label class="form-check-label checklabel">19</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                                <label class="form-check-label checklabel">20</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                                <label class="form-check-label checklabel">21</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                                <label class="form-check-label checklabel">22</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                                <label class="form-check-label checklabel">23</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                                <label class="form-check-label checklabel">24</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                                <label class="form-check-label checklabel">25</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                                <label class="form-check-label checklabel">26</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                                <label class="form-check-label checklabel">27</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                                <label class="form-check-label checklabel">28</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                                <label class="form-check-label checklabel">29</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                                <label class="form-check-label checklabel">30</label>
                                                            </div>
                                                            <div class="form-check form-check-inline" style="width: 30px;">
                                                                <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                                <label class="form-check-label checklabel">31</label>
                                                            </div>
                                                            <div class="form-group">
                                                                <span asp-validation-for="CronExpression" id="CronMon-error"></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="form-group">
                                                            <div class="form-label">Starts at</div>
                                                            <div class="input-group">
                                                                <span class="input-group-text">
                                                                    <i class="cp-calendar"></i>
                                                                </span>
                                                                @Html.TextBox("MonthlyHours", null, new { id = "MonthlyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                            </div>
                                                            <span asp-validation-for="CronExpression" id="MonthlyHours-error"></span>
                                                            <input asp-for="IsSchedule" id="textIsSchedule" type="hidden" class="form-control" />
                                                            <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                                                            <input asp-for="ScheduleTime" id="textScheduleTime" type="hidden" class="form-control" />
                                                            <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                   
                </div>
                <div class="form-group">
                    <label class="form-label custom-cursor-default-hover" cursorshover="true">State</label>
                    <div class="row row-cols-4">
                        <div class="col-auto col">
                            <div class="form-check">
                                <input asp-for="State" name="state" type="radio" id="textStateActive"
                                       class="form-check-input" value="Active"><label for="textStateActive"
                                                                                      class="form-check-label" cursorshover="true">Active</label>
                            </div>
                        </div>
                        <div class="col-auto col">
                            <div class="form-check">
                                <input asp-for="State" name="state" type="radio" id="textStateInactive"
                                       class="form-check-input" value="InActive" cursorshover="true"><label for="textStateInactive" class="form-check-label"
                                                                                                            cursorshover="true">Inactive</label>
                            </div>
                        </div>
                    </div>
                    <span asp-validation-for="State" id="state-error"></span>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm monitorbtn-cancel" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="SaveFunction" title="Save">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<div id="CyberCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Cyber.CreateAndEdit" aria-hidden="true"></div>
<div id="CyberDelete" data-delete-permission="@WebHelper.CurrentSession.Permissions.Cyber.Delete" aria-hidden="true"></div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/slide_toggle.js"></script>
<script src="~/js/CyberResiliency/Job Management/CyberJobManagement.js"></script>

