﻿using ContinuityPatrol.Domain.ViewModels.DataSetModel;

namespace ContinuityPatrol.Application.Features.DataSet.Queries.GetList;

public class GetDataSetListQueryHandler : IRequestHandler<GetDataSetListQuery, List<DataSetListVm>>
{
    private readonly IDataSetRepository _dataSetRepository;
    private readonly IMapper _mapper;

    public GetDataSetListQueryHandler(IMapper mapper, IDataSetRepository dataSetRepository)
    {
        _mapper = mapper;

        _dataSetRepository = dataSetRepository;
    }

    public async Task<List<DataSetListVm>> Handle(GetDataSetListQuery request, CancellationToken cancellationToken)
    {
        var dataSet = (await _dataSetRepository.ListAllAsync()).ToList();

        return dataSet.Count == 0 ? new List<DataSetListVm>() : _mapper.Map<List<DataSetListVm>>(dataSet);
    }
}