using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ServerViewRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ServerViewRepository _repository;
    private readonly ServerViewFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    public ServerViewRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
        
        _repository = new ServerViewRepository(_dbContext, _mockInfraObjectRepository.Object, _mockLoggedInUserService.Object);
        _fixture = new ServerViewFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllServersForCompany_WhenUserIsAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_123");
        var server3 = _fixture.CreateServerView(name: "Server3", companyId: "COMPANY_456");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAssignedServers_WhenUserIsNotAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123", businessServiceId: "BS_001");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_123", businessServiceId: "BS_002");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        
        // Setup AssignedEntity
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices { Id = "BS_001", Name = "Service 1" }
            }
        };
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region ServerCountAsync Tests

    [Fact]
    public async Task ServerCountAsync_ShouldReturnCorrectCount_WhenUserIsAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(companyId: "COMPANY_123");
        var server2 = _fixture.CreateServerView(companyId: "COMPANY_123");
        var server3 = _fixture.CreateServerView(companyId: "COMPANY_456");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ServerCountAsync();

        // Assert
        Assert.Equal(2, result);
    }

    [Fact]
    public async Task ServerCountAsync_ShouldReturnAssignedCount_WhenUserIsNotAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(companyId: "COMPANY_123", businessServiceId: "BS_001");
        var server2 = _fixture.CreateServerView(companyId: "COMPANY_123", businessServiceId: "BS_002");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        
        // Setup AssignedEntity
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices { Id = "BS_001", Name = "Service 1" }
            }
        };
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

        // Act
        var result = await _repository.ServerCountAsync();

        // Assert
        Assert.Equal(1, result);
    }

    #endregion

    #region GetServerList Tests

    [Fact]
    public async Task GetServerList_ShouldReturnProjectedFields()
    {
        // Arrange
        await ClearDatabase();
        
        var server = _fixture.CreateServerView(
            companyId: "COMPANY_123",
            osType: "Windows",
            osTypeId: "OS_001",
            businessServiceId: "BS_001",
            properties: "{\"test\":\"value\"}"
        );

        await _repository.AddAsync(server);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetServerList();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var serverResult = result.First();
        Assert.Equal("Windows", serverResult.OSType);
        Assert.Equal("OS_001", serverResult.OSTypeId);
        Assert.Equal("BS_001", serverResult.BusinessServiceId);
        Assert.Equal("{\"test\":\"value\"}", serverResult.Properties);
    }

    #endregion

    #region GetServerNames Tests

    [Fact]
    public async Task GetServerNames_ShouldReturnActiveServersWithProjectedFields_WhenUserIsAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(
            name: "Server1",
            companyId: "COMPANY_123",
            isActive: true,
            roleTypeId: "ROLE_001",
            roleType: "Web Server"
        );
        var server2 = _fixture.CreateServerView(
            name: "Server2",
            companyId: "COMPANY_123",
            isActive: false
        );

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetServerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var serverResult = result.First();
        Assert.Equal("Server1", serverResult.Name);
        Assert.Equal("ROLE_001", serverResult.RoleTypeId);
        Assert.Equal("Web Server", serverResult.RoleType);
        Assert.NotNull(serverResult.ReferenceId);
    }

    [Fact]
    public async Task GetServerNames_ShouldReturnAssignedActiveServers_WhenUserIsNotAllInfra()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(
            name: "Server1",
            companyId: "COMPANY_123",
            isActive: true,
            businessServiceId: "BS_001"
        );
        var server2 = _fixture.CreateServerView(
            name: "Server2",
            companyId: "COMPANY_123",
            isActive: true,
            businessServiceId: "BS_002"
        );

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        
        // Setup AssignedEntity
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices { Id = "BS_001", Name = "Service 1" }
            }
        };
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(assignedEntity));

        // Act
        var result = await _repository.GetServerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ShouldReturnServersWithMatchingServerTypeId_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(name: "Server1", serverTypeId: "TYPE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", serverTypeId: "TYPE_001");
        var server3 = _fixture.CreateServerView(name: "Server3", serverTypeId: "TYPE_002");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task GetType_ShouldReturnServersForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();
        
        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123", serverTypeId: "TYPE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_456", serverTypeId: "TYPE_001");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetType("TYPE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region GetTypeName Tests

    [Fact]
    public async Task GetTypeName_ShouldReturnServersWithMatchingServerType_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", serverType: "Web Server");
        var server2 = _fixture.CreateServerView(name: "Server2", serverType: "WEB SERVER");
        var server3 = _fixture.CreateServerView(name: "Server3", serverType: "Database Server");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTypeName("web server");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task GetTypeName_ShouldReturnServersForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123", serverType: "Web Server");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_456", serverType: "Web Server");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTypeName("Web Server");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region GetRoleType Tests

    [Fact]
    public async Task GetRoleType_ShouldReturnServersWithMatchingRoleTypeId_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", roleTypeId: "ROLE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", roleTypeId: "ROLE_001");
        var server3 = _fixture.CreateServerView(name: "Server3", roleTypeId: "ROLE_002");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetRoleType("ROLE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task GetRoleType_ShouldReturnServersForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123", roleTypeId: "ROLE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_456", roleTypeId: "ROLE_001");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetRoleType("ROLE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region GetServerBySiteId Tests

    [Fact]
    public async Task GetServerBySiteId_ShouldReturnServersWithMatchingSiteId_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", siteId: "SITE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", siteId: "SITE_001");
        var server3 = _fixture.CreateServerView(name: "Server3", siteId: "SITE_002");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetServerBySiteId("SITE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task GetServerBySiteId_ShouldReturnServersForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123", siteId: "SITE_001");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_456", siteId: "SITE_001");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetServerBySiteId("SITE_001");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    #region GetByServerIdsAsync Tests

    [Fact]
    public async Task GetByServerIdsAsync_ShouldReturnServersWithMatchingIds_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1");
        var server2 = _fixture.CreateServerView(name: "Server2");
        var server3 = _fixture.CreateServerView(name: "Server3");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);
        await _repository.AddAsync(server3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var serverIds = new List<string> { server1.ReferenceId, server2.ReferenceId };

        // Act
        var result = await _repository.GetByServerIdsAsync(serverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.Name == "Server1");
        Assert.Contains(result, s => s.Name == "Server2");
        Assert.DoesNotContain(result, s => s.Name == "Server3");
    }

    [Fact]
    public async Task GetByServerIdsAsync_ShouldReturnServersForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var server1 = _fixture.CreateServerView(name: "Server1", companyId: "COMPANY_123");
        var server2 = _fixture.CreateServerView(name: "Server2", companyId: "COMPANY_456");

        await _repository.AddAsync(server1);
        await _repository.AddAsync(server2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var serverIds = new List<string> { server1.ReferenceId, server2.ReferenceId };

        // Act
        var result = await _repository.GetByServerIdsAsync(serverIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Server1", result.First().Name);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.ServerViews.RemoveRange(_dbContext.ServerViews);
        await _dbContext.SaveChangesAsync();
    }
}
