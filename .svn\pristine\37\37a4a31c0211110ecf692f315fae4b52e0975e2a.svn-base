﻿using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Queries;

public class GetSingleSignOnNameQueryHandlerTests : IClassFixture<SingleSignOnFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;
    private Mock<ISingleSignOnRepository> _mockSingleSignOnRepository;
    private readonly GetSingleSignOnNameQueryHandler _handler;

    public GetSingleSignOnNameQueryHandlerTests(SingleSignOnFixture singleSignOnFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnNamesRepository(_singleSignOnFixture.SingleSignOns);

        _handler = new GetSingleSignOnNameQueryHandler(_singleSignOnFixture.Mapper, _mockSingleSignOnRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_SingleSignOn_Name()
    {
        var result = await _handler.Handle(new GetSingleSignOnNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnNameVm>>();

        result[0].Id.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ReferenceId);
        result[0].ProfileName.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ProfileName);
    }

    [Fact]
    public async Task Handle_Return_Active_SingleSignOnNamesCount()
    {
        var result = await _handler.Handle(new GetSingleSignOnNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnNameVm>>();

        result.Count.ShouldBe(_singleSignOnFixture.SingleSignOns.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnEmptyRepository();

        var handler = new GetSingleSignOnNameQueryHandler(_singleSignOnFixture.Mapper, _mockSingleSignOnRepository.Object);

        var result = await handler.Handle(new GetSingleSignOnNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetSingleSignOnNamesMethod_OneTime()
    {
        await _handler.Handle(new GetSingleSignOnNameQuery(), CancellationToken.None);

        _mockSingleSignOnRepository.Verify(x => x.GetSingleSignOnNames(), Times.Once);
    }
}