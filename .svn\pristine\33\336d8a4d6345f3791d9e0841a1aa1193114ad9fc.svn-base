﻿@{
    ViewData["Title"] = "List";
}
<link href="~/css/pagebuilder.css" rel="stylesheet" />
<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-platform-name"></i><span>Page Builder</span></h6>
            <div class="btn-group gap-2 align-items-center d-none">
                <span>Create Page Builder</span>
                <button type="button" class="btn btn-outline-primary btn-sm rounded-2">Templates</button>
                <div class="vr"></div>
                <button type="button" class="btn btn-outline-primary btn-sm rounded-2">My Files</button>
                <div class="vr"></div>
                <button type="button" class="btn btn-outline-primary btn-sm rounded-2">Custom Dashboard</button>
            </div>
            <div class="btn-group gap-2">
                <button type="button" class="btn btn-primary btn-sm rounded-2" data-bs-toggle="modal" data-bs-target="#LayoutModal">Create Layout</button>
                <button type="button" class="btn btn-outline-primary btn-sm rounded-2 d-none">Preview</button>
                <button type="button" class="btn btn-outline-primary btn-sm rounded-2 d-none">Publish Page</button>
            </div>
        </div>
        <div class="card-body pt-0" style="height:calc(100vh - 128px); overflow:auto;">
            <div class="Choose-Template" id="homepageModal">
                <div class="card-title d-none">Choose template to built Page Builder</div>
                <div class="row row-cols-xl-4 row-cols-xxl-5 mt-0 g-3">
                    <div class="col">
                        <div class="card border" data-bs-toggle="modal" data-bs-target="#TitleModal">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Blank</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Monitoring</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Storage DB2</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Storage Oracle</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Rsync Linux</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Storage Postgres</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Storage MSSQL</div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border mb-0">
                            <img src="~/img/layouts_img/1.monitoring_db2_hadr_linux.png" class="w-100" />
                            <div class="card-footer list-title text-center">Storage Mysql</div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="createpageModal" class="h-100">
                <div class="d-flex justify-content-between border px-3 align-items-center">
                    <ul class="nav align-items-center list-title DesignArea-Tab" id="pills-tab" role="tablist">
                        @*  <li class="nav-item" role="presentation">
                        Design Area
                        </li> *@
                        @* <li class="nav-item ms-3">|</li> *@
                        <li class="nav-item " role="presentation">
                            <button class="nav-link" type="button"><i class="cp-grid me-1"></i>Layout</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link d-none" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false"><i class="cp-card me-1"></i>Card</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link d-none" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab" aria-controls="pills-contact" aria-selected="false"><i class="cp-table me-1"></i>Table</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link d-none" id="pills-Diagram-tab" data-bs-toggle="pill" data-bs-target="#pills-Diagram" type="button" role="tab" aria-controls="pills-Diagram" aria-selected="false"><i class="cp-endpoint-port-number me-1"></i>Diagram</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link d-none" id="pills-Text-tab" data-bs-toggle="pill" data-bs-target="#pills-Text" type="button" role="tab" aria-controls="pills-Text" aria-selected="false"><i class="cp-text me-1"></i>Text</button>
                        </li>
                    </ul>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb list-title mb-0">
                            <li class="breadcrumb-item">Draft</li>
                            <li class="breadcrumb-item active" aria-current="page">Business Dashboard</li>
                        </ol>
                    </nav>
                </div>
                <div class="tab-content mh-100 h-100">
                    <div class="row h-100">
                        <div class="col" id="PB_pageContent">hello
                        </div>
                        <div class="col-3 collapse" id="collapseExample">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    Create Widget
                                </div>
                                <div class="card-body px-0 pt-0">
                                    <div class="accordion accordion-flush" id="accordionFlushExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                    Header
                                                </button>
                                            </h2>
                                            <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body">

                                                    <div class="form-group">
                                                        <label class="form-label">Widget Title</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                                            <input type="text" placeholder="Enter Widget Title" class="form-control" />
                                                        </div>
                                                    </div>


                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                    Data
                                                </button>
                                            </h2>
                                            <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                <div class="accordion-body">
                                                    <div class="switches-container mb-3">
                                                        <input type="radio" id="switchMonthly" name="switchPlan" value="Chart" checked="checked" />
                                                        <input type="radio" id="switchYearly" name="switchPlan" value="Table" />
                                                        <label for="switchMonthly">Chart</label>
                                                        <label for="switchYearly">Table</label>
                                                        <div class="switch-wrapper">
                                                            <div class="switch">
                                                                <div>Chart</div>
                                                                <div>Table</div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="month">
                                                        <ul class="list-group">
                                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                A list item
                                                                <span class="badge bg-primary rounded-pill">14</span>
                                                            </li>
                                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                A second list item
                                                                <span class="badge bg-primary rounded-pill">2</span>
                                                            </li>
                                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                A third list item
                                                                <span class="badge bg-primary rounded-pill">1</span>
                                                            </li>
                                                        </ul>


                                                    </div>
                                                    <div class="year">hiiiiiiiiiii</div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @*  <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">...</div>
                    <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab" tabindex="0">...</div>
                    <div class="tab-pane fade" id="pills-disabled" role="tabpanel" aria-labelledby="pills-disabled-tab" tabindex="0">...</div> *@
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Layouts Modal -->
<div class="modal fade" id="LayoutModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Layouts</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-6 g-2">
                    <div class="col">
                        <div class="card border" data-bs-dismiss="modal" aria-label="Close" role="button">
                            <div class="card-body p-2" role="button" id="PB_layout1">
                                <img src="~/img/layouts_img/grid-layouts/layout1.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout2">
                                <img src="~/img/layouts_img/grid-layouts/layout2.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout3">
                                <img src="~/img/layouts_img/grid-layouts/layout3.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout4">
                                <img src="~/img/layouts_img/grid-layouts/layout4.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout5">
                                <img src="~/img/layouts_img/grid-layouts/layout5.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout6">
                                <img src="~/img/layouts_img/grid-layouts/layout6.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout7">
                                <img src="~/img/layouts_img/grid-layouts/layout7.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout8">
                                <img src="~/img/layouts_img/grid-layouts/layout8.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout9">
                                <img src="~/img/layouts_img/grid-layouts/layout9.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout10">
                                <img src="~/img/layouts_img/grid-layouts/layout10.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout11">
                                <img src="~/img/layouts_img/grid-layouts/layout11.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout12">
                                <img src="~/img/layouts_img/grid-layouts/layout12.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="card border">
                            <div class="card-body p-2" role="button" id="PB_layout13">
                                <img src="~/img/layouts_img/grid-layouts/layout13.png" class="w-100" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>


@* Offcanvas Modal  *@

<div class="offcanvas offcanvas-start" data-bs-scroll="true" data-bs-backdrop="false" tabindex="-1" id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasScrollingLabel">Offcanvas with body scrolling</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <p>Try scrolling the rest of the page to see this option in action.</p>
    </div>
</div>


<!-- Title Modal -->
<div class="modal fade" id="TitleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title" title="Company Configuration"><i class="cp-grid"></i><span>Card Title</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-label" title="Card Title">Card Title</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" type="text" placeholder="Enter Card Title" />
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal" id="blankpage">Save</button>
            </div>
        </div>
    </div>
</div>
<!--Notification-->
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='{ClassName}-toast'>
                    <i class='cp-check toast_icon iconClass'></i>
                </span>
                <span id="notificationAlertmessage">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>
<script src="~/js/PageBuilder/PageBuilder.js"></script>
<script src="~/js/slide_toggle.js"></script>