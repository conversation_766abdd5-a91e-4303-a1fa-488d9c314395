﻿using ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManagerHistory.Queries;

public class GetPluginManagerHistoryDetailQueryHandlerTests : IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerHistoryFixture _pluginManagerHistoryFixture;

    private readonly Mock<IPluginManagerHistoryRepository> _mockPluginManagerHistoryRepository;

    private readonly GetPluginManagerHistoryDetailQueryHandler _handler;

    public GetPluginManagerHistoryDetailQueryHandlerTests(PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerHistoryFixture = pluginManagerHistoryFixture;

        _mockPluginManagerHistoryRepository = PluginManagerHistoryRepositoryMocks.GetPluginManagerHistoryRepository(_pluginManagerHistoryFixture.PluginManagerHistories);

        _handler = new GetPluginManagerHistoryDetailQueryHandler(_pluginManagerHistoryFixture.Mapper, _mockPluginManagerHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_PluginManagerHistoryDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetPluginManagerHistoryDetailQuery { PluginManagerId = _pluginManagerHistoryFixture.PluginManagerHistories[0].PluginManagerId }, CancellationToken.None);

        result.ShouldBeOfType<List<PluginManagerHistoryDetailVm>>();

        result[0].Id.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].ReferenceId);
        result[0].CompanyId.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].CompanyId);
        result[0].LoginName.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].LoginName);
        result[0].PluginManagerId.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].PluginManagerId);
        result[0].PluginManagerName.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].PluginManagerName);
        result[0].Properties.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].Properties);
        result[0].Version.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].Version);
        result[0].UpdaterId.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].UpdaterId);
        result[0].Description.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].Description);
        result[0].Comments.ShouldBe(_pluginManagerHistoryFixture.PluginManagerHistories[0].Comments);
    }

    [Fact]
    public async Task Handle_Call_GetPluginManagerHistoryByPluginManagerIdMethod_OnlyOnce()
    {
        await _handler.Handle(new GetPluginManagerHistoryDetailQuery { PluginManagerId = _pluginManagerHistoryFixture.PluginManagerHistories[0].PluginManagerId }, CancellationToken.None);

        _mockPluginManagerHistoryRepository.Verify(x => x.GetPluginManagerHistoryByPluginManagerId(It.IsAny<string>()), Times.Once);
    }
}