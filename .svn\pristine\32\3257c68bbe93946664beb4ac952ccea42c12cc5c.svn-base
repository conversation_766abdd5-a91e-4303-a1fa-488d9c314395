﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;

namespace ContinuityPatrol.Infrastructure.Contract;

public interface ILicenseValidationService
{
    Task<int> DatabaseTypeLicenseCount(LicenseManager licenseManager, SiteType siteType, string databaseTypeId,
        int index);
    Task<(bool Success, List<string> TypeIds)> IsDatabaseTypeAsync(string json, string key, string databaseTypeId);
    Task<bool> IsDatabaseTypeLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType,
        string databaseTypeId, int databaseCount, int index);
    Task<int> ReplicationLicenseCount(LicenseManager licenseManager, SiteType siteType, int index);
    Task<int> DatabaseLicenseCount(LicenseManager licenseManager, SiteType siteType, int index);
    Task<int> ServerLicenseCount(LicenseManager licenseManager, SiteType siteType, string type, int index);
    Task<bool> IsServerLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType, string type, int serverCount,int index);
    Task<bool> IsDatabaseLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType, int databaseCount, int index);
    Task<bool> IsReplicationLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType, int replicationCount, int index);
    Task<bool> IsLicenseExpired(string date);
    Task<bool> IsLicenseSiteAvailable(string properties, SiteType siteType, string type, int index, string serverRoleType = null);
    Task<bool> IsCompanyNameValidate(string newCompanyName, string existCompanyName);
    Task<bool> IsRenewalLicenseValidate(string licenseType);
    Task<bool> IsMacAddressValidAsync(string macAddresses);
    Task<bool> IsHostNameValidAsync(string hostName);
    Task<bool> IsIpAddressValidAsync(string ipAddress);
    Task<bool> ValidateLicenseKeyDate(string licenseGeneratorDate);
    Task<bool> IsLicenseKeyFormatValid(string licenseKey);
    Task<string> UpdateExpiryDate(LicenseDto newLicenseKey, LicenseManager eventToUpdate);
    Task<string> AmcEndDate(string expireTime);
    Task<string> AmcStartDate(string src);
    Task<bool> IsAmc(string src);
    Task<string> UpdateAmcDate(LicenseDto newLicenseKey, LicenseManager eventToUpdate);
    Task<bool> IsWarranty(string src);
}