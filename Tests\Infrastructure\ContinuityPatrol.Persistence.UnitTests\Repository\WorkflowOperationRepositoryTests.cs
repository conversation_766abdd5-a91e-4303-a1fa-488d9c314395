using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowOperationRepositoryTests : IClassFixture<WorkflowOperationFixture>
    {
        private readonly WorkflowOperationFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowOperationRepository _repoParent;
        private readonly WorkflowOperationRepository _repoNotParent;

        public WorkflowOperationRepositoryTests(WorkflowOperationFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repoParent = new WorkflowOperationRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repoNotParent = new WorkflowOperationRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsParent()
        {
            await _dbContext.WorkflowOperations.AddRangeAsync(_fixture.WorkflowOperationList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.ListAllAsync();

            Assert.Equal(_fixture.WorkflowOperationList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsFiltered_WhenNotParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoNotParent.ListAllAsync();

            Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
        }

        [Fact]
        public async Task GetFilterListAsync_ReturnsFilteredList()
        {
            await _dbContext.WorkflowOperations.AddRangeAsync(_fixture.WorkflowOperationList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetFilterListAsync();

            Assert.All(result, x => Assert.NotNull(x.ReferenceId));
        }

        [Fact]
        public async Task GetLastWorkflowOperation_ReturnsLastNonRunningOrPending()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Completed";
            entity.LastModifiedDate = DateTime.UtcNow;
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetLastWorkflowOperation();

            Assert.NotNull(result);
            Assert.Equal("Completed", result.Status);
        }

        [Fact]
        public async Task GetDrillDetailsByBusinessServiceId_ReturnsVmAndInfraIds()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Completed";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetDrillDetailsByBusinessServiceId("BS1");

            Assert.NotNull(result.WorkflowOperationDrDrillVm);
            Assert.NotNull(result.InfraIds);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAndRunMode_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.RunMode = "Auto";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetByReferenceIdAndRunMode(entity.ReferenceId, "Auto");

            Assert.NotNull(result);
            Assert.Equal("Auto", result.RunMode);
        }

        [Fact]
        public async Task GetWorkflowOperationNames_ReturnsNames_WhenIsParent()
        {
            await _dbContext.WorkflowOperations.AddRangeAsync(_fixture.WorkflowOperationList);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationNames();

            Assert.All(result, x => Assert.NotNull(x.ProfileName));
        }

        [Fact]
        public void GetPaginatedQuery_ReturnsActiveOrdered_WhenIsParent()
        {
            _dbContext.WorkflowOperations.AddRange(_fixture.WorkflowOperationPaginationList);
            _dbContext.SaveChanges();

            var result = _repoParent.GetPaginatedQuery().ToList();

            Assert.All(result, x => Assert.True(x.IsActive));
            Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
        }

        [Fact]
        public async Task GetWorkflowOperationByRunningStatus_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Running";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByRunningStatus();

            Assert.All(result, x => Assert.Equal("Running", x.Status));
        }

        [Fact]
        public async Task GetWorkflowOperationByOperationId_ReturnsList()
        {
            var entity = _fixture.WorkflowOperationDto;
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByOperationId(entity.ReferenceId);

            Assert.All(result, x => Assert.Equal(entity.ReferenceId, x.ReferenceId));
        }

        [Fact]
        public async Task GetWorkflowOperationByRunningUserId_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Running";
            entity.CreatedBy = "USER_456";
            entity.IsActive = true;
             _dbContext.WorkflowOperations.Add(entity);
             _dbContext.SaveChanges();

            var result = await _repoParent.GetWorkflowOperationByRunningUserId(new List<string> { "USER_456" });

            Assert.All(result, x => Assert.Equal(entity.RunMode, x.RunMode));
        }

        [Fact]
        public async Task GetWorkflowOperationByRunningUsers_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Running";
            entity.UserName = "user2";
            entity.CreatedBy = "user2";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByRunningUsers();

            Assert.All(result, x => Assert.Equal("user2", x.UserName));
        }

        [Fact]
        public async Task GetDescriptionByStartTimeAndEndTime_ReturnsList_WhenIsParent()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.Status = "Success";
            entity.IsActive = true;
            entity.CreatedDate = DateTime.UtcNow.Date.AddDays(-1);
            entity.LastModifiedDate = DateTime.UtcNow.Date.AddDays(2);
              _dbContext.WorkflowOperations.Add(entity);
              _dbContext.SaveChanges();

            var start = entity.CreatedDate.ToString("yyyy-MM-dd");
            var end = entity.CreatedDate.ToString("yyyy-MM-dd");
            var result = await _repoParent.GetDescriptionByStartTimeAndEndTime(start, end);

            Assert.All(result, x => Assert.Equal(entity.RunMode, x.RunMode, ignoreCase: true));
        }

        [Fact]
        public async Task GetWorkflowOperationByProfileId_ReturnsList()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.ProfileId = "PID1";
            entity.Status = "Running";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByProfileId("PID1");

            Assert.All(result, x => Assert.Equal("PID1", x.ProfileId));
        }

        [Fact]
        public async Task GetWorkflowOperationByProfileIdAndStatus_ReturnsList()
        {
            var entity = _fixture.WorkflowOperationDto;
            entity.ProfileId = "PID2";
            entity.Status = "completed";
            await _dbContext.WorkflowOperations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repoParent.GetWorkflowOperationByProfileIdAndStatus("PID2", "completed");

            Assert.All(result, x => Assert.Equal("PID2", x.ProfileId));
            Assert.All(result, x => Assert.Equal("completed", x.Status, ignoreCase: true));
        }
    }
}