using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class EscalationMatrixFixture : IDisposable
{
    public List<EscalationMatrix> EscalationMatrixPaginationList { get; set; }
    public List<EscalationMatrix> EscalationMatrixList { get; set; }
    public EscalationMatrix EscalationMatrixDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public EscalationMatrixFixture()
    {
        var fixture = new Fixture();

        EscalationMatrixList = fixture.Create<List<EscalationMatrix>>();

        EscalationMatrixPaginationList = fixture.CreateMany<EscalationMatrix>(20).ToList();

        EscalationMatrixPaginationList.ForEach(x => x.CompanyId = CompanyId);
        EscalationMatrixPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EscalationMatrixPaginationList.ForEach(x => x.IsActive = true);

        EscalationMatrixList.ForEach(x => x.CompanyId = CompanyId);
        EscalationMatrixList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EscalationMatrixList.ForEach(x => x.IsActive = true);

        EscalationMatrixDto = fixture.Create<EscalationMatrix>();
        EscalationMatrixDto.CompanyId = CompanyId;
        EscalationMatrixDto.ReferenceId = Guid.NewGuid().ToString();
        EscalationMatrixDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
