using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class SiteLocationRepository : BaseRepository<SiteLocation>, ISiteLocationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public SiteLocationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<SiteLocation>> ListAllAsync()
    {
        return await SelectSiteLocation(_dbContext.SiteLocations.Active().AsNoTracking())
           .ToListAsync();
    }
   
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
            return await Entities.AnyAsync(e => e.City == name);
        
        return await Entities.AnyAsync(e => e.City == name && e.ReferenceId != id);
        
    }

    public async Task<bool> IsSiteLocationNameUnique(string name)
    {
        return await _dbContext.SiteLocations.AnyAsync(e => e.City == name);
         
    }
    public override async Task<PaginatedResult<SiteLocation>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<SiteLocation> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectSiteLocation(Entities.Specify(productFilterSpec).DescOrderById())
          .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    private IQueryable<SiteLocation> SelectSiteLocation(IQueryable<SiteLocation> query)
    {
        return query.Select(x => new SiteLocation
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            City = x.City,
            CityAscii = x.CityAscii,
            Country = x.Country,
            Lat = x.Lat,
            Lng = x.Lng,
            Iso2 = x.Iso2,
            Iso3 = x.Iso3,
            IsDelete = x.IsDelete

        });
    }
}