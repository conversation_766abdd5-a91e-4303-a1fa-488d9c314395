using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MYSQLMonitorStatusRepositoryTests : IClassFixture<MYSQLMonitorStatusFixture>
{
    private readonly MYSQLMonitorStatusFixture _mysqlMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MYSQLMonitorStatusRepository _repository;

    public MYSQLMonitorStatusRepositoryTests(MYSQLMonitorStatusFixture mysqlMonitorStatusFixture)
    {
        _mysqlMonitorStatusFixture = mysqlMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new MYSQLMonitorStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MysqlMonitorStatuses.RemoveRange(_dbContext.MysqlMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }


    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnActiveStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithMixedActiveStatus(testType);
        
        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(statuses);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active statuses should be returned
        Assert.All(result, status => Assert.True(status.IsActive));
        Assert.All(result, status => Assert.Equal(testType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var activeStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(type: testType, isActive: true);
        var inactiveStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(type: testType, isActive: false);
        
        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(new[] { activeStatus, inactiveStatus });
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal(testType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithWhitespace();
        whitespaceStatus.IsActive = true;
        await _dbContext.MysqlMonitorStatuses.AddAsync(whitespaceStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithLongType(1000);
        longTypeStatus.IsActive = true;
        await _dbContext.MysqlMonitorStatuses.AddAsync(longTypeStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = MYSQLMonitorStatusFixture.TestData.SpecialCharacterTypes;
        var statuses = new List<MYSQLMonitorStatus>();

        foreach (var type in specialTypes)
        {
            var status = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(type: type, isActive: true);
            statuses.Add(status);
        }

        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnMultipleStatuses_WhenMultipleActiveStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _mysqlMonitorStatusFixture.CreateMultipleMYSQLMonitorStatusWithSameType(testType, 5);
        
        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count);
        Assert.All(result, status => Assert.Equal(testType, status.Type));
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseType = "testtype";
        var upperCaseType = "TESTTYPE";
        
        var lowerCaseStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(type: lowerCaseType, isActive: true);
        var upperCaseStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(type: upperCaseType, isActive: true);
        
        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetDetailByType(lowerCaseType);
        var upperResult = await _repository.GetDetailByType(upperCaseType);

        // Assert
        Assert.Single(lowerResult);
        Assert.Single(upperResult);
        Assert.Equal(lowerCaseType, lowerResult.First().Type);
        Assert.Equal(upperCaseType, upperResult.First().Type);
    }

    #endregion

    #region GetMysqlMonitorStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        await _dbContext.MysqlMonitorStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync("9a73e815-ab81-4d54-a468-72b4b9632866");

        // Assert
        Assert.Null(result);
    }

    
    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var status1 = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);
        var status2 = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        // Add status1 first, then status2
        await _dbContext.MysqlMonitorStatuses.AddAsync(status1);
         _dbContext.SaveChanges();
        await _dbContext.MysqlMonitorStatuses.AddAsync(status2);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one added (FirstOrDefaultAsync behavior)
        Assert.Equal(status1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldReturnActiveAndInactiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var inactiveStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithProperties(
            infraObjectId: infraObjectId,
            isActive: false);

        await _dbContext.MysqlMonitorStatuses.AddAsync(inactiveStatus);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.False(result.IsActive); // Should return inactive status as well
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldHandleValidGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var validGuids = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString("D"),
            Guid.NewGuid().ToString("N"),
            Guid.NewGuid().ToString("B"),
            Guid.NewGuid().ToString("P")
        };

        var statuses = new List<MYSQLMonitorStatus>();
        foreach (var guid in validGuids)
        {
            var status = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(guid);
            statuses.Add(status);
        }

        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var guid in validGuids)
        {
            var result = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(guid);
            Assert.NotNull(result);
            Assert.Equal(guid, result.InfraObjectId);
        }
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseGuid = Guid.NewGuid().ToString().ToLower();
        var upperCaseGuid = lowerCaseGuid.ToUpper();

        var lowerCaseStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(lowerCaseGuid);
        var upperCaseStatus = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(upperCaseGuid);

        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(lowerCaseGuid);
        var upperResult = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(upperCaseGuid);

        // Assert
        Assert.NotNull(lowerResult);
        Assert.NotNull(upperResult);
        Assert.Equal(lowerCaseGuid, lowerResult.InfraObjectId);
        Assert.Equal(upperCaseGuid, upperResult.InfraObjectId);
        Assert.NotEqual(lowerResult.ReferenceId, upperResult.ReferenceId);
    }

    [Fact]
    public async Task GetMysqlMonitorStatusByInfraObjectIdAsync_ShouldHandleSpecialCharactersInGuid()
    {
        // Arrange
        await ClearDatabase();
        var guidWithDashes = "12345678-1234-1234-1234-123456789012";
        var guidWithBraces = "{12345678-1234-1234-1234-123456789013}";
        var guidWithParentheses = "(12345678-1234-1234-1234-123456789014)";

        var status1 = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(guidWithDashes);
        var status2 = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(guidWithBraces);
        var status3 = _mysqlMonitorStatusFixture.CreateMYSQLMonitorStatusWithSpecificInfraObjectId(guidWithParentheses);

        await _dbContext.MysqlMonitorStatuses.AddRangeAsync(new[] { status1, status2, status3 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(guidWithDashes);
        var result2 = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(guidWithBraces);
        var result3 = await _repository.GetMysqlMonitorStatusByInfraObjectIdAsync(guidWithParentheses);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
        Assert.Equal(guidWithDashes, result1.InfraObjectId);
        Assert.Equal(guidWithBraces, result2.InfraObjectId);
        Assert.Equal(guidWithParentheses, result3.InfraObjectId);
    }

    #endregion


}
