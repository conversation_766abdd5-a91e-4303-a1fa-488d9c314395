using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Events;

public class BackUpLogUpdatedEventTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BackUpLogUpdatedEventHandler>> _mockLogger;
    private readonly BackUpLogUpdatedEventHandler _handler;

    public BackUpLogUpdatedEventTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockUserActivityRepository = BackUpLogRepositoryMocks.CreateUserActivityRepository(_backUpLogFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpLogUpdatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/backuplog");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BackUpLogUpdatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BackUpLogUpdatedEventReceived()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "UpdatedDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BackUpLogUpdated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "UpdateBackupUser";
        var requestUrl = "/api/v6/backuplog/update";
        var ipAddress = "*************";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "CustomUpdatedDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'CustomUpdatedDatabase' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Update.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BackUpLogUpdatedWithNullName()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = null };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BackUpLogUpdatedWithEmptyName()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleCancellation_When_CancellationRequested()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        // Act & Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexDatabaseName_When_BackUpLogUpdated()
    {
        // Arrange
        var complexDatabaseName = "Updated_Complex_Production_Database_456";
        var updatedEvent = new BackUpLogUpdatedEvent { Name = complexDatabaseName };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BackUpLog '{complexDatabaseName}' updated successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldBeOfType<BackUpLogUpdatedEvent>();
        updatedEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_DifferentiateFromCreateEvent_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Update.ToString() &&
            ua.ActivityDetails.Contains("updated") &&
            !ua.ActivityDetails.Contains("created"))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActionFormat_When_BackUpLogUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_UpdateEventForDifferentialBackup_When_DifferentialBackupUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "UpdatedDifferentialDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'UpdatedDifferentialDatabase' updated successfully." &&
            ua.ActivityType == ActivityType.Update.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateEventForTransactionLogBackup_When_TransactionLogBackupUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "UpdatedTransactionLogDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_UpdateEventForFailedBackup_When_FailedBackupUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "UpdatedFailedDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_UpdateEventForRemoteBackup_When_RemoteBackupUpdated()
    {
        // Arrange
        var updatedEvent = new BackUpLogUpdatedEvent { Name = "UpdatedRemoteDatabase" };

        // Act
        await _handler.Handle(updatedEvent, CancellationToken.None);

        // Assert
        updatedEvent.ShouldNotBeNull();
    }
}
