﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Draft;

public class UpdateWorkflowDraftEventHandler : INotificationHandler<UpdateWorkflowDraftEvent>
{
    private readonly ILogger<UpdateWorkflowDraftEvent> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UpdateWorkflowDraftEventHandler(ILogger<UpdateWorkflowDraftEvent> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(UpdateWorkflowDraftEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{updatedEvent.WorkflowName}' IsDraft  {updatedEvent.IsDraft} Updated successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"IsDraft {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = "IsDraft",
            ActivityDetails = $"Workflow '{updatedEvent.WorkflowName}' Is Draft {updatedEvent.IsDraft} Updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}