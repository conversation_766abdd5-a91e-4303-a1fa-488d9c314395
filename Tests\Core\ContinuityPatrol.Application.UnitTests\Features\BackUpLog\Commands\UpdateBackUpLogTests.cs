using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Commands;

public class UpdateBackUpLogTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateBackUpLogCommandHandler _handler;

    public UpdateBackUpLogTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new UpdateBackUpLogCommandHandler(
            _backUpLogFixture.Mapper,
            _mockBackUpLogRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateBackUpLog_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "UpdatedServer01",
            DatabaseName = "UpdatedDatabase",
            UserName = "UpdatedUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\UpdatedDatabase.bak",
            Type = "Differential",
            Status = "In Progress"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateBackUpLogResponse>();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.Message.ShouldContain("UpdatedDatabase");
    }

    [Fact]
    public async Task Handle_CallGetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "UpdatedServer02",
            DatabaseName = "UpdatedDatabase2"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(existingBackUpLog.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallUpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "UpdatedServer03",
            DatabaseName = "UpdatedDatabase3"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PublishBackUpLogUpdatedEvent_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "UpdatedServer04",
            DatabaseName = "UpdatedDatabase4"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpLogUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateBackUpLogCommand
        {
            Id = nonExistentId,
            HostName = "UpdatedServer05",
            DatabaseName = "UpdatedDatabase5"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogIsInactive()
    {
        // Arrange
        var inactiveBackUpLog = _backUpLogFixture.BackUpLogs.First();
        inactiveBackUpLog.IsActive = false;
        var command = new UpdateBackUpLogCommand
        {
            Id = inactiveBackUpLog.ReferenceId,
            HostName = "UpdatedServer06",
            DatabaseName = "UpdatedDatabase6"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_UpdateAllProperties_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "CompletelyUpdatedServer",
            DatabaseName = "CompletelyUpdatedDatabase",
            UserName = "CompletelyUpdatedUser",
            IsLocalServer = !existingBackUpLog.IsLocalServer,
            IsBackUpServer = !existingBackUpLog.IsBackUpServer,
            BackUpPath = @"E:\NewBackups\CompletelyUpdatedDatabase.bak",
            Type = "Transaction Log",
            Status = "Failed"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.Message.ShouldContain("CompletelyUpdatedDatabase");
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "TypeTestServer",
            DatabaseName = "TypeTestDatabase"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateBackUpLogResponse>();
        result.GetType().Name.ShouldBe("UpdateBackUpLogResponse");
    }

    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "CancellationTestServer",
            DatabaseName = "CancellationTestDatabase"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await Should.ThrowAsync<OperationCanceledException>(async () =>
            await _handler.Handle(command, cts.Token));
    }

    [Fact]
    public async Task Handle_UpdateBackUpLogWithDifferentTypes_When_ValidCommands()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var backupTypes = new[] { "Full", "Differential", "Transaction Log", "Copy-Only" };

        foreach (var backupType in backupTypes)
        {
            var command = new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = $"TypeServer_{backupType}",
                DatabaseName = $"TypeDatabase_{backupType}",
                Type = backupType
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingBackUpLog.ReferenceId);
            result.Message.ShouldContain($"TypeDatabase_{backupType}");
        }
    }

    [Fact]
    public async Task Handle_UpdateBackUpLogWithDifferentStatuses_When_ValidCommands()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var statuses = new[] { "Completed", "In Progress", "Failed", "Cancelled" };

        foreach (var status in statuses)
        {
            var command = new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = $"StatusServer_{status}",
                DatabaseName = $"StatusDatabase_{status}",
                Status = status
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingBackUpLog.ReferenceId);
            result.Message.ShouldContain($"StatusDatabase_{status}");
        }
    }

    [Fact]
    public async Task Handle_UpdateBackUpLogServerTypes_When_ValidCommands()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var serverConfigurations = new[]
        {
            new { IsLocal = true, IsBackUp = false, Description = "LocalOnly" },
            new { IsLocal = false, IsBackUp = true, Description = "BackUpOnly" },
            new { IsLocal = true, IsBackUp = true, Description = "Both" },
            new { IsLocal = false, IsBackUp = false, Description = "Neither" }
        };

        foreach (var config in serverConfigurations)
        {
            var command = new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = $"ConfigServer_{config.Description}",
                DatabaseName = $"ConfigDatabase_{config.Description}",
                IsLocalServer = config.IsLocal,
                IsBackUpServer = config.IsBackUp
            };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingBackUpLog.ReferenceId);
            result.Message.ShouldContain($"ConfigDatabase_{config.Description}");
        }
    }

    [Fact]
    public async Task Handle_UpdateBackUpLogWithLongPaths_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var longPath = @"C:\Very\Long\Path\To\Backup\Directory\With\Many\Subdirectories\And\A\Very\Long\Database\Name\TestDatabase.bak";
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "LongPathServer",
            DatabaseName = "LongPathDatabase",
            BackUpPath = longPath
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.Message.ShouldContain("LongPathDatabase");
    }

    [Fact]
    public async Task Handle_UpdateBackUpLogWithSpecialCharacters_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "Special-Server_01",
            DatabaseName = "Special_Database-Test",
            UserName = "Domain\\Special.User",
            BackUpPath = @"C:\Backups\Special_Database-Test.bak"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.Message.ShouldContain("Special_Database-Test");
    }

    [Fact]
    public async Task Handle_VerifyEventPublishedWithCorrectData_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "EventTestServer",
            DatabaseName = "EventTestDatabase"
        };

        BackUpLogUpdatedEvent publishedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<BackUpLogUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<BackUpLogUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        publishedEvent.ShouldNotBeNull();
        publishedEvent.Name.ShouldBe("EventTestDatabase");
    }

    [Fact]
    public async Task Handle_VerifyRepositoryUpdateCalledWithCorrectEntity_When_ValidCommand()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            HostName = "VerifyUpdateServer",
            DatabaseName = "VerifyUpdateDatabase",
            UserName = "VerifyUpdateUser"
        };

        Domain.Entities.BackUpLog updatedEntity = null;
        _mockBackUpLogRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()))
            .Callback<Domain.Entities.BackUpLog>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUpLog entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        updatedEntity.ShouldNotBeNull();
        updatedEntity.ReferenceId.ShouldBe(existingBackUpLog.ReferenceId);
        updatedEntity.HostName.ShouldBe("VerifyUpdateServer");
        updatedEntity.DatabaseName.ShouldBe("VerifyUpdateDatabase");
        updatedEntity.UserName.ShouldBe("VerifyUpdateUser");
    }

    [Fact]
    public async Task Handle_HandleMultipleUpdatesSequentially_When_ValidCommands()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var commands = new[]
        {
            new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = "Sequential1",
                DatabaseName = "SequentialDB1"
            },
            new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = "Sequential2",
                DatabaseName = "SequentialDB2"
            },
            new UpdateBackUpLogCommand
            {
                Id = existingBackUpLog.ReferenceId,
                HostName = "Sequential3",
                DatabaseName = "SequentialDB3"
            }
        };

        // Act & Assert
        foreach (var command in commands)
        {
            var result = await _handler.Handle(command, CancellationToken.None);
            result.ShouldNotBeNull();
            result.Id.ShouldBe(existingBackUpLog.ReferenceId);
            result.Message.ShouldContain(command.DatabaseName);
        }

        // Verify all updates were called
        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Exactly(3));
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpLogUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Exactly(3));
    }

    [Fact]
    public async Task Handle_PreserveUnchangedProperties_When_PartialUpdate()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var originalHostName = existingBackUpLog.HostName;
        var originalUserName = existingBackUpLog.UserName;

        var command = new UpdateBackUpLogCommand
        {
            Id = existingBackUpLog.ReferenceId,
            DatabaseName = "PartialUpdateDatabase",
            Type = "Incremental"
            // Note: Not updating HostName or UserName
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.Message.ShouldContain("PartialUpdateDatabase");

        // Verify the entity was updated
        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Once);
    }
}
