﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.User.Events.ForgotPassword;

public class ForgotPasswordUpdatedEventHandler : INotificationHandler<ForgotPasswordUpdatedEvent>
{
    private readonly ILogger<ForgotPasswordUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ForgotPasswordUpdatedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<ForgotPasswordUpdatedEventHandler> logger)
    {
        _userService = userService;
        _userActivityRepository = userActivityRepository;
        _logger = logger;
    }

    public async Task Handle(ForgotPasswordUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.User.ToString(),
            Action = $"{ActivityType.ForgotPassword} {Modules.User}",
            ActivityType = ActivityType.ForgotPassword.ToString(),
            ActivityDetails = $"User '{updatedEvent.UserName}' Password reset successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"User '{updatedEvent.UserName}' Password reset successfully.");
    }
}