using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class EscalationMatrixLevelRepositoryTests : IClassFixture<EscalationMatrixLevelFixture>
{
    private readonly EscalationMatrixLevelFixture _escalationMatrixLevelFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly EscalationMatrixLevelRepository _repository;

    public EscalationMatrixLevelRepositoryTests(EscalationMatrixLevelFixture escalationMatrixLevelFixture)
    {
        _escalationMatrixLevelFixture = escalationMatrixLevelFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new EscalationMatrixLevelRepository(_dbContext);
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnExpectedCount_WhenEscalationMatrixLevelsExist()
    {
        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoEscalationMatrixLevelsExist()
    {
        var result = await _repository.ListAllAsync();

        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsOnlyActiveEscalationMatrixLevels_WhenInactiveEscalationMatrixLevelsExist()
    {
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[0].IsActive = true;
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].IsActive = false;
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[2].IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].IsActive = false;
         _dbContext.EscalationMatrixLevels.UpdateRange(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsEscalationMatrixLevel_WhenExists()
    {
        _dbContext.EscalationMatrixLevels.Add(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto.ReferenceId);

        Assert.NotNull(result);
        Assert.Equal(_escalationMatrixLevelFixture.EscalationMatrixLevelDto.ReferenceId, result.ReferenceId);
        Assert.Equal(_escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName, result.EscLevName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        Assert.Null(result);
    }

 

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ReturnsEscalationMatrixLevel_WhenExists()
    {
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.Id = 1;
        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByIdAsync(1);

        Assert.NotNull(result);
        Assert.Equal(_escalationMatrixLevelFixture.EscalationMatrixLevelDto.Id, result.Id);
        Assert.Equal(_escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName, result.EscLevName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenRecordDoesNotExist()
    {
        var nonExistentId = 999;
        var result = await _repository.GetByIdAsync(nonExistentId);

        Assert.Null(result);
    }



    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEscalationMatrixLevel_WhenValidEscalationMatrixLevel()
    {
        var escalationMatrixLevel = _escalationMatrixLevelFixture.EscalationMatrixLevelDto;
        escalationMatrixLevel.EscLevName = "Level 1";

        var result = await _repository.AddAsync(escalationMatrixLevel);

        Assert.NotNull(result);
        Assert.Equal(escalationMatrixLevel.EscLevName, result.EscLevName);
        Assert.Single(_dbContext.EscalationMatrixLevels);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEscalationMatrixLevelIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEscalationMatrixLevel_WhenValidEscalationMatrixLevel()
    {
        _dbContext.EscalationMatrixLevels.Add(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = "Updated Level Name";
        var result = await _repository.UpdateAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);

        Assert.NotNull(result);
        Assert.Equal("Updated Level Name", result.EscLevName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEscalationMatrixLevelIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region IsEscalationMatrixLevelNameExist Tests

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        var levelName = "Level 1";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, null);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        var nonExistentName = "Non Existent Level";

        var result = await _repository.IsEscalationMatrixLevelNameExist(nonExistentName, null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        var existingId = Guid.NewGuid().ToString();
        var levelName = "Level 2";

        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.ReferenceId = existingId;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, existingId);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        var testId = Guid.NewGuid().ToString();
        var levelName = "Level 3";

        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, testId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsTrue_WhenNameExists_WithInvalidGuid()
    {
        var invalidId = "not-a-valid-guid";
        var levelName = "Level 4";

        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, invalidId);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_ReturnsFalse_WhenNameExistsButInactive_WithoutId()
    {
        var levelName = "Inactive Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = false;
        _dbContext.EscalationMatrixLevels.UpdateRange(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_IsCaseSensitive()
    {
        var levelName = "Level 5";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist("LEVEL 5", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_HandlesEmptyString()
    {
        var result = await _repository.IsEscalationMatrixLevelNameExist("", null);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameExist_HandlesEmptyId()
    {
        var levelName = "Test Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameExist(levelName, "");

        Assert.True(result);
    }

    #endregion

    #region IsEscalationMatrixLevelNameUnique Tests

    [Fact]
    public async Task IsEscalationMatrixLevelNameUnique_ReturnsTrue_WhenNameExists()
    {
        var levelName = "Unique Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameUnique(levelName);

        Assert.True(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        var nonExistentName = "Non Existent Level";

        var result = await _repository.IsEscalationMatrixLevelNameUnique(nonExistentName);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameUnique_ReturnsFalse_WhenNameExistsButInactive()
    {
        var levelName = "Inactive Unique Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
  

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = false;
        _dbContext.EscalationMatrixLevels.UpdateRange(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.IsEscalationMatrixLevelNameUnique(levelName);

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameUnique_IsCaseSensitive()
    {
        var levelName = "Case Sensitive Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.EscLevName = levelName;
        _escalationMatrixLevelFixture.EscalationMatrixLevelDto.IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.IsEscalationMatrixLevelNameUnique("CASE SENSITIVE LEVEL");

        Assert.False(result);
    }

    [Fact]
    public async Task IsEscalationMatrixLevelNameUnique_HandlesEmptyString()
    {
        var result = await _repository.IsEscalationMatrixLevelNameUnique("");

        Assert.False(result);
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldMarkAsInactive_WhenEscalationMatrixLevelExists()
    {
        _dbContext.EscalationMatrixLevels.Add(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.DeleteAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelDto);
        var retrieved = await _repository.ListAllAsync();
        Assert.True(retrieved.Count==0);
        
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEscalationMatrixLevelIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilterAsync_ReturnsMatchingEscalationMatrixLevels_WhenFilterMatches()
    {
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[0].EscLevName = "Critical Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].EscLevName = "Warning Level";
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[2].EscLevName = "Critical Alert";

        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.EscLevName.Contains("Critical"));

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains("Critical", x.EscLevName));
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsEmpty_WhenNoMatches()
    {
        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.FindByFilterAsync(e => e.EscLevName == "Non Existent Level");

        Assert.Empty(result);
    }

    [Fact]
    public async Task FindByFilterAsync_ReturnsOnlyActiveRecords_WhenFilteringByActiveStatus()
    {
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[0].IsActive = true;
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[2].IsActive = true;

        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].IsActive = false;
        _dbContext.EscalationMatrixLevels.UpdateRange(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.FindByFilterAsync(e => e.IsActive);

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region QueryAll Tests


    //[Fact]
    //public async Task QueryAll_WithFilter_ReturnsFilteredResults()
    //{
    //    _escalationMatrixLevelFixture.EscalationMatrixLevelList[0].EscLevName = "Test Level 1";
    //    _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].EscLevName = "Test Level 2";
    //    _escalationMatrixLevelFixture.EscalationMatrixLevelList[2].EscLevName = "Other Level";
     
    //    await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
    //    await _dbContext.SaveChangesAsync();

    //    var result = _repository.QueryAll(e => e.EscLevName.StartsWith("Test")).ToList();

    //    Assert.Equal(2, result.Count);
    //    Assert.All(result, x => Assert.StartsWith("Test", x.EscLevName));
    //}

    #endregion

    #region FilterBy Tests

    [Fact]
    public async Task FilterBy_ReturnsFilteredQueryable_WhenCalled()
    {
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[0].EscalationTime = "30";
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[1].EscalationTime = "60";
        _escalationMatrixLevelFixture.EscalationMatrixLevelList[2].EscalationTime = "30";

        await _dbContext.EscalationMatrixLevels.AddRangeAsync(_escalationMatrixLevelFixture.EscalationMatrixLevelList);
        await _dbContext.SaveChangesAsync();

        var result = _repository.FilterBy(e => e.EscalationTime == "30").ToList();

        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("30", x.EscalationTime));
    }

    #endregion
}
