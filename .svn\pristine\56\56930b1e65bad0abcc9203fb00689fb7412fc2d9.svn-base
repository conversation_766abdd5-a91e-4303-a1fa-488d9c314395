﻿using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport;

public class GetRPOSLAReportQueryHandler : IRequestHandler<GetRPOSLAReportQuery, object>
{
    private readonly IDb2HadrMonitorLogRepository _dB2HADRMonitorLogRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorLogRepository _mongoDBMonitorLogRepository;
    private readonly IMssqlAlwaysOnMonitorLogsRepository _mssqlAlwaysOnMonitorLogsRepository;
    private readonly IMsSqlDbMirroringLogRepository _mSSQLDBMirroringLogRepository;
    private readonly IMssqlMonitorLogsRepository _mSSQLMonitorLogsRepository;
    private readonly IMssqlNativeLogShippingMonitorLogRepository _mssqlNativeLogShippingMonitorLogRepository;
    private readonly IMysqlMonitorLogsRepository _mYSQLMonitorLogsRepository;
    private readonly IOracleMonitorLogsRepository _oracleMonitorLogsRepository;
    private readonly IOracleRacMonitorLogsRepository _oracleRACMonitorLogsRepository;
    private readonly IPostgresMonitorLogsRepository _postgresMonitorLogsRepository;
    private readonly IPublisher _publisher;
    private readonly IRoboCopyMonitoringLogsRepository _roboCopyMonitoringLogsRepository;
    private readonly IRsyncMonitorLogRepository _rsyncMonitorLogRepository;
    private readonly ISRMMonitorLogRepository _srmMonitorLogRepository;
    private readonly ISVCGMMonitorLogRepository _sVCGMMonitorLogRepository;
    private readonly IAzureStorageAccountMonitorLogsRepository _azureStorageAccountMonitorLogsRepository;
    private readonly IActiveDirectoryMonitorLogRepository _activeDirectoryMonitorLogsRepository;
    private readonly IDataSyncMonitorLogsRepository _dataSyncMonitorLogsRepository;
    private readonly IFastCopyMonitorLogsRepository _fastCopyMonitorLogsRepository;
    private readonly IZertoVpgMonitorLogsRepository _zertoVpgMonitorLogsRepository;
    private readonly ISybaseRSHADRMonitorLogsRepository _sybaseRSHADRMonitorLogsRepository;

    public GetRPOSLAReportQueryHandler(IMapper mapper, IMysqlMonitorLogsRepository mYSQLMonitorLogsRepository,
        IMssqlMonitorLogsRepository mSSQLMonitorLogsRepository,
        IOracleMonitorLogsRepository oracleMonitorLogsRepository,
        IPostgresMonitorLogsRepository postgresMonitorLogsRepository,
        IOracleRacMonitorLogsRepository oracleRACMonitorLogsRepository,
        IMssqlAlwaysOnMonitorLogsRepository mssqlAlwaysOnMonitorLogsRepository,
        IDb2HadrMonitorLogRepository dB2HADRMonitorLogRepository,
        IMsSqlDbMirroringLogRepository mSSQLDBMirroringLogRepository,
        IMongoDbMonitorLogRepository mongoDBMonitorLogRepository, ISVCGMMonitorLogRepository sVCGMMonitorLogRepository,
        IMssqlNativeLogShippingMonitorLogRepository mssqlNativeLogShippingMonitorLogRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher, IInfraObjectRepository infraObjectRepository,
        IRoboCopyMonitoringLogsRepository roboCopyMonitoringLogsRepository,
        IRsyncMonitorLogRepository rsyncMonitorLogRepository, ISRMMonitorLogRepository srmMonitorLogRepository, IAzureStorageAccountMonitorLogsRepository azureStorageAccountMonitorLogsRepository,
        IActiveDirectoryMonitorLogRepository activeDirectoryMonitorLogsRepository, 
        IDataSyncMonitorLogsRepository dataSyncMonitorLogsRepository,
        IFastCopyMonitorLogsRepository fastCopyMonitorLogsRepository, IZertoVpgMonitorLogsRepository zertoVpgMonitorLogsRepository,
        ISybaseRSHADRMonitorLogsRepository sybaseRSHADRMonitorLogsRepository)
    {
        _mapper = mapper;
        _mYSQLMonitorLogsRepository = mYSQLMonitorLogsRepository;
        _mSSQLMonitorLogsRepository = mSSQLMonitorLogsRepository;
        _oracleMonitorLogsRepository = oracleMonitorLogsRepository;
        _postgresMonitorLogsRepository = postgresMonitorLogsRepository;
        _oracleRACMonitorLogsRepository = oracleRACMonitorLogsRepository;
        _mssqlAlwaysOnMonitorLogsRepository = mssqlAlwaysOnMonitorLogsRepository;
        _dB2HADRMonitorLogRepository = dB2HADRMonitorLogRepository;
        _sVCGMMonitorLogRepository = sVCGMMonitorLogRepository;
        _mSSQLDBMirroringLogRepository = mSSQLDBMirroringLogRepository;
        _mongoDBMonitorLogRepository = mongoDBMonitorLogRepository;
        _mssqlNativeLogShippingMonitorLogRepository = mssqlNativeLogShippingMonitorLogRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
        _infraObjectRepository = infraObjectRepository;
        _roboCopyMonitoringLogsRepository = roboCopyMonitoringLogsRepository;
        _rsyncMonitorLogRepository = rsyncMonitorLogRepository;
        _srmMonitorLogRepository = srmMonitorLogRepository;
        _azureStorageAccountMonitorLogsRepository = azureStorageAccountMonitorLogsRepository;
        _activeDirectoryMonitorLogsRepository = activeDirectoryMonitorLogsRepository;
        _dataSyncMonitorLogsRepository = dataSyncMonitorLogsRepository;
        _fastCopyMonitorLogsRepository = fastCopyMonitorLogsRepository;
        _zertoVpgMonitorLogsRepository = zertoVpgMonitorLogsRepository;
        _sybaseRSHADRMonitorLogsRepository = sybaseRSHADRMonitorLogsRepository;
    }

    public async Task<object> Handle(GetRPOSLAReportQuery request, CancellationToken cancellationToken)
    {
        if (request.Type.Trim().ToLower().Equals("mssql"))
        {
            var mssql = await _mSSQLMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mssqlList = mssql.OrderBy(x => x.CreatedDate).ToList();

            if (mssql.Count == 0) throw new InvalidException("No Data Found");
            mssql.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLABusinessServiceDetails>(mssql.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.MSSQLMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.MSSQLMonitorLogs), request.InfraObjectId));

            var mssqlDto = _mapper.Map<List<GetRPOSLAReportVm>>(mssqlList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mssqlDto.Count;
            infraMapping.DataLagExceededCount = mssqlDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mssqlDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = mssqlDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLAReportVms = mssqlDto;

            return infraMapping;
        }


        if (request.Type.Trim().ToLower().Equals("svc"))
        {
            var svcGM = await _sVCGMMonitorLogRepository.GetSvcgmMonitorLogsByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var svcGMList = svcGM.OrderBy(x => x.CreatedDate).ToList();

            if (svcGM.Count == 0) throw new InvalidException("No Data Found");

            svcGM.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLASVCGMBusinessServiceDetails>(svcGM.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(SVCGMMonitorLog),
                new NotFoundException(nameof(SVCGMMonitorLog), request.InfraObjectId));

            var svcGMDto = _mapper.Map<List<GetRPOSLASVCGMReportVm>>(svcGMList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = svcGMDto.Count;
            infraMapping.DataLagExceededCount = svcGMDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = svcGMDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = svcGMDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLASVCGMReportsVms = svcGMDto;

            return infraMapping;
        }

        //Completed
        if (request.Type.Trim().ToLower().Equals("mysql"))
        {
            var mysql = await _mYSQLMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mysqlList = mysql.OrderBy(x => x.CreatedDate).ToList();

            if (mysql.Count == 0) throw new InvalidException("No Data Found");
            mysql.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLAMySqlBusinessServiceDetails>(mysql.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.MYSQLMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.MYSQLMonitorLogs), request.InfraObjectId));

            var mysqlDto = _mapper.Map<List<GetRPOSLAMySqlReportVm>>(mysqlList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mysqlDto.Count;
            infraMapping.DataLagExceededCount = mysqlDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mysqlDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = mysqlDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLAMySqlReportVms = mysqlDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("oracle"))
        {
            var oracle = await _oracleMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var oracleList = oracle.OrderBy(x => x.CreatedDate).ToList();

            if (oracle.Count == 0) throw new InvalidException("No Data Found");
            oracle.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetOracleRPOSLABusinessServiceDetails>(oracle.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.OracleMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.OracleMonitorLogs), request.InfraObjectId));

            var oracleDto = _mapper.Map<List<GetOracleRPOSLAReportVm>>(oracleList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = oracleDto.Count;
            infraMapping.DataLagExceededCount = oracleDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = oracleDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = oracleDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetOracleRPOSLAReportVms = oracleDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("postgres"))
        {
            var postgres = await _postgresMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var postgresList = postgres.OrderBy(x => x.CreatedDate).ToList();

            if (postgres.Count == 0) throw new InvalidException("No Data Found");

            postgres.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLAPostgresBusinessServiceDetails>(postgres.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.PostgresMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.PostgresMonitorLogs), request.InfraObjectId));

            var postgresDto = _mapper.Map<List<GetRPOSLAPostgresReportVm>>(postgresList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = postgresDto.Count;
            infraMapping.DataLagExceededCount = postgresDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = postgresDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = postgresDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLAPostgresReportVms = postgresDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("oraclerac"))
        {
            var oracleRAC = await _oracleRACMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);//oraclerac_monitor_logs table values

            var oracleRACList = oracleRAC.OrderBy(x => x.CreatedDate).ToList();

            if (oracleRAC.Count == 0) throw new InvalidException("No Data Found");

            oracleRAC.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);//getting infraobject details from infraobject table

            var infraMapping = _mapper.Map<GetOracleRacRPOSLABusinessServiceDetails>(oracleRAC.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.OracleRACMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.OracleRACMonitorLogs), request.InfraObjectId));

            var oracleRACDto = _mapper.Map<List<GetOracleRacRPOSLAReportVm>>(oracleRACList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = oracleRACDto.Count;
            infraMapping.DataLagExceededCount = oracleRACDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = oracleRACDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = oracleRACDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetOracleRacRPOSLAReportVms = oracleRACDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("mssqlalwayson"))
        {
            var mssqlAlwaysOn = await _mssqlAlwaysOnMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mssqlAlwaysOnList = mssqlAlwaysOn.OrderBy(x => x.CreatedDate).ToList();

            if (mssqlAlwaysOn.Count == 0) throw new InvalidException("No Data Found");

            mssqlAlwaysOn.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetMSSQLAlwaysOnBusinessServiceDetails>(mssqlAlwaysOn.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.MSSQLAlwaysOnMonitorLogs),
                new NotFoundException(nameof(Domain.Entities.MSSQLAlwaysOnMonitorLogs), request.InfraObjectId));

            var mssqlAlwaysOnDto = _mapper.Map<List<GetMSSQLAlwaysOnRPOSLAReportVm>>(mssqlAlwaysOnList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mssqlAlwaysOnDto.Count;
            infraMapping.DataLagExceededCount = mssqlAlwaysOnDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mssqlAlwaysOnDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount =
                mssqlAlwaysOnDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetMSSQLAlwaysOnRPOSLAReportVms = mssqlAlwaysOnDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("db2hadr"))
        {
            var db2hadr = await _dB2HADRMonitorLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var db2hadrList = db2hadr.OrderBy(x => x.CreatedDate).ToList();

            if (db2hadr.Count == 0) throw new InvalidException("No Data Found");

            db2hadr.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLADB2HADRBusinessServiceDetails>(db2hadr.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.DB2HADRMonitorLog),
                new NotFoundException(nameof(Domain.Entities.DB2HADRMonitorLog), request.InfraObjectId));

            var db2hadrDto = _mapper.Map<List<GetRPOSLADB2HADRReportVm>>(db2hadrList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = db2hadrDto.Count;
            infraMapping.DataLagExceededCount = db2hadrDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = db2hadrDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = db2hadrDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLADB2HADRReportVms = db2hadrDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("mongodb"))
        {
            var mongoDB = await _mongoDBMonitorLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mongoDBList = mongoDB.OrderBy(x => x.CreatedDate).ToList();

            if (mongoDB.Count == 0) throw new InvalidException("No Data Found");

            mongoDB.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetMongoDBRPOSLABusinessServiceDetails>(mongoDB.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.MongoDBMonitorLog),
                new NotFoundException(nameof(Domain.Entities.MongoDBMonitorLog), request.InfraObjectId));

            var mongoDBDto = _mapper.Map<List<GetRPOSLAReportMongoDBVm>>(mongoDBList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mongoDBDto.Count;
            infraMapping.DataLagExceededCount = mongoDBDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mongoDBDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = mongoDBDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLAReportMongoDBVms = mongoDBDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("mssqldbmirroring"))
        {
            var mssqlDBMirroring = await _mSSQLDBMirroringLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mssqlDBMirroringList = mssqlDBMirroring.OrderBy(x => x.CreatedDate).ToList();

            if (mssqlDBMirroring.Count == 0) throw new InvalidException("No Data Found");

            mssqlDBMirroring.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping =
                _mapper.Map<GetMSSQLDBMirroringRPOSLABusinessServiceDetails>(mssqlDBMirroring.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(MSSQLDBMirroringLogs),
                new NotFoundException(nameof(MSSQLDBMirroringLogs), request.InfraObjectId));

            var mssqlDBMirroringDto = _mapper.Map<List<GetMSSQLDBMirroringRPOSLAReportVm>>(mssqlDBMirroringList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mssqlDBMirroringDto.Count;
            infraMapping.DataLagExceededCount = mssqlDBMirroringDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mssqlDBMirroringDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount =
                mssqlDBMirroringDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetMSSQLDBMirroringRPOSLAReportVms = mssqlDBMirroringDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("robocopy"))
        {
            var roboCopy = await _roboCopyMonitoringLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var roboCopyList = roboCopy.OrderBy(x => x.CreatedDate).ToList();

            if (roboCopy.Count == 0) throw new InvalidException("No Data Found");

            roboCopy.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLARoboCopyBusinessServiceDetails>(roboCopy.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(RoboCopyMonitorLogs),
                new NotFoundException(nameof(RoboCopyMonitorLogs), request.InfraObjectId));

            var roboCopyDtl = new List<GetRPOSLARoboCopyReportVm>();

            var srNo = 1;

            foreach (var robo in roboCopyList)
            {
                var json = robo.Properties;

                var jsonObject = JObject.Parse(json);

                var arrayToken = jsonObject.SelectToken("RoboCopyMonitoringModels") as JArray;

                if (arrayToken != null && arrayToken.Count > 0)
                    foreach (var data in arrayToken)
                    {
                        var robocopyArrayToken = data.SelectToken("MonitoringModel.RoboCopyMonitorModel") as JArray;

                        if (robocopyArrayToken != null && robocopyArrayToken.Count > 0)
                            foreach (var robocopy in robocopyArrayToken)
                            {
                                var report = new GetRPOSLARoboCopyReportVm
                                {
                                    SrNo = srNo++,
                                    Id = robo.ReferenceId,
                                    SourceIP = string.IsNullOrEmpty(robocopy.SelectToken("SourceIP")?.ToString())
                                        ? "NA"
                                        : robocopy.SelectToken("SourceIP")?.ToString(),
                                    DestinationIP =
                                        string.IsNullOrEmpty(robocopy.SelectToken("DestinationIP")?.ToString())
                                            ? "NA"
                                            : robocopy.SelectToken("DestinationIP")?.ToString(),
                                    SourcePath = string.IsNullOrEmpty(robocopy.SelectToken("SourcePath")?.ToString())
                                        ? "NA"
                                        : robocopy.SelectToken("SourcePath")?.ToString(),
                                    DestinationPath =
                                        string.IsNullOrEmpty(robocopy.SelectToken("DestinationPath")?.ToString())
                                            ? "NA"
                                            : robocopy.SelectToken("DestinationPath")?.ToString(),
                                    RepStartTime =
                                        string.IsNullOrEmpty(robocopy.SelectToken("RepStartTime")?.ToString())
                                            ? "NA"
                                            : robocopy.SelectToken("RepStartTime")?.ToString(),
                                    RepEndTime = string.IsNullOrEmpty(robocopy.SelectToken("RepEndTime")?.ToString())
                                        ? "NA"
                                        : robocopy.SelectToken("RepEndTime")?.ToString(),
                                    RepFileCount =
                                        string.IsNullOrEmpty(robocopy.SelectToken("TotalFilesCopiedCount")?.ToString())
                                            ? "NA"
                                            : robocopy.SelectToken("TotalFilesCopiedCount")?.ToString(),
                                    RepFileSize =
                                        string.IsNullOrEmpty(robocopy.SelectToken("TotalBytesCopiedCount")?.ToString())
                                            ? "NA"
                                            : robocopy.SelectToken("TotalBytesCopiedCount")?.ToString(),
                                    ConfigureRPO = robo.ConfiguredRPO,
                                    Threshold= robo.Threshold,
                                    Properties = json,
                                    IsDataLagExceeded = CalculateDataLag(robo),
                                    IsThresholdExceeded = CalculateThreshold(robo),
                                    Datalag = robo.DataLagValue
                                };
                                roboCopyDtl.Add(report);
                            }
                    }
            }

            var roboCopyDto = _mapper.Map<List<GetRPOSLARoboCopyReportVm>>(roboCopyDtl);

            GetJsonProperties.ClearAutoIncrement();

            if (roboCopyDto.Count == 0) throw new InvalidException("No Data Found");

            infraMapping.TotalCount = roboCopyDto.Count;
            infraMapping.DataLagExceededCount = roboCopyDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = roboCopyDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = roboCopyDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLARoboCopyReportVms = roboCopyDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("rsyncappreplication") ||
            request.Type.Trim().ToLower().Equals("rsyncdbreplication"))
        {
            var rsyncData = await _rsyncMonitorLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var rsyncList = rsyncData.OrderBy(x => x.CreatedDate).ToList();

            if (rsyncData.Count == 0) throw new InvalidException("No Data Found");

            rsyncData.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLARSyncBusinessServiceDetails>(rsyncData.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(RsyncMonitorLog),
                new NotFoundException(nameof(RsyncMonitorLog), request.InfraObjectId));

            var rSyncDtl = new List<GetRsyncRPOSLAReportVm>();

            var srNo = 1;

            foreach (var rsync in rsyncList)
            {
                var json = rsync.Properties;

                var jsonObject = JObject.Parse(json);

                var arrayToken = jsonObject.SelectToken("RSyncReplicationModels") as JArray;

                if (arrayToken != null && arrayToken.Count > 0)
                    foreach (var item in arrayToken)
                    {
                        var rsyncArrayToken = item.SelectToken("MonitoringModel.RSyncMonitorModel") as JArray;

                        if (rsyncArrayToken != null && rsyncArrayToken.Count > 0)
                            foreach (var data in rsyncArrayToken)
                            {
                                var report = new GetRsyncRPOSLAReportVm
                                {
                                    SrNo = srNo++,
                                    Id = rsync.ReferenceId,
                                    SourceIP = data.SelectToken("SourceIP")?.ToString() ?? "NA",
                                    DestinationIP = data.SelectToken("DestinationIP")?.ToString() ?? "NA",
                                    SourcePath = data.SelectToken("SourcePath")?.ToString() ?? "NA",
                                    DestinationPath = data.SelectToken("DestinationPath")?.ToString() ?? "NA",
                                    TotalFileSize = data.SelectToken("TotalFilesSize")?.ToString() ?? "NA",
                                    IncrementalFilesCount =
                                        data.SelectToken("NumberOfRegFilesTransfer")?.ToString() ?? "NA",
                                    ReplicationFilesCount = data.SelectToken("TotalNumberoffiles")?.ToString() ?? "NA",
                                    Datalag = item.SelectToken("MonitoringModel.PR_Datalag")?.ToString() ?? "NA",
                                    TimeStamp = data.SelectToken("LastSuccessfullReplTime")?.ToString() ?? "NA",
                                    StartTime = data.SelectToken("RepStartTime")?.ToString() ?? "NA",
                                    EndTime = data.SelectToken("LastSuccessfullReplTime")?.ToString() ?? "NA",
                                    ConfigureRPO = rsync.ConfiguredRPO,
                                    Threshold= rsync.Threshold,
                                    Properties = json,
                                    IsDataLagExceeded = CalculateDataLag(rsync),
                                    IsThresholdExceeded = CalculateThreshold(rsync)
                                };
                                rSyncDtl.Add(report);
                            }
                    }
            }

            var rsyncDto = _mapper.Map<List<GetRsyncRPOSLAReportVm>>(rSyncDtl);

            GetJsonProperties.ClearAutoIncrement();

            if (rsyncDto.Count == 0) throw new InvalidException("No Data Found");

            infraMapping.TotalCount = rsyncDto.Count;
            infraMapping.DataLagExceededCount = rsyncDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = rsyncDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = rsyncDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRsyncRPOSLAReportVms = rsyncDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("mssqlnls"))
        {
            var mssqlNLS = await _mssqlNativeLogShippingMonitorLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var mssqlNLSList = mssqlNLS.OrderBy(x => x.CreatedDate).ToList();

            if (mssqlNLS.Count == 0) throw new InvalidException("No Data Found");

            mssqlNLS.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetMSSqlNLSBusinessServiceDetails>(mssqlNLS.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.MssqlNativeLogShippingMonitorLog),
                new NotFoundException(nameof(Domain.Entities.MssqlNativeLogShippingMonitorLog), request.InfraObjectId));

            var mssqlNLSDto = _mapper.Map<List<GetMSSqlNLSRPOSLAReportVm>>(mssqlNLSList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = mssqlNLSDto.Count;
            infraMapping.DataLagExceededCount = mssqlNLSDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = mssqlNLSDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = mssqlNLSDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetMSSqlNLSRPOSLAReportVms = mssqlNLSDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("srm"))
        {
            var srm = await _srmMonitorLogRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var srmList = srm.OrderBy(x => x.CreatedDate).ToList();

            if (srm.Count == 0) throw new InvalidException("No Data Found");

            srm.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLASRMBusinessServiceDetails>(srm.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(SRMMonitorLog),
                new NotFoundException(nameof(SRMMonitorLog), request.InfraObjectId));

            var srmDto = _mapper.Map<List<GetRPOSLASRMReportVM>>(srmList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = srmDto.Count;
            infraMapping.DataLagExceededCount = srmDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = srmDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = srmDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLASRMReportVM = srmDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("azurestorageaccount"))
        {
            var azurestorageaccount = await _azureStorageAccountMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var azurestorageaccountList = azurestorageaccount.OrderBy(x => x.CreatedDate).ToList();

            if (azurestorageaccount.Count == 0) throw new InvalidException("No Data Found");
            azurestorageaccount.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLAAzureStorageAccountBusinessServiceDetails>(azurestorageaccount.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.AzureStorageAccountMonitorlogs),
                new NotFoundException(nameof(Domain.Entities.AzureStorageAccountMonitorlogs), request.InfraObjectId));

            var azurestorageaccountDto = _mapper.Map<List<GetRPOSLAAzureStorageAccountReportVm>>(azurestorageaccountList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = azurestorageaccountDto.Count;
            infraMapping.DataLagExceededCount = azurestorageaccountDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = azurestorageaccountDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = azurestorageaccountDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetRPOSLAAzureStorageAccountReportVms = azurestorageaccountDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("activedirectory") || request.Type.Trim().ToLower().Equals("windowsactivedirectory"))
        {
            var activeDirectoryMonitorLogs = await _activeDirectoryMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);
            var activeDirectoryList = activeDirectoryMonitorLogs.OrderBy(x => x.CreatedDate).ToList();

            if (activeDirectoryMonitorLogs.Count == 0) throw new InvalidException("No Data Found");

            activeDirectoryMonitorLogs.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetActiveDirectoryBusinessServiceDetails>(activeDirectoryMonitorLogs.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.ActiveDirectoryMonitorLog),
                new NotFoundException(nameof(Domain.Entities.ActiveDirectoryMonitorLog), request.InfraObjectId));

            var activeDirectoryDto = _mapper.Map<List<GetActiveDirectoryRPOSLAReportVm>>(activeDirectoryList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = activeDirectoryDto.Count;
            infraMapping.DataLagExceededCount = activeDirectoryDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = activeDirectoryDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount =
                activeDirectoryDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetActiveDirectoryRPOSLAReportVms = activeDirectoryDto;

            return infraMapping;
        }

        if (request.Type.Trim().ToLower().Equals("datasyncappreplication"))
        {
            var dataSyncMonitorLogs = await _dataSyncMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var fastCopyMonitorLogs = await _fastCopyMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var dataSyncMonitorList = dataSyncMonitorLogs.OrderBy(x => x.CreatedDate).ToList();

            if (dataSyncMonitorLogs.Count == 0) throw new InvalidException("No Data Found");

            dataSyncMonitorLogs.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetDataSyncBusinessServiceDetails>(dataSyncMonitorLogs.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(Domain.Entities.DataSyncMonitorLog),
                new NotFoundException(nameof(Domain.Entities.DataSyncMonitorLog), request.InfraObjectId));

            GetJsonProperties.ClearAutoIncrement();

            var dataSyncDto = _mapper.Map<List<GetDataSyncRPOSLAReportVm>>(dataSyncMonitorList);

            GetJsonProperties.ClearAutoIncrement();

            var fastCopyDto = _mapper.Map<List<GetFastCopyMonitorVm>>(fastCopyMonitorLogs);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = dataSyncDto.Count;
            infraMapping.DataLagExceededCount = dataSyncDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = dataSyncDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = dataSyncDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.DateOption = request.DateOption;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = !string.IsNullOrEmpty(infraObject.BusinessServiceId)
                ? infraObject.BusinessServiceId
                : "NA";
            infraMapping.BusinessServiceName = !string.IsNullOrEmpty(infraObject.BusinessServiceName)
                ? infraObject.BusinessServiceName
                : "NA";
            infraMapping.GetDataSyncRPOSLAReportVms = dataSyncDto;
            infraMapping.GetFastCopyMonitorVms = fastCopyDto;

            return infraMapping;
        }
        if (request.Type.ToLower().Equals("zertovpg"))
        {
            var zertoVPG = await _zertoVpgMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var zertoVPGList = zertoVPG.OrderBy(x => x.CreatedBy).ToList();

            if (zertoVPG.Count == 0) throw new InvalidException("No data found");

            zertoVPG.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetRPOSLAZetroVpgBusinessServiceDetails>(zertoVPG.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(ZertoVpgMonitorLog),
                new NotFoundException(nameof(ZertoVpgMonitorLog), request.InfraObjectId));

            var zertoVpgDto = _mapper.Map<List<GetRPOSLAZertoVpgReportVm>>(zertoVPGList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = zertoVpgDto.Count;
            infraMapping.DataLagExceededCount = zertoVpgDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = zertoVpgDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = zertoVpgDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = infraObject.BusinessServiceId;
            infraMapping.BusinessServiceName = infraObject.BusinessServiceName;
            infraMapping.GetRPOSLAZertoVpgReportVms = zertoVpgDto;
            return infraMapping;


        }
        #region Sybase RS HADR 
        if (request.Type.ToLower().Equals("sybasershadr"))
        {
            var sybaseRSHADR = await _sybaseRSHADRMonitorLogsRepository.GetByInfraObjectId(request.InfraObjectId,
                request.ReportStartDate, request.ReportEndDate);

            var sybaseRSHADRList = sybaseRSHADR.OrderBy(x => x.CreatedBy).ToList();

            if (sybaseRSHADR.Count == 0) throw new InvalidException("No data found");

            sybaseRSHADR.ForEach(x => x.Properties = SecurityHelper.Decrypt(x.Properties));

            var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(request.InfraObjectId);

            var infraMapping = _mapper.Map<GetSybaseRSHADRBusinessServiceDetails>(sybaseRSHADR.LastOrDefault());

            Guard.Against.NullOrDeactive(infraMapping, nameof(SybaseRSHADRMonitorLog),
                new NotFoundException(nameof(SybaseRSHADRMonitorLog), request.InfraObjectId));

            var sybaseDto = _mapper.Map<List<GetRPOSLASybaseRSHADRReportVm>>(sybaseRSHADRList);

            GetJsonProperties.ClearAutoIncrement();

            infraMapping.TotalCount = sybaseDto.Count;
            infraMapping.DataLagExceededCount = sybaseDto.Count(x => x.IsDataLagExceeded);
            infraMapping.ThresholdExceededCount = sybaseDto.Count(x => x.IsThresholdExceeded);
            infraMapping.UnderThresholdCount = sybaseDto.Count(x => !x.IsThresholdExceeded && !x.IsDataLagExceeded);
            infraMapping.ReportGeneratedBy = _loggedInUserService.LoginName;
            infraMapping.FromDate = request.ReportStartDate;
            infraMapping.ToDate = request.ReportEndDate;
            infraMapping.InfraObjectType = request.Type;
            infraMapping.BusinessServiceId = infraObject.BusinessServiceId;
            infraMapping.BusinessServiceName = infraObject.BusinessServiceName;
            infraMapping.GetRPOSLASybaseRSHADRReportVms = sybaseDto;
            return infraMapping;
        }
        #endregion

        throw new InvalidException($"Unsupported type:{request.Type}");
    }

    private static bool CalculateDataLag<T>(T src)
    {
        try
        {
            var jsonString = JsonConvert.SerializeObject(src);

            var jsonObject = JObject.Parse(jsonString);

            var configuredRpo = jsonObject?.SelectToken("ConfiguredRPO")?.ToString();

            var dataLag = jsonObject?.SelectToken("DataLagValue")?.ToString();

            if (dataLag is null || dataLag.Equals("NA")) return false;
            var dataLagMinutesValue = GetJsonProperties.ConvertToMinutes(dataLag);

            if (dataLagMinutesValue > Convert.ToInt32(configuredRpo)) return true;
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private static bool CalculateThreshold<T>(T src)
    {
        try
        {
            var jsonString = JsonConvert.SerializeObject(src);

            var jsonObject = JObject.Parse(jsonString);

            var configuredRPO = jsonObject?.SelectToken("ConfiguredRPO")?.ToString();

            var threshold = jsonObject?.SelectToken("Threshold")?.ToString();

            var dataLag = jsonObject?.SelectToken("DataLagValue")?.ToString();

            var dataLagMinutesValue = GetJsonProperties.ConvertToMinutes(dataLag);

            if (dataLagMinutesValue > Convert.ToInt32(threshold) &&
                dataLagMinutesValue < Convert.ToInt32(configuredRPO)) return true;

            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }
}