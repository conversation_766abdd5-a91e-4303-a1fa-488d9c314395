using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReplicationJobRepositoryTests : IClassFixture<ReplicationJobFixture>, IDisposable
{
    private readonly ReplicationJobFixture _replicationJobFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReplicationJobRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ReplicationJobRepositoryTests(ReplicationJobFixture replicationJobFixture)
    {
        _replicationJobFixture = replicationJobFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationJobFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _repository = new ReplicationJobRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.ReplicationJobs.RemoveRange(_dbContext.ReplicationJobs);
        await _dbContext.SaveChangesAsync();
    }

    #region IsReplicationJobNameExist Tests

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", "ExistingJob");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", "NonExistentJob");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsNull()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist(null, "ExistingJob");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("", "ExistingJob");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnFalse_WhenNameExistsAndIdMatchesExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act - Using the same ID as the existing entity
        var result = await _repository.IsReplicationJobNameExist(existingJob.ReferenceId, "ExistingJob");

        // Assert
        Assert.False(result); // Should return false because it's the same entity
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnTrue_WhenNameExistsAndIdDoesNotMatchExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act - Using a different valid GUID
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReplicationJobNameExist(differentId, "ExistingJob");

        // Assert
        Assert.True(result); // Should return true because it's a different entity with same name
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnTrue_WhenMultipleEntitiesWithSameNameExist()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _replicationJobFixture.CreateReplicationJobsWithSameName("DuplicateName", 3);
        foreach (var job in jobs)
        {
            await _repository.AddAsync(job);
        }

        // Act - Using a different valid GUID
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReplicationJobNameExist(differentId, "DuplicateName");

        // Assert
        Assert.True(result); // Should return true because multiple entities exist (count > 1)
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnFalse_WhenOnlyOneEntityWithNameExistsAndIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("UniqueJob");
        await _repository.AddAsync(existingJob);

        // Act - Using the same ID as the existing entity
        var result = await _repository.IsReplicationJobNameExist(existingJob.ReferenceId, "UniqueJob");

        // Assert
        Assert.False(result); // Should return false because it's the same entity (count == 1 and ID matches)
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleCaseSensitiveNames()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("CaseSensitiveJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result1 = await _repository.IsReplicationJobNameExist("invalid-guid", "CaseSensitiveJob");
        var result2 = await _repository.IsReplicationJobNameExist("invalid-guid", "casesensitivejob");
        var result3 = await _repository.IsReplicationJobNameExist("invalid-guid", "CASESENSITIVEJOB");

        // Assert
        Assert.True(result1);  // Exact match should return true
        Assert.False(result2); // Different case should return false
        Assert.False(result3); // Different case should return false
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", null);

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", "");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldReturnFalse_WhenDatabaseIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", "AnyName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsReplicationJobNameUnique Tests

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique("ExistingJob");

        // Assert
        Assert.True(result); // Method returns true when name exists (opposite of unique)
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique("NonExistentJob");

        // Assert
        Assert.False(result); // Method returns false when name doesn't exist (is unique)
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldReturnTrue_WhenMultipleEntitiesWithSameNameExist()
    {
        // Arrange
        await ClearDatabase();
        var jobs = _replicationJobFixture.CreateReplicationJobsWithSameName("DuplicateName", 3);
        foreach (var job in jobs)
        {
            await _repository.AddAsync(job);
        }

        // Act
        var result = await _repository.IsReplicationJobNameUnique("DuplicateName");

        // Assert
        Assert.True(result); // Method returns true when name exists
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleCaseSensitiveNames()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("CaseSensitiveJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result1 = await _repository.IsReplicationJobNameUnique("CaseSensitiveJob");
        var result2 = await _repository.IsReplicationJobNameUnique("casesensitivejob");
        var result3 = await _repository.IsReplicationJobNameUnique("CASESENSITIVEJOB");

        // Assert
        Assert.True(result1);   // Exact match should return true
        Assert.False(result2);  // Different case should return false
        Assert.False(result3);  // Different case should return false
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique(null);

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("ExistingJob");
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique("");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldReturnFalse_WhenDatabaseIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationJobNameUnique("AnyName");

        // Assert
        Assert.False(result); // Should return false when no entities exist
    }

    #endregion

    #region Integration and Edge Case Tests

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var specialNames = new List<string>
        {
            "Job@#$%",
            "Job with spaces",
            "Job-with-dashes",
            "Job_with_underscores",
            "Job.with.dots",
            "Job123Numbers",
            "ÄÖÜäöü", // Unicode characters
            "Job\tWith\tTabs",
            "Job\nWith\nNewlines"
        };

        var jobs = _replicationJobFixture.CreateReplicationJobsWithDifferentNames(specialNames);
        foreach (var job in jobs)
        {
            await _repository.AddAsync(job);
        }

        // Act & Assert
        foreach (var name in specialNames)
        {
            var result = await _repository.IsReplicationJobNameExist("invalid-guid", name);
            Assert.True(result, $"Should find job with name: {name}");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var specialNames = new List<string>
        {
            "Job@#$%",
            "Job with spaces",
            "Job-with-dashes",
            "Job_with_underscores",
            "Job.with.dots"
        };

        var jobs = _replicationJobFixture.CreateReplicationJobsWithDifferentNames(specialNames);
        foreach (var job in jobs)
        {
            await _repository.AddAsync(job);
        }

        // Act & Assert
        foreach (var name in specialNames)
        {
            var result = await _repository.IsReplicationJobNameUnique(name);
            Assert.True(result, $"Should find job with name: {name}");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleLongNames()
    {
        // Arrange
        await ClearDatabase();
        var longName = new string('A', 500); // Very long name
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName(longName);
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameExist("invalid-guid", longName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleLongNames()
    {
        // Arrange
        await ClearDatabase();
        var longName = new string('B', 500); // Very long name
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName(longName);
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique(longName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleWhitespaceOnlyName()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceNames = new List<string> { " ", "  ", "\t", "\n", "\r\n", "   \t   " };

        foreach (var name in whitespaceNames)
        {
            var job = _replicationJobFixture.CreateReplicationJobWithSpecificName(name);
            await _repository.AddAsync(job);
        }

        // Act & Assert
        foreach (var name in whitespaceNames)
        {
            var result = await _repository.IsReplicationJobNameExist("invalid-guid", name);
            Assert.True(result, $"Should find job with whitespace name: '{name}'");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleWhitespaceOnlyName()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceName = "   ";
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName(whitespaceName);
        await _repository.AddAsync(existingJob);

        // Act
        var result = await _repository.IsReplicationJobNameUnique(whitespaceName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName("TestJob");
        await _repository.AddAsync(existingJob);

        var validGuids = new List<string>
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString().ToUpper(),
            Guid.NewGuid().ToString().ToLower(),
            "{" + Guid.NewGuid().ToString() + "}",
            "(" + Guid.NewGuid().ToString() + ")",
            Guid.NewGuid().ToString("N"), // No hyphens
            Guid.NewGuid().ToString("D"), // With hyphens
            Guid.NewGuid().ToString("B"), // With braces
            Guid.NewGuid().ToString("P")  // With parentheses
        };

        var invalidGuids = new List<string>
        {
            "not-a-guid",
            "12345",
            "",
            null,
            "00000000-0000-0000-0000-000000000000", // All zeros (valid GUID but edge case)
            "invalid-guid-format-too-long-to-be-valid",
            "123e4567-e89b-12d3-a456-42661417400", // Missing one character
            "123e4567-e89b-12d3-a456-4266141740000" // Extra character
        };

        // Act & Assert - Valid GUIDs should use Unique logic
        foreach (var guid in validGuids)
        {
            var result = await _repository.IsReplicationJobNameExist(guid, "TestJob");
            Assert.True(result, $"Should return true for valid GUID: {guid}");
        }

        // Act & Assert - Invalid GUIDs should use Any logic
        foreach (var invalidGuid in invalidGuids)
        {
            var result = await _repository.IsReplicationJobNameExist(invalidGuid, "TestJob");
            Assert.True(result, $"Should return true for invalid GUID: {invalidGuid}");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var baseName = "ConcurrentJob";
        var tasks = new List<Task>();

        // Act - Add multiple jobs concurrently
        for (int i = 0; i < 10; i++)
        {
            var jobName = $"{baseName}_{i}";
            var job = _replicationJobFixture.CreateReplicationJobWithSpecificName(jobName);
            tasks.Add(_repository.AddAsync(job));
        }

        await Task.WhenAll(tasks);

        // Assert - Check that all jobs can be found
        for (int i = 0; i < 10; i++)
        {
            var jobName = $"{baseName}_{i}";
            var result = await _repository.IsReplicationJobNameExist("invalid-guid", jobName);
            Assert.True(result, $"Should find job: {jobName}");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameUnique_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var baseName = "ConcurrentUniqueJob";
        var tasks = new List<Task>();

        // Act - Add multiple jobs concurrently
        for (int i = 0; i < 10; i++)
        {
            var jobName = $"{baseName}_{i}";
            var job = _replicationJobFixture.CreateReplicationJobWithSpecificName(jobName);
            tasks.Add(_repository.AddAsync(job));
        }

        await Task.WhenAll(tasks);

        // Assert - Check that all jobs can be found
        for (int i = 0; i < 10; i++)
        {
            var jobName = $"{baseName}_{i}";
            var result = await _repository.IsReplicationJobNameUnique(jobName);
            Assert.True(result, $"Should find job: {jobName}");
        }
    }

    [Fact]
    public async Task IsReplicationJobNameExist_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var jobs = new List<ReplicationJob>();

        // Create 100 jobs with different names
        for (int i = 0; i < 100; i++)
        {
            var job = _replicationJobFixture.CreateReplicationJobWithSpecificName($"LargeDatasetJob_{i:D3}");
            jobs.Add(job);
        }

        // Add all jobs to database
        foreach (var job in jobs)
        {
            await _repository.AddAsync(job);
        }

        // Act & Assert - Test random samples
        var random = new Random();
        for (int i = 0; i < 20; i++)
        {
            var randomIndex = random.Next(0, 100);
            var jobName = $"LargeDatasetJob_{randomIndex:D3}";

            var result = await _repository.IsReplicationJobNameExist("invalid-guid", jobName);
            Assert.True(result, $"Should find job: {jobName}");
        }

        // Test non-existent job
        var nonExistentResult = await _repository.IsReplicationJobNameExist("invalid-guid", "NonExistentJob");
        Assert.False(nonExistentResult);
    }

    [Fact]
    public async Task BothMethods_ShouldWorkTogetherCorrectly()
    {
        // Arrange
        await ClearDatabase();
        var testName = "IntegrationTestJob";
        var existingJob = _replicationJobFixture.CreateReplicationJobWithSpecificName(testName);
        await _repository.AddAsync(existingJob);

        // Act
        var existsWithInvalidId = await _repository.IsReplicationJobNameExist("invalid-guid", testName);
        var existsWithSameId = await _repository.IsReplicationJobNameExist(existingJob.ReferenceId, testName);
        var existsWithDifferentId = await _repository.IsReplicationJobNameExist(Guid.NewGuid().ToString(), testName);
        var isUnique = await _repository.IsReplicationJobNameUnique(testName);

        // Assert
        Assert.True(existsWithInvalidId, "Should return true when ID is invalid");
        Assert.False(existsWithSameId, "Should return false when ID matches existing entity");
        Assert.True(existsWithDifferentId, "Should return true when ID is different from existing entity");
        Assert.True(isUnique, "IsReplicationJobNameUnique should return true when name exists");
    }

    #endregion
}
