﻿using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyLog.Queries;

public class GetDrReadyLogListQueryHandlerTests : IClassFixture<DrReadyLogFixture>
{
    private readonly DrReadyLogFixture _drReadyLogFixture;

    private Mock<IDrReadyLogRepository> _drReadyLogRepositoryMock;

    private readonly GetDRReadyLogListQueryHandler _handler;

    public GetDrReadyLogListQueryHandlerTests(DrReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
    
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.GetDrReadyLogRepository(_drReadyLogFixture.DrReadyLogs);
        
        _handler = new GetDRReadyLogListQueryHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_DrReadyLogCount()
    {
        var result = await _handler.Handle(new GetDRReadyLogListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DRReadyLogListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_ValidDrReadyLogList()
    {
        var result = await _handler.Handle(new GetDRReadyLogListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DRReadyLogListVm>>();

        result.Count.ShouldBe(_drReadyLogFixture.DrReadyLogs.Count);

        result[0].Id.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ReferenceId);
        result[0].UserId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].UserId);
        result[0].BusinessServiceId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessServiceName);
        result[0].BusinessFunctionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionId);
        result[0].BusinessFunctionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].BusinessFunctionName);
        result[0].IsProtected.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].IsProtected);
        result[0].AffectedInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedInfra);
        result[0].ActiveInfra.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveInfra);
        result[0].WorkflowId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowName);
        result[0].WorkflowStatus.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowStatus);
        result[0].FailedActionName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionName);
        result[0].FailedActionId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].FailedActionId);
        result[0].ActiveBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ActiveBusinessFunction);
        result[0].AffectedBusinessFunction.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].AffectedBusinessFunction);
        result[0].DRReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].DRReady);
        result[0].NotReady.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].NotReady);
        result[0].WorkflowAttach.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowAttach);
        result[0].InfraObjectId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectName);
        result[0].ComponentName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ComponentName);
        result[0].Type.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].Type);
        result[0].ErrorMessage.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.GetDrReadyLogEmptyRepository();

        var handler = new GetDRReadyLogListQueryHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);

        var result = await handler.Handle(new GetDRReadyLogListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetDRReadyLogListQuery(), CancellationToken.None);

        _drReadyLogRepositoryMock.Verify(x => x.ListAllAsync(), Times.Once);
    }
}