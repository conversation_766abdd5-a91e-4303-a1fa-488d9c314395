﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;

public class GetLicenseReportQueryHandler : IRequestHandler<GetLicenseReportQuery, LicenseReport>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public GetLicenseReportQueryHandler(ILicenseManagerRepository licenseManagerRepository, IMapper mapper,
        ILicenseInfoRepository licenseInfoRepository, IPublisher publisher, ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _licenseManagerRepository = licenseManagerRepository;
        _licenseInfoRepository = licenseInfoRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<LicenseReport> Handle(GetLicenseReportQuery request, CancellationToken cancellationToken)
    {
        var licenseReport = new List<LicenseReportVm>();

        var licenseDto = new LicenseReportVm();

        var licenseListVm = new List<Domain.Entities.LicenseManager>();

        if (request.LicenseId.IsNotNullOrWhiteSpace())
        {
            var splitLicenseId = request.LicenseId.Split(",");

            splitLicenseId.ForEach(lic =>
            {
                var license = _licenseManagerRepository.GetLicenseDetailByIdAsync(lic).Result;

                licenseListVm.AddRange(license);
            });
        }

        else
        {
            var parentLicense = await _licenseManagerRepository.ListAllLicense();

            if (_loggedInUserService.IsParent)
                licenseListVm = parentLicense.Where(x => x.IsParent).ToList();
            else
                licenseListVm = parentLicense;
        }

        var baseDatabaseCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydatabaseCount"));
        var baseReplicationCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryreplicationCount"));
        var baseStorageCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarystorageCount"));
        var baseVirtualizationCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryvirtualizationCount"));
        var baseApplicationCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryapplicationCount"));
        var baseDnsCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydnsCount"));
        var baseNetworkCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarynetworkCount"));
        var baseThirdPartyCount =
            licenseListVm.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarythirdPartyCount"));

        var baseUsedDatabaseCount = 0;
        var baseUsedReplicationCount = 0;
        var baseUsedStorageCount = 0;
        var baseUsedVirtualizationCount = 0;
        var baseUsedApplicationCount = 0;
        var baseUsedDnsCount = 0;
        var baseUsedNetworkCount = 0;
        var baseUsedThirdPartyCount = 0;

        var databaseAvailableCount = 0;
        var replicationAvailableCount = 0;
        var storageAvailableCount = 0;
        var virtualizationAvailableCount = 0;
        var applicationAvailableCount = 0;
        var dnsAvailableCount = 0;
        var networkAvailableCount = 0;
        var thirdPartyAvailableCount = 0;

        licenseListVm.ForEach(license =>
        {
            baseUsedDatabaseCount += _licenseInfoRepository
                .GetAvailableCountByLicenseId(license.ReferenceId, Modules.Database.ToString()).Result;
            baseUsedReplicationCount += _licenseInfoRepository
                .GetAvailableCountByLicenseId(license.ReferenceId, Modules.Replication.ToString()).Result;
            baseUsedStorageCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "storage")
                .Result;
            baseUsedVirtualizationCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "virtualization").Result;
            baseUsedApplicationCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "application").Result;
            baseUsedDnsCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "dns")
                .Result;
            baseUsedNetworkCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(), "network")
                .Result;
            baseUsedThirdPartyCount += _licenseInfoRepository
                .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                    "thirdparty").Result;

            databaseAvailableCount +=
                GetJsonProperties.GetLicenseJsonValue(license.Properties, "primarydatabaseCount") -
                _licenseInfoRepository
                    .GetAvailableCountByLicenseId(license.ReferenceId,
                        Modules.Database.ToString()).Result;
            replicationAvailableCount +=
                GetJsonProperties.GetLicenseJsonValue(license.Properties, "primaryreplicationCount") -
                _licenseInfoRepository.GetAvailableCountByLicenseId(license.ReferenceId,
                    Modules.Replication.ToString()).Result;
            storageAvailableCount += GetJsonProperties.GetLicenseJsonValue(license.Properties, "primarystorageCount") -
                                     _licenseInfoRepository
                                         .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId,
                                             Modules.Server.ToString(), "storage").Result;
            virtualizationAvailableCount +=
                GetJsonProperties.GetLicenseJsonValue(license.Properties, "primaryvirtualizationCount") -
                _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId, Modules.Server.ToString(),
                        "virtualization").Result;
            applicationAvailableCount +=
                GetJsonProperties.GetLicenseJsonValue(license.Properties, "primaryapplicationCount") -
                _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId,
                        Modules.Server.ToString(), "application").Result;
            dnsAvailableCount += GetJsonProperties.GetLicenseJsonValue(license.Properties, "primarydnsCount") -
                                 _licenseInfoRepository
                                     .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId,
                                         Modules.Server.ToString(), "dns").Result;
            networkAvailableCount += GetJsonProperties.GetLicenseJsonValue(license.Properties, "primarynetworkCount") -
                                     _licenseInfoRepository
                                         .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId,
                                             Modules.Server.ToString(), "network").Result;
            thirdPartyAvailableCount +=
                GetJsonProperties.GetLicenseJsonValue(license.Properties, "primarythirdPartyCount") -
                _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(license.ReferenceId,
                        Modules.Server.ToString(), "thirdparty").Result;

            var derivedLicenses = _licenseManagerRepository
                .GetDerivedLicenseDetailByBaseLicenseDetailAsync(license.CompanyId, license.PoNumber).Result;

            baseDatabaseCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydatabaseCount"));
            baseReplicationCount +=
                derivedLicenses.Sum(x =>
                    GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryreplicationCount"));
            baseStorageCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarystorageCount"));
            baseVirtualizationCount += derivedLicenses.Sum(x =>
                GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryvirtualizationCount"));
            baseApplicationCount +=
                derivedLicenses.Sum(x =>
                    GetJsonProperties.GetLicenseJsonValue(x.Properties, "primaryapplicationCount"));
            baseDnsCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarydnsCount"));
            baseNetworkCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarynetworkCount"));
            baseThirdPartyCount +=
                derivedLicenses.Sum(x => GetJsonProperties.GetLicenseJsonValue(x.Properties, "primarythirdPartyCount"));

            derivedLicenses.ForEach(delicense =>
            {
                baseUsedDatabaseCount += _licenseInfoRepository
                    .GetAvailableCountByLicenseId(delicense.ReferenceId, Modules.Database.ToString()).Result;
                baseUsedReplicationCount += _licenseInfoRepository
                    .GetAvailableCountByLicenseId(delicense.ReferenceId, Modules.Replication.ToString()).Result;
                baseUsedStorageCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "storage").Result;
                baseUsedVirtualizationCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "virtualization").Result;
                baseUsedApplicationCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "application").Result;
                baseUsedDnsCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "dns").Result;
                baseUsedNetworkCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "network").Result;
                baseUsedThirdPartyCount += _licenseInfoRepository
                    .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId, Modules.Server.ToString(),
                        "thirdparty").Result;

                databaseAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primarydatabaseCount") -
                    _licenseInfoRepository.GetAvailableCountByLicenseId(delicense.ReferenceId,
                        Modules.Database.ToString()).Result;
                replicationAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primaryreplicationCount") -
                    _licenseInfoRepository
                        .GetAvailableCountByLicenseId(delicense.ReferenceId, Modules.Replication.ToString()).Result;
                storageAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primarystorageCount") -
                    _licenseInfoRepository
                        .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                            Modules.Server.ToString(), "storage").Result;
                virtualizationAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primaryvirtualizationCount") -
                    _licenseInfoRepository.GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                        Modules.Server.ToString(), "virtualization").Result;
                applicationAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primaryapplicationCount") -
                    _licenseInfoRepository.GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                        Modules.Server.ToString(), "application").Result;
                dnsAvailableCount += GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primarydnsCount") -
                                     _licenseInfoRepository
                                         .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                                             Modules.Server.ToString(), "dns").Result;
                networkAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primarynetworkCount") -
                    _licenseInfoRepository
                        .GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                            Modules.Server.ToString(), "network").Result;
                thirdPartyAvailableCount +=
                    GetJsonProperties.GetLicenseJsonValue(delicense.Properties, "primarythirdPartyCount") -
                    _licenseInfoRepository.GetAvailableLicenseByLicenseIdAndEntityType(delicense.ReferenceId,
                        Modules.Server.ToString(), "thirdparty").Result;
            });
        });

        licenseDto.TotalCount = baseDatabaseCount + baseReplicationCount + baseStorageCount + baseVirtualizationCount +
                                baseApplicationCount
                                + baseDnsCount + baseNetworkCount + baseThirdPartyCount;

        licenseDto.AvailableCountVm = new AvailableCountViewVm
        {
            DatabaseAvailableCount = databaseAvailableCount,
            ReplicationAvailableCount = replicationAvailableCount,
            StorageAvailableCount = storageAvailableCount,
            VirtualizationAvailableCount = virtualizationAvailableCount,
            ApplicationAvailableCount = applicationAvailableCount,
            DnsAvailableCount = dnsAvailableCount,
            NetworkAvailableCount = networkAvailableCount,
            ThirdPartyAvailableCount = thirdPartyAvailableCount
        };

        licenseDto.UsedCountVm = new UsedCountViewVm
        {
            DatabaseUsedCount = baseUsedDatabaseCount,
            ReplicationUsedCount = baseUsedReplicationCount,
            StorageUsedCount = baseUsedStorageCount,
            ApplicationUsedCount = baseUsedApplicationCount,
            NetworkUsedCount = baseUsedNetworkCount,
            VirtualizationUsedCount = baseUsedVirtualizationCount,
            DnsUsedCount = baseUsedDnsCount,
            ThirdPartyUsedCount = baseUsedThirdPartyCount
        };

        licenseListVm.ForEach(lic =>
        {
            var license = _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(lic.ReferenceId).Result;

            if (license.Count > 0)
            {
                var removeDuplicateLic = license.DistinctBy(x => x.LicenseId)
                    .Where(y => !string.IsNullOrEmpty(y.PONumber)).FirstOrDefault();
                if (removeDuplicateLic != null)
                {
                    var licenseMapping = _mapper.Map<LicenseReportDetail>(removeDuplicateLic);

                    var database = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("database") && x.EntityType.IsNullOrWhiteSpace()).ToList();

                    licenseMapping.DatabaseReportVms = _mapper.Map<List<DatabaseReportVm>>(database);

                    var replication = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("replication") && x.EntityType.IsNullOrWhiteSpace()).ToList();

                    licenseMapping.ReplicationReportVms = _mapper.Map<List<ReplicationReportVm>>(replication);

                    var network = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                        x.EntityType.Trim().ToLower().Equals("network")).ToList();

                    licenseMapping.NetworkReportVms = _mapper.Map<List<NetworkReportVm>>(network);

                    var application = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                        x.EntityType.Trim().ToLower().Equals("application")).ToList();

                    licenseMapping.ApplicationReportVms = _mapper.Map<List<ApplicationReportVm>>(application);

                    var storage = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                        x.EntityType.Trim().ToLower().Equals("storage")).ToList();

                    licenseMapping.StorageReportVms = _mapper.Map<List<StorageReportVm>>(storage);

                    var virtualization = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                x.EntityType.Trim().ToLower().Equals("virtualization")).ToList();

                    licenseMapping.VirtualizationReportVms = _mapper.Map<List<VirtualizationReportVm>>(virtualization);

                    var dns = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                        x.EntityType.Trim().ToLower().Equals("dns")).ToList();

                    licenseMapping.DNSReportVms = _mapper.Map<List<DNSReportVm>>(dns);

                    var thirdParty = license.Where(x =>
                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                        x.EntityType.Trim().ToLower().Equals("thirdParty")).ToList();

                    licenseMapping.ThirdPartyReportVms = _mapper.Map<List<ThirdPartyReportVm>>(thirdParty);

                    var derivedLicense = _licenseManagerRepository
                        .GetDerivedLicenseDetailByBaseLicenseDetailAsync(lic.CompanyId, lic.PoNumber).Result;

                    if (derivedLicense.Count > 0)
                        derivedLicense.ForEach(dervidedlic =>
                        {
                            var dervidedLicenseInfo = _licenseInfoRepository
                                .GetLicenseInfoDetailByLicenseId(dervidedlic.ReferenceId).Result;

                            if (dervidedLicenseInfo.Count > 0)
                            {
                                var removeDuplicatederivedLic = dervidedLicenseInfo.DistinctBy(x => x.LicenseId)
                                    .Where(y => !string.IsNullOrEmpty(y.PONumber)).FirstOrDefault();
                                if (removeDuplicatederivedLic != null)
                                {
                                    var derivedMapping = _mapper.Map<ChildLicenseReportVm>(removeDuplicatederivedLic);

                                    var database = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("database") &&
                                        x.EntityType.IsNullOrWhiteSpace()).ToList();

                                    derivedMapping.DatabaseReportVms = _mapper.Map<List<DatabaseReportVm>>(database);

                                    var replication = dervidedLicenseInfo.Where(x =>
                                            x.Entity.Trim().ToLower().Equals("replication") &&
                                            x.EntityType.IsNullOrWhiteSpace())
                                        .ToList();

                                    derivedMapping.ReplicationReportVms =
                                        _mapper.Map<List<ReplicationReportVm>>(replication);

                                    var network = dervidedLicenseInfo.Where(x =>
                                                x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("network")).ToList();

                                    derivedMapping.NetworkReportVms = _mapper.Map<List<NetworkReportVm>>(network);

                                    var application = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("application")).ToList();

                                    derivedMapping.ApplicationReportVms =
                                        _mapper.Map<List<ApplicationReportVm>>(application);

                                    var storage = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("storage")).ToList();

                                    derivedMapping.StorageReportVms = _mapper.Map<List<StorageReportVm>>(storage);

                                    var virtualization = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("virtualization")).ToList();

                                    derivedMapping.VirtualizationReportVms =
                                        _mapper.Map<List<VirtualizationReportVm>>(virtualization);

                                    var dns = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("dns")).ToList();

                                    derivedMapping.DNSReportVms = _mapper.Map<List<DNSReportVm>>(dns);

                                    var thirdParty = dervidedLicenseInfo.Where(x =>
                                        x.Entity.Trim().ToLower().Equals("server") && x.EntityType != null &&
                                        x.EntityType.Trim().ToLower().Equals("thirdParty")).ToList();

                                    derivedMapping.ThirdPartyReportVms =
                                        _mapper.Map<List<ThirdPartyReportVm>>(thirdParty);
                                    if (database.Count != 0 || replication.Count != 0 || network.Count != 0 ||
                                        application.Count != 0 || storage.Count != 0 || virtualization.Count != 0 ||
                                        dns.Count != 0 ||
                                        thirdParty.Count != 0)
                                        licenseMapping.ChildLicenseReportVms.AddRange(derivedMapping);
                                }
                            }
                        });
                    if (database.Count != 0 || replication.Count != 0 || network.Count != 0 || application.Count != 0 ||
                        storage.Count != 0 || virtualization.Count != 0 || dns.Count != 0 || thirdParty.Count != 0 ||
                        licenseMapping.ChildLicenseReportVms.Count != 0)
                    {
                        licenseDto.LicenseReportDetail.AddRange(licenseMapping);
                    }
                    else
                    {
                        licenseDto.TotalCount = baseDatabaseCount + baseReplicationCount + baseStorageCount +
                                                baseVirtualizationCount + baseApplicationCount
                                                + baseDnsCount + baseNetworkCount + baseThirdPartyCount;

                        licenseDto.AvailableCountVm = new AvailableCountViewVm
                        {
                            DatabaseAvailableCount = databaseAvailableCount,
                            ReplicationAvailableCount = replicationAvailableCount,
                            StorageAvailableCount = storageAvailableCount,
                            VirtualizationAvailableCount = virtualizationAvailableCount,
                            ApplicationAvailableCount = applicationAvailableCount,
                            DnsAvailableCount = dnsAvailableCount,
                            NetworkAvailableCount = networkAvailableCount,
                            ThirdPartyAvailableCount = thirdPartyAvailableCount
                        };

                        licenseDto.UsedCountVm = new UsedCountViewVm
                        {
                            DatabaseUsedCount = baseUsedDatabaseCount,
                            ReplicationUsedCount = baseUsedReplicationCount,
                            StorageUsedCount = baseUsedStorageCount,
                            ApplicationUsedCount = baseUsedApplicationCount,
                            NetworkUsedCount = baseUsedNetworkCount,
                            VirtualizationUsedCount = baseUsedVirtualizationCount,
                            DnsUsedCount = baseUsedDnsCount,
                            ThirdPartyUsedCount = baseUsedThirdPartyCount
                        };
                    }
                }
            }
        });

        licenseReport.AddRange(licenseDto);

        await _publisher.Publish(
            new ReportViewedEvent { ReportName = "License Report", ActivityType = ActivityType.View.ToString() },
            CancellationToken.None);

        return new LicenseReport
        {
            ReportGeneratedBy = _loggedInUserService.LoginName,
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            LicenseReportVms = _mapper.Map<List<LicenseReportVm>>(licenseReport)
        };
    }
}