﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
 
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
 
    <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
 
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
 
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
 
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
 
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
  
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Infrastructure\ContinuityPatrol.Persistence\ContinuityPatrol.Persistence.csproj" />
  </ItemGroup>

</Project>
