﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Commands;

public class DeleteComponentTypeTests : IClassFixture<ComponentTypeFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;

    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;
    private readonly Mock<IInfraReplicationMappingRepository> _mockInfraReplicationMappingRepository;

    private readonly DeleteComponentTypeCommandHandler _handler;

    public DeleteComponentTypeTests(ComponentTypeFixture componentTypeFixture)
    {
        _componentTypeFixture = componentTypeFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockInfraReplicationMappingRepository = new Mock<IInfraReplicationMappingRepository>();

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.DeleteComponentTypeRepository(_componentTypeFixture.ComponentTypes);

        _handler = new DeleteComponentTypeCommandHandler(_mockComponentTypeRepository.Object, mockPublisher.Object, _mockInfraReplicationMappingRepository.Object);

        _componentTypeFixture.ComponentTypes[0].Properties =
            "{\"name\":\"MSSQL\",\"version\":\"[\\\"2015\\\",\\\"2016\\\",\\\"2017\\\",\\\"2018\\\",\\\"2019\\\"]\",\"icon\":\"cp-mssql\"}";

    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_ComponentTypeDeleted()
    {
        var validGuid = Guid.NewGuid();

        _componentTypeFixture.ComponentTypes[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteComponentTypeCommand { Id = validGuid.ToString() }, CancellationToken.None);

        Assert.True(result.Success);

        var siteType = await _mockComponentTypeRepository.Object.GetComponentTypeById(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        Assert.False(siteType.IsActive);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulComponentTypeResponse_When_ComponentTypeDeleted()
    {
        var validGuid = Guid.NewGuid();

        _componentTypeFixture.ComponentTypes[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteComponentTypeCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteComponentTypeResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_Delete_ComponentType()
    {
        var validGuid = Guid.NewGuid();

        _componentTypeFixture.ComponentTypes[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteComponentTypeCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var componentType = await _mockComponentTypeRepository.Object.GetComponentTypeById(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        componentType.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidComponentTypeId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        var mockPublisher = new Mock<IPublisher>();

        var handler = new DeleteComponentTypeCommandHandler(_mockComponentTypeRepository.Object, mockPublisher.Object, _mockInfraReplicationMappingRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new DeleteComponentTypeCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _componentTypeFixture.ComponentTypes[0].ReferenceId = validGuid.ToString();

        var componentType = _componentTypeFixture.ComponentTypes[0];

        var ValidGuid = _componentTypeFixture.ComponentTypes[0].ReferenceId.ToString();

        _componentTypeFixture.ComponentTypes[0].ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new DeleteComponentTypeCommand { Id = _componentTypeFixture.ComponentTypes[0].ReferenceId }, CancellationToken.None);

        _mockComponentTypeRepository.Verify(x => x.GetComponentTypeById(It.IsAny<string>()), Times.Once);

        _mockComponentTypeRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.ComponentType>()), Times.Once);
    }
}
