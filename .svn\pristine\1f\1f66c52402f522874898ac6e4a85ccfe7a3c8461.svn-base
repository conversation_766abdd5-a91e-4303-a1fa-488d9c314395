﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUp.Events.Execute;

public class BackUpExecutedEventHandler : INotificationHandler<BackUpExecutedEvent>
{
    private readonly ILogger<BackUpExecutedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BackUpExecutedEventHandler(ILoggedInUserService userService, ILogger<BackUpExecutedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BackUpExecutedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Execute} BackUp",
            Entity = "BackUp",
            ActivityType = ActivityType.Execute.ToString(),
            ActivityDetails = $"Backup request sent successfully for '{createdEvent.BackUpName}'."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Backup request sent successfully for '{createdEvent.BackUpName}'.");
    }
}