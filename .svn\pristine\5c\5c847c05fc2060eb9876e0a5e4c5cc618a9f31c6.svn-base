﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Features.Report.Queries.DriftReport;


public class GetDriftReportInfraObjectHandler : IRequestHandler<GetDriftReportInfraObjectQuery, List<DriftEventReportVm>>
{
    private static IDriftEventRepository _driftEventRepository;
    private static ILoggedInUserService _loggedInUserService;
    private static IInfraObjectRepository _infraObjectRepository;
    private static ILogger<GetDriftReportInfraObjectHandler> _logger;
    private static IMapper _mapper;

    public GetDriftReportInfraObjectHandler(IDriftEventRepository driftEventRepository,
        ILoggedInUserService loggedInUserService, IInfraObjectRepository infraObjectRepository,
        ILogger<GetDriftReportInfraObjectHandler> logger, IMapper mapper)
    {
        _driftEventRepository = driftEventRepository;
        _loggedInUserService = loggedInUserService;
        _infraObjectRepository = infraObjectRepository;
        _logger = logger;
        _mapper = mapper;
    }

    public async Task<List<DriftEventReportVm>> Handle(GetDriftReportInfraObjectQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var mappedDriftList = new List<DriftEventReportVm>();

            // Fetch the list of drift events within the given date range
            var driftInfraList = await _driftEventRepository.GetStartDateAndEndDate(request.StartDate, request.EndDate);

            // Remove duplicates based on InfraObjectId
            driftInfraList = driftInfraList
                .GroupBy(drift => drift.InfraObjectId)
                .Select(group => group.First())
                .ToList();

            // Map the filtered drift events to the view model list
            mappedDriftList = _mapper.Map<List<DriftEventReportVm>>(driftInfraList);

            if (mappedDriftList.Count > 0)
            {
                foreach (var driftDetails in mappedDriftList)
                {
                    if (driftDetails.InfraObjectId is not null)
                    {
                        var infraObject = await _infraObjectRepository.GetByReferenceIdAsync(driftDetails.InfraObjectId);

                        if (infraObject is not null)
                        {
                            driftDetails.InfraObjectName = infraObject.Name;
                        }
                        else
                        {
                            _logger.LogInformation("An error occurred: InfraObject values were null for ID: {InfraObjectId}.", driftDetails.InfraObjectId);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("An error occurred: InfraObject ID was null.");
                    }
                }
            }
            else
            {
                _logger.LogInformation("An error occurred: No values found in InfraObject.");
            }

            return mappedDriftList;


        }
        catch (Exception ex)
        {
            _logger.LogError("An error occurred in GetDriftReportInfraObjectQueryHandler." + ex.Message);
            return null;
        }
    }
}
