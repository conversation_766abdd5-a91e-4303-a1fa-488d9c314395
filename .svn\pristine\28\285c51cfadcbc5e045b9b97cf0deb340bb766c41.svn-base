using ContinuityPatrol.Application.Features.InfraMaster.Events.Create;

namespace ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;

public class CreateInfraMasterCommandHandler : IRequestHandler<CreateInfraMasterCommand, CreateInfraMasterResponse>
{
    private readonly IInfraMasterRepository _infraMasterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateInfraMasterCommandHandler(IMapper mapper, IInfraMasterRepository infraMasterRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _infraMasterRepository = infraMasterRepository;
    }

    public async Task<CreateInfraMasterResponse> Handle(CreateInfraMasterCommand request,
        CancellationToken cancellationToken)
    {
        var infraMaster = _mapper.Map<Domain.Entities.InfraMaster>(request);

        infraMaster = await _infraMasterRepository.AddAsync(infraMaster);

        var response = new CreateInfraMasterResponse
        {
            Message = Message.Create(nameof(Domain.Entities.InfraMaster), infraMaster.Name),

            Id = infraMaster.ReferenceId
        };

        await _publisher.Publish(new InfraMasterCreatedEvent { Name = infraMaster.Name }, cancellationToken);

        return response;
    }
}