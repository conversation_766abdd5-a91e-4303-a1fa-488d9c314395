using ContinuityPatrol.Web.Middlewares;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class AntiXssMiddlewareTests
{
    private readonly AntiXssMiddleware _middleware;
    private readonly DefaultHttpContext _context;

    public AntiXssMiddlewareTests()
    {
        RequestDelegate next = _ => Task.CompletedTask;
        _middleware = new AntiXssMiddleware(next);
        _context = new DefaultHttpContext
        {
            Response =
            {
                Body = new MemoryStream()
            }
        };
    }

    [Fact]
    public void Constructor_WithNullNext_ShouldThrowArgumentNullException()
    {
        Assert.Throws<ArgumentNullException>(() => new AntiXssMiddleware(null!));
    }

    [Fact]
    public async Task Invoke_WithSafeRequest_ShouldCallNext()
    {
        var wasCalled = false;
        var context = new DefaultHttpContext
        {
            Request =
            {
                Path = "/safe/path",
                QueryString = new QueryString("?param=value"),
                Body = new MemoryStream(Encoding.UTF8.GetBytes("safe content"))
            },
            Response = { Body = new MemoryStream() }
        };

        var middleware = new AntiXssMiddleware(_ =>
        {
            wasCalled = true;
            return Task.CompletedTask;
        });

        await middleware.Invoke(context);

        Assert.True(wasCalled, "Expected the next delegate to be called.");
    }


    [Theory]
    [InlineData("/path/<script>alert('xss')</script>")]
    [InlineData("/path/<img src=x onerror=alert(1)>")]
    [InlineData("/path/<svg onload=alert(1)>")]
    public async Task Invoke_WithDangerousPath_ShouldReturnBadRequest(string dangerousPath)
    {
        _context.Request.Path = dangerousPath;

        await _middleware.Invoke(_context);

        Assert.Equal((int)HttpStatusCode.BadRequest, _context.Response.StatusCode);
    }

    [Theory]
    [InlineData("?param=<script>alert('xss')</script>")]
    [InlineData("?search=<img src=x onerror=alert(1)>")]
    [InlineData("?data=<svg onload=alert(1)>")]
    public async Task Invoke_WithDangerousQueryString_ShouldReturnBadRequest(string dangerousQuery)
    {
        _context.Request.Path = "/safe/path";
        _context.Request.QueryString = new QueryString(dangerousQuery);

        await _middleware.Invoke(_context);

        Assert.Equal((int)HttpStatusCode.BadRequest, _context.Response.StatusCode);
    }

    [Fact]
    public async Task Invoke_WithDangerousRequestBody_ShouldReturnBadRequest()
    {
        _context.Request.Path = "/safe/path";
        var dangerousContent = "<script>alert('xss')</script>";
        _context.Request.Body = new MemoryStream(Encoding.UTF8.GetBytes(dangerousContent));

        await _middleware.Invoke(_context);

        Assert.Equal((int)HttpStatusCode.BadRequest, _context.Response.StatusCode);
    }

    [Fact]
    public async Task IsDangerousString_ShouldNotReturnTrue_ForAmpersandNotFollowedByHash()
    {
        var input = "&x"; 

        var result = CrossSiteScriptingValidation.IsDangerousString(input, out var index);

        Assert.False(result);      
        Assert.Equal(0, index); 

       await Task.CompletedTask;
    }


    [Fact]
    public async Task Invoke_WithAntiXssException_ShouldReturnErrorResponse()
    {
        // Arrange
        _context.Request.Path = "/path/<script>";
        _context.Response.Body = new MemoryStream();

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.Equal((int)HttpStatusCode.BadRequest, _context.Response.StatusCode);
        Assert.Equal("application/json; charset=utf-8", _context.Response.ContentType);

        _context.Response.Body.Position = 0;
        var responseContent = await new StreamReader(_context.Response.Body).ReadToEndAsync();
        var errorResponse = JsonConvert.DeserializeObject<ErrorResponse>(responseContent);

        Assert.NotNull(errorResponse);
        Assert.Equal(5005, errorResponse.ErrorCode);
        Assert.Contains("A Potentially dangerous request was detected", errorResponse.Description);
    }

    [Theory]
    [InlineData("/normal/path")]
    [InlineData("/api/users")]
    [InlineData("/dashboard")]
    public async Task Invoke_WithSafePaths_ShouldCallNext(string safePath)
    {
        var (middleware, context, wasCalledCheck) = CreateMiddlewareWithContext(safePath);

        await middleware.Invoke(context);

        Assert.True(wasCalledCheck(), "Expected next delegate to be called.");
    }


    [Theory]
    [InlineData("?search=normal")]
    [InlineData("?id=123")]
    [InlineData("?name=john")]
    public async Task Invoke_WithSafeQueryStrings_ShouldCallNext(string safeQuery)
    {
        var (middleware, context, wasCalledCheck) = CreateMiddlewareWithContext("/safe/path", safeQuery);

        await middleware.Invoke(context);

        Assert.True(wasCalledCheck(), "Expected next delegate to be called.");
    }


    [Fact]
    public async Task Invoke_WithEmptyRequestBody_ShouldCallNext()
    {
        var (middleware, context, wasCalledCheck) = CreateMiddlewareWithContext(
            path: "/safe/path",
            requestBody: new MemoryStream()
        );

        await middleware.Invoke(context);

        Assert.True(wasCalledCheck(), "Expected the next delegate to be called.");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Invoke_WithNullOrEmptyPath_ShouldCallNext(string path)
    {
        var (middleware, context, wasCalledCheck) = CreateMiddlewareWithContext(
            path: path,
            requestBody: new MemoryStream(Encoding.UTF8.GetBytes("safe content"))
        );

        await middleware.Invoke(context);

        Assert.True(wasCalledCheck(), "Expected the next delegate to be called.");
    }

    [Theory]
    [InlineData("<a")]   // Dangerous: tag
    [InlineData("<!")]   // Dangerous: comment
    [InlineData("</")]   // Dangerous: closing tag
    [InlineData("<?")]   // Dangerous: processing instruction
    [InlineData("&#")]   // Dangerous: HTML entity
    public void IsDangerousString_ShouldReturnTrue_ForDangerousInputs(string input)
    {
        // Act
        var result = CrossSiteScriptingValidation.IsDangerousString(input, out var index);

        // Assert
        Assert.True(result);
        Assert.Equal(0, index); // Should always start at 0 in these cases
    }


    [Theory]
    [InlineData("<")]    // Safe: last char only
    [InlineData("&")]    // Safe: last char only
    [InlineData("test<")]// Safe: < is last character
    [InlineData("test&")]// Safe: & is last character
    public void IsDangerousString_ShouldReturnFalse_ForSafeSingleChars(string input)
    {
        // Act
        var result = CrossSiteScriptingValidation.IsDangerousString(input, out var index);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsDangerousString_ShouldContinueSearchingUntilDangerousFound()
    {
        // Example: safe < followed by space, then dangerous <a
        string input = "< test <a";

        // Act
        var result = CrossSiteScriptingValidation.IsDangerousString(input, out var index);

        // Assert
        Assert.True(result);
        Assert.Equal(7, index); // Second <
    }

    private (AntiXssMiddleware middleware, DefaultHttpContext context, Func<bool> wasCalledCheck) CreateMiddlewareWithContext(
        string path = "/safe/path",
        string queryString = "",
        Stream? requestBody = null)
    {
        var wasCalled = false;

        var context = new DefaultHttpContext
        {
            Request =
            {
                Path = path,
                QueryString = new QueryString(queryString),
                Body = requestBody ?? new MemoryStream(Encoding.UTF8.GetBytes("safe content"))
            },
            Response = { Body = new MemoryStream() }
        };

        var middleware = new AntiXssMiddleware(_ =>
        {
            wasCalled = true;
            return Task.CompletedTask;
        });

        return (middleware, context, () => wasCalled);
    }
}

public class AntiXssMiddlewareExtensionTests
{
    [Fact]
    public void UseAntiXss_ShouldRegisterMiddleware()
    {
        // Arrange
        var app = new Mock<IApplicationBuilder>();
        app.Setup(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()))
            .Returns(app.Object);

        // Act
        var result = app.Object.UseAntiXssMiddleware();

        // Assert
        result.Should().Be(app.Object);
        app.Verify(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()), Times.Once);
    }
}
