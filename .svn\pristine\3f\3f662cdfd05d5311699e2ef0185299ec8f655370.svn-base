﻿namespace ContinuityPatrol.Domain.Entities;

public class RpForVmCGMonitorStatus:AuditableEntity
{
    public string InfraObjectId { get; set; }
    public string ConsistencyGroupId { get; set; }
    public string ConsistencyGroupName { get; set; }
    [Column(TypeName = "NCLOB")]
    public string CGProperties { get; set; }
    public string State { get; set; }
    public string TransferStatus { get; set; }
    public string ActivityType { get; set; }
    public string ActivityStatus { get; set; }
    public string LastSnapShotTime { get; set; }
    public string DataLag { get; set; }

    [Column(TypeName = "NCLOB")]
    public string SnapProperties { get; set; }
    public string AvailabilityStatus { get; set; }
    public string ProtectedSize { get; set; }
    public bool IsAlertSend { get; set; }

}
