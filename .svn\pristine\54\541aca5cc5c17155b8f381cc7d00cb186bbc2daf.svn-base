﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetPaginatedList;

public class GetRiskMitigationPaginatedListQueryHandler : IRequestHandler<GetRiskMitigationPaginatedListQuery,
    PaginatedResult<RiskMitigationListVm>>
{
    private readonly IMapper _mapper;
    private readonly IRiskMitigationRepository _riskMitigationRepository;

    public GetRiskMitigationPaginatedListQueryHandler(IMapper mapper,
        IRiskMitigationRepository riskMitigationRepository)
    {
        _mapper = mapper;
        _riskMitigationRepository = riskMitigationRepository;
    }

    public async Task<PaginatedResult<RiskMitigationListVm>> Handle(GetRiskMitigationPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _riskMitigationRepository.PaginatedListAllAsync();

        var productFilterSpec = new RiskMitigationFilterSpecification(request.SearchString);

        var riskMitigation = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<RiskMitigationListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return riskMitigation;
    }
}