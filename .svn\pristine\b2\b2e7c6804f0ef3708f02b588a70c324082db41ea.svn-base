﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Queries
{
    public class GetSmtpConfigurationPaginatedListQueryHandlerTests
    {
        private readonly Mock<ISmtpConfigurationRepository> _smtpConfigurationRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetSmtpConfigurationPaginatedListQueryHandler _handler;

        public GetSmtpConfigurationPaginatedListQueryHandlerTests()
        {
            _smtpConfigurationRepositoryMock = new Mock<ISmtpConfigurationRepository>();
            _mapperMock = new Mock<IMapper>();

            _handler = new GetSmtpConfigurationPaginatedListQueryHandler(
                _mapperMock.Object,
                _smtpConfigurationRepositoryMock.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenCalledWithValidQuery()
        {
            var query = new GetSmtpConfigurationPaginatedListQuery
            {
                SearchString = "test",
                PageNumber = 1,
                PageSize = 2
            };
            var cancellationToken = CancellationToken.None;

            var smtpConfigurationEntities = new List<Domain.Entities.SmtpConfiguration>
            {
                new Domain.Entities.SmtpConfiguration { UserName = SecurityHelper.Encrypt("User1") },
                new Domain.Entities.SmtpConfiguration { UserName = SecurityHelper.Encrypt("User2") }
            }.AsQueryable();

            var paginatedResult = new PaginatedResult<Domain.Entities.SmtpConfiguration>
            {
                Data = smtpConfigurationEntities.ToList(),
                TotalPages = 1,
                PageSize = 2,
                TotalCount = 2
            };

            _smtpConfigurationRepositoryMock
                .Setup(repo => repo.GetPaginatedQuery())
                .Equals( paginatedResult );

            _mapperMock.Setup(mapper =>
                    mapper.Map<SmtpConfigurationListVm>(It.IsAny<Domain.Entities.SmtpConfiguration>()))
                .Returns((Domain.Entities.SmtpConfiguration source) => new SmtpConfigurationListVm
                {
                    UserName = SecurityHelper.Decrypt(source.UserName)
                });

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("User1", result.Data.First().UserName);
            Assert.Equal("User2", result.Data.Last().UserName);

            _smtpConfigurationRepositoryMock.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mapperMock.Verify(
                mapper => mapper.Map<SmtpConfigurationListVm>(It.IsAny<Domain.Entities.SmtpConfiguration>()),
                Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoRecordsMatchFilter()
        {
            var query = new GetSmtpConfigurationPaginatedListQuery
            {
                SearchString = "nonexistent",
                PageNumber = 1,
                PageSize = 2
            };
            var cancellationToken = CancellationToken.None;

            var paginatedResult = new PaginatedResult<Domain.Entities.SmtpConfiguration>
            {
                Data = new List<Domain.Entities.SmtpConfiguration>(),
                TotalPages = 1,
                PageSize = 2,
                TotalCount = 0
            };

            _smtpConfigurationRepositoryMock
                .Setup(repo => repo.GetPaginatedQuery())
                .Equals(paginatedResult);

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);

            _smtpConfigurationRepositoryMock.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
