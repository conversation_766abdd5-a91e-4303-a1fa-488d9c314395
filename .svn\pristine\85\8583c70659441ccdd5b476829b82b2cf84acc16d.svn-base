﻿using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class UserActivityReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;

        public Application.Features.Report.Queries.UserActivityReport.UserActivityReport userActivityReport;//= new Application.Features.Report.Queries.UserActivityReport.UserActivityReport();
        public UserActivityReport(string data)
        {
            try
            {
                _logger = PreBuildReportController._logger;
                userActivityReport = new Application.Features.Report.Queries.UserActivityReport.UserActivityReport();
                userActivityReport = JsonConvert.DeserializeObject<Application.Features.Report.Queries.UserActivityReport.UserActivityReport>(data); 
                InitializeComponent();
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
                this.DataSource = userActivityReport.UserActivityReportVms;
                ClientCompanyLogo();
                Int64 LoginCount = 0;
                Int64 ConfigureCount = 0;
                Int64 ModifyCount = 0;
                Int64 DeleteCount = 0;
                Int64 WorkfloExecutionCount = 0;
                Int64 ReportGenerationCount = 0;
                Int64 LogoutCount = 0;
                foreach (var row in userActivityReport.UserActivityReportVms)
                {

                    if (row.ActivityType.Contains("Login"))
                    {
                        LoginCount = LoginCount + 1;
                    }
                    if (row.ActivityType.Contains("Logout"))
                    {
                        LogoutCount = LogoutCount + 1;
                    }
                    if (row.ActivityType.Contains("Generate"))
                    {
                        ReportGenerationCount = ReportGenerationCount + 1;
                    }
                    if (row.ActivityType.Contains("Create"))
                    {
                        ConfigureCount = ConfigureCount + 1;
                    }
                    if (row.ActivityType.Contains("Update"))
                    {
                        ModifyCount = ModifyCount + 1;
                    }
                    if (row.ActivityType.Contains("Delete"))
                    {
                        DeleteCount = DeleteCount + 1;
                    }
                    if (row.ActivityType.Contains("Execute"))
                    {
                        WorkfloExecutionCount = WorkfloExecutionCount + 1;
                    }
                }
                xrLabel2.Text = userActivityReport.UserName;
                var startDate = userActivityReport.ActiveStartDate.ToString();
                xrLabel5.Text = startDate.ToDateTime().ToString("dd-MM-yyyy");
                var endDate = userActivityReport.ActiveEndDate.ToString();
                xrLabel6.Text = endDate.ToDateTime().ToString("dd-MM-yyyy");
                xrLabel19.Text = LoginCount.ToString();
                xrLabel20.Text = ConfigureCount.ToString();
                xrLabel21.Text = WorkfloExecutionCount.ToString();
                xrLabel22.Text = ReportGenerationCount.ToString();
                xrLabel23.Text = LogoutCount.ToString();
                xrLabel30.Text = ModifyCount.ToString();
                xrLabel28.Text = DeleteCount.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the User Audit Report. The error message : " + ex.Message); throw; }
        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                
                _username.Text = "Report Generated By: " + userActivityReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the User Audit Report's User name. The error message : " + ex.Message); throw; }
        }
        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try {          
            Int64 LoginCount = 0;
            Int64 ConfigureCount = 0;
            Int64 ModifyCount = 0;
            Int64 DeleteCount = 0;
            Int64 WorkfloExecutionCount = 0;
            Int64 ReportGenerationCount = 0;
            Int64 LogoutCount = 0;
            Int64 NodataFound = 0;
            foreach (var row in userActivityReport.UserActivityReportVms)
            {

                if (row.ActivityType.Contains("Login"))
                {
                    LoginCount = LoginCount + 1;
                }
                if (row.ActivityType.Contains("Logout"))
                {
                    LogoutCount = LogoutCount + 1;
                }
                if (row.ActivityType.Contains("Generate"))
                {
                    ReportGenerationCount = ReportGenerationCount + 1;
                }
                if (row.ActivityType.Contains("Create"))
                {
                    ConfigureCount = ConfigureCount + 1;
                }
                if (row.ActivityType.Contains("Update"))
                {
                    ModifyCount = ModifyCount + 1;
                }
                if (row.ActivityType.Contains("Delete"))
                {
                    DeleteCount = DeleteCount + 1;
                }
                if (row.ActivityType.Contains("Execute"))
                {
                    WorkfloExecutionCount = WorkfloExecutionCount + 1;
                }
            }
            if (LoginCount == 0 && ConfigureCount == 0 && WorkfloExecutionCount == 0 && ReportGenerationCount == 0 && LogoutCount == 0 && ModifyCount == 0 && DeleteCount == 0)
            {
                Series series1 = new Series("Series1", ViewType.Doughnut);
                xrChart1.Series.Add(series1);
                NodataFound = 1;
                series1.DataSource = CreateChartData(LoginCount, ConfigureCount, WorkfloExecutionCount, ReportGenerationCount, LogoutCount, ModifyCount, DeleteCount, NodataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series1.View = doughnutSeriesView;
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.Label.TextPattern = "{A}";
                series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;

            }
            else
            {
                Series series = new Series("Series1", ViewType.Doughnut);
                xrChart1.Series.Add(series);

                series.DataSource = CreateChartData(LoginCount, ConfigureCount, WorkfloExecutionCount, ReportGenerationCount, LogoutCount, ModifyCount, DeleteCount, NodataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 70D;
                series.View = doughnutSeriesView;
                series.ArgumentScaleType = ScaleType.Qualitative;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{A}\n{V}";
                ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                // ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMinIndent = 20;
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the User Audit Report's Chart data. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(Int64 LoginCount, Int64 ConfigureCount, Int64 WorkfloExecutionCount, Int64 ReportGenerationCount, Int64 LogoutCount, Int64 ModifyCount, Int64 DeleteCount, Int64 NodataFound)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Login", LoginCount);
            table.Rows.Add("Configuration", ConfigureCount);
            table.Rows.Add("Workflow Execution", WorkfloExecutionCount);
            table.Rows.Add("Report Generation", ReportGenerationCount);
            table.Rows.Add("Logout", LogoutCount);
            table.Rows.Add("Delete", DeleteCount);
            table.Rows.Add("Modify", ModifyCount);
            table.Rows.Add("No Data Found", NodataFound);

            return table;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try { 
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the User Audit Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in User Audit Report" + ex.Message.ToString());
            }
        }
    }
}
