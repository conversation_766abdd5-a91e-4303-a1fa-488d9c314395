﻿namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetNameUnique;

public class GetWorkflowNameUniqueQueryHandler : IRequestHandler<GetWorkflowNameUniqueQuery, bool>
{
    private readonly IWorkflowRepository _workflowRepository;

    public GetWorkflowNameUniqueQueryHandler(IWorkflowRepository workflowRepository)
    {
        _workflowRepository = workflowRepository;
    }

    public async Task<bool> Handle(GetWorkflowNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _workflowRepository.IsWorkflowNameExist(request.WorkflowName, request.WorkflowId);
    }
}