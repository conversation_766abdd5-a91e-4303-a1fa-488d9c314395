﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetPaginatedList;

public class GetDRReadyStatusPaginatedListQueryHandler : IRequestHandler<GetDRReadyStatusPaginatedListQuery,
    PaginatedResult<DRReadyStatusListVm>>
{
    private readonly IDrReadyStatusRepository _dRReadyStatusRepository;
    private readonly IMapper _mapper;

    public GetDRReadyStatusPaginatedListQueryHandler(IMapper mapper, IDrReadyStatusRepository dRReadyStatusRepository)
    {
        _mapper = mapper;
        _dRReadyStatusRepository = dRReadyStatusRepository;
    }

    public async Task<PaginatedResult<DRReadyStatusListVm>> Handle(GetDRReadyStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _dRReadyStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new DrReadyStatusFilterSpecification(request.SearchString);

        var dRReadyStatus = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<DRReadyStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return dRReadyStatus;
    }
}