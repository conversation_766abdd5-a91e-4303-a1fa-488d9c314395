using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BiaRulesRepositoryTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BiaRulesRepository _repository;

    public BiaRulesRepositoryTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BiaRulesRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesDto;

        // Act
        var result = await _repository.AddAsync(biaRules);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(biaRules.EntityId, result.EntityId);
        Assert.Equal(biaRules.Type, result.Type);
        Assert.Single(_dbContext.BiaImpacts);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesDto;
        await _repository.AddAsync(biaRules);

        biaRules.EntityId = "UpdatedEntityId";
        biaRules.Type = "UpdatedType";
        biaRules.IsEffective = true;

        // Act
        var result = await _repository.UpdateAsync(biaRules);

        // Assert
        Assert.Equal("UpdatedEntityId", result.EntityId);
        Assert.Equal("UpdatedType", result.Type);
        Assert.True(result.IsEffective);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesDto;
        await _repository.AddAsync(biaRules);

        // Act
        var result = await _repository.DeleteAsync(biaRules);

        // Assert
        Assert.Equal(biaRules.EntityId, result.EntityId);
        Assert.Empty(_dbContext.BiaImpacts);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesDto;
        var addedEntity = await _repository.AddAsync(biaRules);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesDto;
        await _repository.AddAsync(biaRules);

        // Act
        var result = await _repository.GetByReferenceIdAsync(biaRules.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(biaRules.ReferenceId, result.ReferenceId);
        Assert.Equal(biaRules.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        await _repository.AddRangeAsync(biaRules);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(biaRules.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;

        // Act
        var result = await _repository.AddRangeAsync(biaRules);

        // Assert
        Assert.Equal(biaRules.Count, result.Count());
        Assert.Equal(biaRules.Count, _dbContext.BiaImpacts.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        await _repository.AddRangeAsync(biaRules);

        // Act
        var result = await _repository.RemoveRangeAsync(biaRules);

        // Assert
        Assert.Equal(biaRules.Count, result.Count());
        Assert.Empty(_dbContext.BiaImpacts);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        var targetEntityId = "TEST_ENTITY_ID";
        biaRules.First().EntityId = targetEntityId;
        await _repository.AddRangeAsync(biaRules);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.EntityId == targetEntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetEntityId, result.First().EntityId);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        await _repository.AddRangeAsync(biaRules);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.EntityId == "NON_EXISTENT_ENTITY");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetBiaRulesByEntityIdAndType Tests

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entityId = "ENTITY_001";
        var type = "TYPE_001";
        var biaRules = _biaRulesFixture.BiaRulesDto;
        biaRules.EntityId = entityId;
        biaRules.Type = type;
        biaRules.IsActive = true;
        await _repository.AddAsync(biaRules);

        // Act
        var result = await _repository.GetBiaRulesByEntityIdAndType(entityId, type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entityId, result.EntityId);
        Assert.Equal(type, result.Type);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        await _repository.AddRangeAsync(biaRules);

        // Act
        var result = await _repository.GetBiaRulesByEntityIdAndType("NON_EXISTENT_ENTITY", "NON_EXISTENT_TYPE");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ShouldReturnNull_WhenInactive()
    {
        // Arrange
        var entityId = "ENTITY_001";
        var type = "TYPE_001";
        var biaRules = _biaRulesFixture.BiaRulesDto;
        biaRules.EntityId = entityId;
        biaRules.Type = type;
        biaRules.IsActive = false;
        _dbContext.BiaImpacts.Add(biaRules);

        // Act
        var result = await _repository.GetBiaRulesByEntityIdAndType(entityId, type);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ShouldReturnCorrectEntity_WhenMultipleExist()
    {
        // Arrange
        var targetEntityId = "ENTITY_001";
        var targetType = "TYPE_001";

        var biaRules = _biaRulesFixture.BiaRulesList;

        biaRules[0].EntityId= targetEntityId;
        biaRules[0].Type = targetType;
        _dbContext.BiaImpacts.AddRange(biaRules);

        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetBiaRulesByEntityIdAndType(targetEntityId, targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(targetEntityId, result.EntityId);
        Assert.Equal(targetType, result.Type);
    }

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ShouldHandleNullParameters()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        await _repository.AddRangeAsync(biaRules);

        // Act & Assert
        var result1 = await _repository.GetBiaRulesByEntityIdAndType(null, "TYPE_001");
        var result2 = await _repository.GetBiaRulesByEntityIdAndType("ENTITY_001", null);
        var result3 = await _repository.GetBiaRulesByEntityIdAndType(null, null);

        Assert.Null(result1);
        Assert.Null(result2);
        Assert.Null(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;
        var biaRules1 = biaRules[0];
        var biaRules2 = biaRules[1];

        // Act
        var task1 = _repository.AddAsync(biaRules1);
        var task2 = _repository.AddAsync(biaRules2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BiaImpacts.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(biaRules);
        var initialCount = biaRules.Count;

        var toUpdate = biaRules.Take(2).ToList();
        toUpdate.ForEach(x => x.RuleCode = "UpdatedImpact");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = biaRules.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.RuleCode == "UpdatedImpact").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleTypeFiltering()
    {
        // Arrange
        var biaRules = _biaRulesFixture.BiaRulesList;

        biaRules[0].Type = "Critical";
        biaRules[1].Type = "Normal";
        biaRules[2].Type = "Critical";
        _dbContext.BiaImpacts.AddRange(biaRules);

        _dbContext.SaveChanges();
        // Act
        var criticalRules = await _repository.FindByFilterAsync(x => x.Type == "Critical");
        var normalRules = await _repository.FindByFilterAsync(x => x.Type == "Normal");

        // Assert
        Assert.Equal(2, criticalRules.Count);
        Assert.Single(normalRules);
        Assert.All(criticalRules, x => Assert.Equal("Critical", x.Type));
        Assert.All(normalRules, x => Assert.Equal("Normal", x.Type));
    }

    #endregion
}
