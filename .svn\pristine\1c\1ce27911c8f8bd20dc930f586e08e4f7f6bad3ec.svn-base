﻿namespace ContinuityPatrol.Shared.Tests.Mocks;

public class ConfigurationRepositoryMocks
{
    public static Mock<IConfiguration> GetValue()
    {
        var configurationRepository = new Mock<IConfiguration>();

        configurationRepository.Setup(x => x.GetValue<string>("WindowService:ServiceUrl")).Returns("https://localhost:7162/LoadBalancer/");

        return configurationRepository;
    }

    public static Mock<IConfiguration> GetConnectionString()
    {
        var mockConfiguration = new Mock<IConfiguration>();

        var mockConfSection = new Mock<IConfigurationSection>();
        
        mockConfSection.SetupGet(m => m[It.Is<string>(s => s == "Default")]).Returns("oN/29mASJST361MUp1E3Ckl56ujuqvDDRnFp1N4rmZyGSrc//m6VKegNVq+xiEVWqL8BWJmDo7i6L6LMlF+XO6HH8biIaFDXKlwoXTib+Ax8iYnM9+/mQlyZpFjU6wrZbl5pfycXD7I9H0AZAIjZ3KeaK6KK6Wk6sJyw25macyLuh/eFucserJpHG/nfkKrS");

        mockConfSection.SetupGet(m => m[It.Is<string>(s => s == "DBProvider")]).Returns("tvHNBMgwGtcsdLILBsKZ3Q==");

        mockConfiguration.Setup(a => a.GetSection(It.Is<string>(s => s == "ConnectionStrings"))).Returns(mockConfSection.Object);

        return mockConfiguration;
    }
}