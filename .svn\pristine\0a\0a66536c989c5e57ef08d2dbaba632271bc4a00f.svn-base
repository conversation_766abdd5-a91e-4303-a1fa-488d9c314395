﻿using ContinuityPatrol.Application.Features.ManageWorkflow.Queries;
using ContinuityPatrol.Application.Features.ManageWorkflow.Queries.GetManagedWorkflow;
using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class ManageWorkflowlistService : BaseService, IManageWorkflowListService
{
    public ManageWorkflowlistService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<ManageWorkflowModel>> GetWorkflowList()
    {
        Logger.LogDebug("Get All workflowList");

        return await Mediator.Send(new GetWorkflowlistQuery());
    }

    public async Task<PaginatedResult<Manageworkflowlist>> GetManagedWorkflow(GetManagedWorkflowListQuery query)
    {
        Logger.LogDebug("==== Getting Workflow Details for Four Eye ==== ");

        return await Mediator.Send(query);
    }
}