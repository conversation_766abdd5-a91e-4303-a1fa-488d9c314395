﻿using ContinuityPatrol.Application.Features.UserLogin.Events.Login;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Application.Services;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class AccountService : IAccountService
{
    private readonly IAuthenticationServiceFactory _authenticationFactory;
    private readonly ILogger<AccountService> _logger;
    private readonly IPublisher _publisher;
    private readonly ITokenManager _tokenManager;

    public AccountService(
        IAuthenticationServiceFactory authenticationFactory,
        ITokenManager tokenManager,
        IPublisher publisher,
        ILogger<AccountService> logger)
    {
        _authenticationFactory = authenticationFactory;
        _tokenManager = tokenManager;
        _publisher = publisher;
        _logger = logger;
    }

    public async Task<AuthenticationResponse> Authenticate(AuthenticationRequest request)
    {
        var ipAddress = WebHelper.Current.Connection.RemoteIpAddress?.ToString();

        request.IpAddress = ipAddress ?? "localhost";

        var authenticationService = _authenticationFactory.GetAuthenticationService(request.AuthenticationType);

        var response = await authenticationService.AuthenticateAsync(request);

        if (!response.IsAuthorized) return new AuthenticationResponse { IsAuthorized = false };

        _logger.LogInformation($"User '{request.LoginName}' authorized.");

        var token = await _tokenManager.GenerateToken(response);

        _logger.LogDebug($"User '{request.LoginName}' authentication token generated successfully.");

        var permission = JsonConvert.SerializeObject(response.Permissions);

        var authenticationResponse = new AuthenticationResponse
        {
            Token = token,
            UserName = response.LoginName,
            UserId = response.UserId,
            CompanyId = response.CompanyId,
            CompanyName = response.CompanyName,
            RoleId = response.Role,
            RoleName = response.RoleName,
            Permissions = permission,
            TwoFactorAuthentication = response.TwoFactorAuthentication,
            IsReset = response.IsReset,
            //RefreshToken = refreshToken.Token,
            IsParent = response.IsParent,
            IsAllInfra = response.IsAllInfra,
            IsAuthorized = true,
            Expires = response.SessionTimeout,
            AuthenticationType = response.AuthenticationType,
            IsLicenseValidity = response.IsLicenseValidity,
            LicenseEmpty = response.LicenseEmpty,
            AssignedInfras = response.AssignedInfras,
            ParentCompanyId = response.ParentCompanyId,
            IsDefaultDashboard = response.IsDefaultDashboard,
            Url = response.Url,
            LastPasswordChanged = response.LastPasswordChanged
        };

        await _publisher.Publish(new UserLoginEvent { UserId = response.UserId, LoginName = response.LoginName,CompanyId=response.CompanyId });

        return authenticationResponse;
    }
}