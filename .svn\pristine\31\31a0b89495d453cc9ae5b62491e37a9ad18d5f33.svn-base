﻿//using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
//using ContinuityPatrol.Domain.Entities;
//using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
//using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

//namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
//{
//    public class GetBulkImportReportQueryHandlerTests
//    {
//        private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
//        private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
//        private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
//        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
//        private readonly Mock<IPublisher> _mockPubilsh;
//        private readonly IMapper _mapper;
//        private readonly GetBulkImportReportQueryHandler _handler;

//        public GetBulkImportReportQueryHandlerTests()
//        {
//            _mockBulkImportActionResultRepository = new Mock<IBulkImportActionResultRepository>();
//            _mockBulkImportOperationGroupRepository = new Mock<IBulkImportOperationGroupRepository>();
//            _mockBulkImportOperationRepository = new Mock<IBulkImportOperationRepository>();
//            _mockLoggedInUserService = new Mock<ILoggedInUserService>();

//            _mockPubilsh = new Mock<IPublisher>();

//            var mapperConfig = new MapperConfiguration(cfg =>
//            {
//                cfg.CreateMap<BulkImportOperationGroup, BulkImportOperationGroupList>();
//                cfg.CreateMap<BulkImportActionResult, BulkImportActionResultListVm>();
//            });
//            _mapper = mapperConfig.CreateMapper();

//            _handler = new GetBulkImportReportQueryHandler(
//                _mapper,
//                _mockBulkImportOperationGroupRepository.Object,
//                _mockBulkImportOperationRepository.Object,
//                _mockBulkImportActionResultRepository.Object,
//                _mockLoggedInUserService.Object, _mockPubilsh.Object);
//        }

//        [Fact]
//        public async Task Handle_ReturnsCorrectReport_ForAllOperationGroups()
//        {
//            var query = new GetBulkImportReportListQuery { Id = "all" };
//            var bulkImportOperationGroups = new List<BulkImportOperationGroup>
//            {
//                new BulkImportOperationGroup { Id = 1, ProgressStatus = "5/5", Status = "Success" },
//                new BulkImportOperationGroup { Id = 2, ProgressStatus = "3/5", Status = "Next" },
//                new BulkImportOperationGroup { Id = 3, ProgressStatus = "5/5", Status = "Error" }
//            };

//            _mockBulkImportOperationGroupRepository
//                .Setup(repo => repo.ListAllAsync())
//                .ReturnsAsync(bulkImportOperationGroups);

//            _mockBulkImportActionResultRepository
//                .Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
//                .ReturnsAsync(new List<BulkImportActionResult>());

//            _mockLoggedInUserService
//                .Setup(service => service.LoginName)
//                .Returns("TestUser");

//            var result = await _handler.Handle(query, CancellationToken.None);

//            Assert.NotNull(result);
//            Assert.Equal(3, result.TotalInfra);
//            Assert.Equal(1, result.SuccessInfra);
//            Assert.Equal(1, result.FailureInfra);
//            Assert.Equal(1, result.RunningInfra);
//            Assert.Equal("TestUser", result.ReportGeneratedBy);
//            Assert.NotNull(result.BulkImportOperationGroupListVms);
//        }

//        [Fact]
//        public async Task Handle_ReturnsEmptyReport_WhenNoOperationGroupsExist()
//        {
//            var query = new GetBulkImportReportListQuery { Id = "all" };

//            _mockBulkImportOperationGroupRepository
//                .Setup(repo => repo.ListAllAsync())
//                .ReturnsAsync(new List<BulkImportOperationGroup>());

//            _mockLoggedInUserService
//                .Setup(service => service.LoginName)
//                .Returns("TestUser");

//            var result = await _handler.Handle(query, CancellationToken.None);

//            Assert.NotNull(result);
//            Assert.Equal(0, result.TotalInfra);
//            Assert.Equal(0, result.SuccessInfra);
//            Assert.Equal(0, result.FailureInfra);
//            Assert.Equal(0, result.RunningInfra);
//            Assert.Equal("TestUser", result.ReportGeneratedBy);
//            Assert.Empty(result.BulkImportOperationGroupListVms);
//        }

//        [Fact]
//        public async Task Handle_ProcessesCorrectly_ForSpecificOperationId()
//        {
//            var query = new GetBulkImportReportListQuery { Id = "specific-id" };
//            var bulkImportOperationGroups = new List<BulkImportOperationGroup>
//            {
//                new BulkImportOperationGroup { Id = 1, ProgressStatus = "2/5", Status = "Next" }
//            };

//            _mockBulkImportOperationGroupRepository
//                .Setup(repo => repo.GetBulkImportOperationGroupByBulkImportOperationId(query.Id))
//                .ReturnsAsync(bulkImportOperationGroups);

//            _mockBulkImportActionResultRepository
//                .Setup(repo => repo.GetByOperationIdAndOperationGroupId(It.IsAny<string>(), It.IsAny<string>()))
//                .ReturnsAsync(new List<BulkImportActionResult>());

//            _mockLoggedInUserService
//                .Setup(service => service.LoginName)
//                .Returns("TestUser");

//            var result = await _handler.Handle(query, CancellationToken.None);

//            Assert.NotNull(result);
//            Assert.Equal(1, result.TotalInfra);
//            Assert.Equal(0, result.SuccessInfra);
//            Assert.Equal(0, result.FailureInfra);
//            Assert.Equal(1, result.RunningInfra);
//            Assert.Equal("TestUser", result.ReportGeneratedBy);
//            Assert.NotNull(result.BulkImportOperationGroupListVms);
//        }
//    }
//}
