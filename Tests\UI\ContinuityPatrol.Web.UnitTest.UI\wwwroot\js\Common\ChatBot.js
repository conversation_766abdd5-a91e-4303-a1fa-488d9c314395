﻿$('#chatContainerDiv').hide()
let aidaImage = `<div class="Bot-Avatar" style="z-index: 1;"><img src="/../img/Profile-img/lisa.png"/></div>`;
let userImage = `<div class="User-Avatar"><img src="/../img/Profile-img/User.jpg"/></div>`;
let daveImage = `<div class="Bot-Avatar" style="z-index: 1;"><img src="/../img/Profile-img/dave.png"/></div>`;
let globalCount = 0;
let globalChatArray = [];
let formJson;
let dateStatus = false;
let BotMode = 'lisa';
let globalChatData = [];
let chatURL = '';
let isCompleted = false;
let getChatBotURL = $('#Chat_Bot').attr('chatBoturl')

const delay = async (ms) => new Promise(res => setTimeout(res, ms))

const scrollToDown = () => {
    let d = $('#parentChatContainer');
    d.scrollTop(d.prop("scrollHeight"));
}

const getRandomChatId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

const chatGeneration = (e) => {
    e.preventDefault()
    if ($('#chatContainerDiv').is(':visible')) {
        $('#chatContainerDiv').hide()
    } else {
        $('#chatContainerDiv').show()
        scrollToDown()
        isCompleted = true;

        if ($('#chatBotContainer').children().length === 0) {

            sendChatMessage('hi')
        }
    }
    $('#chatBotInput').attr('type', 'text')
}

$('#btnChatContainerClose').on('click', function () {
    $('#chatContainerDiv').hide()
    $('#botHeaderImage').empty().append(` <div class="d-flex align-items-center"><img src="/../img/Profile-img/lisa.png" class="me-2"/><div class="d-grid fw-normal"><small>Chat with</small><span>Lisa</span></div></div>`)
    $('#chatBotContainer').empty()
    $('#chatBotInput').prop('type', 'text')
    sessionStorage.removeItem('chatBotToken')
})

$('#btnChatContainerMinimize').on('click', function () {
    $('#chatContainerDiv').hide()
})


let borderSpinner = `<svg class="loader-icon" width='200px' height='200px' xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" class="uil-default"><rect x="0" y="0" width="100" height="100" fill="none" class="bk"></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(0 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(30 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.041666666666666664s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(60 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.08333333333333333s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(90 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.125s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(120 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.16666666666666666s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(150 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.20833333333333334s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(180 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.25s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(210 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.2916666666666667s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(240 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.3333333333333333s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(270 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.375s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(300 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.4166666666666667s' repeatCount='indefinite'/></rect><rect  x='46.5' y='40' width='7' height='20' rx='5' ry='5' fill='#47bbed' transform='rotate(330 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.5s' begin='0.4583333333333333s' repeatCount='indefinite'/></rect></svg>`

let spinner = `<div class="spinner-grow text-primary ms-2" role="status" style="height:0.5rem;width: 0.5rem;">
  <span class="visually-hidden"></span>
</div>
<div class="spinner-grow text-primary ms-2" role="status" style="height:0.5rem;width: 0.5rem;>
  <span class="visually-hidden"></span>
</div>
<div class="spinner-grow text-primary ms-2" role="status" style="height:0.5rem;width: 0.5rem;>
  <span class="visually-hidden"></span>
</div>`

let secondSpinner = `<div class="d-flex flex-row"><div>${borderSpinner}</div>
<div class='ms-2'>Searching</div></div>`

let thirdSpinner = `<div class="d-flex flex-row"><div>${borderSpinner}</div>
<div class='ms-2'>Generating responses...</div></div>`



const sendChatMessage = (chatMessage, actionMessage = '') => {
    setTimeout(() => {
        $(`#${chatId}`).empty()
        $(`#${chatId}`).append(secondSpinner)
        setTimeout(() => {
            $(`#${chatId}`).empty()
            $(`#${chatId}`).append(thirdSpinner)
        }, 4000)
    }, 3000)

    $('.btnChatData').css('pointer-events', 'none').css('opacity', '0.5')

    let chatId = getRandomChatId('chat')
    let textCont = `<li class="more-button-list-item mt-1 mb-2 Bot-Chat loaderCont"> ${BotMode === 'lisa' ? aidaImage : daveImage} <div class="Message"><div class="d-flex align-items-end mt-2"><span class='me-2' id=${chatId}>${spinner}</span><small class="time"></small> </div> </div></li>`
    $('#chatBotContainer').append(textCont).show('slow')
    scrollToDown()
    isCompleted = false
    globalCount = 0;
    globalChatArray = [];
    //let chatMessage = sessionStorage.getItem('chatBotToken') === null || sessionStorage.getItem('chatBotToken') === 'undefined' ? 'hi' :  $('#chatBotInput').val();
    let sessionToken = sessionStorage.getItem('chatBotToken') === null || sessionStorage.getItem('chatBotToken') === 'undefined' ? '' : sessionStorage.getItem('chatBotToken')
    let userName = $('#userLoginName').text().replace(/ /g, '')

    let data = {
        token: sessionToken,
        action: actionMessage,
        UserId: "195",
        q: chatMessage,
        nlumodel: "20190516_095554",
        agentname: "bala1",
        userName: userName
    }

    let url = ''
    if (chatURL !== '') {
        url = `${chatURL}av3arapp/new_chat_response`
    } else {
        url = `${getChatBotURL}av3arapp/new_chat_response`
    }


    $.ajax({
        type: "POST",
        url: url,
        headers: { "Authorization": sessionToken },
        data: data,
        //dataType: "json",
        success: async function (result) {

            if (result.chatbot_type === 'Dave') {
                if (BotMode === 'lisa') {
                    let data = {
                        'data': {
                            'botsays': [{ 'value': `Looks like it's script o'clock! That's Dave's speciality. I'm transferring you to him for a tailored code generation.` }]
                        }
                    }

                    $('.loaderCont').remove()
                    textChatBox(data)
                    BotMode = 'dave'

                    setTimeout(() => {

                        $('#Chat_Bot').addClass('DaveClass')
                        $('#botHeaderImage').empty()

                        $('#botHeaderImage').append(`<div class="d-flex align-items-center"><img src="/../img/Profile-img/dave.png" class="me-2"/><div class="d-grid fw-normal"><small>Chat with</small><span>Dave</span></div></div> `)
                    }, 3000)
                    await delay(4000)
                }
            } else {
                if (BotMode === 'dave') {
                    await delay(2000)
                }

                if ($('#Chat_Bot').hasClass('DaveClass')) {
                    $('#Chat_Bot').removeClass('DaveClass')
                }

                BotMode = 'lisa';

                $('#botHeaderImage').empty()
                $('#botHeaderImage').append(` <div class="d-flex align-items-center"><img src="/../img/Profile-img/lisa.png" class="me-2"/><div class="d-grid fw-normal"><small>Chat with</small><span>Lisa</span></div></div>`)
            }
            sessionStorage.getItem('chatBotToken') === null || sessionStorage.getItem('chatBotToken') === 'undefined' ? sessionStorage.setItem('chatBotToken', result.sess_key) : null
            globalChatArray = result.response
            getChatMessage(result.response)

        },
        error: function (err) {
            $('.loaderCont').remove();
            $('#chatBotSubmitBtn').prop('disabled', false);
            $('#chatBotInput').prop('disabled', false)

            let data = {
                'data': {
                    'botsays': [{ 'value': `Error occured while fetching the response...` }]
                }
            }
            textChatBox(data)
        }
    })


}

$(document).on('click', '#chatBotSubmitBtn', function () {
    let userInput = '';
    $('#chatBotSubmitBtn').prop('disabled', true)
    setTimeout(() => {
        $('#chatBotSubmitBtn').prop('disabled', false)
    }, 2000)
    if ($('#chatBotInput').prop('type') == 'text') {
        userInput = $('#chatBotInput').val();
    } else if ($('#chatBotInput').prop('type') === 'date') {
        userInput = $('#chatBotInput').val().split('-').reverse().join('-')
    }
    setTimeout(() => {
        if (userInput !== '') {
            setUserMessage(userInput)
        }
    }, 1000)

    if (dateStatus) {
        $('#chatBotInput').prop('type', 'text')
        dateStatus = false;
    }

})

const setUserMessage = async (text, action) => {
    let userCont = `<li class="more-button-list-item User-Chat"><div class="Message"><div class="d-flex align-items-end">
                            <small class="time">${getCurrentTime()}</small> <span >${text}</span></div></div>${userImage}</li>`

    $('#chatBotContainer').append(userCont);
    $('#chatBotInput').val('').focus();
    scrollToDown()
    if (typeof action === 'string' && action.length > 1) {
        await sendChatMessage('form sent', action);
    } else {
        await sendChatMessage(text, action);
    }

}



const getChatMessage = async () => {
    if (globalChatArray.length > 0) {
        $('#chatBotInput').prop('disabled', true)

        chatIterationCont(globalChatArray[globalCount])
        if (globalChatArray[globalCount].type !== "Text") {
            globalCount++
            await delay(2000)
            if (globalChatArray.length > globalCount) {
                chatIterationCont(globalChatArray[globalCount])
            } else {
                isCompleted = true
            }

        }

    }

}

const chatIterationCont = async (data) => {
    $('.loaderCont').remove()
    if (data.type === "Text") {
        $('#chatBotInput').prop('type', 'text')
        textChatBox(data)
    } else if (data.type === "Button") {
        btnChatBox(data)
    } else if (data.type === "Form") {
        formChatBox(data)
    } else if (data.type === 'html') {
        htmlChatBox(data)
    } else if (data.type === 'Ask question') {
        AskQuestionChatBox(data)
    } else if (data.type === 'Table') {
        TableChatBox(data)
    } else if (data.type === 'code') {
        codeChatBox(data)
    }
    if ((globalChatArray.length - 1) === globalCount) {
        $('#chatBotInput').prop('disabled', false)
        $('#chatBotInput').val('').focus();
    }
    if (globalChatArray[globalCount].type !== "Text") {
        globalCount++
        await delay(2000)
        if (globalChatArray.length > globalCount) {
            chatIterationCont(globalChatArray[globalCount])
        } else {
            isCompleted = true
        }

    }

}

const codeChatBox = (data) => {
    let chatId = getRandomChatId('chat')
    let codeCont = `<li class="more-button-list-item mt-1 Bot-Chat ">${BotMode == 'lisa' ? aidaImage : daveImage}<div class="d-flex flex-wrap gap-2 code_ChatBot">`
    codeCont += `<pre id=${chatId} ><code class="php" style="width:270px;word-break:break-word;white-space:break-spaces;">${data.data.botsays[0].value.replace(/@#@/g, "'").replace(/#@#/g, "\\\"")}</code></pre>`
    codeCont += `</div></li>`
    $('#chatBotContainer').append(codeCont).show('slow')
    hljs.highlightBlock(document.getElementById(chatId).querySelector('code'));

    setTimeout(() => {
        $('#' + chatId).copyToClipboard({
            buttonText: 'Copy',
        });
    }, 1000)
    scrollToDown()
}


const TableChatBox = (data) => {
    let obj = Object.keys(data.data[0])
    let btnCont = ` <li class="more-button-list-item mt-1 Bot-Chat">${BotMode === 'lisa' ? aidaImage : daveImage}<div class="d-flex flex-wrap gap-2">`
    //for (let i = 0; i < data.data.buttons.length; i++) {
    btnCont += `<div><table><tr><th>Parameter</th><th>Value</th>`

    obj.forEach((item) => {
        btnCont += `<tr><td>${item}</td><td>${data.data[0][item]}</td></tr>`
    })

    btnCont += `</table></div>`
    //}
    btnCont += `</div></li>`
    $('#chatBotContainer').append(btnCont).show('slow')
}

const AskQuestionChatBox = (data) => {
    if (data.data[0].validationType === 'Date' || data.data[0].validationType === 'End Date' || data.data[0].validationType === 'start_date' || data.data[0].validationType === 'end_date') {
        let chatId = getRandomChatId('chat')
        let textCont = `<li class="more-button-list-item mt-1 mb-3 Bot-Chat"> ${BotMode === 'lisa' ? aidaImage : daveImage} <div class="Message"><div class="d-flex align-items-end"><span id=${chatId}></span> <small class="time">${getCurrentTime()}</small> </div> </div></li>`
        $('#chatBotContainer').append(textCont).show('slow')
        setTimeout(() => {
            typeWriter(data.data[0].questions[0].value.replace(/@#@/g, "'").replace(/#@#/g, "\\\""), chatId)
            scrollToDown()
        }, 50)
    }
    $('#chatBotInput').attr('type', 'date')
    if (data.data[0].validationType === 'End Date' || data.data[0].validationType === 'end_date') {
        dateStatus = true
    }
    scrollToDown()
}


$(document).on('click', '#btnDateChatSend', function () {
    let value = $('#chatDateValue').val().split('-').reverse().join('-')
    setUserMessage(value)
})

const getCurrentTime = () => {
    let dateValue = new Date().toLocaleTimeString().split(':')
    let hours = '';
    if (+dateValue[0] < 9) {
        hours = '0' + dateValue[0]
    } else {
        hours = dateValue[0]
    }
    return hours + ':' + dateValue[1]
}

const textChatBox = (data) => {
    let chatId = getRandomChatId('chat')
    let textCont = `<li class="more-button-list-item mt-1 mb-3 Bot-Chat"> ${BotMode === 'lisa' ? aidaImage : daveImage} <div class="Message"><div class="d-flex align-items-end"><span id=${chatId} class='divContstrain'></span> <small class="time">${getCurrentTime()}</small> </div> </div></li>`
    $('#chatBotContainer').append(textCont).show('slow')

    setTimeout(() => {
        typeWriter(data.data.botsays[0].value.replace(/@#@/g, "'").replace(/#@#/g, "\\\""), chatId)
        scrollToDown()
    }, 50)
}

const btnChatBox = (data) => {
    let btnCont = ` <li class="more-button-list-item Bot-Chat">${BotMode === 'lisa' ? aidaImage : daveImage}<div class="d-flex flex-wrap gap-2 p-2 pb-0">`
    for (let i = 0; i < data.data.buttons.length; i++) {
        btnCont += `<button class="btn btn-sm btn-outline-primary rounded-pill btnChatData" >${data.data.buttons[i].title}</button>`
    }
    btnCont += `</div></li>`
    $('#chatBotContainer').append(btnCont).show('slow')
    $('#chatBotContainer').append(`<li> <div id="Loader" class="justify-content-center align-items-center h-100 gap-2 hidden"></li>`)
    scrollToDown()
}

const htmlChatBox = (data) => {
    let btnCont = ` <li class="more-button-list-item mt-1 Bot-Chat">${BotMode === 'lisa' ? aidaImage : daveImage}<div class="d-flex flex-wrap gap-2">`
    //for (let i = 0; i < data.data.buttons.length; i++) {
    btnCont += `<div>${data.data.botsays[0].value.replace(/@#@/g, "'").replace(/#@#/g, "\\\"")}</div>`
    //}
    btnCont += `</div></li>`
    $('#chatBotContainer').append(btnCont).show('slow')
    $('#chatBotContainer').append(`<li> <div id="Loader" class="justify-content-center align-items-center h-100 gap-2 hidden"></li>`)
    scrollToDown()

}

const formChatBox = (data) => {
    formJson = data
    let formCont = '';
    let value = data.data.fields
    formCont += `<li class="more-button-list-item formChatContainer"> ${BotMode === 'lisa' ? aidaImage : daveImage}                     
                         <div class="d-flex flex-wrap gap-1">    
                         <div>${data.data.message}</div>`
    for (let i = 0; i < value.length; i++) {
        if (value[i].typeData.type === 'regex') {
            formCont += ` <div class="form-group inputCont" id=${getRandomChatId('chat')}> <div class="input-group bg-white border rounded-2">  <span class="input-group-text ps-2"><i class="cp-name"></i>
                                    </span> <input class="form-control chatBotTextInput" type="text" index='${i}' placeholder=${value[i].value} /> </div> </div>`
        } else if (value[i].typeData.type === 'Entity') {
            formCont += `<div class="form-group inputCont" id=${getRandomChatId('chat')}> <div class="input-group bg-white border rounded-2"><span class="input-group-text ps-2">
                                    <i class="cp-name"></i> </span> <select index='${i}' class="form-select w-100 chatBotSelectInput" id=""> <option selected>Open this select menu</option>`
            if (value[i].typeData.pattern.length === 0) {
                value[i].typeData.pattern_data.forEach((d) => {
                    formCont += `<option value=${d.id}>${d.name}</option>`
                })
            } else {
                value[i].typeData.pattern.forEach((d) => {
                    formCont += `<option value=${d}>${d}</option>`
                })
            }
            formCont += `</select></div></div>`
        } else if (value[i].typeData.type === 'duckling') {
            formCont += ` <div class="form-group inputCont" id=${getRandomChatId('chat')}> <div class="input-group bg-white border rounded-2">  <span class="input-group-text ps-2"><i class="cp-name"></i>
                                    </span> <input index='${i}' class="form-control chatBotDateInput" type="date" placeholder=${value[i]?.value} /> </div> </div>`
        }
    }
    formCont += `<button class='btn btn-primary formSelectChatClass' id='btnFormChatSend'>Send</button></div>`
    $('#chatBotContainer').append(formCont)
    scrollToDown()
}


$(document).on('click', '#btnFormChatSend', function (e) {
    let formContainer = $(this).parents().children('.inputCont')
    formContainer.each(function (idx, obj) {
        console.log(obj)
        let formId = obj.id
        if (formId !== null && formId !== undefined) {
            if ($(`#${formId} input`).prop('type') === 'text') {
                let index = +$('#' + obj.id + ' input').attr('index')
                formJson.data.fields[index]['userVal'] = $(`#${formId} input`).val()
            } else if ($(`#${formId} input`).prop('type') === 'date') {
                let index = +$('#' + obj.id + ' input').attr('index')
                let value = $('#' + obj.id + ' input').val().split('-').reverse().join('-')
                formJson.data.fields[index]['userVal'] = value
            } else if ($(obj).find('select').length === 1) {
                let index = +$('#' + obj.id + ' select').attr('index')
                formJson.data.fields[index]['userVal'] = $(`#${formId} select`).val()
            }
        }
    })
    formJson = JSON.stringify(formJson)
    setUserMessage('Form sent', formJson)
})

$(document).on('click', '.btnChatData', function (e) {
    if (isCompleted) {
        setUserMessage(this.textContent)
    }

})

async function typeWriter(txt, id) {
    let elem = $("#" + id)[0];
    let resizeObserver = new ResizeObserver(() => {
        scrollToDown();
    });

    resizeObserver.observe(elem);

    let typed2 = new Typed(`#${id}`, {
        strings: [txt],
        typeSpeed: 10,
        backSpeed: 0,
        showCursor: false,
        onComplete: async function (pos, sel) {
            //clearInterval(myInterval)
            globalCount++
            await delay(1000)
            if (globalChatArray.length > globalCount) {
                chatIterationCont(globalChatArray[globalCount])
            } else {
                isCompleted = true
            }



        }
    });

}

$(document).on('keypress', function (e) {
    if (e.which == 13) {
        $('#chatBotSubmitBtn').click();
    }
})

$(window).bind('load', function () {
    localStorage.getItem('chatBotData') !== null ? $('#chatBotContainer').append(JSON.parse(localStorage.getItem('chatBotData'))) : null
    let contStatus = localStorage.getItem('chatBotStatus')
    if (contStatus === 'true') {
        $('#chatContainerDiv').show()
    } else {
        $('#chatContainerDiv').hide()
    }

    scrollToDown()
    isCompleted = true
})

$(window).bind("beforeunload", function () {
    let chatData = JSON.stringify($('#chatBotContainer').html())
    if ($('#chatBotContainer').children().length > 0) {
        localStorage.setItem('chatBotData', chatData)
        localStorage.setItem('chatBotStatus', $('#chatContainerDiv').is(':visible'))
    } else {
        localStorage.removeItem('chatBotData')
        localStorage.removeItem('chatBotStatus')
    }
})










