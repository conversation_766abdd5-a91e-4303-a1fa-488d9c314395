using ContinuityPatrol.Application.Features.CyberComponentGroup.Events.Create;

namespace ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;

public class
    CreateCyberComponentGroupCommandHandler : IRequestHandler<CreateCyberComponentGroupCommand,
        CreateCyberComponentGroupResponse>
{
    private readonly ICyberComponentGroupRepository _cyberComponentGroupRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateCyberComponentGroupCommandHandler(IMapper mapper,
        ICyberComponentGroupRepository cyberComponentGroupRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _cyberComponentGroupRepository = cyberComponentGroupRepository;
    }

    public async Task<CreateCyberComponentGroupResponse> Handle(CreateCyberComponentGroupCommand request,
        CancellationToken cancellationToken)
    {
        var cyberComponentGroup = _mapper.Map<Domain.Entities.CyberComponentGroup>(request);

        cyberComponentGroup = await _cyberComponentGroupRepository.AddAsync(cyberComponentGroup);

        var response = new CreateCyberComponentGroupResponse
        {
            Message = Message.Create("Component group", cyberComponentGroup.GroupName),

            Id = cyberComponentGroup.ReferenceId
        };

        await _publisher.Publish(new CyberComponentGroupCreatedEvent { Name = cyberComponentGroup.GroupName },
            cancellationToken);

        return response;
    }
}