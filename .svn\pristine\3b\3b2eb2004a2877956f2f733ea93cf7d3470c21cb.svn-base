using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SmsConfigurationRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SmsConfigurationRepository _repository;
    private readonly SmsConfigurationFixture _fixture;

    public SmsConfigurationRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _repository = new SmsConfigurationRepository(_dbContext);
        _fixture = new SmsConfigurationFixture();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllSmsConfigurations()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig1 = _fixture.CreateSmsConfiguration(url: "https://sms1.example.com", senderId: "SENDER1");
        var smsConfig2 = _fixture.CreateSmsConfiguration(url: "https://sms2.example.com", senderId: "SENDER2");

        await _repository.AddAsync(smsConfig1);
        await _repository.AddAsync(smsConfig2);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, s => s.URL == "https://sms1.example.com");
        Assert.Contains(result, s => s.URL == "https://sms2.example.com");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoConfigurations()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnConfiguration_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(
            url: "https://sms.example.com",
            senderId: "TEST_SENDER",
            userName: "testuser",
            password: "testpass",
            recipientNo: "1234567890"
        );

        await _repository.AddAsync(smsConfig);

        // Act
        var result = await _repository.GetByReferenceIdAsync(smsConfig.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(smsConfig.ReferenceId, result.ReferenceId);
        Assert.Equal("https://sms.example.com", result.URL);
        Assert.Equal("TEST_SENDER", result.SenderId);
        Assert.Equal("testuser", result.UserName);
        Assert.Equal("testpass", result.Password);
        Assert.Equal("1234567890", result.RecipientNo);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }



    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnConfiguration_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(url: "https://sms.example.com");
        await _repository.AddAsync(smsConfig);

        // Act
        var result = await _repository.GetByIdAsync(smsConfig.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(smsConfig.Id, result.Id);
        Assert.Equal("https://sms.example.com", result.URL);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(
            url: "https://newsms.example.com",
            senderId: "NEW_SENDER",
            userName: "newuser",
            password: "newpass",
            recipientNo: "9876543210",
            properties: "{\"timeout\": 30}"
        );

        // Act
        var result = await _repository.AddAsync(smsConfig);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.Equal("https://newsms.example.com", result.URL);
        Assert.Equal("NEW_SENDER", result.SenderId);
        Assert.Equal("newuser", result.UserName);
        Assert.Equal("newpass", result.Password);
        Assert.Equal("9876543210", result.RecipientNo);
        Assert.Equal("{\"timeout\": 30}", result.Properties);

        // Verify it was saved to database
        var savedConfig = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(savedConfig);
        Assert.Equal(result.URL, savedConfig.URL);
    }

    [Fact]
    public async Task AddAsync_ShouldThrowException_WhenEntityIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(url: "https://original.example.com", senderId: "ORIGINAL");
        await _repository.AddAsync(smsConfig);

        // Modify the entity
        smsConfig.URL = "https://updated.example.com";
        smsConfig.SenderId = "UPDATED";
        smsConfig.UserName = "updateduser";
        smsConfig.Password = "updatedpass";

        // Act
        var result = await _repository.UpdateAsync(smsConfig);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("https://updated.example.com", result.URL);
        Assert.Equal("UPDATED", result.SenderId);
        Assert.Equal("updateduser", result.UserName);
        Assert.Equal("updatedpass", result.Password);

        // Verify it was updated in database
        var updatedConfig = await _repository.GetByReferenceIdAsync(result.ReferenceId);
        Assert.NotNull(updatedConfig);
        Assert.Equal("https://updated.example.com", updatedConfig.URL);
        Assert.Equal("UPDATED", updatedConfig.SenderId);
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveConfiguration_WhenValidEntity()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(url: "https://todelete.example.com");
        await _repository.AddAsync(smsConfig);

        // Act
        var result = await _repository.DeleteAsync(smsConfig);

        // Assert
        Assert.NotNull(result);

        // Verify it was removed from database
        var deletedConfig = await _repository.GetByReferenceIdAsync(smsConfig.ReferenceId);
        Assert.Null(deletedConfig);
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnMatchingConfigurations()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig1 = _fixture.CreateSmsConfiguration(url: "https://sms1.example.com", senderId: "SENDER1");
        var smsConfig2 = _fixture.CreateSmsConfiguration(url: "https://sms2.example.com", senderId: "SENDER2");
        var smsConfig3 = _fixture.CreateSmsConfiguration(url: "https://sms3.example.com", senderId: "SENDER1");

        await _repository.AddAsync(smsConfig1);
        await _repository.AddAsync(smsConfig2);
        await _repository.AddAsync(smsConfig3);

        // Act
        var result = await _repository.FindByFilterAsync(s => s.SenderId == "SENDER1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, s => Assert.Equal("SENDER1", s.SenderId));
    }

    [Fact]
    public async Task FindByFilterAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        var smsConfig = _fixture.CreateSmsConfiguration(senderId: "SENDER1");
        await _repository.AddAsync(smsConfig);

        // Act
        var result = await _repository.FindByFilterAsync(s => s.SenderId == "NONEXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    //[Fact]
    //public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    var configs = _fixture.CreateMultipleSmsConfigurations(5);
    //    foreach (var config in configs)
    //    {
    //        await _repository.AddAsync(config);
    //    }

    //    string? searchString = null;

    //    var specification = new SmtpConfigurationFilterSpecification(searchString);
    //    // Act
    //    var result = await _repository.PaginatedListAllAsync(1, 3, specification, "Id", "asc");

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(3, result.Data.Count);
    //    Assert.Equal(5, result.TotalCount);
    //    Assert.Equal(3, result.PageSize);
    //    Assert.Equal(2, result.TotalPages);
    //}

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SmsConfigurations.RemoveRange(_dbContext.SmsConfigurations);
        await _dbContext.SaveChangesAsync();
    }
}
