﻿using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Create;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Update;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Events.Pagination;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using Newtonsoft.Json;
namespace ContinuityPatrol.Web.Areas.Drift.Controllers;

[Area("Drift")]
public class DriftDashboardController : BaseController
{
    private readonly IDataProvider _provider;
    private readonly IMapper _mapper;
    private readonly ILogger<DriftDashboardController> _logger;
    private readonly IPublisher _publisher;


    public static string CompanyLogo { get; set; }
    public string reportsDirectory { get; set; }

    public DriftDashboardController(IDataProvider provider, IMapper mapper, ILogger<DriftDashboardController> logger, IPublisher publisher)
    {
        _publisher = publisher;
        _provider = provider;
        _mapper = mapper;
        _logger = logger;
    }
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new DriftDashboardPaginatedEvent());
        return View();
    }

    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftTreeList()
    {
        _logger.LogDebug("Entering GetDriftManagementList method in Drift Dashboard Page.");
        try
        {
            var GetDriftManagementList = await _provider.DriftManagementMonitorStatus.GetDriftTreeList();
            _logger.LogDebug("Successfully retrieved driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = GetDriftManagementList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> UpdateDriftSnap(DriftManagementMonitorStatusViewModel DriftMonitoringData)
    {
        try
        {
            //var DriftMonitoringId = Request.Form["id"];
            BaseResponse result;
            var DriftMonitoringModel = _mapper.Map<UpdateDriftManagementMonitorStatusCommand>(DriftMonitoringData);
            result = await _provider.DriftManagementMonitorStatus.UpdateAsync(DriftMonitoringModel);
            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }


    }


    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftManagementStatusByInfraObjectId(string infraId)
    {
        _logger.LogDebug("Entering DriftManagementStatusByInfraObjectId method in Drift Dashboard Page.");
        try
        {
            var DriftManagementStatus = await _provider.DriftManagementMonitorStatus.GetDriftManagementStatusByInfraObjectId(infraId);
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = DriftManagementStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftOperationSummary()
    {
        _logger.LogDebug("Entering DriftOperationSummary method in Drift Dashboard Page.");
        try
        {
            var DriftOperationSummary = await _provider.DriftManagementMonitorStatus.GetDriftOperationSummary();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = DriftOperationSummary });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the Drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftDashboardResourceStatus()
    {
        _logger.LogDebug("Entering DriftDashboardResourceStatus method in Drift Dashboard Page.");
        try
        {
            var DriftDashboardResourceStatus = await _provider.DriftManagementMonitorStatus.GetDriftDashboardResourceStatus();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = DriftDashboardResourceStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftResourceSummary()
    {
        _logger.LogDebug("Entering GetDriftResourceSummary method in Drift Dashboard Page.");
        try
        {
            var DriftResourceSummary = await _provider.DriftManagementMonitorStatus.GetDriftResourceSummary();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = DriftResourceSummary });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftCategory()
    {
        _logger.LogDebug("Entering GetDriftManagementList method in Drift Dashboard Page.");
        try
        {
            var GetDriftCategory = await _provider.DriftManagementMonitorStatus.GetDriftCategory();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = GetDriftCategory });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetConflictOverView()
    {
        _logger.LogDebug("Entering GetDriftManagementList method in Drift Dashboard Page.");
        try
        {
            var GetConflictOverView = await _provider.DriftManagementMonitorStatus.GetConflictOverView();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = GetConflictOverView });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> GetDriftManagementMonitorStatusList()
    {
        _logger.LogDebug("Entering GetDriftManagementList method in Drift Dashboard page.");
        try
        {
            var DriftManagementMonitorStatusList = await _provider.DriftManagementMonitorStatus.GetDriftManagementMonitorStatusList();
            _logger.LogDebug("Successfully retrieved InfraObjectId Based on driftManagement list in Drift Dashboard Page.");
            return Json(new { success = true, data = DriftManagementMonitorStatusList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on Drift Dashboard Page while retrieving the drift Dashboard list.", ex);
            return ex.GetJsonException();
        }

    }

    public async Task<IActionResult> GetDriftReport(string startDate, string endDate, string InfraId, string DriftStatusId)
    {
        CompanyLogo = string.Empty;
        var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
        if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
        var driftReport = await _provider.Report.GetDriftReport(startDate, endDate, InfraId, DriftStatusId);
        if (driftReport.DriftEventReportVm.Count > 0)
        {
            var driftreport = JsonConvert.SerializeObject(driftReport);
            _logger.LogInformation($"Get Drift Report Report values successfully.");
            return Json(new { success = true, data = driftreport });
        }
        return Json(new { success = false, data = driftReport });
    }
    public async Task<IActionResult> GetDriftReports(string data)
    {
        reportsDirectory = "";
        try
        {
            XtraReport report = new DriftReport(data);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            var fileName = "DriftReport_" + filenameSuffix + ".pdf";
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
            report.ExportToPdf(reportsDirectory);
            await GetCompanyLogo();
            byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);


            return File(fileBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while downloading workflow operation report.", ex);

            return Json(new { success = false, message = ex.GetMessage() });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
    public async Task GetCompanyLogo()
    {
        CompanyLogo = string.Empty;
        var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
        if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> DriftEventCreateOrUpdate (DriftEventViewModel driftEvent)
    {
        try
        {
            var driftId = Request.Form["id"];
            BaseResponse result;

            if (string.IsNullOrEmpty(driftId))
            {
                var driftEventModel = _mapper.Map<CreateDriftEventCommand>(driftEvent);
                result = await _provider.DriftEvent.CreateAsync(driftEventModel);
            }
            else
            {
                var driftEventModel = _mapper.Map<UpdateDriftEventCommand>(driftEvent);
                result = await _provider.DriftEvent.UpdateAsync(driftEventModel);
            }

            return Json(result);
        }
        catch(ValidationException e)
        {
            return Json(e.GetJsonException());
        }
        catch(Exception ex)
        {
            return Json(ex.GetJsonException());
        }
    }

}

