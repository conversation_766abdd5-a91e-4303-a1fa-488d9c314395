﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Events
{
    public class DeleteRoboCopyJobEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<RoboCopyJobDeletedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly RoboCopyJobDeletedEventHandler _handler;

        public DeleteRoboCopyJobEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<RoboCopyJobDeletedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();

            _handler = new RoboCopyJobDeletedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldCreateUserActivity_WhenRoboCopyJobDeleted()
        {
            var deletedEvent = new RoboCopyJobDeletedEvent
            {
                ReplicationName = "TestReplication"
            };

            _mockUserService.Setup(s => s.UserId).Returns("test-user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("test-login-name");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo =>
                repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                    activity.UserId == "test-user-id" &&
                    activity.LoginName == "test-login-name" &&
                    activity.RequestUrl == "http://testurl.com" &&
                    activity.CompanyId == "test-company-id" &&
                    activity.HostAddress == "127.0.0.1" &&
                    activity.Action == "Delete RoboCopyJob" &&
                    activity.Entity == "RoboCopyJob" &&
                    activity.ActivityType == "Delete" &&
                    activity.ActivityDetails == "RoboCopy Job 'TestReplication' deleted successfully" &&
                    !string.IsNullOrEmpty(activity.CreatedBy) &&
                    !string.IsNullOrEmpty(activity.LastModifiedBy)
                )),
                Times.Once);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(s => s.Contains("RoboCopy Job 'TestReplication' deleted successfully"))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldGenerateGuid_WhenUserIdIsEmpty()
        {
            var deletedEvent = new RoboCopyJobDeletedEvent
            {
                ReplicationName = "TestReplication"
            };

            _mockUserService.Setup(s => s.UserId).Returns(string.Empty);
            _mockUserService.Setup(s => s.LoginName).Returns("test-login-name");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo =>
                repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                    IsValidGuid(activity.CreatedBy) &&
                    IsValidGuid(activity.LastModifiedBy)
                )),
                Times.Once);
        }

        private bool IsValidGuid(string value)
        {
            return Guid.TryParse(value, out _);
        }
    }
}
