﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Wrapper;
using Microsoft.AspNetCore.Http;


namespace ContinuityPatrol.Persistence.Repositories;

public class TableAccessRepository : BaseRepository<TableAccess>, ITableAccessRepository
{
    private readonly ApplicationDbContext _dbContext;

    public TableAccessRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }

    public Task<bool> IsTableAccessNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.TableAccesses.Any(e => e.TableName.Equals(name)))
            : Task.FromResult(_dbContext.TableAccesses.Where(e => e.TableName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsTableAccessNameUnique(string name)
    {
        var matches = _dbContext.TableAccesses.Any(e => e.TableName.Equals(name));

        return Task.FromResult(matches);
    }

    public override async Task<IReadOnlyList<TableAccess>>ListAllAsync()
    {
        return await _dbContext.TableAccesses.Active().Select(x=> new TableAccess
        {
            Id=x.Id,
            ReferenceId=x.ReferenceId,
            TableName=x.TableName,
            SchemaName=x.SchemaName,
            IsChecked=x.IsChecked
        }).ToListAsync();
    }

    public Task<List<TableAccess>> GetTableAccessNames()
    {
        return _dbContext.TableAccesses.Active()
            .Select(x => new TableAccess
                { ReferenceId = x.ReferenceId, TableName = x.TableName, SchemaName = x.SchemaName })
            .OrderBy(x => x.SchemaName)
            .ToListAsync();
    }

    public Task<List<TableAccess>> GetSchemaNameList()
    {
        return _dbContext.TableAccesses.Active().ToListAsync();
    }

    public Task<List<TableAccess>> GetTableNameListBySchema(string schemaName)
    {
        return _dbContext.TableAccesses.Active()
            .Where(x => x.SchemaName.Equals(schemaName) && x.IsChecked).Select(x => new TableAccess
            {Id=x.Id, ReferenceId = x.ReferenceId, TableName = x.TableName, SchemaName = x.SchemaName ,IsChecked=x.IsChecked})
            .ToListAsync();
    }
    public async Task<List<TableAccess>> GetTableAccessByTableNames(List<string> tableNames)
    {
        var  tableAccess=await _dbContext.TableAccesses.Active().AsNoTracking().ToListAsync();
      
        var results = tableAccess.Where(x => tableNames.Any(tb => tb.Equals(x.TableName))).ToList();

        return results;
    }
    public async  Task<List<TableAccess>> GetUnUsedTableAccessByTableNames(List<string> tableNames)
    {
        var tableAccess = await _dbContext.TableAccesses.Active().AsNoTracking().ToListAsync();

        var results = tableAccess.Where(x => !tableNames.Contains(x.TableName)).ToList();

        return results;
    }
    public Task<TableAccess> GetTableAccessByTableName(string tableName)
    {
        return _dbContext.TableAccesses
            .Active()
            .Where(x => x.TableName.Equals(tableName))
            .FirstOrDefaultAsync();
    }

    public async Task<List<TableInformation>> GetSchemas(string schema, string provider)
    {
        if (provider.ToLower().Equals("mysql"))
        {
            var sql =
                $"SELECT TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '{schema}'";

            return await _dbContext.TableInformation.FromSqlRaw(sql).ToListAsync();
        }

        if (provider.ToLower().Equals("mssql"))
        {
            var sql = $"SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_CATALOG = '{schema}'";

            return await _dbContext.TableInformation.FromSqlRaw(sql).ToListAsync();
        }
        else
        {
            var sql = $"SELECT 'Oracle Database' AS TABLE_CATALOG, USER AS TABLE_SCHEMA, TABLE_NAME FROM USER_TABLES;";

            return await _dbContext.TableInformation.FromSqlRaw(sql).ToListAsync();
        }

    }

    public async Task<List<TableAccessListVm>> GetTableAccessListAsync()
    {
        //var tableAccess = await _dbContext.TableAccesses.Active().
        //    Select(x => new TableAccessListVm
        //    {
        //        Id = x.ReferenceId,
        //        TableName = x.TableName,
        //        SchemaName = x.SchemaName,
        //        IsChecked = x.IsChecked,
        //        //IsConfigured = _dbContext.Archives.Any(e => e.TableNameProperties.Contains(x.ReferenceId)) 
        //    }).ToListAsync();

        var tableAccesses = await _dbContext.TableAccesses.Active().ToListAsync();

        var archiveData = await _dbContext.Archives
            .Select(a => new {TableNameProps = a.TableNameProperties.ToString() })
            .ToListAsync();

        var datasets = await _dbContext.DataSets.Active()
            .Select(a => new { TableAccessId = a.TableAccessId })
            .ToListAsync();

        var tableAccess = tableAccesses.Select(x => new TableAccessListVm
        {
            Id = x.ReferenceId,
            TableName = x.TableName,
            SchemaName = x.SchemaName,
            IsChecked = x.IsChecked,
            IsConfigured = archiveData.Any(a => a.TableNameProps.Contains(x.ReferenceId.ToString())) || datasets.Any(a => a.TableAccessId.Contains(x.ReferenceId.ToString())),
        }).ToList();

        return tableAccess;
    }
}