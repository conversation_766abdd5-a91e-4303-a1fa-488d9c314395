using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaTemplateFixture : IDisposable
{
    public List<FiaTemplate> FiaTemplatePaginationList { get; set; }
    public List<FiaTemplate> FiaTemplateList { get; set; }
    public FiaTemplate FiaTemplateDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public FiaTemplateFixture()
    {
        var fixture = new Fixture();

        FiaTemplateList = fixture.Create<List<FiaTemplate>>();

        FiaTemplatePaginationList = fixture.CreateMany<FiaTemplate>(20).ToList();

        FiaTemplatePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaTemplatePaginationList.ForEach(x => x.IsActive = true);

        FiaTemplateList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FiaTemplateList.ForEach(x => x.IsActive = true);

        FiaTemplateDto = fixture.Create<FiaTemplate>();
        FiaTemplateDto.ReferenceId = Guid.NewGuid().ToString();
        FiaTemplateDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
