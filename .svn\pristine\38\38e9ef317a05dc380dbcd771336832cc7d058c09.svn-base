﻿
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="margin-top:100px">'
function defaultDesign(expression) {
    let html = ""
    debugger
    switch (expression) {
        
        case "customTable":
            html += '<div class="card pageBuilderInfraId mb-0 dotted-border">'
            html += '<div class="dropdown">'
            html += '<div class="card-header card-title widgettitle" id="title"><i class="cp-edit me-2 iconcollection" onclick="iconCollection()"  data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside"></i><span id="labelHeaderTitle">Title</span>'
            //html += '<div class="card-header card-title widgettitle" id="title">Title'
            html += '<form class="dropdown-menu p-3 pt-2">'
            html += '<div class="form-group mb-0">'
            html += '<label class="form-label">Title Editing</label>'
            html += '<div class="input-group h-auto">'
            html += '<span class="input-group-text"><i class="cp-name"></i></span>'
            html += '<input class="form-control" id="labelTL" type="text" placeholder="Enter Label Name">'
            html += '<button class="btn btn-primary btn-sm rounded-1" type="button"  onclick="widgetLabel()">Update</button>'
            html += '</div>'
            html += '<span id="pagelabelTitle_error"></span>'
            html += '</div>'
            html += '</form>'
            html += '</div>'
            html += '</div>'
            html += '<div class="card-body pt-0">'
            html += '<table style="table-layout:fixed" class="table mb-0" id="tableCreation">'
            html += '<thead>'
            html += '<tr>'
            html += '<th class="ComponentClass"><span class="tablerowcolumn" tableclass="tablerowcolumn" contenteditable="true"  id="column_1">S.no</span></th>'
            // html += '<th id="columnAdd" title="Add Column" style="width:40px"><span class="cp-circle-plus fs-5 text-primary" ></span ></th>'
            // html += '<th title="Primary" class="text-primary"><span contenteditable="true">Primary</span></th><th title="DR"><span contenteditable="true">DR</span></th></tr>'
            html += '</thead>'
            html += '<tbody class="widgettable" id="widgettable">'
            html += '<tr class="dragtr">'
            html += '<td class="text-truncate fw-semibold" >'
            html += '<span class="d-flex align-items-center text-truncate">'
            //html += '<i class="cp-images me-1 cpimages" name="cpimages" icon="cp-images" color="select" onclick="iconBondle(this)"></i>'
            html += '<span contenteditable="true" class="tablerowcolumn text-truncate" tableclass="tablerowcolumn" id="row_1">1</span>'
            html += '</span>'
            html += '<div class="collapse mt-2" id="IconsCollapse">'
            html += '<div class="d-flex flex-wrap align-items-center">'
            html += '<div class="border p-2" role="button"><i class="cp-server"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-database"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-cloud"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-server-role"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-dataguard-status"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-virtualization"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-business-function"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-business-service"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-database-success"></i></div>'
            html += '</div>'
            html += '</div>'
            html += '</td >'
            html += '</tr >'

            html += '</tbody>'
            html += '</table>'
            html += '</div>'
           // html +='<div class="d-flex justify-content-end p-2" > <button type="button" id="conditionList" onclick="conditionList(this)" class="btn btn-primary btn-sm rounded-3 text-right conditionList"><i class="cp-add me-1"></i>Condition</button></div >'
            html += '</div>'
            $('#widgetCreationCard').append(html)
            break;
        case "customList":
            html += '<div class="card pageBuilderInfraId mb-0 dotted-border">'
            html += '<div class="dropdown">'
            html += '<div class="card-header card-title widgettitle" id="title"><i class="cp-edit me-2 iconcollection" onclick="iconCollection()"  data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside"></i><span id="labelHeaderTitle">Title</span>'
           //html += '<div class="card-header card-title widgettitle" id="title">Title'
            html += '<form class="dropdown-menu p-3 pt-2">'
            html += '<div class="form-group mb-0">'
            html += '<label class="form-label">Title Editing</label>'
            html += '<div class="input-group h-auto">'
            html += '<span class="input-group-text"><i class="cp-name"></i></span>'
            html += '<input class="form-control" id="labelTL" type="text" placeholder="Enter Label Name">'
            html += '<button class="btn btn-primary btn-sm rounded-1" type="button"  onclick="widgetLabel()">Update</button>'
            html += '</div>'
            html += '<span id="pagelabelTitle_error"></span>'
            html += '</div>'
            html += '</form>'
            html += '</div>'
            html += '</div>'
            html += '<div class="card-body pt-0">'
            html += '<table style="table-layout:fixed" class="table mb-0" id="tableCreation">'
            html += '<thead>'
            html += '<tr>'
            html += '<th class="ComponentClass"><span class="tablerowcolumn" tableclass="tablerowcolumn" contenteditable="true"  id="column_1">Label</span></th>' 
           // html += '<th id="columnAdd" title="Add Column" style="width:40px"><span class="cp-circle-plus fs-5 text-primary" ></span ></th>'
           // html += '<th title="Primary" class="text-primary"><span contenteditable="true">Primary</span></th><th title="DR"><span contenteditable="true">DR</span></th></tr>'
            html += '</thead>'
            html += '<tbody class="widgettable" id="widgettable">'
            html += '<tr class="dragtr">'
            html += '<td class="text-truncate fw-semibold" >'
            html += '<span class="d-flex align-items-center text-truncate">'
            html += '<i class="cp-images me-1 cpimages" name="cpimages" icon="cp-images" color="select" onclick="iconBondle(this)"></i>'
            html += '<span contenteditable="true" class="tablerowcolumn text-truncate" tableclass="tablerowcolumn" id="row_1">Label</span>'
            html += '</span>'
            html += '<div class="collapse mt-2" id="IconsCollapse">'
            html += '<div class="d-flex flex-wrap align-items-center">'
            html += '<div class="border p-2" role="button"><i class="cp-server"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-database"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-cloud"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-server-role"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-dataguard-status"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-virtualization"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-business-function"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-business-service"></i></div>'
            html += '<div class="border p-2" role="button"><i class="cp-database-success"></i></div>'
            html += '</div>'
            html += '</div>'
            html += '</td >'
            html += '</tr >'
            html += '</tbody>'
            html += '</table>'
            html += '</div>'
            //html += '<div class="d-flex justify-content-end p-2 " > <button type="button" id="conditionList" onclick="conditionList(this)" class="btn btn-primary btn-sm rounded-3 text-right conditionList"><i class="cp-add me-1"></i>Condition</button></div >'
            html += '</div>'
            $('#widgetCreationCard').append(html)
            break;
        case "customDiagram":
        case 'preBuildDiagram':
            html += "<div class='card Card_Design_None solutionDiagramData'>"
            html += "<div class='card-header card-title' > Solution Diagram</div>"
            html +=  "<div class='card-body text-center'>"
            html += "<div id='Solution_Diagram'>"
            html += '<img src="/../img/charts_img/pagebuilder-solution.svg" />'
            html +="</div >"
            html +="</div >"
            html +="</div >"
            $('#widgetCreationCard').append(html)
            break;
        case 'LogSequence':
             html+=   '<div class="d-flex gap-2" >'
            html +=   '<div class="card Card_Design_None w-50 mb-0">'
            html +=       '<div class="card-body">'
            html +=          '<i class="cp-log-sequence text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Log Sequence"></i><span title="Log Sequence" class="fw-semibold">Log Sequence</span>'
            html +=           '<div class=" mt-3">'
            html +=               '<div class="w-50 d-grid mb-0" ondrop="drop(event)" ondragover="allowDrop(event)">'
            html +=                  '<small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>'
            html +=                    '<h6 class="mb-0  fs-7" id="PR_Log_sequence" tableclass="tablerowcolumn" title="1231(Thread#1)">1231(Thread#1)</h6>'
            html +=                '</div>'
            html +=               '<div class="w-50 d-grid" ondrop="drop(event)" ondragover="allowDrop(event)">'
            html +=                   '<small class=" fs-7 mb-1 fw-semibold" title="DR">DR</small>'
            html +=                   '<h6 class="mb-0 fs-7 d-inline-block" tableclass="tablerowcolumn" id="DR_Log_sequence" title="1231(Thread#1)">1231(Thread#1)</h6>'
            html +=                '</div>'
            html +=            '</div>'
            html +=        '</div>'
            html +=      '</div>' 
            html += '</div >'

            $('#widgetCreationCard').append(html)
            break;
        case 'CurrentSCN':
            html += '<div class="d-flex gap-2" >'
            html += '<div class="card Card_Design_None w-50 mb-2">'
            html += '<div class="card-body">'
            html += '<i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Current SCN"></i><span title="Current SCN" class="fw-semibold">Current SCN</span>'
            html += '<div class=" mt-3">'
            html += '<div class="w-50 d-grid mb-2" ondrop="drop(event)" ondragover="allowDrop(event)">'
            html += '<small class="text-primary fs-7 mb-1 fw-semibold" title="Primary">Primary</small>'
            html += '<h6 class="mb-0  fs-7" id="PR_Currentscn" tableclass="tablerowcolumn" title="154315831">154315831</h6>'
            html += '</div>'
            html += '<div class="w-50 d-grid" ondrop="drop(event)" ondragover="allowDrop(event)">'
            html += '<small class=" fs-7 mb-1 fw-semibold" title="DR">DR</small>'
            html += '<h6 class="mb-0 fs-7" id="DR_Currentscn" tableclass="tablerowcolumn" title="154315697">154315697</h6>'
            html += '</div>'
            html += '</div>'
            html += '</div>'
            html += '</div>'
            html += '</div >'
            $('#widgetCreationCard').append(html)
            break;
        case 'DatabaseSize': 
            html +=  '<div class="card Card_Design_None mb-2" >'
            html +=       '<div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>'
            html +=      '<div class="row p-4 align-items-center gap-4 justify-content-center">'
            html +=          '<div class="col-2">'
            html +=             '<i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>'
            html +=          '</div>'
            html +=         '<div class="col-3  border-start border-3">'
            html +=            '<div class="text-primary  fw-semibold" title="Primary">Primary</div>'
            html +=             '<span class="text-secondary mb-1" title="Database Size">Database Size</span>'
            html +=            '<h6 class="mb-0 fw-bold " ondrop="drop(event)" tableclass="tablerowcolumn" ondragover="allowDrop(event)" >3385</h6>'
            html +=        '</div>'
            html +=              '<div class="col-3">'
            html +=                  '<div class=" fw-semibold" title="DR">DR</div>'
            html +=                 '<span class="text-secondary mb-1" title="Database Size">Database Size</span>'
            html +=  '<h6 class="mb-0 fw-bold " ondrop="drop(event)" tableclass="tablerowcolumn" ondragover="allowDrop(event)" > 3385</h6 >'
            html +=               '</div>'  
            html +=     '</div>'
            html += '</div >'
            $('#widgetCreationCard').append(html)
            break;

        case 'ReplicationConnectState':
           
            html = `
          <div class="card-body">
                        <i class="cp-last-copied-transaction text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Replication Connect State"></i><span class="fw-semibold">
                                        Replication Connect State
                                    </span>
                                    <div class="d-flex mt-3">
                                        <div class="w-50 d-grid">
                                            <small class="text-primary fw-semibold" title="Primary">Primary</small>
                                            <span ondrop="drop(event)" tableclass="tablerowcolumn" ondragover="allowDrop(event)" id="PRReplication_Connect_State" title="Source has sent all binlog to replica; waiting for more updates">Source has sent all binlog to replica; waiting for more updates</span>
                                        </div>
                                        <div class="w-50 d-grid">
                                            <small title="DR" class="fw-semibold">DR</small>
                                            <span ondrop="drop(event)" tableclass="tablerowcolumn" ondragover="allowDrop(event)" id="DRReplication_Connect_State" title="| Source has sent all binlog to replica; waiting for more updates |">| Source has sent all binlog to replica; waiting for more updates |</span>
                                        </div>
                                    </div>
                                </div>
            `
            $('#widgetCreationCard').append(html)
            break
        case 'DataLag':
             html = `<div class="card-body">
                        <i class="cp-data-lag text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="Datalag"></i><span class="fw-semibold">DataLag</span>
                                    <div class="d-flex mt-3">
                                        <div class="w-50 d-grid">
                                            <span>
                                                <i class="cp-time text-primary mt-2"></i> <span id="PR_Datalag" tableclass="tablerowcolumn" title="00:00">00:00</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>`
            $('#widgetCreationCard').append(html)
            break;
        case 'TNSService':
             html = `<div class="card Card_Design_None mb-0">
                    <div class="card-body">
                        <i class="cp-current-scn text-primary fs-4 me-2 bg-body-secondary p-1 rounded-2" title="TNS Service Details"></i><span title="TNS Service Details" class="fw-semibold">TNS Service Details</span>
                        <div class="d-flex mt-3">
                                <div class="w-50 d-grid mb-2">
                                    <span class="fw-semibold text-primary mb-1">Primary</span>
                                    <h6 class="mb-0 fs-7 d-inline-block" id="PR_TNSServiceName" tableclass="tablerowcolumn" title="TESTDB">TESTDB</h6>
                                </div>
                                <div class="w-50 d-grid mb-2">
                                    <span class="fw-semibold text-info mb-1">DR</span>
                                    <h6 class="mb-0 fs-7  d-inline-block" id="DR_TNSServiceName" tableclass="tablerowcolumn" title="TESTDBDR">TESTDBDR</h6>
                                </div>
                              </div>
                         
                    </div>
                </div>`
            $('#widgetCreationCard').append(html)
            break;
    }
}




function ChartFunction(expression, archieData, chartId) {
    
    switch (expression) {
        case "bar":
            var options = {
                series: [{
                    data: [400, 430, 448, 470, 540, 580, 690, 1100, 1200, 1380]
                }],
                chart: {
                    type: 'bar'
                  
                },
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        horizontal: true,
                    }
                },
                title: {
                    //text: "Bar chart",
                    align: 'left'
                },
                dataLabels: {
                    enabled: false
                },
                xaxis: {
                    categories: ['South Korea', 'Canada', 'United Kingdom', 'Netherlands', 'Italy', 'France', 'Japan',
                        'United States', 'China', 'Germany'
                    ],
                }
            };

            var chart = new ApexCharts(document.querySelector("#widgetCreationCard"), options);
            chart.render();
            break;
        case "line":

            var options = {
                series: [{
                    name: "Desktops",
                    data: [10, 41, 35, 51, 49, 62, 69, 91, 148]
                }],
                chart: {
                    height: 350,
                    type: 'line',
                    zoom: {
                        enabled: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'straight'
                },
                title: {
                    text: 'Product Trends by Month',
                    align: 'left'
                },
                grid: {
                    row: {
                        colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
                        opacity: 0.5
                    },
                },
                xaxis: {
                    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
                }
            };

            var chart = new ApexCharts(document.querySelector("#widgetCreationCard"), options);
            chart.render();
            break;
        case "pie":

            var options = {
                series: [44, 55, 13, 43, 22],
                chart: {
                    width: 380,
                    type: 'pie',
                },
                labels: ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 200
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };

            var chart = new ApexCharts(document.querySelector("#widgetCreationCard"), options);
            chart.render();
            break;
        case "donut":
            var options = {
                series: [44, 55, 41, 17, 15],
                chart: {
                    type: 'donut',
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 200
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }]
            };

            var chart = new ApexCharts(document.querySelector("#widgetCreationCard"), options);
            chart.render();
            break;
        case "bubble":
            var options = {
                series: [{
                    name: 'Bubble1',
                    data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
                        min: 10,
                        max: 60
                    })
                },
                {
                    name: 'Bubble2',
                    data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
                        min: 10,
                        max: 60
                    })
                },
                {
                    name: 'Bubble3',
                    data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
                        min: 10,
                        max: 60
                    })
                },
                {
                    name: 'Bubble4',
                    data: generateData(new Date('11 Feb 2017 GMT').getTime(), 20, {
                        min: 10,
                        max: 60
                    })
                    }],
              
                chart: {
                    height: 350,
                    type: 'bubble',
                },
                dataLabels: {
                    enabled: false
                },
                fill: {
                    opacity: 0.8
                },
                title: {
                    text: 'Simple Bubble Chart'
                },
                xaxis: {
                    tickAmount: 12,
                    type: 'category',
                },
                yaxis: {
                    max: 70
                }
            };

            var chart = new ApexCharts(document.querySelector("#widgetCreationCard"), options);
            chart.render();
            break;
        case "ArchiveLogHour":
            
            const hourValue = archieData.Archieve_Log_Genearion_Hourly && archieData.Archieve_Log_Genearion_Hourly !== "NA" ? JSON.parse(archieData.Archieve_Log_Genearion_Hourly) : {};
          
            am4core.useTheme(am4themes_animated);
            const charts = am4core.create(chartId, am4charts.XYChart);
            if (charts.logo) {
                charts.logo.disabled = true;    
            }
            const hours = hourValue?.Hours;
            const dayData = hourValue?.Data;
            if (!hours && !dayData) {
                $('#' + chartId).css('text-align', 'center')
                    .html(noDataImage);
            }
            else {
                const lastObj = dayData[dayData?.length - 1];
                const data = [];
                const dataArray = lastObj?.Data.map(value => parseInt(value));
                for (let hour = 0; hour < 24; hour++) {
                    data.push({ hours: hour, value: dataArray[hour] });
                }

                charts.data = data;
                charts.padding(-5, -3, -3, -2);

                const categoryAxis = charts.xAxes.push(new am4charts.CategoryAxis());
                categoryAxis.dataFields.category = "hours";
                categoryAxis.renderer.grid.template.location = 0;
                categoryAxis.renderer.labels.template.fill = am4core.color("#707e87");
                categoryAxis.title.text = "Hours";
                categoryAxis.title.fontSize = 10;
                categoryAxis.title.marginBottom = 30;
                categoryAxis.renderer.minGridDistance = 10;
                categoryAxis.renderer.labels.template.fontSize = 10;
                categoryAxis.renderer.labels.template.adapter.add("text", (text, target) => {
                    if (target.dataItem.index % 4 !== 0) {
                        return "";
                    } else {
                        return text;
                    }
                });


                const valueAxis = charts.yAxes.push(new am4charts.ValueAxis());
                valueAxis.tooltip.disabled = true;
                valueAxis.renderer.labels.template.fill = am4core.color("#707e87");
                valueAxis.renderer.minWidth = 10;
                valueAxis.title.text = "Count";
                valueAxis.title.fontSize = 10;
                valueAxis.title.marginBottom = 30;
                valueAxis.renderer.labels.template.fontSize = 10;

                const series1 = charts.series.push(new am4charts.ColumnSeries());
                series1.dataFields.valueY = "value";
                series1.dataFields.categoryX = "hours";
                series1.fill = am4core.color("#147ad6");
                series1.stroke = am4core.color("#147ad6");
                series1.tensionX = 0.8;
                series1.fillOpacity = 0.1;
                series1.defaultState.transitionDuration = 1000;
                series1.tooltipText = "{valueY}";

                const axisTooltip = categoryAxis.tooltip;
                axisTooltip.background.fill = am4core.color("#147ad6");
                axisTooltip.background.strokeWidth = 0;
                axisTooltip.background.cornerRadius = 3;
                axisTooltip.background.pointerLength = 0;
                axisTooltip.dy = 5;

                charts.cursor = new am4charts.XYCursor();

                categoryAxis.renderer.grid.template.strokeOpacity = 0.07;
                valueAxis.renderer.grid.template.strokeOpacity = 0.07;
            }

            break;
        case "ArchiveLogDay":
            const daily = archieData.Archieve_Log_Genearion_Day && archieData.Archieve_Log_Genearion_Day !== "NA" ? JSON.parse(archieData.Archieve_Log_Genearion_Day) : {};
    
            am4core.useTheme(am4themes_animated);
            const charts1 = am4core.create(chartId, am4charts.XYChart);
            if (charts1.logo) {
                charts1.logo.disabled = true;
            }

            const everyDay = daily?.DayData;

            if (!everyDay) {
                $('#' + chartId).css('text-align', 'center').html(noDataImage);

            } else {

                const data = [];

                for (let hour = 0; hour < 24; hour++) {
                    const hourString = hour.toString().padStart(2, '0');
                    const hourData = everyDay.find(item => item.Hour === hourString);
                    const size = hourData ? parseFloat(hourData.Size) : 0;
                    data.push({ hours: hour, value: Math.round(size) });
                }
                charts1.data = data;
                charts1.padding(-5, 0, -3, -2);

                const categoryAxis = charts1.xAxes.push(new am4charts.CategoryAxis());
                categoryAxis.dataFields.category = "hours";
                categoryAxis.renderer.grid.template.location = 0;
                categoryAxis.renderer.labels.template.fill = am4core.color("#707e87");
                categoryAxis.title.text = "Hours";
                categoryAxis.title.fontSize = 10;
                categoryAxis.title.marginBottom = 30;
                categoryAxis.renderer.minGridDistance = 10;
                categoryAxis.renderer.labels.template.fontSize = 10;
                categoryAxis.renderer.labels.template.adapter.add("text", (text, target) => {
                    if (target.dataItem.index % 4 !== 0) {
                        return "";
                    } else {
                        return text;
                    }
                });

                const valueAxis = charts1.yAxes.push(new am4charts.ValueAxis());
                valueAxis.tooltip.disabled = true;
                valueAxis.title.text = "Size (MB)";
                valueAxis.title.fontSize = 10;
                valueAxis.renderer.labels.template.fill = am4core.color("#707e87");
                valueAxis.renderer.minWidth = 60;

                const series1 = charts1.series.push(new am4charts.ColumnSeries());
                series1.dataFields.categoryX = "hours";
                series1.dataFields.valueY = "value";
                series1.tooltipText = "[bold]{value}[/]";
                series1.fill = am4core.color("#147ad6");
                series1.stroke = am4core.color("#147ad6");
                series1.tensionX = 0.8;
                series1.fillOpacity = 0.1;
                series1.defaultState.transitionDuration = 1000;

                const axisTooltip = categoryAxis.tooltip;
                axisTooltip.background.fill = am4core.color("#147ad6");
                axisTooltip.background.strokeWidth = 0;
                axisTooltip.background.cornerRadius = 3;
                axisTooltip.background.pointerLength = 0;
                axisTooltip.dy = 5;

                charts1.cursor = new am4charts.XYCursor();

                categoryAxis.renderer.grid.template.strokeOpacity = 0.07;
                valueAxis.renderer.grid.template.strokeOpacity = 0.07;
            }

            break;
        case "ArchiveLogWeek":
            const weekly = archieData.Archieve_Log_Genearion_Weekly && archieData.Archieve_Log_Genearion_Weekly !== "NA" ? JSON.parse(archieData.Archieve_Log_Genearion_Weekly) : {};
            am4core.useTheme(am4themes_animated);
            const charts2 = am4core.create(chartId, am4charts.XYChart);
            if (charts2.logo) {
                charts2.logo.disabled = true;
            }

            const daysData = weekly?.WeekData;
            if (!daysData) {
                $('#' + chartId).css('text-align', 'center')
                    .html(noDataImage);
            }
            else {
                const data = [];
                (daysData || []).forEach(daysItem => {
                    let sizes = parseFloat(daysItem.Size);
                    let sizeData = Math.round(sizes);
                    const [day, month, year] = daysItem?.Days.split('/');
                    const date = new Date(year, month - 1, day);
                    const monthAbbreviation = new Intl.DateTimeFormat('en', { month: 'short' }).format(date);
                    const formattedDate = `${monthAbbreviation} ${date.getDate().toString().padStart(2, '0')}`;

                    data.push({ day: formattedDate, sizeData });
                });

                charts2.data = data;
                charts2.padding(-5, -3, -3, -2);
                const categoryAxis = charts2.xAxes.push(new am4charts.CategoryAxis());
                categoryAxis.dataFields.category = "day";
                categoryAxis.renderer.grid.template.location = 0;
                categoryAxis.renderer.labels.template.fill = am4core.color("#707e87");
                categoryAxis.title.text = "Days";
                categoryAxis.title.fontSize = 10;
                categoryAxis.title.marginBottom = 30;
                categoryAxis.renderer.minGridDistance = 30;
                categoryAxis.renderer.labels.template.fontSize = 10;

                const valueAxis = charts2.yAxes.push(new am4charts.ValueAxis());
                valueAxis.tooltip.disabled = true;
                valueAxis.renderer.labels.template.fill = am4core.color("#707e87");
                valueAxis.title.text = "Size (MB)";
                valueAxis.title.fontSize = 10;
                valueAxis.title.marginBottom = 30;
                valueAxis.renderer.labels.template.fontSize = 10;
                valueAxis.max = Math.max(...data.map(item => item.sizeData)) + 10;

                const series1 = charts2.series.push(new am4charts.ColumnSeries());
                series1.dataFields.categoryX = "day";
                series1.dataFields.valueY = "sizeData";
                series1.tooltipText = " [bold]{sizeData}[/]";
                series1.fill = am4core.color("#147ad6");
                series1.stroke = am4core.color("#147ad6");
                series1.tensionX = 0.8;
                series1.fillOpacity = 0.1;
                series1.defaultState.transitionDuration = 1000;
                series1.columns.template.width = am4core.percent(30);

                const axisTooltip = categoryAxis.tooltip;
                axisTooltip.background.fill = am4core.color("#147ad6");
                axisTooltip.background.strokeWidth = 0;
                axisTooltip.background.cornerRadius = 3;
                axisTooltip.background.pointerLength = 0;
                axisTooltip.dy = 5;

                charts2.cursor = new am4charts.XYCursor();

                categoryAxis.renderer.grid.template.strokeOpacity = 0.07;
                valueAxis.renderer.grid.template.strokeOpacity = 0.07;
            }
            break;
    }
}

function generateData(baseval, count, yrange) {
    var i = 0;
    var series = [];
    while (i < count) {
        var x = Math.floor(Math.random() * (750 - 1 + 1)) + 1;
        var y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
        var z = Math.floor(Math.random() * (75 - 15 + 1)) + 15;

        series.push([x, y, z]);
        baseval += 86400000;
        i++;
    }
    return series;
}


function conditionList(data) {

    $("#ConditionalModal").modal("show")
}