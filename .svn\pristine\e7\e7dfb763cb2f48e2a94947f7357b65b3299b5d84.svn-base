using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberAirGapStatusService
{
    Task<List<CyberAirGapStatusListVm>> GetCyberAirGapStatusList();
    Task<BaseResponse> CreateAsync(CreateCyberAirGapStatusCommand createCyberAirGapStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberAirGapStatusCommand updateCyberAirGapStatusCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BaseResponse> UpdateStatusAsync(UpdateAirGapStatusCommand updateAirGapStatusCommand);
    Task<CyberAirGapStatusDetailVm> GetByReferenceId(string id);
    #region NameExist
 Task<bool> IsCyberAirGapStatusNameExist(string name, string id);
   #endregion
    #region Paginated
 Task<PaginatedResult<CyberAirGapStatusListVm>> GetPaginatedCyberAirGapStatuss(GetCyberAirGapStatusPaginatedListQuery query);
    #endregion
}
