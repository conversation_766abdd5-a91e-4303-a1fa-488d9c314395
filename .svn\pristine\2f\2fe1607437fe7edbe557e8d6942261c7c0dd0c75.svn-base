using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DatalagImpactAvailabilityViewRepositoryTests : IClassFixture<DatalagImpactAvailabilityViewFixture>
{
    private readonly DatalagImpactAvailabilityViewFixture _datalagImpactAvailabilityViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatalagImpactAvailabilityViewRepository _repository;

    public DatalagImpactAvailabilityViewRepositoryTests(DatalagImpactAvailabilityViewFixture datalagImpactAvailabilityViewFixture)
    {
        _datalagImpactAvailabilityViewFixture = datalagImpactAvailabilityViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DatalagImpactAvailabilityViewRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;

        // Act
        var result = await _repository.AddAsync(datalagImpactAvailabilityView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityView.ReferenceId, result.ReferenceId);
        Assert.Single(_dbContext.DatalagImpactAvailabilityViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        await _repository.AddAsync(datalagImpactAvailabilityView);

        datalagImpactAvailabilityView.ReferenceId = "UPDATED_BS_123";

        // Act
        var result = await _repository.UpdateAsync(datalagImpactAvailabilityView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UPDATED_BS_123", result.ReferenceId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        await _repository.AddAsync(datalagImpactAvailabilityView);

        // Act
        var result = await _repository.DeleteAsync(datalagImpactAvailabilityView);

        // Assert
        Assert.Equal(datalagImpactAvailabilityView.ReferenceId, result.ReferenceId);
        Assert.Empty(_dbContext.DatalagImpactAvailabilityViews);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        var addedEntity = await _repository.AddAsync(datalagImpactAvailabilityView);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        await _repository.AddAsync(datalagImpactAvailabilityView);

        // Act
        var result = await _repository.GetByReferenceIdAsync(datalagImpactAvailabilityView.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityView.ReferenceId, result.ReferenceId);
        Assert.Equal(datalagImpactAvailabilityView.BusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityViews.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        datalagImpactAvailabilityViews.First().IsActive = false; // Make one inactive
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityViews.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByBusinessServiceId Tests

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        datalagImpactAvailabilityView.ReferenceId = DatalagImpactAvailabilityViewFixture.BusinessServiceId;
        await _repository.AddAsync(datalagImpactAvailabilityView);

        // Act
        var result = await _repository.GetByBusinessServiceId(DatalagImpactAvailabilityViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DatalagImpactAvailabilityViewFixture.BusinessServiceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Act
        var result = await _repository.GetByBusinessServiceId("non-existent-business-service-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnCorrectEntity_WhenMultipleExist()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        var targetBusinessServiceId = "TARGET_BS_123";
        datalagImpactAvailabilityViews.First().ReferenceId = targetBusinessServiceId;
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Act
        var result = await _repository.GetByBusinessServiceId(targetBusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(targetBusinessServiceId, result.ReferenceId);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;

        // Act
        var result = await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Assert
        Assert.Equal(datalagImpactAvailabilityViews.Count, result.Count());
        Assert.Equal(datalagImpactAvailabilityViews.Count, _dbContext.DatalagImpactAvailabilityViews.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);

        // Act
        var result = await _repository.RemoveRangeAsync(datalagImpactAvailabilityViews);

        // Assert
        Assert.Equal(datalagImpactAvailabilityViews.Count, result.Count());
        Assert.Empty(_dbContext.DatalagImpactAvailabilityViews);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(datalagImpactAvailabilityViews);
        var initialCount = datalagImpactAvailabilityViews.Count;
        
        var toUpdate = datalagImpactAvailabilityViews.Take(2).ToList();
        toUpdate.ForEach(x => x.ReferenceId = "05127cbe-0998-4eff-8925-150eef5d35e7");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = datalagImpactAvailabilityViews.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.ReferenceId == "05127cbe-0998-4eff-8925-150eef5d35e7").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
