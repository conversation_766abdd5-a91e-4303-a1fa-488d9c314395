using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReplicationJobFixture : IDisposable
{
    public List<ReplicationJob> ReplicationJobPaginationList { get; set; }
    public List<ReplicationJob> ReplicationJobList { get; set; }
    public ReplicationJob ReplicationJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReplicationJobFixture()
    {
        var fixture = new Fixture();

        ReplicationJobList = fixture.Create<List<ReplicationJob>>();

        ReplicationJobPaginationList = fixture.CreateMany<ReplicationJob>(20).ToList();

        ReplicationJobPaginationList.ForEach(x => x.CompanyId = CompanyId);

        ReplicationJobList.ForEach(x => x.CompanyId = CompanyId);

        ReplicationJobDto = fixture.Create<ReplicationJob>();

        ReplicationJobDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
