﻿using ContinuityPatrol.Application.Features.Workflow.Events.Verify;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Verify;

public class UpdateWorkflowVerifyCommandHandler : IRequestHandler<UpdateWorkflowVerifyCommand,
    UpdateWorkflowVerifyCommandResponse>
{
    private readonly IPublisher _publisher;
    private readonly IWorkflowRepository _workflowRepository;

    public UpdateWorkflowVerifyCommandHandler(IWorkflowRepository workflowRepository, IPublisher publisher)
    {
        _workflowRepository = workflowRepository;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowVerifyCommandResponse> Handle(UpdateWorkflowVerifyCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        if (eventToUpdate.IsVerify)
        {
            throw new Exception($"Workflow '{eventToUpdate.Name}' is already marked as verified.");
        }

        eventToUpdate.IsVerify = request.IsVerify;

        await _workflowRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowVerifyCommandResponse
        {
            Message = request.IsVerify
                ? $"Workflow '{eventToUpdate.Name}' verify successfully."
                : $"Workflow '{eventToUpdate.Name}' unVerify successfully.",

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new UpdateWorkflowVerifyEvent { WorkflowName = eventToUpdate.Name,IsVerify = eventToUpdate.IsVerify },
            cancellationToken);

        return response;
    }
}