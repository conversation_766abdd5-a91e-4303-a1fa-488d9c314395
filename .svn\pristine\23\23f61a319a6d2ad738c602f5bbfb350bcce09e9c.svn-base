﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;

public record BusinessServiceSummaryReportVm
{
    public int SrNo { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    [JsonIgnore] public string InfraObjectId { get; set; }
    public string Description { get; set; }
    public int Up { get; set; }
    public int Down { get; set; }
    public int Maintenance { get; set; }
    public string Health { get; set; }
}

public class BusinessServiceSummaryReport
{
    public string ReportGeneratedBy { get; set; }
    public string Date { get; set; }
    public List<BusinessServiceSummaryReportVm> BusinessServiceSummaryReportVms { get; set; }
}