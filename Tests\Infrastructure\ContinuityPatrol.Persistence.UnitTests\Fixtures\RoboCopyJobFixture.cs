using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RoboCopyJobFixture : IDisposable
{
    public List<RoboCopyJob> RoboCopyJobPaginationList { get; set; }
    public List<RoboCopyJob> RoboCopyJobList { get; set; }
    public RoboCopyJob RoboCopyJobDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public RoboCopyJobFixture()
    {
        var fixture = new Fixture();

        RoboCopyJobList = fixture.Create<List<RoboCopyJob>>();

        RoboCopyJobPaginationList = fixture.CreateMany<RoboCopyJob>(20).ToList();

        RoboCopyJobDto = fixture.Create<RoboCopyJob>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
