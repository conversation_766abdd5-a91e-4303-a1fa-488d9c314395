﻿using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedList;

public class GetFourEyeApproversPaginatedListQueryHandler : IRequestHandler<GetFourEyeApproversPaginatedListQuery,
    PaginatedResult<FourEyeApproversListVM>>
{
    private readonly IFourEyeRepository _FourEyeRepository;
    private readonly IMapper _mapper;


    public GetFourEyeApproversPaginatedListQueryHandler(IFourEyeRepository fourEyeRepository, IMapper mapper)
    {
        _FourEyeRepository = fourEyeRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<FourEyeApproversListVM>> Handle(GetFourEyeApproversPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _FourEyeRepository.PaginatedListAllAsync();

        var FourEyeApproversList = await queryable
            .Where(x => x.WorkflowProfile.Equals("ALL"))
            .Select(m => _mapper.Map<FourEyeApproversListVM>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);


        return FourEyeApproversList;
    }
}