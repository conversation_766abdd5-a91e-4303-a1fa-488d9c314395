﻿@{
    ViewData["Title"] = "List";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
            <div class="d-flex align-items-center">
                <h6 class="page_title">
                    <i class="cp-prebuild-reports"></i><span>Report Builder</span>
                </h6>
            </div>
            <form class="d-flex align-items-center">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter" aria-expanded="false">
                                <i class="cp-filter"></i>
                            </span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input " type="checkbox" value="reportName=" id="Namecheckbox">
                                        <label class="form-check-label" for="Namecheckbox">
                                            Report Name
                                        </label>
                                    </div>
                                    <div>
                                        <input class="form-check-input " type="checkbox" value="reportType=" id="Typecheckbox">
                                        <label class="form-check-label" for="Typecheckbox">
                                            Report Type
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#ReportDetailsModal"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="ReportBuilderList" class="table table-hover">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Custom Report Name</th>
                        <th>Created By</th>
                        <th>Last Modified on</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Operational Service Summary Report</td>
                        <td>Srivignesh R</td>
                        <td>02-05-2024 10:00:20 AM</td>
                        <td>
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Report Details Modal -->
<div class="modal fade" id="ReportDetailsModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-prebuild-reports"></i><span>Report Details</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input id="reportName" type="text" class="form-control" placeholder="Enter Report Name" maxlength="100" autocomplete="off">
                    </div>
                 </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-description"></i></span>
                        <input id="reportName" type="text" class="form-control" placeholder="Enter Description" maxlength="100" autocomplete="off">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Dataset</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-configure-dataset"></i></span>
                        <select class="form-select-modal">
                            <option selected>Select Report</option>
                            <option>RPO SLA Report</option>
                            <option>Datalag Status Report</option>
                            <option>InfraObject Summary</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary btn-sm" id="redirectBtn">Add & Design</button>
            </div>
        </div>
    </div>
</div>

<script>
    var dataTable = $('#ReportBuilderList').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,

        });
</script>

<script>
    $("#redirectBtn").on('click', function () {
        window.location.assign('/Report/CustomReport/ReportDesignSpace');
    })
</script>
