﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowOperationGroupRepository : BaseRepository<WorkflowOperationGroup>,
    IWorkflowOperationGroupRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowOperationGroupRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public  override async Task<IReadOnlyList<WorkflowOperationGroup>> ListAllAsync()
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(base.QueryAll(workflow =>
            workflow.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : AssignedInfraObjects(workflowOperationGroups);
    }
    public  async Task<IReadOnlyList<WorkflowOperationGroup>> ListAllAsyncForDashBoardView()
    {
        var workflowOperationGroups = base.QueryAll(workflow =>
            workflow.CompanyId.Equals(_loggedInUserService.CompanyId)).Select(x => new WorkflowOperationGroup
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                Status = x.Status,
                InfraObjectId = x.InfraObjectId
            });

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : AssignedInfraObjects(workflowOperationGroups);
    }


    public override Task<WorkflowOperationGroup> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(workflowOperationGroup => workflowOperationGroup.ReferenceId.Equals(id) && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupNames()
    {
        var workflowOperationGroups = base
            .QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new WorkflowOperationGroup
            { ReferenceId = x.ReferenceId, CurrentActionName = x.CurrentActionName, InfraObjectId = x.InfraObjectId })
            .OrderBy(x => x.CurrentActionName);

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : AssignedInfraObjects(workflowOperationGroups).ToList();
    }

    public override IQueryable<WorkflowOperationGroup> GetPaginatedQuery()
    {
        var workflowOperationGroups = base.QueryAll(workflow =>
                   workflow.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking();

        return _loggedInUserService.IsAllInfra
            ? workflowOperationGroups.AsNoTracking()
            : PaginatedAssignedInfraObjects(workflowOperationGroups).AsNoTracking();
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationId(
        string workflowOperationId)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId)).OrderBy(x => x.Id)
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) &&
                                          workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)).OrderBy(x => x.Id));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIds(
        List<string> workflowOperationIds)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationIds.Contains(workflowOperationGroup.WorkflowOperationId))
            : base.FilterBy(workflowOperationGroup => workflowOperationIds.Contains(workflowOperationGroup.WorkflowOperationId) &&
                                          workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public async Task<List<WorkflowOperationGroup>> GetOperationGroupByWorkflowOperationIds(List<string> workflowOperationIds)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationIds.Contains(workflowOperationGroup.WorkflowOperationId)).OrderBy(x => x.Id)
            : base.FilterBy(workflowOperationGroup => workflowOperationIds.Contains(workflowOperationGroup.WorkflowOperationId) &&
                                                      workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)).OrderBy(x => x.Id));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }


    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdAndNodeId(
        string workflowOperationId, string nodeId)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) &&
                                          workflowOperationGroup.NodeId.Equals(nodeId))
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) &&
                                          workflowOperationGroup.NodeId.Equals(nodeId) && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public Task<bool> IsWorkflowOperationGroupNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkflowOperationGroups.Any(e => e.CurrentActionName.Equals(name)))
            : Task.FromResult(_dbContext.WorkflowOperationGroups.Where(e => e.CurrentActionName.Equals(name)).ToList()
                .Unique(id));
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByInfraObjectId(string infraObjectId)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationGroup.InfraObjectId.Equals(infraObjectId))
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.InfraObjectId.Equals(infraObjectId)
                                                    && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByBusinessServiceId(
        string businessServiceId)
    {

        var status = new[] { "aborted", "completed" };

        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationGroup.BusinessServiceId.Equals(businessServiceId) &&
                                                                 status.Contains(workflowOperationGroup.Status.ToLower()))
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.BusinessServiceId.Equals(businessServiceId)
                                                    && status.Contains(workflowOperationGroup.Status.ToLower()) && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }


    public async Task<ProfileExecutorByBusinessServiceIdVm> GetDrillProfileCountByBusinessServiceId(string businessServiceId)
    {
        var status = new[] { "Aborted", "Completed" };

        var workflowOperationGroups =
            await _dbContext.WorkflowOperationGroups
                .AsNoTracking()
                .Active()
                .Where(x => status.Contains(x.Status) && x.BusinessServiceId.Equals(businessServiceId))
                .GroupBy(x => x.WorkflowOperationId)
                .Select(group => new
                {
                    WorkflowOperationId = group.Key,
                    CompletedCount = group.Count(x => x.Status.Equals("Completed")),
                    AbortedCount = group.Count(x => x.Status.Equals("Aborted")),
                    LastModifiedDate = group.Max(x => x.LastModifiedDate),
                    CompletedWorkflowOperationGroupInfraObjcetIds = group.Where(x => x.Status.Equals("Completed"))
                    .Select(x => x.InfraObjectId).ToList()
                }).ToListAsync();

        var profileExecutorByBusinessServiceIdVm = new ProfileExecutorByBusinessServiceIdVm
        {
            DrDrillCount = workflowOperationGroups.Count,
            CompletedStatus = workflowOperationGroups.Any() ? workflowOperationGroups.Sum(x => x.CompletedCount) : 0,
            AbortedStatus = workflowOperationGroups.Any() ? workflowOperationGroups.Sum(x => x.AbortedCount) : 0,
            LastModifiedDate = workflowOperationGroups.Any() ? workflowOperationGroups.Max(x => x.LastModifiedDate) : DateTime.MinValue,
            CompletedWorkflowOperationGroupInfraObjcetIds = workflowOperationGroups.SelectMany(x => x.CompletedWorkflowOperationGroupInfraObjcetIds).DistinctBy(x => x).ToList()
        };

        return profileExecutorByBusinessServiceIdVm;
    }


    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupListByWorkflowId(string workflowId,
        string workflowOperationId)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup =>
                                      workflowOperationGroup.WorkflowId.Equals(workflowId) &&
                                                                               workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId))
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowId.Equals(workflowId)
                                                    && workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));
        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationByWorkflowOperationId(string workflowOperationId)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup =>
                workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId))
            : base.FilterBy(workflowOperationGroup =>workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) &&
                                                      workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));
        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

    public async Task<Dictionary<string , List<WorkflowOperationGroup>>> GetWorkflowOperationGroupByInfraObjectIds(List<string> infraObjectIds)
    {
        var workflowOperationGroups = SelectToWorkflowOperationGroup(_loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => infraObjectIds.Contains(workflowOperationGroup.InfraObjectId))
            : base.FilterBy(workflowOperationGroup => infraObjectIds.Contains(workflowOperationGroup.InfraObjectId)
                                                    && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)));

        var result= _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();

        return result.GroupBy(x=>x.InfraObjectId).ToDictionary(x=>x.Key,y=>y.ToList());
    }


    //

    public IQueryable<WorkflowOperationGroup> SelectToWorkflowOperationGroup(IQueryable<WorkflowOperationGroup> query)
    {
        return query.Select(x => new WorkflowOperationGroup
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            InfraObjectId = x.InfraObjectId,
            InfraObjectName = x.InfraObjectName,
            WorkflowId = x.WorkflowId,
            WorkflowName = x.WorkflowName,
            CurrentActionId = x.CurrentActionId,
            CurrentActionName = x.CurrentActionName,
            Status = x.Status,
            Message = x.Message,
            SnapProperties = x.SnapProperties,
            IsCustom = x.IsCustom,
            WorkflowOperationId = x.WorkflowOperationId,
            ConditionalOperation = x.ConditionalOperation,
            ProgressStatus = x.ProgressStatus,
            JobName = x.JobName,
            IsResume = x.IsResume,
            IsReExecute = x.IsReExecute,
            IsPause = x.IsPause,
            IsAbort = x.IsAbort,
            WaitToNext = x.WaitToNext,
            WorkflowVersion = x.WorkflowVersion,
            NodeId = x.NodeId,
            ActionMode = x.ActionMode,
            WorkflowExecutionTempId = x.WorkflowExecutionTempId,
            CreatedBy=x.CreatedBy
        });
    }

    public async Task<bool> IsWorkflowIdExist(string workflowId)
    {
        var result = await _dbContext.WorkflowOperationGroups.AnyAsync(x => x.WorkflowId.Equals(workflowId));

        return result;
    }


    private IReadOnlyList<WorkflowOperationGroup> AssignedInfraObjects(IQueryable<WorkflowOperationGroup> infraObjects)
    {
        var infraObjectList = new List<WorkflowOperationGroup>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }
    private IReadOnlyList<WorkflowOperationGroup> GetAssignedInfraObjectsByWorkflow(IQueryable<WorkflowOperationGroup> infraObjects)
    {
        var infraObjectList = new List<WorkflowOperationGroup>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }
    private IQueryable<WorkflowOperationGroup> PaginatedAssignedInfraObjects(IQueryable<WorkflowOperationGroup> infraObjects)
    {
        var assignedInfraIds = AssignedEntity.AssignedBusinessServices.SelectMany(x => x.AssignedBusinessFunctions.SelectMany(y => y.AssignedInfraObjects.Select(z => z.Id))).ToList();

        return infraObjects.Where(s => assignedInfraIds.Contains(s.InfraObjectId));
    }

    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds(List<string> workflowIds, string workflowOperationId)
    {
        var workflowOperationGroups = _loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowIds.Contains(workflowOperationGroup.WorkflowId)
            && workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId))
            : base.FilterBy(workflowOperationGroup => workflowIds.Contains(workflowOperationGroup.WorkflowId)
            && workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId)
             && workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId));

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }
    public async Task<List<WorkflowOperationGroup>> GetWorkflowOperationGroupByWorkflowOperationIdByReport(string workflowOperationId)
    {
        var workflowOperationGroups = _loggedInUserService.IsParent
            ? base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId)).Select(x => new WorkflowOperationGroup
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                CompanyId = x.CompanyId,
                BusinessServiceId = x.BusinessServiceId,
                BusinessServiceName = x.BusinessServiceName,
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                CurrentActionId = x.CurrentActionId,
                CurrentActionName = x.CurrentActionName,
                Status = x.Status,
                Message = x.Message,
                SnapProperties = x.SnapProperties,
                IsCustom = x.IsCustom,
                WorkflowOperationId = x.WorkflowOperationId,
                ConditionalOperation = x.ConditionalOperation,
                ProgressStatus = x.ProgressStatus,
                JobName = x.JobName,
                IsResume = x.IsResume,
                IsReExecute = x.IsReExecute,
                IsPause = x.IsPause,
                IsAbort = x.IsAbort,
                WaitToNext = x.WaitToNext,
                WorkflowVersion = x.WorkflowVersion,
                NodeId = x.NodeId,
                ActionMode = x.ActionMode,
                WorkflowExecutionTempId = x.WorkflowExecutionTempId,
                CreatedBy = x.CreatedBy,
                CreatedDate = x.CreatedDate,
                LastModifiedDate = x.LastModifiedDate,
                IsActive = x.IsActive
            }).OrderBy(x => x.Id)
            : base.FilterBy(workflowOperationGroup => workflowOperationGroup.WorkflowOperationId.Equals(workflowOperationId) &&
                                          workflowOperationGroup.CompanyId.Equals(_loggedInUserService.CompanyId)).OrderBy(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? await workflowOperationGroups.ToListAsync()
            : GetAssignedInfraObjectsByWorkflow(workflowOperationGroups).ToList();
    }

   
}