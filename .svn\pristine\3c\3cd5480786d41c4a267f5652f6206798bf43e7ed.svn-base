namespace ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;

public class GetDataSyncOptionsDetailsQueryHandler : IRequestHandler<GetDataSyncOptionsDetailQuery, DataSyncOptionsDetailVm>
{
    private readonly IDataSyncOptionsRepository _dataSyncOptionsRepository;
    private readonly IMapper _mapper;

    public GetDataSyncOptionsDetailsQueryHandler(IMapper mapper, IDataSyncOptionsRepository dataSyncOptionsRepository)
    {
        _mapper = mapper;
        _dataSyncOptionsRepository = dataSyncOptionsRepository;
    }

    public async Task<DataSyncOptionsDetailVm> Handle(GetDataSyncOptionsDetailQuery request, CancellationToken cancellationToken)
    {
        var dataSync = await _dataSyncOptionsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dataSync, nameof(Domain.Entities.DataSyncOptions),
            new NotFoundException(nameof(Domain.Entities.DataSyncOptions), request.Id));

        var dataSyncDetailDto = _mapper.Map<DataSyncOptionsDetailVm>(dataSync);

        return dataSyncDetailDto;
    }
}