namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;

public class
    GetDynamicDashboardMapDetailsQueryHandler : IRequestHandler<GetDynamicDashboardMapDetailQuery,
        DynamicDashboardMapDetailVm>
{
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IMapper _mapper;

    public GetDynamicDashboardMapDetailsQueryHandler(IMapper mapper,
        IDynamicDashboardMapRepository dynamicDashboardMapRepository)
    {
        _mapper = mapper;
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;
    }

    public async Task<DynamicDashboardMapDetailVm> Handle(GetDynamicDashboardMapDetailQuery request,
        CancellationToken cancellationToken)
    {
        var dynamicDashboardMap = await _dynamicDashboardMapRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(dynamicDashboardMap, nameof(Domain.Entities.DynamicDashboardMap),
            new NotFoundException(nameof(Domain.Entities.DynamicDashboardMap), request.Id));

        var dynamicDashboardMapDetailDto = _mapper.Map<DynamicDashboardMapDetailVm>(dynamicDashboardMap);

        return dynamicDashboardMapDetailDto;
    }
}