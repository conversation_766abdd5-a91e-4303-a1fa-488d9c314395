﻿let modifiedArray = [], originalTableData = [];
const tablesPerRow = 5;
let tableHtml = '';
const tableAccessURL = {
    tableList: "Admin/TableAccess/TableList",
    createOrUpdate: "Admin/TableAccess/CreateOrUpdate",
    dataSet: "Admin/TableAccess/DataSet"
}
let createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
if (createPermission == 'false') {
    $('input[type="checkbox"]').prop('disabled', true);   
    $("#tableAccessSaveButton").removeClass('#tableAccessSaveButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    $("#tableAccessCancelButton").removeClass('#tableAccessCancelButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
function renderTableItems(data) {
    if (!Array.isArray(data)) {
        console.error('Expected an array but got:', data);
        return;
    }

    tableHtml = data.map((paginatedTableAccess, index) => {
        const isChecked = paginatedTableAccess.isChecked ? "checked" : "";
        const isFirstInRow = index % tablesPerRow === 0;
        const isLastInRow = (index + 1) % tablesPerRow === 0 || index === data.length - 1;

        return `
            ${isFirstInRow ? `<div class="row gx-3 gy-3">` : ""}
            <div class="col-md-2_5 d-flex">
                <div class="w-100 p-2 rounded-3 d-flex justify-content-between align-items-center table-item">
                    <span title="${paginatedTableAccess.tableName}" class="custom-control-label w-75 mb-0 d-flex gap-2 align-items-center">
                        <i class="cp-table fs-5"></i>
                        <span class="tablecheck_name text-truncate d-inline-block mw-75">${paginatedTableAccess.tableName}</span>
                    </span>
                    <div class="form-switch">
                        <input id="${paginatedTableAccess.id}" title="${paginatedTableAccess.tableName}" type="checkbox" class="form-check-input isTableChecked" data-tableName="${paginatedTableAccess.tableName}" data-schemaName="${paginatedTableAccess.schemaName}" data-configured="${paginatedTableAccess.isConfigured}" ${isChecked}>
                    </div>
                </div>
            </div>
            ${isLastInRow ? `</div>` : ""}
        `;
    }).join('');
    $('#tableaccess_body').html(tableHtml);

    
    if (data.length === 0) {
        $('#data_notfound').show() 
    } else {
      
        $('#data_notfound').hide()
    }
}

$.ajax({
    type: "GET",
    url: RootUrl + tableAccessURL.tableList,
    dataType: "json",
    success: function (response) {
       
        if (response && Array.isArray(response?.paginatedTableAccess)) {
            originalTableData = response?.paginatedTableAccess; 
        } else {
            console.error('Data is not in the expected format:', response);
            originalTableData = []; 
        }
        originalTableData.sort((a, b) => a.tableName.localeCompare(b.tableName, undefined, { sensitivity: 'base' }));           
        renderTableItems(originalTableData);
        const allChecked = originalTableData.every(item => item.isChecked === true);
        setTimeout(() => { $('#flexSwitchCheckChecked').prop('checked', allChecked); }, 0);
        
        if (!document.getElementById("tableAccessStyles")) {
            $("<style>", { id: "tableAccessStyles", type: "text/css" }).html(`
                .col-md-2_5 { flex: 0 0 21%; max-width: 20%; }
                .table-item {
                    background: var(--bs-primary-bg-subtle);
                    min-height: 40px;
                    padding: 8px;
                    margin-bottom: 12px;
                }
                .tablecheck_name {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .gx-3 { --bs-gutter-x: 1.5rem; }
                .gy-3 { --bs-gutter-y: 1.5rem; }
            `).appendTo("head");
        }
       
        if ($("#tableaccess_body .tablecheck").length == $(".isTableChecked:checked").length) {
            $('#flexSwitchCheckChecked').prop('checked', true);
        } else {
            $('#flexSwitchCheckChecked').prop('checked', false);
        }
    },
    error: function (xhr, status, error) {
        console.error('AJAX error:', status, error);
    }
});

// Search functionality
$('#search-inp').on('input', function () {
    const query = $(this).val().toLowerCase().trim();
    const filteredData = originalTableData.filter((item) => {
        return item.tableName.toLowerCase().includes(query);
    });
    renderTableItems(filteredData);   
    function filterElement($element) {
        const elementText = $element.find('.tablecheck_name').text().toLowerCase();
        return elementText.includes(query);
    }   


    $('.tablecheck').each(function () {
        const $row = $(this);
        $row.toggle(filterElement($row));
    });

    let isVisible = $('.tablecheck').filter(function () {
        return $(this).css('display') === 'block';
    });

});

// Prevent input of '=' and 'Enter' keys (as requested)
$('#search-inp').on('keypress', function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
});

$('#tableAccessSaveButton').on('click', async function () {
    let TableAccessModel = {};
    TableAccessModel.PaginatedTableAccess = [];
   // await dataSet();
    TableAccessModel.PaginatedTableAccess = modifiedArray;
    if (modifiedArray.length) {
      const filteredArray = modifiedArray.filter(item => item.isConfigured === false);
     TableAccessModel.PaginatedTableAccess = filteredArray;
    }
    await $.ajax({
        type: "POST",
        url: RootUrl + tableAccessURL.createOrUpdate,
        data: JSON.stringify(TableAccessModel),
        headers: {
            'RequestVerificationToken': gettoken()
        },
        contentType: 'application/json',
        dataType: "json",
        traditional: true,
        success: function (data) {
            $('#tableAccessSaveButton, #tableAccessCancelButton').addClass('disabled');

            if (data && data?.success) {
                if (data.data === "The Table is currently in use.") {
                    notificationAlert("warning", data.data);
                } else {
                    notificationAlert("success", data.data.message);
                }
            } else {
                errorNotification(data);
            }

            setTimeout(() => {
                window.location.reload();
            }, 3000);
        },
        error: function (xhr) {
            $('#tableAccessSaveButton, #tableAccessCancelButton').addClass('disabled');
            errorNotification(xhr.responseJSON || { message: "Something went wrong!" });

            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    });
});

$(document).on('change', '.isTableChecked,#flexSwitchCheckChecked', function (e) {
    $('#tableAccessSaveButton, #tableAccessCancelButton').removeClass('disabled')
    if (e.target.id == 'flexSwitchCheckChecked') {
        modifiedArray = []
        $('#flexSwitchCheckChecked').prop('checked', true);
            $('.isTableChecked').each(function () {
                modifiedArray.push({
                    id: this.id,
                    schemaName: $(this).data('schemaname'),
                    tableName: $(this).data('tablename'),
                    isChecked: this.checked,
                    isConfigured: $(this).data('configured')
                });
            });     

    } else {
        $('#flexSwitchCheckChecked').prop('checked', false);
        let findDuplicate = modifiedArray.findIndex((d) => d.id === $(this)[0].id)
        if (findDuplicate !== -1) {
            modifiedArray.forEach((x) => {
                if (x.id === $(this)[0].id) {
                    x.isChecked = $(this)[0].checked                   
                }
            })
        } else {
            modifiedArray.push({ 'id': $(this)[0].id, 'schemaName': $(this).data('schemaname'), 'tableName': $(this).data('tablename'), isChecked: $(this)[0].checked, 'isConfigured': $(this).data('configured') })
        }
    }
    let allChecked = $('.isTableChecked').length === $('.isTableChecked:checked').length;
    $('#flexSwitchCheckChecked').prop('checked', allChecked);
});

$('#tableAccessCancelButton').on('click', function () {
    window.location.reload()
})

$('#flexSwitchCheckChecked').on("change", function () {    
    $('.form-check-input').prop('checked', $(this).is(':checked'));
});
//let populateArray = [];
//async function dataSet() {
//    try {
//        const result = await $.getJSON(RootUrl + tableAccessURL.dataSet);
//        if (Array.isArray(result)) {
//            const PrimaryTableNames = new Set(
//                result.map(x => x.primaryTableName?.toLowerCase())
//            );
//            if (modifiedArray.length) {
//                populateArray = modifiedArray.filter(item =>
//                    !PrimaryTableNames.has(item.tableName?.toLowerCase())
//                );
//            }
//        } else {
//            errorNotification({ message: "No result returned" });
//        }
//    } catch (xhr) {
//        errorNotification(xhr.responseJSON || { message: "Something went wrong!" });
//    }
//}
