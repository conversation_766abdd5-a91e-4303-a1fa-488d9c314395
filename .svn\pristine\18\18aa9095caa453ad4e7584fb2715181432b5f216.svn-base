﻿using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Alert.Queries.GetPaginatedList;

public record GetAlertPaginatedListQuery : PaginatedBase, IRequest<(PaginatedResult<AlertListVm>,Dictionary<string,int>)>
{
    public int AlertId { get; set; }
    public string InfraObjectId { get; set; }
    public string Severity { get; set; }
    public string Type { get; set; }
    public string CreateDate { get; set; }
    public string EndDate { get; set; }
}