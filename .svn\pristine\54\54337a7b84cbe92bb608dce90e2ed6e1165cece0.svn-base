﻿using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMongoDbMonitorStatusService
{
    Task<BaseResponse> CreateAsync(CreateMongoDbMonitorStatusCommand createMongoDbMonitorStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdateMongoDbMonitorStatusCommand updateMongoDbMonitorStatusCommand);
    Task<List<MongoDbMonitorStatusListVm>> GetAllMongoDbMonitorStatus();
    Task<MongoDbMonitorStatusDetailVm> GetByReferenceId(string id);
    Task<List<MongoDbMonitorStatusDetailByTypeVm>> GetMongoDbMonitorStatusByType(string type);
    Task<PaginatedResult<MongoDbMonitorStatusListVm>> GetPaginatedMongoDbMonitorStatus(GetMongoDbMonitorStatusPaginatedListQuery query);
}