﻿using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Queries;

public class GetSingleSignOnListQueryHandlerTests : IClassFixture<SingleSignOnFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;
    private Mock<ISingleSignOnRepository> _mockSingleSignOnRepository;
    private readonly GetSingleSignOnListQueryHandler _handler;

    public GetSingleSignOnListQueryHandlerTests(SingleSignOnFixture singleSignOnFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnRepository(_singleSignOnFixture.SingleSignOns);

        _handler = new GetSingleSignOnListQueryHandler(_singleSignOnFixture.Mapper, _mockSingleSignOnRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_SingleSignOnCount()
    {
        var result = await _handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_Type_Arcos()
    {
        _singleSignOnFixture.SingleSignOns[0].Id = 1;
        _singleSignOnFixture.SingleSignOns[0].ProfileName = "SingleSignOn_Demo";
        _singleSignOnFixture.SingleSignOns[0].SignOnType = "Arcos";
        _singleSignOnFixture.SingleSignOns[0].Properties = "{\"ssoName\":\"VishwaSSo\",\"arcosOnlineURL\": \"https://www.google.com\",\"arcosWebAPIURL\":\"https://www.perpetuuiti.com\",\"sharedKey\":\"DGIDHGIDSGIDS\",\"serverIP\":\"***********\",\"serviceType\":\"Normal\",\"userName\":\"Vishwa123\",\"dbInstanceName\":\"NameInstance\" }";

        var result = await _handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnListVm>>();
        result[0].Id.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ReferenceId);
        result[0].ProfileName.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ProfileName);
        result[0].SignOnType.ShouldBe(_singleSignOnFixture.SingleSignOns[0].SignOnType);
        result[0].Properties.ShouldBe(_singleSignOnFixture.SingleSignOns[0].Properties);
    }

    [Fact]
    public async Task Handle_Return_Valid_Type_CyberArk()
    {
        _singleSignOnFixture.SingleSignOns[0].Id = 2;
        _singleSignOnFixture.SingleSignOns[0].ProfileName = "Implementation";
        _singleSignOnFixture.SingleSignOns[0].SignOnType = "CyberArk";
        _singleSignOnFixture.SingleSignOns[0].Properties = "{\"CredFilePath\":\"VishwaCreadfile\",\"ConnectionPort\":1234,\"ConnectionTimeOut\":\"2021-08-26T06:41:37.829Z\"}";

        var result = await _handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnListVm>>();
        result[0].Id.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ReferenceId);
        result[0].SignOnType.ShouldBe(_singleSignOnFixture.SingleSignOns[0].SignOnType);
        result[0].ProfileName.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ProfileName);
        result[0].Properties.ShouldBe(_singleSignOnFixture.SingleSignOns[0].Properties);
    }

    [Fact]
    public async Task Handle_Return_Valid_Type_TPAM()
    {
        _singleSignOnFixture.SingleSignOns[0].Id = 3;
        _singleSignOnFixture.SingleSignOns[0].ProfileName = "Testing";
        _singleSignOnFixture.SingleSignOns[0].SignOnType = "TPAM";
        _singleSignOnFixture.SingleSignOns[0].Properties = "{\"userName\":\"jayanth\",\"siteId\":110,\"port\":\"8000\",\"executionPath\":\"dsfds\",\"keyLocation\":\"dsfsdfsd\",\"ipAddress\":\"***********\"}";

        var result = await _handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SingleSignOnListVm>>();
        result[0].Id.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ReferenceId);
        result[0].SignOnType.ShouldBe(_singleSignOnFixture.SingleSignOns[0].SignOnType);
        result[0].ProfileName.ShouldBe(_singleSignOnFixture.SingleSignOns[0].ProfileName);
        result[0].Properties.ShouldBe(_singleSignOnFixture.SingleSignOns[0].Properties);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnEmptyRepository();

        var handler = new GetSingleSignOnListQueryHandler(_singleSignOnFixture.Mapper, _mockSingleSignOnRepository.Object);

        var result = await handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSingleSignOnListQuery(), CancellationToken.None);

        _mockSingleSignOnRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}