﻿using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetOracleMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;


[ApiVersion("6")]
public class OracleMonitorStatusController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateOracleMonitorStatusResponse>> CreateOracleMonitorStatus([FromBody] CreateOracleMonitorStatusCommand createOracleMonitorStatusCommand)
    {
        Logger.LogDebug($"Create Oracle Monitor Status '{createOracleMonitorStatusCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateOracleMonitorStatus), await Mediator.Send(createOracleMonitorStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateOracleMonitorStatusResponse>> UpdateOracleMonitorStatus([FromBody] UpdateOracleMonitorStatusCommand updateOracleMonitorStatusCommand)
    {
        Logger.LogDebug($"Update Oracle Monitor Status '{updateOracleMonitorStatusCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateOracleMonitorStatusCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<OracleMonitorStatusListVm>>> GetAllOracleMonitorStatus()
    {
        Logger.LogDebug("Get All  Oracle Monitor Status");

        return Ok(await Mediator.Send(new GetOracleMonitorStatusListQuery()));
    }

    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<OracleMonitorStatusDetailVm>> GetOracleLMonitorStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Oracle MonitorStatus Detail By Id");

        Logger.LogDebug($"Get Oracle Monitor Status Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetOracleMonitorStatusDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<OracleMonitorStatusDetailVm>>> GetPaginatedOracleMonitorStatus([FromQuery] GetOracleMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Oracle MonitorStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<OracleMonitorStatusDetailByTypeVm>> GetOracleMonitorStatusByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "Oracle Monitor Status Detail By Type");

        Logger.LogDebug($"Get Oracle Monitor Status Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetOracleMonitorStatusDetailByTypeQuery { Type = type }));
    }

    [HttpGet("by/{infraObjectId}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<string>> GetOracleMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "Oracle MonitorStatus InfraObjectId");

        Logger.LogDebug($"Get Oracle MonitorStatus Detail by InfraObjectId '{infraObjectId}'");

        return Ok(await Mediator.Send(new GetOracleMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllOracleMonitorStatusCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllOracleMonitorStatusNameCacheKey };

        ClearCache(cacheKeys);
    }
}

