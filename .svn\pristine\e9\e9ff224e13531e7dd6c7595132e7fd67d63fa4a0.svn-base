using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetByDynamicDashboardId;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DynamicSubDashboardControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DynamicSubDashboardsController _controller;
    private readonly DynamicSubDashboardFixture _dynamicSubDashboardFixture;

    public DynamicSubDashboardControllerTests()
    {
        _dynamicSubDashboardFixture = new DynamicSubDashboardFixture();

        var testBuilder = new ControllerTestBuilder<DynamicSubDashboardsController>();
        _controller = testBuilder.CreateController(
            _ => new DynamicSubDashboardsController(),
            out _mediatorMock);
    }

    #region GetDynamicSubDashboards Tests

    [Fact]
    public async Task GetDynamicSubDashboards_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicSubDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Id));
    }

    [Fact]
    public async Task GetDynamicSubDashboards_WithEmptyList_ReturnsEmptyOkResult()
    {
        // Arrange
        var emptyList = new List<DynamicSubDashboardListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDynamicSubDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicSubDashboards_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDynamicSubDashboards());
    }

    #endregion

    #region GetByDashboardIdAsync Tests

    [Fact]
    public async Task GetByDashboardIdAsync_WithValidDashboardId_ReturnsOkResult()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.NotNull(item.Id));
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithNullDashboardId_ReturnsOkResult()
    {
        // Arrange
        string dynamicDashboardId = null;
        var expectedList = new List<DynamicSubDashboardListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetByDynamicDashBoardIdListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithNonExistentDashboardId_ReturnsEmptyList()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var emptyList = new List<DynamicSubDashboardListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    #endregion

    #region GetDynamicSubDashboardById Tests

    [Fact]
    public async Task GetDynamicSubDashboardById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicSubDashboardFixture.DynamicSubDashboardDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicSubDashboardDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicSubDashboardById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicSubDashboardDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.NotNull(returnedDetail.Name);
    }

    [Fact]
    public async Task GetDynamicSubDashboardById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDynamicSubDashboardById(invalidId));
    }

    [Fact]
    public async Task GetDynamicSubDashboardById_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDynamicSubDashboardById(null));
    }

    #endregion

    #region GetPaginatedDynamicSubDashboards Tests

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        var expectedResult = _dynamicSubDashboardFixture.DynamicSubDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_WithNullQuery_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetPaginatedDynamicSubDashboards(null));
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_WithLargePageSize_ReturnsValidResult()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        query.PageSize = 100;
        var expectedResult = _dynamicSubDashboardFixture.DynamicSubDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
    }

    #endregion

    #region CreateDynamicSubDashboard Tests

    [Fact]
    public async Task CreateDynamicSubDashboard_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.CreateDynamicSubDashboardCommand;
        var expectedResponse = _dynamicSubDashboardFixture.CreateDynamicSubDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicSubDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicSubDashboardResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDynamicSubDashboard_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.CreateDynamicSubDashboard(null));
    }

    [Fact]
    public async Task CreateDynamicSubDashboard_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.CreateDynamicSubDashboardCommand;
        var failureResponse = new CreateDynamicSubDashboardResponse
        {
            Success = false,
            Message = "Validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDynamicSubDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicSubDashboardResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Validation failed", returnedResponse.Message);
    }

    #endregion

    #region UpdateDynamicSubDashboard Tests

    [Fact]
    public async Task UpdateDynamicSubDashboard_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardCommand;
        var expectedResponse = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicSubDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDynamicSubDashboard_WithNullCommand_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDynamicSubDashboard(null));
    }

    [Fact]
    public async Task UpdateDynamicSubDashboard_WithInvalidCommand_ReturnsFailureResponse()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardCommand;
        var failureResponse = new UpdateDynamicSubDashboardResponse
        {
            Success = false,
            Message = "Update validation failed"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDynamicSubDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicSubDashboardResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Equal("Update validation failed", returnedResponse.Message);
    }

    #endregion

    #region DeleteDynamicSubDashboard Tests

    [Fact]
    public async Task DeleteDynamicSubDashboard_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicSubDashboardFixture.DeleteDynamicSubDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicSubDashboardCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicSubDashboard(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDynamicSubDashboard_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDynamicSubDashboard(invalidId));
    }

    [Fact]
    public async Task DeleteDynamicSubDashboard_WithNullId_ThrowsArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDynamicSubDashboard(null));
    }

    #endregion

    #region IsDynamicSubDashboardNameExist Tests

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var subDashboardName = "Enterprise Sub Dashboard";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicSubDashboardNameExist(subDashboardName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var subDashboardName = "Non-Existing Sub Dashboard";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicSubDashboardNameExist(subDashboardName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_WithNullName_ThrowsArgumentException()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDynamicSubDashboardNameExist(null, id));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDynamicSubDashboard_WithComplexConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.CreateDynamicSubDashboardCommand;
        command.Name = "Enterprise Multi-Widget Sub Dashboard";
        var expectedResponse = _dynamicSubDashboardFixture.CreateDynamicSubDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicSubDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDynamicSubDashboardResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Multi-Widget Sub Dashboard", command.Name);
    }

    [Fact]
    public async Task UpdateDynamicSubDashboard_WithLayoutChange_ReturnsOkResult()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardCommand;
        command.Name = "Updated Enterprise Sub Dashboard";
        var expectedResponse = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicSubDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Updated Enterprise Sub Dashboard", command.Name);
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithMultipleSubDashboards_ReturnsAllSubDashboards()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;
        expectedList.ForEach(x => x.Name = "Enterprise Sub Dashboard Component");

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise Sub Dashboard", item.Name));
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_WithSearchFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";
        var expectedResult = _dynamicSubDashboardFixture.DynamicSubDashboardPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal("Enterprise", query.SearchString);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, query.PageSize);
    }

    [Fact]
    public async Task GetDynamicSubDashboardById_WithComplexSubDashboard_ReturnsDetailedSubDashboard()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicSubDashboardFixture.DynamicSubDashboardDetailVm;
        expectedDetail.Id = id;
        expectedDetail.Name = "Enterprise Performance Analytics Sub Dashboard";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicSubDashboardDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicSubDashboardById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicSubDashboardDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Equal("Enterprise Performance Analytics Sub Dashboard", returnedDetail.Name);
    }

    [Fact]
    public async Task DeleteDynamicSubDashboard_WithDependentMappings_ReturnsSuccessResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _dynamicSubDashboardFixture.DeleteDynamicSubDashboardResponse;
        expectedResponse.Message = "DynamicSubDashboard and all dependent dashboard mappings deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicSubDashboardCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicSubDashboard(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("dependent dashboard mappings", returnedResponse.Message);
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithHierarchicalStructure_ReturnsOrderedSubDashboards()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;
        for (int i = 0; i < expectedList.Count; i++)
        {
            expectedList[i].Name = $"Enterprise Sub Dashboard Level {i + 1}";
        }

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Level", item.Name));
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_WithSimilarNamesButDifferentCase_ReturnsCorrectResult()
    {
        // Arrange
        var subDashboardName = "ENTERPRISE SUB DASHBOARD";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicSubDashboardNameExist(subDashboardName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    #endregion

    #region Additional Test Cases Following CompanyControllerTests and BusinessServiceControllerTests Patterns

    [Fact]
    public async Task GetDynamicSubDashboards_ReturnsEmptyList_WhenNoSubDashboardsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardListQuery>(), default))
            .ReturnsAsync(new List<DynamicSubDashboardListVm>());

        // Act
        var result = await _controller.GetDynamicSubDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicSubDashboardById_Throws_WhenSubDashboardNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicSubDashboardDetailQuery>(q => q.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicSubDashboard", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDynamicSubDashboardById(id));
    }

    [Fact]
    public async Task CreateDynamicSubDashboard_Throws_WhenNameExists()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.CreateDynamicSubDashboardCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDynamicSubDashboardCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Sub dashboard name already exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDynamicSubDashboard(command));
    }

    [Fact]
    public async Task UpdateDynamicSubDashboard_Throws_WhenSubDashboardNotFound()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardCommand;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDynamicSubDashboardCommand>(), default))
            .ThrowsAsync(new NotFoundException("DynamicSubDashboard", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDynamicSubDashboard(command));
    }

    [Fact]
    public async Task DeleteDynamicSubDashboard_Throws_WhenSubDashboardNotFound()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicSubDashboardCommand>(c => c.Id == id), default))
            .ThrowsAsync(new NotFoundException("DynamicSubDashboard", id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.DeleteDynamicSubDashboard(id));
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_IncludesIdInQuery_WhenProvided()
    {
        // Arrange
        var subDashboardName = "Test Sub Dashboard";
        var subDashboardId = Guid.NewGuid().ToString();
        string? capturedId = null;
        string? capturedName = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicSubDashboardNameUniqueQuery query)
                {
                    capturedId = query.Id;
                    capturedName = query.Name;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicSubDashboardNameExist(subDashboardName, subDashboardId);

        // Assert
        Assert.Equal(subDashboardId, capturedId);
        Assert.Equal(subDashboardName, capturedName);
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        // Arrange
        var subDashboardName = "Test Sub Dashboard";
        string? capturedId = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDynamicSubDashboardNameUniqueQuery query)
                {
                    capturedId = query.Id;
                }
            })
            .ReturnsAsync(false);

        // Act
        await _controller.IsDynamicSubDashboardNameExist(subDashboardName, null);

        // Assert
        Assert.Null(capturedId);
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_HandlesSearchString_Correctly()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";

        var expectedData = _dynamicSubDashboardFixture.DynamicSubDashboardListVm.Take(1).ToList();
        var expectedResult = PaginatedResult<DynamicSubDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicSubDashboardPaginatedListQuery>(q => q.SearchString == "Enterprise"), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal(1, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_HandlesDifferentPageSizes()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 3;

        var expectedData = _dynamicSubDashboardFixture.DynamicSubDashboardListVm.Take(3).ToList();
        var expectedResult = PaginatedResult<DynamicSubDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(3, paginatedResult.PageSize);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(3, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateDynamicSubDashboard_Returns201Created_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.CreateDynamicSubDashboardCommand;
        var expectedMessage = $"DynamicSubDashboard '{command.Name}' has been created successfully!";
        var expectedResponse = new CreateDynamicSubDashboardResponse
        {
            Id = Guid.NewGuid().ToString(),
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDynamicSubDashboard(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateDynamicSubDashboardResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
    }

    [Fact]
    public async Task UpdateDynamicSubDashboard_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var command = _dynamicSubDashboardFixture.UpdateDynamicSubDashboardCommand;
        var expectedMessage = $"DynamicSubDashboard '{command.Name}' has been updated successfully!";
        var expectedResponse = new UpdateDynamicSubDashboardResponse
        {
            Id = command.Id,
            Success = true,
            Message = expectedMessage
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDynamicSubDashboard(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains(command.Name, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithValidDashboardId_ReturnsCorrectSubDashboards()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;
        expectedList.ForEach(x => x.Name = "Dashboard Specific Sub Dashboard");

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Dashboard Specific", item.Name));
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithInvalidDashboardId_ReturnsEmptyList()
    {
        // Arrange
        var invalidDashboardId = Guid.NewGuid().ToString();
        var emptyList = new List<DynamicSubDashboardListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == invalidDashboardId), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(invalidDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDynamicSubDashboardById_WithValidId_ReturnsCorrectSubDashboardType()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _dynamicSubDashboardFixture.DynamicSubDashboardDetailVm;
        expectedDetail.Id = id;
        expectedDetail.Name = "Analytics Sub Dashboard";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDynamicSubDashboardDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDynamicSubDashboardById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DynamicSubDashboardDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Equal("Analytics Sub Dashboard", returnedDetail.Name);
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var subDashboardName = "Existing Sub Dashboard";
        var subDashboardId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDynamicSubDashboardNameExist(subDashboardName, subDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsDynamicSubDashboardNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var subDashboardName = "New Sub Dashboard";
        var subDashboardId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDynamicSubDashboardNameExist(subDashboardName, subDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteDynamicSubDashboard_ReturnsOkResult_WithSuccessMessage()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = new DeleteDynamicSubDashboardResponse
        {
            Success = true,
            Message = "DynamicSubDashboard has been deleted successfully!"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDynamicSubDashboardCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDynamicSubDashboard(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteDynamicSubDashboardResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("deleted successfully", response.Message);
    }

    [Fact]
    public async Task GetPaginatedDynamicSubDashboards_WithEmptySearchString_ReturnsAllResults()
    {
        // Arrange
        var query = _dynamicSubDashboardFixture.GetDynamicSubDashboardPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "";

        var expectedData = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;
        var expectedResult = PaginatedResult<DynamicSubDashboardListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDynamicSubDashboards(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetByDashboardIdAsync_WithMultipleDashboards_ReturnsCorrectHierarchy()
    {
        // Arrange
        var dynamicDashboardId = Guid.NewGuid().ToString();
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;

        for (int i = 0; i < expectedList.Count; i++)
        {
            expectedList[i].Name = $"Sub Dashboard Level {i + 1}";
            expectedList[i].DynamicDashBoardId = dynamicDashboardId;
            expectedList[i].DynamicDashBoardName = "Parent Dashboard";
            expectedList[i].Properties = $"{{\"level\":{i + 1},\"widgets\":[]}}";
        }

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByDynamicDashBoardIdListQuery>(q => q.DynamicDashboardId == dynamicDashboardId), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetByDashboardIdAsync(dynamicDashboardId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item =>
        {
            Assert.Equal(dynamicDashboardId, item.DynamicDashBoardId);
            Assert.Equal("Parent Dashboard", item.DynamicDashBoardName);
            Assert.Contains("level", item.Properties);
        });
    }

    [Fact]
    public async Task GetDynamicSubDashboards_WithSpecificProperties_ReturnsFilteredResults()
    {
        // Arrange
        var expectedList = _dynamicSubDashboardFixture.DynamicSubDashboardListVm;
        expectedList.ForEach(s => s.Properties = "{\"layout\":\"grid\",\"columns\":3,\"widgets\":[]}");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDynamicSubDashboardListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDynamicSubDashboards();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DynamicSubDashboardListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, subDashboard =>
        {
            Assert.Contains("grid", subDashboard.Properties);
            Assert.Contains("columns", subDashboard.Properties);
        });
    }

    #endregion
}
