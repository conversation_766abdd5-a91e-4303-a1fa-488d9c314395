using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.FiaBia;

public class BiaRulesService : BaseClient, IBiaRulesService
{
    public BiaRulesService(IConfiguration config, IAppCache cache, ILogger<BiaRulesService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<BiaRulesListVm>> GetBiaImpactList()
    {
        var request = new RestRequest("api/v6/biarules");

        return await GetFromCache<List<BiaRulesListVm>>(request, "GetBiaImpactList");
    }

    public async Task<BaseResponse> CreateAsync(CreateBiaRulesCommand createBiaImpactCommand)
    {
        var request = new RestRequest("api/v6/biarules", Method.Post);

        request.AddJsonBody(createBiaImpactCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBiaRulesCommand updateBiaImpactCommand)
    {
        var request = new RestRequest("api/v6/biarules", Method.Put);

        request.AddJsonBody(updateBiaImpactCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/biarules/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BiaRulesDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/biarules/{id}");

        return await Get<BiaRulesDetailVm>(request);
    }
    #region NameExist
    //  public async Task<bool> IsBiaImpactNameExist(string name, string? id)
    //  {
    //     var request = new RestRequest($"api/v6/biarules/name-exist?biaimpactName={name}&id={id}");
    //
    //     return await Get<bool>(request);
    //  }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<BiaRulesListVm>> GetPaginatedBiaImpacts(GetBiaRulesPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/biarules/paginated-list");

        return await Get<PaginatedResult<BiaRulesListVm>>(request);
    }

    #endregion

    public async Task<BiaRulesListVm> GetBiaRulesByEntityIdAndType(string entityId, string type)
    {
        var request = new RestRequest($"api/v6/biarules//entityid?entityId=&type=");

        return await Get<BiaRulesListVm>(request);
    }
}
