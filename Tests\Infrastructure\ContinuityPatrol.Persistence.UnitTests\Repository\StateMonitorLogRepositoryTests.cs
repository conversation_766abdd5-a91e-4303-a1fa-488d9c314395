﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
  public  class StateMonitorLogRepositoryTests
    {
        private readonly ApplicationDbContext _dbContext;

        public StateMonitorLogRepositoryTests()
        {
            _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());

        }

        [Fact]
        public void Constructor_ShouldInitializeRepository()
        {
            // Arrange

            // Act
            var repository = new StateMonitorLogRepository(_dbContext);

            // Assert
            Assert.NotNull(repository);
        }
    }
}
