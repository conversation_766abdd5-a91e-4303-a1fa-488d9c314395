﻿using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Delete;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class GroupPolicyService : BaseService, IGroupPolicyService
{
    public GroupPolicyService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<GroupPolicyListVm>> GetGroupPolicies()
    {
        Logger.LogDebug("Get All GroupPolicy");

        return await Mediator.Send(new GetGroupPolicyListQuery());
    }

    public async Task<BaseResponse> CreateAsync(CreateGroupPolicyCommand createGroupPolicyCommand)
    {
        Logger.LogDebug($"Create Group Policy '{createGroupPolicyCommand}'");

        return await Mediator.Send(createGroupPolicyCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateGroupPolicyCommand updateGroupPolicyCommand)
    {
        Logger.LogDebug($"Update Group Policy '{updateGroupPolicyCommand}'");

        return await Mediator.Send(updateGroupPolicyCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string formId)
    {
        Guard.Against.InvalidGuidOrEmpty(formId, "Form Id");

        Logger.LogDebug($"Delete Group Policy Details by Id '{formId}'");

        return await Mediator.Send(new DeleteGroupPolicyCommand { Id = formId });
    }

    public async Task<GetGroupPolicyDetailVm> GetGroupPolicyById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Group Policy Id");

        Logger.LogDebug($"Get Group Policy Detail by Id '{id}'");

        return await Mediator.Send(new GetGroupPolicyDetailQuery { Id = id });
    }

    public async Task<List<GroupPolicyNameVm>> GetGroupPolicyNames()
    {
        Logger.LogDebug("Get All GroupPolicy Names");

        return await Mediator.Send(new GetGroupPolicyNameQuery());
    }

    public async Task<bool> IsGroupPolicyNameExist(string groupPolicyName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(groupPolicyName, "GroupPolicy Name");

        Logger.LogDebug($"Check Name Exists Detail by GroupPolicy Name '{groupPolicyName}' and Id '{id}'");

        return await Mediator.Send(new GetGroupPolicyNameUniqueQuery
            { GroupPolicyName = groupPolicyName, GroupPolicyId = id });
    }

    public async Task<PaginatedResult<GroupPolicyListVm>> GetPaginatedGroupPolicies(
        GetGroupPolicyPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Group Policy Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<GroupPolicyTypeVm>> GetByType(string groupPolicyType)
    {
        Logger.LogDebug($"Get Group Policy Details by Type '{groupPolicyType}'");

        return await Mediator.Send(new GetGroupPolicyTypeQuery { Type = groupPolicyType });
    }
}