namespace ContinuityPatrol.Domain.Entities;

public class CyberAlert : AuditableEntity
{
    public string Type { get; set; }
	public string Severity { get; set; }
    [Column(TypeName = "NCLOB")] public string SystemMessage { get; set; }
    [Column(TypeName = "NCLOB")] public string UserMessage { get; set; }
	public string JobName { get; set; }
	public string ClientAlertId { get; set; }
	public int IsResolve { get; set; }
	public int IsAcknowledgement { get; set; }
	public string EntityId { get; set; }
	public string EntityType { get; set; }
	public int AlertCategoryId { get; set; }
		
}
