﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Persistence.Services;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.UnitTests.Services
{
    public class PasswordAuthenticationServiceTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IUserLoginRepository> _mockUserLoginRepository;
        private readonly Mock<ICompanyRepository> _mockCompanyRepository;
        private readonly Mock<ILogger<BaseAuthenticationService>> _mockLogger;
        private readonly Mock<IAccessManagerRepository> _mockAccessManagerRepository;
        private readonly Mock<IUserCredentialRepository> _mockUserCredentialRepository;
        private readonly Mock<IUserInfraObjectRepository> _mockUserInfraObjectRepository;
        private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
        private readonly Mock<IWebHostEnvironment> _mockWebHostEnvironment;
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;
        private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;
        private readonly Mock<IAlertRepository> _mockAlertRepository;
        private readonly Mock<IDynamicDashboardMapRepository> _mockDynamicDashboardMapRepository;
        private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly PasswordAuthenticationService _service;

        public PasswordAuthenticationServiceTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockUserLoginRepository = new Mock<IUserLoginRepository>();
            _mockCompanyRepository = new Mock<ICompanyRepository>();
            _mockLogger = new Mock<ILogger<BaseAuthenticationService>>();
            _mockAccessManagerRepository = new Mock<IAccessManagerRepository>();
            _mockUserCredentialRepository = new Mock<IUserCredentialRepository>();
            _mockUserInfraObjectRepository = new Mock<IUserInfraObjectRepository>();
            _mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
            _mockWebHostEnvironment = new Mock<IWebHostEnvironment>();
            _mockEmailService = new Mock<IEmailService>();
            _mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();
            _mockUserInfoRepository = new Mock<IUserInfoRepository>();
            _mockAlertRepository = new Mock<IAlertRepository>();
            _mockDynamicDashboardMapRepository = new Mock<IDynamicDashboardMapRepository>();
            _mockGlobalSettingRepository = new Mock<IGlobalSettingRepository>();
            _mockConfiguration = new Mock<IConfiguration>();

            _service = new PasswordAuthenticationService(
                _mockConfiguration.Object,
                _mockUserRepository.Object,
                _mockUserLoginRepository.Object,
                _mockCompanyRepository.Object,
                _mockLogger.Object,
                _mockAccessManagerRepository.Object,
                _mockUserCredentialRepository.Object,
                _mockUserInfraObjectRepository.Object,
                _mockLicenseManagerRepository.Object,
                _mockWebHostEnvironment.Object,
                _mockEmailService.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockUserInfoRepository.Object,
                _mockAlertRepository.Object,
                _mockDynamicDashboardMapRepository.Object,
                _mockGlobalSettingRepository.Object
            );
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnSuccessResponse_WhenValidCredentialsProvided()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                CompanyName = "Test Company",
                SessionTimeout = 30,
                InfraObjectAllFlag = true,
                TwoFactorAuthentication = "",
                IsReset = false,
                LoginType = "local",
                IsLock = false,
                IsDefaultDashboard = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0,
                LastPasswordChanged = DateTime.UtcNow.AddDays(-10)
            };

            var company = new Company
            {
                ReferenceId = "company1",
                IsParent = true
            };

            var accessManager = new AccessManager
            {
                RoleId = "Administrator",
                Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>(){new UserCredential() { UserId = user.ReferenceId }});

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(accessManager);

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(company);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
                    new LicenseManager
                    {
                        ExpiryDate = DateTime.UtcNow.AddDays(30).ToString("dd MMMM yyyy"),
                        MacAddress = "00:00:00:00:00:00"
                    }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" });

            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.Equal(user.LoginName, result.LoginName);
            Assert.Equal(user.CompanyId, result.CompanyId);
            Assert.Equal(user.CompanyName, result.CompanyName);
            Assert.Equal(user.Role, result.Role);
            Assert.Equal(user.RoleName, result.RoleName);
            Assert.Equal(user.ReferenceId, result.UserId);
            Assert.Equal(user.SessionTimeout, result.SessionTimeout);
            Assert.Equal(user.InfraObjectAllFlag, result.IsAllInfra);
            Assert.Equal(user.TwoFactorAuthentication, result.TwoFactorAuthentication);
            Assert.Equal(user.IsReset, result.IsReset);
            Assert.Equal(user.LoginType, result.AuthenticationType);
            Assert.True(result.IsLicenseValidity);
            Assert.True(result.LicenseEmpty);
            Assert.True(result.IsParent);
            Assert.Equal(company.ReferenceId, result.ParentCompanyId);
            Assert.Equal(user.IsDefaultDashboard, result.IsDefaultDashboard);
            Assert.Equal(string.Empty, result.Url);
            Assert.Equal(userLogin.LastPasswordChanged, result.LastPasswordChanged);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenUserNotFound()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "nonexistentuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync((User)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(Authentication.InvalidLogin, exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenInvalidPassword()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("wrongpassword"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            // Act & Assert
            await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenUserIsLocked()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "lockeduser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "lockeduser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                IsLock = true
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(string.Format(Authentication.UserAccountLocked, user.LoginName), exception.Message);
            Assert.Equal((int)ErrorCode.AccountLocked, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenCompanyIdMismatch()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "69edbf31-d2ae-4274-bafd-cc0c3fab3314"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "6244bbad-c712-418a-90db-486832634c0c",
                ReferenceId = "user1",
                IsLock = false
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            var accessManager = new AccessManager
            {
                RoleId = "Administrator",
                Properties = "[]"
            };

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(accessManager);

            var company = new Company
            {
                ReferenceId = "6244bbad-c712-418a-90db-486832634c0c",
                IsParent = false
            };

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(request.CompanyId))
                .ReturnsAsync(company);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(request.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());
             
            // Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal("Invalid Login Credentials.", exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowAuthenticationException_WhenInvalidRole()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "InvalidRole",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync((AccessManager)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(Authentication.InvalidAccess, exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldSetDefaultTimeout_WhenUserTimeoutIsZero()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                CompanyName = "Test Company",
                SessionTimeout = 0, // Zero timeout
                InfraObjectAllFlag = true,
                TwoFactorAuthentication = "",
                IsReset = false,
                LoginType = "local",
                IsLock = false,
                IsDefaultDashboard = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            var company = new Company
            {
                ReferenceId = "company1",
                IsParent = true
            };

            var accessManager = new AccessManager
            {
                RoleId = "Administrator",
                Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(accessManager);

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(company);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(PasswordAuthenticationService.DefaultTimeout, result.SessionTimeout);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleExpiredLicense()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup expired license (30 days in the past)
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
            new LicenseManager
            {
                ExpiryDate = DateTime.UtcNow.AddDays(-30).ToString("dd MMMM yyyy"),
                MacAddress = "00:00:00:00:00:00"
            }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" });

            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsLicenseValidity); // License should be invalid due to expiration
            Assert.True(result.LicenseEmpty); // LicenseEmpty should still be true
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleInvalidDateFormat()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup license with invalid date format
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
            new LicenseManager
            {
                ExpiryDate = "Invalid Date Format",
                MacAddress = "00:00:00:00:00:00"
            }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" });

            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsLicenseValidity); // License should be invalid due to invalid date format
            Assert.True(result.LicenseEmpty); // LicenseEmpty should still be true
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleMacAddressMismatch()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup valid license but with mismatched MAC address
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
            new LicenseManager
            {
                ExpiryDate = DateTime.UtcNow.AddDays(30).ToString("dd MMMM yyyy"),
                MacAddress = "11:22:33:44:55:66" // Different MAC address
            }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" }); // System MAC address

            // Set environment to Production to trigger MAC address check
            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsLicenseValidity); // License should be valid (not expired)
            Assert.False(result.LicenseEmpty); // LicenseEmpty should be false due to MAC address mismatch
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleMultipleLicensesWithMixedValidity()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);
            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup multiple licenses with mixed validity
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
            new LicenseManager
            {
                ExpiryDate = DateTime.UtcNow.AddDays(-30).ToString("dd MMMM yyyy"), // Expired
                MacAddress = "00:00:00:00:00:00"
            },
            new LicenseManager
            {
                ExpiryDate = DateTime.UtcNow.AddDays(30).ToString("dd MMMM yyyy"), // Valid
                MacAddress = "00:00:00:00:00:00"
            },
            new LicenseManager
            {
                ExpiryDate = "Invalid Date Format", // Invalid date
                MacAddress = "00:00:00:00:00:00"
            }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" });

            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsLicenseValidity); // Should be true because at least one license is valid
            Assert.True(result.LicenseEmpty); // Should be true in development environment
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleEmptyLicenseList()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup empty license list
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.IsLicenseValidity); // Should be false with no licenses
            Assert.False(result.LicenseEmpty); // Should be false with no licenses
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleMultipleMacAddresses()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);
            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            // Setup license with multiple MAC addresses in comma-separated format
            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>
                {
            new LicenseManager
            {
                ExpiryDate = DateTime.UtcNow.AddDays(30).ToString("dd MMMM yyyy"),
                MacAddress = "11:22:33:44:55:66,00:00:00:00:00:00,AA:BB:CC:DD:EE:FF" // Multiple MAC addresses
            }
                });

            _mockLicenseManagerRepository.Setup(repo => repo.GetMacAddress())
                .ReturnsAsync(new List<string> { "00:00:00:00:00:00" }); // System MAC address matches one in the list

            // Set environment to Production to trigger MAC address check
            _mockWebHostEnvironment.Setup(env => env.EnvironmentName).Returns("Production");

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsLicenseValidity); // License should be valid (not expired)
            Assert.True(result.LicenseEmpty); // LicenseEmpty should be true because MAC address matches
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnOperatorPermissions_WhenUserIsOperator()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "operatoruser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }
            };

            var user = new User
            {
                LoginName = "operatoruser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "6244bbad-c712-418a-90db-486832634c0c",
                Role = "operator", // Operator role
                RoleName = "Operator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "6244bbad-c712-418a-90db-486832634c0c",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            //// Mock the ToDictionary extension method behavior
            //var mockExtensionMethod = new Mock<Func<UserRole, Dictionary<string, string>>>();
            //mockExtensionMethod.Setup(f => f(It.IsAny<UserRole>())).Returns(userRoleDictionary);

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "operator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // Verify that the permissions match what Permissions.OperatorAll() would return
            var operatorPermissions = new List<string>
            {
                "Permissions.Dashboard.View",
                "Permissions.Dashboard.Monitor",
                "Permissions.Dashboard.Management"
            };

            Assert.Equal(operatorPermissions.Count, result.Permissions.Count());
            foreach (var permission in operatorPermissions)
            {
                Assert.Contains(permission, result.Permissions);
            }
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnManagerPermissions_WhenUserIsManager()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "manageruser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
             var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }

            };


            var user = new User
            {
                LoginName = "manageruser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "manager", // Manager role
                RoleName = "Manager",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "manager", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // Verify that the permissions match what Permissions.ManageAll() would return
            var managerPermissions = new List<string>
            {
                "Permissions.Dashboard.View",
                "Permissions.Dashboard.Monitor",
                "Permissions.Dashboard.Management"
            };

            Assert.Equal(managerPermissions.Count, result.Permissions.Count());
            foreach (var permission in managerPermissions)
            {
                Assert.Contains(permission, result.Permissions);
            }
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnAllPermissions_WhenUserIsSuperAdmin()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "superadminuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
 
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }

            };

            var user = new User
            {
                LoginName = "superadminuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "superadmin", // SuperAdmin role
                RoleName = "SuperAdmin",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "superadmin", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // We can't check all permissions, but we can verify that it contains more permissions than other roles
            Assert.True(result.Permissions.Count() > 2); // All permissions should be more than what Operator or Manager has
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnAllPermissions_WhenUserIsSiteAdmin()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "siteadminuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }

            };

            var user = new User
            {
                LoginName = "siteadminuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "siteadmin", // SiteAdmin role
                RoleName = "SiteAdmin",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);
            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "siteadmin", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // We can't check all permissions, but we can verify that it contains more permissions than other roles
            Assert.True(result.Permissions.Count() > 2); // All permissions should be more than what Operator or Manager has
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnAllPermissions_WhenUserIsAdministrator()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "adminuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" }, 
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }

            };

            var user = new User
            {
                LoginName = "adminuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "administrator", // Administrator role
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // We can't check all permissions, but we can verify that it contains more permissions than other roles
            Assert.True(result.Permissions.Count() > 2); // All permissions should be more than what Operator or Manager has
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldReturnCustomPermissions_WhenUserHasCustomRole()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "customuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }
            };

            var user = new User
            {
                LoginName = "customuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "customrole", // Custom role not in the standard roles
                RoleName = "Custom Role",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            // Custom JSON permissions
            var customPermissionsJson = @"{
                ""Permissions"": {
                    ""Dashboard"": {
                        ""View"": true,
                        ""Monitor"": true
                    },
                    ""Reports"": {
                        ""View"": true
                    }
                }
            }";

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "customrole", Properties = customPermissionsJson });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // Verify that the permissions include the custom permissions from the JSON
            Assert.Contains("Permissions.Dashboard.View", result.Permissions);
            Assert.Contains("Permissions.Dashboard.Monitor", result.Permissions);
            Assert.Contains("Permissions.Reports.View", result.Permissions);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowException_WhenRoleNotFoundAndNoAccessManager()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "invalidroleuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1"
            };

            // Create a dictionary that matches what default(UserRole).ToDictionary() would return
            var userRoleDictionary = new Dictionary<string, string>
            {
                { "Operator", "operator" },
                { "Manager", "manager" },
                { "SuperAdmin", "superadmin" },
                { "SiteAdmin", "siteadmin" },
                { "Administrator", "administrator" }
            };

            var user = new User
            {
                LoginName = "invalidroleuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "invalidrole", // Role that doesn't exist
                RoleName = "Invalid Role",
                IsLock = false
            };

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            // Return null for the access manager to simulate a role that doesn't exist
            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync((AccessManager)null);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal(Authentication.InvalidAccess, exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleNullCompany()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false,
                IsDefaultDashboard = true,
                Url = "https://dashboard.example.com"
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            // Return null for company to trigger the null company condition
            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync((Company)null);

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.Equal(user.LoginName, result.LoginName);
            Assert.Equal(user.CompanyId, result.CompanyId);
            Assert.Equal(user.Role, result.Role);
            Assert.Equal(user.RoleName, result.RoleName);
            Assert.Equal(user.ReferenceId, result.UserId);
            Assert.False(result.IsParent); // Should be false when company is null
            Assert.Null(result.ParentCompanyId); // Should be null when company is null
            Assert.True(result.IsDefaultDashboard);
            Assert.Equal("https://dashboard.example.com", result.Url);

        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleGenericException()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            // Throw a generic exception during the process to trigger the catch block
            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ThrowsAsync(new InvalidOperationException("Simulated database error"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            Assert.Equal("An unexpected error occurred during authentication", exception.Message);
            Assert.Equal((int)ErrorCode.InvalidAuthentication, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldPropagateAuthenticationException()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            // Throw an AuthenticationException to test the specific catch block
            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ThrowsAsync(new AuthenticationException("Custom authentication error", (int)ErrorCode.AccountLocked));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            // Verify that the original AuthenticationException is propagated without modification
            Assert.Equal("Custom authentication error", exception.Message);
            Assert.Equal((int)ErrorCode.AccountLocked, exception.ErrorCode);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleDefaultDashboardUrl()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false,
                IsDefaultDashboard = true,
                Url = "https://dashboard.example.com"
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.True(result.IsDefaultDashboard);
            Assert.Equal("https://dashboard.example.com", result.Url);
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleNonDefaultDashboard()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false,
                IsDefaultDashboard = false, // Not using default dashboard
                Url = "https://dashboard.example.com" // This URL should be ignored
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}        " });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.False(result.IsDefaultDashboard);
            Assert.Equal(string.Empty, result.Url); // URL should be empty when not using default dashboard
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleParentCompany()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0,
                LastPasswordChanged = DateTime.Now.AddDays(-30)
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            // Setup a parent company
            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company
                {
                    ReferenceId = "company1",
                    IsParent = true,
                    ParentId = "parent1"
                });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.True(result.IsParent); // Should be true for parent company
            Assert.Equal("parent1", result.ParentCompanyId); // Should use ParentId when available
            Assert.Equal(userLogin.LastPasswordChanged, result.LastPasswordChanged); // Should include LastPasswordChanged
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleCompanyWithNoParentId()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            // Setup a company with no ParentId
            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company
                {
                    ReferenceId = "company1",
                    IsParent = true,
                    ParentId = null // No ParentId
                });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.True(result.IsParent); // Should be true for parent company
            Assert.Equal("company1", result.ParentCompanyId); // Should use ReferenceId when ParentId is null
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleDefaultSessionTimeout()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false,
                SessionTimeout = 0 // Zero session timeout should use default
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.Equal(PasswordAuthenticationService.DefaultTimeout, result.SessionTimeout); // Should use default timeout
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldHandleCustomSessionTimeout()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("password123"),
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false,
                SessionTimeout = 45 // Custom session timeout
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);
            Assert.Equal(45, result.SessionTimeout); // Should use custom timeout
        }

        [Fact]
        public async Task AuthenticateAsync_ShouldThrowException_WhenPasswordIsInvalid()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("wrongpassword"), // Wrong password
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("correctpassword"), // Correct password
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 2 // Already has 2 invalid attempts
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            // Setup for VerifyLoginAttempt method
            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync((UserLogin ul) =>
                {
                    // Simulate updating the InvalidLoginAttempt count
                    ul.InvalidLoginAttempt += 1;
                    return ul;
                });

            // Setup to throw AuthenticationException with InvalidLoginAttempt error code
            // This simulates what happens in the VerifyLoginAttempt method
            _mockUserRepository.Setup(repo => repo.UpdateAsync(It.IsAny<User>()))
                .Callback(() => throw new AuthenticationException(
                    string.Format(Authentication.InvalidLoginAttempt, 5 - (userLogin.InvalidLoginAttempt + 1), user.LoginName),
                    (int)ErrorCode.InvalidLoginAttempt));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            // Verify that the correct exception is thrown
            Assert.Equal((int)ErrorCode.InvalidLoginAttempt, exception.ErrorCode);
            Assert.Contains("Invalid login ", exception.Message);


        }

        [Fact]
        public async Task AuthenticateAsync_ShouldLockAccount_WhenMaxInvalidAttemptsReached()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("wrongpassword"), // Wrong password
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("correctpassword"), // Correct password
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 4 // Already has 4 invalid attempts (max is 4)
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            // Setup for VerifyLoginAttempt method
            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .Callback<UserLogin>(ul => ul.InvalidLoginAttempt += 1)
                .ReturnsAsync(userLogin);

            // Setup to update user.IsLock to true and throw AccountLocked exception
            _mockUserRepository.Setup(repo => repo.UpdateAsync(It.IsAny<User>()))
                .Callback<User>(u => u.IsLock = true)
                .ReturnsAsync(user)
                .Callback(() => throw new AuthenticationException(
                    string.Format(Authentication.UserAccountLocked, user.LoginName),
                    (int)ErrorCode.AccountLocked));

            //// Setup for email service (used in SendEmail method)
            //_mockEmailService.Setup(service => service.SendEmailAsync(It.IsAny<EmailMessage>()))
            //    .Returns(Task.CompletedTask);

            _mockSmtpConfigurationRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<SmtpConfiguration> { new SmtpConfiguration() });

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AuthenticationException>(() =>
                _service.AuthenticateAsync(request));

            // Verify that the correct exception is thrown
            Assert.Equal((int)ErrorCode.AccountLocked, exception.ErrorCode);
            Assert.Contains("account locked", exception.Message);

        }

        [Fact]
        public async Task AuthenticateAsync_ShouldSucceed_WhenPasswordIsValid()
        {
            // Arrange
            var request = new AuthenticationRequest
            {
                LoginName = "testuser",
                Password = SecurityHelper.Encrypt("correctpassword"), // Correct password
                CompanyId = "company1",
                IpAddress = "127.0.0.1"
            };

            var user = new User
            {
                LoginName = "testuser",
                LoginPassword = SecurityHelper.Encrypt("correctpassword"), // Same password
                CompanyId = "company1",
                ReferenceId = "user1",
                Role = "Administrator",
                RoleName = "Administrator",
                IsLock = false
            };

            var userLogin = new UserLogin
            {
                UserId = "user1",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(request.LoginName))
                .ReturnsAsync(user);

            _mockUserLoginRepository.Setup(repo => repo.GetUserLoginByUserId(user.ReferenceId))
                .ReturnsAsync(userLogin);

            _mockUserLoginRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserLogin>()))
                .ReturnsAsync(userLogin);

            _mockUserCredentialRepository.Setup(repo => repo.GetUserCredentialByUserId(user.ReferenceId))
                .ReturnsAsync(new List<UserCredential>() { new UserCredential() { UserId = user.ReferenceId } });

            _mockUserCredentialRepository.Setup(repo => repo.UpdateAsync(It.IsAny<UserCredential>()))
                .ReturnsAsync(new UserCredential());

            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role))
                .ReturnsAsync(new AccessManager { RoleId = "Administrator", Properties = "{\"Permissions\":{\"Dashboard\":{\"View\":true,\"Monitor\":true,\"Management\":true}}}" });

            _mockCompanyRepository.Setup(repo => repo.GetParentCompanyByLoginCompanyId(user.CompanyId))
                .ReturnsAsync(new Company { ReferenceId = "company1" });

            _mockUserInfraObjectRepository.Setup(repo => repo.GetUserInfraObjectPropsByUserIdAsync(user.ReferenceId))
                .ReturnsAsync(new UserInfraObject { Properties = "" });

            _mockLicenseManagerRepository.Setup(repo => repo.GetLicenseExpiryDateByCompanyId(user.CompanyId))
                .ReturnsAsync(new List<LicenseManager>());

            // Act
            var result = await _service.AuthenticateAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsAuthorized);

            // Verify that VerifyLoginAttempt was NOT called (since password is valid)
            _mockUserRepository.Verify(repo => repo.UpdateAsync(It.IsAny<User>()), Times.Never);
 
        }

        [Fact]
        public void IsPasswordValid_ShouldReturnTrue_WhenPasswordsMatch()
        {
            // Arrange
            var inputPassword = SecurityHelper.Encrypt("password123");
            var storedPassword = SecurityHelper.Encrypt("password123");

            // Create an instance of the service to test the private method
            var service = new PasswordAuthenticationService(
                _mockConfiguration.Object,
                _mockUserRepository.Object,
                _mockUserLoginRepository.Object,
                _mockCompanyRepository.Object,
                _mockLogger.Object,
                _mockAccessManagerRepository.Object,
                _mockUserCredentialRepository.Object,
                _mockUserInfraObjectRepository.Object,
                _mockLicenseManagerRepository.Object,
                _mockWebHostEnvironment.Object,
                _mockEmailService.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockUserInfoRepository.Object,
                _mockAlertRepository.Object,
                _mockDynamicDashboardMapRepository.Object,
                _mockGlobalSettingRepository.Object);

            // Use reflection to access the private method
            var methodInfo = typeof(PasswordAuthenticationService).GetMethod("IsPasswordValid",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = (bool)methodInfo.Invoke(service, new object[] { inputPassword, storedPassword });

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsPasswordValid_ShouldReturnFalse_WhenPasswordsDontMatch()
        {
            // Arrange
            var inputPassword = SecurityHelper.Encrypt("wrongpassword");
            var storedPassword = SecurityHelper.Encrypt("correctpassword");

            // Create an instance of the service to test the private method
            var service = new PasswordAuthenticationService(
                _mockConfiguration.Object,
                _mockUserRepository.Object,
                _mockUserLoginRepository.Object,
                _mockCompanyRepository.Object,
                _mockLogger.Object,
                _mockAccessManagerRepository.Object,
                _mockUserCredentialRepository.Object,
                _mockUserInfraObjectRepository.Object,
                _mockLicenseManagerRepository.Object,
                _mockWebHostEnvironment.Object,
                _mockEmailService.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockUserInfoRepository.Object,
                _mockAlertRepository.Object,
                _mockDynamicDashboardMapRepository.Object,
                _mockGlobalSettingRepository.Object);

            // Use reflection to access the private method
            var methodInfo = typeof(PasswordAuthenticationService).GetMethod("IsPasswordValid",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = (bool)methodInfo.Invoke(service, new object[] { inputPassword, storedPassword });

            // Assert
            Assert.False(result);
        }


    }
}
