﻿namespace ContinuityPatrol.Application.Features.MonitorService.Queries.GetNameUnique;

public class GetMonitorServiceNameUniqueQuery : IRequest<bool>
{
    public string Id { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ServicePath { get; set; }
    public string Type { get; set; }
    public string ThreadType { get; set; }

    public string WorkflowType { get; set; }
}