﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.Mappings;

internal class DatalagImpactAvailabilityViewProfile: Profile
{
    public DatalagImpactAvailabilityViewProfile()
    {
        CreateMap<DatalagImpactAvailabilityView, ImpactAvailabilityDetailVm>()
            .ForMember(x => x.BusinessServiceId, y => y.MapFrom(src => src.ReferenceId)); 
    }
}
