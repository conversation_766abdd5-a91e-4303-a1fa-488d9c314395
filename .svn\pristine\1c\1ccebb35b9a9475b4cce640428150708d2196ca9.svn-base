namespace ContinuityPatrol.Application.Contexts;

public interface ICyberDbContext
{
    #region ContinuityPatrol

    public DbSet<CyberAirGap> CyberAirGaps { get; set; }
    public DbSet<CyberSnaps> CyberSnaps { get; set; }
    public DbSet<CyberAlert> CyberAlerts { get; set; }
    public DbSet<CyberAirGapLog> CyberAirGapLogs { get; set; }
    public DbSet<CyberAirGapStatus> CyberAirGapStatus { get; set; }
    public DbSet<CyberJobManagement> CyberJobManagements { get; set; }
    public DbSet<CyberComponentGroup> CyberComponentGroups { get; set; }
    public DbSet<CyberComponent> CyberComponents { get; set; }
    public DbSet<CyberMappingHistory> CyberMappingHistory { get; set; }
    public DbSet<CyberComponentMapping> CyberComponentMappings { get; set; }

    #endregion
}