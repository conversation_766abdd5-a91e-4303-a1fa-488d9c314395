﻿using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class BusinessFunctionFilterSpecification : Specification<BusinessFunction>
{
    public BusinessFunctionFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessServiceName=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BusinessServiceName.Contains(stringItem.Replace("businessServiceName=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("criticalitylevel=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.CriticalityLevel.Contains(stringItem.Replace("criticalitylevel=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                //else if (stringItem.Contains("description=", StringComparison.InvariantCultureIgnoreCase))
                //    Or(p => p.Description.Contains(stringItem.Replace("description=", "",
                //        StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                var criticalityValue = MapSearchStringToDatabaseValue(searchString);
                Criteria = p =>
                    p.Name.Contains(searchString) || p.BusinessServiceName.Contains(searchString) ||
                    p.CriticalityLevel == criticalityValue;
            }
        }
    }

    public string MapSearchStringToDatabaseValue(string searchString)
    {
        var enumMappings = new[]
        {
            new { Key = "high", Value = ((int)CriticalityLevel.High).ToString() },
            new { Key = "medium", Value = ((int)CriticalityLevel.Medium).ToString() },
            new { Key = "low", Value = ((int)CriticalityLevel.Low).ToString() }
        };

        var lowerSearchString = searchString.ToLower();

        var matchingMapping = enumMappings
            .FirstOrDefault(mapping => mapping.Key.Contains(lowerSearchString));

        return matchingMapping?.Value;
    }
}