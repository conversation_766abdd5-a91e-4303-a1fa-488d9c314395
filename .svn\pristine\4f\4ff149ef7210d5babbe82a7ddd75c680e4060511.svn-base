﻿const serverURL = {
    createOrUpdate: "Configuration/Server/CreateOrUpdate",
    delete: "Configuration/Server/Delete",
    getBusinessServiceNames: 'Configuration/OperationalService/GetBusinessServiceNames',
    getByReferenceId: "Configuration/Server/GetByReferenceId",
    getFormMappingListByName: 'Admin/FormMapping/GetFormMappingListByName',
    getLicensesNamesWithCount: 'Admin/LicenseManager/GetLicensesNamesWithCount',
    getPagination: "/Configuration/Server/GetPagination",
    getServerListData: "Configuration/Server/GetServerListData",
    getServerNamesForSaveAs: 'Configuration/Server/GetServerNamesForSaveAs',
    getServerRole: 'Configuration/Server/GetServerRole',
    getServerType: 'Configuration/Server/GetServerType',
    getSiteNames: 'Configuration/Server/GetSiteNames',
    isServerNameExist: "Configuration/Server/IsServerNameExist",
    saveAllServer: "Configuration/Server/SaveAllServer",
    saveAsServer: "Configuration/Server/SaveAsServer",
    serverTestConnection: 'Configuration/Server/ServerTestConnection'
};

let btnDisableServer = false;
let clonedServerLists = {
    "ServerId": "",
    "ServerList": [
    ]
};
let clonedserverRowData = "";
let cloneServerSlNo = 0;
let createPermission = $("#configurationCreate").data("create-permission")?.toLowerCase();
let dataTable = "";
let deleteCloneServerRow = "";
let deletePermission = $("#configurationDelete").data("delete-permission")?.toLowerCase();
let flagEdit = true;
let forOsTypeName = [];
let getserver = '';
let isEdit = false;
let isSession = false;
let licenseIdForCountError = "";
let osLists = "";
let OSTypeAfterEditClicked = "";
let reportSearchStr = "";
let selectedValues = [];
let serverList = "";
let serverProps = "";
let siteCategory = "";
let this1 = "";
let typeIcon = "";

//resetVars in funtions.js
let deploymentsTable = true;
let enableDeploymentsValidation = false;
let enableValidation = false;
let substitueAuthType = true;

//let previousVersion = "";
let checkedTestConnection = [];

$(async function () {
    infraPreventSpecialKeys('#search-inp, #ServerName'); //commonfunctions.js
    $("#testConnectionBtn").addClass("d-none");

    let OsTypeNameLists = await fetchDataServer('Admin/ComponentType/GetComponentTypeListByName', 'server'); //used in dataTable

    if (Array.isArray(OsTypeNameLists) && OsTypeNameLists.length) {
        forOsTypeName = OsTypeNameLists.map((d) => {
            d.properties = JSON.parse(d.properties)
            return d;
        });
    }

    dataTable = $('#datatablelist').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": serverURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "servertype" : sortIndex === 3 ? "ipaddress" :
                        sortIndex === 4 ? "hostName" : sortIndex === 5 ? "ostype" : sortIndex === 6 ? "status" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#search-in-type').val();

                    if (getserver?.length > 0) {
                        selectedType = getserver;
                    } else if (selectedType === "All") {
                        selectedType = "";
                    }
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.OSTypeId = selectedType;
                    selectedValues.length = 0;
                    reportSearchStr = d?.searchString;
                },
                "dataSrc": function (json) {

                    if (json?.success) {
                        const { data } = json;
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        const isEmpty = data?.data?.length === 0;
                        $(".pagination-column").toggleClass("disabled", isEmpty);
                        $("#TestConnectionAll").prop("disabled", isEmpty);
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                    "orderable": false
                },
                {
                    "data": "name", "name": "Server Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>${data || "NA"}</span>` : data;
                    }
                },
                {
                    "data": "serverType", "name": "Server Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        const serverType = row?.serverType || 'NA';
                        const lowerServerType = serverType.toLowerCase();
                        const iconClass = lowerServerType.includes("dr") && !lowerServerType.includes("near")
                            ? "cp-virtual-drsite me-1"
                            : lowerServerType.includes("pr") ? "cp-virtual-prsite me-1" : "cp-virtual-neardrsite me-1";
                        return type === 'display'
                            ? `<span title='${serverType}'> <i class='${iconClass}'></i> ${serverType} </span>`
                            : data;
                    }
                },
                {
                    "data": "properties", "name": "IP Address", "autoWidth": true,
                    "render": function (data, type, row) {
                        const parsedData = JSON.parse(data);
                        const ipAddress = parsedData?.IpAddress || parsedData?.ipAddress || "NA";
                        return type === 'display' ? `<span title='${ipAddress}'> ${ipAddress} </span>` : data;
                    }
                },
                {
                    "data": "properties", "name": "Host Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        const parsedData = JSON.parse(data);
                        const hostName = parsedData?.HostName || parsedData?.hostName || "NA";
                        return type === 'display' ? `<span title='${hostName}'>${hostName}</span>` : data;
                    }
                },
                {
                    "data": "osType", "name": "OS Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        const editedData = data ?? "NA";
                        if (type === 'display' && forOsTypeName?.length) {
                            const filter = forOsTypeName
                                .filter(item => item.properties.name === data)
                                .map(item => item.properties.name)[0] ?? '';
                            const iconClass = filter.toLocaleLowerCase().includes("windows") ? "cp-windows me-1" :
                                filter.toLocaleLowerCase().includes("linux") ? "cp-linux me-1" :
                                    "cp-os-type me-1";
                            return `<span title="${editedData}"> <i class="${iconClass}"></i> ${editedData} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "status", "name": "status", "autoWidth": true,
                    render: function (data, type, row) {
                        if (type === 'display') {
                            const statusForError = data ? data.toLowerCase() : "NA";
                            const iconClass =
                                statusForError === "up" ? "text-success" :
                                    statusForError === "pending" ? "text-warning" :
                                        statusForError === "down" ? "text-danger" :
                                            statusForError;
                            const tooltipText =
                                statusForError === "up" ? "Up" :
                                    statusForError === "pending" ? "Pending" :
                                        statusForError === "down" ? "Down" :
                                            statusForError;
                            const iconType = statusForError === "pending" ? "cp-pending" : `cp-${statusForError}-linearrow`;
                            return `
                                    <span title="${tooltipText}">
                                        <i class="text-primary ${iconType} me-1 ${iconClass}"></i>${tooltipText}
                                    </span>`;
                        }
                        return data;
                    },
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        let exceptionMessage = row?.exceptionMessage || "NA";
                        let rowId = row?.id || "NA";
                        let isAttached = row?.isAttached || "NA";
                        let errorHTML = `<span title="Error Message" role="button" id="server-exception-Button" data-server="${exceptionMessage}"
                                            data-bs-toggle="modal" data-bs-target="#ErrorModal">
                                        <i class="cp-fail-back blink text-danger"></i>
                                    </span>`
                        let serverAttachedToDB = `
                                    <span role="button" >
                                         <i class="cp-server" title="Not attached to the database"></i>
                                    </span>
                        `;
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" >
                                        <i class="cp-test-connection TestConnection" id='TestConnection' test-status="off" title="Test Connection" data-server-id="${rowId}"></i>
                                    </span>
                                    <span role="button" title="Edit"  class="edit-button" data-server='${rowId}'>
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete"  class="delete-button" data-server-id="${rowId}" data-server-name="${row?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>                                   
                                    ${(row?.status?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}
                                    ${row?.roleType?.toLowerCase() === "database" ? (isAttached ? "" : serverAttachedToDB) : ""}
                                </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" >
                                        <i class="cp-test-connection TestConnection" id='TestConnection' test-status="off" title="Test Connection" data-server-id="${rowId}"></i>
                                    </span>
                                    <span role="button" title="Edit"  class="edit-button" data-server='${rowId}'>
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete"  class="icon-disabled">
                                        <i class="cp-Delete"></i>
                                    </span>                                   
                                        ${(row?.status?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}     
                                        ${row?.roleType?.toLowerCase() === "database" ? (isAttached ? "" : serverAttachedToDB) : ""}
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                                <div class="d-flex align-items-center gap-2">
                                   <span role="button"  class="icon-disabled">
                                      <i class="cp-test-connection" title="Test Connection"></i>
                                   </span>
                                   <span role="button" title="Edit"  class="icon-disabled">
                                      <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete"  class="delete-button" data-server-id="${rowId}" data-server-name="${row?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                      <i class="cp-Delete"></i>
                                    </span>                                   
                                    ${(row?.status?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}    
                                    ${row?.roleType?.toLowerCase() === "database" ? (isAttached ? "" : serverAttachedToDB) : ""}
                                </div>`;
                        }
                        else {
                            return `
                                <div class="d-flex align-items-center gap-2">
                                    <span role="button" class="icon-disabled">
                                        <i class="cp-test-connection" title="Test Connection"></i>
                                    </span>
                                    <span role="button" title="Edit" class="icon-disabled">
                                        <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" title="Delete" class="icon-disabled">
                                        <i class="cp-Delete"></i>
                                    </span>                                   
                                      ${(row?.status?.toLowerCase() === "down" && exceptionMessage) ? errorHTML : ""}  
                                      ${row?.roleType?.toLowerCase() === "database" ? (isAttached ? "" : serverAttachedToDB) : ""}
                                </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        }
    );

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
        $('[data-bs-toggle="tooltip"]').each(function () {
            new bootstrap.Tooltip(this);
        });
    });

    $('#search-in-type').on('change', function () {
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    });

    $('#search-inp').on('input', commonDebounce(async function (e) {
        let sanitizedValue = $(this).val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');

        if (sanitizedValue.trim() === "") {
            $(this).val("");
            sanitizedValue = "";
        } else {
            $(this).val(sanitizedValue);
        }
        ["Name", "OSType", "ServerType", "IPAddress", "HostName", "Status"].forEach(id => {

            if ($(`#${id}`).is(':checked')) {
                selectedValues.push($(`#${id}`).val() + sanitizedValue);
            }
        });

        dataTable.ajax.reload(function (json) {
            let $dataTablesEmpty = $('.dataTables_empty');

            if (sanitizedValue.length === 0 && json?.data?.data?.length === 0) {
                $dataTablesEmpty.text('No Data Found');
            } else if (json?.recordsFiltered === 0) {
                $dataTablesEmpty.text('No matching records found');
            }
        })

        $('#TestConnectionAll').attr('test-status', "off");
        $(".TestConnection").attr('test-status', "off");
    }));

    $("#nextButton").on("click", async function () {
        const validations = [
            await InfraNameValidation($("#ServerName").val(), $('#serverId').val(), serverURL.isServerNameExist,
                $("#Name-error"), "Enter server name", 'Special characters not allowed', 'ServerName'),
            commonInputValidation($("#siteNames").val(), " Select site name", "SiteName-error"),
            commonInputValidation($("#businessServiceID").val(), " Select operational service", "BusinessServiceIdError"),
            commonInputValidation($("#serverRole").val(), " Select server role", "RoleType-error"),
            commonInputValidation($("#serverType").val(), " Select server type", "ServerType-error"),
            commonInputValidation($("#osType").val(), " Select OS type", "OS-error"),
            commonInputValidation($("#license").val(), " Select license key", "Licensekey-error"),
            commonInputValidation($(".infraComponentsVersion").val(), " Select version", "Version-error"),
            licenseCountValidation()
        ];

        if (validations.every(validation => validation)) {
            form.steps('next');
        }
    });

    $("#createBtn").toggleClass('btn-disabled', createPermission === 'false')
        .css('pointer-events', createPermission === 'false' ? 'none' : '');

    $("#license").on("change", function () {
        let $license = $("#license :selected");
        $('#server_LicenseId').val($license.attr('licenseId'));
        let count = $license.attr('remainingcount');

        if (count) {
            $("#information").html(`<i class="cp-note me-1 fs-8"></i><span>Remaining count ${count} (${$('#serverRole').val()})</span>`);
        }
        commonInputValidation($license.val(), " Select license key", "Licensekey-error");
        licenseCountValidation();
    });

    $("#siteNames").on("change", function () {
        const $selectedOption = $(this).find(":selected");
        const selectedText = $selectedOption.text();
        siteCategory = $selectedOption.attr("siteCategory");
        const selectedRoleId = $("#serverRole").find('option:selected').attr('roleid');
        $("#names").val(selectedText);

        if (selectedRoleId) {
            getServerType(selectedRoleId, $('#serverType'));
        }
        commonInputValidation($(this).val(), " Select site name", "SiteName-error");
        getLicenseWithCount();
    });

    $("#businessServiceID").on("change", function () {
        $("#businessServiceName").val($('#businessServiceID option:selected').attr('businessServiceName'));
        commonInputValidation($("#businessServiceID").val(), " Select operational service", "BusinessServiceIdError");
    });

    $("#serverRole").on("change", function () {
        const $selectedOption = $(this).find('option:selected');
        const selectedRoleId = $selectedOption.attr('roleid');
        $('#server_RoleTypeId').val(selectedRoleId);
        getServerType(selectedRoleId, $('#serverType'));
        commonInputValidation($(this).val(), " Select server role", "RoleType-error");
        getLicenseWithCount();
    });

    $("#serverType").on("change", function () {
        const $selectedOption = $(this).find(":selected");
        const getId = $selectedOption.attr('serverTypeId');
        $('#server_ServerTypeId').val(getId);
        commonInputValidation($(this).val(), " Select server type", "ServerType-error");
    });

    $("#osType").on("change", function () {
        const $selectedOption = $('#osType option:selected');
        const attrValue = $selectedOption.attr('itemName');
        const attrValue3 = $selectedOption.attr('icon');
        const iconClass = (attrValue3 && attrValue3 !== "undefined" && attrValue3 !== "d-flex") ? attrValue3 : "cp-os-type";
        typeIcon = iconClass;
        $('#serverTypeTitleIcon').removeClass().addClass(iconClass);
        $('#serverTypeName').attr('title', attrValue).text(attrValue);
        commonInputValidation($(this).val(), " Select OS type", "OS-error");
    });

    $("#version").on("change", function () {
        commonInputValidation($(".infraComponentsVersion").val(), " Select version", "Version-error");
    });

    (function () {
        let sessionData = sessionStorage.getItem('serverDataFromITView');

        if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
            getserver = sessionData;
            isSession = true;
        }
    })();

    clearSessionData();

    $("#btnServerRefresh").on("click", commonDebounce(function () {
        dataTable.ajax.reload();
        testConnectionStatuson($("#testConnectionBtn"), $('#TestConnectionAll'), $('.TestConnection'));
    }));

    //test-connection
    $(document).on('click', '.TestConnection', async function () {
        let id = $(this).data("server-id");
        let status = $(this).attr("test-status");
        let $testConnectionAll = $('#TestConnectionAll');

        if (status == "off") {
            $("#testConnectionBtn").removeClass("d-none");
            checkedTestConnection.push(id);
            $(this).attr("test-status", "on");
            $(this).addClass('text-success');
        } else {
            checkedTestConnection = checkedTestConnection.filter(item => item !== id);
            $("#testConnectionBtn").addClass("d-none");
            $(this).attr("test-status", "off")
            $(this).removeClass('text-success');
            $testConnectionAll
                .removeClass("btn btn-primary")
                .addClass("bg-white btn-outline-secondary")
                .attr("test-status", "off");
        }
        let count = $(".TestConnection[test-status='on']").length;
        const table = document.getElementById('datatablelist');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        const rowCount = rows.length;

        if (rowCount === count) {
            $testConnectionAll
                .removeClass("bg-white btn-outline-secondary")
                .addClass("btn btn-primary");
        }
        let anyChecked = $(".TestConnection[test-status='on']").length > 0;

        if (!anyChecked) {
            $("#testConnectionBtn").addClass("d-none");
        } else {
            $("#testConnectionBtn").removeClass("d-none");
        }
    });

    $("#testConnectionBtn").on("click", async function () {
        $(this).prop("disabled", true);
        let testConnectionIDs = {
            'id': checkedTestConnection,
            __RequestVerificationToken: gettoken()
        }
        let serverTestConnection = await infraPostRequest(RootUrl + serverURL.serverTestConnection, testConnectionIDs);  //CommonFunctions.js
        $(this).prop("disabled", false);
        testConnectionBtnOnClick($("#testConnectionBtn"), $('#TestConnectionAll'), $(".TestConnection"));
        checkedTestConnection = [];

        if (serverTestConnection) {
            dataTable.ajax.reload();
            notificationAlert("success", serverTestConnection?.data?.message);
        }
    });

    $('#TestConnectionAll').on("click", function () {
        let status = $(this).attr("test-status");
        checkedTestConnection = [];
        let $testConnectionButton = $("#testConnectionBtn");
        let $testConnectionAll = $('#TestConnectionAll');
        let $testConnection = $(".TestConnection");

        if (status == "off") {
            testConnectionStatusOff($testConnectionButton, $testConnectionAll, $testConnection)
        } else {
            testConnectionStatuson($testConnectionButton, $testConnectionAll, $testConnection);
        }
    });

    $('#datatablelist').on('click', '.edit-button', async function () {
        let serverID = $(this).data('server');
        clearErrorMessage();
        form.steps('previous');
        isEdit = true;
        $('#saveButton').text('Update');
        $('#CreateModal').modal('show');
        let result = await infraGetRequestWithData(RootUrl + serverURL.getByReferenceId, { id: serverID }) //CommonFunctions.js 

        if (result && typeof result === 'object' && Object.keys(result).length > 0) {
            populateModalFields(result);
        }
    });

    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No results found";
            }
        },
    });

    $('#datatablelist').on('click', '.delete-button', function () {
        const serverName = $(this).data('server-name');
        const serverId = $(this).data('server-id');
        $('#deleteData').attr('title', serverName).text(serverName);
        $('#textDeleteId').val(serverId);
    });

    $("#confirmDeleteButton").on("click", async function () {
        const form = $('#serverDelete')[0];
        const formData = new FormData(form);

        if (!btnDisableServer) {
            btnDisableServer = true;
            showLoaderAndDisableButton($('#loginLoader'), $(this));
            let response = await infraDeleteData(RootUrl + serverURL.delete, formData); //commonfunctions.js
            $("#DeleteModal").modal("hide");
            btnDisableServer = false;
            if (response?.success) {
                notificationAlert("success", response?.data?.message);
                setTimeout(() => {
                    dataTableDelete(dataTable);
                    resetVars();
                }, 2000)
            } else {
                errorNotification(response);
            }
            hideLoaderAndEnableButton($('#loginLoader'), $(this));
        }
    });

    $('#datatablelist').on('click', '#server-exception-Button', function () {
        let serverData = $(this).data('server');
        let text = serverData ? serverData.split("$") : null;
        let html = "";
        if (Array.isArray(text)) {
            text.forEach((data, index) => {
                let modifiedData = data.replace(/(Node Name :|Error Message :)/g, '<strong>$1</strong>');
                html += `<div>${modifiedData}</div>`
                if ((index + 1) % 2 === 0) {
                    html += `<br>`;
                }
            });
        } else {
            html = "NA";
        }
        $('#serverException').html(html);
    });

    $("#createBtn").on("click", function () {
        $("#information").html("")
        OSTypeAfterEditClicked = "";
        isEdit = false;
        siteCategory = "";
        licenseIdForCountError = "";
        $("#license").empty();
        clearErrorMessage();
        $('#serverTypeTitleIcon').attr('class', 'cp-os-type');
        $("#formRenderingArea, #serverType, #version").empty();
        $("#serverId, #BusinessServiceId, #serverFormVersion").val('');
        $('#serverTypeName').attr('title', 'Server').text('Server');
        $('#saveButton').prop('disabled', false).text('Save');
        form.steps('previous');
    });

    $('#ServerName').on('keyup', commonDebounce(function () {
        let $serverName = $("#ServerName");
        $serverName.val($serverName.val().replace(/\s{2,}/g, ' '));

        //CommonFunctions.js InfraNameValidation
        InfraNameValidation($serverName.val(), $('#serverId').val(), serverURL.isServerNameExist,
            $("#Name-error"), "Enter server name", 'Special characters not allowed', 'ServerName');
    }));

    $('#osType').on("change", function () {
        if ($(this).find(":selected").text() !== "") {
            let $version = $('#version');
            let $osType = $('#osType :selected');
            let options = [];
            const attrValue = $osType.attr('itemName');
            const ostype = $osType.text();
            $("#ostypeid").val(attrValue)
            $version.empty().append($('<option>').val("").text("Select Version"));

            if (OSTypeAfterEditClicked) {
                if (OSTypeAfterEditClicked !== ostype) {
                    isEdit = false;
                }
            }
            osLists?.forEach(function (item) {
                const osType = $('#osType').val();

                if (osType === item.formTypeId) {
                    const versions = JSON.parse(item.version);
                    versions?.Version?.forEach(versionData => {
                        options.push($('<option>').val(versionData).text(versionData));
                    });
                    $version.append(options)
                }
            });
        }
    });

    $('#version').on("change", async function () {
        $("#serverLogo").val($('#osType :selected').attr('icon'));
        let data = { "formTypeId": $("#osType").val(), "version": $(this).val() };
        let getFormMapping = await infraGetRequestWithData(RootUrl + "Admin/FormMapping/GetFormMappingByFormId", data);
        if (getFormMapping && typeof getFormMapping === 'object' && Object.keys(getFormMapping).length > 0) {
            nextButtonStyle('', '');
            populateFormModal(getFormMapping);
            //if ($("#serverFormVersion").val() === "" || $("#serverFormVersion").val() === undefined) {
            //    $("#serverFormVersion").val(data.formVersion);
            //    populateFormModal(data);
            //} else if ($("#serverFormVersion").val() === data.formVersion) {
            //    populateFormModal(data);
            //} else {
            //    $("#serverFormVersion").val(data.formVersion);
            //    $("#serverVersionName").text($('#ServerName').val())
            //    $("#newVersion").text(data.formVersion);
            //    populateFormModal(data);
            //    $('#RestoreModal').modal('show');
            //    nextButtonStyle('0.5', 'none');
            //}
        } else {
            nextButtonStyle('0.5', 'none');
        }
    });

    //$("#cancelFormRestore").on("click", function () {
    //    $('#formRenderingArea').empty();
    //    $('#RestoreModal').modal('hide');
    //});

    //$("#confirmFormRestore").on("click", async function () {
    //    let serverData = {
    //        Id: $('#serverId').val(),
    //        OldFormVersion: previousVersion,
    //        NewFormVersion: $("#serverFormVersion").val(),
    //        IsUpdateAll: false,
    //        __RequestVerificationToken: gettoken()
    //    }
    //    await updateServerFormVersion(serverData);
    //    nextButtonStyle('', '');
    //    $('#RestoreModal').modal('hide');
    //});

    //$("#confirmFormRestoreAll").on("click", async function () {
    //    let serverData = {
    //        OsTypeId: $('#osType').val(),
    //        OldFormVersion: previousVersion,
    //        NewFormVersion: $("#serverFormVersion").val(),
    //        IsUpdateAll: true,
    //        __RequestVerificationToken: gettoken()
    //    };
    //    await updateServerFormVersion(serverData);
    //    nextButtonStyle('', '');
    //    $('#RestoreModal').modal('hide');
    //});

    $(".btn-cancel, .btn-close").on("click", function () {
        OSTypeAfterEditClicked = "";
    });

    $('.prev_btn').on('click', function () {
        const removeElements = (selector) => {
            document.querySelectorAll(selector)?.forEach(element => element.remove());
        };
        removeElements('.dynamic-select-tag');
        const inputValues = $(".formeo-render .f-field-group input[type='text']:visible, .formeo-render .f-field-group input[type='number']:visible, .formeo-render .f-field-group input[type='password']:visible");

        if (inputValues?.length) {
            inputValues?.each(function () {
                let $this = $(this);
                let res = $this.val();
                if (!res) {
                    $this.siblings('.dynamic-input-field').remove();
                }
            })
        }
    });

    $("#saveButton").on("click", async function () {
        substitueAuthType = true;
        deploymentsTable = true;
        let res = await inputFormValidation('server');
        $('.substituteAuthenticationType, .substituteUserName').each(function () {
            const $this = $(this);
            const isVisible = $this.is(":visible");

            if (isVisible) {
                enableValidation = true;
            } else {
                enableValidation = false;
            }
        });

        if (enableValidation) {
            let type = await SubstituteAuthenticationType();
            let user = await SubstituteAuthenticationUser();
            if (type && user) {
                substitueAuthType = true;
            } else {
                substitueAuthType = false;
            }
        }
        let getDeploymentsTable = $("table[name='Deployments Table']");

        if (getDeploymentsTable?.length > 0) {
            getDeploymentsTable?.map(async function () {
                let $this = $(this);
                if ($this.is(':visible')) {
                    enableDeploymentsValidation = true;
                } else {
                    $(".deploymentsReplicaName").val("");
                    $(".deploymentsName").val("");
                }
            });
        }

        if (enableDeploymentsValidation) {
            let replicaName = await deploymentsReplicaSetName("save");
            let name = await deploymentsName("save");
            const rowCount = $("table[name='Deployments Table'] tr").length;
            if (replicaName && name) {
                deploymentsTable = true;
            } else {
                deploymentsTable = false;
            }
        }
        setTimeout(async () => {

            if (substitueAuthType && res && deploymentsTable) {
                let fd = await serverSaveFormFields();
                fd["icon"] = typeIcon;
                const keys = Object.keys(fd);
                keys.forEach(key => {
                    if (key.startsWith('f-')) {
                        delete fd[key];
                    }
                });
                $("#serverStatus").val("Pending"); //after save or update it should be in pending state.
                let hiddenInput = document.getElementById('Props');
                let encryption = await propertyEncryption(fd);
                hiddenInput.value = encryption;

                if (!btnDisableServer) {
                    btnDisableServer = true;
                    $('#formRenderingArea :input').prop('disabled', true);
                    const form = $('#server-form')[0];
                    const formData = new FormData(form);
                    $('#formRenderingArea :input').prop('disabled', false);

                    let response = await infraCreateOrUpdate(RootUrl + serverURL.createOrUpdate, formData);
                    $('.serverModal').modal('hide');
                    btnDisableServer = false;

                    if (response?.success) {
                        notificationAlert("success", response?.data?.message);
                        serverOSType();
                        setTimeout(() => {
                            let selectedOSType = $("#osType :selected").val();
                            dataTableCreateAndUpdate($("#saveButton"), dataTable, $('#search-in-type'), selectedOSType);
                            resetVars();
                        }, 2000)
                    } else {
                        errorNotification(response);
                    }
                }
            }
        }, 50)
    });

    $('.serverModal').on('shown.bs.modal', function async() {
        this1 = $(this)
    });

    let serverRoleData = await fetchDataServer(serverURL.getServerRole);

    if (Array.isArray(serverRoleData) && serverRoleData.length > 0) {
        let options = [];
        const sortedData = serverRoleData.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        let serverRole = $('#serverRole');
        serverRole.empty().append($('<option>').val("").text("Select Server Role"));
        sortedData.forEach(function (item) {
            options.push($('<option>').val(item.name).text(item.name).attr('roleid', item.id));
        });
        serverRole.append(options)
    }

    let businessServiceData = await fetchDataServer(serverURL.getBusinessServiceNames);

    if (Array.isArray(businessServiceData) && businessServiceData.length > 0) {
        let options = [];
        const sortedData = businessServiceData.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        let businessService = $('#businessServiceID');
        businessService.empty().append($('<option>').val("").text("Select Operational Service"));
        sortedData.forEach(function (item) {
            options.push($('<option>').val(item.id).text(item.name).attr('businessServiceName', item.name));
        });
        businessService.append(options);
    }

    let siteNameLists = await fetchDataServer(serverURL.getSiteNames);

    if (Array.isArray(siteNameLists) && siteNameLists.length > 0) {
        let options = [];
        const sortedData = siteNameLists.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
        let siteNames = $('#siteNames');
        siteNames.empty().append($('<option>').val('').text('Select Site Name'));
        sortedData.forEach(function (item) {
            options.push($('<option>').val(item.id).text(item.name).attr('siteCategory', item.category));
        });
        siteNames.append(options);
    }

    let serverOSTypeLists = await fetchDataServer(serverURL.getFormMappingListByName, 'server');

    if (Array.isArray(serverOSTypeLists) && serverOSTypeLists.length > 0) {
        let options = [];
        let OSType = $('#osType');
        osLists = serverOSTypeLists
        OSType.empty().append($('<option>').val("").text("Select OS type"));
        serverOSTypeLists.forEach(function (item) {
            options.push($('<option>').val(item.formTypeId).text(item.formTypeName).attr('itemName', item.formTypeName).attr("icon", item.logo));
        });
        OSType.append(options);
    }
    serverOSType();
});

$('#ReportType').on('change', async function () {
    $('#ReportType_Error').text('').removeClass('field-validation-error');
});

$('#BtnServerDownload').on('click', async function () {
    try {
        let templateType = $("#ReportType option:selected").val();
        const errorElement = $('#ReportType_Error');

        if (!templateType) {
            errorElement.text("Select Report Type").addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
        }
        $('#BtnServerDownload').addClass("disabled");
        $('#ReportType').addClass("disabled");
        var selectedTypeId = $('#search-in-type').val();
        var authentication = $("#SubAuthentication").prop("checked");
        var isAuthenticate = authentication ? "true" : "false";
        const url = `/Configuration/Server/LoadReport?type=${templateType}&selectedTypeId=${selectedTypeId}&searchString=${reportSearchStr}`;
        const response = await fetch(url);
        if (response.ok) {
            const blob = await response.blob();
            var alertClass, iconClass, message;
            if (blob.size > 0) {
                const DateTime = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
                if (templateType == "pdf") downloadServer(blob, "ServerComponent_" + DateTime + ".pdf", "application/pdf");
                else { downloadServer(blob, "ServerComponent_" + DateTime + ".xls", "application/vnd.ms-excel"); }
                alertClass = "success-toast";
                iconClass = "cp-check toast_icon";
                message = "ServerComponent report downloaded successfully";
            }
            else {
                alertClass = "warning-toast";
                iconClass = "cp-exclamation toast_icon";
                message = "ServerComponentReport Download Error";
            }
        }
        else {
            alertClass = "warning-toast";
            iconClass = "cp-exclamation toast_icon";
            message = "ServerComponentReport Download Error";
        }
        $('#alertClass').removeClass().addClass(alertClass);
        $('#icon').removeClass().addClass(iconClass);
        $('#notificationAlertmessage').text(message);
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
        $('#BtnServerDownload').removeClass("disabled");
    }
    catch (error) {
        $('#BtnServerDownload').removeClass("disabled");
        notificationAlert("An error occurred:", error.message);
    }
    $("#ReportType").val("").trigger("change");
});

$(document).on("change input", ".substituteAuthenticationType, .substituteUserName, .deploymentsReplicaName, .deploymentsName", function () {
    const $this = $(this);
    const value = $this.val();

    if (value.length === 0) {
        switch (true) {
            case $this.hasClass("substituteAuthenticationType"):
            case $this.hasClass("substituteUserName"):
                enableValidation = true;
                break;
            case $this.hasClass("deploymentsReplicaName"):
            case $this.hasClass("deploymentsName"):
                enableDeploymentsValidation = true;
                break;
        }
    }
});

// Initialize tooltips when the page is loaded
$('[data-bs-toggle="tooltip"]').each(function () {
    new bootstrap.Tooltip(this);
});
