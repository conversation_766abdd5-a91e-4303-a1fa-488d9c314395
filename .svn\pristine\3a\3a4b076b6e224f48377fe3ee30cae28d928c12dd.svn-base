﻿using ContinuityPatrol.Application.Features.ComponentType.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Queries;

public class GetComponentTypeListQueryHandlerTests : IClassFixture<ComponentTypeFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;

    private Mock<IComponentTypeRepository> _mockComponentTypeRepository;

    private readonly GetComponentTypeListQueryHandler _handler;

    public GetComponentTypeListQueryHandlerTests(ComponentTypeFixture componentTypeFixture)
    {
        _componentTypeFixture = componentTypeFixture;

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.GetComponentTypeRepository(_componentTypeFixture.ComponentTypes);

        _handler = new GetComponentTypeListQueryHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_ComponentTypesCount()
    {
        var result = await _handler.Handle(new GetComponentTypeListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<ComponentTypeListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_ComponentTypesList()
    {
        var result = await _handler.Handle(new GetComponentTypeListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<ComponentTypeListVm>>();

        result[0].Id.ShouldBe(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        result[0].ComponentName.ShouldBe(_componentTypeFixture.ComponentTypes[0].ComponentName);

        result[0].Properties.ShouldBe(_componentTypeFixture.ComponentTypes[0].Properties);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.GetComponentTypeEmptyRepository();

        var handler = new GetComponentTypeListQueryHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object);

        var result = await handler.Handle(new GetComponentTypeListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetComponentTypeListQuery(), CancellationToken.None);

        _mockComponentTypeRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}
