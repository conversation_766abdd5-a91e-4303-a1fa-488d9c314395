﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessServiceEvaluationRepository : BaseRepository<BusinessServiceEvaluation>,
    IBusinessServiceEvaluationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public BusinessServiceEvaluationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<BusinessServiceEvaluation>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService => businessService.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices);
    }

    public async Task<BusinessServiceEvaluation> GetBusinessServiceEvaluationByReferenceIdAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ReferenceId", "ReferenceId cannot be invalid");

        var assignedInfra = new AssignedEntity();

        if (!_loggedInUserService.IsAllInfra)
            assignedInfra = JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras);

        var bServiceEvaluation = await GetByReferenceIdAsync(id);
        if (bServiceEvaluation == null) throw new NotFoundException(nameof(BusinessServiceEvaluation), id);

        return _loggedInUserService.IsAllInfra
            ? bServiceEvaluation
            : assignedInfra.AssignedBusinessServices
                .Where(assignedBusinessService => bServiceEvaluation.BusinessServiceId == assignedBusinessService.Id)
                .Select(_ => bServiceEvaluation).SingleOrDefault();
    }


    public IReadOnlyList<BusinessServiceEvaluation> AssignedBusinessServices(
        IQueryable<BusinessServiceEvaluation> businessServices)
    {
        var services = new List<BusinessServiceEvaluation>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                    where businessService.BusinessServiceId == assignedBusinessService.Id
                    select businessService);
        return services;
    }
}