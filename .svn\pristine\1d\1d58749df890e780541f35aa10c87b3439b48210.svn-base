﻿using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Events.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Events.Delete;
using ContinuityPatrol.Application.Features.FormTypeCategory.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class FormTypeCategoryFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<FormTypeCategory> FormTypeCategories { get; set; }

    public FormTypeCategory FormTypeCategory { get; set; }

    public List<UserActivity> UserActivities { get; set; }

    public CreateFormTypeCategoryCommand CreateFormTypeCategoryCommand { get; set; }

    public UpdateFormTypeCategoryCommand UpdateFormTypeCategoryCommand { get; set; }

    public FormTypeCategoryCreatedEvent FormTypeCategoryCreatedEvent { get; set; }

    public FormTypeCategoryDeletedEvent FormTypeCategoryDeletedEvent { get; set; }

    public FormTypeCategoryUpdatedEvent FormTypeCategoryUpdatedEvent { get; set; }


    public FormTypeCategoryFixture()
    {
        FormTypeCategories = AutoFormTypeFixture.Create<List<FormTypeCategory>>();

        FormTypeCategory = AutoFormTypeFixture.Create<FormTypeCategory>();

        UserActivities = AutoFormTypeFixture.Create<List<UserActivity>>();

        CreateFormTypeCategoryCommand = AutoFormTypeFixture.Create<CreateFormTypeCategoryCommand>();

        UpdateFormTypeCategoryCommand = AutoFormTypeFixture.Create<UpdateFormTypeCategoryCommand>();

        FormTypeCategoryCreatedEvent = AutoFormTypeFixture.Create<FormTypeCategoryCreatedEvent>();

        FormTypeCategoryDeletedEvent = AutoFormTypeFixture.Create<FormTypeCategoryDeletedEvent>();

        FormTypeCategoryUpdatedEvent = AutoFormTypeFixture.Create<FormTypeCategoryUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<FormTypeCategoryProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoFormTypeFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFormTypeCategoryCommand>(p => p.FormTypeName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFormTypeCategoryCommand>(p => p.FormTypeName, 10));
            fixture.Customize<UpdateFormTypeCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<FormType>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateFormTypeCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<FormType>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormTypeCategoryCreatedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormTypeCategoryDeletedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<FormTypeCategoryUpdatedEvent>(p => p.Name, 10));


            return fixture;
        }
    }

    public void Dispose()
    {

    }

}