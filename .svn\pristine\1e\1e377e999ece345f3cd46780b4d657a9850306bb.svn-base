using ContinuityPatrol.Application.Features.DriftEvent.Commands.Create;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Update;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftEventFixture : IDisposable
{
    public CreateDriftEventCommand CreateDriftEventCommand { get; }
    public CreateDriftEventResponse CreateDriftEventResponse { get; }
    public UpdateDriftEventCommand UpdateDriftEventCommand { get; }
    public UpdateDriftEventResponse UpdateDriftEventResponse { get; }
    public DeleteDriftEventCommand DeleteDriftEventCommand { get; }
    public DeleteDriftEventResponse DeleteDriftEventResponse { get; }
    public DriftEventDetailVm DriftEventDetailVm { get; }
    public List<DriftEventListVm> DriftEventListVm { get; }
    public GetDriftEventPaginatedListQuery GetDriftEventPaginatedListQuery { get; }
    public PaginatedResult<DriftEventListVm> DriftEventPaginatedResult { get; }
    public GetDriftEventNameUniqueQuery GetDriftEventNameUniqueQuery { get; }

    public DriftEventFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise drift event scenarios
        fixture.Customize<CreateDriftEventCommand>(c => c
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.EntityType, "Enterprise Server Configuration")
            .With(x => x.Entity, "Enterprise Database Server")
            .With(x => x.EntityName, "Enterprise Primary DB Server")
            .With(x => x.EntityStatus, "Configuration Drift Detected")
            .With(x => x.Message, "Enterprise configuration drift detected in database server settings"));

        fixture.Customize<CreateDriftEventResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Event created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDriftEventCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.EntityType, "Enterprise Updated Server Configuration")
            .With(x => x.Entity, "Enterprise Updated Database Server")
            .With(x => x.EntityName, "Enterprise Updated Primary DB Server")
            .With(x => x.EntityStatus, "Configuration Drift Resolved")
            .With(x => x.Message, "Enterprise configuration drift resolved in database server settings"));

        fixture.Customize<UpdateDriftEventResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Event updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDriftEventResponse>(c => c
            .With(x => x.IsActive, false)
            .With(x => x.Message, "Enterprise Drift Event deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DriftEventDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.EntityType, "Enterprise Detail Server Configuration")
            .With(x => x.Entity, "Enterprise Detail Database Server")
            .With(x => x.EntityName, "Enterprise Detail Primary DB Server")
            .With(x => x.EntityStatus, "Configuration Drift Monitored")
            .With(x => x.Message, "Enterprise detailed configuration drift monitoring active"));

        fixture.Customize<DriftEventListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.InfraObjectId, Guid.NewGuid().ToString())
            .With(x => x.EntityType, "Enterprise List Server Configuration")
            .With(x => x.Entity, "Enterprise List Database Server")
            .With(x => x.EntityName, "Enterprise List Primary DB Server")
            .With(x => x.EntityStatus, "Configuration Drift Listed")
            .With(x => x.Message, "Enterprise configuration drift event listed"));

        // Initialize properties
        CreateDriftEventCommand = fixture.Create<CreateDriftEventCommand>();
        CreateDriftEventResponse = fixture.Create<CreateDriftEventResponse>();
        UpdateDriftEventCommand = fixture.Create<UpdateDriftEventCommand>();
        UpdateDriftEventResponse = fixture.Create<UpdateDriftEventResponse>();
        DeleteDriftEventCommand = new DeleteDriftEventCommand { Id = Guid.NewGuid().ToString() };
        DeleteDriftEventResponse = fixture.Create<DeleteDriftEventResponse>();
        DriftEventDetailVm = fixture.Create<DriftEventDetailVm>();
        DriftEventListVm = fixture.CreateMany<DriftEventListVm>(6).ToList();
        
        GetDriftEventPaginatedListQuery = fixture.Create<GetDriftEventPaginatedListQuery>();
        DriftEventPaginatedResult = new PaginatedResult<DriftEventListVm>
        {
            Data = fixture.CreateMany<DriftEventListVm>(12).ToList(),
            TotalCount = 12,
            PageSize = 12,
            Succeeded = true
        };
        
        GetDriftEventNameUniqueQuery = new GetDriftEventNameUniqueQuery { Name = "Enterprise Primary DB Server" };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
