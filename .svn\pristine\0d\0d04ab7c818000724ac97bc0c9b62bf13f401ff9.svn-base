﻿// Variable Assignment
let workflowProfileURL = {
    nameUrl: "ITAutomation/WorkflowProfileManagement/IsWorkflowProfileNameExist",
    getBusinessFunctionUrl: "ITAutomation/WorkflowProfileManagement/GetBusinessFunctionByBusinessServiceId",
    getInfraObjectUrl: "ITAutomation/WorkflowProfileManagement/GetInfraObjectByBusinessFunctionId",
    getWorkflowUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowInfraObjectByInfraObjectId",
    getWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowProfileInfoByProfileId",
    createWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/CreateWorkflowProfileInfo",
    updateWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/UpdateWorkflowProfileInfo",
    deleteWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/DeleteWorkflowProfileInfo",
    idInfo: "ITAutomation/WorkflowProfileManagement/WorkflowProfileInfo",
    getSavefunctionUrl: "ITAutomation/WorkflowProfileManagement/CreateOrUpdate",
    getpolicyUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowProfileById",
    getDeleteUrl: "ITAutomation/WorkflowProfileManagement/Delete"
}
let createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();
let globalProfileUpdateId;
let globalProfileDeleteId;

let NoDataFoundImage = "<img src='../../img/isomatric/Workflow_Execution_No_Data_Found.svg' class='Card_NoData_ImgExe' style='margin-left:337px;margin-top:110px;'>"

// Access field
if (createPermission === 'false') {
    const buttons = $("#btnWorkflowProfileCreate, #btnWorkflowProfileEdit, #btnSaveProfile, #btnProfileChangePassword");
    buttons.addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle data-bs-target id');
}
if (deletePermission == 'false') {
    $("#btnWorkfloProfileDelete").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {    
    // Functions
    async function EditWorkflow(workflowId, workflowName) {

        let data = {
            'workflowProfileInfoId': workflowId
        }
        $.ajax({
            type: "GET",
            url: RootUrl + workflowProfileURL.idInfo,
            data: data,
            dataType: "json",
            success: async function (response) {
                if (response?.success && response?.data) {
                    let { businessServiceName, businessFunctionName, infraObjectName, workflowName } = response.data;
                    $("#btnSaveProfile").hide();
                    $("#btnWorkflowProfileEditProfile").show();
                    const Optionsvalue = async (dropdown, value) => {
                        if (dropdown.find(`option[value='${value}']`).length) {
                            dropdown.val(value).trigger('change');
                            return;
                        }
                        await new Promise(resolve => requestAnimationFrame(resolve));
                        return Optionsvalue(dropdown, value);
                    };
                    const setDropdown = async (dropdownId, value) => {const dropdown = $(dropdownId);
                        await Optionsvalue(dropdown, value);
                    };                  
                        await Promise.all([
                            setDropdown("#operationalServiceId", businessServiceName),
                            setDropdown("#operationalFunction", businessFunctionName),
                            setDropdown("#infraObject", infraObjectName),
                            setDropdown("#workflowName", workflowName)
                        ]);
                 
                } else {
                    errorNotification(workflowName);
                }
            },
        });
    }
    function validateDropDown(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    function SetDropDownValues(values, elementId) {
        if (Array.isArray(values) && values.length) {
            let dropdownOptions = `<option value=""></option>`;
            values.forEach(item => {
                dropdownOptions += `<option value="${item.name}" data-id="${item.id}">${item.name}</option>`;
            });
            $("#" + elementId).empty().append(dropdownOptions);
        }
    }
    function ClearWorkFlowErrorElements(errorElements) {
        errorElements.forEach(element => { $(element).text('').removeClass('field-validation-error');
        });
    }

    async function IsSameNameExist(url, inputValue) {
        return !inputValue.name.trim() ? true : (await getAysncWithHandler(url, inputValue, OnError)) ? " Profile name already exists" : true;
    }

    async function profileValidateName(value, id = null, IsNameUrl) {
        const errorElement = $('#ProfileNameError');
        if (!value) {
            errorElement.text('Enter profile name').addClass('field-validation-error');
            return false;
        } else {
            let url = RootUrl + IsNameUrl;
            let data = {};
            data.name = value;
            data.id = id;
            const validationResults = [
                SpecialCharValidateCustom(value),
                ShouldNotBeginWithUnderScore(value),
                ShouldNotBeginWithSpace(value),
                OnlyNumericsValidate(value),
                SpaceWithUnderScore(value),
                ShouldNotEndWithUnderScore(value),
                ShouldNotEndWithSpace(value),
                MultiUnderScoreRegex(value),
                SpaceAndUnderScoreRegex(value),
                minMaxlength(value, 200),
                secondChar(value),
                await IsSameNameExist(url, data)
            ];
            return CommonValidation(errorElement, validationResults);
        }
    }
    function sanitizeInput(value) {
        return value?.replace(/\s+/g, '');
    }

    // Events
    $(document).on("click", "#editProfileId", async function () {
        $('#btnWorkflowProfileResetProfile').show();
        const workflowButton = $(this);
        const workflowId = workflowButton.attr("workflowProInfoId");
        const workflowName = workflowButton.attr("workflowName");
        if (workflowId) {
            globalProfileUpdateId = workflowId;
            $('[id="editProfileId"]').css("visibility", "visible");
            $('[id="deleteProfileId"]').removeClass('disabled').css({ 'pointer-events': 'auto', 'opacity': '1' });
            workflowButton.css("visibility", "hidden");
            const deleteBtn = workflowButton.closest('tr').find('[id="deleteProfileId"]');
            deleteBtn.addClass('disabled').css({ 'pointer-events': 'none', 'opacity': '0.5' });
        }       
        await EditWorkflow(workflowId, workflowName);
    });

    $('#workflowtable').on('click', '#deleteProfileId', function () {
        let deleteId = $(this).attr('workflowProInfoId');
        let workflowName = $(this).attr('workflowName');
        $('#txtDeleteId').val(deleteId);
        $('#txtWorkflow').text(workflowName);
        globalProfileDeleteId = deleteId;
    });

    $("#confirmWFProfileDeleteButton").on("click", commonDebounce(async function () {
        let datas = {
            'workflowProfileInfoId': globalProfileDeleteId
        }
        await $.ajax({
            type: 'DELETE',
            url: RootUrl + workflowProfileURL.deleteWorkflowProfileUrl,
            data: datas,
            dataType: "json",
            success: function (result) {
                var data = result?.data
                if (result?.success) {
                    notificationAlert("success", data.message)
                    $('#DeleteWorkflowProfileModal').modal('hide')
                    $('#selectWorkflowprofileName').trigger('change')
                }
                else {
                    errorNotification(result)
                    $('#DeleteWorkflowProfileModal').modal('hide')
                }
            }
        })
    }, 800));

    $('#btnProfileChangePassword').on('click', function () {
        clearDataFields();
        $('#ProfileChangepasswordModal').modal('show');
    });

    $('#operationalServiceId').on('change', async function () {
        operationalServiceId = $('#operationalServiceId option:selected').data('id');
        operationalServiceName = $('#operationalServiceId option:selected').text();
        if (operationalServiceId) {
            validateDropDown(operationalServiceId, "Select operational service", $('#operationalServiceError'));
            $('#workflowName, #operationalFunction, #infraObject').empty();
            let data = { businessServiceId: operationalServiceId };
            let businessFunctions = await getAysncWithHandler(RootUrl + workflowProfileURL.getBusinessFunctionUrl, data, OnError);
            if (businessFunctions?.length > 0) {
                SetDropDownValues(businessFunctions, "operationalFunction", "Select Operational Function");
            }
        }
    });

    $('#operationalFunction').on('change', async function () {
        operationalFunctionId = $('#operationalFunction option:selected').data('id');
        operationalFunctionName = $('#operationalFunction option:selected').text();
        $("#infraObject,#workflowName").empty();
        if (operationalFunctionId) {
            let data = { 'businessFunctionId': operationalFunctionId };
            let infraObject = await getAysncWithHandler(RootUrl + workflowProfileURL.getInfraObjectUrl, data, OnError);
            validateDropDown(operationalFunctionName, "Select Operational Function", $('#operationalFunctionError'));
            if (infraObject && infraObject?.length > 0) {
                SetDropDownValues(infraObject, "infraObject", "Select InfraObject");
            }
        }
    });

    $('#infraObject').on('change', async function () {
        infraObjectId = $('#infraObject option:selected').data('id');
        infraObjectName = $('#infraObject').val();
        $("#workflowName").empty();
        if (infraObjectId) {
            let data = { 'infraObjectId': infraObjectId};
            let workflows = await getAysncWithHandler(RootUrl + workflowProfileURL.getWorkflowUrl, data, OnError);
            if (workflows?.length > 0) {
                let options = `<option value="">Select workflow</option>`; // <-- Add default unselected option
                workflows.forEach(wf => {
                    options += `<option value="${wf.workflowName}" id="${wf.workflowId}" name="${wf.actionType}">
                    ${wf.workflowName} - ${wf.actionType}</option>`;
                });
                $("#workflowName").append(options);
            }
        }        
        $('#workflowName').select2();
        validateDropDown($('#infraObject').val(), "Select infraObject", $('#infraObjectError'));
    });

    $('#workflowName').on('change', function () {
        workflowName = $('#workflowName').val();
        workflowId = $('#workflowName option:selected').attr('id');
        workflowType = $('#workflowName option:selected').attr('name');       
        validateDropDown($('#workflowName').val(), "Select workflow", $('#workflowError'));
    });

    $('#btnSaveProfile').on('click', commonDebounce(async function (e) {
        if ($(this).prop('disabled')) {
            e.preventDefault();
            return;
        }        
        const profileId = $('#profileId').val();
        const businessServiceId = $('#operationalServiceId option:selected').data('id');
        const businessServiceName = $('#operationalServiceId option:selected').text();
        const businessFunctionId = $('#operationalFunction option:selected').data('id');
        const businessFunctionName = $('#operationalFunction option:selected').text();
        const infraObjectId = $('#infraObject option:selected').data('id');
        const infraObjectName = $('#infraObject option:selected').text();
        const workflowId = $('#workflowName option:selected').attr('id');
        let workflowName = $('#workflowName option:selected').text().trim() === "Select workflow" ? '' : $('#workflowName option:selected').text();
        const workflowType = $('#workflowName option:selected').attr('name');
        const workflowProfileName = $('#selectWorkflowprofileName option:selected').text();
        const workflowProfileId = $('#selectWorkflowprofileName').val();

        // Validate dropdowns
        const isBusinessService = validateDropDown(businessServiceName, "Select operational service", $('#operationalServiceError'));
        const isBusinessFunction = validateDropDown(businessFunctionName, "Select operational function", $('#operationalFunctionError'));
        const isInfraObject = validateDropDown(infraObjectName, "Select infraobject", $('#infraObjectError'));
        const isWorkflow = validateDropDown(workflowName, "Select workflow", $('#workflowError'));
        const isSelectWorkflowProfile = validateDropDown(workflowProfileId, "Select workflow profile", $('#workfloProfileError'));
        if (isBusinessService && isBusinessFunction && isInfraObject && isWorkflow && isSelectWorkflowProfile) {
            const formData = {
                ProfileId: profileId,
                ProfileName: workflowProfileName,
                BusinessServiceId: businessServiceId,
                BusinessServiceName: businessServiceName,
                BusinessFunctionId: businessFunctionId,
                BusinessFunctionName: businessFunctionName,
                InfraObjectId: infraObjectId,
                InfraObjectName: infraObjectName,
                WorkflowId: workflowId,
                WorkflowName: workflowName,
                WorkflowType: workflowType,
                __RequestVerificationToken: gettoken()
            };
                const response = await $.ajax({
                    url: `${RootUrl}${workflowProfileURL.createWorkflowProfileUrl}`,
                    data: formData,
                    dataType: "json",
                    traditional: true,
                    type: 'POST'
                });

                if (response?.success && response?.data) {
                    $('#selectWorkflowprofileName').val(profileId).trigger('change');
                    notificationAlert('success', response.data.message);
                } else {
                    errorNotification(response);
                };
                clearProfileInfo();            
        }
    }, 800));

    $('#btnWorkflowProfileEditProfile').on('click', commonDebounce(async function () {
        const profileId = $('#profileId').val();
        const businessServiceId = $('#operationalServiceId option:selected').data('id');
        const businessServiceName = $('#operationalServiceId option:selected').text();
        const businessFunctionId = $('#operationalFunction option:selected').data('id');
        const businessFunctionName = $('#operationalFunction option:selected').text().trim() === "Select Value" ? '' : $('#operationalFunction option:selected').text();
        const infraObjectId = $('#infraObject option:selected').data('id');
        const infraObjectName = $('#infraObject option:selected').text().trim() === "Select Value" ? '' : $('#infraObject option:selected').text();
        const workflowId = $('#workflowName option:selected').attr('id');
        let workflowName = $('#workflowName option:selected').text().trim() === "Select workflow" ? '' : $('#workflowName option:selected').text();
        const workflowType = $('#workflowName option:selected').attr('name');
        const workflowProfileName = $('#selectWorkflowprofileName option:selected').text();
        const workflowProfileId = $('#selectWorkflowprofileName').val();

        // Validate dropdowns
        const isBusinessService = validateDropDown(businessServiceName, "Select operational service", $('#operationalServiceError'));
        const isBusinessFunction = validateDropDown(businessFunctionName, "Select operational function", $('#operationalFunctionError'));
        const isInfraObject = validateDropDown(infraObjectName, "Select infraobject", $('#infraObjectError'));
        const isWorkflow = validateDropDown(workflowName, "Select workflow", $('#workflowerror'));
        const isSelectWorkflowProfile = validateDropDown(workflowProfileId, "Select workflow profile", $('#workfloProfileError'));

        if (isBusinessService && isBusinessFunction && isInfraObject && isWorkflow && isSelectWorkflowProfile) {
            const formData = {
                Id: globalProfileUpdateId,
                ProfileId: profileId,
                ProfileName: workflowProfileName,
                BusinessServiceId: businessServiceId,
                BusinessServiceName: businessServiceName,
                BusinessFunctionId: businessFunctionId,
                BusinessFunctionName: businessFunctionName,
                InfraObjectId: infraObjectId,
                InfraObjectName: infraObjectName,
                WorkflowId: workflowId,
                WorkflowName: workflowName,
                WorkflowType: workflowType,
                __RequestVerificationToken: gettoken()
            };
                const result = await $.ajax({
                    url: RootUrl + workflowProfileURL.updateWorkflowProfileUrl,
                    data: formData,
                    dataType: "json",
                    traditional: true,
                    type: 'POST'
                });

                if (result?.success) {
                    $('#selectWorkflowprofileName').val(profileId).trigger('change');
                    notificationAlert('success', result.data.message);
                    $('#btnWorkflowProfileEditProfile,#btnWorkflowProfileResetProfile').hide();
                } else {
                    errorNotification(result);
                }
                clearProfileInfo();
        }
    }, 800));

    $('#btnWorkflowProfileResetProfile').on('click', commonDebounce(async function () {
        clearProfileInfo();
        $('#btnWorkflowProfileEditProfile, #btnWorkflowProfileResetProfile').hide();
        $('#btnSaveProfile').show();
        $('[id="editProfileId"]').css("visibility", "visible");
        $('[id="deleteProfileId"]').removeClass('disabled').css({ 'pointer-events': 'auto', 'opacity': '1' });
    }, 800));

    $('#textProfileName').on('keyup', commonDebounce(async function () {
        let profileId = $('#textProfileId').val();
        let elementValue = $(this).val();
        let sanitizedValue = elementValue.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await profileValidateName(elementValue, profileId, workflowProfileURL.nameUrl);
    }));
 
    $('#workflowPassword, #workflowConfirmPassword').on('input', function () {
        if (this.value) {
            const value = sanitizeInput(this.value);
            this.id === 'workflowPassword'? inputpassword(this.id, value) || $('#workflowConfirmPassword').val(''): inputConfirmpassword(this.id, value);
        }
    });

    $(document).on('blur', '#workflowPassword, #workflowConfirmPassword', function () {
        if (this.value) {
            const value = sanitizeInput(this.value);
            blurpassword(this.id, value);
        }
    });

    $('#workflowPassword').on('focus', function () {
        if (this.id) focuspassword(this.id);
    });

    $('#workflowConfirmPassword').on('focus', function () {
        if (this.id) focusconfirmpassword(this.id);
    });

    $('#selectGroupPolicy').on('change', function () {
        const id = $("#selectGroupPolicy option:selected")?.attr('id');
        $('#textGroupPolicyId').val(id);
        validateDropDown($(this).val(), "Select group node policy", $('#GroupPolicyError'));
    });


    $('#selectExecutionPolicy').on('change', function () {
        const value = $(this).val();
        if (value) {
            $('#GroupPolicy').toggle(value === '1');
        }
        const groupPolicyUrl = `${RootUrl}Admin/GroupNodePolicy/GetGroupPolicyByType`;
        $('#selectGroupPolicy').val('').trigger('change');
        $('#GroupPolicyError').text('').removeClass('field-validation-error');
        validateDropDown(value, "Select execution policy", $('#ExecutionPolicyError'));
        $.ajax({
            type: "GET",
            url: groupPolicyUrl,
            dataType: "json",
            success: function (response) {
                if (response && Array.isArray(response)) {
                    const $dropdown = $('#selectGroupPolicy');
                    $dropdown.empty().append('<option value="">Select group node policy</option>');
                    response.forEach(item => {
                        if (item?.groupName) {
                            $dropdown.append(`<option id="${item?.id}" value="${item?.groupName}">${item?.groupName}</option>`);
                        }
                    });
                }
            },           
        });
    });

    $('#btnProfileChangePassword').on('click', function () {
        $('.input-group-text.toggle-password').empty().append('<i class="cp-password-visible fs-6"></i>');
        $('#ProfileChangepasswordModal').modal('show');
    });
    
    $('#btnWorkflowProfileCreate').on('click', function () {
        clearFields();
        $('#profileSaveFunctions').text("Save");
        $('#profilePasswordField, #profilePasswordField1').show()
        if ($('#selectExecutionPolicy').val() == "") {
            $('#GroupPolicy').hide();
        }
    });

    //Validation

    const clearFields = () => {
        const errorElements = ['#ProfileNameError', '#Password-error', '#Confirmpassword-error', '#GroupPolicyError', '#ExecutionPolicyError']
        $('#textProfileName, #workflowPassword, #workflowConfirmPassword, #textProfileId').val('');
        $('#selectGroupPolicy, #selectExecutionPolicy').val('').trigger('change');
        $('.input-group-text.toggle-password').html('<i class="cp-password-visible fs-6"></i>');
        ClearWorkFlowErrorElements(errorElements);
    };

    const clearDataFields = () => {
        $('#CurrentPassword, #Password, #ConfirmPassword').val('');
        ['#OldPassword-error', '#NewPassword-error', '#ConfirmPassword-error'].forEach(selector => { $(selector).text('').removeClass('field-validation-error');
        });
    };

    const clearProfileInfo = () => {
        $('#operationalServiceId').val('').trigger('change');
        $('#operationalFunction, #infraObject, #workflowName').empty();
    };


})