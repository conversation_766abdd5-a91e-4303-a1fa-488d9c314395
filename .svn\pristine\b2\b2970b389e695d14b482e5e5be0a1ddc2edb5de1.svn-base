﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class InfraObjectSchedulerRepositoryMocks
{
    public static Mock<IInfraObjectSchedulerRepository> CreateInfraObjectSchedulerRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulers);

        infraObjectSchedulerRepository.Setup(repo => repo.AddAsync(It.IsAny<InfraObjectScheduler>())).ReturnsAsync(
            (InfraObjectScheduler infraObjectScheduler) =>
            {
                infraObjectScheduler.Id = new Fixture().Create<int>();
                infraObjectScheduler.ReferenceId = new Fixture().Create<Guid>().ToString();
                infraObjectSchedulers.Add(infraObjectScheduler);

                return infraObjectScheduler;
            });

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> UpdateInfraObjectSchedulerRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulers);

        infraObjectSchedulerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulers.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectSchedulerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObjectScheduler>())).ReturnsAsync((InfraObjectScheduler infraObjectScheduler) =>
        {
            var index = infraObjectSchedulers.FindIndex(item => item.ReferenceId == infraObjectScheduler.ReferenceId);

            infraObjectSchedulers[index] = infraObjectScheduler;

            return infraObjectScheduler;
        });

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> DeleteInfraObjectSchedulerRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulers);

        infraObjectSchedulerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulers.SingleOrDefault(x => x.ReferenceId == i));

        infraObjectSchedulerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<InfraObjectScheduler>())).ReturnsAsync((InfraObjectScheduler infraObjectScheduler) =>
        {
            var index = infraObjectSchedulers.FindIndex(item => item.ReferenceId == infraObjectScheduler.ReferenceId);

            infraObjectScheduler.IsActive = false;
            infraObjectSchedulers[index] = infraObjectScheduler;

            return infraObjectScheduler;
        });

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> GetInfraObjectSchedulerRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjectSchedulers);

        infraObjectSchedulerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => infraObjectSchedulers.SingleOrDefault(x => x.ReferenceId == i));

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> GetInfraObjectSchedulerNamesRepository(List<InfraObjectScheduler> infraObjects)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.GetInfraObjectSchedulerNames()).ReturnsAsync(infraObjects);

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(infraObjects);

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> GetInfraObjectSchedulerNameUniqueRepository(List<InfraObjectScheduler> infraObjects)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.IsInfraObjectSchedulerNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => infraObjects.Exists(x => x.InfraObjectName == i && x.ReferenceId == j));

        return infraObjectSchedulerRepository;
    }
    public static Mock<IInfraObjectSchedulerRepository> GetInfraObjectSchedulerEmptyRepository()
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<InfraObjectScheduler>());

        infraObjectSchedulerRepository.Setup(repo => repo.GetInfraObjectSchedulerByInfraObjectId(It.IsAny<string>())).ReturnsAsync(new List<InfraObjectScheduler>());

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> GetPaginatedInfraObjectSchedulerRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        var queryableInfraObjectScheduler = infraObjectSchedulers.BuildMock();

        infraObjectSchedulerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableInfraObjectScheduler);

        return infraObjectSchedulerRepository;
    }

    public static Mock<IInfraObjectSchedulerRepository> GetInfraObjectSchedulerByInfraObjectIdRepository(List<InfraObjectScheduler> infraObjectSchedulers)
    {
        var infraObjectSchedulerRepository = new Mock<IInfraObjectSchedulerRepository>();

        infraObjectSchedulerRepository.Setup(repo => repo.GetInfraObjectSchedulerByInfraObjectId(It.IsAny<string>())).ReturnsAsync(infraObjectSchedulers);
        
        return infraObjectSchedulerRepository;
    }
    

    //Events

    public static Mock<IUserActivityRepository> CreateInfraObjectSchedulerEventRepository(List<UserActivity> userActivities)
    {
        var infraObjectSchedulerEventRepository = new Mock<IUserActivityRepository>();

        infraObjectSchedulerEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        infraObjectSchedulerEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();
                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                userActivities.Add(userActivity);

                return userActivity;
            });

        return infraObjectSchedulerEventRepository;
    }
}