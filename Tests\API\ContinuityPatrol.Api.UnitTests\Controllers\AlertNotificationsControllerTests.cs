using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertNotification.Commands.Update;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertNotification.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AlertNotificationsControllerTests : IClassFixture<AlertNotificationFixture>
{
    private readonly AlertNotificationFixture _alertNotificationFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AlertNotificationsController _controller;

    public AlertNotificationsControllerTests(AlertNotificationFixture alertNotificationFixture)
    {
        _alertNotificationFixture = alertNotificationFixture;

        var testBuilder = new ControllerTestBuilder<AlertNotificationsController>();
        _controller = testBuilder.CreateController(
            _ => new AlertNotificationsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAlertNotifications_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_alertNotificationFixture.AlertNotificationListVm);

        // Act
        var result = await _controller.GetAlertNotifications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationListVm>>(okResult.Value);
        Assert.Equal(3, alertNotifications.Count);
    }

    [Fact]
    public async Task GetAlertNotifications_ReturnsEmptyList_WhenNoAlertNotificationsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationListQuery>(), default))
            .ReturnsAsync(new List<AlertNotificationListVm>());

        // Act
        var result = await _controller.GetAlertNotifications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationListVm>>(okResult.Value);
        Assert.Empty(alertNotifications);
    }

    [Fact]
    public async Task GetAlertNotificationById_ReturnsAlertNotification_WhenIdIsValid()
    {
        // Arrange
        var alertNotificationId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertNotificationDetailQuery>(q => q.Id == alertNotificationId), default))
            .ReturnsAsync(_alertNotificationFixture.AlertNotificationDetailVm);

        // Act
        var result = await _controller.GetAlertNotificationById(alertNotificationId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAlertNotificationById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertNotificationById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAlertNotification_Returns201Created()
    {
        // Arrange
        var command = _alertNotificationFixture.CreateAlertNotificationCommand;
        var expectedMessage = $"AlertNotification '{command.AlertCode}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertNotificationResponse
            {
                Message = expectedMessage,
                AlertNotificationId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertNotification(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertNotificationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAlertNotification_Throws_WhenAlertCodeExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAlertNotificationCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("AlertCode exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAlertNotification(_alertNotificationFixture.CreateAlertNotificationCommand));
    }

    [Fact]
    public async Task UpdateAlertNotification_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AlertNotification '{_alertNotificationFixture.UpdateAlertNotificationCommand.AlertCode}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAlertNotificationCommand>(), default))
            .ReturnsAsync(new UpdateAlertNotificationResponse
            {
                Message = expectedMessage,
                AlertNotificationId = _alertNotificationFixture.UpdateAlertNotificationCommand.Id
            });

        // Act
        var result = await _controller.UpdateAlertNotification(_alertNotificationFixture.UpdateAlertNotificationCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAlertNotificationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertNotification_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AlertNotification 'CRIT_001' has been deleted successfully!.";
        var alertNotificationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertNotificationCommand>(c => c.Id == alertNotificationId), default))
            .ReturnsAsync(new DeleteAlertNotificationResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertNotification(alertNotificationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertNotificationResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertNotification_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAlertNotification("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAlertNotifications_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAlertNotificationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _alertNotificationFixture.AlertNotificationListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AlertNotificationListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAlertNotifications(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertNotificationListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertNotificationListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }


    [Fact]
    public async Task GetAlertNotifications_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertNotificationListVm>());

        // Act
        await _controller.GetAlertNotifications();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task CreateAlertNotification_ClearsCacheAfterCreation()
    {
        // Arrange
        var command = _alertNotificationFixture.CreateAlertNotificationCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertNotificationResponse
            {
                Message = "Created successfully",
                AlertNotificationId = Guid.NewGuid().ToString()
            });

        // Act
        await _controller.CreateAlertNotification(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task UpdateAlertNotification_ClearsCacheAfterUpdate()
    {
        // Arrange
        var command = _alertNotificationFixture.UpdateAlertNotificationCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateAlertNotificationResponse
            {
                Message = "Updated successfully",
                AlertNotificationId = command.Id
            });

        // Act
        await _controller.UpdateAlertNotification(command);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task DeleteAlertNotification_ClearsCacheAfterDeletion()
    {
        // Arrange
        var alertNotificationId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertNotificationCommand>(c => c.Id == alertNotificationId), default))
            .ReturnsAsync(new DeleteAlertNotificationResponse
            {
                IsActive = false,
                Message = "Deleted successfully"
            });

        // Act
        await _controller.DeleteAlertNotification(alertNotificationId);

        // Assert
        // Cache should be cleared (tested by ensuring no exceptions are thrown)
        Assert.True(true);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ReturnsAlertNotifications_WhenParametersAreValid()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var alertCode = "CRIT_001";
        var expectedResult = new List<AlertNotificationDetailByInfraObjectIdVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                AlertCategoryId = 1,
                AlertType = "Critical",
                AlertSentCount = 5,
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 3
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                AlertCategoryId = 1,
                AlertType = "Critical",
                AlertSentCount = 2,
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 1
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertNotificationDetailByInfraObjectIdQuery>(q =>
                q.InfraObjectId == infraObjectId && q.AlertCode == alertCode), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationDetailByInfraObjectIdVm>>(okResult.Value);
        Assert.Equal(2, alertNotifications.Count);
        Assert.All(alertNotifications, an =>
        {
            Assert.Equal(infraObjectId, an.InfraObjectId);
            Assert.Equal(alertCode, an.AlertCode);
        });
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_ReturnsEmptyList_WhenNoMatchingNotifications()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var alertCode = "NONEXISTENT_001";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertNotificationDetailByInfraObjectIdQuery>(q =>
                q.InfraObjectId == infraObjectId && q.AlertCode == alertCode), default))
            .ReturnsAsync(new List<AlertNotificationDetailByInfraObjectIdVm>());

        // Act
        var result = await _controller.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationDetailByInfraObjectIdVm>>(okResult.Value);
        Assert.Empty(alertNotifications);
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_Throws_WhenInfraObjectIdIsInvalid()
    {
        // Arrange
        var invalidInfraObjectId = "invalid-guid";
        var alertCode = "CRIT_001";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertNotificationByInfraObjectIdAndAlertCode(invalidInfraObjectId, alertCode));
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_CallsCorrectQuery()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var alertCode = "TEST_001";
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertNotificationDetailByInfraObjectIdQuery>(q =>
                q.InfraObjectId == infraObjectId && q.AlertCode == alertCode), default))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertNotificationDetailByInfraObjectIdVm>());

        // Act
        await _controller.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateAlertNotification_ValidatesRequiredFields()
    {
        // Arrange
        var command = new CreateAlertNotificationCommand
        {
            AlertCategoryId = 0, // Invalid category ID
            AlertCode = "",      // Empty alert code
            AlertType = "Critical"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("AlertCategoryId and AlertCode are required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateAlertNotification(command));
    }

    [Fact]
    public async Task UpdateAlertNotification_ValidatesNotificationExists()
    {
        // Arrange
        var command = new UpdateAlertNotificationCommand
        {
            Id = Guid.NewGuid().ToString(),
            AlertCategoryId = 1,
            AlertCode = "UPDATED_001",
            AlertType = "Updated Type"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("AlertNotification not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateAlertNotification(command));
    }

    [Fact]
    public async Task GetPaginatedAlertNotifications_HandlesFilteringByCategory()
    {
        // Arrange
        var query = new GetAlertNotificationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var criticalNotifications = new List<AlertNotificationListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertCategoryId = 1,
                AlertCode = "CRIT_001",
                AlertType = "Critical",
                AlertSentCount = 5
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                AlertCategoryId = 1,
                AlertCode = "CRIT_002",
                AlertType = "Critical",
                AlertSentCount = 3
            }
        };

        var expectedPaginatedResult = PaginatedResult<AlertNotificationListVm>.Success(
            data: criticalNotifications,
            count: criticalNotifications.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAlertNotifications(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertNotificationListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertNotificationListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, notification => Assert.Equal(1, notification.AlertCategoryId));
    }

    [Fact]
    public async Task GetAlertNotificationByInfraObjectIdAndAlertCode_HandlesComplexScenarios()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var alertCode = "COMPLEX_ALERT_001";
        var expectedResult = new List<AlertNotificationDetailByInfraObjectIdVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                AlertCategoryId = 1,
                AlertType = "Critical System Failure",
                AlertSentCount = 15,
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 10
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                AlertCode = alertCode,
                AlertCategoryId = 1,
                AlertType = "Critical System Failure",
                AlertSentCount = 8,
                EntityId = Guid.NewGuid().ToString(),
                PositiveAlertCount = 5
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertNotificationDetailByInfraObjectIdQuery>(q =>
                q.InfraObjectId == infraObjectId && q.AlertCode == alertCode), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertNotificationByInfraObjectIdAndAlertCode(infraObjectId, alertCode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationDetailByInfraObjectIdVm>>(okResult.Value);
        Assert.Equal(2, alertNotifications.Count);
        Assert.All(alertNotifications, an =>
        {
            Assert.Equal(infraObjectId, an.InfraObjectId);
            Assert.Equal(alertCode, an.AlertCode);
            Assert.True(an.AlertSentCount > 0);
            Assert.True(an.PositiveAlertCount > 0);
        });
    }

    [Fact]
    public async Task CreateAlertNotification_HandlesHighVolumeAlerts()
    {
        // Arrange
        var command = new CreateAlertNotificationCommand
        {
            AlertCategoryId = 1,
            AlertCode = "HIGH_VOLUME_001",
            AlertType = "High Volume Alert",
            AlertSentCount = 1000, // High volume
            InfraObjectId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            PositiveAlertCount = 750
        };

        var expectedMessage = $"AlertNotification '{command.AlertCode}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertNotificationResponse
            {
                Message = expectedMessage,
                AlertNotificationId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertNotification(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertNotificationResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertNotification_VerifiesNotificationIsDeactivated()
    {
        // Arrange
        var alertNotificationId = Guid.NewGuid().ToString();
        var expectedMessage = "AlertNotification 'TEST_001' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertNotificationCommand>(c => c.Id == alertNotificationId), default))
            .ReturnsAsync(new DeleteAlertNotificationResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertNotification(alertNotificationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertNotificationResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetAlertNotifications_HandlesEmptyDatabase()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertNotificationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<AlertNotificationListVm>());

        // Act
        var result = await _controller.GetAlertNotifications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertNotifications = Assert.IsAssignableFrom<List<AlertNotificationListVm>>(okResult.Value);
        Assert.Empty(alertNotifications);
    }
}
