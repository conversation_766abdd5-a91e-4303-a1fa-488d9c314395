using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ImpactAvailabilityRepositoryTests : IClassFixture<ImpactAvailabilityFixture>, IDisposable
{
    private readonly ImpactAvailabilityFixture _impactAvailabilityFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ImpactAvailabilityRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public ImpactAvailabilityRepositoryTests(ImpactAvailabilityFixture impactAvailabilityFixture)
    {
        _impactAvailabilityFixture = impactAvailabilityFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new ImpactAvailabilityRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.ImpactAvailabilities.RemoveRange(_dbContext.ImpactAvailabilities);
        await _dbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveImpactAvailabilities_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Business Service 1",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Business Service 2",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                BusinessServiceName = "Business Service 3",
                TotalServiceCount = "5",
                ServiceUp = "3",
                ServiceDown = "2",
                IsActive = false // This should be excluded
            }
        };

        await _dbContext.ImpactAvailabilities.AddRangeAsync(impactAvailabilities);
         _dbContext.SaveChanges();
       
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count); // Only active records
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Contains(result, x => x.BusinessServiceName == "Business Service 1");
        Assert.Contains(result, x => x.BusinessServiceName == "Business Service 2");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Business Service 3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoActiveData()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Business Service 1",
            IsActive = false
        };
        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();
        impactAvailability.IsActive = false;
        _dbContext.ImpactAvailabilities.Update(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsImpactAvailability_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var referenceId = Guid.NewGuid().ToString();
        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = referenceId,
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Business Service 1",
            TotalServiceCount = "10",
            ServiceUp = "8",
            ServiceDown = "2",
            TotalBusinessFunctionCount = "5",
            BusinessFunctionUp = "4",
            BusinessFunctionDown = "1",
            TotalInfraObjectCount = "15",
            InfraObjectUp = "12",
            InfraObjectDown = "3",
            MajorServiceImpact = "1",
            MinorServiceImpact = "2",
            TotalBusinessFunctionImpact = "3",
            BusinessFunctionAvailable = "4",
            TotalInfraObjectImpact = "5",
            InfraObjectAvailable = "10",
            InfraPartial = "3",
            InfraMajor = "2",
            IsActive = true
        };

        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Business Service 1", result.BusinessServiceName);
        Assert.Equal("10", result.TotalServiceCount);
        Assert.Equal("8", result.ServiceUp);
        Assert.Equal("2", result.ServiceDown);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    //[Fact]
    //public async Task GetByReferenceIdAsync_ThrowsException_WhenInvalidGuid()
    //{
    //    // Arrange
    //    var invalidGuid = "invalid-guid";

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentException>(() =>
    //        _repository.GetByReferenceIdAsync(invalidGuid));
    //}

 

    #endregion

    #region GetImpactAvailabilityByBusinessServiceId Tests

    [Fact]
    public async Task GetImpactAvailabilityByBusinessServiceId_ReturnsMatchingRecord_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Matching Service",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                TotalBusinessFunctionCount = "5",
                BusinessFunctionUp = "4",
                BusinessFunctionDown = "1",
                TotalInfraObjectCount = "15",
                InfraObjectUp = "12",
                InfraObjectDown = "3",
                MajorServiceImpact = "1",
                MinorServiceImpact = "2",
                TotalBusinessFunctionImpact = "3",
                BusinessFunctionAvailable = "4",
                TotalInfraObjectImpact = "5",
                InfraObjectAvailable = "10",
                InfraPartial = "3",
                InfraMajor = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456", // Different business service
                BusinessServiceName = "Different Service",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            },
            
        };

        await _dbContext.ImpactAvailabilities.AddRangeAsync(impactAvailabilities);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServiceId, result.BusinessServiceId);
        Assert.Equal("Matching Service", result.BusinessServiceName);
        Assert.Equal("10", result.TotalServiceCount);
        Assert.Equal("8", result.ServiceUp);
        Assert.Equal("2", result.ServiceDown);
        Assert.Equal("5", result.TotalBusinessFunctionCount);
        Assert.Equal("4", result.BusinessFunctionUp);
        Assert.Equal("1", result.BusinessFunctionDown);
        Assert.Equal("15", result.TotalInfraObjectCount);
        Assert.Equal("12", result.InfraObjectUp);
        Assert.Equal("3", result.InfraObjectDown);
        Assert.Equal("1", result.MajorServiceImpact);
        Assert.Equal("2", result.MinorServiceImpact);
        Assert.Equal("3", result.TotalBusinessFunctionImpact);
        Assert.Equal("4", result.BusinessFunctionAvailable);
        Assert.Equal("5", result.TotalInfraObjectImpact);
        Assert.Equal("10", result.InfraObjectAvailable);
        Assert.Equal("3", result.InfraPartial);
        Assert.Equal("2", result.InfraMajor);
    }

    [Fact]
    public async Task GetImpactAvailabilityByBusinessServiceId_ReturnsNull_WhenNoActiveMatch()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = businessServiceId,
            BusinessServiceName = "Inactive Service",
        };

        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();
        impactAvailability.IsActive = false;
       _dbContext.ImpactAvailabilities.Update(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetImpactAvailabilityByBusinessServiceId_ReturnsNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_456", // Different business service
            BusinessServiceName = "Different Service",
            IsActive = true
        };

        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetImpactAvailabilityByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddImpactAvailability_WhenValidImpactAvailability()
    {
        // Arrange
        await ClearDatabase();
        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            TotalServiceCount = "10",
            ServiceUp = "8",
            ServiceDown = "2",
            TotalBusinessFunctionCount = "5",
            BusinessFunctionUp = "4",
            BusinessFunctionDown = "1",
            TotalInfraObjectCount = "15",
            InfraObjectUp = "12",
            InfraObjectDown = "3",
            MajorServiceImpact = "1",
            MinorServiceImpact = "2",
            TotalBusinessFunctionImpact = "3",
            BusinessFunctionAvailable = "4",
            TotalInfraObjectImpact = "5",
            InfraObjectAvailable = "10",
            InfraPartial = "3",
            InfraMajor = "2",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(impactAvailability);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(impactAvailability.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(impactAvailability.TotalServiceCount, result.TotalServiceCount);
        Assert.Equal(impactAvailability.ServiceUp, result.ServiceUp);
        Assert.Equal(impactAvailability.ServiceDown, result.ServiceDown);
        Assert.Equal(impactAvailability.TotalBusinessFunctionCount, result.TotalBusinessFunctionCount);
        Assert.Equal(impactAvailability.BusinessFunctionUp, result.BusinessFunctionUp);
        Assert.Equal(impactAvailability.BusinessFunctionDown, result.BusinessFunctionDown);
        Assert.Equal(impactAvailability.TotalInfraObjectCount, result.TotalInfraObjectCount);
        Assert.Equal(impactAvailability.InfraObjectUp, result.InfraObjectUp);
        Assert.Equal(impactAvailability.InfraObjectDown, result.InfraObjectDown);
        Assert.Equal(impactAvailability.MajorServiceImpact, result.MajorServiceImpact);
        Assert.Equal(impactAvailability.MinorServiceImpact, result.MinorServiceImpact);
        Assert.Equal(impactAvailability.TotalBusinessFunctionImpact, result.TotalBusinessFunctionImpact);
        Assert.Equal(impactAvailability.BusinessFunctionAvailable, result.BusinessFunctionAvailable);
        Assert.Equal(impactAvailability.TotalInfraObjectImpact, result.TotalInfraObjectImpact);
        Assert.Equal(impactAvailability.InfraObjectAvailable, result.InfraObjectAvailable);
        Assert.Equal(impactAvailability.InfraPartial, result.InfraPartial);
        Assert.Equal(impactAvailability.InfraMajor, result.InfraMajor);
        Assert.Single(_dbContext.ImpactAvailabilities);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenImpactAvailabilityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsImpactAvailability_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            TotalServiceCount = "10",
            ServiceUp = "8",
            ServiceDown = "2",
            IsActive = true
        };

        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(impactAvailability.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(impactAvailability.Id, result.Id);
        Assert.Equal(impactAvailability.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(impactAvailability.TotalServiceCount, result.TotalServiceCount);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateImpactAvailability_WhenValidImpactAvailability()
    {
        // Arrange
        await ClearDatabase();
        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Original Service",
            TotalServiceCount = "10",
            ServiceUp = "8",
            ServiceDown = "2",
            TotalBusinessFunctionCount = "5",
            BusinessFunctionUp = "4",
            BusinessFunctionDown = "1",
            IsActive = true
        };

        _dbContext.ImpactAvailabilities.Add(impactAvailability);
        await _dbContext.SaveChangesAsync();

        impactAvailability.BusinessServiceName = "Updated Service";
        impactAvailability.TotalServiceCount = "20";
        impactAvailability.ServiceUp = "18";
        impactAvailability.ServiceDown = "2";
        impactAvailability.TotalBusinessFunctionCount = "10";
        impactAvailability.BusinessFunctionUp = "9";
        impactAvailability.BusinessFunctionDown = "1";

        // Act
        var result = await _repository.UpdateAsync(impactAvailability);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service", result.BusinessServiceName);
        Assert.Equal("20", result.TotalServiceCount);
        Assert.Equal("18", result.ServiceUp);
        Assert.Equal("2", result.ServiceDown);
        Assert.Equal("10", result.TotalBusinessFunctionCount);
        Assert.Equal("9", result.BusinessFunctionUp);
        Assert.Equal("1", result.BusinessFunctionDown);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenImpactAvailabilityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveImpactAvailability_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var impactAvailability = new ImpactAvailability
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            TotalServiceCount = "10",
            IsActive = true
        };

        await _dbContext.ImpactAvailabilities.AddAsync(impactAvailability);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(impactAvailability);

        // Assert
        var deletedAvailability = await _dbContext.ImpactAvailabilities.FindAsync(impactAvailability.Id);
        Assert.Null(deletedAvailability);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleImpactAvailabilities_WhenValidList()
    {
        // Arrange
        await ClearDatabase();
        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Service 1",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Service 2",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Service 3",
                TotalServiceCount = "15",
                ServiceUp = "12",
                ServiceDown = "3",
                IsActive = true
            }
        };

        // Act
        var result = await _repository.AddRangeAsync(impactAvailabilities);

        // Assert
        Assert.Equal(3, result.Count());
        Assert.Equal(3, _dbContext.ImpactAvailabilities.Count());
        Assert.Contains(result, x => x.BusinessServiceName == "Service 1");
        Assert.Contains(result, x => x.BusinessServiceName == "Service 2");
        Assert.Contains(result, x => x.BusinessServiceName == "Service 3");
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldReturnEmpty_WhenListIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var emptyList = new List<ImpactAvailability>();

        // Act
        var result = await _repository.AddRangeAsync(emptyList);

        // Assert
        Assert.Empty(result);
        Assert.Empty(_dbContext.ImpactAvailabilities);
    }

    #endregion

    #region AssignedBusinessServices Tests

    [Fact]
    public void AssignedBusinessServices_ReturnsMatchingServices_WhenAssignedServicesExist()
    {
        // Arrange
        var businessServiceId1 = "BS_123";
        var businessServiceId2 = "BS_456";
        var businessServiceId3 = "BS_789";

        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId1,
                BusinessServiceName = "Assigned Service 1",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId2,
                BusinessServiceName = "Assigned Service 2",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId3,
                BusinessServiceName = "Non-Assigned Service",
                TotalServiceCount = "15",
                ServiceUp = "12",
                ServiceDown = "3",
                IsActive = true
            }
        }.AsQueryable();

        // Mock the ILoggedInUserService to return assigned business services JSON
        var assignedEntity = new ContinuityPatrol.Shared.Core.Domain.AssignedEntity
        {
            AssignedBusinessServices = new List<ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices>
            {
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = businessServiceId1, Name = "Service 1" },
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = businessServiceId2, Name = "Service 2" }
            }
        };

        var assignedInfrasJson = Newtonsoft.Json.JsonConvert.SerializeObject(assignedEntity);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        // Create a new repository instance with the mocked service
        var repository = new ImpactAvailabilityRepository(_dbContext, _mockLoggedInUserService.Object);

        // Act
        var result = repository.AssignedBusinessServices(impactAvailabilities);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.BusinessServiceId == businessServiceId1);
        Assert.Contains(result, x => x.BusinessServiceId == businessServiceId2);
        Assert.DoesNotContain(result, x => x.BusinessServiceId == businessServiceId3);
        Assert.Contains(result, x => x.BusinessServiceName == "Assigned Service 1");
        Assert.Contains(result, x => x.BusinessServiceName == "Assigned Service 2");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Non-Assigned Service");
    }

    [Fact]
    public void AssignedBusinessServices_ReturnsEmpty_WhenNoAssignedServices()
    {
        // Arrange
        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Service 1",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Service 2",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            }
        }.AsQueryable();

        // Mock the ILoggedInUserService to return empty assigned business services JSON
        var assignedEntity = new ContinuityPatrol.Shared.Core.Domain.AssignedEntity
        {
            AssignedBusinessServices = new List<ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices>()
        };

        var assignedInfrasJson = Newtonsoft.Json.JsonConvert.SerializeObject(assignedEntity);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        // Create a new repository instance with the mocked service
        var repository = new ImpactAvailabilityRepository(_dbContext, _mockLoggedInUserService.Object);

        // Act
        var result = repository.AssignedBusinessServices(impactAvailabilities);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public void AssignedBusinessServices_ReturnsEmpty_WhenNoMatchingServices()
    {
        // Arrange
        var impactAvailabilities = new List<ImpactAvailability>
        {
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Service 1",
                TotalServiceCount = "10",
                ServiceUp = "8",
                ServiceDown = "2",
                IsActive = true
            },
            new ImpactAvailability
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Service 2",
                TotalServiceCount = "20",
                ServiceUp = "18",
                ServiceDown = "2",
                IsActive = true
            }
        }.AsQueryable();

        // Mock the ILoggedInUserService to return different assigned business services JSON
        var assignedEntity = new ContinuityPatrol.Shared.Core.Domain.AssignedEntity
        {
            AssignedBusinessServices = new List<ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices>
            {
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = "BS_999", Name = "Different Service 1" },
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = "BS_888", Name = "Different Service 2" }
            }
        };

        var assignedInfrasJson = Newtonsoft.Json.JsonConvert.SerializeObject(assignedEntity);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        // Create a new repository instance with the mocked service
        var repository = new ImpactAvailabilityRepository(_dbContext, _mockLoggedInUserService.Object);

        // Act
        var result = repository.AssignedBusinessServices(impactAvailabilities);

        // Assert
        Assert.Empty(result);
    }

    #endregion
}
