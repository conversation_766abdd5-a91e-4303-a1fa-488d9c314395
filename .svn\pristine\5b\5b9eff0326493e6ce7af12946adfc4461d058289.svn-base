﻿using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Commands;

public class UpdateCompanyTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateCompanyCommandHandler _handler;

    public UpdateCompanyTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockCompanyRepository = CompanyRepositoryMocks.UpdateCompanyRepository(_companyFixture.Companies);

        _handler = new UpdateCompanyCommandHandler(_companyFixture.Mapper, _mockCompanyRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidCompany_UpdateToCompaniesRepo()
    {
        _companyFixture.UpdateCompanyCommand.Id = _companyFixture.Companies[0].ReferenceId;

        var result = await _handler.Handle(_companyFixture.UpdateCompanyCommand, CancellationToken.None);

        var company = await _mockCompanyRepository.Object.GetByReferenceIdAsync(result.CompanyId);

        Assert.Equal(_companyFixture.UpdateCompanyCommand.Name, company.Name);
    }

    [Fact]
    public async Task Handle_Return_UpdateCompanyResponse_When_CompanyUpdated()
    {
        _companyFixture.UpdateCompanyCommand.Id = _companyFixture.Companies[0].ReferenceId;

        var result = await _handler.Handle(_companyFixture.UpdateCompanyCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateCompanyResponse));

        result.CompanyId.ShouldBeGreaterThan(0.ToString());

        result.CompanyId.ShouldBe(_companyFixture.UpdateCompanyCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidCompanyId()
    {
        _companyFixture.UpdateCompanyCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_companyFixture.UpdateCompanyCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var handler = new UpdateCompanyCommandHandler(_companyFixture.Mapper, _mockCompanyRepository.Object, _mockPublisher.Object);

        _companyFixture.UpdateCompanyCommand.Id = _companyFixture.Companies[0].ReferenceId;

        await handler.Handle(_companyFixture.UpdateCompanyCommand, CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockCompanyRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Company>()), Times.Once);
    }
}