﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class AlertNotificationFilterSpecification : Specification<AlertNotification>
{
    public AlertNotificationFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.InfraObjectId != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("alerttype=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.AlertType.Contains(stringItem.Replace("alerttype=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.AlertType.Contains(searchString);
            }
        }
    }
}