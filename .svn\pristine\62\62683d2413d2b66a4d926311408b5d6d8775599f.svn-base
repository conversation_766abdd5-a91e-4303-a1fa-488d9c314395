﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class SiteTypeRepository : BaseRepository<SiteType>, ISiteTypeRepository
{
    private readonly ApplicationDbContext _dbContext;

    public SiteTypeRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
    public override async Task<IReadOnlyList<SiteType>> ListAllAsync()
    {
        return await SelectSiteType(_dbContext.SiteTypes.Active().AsNoTracking())
           .ToListAsync();
    }
   
    public async Task<SiteType> GetSiteTypeById(string id)
    {
        return await SelectSiteType(_dbContext.SiteTypes
            .Active()
            .Where(s => s.ReferenceId.Equals(id) && s.IsDelete))
            .FirstOrDefaultAsync();
    }

    public Task<bool> IsSiteTypeNameExist(string type, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.SiteTypes.Any(e => e.Type.Equals(type)))
            : Task.FromResult(_dbContext.SiteTypes.Where(e => e.Type.Equals(type)).ToList().Unique(id));
    }

    public Task<bool> IsSiteTypeNameUnique(string type)
    {
        var matches = _dbContext.SiteTypes.Any(e => e.Type.Equals(type));

        return Task.FromResult(matches);
    }
    public Task<bool> IsSiteTypeCategoryUnique(string category)
    {
        var matches = _dbContext.SiteTypes.Any(e => e.Category.Equals(category));

        return Task.FromResult(matches);
    }

    public override async Task<PaginatedResult<SiteType>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<SiteType> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectSiteType(Entities.Specify(productFilterSpec).DescOrderById())           
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<SiteType> GetPaginatedQuery()
    {
        return Entities.Where(x => x.IsActive)
            .AsNoTracking()
            .OrderBy(x => x.Id);
    }

    public async Task<int> GetSiteTypeIndexByIdAsync(string id)
    {
        var siteTypes = await SelectSiteType(_dbContext.SiteTypes
            .Active()
            .Where(s => !s.Category.ToLower().Equals("primary") && !s.Category.ToLower().Equals("dr"))
            .OrderBy(s => s.Id))
            .ToListAsync();

        var index = siteTypes.FindIndex(s => s.ReferenceId.Equals(id) && s.IsDelete);

        return index;
    }
    public async Task<List<SiteType>> GetSitesBySiteTypeId(List<string> typeIds)
    {
       return await _dbContext.SiteTypes.Active().AsNoTracking()
            .Where(x => typeIds.Contains(x.ReferenceId))
            .Select(x => new SiteType
            {
                ReferenceId=x.ReferenceId,                            
                Category=x.Category
            }).ToListAsync();
       
    }
    private IQueryable<SiteType> SelectSiteType(IQueryable<SiteType> query)
    {
        return query.Select(x => new SiteType
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Category = x.Category,
            Icon = x.Icon,
            IsDelete = x.IsDelete,
            Type = x.Type
        });
    }
}