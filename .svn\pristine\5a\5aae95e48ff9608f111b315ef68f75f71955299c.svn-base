using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DataSyncOptions.Events.Update;

public class DataSyncOptionsUpdatedEventHandler : INotificationHandler<DataSyncOptionsUpdatedEvent>
{
    private readonly ILogger<DataSyncOptionsUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DataSyncOptionsUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DataSyncOptionsUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DataSyncOptionsUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DataSyncProperties",
            Entity = "DataSyncProperties",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DataSync '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DataSync '{updatedEvent.Name}' updated successfully.");
    }
}