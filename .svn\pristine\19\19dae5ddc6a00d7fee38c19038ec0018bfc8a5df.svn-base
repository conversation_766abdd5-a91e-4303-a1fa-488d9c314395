﻿using ContinuityPatrol.Web.Areas.Admin.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LicenseHistoryControllerTests
    {
        private readonly LicenseHistoryController _controller;

        public LicenseHistoryControllerTests()
        {
            _controller = new LicenseHistoryController();
        }

        [Fact]
        public void List_ShouldReturnViewResult()
        {
            var result = _controller.List();

            Assert.IsType<ViewResult>(result);
        }
    }
}
