using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FiaImpactTypeFixture : IDisposable
{
    public List<FiaImpactType> FiaImpactTypePaginationList { get; set; }
    public List<FiaImpactType> FiaImpactTypeList { get; set; }
    public FiaImpactType FiaImpactTypeDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public FiaImpactTypeFixture()
    {
        var fixture = new Fixture();

        FiaImpactTypeList = fixture.Create<List<FiaImpactType>>();

        FiaImpactTypePaginationList = fixture.CreateMany<FiaImpactType>(20).ToList();

        FiaImpactTypeDto = fixture.Create<FiaImpactType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
