using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;

namespace ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;

public class CreateCyberComponentGroupCommandValidator : AbstractValidator<CreateCyberComponentGroupCommand>
{
    private readonly ICyberComponentGroupRepository _cyberComponentGroupRepository;

    public CreateCyberComponentGroupCommandValidator(ICyberComponentGroupRepository cyberComponentGroupRepository)
    {
        _cyberComponentGroupRepository = cyberComponentGroupRepository;

        RuleFor(x => x.GroupName)
           .NotNull().WithMessage("{PropertyName} is required.")
           .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
           .WithMessage("Please Enter Valid {PropertyName}")
           .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");
        RuleFor(x => x)
           .MustAsync(CyberComponentGroupAndSiteNameUnique)
           .WithMessage("A same name already exists.");
        RuleFor(x => x)
         .MustAsync(IsValidGuid)
         .WithMessage("Invalid Id.");


        RuleFor(x => x.SiteName).NotNull().WithMessage("{PropertyName} is required.")
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.");


    }
    private async Task<bool> CyberComponentGroupAndSiteNameUnique(CreateCyberComponentGroupCommand c, CancellationToken token)
    {
        return !await _cyberComponentGroupRepository.IsNameExist(c.GroupName,string.Empty);
    }

    private Task<bool> IsValidGuid(CreateCyberComponentGroupCommand c, CancellationToken cancellationToken)
    {
        try
        {
            Guard.Against.InvalidGuidOrEmpty(c.SiteId, "SiteId");
            return Task.FromResult(true);
        }
        catch (Exception)
        {
            return Task.FromResult(false);
        }
    }
}
