﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ICredentialProfileRepository : IRepository<CredentialProfile>
{
    Task<List<CredentialProfile>> GetCredentialProfileNames();

    Task<bool> IsCredentialProfileNameExist(string name, string id);

    Task<bool> IsCredentialProfileNameUnique(string name);

    Task<List<CredentialProfile>> GetType(string type);

    IQueryable<CredentialProfile> GetCredentialProfileByType(string type);
    Task<PaginatedResult<CredentialProfile>> GetCredentialProfileByType(int pageNumber, int pageSize, Specification<CredentialProfile> specification, string type,string sortColumn, string sortOrder);
}