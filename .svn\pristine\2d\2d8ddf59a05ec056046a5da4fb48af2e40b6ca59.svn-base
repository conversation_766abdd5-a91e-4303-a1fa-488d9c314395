﻿using ContinuityPatrol.Application.Features.PluginManager.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManager.Commands;

public class CreatePluginManagerTests : IClassFixture<PluginManagerFixture>, IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerFixture _pluginManagerFixture;

    private readonly Mock<IPluginManagerRepository> _mockPluginManagerRepository;

    private readonly CreatePluginManagerCommandHandler _handler;

    public CreatePluginManagerTests(PluginManagerFixture pluginManagerFixture, PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;

        var mockPublisher = new Mock<IPublisher>();

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockVersionManager = new Mock<IVersionManager>();

        _mockPluginManagerRepository = PluginManagerRepositoryMocks.CreatePluginManagerRepository(_pluginManagerFixture.PluginManagers);

        var mockPluginManagerHistoryRepository = PluginManagerHistoryRepositoryMocks.CreatePluginManagerHistoryRepository(pluginManagerHistoryFixture.PluginManagerHistories);

        _handler = new CreatePluginManagerCommandHandler(_pluginManagerFixture.Mapper, _mockPluginManagerRepository.Object, mockLoggedInUserService.Object, mockPublisher.Object, mockVersionManager.Object, mockPluginManagerHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_IncreasePluginManagerCount_When_AddValidPluginManager()
    {
        await _handler.Handle(_pluginManagerFixture.CreatePluginManagerCommand, CancellationToken.None);

        var allCategories = await _mockPluginManagerRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_pluginManagerFixture.PluginManagers.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulPluginManagerResponse_When_AddValidPluginManager()
    {
        var result = await _handler.Handle(_pluginManagerFixture.CreatePluginManagerCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreatePluginManagerResponse));

        result.PluginId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_pluginManagerFixture.CreatePluginManagerCommand, CancellationToken.None);

        _mockPluginManagerRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.PluginManager>()), Times.Once);
    }
}
