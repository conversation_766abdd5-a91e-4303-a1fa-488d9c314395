const hacmpClusterUrl = {
    clusterExistUrl: "Configuration/HACMPCluster/IsClusterNameExist",
    clusterSaveUrl: "Configuration/HACMPCluster/CreateOrUpdate",
    clusterDeleteUrl: "Configuration/HACMPCluster/Delete",
    paginationUrl: "/Configuration/HACMPCluster/GetPagination"
}
let selectedValues = [];


let permission = {
    create: $("#hacmpConfigCreate").data("create-permission")?.toLowerCase(),
    delete: $("#hacmpConfigDelete").data("delete-permission")?.toLowerCase()
}

if (permission.create == 'false') $("#hacmpCreateBtn").addClass('btn-disabled').css('pointer-events', 'none');


let dataTable = $('#hacmpClusterTable').DataTable(
    {
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            },
            infoFiltered: ""
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",
            "url": hacmpClusterUrl.paginationUrl,
            "dataType": "json",
            "data": function (d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#hacmpSearchInp').val() : selectedValues?.join(';');
                selectedValues.length = 0;
            },
            "dataSrc": function (json) {
                json.recordsFiltered = json?.data?.totalCount;
                json.recordsTotal = json?.data?.totalPages;
                if (json?.success && Array.isArray(json?.data?.data) && json?.data?.data?.length) {
                    $(".pagination-column").removeClass("disabled");
                    return json?.data?.data;
                } else {
                    $(".pagination-column").addClass("disabled");
                    if (!json?.success && json?.message) {
                        $('.dataTables_empty').text('No Data Found');
                        notificationAlert('warning', json?.message);
                    }
                    return [];
                }
            }
        },
        "columnDefs": [
            {
                "targets": [1, 3, 4, 5],
                "className": "truncate"
            }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                orderable: false,
                "render": function (data, type, row, meta) {
                    return data;
                },
                orderable: false
            },
            {
                "data": "name", "name": "Name", "autoWidth": true,
                "render": function (data, type, row) {
                    return `<span title="${data || 'NA'}">${data || 'NA'}</span>`;
                }
            },
            {
                "data": "serverName", "name": "Server Name", "autoWidth": true,
                "render": function (data, type, row) {
                    return data;
                }
            },
            {
                "data": "lssrcPath", "name": "LSSRC Path", "autoWidth": true,
                "render": function (data, type, row) {
                    return `<span title="${data || 'NA'}">${data || 'NA'}</span>`;
                }
            }, {
                "data": "clrgInfoPath", "name": "CLRG Path", "autoWidth": true,
                "render": function (data, type, row) {
                    return `<span title="${data || 'NA'}">${data || 'NA'}</span>`;
                }
            }, {
                "data": "resourceGroupName", "name": "Group Name", "autoWidth": true,
                "render": function (data, type, row) {
                    return `<span title="${data || 'NA'}">${data || 'NA'}</span>`;
                }
            },
            {
                "render": function (data, type, row) {
                    return `<div class="d-flex align-items-center gap-2"> ${permission.create ? `<span role="button" title="Edit" class="editbutton" data-cluster='${btoa(JSON.stringify(row || 'NA'))}'><i class="cp-edit"></i></span>` : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`} 
                        ${permission.delete ? `<span role="button" title="Delete" class="deletebutton" data-cluster-id="${row.id || 'NA'}" data-cluster-name="${row.name || 'NA'}"><i class="cp-Delete"></i></span>` : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`}</div>`;
                },
                "orderable": false
            }
        ],
        "rowCallback": function (row, data, index) {
            let api = this?.api();
            let startIndex = api?.context[0]?._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });

$('#hacmpSearchInp').on('keydown input', function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
    handleSearchDebounced();
});

const handleSearchDebounced = commonDebounce(function () {

    const inputValue = $('#hacmpSearchInp')?.val();
    const fields = ['#Name', '#serverName', '#Lssrcpath', '#Clrgpath', '#groupValue'];
    const fieldslength = fields?.length;
    for (let i = 0; i < fieldslength; i++) {
        const element = $(fields[i])?.get(0);
        if (element?.checked) {
            const value = $(element)?.val();
            if (value) {
                selectedValues.push(value + inputValue);
            }
        }
    }
    dataTable.ajax.reload(function (json) {
        if (json.recordsFiltered === 0) {
            $('.dataTables_empty').text('No matching records found');
        }
    });
}, 500);

//Validation
async function validateFields(value, valueid, errorElement, errorMsg, checkExist = false) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            SpecialCharValidate(value), ShouldNotBeginWithSpace(value), ShouldNotBeginWithUnderScore(value),
            OnlyNumericsValidate(value), ShouldNotBeginWithNumber(value), ShouldNotEndWithSpace(value),
            ShouldNotAllowMultipleSpace(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value),
            MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value), minMaxlength(value), secondChar(value)
        ];
        if (checkExist) {
            let data = { name: value, id: valueid };
            validationResults.push(await IsNameExist(RootUrl + hacmpClusterUrl.clusterExistUrl, data, OnError));
        }
        return CommonValidation(errorElement, validationResults);
    }
}

async function IsNameExist(url, data, errorFunc) {
    return !data?.name?.trim() ? true : (await getAysncWithHandler(url, data, errorFunc)) ? "Name already exists" : true;
}
function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

async function validatePath(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await InvalidPathRegex(value)
    ];
    return await CommonValidation(errorElement, validationResults);
}

function populateHACMPFields(ClusterData) {
    $('#hacmpName').val(ClusterData?.name).attr('clusterNameId', ClusterData?.id);
    $('#hacmpServerDrpdwn').val(ClusterData?.serverName).attr('hacmpServerId', ClusterData?.serverId);
    $('#hacmpLSSRCPath').val(ClusterData?.lssrcPath);
    $('#hacmpCLRGPath').val(ClusterData?.clrgInfoPath);
    $('#hacmpGroupName').val(ClusterData?.resourceGroupName);
}

//Save button on click
$('#SaveFunction').on('click', async function () {
    let name = $('#hacmpName').val();
    let nameId = $('#hacmpName').attr('clusterNameId');
    let serverName = $('#hacmpServerDrpdwn').val();
    let serverId = $('#hacmpServerDrpdwn').attr('hacmpServerId');
    let lssrcPath = $('#hacmpLSSRCPath').val();
    let clrgPath = $('#hacmpCLRGPath').val();
    let groupName = $('#hacmpGroupName').val();
    let isName = await validateFields(name, nameId, $('#hacmpNameError'), 'Enter HACMP cluster name', true);
    let isgroupName = await validateFields(groupName, null, $('#hacmpGroupNameError'), 'Enter resource group name');
    let isServerName = await validateDropDown(serverName, 'Select server', 'hacmpServerError');
    let islssrcPath = await validatePath(lssrcPath, 'Enter LSSRC path', $('#hacmpLSSRCPathError'));
    let isclrgPath = await validatePath(clrgPath, 'Enter CLRG info path', $('#hacmpCLRGPathError'));

    if (isName && isServerName && islssrcPath && isclrgPath && isgroupName) {

        sanitizeContainer(['hacmpName', 'hacmpServerDrpdwn', 'hacmpLSSRCPath', 'hacmpCLRGPath', 'hacmpGroupName']);

        let savedata = { Id: nameId, Name: name, ServerId: serverId, ServerName: serverName, LSSRCPath: lssrcPath, CLRGInfoPath: clrgPath, ResourceGroupName: groupName, __RequestVerificationToken: gettoken() };

        await $.ajax({
            url: RootUrl + hacmpClusterUrl.clusterSaveUrl,
            type: "POST",
            dataType: "json",
            data: savedata,
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#hacmpCreateModal').modal('hide');
                        dataTable.ajax.reload()
                    }, 1000);
                } else {
                    $('#hacmpCreateModal').modal('hide');
                    errorNotification(response);
                }
            }
        });
    }
})

//Update icon on click
$('#hacmpClusterTable').on('click', '.editbutton', function () {
    let ClusterData = JSON.parse(atob($(this).data("cluster")));
    if (ClusterData) {
        populateHACMPFields(ClusterData);
        $('#hacmpNameError,#hacmpLSSRCPathError,#hacmpCLRGPathError,#hacmpGroupNameError,#hacmpServerError').text('').removeClass('field-validation-error');
        $('#SaveFunction').text('Update');
        $('#hacmpCreateModal').modal('show');
    }
});

//Delete icon on click
$('#hacmpClusterTable').on('click', '.deletebutton', function () {
    let clusterId = $(this).data('cluster-id');
    let clusterName = $(this).data('cluster-name');
    if (clusterId) {
        $('#hacmpDeleteId').text(clusterName).attr('title', clusterName).val(clusterId);
        $('#hacmpDeleteModal').modal('show');
    }
});

//Confirm delete on click
$('#hacmpConfirmDeleteBtn').on('click', async function () {
    let deleteid = $('#hacmpDeleteId').val();
    if (deleteid) {
        await $.ajax({
            url: RootUrl + hacmpClusterUrl.clusterDeleteUrl,
            type: "DELETE",
            dataType: "json",
            data: { id: deleteid, __RequestVerificationToken: gettoken() },
            success: function (response) {
                if (response && response?.success && response?.data?.message) {
                    notificationAlert("success", response?.data?.message);
                    setTimeout(() => {
                        $('#hacmpDeleteModal').modal('hide');
                        dataTable.ajax.reload()
                    }, 1000);
                } else {
                    errorNotification(response);
                    $('#hacmpDeleteModal').modal('hide');
                }
            }
        });
    }
});

//Clear previous data
$("#hacmpCreateBtn").on('click', function () {
    $('#hacmpName').val('').attr('clusterNameId', "");
    $('#hacmpServerDrpdwn').val('').attr('hacmpServerId', "");
    $('#hacmpLSSRCPath, #hacmpCLRGPath, #hacmpGroupName').val('');
    $('#hacmpNameError,#hacmpLSSRCPathError,#hacmpCLRGPathError,#hacmpGroupNameError,#hacmpServerError').text('').removeClass('field-validation-error');
    $('#SaveFunction').text('Save');
    $('#hacmpCreateModal').modal('show');
});

// Feild Validations
$('#hacmpName').on('keyup', commonDebounce(async function () {
    const sanitizedValue = $(this).val()?.replace(/\s{2,}/g, ' ');
    let nameId = $('#hacmpName').attr('clusterNameId');
    $(this).val(sanitizedValue);
    await validateFields(sanitizedValue, nameId, $('#hacmpNameError'), 'Enter HACMP cluster name', true);
}, 400))

$('#hacmpServerDrpdwn').on('change', function () {
    const value = $(this).val();
    let id = $(this).children(":selected").attr("id");
    $('#hacmpServerDrpdwn').attr('hacmpServerId', id);
    validateDropDown(value, 'Select server', 'hacmpServerError');
});

$('#hacmpLSSRCPath, #hacmpCLRGPath, #hacmpGroupName').on('keyup', async function () {
    const id = $(this).attr('id');
    let sanitizedValue = $(this).val().replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (id === 'hacmpLSSRCPath') {
        validatePath(sanitizedValue, 'Enter LSSRC path', $('#hacmpLSSRCPathError'));
    } else if (id === 'hacmpCLRGPath') {
        validatePath(sanitizedValue, 'Enter CLRG info path', $('#hacmpCLRGPathError'));
    } else if (id === 'hacmpGroupName') {
        await validateFields(sanitizedValue, null, $('#hacmpGroupNameError'), 'Enter resource group name');
    }
});
