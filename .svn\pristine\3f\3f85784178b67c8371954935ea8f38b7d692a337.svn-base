﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Site.Commands.Update;

namespace ContinuityPatrol.Application.Features.ServerLog.Commands.Update;

public class UpdateServerLogCommandValidator
{
    private readonly IServerLogRepository _serverLogRepository;

    public UpdateServerLogCommandValidator(IServerLogRepository serverLogRepository)
    {
        this._serverLogRepository = serverLogRepository;
    }

    private async Task<bool> ServerNameUnique(UpdateServerLogCommand e, CancellationToken token)
    {
        return !await _serverLogRepository.IsServerLogNameUnique(e.Name, e.Id);
    }
}