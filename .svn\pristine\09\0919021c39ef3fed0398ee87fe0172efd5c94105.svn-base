﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetPaginatedList;

public class GetMYSQLMonitorStatusPaginatedListQueryHandler : IRequestHandler<GetMYSQLMonitorStatusPaginatedListQuery,
    PaginatedResult<MYSQLMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorStatusRepository _mysqlMonitorStatusRepository;

    public GetMYSQLMonitorStatusPaginatedListQueryHandler(IMysqlMonitorStatusRepository mysqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mysqlMonitorStatusRepository = mysqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MYSQLMonitorStatusListVm>> Handle(GetMYSQLMonitorStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _mysqlMonitorStatusRepository.PaginatedListAllAsync();

        var productFilterSpec = new MySqlMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MYSQLMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}