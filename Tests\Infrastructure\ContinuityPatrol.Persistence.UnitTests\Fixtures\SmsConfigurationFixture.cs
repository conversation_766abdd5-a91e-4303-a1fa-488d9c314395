using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SmsConfigurationFixture : IDisposable
{
    public List<SmsConfiguration> SmsConfigurationPaginationList { get; set; }
    public List<SmsConfiguration> SmsConfigurationList { get; set; }
    public SmsConfiguration SmsConfigurationDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public SmsConfigurationFixture()
    {
        var fixture = new Fixture();

        SmsConfigurationList = fixture.Create<List<SmsConfiguration>>();

        SmsConfigurationPaginationList = fixture.CreateMany<SmsConfiguration>(20).ToList();

        SmsConfigurationDto = fixture.Create<SmsConfiguration>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SmsConfiguration CreateSmsConfiguration(
        string url = "https://default.sms.com",
        string senderId = "DEFAULT_SENDER",
        string userName = "defaultuser",
        string password = "defaultpass",
        string recipientNo = "**********",
        string properties = null,
        bool isActive = true,
        bool isDelete = false)
    {
        return new SmsConfiguration
        {
            ReferenceId = Guid.NewGuid().ToString(),
            URL = url,
            SenderId = senderId,
            UserName = userName,
            Password = password,
            RecipientNo = recipientNo,
            Properties = properties ?? "{\"timeout\": 30, \"retries\": 3}",
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SmsConfiguration> CreateMultipleSmsConfigurations(int count)
    {
        var configurations = new List<SmsConfiguration>();
        for (int i = 1; i <= count; i++)
        {
            configurations.Add(CreateSmsConfiguration(
                url: $"https://sms{i}.example.com",
                senderId: $"SENDER_{i}",
                userName: $"user{i}",
                password: $"pass{i}",
                recipientNo: $"123456789{i}",
                properties: $"{{\"timeout\": {30 + i}, \"retries\": {i}}}"
            ));
        }
        return configurations;
    }

    public SmsConfiguration CreateSmsConfigurationWithSpecificId(string referenceId, string url = "https://test.sms.com")
    {
        return new SmsConfiguration
        {
            ReferenceId = referenceId,
            URL = url,
            SenderId = "TEST_SENDER",
            UserName = "testuser",
            Password = "testpass",
            RecipientNo = "**********",
            Properties = "{\"timeout\": 30}",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SmsConfiguration CreateSmsConfigurationForProvider(string provider, string url = null)
    {
        return CreateSmsConfiguration(
            url: url ?? $"https://{provider.ToLower()}.sms.com",
            senderId: $"{provider.ToUpper()}_SENDER",
            userName: $"{provider.ToLower()}user",
            password: $"{provider.ToLower()}pass"
        );
    }

    public List<SmsConfiguration> CreateSmsConfigurationsWithStatus(int activeCount, int inactiveCount)
    {
        var configurations = new List<SmsConfiguration>();

        for (int i = 1; i <= activeCount; i++)
        {
            configurations.Add(CreateSmsConfiguration(
                url: $"https://active{i}.sms.com",
                senderId: $"ACTIVE_{i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            configurations.Add(CreateSmsConfiguration(
                url: $"https://inactive{i}.sms.com",
                senderId: $"INACTIVE_{i}",
                isActive: false
            ));
        }

        return configurations;
    }

    public SmsConfiguration CreateSmsConfigurationWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSmsConfiguration(properties: propertiesJson);
    }

    public SmsConfiguration CreateSmsConfigurationForTesting(
        string testName,
        string url = null,
        string senderId = null)
    {
        return CreateSmsConfiguration(
            url: url ?? $"https://{testName.ToLower()}.test.com",
            senderId: senderId ?? $"TEST_{testName.ToUpper()}",
            userName: $"{testName.ToLower()}_user",
            password: $"{testName.ToLower()}_pass",
            recipientNo: $"555{testName.GetHashCode().ToString().Substring(0, 7)}"
        );
    }

    public List<SmsConfiguration> CreateSmsConfigurationsForProviders(List<string> providers)
    {
        var configurations = new List<SmsConfiguration>();
        foreach (var provider in providers)
        {
            configurations.Add(CreateSmsConfigurationForProvider(provider));
        }
        return configurations;
    }

    public SmsConfiguration CreateMinimalSmsConfiguration()
    {
        return new SmsConfiguration
        {
            ReferenceId = Guid.NewGuid().ToString(),
            URL = "https://minimal.sms.com",
            SenderId = "MIN",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
