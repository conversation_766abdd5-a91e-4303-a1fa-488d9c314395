﻿using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowExecutionEventLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowExecutionEventLog.Queries;

public class GetWorkflowExecutionEventLogPaginatedListQueryHandlerTests : IClassFixture<WorkflowExecutionEventLogFixture>
{
    private readonly WorkflowExecutionEventLogFixture _workflowExecutionEventLogFixture;

    private readonly Mock<IWorkflowExecutionEventLogRepository> _mockWorkflowExecutionEventLogRepository;

    private readonly GetWorkflowExecutionEventLogPaginatedListQueryHandler _handler;

    public GetWorkflowExecutionEventLogPaginatedListQueryHandlerTests(WorkflowExecutionEventLogFixture workflowExecutionEventLogFixture)
    {
        _workflowExecutionEventLogFixture = workflowExecutionEventLogFixture;

        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].LoginName = "Admin";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowActionName = "DGSwitchLogFile_DR";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].CompanyId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].UserEvent = "WorkflowModified";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Status = "Success";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Message = "Running";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationId = "9a9bd699-3b01-4b52-8508-97e6868565d0";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationGroupId = "33cc06d8-6962-4537-816f-48656f207fda";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].InfraObjectId = "c81bbada-e368-401d-acb5-2c0f6fe04eba";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ActionId = "e8d01f23-31dc-43de-bb23-3c5a83e951e0";

        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].LoginName = "Admin_123";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].WorkflowActionName = "DGSwitchLogFile_DR_123";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].CompanyId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].UserEvent = "WorkflowModified_123";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].Status = "Success_123";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].Message = "Running_123";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].WorkflowOperationId = "9a9bd699-3b01-4b52-8508-97e6868565d0";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[1].WorkflowOperationGroupId = "33cc06d8-6962-4537-816f-48656f207fda";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].InfraObjectId = "c81bbada-e368-401d-acb5-2c0f6fe04eba";
        _workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ActionId = "e8d01f23-31dc-43de-bb23-3c5a83e951e0";

        _mockWorkflowExecutionEventLogRepository = WorkflowExecutionEventLogRepositoryMocks.GetPaginatedWorkflowExecutionEventLogRepository(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs);
        _handler = new GetWorkflowExecutionEventLogPaginatedListQueryHandler(_workflowExecutionEventLogFixture.Mapper, _mockWorkflowExecutionEventLogRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowExecutionEventLogPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowExecutionEventLogListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowExecutionEventLog_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowExecutionEventLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "login name=Admin;workflow action name=DGSwitchLogFile_DR;Status=Success" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowExecutionEventLogListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].LoginName.ShouldBe("Admin");
        result.Data[0].WorkflowActionName.ShouldBe("DGSwitchLogFile_DR");
        result.Data[0].CompanyId.ShouldBe("5287bf71-be04-4c55-97e8-a65b7ff17114");
        result.Data[0].Status.ShouldBe("Success");
        result.Data[0].UserEvent.ShouldBe("WorkflowModified");
        result.Data[0].Message.ShouldBe("Running");
        result.Data[0].WorkflowOperationId.ShouldBe("9a9bd699-3b01-4b52-8508-97e6868565d0");
        result.Data[0].WorkflowOperationGroupId.ShouldBe("33cc06d8-6962-4537-816f-48656f207fda");
        result.Data[0].InfraObjectId.ShouldBe("c81bbada-e368-401d-acb5-2c0f6fe04eba");
        result.Data[0].ActionId.ShouldBe("e8d01f23-31dc-43de-bb23-3c5a83e951e0");

    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowExecutionEventLog_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowExecutionEventLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Admin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowExecutionEventLogListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].LoginName.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].LoginName);
        result.Data[0].WorkflowActionName.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowActionName);
        result.Data[0].CompanyId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].CompanyId);
        result.Data[0].UserEvent.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].UserEvent);
        result.Data[0].Status.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Status);
        result.Data[0].Message.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].Message);
        result.Data[0].WorkflowOperationId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationId);
        result.Data[0].WorkflowOperationGroupId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].WorkflowOperationGroupId);
        result.Data[0].InfraObjectId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].InfraObjectId);
        result.Data[0].ActionId.ShouldBe(_workflowExecutionEventLogFixture.WorkflowExecutionEventLogs[0].ActionId);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowExecutionEventLogPaginatedListQuery() { PageNumber = 1, PageSize = 10, SearchString = "Admin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowExecutionEventLogListVm>>();

        result.TotalCount.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowExecutionEventLogPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowExecutionEventLogRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
