{"MssqlAlwaysOn": {"Path": {"PR": ["PrAlwaysOnMonitoringModel", "PrMonitoringModel"], "DR": ["AlwaysOnMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Edition", "label": "MSSQL Server Edition"}, {"key": "PR_Server_IpAddress", "label": "IP Address"}, {"key": "PR_Server_HostName", "label": "Host Name"}, {"key": "PR_DataBase_Synchroniztion_State", "label": "Synchronization Health Status"}, {"key": "PR_DataBase_State", "label": "Database Status"}]}, {"type": "DR", "keys": [{"key": "Server_Edition", "label": "MSSQL Server Edition"}, {"key": "Server_IpAddress", "label": "IP Address"}, {"key": "Server_HostName", "label": "Host Name"}, {"key": "DataBase_Synchroniztion_State", "label": "Synchronization Health Status"}, {"key": "DataBase_State", "label": "Database Status"}]}]}, {"type": "replication", "prOnlyKeys": ["PR_Datalag", "replicationType"], "keys": [{"type": "PR", "keys": [{"key": "PR_Availability_Group_Name", "label": "Availability Group Name"}, {"key": "PR_Database", "label": "Database Name"}, {"key": "PR_Availability_Group_Role", "label": "Availability Group Role"}, {"key": "PR_Availability_Mode", "label": "Replica Mode"}, {"key": "PR_Availability_Group_Connnected_State", "label": "Availability Group Connnected State"}]}, {"type": "DR", "keys": [{"key": "Availability_Group_Name", "label": "Availability Group Name"}, {"key": "Database", "label": "Database Name"}, {"key": "Availability_Group_Role", "label": "Availability Group Role"}, {"key": "Availability_Mode", "label": "Replica Mode"}, {"key": "Availability_Group_Connnected_State", "label": "Availability Group Connnected State"}]}]}]}, "mssqldbmirroring": {"Path": {"PR": ["PrMSSQLDBMirroringModel"], "DR": ["MSSQLDBMirroringModel"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PRServerName", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/HostName"}, {"key": "PrDatabaseName", "label": "Database Name"}, {"key": "PR_Server_NetworkAddress", "label": "Server Network Address"}, {"key": "PROpreationMode", "label": "Opreation Mode"}, {"key": "PRDBRole", "label": "Role Of DB"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}, {"key": "DatabaseName", "label": "Database Name"}, {"key": "Server_NetworkAddress", "label": "Server Network Address"}, {"key": "OpreationMode", "label": "Opreation Mode"}, {"key": "DBRole", "label": "Role Of DB"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "PRMirroringState", "label": "Monitoring State"}, {"key": "PRLogGenerateRate", "label": "Log Generate Rate (Kbytes/Sec)"}, {"key": "PRUnsentLog", "label": "UnSent Log (Kbytes)"}, {"key": "PRSentRate", "label": "Log Sent Rate (Kb/Sec)"}, {"key": "PRUnrestoredLog", "label": "Unrestored Queue Log Value (Kbytes)"}, {"key": "PRRecoveryRate", "label": "Log Recovery Rate (Kbytes/Sec)"}, {"key": "PRTransactionDelay", "label": "Transaction Delay (Milisec)"}, {"key": "PRTransactionPerSecond", "label": "Transaction PerSecond (Trans/Sec)"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "MirroringState", "label": "Monitoring State"}, {"key": "LogGenerateRate", "label": "Log Generate Rate (Kbytes/Sec)"}, {"key": "UnsentLog", "label": "UnSent Log (Kbytes)"}, {"key": "SentRate", "label": "Log Sent Rate (Kb/Sec)"}, {"key": "UnrestoredLog", "label": "Unrestored Queue Log Value (Kbytes)"}, {"key": "RecoveryRate", "label": "Log Recovery Rate (Kbytes/Sec)"}, {"key": "TransactionDelay", "label": "Transaction Delay (Milisec)"}, {"key": "TransactionPerSecond", "label": "Transaction PerSecond (Trans/Sec)"}]}]}]}, "MssqlNLS": {"Path": {"PR": ["PrNlsMonitoringModel", "MonitoringModel"], "DR": ["NlsOnMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/HostName"}, {"key": "PR_Database_Name", "label": "Database Name"}, {"key": "PR_Last_Backup_Transaction_Log", "label": "Last Generated Transaction Log"}, {"key": "PR_Last_Restored_Transaction_Log", "label": "Last Applied Transaction Log"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}, {"key": "Database_Name", "label": "Database Name"}, {"key": "Last_Backup_Transaction_Log", "label": "Last Generated Transaction Log"}, {"key": "Last_Restored_Transaction_Log", "label": "Last Applied Transaction Log"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "PRLastLSN_backup", "label": "LSN of Last Backup Log"}, {"key": "PRLastLSN_copied", "label": "LSN of Last Copied Log"}, {"key": "PRlast_copied_file", "label": "Last Copied Log"}, {"key": "PRLastLSN_restored", "label": "LSN of Last Restored Log"}]}, {"type": "DR", "keys": [{"key": "LastLSN_backup", "label": "LSN of Last Backup Log"}, {"key": "LastLSN_copied", "label": "LSN of Last Copied Log"}, {"key": "last_copied_file", "label": "Last Copied Log"}, {"key": "LastLSN_restored", "label": "LSN of Last Restored Log"}]}]}]}, "Postgres": {"Path": {"PR": ["PrPostgresMonitoringModel", "MonitoringModel"], "DR": ["PostgresMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_IpAddress", "label": "IP Address/HostName"}, {"key": "PR_Database", "label": "Database Name"}, {"key": "PR_DatabaseClusterStatus", "label": "Database Cluster state"}, {"key": "PR_RecoveryStatus", "label": "Database Recovery State"}]}, {"type": "DR", "keys": [{"key": "Server_IpAddress", "label": "IP Address/HostName"}, {"key": "Database", "label": "Database Name"}, {"key": "DatabaseClusterStatus", "label": "Database Cluster state"}, {"key": "RecoveryStatus", "label": "Database Recovery State"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "PR_ReplicationStatus", "label": "Replication Status"}, {"key": "CurrentWalLsnPR", "label": "Current WAL Log Location"}, {"key": "PR_LastWalReceiveLsn", "label": "Last WAL Log Receive Location"}, {"key": "PR_LastWalReplayLsnPR", "label": "Last WAL Log Replay Location"}, {"key": "PR_DataLagInSize", "label": "Datalag(in MB)"}, {"key": "PR_Datalag", "label": "Datalag(hh:mm:ss)"}]}, {"type": "DR", "keys": [{"key": "ReplicationStatus", "label": "Replication Status"}, {"key": "CurrentWalLsn", "label": "Current WAL Log Location"}, {"key": "LastWalReceiveLsn", "label": "Last WAL Log Receive Location"}, {"key": "LastWalReplayLsnDR", "label": "Last WAL Log Replay Location"}]}]}]}, "Mysql": {"Path": {"PR": ["PrMySqlMonitoringModel", "MonitoringModel"], "DR": ["MySqlMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PR_Database", "label": "Database Name"}, {"key": "PR_Master_Log_File", "label": "Master Log File"}, {"key": "PR_Relay_Master_Log_File", "label": "Relay Master Log File"}, {"key": "PR_Master_Log_Position", "label": "Maste Log Position"}, {"key": "PR_Exec_Master_Log_Position", "label": "Exec Master Log Position"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "Database", "label": "Database Name"}, {"key": "Master_Log_File", "label": "Master Log File"}, {"key": "Relay_Master_Log_File", "label": "Relay Master Log File"}, {"key": "Master_Log_Position", "label": "Maste Log Position"}, {"key": "Exec_Master_Log_Position", "label": "Exec Master Log Position"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "PRReplication_Connect_State", "label": "Replication Connect State"}, {"key": "PR_Datalag", "label": "Datalag"}]}, {"type": "DR", "keys": [{"key": "Replication_Connect_State", "label": "Replication Connect State"}]}]}]}, "Oracle": {"Path": {"PR": ["PrOracleDataGuardModel", "PrMonitoringModel"], "DR": ["OracleDataGuardModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PR_Database_Sid", "label": "Database Name"}, {"key": "PR_Log_sequence", "label": "Master Log File"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "Database_Sid", "label": "Database Name"}, {"key": "Log_sequence", "label": "Master Log File"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "PR_Replication_Mode", "label": "Replication Mode"}, {"key": "PR_Services", "label": "Service Name"}, {"key": "PR_Protection_mode", "label": "Protection Mode"}, {"key": "PR_Dataguard_status", "label": "Dataguard Status"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "Replication_Mode", "label": "Replication Mode"}, {"key": "Services", "label": "Service Name"}, {"key": "Protection_mode", "label": "Protection Mode"}, {"key": "Dataguard_status", "label": "Dataguard Status"}]}]}]}, "MongoDB": {"Path": {"PR": ["PRMongoDBMonitoringPRModel", "PRMongoDBMonitoringModel"], "DR": ["MongoDBMonitoringModel"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PRDBName", "label": "Database Name"}, {"key": "PRMongodbStatus", "label": "MongoDB Status"}, {"key": "PR_Server_HostName", "label": "Host Name"}, {"key": "PRDBVersion", "label": "Version"}, {"key": "PRStateDescription", "label": "State Description"}, {"key": "PRhealth", "label": "Health"}, {"key": "PRlastHeartbeatMessage", "label": "Last Heartbeat Message"}]}, {"type": "DR", "keys": [{"key": "Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "DBName", "label": "Database Name"}, {"key": "MongodbStatus", "label": "MongoDB Status"}, {"key": "Server_HostName", "label": "Host Name"}, {"key": "DBVersion", "label": "Version"}, {"key": "StateDescription", "label": "State Description"}, {"key": "health", "label": "Health"}, {"key": "lastHeartbeatMessage", "label": "Last Heartbeat Message"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "PRreplicaSetName", "label": "ReplicaSet Name"}, {"key": "PRmemberID", "label": "MemberID"}, {"key": "PRCurrentPriority", "label": "Current Priority"}, {"key": "Datalag", "label": "Datalag"}]}, {"type": "DR", "keys": [{"key": "replicaSetName", "label": "ReplicaSet Name"}, {"key": "memberID", "label": "MemberID"}, {"key": "CurrentPriority", "label": "Current Priority"}]}]}]}, "NetAppSnapMirror": {"Path": {"PR": ["PRNetAppSnapMirrorMonitoring", "PRNetAppSnapMirrorMonitor"], "DR": ["NetAppSnapMirrorMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}, {"key": "StorageId", "label": "Storage Id"}, {"key": "Volume", "label": "Volume"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}, {"key": "StorageId", "label": "Storage Id"}, {"key": "Volume", "label": "Volume"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "Source", "label": "Source"}, {"key": "Destination", "label": "Destination"}, {"key": "State", "label": "State"}, {"key": "Lag", "label": "Lag"}, {"key": "Status", "label": "Status"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "Source", "label": "Source"}, {"key": "Destination", "label": "Destination"}, {"key": "State", "label": "State"}, {"key": "Lag", "label": "Lag"}, {"key": "Status", "label": "Status"}]}]}]}, "WindowsActiveDirectory": {"Path": {"PR": ["PrActiveDirectoryReplication", "ActiveDirectoryMonitoring"], "DR": ["ActiveDirectoryReplication"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "<PERSON><PERSON><PERSON>", "label": "IP Address"}, {"key": "DomainName", "label": "Domain Name"}, {"key": "DomainControllerName", "label": "Domain Controller Name"}, {"key": "ADReplicationSite", "label": "AD Replication Site"}, {"key": "ReplicationPartnerName", "label": "Partner Name"}]}, {"type": "DR", "keys": [{"key": "<PERSON><PERSON><PERSON>", "label": "IP Address"}, {"key": "DomainName", "label": "Domain Name"}, {"key": "DomainControllerName", "label": "Domain Controller Name"}, {"key": "ADReplicationSite", "label": "AD Replication Site"}, {"key": "ReplicationPartnerName", "label": "Partner Name"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "ConsecutiveReplicationFailures", "label": "Consecutive Replication Failures"}, {"key": "LastReplicationAttempt", "label": "Last Replication Attempt"}, {"key": "LastReplicationSuccess", "label": "Last Replication Success"}, {"key": "LastReplicationResult", "label": "Last Replication Result"}, {"key": "ReplicationFailureFirstRecordedTime", "label": "Replication Failure First Recorded Time"}, {"key": "DataLag", "label": "DataLag"}]}, {"type": "DR", "keys": [{"key": "ConsecutiveReplicationFailures", "label": "Consecutive Replication Failures"}, {"key": "LastReplicationAttempt", "label": "Last Replication Attempt"}, {"key": "LastReplicationSuccess", "label": "Last Replication Success"}, {"key": "LastReplicationResult", "label": "Last Replication Result"}, {"key": "ReplicationFailureFirstRecordedTime", "label": "Replication Failure First Recorded Time"}]}]}]}, "ApplicationNoReplication": {"Path": {"PR": ["PrNoReplicationModel", "PrMonitorModel"], "DR": ["NoReplicationModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/Host Name"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "Hp3par": {"Path": {"PR": ["Hp3ParReplicationPRModel", "Hp3parReplicationPRMonitoring"], "DR": ["Hp3parReplicationDRModel"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}]}, {"type": "DR", "keys": [{"key": "DR_Server_Name", "label": "Server Name"}, {"key": "DR_Server_IpAddress", "label": "IP Address/Host Name"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "RSyncAppReplication": {"Path": {"PR": ["PrRSyncReplicationModel", "PrMonitoringModel"], "DR": ["RSyncReplicationModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "SourcePath", "label": "Source Path"}, {"key": "DestinationPath", "label": "Destination Path"}, {"key": "TotalNumberoffiles", "label": "Total Number of files"}, {"key": "TotalFilesSize", "label": "Total File Size"}, {"key": "NumberOfRegFilesTransfer", "label": "Number Of Regular Files Transferred"}, {"key": "TotalTransferfileSize", "label": "Total Transfer file Size"}]}, {"type": "DR", "keys": [{"key": "SourcePath", "label": "Source Path"}, {"key": "DestinationPath", "label": "Destination Path"}, {"key": "TotalNumberoffiles", "label": "Total Number of files"}, {"key": "TotalFilesSize", "label": "Total File Size"}, {"key": "NumberOfRegFilesTransfer", "label": "Number Of Regular Files Transferred"}, {"key": "TotalTransferfileSize", "label": "Total Transfer file Size"}]}]}]}, "RoboCopy": {"Path": {"PR": ["PrRoboCopyMonitorngModel", "MonitoringModel"], "DR": ["RoboCopyMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/Host Name"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PR_Server_HostName", "label": "Host Name"}, {"key": "MonitoringMode", "label": "No.Of Jobs"}]}, {"type": "DR", "keys": [{"key": "Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "Server_HostName", "label": "Host Name"}]}]}]}, "OpenShift": {"Path": {"PR": [], "DR": []}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "OpenShiftClusterFQDN", "label": "OpenShiftClusterFQDN"}, {"key": "ClusterResourceName", "label": "Cluster Resource Name"}, {"key": "ClusterInfrastructure", "label": "Cluster Infrastructure"}, {"key": "DistributionVersion", "label": "Distribution Version"}, {"key": "ProjectsName", "label": "Projects Name"}]}, {"type": "DR", "keys": []}]}]}, "DB2HADR": {"Path": {"PR": ["MonitoringPRModel", "PRDB2HADRMonitoringModel"], "DR": ["DB2HADRMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_HostName", "label": "Server Host Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PR_Database", "label": "Database Name"}, {"key": "PRDatabaseStatus", "label": "Database Status"}, {"key": "PRLogFile", "label": "Last Log Generated / Last Log Applied"}, {"key": "PRLSN", "label": "LSN"}, {"key": "PRTimestamp", "label": "Time Stamp"}]}, {"type": "DR", "keys": [{"key": "DR_Server_HostName", "label": "Server Host Name"}, {"key": "DR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "DR_Database", "label": "Database Name"}, {"key": "DRDatabaseStatus", "label": "Database Status"}, {"key": "DRLogFile", "label": "Last Log Generated / Last Log Applied"}, {"key": "DRLSN", "label": "LSN"}, {"key": "DRTimestamp", "label": "Time Stamp"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "HRole", "label": "HADR Role"}, {"key": "State", "label": "HADR State"}, {"key": "SyncMode", "label": "Sync Mode"}, {"key": "ConnectionStatus", "label": "Connection Status"}, {"key": "HeartbeatsMissed", "label": "Heart Beat Status"}, {"key": "LocalHost", "label": "Local Host"}, {"key": "LocalService", "label": "Local Service"}, {"key": "RemoteHost", "label": "Remote Host"}, {"key": "RemoteService", "label": "Remote Service"}, {"key": "Timeout", "label": "Timeout"}, {"key": "LogGap", "label": "LogGap"}]}, {"type": "DR", "keys": []}]}]}, "SRM": {"Path": {"PR": [], "DR": []}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Site_Name", "label": "Site Name"}, {"key": "PR_Site_Status", "label": "Site Status"}, {"key": "PR_VCenter_IPAddress", "label": "vCenter Server - IP Address/HostName:Port"}, {"key": "PR_SRMServer_IPAddress", "label": "SRM Server - IP Address/HostName:Port"}, {"key": "PR_SRM_Version", "label": "SRM Version"}]}, {"type": "DR", "keys": [{"key": "DR_Site_Name", "label": "Site Name"}, {"key": "DR_Site_Status", "label": "Site Status"}, {"key": "DR_VCenter_IPAddress", "label": "vCenter Server - IP Address/HostName:Port"}, {"key": "DR_SRMServer_IPAddress", "label": "SRM Server - IP Address/HostName:Port"}, {"key": "DR_SRM_Version", "label": "SRM Version"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "PR_Protection_GroupName", "label": "Protection Groups Name,Type,State,VM Count()"}, {"key": "PR_Recovery_Plan_And_State", "label": "Recovery Plan Name,State"}]}, {"type": "DR", "keys": [{"key": "DR_Protection_GroupName", "label": "Protection Groups Name,Type,State,VM Count()"}, {"key": "DR_Recovery_Plan_And_State", "label": "Recovery Plan Name,State"}]}]}]}, "ZertoVPG": {"Path": {"PR": ["ZertoVPGMonitoringPR", "ReplicationMonitoringPR"], "DR": ["ZertoVPGMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "AzurePostgresPaas": {"Path": {"PR": ["AzurePostgresPaasPRMonitoring", "Primary"], "DR": ["AzurePostgresPaasMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerVersion", "label": "Server Version"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerResourceGroupName", "label": "Server Resource GroupName"}, {"key": "ServerLocation", "label": "Server Location(Region)"}, {"key": "ServerStatus", "label": "Server Status"}, {"key": "ReplicationRole", "label": "Replication Role"}, {"key": "Datalag", "label": "Datalag"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerVersion", "label": "Server Version"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerResourceGroupName", "label": "Server Resource GroupName"}, {"key": "ServerLocation", "label": "Server Location(Region)"}, {"key": "ServerStatus", "label": "Server Status"}, {"key": "ReplicationRole", "label": "Replication Role"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "AzureMysqlPaas": {"Path": {"PR": ["AzureMysqlPaasPRMonitoring", "Primary"], "DR": ["AzureMysqlPaasMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerVersion", "label": "Server Version"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerResourceGroupName", "label": "Server Resource GroupName"}, {"key": "ServerLocation", "label": "Server Location(Region)"}, {"key": "ServerStatus", "label": "Server Status"}, {"key": "ReplicationRole", "label": "Replication Role"}, {"key": "Datalag", "label": "Datalag"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerVersion", "label": "Server Version"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerResourceGroupName", "label": "Server Resource GroupName"}, {"key": "ServerLocation", "label": "Server Location(Region)"}, {"key": "ServerStatus", "label": "Server Status"}, {"key": "ReplicationRole", "label": "Replication Role"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "AzureStorageAccount": {"Path": {"PR": ["AzureStorageReplicationMonitoring", "AzureStorageAccountReplicationPRMonitoring", "Primary"], "DR": ["AzureStorageReplicationMonitoring", "AzureStorageAccountReplicationMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "Location", "label": "Location"}, {"key": "Diskstate", "label": "Diskstate"}]}, {"type": "DR", "keys": [{"key": "Location", "label": "Location"}, {"key": "Diskstate", "label": "Diskstate"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "DataSyncAppReplication": {"Path": {"PR": ["PrDataSyncReplicationModel", "PrMonitoringModel"], "DR": ["DataSyncReplicationModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_Server_Name", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/HostName"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "sourcePath", "label": "Source Replication Path"}, {"key": "destinationPath", "label": "Destination Path"}, {"key": "totalFilesCount", "label": "Number of Files"}, {"key": "totalFilesSize", "label": "Total File Size"}, {"key": "skippedFilesCount", "label": "Skipped File Count"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "sourcePath", "label": "Source Replication Path"}, {"key": "destinationPath", "label": "Destination Path"}, {"key": "totalFilesCount", "label": "Number of Files"}, {"key": "totalFilesSize", "label": "Total File Size"}, {"key": "skippedFilesCount", "label": "Skipped File Count"}]}]}]}, "MssqlAlwaysOnAvailabilityGroup": {"Path": {"PR": ["PrAlwaysOnAvailabilityGroupMonitoringDetails"], "DR": ["AlwaysOnAvailabilityGroupMonitoringDetails"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "IP Address/HostName"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "AvailabilityGroupName", "label": "Availability Group Name"}, {"key": "AvailabilityGroupRole", "label": "Availability Group Role"}, {"key": "ReplicaMode", "label": "Replica Mode"}, {"key": "AvailabilityGroupConnnectedState", "label": "Connected State"}]}, {"type": "DR", "keys": [{"key": "Availability_Group_Name", "label": "Availability Group Name"}, {"key": "Availability_Group_Role", "label": "Availability Group Role"}, {"key": "Availability_Mode", "label": "Replica Mode"}, {"key": "Availability_Group_Connnected_State", "label": "Connected State"}]}]}]}, "AzureMssqlPaas": {"Path": {"PR": ["AzureMssqlPaasPRMonitoring", "Primary"], "DR": ["AzureMssqlPaasMonitoring"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerRegion", "label": "Server Location(Region)"}, {"key": "DatabaseName", "label": "Database Name"}, {"key": "DatabaseStatus", "label": "Database Status"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "ServerFQDN", "label": "Server FQDN"}, {"key": "ServerRegion", "label": "Server Location(Region)"}, {"key": "DatabaseName", "label": "Database Name"}, {"key": "DatabaseStatus", "label": "Database Status"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "ReplicationState", "label": "Replication State"}, {"key": "Datalag", "label": "Datalag"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "ReplicationState", "label": "Replication State"}]}]}]}, "PowerMaxSRDF": {"Path": {"PR": ["PRComponentMontoring", "ComponentDetail"], "DR": ["DRComponentMontoring", "ComponentDetail"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "IP Address/Host Name"}]}]}, {"type": "replication", "prOnlyKeys": ["replicationType"], "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}]}]}]}, "OracleRac": {"Path": {"PR": ["MonitoringOracleRacModel"], "DR": ["MonitoringOracleRacModel"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "PR_ServerName", "label": "Server Name"}, {"key": "PR_Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "PR_Database_Sid", "label": "Database SID"}, {"key": "PR_Log_sequence", "label": "Last Generated Archive Log"}]}, {"type": "DR", "keys": [{"key": "ServerName", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/Host Name"}, {"key": "Database_Sid", "label": "Database SID"}, {"key": "Log_sequence", "label": "Last Generated Archive Log"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "PR_Replication_Mode", "label": "Replication_Mode"}, {"key": "PR_Services", "label": "Service Name"}, {"key": "PR_Protection_mode", "label": "Protection Mode"}, {"key": "PR_Dataguard_status", "label": "DataGuard Status"}]}, {"type": "DR", "keys": [{"key": "replicationType", "label": "Replication Type"}, {"key": "DR_Replication_Mode", "label": "Replication_Mode"}, {"key": "Services", "label": "Service Name"}, {"key": "Protection_mode", "label": "Protection Mode"}, {"key": "Dataguard_status", "label": "DataGuard Status"}]}]}]}, "goldengatereplication": {"Path": {"PR": ["PrGoldenGateMonitoringModel", "PrMonitoringModel"], "DR": ["GoldenGateMonitoringModels"]}, "tableData": [{"type": "component", "keys": [{"type": "PR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}, {"key": "GGVersion", "label": "Golden Gate Version"}, {"key": "GGMgrStatus", "label": "Golden Gate Manager Status"}]}, {"type": "DR", "keys": [{"key": "Server_Name", "label": "Server Name"}, {"key": "Server_IpAddress", "label": "IP Address/HostName"}, {"key": "GGVersion", "label": "Golden Gate Version"}, {"key": "GGMgrStatus", "label": "Golden Gate Manager Status"}]}]}, {"type": "replication", "keys": [{"type": "PR", "keys": [{"key": "Program", "label": "Program"}, {"key": "Group", "label": "Group"}, {"key": "Status", "label": "Status"}, {"key": "LagAtCheckPoint", "label": "Lag at checkpoint"}]}, {"type": "DR", "keys": [{"key": "Program", "label": "Program"}, {"key": "Group", "label": "Group"}, {"key": "Status", "label": "Status"}, {"key": "LagAtCheckPoint", "label": "Lag at checkpoint"}]}]}]}}