﻿namespace ContinuityPatrol.Application.Features.ComponentType.Queries.GetNameUnique;

public class GetComponentTypeNameUniqueQueryHandler : IRequestHandler<GetComponentTypeNameUniqueQuery, bool>
{
    private readonly IComponentTypeRepository _componentTypeRepository;

    public GetComponentTypeNameUniqueQueryHandler(IComponentTypeRepository componentTypeRepository)
    {
        _componentTypeRepository = componentTypeRepository;
    }

    public async Task<bool> Handle(GetComponentTypeNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _componentTypeRepository.IsComponentTypeNameExist(request.Type, request.Id);
    }
}