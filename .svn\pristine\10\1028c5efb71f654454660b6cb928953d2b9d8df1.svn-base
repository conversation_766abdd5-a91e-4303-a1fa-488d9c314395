﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.SmsConfiguration.Events
{
    public class SmsConfigurationCreatedEventTests
    {
        private readonly Mock<ILogger<SmsConfigurationCreatedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SmsConfigurationCreatedEventHandler _handler;

        public SmsConfigurationCreatedEventTests()
        {
            _loggerMock = new Mock<ILogger<SmsConfigurationCreatedEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SmsConfigurationCreatedEventHandler(
                _loggerMock.Object,
                _userActivityRepositoryMock.Object,
                _userServiceMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogAndAddUserActivity_WhenEventIsHandled()
        {
            var notification = new SmsConfigurationCreatedEvent
            {
                UserName = "TestUser"
            };
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");
            _userServiceMock.Setup(service => service.LoginName).Returns("TestLogin");
            _userServiceMock.Setup(service => service.RequestedUrl).Returns("http://test.com");
            _userServiceMock.Setup(service => service.CompanyId).Returns("67890");
            _userServiceMock.Setup(service => service.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(notification, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "12345" &&
                activity.LoginName == "TestLogin" &&
                activity.RequestUrl == "http://test.com" &&
                activity.CompanyId == "67890" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Action == "Create SmsConfiguration" &&
                activity.Entity == "SmsConfiguration" &&
                activity.ActivityType == "Create" &&
                activity.ActivityDetails == "Sms Configuration 'TestUser' created successfully"
            )), Times.Once);

            _loggerMock.Verify(logger => logger.LogInformation("Sms Configuration 'TestUser' created successfully"), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenEventIsHandledSuccessfully()
        {
            var notification = new SmsConfigurationCreatedEvent
            {
                UserName = "TestUser"
            };
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");

            await _handler.Handle(notification, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _loggerMock.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);
        }
    }
}
