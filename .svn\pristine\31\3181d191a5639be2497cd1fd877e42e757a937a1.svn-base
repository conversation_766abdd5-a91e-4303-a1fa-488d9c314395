﻿let templatesArray = [];
let importTemplate = [];
let templateTypeIcon = '';
let importTemplateId = '';
let TemplateName = '';

const nameExistUrl = "ITAutomation/WorkflowTemplate/TemplateNameExist";

let $filterItems = $(".filter-card");
let $parentTemplate = $('#parentTemplate');
let createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

const clearFields = () => {
    $('#ImportTemplateError, #templateNameError').text('').removeClass('field-validation-error');
    $('#templateName').text('').val('');
    $('#ImportTemplate').val('');
    $('#collapseIcon').removeClass('show')
}

const getTemplate = async () => {   
    templatesArray = []

    $('#templateContainer').css('opacity', '0.5')
    $('#WFTemplateLoader').show();

        await $.ajax({
            type: "GET",
            url: RootUrl + "ITAutomation/WorkflowTemplate/GetTemplateList",
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    if (Array.isArray(result?.data) && result?.data?.length) {
                        templatesArray = result?.data;
                        renderTemplateList(result?.data);                                         
                    }
                }
                else {
                    errorNotification(result)
                }
            }
        });

    $('#WFTemplateLoader').hide();
    $('#templateContainer').css('opacity', '1')
};

getTemplate();

const renderTemplateList = (data) => {

    const templateListDiv = $("#templateList");
    let optionData = ''

    data?.length && data?.forEach(userpre => {
        const tempName = userpre?.replicationTypeName;
        optionData += `<option value=${userpre?.replicationTypeId}> ${tempName ?? 'NA'}</option>`

        templateListDiv.append(generateTemplateCard(userpre, tempName));
    })

    $('#replicationTypeFilter').append(optionData)
}

const generateTemplateCard = (userpre, tempName) => {

    const templateCard = `<div class='parentContainer' id=${getRandomId('type')}>
                              <div class="d-flex align-items-center gap-2 my-2 " id=${getRandomId('type')}>
                                <div style="width:18%;">
                                 <span class="fw-semibold text-truncate headerTypeName">${tempName ?? 'NA'}</h6>
                                  </div><div class="w-75">
                                    <hr class="opacity-75" style="border-color: var(--bs-gray-500)" /></div></div>

                    <div id="parentTemplate">
                        <div class="filter-item row g-3">
                    ${userpre?.templateListVm && userpre?.templateListVm?.length && userpre?.templateListVm?.map(list =>

                       `<div class="col-2 filter-card templateCard" id=${getRandomId('type')}>
                                <div class="card position-relative border border-light-subtle mb-0" style=${list?.id == '' ? "opacity:0.5;" : ''}>
                                    <div class="card-header d-flex align-items-center header p-0 pe-2">
                                      <small class="py-1 px-2 text-white ${getActionTypeClass(list)}" id="ActionType" data-templates-id="${list?.actionType}" style="border-radius: 0px 0px 10px 0px;">
                                                 ${list?.actionType || 'NA'}
                                           </small>

                               ${createPermission === 'true' ? `<div class="d-flex flex-direction-row gap-2 mt-1 me-1">

                                <div class="delete-button templateDeleteFun" role="button" id="deleteButton" data-bs-toggle="modal" data-bs-target="#DeleteModal" data-templates-id="${list?.id}" 
                                    data-templates-type="${list?.name}" data-templates-delete="${list?.isDelete}" style="${list?.isDelete ? 'pointer-events:none;opacity:0.3' : ''}">
                                    <i class="cp-Delete" title="Delete"></i>
                                </div>
                                <div class="download-button" role="button">
                                    <span role="button" class="templateDownload" id="tempDownload" data-template_name="${list?.name || 'NA'}" data-template='${JSON.stringify(list)}'>
                                        <i class="cp-download" title="Download"></i>
                                    </span>
                                </div>
                                </div>
                            ` : `
                            <div class="d-flex flex-direction-row gap-2 mt-1 me-1">
                                <div class="icon-disabled" role="button">
                                    <i class="cp-Delete" title="Delete"></i>
                                </div>
                                <div class="icon-disabled" role="button">
                                    <span role="button">
                                        <i class="cp-download" title="Download"></i>
                                    </span>
                                </div>
                                <div>
                            `}
                                    </div>
                                    <div class="card-body pt-0 p-2">
                                <div class="text-center">
                                    <div class="circle text-dark p-2 mx-auto position-relative" style="background:#f1f1f0; width:fit-content;">
                                     <i class="${list?.icon || "cp-workflow-templates"} fs-2"></i>
                                        <span class="position-absolute top-100 start-50 translate-middle"><i class="${list?.id !== '' ? 'cp-success-fill text-success' : 'cp-error-fill text-danger'} fs-5"></i></span>
                                    </div>
                                    <div class="text-center d-grid justify-content-center" style="line-height: 1.5;">
                                        <span class="fw-semibold mt-3 d-inline-block text-truncate templateNameClass" style="max-width: 160px;" title='${list.name}'> ${list?.name}</span>
                                         <span class="d-inline-block fs-9" title='v${list?.version || '1.0.0'}'>v${list?.version || '1.0.0'}</span>
                                   <small class="text-light d-inline-block text-truncate" title='${list?.description || "No description"}' style="max-width: 160px;">${list?.description || "No description"}</small>
                                    </div>
                               </div>
                                    </div>
                                </div>
                            </div>
                    `).join('')}
                 </div>
                </div>
                </div> `;

    return templateCard;

}

const getActionTypeClass = (template) => {
    if (template?.id === '') return "bg-secondary";

    switch (template?.actionType) {
        case "Custom": return "template_store_custom_bg";
        case "SwitchOver": return "template_store_switch_back_bg";
        case "SwitchBackRoll": return "template_store_switch_backroll_bg";
        case "DynamicMonitoring": return "template_store_monitoring_bg";
        case "SwitchBack": return "template_store_switch_over_bg";
        case "FailOver": return "template_store_fail_over_bg";
        case "FailBack": return "template_store_fail_back_bg";
        case "DR Ready": return "template_store_drready_bg";
        case "Monitoring": return "template_store_monitoring_bg";
        default: return "bg-secondary";
    }
};

$(document).on('click', '.templateDownload', async function () {

    const templateString = JSON.stringify($(this).data('template'))
    const name = $(this).data('template_name')

        $.ajax({
            type: "POST",
            url: RootUrl + 'ITAutomation/WorkflowTemplate/TemplateDataEncrypt',
            data: { data: templateString, __RequestVerificationToken: gettoken() },
            dataType: 'text',
            success: function (response) {
                const blob = new Blob([response], { type: 'application/octet-stream' });
                const a = document.createElement('a');
                a.href = URL.createObjectURL(blob);
                a.download = name + '_' + new Date().toLocaleDateString().split('/').reverse().join('') + '_' + new Date().toLocaleTimeString().split(':')[0] + '_' + new Date().toLocaleTimeString().split(':')[1] + '_temp' + ".json";
                a.click();
                notificationAlert('success', `Template ${name} has been exported successfully`)
            }
        });
});

$("#templateSearch").on("keyup input", function () {
    let searchText = $(this)?.val()?.toLowerCase();
    $('#templateList').find('.NoDataContainer').remove()    

    if ($('#replicationTypeFilter :selected').val() !== '') {
        $('#replicationTypeFilter').val('').trigger('change')
        setTimeout(() => {
            $("#templateSearch").text(searchText).val(searchText)
        }, 200)
    }

    $('.templateCard').each(function (idx, obj) {

        $('.parentContainer').show()
        let text = $(`#${obj?.id} .templateNameClass`).text().replace(/\s+/g, '')
        if (searchText?.length) {
            if (text.toLowerCase().startsWith(searchText)) $(`#${obj?.id}`).show()
            else $(`#${obj?.id}`).hide()
        } else {
            $(`#${obj?.id}`).show()
            $('#replicationTypeFilter').val('').trigger('change')
        }

    })

    $('.parentContainer').each(function (idx, obj) {

        if ($(`#${obj?.id} .templateCard`).is(':visible')) $(`#${obj?.id}`).show()
        else $(`#${obj?.id}`).hide()

    })

    if (!$('.parentContainer').is(':visible')) {
        $('#templateList').find('.NoDataContainer').remove()
        $('#templateList').append(`<div class="d-flex flex-column align-items-center justify-content-center NoDataContainer mt-5"><img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img "><span>No matching records found</span></div>`)
    }
});

function searchItem(selectedType) {
    let searchText = $("#search-inp").val().toLowerCase();
    $parentTemplate.children().each(function () {
        let itemName = $(this).find(".fw-bold").text().toLowerCase();
        if (selectedType === "All" || $(this).data('item') === selectedType) {
            if (itemName.indexOf(searchText) !== -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        } else {
            $(this).hide();
        }
    });
}

$('#replicationTypeFilter').on('change', function () {
    let value = $('#replicationTypeFilter').find(':selected').text().trim()
    $('.templateCard, .parentContainer').show()
    $('#templateSearch').val('').text('')

    $('.headerTypeName').each(function (idx, obj) {
        $('#' + $(this).parents('.parentContainer')[0]?.id).show()
        if (this?.innerText === value) {
            $('#' + $(this).parents('.parentContainer')[0]?.id).show()
        } else if (value === "All") {
            $('.headerTypeName').show()
        }
        else {
            $('#' + $(this).parents('.parentContainer')[0]?.id).hide()
        }

    })
})

function filterElements(selectedType) {
    if (selectedType === "All") {
        $('#parentTemplate').children().each(function () {
            $(this).show()
        });
    } else {
        $('#parentTemplate').children().each(function () {
            if ($(this).data('item') === selectedType) $(this).show()
            else $(this).hide()
        });
    }
}

$('#ImportTemplate').on('change', async function () {
    const value = $("#ImportTemplate").val();
    importExportValidation(value, 'ImportTemplateError', 'Choose workflow template');
})

function importExportValidation(value, errorId, text) {
    const errorElement = $('#' + errorId)
    if (!value) {
        errorElement.text(text).addClass('field-validation-error')
        return false;
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return true;
    }
}

$('#btnImportWorkFlow').on('click', function () {
    $('#ImportTemplate').val('')
    templateTypeIcon = ''
    $("#imageSubSelected").attr("class", 'cp-images')
    $('#ImportTemplateError').text('').removeClass('field-validation-error');
    if (createPermission == 'true') $('#ImportTemplateModal').modal('show')

});

$("#collapseIcon div.Category_Icon i").on('click', function () {

    if ($(this)?.length) templateTypeIcon = $(this)[0]?.classList[0]
    $("#imageSubSelected").attr("class", $(this).attr("class"))
    $('#collapseIcon').removeClass('show')
})

$('#loadImportTemplate').on('click', async function (e) {
    e.preventDefault();
    importTemplateId = '';
    const fileInput = $('#ImportTemplate')[0];
    const file = fileInput?.files[0];
    let templatename = $("#templateName")?.val();
    let templateValidation = await templateNameValidate(templatename)

    let exportValidation = importExportValidation(file, 'ImportTemplateError', 'Choose workflow template')

    if (exportValidation && file && templateNameValidate(templatename) && templateValidation) {
        if (file.type === 'application/json') {
            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const importedJson = e?.target?.result;
                    $.ajax({
                        type: "POST",
                        url: RootUrl + 'ITAutomation/WorkflowTemplate/TemplateDataDecrypt',
                        data: { data: importedJson, __RequestVerificationToken: gettoken() },
                        dataType: 'text',
                        success: function (response) {
                            if (response && response?.length) {
                                let temp = JSON.parse(response)
                                let templateData = {
                                    "Name": TemplateName,
                                    "CompanyId": "",
                                    "Properties": temp?.properties,
                                    "Type": temp?.type,
                                    "ActionType": temp?.actionType,
                                    "ReplicationTypeName": temp?.replicationTypeName,
                                    "ReplicationTypeId": temp?.replicationTypeId,
                                    "ReplicationCategoryTypeId": temp?.replicationCategoryTypeId,
                                    "ReplicationCategoryTypeName": temp?.replicationCategoryTypeName,
                                    "SubTypeId": temp?.subTypeId,
                                    "subTypeName": temp?.subTypeName,
                                    "Description": temp?.description,
                                    "Icon": templateTypeIcon ? templateTypeIcon : temp?.icon
                                }

                                importTemplate.push(templateData)
                                let filterTemplate = templatesArray?.length && templatesArray?.filter((d) => d?.replicationTypeId === temp?.replicationTypeId)

                                if (filterTemplate?.length) {
                                    let filterActionType = filterTemplate[0]?.templateListVm?.length &&
                                        filterTemplate[0]?.templateListVm?.filter((i) => i?.actionType === temp?.actionType);

                                    if (filterActionType?.length) {
                                        importTemplateId = filterActionType[0]?.id
                                        $('#confirmationTempText').html(`<span class='text-primary'>${temp?.actionType}</span> of <span class='text-primary'>${temp?.replicationTypeName} </span> solution already exist. Do you want to override ?`)
                                        $('#ImportTemplateModal').modal('hide')
                                        $('#templateConfModal').modal('show')
                                    } else {
                                        saveTemplateFromImport(templateData)
                                    }
                                } else {
                                    saveTemplateFromImport(templateData)
                                }
                            } else {
                                $('#ImportTemplateError').text('Invalid format').addClass('field-validation-error');
                            }
                        }
                    });
                } catch (error) {
                    notificationAlert("warning", 'Error on reading file');
                }
            };

            reader.onerror = function (e) {
                notificationAlert("warning", 'Error on reading file');
            };

            reader.readAsText(file);
        } else {
            $('#ImportTemplateError').text('Invalid format').addClass('field-validation-error');
        }
    }
});

const saveTemplateFromImport = (templateData) => {

    templateData['__RequestVerificationToken'] = gettoken()

        $.ajax({
            type: "POST",
            url: RootUrl + 'ITAutomation/WorkflowTemplate/CreateTemplate',
            data: templateData,
            datatype: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result?.data?.message)
                } else {
                    errorNotification(result)
                }

                $('#ImportTemplateModal, #templateConfModal').modal('hide')
                setTimeout(() => {
                    window.location.reload()
                }, 2000)
            },
        });   
}

$('#confimTemplateDiscard').on('click', function () {
    $('#templateConfModal').modal('hide')
    $('#ImportTemplateModal').modal('show')
})

$('#templateConfirmationSave').on('click', function () {
    let data = importTemplate[0]
    data['id'] = importTemplateId
    saveTemplateFromImport(data)
})

$('#templateName').on('keyup', async function () {
    const value = $("#templateName").val();
    let sanitizedValue = value?.replace(/\s{2,}/g, ' ');
    TemplateName = sanitizedValue
    $(this).val(sanitizedValue);
    await templateNameValidate(sanitizedValue);
})

async function templateNameValidate(value, id = null) {
    const errorElement = $('#templateNameError')

    if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }else if (!value) {
        errorElement.text('Enter workflow template name').addClass('field-validation-error')
        return false;

    } else {
        let url = RootUrl + nameExistUrl;
        let data = {};
        data.name = value;
        data.id = id;

        const validationResults = [
            SpecialCharValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            OnlyNumericsValidate(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            ShouldNotAllowMultipleSpace(value),
            minMaxlength(value),
            secondChar(value),
            ShouldNotBeginWithNumber(value),
            await IsNameExist(url, data?.name, data, OnError)
        ];
        return CommonValidation(errorElement, validationResults);
    }
}

async function IsNameExist(url, name, data, errorFunc) {
    return !name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

$(document).on('click', '.delete-button', function () {

    let TemplateId = $(this).data('templates-id');
    let Template = $(this).data('templates-type');
    $('#deleteData').text(Template);
    $('#textDeleteId').val(TemplateId);
    $('#DeleteModal').modal('show')
});

$('#ImportCancelTemplate').on('click', function () {
    $('#ImportTemplate, #templateName').val('').text('')
    templateTypeIcon = '';
    clearFields()
})

if (createPermission == 'false') {
    $("#tempDownload").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    $("#btnImportWorkFlow").removeAttr('id').addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target');

}

if (deletePermission == 'false') {
    setTimeout(() => {
        $(".templateDeleteFun, .templateDownload").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id').removeClass('delete-button').removeClass('templateDownload');
    }, 300)

}