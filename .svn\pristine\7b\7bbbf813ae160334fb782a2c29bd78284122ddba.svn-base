﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Import;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Form.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class FormBuilderControllerShould
    {
        private readonly Mock<ILogger<FormBuilderController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Fixture _fixture = new();
        private  FormBuilderController _controller ;

        public FormBuilderControllerShould()
        {
            Initialize();

        }
        internal void Initialize()
        {
            _controller = new FormBuilderController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsView()
        {
            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_ThrowsException_PropagatesException()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<FormPaginatedViewEvent>(), It.IsAny<CancellationToken>()))
                         .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.List());
        }

        [Fact]
        public async Task GetNames_ReturnsJsonResultWithSuccess()
        {
            // Arrange
            var formNames = new List<FormNameVm> { new FormNameVm { Name = "TestForm" } };
            _mockDataProvider.Setup(p => p.Form.GetFormNames()).ReturnsAsync(formNames);

            // Act
            var result = await _controller.GetNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetNames_HandlesException_ReturnsJsonException()
        {
            // Arrange
            _mockDataProvider.Setup(p => p.Form.GetFormNames())
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesFormSuccessfully()
        {
            // Arrange
            var form = _fixture.Create<FormViewModel>();
            form.Properties = ""; // Ensure empty properties for test
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateFormCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(mapper => mapper.Map<CreateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.CreateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(form) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesFormSuccessfully()
        {
            // Arrange
            var form = _fixture.Create<FormViewModel>();
            form.Properties = ""; // Ensure empty properties for test
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateFormCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockMapper.Setup(mapper => mapper.Map<UpdateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.UpdateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(form) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_ReturnsJsonException()
        {
            // Arrange
            var form = _fixture.Create<FormViewModel>();
            form.Properties = "";
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var command = new CreateFormCommand();
            _mockMapper.Setup(mapper => mapper.Map<CreateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.CreateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(form) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException_ReturnsJsonException()
        {
            // Arrange
            var form = _fixture.Create<FormViewModel>();
            form.Properties = "";
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormCommand();
            _mockMapper.Setup(mapper => mapper.Map<CreateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.CreateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(form) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEncryptedProperties_DecryptsSuccessfully()
        {
            // Arrange
            var originalData = "test properties data";
            var encryptedData = SecurityHelper.Encrypt(originalData);
            var form = _fixture.Create<FormViewModel>();
            form.Properties = encryptedData; // Set encrypted properties

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateFormCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockMapper.Setup(mapper => mapper.Map<CreateFormCommand>(It.IsAny<FormViewModel>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.CreateAsync(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(form) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            // Verify that Properties was decrypted (form.Properties should now contain decrypted data)
            Assert.Equal(originalData, form.Properties);
        }

        [Fact]
        public async Task IsPublish_PublishesFormSuccessfully()
        {
            // Arrange
            var command = new UpdateFormPublishCommand { Id = "test-id" };
            var response = new BaseResponse { Success = true, Message = "Published" };

            _mockMapper.Setup(mapper => mapper.Map<UpdateFormPublishCommand>(It.IsAny<UpdateFormPublishCommand>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.Publish(command)).ReturnsAsync(response);

            // Act
            var result = await _controller.IsPublish(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task IsPublish_HandlesValidationException_ReturnsJsonException()
        {
            // Arrange
            var command = new UpdateFormPublishCommand { Id = "test-id" };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Id", "Id is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(mapper => mapper.Map<UpdateFormPublishCommand>(It.IsAny<UpdateFormPublishCommand>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.Publish(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.IsPublish(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task IsPublish_HandlesGeneralException_ReturnsJsonException()
        {
            // Arrange
            var command = new UpdateFormPublishCommand { Id = "test-id" };

            _mockMapper.Setup(mapper => mapper.Map<UpdateFormPublishCommand>(It.IsAny<UpdateFormPublishCommand>())).Returns(command);
            _mockDataProvider.Setup(service => service.Form.Publish(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsPublish(command) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_DeletesFormSuccessfully()
        {
            // Arrange
            var id = "someId";
            var response = new BaseResponse { Success = true, Message = "Deleted" };
            _mockDataProvider.Setup(provider => provider.Form.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task Delete_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "someId";
            _mockDataProvider.Setup(provider => provider.Form.DeleteAsync(id))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetFormById_ReturnsJsonResultWithForm()
        {
            // Arrange
            var id = "someId";
            var formDetail = new FormDetailVm { Id = id, Name = "Test Form" };
            _mockDataProvider.Setup(m => m.Form.GetByReferenceId(id)).ReturnsAsync(formDetail);

            // Act
            var result = await _controller.GetFormById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetFormById_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "someId";
            _mockDataProvider.Setup(m => m.Form.GetByReferenceId(id))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFormById(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task FormExport_ReturnsJsonResultWithFormsAndMappings()
        {
            // Arrange
            var id = "form1,form2";
            var form1 = new FormDetailVm { Id = "form1", Name = "Form 1" };
            var form2 = new FormDetailVm { Id = "form2", Name = "Form 2" };
            var formMappings = new List<FormTypeCategoryListVm>
            {
                new() { FormId = "form1", FormTypeId = "type1" }
            };
            var components = new List<ComponentTypeListVm>
            {
                new() { Id = "type1", ComponentName = "Component 1" }
            };

            _mockDataProvider.Setup(m => m.Form.GetByReferenceId("form1")).ReturnsAsync(form1);
            _mockDataProvider.Setup(m => m.Form.GetByReferenceId("form2")).ReturnsAsync(form2);
            _mockDataProvider.Setup(m => m.FormMapping.GetFormMappingList()).ReturnsAsync(formMappings);
            _mockDataProvider.Setup(m => m.ComponentType.GetComponentTypeList()).ReturnsAsync(components);

            // Act
            var result = await _controller.FormExport(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"Forms\":", json);
            Assert.Contains("\"FormMappings\":", json);
            Assert.Contains("\"Componts\":", json); // Note: typo in production code
        }

        [Fact]
        public async Task FormExport_WithNoComponents_ReturnsEmptyComponentsList()
        {
            // Arrange
            var id = "form1";
            var form1 = new FormDetailVm { Id = "form1", Name = "Form 1" };
            var formMappings = new List<FormTypeCategoryListVm>
            {
                new() { FormId = "form1", FormTypeId = null } // No FormTypeId
            };

            _mockDataProvider.Setup(m => m.Form.GetByReferenceId("form1")).ReturnsAsync(form1);
            _mockDataProvider.Setup(m => m.FormMapping.GetFormMappingList()).ReturnsAsync(formMappings);

            // Act
            var result = await _controller.FormExport(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.Contains("\"Componts\":[]", json); // Should be empty array
        }

        [Fact]
        public async Task FormExport_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var id = "form1";
            _mockDataProvider.Setup(m => m.Form.GetByReferenceId("form1"))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.FormExport(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task FormImport_ReturnsJsonResultWithSuccess()
        {
            // Arrange
            var importCommand = new ImportFormCommand
            {
                Forms = new List<FormListCommand> { new() { Name = "Test Form" } }
            };
            var commandJson = JsonConvert.SerializeObject(importCommand);
            var response = new ImportFormResponse { Success = true, Message = "Imported successfully" };

            _mockDataProvider.Setup(m => m.Form.ImportForms(It.IsAny<ImportFormCommand>()))
                             .ReturnsAsync(response);

            // Act
            var result = await _controller.FormImport(commandJson) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task FormImport_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var commandJson = "invalid json";

            // Act
            var result = await _controller.FormImport(commandJson) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public void FormDataEncrypt_ReturnsJsonResultWithEncryptedData()
        {
            // Arrange
            var data = "test data";

            // Act
            var result = _controller.FormDataEncrypt(data) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public void FormDataEncrypt_Returns_Json_With_Error_When_Exception_Occurs()
        {
            // Arrange
            // Pass null to cause SecurityHelper.Encrypt to throw an exception
            string data = null;

            // Act
            var result = _controller.FormDataEncrypt(data) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Value);

            // Convert to JSON and check structure - should be error response
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":", json);
            Assert.Contains("\"ErrorCode\":", json);
        }

        [Fact]
        public void FormDataDecrypt_ReturnsJsonResultWithDecryptedData()
        {
            // Arrange
            var data = "test data";
            var encryptedData = SecurityHelper.Encrypt(data);

            // Act
            var result = _controller.FormDataDecrypt(encryptedData) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public void FormDataDecrypt_HandlesException_ReturnsJsonException()
        {
            // Arrange
            // Create data that will cause Convert.FromBase64String to throw a FormatException
            // Format: validBase64Key$invalidBase64Data - this will pass initial checks but fail during base64 conversion
            var invalidData = "dGVzdGtleWZvcmVuY3J5cHRpb25wdXJwb3Nlc29ubHl0ZXN0aW5nZGF0YQ==$invalid@base64!data#with$special*chars";

            // Act
            var result = _controller.FormDataDecrypt(invalidData) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetForms_ReturnsJsonResultWithFormList()
        {
            // Arrange
            var type = "someType";
            var formList = new List<FormTypeVm> { new() { Name = "Test Form" } };
            _mockDataProvider.Setup(p => p.Form.GetFormByType(type)).ReturnsAsync(formList);

            // Act
            var result = await _controller.GetForms(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetForms_WithEmptyType_ReturnsEmptyJson()
        {
            // Arrange
            var type = "";

            // Act
            var result = await _controller.GetForms(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Equal("\"\"", json);
        }

        [Fact]
        public async Task GetForms_WithNullType_ReturnsEmptyJson()
        {
            // Arrange
            string type = null;

            // Act
            var result = await _controller.GetForms(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Equal("\"\"", json);
        }

        [Fact]
        public async Task GetForms_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var type = "someType";
            _mockDataProvider.Setup(p => p.Form.GetFormByType(type))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetForms(type) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task FormNameExist_ReturnsTrueIfNameExists()
        {
            // Arrange
            var formName = "formName";
            var id = "someId";
            _mockDataProvider.Setup(p => p.Form.IsFormNameExist(formName, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.FormNameExist(formName, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task FormNameExist_ReturnsFalseIfNameDoesNotExist()
        {
            // Arrange
            var formName = "formName";
            var id = "someId";
            _mockDataProvider.Setup(p => p.Form.IsFormNameExist(formName, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.FormNameExist(formName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task FormNameExist_HandlesException_ReturnsFalse()
        {
            // Arrange
            var formName = "formName";
            var id = "someId";
            _mockDataProvider.Setup(p => p.Form.IsFormNameExist(formName, id))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.FormNameExist(formName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFormPagination_ReturnsJsonResultWithPaginatedFormList()
        {
            // Arrange
            var query = new GetFormPaginatedListQuery { Type = "" };
            var paginatedList = new PaginatedResult<FormListVm>
            {
                Data = new List<FormListVm> { new() { Name = "Test Form" } },
                TotalCount = 1
            };
            _mockDataProvider.Setup(provider => provider.Form.GetPaginatedForms(query)).ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetFormPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        [Fact]
        public async Task GetFormPagination_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var query = new GetFormPaginatedListQuery { Type = "" };
            _mockDataProvider.Setup(provider => provider.Form.GetPaginatedForms(query))
                             .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFormPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== ATTRIBUTE TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange & Act
            var areaAttribute = typeof(FormBuilderController).GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void CreateOrUpdate_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.CreateOrUpdate));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void IsPublish_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.IsPublish));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetFormById_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.GetFormById));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void FormExport_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.FormExport));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void FormImport_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.FormImport));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void FormDataEncrypt_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.FormDataEncrypt));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void FormDataDecrypt_ShouldHaveRequiredAttributes()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.FormDataDecrypt));

            // Act
            var httpPostAttribute = method?.GetCustomAttribute<HttpPostAttribute>();
            var validateAntiForgeryTokenAttribute = method?.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();
            var antiXssAttribute = method?.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void GetForms_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.GetForms));

            // Act
            var httpGetAttribute = method?.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void FormNameExist_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.FormNameExist));

            // Act
            var httpGetAttribute = method?.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void GetFormPagination_ShouldHaveHttpGetAttribute()
        {
            // Arrange
            var method = typeof(FormBuilderController).GetMethod(nameof(FormBuilderController.GetFormPagination));

            // Act
            var httpGetAttribute = method?.GetCustomAttribute<HttpGetAttribute>();

            // Assert
            Assert.NotNull(httpGetAttribute);
        }

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new FormBuilderController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );

            // Assert
            Assert.NotNull(controller);
        }
    }
}
