using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AboutCpFixture : IDisposable
{
    public List<AboutCp> AboutCpPaginationList { get; set; }
    public List<AboutCp> AboutCpList { get; set; }
    public AboutCp AboutCpDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AboutCpFixture()
    {
        var fixture = new Fixture();

        AboutCpList = fixture.Create<List<AboutCp>>();

        AboutCpPaginationList = fixture.CreateMany<AboutCp>(20).ToList();

        AboutCpPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AboutCpPaginationList.ForEach(x => x.IsActive = true);

        AboutCpList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AboutCpList.ForEach(x => x.IsActive = true);

        AboutCpDto = fixture.Create<AboutCp>();
        AboutCpDto.ReferenceId = Guid.NewGuid().ToString();
        AboutCpDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}