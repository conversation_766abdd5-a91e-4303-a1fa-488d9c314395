﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetNames;

public class GetAlertMasterNameQueryHandler : IRequestHandler<GetAlertMasterNameQuery, List<AlertMasterNameVm>>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;

    public GetAlertMasterNameQueryHandler(IAlertMasterRepository alertMasterRepository, IMapper mapper)
    {
        _alertMasterRepository = alertMasterRepository;
        _mapper = mapper;
    }

    public async Task<List<AlertMasterNameVm>> Handle(GetAlertMasterNameQuery request,
        CancellationToken cancellationToken)
    {
        var alertMasters = await _alertMasterRepository.GetAlertMasterNames();

        var alertMasterDto = _mapper.Map<List<AlertMasterNameVm>>(alertMasters);

        return alertMasterDto;
    }
}