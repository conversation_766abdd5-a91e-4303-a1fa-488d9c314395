﻿function monitorTypeZertoVpg(value, infraObjectName, moniterType, parsedData, glob) {
    let MonitorType = moniterType?.toLowerCase();    
    let monitoringServiceDetails = value?.monitorServiceDetails

    const parseStatus = (status) => {

        switch (status?.toLowerCase()) {
            case "down":
            case "failure":
                return "cp-down-linearrow me-1 text-danger";
            case "pending":
                return "cp-pending me-1 text-warning";
            case "success":
            case "up":
                return "cp-up-linearrow me-1 text-success";

        }
    };

    function checkCondition(value) {
        
        if (value === null || value === undefined || value === '' || value === 'null' ) {
            return "NA";
        }
        //if (typeof value === 'string' && value.includes(undefined) && value.includes('$')) {
        //    return "NA"
        //}
        if (Array.isArray(value) && (value.includes(null) || value.includes('null'))) {
            return "NA";
        }

        if (typeof value === 'string' && value.includes('null')) {
            return "NA";
        }
        return value;
    }

    const generateTableSection = (headers, rows, mode) => {
        let html = ""
        html += '<table class="table table-hover mb-0 table-sm" style="table-layout:fixed">'
        html += '<thead style="position: sticky;top: 0px;">'
        html += '<tr>'
        headers.map(header => html += '<th>' + header + '</th>').join('')
        html += '</tr>'
        html += '</thead>'
        html += '<tbody >'
        rows.forEach((data) => {
            html += '<tr>'
            const icons = {};
            for (let key in data) {
                if (key.includes('Icon')) {
                    icons[data] = data[key];
                }
            }
            for (let key in data) {
                if (data.hasOwnProperty(key)) {

                    let splitkey = key.split("_")

                    if (splitkey[1] != "Icon" && splitkey != undefined && splitkey != null) {
                        let datasplit = data[key]?.split("$")
                        html += '<td><i class="' + datasplit[1] + '"></i>' + datasplit[0] + '</td>'
                    }
                }
            }

            html += '</tr>'
        })
        
        html += monitoringServiceDetails && monitoringServiceDetails?.length && mode ? generateMonitorServiceDetailsRow('', '', value?.monitorServiceDetails) : ''
        html += '</tbody >'
        html += '</table >'
        return html;

    };

    const generateGoldenTableSection = (headers, rows, mode) => {
        const prReplicationDetails = rows[1]?.PrGoldenGateMonitoringModel?.PrMonitoringModel?.ReplicationDetails;
       
        let html = ""
        html += '<table class="table mb-0 align-middle" id="tableCluster" style="table-layout:fixed">'
        html += '<thead>'
        //html += '<tr>'
        //headers.map(header => html += '<th>' + header + '</th>').join('')
        //html += '</tr>'
        html += '<tr>';
        html += '<th>Server Type</th>';
        html += '<th>Program</th>';
        html += '<th>Group</th>';
        html += '<th>Status</th>';
        html += '<th>Lag at checkpoint</th>';
        html += '</tr>';
        html += '</thead>'
        html += '<tbody >'

        prReplicationDetails?.forEach((detail, index) => {
            html += `<tr>`

            if (index === 0) {
                // First row includes the "Primary" cell with rowspan
                html += `
            <td rowspan="${prReplicationDetails.length}">Primary</td>
            <td>${detail.Program }</td>
            <td>${detail.Group}</td>
            <td>${detail.Status}</td>
            <td>${detail.LagAtCheckPoint}</td>
        `;
            } else {
                // Subsequent rows omit the "Primary" cell
                html += `
            <td>${detail.Program}</td>
            <td>${detail.Group}</td>
            <td>${detail.Status}</td>
            <td>${detail.LagAtCheckPoint}</td>
        `;
            }

            html += `</tr>`;


            //tableBody.appendChild(row);
        });

        //DR-replication
        rows[1]?.GoldenGateMonitoringModels.forEach(model => {
            if (model.Type === "DR") {
                const drReplicationDetails = model?.MonitoringModel?.ReplicationDetails;
                drReplicationDetails.forEach((detail, index) => {

                    html += `<tr>
                ${index === 0 ? `<td rowspan="${drReplicationDetails.length}">Secondary</td>` : ''}
                <td>${detail.Program}</td>
                <td>${detail.Group}</td>
                <td>${detail.Status}</td>
                <td>${detail.LagAtCheckPoint}</td>
            </tr>`;
                    //tableBody.appendChild(row);
                });
            }
        });

        html += '</tbody >'
        html += '</table >'
        return html;

    };


    let header = []
    let rowData = []
    let repheader = []
    let repData = []
    if (parsedData?.Monitor_Type?.toLowerCase() === "robocopy") {
        
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata = {}
        let IPAddressdata = {}
        let IpAddressrep = {}
        let hostdata = {}
        let jobdata = {}
        Serverdata.label = "Server Name"
        Serverdata.pr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata.dr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata[parsedData?.PrRoboCopyMonitorngModel?.Type] = checkCondition(parsedData?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_Name + "$" + Serverdata?.pr_Icon)

        IPAddressdata.pr_Icon = parseStatus(parsedData?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_Status)
        IPAddressdata.dr_Icon = parseStatus(parsedData?.RoboCopyMonitoringModels[0]?.MonitoringModel?.Server_Status)
        IPAddressdata.label = "IP Address/HostName"
        IPAddressdata[parsedData?.PrRoboCopyMonitorngModel?.Type] = checkCondition(parsedData?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_IpAddress + "$" + IPAddressdata?.pr_Icon)

        IpAddressrep.pr_Icon = parseStatus(value?.prServerStatus)
        IpAddressrep.dr_Icon = parseStatus(value?.drServerStatus)
        IpAddressrep.label = "IP Address/HostName"
        IpAddressrep[parsedData?.PrRoboCopyMonitorngModel?.Type] = checkCondition(parsedData?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_IpAddress + "$" + IpAddressrep?.pr_Icon)

        hostdata.pr_Icon = parseStatus(value?.prServerStatus)
        hostdata.dr_Icon = parseStatus(value?.drServerStatus)
        hostdata.label = "HostName"
        hostdata[parsedData?.PrRoboCopyMonitorngModel?.Type] = checkCondition(parsedData?.PrRoboCopyMonitorngModel?.MonitoringModel?.PR_Server_HostName + "$" + hostdata?.pr_Icon)

        jobdata.pr_Icon = "cp-disable me-1 text-danger"
        jobdata.label = "No.Of Jobs"       

        repheader.push("VM Replication Health Status")
        parsedData?.PrRoboCopyMonitorngModel?.Type ? repheader.push("Source Server") : ""
        header.push("Robocopy_App_Infra")

        parsedData?.PrRoboCopyMonitorngModel?.Type ? header.push("Production Server") : ""
        parsedData?.RoboCopyMonitoringModels.forEach((Custom) => {
            Custom?.Type ? header.push(Custom?.Type + " Server") : ""
            Custom?.Type ? repheader.push(Custom?.Type + " Server") : ""
            Serverdata[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.Server_Name + "$" + Serverdata?.dr_Icon)
            IPAddressdata[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.Server_IpAddress + "$" + IPAddressdata?.dr_Icon)
            IpAddressrep[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.Server_IpAddress + "$" + IpAddressrep?.dr_Icon)
            hostdata[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.Server_HostName + "$" + hostdata?.dr_Icon)
            if (Array.isArray(Custom?.MonitoringModel?.RoboCopyMonitorModel)) {
                jobdata[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.RoboCopyMonitorModel?.length + "$" + '')
            } else {
                jobdata[Custom?.Type] = checkCondition(Custom?.MonitoringModel?.RoboCopyMonitorModel?.length ?? 0 + "$" + '')
            }
            jobdata[parsedData?.PrRoboCopyMonitorngModel?.Type] = ''
        })
        rowData.push(Serverdata, IPAddressdata)

        repData.push(IpAddressrep, hostdata, jobdata)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "mssqlalwaysonavailabilitygroup") {
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata = {}
        let IpAddress = {}
        let replicationType = {}
        let State = {}
        let Role = {}
        let GroupName = {}
        let Mode = {}

        Serverdata.label = "Server Name"
        Serverdata.pr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata.dr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.ServerName + "$" + Serverdata?.pr_Icon)
       
        const drIcons = [];
        glob?.serverDto?.forEach(server => {
            if (server?.type?.toLowerCase()?.includes('dr')) {
                const iconClass = parseStatus(server?.status);
                drIcons.push(iconClass)
            }
           
        })
       
        IpAddress.label = "IP Address/HostName"
        IpAddress.pr_Icon = parseStatus(value?.prServerStatus)
        IpAddress.dr_Icon = drIcons
        IpAddress[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.IpAddress + "$" + IpAddress?.pr_Icon)

        State.label = "Availability Group Connected State"
        State.pr_Icon = parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState?.toLowerCase() === "connected" ? "cp-connected me-1 text-success" : parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState?.toLowerCase() === "disconnected" ? "cp-disconnected me-1 text-danger" : "cp-disable me-1 text-danger";
        State.dr_Icon = ''
        State[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState + "$" + State?.pr_Icon)

        Role.label = "Availability Group Role"
        Role.pr_Icon = parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupRole?.toLowerCase() === "primary" ? "text-primary me-1 cp-list-prsite" : parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupRole?.toLowerCase() === "secondary" ? "text-info me-1 cp-dr" : "cp-disable me-1 text-danger";
        Role.dr_Icon = ''
        Role[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupRole + "$" + Role?.pr_Icon)

        GroupName.label = "Availability Group Name"
        GroupName.pr_Icon = 'cp-group text-primary me-1'
        GroupName.dr_Icon = 'cp-group text-primary me-1'
        GroupName[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.AvailabilityGroupName + "$" + GroupName?.pr_Icon)

        replicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        replicationType.label = "Replication Type"
        replicationType[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(value?.replicationType + "$" + replicationType.pr_Icon)
        replicationType[parsedData?.AlwaysOnAvailabilityGroupMonitoringDetails?.Type] = ''

        Mode.pr_Icon = parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.ReplicaMode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.ReplicaMode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
        Mode.dr_Icon = ''
        Mode.label = "Replica Mode"
        Mode[parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type] = checkCondition(parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.AvailabilityGroupMonitoring?.ReplicaMode + "$" + Mode.pr_Icon)
        

        header.push("Component Monitor")
        parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type ? header.push("Production Server") : ""
        repheader.push("Replication Monitor")
        parsedData?.PrAlwaysOnAvailabilityGroupMonitoringDetails?.Type ? repheader.push("Production Server") : ""

        parsedData?.AlwaysOnAvailabilityGroupMonitoringDetails?.forEach((bind) => {
            if (bind?.Type && !header.includes(bind.Type + " Server")) {
                header.push(bind?.Type + " Server");
            }
            if (bind?.Type && !repheader.includes(bind?.Type + " Server")) {
                repheader.push(bind?.Type + " Server");
            }
            let drServers = []
            let drIPAddress = []
            let drState = []
            let drRole = []
            let drAGName = []
            let drMode = []
            bind?.AlwaysOnAvailabilityGroupMonitoringDetailsList?.forEach(list => {                
                drState.push(list?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState)
                drServers.push(list?.ServerName);
                drIPAddress.push(list?.IpAddress);
                drRole.push(list?.AvailabilityGroupMonitoring?.AvailabilityGroupRole)
                drAGName.push(list?.AvailabilityGroupMonitoring?.AvailabilityGroupName)
                drMode.push(list?.AvailabilityGroupMonitoring?.ReplicaMode)

                State.dr_Icon = list?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState?.toLowerCase() === "connected" ? "cp-connected me-1 text-success" : list?.AvailabilityGroupMonitoring?.AvailabilityGroupConnnectedState?.toLowerCase()?.toLowerCase() === "disconnected" ? "cp-disconnected me-1 text-danger" : "cp-disable me-1 text-danger";
                Role.dr_Icon = list?.AvailabilityGroupMonitoring?.AvailabilityGroupRole?.toLowerCase() === "primary" ? "text-primary me-1 cp-list-prsite" : list?.AvailabilityGroupMonitoring?.AvailabilityGroupRole?.toLowerCase() === "secondary" ? "text-info me-1 cp-dr" : "cp-disable me-1 text-danger";
                Mode.dr_Icon = list?.AvailabilityGroupMonitoring?.ReplicaMode?.toLowerCase()?.substring(0, 6) === 'asynch' ? "cp-refresh me-1 text-danger" : list?.AvailabilityGroupMonitoring?.ReplicaMode?.toLowerCase()?.substring(0, 6) === 'synchr' ? "cp-refresh me-1 text-success" : "cp-disable me-1 text-danger";
            })
            const typeKey = bind?.Type || "UnknownType";
            Serverdata[typeKey] = checkCondition(`${drServers.join(', ')} $ ${Serverdata?.dr_Icon || ''}`);
            IpAddress[typeKey] = checkCondition(`${drIPAddress.join(', ')} $ ${IpAddress?.dr_Icon || ''}`);
            State[typeKey] = checkCondition(`${drState.join(', ')} $ ${State?.dr_Icon || ''}`);
            Role[typeKey] = checkCondition(`${drRole.join(', ')} $ ${Role?.dr_Icon || ''}`);
            GroupName[typeKey] = checkCondition(`${drAGName.join(', ')} $ ${GroupName?.dr_Icon || ''}`);
            Mode[typeKey] = checkCondition(`${drMode.join(', ')} $ ${Mode?.dr_Icon || ''}`);
            
        })
        rowData.push(Serverdata, IpAddress,State,Role,GroupName)
        repData.push(replicationType,Mode)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "rsyncappreplication") {        
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata1 = {}
        let IPAddressdata1 = {}
        let primarydata = {}
        let secondarydata = {}
        let sourcedata = {}
        let destdata = {}
        let filedata = {}
        let sizedata = {}
        let regulardata = {}
        let transdata = {}
        Serverdata1.label = "Server Name"
        Serverdata1.pr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata1.dr_Icon = "cp-stand-server me-1 text-primary"
        Serverdata1[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition(parsedData?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_Name + "$" + Serverdata1?.pr_Icon)

        IPAddressdata1.pr_Icon = parseStatus(parsedData?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_Status)
        IPAddressdata1.dr_Icon = parseStatus(parsedData?.RSyncReplicationModels[0]?.MonitoringModel?.Server_Status)
        IPAddressdata1.label = "IP Address/HostName"
        IPAddressdata1[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition(parsedData?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress + "$" + IPAddressdata1?.pr_Icon)

        primarydata.label = "Primary Server"
        primarydata.pr_Icon = parseStatus(value?.prServerStatus)
        primarydata.dr_Icon = parseStatus(value?.drServerStatus)
        primarydata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition(parsedData?.PrRSyncReplicationModel?.PrMonitoringModel?.PR_Server_IpAddress + "$" + primarydata?.pr_Icon)

        secondarydata.label = "Secondary Server"
        secondarydata.pr_Icon = "cp-disable me-1 text-danger"
        secondarydata.dr_Icon = parseStatus(value?.drServerStatus)
        secondarydata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + secondarydata?.pr_Icon)

        sourcedata.label = "Source Replication Path"
        sourcedata.pr_Icon = "cp-disable me-1 text-danger"
        sourcedata.dr_Icon = "cp-report-path me-1 text-primary"
        sourcedata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + sourcedata?.pr_Icon)

        destdata.label = "Destination Path"
        destdata.pr_Icon = "cp-disable me-1 text-danger"
        destdata.dr_Icon = "cp-report-path me-1 text-primary"
        destdata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + destdata?.pr_Icon)

        filedata.label = "Number of files"
        filedata.pr_Icon = "cp-disable me-1 text-danger"
        filedata.dr_Icon = "cp-files me-1 text-primary"
        filedata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + filedata?.pr_Icon)

        sizedata.label = "Total file size"
        sizedata.pr_Icon = "cp-disable me-1 text-danger"
        sizedata.dr_Icon = "cp-file-size me-1 text-primary"
        sizedata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + sizedata?.pr_Icon)

        regulardata.label = "Number of regular files transferred"
        regulardata.pr_Icon = "cp-disable me-1 text-danger"
        regulardata.dr_Icon = "cp-files me-1 text-primary"
        regulardata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + regulardata?.pr_Icon)

        transdata.label = "Total transferred file size"
        transdata.pr_Icon = "cp-disable me-1 text-danger"
        transdata.dr_Icon = "cp-file-size me-1 text-primary"
        transdata[parsedData?.PrRSyncReplicationModel?.Type] = checkCondition('NA' + "$" + transdata?.pr_Icon)

        repheader.push("Replication Monitor")
        //parsedData?.PrRSyncReplicationModel?.Type ? repheader.push("Production Server") : ""
        header.push("Component Monitor")

        parsedData?.PrRSyncReplicationModel?.Type ? header.push("Production Server") : ""

        parsedData?.RSyncReplicationModels?.forEach((bind) => {
            if (bind.MonitoringModel) {


                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }

                let sourcePaths = [];
                let destPaths = [];
                let filePaths = [];
                let sizePaths = [];
                let regularPaths = [];
                let transPaths = [];
                bind.MonitoringModel.RSyncMonitorModel.forEach((Custom) => {
                    const typeKey = bind?.Type || "UnknownType";

                    sourcePaths.push(Custom?.SourcePath);
                    destPaths.push(Custom?.DestinationPath);
                    filePaths.push(Custom?.TotalNumberoffiles);
                    sizePaths.push(Custom?.TotalFilesSize);
                    regularPaths.push(Custom?.NumberOfRegFilesTransfer);
                    transPaths.push(Custom?.TotalTransferfileSize);
                    Serverdata1[typeKey] = checkCondition(`${bind?.MonitoringModel?.Server_Name} $ ${Serverdata1?.dr_Icon || ''}`);
                    IPAddressdata1[typeKey] = checkCondition(`${bind?.MonitoringModel?.Server_IpAddress} $ ${IPAddressdata1?.dr_Icon || ''}`);
                    secondarydata[typeKey] = checkCondition(`${bind?.MonitoringModel?.Server_IpAddress} $ ${secondarydata?.dr_Icon || ''}`);
                });


                const typeKey = bind?.Type || "UnknownType";
                sourcedata[typeKey] = checkCondition(`${sourcePaths.join(', ')} $ ${sourcedata?.dr_Icon || ''}`);
                destdata[typeKey] = checkCondition(`${destPaths.join(', ')} $ ${destdata?.dr_Icon || ''}`);
                filedata[typeKey] = checkCondition(`${filePaths.join(', ')} $ ${filedata?.dr_Icon || ''}`);
                sizedata[typeKey] = checkCondition(`${sizePaths.join(', ')} $ ${sizedata?.dr_Icon || ''}`);
                regulardata[typeKey] = checkCondition(`${regularPaths.join(', ')} $ ${regulardata?.dr_Icon || ''}`);
                transdata[typeKey] = checkCondition(`${transPaths.join(', ')} $ ${transdata?.dr_Icon || ''}`);
            }
        });

        rowData.push(Serverdata1, IPAddressdata1)

        repData.push(primarydata, secondarydata, sourcedata, destdata, filedata, sizedata, regulardata, transdata)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "datasyncappreplication") {        
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata1 = {}
        let IpAddress = {}
        let replicationType = {}

        Serverdata1.label = "Server Name"
        Serverdata1.pr_Icon = "text-primary cp-server me-1 fs-6"
        Serverdata1.dr_Icon = "text-primary cp-server me-1 fs-6"
        Serverdata1[parsedData?.PrDataSyncReplicationModel?.Type] = checkCondition(parsedData?.PrDataSyncReplicationModel?.PrMonitoringModel
            ?.PR_Server_Name + "$" + Serverdata1?.pr_Icon)

        IpAddress.label = "IP Address/HostName"
        IpAddress.pr_Icon = parseStatus(parsedData?.PrDataSyncReplicationModel?.PrMonitoringModel?.PR_Server_Status)
        IpAddress.dr_Icon = parseStatus(parsedData?.DataSyncReplicationModels[0]?.MonitoringModel?.Server_Status)
        IpAddress[parsedData?.PrDataSyncReplicationModel?.Type] = checkCondition(parsedData?.PrDataSyncReplicationModel?.PrMonitoringModel
            ?.PR_Server_IpAddress + "$" + IpAddress?.pr_Icon)

        replicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        replicationType.label = "Replication Type"
        replicationType[parsedData?.Hp3ParReplicationPRModel?.Type] = checkCondition(value.replicationType + "$" + replicationType.pr_Icon)

        header.push("Component Monitor")

        /* parsedData?.PrDataSyncReplicationModel?.Type ? header.push("Primary Server") : ""*/
        
        if (parsedData?.PrDataSyncReplicationModel) {
            if (parsedData.PrDataSyncReplicationModel.Type !== undefined || Object.keys(parsedData.PrDataSyncReplicationModel)?.length > 0) {
                header.push("Primary Server");
            }
        }
        repheader.push("Replication Monitor")

        parsedData?.PrDataSyncReplicationModel?.Type ? repheader.push("Source Server") : ""
        parsedData?.DataSyncReplicationModels?.forEach((bind) => {
            if (bind?.MonitoringModel) {
                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }
                Serverdata1[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Server_Name} $ ${Serverdata1?.dr_Icon || ''}`);

                IpAddress[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Server_IpAddress} $ ${IpAddress?.dr_Icon || ''}`);
            }
        })
        rowData.push(Serverdata1, IpAddress)
        repData.push(replicationType)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "zertovpg") {

        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata = {}
        let IpAddress = {}

        Serverdata.label = "Server Name"
        Serverdata.pr_Icon = "text-primary cp-server me-1 fs-6"
        Serverdata.dr_Icon = "text-primary cp-server me-1 fs-6"
        Serverdata[parsedData?.ZertoVPGMonitoringPR?.Type] = checkCondition(parsedData?.ZertoVPGMonitoringPR?.ReplicationMonitoringPR?.ServerName + "$" + Serverdata?.pr_Icon)

        IpAddress.label = "IP Address/HostName"
        IpAddress.pr_Icon = parseStatus(value?.prServerStatus)
        IpAddress.dr_Icon = parseStatus(value?.drServerStatus)
        IpAddress[parsedData?.ZertoVPGMonitoringPR?.Type] = checkCondition(parsedData?.ZertoVPGMonitoringPR?.ReplicationMonitoringPR
            ?.IPAddress + "$" + IpAddress?.pr_Icon)

        header.push("Component Monitor")

        parsedData?.ZertoVPGMonitoringPR?.Type ? header.push("Source Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.ZertoVPGMonitoringPR?.Type ? repheader.push("Source Server") : ""
        parsedData?.ZertoVPGMonitoring?.forEach((bind) => {
            if (bind?.ReplicationMonitoring) {
                if (bind?.Type && !header.includes(bind?.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind?.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }
                Serverdata[bind?.Type] = checkCondition(`${bind?.ReplicationMonitoring?.ServerName} $ ${Serverdata?.dr_Icon || ''}`);

                IpAddress[bind?.Type] = checkCondition(`${bind?.ReplicationMonitoring?.IPAddress} $ ${IpAddress?.dr_Icon || ''}`);
            }
        })
        rowData.push(Serverdata, IpAddress)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "windowsactivedirectory") {
        
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverdata1 = {}
        let Domain = {}
        let DomainController = {}
        let ADSite = {}
        let Partner = {}
        let RepFailure = {}
        let lastReplica = {}
        let lastReplicaSuccess = {}
        let lastRepResult = {}
        let RecordTime = {}
        let Datalag = {}

        Serverdata1.label = "IP Address"
        Serverdata1.pr_Icon = "text-success cp-fal-server me-1 fs-6"
        Serverdata1.dr_Icon = "text-success cp-fal-server me-1 fs-6"
        Serverdata1[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.IPAddress + "$" + Serverdata1?.pr_Icon)

        Domain.label = "Domain Name"
        Domain.pr_Icon = "text-primary cp-command-center me-1 fs-6"
        Domain.dr_Icon = "text-primary cp-command-center me-1 fs-6"
        Domain[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.DomainName + "$" + Domain?.pr_Icon)

        DomainController.label = "Domain Controller Name"
        DomainController.pr_Icon = "text-primary cp-site-names me-1 fs-6"
        DomainController.dr_Icon = "text-primary cp-site-names me-1 fs-6"
        DomainController[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.DomainControllerName + "$" + DomainController?.pr_Icon)

        ADSite.label = "AD Replication Site"
        ADSite.pr_Icon = "text-primary cp-site-names me-1 fs-6"
        ADSite.dr_Icon = "text-primary cp-site-names me-1 fs-6"
        ADSite[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.ADReplicationSite + "$" + ADSite?.pr_Icon)

        Partner.label = "Partner Name"
        Partner.pr_Icon = "text-primary cp-files me-1 fs-6"
        Partner.dr_Icon = "text-primary cp-files me-1 fs-6"
        Partner[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.ReplicationPartnerName + "$" + Partner?.pr_Icon)

        RepFailure.label = "Consecutive Replication Failures"
        RepFailure.pr_Icon = "text-primary cp-files me-1 fs-6"
        RepFailure.dr_Icon = "text-primary cp-files me-1 fs-6"
        RepFailure[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.ConsecutiveReplicationFailures + "$" + RepFailure?.pr_Icon)

        lastReplica.label = "Last Replication Attempt"
        lastReplica.pr_Icon = "text-success cp-estimated-time me-1 fs-6"
        lastReplica.dr_Icon = "text-success cp-estimated-time me-1 fs-6"
        lastReplica[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.LastReplicationAttempt + "$" + lastReplica?.pr_Icon)

        lastReplicaSuccess.label = "Last Replication Success"
        lastReplicaSuccess.pr_Icon = "text-success cp-time me-1 fs-6"
        lastReplicaSuccess.dr_Icon = "text-success cp-time me-1 fs-6"
        lastReplicaSuccess[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.LastReplicationSuccess + "$" + lastReplicaSuccess?.pr_Icon)

        lastRepResult.label = "Last Replication Result"
        lastRepResult.pr_Icon = "text-primary cp-snap-file me-1 fs-6"
        lastRepResult.dr_Icon = "text-primary cp-snap-file me-1 fs-6"
        lastRepResult[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.LastReplicationResult + "$" + lastRepResult?.pr_Icon)

        RecordTime.label = "Replication Failure First Recorded time"
        RecordTime.pr_Icon = "text-primary cp-success me-1 fs-6"
        RecordTime.dr_Icon = "text-primary cp-success me-1 fs-6"
        RecordTime[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.ReplicationFailureFirstRecordedTime + "$" + RecordTime?.pr_Icon)

        Datalag.label = "DataLag"
        Datalag.pr_Icon = "text-primary cp-time me-1 fs-6"
        Datalag.dr_Icon = "text-primary cp-time me-1 fs-6"
        Datalag[parsedData?.PrActiveDirectoryReplication?.Type] = checkCondition(parsedData?.PrActiveDirectoryReplication?.ActiveDirectoryMonitoring
            ?.DataLag + "$" + Datalag?.pr_Icon)

        header.push("Component Monitor")

        parsedData?.PrActiveDirectoryReplication?.Type ? header.push("Primary Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.PrActiveDirectoryReplication?.Type ? repheader.push("Source Server") : ""

        parsedData?.ActiveDirectoryReplication?.forEach((bind) => {
            if (bind?.ActiveDirectoryMonitoring) {
                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }
                Serverdata1[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.IPAddress} $ ${Serverdata1?.dr_Icon || ''}`);

                Domain[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.DomainName} $ ${Domain?.dr_Icon || ''}`);

                DomainController[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.DomainControllerName} $ ${DomainController?.dr_Icon || ''}`);

                ADSite[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.ADReplicationSite} $ ${ADSite?.dr_Icon || ''}`);

                Partner[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.ReplicationPartnerName} $ ${Partner?.dr_Icon || ''}`);

                RepFailure[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.ConsecutiveReplicationFailures} $ ${RepFailure?.dr_Icon || ''}`);

                lastReplica[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.LastReplicationAttempt} $ ${lastReplica?.dr_Icon || ''}`);

                lastReplicaSuccess[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.LastReplicationSuccess} $ ${lastReplicaSuccess?.dr_Icon || ''}`);

                lastRepResult[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.LastReplicationResult} $ ${lastRepResult?.dr_Icon || ''}`);

                RecordTime[bind?.Type] = checkCondition(`${bind?.ActiveDirectoryMonitoring?.ReplicationFailureFirstRecordedTime} $ ${RecordTime?.dr_Icon || ''}`);

               
            }
        })
        rowData.push(Serverdata1, Domain, DomainController, ADSite, Partner)
        repData.push(RepFailure, lastReplica, lastReplicaSuccess, lastRepResult, RecordTime, Datalag)
    }
   
    if (parsedData?.Monitor_Type?.toLowerCase() === "azuremysqlpaas") {
      
        header = []
        rowData = []
        repheader = []
        repData = []
        let Servermysql = {}
        let Versionmysql = {}
        let Fqdnmysql = {}
        let Groupmysql = {}
        let Locationmysql = {}
        let Statusmysql = {}
        let Rolemysql = {}
        let Lagmysql = {}

        Servermysql.label = "MySQL Server Name"
        Servermysql.pr_Icon = "cp-server me-1 text-primary"
        Servermysql.dr_Icon = "cp-server me-1 text-primary"
        Servermysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerName + "$" + Servermysql?.pr_Icon)

        Versionmysql.pr_Icon = "cp-version me-1 text-primary"
        Versionmysql.dr_Icon = "cp-version me-1 text-primary"
        Versionmysql.label = "MySQL Server Version"
        Versionmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerVersion + "$" + Versionmysql?.pr_Icon)

        Fqdnmysql.label = "MySQL Server FQDN"
        Fqdnmysql.pr_Icon = "cp-control-file-type me-1 text-primary"
        Fqdnmysql.dr_Icon = "cp-control-file-type me-1 text-primary"
        Fqdnmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerFQDN + "$" + Fqdnmysql?.pr_Icon)

        Groupmysql.label = "MySQL Server Resource Group Name"
        Groupmysql.pr_Icon = "cp-manage-server me-1 text-primary"
        Groupmysql.dr_Icon = "cp-manage-server me-1 text-primary"
        Groupmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerResourceGroupName + "$" + Groupmysql?.pr_Icon)

        Locationmysql.label = "MySQL Server Location(Region)"
        Locationmysql.pr_Icon = "cp-location me-1 text-primary"
        Locationmysql.dr_Icon = "cp-location me-1 text-primary"
        Locationmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerLocation + "$" + Locationmysql?.pr_Icon)

        Statusmysql.label = "MySQL Server Status"
        Statusmysql.pr_Icon = "cp-reload cp-animate me-1 text-success"
        Statusmysql.dr_Icon = "cp-reload cp-animate me-1 text-success"
        Statusmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ServerStatus + "$" + Statusmysql?.pr_Icon)

        Rolemysql.label = "MySQL Replication Role"
        Rolemysql.pr_Icon = "cp-replication-type me-1 text-primary"
        Rolemysql.dr_Icon = "cp-replication-type me-1 text-primary"
        Rolemysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.ReplicationRole + "$" + Rolemysql?.pr_Icon)

        Lagmysql.label = "Datalag(Sec)"
        Lagmysql.pr_Icon = "cp-time text-primary mt-2"
        Lagmysql.dr_Icon = "cp-time text-primary mt-2"
        Lagmysql[parsedData?.AzureMysqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMysqlPaasPRMonitoring?.Primary?.Datalag + "$" + Lagmysql?.pr_Icon)


        header.push("Component Monitor")

        parsedData?.AzureMysqlPaasPRMonitoring?.Type ? header.push("Primary Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.AzureMysqlPaasPRMonitoring?.Type ? repheader.push("Source Server") : ""

        parsedData?.AzureMysqlPaasMonitoring?.forEach((bind) => {
            if (bind.Replica) {


                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }


                Servermysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerName} $ ${Servermysql?.dr_Icon || ''}`);
                Versionmysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerVersion} $ ${Versionmysql?.dr_Icon || ''}`);
                Fqdnmysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerFQDN} $ ${Fqdnmysql?.dr_Icon || ''}`);
                Groupmysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerResourceGroupName} $ ${Groupmysql?.dr_Icon || ''}`);
                Locationmysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerLocation} $ ${Locationmysql?.dr_Icon || ''}`);
                Statusmysql[bind?.Type] = checkCondition(`${bind?.Replica?.ServerStatus} $ ${Statusmysql?.dr_Icon || ''}`);
                Rolemysql[bind?.Type] = checkCondition(`${bind?.Replica?.ReplicationRole} $ ${Rolemysql?.dr_Icon || ''}`);
                Lagmysql[bind?.Type] = checkCondition(`${bind?.Replica?.Datalag} $ ${Lagmysql?.dr_Icon || ''}`);

            }
        });


        rowData.push(Servermysql, Versionmysql, Fqdnmysql, Groupmysql, Locationmysql, Statusmysql)
        repData.push(Rolemysql, Lagmysql)

    }
    
    if (parsedData?.Monitor_Type?.toLowerCase() === "azurestorageaccount") {
        
        header = []
        rowData = []
        repheader = []
        repData = []
        let location = {}
        let Diskstate = {}
        let GeoReplicationType = {}
        let FailoverProgressState = {}
        let LastGeoFailoverTime = {}
        let LastSyncTime = {}
        let DataLag = {}

        location.label = "Location"
        location.pr_Icon = "cp-location me-1 text-primary"
        location.dr_Icon = "cp-location me-1 text-primary"
        location[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Primary?.Location
            + "$" + location?.pr_Icon)

        Diskstate.label = "Diskstate"
        Diskstate.pr_Icon = "text-success cp-success me-1"
        Diskstate.dr_Icon = "text-success cp-success me-1"
        Diskstate[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Primary?.Diskstate
            + "$" + Diskstate?.pr_Icon)

        GeoReplicationType.label = "Geo Replication Type"
        GeoReplicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        GeoReplicationType.dr_Icon = "cp-replication-type me-1 text-primary"
        GeoReplicationType[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication?.GeoReplicationType
            + "$" + GeoReplicationType?.pr_Icon)

        DataLag.label = "Datalag(Sec)"
        DataLag.pr_Icon = "cp-time text-primary mt-2"
        DataLag.dr_Icon = "cp-time text-primary mt-2"
        DataLag[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication?.DataLag
            + "$" + DataLag?.pr_Icon)

        FailoverProgressState.label = "Failover Progress State"
        FailoverProgressState.pr_Icon = "text-danger cp-not-applicable me-1"
        FailoverProgressState.dr_Icon = "text-danger cp-not-applicable me-1"
        FailoverProgressState[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication?.FailoverProgressState
            + "$" + FailoverProgressState?.pr_Icon)

        LastGeoFailoverTime.label = "Last Geo FailoverTime"
        LastGeoFailoverTime.pr_Icon = "text-primary cp-calendar me-1"
        LastGeoFailoverTime.dr_Icon = "text-primary cp-calendar me-1"
        LastGeoFailoverTime[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication?.LastGeoFailoverTime
            + "$" + LastGeoFailoverTime?.pr_Icon)

        LastSyncTime.label = "Last Sync Time"
        LastSyncTime.pr_Icon = "text-primary cp-calendar me-1"
        LastSyncTime.dr_Icon = "text-primary cp-calendar me-1"
        LastSyncTime[parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type] = checkCondition(parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication?.LastSyncTime
            + "$" + LastSyncTime?.pr_Icon)

        header.push("Component Monitor")

        parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type ? header.push("Source Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring?.Type ? repheader.push("Replication Details") : ""
        parsedData?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring?.forEach((bind) => {
            if (bind.Secondary) {
                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                
                location[bind?.Type] = checkCondition(`${bind?.Secondary?.Location} $ ${location?.dr_Icon || ''}`);
                Diskstate[bind?.Type] = checkCondition(`${bind?.Secondary?.Diskstate} $ ${Diskstate?.dr_Icon || ''}`);
            }
        })

        let repli = parsedData?.AzureStorageAccountMonitoring
            ?.AzureStorageAccountReplication
        if (repli) {
            if (repli?.Type && !repheader.includes(bind.Type + " Server")) {
                repheader.push(repli?.Type + " Server");
            }
            GeoReplicationType[repli?.Type] = checkCondition(`${repli?.GeoReplicationType} $ ${GeoReplicationType?.dr_Icon || ''}`);
            DataLag[repli?.Type] = checkCondition(`${repli?.DataLag} $ ${DataLag?.dr_Icon || ''}`);
            FailoverProgressState[repli?.Type] = checkCondition(`${repli?.FailoverProgressState} $ ${FailoverProgressState?.dr_Icon || ''}`);
            LastGeoFailoverTime[repli?.Type] = checkCondition(`${repli?.LastGeoFailoverTime} $ ${LastGeoFailoverTime?.dr_Icon || ''}`);
            LastSyncTime[repli?.Type] = checkCondition(`${repli?.LastSyncTime} $ ${LastSyncTime?.dr_Icon || ''}`);
        }
        rowData.push(location, Diskstate)
        repData.push(GeoReplicationType, DataLag, FailoverProgressState, LastGeoFailoverTime, LastSyncTime)
    }
    
    if (parsedData?.Monitor_Type?.toLowerCase() === "azurepostgrespaas") {
        
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverpostgres = {}
        let Versionpostgres = {}
        let Fqdnpostgres = {}
        let Grouppostgres = {}
        let Locationpostgres = {}
        let Statuspostgres = {}
        let Rolepostgres = {}
        let Lagpostgres = {}

        Serverpostgres.label = "MySQL Server Name"
        Serverpostgres.pr_Icon = "cp-server me-1 text-primary"
        Serverpostgres.dr_Icon = "cp-server me-1 text-primary"
        Serverpostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerName + "$" + Serverpostgres?.pr_Icon)

        Versionpostgres.pr_Icon = "cp-version me-1 text-primary"
        Versionpostgres.dr_Icon = "cp-version me-1 text-primary"
        Versionpostgres.label = "MySQL Server Version"
        Versionpostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerVersion + "$" + Versionpostgres?.pr_Icon)

        Fqdnpostgres.label = "MySQL Server FQDN"
        Fqdnpostgres.pr_Icon = "cp-control-file-type me-1 text-primary"
        Fqdnpostgres.dr_Icon = "cp-control-file-type me-1 text-primary"
        Fqdnpostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerFQDN + "$" + Fqdnpostgres?.pr_Icon)

        Grouppostgres.label = "MySQL Server Resource Group Name"
        Grouppostgres.pr_Icon = "cp-manage-server me-1 text-primary"
        Grouppostgres.dr_Icon = "cp-manage-server me-1 text-primary"
        Grouppostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerResourceGroupName + "$" + Grouppostgres?.pr_Icon)

        Locationpostgres.label = "MySQL Server Location(Region)"
        Locationpostgres.pr_Icon = "cp-location me-1 text-primary"
        Locationpostgres.dr_Icon = "cp-location me-1 text-primary"
        Locationpostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerLocation + "$" + Locationpostgres?.pr_Icon)

        Statuspostgres.label = "MySQL Server Status"
        Statuspostgres.pr_Icon = "cp-reload cp-animate me-1 text-success"
        Statuspostgres.dr_Icon = "cp-reload cp-animate me-1 text-success"
        Statuspostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ServerStatus + "$" + Statuspostgres?.pr_Icon)

        Rolepostgres.label = "MySQL Replication Role"
        Rolepostgres.pr_Icon = "cp-replication-type me-1 text-primary"
        Rolepostgres.dr_Icon = "cp-replication-type me-1 text-primary"
        Rolepostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.ReplicationRole + "$" + Rolepostgres?.pr_Icon)

        Lagpostgres.label = "Datalag(Sec)"
        Lagpostgres.pr_Icon = "cp-time text-primary mt-2"
        Lagpostgres.dr_Icon = "cp-time text-primary mt-2"
        Lagpostgres[parsedData?.AzurePostgresPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzurePostgresPaasPRMonitoring?.Primary?.Datalag + "$" + Lagpostgres?.pr_Icon)




        header.push("Component Monitor")

        parsedData?.AzurePostgresPaasPRMonitoring?.Type ? header.push("Source Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.AzurePostgresPaasPRMonitoring?.Type ? repheader.push("Source Server") : ""

        parsedData?.AzurePostgresPaasMonitoring?.forEach((bind) => {
            if (bind.Replica) {


                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }


                Serverpostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerName} $ ${Serverpostgres?.dr_Icon || ''}`);
                Versionpostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerVersion} $ ${Versionpostgres?.dr_Icon || ''}`);
                Fqdnpostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerFQDN} $ ${Fqdnpostgres?.dr_Icon || ''}`);
                Grouppostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerResourceGroupName} $ ${Grouppostgres?.dr_Icon || ''}`);
                Locationpostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerLocation} $ ${Locationpostgres?.dr_Icon || ''}`);
                Statuspostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerStatus} $ ${Statuspostgres?.dr_Icon || ''}`);
                Rolepostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ReplicationRole} $ ${Rolepostgres?.dr_Icon || ''}`);
                Lagpostgres[bind?.Type] = checkCondition(`${bind?.Replica?.Datalag} $ ${Lagpostgres?.dr_Icon || ''}`);

            }
        });


        rowData.push(Serverpostgres, Versionpostgres, Fqdnpostgres, Grouppostgres, Locationpostgres, Statuspostgres)
        repData.push(Rolepostgres, Lagpostgres)
    }
   
    if (parsedData?.Monitor_Type?.toLowerCase() === "azuremssqlpaas") {
        
        header = []
        rowData = []
        repheader = []
        repData = []
        let ServerName = {}
        let DatabaseName = {}
        let ServerFQDN = {}
        let ServerRegion = {}
        //let ServerRole = {}
        let DatabaseStatus = {}
        let ReplicationType = {}
        let ReplicationState = {}
        let Datalag = {}

        ServerName.label = "Server Name"
        ServerName.pr_Icon = "cp-server me-1 text-primary"
        ServerName.dr_Icon = "cp-server me-1 text-primary"
        ServerName[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.ServerName + "$" + ServerName?.pr_Icon)

        ServerFQDN.label = "Server FQDN"
        ServerFQDN.pr_Icon = "cp-control-file-type me-1 text-primary"
        ServerFQDN.dr_Icon = "cp-control-file-type me-1 text-primary"
        ServerFQDN[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.ServerFQDN + "$" + ServerFQDN?.pr_Icon)

        ServerRegion.label = "Server Location(Region)"
        ServerRegion.pr_Icon = "cp-location me-1 text-primary"
        ServerRegion.dr_Icon = "cp-location me-1 text-primary"
        ServerRegion[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.ServerRegion + "$" + ServerRegion?.pr_Icon)

        DatabaseName.label = "Database Name"
        DatabaseName.pr_Icon = "cp-database me-1 text-primary me-1"
        DatabaseName.dr_Icon = "cp-database me-1 text-primary me-1"
        DatabaseName[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.DatabaseName + "$" + DatabaseName?.pr_Icon)

        DatabaseStatus.label = "Database Status"
        DatabaseStatus.pr_Icon = "cp-database me-1 text-primary me-1"
        DatabaseStatus.dr_Icon = "cp-database me-1 text-primary me-1"
        DatabaseStatus[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.DatabaseStatus + "$" + DatabaseStatus?.pr_Icon)

        ReplicationType.label = "Replication Type"
        ReplicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        ReplicationType.dr_Icon = "cp-replication-type me-1 text-primary"
        ReplicationType[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.ReplicationType + "$" + ReplicationType?.pr_Icon)

        ReplicationState.label = "Replication State"
        ReplicationState.pr_Icon = "cp-replication-type me-1 text-primary"
        ReplicationState.dr_Icon = "cp-replication-type me-1 text-primary"
        ReplicationState[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.ReplicationState + "$" + ReplicationState?.pr_Icon)

        Datalag.label = "Datalag(Sec)"
        Datalag.pr_Icon = "cp-time text-primary mt-2"
        Datalag.dr_Icon = "cp-time text-primary mt-2"
        Datalag[parsedData?.AzureMssqlPaasPRMonitoring?.Type] = checkCondition(parsedData?.AzureMssqlPaasPRMonitoring?.Primary?.Datalag + "$" + Datalag?.pr_Icon)

        header.push("Component Monitor")

        parsedData?.AzureMssqlPaasPRMonitoring?.Type ? header.push("Source Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.AzureMssqlPaasPRMonitoring?.Type ? repheader.push("Source Server") : ""

        parsedData?.AzureMssqlPaasMonitoring?.forEach((bind) => {
            
            if (bind?.Replica) {
                

                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }
                ServerName[bind?.Type] = checkCondition(`${bind?.Replica?.ServerName} $ ${ServerName?.dr_Icon || ''}`);
                ServerFQDN[bind?.Type] = checkCondition(`${bind?.Replica?.ServerFQDN} $ ${ServerFQDN?.dr_Icon || ''}`);
                ServerRegion[bind?.Type] = checkCondition(`${bind?.Replica?.ServerRegion} $ ${ServerRegion?.dr_Icon || ''}`);
                DatabaseName[bind?.Type] = checkCondition(`${bind?.Replica?.DatabaseName} $ ${DatabaseName?.dr_Icon || ''}`);
                DatabaseStatus[bind?.Type] = checkCondition(`${bind?.Replica?.DatabaseStatus} $ ${DatabaseStatus?.dr_Icon || ''}`);

                ReplicationType[bind?.Type] = checkCondition(`${bind?.Replica?.ReplicationType} $ ${ReplicationType?.dr_Icon || ''}`);
                ReplicationState[bind?.Type] = checkCondition(`${bind?.Replica?.ReplicationState} $ ${ReplicationState?.dr_Icon || ''}`);
                Datalag[bind?.Type] = checkCondition(`${bind?.Replica?.Datalag} $ ${Datalag?.dr_Icon || ''}`);
            }
        })
        rowData.push(ServerName, ServerFQDN, ServerRegion, DatabaseName, DatabaseStatus)       
        repData.push(ReplicationType, ReplicationState, Datalag)
    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "goldengatereplication") {
        
       
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverpostgres = {}
        let Versionpostgres = {}
        let Fqdnpostgres = {}
        let Grouppostgres = {}
       // let Locationpostgres = {}
       // let Statuspostgres = {}
        let Rolepostgres = {}
        //let Lagpostgres = {}

        Serverpostgres.label = "Server_Name"
        Serverpostgres.pr_Icon = "cp-server me-1 text-primary"
        Serverpostgres.dr_Icon = "cp-server me-1 text-primary"
        Serverpostgres[parsedData?.PrGoldenGateMonitoringModel?.Type] = checkCondition( parsedData?.PrGoldenGateMonitoringModel?.PrMonitoringModel?.Server_Name + "$" + Serverpostgres?.pr_Icon )

        Versionpostgres.pr_Icon = parseStatus(value?.prServerStatus)
        Versionpostgres.dr_Icon = parseStatus(value?.drServerStatus)
        Versionpostgres.label = "IP Address/HostName"
        Versionpostgres[parsedData?.PrGoldenGateMonitoringModel?.Type] = checkCondition( parsedData?.PrGoldenGateMonitoringModel?.PrMonitoringModel?.Server_IpAddress + "$" + Versionpostgres?.pr_Icon)

        Fqdnpostgres.label = "Golden Gate Version"
        Fqdnpostgres.pr_Icon = "cp-version me-1 text-primary"
        Fqdnpostgres.dr_Icon = "cp-version me-1 text-primary"
        Fqdnpostgres[parsedData?.PrGoldenGateMonitoringModel?.Type] = checkCondition( parsedData?.PrGoldenGateMonitoringModel?.PrMonitoringModel?.DbMonitoringDetails?.GGVersion + "$" + Fqdnpostgres?.pr_Icon )

        Grouppostgres.label = "Golden Gate Manager Status"
        Grouppostgres.pr_Icon = "cp-online me-1 text-success"
        Grouppostgres.dr_Icon = "cp-online me-1 text-success"
        Grouppostgres[parsedData?.PrGoldenGateMonitoringModel?.Type] = checkCondition( parsedData?.PrGoldenGateMonitoringModel?.PrMonitoringModel?.DbMonitoringDetails?.GGMgrStatus + "$" + Grouppostgres?.pr_Icon)


        header.push("Component Monitor")

        parsedData?.PrGoldenGateMonitoringModel?.Type ? header.push("Production Server") : ""

        repheader.push("Replication Monitor")
   
        parsedData?.PrGoldenGateMonitoringModel?.Type ? repheader.push("Source Server") : ""

        parsedData?.GoldenGateMonitoringModels?.forEach((bind) => {

        

            if (bind?.Type && !header.includes(bind.Type + " Server")) {
                header.push(bind?.Type + " Server");
            }
            //if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
            //    repheader.push(bind?.Type + " Server");
            //}

      
            Serverpostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Server_Name} $ ${Serverpostgres?.dr_Icon || ''}`);
            Versionpostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Server_IpAddress} $ ${Versionpostgres?.dr_Icon || ''}`);
            Fqdnpostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.DbMonitoringDetails?.GGVersion} $ ${Fqdnpostgres?.dr_Icon || ''}`);
            Grouppostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.DbMonitoringDetails?.GGMgrStatus} $ ${Grouppostgres?.dr_Icon || ''}`);
            //  Locationpostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.ServerLocation} $ ${Locationpostgres?.dr_Icon || ''}`);
            //Statuspostgres[bind?.Type] = checkCondition(`${bind?.Replica?.ServerStatus} $ ${Statuspostgres?.dr_Icon || ''}`);
            //Rolepostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.ReplicationRole} $ ${Rolepostgres?.dr_Icon || ''}`);
            //Lagpostgres[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Datalag} $ ${Lagpostgres?.dr_Icon || ''}`);


        });


        rowData.push(Serverpostgres, Versionpostgres, Fqdnpostgres, Grouppostgres)
        repData.push(Rolepostgres, parsedData)
    }

    if (parsedData?.Monitor_Type.toLowerCase() === "applicationnoreplication") {
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverapplication = {}
        let Ipaddressaplication = {}
        let replicationType = {}
        let ipaddresshost = parsedData?.PrNoReplicationModel?.PrMonitorModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_HostName : parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_IpAddress
        Serverapplication.label = "Server Name"
        Serverapplication.pr_Icon = "cp-server me-1 text-primary"
        Serverapplication.dr_Icon = "cp-server me-1 text-primary"
        Serverapplication[parsedData?.PrNoReplicationModel?.Type] = checkCondition(parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_Name + "$" + Serverapplication?.pr_Icon)

        Ipaddressaplication.pr_Icon = parseStatus(value?.prServerStatus)
        Ipaddressaplication.dr_Icon = parseStatus(value?.drServerStatus)
        Ipaddressaplication.label = "IP Address/HostName"
        Ipaddressaplication[parsedData?.PrNoReplicationModel?.Type] = checkCondition(ipaddresshost + "$" + Ipaddressaplication?.pr_Icon)

        replicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        replicationType.label = "Replication Type"
        replicationType[parsedData?.PrNoReplicationModel?.Type] = checkCondition(value.replicationType + "$" + replicationType.prIcon)

        header.push("Component Monitor")

        parsedData?.PrNoReplicationModel?.Type ? header.push("Production Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.PrNoReplicationModel?.Type ? repheader.push("Production Server") : ""

        parsedData?.NoReplicationModels?.forEach((bind) => {
            if (bind.MonitoringModel) {

                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }

                let driphost = bind?.MonitoringModel?.ConnectViaHostName?.toLowerCase() === "true" ? bind?.MonitoringModel?.Server_HostName : bind?.MonitoringModel?.Server_IpAddress
                Serverapplication[bind?.Type] = checkCondition(`${bind?.MonitoringModel?.Server_Name} $ ${Serverapplication?.dr_Icon || ''}`);
                Ipaddressaplication[bind?.Type] = checkCondition(`${driphost} $ ${Ipaddressaplication?.dr_Icon || ''}`);

            }
        });

        rowData.push(Serverapplication, Ipaddressaplication)
        repData.push(replicationType)
        monitoringServiceDetails = value.monitorServiceDetails

    }
    if (parsedData?.Monitor_Type?.toLowerCase() === "netappsnapmirror") {

        header = []
        rowData = []
        repheader = []
        repData = []
        let Servernetapp = {}
        let Ipaddressnetapp = {}
        let Storagenetapp = {}
        let Volumenetapp = {}
        let Replicationtype = {}
        let Sourcenetapp = {}
        let Destinationnetapp = {}
        let Statenetapp = {}
        let Lagnetapp = {}
        let Statusnetapp = {}

        //  let ipaddresshost = parsedData?.PRNetAppSnapMirrorMonitoring?.PrMonitorModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PRNetAppSnapMirrorMonitoring?.PrMonitorModel?.PR_Server_HostName : parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_IpAddress
        Servernetapp.label = "Server Name"
        Servernetapp.pr_Icon = "cp-server me-1 text-primary"
        Servernetapp.dr_Icon = "cp-server me-1 text-primary"
        Servernetapp[parsedData?.PRNetAppSnapMirrorMonitoring?.Type] = checkCondition(parsedData?.PRNetAppSnapMirrorMonitoring?.PRNetAppSnapMirrorMonitor?.ServerName + "$" + Servernetapp?.pr_Icon)

        Ipaddressnetapp.pr_Icon = parseStatus(value?.prServerStatus)
        Ipaddressnetapp.dr_Icon = parseStatus(value?.drServerStatus)
        Ipaddressnetapp.label = "Server IP/HostName"
        Ipaddressnetapp[parsedData?.PRNetAppSnapMirrorMonitoring?.Type] = checkCondition(parsedData?.PRNetAppSnapMirrorMonitoring?.PRNetAppSnapMirrorMonitor?.IpAddress + "$" + Ipaddressnetapp?.pr_Icon)

        Storagenetapp.pr_Icon = "cp-storage-name me-1 text-primary"
        Storagenetapp.dr_Icon = "cp-storage-name me-1 text-primary"
        Storagenetapp.label = "Storage Id"
        Storagenetapp[parsedData?.PRNetAppSnapMirrorMonitoring?.Type] = checkCondition(parsedData?.PRNetAppSnapMirrorMonitoring?.PRNetAppSnapMirrorMonitor?.StorageId + "$" + Storagenetapp?.pr_Icon)

        Volumenetapp.pr_Icon = "cp-volume-adjustment me-1 text-primary"
        Volumenetapp.dr_Icon = "cp-volume-adjustment me-1 text-primary"
        Volumenetapp.label = "Volume"
        Volumenetapp[parsedData?.PRNetAppSnapMirrorMonitoring?.Type] = checkCondition(parsedData?.PRNetAppSnapMirrorMonitoring?.PRNetAppSnapMirrorMonitor?.Volume + "$" + Volumenetapp?.pr_Icon)
        Replicationtype.pr_Icon = "cp-replication-type me-1 text-primary"
        Replicationtype.label = "Replication Type"
        Replicationtype[parsedData?.PRNetAppSnapMirrorMonitoring?.Type] = checkCondition(value.replicationType + "$" + Replicationtype?.pr_Icon)

        Sourcenetapp.pr_Icon = "cp-disable me-1 text-danger"
        Sourcenetapp.dr_Icon = "cp-replication-source me-1 text-primary"
        Sourcenetapp.label = "Source"
        Sourcenetapp[parsedData?.NetAppSnapMirrorMonitoring?.Type] = checkCondition("NA" + "$" + Sourcenetapp?.pr_Icon)

        Destinationnetapp.pr_Icon = "cp-disable me-1 text-danger"
        Destinationnetapp.dr_Icon = "cp-file-location me-1 text-primary"
        Destinationnetapp.label = "Destination"
        Destinationnetapp[parsedData?.NetAppSnapMirrorMonitoring?.Type] = checkCondition("NA" + "$" + Destinationnetapp?.pr_Icon)

        Statenetapp.pr_Icon = "cp-disable me-1 text-danger"
        Statenetapp.dr_Icon = "cp-relationship-state me-1 text-primary"
        Statenetapp.label = "State"
        Statenetapp[parsedData?.NetAppSnapMirrorMonitoring?.Type] = checkCondition("NA" + "$" + Statenetapp?.pr_Icon)

        Lagnetapp.pr_Icon = "cp-disable me-1 text-danger"
        Lagnetapp.dr_Icon = "cp-data-lag me-1 text-primary"
        Lagnetapp.label = "Lag"
        Lagnetapp[parsedData?.NetAppSnapMirrorMonitoring?.Type] = checkCondition("NA" + "$" + Lagnetapp?.pr_Icon)

        Statusnetapp.pr_Icon = "cp-disable me-1 text-danger"
        Statusnetapp.dr_Icon = "cp-ohas-status me-1 text-primary"
        Statusnetapp.label = "Status"
        Statusnetapp[parsedData?.NetAppSnapMirrorMonitoring?.Type] = checkCondition("NA" + "$" + Statusnetapp?.pr_Icon)
        header.push("Component Monitor")

        parsedData?.PRNetAppSnapMirrorMonitoring?.Type ? header.push("Production Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.PRNetAppSnapMirrorMonitoring?.Type ? repheader.push("Production Server") : ""

        parsedData?.NetAppSnapMirrorMonitoring?.forEach((bind) => {
            if (bind.NetAppSnapMirrorMonitor) {


                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    //header.push(bind?.Type + " Server");
                    header.push("Production Server")
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    //repheader.push(bind?.Type + " Server");
                    repheader.push("DR Server")
                }

                //  let driphost = bind?.MonitoringModel?.ConnectViaHostName?.toLowerCase() === "true" ? bind?.MonitoringModel?.Server_HostName : bind?.MonitoringModel?.Server_IpAddress
                Servernetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorMonitor?.ServerName} $ ${Servernetapp?.dr_Icon || ''}`);
                Ipaddressnetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorMonitor?.IpAddress} $ ${Ipaddressnetapp?.dr_Icon || ''}`);
                Storagenetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorMonitor?.StorageId} $ ${Storagenetapp?.dr_Icon || ''}`);
                Volumenetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorMonitor?.Volume} $ ${Volumenetapp?.dr_Icon || ''}`);
                Sourcenetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorReplication?.Source} $ ${Sourcenetapp?.dr_Icon || ''}`);
                Destinationnetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorReplication?.Destination} $ ${Destinationnetapp?.dr_Icon || ''}`);
                Statenetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorReplication?.State} $ ${Statenetapp?.dr_Icon || ''}`);
                Lagnetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorReplication?.Lag} $ ${Lagnetapp?.dr_Icon || ''}`);
                Statusnetapp[bind?.Type] = checkCondition(`${bind?.NetAppSnapMirrorReplication?.Status} $ ${Statusnetapp?.dr_Icon || ''}`);
                // Ipaddressaplication[bind?.Type] = checkCondition(`${driphost} $ ${Ipaddressaplication?.dr_Icon || ''}`);

            }
        });


        rowData.push(Servernetapp, Ipaddressnetapp, Storagenetapp, Volumenetapp)
        repData.push(Replicationtype, Sourcenetapp, Destinationnetapp, Statenetapp, Lagnetapp, Statusnetapp)
    }
    if (parsedData?.Monitor_Type.toLowerCase() === "hp3par") {
     
        header = []
        rowData = []
        repheader = []
        repData = []
        let Serverhp = {}
        let Ipaddresshp = {}
        let replicationType = {}
        //  let ipaddresshost = parsedData?.PrNoReplicationModel?.PrMonitorModel?.Pr_ConnectViaHostName?.toLowerCase() === "true" ? parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_HostName : parsedData?.PrNoReplicationModel?.PrMonitorModel?.PR_Server_IpAddress
        Serverhp.label = "Server Name"
        Serverhp.pr_Icon = "cp-server me-1 text-primary"
        Serverhp.dr_Icon = "cp-server me-1 text-primary"
        Serverhp[parsedData?.Hp3ParReplicationPRModel?.Type] = checkCondition(parsedData?.Hp3ParReplicationPRModel?.Hp3parReplicationPRMonitoring?.PR_Server_Name + "$" + Serverhp?.pr_Icon)

        Ipaddresshp.pr_Icon = parseStatus(value?.prServerStatus)
        Ipaddresshp.dr_Icon = parseStatus(value?.drServerStatus)
        Ipaddresshp.label = "IP Address/HostName"
        Ipaddresshp[parsedData?.Hp3ParReplicationPRModel?.Type] = checkCondition(parsedData?.Hp3ParReplicationPRModel?.Hp3parReplicationPRMonitoring?.PR_Server_IpAddress + "$" + Ipaddresshp?.pr_Icon)

        replicationType.pr_Icon = "cp-replication-type me-1 text-primary"
        replicationType.label = "Replication Type"
        replicationType[parsedData?.Hp3ParReplicationPRModel?.Type] = checkCondition(value.replicationType + "$" + replicationType.prIcon)

        header.push("Component Monitor")

        parsedData?.Hp3ParReplicationPRModel?.Type ? header.push("Production Server") : ""

        repheader.push("Replication Monitor")

        parsedData?.Hp3ParReplicationPRModel?.Type ? repheader.push("Production Server") : ""

        parsedData?.Hp3parReplicationDRModel?.forEach((bind) => {
            if (bind.Hp3parReplicationMonitoring) {


                if (bind?.Type && !header.includes(bind.Type + " Server")) {
                    header.push(bind?.Type + " Server");
                }
                if (bind?.Type && !repheader.includes(bind.Type + " Server")) {
                    repheader.push(bind?.Type + " Server");
                }

                // let driphost = bind?.MonitoringModel?.ConnectViaHostName?.toLowerCase() === "true" ? bind?.MonitoringModel?.Server_HostName : bind?.MonitoringModel?.Server_IpAddress
                Serverhp[bind?.Type] = checkCondition(`${bind?.Hp3parReplicationMonitoring?.DR_Server_Name} $ ${Serverhp?.dr_Icon || ''}`);
                Ipaddresshp[bind?.Type] = checkCondition(`${bind?.Hp3parReplicationMonitoring?.DR_Server_IpAddress} $ ${Ipaddresshp?.dr_Icon || ''}`);

            }
        });


        rowData.push(Serverhp, Ipaddresshp)
        repData.push(replicationType)
    }

    const tableConfigurations = {

        rsyncappreplication: {
            home: {
                headers: header,
                rows: rowData

            },
            profile: {
                headers: repheader,
                rows: repData

            }
        },
        zertovpg: {
            home: {
                headers: header,
                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData
            }
        },
        windowsactivedirectory: {
            home: {
                headers: header,
                rows: rowData

            },
            profile: {
                headers: repheader,
                rows: repData

            }
        },
        robocopy: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }
        },
        azuremysqlpaas: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        azurepostgrespaas: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        mssqlalwaysonavailabilitygroup: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        applicationnoreplication: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        netappsnapmirror: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        hp3par: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        azurestorageaccount: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        azuremssqlpaas: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        datasyncappreplication: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },
        goldengatereplication: {
            home: {

                headers: header,

                rows: rowData
            },
            profile: {
                headers: repheader,
                rows: repData

            }

        },

    };


    const generateTable = (config) => {
        const { home, profile } = config;
        
        let html = ''

        if (profile?.rows[1]?.Monitor_Type?.toLowerCase() === "goldengatereplication") {

            html = ` <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
               ${generateGoldenTableSection(profile.headers, profile.rows)}
           </div>`

        } else {
            html = ` <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
               ${generateTableSection(profile.headers, profile.rows)}
           </div>`
        }

        return `
            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">
                ${generateTableSection(home.headers, home.rows, true)}
               
                </div>
          
                ${html}
            
        `;

    };

    if (tableConfigurations[MonitorType]) {
        const infraobjectdata = generateTable(tableConfigurations[MonitorType]);

        $("#infraobjectalldata").html(infraobjectdata);

    }
}