﻿// test.js

// Mock URLs used in AJAX calls
window.siteLocationURL = {
    siteLocationPaginatedUrl: "/api/sitelocation/paginated",
    siteLocationCreateUrl: "/api/sitelocation/create"
};

// Dummy validation function (replace with your actual implementation)
window.validateSiteLocationForm = function () {
    let valid = true;
    if (!$('#cityName').val()) {
        $('#cityNameError').text('City Name required');
        valid = false;
    }
    if (!$('#countryName').val()) {
        $('#countryNameError').text('Country Name required');
        valid = false;
    }
    let lat = $('#latitudeID').val();
    let lon = $('#longitudeId').val();
    if (lat && isNaN(parseFloat(lat))) {
        $('#latitudeError').text('Invalid latitude format');
        valid = false;
    }
    if (lon && (isNaN(parseFloat(lon)) || lon < -180 || lon > 180)) {
        $('#longitudeError').text('Longitude must be between -180 and 180');
        valid = false;
    }
    return valid;
};

QUnit.module("DataTable Initialization", hooks => {
    hooks.beforeEach(() => {
        $('#siteLocationTable').DataTable({
            ajax: function (data, cb) {
                cb({ data: [] });
            }
        });
    });
    hooks.afterEach(() => {
        $('#siteLocationTable').DataTable().destroy();
    });

    QUnit.test("should initialize DataTable", assert => {
        let dt = $('#siteLocationTable').DataTable();
        assert.ok(dt, "DataTable initialized");
    });
});

QUnit.module("Search and Filter", hooks => {
    QUnit.test("should build search string correctly", assert => {
        let selectedValues = ['city=test', 'country=test'];
        let searchString = selectedValues.join(';');
        assert.equal(searchString, "city=test;country=test", "Search string formatted correctly");
    });
});

QUnit.module("Form Validation", hooks => {
    hooks.beforeEach(() => {
        $('#cityName').val('');
        $('#countryName').val('');
        $('#latitudeID').val('');
        $('#longitudeId').val('');
        $('#cityNameError').text('');
        $('#countryNameError').text('');
        $('#latitudeError').text('');
        $('#longitudeError').text('');
    });

    QUnit.test("should validate required fields", assert => {
        let isValid = validateSiteLocationForm();
        assert.notOk(isValid, "Form is invalid when required fields are empty");
        assert.equal($('#cityNameError').text(), "City Name required", "City name error shown");
        assert.equal($('#countryNameError').text(), "Country Name required", "Country name error shown");
    });

    QUnit.test("should validate coordinate formats", assert => {
        $('#cityName').val('Test City');
        $('#countryName').val('Test Country');
        $('#latitudeID').val('invalid');
        $('#longitudeId').val('200');
        let isValid = validateSiteLocationForm();
        assert.notOk(isValid, "Form is invalid with bad coordinates");
        assert.equal($('#latitudeError').text(), "Invalid latitude format", "Latitude error shown");
        assert.equal($('#longitudeError').text(), "Longitude must be between -180 and 180", "Longitude error shown");
    });

    QUnit.test("should pass with valid data", assert => {
        $('#cityName').val('Test City');
        $('#countryName').val('Test Country');
        $('#latitudeID').val('25.123');
        $('#longitudeId').val('77.123');
        let isValid = validateSiteLocationForm();
        assert.ok(isValid, "Form is valid with correct data");
    });
});

QUnit.module("AJAX Operations", hooks => {
    let server;
    hooks.beforeEach(() => {
        server = sinon.createFakeServer();
        server.respondImmediately = true;
    });
    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("should create site location successfully", assert => {
        server.respondWith("POST", "/api/sitelocation/create", [200, { "Content-Type": "application/json" }, JSON.stringify({ success: true })]);
        let done = assert.async();
        // Simulate form data
        $('#cityName').val('New York');
        $('#countryName').val('USA');
        $('#latitudeID').val('40.7128');
        $('#longitudeId').val('-74.0060');
        // Simulate AJAX call
        $.ajax({
            url: window.siteLocationURL.siteLocationCreateUrl,
            method: "POST",
            data: {
                City: $('#cityName').val(),
                Country: $('#countryName').val(),
                Latitude: $('#latitudeID').val(),
                Longitude: $('#longitudeId').val()
            },
            success: function (resp) {
                assert.ok(resp.success, "AJAX call succeeded");
                done();
            }
        });
    });

    QUnit.test("should handle AJAX errors", assert => {
        server.respondWith("POST", "/api/sitelocation/create", [500, {}, "Server error"]);
        let done = assert.async();
        $.ajax({
            url: window.siteLocationURL.siteLocationCreateUrl,
            method: "POST",
            error: function (xhr) {
                assert.equal(xhr.status, 500, "AJAX error handled");
                done();
            }
        });
    });
});

QUnit.module("Event Handling", hooks => {
    QUnit.test("should open create modal on button click", assert => {
        $('#siteLocCreateModal').hide();
        $('#siteLocCreate').on('click', function () {
            $('#siteLocCreateModal').show();
        });
        $('#siteLocCreate').trigger('click');
        assert.equal($('#siteLocCreateModal').css('display'), 'block', "Create modal is shown");
    });

    QUnit.test("should open delete modal and set correct ID", assert => {
        $('#siteLocDeleteModal').hide();
        $('#siteLocDeleteId').text('');
        $('#siteLocDeleteButton').data('id', null);
        // Simulate click event
        function openDeleteModal(id, city) {
            $('#siteLocDeleteId').text(city);
            $('#siteLocDeleteButton').data('id', id);
            $('#siteLocDeleteModal').show();
        }
        openDeleteModal(123, "Paris");
        assert.equal($('#siteLocDeleteId').text(), "Paris", "Delete modal shows correct name");
        assert.equal($('#siteLocDeleteButton').data('id'), 123, "Delete button stores correct ID");
        assert.equal($('#siteLocDeleteModal').css('display'), 'block', "Delete modal is shown");
    });
});

QUnit.module("validateLocationOrCountry - Dynamic Full Scenario", hooks => {
    let originalIsNameExist;

    hooks.before(() => {
        // Mock IsNameExist to always return true (i.e., name is available)
        originalIsNameExist = window.IsNameExist;
        window.IsNameExist = async () => true;
    });

    hooks.after(() => {
        // Restore the original function
        window.IsNameExist = originalIsNameExist;
    });

    function generateRandomName() {
        const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 _-.";
        let name = "";
        const length = Math.floor(Math.random() * 48) + 3; // Between 3 and 50 chars

        for (let i = 0; i < length; i++) {
            name += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        // Occasionally inject edge cases
        if (Math.random() < 0.2) name = "  " + name;
        if (Math.random() < 0.2) name = "_" + name;
        if (Math.random() < 0.2) name += "--";
        if (Math.random() < 0.2) name += "..";
        if (Math.random() < 0.2) name += "_ _";
        if (Math.random() < 0.2) name += "_\t";
        if (Math.random() < 0.2) name = name.replace(/\s/g, "  ");

        return name.trim();
    }

    function createErrorElement() {
        const el = document.createElement("span");
        el.id = "mockErrorElement";
        document.body.appendChild(el);
        return $(el);
    }

    QUnit.test("Run random city/country names through validateLocationOrCountry", async assert => {
        const done = assert.async();
        const totalTests = 150;

        for (let i = 0; i < totalTests; i++) {
            const input = generateRandomName();
            const $el = createErrorElement();
            const result = await validateLocationOrCountry(input, $el, "Enter name", true, null);
            const msg = `${result === true ? '✅' : '❌'} Test ${i + 1}: '${input}' → ${result === true ? 'Passed' : 'Failed'} | Message: ${$el.text()} | Returns: ${result}`;
            assert.ok(true, msg);
            $el.remove();
        }
        done();
    });
});

QUnit.module("validateDropDown - Latitude & Longitude Validation (Randomized)", function () {
    const specialChars = ["!", "@", "#", "$", "%", "^", "&", "*", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", ",", "?", "/", "\\"];

    function getRandomInput(isLat = true) {
        const range = isLat ? 90 : 180;
        const generators = [
            () => (Math.random() * (range * 2) - range).toFixed(4),         // valid lat/long
            () => ".".repeat(Math.floor(Math.random() * 3)),               // only dots
            () => specialChars[Math.floor(Math.random() * specialChars.length)], // single special
            () => "0".repeat(Math.floor(Math.random() * 5)),               // all zeros (invalid)
            () => "-",                                                     // only hyphen
            () => `${Math.random().toFixed(2)}${specialChars[Math.floor(Math.random() * specialChars.length)]}`, // valid + special
            () => "",                                                      // empty
            () => "-.",                                                    // invalid
        ];
        return generators[Math.floor(Math.random() * generators.length)]();
    }

    QUnit.test(`Latitude Test`, function (assert) {
        for (let i = 0; i < 150; i++) {
            const input = getRandomInput(true);
            const $error = $('#latitudeError');
            $error.text('').removeClass('field-validation-error');
            const result = validateDropDown(input, 'Latitude required', 'latitudeError', 'Invalid latitude');
            const msg = `${result === true ? '✅' : '❌'} Latitude ${i + 1}: '${input}' → ${result === true ? 'Passed' : 'Failed'} | Message: ${$error.text()} | Returns: ${result}`;
            assert.ok(true, msg);
        }
    });

    QUnit.test(`Longitude Test`, function (assert) {
        for (let i = 0; i < 150; i++) {
            const input = getRandomInput(false);
            const $error = $('#longitudeError');
            $error.text('').removeClass('field-validation-error');
            const result = validateDropDown(input, 'Longitude required', 'longitudeError', 'Invalid longitude');
            const msg = `${result === true ? '✅' : '❌'} Longitude ${i + 1}: '${input}' → ${result === true ? 'Passed' : 'Failed'} | Message: ${$error.text()} | Returns: ${result}`;
            assert.ok(true, msg);
        }
    });
});
