﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraObject.Events.Create;

public class InfraObjectCreatedEventHandler : INotificationHandler<InfraObjectCreatedEvent>
{
    private readonly ILogger<InfraObjectCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraObjectCreatedEventHandler(ILoggedInUserService userService,
        ILogger<InfraObjectCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(InfraObjectCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Entity = Modules.InfraObject.ToString(),
            Action = $"{ActivityType.Create} {Modules.InfraObject}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $" InfraObject '{createdEvent.InfraObjectName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"InfraObject '{createdEvent.InfraObjectName}' created successfully.");
    }
}