﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class SiteTypeService : BaseClient, ISiteTypeService
{
    public SiteTypeService(IConfiguration configuration, IAppCache cache, ILogger<SiteTypeService> logger) : base(configuration, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateSiteTypeCommand siteType)
    {
        var request = new RestRequest("api/v6/sitetypes", Method.Post);

        request.AddJsonBody(siteType);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateSiteTypeCommand siteType)
    {
        var request = new RestRequest("api/v6/sitetypes", Method.Put);

        request.AddJsonBody(siteType);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id,string name)
    {
        var request = new RestRequest($"api/v6/sitetypes?id={id}&name={name}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<SiteTypeListVm>> GetSiteTypeList()
    {
        var request = new RestRequest("api/v6/sitetypes");

        return await Get<List<SiteTypeListVm>>(request);
    }

    public async Task<bool> IsSiteTypeExist(string siteType, string id)
    {
        var request = new RestRequest($"api/v6/sitetypes/name-exist?type={siteType}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<PaginatedResult<SiteTypeListVm>> GetSiteTypePaginatedList(GetSiteTypePaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/sites/paginated-list{query}");

        return await Get<PaginatedResult<SiteTypeListVm>>(request);
    }
}