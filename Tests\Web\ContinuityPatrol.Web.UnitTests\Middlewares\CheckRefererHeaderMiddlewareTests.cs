using ContinuityPatrol.Web.Middlewares;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class CheckRefererHeaderMiddlewareTests
{
    private readonly DefaultHttpContext _context;
    private readonly CheckRefererHeaderMiddleware _middleware;
    private bool WasCalled { get; set; }

    public CheckRefererHeaderMiddlewareTests()
    {
        _context = new DefaultHttpContext
        {
            Response = { Body = new MemoryStream() }
        };

        Mock<ILogger<CheckRefererHeaderMiddleware>> loggerMock = new();

        RequestDelegate next = _ =>
        {
            WasCalled = true;
            return Task.CompletedTask;
        };

        _middleware = new CheckRefererHeaderMiddleware(next, loggerMock.Object);
    }

    [Fact]
    public async Task Invoke_WithRefererHeader_ShouldCallNext()
    {
        _context.Request.Headers["Referer"] = "https://example.com";

        await _middleware.Invoke(_context);

        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/login")]
    [InlineData("/api/health")]
    [InlineData("/.well-known/appspecific/com.chrome.devtools.json")]
    [InlineData("/")]
    public async Task Invoke_WithoutRefererHeader_WithAllowedPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }




    [Theory]
    [InlineData("/prelogin")]
    [InlineData("/PreLogin")]
    [InlineData("/account/prelogin")]
    public async Task Invoke_WithoutRefererButValidPreLoginPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/basic")]
    [InlineData("/Basic")]
    [InlineData("/basic/info")]
    public async Task Invoke_WithoutRefererButValidBasicPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/canvas")]
    [InlineData("/Canvas")]
    [InlineData("/canvas/dashboard")]
    public async Task Invoke_WithoutRefererButValidCanvasPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Fact]
    public async Task Invoke_WithoutRefererButRootPath_ShouldCallNext()
    {
        // Arrange
        _context.Request.Path = "/";

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/logout")]
    [InlineData("/Logout")]
    [InlineData("/account/logout")]
    public async Task Invoke_WithoutRefererButValidLogoutPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/hub")]
    [InlineData("/Hub")]
    [InlineData("/signalr/hub")]
    public async Task Invoke_WithoutRefererButValidHubPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/api/users")]
    [InlineData("/api/data")]
    [InlineData("/API/test")]
    public async Task Invoke_WithoutRefererButValidApiPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/health")]
    [InlineData("/Health")]
    [InlineData("/health/check")]
    public async Task Invoke_WithoutRefererButValidHealthPath_ShouldCallNext(string path)
    {
        // Arrange
        _context.Request.Path = path;

        // Act
        await _middleware.Invoke(_context);

        // Assert
        Assert.True(WasCalled);
    }

    [Fact]
    public async Task Invoke_WithoutRefererButValidChromeDevToolsPath_ShouldCallNext()
    {
        _context.Request.Path = "/.well-known/appspecific/com.chrome.devtools.json";

        await _middleware.Invoke(_context);

        Assert.True(WasCalled);
    }

    
    [Theory]
    [InlineData("/dashboard")]
    [InlineData("/admin/users")]
    [InlineData("/reports")]
    [InlineData("/configuration")]
    public async Task Invoke_WithoutRefererAndInvalidPath_ShouldRedirectToLogout(string path)
    {
        var wasCalled = false;

        var context = new DefaultHttpContext
        {
            Request = { Path = path },
            Response = { Body = new MemoryStream() }
        };

        var loggerMock = new Mock<ILogger<CheckRefererHeaderMiddleware>>();

        var middleware = new CheckRefererHeaderMiddleware(
            _ =>
            {
                wasCalled = true;
                return Task.CompletedTask;
            },
            loggerMock.Object
        );

        await middleware.Invoke(context);

        Assert.Equal(302, context.Response.StatusCode);
        Assert.Contains("/Account/Logout", context.Response.Headers["Location"].ToString());
        Assert.False(wasCalled);
    }

    [Fact]
    public async Task Invoke_WithEmptyRefererHeader_ShouldCallNext()
    {
        _context.Request.Headers.Add("Referer", "");
        _context.Request.Path = "/dashboard";

        await _middleware.Invoke(_context);

        Assert.Equal(200, _context.Response.StatusCode);
        Assert.True(WasCalled);
    }

    [Theory]
    [InlineData("/")]
    [InlineData("/login")]
    [InlineData("/api/test")]
    public async Task Invoke_WithMultipleValidPaths_ShouldCallNext(string path)
    {
        var wasCalled = false;

        var context = new DefaultHttpContext
        {
            Request =
            {
                Path = path
            },
            Response = { Body = new MemoryStream() }
        };

        var loggerMock = new Mock<ILogger<CheckRefererHeaderMiddleware>>();

        var middleware = new CheckRefererHeaderMiddleware(
            _ =>
            {
                wasCalled = true;
                return Task.CompletedTask;
            },
            loggerMock.Object
        );

        await middleware.Invoke(context);

        Assert.True(wasCalled, $"Expected middleware to call next for path '{path}'");
    }
}

public class CheckRefererHeaderMiddlewareExtensionTests
{
    [Fact]
    public void UseRefererHeader_ShouldRegisterMiddleware()
    {
        // Arrange
        var app = new Mock<IApplicationBuilder>();
        app.Setup(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()))
            .Returns(app.Object);

        // Act
        var result = app.Object.UseRefererHeaderMiddleware();

        // Assert
        result.Should().Be(app.Object);
        app.Verify(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()), Times.Once);
    }
}
