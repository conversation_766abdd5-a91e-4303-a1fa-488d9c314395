﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.SiteType.Commands.Create;

public class CreateSiteTypeCommandValidator : AbstractValidator<CreateSiteTypeCommand>
{
    public static int MaxAllowedLicenses;
    public static string Type = string.Empty;
    private readonly List<string> _allowedCategory = new() { "primary", "dr", "custom dr" };
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ISiteTypeRepository _siteTypeRepository;

    public CreateSiteTypeCommandValidator(ISiteTypeRepository siteTypeRepository,
        ILicenseManagerRepository licenseManagerRepository, ILoggedInUserService loggedInUserService)
    {
        _siteTypeRepository = siteTypeRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Name is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid Name")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.Category)
            .NotEmpty().WithMessage("Select Site Type")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$")
            .WithMessage("Please Enter Valid Type")
            .NotNull()
            .Must(value => value != null && _allowedCategory.Contains(value.ToLower()))
            .WithMessage("{PropertyName} is invalid.");

        //RuleFor(p => p.Icon)
        //    .NotEmpty()
        //    .WithMessage("{PropertyName} is required.")
        //    .NotNull()
        //    .Matches(@"^[a-zA-Z\d]([a-zA-Z\d_\-\s]+)*[a-zA-Z\d]$")
        //    .WithMessage("Please Enter Valid Icon");


        RuleFor(p => p)
            .MustAsync(IsSiteTypeNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(p => p)
            .MustAsync(IsSiteTypeCategoryUnique)
            .WithMessage(_ => $"{Type} site already exists. Please choose a different type.");

        RuleFor(p => p)
            .MustAsync(IsLicenseCountValid)
            .WithMessage(p =>
                $"Site Creation Limit Exceeded: Your current license allows for up to {MaxAllowedLicenses} sites.");
    }

    private async Task<bool> IsLicenseCountValid(CreateSiteTypeCommand command, CancellationToken token)
    {
        if (command.Category.IsNullOrWhiteSpace()) return false;

        if (command.Category.ToLower().Equals("custom dr"))
        {
            var license = await _licenseManagerRepository.GetLicenseDetailByCompanyId(_loggedInUserService.CompanyId);

            var siteCount = (await _siteTypeRepository.ListAllAsync()).Count;

            var isLicense = license.Any(x =>
            {
                if (x.Properties.IsNotNullOrWhiteSpace())
                {
                    var properties = JObject.Parse(x.Properties);

                    var count = properties["totalSites"]?.ToString();

                    if (count.IsNotNullOrWhiteSpace())
                    {
                        MaxAllowedLicenses = Convert.ToInt32(count);

                        return Convert.ToInt32(count) > siteCount;
                    }
                }

                return false;
            });

            return isLicense;
        }

        return true;
    }

    public async Task<bool> IsSiteTypeNameUnique(CreateSiteTypeCommand p, CancellationToken cancellationToken)
    {
        return !await _siteTypeRepository.IsSiteTypeNameUnique(p.Type);
    }

    public async Task<bool> IsSiteTypeCategoryUnique(CreateSiteTypeCommand p, CancellationToken cancellationToken)
    {
        if (!p.Category.ToLower().Equals("custom dr"))
        {
            Type = p.Category;
            return !await _siteTypeRepository.IsSiteTypeCategoryUnique(p.Category);
        }

        return true;
    }
}