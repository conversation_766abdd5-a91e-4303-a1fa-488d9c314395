using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberJobWorkflowSchedulerControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberJobWorkflowSchedulerController _controller;
    private readonly CyberJobWorkflowSchedulerFixture _cyberJobWorkflowSchedulerFixture;

    public CyberJobWorkflowSchedulerControllerTests()
    {
        _cyberJobWorkflowSchedulerFixture = new CyberJobWorkflowSchedulerFixture();

        var testBuilder = new ControllerTestBuilder<CyberJobWorkflowSchedulerController>();
        _controller = testBuilder.CreateController(
            _ => new CyberJobWorkflowSchedulerController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_ReturnsExpectedResults()
    {
        // Arrange
        var query = _cyberJobWorkflowSchedulerFixture.GetCyberJobWorkflowSchedulerPaginatedListQuery;

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerListVm,
            _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerListVm
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task UpdateCyberJobWorkflowScheduler_ReturnsOk()
    {
        // Arrange
        var command = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerCommand;
        var expectedMessage = "Job Execution History has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CyberJobWorkflowSchedulerResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<CyberJobWorkflowSchedulerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesEmptyResults()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            StartDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd")
        };

        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(
            new List<CyberJobWorkflowSchedulerListVm>(), 0, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task UpdateCyberJobWorkflowScheduler_HandlesNotFound()
    {
        // Arrange
        var command = new CyberJobWorkflowSchedulerCommand
        {
            JobId = Guid.NewGuid().ToString(),
            ConditionActionId = 1
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("Job Execution History", command.JobId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateConditionActionId(command));
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesDateRangeFiltering()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 20,
            StartDate = "2024-01-01",
            EndDate = "2024-01-31"
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = "January Workflow Scheduler",
                WorkflowName = "Monthly Backup Workflow",
                Status = "Active",
                State = "Completed",
                StartTime = new DateTime(2024, 1, 15, 2, 0, 0),
                EndTime = new DateTime(2024, 1, 15, 4, 0, 0)
            }
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 1, 1, 20);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && 
                q.PageSize == query.PageSize && 
                q.StartDate == query.StartDate && 
                q.EndDate == query.EndDate), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal("January Workflow Scheduler", paginatedResult.Data.First().Name);
    }

    [Fact]
    public async Task UpdateCyberJobWorkflowScheduler_HandlesConditionActionUpdate()
    {
        // Arrange
        var command = new CyberJobWorkflowSchedulerCommand
        {
            JobId = Guid.NewGuid().ToString(),
            ConditionActionId = 5
        };

        var expectedMessage = "Job Execution History condition action has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CyberJobWorkflowSchedulerResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<CyberJobWorkflowSchedulerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.NotNull(response.Id);
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesLargePageSize()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 100,
            StartDate = DateTime.Now.AddDays(-90).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd")
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>();
        for (int i = 0; i < 50; i++)
        {
            expectedData.Add(new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = $"Workflow Scheduler {i + 1}",
                WorkflowName = $"Automated Workflow {i + 1}",
                Status = i % 2 == 0 ? "Active" : "Inactive",
                State = i % 3 == 0 ? "Running" : "Scheduled"
            });
        }
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 50, 1, 100);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Equal(50, paginatedResult.Data.Count);
        Assert.Equal(50, paginatedResult.TotalCount);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(100, paginatedResult.PageSize);
    }

    [Fact]
    public async Task UpdateCyberJobWorkflowScheduler_ValidatesJobId()
    {
        // Arrange
        var command = new CyberJobWorkflowSchedulerCommand
        {
            JobId = "", // Empty JobId should cause validation error
            ConditionActionId = 1
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("JobId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.UpdateConditionActionId(command));
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesInvalidDateRange()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            StartDate = "2024-12-31", // Start date after end date
            EndDate = "2024-01-01"
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ThrowsAsync(new ArgumentException("Start date cannot be after end date"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.GetPaginatedCyberJobWorkflowScheduler(query));
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesWorkflowFiltering()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 15,
            StartDate = DateTime.Now.AddDays(-30).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd"),
            
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = "Enterprise Backup Workflow Scheduler",
                WorkflowName = "Daily Backup Workflow",
                Status = "Completed",
                State = "Finished",
                StartTime = DateTime.Now.AddHours(-6),
                EndTime = DateTime.Now.AddHours(-4),
                ConditionActionId = 1
            }
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 1, 1, 15);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.StartDate == query.StartDate &&
                q.EndDate == query.EndDate), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Contains("Backup", paginatedResult.Data.First().WorkflowName);
        Assert.Equal("Completed", paginatedResult.Data.First().Status);
    }

    [Fact]
    public async Task UpdateConditionActionId_HandlesComplexWorkflowUpdate()
    {
        // Arrange
        var command = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerCommand;

        var expectedMessage = "Complex workflow condition action updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CyberJobWorkflowSchedulerResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
               
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<CyberJobWorkflowSchedulerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);

        Assert.Contains("Complex", response.Message);
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesScheduleTypeFiltering()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 20,
            StartDate = DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd"),
          
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = "Cron-based Workflow Scheduler",
                WorkflowName = "Automated Maintenance Workflow",
                ScheduleType = 3, // Cron type
                CronExpression = "0 0 3 * * ? *",
                Status = "Active",
                State = "Running",
                IsSchedule = 1
            }
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 1, 1, 20);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                 q.StartDate == query.StartDate &&
               q.EndDate == query.EndDate), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal(3, paginatedResult.Data.First().ScheduleType);
        Assert.Equal("Running", paginatedResult.Data.First().State);
        Assert.Contains("0 0 3 * * ? *", paginatedResult.Data.First().CronExpression);
    }

    [Fact]
    public async Task UpdateConditionActionId_HandlesMultipleConditionActions()
    {
        // Arrange
        var command = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerCommand;

        var expectedMessage = "Condition action transitioned from 8 to 15 successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CyberJobWorkflowSchedulerResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString(),
                
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<CyberJobWorkflowSchedulerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesNodeFiltering()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            StartDate = DateTime.Now.AddDays(-14).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd"),
           
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = "Node-Specific Workflow Scheduler",
                WorkflowName = "Node Maintenance Workflow",
                NodeId = "PROD-NODE-01",
                Status = "Active",
                State = "Scheduled",
                Mode = "Automatic"
            }
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 1, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.StartDate==query.StartDate
               ), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal("PROD-NODE-01", paginatedResult.Data.First().NodeId);
        Assert.Equal("Automatic", paginatedResult.Data.First().Mode);
    }

    [Fact]
    public void ClearDataCache_ClearsExpectedCacheKeys()
    {
        // Arrange & Act
        _controller.ClearDataCache();

        // Assert
        // Note: Since ClearDataCache is a void method that calls protected methods,
        // we verify it doesn't throw exceptions and completes successfully
        Assert.True(true); // Test passes if no exception is thrown
    }

    [Fact]
    public async Task GetPaginatedCyberJobWorkflowSchedulers_HandlesComplexScheduling()
    {
        // Arrange
        var query = new GetCyberJobWorkflowSchedulerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 25,
            StartDate = DateTime.Now.AddDays(-60).ToString("yyyy-MM-dd"),
            EndDate = DateTime.Now.ToString("yyyy-MM-dd"),
           
        };

        var expectedData = new List<CyberJobWorkflowSchedulerListVm>
        {
            new CyberJobWorkflowSchedulerListVm
            {
                Id = Guid.NewGuid().ToString(),
                JobId = Guid.NewGuid().ToString(),
                Name = "Enterprise Complex Workflow Scheduler",
                WorkflowName = "Multi-Phase Enterprise Backup and Replication",
                ScheduleType = 4, // Complex scheduling
                CronExpression = "0 0 2 ? * MON-FRI *", // Weekdays at 2 AM
                Status = "Active",
                State = "Scheduled",
                StartTime = DateTime.Now.AddDays(1).AddHours(2),
                EndTime = DateTime.MinValue,
                IsSchedule = 1,
                Mode = "Enterprise",
                NodeId = "ENT-NODE-CLUSTER-01",
                ConditionActionId = 25
            }
        };
        var expectedResults = PaginatedResult<CyberJobWorkflowSchedulerListVm>.Success(expectedData, 1, 1, 25);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberJobWorkflowSchedulerPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString &&
                q.StartDate == query.StartDate &&
                q.EndDate == query.EndDate), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberJobWorkflowScheduler(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        var scheduler = paginatedResult.Data.First();
        Assert.Contains("Enterprise", scheduler.Name);
        Assert.Equal(4, scheduler.ScheduleType);
        Assert.Contains("MON-FRI", scheduler.CronExpression);
        Assert.Equal("Enterprise", scheduler.Mode);
    }

    [Fact]
    public async Task UpdateConditionActionId_HandlesEnterpriseWorkflowTransition()
    {
        // Arrange
        var command = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerCommand;

        var expectedMessage = "Enterprise workflow condition action escalated from 25 to 50 successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CyberJobWorkflowSchedulerResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString(),
               
            });

        // Act
        var result = await _controller.UpdateConditionActionId(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<CyberJobWorkflowSchedulerResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
       
    }
}
