﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.WorkflowProfileAuthentication;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Commands
{
    public class WorkflowProfileAuthenticationTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly WorkflowProfileAuthenticationCommandHandler _handler;

        public WorkflowProfileAuthenticationTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _handler = new WorkflowProfileAuthenticationCommandHandler(_mockUserRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldAuthenticateSuccessfully_WithValidADUser()
        {
            var command = new WorkflowProfileAuthenticationCommand
            {
                LoginName = "validUser",
                Password = SecurityHelper.Encrypt("validPassword"),
                AuthenticationType = "ad"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync(new Domain.Entities.User { LoginName = command.LoginName });

            _mockUserRepository.Setup(repo => repo.IsValidActiveDirectoryUser(command.LoginName, It.IsAny<string>()))
                .ReturnsAsync(true);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.True(result.Success);
            Assert.Equal("Authenticated Successfully", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldAuthenticateSuccessfully_WithValidNonADUser()
        {
            var command = new WorkflowProfileAuthenticationCommand
            {
                LoginName = "validUser",
                Password = SecurityHelper.Encrypt("validPassword"),
                AuthenticationType = "db"
            };

            var user = new Domain.Entities.User { LoginName = command.LoginName, LoginPassword = SecurityHelper.Encrypt("validPassword") };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync(user);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.True(result.Success);
            Assert.Equal("Authenticated Successfully", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldReturnInvalidCredential_WithInvalidPassword()
        {
            var command = new WorkflowProfileAuthenticationCommand
            {
                LoginName = "validUser",
                Password = SecurityHelper.Encrypt("wrongPassword"),
                AuthenticationType = "db"
            };

            var user = new Domain.Entities.User { LoginName = command.LoginName, LoginPassword = SecurityHelper.Encrypt("validPassword") };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync(user);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.False(result.Success);
            Assert.Equal("Invalid Credential", result.Message);
        }

        [Fact]
        public async Task Handle_ShouldThrowInvalidException_WhenUserNotFound()
        {
            var command = new WorkflowProfileAuthenticationCommand
            {
                LoginName = "nonExistentUser",
                Password = SecurityHelper.Encrypt("anyPassword"),
                AuthenticationType = "db"
            };

            _mockUserRepository.Setup(repo => repo.FindByLoginNameAsync(command.LoginName))
                .ReturnsAsync((Domain.Entities.User)null);

            var exception = await Assert.ThrowsAsync<InvalidException>(() =>
                _handler.Handle(command, CancellationToken.None));

            Assert.Equal("Invalid User Credential.", exception.Message);
        }
    }
}
