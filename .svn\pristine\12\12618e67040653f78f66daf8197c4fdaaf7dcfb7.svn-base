using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DriftCategoryMastersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<DriftCategoryMasterListVm>>> GetDriftCategoryMasters()
    {
        Logger.LogInformation("Get All DriftCategoryMasters");

        return Ok(await Mediator.Send(new GetDriftCategoryMasterListQuery()));
    }

    [HttpGet("{id}", Name = "GetDriftCategoryMaster")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<DriftCategoryMasterDetailVm>> GetDriftCategoryMasterById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftCategoryMaster Id");

        Logger.LogInformation($"Get DriftCategoryMaster Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftCategoryMasterDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<DriftCategoryMasterListVm>>> GetPaginatedDriftCategoryMasters([FromQuery] GetDriftCategoryMasterPaginatedListQuery query)
 {
     Logger.LogInformation("Get Searching Details in DriftCategoryMaster Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateDriftCategoryMasterResponse>> CreateDriftCategoryMaster([FromBody] CreateDriftCategoryMasterCommand createDriftCategoryMasterCommand)
    {
        Logger.LogInformation($"Create DriftCategoryMaster '{createDriftCategoryMasterCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDriftCategoryMaster), await Mediator.Send(createDriftCategoryMasterCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateDriftCategoryMasterResponse>> UpdateDriftCategoryMaster([FromBody] UpdateDriftCategoryMasterCommand updateDriftCategoryMasterCommand)
    {
        Logger.LogInformation($"Update DriftCategoryMaster '{updateDriftCategoryMasterCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDriftCategoryMasterCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteDriftCategoryMasterResponse>> DeleteDriftCategoryMaster(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftCategoryMaster Id");

        Logger.LogInformation($"Delete DriftCategoryMaster Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDriftCategoryMasterCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDriftCategoryMasterNameExist(string driftCategoryMasterName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(driftCategoryMasterName, "DriftCategoryMaster Name");

     Logger.LogInformation($"Check Name Exists Detail by DriftCategoryMaster Name '{driftCategoryMasterName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDriftCategoryMasterNameUniqueQuery { Name = driftCategoryMasterName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


