﻿//using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
//using ContinuityPatrol.Application.Features.FourEyeApprovers.Commands.Create;
//using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListProfile;
//using ContinuityPatrol.Application.Features.FourEyeApprovers.Queries.GetPaginatedListWorkflow;
//using ContinuityPatrol.Application.Features.UserGroup.Queries.GetAssignedUserGroups;
//using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetPaginatedList;
//using ContinuityPatrol.Domain.ViewModels.FourEyeApproversModel;
//using ContinuityPatrol.Shared.Services.Provider;
//using ContinuityPatrol.Web.Areas.Manage.Controllers;
//using ContinuityPatrol.Web.Attributes;
//using Newtonsoft.Json;

//namespace ContinuityPatrol.Web.Areas.Configuration.Controllers
//{
//    [Area("Configuration")]
//    public class FourEyeApproversController : BaseController
//    {

//        private readonly ILogger<ManageWorkflowController> _logger;
//        private readonly IMapper _mapper;
//        private readonly IDataProvider _dataProvider;

//        public FourEyeApproversController(IMapper mapper, ILogger<ManageWorkflowController> logger, IDataProvider dataProvider)
//        {
//            _logger = logger;
//            _mapper = mapper;
//            _dataProvider = dataProvider;
//        }

//        public async Task<IActionResult> List()
//        {
//            _logger.LogInformation("Getting Four Eye Approvers List");

//            var All_FourEyeApproverslist = await _dataProvider.FourEyeApproverService.GetPaginatedFourEyeApprovers(new GetFourEyeApproversPaginatedListQuery());
//            var Workflow_FourEyeApproverslist = await _dataProvider.FourEyeApproverService.GetPaginatedFourEyeApprovers_Workflow(new GetFourEyeApproversPaginatedListWorkflowQuery());
//            var Profile_FourEyeApproverslist = await _dataProvider.FourEyeApproverService.GetPaginatedFourEyeApprovers_Profile(new GetFourEyeApproversPaginatedListProfileQuery());

//            var usergrouplist = _dataProvider.UserGroup.GetUserGroupList();
//            var paginatedworkflows = await _dataProvider.Workflow.GetPaginatedWorkflow(new GetWorkflowPaginatedListQuery());
//            var paginatedworkflowprofile = await _dataProvider.WorkflowProfile.GetPaginatedWorkflowProfile(new GetWorkflowProfilePaginatedListQuery());

//            var FourEyeApproverModel = new FourEyeApproversVm
//            {
//                PaginatedApprovers = All_FourEyeApproverslist,
//                PaginatedApprovers_Workflow = Workflow_FourEyeApproverslist,
//                PaginatedApprovers_Profile = Profile_FourEyeApproverslist,
//                UserGroups = usergrouplist.Result,
//                PaginatedWorkflows = paginatedworkflows,
//                PaginatedWorkflowsProfile = paginatedworkflowprofile
//            };
//            return View(FourEyeApproverModel);
//        }


//        public string GetJsonUserGroup()
//        {
//            var usergrouplist = _dataProvider.UserGroup.GetUserGroupList();

//            return JsonConvert.SerializeObject(usergrouplist).ToString();
//        }

//        [HttpGet]
//        public async Task<UsersWithUserGroup> GetAllUserGroup(string data)
//        {
//            var usergrouplist = await _dataProvider.UserGroup.GetAllUserWithGroups(new GetAllUserWithGroupListQuery());

//            return usergrouplist;
//        }

//        public async Task<JsonResult> GetWorkflowNames()
//        {
//            var databaseNames = await _dataProvider.Workflow.GetWorkflowNames();
//            return Json(databaseNames);
//        }

//        [HttpPost]
//        [AntiXss]
//        [ValidateAntiForgeryToken]
//        public async Task<IActionResult> CreateOrUpdate(FourEyeApproversVm fourEyeModel)
//        {

//            if (string.IsNullOrEmpty(fourEyeModel.Id))
//            {
//                CreateFourEyeApproversCommand createFourEyeApproversCommand = new CreateFourEyeApproversCommand();

//                createFourEyeApproversCommand.workflow_profileName = fourEyeModel.workflow_profileName;   
//                var FourEyeApproval_Command = _mapper.Map<CreateFourEyeApproversCommand>(fourEyeModel);

//                await _dataProvider.FourEyeApproverService.CreateAsync(FourEyeApproval_Command);
//                return RedirectToAction("List");
//            }
//            else
//            {
//                var approvalmatrix_Command = _mapper.Map<UpdateApprovalMatrixCommand>(fourEyeModel);
//                await _dataProvider.approvalMatrixService.UpdateAsync(approvalmatrix_Command);
//                return RedirectToAction("List");
//            }


//        }
//    }
//}
