﻿
const errorElements = ['#Ftpusername-error,#Ftphostname-error,#Ftppassword-error,#Ftpport-error,#Ftpsource-error,#Ftptarget-error,#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronMonthMins-error,#CronddlHour-error,#CronMonthHrs-error,#CronMonth-error,#CronMonthlyDay-error,#Backup-error'];
const errorElementt = ['#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#CronMonthMins-error,#CronddlHour-error,#CronMonthHrs-error,#CronMonth-error,#CronMonthlyDay-error'];
let BackupURL = {
    "CheckWindowServiceurl": RootUrl + 'Admin/BackupData/CheckWindowsService',
}

$(function () {
 
    setTimeout(() => {
        getBackupConfigDetails()
        getBackupDetails();
        dataVl();
    }, 500)

    $('#btnSave').prop('disabled', true);
  
    let createPermission = $("#AdminBkCreate").data("create-permission")?.toLowerCase();

    if (createPermission == 'false') {
        $("#ScheduleId").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-dismiss').removeAttr('id');

    }
    let selectedValues = [];

    let dataTable = $('#appendbackuptable').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Admin/BackupData/GetPagination",
                "dataType": "json",
                "data": function (d) {

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")


                    }
                    else {
                        $(".pagination-column").removeClass("disabled")

                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3, 4],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "backUpPath", "name": "Backup Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Backup Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span>' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "createdDate", "name": "Created Date", "autoWidth": true,
                    "render": function (data, type, row) {
                        const formattedDatetimeCreate = formatDateTime(row.createdDate);

                        if (type === 'display') {
                            return '<span>' + formattedDatetimeCreate + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "lastModifiedDate", "name": "Last Executed", "autoWidth": true,
                    "render": function (data, type, row) {
                        const formattedDatetimeExecute = formatDateTime(row.lastModifiedDate);
                        if (type === 'display') {
                            return '<span>' + formattedDatetimeExecute + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {

                        const statusBadge = (row.status.toLowerCase() === "error") ? '<span class="badge text-bg-danger">' + row.status + '</span>' : row.status.toLowerCase() === "pending" ? '<span class="badge text-bg-warning">' + row.status + '</span>' : '<span class="badge text-bg-success">' + backup.status + '</span>';
                        if (type === 'display') {
                            return '<span>' + statusBadge + '</span>';
                        }
                        return data;
                    }

                },

            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }

        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500))
})

async function dataVl() {
    let html = '';

    try {
        const result = await $.ajax({
            type: "POST",
            url: BackupURL.CheckWindowServiceurl,
            data: { type: 'monitoring', __RequestVerificationToken: gettoken() },
            dataType: "json",
            traditional: true,
        });

        // Check if the response is successful
        if (result?.success) {
            if (result?.success) {
                // $('#ProfileList').prop('disabled', false)
                //let message = result.activeNodes[0] || result.inActiveNodes[0]
                // notificationAlert("success", message)
                if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
                    for (let i = 0; i < result?.activeNodes?.length; i++) {
                        html += `<div class='mb-1'><i class="cp-network fs-4 text-success" ></i> '${result.activeNodes[i]}'</div>`;
                    }
                }

                if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
                    for (let i = 0; i < result?.inActiveNodes?.length; i++) {
                        html += `<div class='mb-1'><i class="cp-network fs-4 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
                    }
                }

                notificationAlert("success", html, 'execution')
            } else {
                errorNotification(result)
            }
            notificationAlert("success", html, 'execution');
        } else {

            errorNotification(result);
        }
    } catch (error) {

        console.error("Error during AJAX request:", error);
        //  errorNotification({ message: "An error occurred while fetching the data." });
    }
}

function getBackupConfigDetails() {
    $.ajax({
        type: "GET",
        url: "/Admin/BackupData/GetBackupConfig",
        async: true,
        success: function (result) {

            $('#txthostname').val(result.serverName);
            $('#txtdatabasename').val(result.databaseName);
            $('#txtusername').val(result.userName);
            $('#txtpassword').val(result.password);

        }

    });

}



$('#txtbackuppath').on('keydown keyup', async function () {
    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateBackuppath(value);
});

$('#selectbackup').on('change', function (event) {
    let value = $(this).val();
    const errorElement = $('#Keepbackup-error');
    if (value === "Select keep last backup") {
        value = ""
    }
    validateDropDown(value, "Select keep last backup", errorElement);
});

$('#ftphostname').on('keydown keyup', async function () {
    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateHostName(value);
});

$('#ftpurl').on('keydown keyup', async function () {
    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateTargetPath(value);
});

$('#cpurl').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateSourcePath(value);
});

$('#ftppassword').on('keydown keyup', async function () {
    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validatePassword(value);
});

$('#ftpusername').on('keydown keyup', async function () {
    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateUserName(value);
});

$('#ftpport').on('input', async function (event) {
    let value = $(this).val();
    var sanitizedValue = value.replace(/\D/g, '').substring(0, 5);
    $(this).val(sanitizedValue);

    if (event.originalEvent && event.originalEvent.inputType === 'insertText') {
        let key = event.originalEvent.data;
        if (isNaN(key) || sanitizedValue.length >= 5) {
            event.preventDefault();
        }
    }
    if (sanitizedValue == 0) {
        $(this).val("");
    }
    await validatePort(sanitizedValue);
});

// Minites
$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});

// Hourly 

$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 23) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 59) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});

$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})

// Daily Button

$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});

$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});

$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});


// weekly Button 

$('input[name=weekDays]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day", $('#CronDay-error'));
});

$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});


//  Month And Year
$('#lblMonth').on("change", function () {

    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    let selectedDate = new Date($(this).val()), currentDate = new Date()
    let getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    let daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        let data = ""
        data = i + 1
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel").each(function () {
            let checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
    } else {
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});

$('input[name=Monthyday]').on('click', function () {

    let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
});

// Execute
$('#btnExecute').on('click', backupDebounce(async function () {
    let backupserver = $('#txtserverid').val().trim();
    var backuppath1 = $('#txtbackuppath').val();
    var ftphostName1 = $('#ftphostname').val();
    var ftpuserName1 = $('#ftpusername').val();
    var ftphostPassword1 = $('#ftppassword').val();
    var ftpport1 = $('#ftpport').val();
    var ftpcpUrl1 = $('#cpurl').val();
    var ftpserverUrl1 = $('#ftpurl').val();
    if (backupserver && backuppath1 || (ftphostName1 && ftpuserName1 && ftphostPassword1 && ftpport1 && ftpcpUrl1 && ftpserverUrl1)) {
        try {
            const response = await $.ajax({
                url: "/Admin/BackupData/ExecuteBackUpcommand",
                type: 'POST',
                dataType: "json",
                contentType: 'application/json',
                data: JSON.stringify({ Id: backupserver }),
            });

            if (response && response.success) {
                notificationAlert("success", response.message);
                setTimeout(() => {
                    window.location.reload();
                }, 300)
            } else {
                notificationAlert("warning", response.message);
            }
        } catch (error) {
            console.log(error)
        }
    } else {
        // notificationAlert("error", "Please provide a valid server ID.");
    }
}, 500));

// Save
$('#btnSave').on('click', async function () {
    GetIsSchedule();
    var backupType = $("input[name='switchPlan']:checked").val();
    if (backupType === 'Cycle') {
        $("#switchMonthly").prop("checked", true);
        $('#btnExecute').prop('disabled', true);
        $('#btnSave').prop('disabled', false);
    } else if (backupType === 'Once') {
        $("#switchYearly").prop("checked", true);
        $('#btnExecute').prop('disabled', false);
        $('#btnSave').prop('disabled', true);
    }

    $("#ChkValue").val(backupType);
    errorElement = $('#Keepbackup-error');
    var keepBackupLast = $("#selectbackup").find(':selected').val()
    var serverName = $("#txthostname").val();
    var databaseName = $("#txtdatabasename").val();
    var userName = $("#txtusername").val();
    var backuppath = $('#txtbackuppath').val();
    var ftphostName = $('#ftphostname').val();
    var ftpuserName = $('#ftpusername').val();
    var ftphostPassword = $('#ftppassword').val();
    var ftpport = $('#ftpport').val();
    var ftpcpUrl = $('#cpurl').val();
    var ftpserverUrl = $('#ftpurl').val();
    var isFtphostname = await validateHostName(ftphostName);
    var isFtpusername = await validateUserName(ftpuserName);
    var isFtppassword = await validatePassword(ftphostPassword)
    var isFtpport = await validatePort(ftpport)
    var isFtpsourcepath = await validateSourcePath(ftpcpUrl)
    var isFtptargetpath = await validateTargetPath(ftpserverUrl)
    var isBackuppath = await validateBackuppath(backuppath)
    var iskeepbackup = await validateDropDown(keepBackupLast, "Select keep last backup ", errorElement)
    let baseProperties = {
        ftphostName: ftphostName,
        ftpuserName: ftpuserName,
        ftphostPassword: ftphostPassword,
        ftpport: ftpport,
        sourcepath: ftpcpUrl,
        targetpath: ftpserverUrl
    };

    let jsonString = JSON.stringify(baseProperties);
    $('#textProperties').val(jsonString);
    var isLocal = $('#inlineRadio1').prop('checked');
    var isBackup = $('#inlineRadio2').prop('checked');

    $('#txtLocalserver').val(isLocal);
    $('#txtBackupserver').val(isBackup);

    if (backupType === "Cycle") {
        let isScheduler = CronValidation();
        let { CronExpression, listcron } = JobCronExpression();
        $('#textCronExpression').val(CronExpression);
        $('#txtCronViewList').val(listcron);       
        Get_ScheduleTypes();
 
        let sanitizebackupArray = ['txthostname', 'txtdatabasename', 'txtusername', 'txtbackuppath', 'ftphostname', 'ftpusername', 'ftppassword', 'ftpport',
            'cpurl', 'ftpurl', 'txtLocalserver', 'txtBackupserver']
        sanitizeContainer(sanitizebackupArray)

        if (isLocal) {
            if (serverName && databaseName && userName && isBackuppath && isScheduler && iskeepbackup && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        } else {
            if (serverName && databaseName && userName && isScheduler && iskeepbackup && isBackup && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        }
        let backupserver = $('#txtserverid').val().trim();

        if (backupserver && isScheduler && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath) {

            const response = await $.ajax({
                url: "/Admin/BackupData/ExecuteBackUpcommand",
                type: 'POST',
                dataType: "json",
                headers: {
                    'RequestVerificationToken': gettoken()
                },
                contentType: 'application/json',
                data: JSON.stringify({
                    Id: backupserver,

                }),
            });

            if (response && response.success) {
                notificationAlert("success", response.message);
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                notificationAlert("warning", response.message);
            }

        }
    } else {

        if (isLocal) {
            if (serverName && databaseName && userName && iskeepbackup && isBackuppath && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        } else {
            if (serverName && databaseName && userName && backupType && iskeepbackup && isScheduler && isBackup && isFtphostname && isFtpusername && isFtppassword && isFtpport && isFtpsourcepath && isFtptargetpath && backupType) {
                $("#backpth").val(backuppath)
                $("#KPbackLast").val(keepBackupLast)
                $('#BackupForm').trigger('submit');
            }
        }


    }

});


$('.nav-link').on("click", function () {
    ClearCroneElements();
    $('#btnsave').text("save");
    clearCronExpressionData();
    ClearErrorElements(errorElementt);
});

function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours").val('');
    $("input[name=weekDays],input[name=daysevery],input[name=Monthyday]").prop("checked", false);
}

$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab,#Next_profile_data").on("click", function () {
    $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})

$("#nav-Monthly-tab").on("click", function () {
    if ($("btnSave").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})

// Validation
function validateDropDown(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg);
        errorElement.addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}

async function validateBackuppath(value) {
    const errorElement = $('#Backup-error');

    if (!value) {
        errorElement.text('Enter backup path').addClass('field-validation-error');

        return false;

    }
    const validationResults = [
        await Backupfilepath(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}

async function validateHostName(value) {
    const errorElement = $('#Ftphostname-error');

    if (!value) {
        errorElement.text('Enter FTP host name').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await SpecialCharValidate(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await ShouldNotBeginWithNumber(value),
    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}

async function validateUserName(value) {
    const errorElement = $('#Ftpusername-error');

    if (!value) {
        errorElement.text('Enter FTP username').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await SpecialCharValidate(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await ShouldNotBeginWithNumber(value),
    ];

    return await CommonValidation(errorElement, validationResults);
    return true;
}

async function validatePassword(value) {
    const errorElement = $('#Ftppassword-error');
    if (!value) {
        errorElement.text('Enter FTP password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');

    }
    return true;
}


async function validatePort(value) {
    const errorElement = $('#Ftpport-error');

    if (!value) {
        errorElement.text('Enter FTP port').addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await PortReg(value),
    ];
    return await CommonValidation(errorElement, validationResults);

    return true;
}


async function validateSourcePath(value) {
    const errorElement = $('#Ftpsource-error');

    if (!value) {
        errorElement.text('Enter source path').addClass('field-validation-error');

        return false;

    } const validationResults = [
        await BackupSourcefilepath(value),

    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}


async function validateTargetPath(value) {
    const errorElement = $('#Ftptarget-error');

    if (!value) {
        errorElement.text('Enter target path').addClass('field-validation-error');

        return false;
    }
    const validationResults = [
        await BackupTargetfilepath(value),
    ];

    return await CommonValidation(errorElement, validationResults);

    return true;
}

function CronValidation() {

    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    var everyHours = $('#everyHours').val();
    var datetime = $('#datetimeCron').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var isScheduler = '';
    if (document.getElementById('switchMonthly').checked == true) {

        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();

        switch (Scheduler_types) {

            case "Minutes":
                errorElement = $('#CronMin-error');
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", errorElement);
                break;

            case "Hourly":
                errorElement = $('#CronHourly-error');
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", errorElement);
                errorElement = $('#CronHourMin-error');
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", errorElement);
                break;

            case "Daily":
                errorElement = $('#CroneveryHour-error');
                isSchedulerHour = validateHourJobNumber(everyHours, "Select hours", errorElement);
                errorElement = $('#Crondaysevery-error');
                isSchedulerDay = ValidateCronRadioButton(errorElement);
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;

            case "Weekly":
                errorElement = $('#CronddlHour-error');
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select hours", errorElement);
                isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;

            case "Monthly":
                errorElement = $('#MonthlyHours-error');
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select hours", errorElement);
                errorElement = $('#CronMon-error');
                let mt = $("#lblMonth").val()
                if (mt) {
                    isSchedulerDay = validateDayNumber(txtmonthday, "Select date(s)", errorElement);
                }
                errorElement = $('#CronMonthly-error');
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", errorElement);
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        errorElement = $('#CronExpression-error');
        isScheduler = validateDayNumber(datetime, "Select schedule time", errorElement) && validateprevNumber(datetime, "", errorElement);
    }
    return isScheduler;
}

function populateModalFields(Backupdata) {
    
    var scheduleTime = Backupdata?.scheduleTime?.split(" ")
    setTimeout(() => {
        
        if (Backupdata?.scheduleTime?.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }

        if (Backupdata?.scheduleTime?.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }

        if (scheduleTime?.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }

        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (Backupdata?.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (Backupdata?.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
        }

        if (scheduleTime?.length >= 12) {
            var year = parseInt(scheduleTime[12])
            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;

            $("#lblMonth").val(newdate).trigger("change")

            scheduleTime[5]?.split(",").forEach(function (i) {

                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)

}
function ClearErrorElements(errorElements) {
    errorElements.forEach(element => {
        $(element).text('').removeClass('field-validation-error');
    });

}

const clearCronExpressionData = () => {
    $('#bd_txtMins, #bd_txtHours, #bd_txtMinutes, #bd_ddlHours, #ddlMinutes, #datetimeCron, #textCronExpression, #bd_lblMonth, #bd_txtHourss, #bd_txtMinss, #MonthlyHours, #everyHours').val('');
    $('input[name=weekDays]').prop("checked", false)
    $('input[name=Days]').prop("checked", false)
    $('input[name=daysevery1]').prop("checked", false)
    $("#option1").prop("checked", true);
    $("#option3,#option2,#option4,#option5").prop("checked", false);
    $('input[name="Monthyday"]').prop('disabled', true);
    $('input[name="Monthyday"]').prop('checked', false);
};

$('#btnCreate').on('click', function () {
    getBackupConfigDetails()
})

$('#ScheduleId').on('click', function () {
    clearCronExpressionData();
    ClearErrorElements(errorElementt);
})

$("input[name='switchPlan']").on('change', function () {
    let backuptextpath = $('#txtbackuppath').val();
    let ftpchange = $('#ftphostname').val();
    var backupschedule = $(this).val();
    if (backupschedule.toLowerCase() === "once" && (backuptextpath !== "" || ftpchange !== "")) {
        $('#btnExecute').prop('disabled', false);
        $('#btnSave').prop('disabled', false);
        $('#pills-profile').hide();
    } else {
        $('#btnExecute').prop('disabled', true);
        // $('#btnSave').prop('disabled', true);
        $('#pills-profile').show();
    }
});

function switchPlanHandler() {

    if ($('#switchMonthly').is(':checked')) {
        $('#pills-profile').show();
    } else {
        $('#pills-profile').hide();
        $('#btnExecute').prop('disabled', true);
    }
}

function inlineRadioOptionsHandler() {

    if ($('#inlineRadio2').prop('checked')) {
        $('#pills-Local').show();
        $('#backuphidepath').hide();
        $('#btnExecute').prop('disabled', true);
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }
    else if ($('#inlineRadio1').prop('checked')) {
        $('#pills-Local').hide();
        $('#backuphidepath').show();
        $('#btnExecute').prop('disabled', true);
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }


}

switchPlanHandler();
inlineRadioOptionsHandler();

$('input[name="switchPlan"]').on('change', switchPlanHandler);
$('input[name="inlineRadioOptions"]').on('change', inlineRadioOptionsHandler);
$('input[name="inlineRadioOptions"]:checked').trigger('change');

function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#ChkValue').val(1);
    } else {
        $('#ChkValue').val(2);
    }
}
function formatDateTime(datetime) {
    const dateObj = new Date(datetime);
    const formattedDate = `${dateObj.getDate().toString().padStart(2, '0')}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getFullYear()}`;
    const formattedTime = `${dateObj.getHours().toString().padStart(2, '0')}:${dateObj.getMinutes().toString().padStart(2, '0')}:${dateObj.getSeconds().toString().padStart(2, '0')}`;
    return `${formattedDate} ${formattedTime}`;
}

function getBackupDetails() {

    $('#btnSave').prop('disabled', true);

    $.ajax({
        type: "GET",
        url: "/Admin/BackupData/GetList",
        async: true,
        success: function (response) {
            
            if (response.length > 0) {
                
                if (response[0].backUpType === "Cycle") {
                    populateModalFields(response[0]);
                }

                $('#btnSave').text('Update');
                $('#btnSave').prop('disabled', false);
                $('#txthostname').val(response[0].hostName);
                $('#txtdatabasename').val(response[0].databaseName);
                $('#txtusername').val(response[0].userName);
                $('#txtpassword').val(response[0].password);
                $('#inlineRadio1').prop('checked', response[0].isLocalServer);
                $('#inlineRadio2').prop('checked', response[0].isBackUpServer);
                $('#txtbackuppath').val(response[0].backUpPath);
                var backupType = response[0].backUpType;
                var keepBackupLast = response[0].keepBackUpLast;
                $("#ChkValue").val(backupType)
                $("input[name='switchPlan'][value='" + backupType + "']").prop('checked', true);
                var selectedRadioValue = $("input[name='inlineRadioOptions']:checked").val();

                $("#selectbackup").val(keepBackupLast).trigger('change');

                let ftpProperties = JSON.parse(response[0].properties);
                setTimeout(() => {
                    $('#ftphostname').val(ftpProperties.ftphostName);
                    $('#ftpusername').val(ftpProperties.ftpuserName);
                    $('#ftppassword').val(ftpProperties.ftphostPassword);
                    $('#ftpport').val(ftpProperties.ftpport);
                    $('#cpurl').val(ftpProperties.sourcepath);
                    $('#ftpurl').val(ftpProperties.targetpath);
                    $('#txtserverid').val(response[0].id);
                }, 3000)

                var clickedLink = "";
                var linkId = "";
                switch (response[0].scheduleType) {
                    case "1":
                        linkId = "nav-Minutes-tab";
                        setTimeout(() => {
                            clickedLink = document.getElementById(linkId);
                            clickedLink.click();
                            const { minutes } = parseMinCronExpression(response[0].cronExpression);
                            document.getElementById("txtMins").value = minutes;
                        }, 150)
                        break;

                    case "2":
                        linkId = "nav-Hourly-tab";
                        setTimeout(() => {
                            clickedLink = document.getElementById(linkId);
                            clickedLink.click();
                            const { hours, minutes } = parseHoursCronExpression(response[0].cronExpression);
                            document.getElementById("txtHours").value = hours;
                            document.getElementById("txtMinutes").value = minutes;
                        }, 150)
                        break;
                    case '3':
                        linkId = "nav-Daily-tab";
                        setTimeout(() => {
                            clickedLink = document.getElementById(linkId);
                            clickedLink.click();
                            const { hours, day } = parseDailyCronExpression(response[0].cronExpression);
                            document.getElementById("everyHours").value = hours;
                            if (day == "?") {
                                $("#defaultCheck-everyday").prop("checked", true);
                            }
                            else {
                                $("#defaultCheck-MON-FRI").prop("checked", true);
                            }
                        }, 150)
                        break;
                    case '4':
                        linkId = "nav-Weekly-tab";
                        setTimeout(() => {
                            clickedLink = document.getElementById(linkId);
                            clickedLink.click();
                            const { hours, day } = parseWeeklyCronExpression(response[0].cronExpression);
                            document.getElementById("ddlHours").value = hours;
                            dayconventor(day);
                        }, 150)
                        break;
                    case '5':
                        linkId = "nav-Monthly-tab";
                        setTimeout(() => {
                            clickedLink = document.getElementById(linkId);
                            clickedLink.click();
                            const { hours, month, days } = parseCronMonthExpression(response[0].cronExpression);
                            document.getElementById("MonthlyHours").value = hours;
                            document.getElementById("lblMonth").value = month;
                            monthDayconventor(days);
                        }, 150)
                        break;

                }


                $('input[name="inlineRadioOptions"]:checked').trigger('change');

                if (selectedRadioValue === 'isremote' && response[0].properties.ftphostName === "") {


                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                } else {

                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                }
                if (selectedRadioValue === 'islocal' && response[0].backUpPath === "") {
                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                } else {

                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                }
                if ((backupType?.toLowerCase() === "once")) {

                    $('#pills-profile').hide();
                    $('#btnExecute').prop('disabled', false);
                    $('#btnSave').prop('disabled', true);
                } else {
                    $('#pills-profile').show();
                    $('#btnExecute').prop('disabled', true);
                    $('#btnSave').prop('disabled', false);
                }
            } else {
                $('#btnSave').text('Save');
                $('#btnSave').prop('disabled', false);
            }

        },
        error: function (xhr, status, error) {

        }
    });
}

function backupDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}


let monthInput = document.getElementById("lblMonth");
let today = new Date();
let currentYear = today.getFullYear();
let currentMonth = today.getMonth() + 1;
let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth)


