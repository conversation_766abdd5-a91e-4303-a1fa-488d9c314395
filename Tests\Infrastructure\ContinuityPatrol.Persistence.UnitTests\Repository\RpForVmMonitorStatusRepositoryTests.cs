using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RpForVmMonitorStatusRepositoryTests : IClassFixture<RpForVmMonitorStatusFixture>
{
    private readonly RpForVmMonitorStatusFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RpForVmMonitorStatusRepository _repository;

    public RpForVmMonitorStatusRepositoryTests(RpForVmMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RpForVmMonitorStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetDeatilByInfraObjectId Tests

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldReturnMatchingRecord_WhenRecordExists()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST_001";
        var matchingStatus = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);
        var nonMatchingStatus = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("DIFFERENT_INFRA");

        _dbContext.rpForVmMonitorStatuses.AddRange(matchingStatus, nonMatchingStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldReturnNull_WhenNoMatchingRecords()
    {
        // Arrange
        var status = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("DIFFERENT_INFRA");
        _dbContext.rpForVmMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId("NON_EXISTENT_INFRA");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleRecordsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_MULTIPLE_TEST";
        var status1 = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);
        status1.CreatedDate = DateTime.UtcNow.AddDays(-2);

        var status2 = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);
        status2.CreatedDate = DateTime.UtcNow.AddDays(-1);

        _dbContext.rpForVmMonitorStatuses.AddRange(status1, status2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return one of the matching records (FirstOrDefault behavior)
        Assert.True(result.ReferenceId == status1.ReferenceId || result.ReferenceId == status2.ReferenceId);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetDeatilByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdIsNullOrEmpty(string infraObjectId)
    {
        // Arrange
        var status = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("VALID_INFRA");
        _dbContext.rpForVmMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldReturnActiveRecord_WhenActiveRecordExists()
    {
        // Arrange
        var infraObjectId = "INFRA_ACTIVE_TEST";
        var activeStatus = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);
        activeStatus.IsActive = true;

        _dbContext.rpForVmMonitorStatuses.Add(activeStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal(infraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldReturnInactiveRecord_WhenOnlyInactiveRecordExists()
    {
        // Arrange
        var infraObjectId = "INFRA_INACTIVE_TEST";
        var inactiveStatus = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);
        inactiveStatus.IsActive = false;

        _dbContext.rpForVmMonitorStatuses.Add(inactiveStatus);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.IsActive);
        Assert.Equal(infraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDeatilByInfraObjectId_ShouldHandleSpecialCharactersInInfraObjectId()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST@#$%_001";
        var status = _fixture.CreateRpForVmMonitorStatusWithInfraObjectId(infraObjectId);

        _dbContext.rpForVmMonitorStatuses.Add(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDeatilByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRpForVmMonitorStatus_WhenValidEntity()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmMonitorStatusDto;
        monitorStatus.Type = "RpForVm";
        monitorStatus.InfraObjectId = "INFRA_TEST_001";
        monitorStatus.InfraObjectName = "Test Infrastructure Object";
        monitorStatus.WorkflowId = "WORKFLOW_TEST_001";
        monitorStatus.WorkflowName = "Test Workflow";
        monitorStatus.ConfiguredRPO = "4 hours";
        monitorStatus.Threshold = "2 hours";
        monitorStatus.DataLagValue = "30 minutes";
        monitorStatus.Properties = "{\"property1\":\"value1\",\"property2\":\"value2\"}";

        // Act
        var result = await _repository.AddAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Type, result.Type);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.InfraObjectName, result.InfraObjectName);
        Assert.Equal(monitorStatus.WorkflowId, result.WorkflowId);
        Assert.Equal(monitorStatus.WorkflowName, result.WorkflowName);
        Assert.Equal(monitorStatus.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Equal(monitorStatus.Threshold, result.Threshold);
        Assert.Equal(monitorStatus.DataLagValue, result.DataLagValue);
        Assert.Equal(monitorStatus.Properties, result.Properties);
        Assert.Single(_dbContext.rpForVmMonitorStatuses);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmMonitorStatusDto;
        _dbContext.rpForVmMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(monitorStatus.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.Id, result.Id);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorStatus.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmMonitorStatusDto;
        _dbContext.rpForVmMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(monitorStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(monitorStatus.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var monitorStatuses = _fixture.RpForVmMonitorStatusList;
        _dbContext.rpForVmMonitorStatuses.AddRange(monitorStatuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorStatuses.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmMonitorStatusDto;
        _dbContext.rpForVmMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        var updatedType = "Updated Type";
        var updatedThreshold = "Updated Threshold";
        monitorStatus.Type = updatedType;
        monitorStatus.Threshold = updatedThreshold;

        // Act
        var result = await _repository.UpdateAsync(monitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedType, result.Type);
        Assert.Equal(updatedThreshold, result.Threshold);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var monitorStatus = _fixture.RpForVmMonitorStatusDto;
        _dbContext.rpForVmMonitorStatuses.Add(monitorStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(monitorStatus);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(monitorStatus.Id);
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        var monitorStatuses = new List<RpForVmMonitorStatus>
        {
            _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("INFRA_001"),
            _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("INFRA_002"),
            _fixture.CreateRpForVmMonitorStatusWithInfraObjectId("INFRA_003")
        };

        // Act
        var result = await _repository.AddRangeAsync(monitorStatuses);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count());

        var savedEntities = await _repository.ListAllAsync();
        Assert.Equal(3, savedEntities.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.rpForVmMonitorStatuses.RemoveRange(_dbContext.rpForVmMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }
}
