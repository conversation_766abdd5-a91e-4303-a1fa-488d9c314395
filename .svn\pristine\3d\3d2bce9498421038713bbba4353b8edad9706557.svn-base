﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;

public class GetTeamMasterPaginatedListQueryHandler : IRequestHandler<GetTeamMasterPaginatedListQuery,
    PaginatedResult<TeamMasterListVm>>
{
    private readonly IMapper _mapper;
    private readonly ITeamMasterRepository _teamMasterRepository;
    private readonly ITeamResourceRepository _teamResourceRepository;

    public GetTeamMasterPaginatedListQueryHandler(IMapper mapper, ITeamMasterRepository teamMasterRepository,
        ITeamResourceRepository teamResourceRepository)
    {
        _mapper = mapper;
        _teamMasterRepository = teamMasterRepository;
        _teamResourceRepository = teamResourceRepository;
    }

    public async Task<PaginatedResult<TeamMasterListVm>> Handle(GetTeamMasterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new TeamMasterFilterSpecification(request.SearchString);

        var teamMasterQuery =await _teamMasterRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var teamResourceQuery = _teamResourceRepository.GetPaginatedQuery();

        var teamMasterList = _mapper.Map<PaginatedResult<TeamMasterListVm>>(teamMasterQuery);

        teamMasterList.Data.ForEach(tm =>
        {
            tm.MemberCount = teamResourceQuery
                .Count(x => x.TeamMasterName.Trim().ToLower().Equals(tm.GroupName.Trim().ToLower())).ToString();
        });

        return teamMasterList;
    }
}