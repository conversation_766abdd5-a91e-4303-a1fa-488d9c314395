﻿namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetByType;

public class GetMSSQLMonitorStatusDetailByTypetQueryHandler : IRequestHandler<GetMSSQLMonitorStatusDetailByTypeQuery,
    List<MSSQLMonitorStatusDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorStatusRepository _mssqlMonitorStatusRepository;

    public GetMSSQLMonitorStatusDetailByTypetQueryHandler(IMssqlMonitorStatusRepository mssqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mssqlMonitorStatusRepository = mssqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<MSSQLMonitorStatusDetailByTypeVm>> Handle(GetMSSQLMonitorStatusDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var mssqlMonitorStatus = await _mssqlMonitorStatusRepository.GetDetailByType(request.Type);

        return mssqlMonitorStatus.Count <= 0
            ? new List<MSSQLMonitorStatusDetailByTypeVm>()
            : _mapper.Map<List<MSSQLMonitorStatusDetailByTypeVm>>(mssqlMonitorStatus);
    }
}