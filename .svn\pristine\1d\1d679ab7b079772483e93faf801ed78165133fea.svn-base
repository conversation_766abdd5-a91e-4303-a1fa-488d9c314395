using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftCategoryList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetConflictList;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;
using ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftManagementMonitorStatusService : BaseClient, IDriftManagementMonitorStatusService
{
    public DriftManagementMonitorStatusService(IConfiguration config, IAppCache cache, ILogger<DriftManagementMonitorStatusService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementMonitorStatusList()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss");

        return await GetFromCache<List<DriftManagementMonitorStatusListVm>>(request, "GetDriftManagementMonitorStatusList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftManagementMonitorStatusCommand createDriftManagementMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss", Method.Post);

        request.AddJsonBody(createDriftManagementMonitorStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftManagementMonitorStatusCommand updateDriftManagementMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss", Method.Put);

        request.AddJsonBody(updateDriftManagementMonitorStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DriftManagementMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/{id}");

        return await Get<DriftManagementMonitorStatusDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftManagementMonitorStatusNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/name-exist?driftmanagementmonitorstatusName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion


    #region DriftDashboardResourceStatus
    public async Task<DriftDashboardResourceStatusVm> GetDriftDashboardResourceStatus()
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/resource-status");

        return await Get<DriftDashboardResourceStatusVm>(request);
    }

    #endregion
    #region Paginated
    public async Task<PaginatedResult<DriftManagementMonitorStatusListVm>> GetPaginatedDriftManagementMonitorStatuss(GetDriftManagementMonitorStatusPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/paginated-list{query}");

        return await Get<PaginatedResult<DriftManagementMonitorStatusListVm>>(request);
    }

    public async Task<List<DriftManagementMonitorStatusListVm>> GetDriftManagementStatusByInfraObjectId(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/driftmanagementmonitorstatuss/infraobjectid?infraObjectId={infraObjectId}");

        return await Get<List<DriftManagementMonitorStatusListVm>>(request);
    }

    public async Task<List<GetDriftTreeListVm>> GetDriftTreeList()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss/drift-tree");

        return await Get<List<GetDriftTreeListVm>>(request);
    }

    public async Task<List<DriftResourceSummaryListVm>> GetDriftResourceSummary()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss/drift-resource-summary");

        return await Get<List<DriftResourceSummaryListVm>>(request);
    }

    public async Task<DriftOperationSummaryVm> GetDriftOperationSummary()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss/drift-operation-summary");

        return await Get<DriftOperationSummaryVm>(request);
    }

    public async Task<DriftCategoryListVm> GetDriftCategory()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss/drift-category");

        return await Get<DriftCategoryListVm>(request);
    }

    public async Task<List<ConflictListVm>> GetConflictOverView()
    {
        var request = new RestRequest("api/v6/driftmanagementmonitorstatuss/conflict-overview");

        return await Get<List<ConflictListVm>>(request);
    }

    #endregion
}
