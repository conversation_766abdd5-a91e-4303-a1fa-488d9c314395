﻿namespace ContinuityPatrol.Shared.Core.Constants;

public sealed class ApplicationConstants
{
    public static class Module
    {
        public const string ModulePart = "ContinuityPatrol.Modules.*.dll";

        public const string ModulePath = "Modules\\net6.0";
    }

    public static class Cache
    {
        public const string AllAlertsCacheKey = "all-alerts";

        public const string AllCompaniesCacheKey = "all-companies";

        public const string AllCompaniesNameCacheKey = "all-companies-name";

        public const string AllBusinessFunctionsCacheKey = "all-businessfunctions";

        public const string AllBusinessFunctionsNameCacheKey = "all-businessfunctions-name";

        public const string AllBusinessServicesCacheKey = "all-businessservices";

        public const string AllBusinessServicesNameCacheKey = "all-businessservices-name";

        public const string AllCredentialsCacheKey = "all-credentials";

        public const string AllCredentialsNamesCacheKey = "all-credentials-name";

        public const string AllNodesCacheKey = "all-nodes";

        public const string AllNodesNameCacheKey = "all-nodes-name";

        public const string AllServersCacheKey = "all-servers";

        public const string AllServersNameCacheKey = "all-servers-name";

        public const string AllUsersCacheKey = "all-users";

        public const string AllUsersNameCacheKey = "all-users-name";

        public const string AllUserLoginCacheKey = "all-userlogin";

        public const string AllUserLoginNameCacheKey = "all-userlogin-name";

        public const string AllUsersRoleCacheKey = "all-users-role";

        public const string AllInfraObjectsCacheKey = "all-infraobject";

        public const string AllInfraObjectSchedulersCacheKey = "all-infraobjectscheduler";

        public const string AllInfraObjectSchedulerNamesCacheKey = "all-infraobjectscheduler-name";

        public const string AllInfraObjectsNamesCacheKey = "all-infraobject-name";

        public const string AllSitesCacheKey = "all-site";

        public const string AllTeamMastersCacheKey = "all-team_master";

        public const string AllTeamMasterNameCacheKey = "all-team-master-name";

        public const string AllTeamResourceCacheKey = "all-team-resource";

        public const string AllTeamResourceNameCacheKey = "all-team-resource-name";

        public const string AllSitesNameCacheKey = "all-site-name";

        public const string AllJobsCacheKey = "all-job";

        public const string AllJobsNameCacheKey = "all-job-name";

        public const string AllSettingsCacheKey = "all-setting";

        public const string AllSettingsNameCacheKey = "all-setting-name";

        public const string AllDatabaseNameCacheKey = "all-databaseName";

        public const string AllDatabasesCacheKey = "all-database";

        public const string AllDrCalenderCacheKey = "all-drcalender";

        public const string AllReplicationsCacheKey = "all-replication";

        public const string AllReplicationNameCacheKey = "all-replication-name";

        public const string AllSingleSignOnCacheKey = "all-singlesignon";

        public const string AllSingleSignOnNameCacheKey = "all-singlesignon-name";

        public const string AllFormsCacheKey = "all-forms";

        public const string AllFormNamesCacheKey = "all-form-name";

        public const string AllNodeConfigurationsCacheKey = "all-nodeConfigurations";

        public const string AllNodeConfigurationNamesCacheKey = "all-nodeConfiguration-name";

        public const string AllNodeWorkflowExecutionsCacheKey = "all-nodeConfigurations";

        public const string AllNodeWorkflowExecutionNamesCacheKey = "all-nodeConfiguration-name";

        public const string AllFormHistoryCacheKey = "all-formshistory";

        public const string AllFormHistoryNamesCacheKey = "all-formhistory-name";

        public const string AllFormTypesCacheKey = "all-formtypes";

        public const string AllFormTypeCategoryCacheKey = "all-formtypecategory";

        public const string AllFormTypeCategoryNamesCacheKey = "all-formtypecategory_name";

        public const string AllFormTypeNamesCacheKey = "all-formtype-name";

        public const string AllLicenseManagersCacheKey = "all-licensemanagers";

        public const string AllLicenseHistoriesCacheKey = "all-licensehistories";

        public const string AllLicenseInfoCacheKey = "all-licenseinfo";

        public const string AllLicenseManagerNamesCacheKey = "all-licensemanager-name";

        public const string AllLicenseHistoryNamesCacheKey = "all-licensehistory-name";

        public const string AllLicenseInfoNamesCacheKey = "all-licenseinfo-name";

        public const string AllSettingCacheKey = "all-setting";

        public const string AllGlobalSettingCacheKey = "all-global-setting";

        public const string AllWorkflowVersionHistoryCacheKey = "all-workflow-version-history";

        public const string AllWorkflowProfileInfosCacheKey = "all-workflowprofile-info";

        public const string AllWorkflowPredictionCacheKey = "all-workflowprediction";

        public const string AllWorkflowProfileInfosNameCacheKey = "all-workflowprofile-info-name";

        public const string AllWorkflowActionBuildersCacheKey = "all-workflowactionbuilder";

        public const string AllWorkflowActionBuildersNameCacheKey = "all-workflowactionbuilder-name";

        public const string AllWorkflowCacheKey = "all-workflow";

        public const string AllWorkflowNameCacheKey = "all-workflow-name";

        public const string AllExecutionTempCacheKey = "all-executiontemp";

        public const string AllExecutionTempNameCacheKey = "all-executiontemp-name";

        public const string AllApprovalMatrixCacheKey = "all-approvalmatrix";

        public const string AllApprovalMatrixNameCacheKey = "all-approvalmatrix-name";

        public const string AllWorkflowOperationGroupCacheKey = "all-workflowoperationgroup";

        public const string AllWorkflowOperationGroupNameCacheKey = "all-workflowoperationgroup-name";

        public const string AllWorkflowOperationCacheKey = "all-workflowoperation";

        public const string AllWorkflowOperationNameCacheKey = "all-workflowoperation-name";

        public const string AllWorkflowInfraObjectCacheKey = "all-workflow";

        public const string AllWorkflowInfraObjectNameCacheKey = "all-workflowinfraobject-name";

        public const string AllWorkflowActionTypesCacheKey = "all-workflowactiontype";

        public const string AllWorkflowActionResultCacheKey = "all-workflowactionResult";

        public const string AllWorkflowCategoriesCacheKey = "all-workflowinfraobjectcategory";

        public const string AllWorkflowCategoriesNameCacheKey = "all-workflowcategory-name";

        public const string AllTemplatesCacheKey = "all-templates";

        public const string AllTemplateHistoryCacheKey = "all-templatehistory";

        public const string AllTemplatesNameCacheKey = "all-template-name";

        public const string AllWorkflowActionsCacheKey = "all-workflowaction";

        public const string AllWorkflowActionsNameCacheKey = "all-workflowaction-name";

        public const string AllWorkflowProfileCacheKey = "all-Workflow-profile";

        public const string AllWorkflowProfileNameCacheKey = "all-Workflow-profile-name";

        public const string AllWorkflowExecutionEventLogCacheKey = "all-workflow-execution-event-log";

        public const string AllWorkflowHistoryCacheKey = "all-workflowhistory";

        public const string AllWorkflowHistoryNameCacheKey = "all-workflowhistory_name";

        public const string AllAccessManagersCacheKey = "all-accessmanagers";

        public const string AllAccessManagersRoleCacheKey = "all-accessmanagers-role";

        public const string AllReportsNameCacheKey = "all-Report-name";

        public const string AllReportScheduleCacheKey = "all-ReportSchedule-name";

        public const string AllTableAccessesNameCacheKey = "all-tableaccess-name";

        public const string AllTableAccessesCacheKey = "all-tableaccess";

        public const string AllGroupPolicyCacheKey = "all-grouppolicy";

        public const string AllGroupPoliciesNameCacheKey = "all-grouppolicy_name";

        public const string AllDataSetsNameCacheKey = "all-datasets-name";

        public const string AllDataSetsCacheKey = "all-datasets";

        public const string AllAlertNameCacheKey = "all-alerts-name";

        public const string AllAlertInformationNameCacheKey = "all-alertinformation-name";

        public const string AllAlertCacheKey = "all-alerts";

        public const string AllAlertReceiverNameCacheKey = "all-alertreceivers-name";

        public const string AllAlertReceiverCacheKey = "all-alertreceivers";

        public const string AllAlertNotificationNameCacheKey = "all-alertnotifications-name";

        public const string AllAlertNotificationCacheKey = "all-alertnotifications";

        public const string AllAlertMasterNameCacheKey = "all-alertmasters-name";

        public const string AllAlertMasterCacheKey = "all-alertmasters";

        public const string AllBusinessViewCacheKey = "all-businessviews";

        public const string AllBusinessViewNameCacheKey = "all-businessviews-name";

        public const string AllDataSetColumnsNameCacheKey = "all-datasetcolumns-name";

        public const string AllDataSetColumnsCacheKey = "all-datasetcolumns";

        public const string AllMonitorServicesCacheKey = "all-monitorservices";

        public const string AllMSSQLMonitorLogsCacheKey = "all-mssqlmonitorLogs";

        public const string AllDB2HADRMonitorLogCacheKey = "all-db2HADRMonitorLog";

        public const string AllMssqlNativeLogShippingMonitorLogCacheKey = "all-mssqlNativelogshippingmonitorlog";

        public const string AllMongoDBMonitorLogCacheKey = "all-mongodbmonitorlog";

        public const string AllSVCMssqlMonitorLogCacheKey = "all-svcmssqlmonitorlog";

        public const string AllMSSQLAlwaysOnMonitorLogsCacheKey = "all-mssqlalwaysonmonitorLogs";

        public const string AllMSSQLAlwaysOnMonitorStatusCacheKey = "all-mssqlalwaysonmonitorStatus";

        public const string AllMYSQLMonitorLogsCacheKey = "all-mysqlmonitorstatus";

        public const string AllOracleRACStatusCacheKey = "all-oracleracmonitorstatus";

        public const string AllOracleRACStatusNameCacheKey = "all-oracleracmonitorstatus-name";

        public const string AllOracleMonitorLogsCacheKey = "all-oraclemonitorstatus";

        public const string AllOracleRACMonitorLogsCacheKey = "all-oracleracmonitorlogs";

        public const string AllOracleRAConitorLogsNameCacheKey = "all-oracleracmonitorlogs-name";

        public const string AllPostgresMonitorLogsCacheKey = "all-postgresmonitorstatus";

        public const string AllMSSQLMonitorStatusCacheKey = "all-mssqlmonitorstatus";

        public const string AllMYSQLMonitorStatusCacheKey = "all-mysqlmonitorstatus";

        public const string AllOracleMonitorStatusCacheKey = "all-oraclemonitorstatus";

        public const string AllPostgresMonitorStatusCacheKey = "all-postgresmonitorstatus";

        public const string AllMonitorServicesNameCacheKey = "all-monitorservices-infraobjectname";

        public const string AllMSSQLMonitorLogsNameCacheKey = "all-mssqlmonitorlogs-name";

        public const string AllMSSQLAlwaysOnMonitorLogsNameCacheKey = "all-mssqlalwaysonmonitorlogs-name";

        public const string AllMSSQLAlwaysOnMonitorStatusNameCacheKey = "all-mssqlalwaysonmonitorstatus-name";

        public const string AllMYSQLMonitorLogsNameCacheKey = "all-mysqlmonitorlogs-name";

        public const string AllPostgresMonitorLogsNameCacheKey = "all-postgresmonitorlogs-name";

        public const string AllOracleMonitorLogsNameCacheKey = "all-oraclemonitorlogs-name";

        public const string AllMSSQLMonitorStatusNameCacheKey = "all-mssqlmonitorstatus-name";

        public const string AllMYSQLMonitorStatusNameCacheKey = "all-mysqlmonitorstatus-name";

        public const string AllOracleMonitorStatusNameCacheKey = "all-oraclemonitorstatus-name";

        public const string AllPostgresMonitorStatusNameCacheKey = "all-postgresmonitorstatus-name";

        public const string AllAlertInformationCacheKey = "all-monitorservices-infraobjectname";

        public const string AllMonitorServiceStatusesCacheKey = "all-monitorservice-statuses";

        public const string AllMonitorServiceStatusesNameCacheKey = "all-monitorservice-statuses-infraobjectname";

        public const string AllMonitorServiceLogsCacheKey = "all-monitorservice-logs";

        public const string AllMonitorServiceLogsNameCacheKey = "all-monitorservice-logs-infraobjectname";

        public const string AllUserActivitiesCacheKey = "all-useractivities";

        public const string AllUserActivitiesNameCacheKey = "all-useractivities-name";

        public const string AllUserRolesCacheKey = "all-userroles";

        public const string AllUserRolesNameCacheKey = "all-userroles-name";

        public const string AllUserInfraObjectsCacheKey = "all-userinfraobjects";

        public const string AllUserInfraObjectsNameCacheKey = "all-userinfraobjects-name";

        public const string AllSiteTypeCacheKey = "all-sitetypes";

        public const string AllSiteTypeTypeCacheKey = "all-sitetype-type";

        public const string AllServerTypeCacheKey = "all-servertypes";

        public const string AllServerTypeNameCacheKey = "all-servertype-name";

        public const string AllServerSubTypeCacheKey = "all-server-subtypes";

        public const string AllServerSubTypeNameCacheKey = "all-server-subtype-name";

        public const string AllComponentTypeCacheKey = "all-componenttypes";

        public const string AllComponentTypeNameCacheKey = "all-componenttype-name";

        public const string AllSolutionHistoryCacheKey = "all-solutionhistory";

        public const string AllSolutionHistoryNameCacheKey = "all-solutionhistory-name";

        public const string AllPluginManagerCacheKey = "all-Pluginmanager";

        public const string AllDRReadyStatusCacheKey = "all-drreadystatusCacheKey";

        public const string AllBusinessServiceHealthStatusCacheKey = "all-business_service_health_status";

        public const string AllBusinessServiceHealthStatusNameCacheKey = "all-business_service_health_status-name";

        public const string AllDRReadyStatusNameCacheKey = "all-drreadystatusnamecacheKey-name";

        public const string AllBusinessServiceAvailabilityCacheKey = "all-businessServiceAvailabilityCacheKey";

        public const string AllBusinessServiceAvailabilityNameCacheKey =
            "all-BusinessServiceAvailabilitynamecacheKey-name";

        public const string AllStateMonitorLogNameCacheKey = "all-statemonitorlognamecachekey-name";

        public const string AllStateMonitorStatusNameCacheKey = "all-statemonitorstatusnamecachekey-name";

        public const string AllBusinessServicEvaluationCacheKey = "all-BusinessServiceEvaluationCacheKey";

        public const string AllBusinessServiceEvaluationNameCacheKey = "all-BusinessServiceEvaluationnamecacheKey-name";

        public const string AllPluginManagerNamesCacheKey = "all-Pluginmanager-name";

        public const string AllPluginManagerHistoryCacheKey = "all-pluginmanagerhistory";

        public const string AllPluginManagerHistoryNamesCacheKey = "all-pluginmanagerhistory-name";

        public const string AllDRReadyLogsCacheKey = "all-drreadylogs";

        public const string AllDRReadyLogCacheKey = "all-drreadylog";

        public const string AllDRReadyLogNameCacheKey = "all-drreadylog-name";

        public const string AllBusinessServiceLogCacheKey = "all-business_service_log";

        public const string AllBusinessServiceLogNameCacheKey = "all-business_service_log-name";

        public const string AllDRReadyLogsNamesCacheKey = "all-drreadylogs-name";

        public const string AllInfraSummaryCacheKey = "all-infrasummary";

        public const string AllInfraSummaryNamesCacheKey = "all-infrasummary-name";

        public const string AllSmtpConfigurationNamesCacheKey = "all-smtpconfiguration-name";

        public const string AllSmsConfigurationCacheKey = "all=smsconfiguration";

        public const string AllHeatMapStatusCacheKey = "all-heatmap_status";

        public const string AllRiskMitigationCacheKey = "all-risk_mitigation";

        public const string AllHeatMapLogsCacheKey = "all-heatmap_logs";

        public const string AllServiceDRProtectionsCacheKey = "all-service_drprotections";

        public const string AllServiceDiagramDetailsCacheKey = "all-service_diagram_details";

        public const string AllDataLagStatusCacheKey = "all-datalag_status";

        public const string AllDataLagLogCacheKey = "all-datalag_log";

        public const string AllRpoSlaDeviationReportsCacheKey = "all-rpo-sla-deviation-reports";

        public const string AllRpoSlaDeviationReportsNameCacheKey = "all-rpo-sla-deviation-reports-name";

        public const string AllDashboardViewLogCacheKey = "all-dashboard_view_log";

        public const string AllDashboardViewLogNameCacheKey = "all-dashboard_view_log_name";

        public const string AllInfraObjectInfosCacheKey = "all-infraobject_infos";

        public const string AllMonitorServiceCacheKey = "all-monitor_service";

        public const string AllMonitorServiceNameCacheKey = "all-monitor_service-name";

        public const string AllImpactAvailabilityCacheKey = "all-impact_availabilities";

        public const string AllInfraObjectSchedulerWorkflowDetailCacheKey =
            "all-infraobject-scheduler-workflow-details";

        public const string AllBusinessServiceStatusCacheKey = "all-businessservice-status";

        public const string AllWorkflowPermissionCacheKey = "all-workflowpermission";

        public const string AllWorkflowPermissionNameCacheKey = "all-workflowpermission-name";

        public const string AllReplicationMasterCacheKey = "all-replicationmaster";

        public const string AllReplicationMasterNameCacheKey = "all-replicationmaster-name";

        public const string AllInfraReplicationMappingCacheKey = "all-infrareplicationmapping";

        public const string AllInfraReplicationMappingNameCacheKey = "all-infrareplicationmapping-name";

        public const string AllAboutCPCacheKey = "all-aboutcp";

        public const string AllDb2HaDrMonitorStatusCacheKey = "all-d2HaDrMonitorStatus";

        public const string AllDb2HaDrMonitorStatusNameCacheKey = "all-d2HaDrMonitorStatus-name";

        public const string AllMsSqlNativeLogShippingMonitorStatusCacheKey = "all-mssqlNativeLogShippingMonitorStatus";

        public const string AllMsSqlNativeLogShippingMonitorStatusNameCacheKey = "all-mssqlNativeLogShippingMonitorStatus-name";

        public const string AllMongoDbMonitorStatusCacheKey = "all-mongoDbMonitorStatus";

        public const string AllMongoDbMonitorStatusNameCacheKey = "all-mongoDbMonitorStatus-name";

        public const string AllSvcMsSqlMonitorStatusCacheKey = "all-svcMsSqlMonitorStatus";

        public const string AllSvcMsSqlMonitorStatusNameCacheKey = "all-svcMsSqlMonitorStatus-name";

        public const string AllSvcGMMonitorStatusCacheKey = "all-svcGMMonitorStatus";

        public const string AllSvcGMMonitorStatusNameCacheKey = "all-svcGMMonitorStatus-name";

        public const string AllUserGroupCacheKey = "all-UserGroup-name";

        public const string AllSQLDbMirroingStatusCachekey = "all-SQLDbMirroingStatus-name";

        public const string AllTimeIntervalMasterCacheKey = "all-timeintervalmaster";

        public const string AllTimeIntervalMasterNameCacheKey = "all-timeintervalmaster-name";

        public const string AllBfBiaMatrixCacheKey = "all-bfbiamatrix";

        public const string AllBfBiaMatrixNameCacheKey = "all-bfbiamatrix-name";

        public const string AllBfBiaMatrixDetailsCacheKey = "all-bfbiamatrixdetails";

        public const string AllBfBiaMatrixDetailsNameCacheKey = "all-bfbiamatrixdetails-name";

        public const string AllBiaProfileImpactTypesCacheKey = "all-biaprofileimpacttypes";

        public const string AllBiaProfileImpactTypesNameCacheKey = "all-biaprofileimpacttypes-name";

        public const string AllBiaProfileMasterCacheKey = "all-biaprofilemaster";

        public const string AllBiaProfileMasterNameCacheKey = "all-biaprofilemaster-name";

        public const string AllBiaProfileTimeIntervalCacheKey = "all-biaprofiletimeinterval";

        public const string AllBiaProfileTimeIntervalNameCacheKey = "all-biaprofiletimeinterval-name";

        public const string AllImpactRelationBfToBfCacheKey = "all-impactrelationbftobf";

        public const string AllImpactRelationBfToBfNameCacheKey = "all-impactrelationbftobf-name";

        public const string AllImpactRelationBfToBsCacheKey = "all-impactrelationbftobs";

        public const string AllImpactRelationBfToBsNameCacheKey = "all-impactrelationbftobs-name";

        public const string AllImpactRelationBsToBsCacheKey = "all-impactrelationbstobs";

        public const string AllImpactRelationBsToBsNameCacheKey = "all-impactrelationbstobs-name";

        public const string AllImpactRelationInfraToBfCacheKey = "all-impactrelationinfratobf";

        public const string AllImpactRelationInfraToBfNameCacheKey = "all-impactrelationinfratobf-name";

        public const string AllEscalationMatrixCacheKey = "all-escalation-matrix";

        public const string AllEscalationMatrixLevelCacheKey = "all-escalation-level";

        public const string AllPageBuildersCacheKey = "all-pagebuilders";

        public const string AllPageBuilderNamesCacheKey = "all-pagebuilders-name";

        public const string AllPageSolutionMappingCacheKey = "all-pagesolutionmappings";

        public const string AllPageIncidentNameCacheKey = "all-pageincident-name";

        public const string AllPageIncidentLogsCacheKey = "all-pageincidentlogs";

        public const string AllPageIncidentDailyCacheKey = "all-pageincidentdaily";

        public const string AllPageRoboCopyJobCacheKey = "all-pagerobocopyjob";

        public const string AllRoboCopyCacheKey = "all-robocopy";

        public const string AllPageDataSyncJobCacheKey = "all-pagedatasyncjob";

        public const string AllDataSyncCacheKey = "all-datasync";

        public const string AllPageRsyncJobCacheKey = "all-pagersyncjob";

        public const string AllRsyncCacheKey = "all-rsync";
        public const string AllPageReplicationJobNameCacheKey = "all-pageReplicationJob-name";

        public const string AllReplicationJobCacheKey = "all-replicationjobs";


        public const string AllWorkflowCategoryView = "all_workflowcategoryview";

        public const string AllWorkflowCategoryViewlist = "all_workflowCategorview_list";

        public const string AllServerLogsCacheKey = "all-serverlog";

        public const string AllServerLogsNameCacheKey = "all-serverlog-name";
    }
}