﻿let msServiceStatus = "";
let msStatus = "";
let msId = "";
let datas = [];
let msInfra = [];
let selectedValues = [];
let actionData = {};
let addedname = [];
//let allServerIds = []; deadcode

$(function () {
  
    let Permission = {
        'Create': $("#ManageCreate").data("create-permission").toLowerCase(),
        'Delete': $("#ManageDelete").data("delete-permission").toLowerCase()
    }
    let monitorURL = {
        nameExistUrl: "Manage/MonitoringServices/IsServiceNameExist",
        getPagination: "/Manage/MonitoringServices/GetPagination",
        CreateOrUpdate: "Manage/MonitoringServices/CreateOrUpdate",
        Delete: "Manage/MonitoringServices/Delete",
        GetInfraObjectsByBusinessServiceId: "Configuration/InfraObject/GetInfraObjectByBusinessServiceId",
        GetServersByInfraObjectId: "Configuration/InfraObject/GetServersByInfraObjectId",
        GetServerDataByServerId: "Manage/MonitoringServices/GetServerById",
        getWorkflowByType: "ITAutomation/WorkflowInfraObject/GetWorkflowByInfraObjectIdAndActionType",
        UpdateMonitorStatus: "Manage/MonitoringServices/UpdateMonitorStatus",
        CheckWindowServiceurl: 'ITAutomation/WorkflowExecution/CheckWindowsService',
    }

    if (Permission.Create == 'false') {
        $("#btnMonitoringServiceCreate").addClass("btn-disabled").css("cursor", "not-allowed").removeAttr("data-bs-toggle data-bs-target id");
    }
 
    let dataTable = $('#tblMoniterService').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": false,
        "filter": true,
        "order": [],
        "ajax": {
            "type": "GET",  
            "url": monitorURL.getPagination,
            "dataType": "json",
            "data": function (d) {
                d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                d.pageSize = d?.length;
                d.searchString = selectedValues?.length === 0 ? $('#msSearch').val() : selectedValues.join(';');
                selectedValues.length = 0;
                d.businessServiceId = $('#msListOperationService').find("option:selected").val() === "All" ? '' : $('#msListOperationService').find("option:selected").val();
                d.infraObjectId = $('#msListInfra').find("option:selected").val() === "All" ? '' : $('#msListInfra').find("option:selected").val()
            },
            "dataSrc": function (json) {
                if (json?.success && json?.data?.data && json?.data?.data.length) {
                    json?.data?.data?.forEach((item) => {
                        if (item.lastExecutionTime) {
                            let date = new Date(item.lastExecutionTime);
                            item.lastExecutionTime = `${('0' + date.getDate()).slice(-2)}-${('0' + (date.getMonth() + 1)).slice(-2)}-${date.getFullYear()} ${('0' + date.getHours()).slice(-2)}:${('0' + date.getMinutes()).slice(-2)}:${('0' + date.getSeconds()).slice(-2)}`;
                        }
                        datas.push(item);
                        if (item.businessServiceName) {
                            $('#msListOperationService').append(`<option value="${item.businessServiceId}">${item.businessServiceName}</option>`);
                        }
                    });
                } else {
                    notificationAlert("warning", json?.message);
                    return false;
                }

                $('#msListOperationService').find('option').each(function () {
                    $(this).siblings(`[value="${this.value}"]`).remove();
                });
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                $(".pagination-column").toggleClass("disabled", json?.data?.data?.length === 0);
                return json?.data?.data;
            }
        },
        "columnDefs": [
            { "targets": [1, 2, 3, 4], "className": "truncate" }
        ],
        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "orderable": false,
                "render": (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            { "data": "businessServiceName", "name": "businessServiceName" },
            { "data": "infraObjectName", "name": "infraObjectName" },
            { "data": "serverName", "name": "serverName" },
            {   "data": "authenticationType","name": "authenticationType",
                "render": (data, type, row) => {
                    return row.type ? row.type.replace(/^use\s+/i, '') : '';
                }
            },
            {
                "data": "servicePath", "name": "servicePath",
                "render": (data, type, row) => {    
                    if (row?.properties) {
                        const props = JSON.parse(row.properties);
                        if (props?.details?.length) {
                            return type === 'display'
                                ? `<span title="${props.details.map(item => item.name).join(', ') || 'NA'}" class="text-truncate" style="max-width:450px; display:inline-block">${props.details.map(item => item.name).join(', ') || 'NA'}</span>`
                                : data;
                        }
                    } else {
                        return '-';
                    }              
                    
                }
            },
            
            {
                "data": "lastExecutionTime", "name": "lastExecutionTime",
                "render": (data) => data || 'NA'
            },         
            {
                "data": "status", "name": "Status",
                "render": (data) => {
                    if (!data) return `<td></td>`;

                    let status = data.toLowerCase();
                    let iconClass = {
                        started: "text-success cp-success me-1",
                        stopped: "text-danger cp-error me-1",
                        pending: "cp-pending text-warning me-1"
                    }[status];
                    let title = data.charAt(0).toUpperCase() + data.slice(1);
                    return `<td><i class="${iconClass}" title="${title}"></i></td><td><span>${title}</span></td>`;
                }
            },
            {
                "orderable": false,
                "render": (data, type, row) => {
                    let isEditAllowed = Permission.Create === "true";
                    let isDeleteAllowed = Permission.Delete === "true";
                    let isStarted = row?.status?.toLowerCase() === 'started';
                    let isPending = row?.status?.toLowerCase() === 'pending';

                    let statusIcon = (!isPending && row?.status) ? `<i id='MonitorStatus' class="${isStarted ? 'text-danger cp-Stopped' : 'text-success cp-circle-play'}"  data-moniter-name="${row.infraObjectName}"  title="${isStarted ? 'Stop' : 'Start'}"   data-title="${isStarted ? 'Stop' : 'Start'}" data-status="${row.status}" data-moniter-state="${isStarted ? 'Stopped' : 'Started'}" data-moniter-id="${row.id}"></i>`
                        : '';
                    let editBtn = isEditAllowed
                        ? `<span role="button" title="Edit" class="btnMSEdit ${isStarted ? 'form-delete-disable' : ''}" data-moniter-service='${JSON.stringify(row)}'><i class="cp-edit"></i></span>`
                        : `<span role="button" title="Edit" class="btn-disabled"><i class="cp-edit"></i></span>`;
                    let deleteBtn = isDeleteAllowed
                        ? `<span role="button" title="Delete" class="btnMSDelete ${isStarted ? 'form-delete-disable' : ''}" data-moniter-id="${row.id}" data-moniter-name="${row.infraObjectName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i></span>`
                        : `<span role="button" title="Delete" class="btn-disabled"><i class="cp-Delete"></i></span>`;

                    return `<div class="d-flex align-items-center gap-2">${editBtn}${deleteBtn}${statusIcon}</div>`;
                }
            }
        ], "rowCallback": function (row, data, index) {
            let api = this.api();
            let startIndex = api.context[0]._iDisplayStart;
            let counter = startIndex + index + 1;
            $('td:eq(0)', row).html(counter);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
            $('th.Sr_No').removeClass('sorting_asc');
        }
    });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
        $('th.Sr_No').removeClass('sorting_asc');
    });

    document.getElementById('msSearch')?.addEventListener('keypress', preventEnterKey);

    $('#msSearch').on('input', commonDebounce(function (e) {
        let inputValue = $('#msSearch').val();
        let checkboxes = [
            $('#filterBusinessServiceName'),
            $('#filterInfraObjectName'),
            $('#filterServerName')
        ];
        selectedValues = checkboxes
            .filter(checkbox => checkbox.is(':checked'))
            .map(checkbox => checkbox.val() + inputValue);
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e?.target?.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false);
        }
    }, 500));

    const MonitoringService = async () => {
        await $.ajax({
            type: "POST",
            url: RootUrl + monitorURL.CheckWindowServiceurl,
            data: { type: 'monitor', __RequestVerificationToken: gettoken() },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (result && result.success) {
                        let html =  MonitoringMessage(result)
                        notificationAlert("success", html, 'execution')
                    } else {
                        notificationAlert("warning", response.message);
                    }

                } else {
                    errorNotification(result)
                }
            }
        })
    }

    const MonitoringMessage = (result) => {
        let html = ''
        if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
            for (let i = 0; i < result?.activeNodes?.length; i++) {
                html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
            }
        }
        if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
            for (let i = 0; i < result?.inActiveNodes?.length; i++) {
                html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
            }
        }
        return html;
    }

    $(async function () {      
        await MonitoringService()
    })
   
    async function populateMSFields(monitorData) {
         $('#msBusinessService').val(monitorData.businessServiceName);
         await getInfraObjectsByBusinessServiceId(monitorData.businessServiceId).then(() => {
             $('#msInfraObject').val(monitorData.infraObjectName).trigger('change');
             setTimeout(() => {
                 if (monitorData.monitoringType) {
                     $('#msMonitoringType').show();
                     const radio = document.querySelector(`.msserverradio[value="${monitorData.monitoringType}"]`);
                     if (radio) {
                         radio.checked = true;
                     }
                 }
             }, 1000); 
         })    
     
            const properties = JSON.parse(monitorData.properties);
            if (monitorData.monitoringType) {              
                await getServersByInfraObjectId(monitorData.infraObjectId, monitorData.monitoringType).then(() => {
                    $('#msServer').val(monitorData.serverName);
                });
                await getServerData(monitorData.serverId).then(() => {
                    $('#msAuthenticationType').val(monitorData.type).trigger('change');
                })
            } else {
                $('#msMonitoringType').hide();
                $('#msServer').val(monitorData.serverName);
                await getServerData(monitorData.serverId).then(() => {
                    $('#msAuthenticationType').val(monitorData.type).trigger('change');
                })
            }

            msId = monitorData.id;

            $('.msradio[value="' + properties.SubType + '"]').prop("checked", true);
            const WorkflowType = properties.details?.[0]?.type || '';
            $('#msWorkflowType').val(WorkflowType);
            properties.details.forEach(item => {
                addPaths(item.name, item.id, item.type);
            });
            msServiceStatus = monitorData.isServiceUpdate ?? '';
            msStatus = monitorData.status ?? '';
            // getServerData(monitorData.serverId, monitorData.type);

            let isWorkflow = monitorData.type === "Use Workflow";
            $('#workflowType, #workflow').toggle(isWorkflow);
            $('#command').toggle(!isWorkflow);
            if (isWorkflow) {
                getWorkflowsByType(monitorData.infraObjectId, WorkflowType, monitorData.workflowId, monitorData.workflowName);
            } else {
                let isService = $('.msradio:checked').val() === "Service";
                $('#msServiceDiv').toggle(isService);
                $('#msProcessDiv').toggle(!isService);
                $(isService ? '#msServiceName' : '#msProcessName').val(monitorData.servicePath);
            }           
    }

    async function getInfraObjectsByBusinessServiceId(businessServiceId) {
        let infraDropdown = $('#msInfraObject').empty();
        let url = `${RootUrl}${monitorURL.GetInfraObjectsByBusinessServiceId}`;
        let data = { businessServiceId };
        let result = await getAysncWithHandler(url, data, OnError);
        infraDropdown.append('<option value="">Select InfraObject</option>');
        result?.forEach(({ id, name }) => {
            if (id && name) {
                infraDropdown.append(`<option data-infraid="${id}" value="${name}">${name}</option>`);
            }
        });
    };
    async function getServers(infraObjectId) {
        let url = `${RootUrl}${monitorURL.GetServersByInfraObjectId}`;
        let data = { infraObjectId };
        return result = await getAysncWithHandler(url, data, OnError);
    }

    async function getServersByInfraObjectId(infraObjectId, ServerType) {
        let serverDropdown = $('#msServer').empty();
        $('#msAuthenticationType').empty();      
        let result = await getServers(infraObjectId);
        serverDropdown.append('<option value="">Select Server</option>');
        let servers = JSON?.parse(result?.serverProperties || '{}');
        let totalServerIds = 0;
        if (servers && typeof servers === 'object' && Object.keys(servers).length > 0) {
            let typesToBind = [];
            if (ServerType === 'All') {
                typesToBind = Object.keys(servers);
            } else if (servers[ServerType]) {
                typesToBind = [ServerType];
            }
            typesToBind.forEach(type => {
                let { id, name } = servers[type];
                let ids = id?.split(',') || [];
                let names = name?.split(',') || [];
                totalServerIds += ids.length;               
                ids.forEach((serverId, i) => {                 
                    let serverName = names[i] || names[names.length - 1];      
                    //allServerIds.push({ serverId: serverId, serverName: serverName }); deadcode
                    serverDropdown.append(
                        `<option data-serverid="${serverId}" value="${serverName}">${serverName}</option>`
                    );
                });
            });
        
            let totalOptions = serverDropdown.find('option').length;
            if (totalOptions <= 2) {
                serverDropdown.prop('selectedIndex', 1);
                serverDropdown.prop('disabled', true);
                serverDropdown.trigger('change');
            } else {
                serverDropdown.prop('disabled', false);
            }
        }
        if (ServerType === "All") {
            serverDropdown.prop('selectedIndex', 1);
            serverDropdown.prop('disabled', true);
            serverDropdown.trigger('change');
            $('#MSServerDiv').hide();
        }

        //if (ServerType === "All" && totalServerIds > 2) {
        //    $('#MSServerDiv').show();
        //}
        //else if (ServerType === "All" && totalServerIds <= 2) {
        //    serverDropdown.prop('selectedIndex', 1);
        //    serverDropdown.prop('disabled', true);
        //    serverDropdown.trigger('change');
        //    $('#MSServerDiv').hide();
        //}
    }

    async function msCreateOrUpdate(commonData) {
        await $.ajax({
            url: RootUrl + monitorURL.CreateOrUpdate,
            type: "POST",
            dataType: "json",
            data: commonData,
            success: function (result) {
                if (result?.success) {
                    notificationAlert("success", result.data);
                    $('#CreateModal').modal('hide');
                    setTimeout(() => dataTable.ajax.reload(), 200);
                } else {
                    errorNotification(result);
                }
            }
        });
    };
    async function getServerData (id) {
        let authDropdown = $('#msAuthenticationType');      
        let serverId = id?.includes(',') ? id.split(',')[0] : id;
        let data = { serverId };
        let url = RootUrl + monitorURL.GetServerDataByServerId;
        await $.ajax({
            type: "GET",
            url: url,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result && typeof result === 'object') {
                    let authOptions = {
                        Linux: ["Use ps -ef", "Use Workflow"],
                        Windows: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell"],
                        AS400: ["Use WMI", "Use Workflow", "Use SSH", "Use PowerShell", "Use Telnet"],
                        default: ["Use ps -ef", "Use Workflow"]
                    };
                    let options = authOptions[result?.osType] || authOptions.default;
                    authDropdown.empty();
                    authDropdown.append(new Option("Select Server Authentication Type", ""));
                    options.forEach(option => authDropdown.append(new Option(option, option)));
                   
                } else {
                    authDropdown.append(new Option("Authentication type Not Found", ""));
                }
            }
        })
    };

    async function getWorkflowsByType(infraId, actionType, selectedId = '', selectedName = '') {
        let url = RootUrl + monitorURL.getWorkflowByType;
        let data = { infraId, actionType };
        let result = await getAysncWithHandler(url, data, OnError);
        let $Workflow = $('#msWorkflow').empty().append('<option value="">Select Workflow Name</option>');
        result?.forEach(({ workflowId, workflowName }) => {
            if (workflowId && workflowName) {
                $Workflow.append(`<option value="${workflowId}">${workflowName}</option>`);
            }
        });
        if (selectedId?.length > 1) {
            $Workflow.val(selectedId);
        }
    }

    //Validations-functions
    async function isNameExist(url, inputValue, fieldName) {
        if (!inputValue || !inputValue[fieldName] || !inputValue[fieldName].trim()) {
            return true;
        }
        return (await getAysncWithHandler(url, inputValue, OnError)) ? " Name already exists" : true;
    }

    async function pathNameValidate(value, id = null, infraObjectId, type, serverId, threadType, urlPath, errorMsg, errorElement) {
        let isValid = !!value;
        errorElement.text(isValid ? '' : errorMsg).toggleClass('field-validation-error', !isValid);
        return isValid;       
    }

    async function monitoringServiceValidate(value, errorMessage, errorElement) {
        let isValid = !!value;
        errorElement.text(isValid ? '' : errorMessage).toggleClass('field-validation-error', !isValid);
        return isValid;
    }

    async function workflowNameValidate(value, id = null, infraId, type, serverId, workflowType, workflowId, urlPath, errorMessage, errorElement) {
        let url = RootUrl + urlPath;
        let data = { workflowId: value, id, infraObjectId: infraId, type, serverId, workflowType, workflowName: workflowId };
        if (!value) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        let validationResult = await isNameExist(url, data, "workflowId", OnError);
        return CommonValidation(errorElement, [validationResult]);
    }

    async function monitorServiceValidateFields() {
        let errorElements = ['#BusinessServiceId-error', '#InfraObjectId-error', '#ServerId-error', '#Type-error', '#WorkflowType-error', '#WorkflowId-error', '#ServicePath-error', '#ThreadType-error', '#ProcessPath-error', '#MonitoringType-error'];
        clearInputFields('CreateForm', errorElements);
    }

    function addPaths(data, Id, Type) {
        if (data?.trim()) {
            const trimmedData = data.trim();
            const alreadyExists = addedname.some(item => item.name === trimmedData);
            if (!alreadyExists) {
                addedname.push({ name: trimmedData, id: Id, type: Type });
                $("#msAddedName").show();
                updateSelectedPaths();
            }
        }
    }

    function updateSelectedPaths() {
        $("#selectedPathDetails").empty();
        if (addedname?.length === 0) {
            $("#msAddedName").hide();
        }
        addedname?.forEach((versionObj, index) => {
            const versionElement = document.createElement("span");
            versionElement.style.marginRight = "10px";

            const addValue = document.createElement("button");
            addValue.innerHTML = `${versionObj.name}<span >&nbsp; X</span>`;
            addValue.className = `remove-button btn rounded-pill btn-sm shadow mt-2`;
            addValue.title = "Remove";
            addValue.setAttribute("data-id", versionObj.id);
            addValue.style.backgroundColor = "#95c9ff";
            addValue.setAttribute("data-type", versionObj.type);
            addValue.addEventListener("click", function () {
                removePaths(index);
            });
            versionElement.appendChild(addValue);
            $("#selectedPathDetails").append(versionElement);
        });
    }

    async function getSelectedPathDetails() {
        const values = [];
        $('#selectedPathDetails button').each(function () {
            let name = $(this).clone().children().remove().end().text().trim();;
            values.push({ name });
        });
        return values;
    }
    async function getSelectedWorkflowDetails() {
        const values = [];
        $('#selectedPathDetails button').each(function () {
            let name = $(this).clone().children().remove().end().text().trim();
            const id = $(this).data('id');
            const type = $(this).data('type');
            values.push({ id, name, type });
        });
        return values;
    }
    function removePaths(index) {
        addedname.splice(index, 1);
        updateSelectedPaths();
    }

    $('#btnWorkflowSaveProfile').on('click', function () {
        let WorkflowName = $("#msWorkflow option:selected").text();
        let WorkflowId = $("#msWorkflow").val();
        let WorkflowType = $("#msWorkflowType").val();
        addPaths(WorkflowName, WorkflowId, WorkflowType);
        $('#msWorkflow').val('').trigger('change');
        $('#msWorkflowAdded').hide();
    });

    $('#btnProcessSaveProfile').on('click', function () {
        let ProcessName = $('#msProcessName').val();
        let threadType = $('.msradio:checked').val();
        addPaths(ProcessName, "", threadType);
        $('#msProcessName').val('');
        $('#msProcessAdded').hide();

    });
    $('#btnServiceSaveProfile').on('click', function () {
        let ServiceName = $('#msServiceName').val();
        let threadType = $('.msradio:checked').val();
        addPaths(ServiceName, "", threadType);
        $('#msServiceName').val('');
        $('#msServiceAdded').hide();

    });

    $('#btnMonitoringServiceCreate').on('click', function () {
        monitorServiceValidateFields();
        msId = "";
        $('#msServiceName,#msProcessName').val('');
        ['#msBusinessService', '#msInfraObject', '#msServer', '#msAuthenticationType', '#msWorkflowType', '#msWorkflow'].forEach(selector => $(selector).prop('selectedIndex', 0));
        $('#btnMSSave').text('Save');
        $('#msInfraObject,#msServer,#msWorkflow,#msAuthenticationType,#selectedPathDetails').empty().prop('disabled', false);
        $('.msradio').prop("checked", false);
        $('#msMonitoringType,#msServiceDiv,#msProcessDiv,#workflowType,#workflow,#command,#msProcessAdded,#msServiceAdded,#msWorkflowAdded,#msAddedName').hide();
    });

    $('.msradio').on('click', function () {
        let selectedValue = document.querySelector('.msradio:checked').value;
        let targetBox = $("." + selectedValue);
        $(".box").not(targetBox).hide();
        $(targetBox).show();
        addedname = [];
        $('#selectedPathDetails').empty();
        $('#msAddedName').hide();
        if (selectedValue) {
            $('#msProcessName,#msServiceName').val('');
            $("#ProcessPath-error,#ServicePath-error,#ThreadType-error").text("").removeClass('field-validation-error');
        }        
    });

    $('#tblMoniterService').on('click', '#MonitorStatus', function () {
        let $this = $(this);
        let monitorId = $this.data('moniter-id');
        let status = $this.data('status');
        let textStatus = $this.data('title').toLowerCase();
        let monitorName = $this.data('moniter-name');
        let newStatus = status === 'Stopped' ? 'Started' : 'Stopped';
        actionData = { id: monitorId, status: newStatus, isServiceUpdate: newStatus };
        $('#statusData').text(textStatus);
        $('#monitorData').text(monitorName);
        $('#msControlModal').modal('show');
    });

    $("#btnMSControl").on('click', function () {
        $('#btnMSControl').prop('disabled', true);
        $('#MSLoader').removeClass('d-none').show();
        $.ajax({
            type: "POST",
            url: RootUrl + monitorURL.UpdateMonitorStatus,
            data: actionData,
            traditional: true,
            headers: { 'RequestVerificationToken': gettoken() },
            dataType: 'json',
            success: function (result) {
                let dt = $('#tblMoniterService').DataTable();

                if (result?.data?.success) {
                    let page = dt.page();
                    dt.ajax.reload(() => dt.page(page).draw(false), false);
                    notificationAlert("success", result.data.message);
                } else {
                    $('.btnMSEdit, .btnMSDelete, .text-danger.cp-Stopped, .text-success.cp-circle-play')
                        .addClass('icon-disabled').prop('disabled', true)
                        .removeAttr('data-bs-toggle data-bs-target');
                    errorNotification(result);
                }
                $('#MSLoader').addClass('d-none').hide();
                $('#msControlModal').modal('hide');
                $("#btnMSControl").prop("disabled", false);
            }
        });
    });

    $('#msWorkflowType').on('change', function () {
        let type = $(this).val();
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let errorElement = $('#WorkflowType-error');
        monitoringServiceValidate(type, 'Select workflow type', errorElement);
        if (infraId && type) {
            getWorkflowsByType(infraId, type);
        }
    });

    $('#msListOperationService').on('change', function () {
        $('#msListInfra').empty()
        msInfra = [];
        $('#msListInfra').append('<option value="All">All</option>');
        datas.forEach(function (value) {
            if ($('#msListOperationService').find(':selected').val() === value.businessServiceId && !msInfra.some(item => item.infraObjectId === value.infraObjectId)) {
                let bsValue = value.businessServiceName
                let infraValue = value.infraObjectName
                let infraId = value.infraObjectId
                $('#msListInfra').append('<option value="' + value.infraObjectId + '">' + value.infraObjectName + '</option>');
                msInfra.push({ infraObjectId: infraId, infraObjectName: infraValue })
                selectedValues.push(`businessServiceName=${bsValue}`)

            }
        });
        dataTable.ajax.reload()
    })

    $("#msListInfra").on("change", function () {
        let selectedInfraId = $(this).val();
        if (selectedInfraId !== 'All') {
            let selectedInfra = msInfra.find(item => item.infraObjectId === selectedInfraId);
            if (selectedInfra) {
                selectedValues.push(`infraObjectName=${selectedInfra.infraObjectName}`);
            }
        } else {
            msInfra.forEach(({ infraObjectName }) => {
                selectedValues.push(`infraObjectName=${infraObjectName}`);
            });
        }
        dataTable.ajax.reload();
    });
    $('#msInfraObject').on('change', async function () {
        const value = $(this).val();
        const infraID = $(this).find('option:selected').data('infraid');
        const errorElement = $('#InfraObjectId-error');
        $('#msServer').empty();
        $('#workflowType, #workflow, #command').hide();
        $('#msServiceName, #msWorkflowType, #msWorkflow').val('');
        $('.msserverradio').prop('checked', false);
        $('#msServer').prop('disabled', false);
        const servers = await getServers(infraID);
        const infraServers = JSON?.parse(servers?.serverProperties || '{}');

        let serverCount = 0;
        const keys = Object.keys(infraServers);
        keys.forEach(key => {
            const ids = infraServers[key].id?.split(',') || [];
            serverCount += ids.length;
        });
        const msMonitoringType = $('#msMonitoringType .row').empty(); // clear existing radios

        if (keys.length > 1) {
            keys.forEach((key, idx) => {
                const radio = `
                <div class="col-auto col">
                    <div class="form-check">
                        <input name="MonitoringType" type="radio" class="form-check-input msserverradio" value="${key}" id="radio-${key}">
                        <label class="form-check-label" for="radio-${key}">${key}</label>
                    </div>
                </div>
            `;
                msMonitoringType.append(radio);
            });
            msMonitoringType.append(`
            <div class="col-auto col">
                <div class="form-check">
                    <input name="MonitoringType" type="radio" class="form-check-input msserverradio" value="All" id="radio-All">
                    <label class="form-check-label" for="radio-All">All</label>
                </div>
            </div>
        `);

            $('#msMonitoringType').show();
        } else {
            $('#msMonitoringType').hide();
            const serverKey = keys[0] || '';
            await getServersByInfraObjectId(infraID, serverKey);
        }
        monitoringServiceValidate(value, 'Select infraobject', errorElement);
    });

    $(document).on('click', '.msserverradio', async function () {
        let ServerType = document.querySelector('.msserverradio:checked').value;
        let id = $('#msInfraObject').find('option:selected').data('infraid');
        let errorElement = $('#MonitoringType-error');
        $('#msServer').prop('disabled', false);
        $('#MSServerDiv').show();
        $('#msAuthenticationType').empty();
        await getServersByInfraObjectId(id, ServerType);
        monitoringServiceValidate(ServerType, 'Select type', errorElement);
        $('#msAddedName').hide(); 
    });

    $('#msServer').on('change', function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').data('serverid');
        let errorElement = $('#ServerId-error');
        $('#workflowType,#workflow,#command').hide();
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('');
        getServerData(id)
        monitoringServiceValidate(value, 'Select server', errorElement);
    });

    $('#msAuthenticationType').on('change', function () {
        let value = $(this).val();
        let isWorkflow = value === 'Use Workflow';
        let isCommandBased = ['Use ps -ef', 'Use WMI', 'Use SSH', 'Use PowerShell', 'Use Telnet'].includes(value);
        $('#workflowType, #workflow').toggle(isWorkflow);
        $('#command').toggle(isCommandBased);
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('').trigger('change');
        $('#selectedPathDetails').val('');
        addedname = [];
        let errorIds = [
            '#WorkflowType-error',
            '#WorkflowId-error',
            '#ThreadType-error',
            '#ServicePath-error'
        ];
        errorIds.forEach(id => $(id).text('').removeClass('field-validation-error'));
        $('#msServiceDiv, #msProcessDiv,#msAddedName').hide();
        $('.msradio').prop("checked", false);
        $('#ProcessPath-error').val('').trigger('change');

        if (!$('#msWorkflowType').val()) {
            $('#msWorkflowType').append('<option value="" disabled selected>Select Workflow Type</option>');
        }
        if (!$('#msWorkflow').val()) {
            $('#msWorkflow').append('<option value="" disabled selected>Select Workflow Name</option>');
        }
        let errorElement = $('#Type-error');
        monitoringServiceValidate(value, 'Select server authentication type', errorElement);

    });

    $('#msWorkflow').on('change', async function () {
        let workflowId = $("#msWorkflow").val();
        let workflowName = $("#msWorkflow option:selected").text();
        let workflowType = $('#msWorkflowType').val();
        let monitorId = msId;
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text();
        let errorElement = $('#WorkflowId-error');
        if (addedname?.length == 0) {
            await workflowNameValidate(workflowId, monitorId, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errorElement);
        }

        if (workflowId) { $('#msWorkflowAdded').show(); } else { $('#msWorkflowAdded').hide(); }
    });

    $("#btnMSSave").on('click', async function () {
        let businessServiceId = $("#msBusinessService option:selected").attr("id");
        let businessServiceName = $("#msBusinessService").val();
        let infraId = $("#msInfraObject option:selected").data("infraid");
        let infraName = $("#msInfraObject").val();
        let addedDetails = "";
        let monitoringType = "";

        if ($('.msserverradio:checked').length > 0) {
            monitoringType = $('.msserverradio:checked').val();
        } else {
            monitoringType = 'All'
        }
        //let serverId = "";   //dead code
        //if (monitoringType == "All") {
        //    serverId = allServerIds;
        //} else {
        //    serverId = $("#msServer option:selected").data("serverid");
        //}  
        let serverId = $("#msServer option:selected").data("serverid");
        let serverName = $("#msServer").val();
        let type = $("#msAuthenticationType").val();
        let id = msId;
        let threadType = $('.msradio:checked').val();
        let subType = (type === "Use Workflow") ? "Workflow" : threadType;
        let isMonitoringType = true;
        let workflowType = $("#msWorkflowType").val();
        let workflow = $("#msWorkflow").val();
        let workflowName = workflow ? $("#msWorkflow option:selected").text() : null;
        let servicePath = threadType === 'Service' ? $('#msServiceName').val() : threadType === 'Process' ? $('#msProcessName').val() : '';
        let errors = {
            Bsservice: $('#BusinessServiceId-error'),
            Infra: $('#InfraObjectId-error'),
            Server: $('#ServerId-error'),
            Type: $('#Type-error'),
            Workflowtype: $('#WorkflowType-error'),
            Workflow: $('#WorkflowId-error'),
            Servicepath: $('#ServicePath-error'),
            Processpath: $('#ProcessPath-error'),
            Threadtype: $('#ThreadType-error'),
            MonitoringType: $('#MonitoringType-error')
        };
        let isBsService = await monitoringServiceValidate(businessServiceName, 'Select operational service', errors.Bsservice);
        let isInfra = await monitoringServiceValidate(infraName, 'Select infraobject', errors.Infra);
            
        let isServer = await monitoringServiceValidate(serverName, 'Select server', errors.Server);
        if (isInfra && monitoringType == "" && !isServer) {
            isMonitoringType = await monitoringServiceValidate(monitoringType, 'Select type', errors.MonitoringType);
        }  
        let isType =await monitoringServiceValidate(type, 'Select server authentication type', errors.Type);
        let isWorkflowType = await monitoringServiceValidate(workflowType, 'Select workflow type', errors.Workflowtype);
        let isThreadType = await monitoringServiceValidate(threadType, 'Select any one', errors.Threadtype);
        let isServicePath = false;
       
        if (type == "Use Workflow") {
            let Workflow = await getSelectedWorkflowDetails();
            addedDetails = Workflow;
            if (workflowName && Workflow.length > 0) {
                isWorkflow = await monitoringServiceValidate("", 'Add workflow name', errors.Workflow)
            }
            else if (!workflowName && Workflow.length > 0) {
                const names = Workflow.map(item => item.name);
                workflowName = names.join(', ');
                const values = workflowName.includes(',') ? workflowName.split(',').map(v => v.trim()).filter(v => v) : [workflowName];
                for (let val of values) {
                    isWorkflow = await workflowNameValidate(val, id, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errors.Workflow);
                }
            }
            else {
                addedDetails = [{ name: workflowName, id: workflow, type: workflowType }]; 
                isWorkflow = await workflowNameValidate(workflowName, id, infraId, type, serverId, workflowType, workflowName, monitorURL.nameExistUrl, 'Select workflow name', errors.Workflow);
            }
        } else {
            let servicePaths = await getSelectedPathDetails();
            addedDetails = servicePaths;
            if (servicePath && servicePaths.length > 0) {
               isServicePath = await monitoringServiceValidate("", 'Add service name', errors.Servicepath)
            }
            else if (!servicePath && servicePaths.length > 0) {
                const names = servicePaths.map(item => item.name);
                servicePath = names.join(', ');
                const values = servicePath.includes(',') ? servicePath.split(',').map(v => v.trim()).filter(v => v) : [servicePath];
                for (let val of values) {
                    isServicePath = await pathNameValidate(val, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errors.Servicepath);
                }
            } else {
                 addedDetails = [{ name: servicePath }];                
                if (threadType === 'Service') {
                    isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errors.Servicepath);
                } else if (threadType === 'Process') {
                    isServicePath = await pathNameValidate(servicePath, id, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errors.Processpath);
                }
            }
        }
        let TypeValue = type === "Use Workflow";
        let isCommandType = ["Use ps -ef", "Use SSH", "Use WMI", "Use PowerShell", "Use Telnet"].includes(type);
        let commonData = {
            BusinessServiceId: businessServiceId,
            BusinessServiceName: businessServiceName,
            InfraObjectId: infraId,
            InfraObjectName: infraName,
            MonitoringType: monitoringType,
            ServerId: serverId,
            ServerName: serverName,
            Type: type,
            Id: id,
            IsServiceUpdate: msServiceStatus,
            Status: msStatus,
            Properties: JSON.stringify({
                SubType: subType,
                details: addedDetails,
            }),
            __RequestVerificationToken: gettoken()
        };

        
        if (TypeValue) {
            if (isBsService && isInfra && isServer && isType && isWorkflowType && isMonitoringType && isWorkflow) {
                msCreateOrUpdate(commonData);
            }
        } else if (isCommandType) {
            if (isBsService && isInfra && isServer && isType && isServicePath && isMonitoringType && isThreadType) {
                msCreateOrUpdate(commonData);
            }
        }
    });

    $('#msBusinessService').on('change', function () {
        let value = $(this).val();
        let id = $(this).find('option:selected').attr('id');
        let errorElement = $('#BusinessServiceId-error');
        monitoringServiceValidate(value, 'Select operational service', errorElement);
        $('#msServer, #msAuthenticationType').empty().prop('disabled', false);
        $('#workflowType, #workflow,#command,#msMonitoringType,#msAddedName').hide();
        $('#MSServerDiv').show();
        $('#msServiceName,#msWorkflowType, #msWorkflow').val('');
        $('.msserverradio').prop("checked", false);
        getInfraObjectsByBusinessServiceId(id);
    });

    $('#msServiceName').on('input', commonDebounce(async function () {
      let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/ {2,}/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text()
        let threadType = $('.msradio:checked').val();
        let monitorId = msId;
        let errorElementPath = $('#ServicePath-error');
        let validation = await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter service name', errorElementPath);
        if (validation) { $('#msServiceAdded').show() } else { $('#msServiceAdded').hide() }
    }, 400));

    $('#msProcessName').on('input', commonDebounce(async function () {
        let value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        let infraId = $('#msInfraObject option:selected').data('infraid');
        let serverId = $('#msServer option:selected').data('serverid');
        let type = $('#msAuthenticationType option:selected').text()
        let threadType = $('.msradio:checked').val();
        let monitorId = msId;
        let errorElementPath = $('#ProcessPath-error');
        let validation = await pathNameValidate(value, monitorId, infraId, type, serverId, threadType, monitorURL.nameExistUrl, 'Enter process name', errorElementPath);
        if (validation) { $('#msProcessAdded').show() } else { $('#msProcessAdded').hide() }
    }, 400));

    $('#tblMoniterService').on('click', '.btnMSEdit', async function () {
        let monitorData = $(this).data('moniterService');
        addedname = [];
        $('#selectedPathDetails').empty();
        $('#msServer').prop('disabled', false);
        $('#MSServerDiv').show();
        $('#msWorkflowAdded,#msProcessAdded,#msServiceAdded').hide();
        monitorServiceValidateFields();
        await populateMSFields(monitorData);
        $('#btnMSSave').text('Update');
        $('#CreateModal').modal('show');
    });

    $('#tblMoniterService').on('click', '.btnMSDelete', function () {
        let monitorId = $(this).data('moniter-id');
        let monitorName = $(this).data('moniter-name');
        $('#deleteData').text(monitorName);
        msId = monitorId;
    });
    
    $('#btnMSConfirmDelete').on('click', async function () {
        let monitorId = msId;       
        await $.ajax({
            url: RootUrl + monitorURL.Delete,
            type: "POST",
            dataType: "json",
            data: { id: monitorId },
            success: function (result) {            
                if (result?.success) {
                    notificationAlert("success", result.data);
                    $('#DeleteModal').modal('hide');
                    setTimeout(() => dataTable.ajax.reload(), 200);
                } else {
                    errorNotification(result);
                }
            }      
        });
    });
});