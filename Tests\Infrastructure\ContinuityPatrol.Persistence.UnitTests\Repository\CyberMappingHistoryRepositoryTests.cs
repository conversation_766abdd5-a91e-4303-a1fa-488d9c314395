using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberMappingHistoryRepositoryTests : IClassFixture<CyberMappingHistoryFixture>
{
    private readonly CyberMappingHistoryFixture _cyberMappingHistoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberMappingHistoryRepository _repository;

    public CyberMappingHistoryRepositoryTests(CyberMappingHistoryFixture cyberMappingHistoryFixture)
    {
        _cyberMappingHistoryFixture = cyberMappingHistoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberMappingHistoryRepository(_dbContext);
    }

    #region GetCyberComponentMappingHistoryById Tests

    [Fact]
    public async Task GetCyberComponentMappingHistoryById_ShouldReturnHistoryForMapping()
    {
        // Arrange
      
        var histories = _cyberMappingHistoryFixture.CyberMappingHistoryList;

        foreach (var item in histories)
        {
            item.CyberComponentMappingId = "21a633c6-c208-4d3d-aec4-9f60f96b1453";
        }
        await _repository.AddRangeAsync(histories);

        // Act
        var result = await _repository.GetCyberComponentMappingHistoryById("21a633c6-c208-4d3d-aec4-9f60f96b1453");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("21a633c6-c208-4d3d-aec4-9f60f96b1453", x.CyberComponentMappingId));
        // Should be ordered by Id descending (most recent first)
        Assert.True(result[0].Id > result[1].Id);
    }

    [Fact]
    public async Task GetCyberComponentMappingHistoryById_ShouldReturnEmpty_WhenNoHistoryForMapping()
    {
        // Arrange
        var histories = _cyberMappingHistoryFixture.CyberMappingHistoryList;
        await _repository.AddRangeAsync(histories);

        // Act
        var result = await _repository.GetCyberComponentMappingHistoryById("NON_EXISTENT_MAPPING");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

   

    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleMapping_FindByFilterAsync_Assignments()
    {
        // Arrange
        var histories = _cyberMappingHistoryFixture.CyberMappingHistoryList;
        histories[0].CyberComponentMappingId = "4871fd85-1a51-4558-b770-9099400c70f6";
        histories[0].CyberComponentMappingName = "DB_001";
        histories[0].UserName = "Aluva";

        await _repository.AddRangeAsync(histories);

        // Act
        var cyberMappings = await _repository.FindByFilterAsync(x => x.CyberComponentMappingId.Contains("4871fd85-1a51-4558-b770-9099400c70f6"));
        

        // Assert
        Assert.Single(cyberMappings);
     
        Assert.Contains("4871fd85-1a51-4558-b770-9099400c70f6", cyberMappings.First().CyberComponentMappingId);
      
        Assert.Contains("DB_001", cyberMappings.First().CyberComponentMappingName);
      
        Assert.Equal("Aluva", cyberMappings.First().UserName);
       
    }

    #endregion
   
}
