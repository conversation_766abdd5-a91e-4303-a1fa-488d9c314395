const requestURL = {
    getRequestPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedRequestList",
    getApprovalPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedApprovalList",
    withdrawRequest: "Manage/ApprovalMatrix/WithdrawRequest",
    approveReject: "Manage/ApprovalMatrix/ApproveReject",
    getApprovalMatrixByRequestId: "Manage/ApprovalMatrix/GetApprovalMatrixByRequestId",
}

let requestDataTable = "";
let approvalDataTable = "";
let selectedValues = [];

const approvalPreventSpecialKeys = (selector) => {
    const blockedKeys = new Set(['=', 'Enter']);
    $(selector).on('keypress', e => blockedKeys.has(e.key) && e.preventDefault());
};

const baseDataTableConfig = {
    language: {
        decimal: ",",
        paginate: {
            next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
            previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
        },
        infoFiltered: ""
    },
    dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
    scrollY: true,
    deferRender: true,
    scroller: true,
    processing: true,
    serverSide: true,
    filter: true,
    Sortable: true,
    order: [],
    fixedColumns: { left: 1, right: 1 },
    rowCallback: function (row, data, index) {
        let startIndex = this.api().context[0]._iDisplayStart;
        $('td:eq(0)', row).html(startIndex + index + 1);
    },
    initComplete: function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    }
};

function commonAjax(url) {
    return {
        type: "GET",
        url,
        dataType: "json",
        data: function (d) {
            const sortIndex = d?.order[0]?.column || '';
            d.PageNumber = Math.ceil(d.start / d.length) + 1;
            d.pageSize = d.length;
            d.searchString = selectedValues?.length === 0
                ? $('#searchInputRequest').val()
                : selectedValues.join(';');
            d.sortColumn = sortIndex === 1 ? "name" : "";
            d.SortOrder = d?.order[0]?.dir || 'asc';
            selectedValues.length = 0;
            reportSearchStr = d.searchString;
        },
        dataSrc: function (json) {
            if (json?.success) {
                const { data } = json;
                json.recordsTotal = data?.totalPages;
                json.recordsFiltered = data?.totalCount;
                $(".pagination-column").toggleClass("disabled", data?.data?.length === 0);
                return url === requestURL.getApprovalPaginatedlist ? data?.data?.reverse() : data?.data;
            } else {
                errorNotification(json);
            }
        }
    };
}

function myRequestLists() {
    requestDataTable = $('#dataTableListsMyRequest').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(requestURL.getRequestPaginatedlist),
        columnDefs: [{ targets: [1, 3], className: "truncate" }],
        columns: [
            {
                data: null, name: "Sr. No.", autoWidth: true, orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                data: "requestId", name: "requestId",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "status", name: "status",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "reason", name: "reason",
                render: (data, type) => {
                    return type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data;
                }
            },
            {
                data: "userName", name: "userName",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                render: function (_, __, row) {
                    const { id, requestId, status } = row;
                    const reqStatus = status?.replace(/\s+/g, '').toLowerCase();
                    const isDisabled = ["withdraw", "rejected", "approved"].includes(reqStatus);
                    return `<div class="d-flex align-items-center gap-2">
                        <span role="button" title="View" id="viewButtonRequest" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#viewModalApprovalReject">
                            <i class="cp-password-visible text-primary fw-semibold me-2"></i> View
                        </span>
                        <span role="button" title="Withdraw" id="requestWithdrawButton" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#approvalModalWithdraw"
                              data-withdraw-id="${id}" data-withdraw-request="${requestId}" data-withdraw-status="Withdraw"
                              style="${isDisabled ? 'pointer-events: none; opacity: 0.5;' : ''}">
                            <i class="cp-undo text-warning fw-semibold me-2"></i> Withdraw
                        </span>
                    </div>`;
                }, orderable: false
            }
        ]
    });
}

function myApprovalLists() {
    approvalDataTable = $('#dataTableListsMyApproval').DataTable({
        ...baseDataTableConfig,
        ajax: commonAjax(requestURL.getApprovalPaginatedlist),
        columns: [
            {
                data: null, name: "Sr. No.", autoWidth: true, orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                data: "requestId", name: "requestId",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "description", name: "description",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "status", name: "status",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },           
            {
                data: "userName", name: "CreatedBy",
                render: (data, type) => type === 'display' ? `<span title="${data || 'NA'}">${data || 'NA'}</span>` : data
            },
            {
                data: "createdDate", name: "CreatedOn",
                render: (data, type) => {
                    const d = new Date(data);
                    const pad = (n) => n.toString().padStart(2, '0');
                    const formatted = `${pad(d.getDate())}-${pad(d.getMonth() + 1)}-${d.getFullYear()} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
                    return type === 'display' ? `<span title="${formatted}">${formatted}</span>` : formatted;
                }
            },
            {
                render: function (_, __, row) {
                    const { id, processName, status, approverName, requestId } = row;
                    const statusClean = status?.replace(/\s+/g, '').toLowerCase();
                    const isAllowed = statusClean === "waitingforapproval"; 

                    const style = isAllowed ? "" : 'pointer-events: none; opacity: 0.5;';
                    return `<div class="d-flex align-items-center gap-2">
                        <span role="button" data-bs-toggle="modal" data-bs-target="#approvalModalRequest"
                              title="Approve" id="buttonApprovalAM" style="${style}"
                              data-approval-id="${id}" data-approval-procname="${processName}" data-approval-status="Approved">
                            <i class="cp-success text-success fw-semibold me-2"></i> Approve
                        </span>
                        <span role="button" data-bs-toggle="modal" data-bs-target="#rejectModalRequest"
                              title="Reject" id="buttonRejectAM" style="${style}"
                              data-approval-id="${id}" data-approval-procname="${processName}" data-approval-status="Rejected">
                            <i class="cp-error text-warning fw-semibold me-2"></i> Reject
                        </span>
                        <span role="button" title="View" id="viewButtonApproval" data-request="${requestId}" data-bs-toggle="modal" data-bs-target="#viewModalApprovalReject">
                            <i class="cp-password-visible text-primary fw-semibold me-2"></i> View
                        </span>
                    </div>`;
                }, orderable: false
            }
        ]
    });
}

function textFieldValidation(value, message, errorelement) {
    if (!value) {
        errorelement.text(message).addClass("field-validation-error");
        return false
    } else {
        errorelement.text("").removeClass("field-validation-error");
        return true
    }
};

async function approvalRejectWithdraw(command, url, approveReject = null) {
    await $.ajax({
        url: RootUrl + url,
        data: command,
        type: "GET",
        dataType: "Json",
        success: function (response) {
            if (response?.success) {
                notificationAlert("success", response?.data?.message);
            } else {
                errorNotification(response);
            }
            requestDataTable.ajax.reload();
            if (approveReject) {
                approvalDataTable.ajax.reload();
            }
        }
    });
}
function formatTo12HourTime(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
}

function buildTimelineItem({ direction, status, process, time, label, description }) {
    return `<li>
              <div class="direction-${direction}">
                  <div class="flag-wrapper">
                      <span class="hexa" data-item-status="${status.toLowerCase()}"></span>
                      ${process ? `<span class="flag">${process}</span>` : ''}
                      <span class="time-wrapper">
                          <span class="time small">${time}</span>
                          <span class="alert alert-${label.color} py-1 px-2 small rounded-pill">${label.text}</span>
                      </span>
                  </div>
                  <div class="desc">${description}</div>
              </div>
          </li>`;
}

async function approvalTimeline(command, url) {
    await $.ajax({
        url: RootUrl + url,
        data: command,
        type: "GET",
        dataType: "json",
        success: function (response) {
            if (response?.success) {
                const $timeline = $("#approvalMatrixTimeline").empty();
                let createdUser = response?.data[0]?.userName;
                let createdDate = "";

                const timelineTemplates = {
                    waitingforapproval: (data, time) =>
                        buildTimelineItem({ direction: 'r', status: 'created', process: data.processName, time, label: { color: 'primary', text: data.status }, description: `Waiting for ${data.approverName} approval` }),
                    withdraw: (data, time) =>
                        buildTimelineItem({direction: 'l',status: 'withdraw', process: data.processName, time, label: { color: 'secondary', text: data.status }, description: `${data.userName} has withdrawn the matrix`}),
                    accepted: (data, time) =>
                        buildTimelineItem({direction: 'r', status: 'approval', process: data.processName, time, label: { color: 'success', text: data.status }, description: `${data.userName} has approved the matrix` }),
                    rejected: (data, time) =>
                        buildTimelineItem({direction: 'l', status: 'rejected', process: data.processName, time, label: { color: 'danger', text: data.status }, description: `${data.userName} has rejected the matrix`}),
                    sent: (names, time) =>
                        buildTimelineItem({ direction: 'r', status: 'created', time, label: { color: 'primary', text: 'Approval' }, description: `Send approval request to ${names}` }),
                    created: (user, time) =>
                        buildTimelineItem({ direction: 'r', status: 'withdraw', time, label: { color: 'secondary', text: 'Created' }, description: `${user} created the matrix` })
                };

                response?.data?.forEach(data => {
                    const status = data?.status?.toLowerCase().replace(/\s+/g, "");
                    const time = formatTo12HourTime(data.lastModifiedDate);
                    const message = data.message || "";

                    if (status in timelineTemplates) {
                        $timeline.append(timelineTemplates[status](data, time));
                    } else if (message.includes("Approval sent to") || message.includes("Approval forwarded to")) {
                        const approverList = message.split(":")[1]?.trim();
                        $timeline.append(timelineTemplates.sent(approverList, time));
                    } else {
                        $timeline.append(timelineTemplates.created(createdUser, time));
                        const splitedDate = message.split("on ")[1]?.replace(".", "");
                        if (splitedDate && !isNaN(new Date(splitedDate))) {
                            dateMatch = new Date(splitedDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' });
                        }

                        if (dateMatch) createdDate = dateMatch;
                    }
                });

                if (createdDate) {
                    $timeline.append(`<li class="text-center"><span class="date-wrapper rounded">${createdDate}</span></li>`);
                }
            } else {
                 errorNotification(response);
            }          
        }
    });
}

$(function () {
    approvalPreventSpecialKeys('#searchInputRequest');
    myRequestLists();
    $("#pills-myrequest-tab").on("click", myRequestLists);
    $("#pills-myapproval-tab").on("click", myApprovalLists);
});

$("#dataTableListsMyApproval").on("click", "#buttonApprovalAM", async function () {   
    $("#approveRequestId").val($(this).data("approval-id"));
    $("#approveProcessName").val($(this).data("approval-procname"));
    $("#approveApprovalStatus").val($(this).data("approval-status"));
    $("#approveRemarkRequestError").text("").removeClass("field-validation-error");
    $("#approveRemarkRequest").val("");
    $("#approvalName").text($(this).data("approval-procname"));
});

$("#dataTableListsMyApproval").on("click", "#buttonRejectAM", async function () {
    $("#rejectRequestId").val($(this).data("approval-id"));
    $("#rejectProcessName").val($(this).data("approval-procname"));
    $("#rejectApprovalStatus").val($(this).data("approval-status"));
    $("#rejectReasonRequestError").text("").removeClass("field-validation-error");
    $("#rejectReasonRequest").val("");
    $("#rejectName").text($(this).data("approval-procname"));
});

$("#approveRemarkRequest").on("input", function () {
    textFieldValidation($(this).val(), "Enter remark", $("#approveRemarkRequestError"))
});

$("#rejectReasonRequest").on("input", function () {
    textFieldValidation($(this).val(), "Enter reason", $("#rejectReasonRequestError"))
});

$("#confirmApproveRequest").on("click", async function () {
    const remarkStatus = textFieldValidation($("#approveRemarkRequest").val(), "Enter remark", $("#approveRemarkRequestError"))

    if (remarkStatus) {
        let command = {
            Id: $("#approveRequestId").val(),
            ProcessName: $("#approveProcessName").val(),
            Status: $("#approveApprovalStatus").val(),
        };
        await approvalRejectWithdraw(command, requestURL.approveReject, true);
        $("#cancelButtonApprove").trigger("click");
    }
})

$("#confirmRejectRequest").on("click", async function () {
    const reasonStatus = textFieldValidation($("#rejectReasonRequest").val(), "Enter reason", $("#rejectReasonRequestError"))

    if (reasonStatus) {
        let command = {
            Id: $("#rejectRequestId").val(),
            ProcessName: $("#rejectProcessName").val(),
            Status: $("#rejectApprovalStatus").val(),
        };
        await approvalRejectWithdraw(command, requestURL.approveReject, true);
        $("#cancelButtonReject").trigger("click");
    }
})

$("#dataTableListsMyRequest").on("click", "#requestWithdrawButton", async function () {
    $("#withdrawRequestId").val($(this).data("withdraw-request"));
    $("#withdrawId").val($(this).data("withdraw-id"));
    $("#withdrawStatus").val($(this).data("withdraw-status"));
    $("#withdrawName").text($(this).data("request"));
});

$("#confirmWithdraw").on("click", async function () {
    let command = {
        Id: $("#withdrawId").val(),
        RequestId: $("#withdrawRequestId").val(),
        Status: $("#withdrawStatus").val(),
    };
    await approvalRejectWithdraw(command, requestURL.withdrawRequest);
    $("#cancelButtonWithdraw").trigger("click");
})

$("#dataTableListsMyApproval").on("click", "#viewButtonApproval", async function () {
    const requestId = $(this).data("request");
    let command = {
        requestId: requestId,
    };
    approvalTimeline(command, requestURL.getApprovalMatrixByRequestId);
});

$("#dataTableListsMyRequest").on("click", "#viewButtonRequest", async function () {
    const requestId = $(this).data("request");
    let command = {
        requestId: requestId,
    };
    approvalTimeline(command, requestURL.getApprovalMatrixByRequestId);
});