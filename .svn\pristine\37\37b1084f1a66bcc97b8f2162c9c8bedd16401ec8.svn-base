﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.MSSQLAlwaysOnMonitorStatus.Queries;

public class GetMssqlAlwaysOnMonitorStatusPaginatedListQueryHandlerTests : IClassFixture<MssqlAlwaysOnMonitorStatusFixture>
{
    private readonly GetMSSQLAlwaysOnMonitorStatusPaginatedListQueryHandler _handler;

    private readonly Mock<IMssqlAlwaysOnMonitorStatusRepository> _mockMssqlAlwaysOnMonitorStatusRepository;

    public GetMssqlAlwaysOnMonitorStatusPaginatedListQueryHandlerTests(MssqlAlwaysOnMonitorStatusFixture mssqlAlwaysOnMonitorStatusFixture)
    {
        var mssqlAlwaysOnMonitorStatusNewFixture = mssqlAlwaysOnMonitorStatusFixture;

        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[0].WorkflowName = "Workflow_State";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[0].InfraObjectName = "InfraObject";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[0].Type = "MSSQL";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[0].Properties = "{\"Name\": \"admin\", \"password\": \"Admin@123\"}";

        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[1].WorkflowName = "Workflow_Category";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[1].InfraObjectName = "Infra_Mapping";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[1].Type = "MYSQL";
        mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses[1].Properties = "{\"Name\": \"operator\", \"password\": \"Common@123\"}";



        _mockMssqlAlwaysOnMonitorStatusRepository = MssqlAlwaysOnMonitorStatusRepositoryMocks.GetPaginatedMssqlAlwaysOnMonitorStatusRepository(mssqlAlwaysOnMonitorStatusNewFixture.MssqlAlwaysOnMonitorStatuses);

        _handler = new GetMSSQLAlwaysOnMonitorStatusPaginatedListQueryHandler(_mockMssqlAlwaysOnMonitorStatusRepository.Object, mssqlAlwaysOnMonitorStatusNewFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Replication_Master" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedTeamResources_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Infra" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].ShouldBeOfType<MSSQLAlwaysOnMonitorStatusListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowName.ShouldBe("Workflow_State");

        result.Data[0].InfraObjectName.ShouldBe("InfraObject");

        result.Data[0].Type.ShouldBe("MSSQL");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_TeamResources_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "workflowname=Workflow_State;infraobjectname=InfraObject;type=MSSQL;properties={\"Name\": \"admin\", \"password\": \"Admin@123\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<MSSQLAlwaysOnMonitorStatusListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowName.ShouldBe("Workflow_State");

        result.Data[0].InfraObjectName.ShouldBe("InfraObject");

        result.Data[0].Type.ShouldBe("MSSQL");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Admin@123\"}");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery(), CancellationToken.None);

        _mockMssqlAlwaysOnMonitorStatusRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}