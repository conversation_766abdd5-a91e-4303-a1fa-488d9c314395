using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormTypeCategoryFixture : IDisposable
{
    public List<FormTypeCategory> FormTypeCategoryPaginationList { get; set; }
    public List<FormTypeCategory> FormTypeCategoryList { get; set; }
    public FormTypeCategory FormTypeCategoryDto { get; set; }

   

    public ApplicationDbContext DbContext { get; private set; }

    public FormTypeCategoryFixture()
    {
        var fixture = new Fixture();

        FormTypeCategoryList = fixture.Create<List<FormTypeCategory>>();

        FormTypeCategoryPaginationList = fixture.CreateMany<FormTypeCategory>(20).ToList();

        FormTypeCategoryPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormTypeCategoryPaginationList.ForEach(x => x.IsActive = true);

        FormTypeCategoryList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormTypeCategoryList.ForEach(x => x.IsActive = true);

        FormTypeCategoryDto = fixture.Create<FormTypeCategory>();
        FormTypeCategoryDto.ReferenceId = Guid.NewGuid().ToString();
        FormTypeCategoryDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
