﻿namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetDriftTreeView;

public class GetDriftTreeListVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }

    public List<DriftBusinessFunctionVm> BusinessFunctions { get; set; } = new();
}

public class DriftBusinessFunctionVm
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }

    public List<DriftInfraObjectVm> InfraObjects { get; set; } = new();
}

public class DriftInfraObjectVm
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string Status { get; set; }
    public bool IsDrift { get; set; }
}