﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IApprovalMatrixRepository : IRepository<ApprovalMatrix>
{
    Task<bool> IsApprovalMatrixNameUnique(string name);
    Task<bool> IsApprovalMatrixNameExist(string name, string id);

    Task<ApprovalMatrix> GetApprovalMatrixByBusinessFunctionId(string businessFunctionId);

    //Task<string> ApproveRequest(string id, string status, string properties, string approver);
    //Task<string> SetCounterForApproval(string id, string counter, string properties, string approver);
    //Task<string> SetCounterForReject(string id, string counter, string properties, string approver);
    //Task<bool> GetByTemplateName(string templateName);
}