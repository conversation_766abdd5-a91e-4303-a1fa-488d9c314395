using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MsSqlNativeLogShippingMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MsSqlNativeLogShippingMonitorStatus";

    public List<MsSqlNativeLogShippingMonitorStatus> MsSqlNativeLogShippingMonitorStatusPaginationList { get; set; }
    public List<MsSqlNativeLogShippingMonitorStatus> MsSqlNativeLogShippingMonitorStatusList { get; set; }
    public MsSqlNativeLogShippingMonitorStatus MsSqlNativeLogShippingMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MsSqlNativeLogShippingMonitorStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MsSqlNativeLogShippingMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
         );

        MsSqlNativeLogShippingMonitorStatusPaginationList = _fixture.CreateMany<MsSqlNativeLogShippingMonitorStatus>(20).ToList();
        MsSqlNativeLogShippingMonitorStatusList = _fixture.CreateMany<MsSqlNativeLogShippingMonitorStatus>(5).ToList();
        MsSqlNativeLogShippingMonitorStatusDto = _fixture.Create<MsSqlNativeLogShippingMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MsSqlNativeLogShippingMonitorStatus CreateMsSqlNativeLogShippingMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MsSqlNativeLogShippingMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MsSqlNativeLogShippingMonitorStatus CreateMsSqlNativeLogShippingMonitorStatusWithWhitespace()
    {
        return CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: "  MsSqlNativeLogShippingMonitorStatus  ");
    }

    public MsSqlNativeLogShippingMonitorStatus CreateMsSqlNativeLogShippingMonitorStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: longType);
    }

    public MsSqlNativeLogShippingMonitorStatus CreateMsSqlNativeLogShippingMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMsSqlNativeLogShippingMonitorStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<MsSqlNativeLogShippingMonitorStatus> CreateMultipleMsSqlNativeLogShippingMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<MsSqlNativeLogShippingMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public List<MsSqlNativeLogShippingMonitorStatus> CreateMsSqlNativeLogShippingMonitorStatusWithMixedActiveStatus(string type)
    {
        return new List<MsSqlNativeLogShippingMonitorStatus>
        {
            CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: type, isActive: true),
            CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: type, isActive: false),
            CreateMsSqlNativeLogShippingMonitorStatusWithProperties(type: type, isActive: true)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MsSqlNativeLogShippingMonitorStatus", "LogShippingStatus", "MSSQL", "NativeLogShippingStatus" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
