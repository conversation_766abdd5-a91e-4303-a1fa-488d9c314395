﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowPrediction.Commands
{
    public class CreateWorkflowPredictionTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowPredictionRepository> _mockWorkflowPredictionRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly CreateWorkflowPredictionCommandHandler _handler;

        public CreateWorkflowPredictionTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowPredictionRepository = new Mock<IWorkflowPredictionRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new CreateWorkflowPredictionCommandHandler(
                _mockMapper.Object,
                _mockWorkflowPredictionRepository.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsCorrectResponse_WhenWorkflowPredictionCreatedSuccessfully()
        {
            var command = new CreateWorkflowPredictionCommand
            {

            };

            var actionId = Guid.NewGuid().ToString();
            var referenceId = Guid.NewGuid().ToString();

            var mappedWorkflowPrediction = new Domain.Entities.WorkflowPrediction
            {
                ActionId = actionId,
                ReferenceId = referenceId
            };
            _mockMapper
                .Setup(m => m.Map<Domain.Entities.WorkflowPrediction>(command))
                .Returns(mappedWorkflowPrediction);

            _mockWorkflowPredictionRepository
                .Setup(repo => repo.AddAsync(mappedWorkflowPrediction))
                .ReturnsAsync(mappedWorkflowPrediction);

            var expectedMessage = $" WorkflowPrediction '{actionId}' has been created successfully";

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(referenceId, result.WorkflowPredictionId);
            Assert.Equal(expectedMessage, result.Message);

            _mockMapper.Verify(m => m.Map<Domain.Entities.WorkflowPrediction>(command), Times.Once);
            _mockWorkflowPredictionRepository.Verify(repo => repo.AddAsync(mappedWorkflowPrediction), Times.Once);
        }

        [Fact]
        public async Task Handle_CallsRepositoryAndMapperMethodsCorrectly()
        {
            var command = new CreateWorkflowPredictionCommand();
            var mappedWorkflowPrediction = new Domain.Entities.WorkflowPrediction
            {
                ActionId = Guid.NewGuid().ToString(),
                ReferenceId = Guid.NewGuid().ToString()
            };

            _mockMapper
                .Setup(m => m.Map<Domain.Entities.WorkflowPrediction>(command))
                .Returns(mappedWorkflowPrediction);

            _mockWorkflowPredictionRepository
                .Setup(repo => repo.AddAsync(mappedWorkflowPrediction))
                .ReturnsAsync(mappedWorkflowPrediction);

            await _handler.Handle(command, CancellationToken.None);

            _mockMapper.Verify(m => m.Map<Domain.Entities.WorkflowPrediction>(command), Times.Once);
            _mockWorkflowPredictionRepository.Verify(repo => repo.AddAsync(mappedWorkflowPrediction), Times.Once);
        }
    }
}
