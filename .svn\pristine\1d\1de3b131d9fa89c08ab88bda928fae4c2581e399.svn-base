﻿namespace ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Update;

public class UpdateNodeWorkflowExecutionCommandHandler : IRequestHandler<UpdateNodeWorkflowExecutionCommand,
    UpdateNodeWorkflowExecutionResponse>
{
    private readonly IMapper _mapper;
    private readonly INodeWorkflowExecutionRepository _nodeWorkflowExecutionRepository;

    public UpdateNodeWorkflowExecutionCommandHandler(IMapper mapper,
        INodeWorkflowExecutionRepository nodeWorkflowExecutionRepository)
    {
        _mapper = mapper;
        _nodeWorkflowExecutionRepository = nodeWorkflowExecutionRepository;
    }

    public async Task<UpdateNodeWorkflowExecutionResponse> Handle(UpdateNodeWorkflowExecutionCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _nodeWorkflowExecutionRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.NodeWorkflowExecution), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateNodeWorkflowExecutionCommand),
            typeof(Domain.Entities.NodeWorkflowExecution));

        await _nodeWorkflowExecutionRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateNodeWorkflowExecutionResponse
        {
            Message = Message.Update(nameof(Domain.Entities.NodeWorkflowExecution), eventToUpdate.NodeName),
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}