﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowProfileInfoViewFixture:IDisposable
{
    public List<WorkflowProfileInfoView> WorkflowProfileInfoViewPaginationList { get; set; }
    public List<WorkflowProfileInfoView> WorkflowProfileInfoViewList { get; set; }
    public WorkflowProfileInfoView WorkflowProfileInfoViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowProfileInfoViewFixture()
    {
        var fixture = new Fixture();

        WorkflowProfileInfoViewList = fixture.Create<List<WorkflowProfileInfoView>>();

        WorkflowProfileInfoViewPaginationList = fixture.CreateMany<WorkflowProfileInfoView>(20).ToList();

        WorkflowProfileInfoViewPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowProfileInfoViewList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowProfileInfoViewDto = fixture.Create<WorkflowProfileInfoView>();

        WorkflowProfileInfoViewDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

}
