﻿namespace ContinuityPatrol.Application.Features.UserInfo.Commands.Create;

public class CreateUserInfoCommandHandler : IRequestHandler<CreateUserInfoCommand, CreateUserInfoResponse>
{
    private readonly IMapper _mapper;
    private readonly IUserInfoRepository _userInfoRepository;

    public CreateUserInfoCommandHandler(IMapper mapper, IUserInfoRepository userInfoRepository)
    {
        _mapper = mapper;
        _userInfoRepository = userInfoRepository;
    }

    public async Task<CreateUserInfoResponse> Handle(CreateUserInfoCommand request, CancellationToken cancellationToken)
    {
        var userInfo = _mapper.Map<Domain.Entities.UserInfo>(request);

        userInfo = await _userInfoRepository.AddAsync(userInfo);

        var response = new CreateUserInfoResponse
        {
            Message = Message.Create(nameof(Domain.Entities.UserInfo), userInfo.UserName),

            UserInfoId = userInfo.Id
        };

        return response;
    }
}