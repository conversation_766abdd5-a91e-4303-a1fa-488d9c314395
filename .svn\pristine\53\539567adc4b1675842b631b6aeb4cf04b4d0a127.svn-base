﻿namespace ContinuityPatrol.Application.Features.Setting.Queries.GetDetail;

public class GetSettingDetailQueryHandler : IRequestHandler<GetSettingDetailQuery, SettingDetailVm>
{
    private readonly IMapper _mapper;
    private readonly ISettingRepository _settingRepository;

    public GetSettingDetailQueryHandler(IMapper mapper, ISettingRepository settingRepository)
    {
        _mapper = mapper;
        _settingRepository = settingRepository;
    }

    public async Task<SettingDetailVm> Handle(GetSettingDetailQuery request, CancellationToken cancellationToken)
    {
        //Guard.Against.NegativeOrZero(request.Id, nameof(request.Id), ErrorMessage.Setting.SettingIdCannotBeZero);

        var setting = await _settingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(setting, nameof(Domain.Entities.Setting),
            new NotFoundException(nameof(Domain.Entities.Setting), request.Id));

        var settingDetailDto = _mapper.Map<SettingDetailVm>(setting);

        return settingDetailDto;
    }
}