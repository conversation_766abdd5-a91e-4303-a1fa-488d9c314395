using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create; 
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel; 

namespace ContinuityPatrol.Application.Mappings;

public class MenuBuilderProfile : Profile
{
    public MenuBuilderProfile()
    {
        CreateMap<MenuBuilder, MenuBuilderListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MenuBuilder, MenuBuilderDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId)); 
     
        CreateMap<MenuBuilder, CreateMenuBuilderCommand>().ReverseMap();
        CreateMap<MenuBuilder, MenuBuilderViewModel>().ReverseMap();

        CreateMap<CreateMenuBuilderCommand, MenuBuilderViewModel>().ReverseMap();
        CreateMap<UpdateMenuBuilderCommand, MenuBuilderViewModel>().ReverseMap(); 

        CreateMap<UpdateMenuBuilderCommand, MenuBuilder>().ForMember(x => x.Id, y => y.Ignore());
    }
}
