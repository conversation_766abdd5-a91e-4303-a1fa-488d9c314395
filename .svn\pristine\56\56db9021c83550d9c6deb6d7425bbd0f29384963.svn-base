﻿using ContinuityPatrol.Application.Features.FormHistory.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormHistory.Validators;

public class UpdateFormHistoryValidatorTests
{
    private readonly Mock<IFormHistoryRepository> _mockFormHistoryRepository;
    public List<Domain.Entities.FormHistory> FormHistories { get; set; }

    public UpdateFormHistoryValidatorTests()
    {
        FormHistories = new Fixture().Create<List<Domain.Entities.FormHistory>>();

        _mockFormHistoryRepository = FormHistoryRepositoryMocks.UpdateFormHistoryRepository(FormHistories);
    }

    //Name

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Name_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Name_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter Valid Type.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Name_InFormHistory_MinimumRange(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "CB";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Name_InFormHistory_MaximumRange(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHI";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "  KG Tech  ";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_DoubleSpace_InFront(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "  KG Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_DoubleSpace_InBack(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "KG Tech  ";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_TripleSpace_InBetween(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "KG   Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_SpecialCharacters_InFront(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "*&&^%$KG Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_SpecialCharacters_InBack(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "KG Tech&^%$$";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_SpecialCharacters_InBetween(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "KG^%^%%$Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_SpecialCharacters_Only(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "&$#^%^%%";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_UnderScore_InFront(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "_KG Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_UnderScore_InBack(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "KG Tech_";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_Numbers_InFront(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "546KG Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_UnderScore_InFront_Numbers_InBack(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "_KG Tech345";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_UnderScore_InFront_AndBack_Numbers_InFront(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "_353KG Tech_";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Name_InFormHistory_With_Numbers_Only(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.FormName = "895211254";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    //LoginName

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_LoginName_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.LoginName = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_LoginName_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.LoginName = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[2].ErrorMessage);
    }


    //Properties

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Properties_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Properties = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Properties_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Properties = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[2].ErrorMessage);
    }

    //Type

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Type_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Type = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Type is Required.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Type_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Type = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "'Type' must not be empty.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Valid_Type_InFormHistory(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Type = "  KG  Tech";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter Valid Type.");
    }


    //Description


    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Description_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Description = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter the Description.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Description_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Description = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "'Description' must not be empty.");
    }


    //Comments


    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Comments_InFormHistory_WithEmpty(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Comments = "";

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter the Comments.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Update_Comments_InFormHistory_IsNull(UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        var validator = new UpdateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        updateFormHistoryCommand.Comments = null;

        var validateResult = await validator.ValidateAsync(updateFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "'Comments' must not be empty.");
    }
}