﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport
{
    public class GetCyberSnapsReportVm
    {
        public string ReportGeneratedBy {  get; set; }
        public string ReportGeneratedTime {  get; set; }
        public string CyberSnapReportDataName { get; set; }
        public List<CyberSnapsReportVm> CyberSnapsReportVm { get; set; } = new();
    }
    public class CyberSnapsReportVm
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Tag { get; set; }
        public string Type { get; set; }
        public string Location { get; set; }
        public int Age { get; set; }
        public int Size { get; set; }
    }
}
