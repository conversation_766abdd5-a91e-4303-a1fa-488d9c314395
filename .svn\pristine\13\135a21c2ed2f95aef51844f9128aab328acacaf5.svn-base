﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Web.Areas.Cloud.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;


namespace ContinuityPatrol.Web.UnitTests.Areas.Cloud.Controllers;

public class CloudConnectControllerTests
{
    private readonly Mock<ILogger<CloudConnectController>> _mockLogger = new();
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository = new();
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
    private readonly CloudConnectController _controller;
    public CloudConnectControllerTests()
    {
        _controller = new CloudConnectController(_mockLogger.Object, _mockUserActivityRepository.Object, _mockLoggedInUserService.Object);
    }


    [Fact]
    public void List_ReturnsViewResult()
    {
        var result =
            _controller.List();

        Assert.IsType<ViewResult>(result);
    }
}