﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CredentialProfile.Commands;

public class UpdateCredentialProfileTests : IClassFixture<CredentialProfileFixture>
{
    private readonly CredentialProfileFixture _credentialProfileFixture;
    private readonly Mock<ICredentialProfileRepository> _mockCredentialProfileRepository;
    private readonly UpdateCredentialProfileCommandHandler _handler;

    public UpdateCredentialProfileTests(CredentialProfileFixture credentialProfileFixture)
    {
        _credentialProfileFixture = credentialProfileFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockCredentialProfileRepository = CredentialProfileRepositoryMocks.UpdateCredentialProfileRepository(_credentialProfileFixture.CredentialProfiles);

        _handler = new UpdateCredentialProfileCommandHandler(_credentialProfileFixture.Mapper, _mockCredentialProfileRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidCredentialProfile_UpdateToCredentialProfilesRepo()
    {
        _credentialProfileFixture.UpdateCredentialProfileCommand.Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId;

        var result = await _handler.Handle(_credentialProfileFixture.UpdateCredentialProfileCommand, CancellationToken.None);

        var credentialProfile = await _mockCredentialProfileRepository.Object.GetByReferenceIdAsync(result.CredentialProfileId);

        Assert.Equal(_credentialProfileFixture.UpdateCredentialProfileCommand.Name, credentialProfile.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidCredentialProfileResponse()
    {
        _credentialProfileFixture.UpdateCredentialProfileCommand.Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId;

        var result = await _handler.Handle(_credentialProfileFixture.UpdateCredentialProfileCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateCredentialProfileResponse));

        result.CredentialProfileId.ShouldBeGreaterThan(0.ToString());

        result.CredentialProfileId.ShouldBe(_credentialProfileFixture.UpdateCredentialProfileCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidCredentialProfileId()
    {
        _credentialProfileFixture.UpdateCredentialProfileCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_credentialProfileFixture.UpdateCredentialProfileCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _credentialProfileFixture.UpdateCredentialProfileCommand.Id = _credentialProfileFixture.CredentialProfiles[0].ReferenceId;

        await _handler.Handle(_credentialProfileFixture.UpdateCredentialProfileCommand, CancellationToken.None);

        _mockCredentialProfileRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockCredentialProfileRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.CredentialProfile>()), Times.Once);
    }
}