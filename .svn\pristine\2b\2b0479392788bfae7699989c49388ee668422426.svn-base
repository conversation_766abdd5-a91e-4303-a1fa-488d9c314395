namespace ContinuityPatrol.Application.Features.RoboCopy.Queries.GetNameUnique;

public class GetRoboCopyNameUniqueQueryHandler : IRequestHandler<GetRoboCopyNameUniqueQuery, bool>
{
    private readonly IRoboCopyRepository _roboCopyRepository;

    public GetRoboCopyNameUniqueQueryHandler(IRoboCopyRepository roboCopyRepository)
    {
        _roboCopyRepository = roboCopyRepository;
    }

    public async Task<bool> Handle(GetRoboCopyNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _roboCopyRepository.IsNameExist(request.Name, request.Id);
    }
}