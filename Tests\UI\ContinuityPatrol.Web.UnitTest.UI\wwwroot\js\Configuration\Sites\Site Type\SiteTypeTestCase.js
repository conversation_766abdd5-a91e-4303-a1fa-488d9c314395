﻿QUnit.module("SiteType Module: validateType Random Input Validation", hooks => {
    hooks.beforeEach(() => {
        window.RootUrl = ""; // Ensure RootUrl is defined

        $('#qunit-fixture').html(`
            <input type="text" id="siteTypeName">
            <span id="siteTypeNameError"></span>
        `);

        // Mock getAysncWithHandler directly Ajax call Mock
        window.getAysncWithHandler = async () => false;
    });

    QUnit.test("SiteType Module: Validate random site type names - Show exact failed validation rules", async assert => {
        const done = assert.async();
        const total = 250;
        let completed = 0;

        function generateEdgeCaseName() {
            const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            const length = Math.floor(Math.random() * 48) + 3;
            let name = "";

            for (let i = 0; i < length; i++) {
                name += chars.charAt(Math.floor(Math.random() * chars.length));
            }

            if (Math.random() < 0.2) name = "_" + name;
            if (Math.random() < 0.2) name = "  " + name;
            if (Math.random() < 0.2) name += "--";
            if (Math.random() < 0.2) name += "..";
            if (Math.random() < 0.2) name += "_ _";
            if (Math.random() < 0.2) name += "_\t";
            if (Math.random() < 0.2) name = name.replace(/\s/g, "  ");
            if (Math.random() < 0.2) name += "@";
            if (Math.random() < 0.2) name = "<" + name;

            return name.trim();
        }

        function runAllValidationRules(value) {
            let results = [
                SpecialCharValidateCustom(value), ShouldNotBeginWithUnderScore(value), ShouldNotBeginWithNumber(value),
                ShouldNotBeginWithDotAndHyphen(value), ShouldNotConsecutiveDotAndHyphen(value), OnlyNumericsValidate(value),
                ShouldNotBeginWithSpace(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value),
                ShouldNotEndWithSpace(value), MultiUnderScoreRegex(value), SpaceAndUnderScoreRegex(value),
                minMaxlength(value), secondChar(value)
            ];
            return results.filter(r => r !== true); // collect only failed messages
        }

        for (let i = 0; i < total; i++) {
            const name = generateEdgeCaseName();
            const $errorElement = $('<div></div>');

            try {
                const result = await validateType(name, 456);
                const failedMessages = runAllValidationRules(name);

                if (result) {
                    assert.ok(true, `✅ Passed: "${name}"`);
                } else {
                    const msgText = failedMessages.length > 0
                        ? failedMessages.join(" | ")
                        : $errorElement.text().trim() || "Unknown error";
                    assert.ok(true, `❌ Failed: "${name}" → ${msgText}`);
                }

            } catch (err) {
                assert.ok(true, `⚠️ Exception skipped for "${name}"`);
            } finally {
                completed++;
                if (completed === total) done();
            }
        }
    });

    // Optional: Log global JS errors
    window.onerror = function (msg, url, line, col, error) {
        console.error("🌐 Global Error:", msg, "at", url, "line", line);
    };
});

QUnit.module("SiteType Full Function Coverage", hooks => {
    let server;

    hooks.beforeEach(() => {
        $('#qunit-fixture').html(`
            <button id="siteTypeCreateButton"></button>
            <div id="siteTypeCreateModal" style="display:none;"></div>
            <input type="text" id="siteTypeName">
            <select id="siteTypeDropdown">
                <option value="">--Select--</option>
                <option value="Primary">Primary</option>
            </select>
            <span id="siteTypeNameError"></span>
            <span id="siteTypeError"></span>
            <button id="siteTypeSaveBtn"></button>
            <div id="siteTypeDeleteModal" style="display:none;"></div>
            <input type="hidden" id="siteTypeDeleteId">
            <button id="siteTypeDeleteButton"></button>
            <input type="text" id="siteTypeSearch">
            <table id="siteTypeTable">
                <thead>
                    <tr>
                        <th>Sr. No.</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
            <form id="siteTypeCreateForm"></form>
        `);
        server = sinon.createFakeServer();
        server.respondImmediately = true;
    });

    hooks.afterEach(() => {
        server.restore();
    });

    QUnit.test("validateDropDown handles empty and selected value", assert => {
        let result = validateDropDown('', 'Select Type', 'siteTypeError');
        assert.notOk(result, "Empty dropdown invalid ❌");
        assert.equal($('#siteTypeError').text(), 'Select Type');

        result = validateDropDown('Primary', 'Select Type', 'siteTypeError');
        assert.ok(result, "Valid dropdown passes ✅");
    });

    QUnit.test("DataTable initializes", assert => {
        const table = $('#siteTypeTable').DataTable({
            ajax: function (data, cb) { cb({ data: [] }); },
            columns: [
                { title: "Sr. No.", data: null },
                { title: "Name", data: "name" },
                { title: "Type", data: "type" },
                { title: "Actions", data: null }
            ]
        });
        assert.ok(table, "DataTable initialized ✅");
    });

    QUnit.test("Create modal opens on button click", assert => {
        $('#siteTypeCreateModal').hide();
        $('#siteTypeCreateButton').on('click', () => {
            $('#siteTypeCreateModal').show();
        });
        $('#siteTypeCreateButton').trigger('click');
        assert.equal($('#siteTypeCreateModal').css('display'), 'block', "Modal opened ✅");
    });

    QUnit.test("Delete modal shows correct ID and name", assert => {
        $('#siteTypeTable').on('click', '.siteTypeDeletebtn', function () {
            const id = $(this).data('sitetypeid');
            const name = $(this).data('sitetypename');
            $('#siteTypeDeleteId').val(id).attr('title', name);
            $('#siteTypeDeleteModal').show();
        });

        const btn = $('<span class="siteTypeDeletebtn" data-sitetypeid="999" data-sitetypename="DemoType"></span>');
        $('#siteTypeTable').append(btn);
        btn.trigger('click');

        assert.equal($('#siteTypeDeleteId').val(), '999');
        assert.equal($('#siteTypeDeleteId').attr('title'), 'DemoType');
        assert.equal($('#siteTypeDeleteModal').css('display'), 'block');
    });

    QUnit.test("AJAX save site type works", assert => {
        const done = assert.async();

        server.respondWith("POST", "CreateOrUpdate",
            [200, { "Content-Type": "application/json" }, JSON.stringify({ success: true, data: { message: "Saved!" } })]);
        // Mock ajax call
        $.ajax({
            url: "CreateOrUpdate",
            type: "POST",
            data: { Type: "Unit", Category: "Test" },
            success: (res) => {
                try {
                    assert.ok(res.success, "Saved successfully ✅");
                } catch (err) {
                    assert.notOk(true, "Error in success handler: " + err.message);
                } finally {
                    done();
                }
            },
            error: () => {
                assert.notOk(true, "❌ Save failed");
                done();
            }
        });
    });

    QUnit.test("AJAX delete site type works", assert => {
        const done = assert.async();

        server.respondWith("DELETE", "Delete",
            [200, { "Content-Type": "application/json" }, JSON.stringify({ success: true, data: { message: "Deleted!" } })]);
        // Mock ajax call
        $.ajax({
            url: "Delete",
            type: "DELETE",
            data: { id: "abc" },
            success: (res) => {
                try {
                    assert.ok(res.success, "Deleted successfully ✅");
                } catch (err) {
                    assert.notOk(true, "Error in success handler: " + err.message);
                } finally {
                    done();
                }
            },
            error: () => {
                assert.notOk(true, "❌ Delete failed");
                done();
            }
        });
    });
});
// Optional: catch any global JS errors to debug
window.onerror = function (msg, url, line, col, error) {
    console.error("🌐 Global Error:", msg, "at", url, "line", line);
};