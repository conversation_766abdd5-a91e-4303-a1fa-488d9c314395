﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberJobWorkflowSchedulerRepository : BaseRepository<CyberJobWorkflowScheduler>, ICyberJobWorkflowSchedulerRepository
{
    private readonly ApplicationDbContext _dbContext;

    public CyberJobWorkflowSchedulerRepository(ApplicationDbContext dbContext): base(dbContext)
    {
        _dbContext = dbContext;
        
    }

    public async Task<PaginatedResult<CyberJobWorkflowSchedulerListVm>> GetCyberJobWorkflowSchedulerPagination(string startDate,string endDate,int pageNumber, int pageSize)
    {
        var query = _dbContext.CyberJobWorkflowSchedulers.AsNoTracking().Active();
       
        if (startDate.IsNotNullOrWhiteSpace() && endDate.IsNotNullOrWhiteSpace())
        {
            query = query.Where(x =>
                        (x.StartTime.ToDateTime() == DateTime.MinValue || x.StartTime.ToDateTime().Date >= startDate.ToDateTime()) &&
                        (x.EndTime.ToDateTime() == DateTime.MinValue || x.EndTime.ToDateTime().Date <= endDate.ToDateTime()));

        }
        var groupedData = await query.ToListAsync();
       
        var queryable = groupedData
             .GroupBy(x => x.JobId)
             .OrderByDescending(g => g.Key)
             .Select(g => new CyberJobWorkflowSchedulerListVm
             {
                 JobId = g.First().JobId,
                 Name = g.First().Name,
                 WorkflowName = g.First().WorkflowName,
                 StartTime = g.First().StartTime,
                 Status = $"{g.Count(x => x.Status != null && x.Status.ToLower() == "completed")}/{g.Count()}",
                 State = g.First().State,
                 EndTime = g.First().EndTime,
                 ConditionActionId = g.First().ConditionActionId,
                 CurrentActionName = g.First().CurrentActionName,
                 SuccessCount = $"{(g.Count(x => x.Status != null && x.Status.ToLower() == "completed") * 100 / g.Count())}%"
             }).AsQueryable();

        var totalCount = queryable.Count();

        pageNumber = pageNumber <= 0 ? 1 : pageNumber;
        pageSize = pageSize <= 0 ? 10 : pageSize;

        var pagedData = queryable.ToList()
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return new PaginatedResult<CyberJobWorkflowSchedulerListVm>(
            succeeded: true,
            data: pagedData,
            count: totalCount,
            page: pageNumber,
            pageSize: pageSize
        );
    }
}
