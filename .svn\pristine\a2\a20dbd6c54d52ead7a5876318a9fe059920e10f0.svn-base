using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class Db2HaDrMonitorStatusFixture : IDisposable
{
    public List<Db2HaDrMonitorStatus> Db2HaDrMonitorStatusPaginationList { get; set; }
    public List<Db2HaDrMonitorStatus> Db2HaDrMonitorStatusList { get; set; }
    public Db2HaDrMonitorStatus Db2HaDrMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
    public const string Type = "TestType";

    public ApplicationDbContext DbContext { get; private set; }

    public Db2HaDrMonitorStatusFixture()
    {
        var fixture = new Fixture();

        Db2HaDrMonitorStatusList = fixture.Create<List<Db2HaDrMonitorStatus>>();

        Db2HaDrMonitorStatusPaginationList = fixture.CreateMany<Db2HaDrMonitorStatus>(20).ToList();

        Db2HaDrMonitorStatusPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        Db2HaDrMonitorStatusPaginationList.ForEach(x => x.IsActive = true);
        Db2HaDrMonitorStatusPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        Db2HaDrMonitorStatusPaginationList.ForEach(x => x.Type = Type);

        Db2HaDrMonitorStatusList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        Db2HaDrMonitorStatusList.ForEach(x => x.IsActive = true);
        Db2HaDrMonitorStatusList.ForEach(x => x.InfraObjectId = InfraObjectId);
        Db2HaDrMonitorStatusList.ForEach(x => x.Type = Type);

        Db2HaDrMonitorStatusDto = fixture.Create<Db2HaDrMonitorStatus>();
        Db2HaDrMonitorStatusDto.ReferenceId = Guid.NewGuid().ToString();
        Db2HaDrMonitorStatusDto.IsActive = true;
        Db2HaDrMonitorStatusDto.InfraObjectId = InfraObjectId;
        Db2HaDrMonitorStatusDto.Type = Type;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
