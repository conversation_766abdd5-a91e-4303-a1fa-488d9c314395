﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetByInfraObjectAndActionId;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetRunningActionCountByOperationId;
using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetWorkflowActionResultByOperationGroupId;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionResultModel;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowActionResultProfile : Profile
{
    public WorkflowActionResultProfile()
    {
        CreateMap<WorkflowActionResult, CreateWorkflowActionResultCommand>().ReverseMap();
        CreateMap<UpdateWorkflowActionResultCommand, WorkflowActionResult>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<WorkflowActionResult, WorkflowActionResultDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowActionResult, WorkflowActionResultListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowActionResult, WorkflowActionResultNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowActionResult, WorkflowActionResultByOperationGroupIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowActionResult, WorkflowActionResultByInfraObjectAndActionIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<List<WorkflowActionResult>, ActionRunningCountListVm>().ForMember(dest => dest.SkipCount,
                opt => opt.MapFrom(src => src.Count(x =>
                    x.Status.Trim().ToLower().Equals("skip") || x.Status.Trim().ToLower().Equals("skipped"))))
            .ForMember(dest => dest.SuccessCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("success"))))
            .ForMember(dest => dest.BypassedCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("bypassed"))))
            .ForMember(dest => dest.ErrorCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("error"))))
            .ForMember(dest => dest.RunningCount,
                opt => opt.MapFrom(src => src.Count(x => x.Status.Trim().ToLower().Equals("running"))));

    }
}