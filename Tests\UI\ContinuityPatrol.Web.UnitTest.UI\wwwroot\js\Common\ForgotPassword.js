﻿const userNameUrl = "Admin/User/GetUserLoginName";

function CommonValidation(errorElement, validationResults) {
    const failedValidations = validationResults.filter(result => result !== true);
    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0])
            .addClass('field-validation-error')
        return false;
    } else {
        errorElement.text('')
            .removeClass('field-validation-error')
        return true;
    }
}
const SpecialCharValidate = (value) => {
    const regex = /^[a-zA-Z0-9_\s]*$/;
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}
const ShouldNotBeginWithUnderScore = (value) => {
    return (RegExp(/(^_+)/).test(value)) ? "Should not begin with underscore" : true;
}
const ShouldNotBeginWithNumberForUser = (value) => { return (RegExp(/^\d+[a-zA-Z]/).test(value)) ? "Should not begin with number" : true; }
const SpaceWithUnderScore = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(\s+)(_)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const ShouldNotEndWithUnderScore = (value) => {
    return (RegExp(/([a-zA-Z0-9]+)(_+$)/).test(value)) ? "Should not end with underscore" : true;
}
const ShouldNotEndWithSpace = (value) => {
    return (!RegExp(/^[^\s]+(\s+[^\s]+)*$/).test(value)) ? "Should not end with space" : true;
}
const MultiUnderScoreRegex = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(_)(_+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const SpaceAndUnderScoreRegex = (value) => {
    return (RegExp(/(([a-zA-z0-9]+)(_)(\s+)([a-zA-Z0-9]+)*)/).test(value)) ? "Invalid format" : true;
}
const secondChar = (value) => {
    return value.charAt(1) === " " ? 'Invalid format' : true
}
const ShouldNotBeginWithSpace = (value) => { return (!RegExp(/^(?![\s-])[\w\s-]+$/).test(value)) ? "Should not begin with space" : true; }
$(function () {       
    $('#txtForgotLoginName').on('keyup', async function () {
        
        var errorElement = $('#error-loginName')
        var name = $(this).val();
        if (name) {
            $('#sendMailButton').prop('disabled', false);
        }
        let sanitizedValue = name.replace(/\s{1,}/g, ' ');
        $(this).val(sanitizedValue);
        //EncryptMail(name)
        if (!name) {
            errorElement.text('Enter login name')
                .addClass('field-validation-error');
        }
        else {
            errorElement.text('')
                .removeClass('field-validation-error');
            //const validationResults = [
            //    await SpecialCharValidate(name),
            //    await ShouldNotBeginWithUnderScore(name),
            //    await ShouldNotBeginWithSpace(name),
            //    await ShouldNotBeginWithNumberForUser(name),
            //    await SpaceWithUnderScore(name),
            //    await ShouldNotEndWithUnderScore(name),
            //    await ShouldNotEndWithSpace(name),
            //    await MultiUnderScoreRegex(name),
            //    await SpaceAndUnderScoreRegex(name),
            //    await secondChar(name),
               
            //];
            //return await CommonValidation(errorElement, validationResults);
        }
                  
       //$('#txtEmail').val(''); 
    });

    //$('#txtEmail').on('focus', function () {
    //    $(this).attr('readonly', true);
    //})
    function forgotDebounce(func, delay = 300) {
        let timer;
        return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(context, args);
            }, delay);
        };
    }
    $('.sendmail').on('click', forgotDebounce(async function () {
        
        //var email = $('#txtEmail').val();
        
        var loginName = $('#txtForgotLoginName').val();
        
        //var email = await validateEmail(email);

        var loginName1 = await validateLoginName(loginName);

        // if (email && loginName) {
        if (loginName1) {
           
            let response = await EncryptMail(loginName);

            if (!response) {
                $("#ForgotPasswordModel").on("submit", function (event) {
                    event.preventDefault(); // Stops form submission
                    return false;
                });

                $('#sendMailButton').prop('disabled', true);
            }   

            
        }

    },500));

    $('#cancel').on('click', function () {

        //$('#txtEmail').val(''); 
        $('#txtForgotLoginName').val('');
        $('#error-loginName').text('').removeClass('field-validation-error')
        //const errorElements = ['#error-Email', '#error-loginName'];

        //errorElements.forEach(element => {
        //    $(element).text('').removeClass('field-validation-error');
        //});
       
    });


    //function validateEmail(value) {
    //    var errorElement = $('#error-Email')  
    //    if (!value) {
    //        errorElement.text('Enter email address')
    //            .addClass('field-validation-error');
    //        return false;
    //    }
       
    //    else {
    //        return true;
    //    }
    //}

    function validateLoginName(value) {
        
        var errorElement = $('#error-loginName');  
        if (!value) {
            errorElement.text('Enter login name')
                .addClass('field-validation-error');  
            return false;
        }
        else {
            return true;
        }
    }


})

async function EncryptMail(name) {


    var url = RootUrl + userNameUrl;
    var data = { loginName: name };

    try {

        var result = await $.get(url, data).fail(OnError);

        if (result.success) {

            const randomString = generateRandomString(5);
            EncryptPassword(name, randomString);
            $('#textNewPassword').val(randomString);

            //const errorElements = ['#error-Email', '#error-loginName'];
            //errorElements.forEach(element => {
            //    $(element).text('').removeClass('field-validation-error');
            //});

            // Handle account lock scenarios
            if (result?.message?.isLock?.toLowerCase() === "true") {
                if (result?.message?.roleName?.toLowerCase() === "superadmin") {
                    notificationAlert_FP("warning", "User account locked, kindly contact another superadmin");
                } else {
                    notificationAlert_FP("warning", "User account locked, kindly contact superadmin");
                }
                $('#sendMailButton').prop('disabled', true);
                return false;
            } else if (result?.message?.roleName?.toLowerCase() === "siteadmin") {
                $('#sendMailButton').prop('disabled', true);
                return false;
            } else {
                $('#sendMailButton').prop('disabled', false);
                return true;
            }
        } else {
            var errorElement = $('#error-loginName');
            if ($('#txtForgotLoginName').val() !== '') {
                errorElement.text('Invalid login name')
                    .addClass('field-validation-error');
            }
            return false;
        }
    } catch (error) {
        // Handle any errors from the get request or other errors
        console.error("An error occurred:", error);
    }
}

function OnError(msg) {
    console.log(msg.d);
}

async function EncryptPassword(loginName, password) {
    let name = loginName.toLowerCase();
    let data = {
        loginName: name,
        password: password
    };

    try {
        const response = await $.ajax({
            type: "GET",
            url: RootUrl + "Account/HashPassword",
            data: data,
            dataType: "json"
        });
        if (response && response.encrypt) {
            
            $('#textPassword').val(response.encrypt);
            $('#ForgotPasswordModel').trigger('submit');
        }

    } catch (error) {
        console.error("Error hashing password: " + error);
    }
}

const generateRandomString = (length) => Array.from({ length }, () => String.fromCharCode(65 + Math.floor(Math.random() * 26) + (Math.random() < 0.5 ? 32 : 0))).join('');


const errorNotification_FP = (data) => {
    if (data.hasOwnProperty('ErrorCode')) {
        if (data.ErrorCode === 1001) {
            notificationAlert("unauthorised", 'Session expired')
            setTimeout(() => {
                window.location.assign(RootUrl + 'Account/Logout');
            }, 2000)
        }
    } else {
        notificationAlert("warning", data.message)
    }
}

const notificationAlert_FP = (toastClass, data) => {
   
    $('#alertClass, #icon_Detail').removeClass();
    let alertClass = toastClass === "success" ? "success-toast" : toastClass === "warning" ? "warning-toast" : toastClass === "info" ? "info-toast"
        : toastClass === "error" || "unauthorised" ? "unauthorised-toast" : '';
    let icon = toastClass === "success" ? "cp-check" : toastClass === "warning" ? "cp-warning" : toastClass === "info" ? "cp-note"
        : toastClass === "error" ? "cp-close" : toastClass === "unauthorised" ? "cp-disable" : '';

    $('#alertClass').addClass(alertClass)
    $("#icon_Detail").addClass(`${icon} toast_icon`)
    $('#message').text(data)
    $('#mytoastrdata').toast('show');
    setTimeout(function () {
        $('#mytoastrdata').toast('hide');
    }, 2000)
}

//$('#txtEmail').on('focus', function () {
//    $('#txtEmail').attr('readonly', true);
//})