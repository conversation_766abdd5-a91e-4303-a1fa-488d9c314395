using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DashboardViewFixture
{
    public DashboardViewListVm DashboardViewListVm { get; }
    public DashboardViewDetailVm DashboardViewDetailVm { get; }
    public BusinessViewPaginatedList BusinessViewPaginatedList { get; }
    public CreateDashboardViewCommand CreateDashboardViewCommand { get; }
    public UpdateDashboardViewCommand UpdateDashboardViewCommand { get; }
    public DeleteDashboardViewCommand DeleteDashboardViewCommand { get; }
    public DashboardViewNameVm DashboardViewNameVm { get; }
    public GetByEntityIdVm GetByEntityIdVm { get; }
    public DashboardViewByBusinessServiceIdVm DashboardViewByBusinessServiceIdVm { get; }
    public GetDashboardViewByInfraObjectIdVm GetDashboardViewByInfraObjectIdVm { get; }
    public DataLagStatusbyLast7DaysVm DataLagStatusbyLast7DaysVm { get; }
    public ItViewByInfraObjectIdVm ItViewByInfraObjectIdVm { get; }
    public DashboardViewByBusinessFunctionIdVm DashboardViewByBusinessFunctionIdVm { get; }
    public GetDcMappingListVm GetDcMappingListVm { get; }
    public GetServiceTopologyListVm GetServiceTopologyListVm { get; }
    public GetItViewListVm GetItViewListVm { get; }
    public DrillAnalyticsDetailVm DrillAnalyticsDetailVm { get; }
    public GetSlaBreachListVm GetSlaBreachListVm { get; }
    public BusinessImpactAnalysisVm BusinessImpactAnalysisVm { get; }
    public SiteCountListVm SiteCountListVm { get; }
    public VerifyWorkflowDetailVm VerifyWorkflowDetailVm { get; }
    public GetDcMappingSitesVm GetDcMappingSitesVm { get; }
    public TotalSiteDetailForOneViewListVm TotalSiteDetailForOneViewListVm { get; }
    public BreachDetailVm BreachDetailVm { get; }
    public LastDrillDetailVm LastDrillDetailVm { get; }
    public DatalagByBusinessServiceIdVm DatalagByBusinessServiceIdVm { get; }
    public ComponentFailureAnalyticsDetailVm ComponentFailureAnalyticsDetailVm { get; }
    public GetOperationalAvailabilityAnalyticsDetailVm GetOperationalAvailabilityAnalyticsDetailVm { get; }
    public OperationalHealthSummaryDetailVm OperationalHealthSummaryDetailVm { get; }
    public GetWorkflowAnalyticsDetailVm GetWorkflowAnalyticsDetailVm { get; }
    public ItViewByBusinessServiceIdVm ItViewByBusinessServiceIdVm { get; }
    public OneViewEntitiesEventView OneViewEntitiesEventView { get; }
    public OneViewRiskMitigationCyberSecurityView OneViewRiskMitigationCyberSecurityView { get; }
    public OneViewRiskMitigationFailedDrillView OneViewRiskMitigationFailedDrillView { get; }
    public ResilienceHealthStatusDetailVm ResilienceHealthStatusDetailVm { get; }
    public SitePropertiesByBusinessServiceIdVm SitePropertiesByBusinessServiceIdVm { get; }
    public DashboardImpactAvailabilityDetailVm DashboardImpactAvailabilityDetailVm { get; }

    public DashboardViewFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<DashboardViewListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Core Business Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise Critical Function")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Database Server")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "Enterprise mission-critical dashboard view for monitoring business service health and performance")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Database Replication")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:05:30")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"monitoring\":{\"type\":\"real_time\",\"frequency\":\"5_minutes\",\"thresholds\":{\"rpo\":\"15_minutes\",\"rto\":\"1_hour\"}}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:15:00")
            .With(b => b.ConfiguredRTO, "01:00:00")
            .With(b => b.RPOThreshold, "00:10:00")
            .With(b => b.CurrentRPO, "00:05:30")
            .With(b => b.CurrentRTO, "00:45:00")
            .With(b => b.State, "Healthy")
            .With(b => b.IsDRReady, true));

        fixture.Customize<DashboardViewDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Mission-Critical Business Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise Core Business Function")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Primary Database Cluster")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "Comprehensive enterprise dashboard view providing real-time monitoring of mission-critical business services with advanced analytics and alerting capabilities")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Advanced Database Replication")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:03:45")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"enterpriseMonitoring\":{\"type\":\"comprehensive\",\"frequency\":\"real_time\",\"analytics\":{\"predictive\":true,\"anomaly_detection\":true},\"thresholds\":{\"rpo\":\"15_minutes\",\"rto\":\"1_hour\",\"availability\":\"99.99%\"},\"alerting\":{\"channels\":[\"email\",\"sms\",\"slack\"],\"escalation\":\"automatic\"}}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:15:00")
            .With(b => b.ConfiguredRTO, "01:00:00")
            .With(b => b.RPOThreshold, "00:10:00")
            .With(b => b.SiteProperties, "{\"site\":\"primary\",\"location\":\"datacenter1\"}")
            .With(b => b.CurrentRPO, "00:03:45")
            .With(b => b.CurrentRTO, "00:35:00")
            .With(b => b.State, "Optimal")
            .With(b => b.ErrorMessage, "")
            .With(b => b.IsDRReady, true));

        fixture.Customize<BusinessViewPaginatedList>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service")
            .With(b => b.Priority, 1)
            .With(b => b.Status, "Active")
            .With(b => b.IsDRReady, true)
            .With(b => b.ConfiguredRPO, "00:15:00")
            .With(b => b.ConfiguredRTO, "01:00:00")
            .With(b => b.RPOThreshold, "00:10:00")
            .With(b => b.CurrentRPO, "00:05:00")
            .With(b => b.CurrentRTO, "00:45:00")
            .With(b => b.Percentage, "95.5")
            .With(b => b.IsDataLagExceed, false)
            .With(b => b.IsPartial, false)
            .With(b => b.WorkflowIsRunning, false));

        fixture.Customize<CreateDashboardViewCommand>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "New Enterprise Business Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "New Enterprise Function")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "New Enterprise Infrastructure")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "New enterprise dashboard view for monitoring critical business operations")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Real-time Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:02:00")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"newDashboard\":{\"monitoring\":\"enabled\",\"alerts\":\"configured\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:10:00")
            .With(b => b.ConfiguredRTO, "00:30:00")
            .With(b => b.RPOThreshold, "00:05:00")
            .With(b => b.SiteProperties, "{\"site\":\"primary\",\"location\":\"datacenter1\"}")
            .With(b => b.CurrentRPO, "00:02:00")
            .With(b => b.CurrentRTO, "00:25:00")
            .With(b => b.RPOGeneratedDate, DateTime.Now.AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.RTOGeneratedDate, DateTime.Now.AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.State, "Active")
            .With(b => b.ErrorMessage, "")
            .With(b => b.EstimatedRTO, "00:20:00")
            .With(b => b.IsDRReady, true));

        fixture.Customize<UpdateDashboardViewCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Updated Enterprise Business Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Updated Enterprise Function")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Updated Enterprise Infrastructure")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "Updated enterprise dashboard view with enhanced monitoring capabilities")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Enhanced Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:01:30")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"updatedDashboard\":{\"monitoring\":\"enhanced\",\"analytics\":\"enabled\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:08:00")
            .With(b => b.ConfiguredRTO, "00:25:00")
            .With(b => b.RPOThreshold, "00:04:00")
            .With(b => b.SiteProperties, "{\"site\":\"primary\",\"location\":\"datacenter1\"}")
            .With(b => b.RPOGeneratedDate, DateTime.Now.AddMinutes(-3).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.RTOGeneratedDate, DateTime.Now.AddMinutes(-3).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.CurrentRPO, "00:01:30")
            .With(b => b.CurrentRTO, "00:20:00")
            .With(b => b.State, "Optimized")
            .With(b => b.ErrorMessage, "")
            .With(b => b.EstimatedRTO, "00:18:00")
            .With(b => b.IsDRReady, true));

        fixture.Customize<DeleteDashboardViewCommand>(c => c
            .With(b => b.BusinessViewId, Guid.NewGuid().ToString()));

        fixture.Customize<DashboardViewNameVm>(c => c
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Dashboard Service")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Oracle"));

        fixture.Customize<GetByEntityIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Type, "Dashboard")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Entity Infrastructure")
            .With(b => b.WorkflowId, Guid.NewGuid().ToString())
            .With(b => b.WorkflowName, "Enterprise Entity Workflow")
            .With(b => b.Properties, "{\"entity\":{\"type\":\"dashboard\",\"monitoring\":\"enabled\"}}")
            .With(b => b.PageProperties, "{\"page\":{\"layout\":\"grid\",\"widgets\":\"enabled\"}}")
            .With(b => b.DashboardViewId, Guid.NewGuid().ToString())
            .With(b => b.PageBuilderId, Guid.NewGuid().ToString())
            .With(b => b.ConfiguredRPO, "00:15:00")
            .With(b => b.DataLagValue, "00:03:00")
            .With(b => b.RPOGeneratedDate, DateTime.Now.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss"))
            .With(b => b.ServerStatus, new List<ServerStatus>()));

        fixture.Customize<DashboardViewByBusinessServiceIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service View")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise Business Function View")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Infrastructure View")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "Enterprise business service dashboard view")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Business Service Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:04:00")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"businessService\":{\"monitoring\":\"enabled\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:15:00")
            .With(b => b.ConfiguredRTO, "01:00:00")
            .With(b => b.RPOThreshold, "00:10:00")
            .With(b => b.SiteProperties, "{\"site\":\"primary\"}")
            .With(b => b.CurrentRPO, "00:04:00")
            .With(b => b.CurrentRTO, "00:50:00")
            .With(b => b.State, "Active")
            .With(b => b.ErrorMessage, "")
            .With(b => b.IsDRReady, true));

        fixture.Customize<GetDashboardViewByInfraObjectIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Infrastructure Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise Infrastructure Function")
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Infrastructure Object")
            .With(b => b.Priority, 1)
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Description, "Enterprise infrastructure dashboard view")
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Infrastructure Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:02:30")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"infrastructure\":{\"monitoring\":\"enabled\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.ConfiguredRPO, "00:10:00")
            .With(b => b.ConfiguredRTO, "00:30:00")
            .With(b => b.RPOThreshold, "00:05:00")
            .With(b => b.SiteProperties, "{\"site\":\"primary\"}")
            .With(b => b.CurrentRPO, "00:02:30")
            .With(b => b.CurrentRTO, "00:25:00")
            .With(b => b.State, "Active")
            .With(b => b.ErrorMessage, "")
            .With(b => b.IsDRReady, true));

        fixture.Customize<DataLagStatusbyLast7DaysVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise 7-Day Infrastructure")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise 7-Day Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise 7-Day Function")
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "7-Day Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:06:00")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"sevenDay\":{\"monitoring\":\"enabled\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.CurrentRTO, "01:00:00"));

        fixture.Customize<ItViewByInfraObjectIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Properties, "{\"itView\":{\"monitoring\":\"enabled\"}}")
            .With(b => b.PRServerId, Guid.NewGuid().ToString())
            .With(b => b.DRServerId, Guid.NewGuid().ToString())
            .With(b => b.PRServerName, "Enterprise Primary Server")
            .With(b => b.DRServerName, "Enterprise DR Server")
            .With(b => b.PRServerStatus, "Active")
            .With(b => b.DRServerStatus, "Standby")
            .With(b => b.PRDatabaseId, Guid.NewGuid().ToString())
            .With(b => b.DRDatabaseId, Guid.NewGuid().ToString())
            .With(b => b.PRDatabaseName, "Enterprise Primary Database")
            .With(b => b.DRDatabaseName, "Enterprise DR Database")
            .With(b => b.PRDatabaseStatus, "Online")
            .With(b => b.DRDatabaseStatus, "Synchronized")
            .With(b => b.ReplicationType, "Synchronous")
            .With(b => b.ReplicationCategoryType, "Database")
            .With(b => b.DROperationStatus, "Ready"));

        fixture.Customize<DashboardViewByBusinessFunctionIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectId, Guid.NewGuid().ToString())
            .With(b => b.InfraObjectName, "Enterprise Function Infrastructure")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Function Service")
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise Core Function")
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.EntityId, Guid.NewGuid().ToString())
            .With(b => b.MonitorType, "Function Monitoring")
            .With(b => b.Type, 1)
            .With(b => b.DataLagValue, "00:05:00")
            .With(b => b.Status, "Active")
            .With(b => b.Properties, "{\"function\":{\"monitoring\":\"enabled\"}}")
            .With(b => b.ReplicationStatus, 1)
            .With(b => b.DROperationStatus, 1)
            .With(b => b.CurrentRTO, "00:45:00"));

        fixture.Customize<GetDcMappingListVm>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise DC Mapping Service")
            .With(b => b.SiteProperties, "{\"site\":{\"type\":\"datacenter\",\"location\":\"primary\"}}")
            .With(b => b.Status, "Active")
            .With(b => b.Priority, 1)
            .With(b => b.GetDcMappingBusinessFunctionListVms, new List<GetDcMappingBusinessFunctionListVm>()));

        fixture.Customize<GetServiceTopologyListVm>(c => c
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Topology Site")
            .With(b => b.SiteType, "Primary")
            .With(b => b.ReplicationStatus, new List<int> { 1, 2 })
            .With(b => b.DROperationStatus, new List<int> { 1, 2 })
            .With(b => b.ServerTopologyLists, new List<ServerTopologyList>())
            .With(b => b.DatabaseTopologyLists, new List<DatabaseTopologyList>())
            .With(b => b.ReplicationTopologyLists, new List<ReplicationTopologyList>())
            .With(b => b.InfraObjectTopologyLists, new List<InfraObjectTopologyList>()));

        fixture.Customize<GetItViewListVm>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise IT View Service")
            .With(b => b.Status, "Active"));

        fixture.Customize<DrillAnalyticsDetailVm>(c => c
            .With(b => b.ConfiguredProfileCount, 25)
            .With(b => b.ExecutedProfileCount, 20)
            .With(b => b.ExecutedWorkflowCount, 18)
            .With(b => b.DrillAnalyticsDetailLists, new List<DrillAnalyticsDetailList>()));

        fixture.Customize<GetSlaBreachListVm>(c => c
            .With(b => b.SlaBreachCount, 5)
            .With(b => b.SlaNonBreachCount, 95)
            .With(b => b.SlaMeetingRtoCount, 90)
            .With(b => b.ActiveAlertCount, 3)
            .With(b => b.SlaImpactList, new List<GetSlaImpactListVm>()));

        fixture.Customize<BusinessImpactAnalysisVm>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.ImpactType, "High")
            .With(b => b.Count, 15)
            .With(b => b.ImpactPercentage, 85));

        fixture.Customize<SiteCountListVm>(c => c
            .With(b => b.SiteCount, 12)
            .With(b => b.TotalDrReadyCount, 10)
            .With(b => b.DrReadyCount, 8)
            .With(b => b.NotDrReadyCount, 2)
            .With(b => b.GroupBySites, new Dictionary<string, List<string>>()));

        fixture.Customize<VerifyWorkflowDetailVm>(c => c
            .With(b => b.TotalWorkflowCount, 50)
            .With(b => b.WorkflowVerifyCount, 45)
            .With(b => b.WorkflowNotVerifyCount, 5));

        fixture.Customize<GetDcMappingSitesVm>(c => c
            .With(b => b.TotalSiteCount, 8)
            .With(b => b.TotalAppCount, 25)
            .With(b => b.DcMappingSites, new List<DcMappingSite>())
            .With(b => b.DcMappingBusinessServices, new List<DcMappingBusinessService>()));

        fixture.Customize<TotalSiteDetailForOneViewListVm>(c => c
            .With(b => b.ServerTypeWithListVms, new List<ServerTypeWithListVm>()));

        fixture.Customize<BreachDetailVm>(c => c
            .With(b => b.RpoAchievedCount, 85)
            .With(b => b.RpoExceededCount, 15)
            .With(b => b.RtoAchievedCount, 90)
            .With(b => b.RtoExceededCount, 10));

        fixture.Customize<LastDrillDetailVm>(c => c
            .With(b => b.LastExecutionTime, DateTime.Now.AddDays(-1))
            .With(b => b.ProfileName, "Enterprise DR Test Profile")
            .With(b => b.Duration, "02:15:30")
            .With(b => b.Status, "Success"));

        fixture.Customize<DatalagByBusinessServiceIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Data Lag Service")
            .With(b => b.TotalBusinessFunctionCount, 20)
            .With(b => b.BusinessFunctionNotAvailable, 2)
            .With(b => b.BusinessFunctionRPOExceededCount, 3)
            .With(b => b.BusinessFunctionThresholdExceededCount, 1)
            .With(b => b.BusinessFunctionUnderRPOCount, 14)
            .With(b => b.TotalInfraObjectCount, 50)
            .With(b => b.InfraObjecNotAvailableCount, 5)
            .With(b => b.InfraRPOExceededCount, 8)
            .With(b => b.InfraThresholdExceededCount, 3)
            .With(b => b.InfraUnderRPOCount, 34));

        fixture.Customize<ComponentFailureAnalyticsDetailVm>(c => c
            .With(b => b.TotalComponent, 150)
            .With(b => b.AvailableCount, 135)
            .With(b => b.FailedCount, 15)
            .With(b => b.ComponentsAffectedToday, 8)
            .With(b => b.TotalDatabaseCount, 75)
            .With(b => b.DatabaseUpCount, 70)
            .With(b => b.DatabaseDownCount, 5)
            .With(b => b.TotalReplicationCount, 50)
            .With(b => b.ReplicationUpCount, 48)
            .With(b => b.ReplicationDownCount, 2)
            .With(b => b.ServerDtl, new List<Dictionary<string, dynamic>>()));

        fixture.Customize<GetOperationalAvailabilityAnalyticsDetailVm>(c => c
            .With(b => b.TotalBusinessServiceCount, 25)
            .With(b => b.BusinessServiceSuccessCount, 22)
            .With(b => b.BusinessServiceErrorCount, 3)
            .With(b => b.TotalInfraObjectCount, 100)
            .With(b => b.InfraObjectSuccessCount, 95)
            .With(b => b.InfraObjectErrorCount, 5)
            .With(b => b.SiteRunningListVm, new List<SiteRunningListVm>()));

        fixture.Customize<OperationalHealthSummaryDetailVm>(c => c
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Operational Health Service")
            .With(b => b.HealthyCount, 85)
            .With(b => b.UnHealthyCount, 10)
            .With(b => b.MaintenanceCount, 5));

        fixture.Customize<GetWorkflowAnalyticsDetailVm>(c => c
            .With(b => b.ExecutedWorkFlowCount, 120)
            .With(b => b.WorkFlowSuccessCount, 110)
            .With(b => b.WorkFlowErrorCount, 10)
            .With(b => b.TotalConfigured, 150));

        fixture.Customize<ItViewByBusinessServiceIdVm>(c => c
            .With(b => b.BusinessFunctionId, Guid.NewGuid().ToString())
            .With(b => b.BusinessFunctionName, "Enterprise IT Business Function")
            .With(b => b.Status, "Active")
            .With(b => b.InfraObjectDataLag, new List<ItViewInfraObjectList>()));

        fixture.Customize<OneViewEntitiesEventView>(c => c
            .With(b => b.Id, 1)
            .With(b => b.Entity, "Enterprise Entity")
            .With(b => b.Message, "Enterprise entity event occurred successfully")
            .With(b => b.LastModifiedDate, DateTime.Now.AddHours(-2))
            .With(b => b.LastModifiedBy, "Enterprise System"));

        fixture.Customize<OneViewRiskMitigationCyberSecurityView>(c => c
            .With(b => b.Status, "Secure")
            .With(b => b.ServerCount, 45)
            .With(b => b.StatusScore, 95)
            .With(b => b.StatusPercentage, "95.5%")
            .With(b => b.HealthContributionPercentage, "18.2%"));

        fixture.Customize<OneViewRiskMitigationFailedDrillView>(c => c
            .With(b => b.ActionDate, DateTime.Now.AddDays(-1))
            .With(b => b.FailedCount, 3)
            .With(b => b.FailedWorkflowOperationGroupIds, "group1,group2,group3"));

        fixture.Customize<ResilienceHealthStatusDetailVm>(c => c
            .With(b => b.InfraObjectName, "Enterprise Resilience Infrastructure")
            .With(b => b.Percentage, 97.5));

        fixture.Customize<SitePropertiesByBusinessServiceIdVm>(c => c
            .With(b => b.SiteProperties, "{\"site1\":{\"Id\":\"site1\",\"Name\":\"Primary Site\"},\"site2\":{\"Id\":\"site2\",\"Name\":\"DR Site\"}}")
            .With(b => b.ReplicationStatus, new List<int> { 1, 2 })
            .With(b => b.DROperationStatus, new List<int> { 1, 2 }));

        fixture.Customize<DashboardImpactAvailabilityDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Impact Availability Service")
            .With(b => b.TotalBusinessFunctionCount, 30)
            .With(b => b.BusinessFunctionTotalImpacted, 5)
            .With(b => b.BusinessFunctionMajorImpactCount, 2)
            .With(b => b.BusinessFunctionPartialImpactCount, 3)
            .With(b => b.BusinessFunctionUnderRPOCount, 25)
            .With(b => b.TotalInfraObjectCount, 80)
            .With(b => b.InfraTotalImpactCount, 8)
            .With(b => b.InfraMajorImpactCount, 3)
            .With(b => b.InfraPartialImpactCount, 5)
            .With(b => b.InfraUnderRPOCount, 72));

        DashboardViewListVm = fixture.Create<DashboardViewListVm>();
        DashboardViewDetailVm = fixture.Create<DashboardViewDetailVm>();
        BusinessViewPaginatedList = fixture.Create<BusinessViewPaginatedList>();
        CreateDashboardViewCommand = fixture.Create<CreateDashboardViewCommand>();
        UpdateDashboardViewCommand = fixture.Create<UpdateDashboardViewCommand>();
        DeleteDashboardViewCommand = fixture.Create<DeleteDashboardViewCommand>();
        DashboardViewNameVm = fixture.Create<DashboardViewNameVm>();
        GetByEntityIdVm = fixture.Create<GetByEntityIdVm>();
        DashboardViewByBusinessServiceIdVm = fixture.Create<DashboardViewByBusinessServiceIdVm>();
        GetDashboardViewByInfraObjectIdVm = fixture.Create<GetDashboardViewByInfraObjectIdVm>();
        DataLagStatusbyLast7DaysVm = fixture.Create<DataLagStatusbyLast7DaysVm>();
        ItViewByInfraObjectIdVm = fixture.Create<ItViewByInfraObjectIdVm>();
        DashboardViewByBusinessFunctionIdVm = fixture.Create<DashboardViewByBusinessFunctionIdVm>();
        GetDcMappingListVm = fixture.Create<GetDcMappingListVm>();
        GetServiceTopologyListVm = fixture.Create<GetServiceTopologyListVm>();
        GetItViewListVm = fixture.Create<GetItViewListVm>();
        DrillAnalyticsDetailVm = fixture.Create<DrillAnalyticsDetailVm>();
        GetSlaBreachListVm = fixture.Create<GetSlaBreachListVm>();
        BusinessImpactAnalysisVm = fixture.Create<BusinessImpactAnalysisVm>();
        SiteCountListVm = fixture.Create<SiteCountListVm>();
        VerifyWorkflowDetailVm = fixture.Create<VerifyWorkflowDetailVm>();
        GetDcMappingSitesVm = fixture.Create<GetDcMappingSitesVm>();
        TotalSiteDetailForOneViewListVm = fixture.Create<TotalSiteDetailForOneViewListVm>();
        BreachDetailVm = fixture.Create<BreachDetailVm>();
        LastDrillDetailVm = fixture.Create<LastDrillDetailVm>();
        DatalagByBusinessServiceIdVm = fixture.Create<DatalagByBusinessServiceIdVm>();
        ComponentFailureAnalyticsDetailVm = fixture.Create<ComponentFailureAnalyticsDetailVm>();
        GetOperationalAvailabilityAnalyticsDetailVm = fixture.Create<GetOperationalAvailabilityAnalyticsDetailVm>();
        OperationalHealthSummaryDetailVm = fixture.Create<OperationalHealthSummaryDetailVm>();
        GetWorkflowAnalyticsDetailVm = fixture.Create<GetWorkflowAnalyticsDetailVm>();
        ItViewByBusinessServiceIdVm = fixture.Create<ItViewByBusinessServiceIdVm>();
        OneViewEntitiesEventView = fixture.Create<OneViewEntitiesEventView>();
        OneViewRiskMitigationCyberSecurityView = fixture.Create<OneViewRiskMitigationCyberSecurityView>();
        OneViewRiskMitigationFailedDrillView = fixture.Create<OneViewRiskMitigationFailedDrillView>();
        ResilienceHealthStatusDetailVm = fixture.Create<ResilienceHealthStatusDetailVm>();
        SitePropertiesByBusinessServiceIdVm = fixture.Create<SitePropertiesByBusinessServiceIdVm>();
        DashboardImpactAvailabilityDetailVm = fixture.Create<DashboardImpactAvailabilityDetailVm>();
    }
}
