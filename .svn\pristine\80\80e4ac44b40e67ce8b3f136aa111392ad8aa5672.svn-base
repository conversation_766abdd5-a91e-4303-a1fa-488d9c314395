using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PageWidgetRepositoryTests : IClassFixture<PageWidgetFixture>
{
    private readonly PageWidgetFixture _pageWidgetFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PageWidgetRepository _repository;

    public PageWidgetRepositoryTests(PageWidgetFixture pageWidgetFixture)
    {
        _pageWidgetFixture = pageWidgetFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        
        _repository = new PageWidgetRepository(_dbContext, mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.PageWidgets.RemoveRange(_dbContext.PageWidgets);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var pageWidget = _pageWidgetFixture.PageWidgetDto;

        // Act
        var result = await _repository.AddAsync(pageWidget);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageWidget.Name, result.Name);
        Assert.Equal(pageWidget.Properties, result.Properties);
        Assert.Single(_dbContext.PageWidgets);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForNewEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageWidget";
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithSpecificName(existingName);
        await _repository.AddAsync(pageWidget);

        // Act - Check for new entity (empty id)
        var result = await _repository.IsNameExist(existingName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentPageWidget";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, string.Empty);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageWidget";
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithSpecificName(existingName);
        await _repository.AddAsync(pageWidget);

        // Act - Check for same entity (using its own id)
        var result = await _repository.IsNameExist(existingName, pageWidget.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageWidget";
        var pageWidget1 = _pageWidgetFixture.CreatePageWidgetWithSpecificName(existingName);
        var pageWidget2 = _pageWidgetFixture.CreatePageWidgetWithSpecificName("DifferentName");
        await _repository.AddAsync(pageWidget1);
        await _repository.AddAsync(pageWidget2);

        // Act - Check if name exists for different entity
        var result = await _repository.IsNameExist(existingName, pageWidget2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageWidget";
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithSpecificName(existingName);
        await _repository.AddAsync(pageWidget);

        // Act - Check with invalid GUID (should treat as new entity)
        var result = await _repository.IsNameExist(existingName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntitiesWithSelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>
        {
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget1"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget2"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget3")
        };
        await _repository.AddRangeAsync(pageWidgets);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        
        // Verify that only selected fields are populated (as per the override implementation)
        foreach (var pageWidget in result)
        {
            Assert.NotNull(pageWidget.Id);
            Assert.NotNull(pageWidget.ReferenceId);
            Assert.NotNull(pageWidget.Name);
            Assert.NotNull(pageWidget.Properties);
        }
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEntitiesInDescendingOrder()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>
        {
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget1"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget2"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget3")
        };
        
        // Add them one by one to ensure different creation times
        foreach (var pw in pageWidgets)
        {
            await _repository.AddAsync(pw);
            await Task.Delay(10); // Small delay to ensure different timestamps
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Should be ordered by Id descending (DescOrderById)
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>();
        for (int i = 0; i < 25; i++)
        {
            pageWidgets.Add(_pageWidgetFixture.CreatePageWidgetWithProperties(name: $"PageWidget_{i:D2}"));
        }
        await _repository.AddRangeAsync(pageWidgets);

        var specification = new PageWidgetFilterSpecification(string.Empty);
        
        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(25, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(3, result.TotalPages); // 25 items / 10 per page = 3 pages
        
        // Verify that only selected fields are populated
        foreach (var pageWidget in result.Data)
        {
            Assert.NotNull(pageWidget.Id);
            Assert.NotNull(pageWidget.ReferenceId);
            Assert.NotNull(pageWidget.Name);
            Assert.NotNull(pageWidget.Properties);
        }
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResult_WhenNoData()
    {
        // Arrange
        await ClearDatabase();
        var specification = new PageWidgetFilterSpecification(string.Empty);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(0, result.TotalPages);
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>
        {
            _pageWidgetFixture.CreatePageWidgetWithProperties(),
            _pageWidgetFixture.CreatePageWidgetWithProperties(),
            _pageWidgetFixture.CreatePageWidgetWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(pageWidgets);

        // Assert
        var allPageWidgets = await _repository.ListAllAsync();
        Assert.Equal(3, allPageWidgets.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        await _repository.AddAsync(pageWidget);

        pageWidget.Name = "UpdatedPageWidget";
        pageWidget.Properties = "UpdatedProperties";

        // Act
        var result = await _repository.UpdateAsync(pageWidget);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedPageWidget", result.Name);
        Assert.Equal("UpdatedProperties", result.Properties);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        await _repository.AddAsync(pageWidget);

        // Act
        var result = await _repository.DeleteAsync(pageWidget);

        // Assert
        Assert.NotNull(result);
        var allPageWidgets = await _repository.ListAllAsync();
        Assert.Empty(allPageWidgets);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        await _repository.AddAsync(pageWidget);

        // Act
        var result = await _repository.GetByReferenceIdAsync(pageWidget.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageWidget.ReferenceId, result.ReferenceId);
        Assert.Equal(pageWidget.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties(name: $"ConcurrentPageWidget_{i}");
            tasks.Add(_repository.AddAsync(pageWidget));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allPageWidgets = await _repository.ListAllAsync();
        Assert.Equal(10, allPageWidgets.Count);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleCaseSensitivity()
    {
        // Arrange
        await ClearDatabase();
        var originalName = "PageWidgetName";
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithSpecificName(originalName);
        await _repository.AddAsync(pageWidget);

        // Act - Check with different case
        var resultLowerCase = await _repository.IsNameExist(originalName.ToLower(), string.Empty);
        var resultUpperCase = await _repository.IsNameExist(originalName.ToUpper(), string.Empty);

        // Assert - Behavior depends on database collation, but method should not throw
        Assert.IsType<bool>(resultLowerCase);
        Assert.IsType<bool>(resultUpperCase);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_WhenUpdatingMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>
        {
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget1"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget2"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "PageWidget3")
        };
        await _repository.AddRangeAsync(pageWidgets);

        // Act - Update all entities
        foreach (var pageWidget in pageWidgets)
        {
            pageWidget.Properties = "UpdatedProperties";
            await _repository.UpdateAsync(pageWidget);
        }

        // Assert
        var allPageWidgets = await _repository.ListAllAsync();
        Assert.Equal(3, allPageWidgets.Count);
        Assert.All(allPageWidgets, pw => Assert.Equal("UpdatedProperties", pw.Properties));
    }

    [Fact]
    public async Task Repository_ShouldHandleLargeDataSet()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>();

        // Create 100 page widgets
        for (int i = 0; i < 100; i++)
        {
            pageWidgets.Add(_pageWidgetFixture.CreatePageWidgetWithProperties(name: $"PageWidget_{i:D3}"));
        }

        await _repository.AddRangeAsync(pageWidgets);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInName()
    {
        // Arrange
        await ClearDatabase();
        var specialName = "<EMAIL>";
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithSpecificName(specialName);

        // Act
        var addedPageWidget = await _repository.AddAsync(pageWidget);
        var nameExists = await _repository.IsNameExist(specialName, string.Empty);

        // Assert
        Assert.NotNull(addedPageWidget);
        Assert.Equal(specialName, addedPageWidget.Name);
        Assert.True(nameExists);
    }

    [Fact]
    public async Task Repository_ShouldHandleEntityWithNullProperties()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        pageWidget.Properties = null;

        // Act & Assert - Should not throw
        var result = await _repository.AddAsync(pageWidget);
        Assert.NotNull(result);
        Assert.Null(result.Properties);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleEmptyAndNullNames()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.IsNameExist(string.Empty, string.Empty);
        var resultNull = await _repository.IsNameExist(null, string.Empty);

        Assert.IsType<bool>(resultEmpty);
        Assert.IsType<bool>(resultNull);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleSpecificationFiltering()
    {
        // Arrange
        await ClearDatabase();
        var pageWidgets = new List<PageWidget>
        {
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "FilteredWidget1"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "FilteredWidget2"),
            _pageWidgetFixture.CreatePageWidgetWithProperties(name: "OtherWidget")
        };
        await _repository.AddRangeAsync(pageWidgets);

        var specification = new PageWidgetFilterSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        // The actual filtering behavior depends on the PageWidgetFilterSpecification implementation
        Assert.True(result.Data.Count <= 3);
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnCorrectResult_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        await ClearDatabase();
        var sameName = "DuplicateName";
        var pageWidgets = _pageWidgetFixture.CreateMultiplePageWidgetsWithSameName(sameName, 3);
        await _repository.AddRangeAsync(pageWidgets);

        // Act
        var result = await _repository.IsNameExist(sameName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldOnlyReturnSelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        pageWidget.CreatedBy = "TestUser";
        pageWidget.CreatedDate = DateTime.UtcNow;

        await _repository.AddAsync(pageWidget);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var returnedWidget = result.First();
        // Verify that only selected fields are returned (Id, ReferenceId, Name, Properties)
        Assert.NotNull(returnedWidget.Id);
        Assert.NotNull(returnedWidget.ReferenceId);
        Assert.NotNull(returnedWidget.Name);
        Assert.NotNull(returnedWidget.Properties);

        // Other fields should be default values since they're not selected in the projection
        Assert.Null(returnedWidget.CreatedBy);
        Assert.Equal(default(DateTime), returnedWidget.CreatedDate);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldOnlyReturnSelectedFields()
    {
        // Arrange
        await ClearDatabase();
        var pageWidget = _pageWidgetFixture.CreatePageWidgetWithProperties();
        pageWidget.CreatedBy = "TestUser";
        pageWidget.CreatedDate = DateTime.UtcNow;

        await _repository.AddAsync(pageWidget);

        var specification = new PageWidgetFilterSpecification(string.Empty);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);

        var returnedWidget = result.Data.First();
        // Verify that only selected fields are returned (Id, ReferenceId, Name, Properties)
        Assert.NotNull(returnedWidget.Id);
        Assert.NotNull(returnedWidget.ReferenceId);
        Assert.NotNull(returnedWidget.Name);
        Assert.NotNull(returnedWidget.Properties);

        // Other fields should be default values since they're not selected in the projection
        Assert.Null(returnedWidget.CreatedBy);
        Assert.Equal(default(DateTime), returnedWidget.CreatedDate);
    }

    #endregion
}
