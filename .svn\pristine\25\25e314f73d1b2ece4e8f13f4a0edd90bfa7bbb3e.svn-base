﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Events;

public class CreateInfraObjectSchedulerEventTests : IClassFixture<InfraObjectSchedulerFixture>, IClassFixture<UserActivityFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly InfraObjectSchedulerCreatedEventHandler _handler;

    public CreateInfraObjectSchedulerEventTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture, UserActivityFixture userActivityFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockInfraObjectSchedulerEventLogger = new Mock<ILogger<InfraObjectSchedulerCreatedEventHandler>>();

        _mockUserActivityRepository = InfraObjectSchedulerRepositoryMocks.CreateInfraObjectSchedulerEventRepository(_userActivityFixture.UserActivities);

        _handler = new InfraObjectSchedulerCreatedEventHandler(mockLoggedInUserService.Object, _mockUserActivityRepository.Object, mockInfraObjectSchedulerEventLogger.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateInfraObjectSchedulerEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_infraObjectSchedulerFixture.InfraObjectSchedulerCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_infraObjectSchedulerFixture.InfraObjectSchedulerCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}