﻿using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class DataSetColumnsService : BaseService, IDataSetColumnsService
{
    public DataSetColumnsService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DataSetColumnsColumnNameVm>> GetColumnNamesBySchemaNameAndTableName(string schemaName,
        string tableName)
    {
        Logger.LogDebug("Get Column Names By Table Name");

        return await Mediator.Send(new GetDataSetColumnsColumnNameQuery { DBName = schemaName, TableName = tableName });
    }
}