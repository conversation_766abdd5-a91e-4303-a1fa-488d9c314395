﻿namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetByInfraObjectAndActionId;

public class GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler : IRequestHandler<
    GetWorkflowActionResultByInfraObjectAndActionIdQuery, WorkflowActionResultByInfraObjectAndActionIdVm>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;

    public GetWorkflowActionResultByInfraObjectAndActionIdQueryHandler(
        IWorkflowActionResultRepository workflowActionResultRepository, IMapper mapper)
    {
        _workflowActionResultRepository = workflowActionResultRepository;
        _mapper = mapper;
    }

    public async Task<WorkflowActionResultByInfraObjectAndActionIdVm> Handle(
        GetWorkflowActionResultByInfraObjectAndActionIdQuery request, CancellationToken cancellationToken)
    {
        var actionResult =
            await _workflowActionResultRepository.GetByInfraObjectAndActionId(request.InfraobjectId, request.ActionId);

        Guard.Against.NullOrDeactive(actionResult, nameof(Domain.Entities.WorkflowActionResult),
            new NotFoundException(nameof(Domain.Entities.WorkflowActionResult), request.ActionId));

        return _mapper.Map<WorkflowActionResultByInfraObjectAndActionIdVm>(actionResult);
    }
}