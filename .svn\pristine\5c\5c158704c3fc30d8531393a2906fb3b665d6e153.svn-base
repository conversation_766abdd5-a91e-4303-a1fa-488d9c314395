﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ComponentType.Events.Update;

public class ComponentTypeUpdatedEventHandler : INotificationHandler<ComponentTypeUpdatedEvent>
{
    private readonly ILogger<ComponentTypeUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ComponentTypeUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<ComponentTypeUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ComponentTypeUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.ComponentType.ToString(),
            Action = $"{ActivityType.Update} {Modules.ComponentType}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ComponentType '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ComponentType '{updatedEvent.Name}' updated successfully.");
    }
}