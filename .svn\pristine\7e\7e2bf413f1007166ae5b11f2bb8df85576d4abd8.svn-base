using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BulkImportOperationGroupFixture : IDisposable
{
    public List<BulkImportOperationGroup> BulkImportOperationGroupPaginationList { get; set; }
    public List<BulkImportOperationGroup> BulkImportOperationGroupList { get; set; }
    public BulkImportOperationGroup BulkImportOperationGroupDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public BulkImportOperationGroupFixture()
    {
        var fixture = new Fixture();

        BulkImportOperationGroupList = fixture.Create<List<BulkImportOperationGroup>>();

        BulkImportOperationGroupPaginationList = fixture.CreateMany<BulkImportOperationGroup>(20).ToList();

        BulkImportOperationGroupPaginationList.ForEach(x => x.CompanyId = CompanyId);
        BulkImportOperationGroupPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BulkImportOperationGroupPaginationList.ForEach(x => x.IsActive = true);

        BulkImportOperationGroupList.ForEach(x => x.CompanyId = CompanyId);
        BulkImportOperationGroupList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BulkImportOperationGroupList.ForEach(x => x.IsActive = true);

        BulkImportOperationGroupDto = fixture.Create<BulkImportOperationGroup>();
        BulkImportOperationGroupDto.CompanyId = CompanyId;
        BulkImportOperationGroupDto.ReferenceId = Guid.NewGuid().ToString();
        BulkImportOperationGroupDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
