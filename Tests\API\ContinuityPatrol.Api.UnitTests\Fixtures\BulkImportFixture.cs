using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BulkImportFixture
{
    public CreateBulkImportCommand CreateBulkImportCommand { get; }
    public NextBulkImportCommand NextBulkImportCommand { get; }
    public RollBackBulkImportCommand RollBackBulkImportCommand { get; }

    public BulkImportFixture()
    {
        var fixture = new Fixture();

        // Create command for creating BulkImport
        CreateBulkImportCommand = new CreateBulkImportCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        // Create command for next BulkImport action
        NextBulkImportCommand = new NextBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };

        // Create command for rollback BulkImport action
        RollBackBulkImportCommand = new RollBackBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };
    }
}
