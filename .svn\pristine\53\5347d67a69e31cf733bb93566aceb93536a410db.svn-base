namespace ContinuityPatrol.Domain.ViewModels.RtoModel;

public record RtoListVm
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public int TotalBusinessFunction { get; set; }
    public int BFAvailable { get; set; }
    public int BFImpact { get; set; }
    public int BFUnderRTO { get; set; }
    public int BFAboveRTO { get; set; }
    public int BFNotAvailable { get; set; }
    public int BFDrillNotExecuted { get; set; }
    public int TotalInfraObject { get; set; }
    public int InfraAvailable { get; set; }
    public int InfraImpact { get; set; }
    public int InfraUnderRTO { get; set; }
    public int InfraAboveRTO { get; set; }
    public int InfraNotAvailable { get; set; }
    public int InfraDrillNotExecuted { get; set; }
}