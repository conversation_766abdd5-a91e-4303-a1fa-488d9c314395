﻿using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMysqlMonitorLogsService
{
    Task<BaseResponse> CreateAsync(CreateMYSQLMonitorLogCommand createMysqlMonitorLogCommand);
    Task<List<MYSQLMonitorLogsListVm>> GetAllMYSQLMonitorLogs();
    Task<MYSQLMonitorLogsDetailVm> GetByReferenceId(string id);
    Task<List<MYSQLMonitorLogsDetailByTypeVm>> GetMYSQLMonitorLogsByType(string type);
    Task<PaginatedResult<MYSQLMonitorLogsListVm>> GetPaginatedMYSQLMonitorLogs(GetMYSQLMonitorLogsPaginatedListQuery query);
}