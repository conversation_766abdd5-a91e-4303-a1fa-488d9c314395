using ContinuityPatrol.Application.Features.MenuBuilder.Events.Delete;

namespace ContinuityPatrol.Application.Features.MenuBuilder.Commands.Delete;

public class DeleteMenuBuilderCommandHandler : IRequestHandler<DeleteMenuBuilderCommand, DeleteMenuBuilderResponse>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;
    private readonly IPublisher _publisher;

    public DeleteMenuBuilderCommandHandler(IMenuBuilderRepository menuBuilderRepository, IPublisher publisher)
    {
        _menuBuilderRepository = menuBuilderRepository;

        _publisher = publisher;
    }

    public async Task<DeleteMenuBuilderResponse> Handle(DeleteMenuBuilderCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _menuBuilderRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.MenuBuilder),
            new NotFoundException(nameof(Domain.Entities.MenuBuilder), request.Id));

        eventToDelete.IsActive = false;

        await _menuBuilderRepository.UpdateAsync(eventToDelete);

        var response = new DeleteMenuBuilderResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.MenuBuilder), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new MenuBuilderDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}
