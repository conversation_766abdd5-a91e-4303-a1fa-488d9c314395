﻿namespace ContinuityPatrol.Domain.ViewModels.ServerModel
{
    public class ServerReportVm
    {
        public string Name { get; set; }
        public string ServerType { get; set; }
        public string IpAddress { get; set; }
        public string ConHost { get; set; }
        public string OSType { get; set; }
        public string AuthType { get; set; }
        public string SSHUser { get; set; }
        public string Status { get; set; }
        public string SubAuth { get; set; }
        public string SubType { get; set; }
        public string IsAttached { get; set; }
        public string ExceptionMsg { get; set; }
    }
}
