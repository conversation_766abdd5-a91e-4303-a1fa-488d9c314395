﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class LoadBalancerRepositoryMocks 
{
    public static Mock<ILoadBalancerRepository> CreateLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(loadBalancers);

        mockLoadBalancerRepository.Setup(repo => repo.AddAsync(It.IsAny<LoadBalancer>())).ReturnsAsync(
            (LoadBalancer loadBalancer) =>
            {
                loadBalancer.Id = new Fixture().Create<int>();

                loadBalancer.ReferenceId = new Fixture().Create<Guid>().ToString();

                loadBalancers.Add(loadBalancer);

                return loadBalancer;
            });

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> UpdateLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(loadBalancers);

        mockLoadBalancerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => loadBalancers.SingleOrDefault(x => x.ReferenceId == i));

        mockLoadBalancerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LoadBalancer>())).ReturnsAsync((LoadBalancer loadBalancer) =>
        {
            var index = loadBalancers.FindIndex(item => item.Id == loadBalancer.Id);

            loadBalancers[index] = loadBalancer;

            return loadBalancer;
        });

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> DeleteLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(loadBalancers);

        mockLoadBalancerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => loadBalancers.SingleOrDefault(x => x.ReferenceId == i));

        mockLoadBalancerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<LoadBalancer>())).ReturnsAsync((LoadBalancer loadBalancer) =>
        {
            var index = loadBalancers.FindIndex(item => item.Id == loadBalancer.Id);

            loadBalancer.IsActive = false;

            loadBalancers[index] = loadBalancer;

            return loadBalancer;
        });

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> GetLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(loadBalancers);

        mockLoadBalancerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => loadBalancers.SingleOrDefault(x => x.ReferenceId == i));

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> GetLoadBalancerEmptyRepository()
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<LoadBalancer>());

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> GetLoadBalancerNameUniqueRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        mockLoadBalancerRepository.Setup(repo => repo.IsNodeConfigurationNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? loadBalancers.Exists(x => x.Name == i) : loadBalancers.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return mockLoadBalancerRepository;
    }

    public static Mock<ILoadBalancerRepository> GetPaginatedLoadBalancerRepository(List<LoadBalancer> loadBalancers)
    {
        var mockLoadBalancerRepository = new Mock<ILoadBalancerRepository>();

        var queryableLoadBalancer = loadBalancers.BuildMock();

        mockLoadBalancerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableLoadBalancer);

        return mockLoadBalancerRepository;
    }
}