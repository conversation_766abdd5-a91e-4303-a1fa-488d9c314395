using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Delete;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetBiaRulesByEntityId;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetList;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BiaRulesControllerTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BiaRulesController _controller;

    public BiaRulesControllerTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;

        var testBuilder = new ControllerTestBuilder<BiaRulesController>();
        _controller = testBuilder.CreateController(
            _ => new BiaRulesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBiaImpacts_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
     

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBiaRulesListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_biaRulesFixture.BiaRulesListVm);

        // Act
        var result = await _controller.GetBiaImpacts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var biaRules = Assert.IsAssignableFrom<List<BiaRulesListVm>>(okResult.Value);
        Assert.Equal(3, biaRules.Count);
    }

    [Fact]
    public async Task GetBiaImpacts_ReturnsEmptyList_WhenNoBiaRulesExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBiaRulesListQuery>(), default))
            .ReturnsAsync(new List<BiaRulesListVm>());

        // Act
        var result = await _controller.GetBiaImpacts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var biaRules = Assert.IsAssignableFrom<List<BiaRulesListVm>>(okResult.Value);
        Assert.Empty(biaRules);
    }

    [Fact]
    public async Task GetBiaImpactById_ReturnsBiaRules_WhenIdIsValid()
    {
        // Arrange
        var biaRulesId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBiaRulesDetailQuery>(q => q.Id == biaRulesId), default))
            .ReturnsAsync(_biaRulesFixture.BiaRulesDetailVm);

        // Act
        var result = await _controller.GetBiaImpactById(biaRulesId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var biaRules = Assert.IsType<BiaRulesDetailVm>(okResult.Value);
        Assert.NotNull(biaRules);
    }

    [Fact]
    public async Task GetBiaImpactById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBiaImpactById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBiaImpact_Returns201Created()
    {
        // Arrange
        var command = _biaRulesFixture.CreateBiaRulesCommand;
        var expectedMessage = $"BiaImpact '{command.Description}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBiaRulesResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateBiaImpact(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBiaRulesResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBiaImpact_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"BiaImpact '{_biaRulesFixture.UpdateBiaRulesCommand.Description}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBiaRulesCommand>(), default))
            .ReturnsAsync(new UpdateBiaRulesResponse
            {
                Message = expectedMessage,
                Id = _biaRulesFixture.UpdateBiaRulesCommand.Id
            });

        // Act
        var result = await _controller.UpdateBiaImpact(_biaRulesFixture.UpdateBiaRulesCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBiaRulesResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBiaImpact_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "BiaImpact 'Test Rule' has been deleted successfully!.";
        var biaRulesId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBiaRulesCommand>(c => c.Id == biaRulesId), default))
            .ReturnsAsync(new DeleteBiaRulesResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteBiaImpact(biaRulesId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBiaRulesResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedBiaImpacts_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetBiaRulesPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _biaRulesFixture.BiaRulesListVm;
        var expectedPaginatedResult = PaginatedResult<BiaRulesListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBiaRulesPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedBiaImpacts(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<BiaRulesListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BiaRulesListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetBiaRulesByEntityIdAndType_ReturnsExpectedResult()
    {
        // Arrange
        var entityId = Guid.NewGuid().ToString();
        var type = "Recovery";
        var expectedResult = _biaRulesFixture.BiaRulesListVm.First();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBiaRulesByEntityIdQuery>(q => q.EntityId == entityId && q.Type == type), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetBiaRulesByEntityIdAndType(entityId, type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var biaRule = Assert.IsType<BiaRulesListVm>(okResult.Value);
        Assert.NotNull(biaRule);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }
}
