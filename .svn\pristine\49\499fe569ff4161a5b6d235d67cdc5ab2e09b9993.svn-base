﻿function monitorTypeDB2HADR(value, infraObjectName, moniterType, parsedData) {
   
    let monitor = value?.monitorServiceDetails;

    
    const getDRDetails = (data, value, obj = null) => {

        let tdHtml = '';
        data.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item[obj][value] : item[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        let iconClass = '';

        if (value == 'DR_Server_HostName') {
            let text = monitoringData?.DR_Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'DR_Server_IpAddress') {
            let text = monitoringData?.DR_Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'DR_Database') {

            iconClass = monitoringData?.DR_Database ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'DRDatabaseStatus') {
            iconClass = monitoringData?.HADResult?.DRDatabaseStatus?.toLowerCase()?.includes("active") ? "cp-success me-1 text-success" : monitoringData?.HADResult?.DRDatabaseStatus?.toLowerCase() ==="standby" ? "text-danger me-1 cp-end" : "cp-disable me-1 text-danger";

        } else if (value === 'DRLogFile') {
            iconClass = monitoringData?.HADResult?.DRLogFile ? "cp-log-file-name me-1 fs-6 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'DRLSN') {
            iconClass = monitoringData?.HADResult?.DRLSN ? "cp-monitoring me-1 fs-6 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'DRTimestamp') {
            iconClass = monitoringData?.HADResult?.DRTimestamp ? "cp-time me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'HRole') {
            iconClass = monitoringData?.HADRReplications?.HRole ? "cp-database-role me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'State') {
            iconClass = monitoringData?.HADRReplications?.State ? "cp-relationship-state me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'SyncMode') {
            iconClass = monitoringData?.HADRReplications?.SyncMode ? "cp-data-sync me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'ConnectionStatus') {
            iconClass = monitoringData?.HADRReplications?.ConnectionStatus ? "cp-connection-sucess me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'HeartbeatsMissed') {
            iconClass = monitoringData?.HADRReplications?.HeartbeatsMissed ? "cp-Job-status me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LocalHost') {
            iconClass = monitoringData?.HADRReplications?.LocalHost ? "cp-host-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LocalService') {
            iconClass = monitoringData?.HADRReplications?.LocalService ? "cp-monitoring-services me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'RemoteHost') {
            iconClass = monitoringData?.HADRReplications?.RemoteHost ? "cp-host-name me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'RemoteService') {
            iconClass = monitoringData?.HADRReplications?.RemoteService ? "cp-monitoring-services me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'Timeout') {
            iconClass = monitoringData?.HADRReplications?.Timeout ? "cp-Timeout me-1 text-primary" : "cp-disable me-1 text-danger";

        } else if (value === 'LogGap') {
            iconClass = monitoringData?.HADRReplications?.LogGap ? "cp-Timeout me-1 text-primary" : "cp-disable me-1 text-danger";

        }

        return iconClass;
    }

    const getDynamicHeader = (DB2HADRMonitoringModels) => {

        let dynamicHeader = '';

        DB2HADRMonitoringModels?.length && DB2HADRMonitoringModels?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "DB2HADR") {
       
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let prreplicationStatus = parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus?.toLowerCase()?.includes("active") ? "cp-success me-1 text-success" : parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus?.toLowerCase()==="standby" ? "text-danger me-1 cp-end" : "cp-disable me-1 text-danger";
        let drreplicationStatus = parsedData?.HADResult?.DRDatabaseStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.HADResult?.DRDatabaseStatus?.toLowerCase()?.includes("active") ? "cp-success me-1 text-success" : parsedData?.HADResult?.DRDatabaseStatus?.toLowerCase()==="standby" ? "text-danger me-1 cp-end" : "cp-disable me-1 text-danger";
        let prrecoverystate = parsedData?.PR_RecoveryStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PR_RecoveryStatus === "t" ? "cp-success me-1 text-success" : "cp-error me-1 text-danger";
        let drrecoverystate = parsedData?.DR_RecoveryStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.DR_RecoveryStatus === "t" ? "cp-success me-1 text-success" : "cp-error me-1 text-danger";
        let pripaddress = value?.prServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" : (value?.prServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";
        let dripaddress = value?.drServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" : (value?.drServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";
      //  let ipprdata = parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.Pr_ConnectViaHostName.toLowerCase() === "true" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_HostName : parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_IpAddress
        let prop = value?.properties;
        let propDataa = JSON?.parse(prop);

        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm ">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.DB2HADRMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + 'Server Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress +'"></i>' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_HostName !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_HostName !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_HostName !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_HostName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DR_Server_HostName')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'IP Address/Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel.PR_Server_IpAddress !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_IpAddress !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_IpAddress !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Server_IpAddress : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DR_Server_IpAddress')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="cp-database me-1 text-primary"></i>' + (propDataa?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Database !== undefined && propDataa?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Database !== null && propDataa?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Database !== "" ? propDataa?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PR_Database : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DR_Database')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Database Status" + '</td>' +
            '<td>' + '<i class="' + prreplicationStatus + '" ></i >' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRDatabaseStatus : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DRDatabaseStatus','HADResult')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Last Log Generated / Last Log Applied" + '</td>' +
            '<td>' + '<i class="cp-log-file-name me-1 fs-6 text-primary"></i>' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLogFile !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLogFile !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLogFile !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLogFile : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DRLogFile', 'HADResult')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "LSN" + '</td>' +
            '<td>' + '<i class="cp-monitoring me-1 fs-6 text-primary"></i>' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLSN !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLSN !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLSN !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRLSN : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DRLSN', 'HADResult')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Time Stamp" + '</td>' +
            '<td>' + '<i class="cp-time me-1 text-primary"></i>' + (parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRTimestamp !== undefined && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRTimestamp !== null && parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRTimestamp !== "" ? parsedData?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult?.PRTimestamp : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'DRTimestamp', 'HADResult')}` +
            '</tr>';
       
            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, monitor);
            infraobjectdata += '</tbody>' +
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th></th>' +
            `${getDynamicHeader(parsedData?.DB2HADRMonitoringModels)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="">' +
            '<tr>' +
            '<td>' + "HADR Role" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'HRole', 'HADRReplications')}` +           
            '</tr>' +
            '<tr>' +
            '<td>' + "HADR State" + '</td>' +
             `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'State', 'HADRReplications')}` +           
            '<td>' + "Sync Mode" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'SyncMode', 'HADRReplications')}` +            
            '</tr>' +
            '<tr>' +
            '<td>' + "Connection Status" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'ConnectionStatus', 'HADRReplications')}` + 
            '<td>' + "Heart Beat Status" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'HeartbeatsMissed', 'HADRReplications')}` + 
            '</tr>' +
            '<tr>' +
            '<td>' + "Local Host" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'LocalHost', 'HADRReplications')}` + 
            '<td>' + "Local Service" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'LocalService', 'HADRReplications')}` + 
            '</tr>' +
            '<tr>' +
            '<td>' + "Remote Host" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'RemoteHost', 'HADRReplications')}` +
            '<td>' + "Remote Service" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'RemoteService', 'HADRReplications')}` +
            '</tr>' +
            '<tr>' +
            '<td>' + "Timeout" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'Timeout', 'HADRReplications')}` +
            '<td>' + "LogGap" + '</td>' +
            `${getDRDetails(parsedData?.DB2HADRMonitoringModels, 'LogGap', 'HADRReplications')}` +
            '</tr>' +
            '</tbody>' +
            '</table>' +
            '</div>' 
            


        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
}
