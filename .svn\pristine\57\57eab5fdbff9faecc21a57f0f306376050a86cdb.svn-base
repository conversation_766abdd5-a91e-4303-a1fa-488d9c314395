﻿namespace ContinuityPatrol.Application.Features.UserRole.Queries.GetRoleUnique;

public class GetUserRoleNameUniqueQueryHandler : IRequestHandler<GetUserRoleNameUniqueQuery, bool>
{
    private readonly IUserRoleRepository _userRoleRepository;

    public GetUserRoleNameUniqueQueryHandler(IUserRoleRepository userRoleRepository)
    {
        _userRoleRepository = userRoleRepository;
    }

    public async Task<bool> Handle(GetUserRoleNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _userRoleRepository.IsUserRoleNameExist(request.Role, request.Id);
    }
}