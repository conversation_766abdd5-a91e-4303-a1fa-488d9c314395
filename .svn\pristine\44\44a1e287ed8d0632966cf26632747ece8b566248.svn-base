using ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Create;

namespace ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;

public class
    CreateFiaImpactCategoryCommandHandler : IRequestHandler<CreateFiaImpactCategoryCommand,
        CreateFiaImpactCategoryResponse>
{
    private readonly IFiaImpactCategoryRepository _fiaImpactCategoryRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateFiaImpactCategoryCommandHandler(IMapper mapper,
        IFiaImpactCategoryRepository fiaImpactCategoryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _fiaImpactCategoryRepository = fiaImpactCategoryRepository;
    }

    public async Task<CreateFiaImpactCategoryResponse> Handle(CreateFiaImpactCategoryCommand request,
        CancellationToken cancellationToken)
    {
        var fiaImpactCategory = _mapper.Map<Domain.Entities.FiaImpactCategory>(request);

        fiaImpactCategory = await _fiaImpactCategoryRepository.AddAsync(fiaImpactCategory);

        var response = new CreateFiaImpactCategoryResponse
        {
            Message = Message.Create(nameof(Domain.Entities.FiaImpactCategory), fiaImpactCategory.Name),

            Id = fiaImpactCategory.ReferenceId
        };

        await _publisher.Publish(new FiaImpactCategoryCreatedEvent { Name = fiaImpactCategory.Name },
            cancellationToken);

        return response;
    }
}