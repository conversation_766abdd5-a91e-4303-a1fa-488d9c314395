﻿//using ContinuityPatrol.Domain.ViewModels.AlertModel;
//using ContinuityPatrol.Shared.Core.Contracts.Identity;
//using ContinuityPatrol.Shared.Core.Domain;
//using Newtonsoft.Json;

//namespace ContinuityPatrol.Application.Features.Alert.Queries.GetDetail;

//public class GetAlertDetailQueryHandler : IRequestHandler<GetAlertDetailQuery, List<AlertListVm>>
//{
//    private readonly IAlertRepository _alertRepository;
//    private readonly ILoggedInUserService _loggedInUserService;
//    private readonly IMapper _mapper;
//    private readonly IUserLoginRepository _userLoginRepository;

//    public GetAlertDetailQueryHandler(IAlertRepository alertRepository, IMapper mapper,
//        ILoggedInUserService loggedInUserService, IUserLoginRepository userLoginRepository)
//    {
//        _mapper = mapper;
//        _alertRepository = alertRepository;
//        _loggedInUserService = loggedInUserService;
//        _userLoginRepository = userLoginRepository;
//    }

//    public async Task<List<AlertListVm>> Handle(GetAlertDetailQuery request, CancellationToken cancellationToken)
//    {
//        var userLogin = await _userLoginRepository.GetUserLoginByUserId(_loggedInUserService.UserId);

//        var alert = await _alertRepository.GetByReferenceIdAsync(request.Id);

//        var userAlertId = await _alertRepository.GetByReferenceIdAsync(userLogin.LastAlertId);

//        List<AlertListVm> alertListVm;

//        if (_loggedInUserService.IsAllInfra)
//        {
//            var alerts = await _alertRepository.GetAlertByUserLastAlertId(userAlertId.Id, alert.CreatedDate);

//            alertListVm = alerts.Count == 0 ? new List<AlertListVm>() : _mapper.Map<List<AlertListVm>>(alerts);

//            userLogin.LastAlertId = alerts.LastOrDefault()?.ReferenceId;

//            await _userLoginRepository.UpdateAsync(userLogin);
//        }
//        else
//        {
//            var deserializeValue = JsonConvert.DeserializeObject<AssignedEntity>(_loggedInUserService.AssignedInfras);

//            var assignedInfra = deserializeValue.AssignedBusinessServices
//                .SelectMany(x => x.AssignedBusinessFunctions
//                    .SelectMany(infra => infra.AssignedInfraObjects))
//                .ToList();

//            var assignedInfraObjects = assignedInfra.Where(y => y.Id.Equals(alert.InfraObjectId)).ToList();

//            var alertDto = new List<Domain.Entities.Alert>();

//            foreach(var assignInfra in assignedInfraObjects)
//            {
//                var listOfAlertCount = await _alertRepository
//                    .GetAlertByUserLastInfraObjectId(assignInfra.Id, alert.CreatedDate);
//                alertDto.AddRangeAsync(listOfAlertCount);
//            };

//            alertListVm = alertDto.Count == 0 ? new List<AlertListVm>() : _mapper.Map<List<AlertListVm>>(alertDto);

//            userLogin.LastAlertId = alertDto.LastOrDefault()?.ReferenceId;

//            await _userLoginRepository.UpdateAsync(userLogin);
//        }

//        return alertListVm;
//    }
//}

