﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Application.Features.ComponentType.Events.Create;
using ContinuityPatrol.Application.Features.ComponentType.Events.Delete;
using ContinuityPatrol.Application.Features.ComponentType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ComponentType.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ComponentTypeFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ComponentType> ComponentTypes { get; set; }
    public CreateComponentTypeCommand CreateComponentTypeCommand { get; set; }
    public UpdateComponentTypeCommand UpdateComponentTypeCommand { get; set; }
    public ComponentTypeCreatedEvent ComponentTypeCreatedEvent { get; set; }
    public ComponentTypeDeletedEvent ComponentTypeDeletedEvent { get; set; }
    public ComponentTypeUpdatedEvent ComponentTypeUpdatedEvent { get; set; }
    public ComponentTypePaginatedEvent ComponentTypePaginatedEvent { get; set; }

    public ComponentTypeFixture()
    {
        ComponentTypes = AutoServerTypeFixture.Create<List<ComponentType>>();

        CreateComponentTypeCommand = AutoServerTypeFixture.Create<CreateComponentTypeCommand>();

        UpdateComponentTypeCommand = AutoServerTypeFixture.Create<UpdateComponentTypeCommand>();

        ComponentTypeCreatedEvent = AutoServerTypeFixture.Create<ComponentTypeCreatedEvent>();

        ComponentTypeDeletedEvent = AutoServerTypeFixture.Create<ComponentTypeDeletedEvent>();

        ComponentTypeUpdatedEvent = AutoServerTypeFixture.Create<ComponentTypeUpdatedEvent>();

        ComponentTypePaginatedEvent = AutoServerTypeFixture.Create<ComponentTypePaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ComponentTypeProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoServerTypeFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateComponentTypeCommand>(p => p.ComponentName, 10));
            fixture.Customize<CreateComponentTypeCommand>(c => c.With(b => b.Properties));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateComponentTypeCommand>(p => p.ComponentName, 10));
            fixture.Customize<UpdateComponentTypeCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<ComponentType>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ComponentTypeCreatedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ComponentTypeDeletedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ComponentTypeUpdatedEvent>(p => p.Name, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ComponentTypePaginatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }

}
