﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IAzureStorageAccountMonitorLogsRepository : IRepository<AzureStorageAccountMonitorlogs>
{
    Task<List<AzureStorageAccountMonitorlogs>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
    Task<List<AzureStorageAccountMonitorlogs>> GetDetailByType(string type);
}