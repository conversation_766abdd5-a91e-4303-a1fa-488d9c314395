﻿using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceAvailability.Queries;

public class GetBusinessServiceAvailabilityListQueryHandlerTests : IClassFixture<BusinessServiceAvailabilityFixture>
{
    private readonly BusinessServiceAvailabilityFixture _businessServiceAvailabilityFixture;
    private Mock<IBusinessServiceAvailabilityRepository> _mockBusinessServiceAvailabilityRepository;
    private readonly GetBusinessServiceAvailabilityListQueryHandler _handler;

    public GetBusinessServiceAvailabilityListQueryHandlerTests(BusinessServiceAvailabilityFixture businessServiceAvailabilityFixture)
    {
        _businessServiceAvailabilityFixture = businessServiceAvailabilityFixture;

        _mockBusinessServiceAvailabilityRepository = BusinessServiceAvailabilityRepositoryMocks.GetBusinessServiceAvailabilityRepository(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities);

        _handler = new GetBusinessServiceAvailabilityListQueryHandler(businessServiceAvailabilityFixture.Mapper, _mockBusinessServiceAvailabilityRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_BusinessServiceAvailabilitiesCount()
    {
        var result = await _handler.Handle(new GetBusinessServiceAvailabilityListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceAvailabilityListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetBusinessServiceAvailabilityListQuery(), CancellationToken.None);

        _mockBusinessServiceAvailabilityRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_BusinessServiceAvailabilitiesDetail()
    {
        _businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].DRReadinessUp = 0;
        _businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].DRReadinessDown = 0;

        var result = await _handler.Handle(new GetBusinessServiceAvailabilityListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceAvailabilityListVm>>();

        result[0].Id.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].ReferenceId);
        result[0].TotalBusinessService.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].TotalBusinessService);
        result[0].AvailabilityUp.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].AvailabilityUp);
        result[0].AvailabilityDown.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].AvailabilityDown);
        result[0].HealthUp.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].HealthUp);
        result[0].HealthDown.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].HealthDown);
        result[0].DRReadynessUp.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].DRReadinessUp);
        result[0].DRReadynessDown.ShouldBe(_businessServiceAvailabilityFixture.BusinessServiceAvailabilities[0].DRReadinessDown);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessServiceAvailabilityRepository = BusinessServiceAvailabilityRepositoryMocks.GetBusinessServiceAvailabilityEmptyRepository();

        var handler = new GetBusinessServiceAvailabilityListQueryHandler(_businessServiceAvailabilityFixture.Mapper, _mockBusinessServiceAvailabilityRepository.Object);

        var result = await handler.Handle(new GetBusinessServiceAvailabilityListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}