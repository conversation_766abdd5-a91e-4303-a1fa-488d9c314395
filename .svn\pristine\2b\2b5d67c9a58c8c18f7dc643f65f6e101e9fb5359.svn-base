﻿let infraId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { monitoringServer(infraId) }, 250)
let globalMSSQLServerData = [];
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}

function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";
    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") return "text-success cp-reload cp-animate";
    if (lowerStatus === "error" || lowerStatus === "stopped") return "text-danger cp-fail-back";
    return "text-danger cp-disable";
}

function bindMonitoringServices(mssqlServerData, selectedType = 'DR') {
    let prData = null;
    let selectedData = null;

    mssqlServerData?.forEach(item => {
        let parsed = [];

        try {
            if (item?.isServiceUpdate && item?.isServiceUpdate !== 'NA') {
                parsed = JSON?.parse(item?.isServiceUpdate);
            }
        } catch (err) {
            console.error("Invalid JSON in isServiceUpdate:", err);
        }

        parsed.forEach(service => {
            let type = service?.Type?.trim();
            if (type === 'PR') prData = service;
            if (type === selectedType) selectedData = service;
        });
    });

    // Fallback UI
    $('#prIp').html(prData
        ? `Primary (${prData?.IpAddress})<br><span class="status-summary text-muted small">${getStatusSummary(prData?.Services?.map(s => s?.Status))}</span>`
        : '--');

    $('#drIp').html(selectedData
        ? `${selectedType} (${selectedData?.IpAddress})<br><span class="status-summary text-muted small">${getStatusSummary(selectedData?.Services?.map(s => s?.Status))}</span>`
        : '--');

    $('#mssqlserverbody').empty();

    // If neither has data, exit early
    if (!prData && !selectedData) {
        $('#mssqlserver').hide();
        return;
    }

    $('#mssqlserver').show();

    let allServices = [...new Set([
        ...(prData?.Services || [])?.map(s => s?.ServiceName),
        ...(selectedData?.Services || [])?.map(s => s?.ServiceName)
    ])];

    allServices.forEach(serviceName => {
        let prStatus = prData?.Services?.find(s => s?.ServiceName === serviceName)?.Status || '--';
        let selStatus = selectedData?.Services?.find(s => s?.ServiceName === serviceName)?.Status || '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let selectedIcon = selStatus !== '--' ? `<i class="${getStatusIconClass(selStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
        $('#mssqlserverbody').append(`
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${selectedIcon}${selStatus}</td>
            </tr>
        `);
    });
}
$('#mssqlserver').hide();
async function monitoringServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);
    globalMSSQLServerData = mssqlServerData || []
    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindMonitoringServices(mssqlServerData, 'DR')
                    } else {
                        $('#mssqlserver').hide();
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }
}