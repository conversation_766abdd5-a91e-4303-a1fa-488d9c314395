﻿namespace ContinuityPatrol.Application.Features.AccessManager.Commands.Create;

public class CreateAccessManagerCommand : IRequest<CreateAccessManagerResponse>
{
    public string CompanyId { get; set; }

    public string RoleId { get; set; }

    public string RoleName { get; set; }

    public string Properties { get; set; }
    public string ProfileProperties { get; set; }

    public override string ToString()
    {
        return $"Role name: {RoleName};";
    }
}