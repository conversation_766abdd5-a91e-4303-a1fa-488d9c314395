using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowCategoryFixture : IDisposable
{
    public List<WorkflowCategory> WorkflowCategoryPaginationList { get; set; }
    public List<WorkflowCategory> WorkflowCategoryList { get; set; }
    public WorkflowCategory WorkflowCategoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowCategoryFixture()
    {
        var fixture = new Fixture();

        WorkflowCategoryList = fixture.Create<List<WorkflowCategory>>();

        WorkflowCategoryPaginationList = fixture.CreateMany<WorkflowCategory>(20).ToList();

        WorkflowCategoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowCategoryList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowCategoryDto = fixture.Create<WorkflowCategory>();

        WorkflowCategoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
