﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.UserGroup.Events.Delete;

public class UserGroupDeleteEventHandler : INotificationHandler<UserGroupDeleteEvent>
{
    private readonly ILogger<UserGroupDeleteEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UserGroupDeleteEventHandler(ILoggedInUserService userService, ILogger<UserGroupDeleteEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(UserGroupDeleteEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.UserGroup}",
            Entity = Modules.UserGroup.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"User Group '{notification.GroupName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"User Group '{notification.GroupName}' deleted successfully.");
    }
}