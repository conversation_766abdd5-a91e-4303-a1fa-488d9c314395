﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Events.Create;

namespace ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;

public class
    CreateSmsConfigurationCommandHandler : IRequestHandler<CreateSmsConfigurationCommand,
        CreateSmsConfigurationResponse>
{
    private readonly IMapper _mapper;
    private readonly ISmsConfigurationRepository _smsConfigurationRepository;
    private readonly IPublisher _publisher;

    public CreateSmsConfigurationCommandHandler(IMapper mapper, ISmsConfigurationRepository smsConfigurationRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _smsConfigurationRepository = smsConfigurationRepository;
        _publisher = publisher;
    }

    public async Task<CreateSmsConfigurationResponse> Handle(CreateSmsConfigurationCommand request,
        CancellationToken cancellationToken)
    {
        var smsConfiguration = _mapper.Map<Domain.Entities.SmsConfiguration>(request);

        await _smsConfigurationRepository.AddAsync(smsConfiguration);

        await _publisher.Publish(new SmsConfigurationCreatedEvent { UserName = smsConfiguration.UserName }, cancellationToken);

        var response = new CreateSmsConfigurationResponse
        {
            Message = Message.Create("SMS Configuration", smsConfiguration.UserName),

            Id = smsConfiguration.ReferenceId
        };

        return response;
    }
}