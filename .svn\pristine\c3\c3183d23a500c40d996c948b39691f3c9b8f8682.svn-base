
const getUrl = "Admin/SmsConfiguration/GetSMSList";

$(function () {
    var createPermission = $("#adminCreate").data("create-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#btnSmsConfigSave").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
        $("#btnSmsConfigTest").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
        //&& $("#btnSmtpTest").removeClass("btn-primary").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    $('#btnSmsConfigSave').on('click',  function () {
        $('#btnSmsConfigSave').prop('disabled', true);
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))
        smsAction(false)        
    })
    $('#btnSmsConfigTest').on('click', function () {
        smsAction(true)
        
    })
    async function smsAction(isTest = false) {
        let Url = $('#smConfigURL').val();
        let sender = $('#smsConfigSenderId').val();
        let name = $('#smsConfigUserName').val();
        let password = $('#smsConfigPassword').val();
        let number = $('#smsConfigRecipientNo').val();
        let mobilePre = $("#smsConfigMobilepre").val();
        if (mobilePre && number) {
            const comMobile = mobilePre + '-' + number;
            $('#comMobile').val(comMobile)
        }        
        $('#smsConfigProperties,#smsConfigId').val()      
        let url1 = await validateUrl(Url);
        let sender1 = await validateSenderId(sender);
        let name1 = await validateUserName(name);
        let password1 = await validatePassword(password);
        let number1 = await validateRecipientNo(number);
        let MobilePre = await validateMobilePre(mobilePre)
        if (url1 && sender1 && name1 && password1 && number1 && MobilePre) {
            if (isTest) {
                notificationAlert("warning", "SMS not rececived on this user");
            } else {
                let form = $('#smsCreateForm')
                form.trigger('submit')
            }
            
        }
    }
    $.getJSON("/json/CountryDailCode.json", function (data) {
        setTimeout(() => {
            let savedMobilePre = localStorage.getItem("savedMobilePre") || ""; 
            $('#smsConfigMobilepre').empty().append('<option></option>');
            data.countrycode.forEach(function (value) {
                $('#smsConfigMobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + '</option>');
            });
            if (savedMobilePre) {
                $('#smsConfigMobilepre').val(savedMobilePre).trigger('change');
            }
        }, 500);
    }).fail(function () {
        console.error("Failed to load JSON data.");
    });

    $("#v-pills-sms-tab").on('click',function () {
        ['#smsConfigPasswordError', '#smsConfigRecipientNoError', '#smsConfigUserNameError', '#smsConfigSenderIdError', '#smsConfigUrError', '#smsConfigMobilePreError'].forEach(clearErrorMessages);
        getSmsDetails()       
    });

    function clearErrorMessages(errorSelector) {
        $(errorSelector).text('').removeClass('field-validation-error');
    }

    $('#smsCreateForm input').on('input', function () {
        $('#btnSmsConfigSave').prop('disabled', false);
    });
   

    function getSmsDetails() {
        $.ajax({
            type: "GET",
            url: RootUrl + getUrl,
            async: true,
            success: function (response) {
                
                if (response || response.length > 0 || response != undefined) {
                    if (response.length) {
                        $('#btnSmsConfigSave').prop('disabled', true).text('Update');
                        $('#btnSmsConfigTest').prop('disabled', false);
                        $('#smConfigURL').val(response[0].url)
                        $('#smsConfigSenderId').val(response[0].senderId)
                        $('#smsConfigUserName').val(response[0].userName)
                        $('#smsConfigPassword').val(response[0].password)                        
                        $('#smsConfigId').val(response[0].id)
                        $('#smsConfigProperties').val(response[0].properties);
                        if (response[0]?.recipientNo) {
                            let mobileNumber = response[0]?.recipientNo?.split('-');

                            if (mobileNumber?.length === 2) {
                                $('#smsConfigMobilepre').val(mobileNumber[0]).trigger('change');
                                $('#smsConfigRecipientNo').val(mobileNumber[1]);
                                localStorage.setItem("savedMobilePre", mobileNumber[0]);
                            }
                        }               
                       
                       
                    }
                    $(".nav .nav-link").removeClass("active")
                    var id = localStorage.getItem("id")
                    if (id !== null) {
                        $("#" + id).removeClass("active")
                        $("#" + id).addClass("nav-link active")
                        var splitId = id.split("-")
                        var slicevalue = splitId.slice(0, splitId.length - 1).join("-");
                        $(".tab-content .tab-pane").removeClass("active")
                        $("#" + slicevalue).removeClass("tab-pane fade")
                        $("#" + slicevalue).addClass("tab-pane fade active show")
                    }
                } else {
                    $('#btnSmsConfigSave').text("Save")  
                    $('#btnSmsConfigTest').prop('disabled', true);
                }
            }
        })
    }
    getSmsDetails()
    $('#smConfigURL').on('keyup', async function (event) {
        const value = $(this).val();       
        if (event.keyCode == 32) {
            event.preventDefault();
        } if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateUrl(value);
    });
    $('#smsConfigSenderId').on('keyup', async function (event) {
        const value = $(this).val();
        var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);        
        await validateSenderId(value);
    });
    $('#smsConfigUserName').on('keyup', async function () {
        const value = $(this).val();
        var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateUserName(value);
    });
    $('#smsConfigProperties').on('keyup', async function () {
        const value = $(this).val();
        var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        
    });
    $('#smsConfigPassword').on('keyup', async function (event) {
        const value = $(this).val();
        if (event.keyCode == 32) {
            event.preventDefault();
        }
        await validatePassword(value);
    });
    const exceptThisSymbols = ["e", "E", "."];
    $('#smsConfigRecipientNo').on('input keypress', async function (event) {
        //const value = $(this).val();
        //if (!/[+0-9-]/.test(event.key) || exceptThisSymbols.includes(event.key)) {
        //    event.preventDefault();
        //}
        if ($(this).val() !== undefined) {
            $(this).val($(this).val().replace(/  +/g, " "));
        }
        if (!/^[0-9]+$/.test(event.key)) {
            event.preventDefault();
        }
        await validateRecipientNo($(this).val());
    }); 
    $('#smsConfigMobilepre').on('change', async function () {        
        localStorage.setItem("savedMobilePre", $(this).val());
        await validateMobilePre($(this).val());
    });
    async function validatePassword(value) {
        const errorElement = $('#smsConfigPasswordError');
        if (!value) {
            errorElement.text('Enter password').addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await passwordRegex(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateRecipientNo(value) {
        if (!value) {
            $('#smsConfigRecipientNoError').text('Enter recipient number')
                .addClass('field-validation-error');
            return false;
        } else if (value == 0) {
            $('#smsConfigRecipientNoError').text('Number not starts with zero').addClass('field-validation-error');
            return false;
        }
        else if (value) {
            const minLength = 7;
            if (value.length < minLength) {
                $('#smsConfigRecipientNoError').text('Must be at least 7 characters').addClass('field-validation-error');
                return false;
            } else {
                $('#smsConfigRecipientNoError').text('').removeClass('field-validation-error');
                return true;
            }
        }
        else {
            $('#smsConfigRecipientNoError').text('').removeClass('field-validation-error');
            return true;
        }
        //const errorElement = $('#SmsRecipientNo-error');
        //if (!value) {
        //    errorElement.text('Enter recipient number')
        //        .addClass('field-validation-error')
        //    return false;
        //} else if (value.length < 7) {
        //    errorElement.text('Must be at least 7 characters')
        //        .addClass('field-validation-error')
        //    return false;
        //}
        //const validationResults = [
        //    await mobileRegex(value)
        //];
        //return await CommonValidation(errorElement, validationResults);
    }
    async function validateMobilePre(value) {
        if (!value) {
            $('#smsConfigMobilePreError').text('Select country code').addClass('field-validation-error');
            return false;
        }
        let validationResults = false
        if (value) {
            validationResults = true
            $('#smsConfigMobilePreError').text('').removeClass('field-validation-error');
            return true;
        }
        return await CommonValidation($('#smsConfigMobilePreError'), validationResults);
    }
    async function validateUserName(value) {
        const errorElement = $('#smsConfigUserNameError');
        if (!value) {
            errorElement.text('Enter username').addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),

        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateSenderId(value) {
        const errorElement = $('#smsConfigSenderIdError');
        if (!value) {
            errorElement.text('Enter sender').addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
            await ShouldNotBeginWithSpace(value),
            await ShouldNotBeginWithUnderScore(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithSpace(value),
            await ShouldNotAllowMultipleSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function validateUrl(value) {
        const errorElement = $('#smsConfigUrError');
        if (!value) {
            errorElement.text('Enter URL').addClass('field-validation-error')
            return false;
        }
        const validationResults = [

            await InvalidSMSUrl(value),
        ];
        return await CommonValidation(errorElement, validationResults);
    }

})
function showPassword(input, icon) {
    input.attr("type", "text");
    icon.removeClass("cp-password-visible").addClass("cp-password-hide");
}

function hidePassword(input, icon) {
    input.attr("type", "password");
    icon.removeClass("cp-password-hide").addClass("cp-password-visible");
}
$(".toggle-password").on('click',async function () {
    //debugger
    let input = $(this).prev();
    let icon = $(this).find("i");

    if (input.attr("type") === "password") {

        showPassword(input, icon);       

    } else {
        hidePassword(input, icon);
    }
});