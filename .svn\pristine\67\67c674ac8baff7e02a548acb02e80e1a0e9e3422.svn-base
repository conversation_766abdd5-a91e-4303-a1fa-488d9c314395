﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetPaginatedList;

public class GetDB2HADRMonitorLogPaginatedListQueryHandler : IRequestHandler<GetDB2HADRMonitorLogPaginatedListQuery,
    PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>
{
    private readonly IDb2HadrMonitorLogRepository _db2HADRMonitorLog;
    private readonly IMapper _mapper;

    public GetDB2HADRMonitorLogPaginatedListQueryHandler(IDb2HadrMonitorLogRepository db2HADRMonitorLog, IMapper mapper)
    {
        _db2HADRMonitorLog = db2HADRMonitorLog;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>> Handle(
        GetDB2HADRMonitorLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _db2HADRMonitorLog.GetPaginatedQuery();

        var productFilterSpec = new Db2HadrMonitorLogFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<DB2HADRMonitorLogPaginatedListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}