﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ServerType.Events.Update;

public class ServerTypeUpdatedEventHandler : INotificationHandler<ServerTypeUpdatedEvent>
{
    private readonly ILogger<ServerTypeUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerTypeUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<ServerTypeUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerTypeUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.ServerType.ToString(),
            Action = $"{ActivityType.Update} {Modules.ServerType}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ServerType '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ServerType '{updatedEvent.Name}' updated successfully.");
    }
}