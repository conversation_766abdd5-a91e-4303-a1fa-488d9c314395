﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetImpactDetails;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Dashboard;

public class HeatMapStatusService : BaseService, IHeatMapStatusService
{
    public HeatMapStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateHeatMapStatusCommand createHeatMapStatusCommand)
    {
        Logger.LogDebug($"Create HeatMapStatus '{createHeatMapStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createHeatMapStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateHeatMapStatusCommand updateHeatMapStatusCommand)
    {
        Logger.LogDebug($"Update HeatMapStatus '{updateHeatMapStatusCommand.InfraObjectName}'");

        return await Mediator.Send(updateHeatMapStatusCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HeatMapStatus Id");

        Logger.LogDebug($"Delete HeatMapStatus Details by Id '{id}'");

        return await Mediator.Send(new DeleteHeatMapStatusCommand { Id = id });
    }

    public async Task<HeatMapStatusDetailVm> GetHeatMapStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HeatMapStatus Id");

        Logger.LogDebug($"Get HeatMapStatus Detail by Id '{id}'");

        return await Mediator.Send(new GetHeatMapStatusDetailQuery { Id = id });
    }

    public async Task<List<HeatMapStatusListVm>> GetHeatMapStatus()
    {
        Logger.LogDebug("Get All HeatMapStatus");

        return await Mediator.Send(new GetHeatMapStatusListQuery());
    }

    public async Task<HeatMapStatusByInfraObjectandEntityIdVm> GetHeatMapStatusByInfraObjectIdAndEntityId(
        string infraObjectId, string entityId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "HeatMapStatus infraObjectId");
        Guard.Against.InvalidGuidOrEmpty(entityId, "HeatMapStatus entityId");

        Logger.LogDebug($"Get HeatMapStatus Detail by Id '{infraObjectId}' & '{entityId}'");

        return await Mediator.Send(new GetHeatMapStatusByInfraObjectandEntityIdQuery
            { InfraObjectId = infraObjectId, EntityId = entityId });
    }

    public async Task<List<HeatMapStatusListVm>> GetHeatMapStatusByType(string? businessServiceId, string type, bool isAffected)
    {
        Logger.LogDebug($"Get HeatMapStatus Details by Type '{type}'");

        return await Mediator.Send(new GetHeatMapStatusTypeQuery
            { BusinessServiceId = businessServiceId, Type = type,IsAffected = isAffected });
    }

    public async Task<ImpactDetailVm> GetImpactDetail(string? businessServiceId)
    {
        Logger.LogDebug("Get All HeatMapStatus");

        return await Mediator.Send(new GetImpactDetailQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<PaginatedResult<HeatMapStatusListVm>> GetPaginatedHeatMapStatus(
        GetHeatMapStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in HeatMapStatus Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<BusinessServiceAvailabilityVm> GetBusinessServiceAvailability()
    {
        Logger.LogDebug("Get All BusinessServiceAvailability");

        return await Mediator.Send(new GetBusinessServiceAvailabilityQuery());
    }

    public async Task<BusinessFunctionAvailabilityVm> GetBusinessFunctionAvailability()
    {
        Logger.LogDebug("Get All BusinessFunctionAvailability");

        return await Mediator.Send(new GetBusinessFunctionAvailabilityQuery());
    }
}