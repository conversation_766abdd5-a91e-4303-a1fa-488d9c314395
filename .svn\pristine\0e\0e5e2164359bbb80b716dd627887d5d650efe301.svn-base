//const getRandomIdApprove = (value) => {
//    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
//}

//let startButtonValue = 1;
//const staticPosition = { top: 116, left: 40 };
//let lastPosition = staticPosition;
//let boxCounter = 0;
//let endMarkerId = "endMarker";
//let templateIDAndClass = [];

$(async function () {
    //$("#createRequest").on("click", function () {
    //    $("#templateDiagram").empty();
    //    $("#editedProcessData").empty();
    //});

    //$("#endTemplate").on("click", function () {
    //    let $templateElement = $(`#${templateIDAndClass[2]}`);
    //    $templateElement
    //        .removeClass("d-none")
    //        .css({
    //            top: (parseInt(templateIDAndClass[4], 10) + 55) + 'px',
    //            left: (parseInt(templateIDAndClass[5], 10) - 10) + '%'
    //        });
    //    $("#saveApprovalTemplate").removeClass("d-none");
    //    let flowLine = $(`.${templateIDAndClass[0]}`);

    //    if (flowLine.length) {
    //        flowLine.addClass('d-none');
    //    }
    //    $(`#${templateIDAndClass[1]}`).addClass('d-none');
    //    let $strightLine = $(`.${templateIDAndClass[3]}`);
    //    $strightLine.removeClass('d-none');
    //    $strightLine.attr({ x1: 0, x2: 0, y1: 17, y2: 58 });
    //    $('#saveApprovalTemplate').attr("disabled", false);
    //});

    //await $.ajax({
    //    type: 'Get',
    //    url: RootUrl + "Manage/ApprovalMatrix/ApprovalMatrixList",
    //    dataType: "json",
    //    success: function (response) {
    //        if (response.success) {
    //            let result = response.data;
    //            $("#templateLists").empty();

    //            result.forEach(function (data, index) {
    //                let userList = `<div class="viewButton mt-2 d-flex align-items-start gap-2 fs-6 card" data-template='${JSON.stringify(data)}' role="button">
    //                                <span class="ms-2 p-2 d-flex align-items-center">
    //                                    ${data.name}
    //                                </span>
    //                            </div>`;

    //                $("#templateLists").append(userList);
    //            })               
    //        }
    //    }
    //});
});

//$(document).on("click", '.viewButton', function () {
//    let data = $(this).attr("data-template");
//    let ParsedData = JSON.parse(data);
//    $("#editedProcessData").empty();
//    $("#templateDiagram").empty();

//    if (ParsedData) {
//        lastPosition = staticPosition;
//        startButtonValue = 1;
//        boxCounter = 0;
//        endMarkerId = "endMarker";
//        let props = JSON.parse(ParsedData.properties);
//        recursiveEdit(props);
//    }
//})

//function recursiveEdit(data) {

//    if (data.name) {
//        createEditTemplate(data);

//        if (data.properties && Array.isArray(data.properties)) {
//            data.properties.forEach(function (child) {
//                recursiveEdit(child);
//            });
//        }
//    } else {
//        $('#endTemplate').trigger("click");
//    }
//}

//function createEditTemplate(data) {
//    let processName = data.name
//    templateIDAndClass = [];
//    let flowLineId = getRandomIdApprove('flowline');
//    let endArrowId = getRandomIdApprove('arrow');
//    let processBoxId = `processBox_${boxCounter++}`;
//    $("#closeOffcanvas").trigger("click");
//    let container = $("#templateDiagram");
//    const boxWidth = 94;
//    const boxHeight = 35;
//    const horizontalGap = 50;
//    const verticalGap = 80;
//    let newTop = lastPosition.top + 80;
//    let newLeft = lastPosition.left + 17.5;
//    let containerOffset = container.offset().top === 0 ? { top: 146.5, left: 71 } : container.offset();

//    if (startButtonValue === 1) {
//        let start = `
//            <div id="startTemplate" 
//                style="position:absolute; 
//                top:10%; 
//                left:40%; 
//                transform:translate(-50%, -50%);
//                width:${boxWidth}px; 
//                height:${boxHeight + 5}px;                
//                border-radius:5px; 
//                display:flex; 
//                align-items:center; 
//                justify-content:center;
//                font-weight:bold;">
//                <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start image">
//            </div>`;
//        container.append(start);

//        let startBox = $("#startTemplate");
//        let startBoxOffset = startBox.offset().top === 0 ? { top: 160.60375, left: 457.15 } : startBox.offset();

//        let startX = startBoxOffset.left - containerOffset.left + boxWidth / 2;
//        let startY = startBoxOffset.top - containerOffset.top + (boxHeight - 10);

//        let pointArray = [[startX, startY + 10], [startX, startY + 50]];
//        let polylinePoints = pointArray.map(point => point.join(",")).join(" ");
//        let endArrowId2 = getRandomIdApprove('arrow');

//        let svg = `<svg 
//                        width="100" height="100"
//                       style="position:absolute; top:0; left:0; overflow:visible; transform-origin: 0 0;">
    
//                        <defs>
//                           <marker id="${endArrowId2}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
//                               <path d="M0,0 L10,5 L0,10 Z" fill="black"/>
//                           </marker>
//                        </defs>

//                        <polyline points="${polylinePoints}" 
//                                fill="none" 
//                                stroke="green" 
//                                stroke-width="1" 
//                                stroke-dasharray="4, 4" 
//                                marker-end="url(#${endArrowId2})"/>
//                  </svg>`;
//        container.append(svg);
//        startButtonValue = 2;
//    }

//    let processBox = `
//            <div id="${processBoxId}" 
//                class="process-box" 
//                title='${processName}'
//                data-template ='${JSON.stringify(data)}'
//                style="position:absolute; 
//                cursor:pointer;
//                top:${lastPosition.top}px; left:${lastPosition.left}%; 
//                transform: translate(-50%, -50%);
//                width:${boxWidth}px; 
//                height:${boxHeight}px; 
//                background-color:#dbf5ff; 
//                border:1px solid #87afc6; 
//                border-radius:8px; 
//                display:flex; 
//                align-items:center; 
//                justify-content:center;
//                font-weight:bold;">
//               <span style="
//                    white-space: nowrap;
//                    overflow: hidden;
//                    text-overflow: ellipsis;
//                    display: block;
//                    max-width: 80%;">
//                    ${processName}
//                </span>
//            </div>`;
//    container.append(processBox);

//    let pointArray = [[-(boxWidth / 2), 0], [-(boxWidth / 2 + horizontalGap + 30), 0], [-(boxWidth / 2 + horizontalGap + 30), verticalGap - 20]];
//    let pointArray2 = [[boxWidth / 2, 0], [boxWidth / 2 + horizontalGap + 30, 0], [boxWidth / 2 + horizontalGap + 30, verticalGap - 20]];
//    let polylinePoints = pointArray.map(point => point.join(",")).join(" "); //red
//    let polylinePoints2 = pointArray2.map(point => point.join(",")).join(" "); //green

//    let svgLine = `
//            <svg id="${flowLineId}" 
//                    style="position:absolute; top:${lastPosition.top}px; left:${lastPosition.left}%; overflow:visible;">

//                    <defs>
//                        <marker id="${endArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
//                            <path d="M0,0 L10,5 L0,10 Z" fill="black" />
//                        </marker>
//                    </defs>

//                    <polyline class="${flowLineId} leftRightLines" points="${polylinePoints}"
//                        stroke="red" stroke-width="1" fill="none" marker-end="url(#${endArrowId})"/> 

//                    <polyline class="${flowLineId} leftRightLines" points="${polylinePoints2}"
//                        stroke="green" stroke-width="1" fill="none" marker-end="url(#${endArrowId})"/>                                         
                   
//                   <line class="d-none endLine straightLine${flowLineId}" 
//                        x1="${boxWidth / 2 + horizontalGap}" y1="0" 
//                        x2="${boxWidth / 2 + horizontalGap}" y2="${verticalGap}" 
//                        stroke="green" stroke-width="1" 
//                        stroke-dasharray="4, 4" 
//                        marker-end="url(#${endArrowId})"/>                        
//            </svg>`;
//    container.append(svgLine);

//    if ($("#lastEndButton").length) {
//        $("#lastEndButton").remove();
//    }

//    let endButton = `
//             <div id="lastEndButton${boxCounter}"
//                class="d-none endButton"
//                style="position:absolute; 
//                top:${lastPosition.top + 24}px; 
//                left:${lastPosition.left + 10}%; 
//                transform:translate(-50%, -50%);                              
//                border-radius:20px;
//                font-weight:bold;
//                box-shadow:0 2px 5px rgba(0,0,0,0.2);">
//                <img title="End" src="/img/input_Icons/End.svg" width="30" height="30" draggable="false" loading="lazy" alt="End image">
//            </div>`;
//    container.append(endButton);

//    let noButton = `
//             <div id="noButton${boxCounter}"   
//                class="noButton"
//                style="position:absolute; 
//                top:${(lastPosition.top + 74)}px; 
//                left:${(lastPosition.left - 17.5)}%; 
//                transform:translate(-50%, -50%);                
//                padding:4px 12px;
//                background-color:#ff0000;
//                color:white;
//                border-radius:20px;
//                font-weight:bold;
//                box-shadow:0 2px 5px rgba(0,0,0,0.2);">
//                No
//            </div>`;
//    container.append(noButton);


//    templateIDAndClass.push(flowLineId);
//    templateIDAndClass.push(`noButton${boxCounter}`);
//    templateIDAndClass.push(`lastEndButton${boxCounter}`);
//    templateIDAndClass.push(`straightLine${flowLineId}`);
//    templateIDAndClass.push(`${lastPosition.top + 20}`);
//    templateIDAndClass.push(`${lastPosition.left + 10}`);
//    lastPosition = { top: newTop, left: newLeft };
//}

//$(document).on('click', '.process-box', function () {
//    let $this = $(this);
//    let data = JSON.parse($this.attr("data-template"));
//    let names = data?.userLists?.map(user => user.name).join(", ");
//    let processData = $("#editedProcessData");
//    processData.empty();

//    let divTag = `
//        <div>
//            <div class="row">
//                <div class="col-xl-12">
//                    <div class="mb-3 form-group">
//                        <div class="form-label">Process Name</div>
//                        <div class="input-group">
//                            <span class="input-group-text"><i class="cp-bag"></i></span>
//                            <input type="text" class="form-control" placeholder="Enter Process Name"
//                                   value='${data.name}' disabled/>
//                        </div>
//                    </div>
//                    <div class="mb-3 ">
//                        <div class="mb-3 form-group">
//                            <div class="form-label">Description<small class="text-secondary">( Optional )</small></div>
//                            <div class="input-group">
//                                <span class="input-group-text"><i class="cp-description"></i></span>
//                                <input type="text" class="form-control" placeholder="Description"
//                                    value='${data.description}' disabled />
//                            </div>
//                        </div>
//                    </div>
//                    <div class="d-flex flex-column" id="approverContainer">
//                        <div class="mb-3 form-group">
//                            <div class="form-label" id="dynamicLabel">User</div>
//                            <div class="input-group">
//                                <span class="input-group-text"><i class="cp-bag"></i></span>
//                               <input type="text" class="form-control" 
//                                    value='${names}' disabled />
//                            </div>
//                        </div>
//                    </div>
//                    <div class="mb-3" id="SLAApproverContainer">
//                        <div>
//                            SLA
//                        </div>
//                        <div class="d-flex align-items-center gap-3">
//                            <div class="form-group w-100">
//                                <div class="input-group">
//                                    <input type="number" class="form-control w-50" placeholder="Enter Duration"
//                                        value='${data?.SLA?.duration}' disabled />
//                                </div>
//                            </div>
//                            <div class="form-group w-100">
//                                <div class="input-group">
//                                  <input type="text" class="form-control w-50"
//                                        value='${data?.SLA?.period}' disabled />                                  
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                    <div id="ruleSetContainer">
//                        <div class="mb-3">Rule Set</div>
//                        <div class="mb-3">
//                            <div>
//                                <div class="mb-3">
//                                    <div class="form-label">Approval</div>
//                                    <div class="d-flex align-items-center gap-3">
//                                        <div class="form-group w-50">
//                                            <div class="input-group">
//                                             <input type="text" class="form-control"
//                                                   value='${data?.ruleSet[0]?.type}' disabled />                                                
//                                            </div>
//                                        </div>
//                                        <div class="form-group w-50">
//                                            <div class="input-group">
//                                                <input type="number" class="form-control" placeholder="Enter Approvar"
//                                                   value='${data?.ruleSet[0]?.ruleCount}' disabled />
//                                            </div>
//                                        </div>
//                                        <div class="form-group w-100">
//                                            approval out of <span>${data?.ruleSet[0]?.approverCount}</span> approvers
//                                        </div>
//                                    </div>
//                                </div>
//                                <div>
//                                    <div class="form-label">Reject</div>
//                                    <div class="d-flex align-items-center gap-3">
//                                        <div class="form-group w-50">
//                                            <div class="input-group">
//                                             <input type="text" class="form-control" placeholder="Enter Approvar"
//                                                   value='${data?.ruleSet[1]?.type}' disabled />                                              
//                                            </div>
//                                        </div>
//                                        <div class="form-group w-50">
//                                            <div class="input-group">
//                                                <input type="number" class="form-control" placeholder="Enter Approvar"
//                                                   value='${data?.ruleSet[1]?.ruleCount}' disabled />
//                                            </div>
//                                        </div>
//                                        <div class="form-group w-100">
//                                            rejects out of <span >${data?.ruleSet[1]?.rejectCount}</span> approvers                                                                                     
//                                        </div>
//                                    </div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                    <div id="approvalNotificationContainer">
//                        <div class="mb-3">
//                            <div class="form-label">Notification Type</div>
//                            <div class="p-2">
//                                <div class="d-flex align-items-center gap-3">
//                                    <div class="d-flex flex-column align-items-center"><span class="p-3 shadow-sm rounded"> <i class="cp-email align-middle text-primary fw-semibold fs-5"></i> </span> <span class="mt-2">Email</span></div>
//                                    <div class="d-flex flex-column align-items-center"><span class="p-3 shadow-sm rounded"> <i class="cp-message-alert align-middle text-primary fw-semibold fs-5"></i> </span> <span class="mt-2">SMS</span></div>
//                                    <div class="d-flex flex-column align-items-center"><span class="p-3 shadow-sm rounded"> <i class="cp-alerts align-middle text-primary fw-semibold fs-5"></i> </span> <span class="mt-2">Application</span></div>
//                                </div>
//                            </div>
//                        </div>
//                    </div>
//                </div>
//            </div>
//        </div>        
//    `;

//    processData.append(divTag);
//});
