using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataSyncMonitorLogFixture : IDisposable
{
    public List<DataSyncMonitorLog> DataSyncMonitorLogPaginationList { get; set; }
    public List<DataSyncMonitorLog> DataSyncMonitorLogList { get; set; }
    public DataSyncMonitorLog DataSyncMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WORKFLOW_123";
    public const string Type = "TestType";

    public ApplicationDbContext DbContext { get; private set; }

    public DataSyncMonitorLogFixture()
    {
        var fixture = new Fixture();

        DataSyncMonitorLogList = fixture.Create<List<DataSyncMonitorLog>>();

        DataSyncMonitorLogPaginationList = fixture.CreateMany<DataSyncMonitorLog>(20).ToList();

        DataSyncMonitorLogPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncMonitorLogPaginationList.ForEach(x => x.IsActive = true);
        DataSyncMonitorLogPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DataSyncMonitorLogPaginationList.ForEach(x => x.WorkflowId = WorkflowId);
        DataSyncMonitorLogPaginationList.ForEach(x => x.Type = Type);

        DataSyncMonitorLogList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSyncMonitorLogList.ForEach(x => x.IsActive = true);
        DataSyncMonitorLogList.ForEach(x => x.InfraObjectId = InfraObjectId);
        DataSyncMonitorLogList.ForEach(x => x.WorkflowId = WorkflowId);
        DataSyncMonitorLogList.ForEach(x => x.Type = Type);

        DataSyncMonitorLogDto = fixture.Create<DataSyncMonitorLog>();
        DataSyncMonitorLogDto.ReferenceId = Guid.NewGuid().ToString();
        DataSyncMonitorLogDto.IsActive = true;
        DataSyncMonitorLogDto.InfraObjectId = InfraObjectId;
        DataSyncMonitorLogDto.WorkflowId = WorkflowId;
        DataSyncMonitorLogDto.Type = Type;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
