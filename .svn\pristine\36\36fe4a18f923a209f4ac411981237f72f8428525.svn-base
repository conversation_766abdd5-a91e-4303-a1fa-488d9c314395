﻿using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class LicenseHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<LicenseHistory> LicenseHistories { get; set; }

    public List<UserActivity> UserActivities { get; set; }

    public CreateLicenseHistoryCommand CreateLicenseHistoryCommand { get; set; }

    public UpdateLicenseHistoryCommand UpdateLicenseHistoryCommand { get; set; }

    public Fixture AutoLicenseHistoryFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateLicenseHistoryCommand>(p => p.<PERSON>, 10));
            fixture.Customize<CreateLicenseHistoryCommand>(c => c.With(b => b.<PERSON><PERSON><PERSON>,
                "6M3+dUhVICEhiWvmot2dMU+X5qYoXpM1OkEfzQZQ8yP8YE5U1huH/FCozZKDBJxbdWa4hQ9wqoCgUwibqYHjmJ9THguBQkw3q+1T0GzHubYBVlxkLFtc8ao/1WjTesmtspBL77mXWBrIui7m/vjecUCj34Pn2RUHgVo/GbicC8FF9vL8YPPqmQgvr10JW0X6CwmpFz94N/BsaV6+yNjNK5/EIRRpt6pwrMXVDr2H21w="));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateLicenseHistoryCommand>(p => p.Id, 10));
            fixture.Customize<UpdateLicenseHistoryCommand>(c => c.With(b => b.Id));
            fixture.Customize<LicenseHistory>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateLicenseHistoryCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<LicenseHistory>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public LicenseHistoryFixture()
    {
        LicenseHistories = AutoLicenseHistoryFixture.Create<List<LicenseHistory>>();

        UserActivities = AutoLicenseHistoryFixture.Create<List<UserActivity>>();

        CreateLicenseHistoryCommand = AutoLicenseHistoryFixture.Create<CreateLicenseHistoryCommand>();

        UpdateLicenseHistoryCommand = AutoLicenseHistoryFixture.Create<UpdateLicenseHistoryCommand>();

        var configurationProvider = new MapperConfiguration(cfg => { cfg.AddProfile<LicenseHistoryProfile>(); });
        Mapper = configurationProvider.CreateMapper();

    }

    public void Dispose()
    {

    }
}