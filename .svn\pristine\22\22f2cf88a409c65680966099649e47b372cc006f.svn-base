﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Events.Approval;

public class ApprovalMatrixApprovalEventHandler : INotificationHandler<ApprovalMatrixApprovalEvent>
{
    private readonly ILogger<ApprovalMatrixApprovalEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixApprovalEventHandler(ILogger<ApprovalMatrixApprovalEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ApprovalMatrixApprovalEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} ApprovalMatrixApproval",
            Entity = "ApprovalMatrixApproval",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ApprovalMatrixApproval '{updatedEvent.Name}' request approved successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ApprovalMatrixApproval '{updatedEvent.Name}' request approved successfully.");
    }
}