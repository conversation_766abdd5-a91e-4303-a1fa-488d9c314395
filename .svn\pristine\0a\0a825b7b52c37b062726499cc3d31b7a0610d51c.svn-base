let validateSourceDir = [];
let validateDestinationDir = [];
let validateProperties = [];
let rsyncRobocopyPath = /^(\\\\[^\\\/]+\\[^\\\/]+(\\[^\\\/]*)*|[A-Za-z]:\\[^\\\/]+(\\[^\\\/]*)*|\\[^\\\/]+(\\[^\\\/]*)*|\/[^\/]+(\/[^\/]*)*|~\/[^\/]+(\/[^\/]*)*|https?:\/\/[^\/]+(\/[^\/]*)*|\\\\\.[\\\/][^\\\/]+|\\\\\?[\\\/][^\\\/]+)$/
//const regex = /^((\\\\[^\\\/]+\\[^\\\/]+)|([A-Za-z]:\\[^\\\/]+(\\[^\\\/]*)*)|(\/[^\/]+(\/[^\/]*)*))$/
// Allows \\WIN22SQL22QA2\Robocopy_155_DR, D:\CP6_MVC_17-11-2023, /home/<USER>

let inputSourceField = (value) =>
    `<div class="form-group">
        <div class="input-group">
            <input type="text" class="form-control sourceDirectory" autocomplete="off" maxlength="500"
                    value="${value}" name="sourceDirectory" placeholder="Enter Source Directory" ${value ? "disabled" : ""}/>
        </div>
        <span class="sourceFieldError"></span>
    </div>`;

let inputDestinationField = (value) =>
    `<div class="form-group">
        <div class="input-group">
            <input type="text" class="form-control destinationDirectory" autocomplete="off" maxlength="500"
                     value="${value}" name="destinationDirectory" placeholder="Enter Destination Directory" ${value ? "disabled" : ""}/>
        </div>
        <span class="destinationFieldError"></span>
    </div>`;

//form-select-modal-dynamic don't use in class.
let inputProperties = (value, index, length) =>
    ` <div class="form-group">
        <div class="input-group">
            <select class="${value ? value : 'replicationProperties'}" placeholder="Select Property"
                name="properties" ${index === length ? "" : "disabled"}>
                <option value="">Select Property</option>
            </select>
        </div>
        <span class="propertyError"></span>
    </div>`;

$(document).on('input', '.sourceDirectory', function () {
    if ($('#ddlReplicationTypeID option:selected').text().toLowerCase()) {
        sourceDirInput($('.sourceDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase());
    }
});

$(document).on('input', '.destinationDirectory', function () {
    if ($('#ddlReplicationTypeID option:selected').text().toLowerCase()) {
        destinationDirInput($('.destinationDirectory'), $('#ddlReplicationTypeID option:selected').text().toLowerCase());
    }
});

$(document).on('change', 'select[class^="replicationProperties"]', function () {
    if ($('#ddlReplicationTypeID option:selected').text().toLowerCase()) {
        let propertyValue = $('select[class^="replicationProperties"]');
        rSyncRoboDataSyncproperties(propertyValue);
    }
});

function rSyncRoboDataSyncproperties(propertyValue, status = null) {
    if (propertyValue?.length > 0) {
        validateProperties = [];
        let validate = [];
        propertyValue.each(function () {
            let $this = $(this);
            let $propertyFieldErrorSpan = $this.closest('.form-group').find('.propertyError');
            let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
            let value = $this.val();
            $propertyFieldErrorSpan.text("").removeClass("field-validation-error");
            if (value) {
                validateProperties.push(true);
                if (status) {
                    validate.push(true);
                }
            } else {
                validateProperties.push(false);
                if (status) {
                    validate.push(false);
                }
                $propertyFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
            }
        })
        if (status) {
            return validate
        }
    }
}

//functions
async function populateTheDynamicFields(fields) {
    for (const key in fields) {
        if (fields.hasOwnProperty(key)) {
            const field = fields[key];
            const { id, meta, config, attrs } = field;
            if (meta.id === "database") {
                const _options = await getDBOptionsFromServer(attrs.DatabaseType);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    _options.forEach(option => {
                        const optionElement = new Option(option.text, option.value);
                        selectField.appendChild(optionElement);
                    });
                } else {
                    errorNotification({ message: "Check the database condition field in the form builder module." });
                }
            }
            if (meta.id === 'date-input') {
                const selectField = $(`#f-${id}`);
                if (selectField.length) {
                    selectField.attr({ type: "datetime-local", step: "1" });
                }
            }
            if (meta.id === "server") {
                const name = attrs.name.toLowerCase();
                //const type = attrs.ServerRole;
                //const serverType = attrs.ServerType;
                const _options = await getServOptionsFromServer(attrs.ServerRoleID, attrs.ServerTypeID);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    _options.forEach(option => {
                        const optionElement = document.createElement("option");
                        optionElement.value = option.value;
                        optionElement.text = option.text;
                        if (name === "@@dscliserver" || name === "dscliserver") {
                            optionElement.setAttribute("data-hostname", option.HostName);
                            optionElement.setAttribute("data-ipaddress", option.IpAddress);
                            optionElement.setAttribute("data-sshuser", option.SSHUser);
                            optionElement.setAttribute("data-sshpassword", option.SSHPassword);
                        }
                        selectField.appendChild(optionElement);
                    });
                    if (name === "@@dscliserver" || name === "dscliserver") {
                        $("#f-" + id).on("change", async function () {
                            let selectedOption = $(this).find("option:selected");
                            let hostname = selectedOption.attr("data-hostname");
                            let ipaddress = selectedOption.attr("data-ipaddress");
                            let sshuser = selectedOption.attr("data-sshuser");
                            let sshpassword = selectedOption.attr("data-sshpassword");
                            let requiredInputs = $(".f-field-group input");
                            if (requiredInputs?.length > 0) {
                                requiredInputs?.each(async function () {
                                    let $this = $(this);
                                    let name = $this.attr("name").toLowerCase();
                                    if (name === "dscliserverhostname") {
                                        if ($this.is(':visible')) {
                                            $this.val(hostname);
                                            $this.next(".dynamic-input-field").remove();
                                            $this.attr("disabled", "disabled");
                                            $this.css("background", "#D3D3D3");
                                        }
                                    }
                                    if (name === "sshuser" || name === "sshuserid") {
                                        if ($this.is(':visible')) {
                                            $this.val(sshuser);
                                            $this.next(".dynamic-input-field").remove();
                                            $this.attr("disabled", "disabled");
                                            $this.css("background", "#D3D3D3");
                                        }
                                    }
                                    if (name === "dscliserverip") {
                                        if ($this.is(':visible')) {
                                            $this.val(ipaddress);
                                            $this.next(".dynamic-input-field").remove();
                                            $this.attr("disabled", "disabled");
                                            $this.css("background", "#D3D3D3");
                                        }
                                    }
                                    if (name === "sshpassword") {
                                        if ($this.is(':visible')) {
                                            $this.val(sshpassword);
                                            $this.next(".dynamic-input-field").remove();
                                            $this.attr("disabled", "disabled");
                                            $this.css("background", "#D3D3D3");
                                            let serverRole = $this.attr('id');
                                            $(`#${serverRole}`).on('blur', async function (event) {
                                                const response = await $.ajax({
                                                    url: RootUrl + "Configuration/Server/HashPassword",
                                                    method: 'GET',
                                                    data: { Password: $this.val() },
                                                    dataType: 'json'
                                                });
                                                $this.val(response.encrypt);
                                            });
                                            $(`#${serverRole}`).on('focus', async function (event) {
                                                const response = await $.ajax({
                                                    url: RootUrl + "Configuration/Server/HashPasswordDecrypt",
                                                    method: 'GET',
                                                    data: { Password: $this.val() },
                                                    dataType: 'json'
                                                });
                                                $this.val(response.decrypt);
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    }
                } else {
                    let error = { message: "Check the server condition field in the form builder module." }
                    errorNotification(error);
                }
            }
            if (meta.id === "workflow") {
                let isAction = attrs.dependentAction
                const _options = await getWorkflowsFromServer(isAction);
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    if (isAction) {
                        // Duplicate the select field
                        const duplicatedSelectField = selectField.parentNode.parentNode.parentNode.parentNode.cloneNode(true);
                        const labelElement = duplicatedSelectField.querySelector('label');
                        labelElement.textContent = 'Workflows Actions';
                        labelElement.htmlFor = 'f-new-select-id8actions0';
                        const selectElement = duplicatedSelectField.querySelector('select');
                        selectElement.name = '@@workflow_actions';
                        selectElement.multiple = true
                        selectElement.id = 'f-new-select-id8actions0';
                        selectElement.setAttribute('placeholder', 'Select Workflow Actions');
                        selectField.parentNode.parentNode.parentNode.parentNode.appendChild(duplicatedSelectField);

                        _options.forEach(option => {
                            const optionElement = new Option(option.text, option.value);
                            selectField.appendChild(optionElement);
                        });

                        selectElement.innerHTML = '';
                        const optionElement2 = new Option("Select Workflow Actions", "");
                        selectElement.appendChild(optionElement2);

                        $(`#f-${id}`).on("change", function () {
                            getWorkflowsActionsFromServer(this.value)?.then(optins => {
                                selectElement.innerHTML = '';
                                optins.forEach(option => {
                                    const optionElement3 = new Option(option.value, option.id);
                                    selectElement.appendChild(optionElement3);
                                });
                            })
                        })
                    }
                    else {
                        _options.forEach(option => {
                            const optionElement4 = new Option(option.text, option.value);
                            selectField.appendChild(optionElement4);
                        });
                    }
                } else {
                    errorNotification({ message: "Check the workflow condition field in the form builder module." });
                }
            }
            if (meta.id === "replication") {
                const _options = await getRepOptionsFromServer();
                const selectField = document.querySelector(`#f-${id}`);
                if (selectField) {
                    _options.forEach(option => {
                        const optionElement5 = new Option(option.text, option.value);
                        selectField.appendChild(optionElement5);
                    });
                } else {
                    errorNotification({ message: "Check the replication condition field in the form builder module." });
                }
            }
            if (meta.id === "replicationConfigTable") {
                const $table = document.querySelector(`#f-${id}`);
                sourcePath = $('.replication-custom-table').attr("source-path");
                destinationPath = $('.replication-custom-table').attr("destination-path");
                propertyType = $("#replicationNameType").text()
                let value = propertyType.toLowerCase().trim();
                if ($table.children.length == 0) {
                    let $headerRow = $('<tr class=".header_row2"></tr>');
                    $headerRow.append(`<th>${sourcePath}</th>`);
                    $headerRow.append(`<th>${destinationPath}</th>`);
                    $headerRow.append(`<th> ${propertyType} Properties</th>`);
                    $headerRow.append('<th>Action</th>');
                    $table.append($headerRow[0]);

                    // Create a data row
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append(`<td>${inputSourceField('')}</td>`);
                    $dataRow.append(`<td>${inputDestinationField('')}</td>`);
                    $dataRow.append(`<td>${inputProperties('', '', '')}</td>`);

                    // Add action buttons to the data row
                    let $actionCell = $('<td></td>');
                    $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddReplicationTable('${id}', this.parentElement.parentElement, event,'create')"> <i  class="cp-add "></i></span>`);
                    $dataRow.append($actionCell);

                    if (value.includes("robocopy")) {
                        $.ajax({
                            type: "GET",
                            url: RootUrl + 'Configuration/Replication/ReplicationRoboCopyOptions',
                            dataType: 'text',
                            success: function (result) {
                                let res = JSON.parse(result);
                                if (res.success) {
                                    let replicationProperties = $('.replicationProperties');
                                    replicationProperties.empty();
                                    replicationProperties.append($('<option>').val("").text("Select Property"));
                                    res?.data?.robocopy?.forEach(function (item) {
                                        replicationProperties.append($('<option>').val(item.id).text(item.name));
                                    });

                                } else {
                                    errorNotification(res)
                                }
                            }
                        });
                    }
                    else if (value.includes('datasync')) {
                        $.ajax({
                            type: "GET",
                            url: RootUrl + 'Configuration/Replication/ReplicationDataSyncProperties',
                            dataType: 'text',
                            success: function (result) {
                                let res = JSON.parse(result);
                                if (res.success) {
                                    let replicationProperties = $('.replicationProperties');
                                    replicationProperties.empty();
                                    replicationProperties.append($('<option>').val("").text("Select Property"));
                                    res?.data?.dataSync?.forEach(function (item) {
                                        replicationProperties.append($('<option>').val(item.id).text(item.name));
                                    });

                                } else {
                                    errorNotification(res);
                                }
                            }
                        });
                    }
                    else if (value.includes('rsync')) {
                        $.ajax({
                            type: "GET",
                            url: RootUrl + 'Configuration/Replication/ReplicationRSyncOptions',
                            dataType: 'text',
                            success: function (result) {
                                let res = JSON.parse(result);
                                if (res.success) {
                                    let replicationProperties = $('.replicationProperties');
                                    replicationProperties.empty();
                                    replicationProperties.append($('<option>').val("").text("Select Property"));
                                    res?.data?.rSync?.forEach(function (item) {
                                        replicationProperties.append($('<option>').val(item.id).text(item.name));
                                    });

                                } else {
                                    errorNotification(res)
                                }
                            }
                        });
                    }
                    else {
                        let replicationProperties = $('.replicationProperties');
                        replicationProperties.empty();
                        replicationProperties.append($('<option>').val("").text("Select Property"));
                    }
                    $table.append($dataRow[0]);
                }
            }
            if (meta.id === "VmPathTable") {
                const $table = document.querySelector(`#f-${id}`);
                if ($table.children.length == 0) {
                    let $headerRow = $('<tr class=".header_row3"></tr>');
                    $headerRow.append('<th>VmName</th>');
                    $headerRow.append('<th>PRVm(vmx) File Path</th>');
                    $headerRow.append('<th>DRVm(vmx) File Path</th>');
                    $headerRow.append('<th>PRDataStore Name</th>');
                    $headerRow.append('<th>DRDataStore Name</th>');
                    $headerRow.append('<th>Action</th>');
                    $table.append($headerRow[0]);

                    // Create a data row
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append('<td><input placeholder="Enter VmName" type="text" name="VmName"/></td>');
                    $dataRow.append('<td><input placeholder="Enter PRVm File Path" type="text" name="PRVmFilePath"/></td>');
                    $dataRow.append('<td><input placeholder="Enter DRVm File Path" type="text" name="DRVmFilePath" /> </td>');
                    $dataRow.append('<td><input placeholder="Enter PRDataStore Name" type="text" name="PRDataStoreName" /> </td>');
                    $dataRow.append('<td><input placeholder="Enter DRDataStore Name" type="text" name="DRDataStoreName" /> </td>');

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:62px" class="d-flex align-items-center  gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddVmTable('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);
                    $dataRow.append($actionCell);
                    $table.append($dataRow[0]);
                }
            }
            if (meta.id === "lunsTable") {
                const $table = document.querySelector(`#f-${id}`);
                if ($table.children.length == 0) {
                    lunHeaderOne = $('.luns-custom-table').attr("luns-header-1");
                    lunHeaderTwo = $('.luns-custom-table').attr("luns-header-2");
                    lunHeaderThree = $('.luns-custom-table').attr("luns-header-3");
                    lunHeaderFour = $('.luns-custom-table').attr("luns-header-4");
                    lunHeaderFive = $('.luns-custom-table').attr("luns-header-5");
                    lunHeaderSix = $('.luns-custom-table').attr("luns-header-6");
                    let $headerRow = $('<tr class=".header_row4"></tr>');
                    $headerRow.append(`<th>${lunHeaderOne}</th>`);
                    $headerRow.append(`<th>${lunHeaderTwo}</th>`);
                    $headerRow.append(`<th>${lunHeaderThree}</th>`);
                    $headerRow.append(`<th>${lunHeaderFour}</th>`);
                    $headerRow.append(`<th>${lunHeaderFive}</th>`);
                    $headerRow.append(`<th>${lunHeaderSix}</th>`);
                    $headerRow.append('<th>Action</th>');
                    $table.append($headerRow[0]);

                    // Create a data row
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerOne" id="lunsTableDescription1" placeholder="Enter ${lunHeaderOne}" class="Description1"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerTwo" id="lunsTableDescription2" placeholder="Enter ${lunHeaderTwo}" class="Description2"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerThree" id="lunsTableDescription3" placeholder="Enter ${lunHeaderThree}" class="Description3"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerFour" id="lunsTableDescription4" placeholder="Enter ${lunHeaderFour}" class="Description4"/></div></div></td>`);
                    $dataRow.append(`<td><input type="text" name="headerFive" id="lunsTableDescription5" placeholder="Enter ${lunHeaderFive}" class="Description5"/></td>`);
                    $dataRow.append(`<td><input type="text" name="headerSix" id="lunsTableDescription6" placeholder="Enter ${lunHeaderSix}" class="Description6"/></td>`);

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:73px" class="d-flex align-items-center gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddLunsTable('${id}', this.parentElement.parentElement, event)">
                                    <i class="cp-add"></i>
                                </span>`);
                    $dataRow.append($actionCell);
                    $table.append($dataRow[0]);

                    $($table).on('keyup', '.LUNSTextInput', function () {
                        let $this = $(this);
                        let error = '.input-field-error';
                        LUNSTableValidaiton($this, error);
                    });
                }
            }
            if (meta.id === "dynamicTable") {
                const $table = document.querySelector(`#f-${id}`);
                $table.innerHTML = '';
                let keys = Object.keys(attrs?.dynamicHeader);
                let length = keys.length;
                if ($table?.children?.length == 0) {
                    let $tHead = $(' <thead> </thead>')
                    let $headerRow = $('<tr class="header_row5"></tr>');
                    for (let i = 0; i < length; i++) {
                        $headerRow.append(`<th id="${keys[i]}">${attrs?.dynamicHeader[keys[i]]}</th>`);
                    }
                    $headerRow.append('<th>Action</th>');
                    $tHead.append($headerRow[0])
                    $table.append($tHead[0]);
                    let $tBody = $('<tbody></tbody>');
                    let $dataRow = $('<tr></tr>');
                    for (let i = 0; i < length; i++) {
                        let dynamicID = Math.floor((Math.random() * 10000) + 1);
                        $dataRow.append(`<td><input type="text" autocomplete="off" name="${attrs?.dynamicHeader[keys[i]]}" placeholder="Enter ${attrs?.dynamicHeader[keys[i]]}" /></td>`);
                    }

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"> </td>');
                    $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddDynamicRow('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);

                    $dataRow.append($actionCell);
                    $tBody.append($dataRow[0])
                    $table.append($tBody[0]);
                }
            }
            if (meta.id === "replicationZFSTable") {
                const $table = document.querySelector(`#f-${id}`);
                if ($table.children.length == 0) {
                    let $headerRow = $('<tr class="header_zfstable"></tr>');
                    $headerRow.append('<th>PRProject Name</th>');
                    $headerRow.append('<th>PRServer</th>');
                    $headerRow.append('<th>PRPool Name</th>');
                    $headerRow.append('<th>PRAction Name</th>');
                    $headerRow.append('<th>PRID</th>');
                    $headerRow.append('<th>DRProject Name</th>');
                    $headerRow.append('<th>DRServer</th>');
                    $headerRow.append('<th>DRPool Name</th>');
                    $headerRow.append('<th>DRAction Name</th>');
                    $headerRow.append('<th>DRID</th>');
                    $headerRow.append('<th>Action</th>');
                    $table.append($headerRow[0]);

                    // Create a data row
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter PRProject Name" type="text" name="PRProject_Name"/></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="select-field-error"><select class="ZFSSelectInput" requiredTableSelectField="true" placeholder="Select PRServer" id="ZFSPRServer" name="PRServer"><option value="" hidden>Select PRServer</option><option value="PRServer_prod">PRServer_prod</option></select></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter PRPool Name" type="text" name="PRPool_Name" /></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter PRAction Name" type="text" name="PRAction_Name" /></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter PRID" type="text" name="PRID" /></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter DRProject Name" type="text" name="DRProject_Name"/></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="select-field-error"><select class="ZFSSelectInput" requiredTableSelectField="true" placeholder="Select DRServer" id="ZFSDRServer" name="DRServer"><option value="" hidden>Select DRServer</option><option value="DRServer_dr">DRServer_dr</option></select></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter DRPool Name" type="text" name="DRPool_Name" /></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter DRAction Name" type="text" name="DRAction_Name" /></div></div></td>');
                    $dataRow.append('<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" class="form-control" requiredTableTextField="true" placeholder="Enter DRID" type="text" name="DRID" /></div></div></td>');

                    $.ajax({
                        type: "GET",
                        url: RootUrl + 'Configuration/Server/GetServerListData',
                        dataType: 'json',
                        success: function (result) {
                            if (result.success) {
                                let PRServerData = result?.data?.filter(prserver => prserver.serverType && prserver.serverType.toLowerCase().includes('pr') && !prserver.serverType.toLowerCase().includes('near'));
                                let DRServerData = result?.data?.filter(drserver => drserver.serverType && drserver.serverType.toLowerCase().includes('dr') && !drserver.serverType.toLowerCase().includes('near'));
                                $('#ZFSPRServer').empty().append($('<option>').val("").text("Select PRServer"));
                                $('#ZFSDRServer').empty().append($('<option>').val("").text("Select DRServer"));
                                PRServerData?.forEach(function (item) {
                                    $('#ZFSPRServer').append($('<option>').val(item.id).text(item.name));
                                });
                                DRServerData?.forEach(function (item) {
                                    $('#ZFSDRServer').append($('<option>').val(item.id).text(item.name));
                                });

                            } else {
                                errorNotification(result)
                            }
                        }
                    });

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:73px" class="d-flex align-items-center  gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); handleAddZFSTable('${id}', this.parentElement.parentElement, event)"> <i  class="cp-add "></i></span>`);
                    $dataRow.append($actionCell);
                    $table.append($dataRow[0]);

                    $($table).on('keyup', '.ZFSTextInput', function () {
                        let $this = $(this);
                        let error = '.input-field-error';
                        ZFSTableValidaiton($this, error);
                    });

                    $($table).on('change', '.ZFSSelectInput', function () {
                        let $this = $(this);
                        let error = '.select-field-error';
                        ZFSTableValidaiton($this, error);
                    });
                }
            }
        }
    }
}

function getDBOptionsFromServer(type) {
    let dbType = (type === "all") ? "" : type;
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Database/GetDatabaseNames",
            method: 'GET',
            dataType: 'json',
            data: { type: dbType },
            success: function (result) {
                if (result.success) {
                    const options = [];
                    options.push({ value: '', text: '' });
                    result?.data?.forEach(function (item) {
                        options.push({ value: item.id, text: item.name });
                    });
                    resolve(options);
                } else {
                    errorNotification(result)
                    reject(result.message);
                }
            }
        });
    });
}

function getWorkflowsFromServer(isAction, id) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowNames",
            method: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.success) {
                    const options = [];
                    options.push({ value: '', text: '' });
                    $.each(result?.data, function (index, item) {
                        options.push({ value: item.id, text: item.name });
                    });
                    resolve(options);
                } else {
                    errorNotification(result)
                }
            }
        });
    });
}

function getWorkflowsActionsFromServer(id) {
    if (id) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: RootUrl + "ITAutomation/WorkflowList/GetWorkflowById",
                method: 'GET',
                dataType: 'json',
                data: { workflowId: id },
                success: function (result) {
                    if (result.success) {
                        const options = [];
                        let Properties = JSON.parse(result?.data.properties)
                        let propLength = Properties.nodes.length
                        for (let i = 0; i < propLength; i++) {
                            if (Properties?.nodes[i]?.hasOwnProperty('children')) {
                                Properties?.nodes[i]?.children?.forEach(function (obj) {
                                    let Obj = { 'id': obj.actionInfo.uniqueId, value: obj.actionInfo.actionName }
                                    options.push(Obj)
                                })
                            }
                            else if (!Properties.nodes[i].hasOwnProperty('groupName')) {
                                let obj = { 'id': Properties.nodes[i].actionInfo.uniqueId, value: Properties.nodes[i].actionInfo.actionName }
                                options.push(obj)
                            }
                        }
                        resolve(options);
                    } else {
                        errorNotification(result)
                    }
                }
            });
        });
    }
}

function getServerOptions(ServerRoleID, ServerTypeID) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Server/GetServerNames",
            method: 'GET',
            dataType: 'json',
            data: { 'roleType': ServerRoleID, 'serverType': ServerTypeID },
            success: function (result) {
                if (result.success) {
                    const options = [];
                    options.push({ value: '', text: 'Select Server' });
                    result.data.forEach(function (item) {
                        const option = { value: item.id, text: item.name };
                        let props = JSON.parse(item.properties)
                        Object.assign(option, props);
                        options.push(option);
                    });
                    resolve(options);
                } else {
                    errorNotification(result)
                    reject(result.message);
                }
            }
        });
    });
}

function getReplicationOptionsFromServer() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + "Configuration/Replication/GetReplicationNames",
            method: 'GET',
            dataType: 'json',
            success: function (result) {
                if (result.success) {
                    const options = [];
                    options.push({ value: '', text: '' });
                    result?.data?.forEach(function (item) {
                        options.push({ value: item.id, text: item.name });
                    });
                    resolve(options);
                } else {
                    errorNotification(result);
                }
            }
        });
    });
};

function populateReplicationDynamicFields(data, rSyncDataSyncRoboCopy = null) {
    let formData = JSON.parse(data);
    $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let fieldName = $(element).find('input, select, textarea, table').attr('name');
        let fieldClass = $(element).find('input, select, textarea, table').attr('class');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        let fieldType = $(element).find('input, select, textarea').attr('type');
        let fieldId = $(element).find('input, select, textarea, table').attr('id');

        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {
            let value = formData[fieldName];
            let checkbox = $(element).find('input[type="checkbox"]').attr("value")
            let chkValue = formData[checkbox];

            if (fieldType == 'radio') {
                $(element).find('input[type="radio"]').map((index, radio) => {
                    let radioValue = $(radio).val();
                    if (radioValue === formData[radioValue]) {
                        $(radio).prop('checked', true);
                    }
                });
            }
            if (typeof value === "boolean") {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change");
            }
            else if (fieldVal) {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            if (fieldType === 'checkbox') {

                // Handle checkbox differently since the value in formData is a boolean
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
            } else if (typeof value === "object") {
                if (value) {
                    const valuesArray = Object.values(value);
                    const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                    let labelsArray;
                    if (containsObject) {
                        labelsArray = valuesArray.map(item => item.value);
                    } else {
                        labelsArray = valuesArray.map(item => item);
                    }
                    const selectElement = $(element).find('select');
                    setTimeout(function () {
                        selectElement?.find('option').each(function () {
                            const optionValue = $(this).val();
                            if (labelsArray.includes(optionValue)) {
                                $(this).prop('selected', true);
                            }
                        });
                        selectElement.trigger('change');
                    }, 350)
                }
            }
            else {
                $(element).find('input, select, textarea').val(value); //.change();
                $(element).find('input, select, textarea').trigger("change");
            }

            if (fieldType == 'select') {
                let $select = $(element)?.find('select');

                if (($(`#${fieldId}`)?.val() == '' || $(`#${fieldId}`)?.val() == null) && value !== '') {
                    $select?.find('option')?.each(function () {
                        if (typeof value === 'string') {
                            if ($(this)?.val()?.toLowerCase() == value?.toLowerCase()) {
                                $(`#${fieldId}`)?.val($(this)?.val())?.trigger('change');
                            }
                        }
                    });
                }
            }

        }
        let replType = $('#ddlReplicationTypeID option:selected').text().toLowerCase();
        if (replType.includes("rsync")) {
            replType = "RsyncTable";
        }
        if (replType.includes("robocopy")) {
            replType = "RobocopyTable";
        }
        if (replType.includes("datasync")) {
            replType = "DataSyncTable";
        }
        if (fieldClass == 'replication-custom-table' && formData.hasOwnProperty(replType)) {
            if (formData[replType].length > 0) {
                let $tableId = $('.replication-custom-table').attr('id');
                let $table = document.querySelector(`#${$tableId}`);
                $table.innerHTML = '';
                sourcePath = $('.replication-custom-table').attr("source-path");
                destinationPath = $('.replication-custom-table').attr("destination-path");
                propertyType = $("#replicationNameType").text();
                let value = propertyType.toLowerCase().trim();
                let $headerRow = $('<tr class=".header_row2"></tr>');
                $headerRow.append(`<th>${sourcePath}</th>`);
                $headerRow.append(`<th>${destinationPath}</th>`);
                $headerRow.append(`<th> ${propertyType} Properties</th>`);
                $headerRow.append('<th>Action</th>');

                // Append the header row to the table
                $table?.append($headerRow[0]);
                let tableLength = formData[replType].length;
                let tableLength2 = tableLength - 1;
                let datat = rSyncDataSyncRoboCopy;

                for (let i = 0; i < tableLength; i++) {
                    let randomNumber = Math.floor((Math.random() * 1000000) + 1);
                    let replicationProperties = "replicationProperties" + randomNumber;
                    let $dataRow = $(`<tr></tr>`);
                    $dataRow.append(`<td class="d-none"> <input type="hidden" value="${rSyncDataSyncRoboCopy[tableLength2 - i]?.id || null}" name="tableID" /></td>`)
                    $dataRow.append(`<td>${inputSourceField(rSyncDataSyncRoboCopy[tableLength2 - i]?.sourceDirectory)}</td>`);
                    $dataRow.append(`<td>${inputDestinationField(rSyncDataSyncRoboCopy[tableLength2 - i]?.destinationDirectory)}</td>`);
                    $dataRow.append(`<td>${inputProperties(replicationProperties, i, tableLength - 1)}</td>`);
                    //$dataRow.append(`<td>${inputSourceField(formData[replType][i].sourceDirectory)}</td>`);
                    //$dataRow.append(`<td>${inputDestinationField(formData[replType][i].destinationDirectory)}</td>`);
                    //$dataRow.append(`<td>${inputProperties(replicationProperties, i, tableLength - 1)}</td>`);

                    if (formData[replType][i].properties !== "") {
                        if (value.includes("robocopy")) {
                            async function fetchData() {
                                let roboCopyData = await fetchRsyncData("ReplicationRoboCopyOptions");
                                if (roboCopyData?.success) {
                                    let replicationProperti = $('.' + replicationProperties);
                                    replicationProperti.empty();
                                    replicationProperti.append($('<option>').val("").text("Select Property"));
                                    roboCopyData?.data?.robocopy?.forEach(function (item) {
                                        replicationProperti.append($('<option>').val(item.id).text(item.name));
                                    });
                                    setTimeout(() => {
                                        replicationProperti.val(rSyncDataSyncRoboCopy[tableLength2 - i]?.roboCopyOptionsId).trigger('change');
                                        //replicationProperti.val(formData[replType][i].properties).trigger('change');
                                    }, 300);
                                }
                            }
                            fetchData();
                        }
                        else if (value.includes('datasync')) {
                            async function fetchData() {
                                let dataSyncData = await fetchRsyncData("ReplicationDataSyncProperties");
                                if (dataSyncData?.success) {
                                    let replicationProperti = $('.' + replicationProperties);
                                    replicationProperti.empty();
                                    replicationProperti.append($('<option>').val("").text("Select Property"));
                                    dataSyncData?.data?.dataSync?.forEach(function (item) {
                                        replicationProperti.append($('<option>').val(item.id).text(item.name));
                                    });
                                    setTimeout(() => {
                                        //replicationProperti.val(formData[replType][i].properties).trigger('change');
                                        replicationProperti.val(rSyncDataSyncRoboCopy[tableLength2 - i]?.dataSyncOptionId).trigger('change');
                                    }, 300);
                                }
                            }
                            fetchData();
                        }
                        else if (value.includes('rsync')) {
                            async function fetchData() {
                                let rsyncData = await fetchRsyncData("ReplicationRSyncOptions");
                                if (rsyncData?.success) {
                                    let replicationProperti = $('.' + replicationProperties);
                                    replicationProperti.empty();
                                    replicationProperti.append($('<option>').val("").text("Select Property"));
                                    rsyncData?.data?.rSync?.forEach(function (item) {
                                        replicationProperti.append($('<option>').val(item.id).text(item.name));
                                    });
                                    setTimeout(() => {
                                        //replicationProperti.val(formData[replType][i].properties).trigger('change');
                                        replicationProperti.val(rSyncDataSyncRoboCopy[tableLength2 - i]?.rsyncOptionId).trigger('change');
                                    }, 300);
                                }
                            }
                            fetchData();
                        }
                    } else {
                        let dynamicURL = value.includes("robocopy") ? "ReplicationRoboCopyOptions" :
                            value.includes("datasync") ? "ReplicationDataSyncProperties" :
                                value.includes("rsync") ? "ReplicationRSyncOptions" : "";
                        let resultType = value.includes("robocopy") ? "robocopy" :
                            value.includes("datasync") ? "dataSync" :
                                value.includes("rsync") ? "rSync" : "";
                        if (dynamicURL.length > 0) {
                            async function fetchData() {
                                let dynamicURLData = await fetchRsyncData(dynamicURL);
                                if (dynamicURLData?.success) {
                                    let replicationProperti = $('.' + replicationProperties);
                                    replicationProperti.empty();
                                    replicationProperti.append($('<option>').val("").text("Select Property"));
                                    dynamicURLData?.data[resultType]?.forEach(function (item) {
                                        replicationProperti.append($('<option>').val(item.id).text(item.name));
                                    });
                                    setTimeout(() => {
                                        $(".propertyError").text("").removeClass("field-validation-error");
                                    }, 300);
                                }
                            }
                            fetchData();
                        }
                    }
                    // Add action buttons to the data row
                    let $actionCell = $('<td></td>');
                    $actionCell.append(`<span role="button" title="Add"  onclick="event.preventDefault(); 
                                                handleAddReplicationTable('${$tableId}', this.parentElement.parentElement, event,'edit')"> 
                                            <i style="${i === (tableLength - 1) ? '' : 'display:none;'}" class="cp-add"></i>
                                        </span>`);

                    $actionCell.append(`<span role="button" title="Edit" class="editTableRow" onclick="editRow(event)">
                                            <i style="${i === (tableLength - 1) ? 'display:none;' : ''}" class="cp-edit"></i>
                                        </span>`);

                    //i !== (tableLength - 1) &&
                    $actionCell.append(`<span role="button" title="Delete" data-type='${value}' class="deleteTableRow" onclick="handleDeleteTableRow(event)">
                                            <i style="${tableLength === 1 ? 'display:none;' : ''}" class="cp-Delete"></i>
                                        </span>`);

                    $dataRow.append($actionCell);
                    $table?.append($dataRow[0]);
                    setTimeout(() => {
                        let lastRow = $('.replication-custom-table tr:last').get(0);
                        if (lastRow) {
                            const inputs = lastRow.querySelectorAll('input[type="text"]');
                            const selects = lastRow.querySelectorAll('select');

                            // Disable the first two input elements, if available
                            if (inputs.length >= 2) {
                                inputs[0].removeAttribute('disabled');
                                inputs[1].removeAttribute('disabled');
                            }

                            // Disable the first select element, if available
                            if (selects.length >= 1) {
                                selects[0].removeAttribute('disabled');
                            }

                            // Trigger focus on the input with class "sourceDirectory"
                            $(lastRow).find('input.sourceDirectory').trigger('focus');
                        }
                    }, 100)
                }
            }
        }

        if (fieldClass == 'vmPath-custom-table' && formData.hasOwnProperty('VmPathTable')) {
            if (formData.VmPathTable.length > 0) {
                let $tableId = $('.vmPath-custom-table').attr('id');
                let $table = document.querySelector(`#${$tableId}`);
                $table.innerHTML = '';
                var $headerRow = $('<tr class=".header_row3"></tr>');
                $headerRow.append('<th>VmName</th>');
                $headerRow.append('<th>PRVm(vmx) File Path</th>');
                $headerRow.append('<th>DRVm(vmx) File Path</th>');
                $headerRow.append('<th>PRDataStore Name</th>');
                $headerRow.append('<th>DRDataStore Name</th>');
                $headerRow.append('<th>Action</th>');
                $table.append($headerRow[0]);

                // Create a data row
                for (let i = 0; i < formData.VmPathTable.length; i++) {
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append('<td><input placeholder="Enter VmName" type="text" name="VmName" value="' + formData.VmPathTable[i].VmName + '" /></td>');
                    $dataRow.append('<td><input placeholder="Enter PRVm File Path" type="text" name="PRVmFilePath" value="' + formData.VmPathTable[i].PRVmFilePath + '"/></td>');
                    $dataRow.append('<td><input placeholder="Enter DRVm File Path" type="text" name="DRVmFilePath" value="' + formData.VmPathTable[i].DRVmFilePath + '" /> </td>');
                    $dataRow.append('<td><input placeholder="Enter PRDataStore Name" type="text" name="PRDataStoreName" value="' + formData.VmPathTable[i].PRDataStoreName + '" /> </td>');
                    $dataRow.append('<td><input placeholder="Enter DRDataStore Name" type="text" name="DRDataStoreName" value="' + formData.VmPathTable[i].DRDataStoreName + '" /> </td>');

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:62px" class="d-flex align-items-center  gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddVmTable('${$tableId}', this.parentElement.parentElement, event)">
                            <i class="cp-add"></i>
                       </span>`);
                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                                       <i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i>
                                   </span>`);
                    $dataRow.append($actionCell);
                    $table.append($dataRow[0]);
                }
            }
        }

        if (fieldClass == 'luns-custom-table' && formData.hasOwnProperty('lunstable')) {
            if (formData.lunstable.length > 0) {
                //let $tableId = $(element).find('table').attr('id');   
                let $tableId = $('.luns-custom-table').attr('id');
                let $table = document.querySelector(`#${$tableId}`);
                $table.innerHTML = '';
                lunHeaderOne = $('.luns-custom-table').attr("luns-Header-1");
                lunHeaderTwo = $('.luns-custom-table').attr("luns-Header-2");
                lunHeaderThree = $('.luns-custom-table').attr("luns-Header-3");
                lunHeaderFour = $('.luns-custom-table').attr("luns-Header-4");
                lunHeaderFive = $('.luns-custom-table').attr("luns-Header-5");
                lunHeaderSix = $('.luns-custom-table').attr("luns-Header-6");
                let $headerRow = $('<tr class=".header_row4"></tr>');
                $headerRow.append(`<th>${lunHeaderOne}</th>`);
                $headerRow.append(`<th>${lunHeaderTwo}</th>`);
                $headerRow.append(`<th>${lunHeaderThree}</th>`);
                $headerRow.append(`<th>${lunHeaderFour}</th>`);
                $headerRow.append(`<th>${lunHeaderFive}</th>`);
                $headerRow.append(`<th>${lunHeaderSix}</th>`);
                $headerRow.append('<th>Action</th>');
                $table.append($headerRow[0]);

                // Create a data row
                for (let i = 0; i < formData.lunstable.length; i++) {
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerOne" value="${formData.lunstable[i].headerOne}" id="lunsTableDescription1" placeholder="Enter ${lunHeaderOne}" class="Description"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerTwo" value="${formData.lunstable[i].headerTwo}" id="lunsTableDescription2" placeholder="Enter ${lunHeaderTwo}" class="Description"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerThree" value="${formData.lunstable[i].headerThree}" id="lunsTableDescription3" placeholder="Enter ${lunHeaderThree}" class="Description"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="LUNSTextInput" type="text" maxlength="4" requiredLunsTableTextField="true" name="headerFour" value="${formData.lunstable[i].headerFour}" id="lunsTableDescription4" placeholder="Enter ${lunHeaderFour}" class="Description"/></div></div></td>`);
                    $dataRow.append(`<td><input type="text" name="headerFive" value="${formData.lunstable[i].headerFive}" id="lunsTableDescription5" placeholder="Enter ${lunHeaderFive}" class="Description"/></td>`);
                    $dataRow.append(`<td><input type="text" name="headerSix" value="${formData.lunstable[i].headerSix}" id="lunsTableDescription6" placeholder="Enter ${lunHeaderSix}" class="Description"/></td>`);

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:73px" class="d-flex align-items-center gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddLunsTable('${$tableId}', this.parentElement.parentElement, event)">
                            <i class="cp-add"></i>
                       </span>`);
                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                                       <i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i>
                                   </span>`);
                    $dataRow.append($actionCell);
                    $table?.append($dataRow[0]);

                    $($table).on('keyup', '.LUNSTextInput', function () {
                        let $this = $(this);
                        let error = '.input-field-error';
                        LUNSTableValidaiton($this, error);
                    });
                }
            }
        }

        if (fieldClass == 'dynamic-custom-table' && formData.hasOwnProperty(fieldName)) {
            if (formData[fieldName].length > 0) {
                let tableIDCollection = document.getElementsByClassName('dynamic-custom-table');
                let tableID = tableIDCollection[0];
                let $tableId
                if (tableID) {
                    $tableId = $(tableID).attr('id');
                    let $table = document.querySelector(`#${$tableId}`);
                    $table.innerHTML = '';
                    let $tHead = $(' <thead> </thead>')
                    let $headerRow = $('<tr class="header_row5"></tr>');
                    for (let i = 0; i < formData[fieldName][0]?.length; i++) {
                        $headerRow.append(`<th>${formData[fieldName][0][i]}</th>`);
                    }
                    // Append the header row to the table
                    $tHead.append($headerRow[0])
                    $table.append($tHead[0]);
                    let $tBody = $('<tbody></tbody>');
                    for (let i = 0; i < formData[fieldName][1]?.length; i++) {
                        let $dataRow = $('<tr></tr>');
                        let values = Object.values(formData[fieldName][1][i]);
                        let keys = Object.keys(formData[fieldName][1][i]);
                        for (let j = 0; j < values.length; j++) {
                            $dataRow.append(`<td><input type="text" value="${values[j]}" name="${keys[j]}" placeholder="Enter ${formData[fieldName][0][j]}" /></td>`);
                        }

                        // Add action buttons to the data row
                        var $actionCell = $('<td style="height:62px" class="d-flex align-items-center justify-content-center"></td>');
                        $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddDynamicRow('${$tableId}', this.parentElement.parentElement, event)">
                                            <i class="cp-add"></i>
                                        </span>`);
                        i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                                       <i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i>
                                   </span>`);
                        $dataRow.append($actionCell);
                        $tBody.append($dataRow[0])
                        $table.append($tBody[0]);
                    }
                }
            }
        }

        if (fieldClass == 'replicationZFSTable' && formData.hasOwnProperty('replicationZFSTable')) {
            if (formData.replicationZFSTable.length > 0) {
                let $tableId = $('.replicationZFSTable').attr('id');
                let $table = document.querySelector(`#${$tableId}`);
                $table.innerHTML = '';
                let $ZFSheaderRow = $('<tr class="header_zfstable"></tr>');
                $ZFSheaderRow.append('<th>PRProject Name</th>');
                $ZFSheaderRow.append('<th>PRServer</th>');
                $ZFSheaderRow.append('<th>PRPool Name</th>');
                $ZFSheaderRow.append('<th>PRAction Name</th>');
                $ZFSheaderRow.append('<th>PRID</th>');
                $ZFSheaderRow.append('<th>DRProject Name</th>');
                $ZFSheaderRow.append('<th>DRServer</th>');
                $ZFSheaderRow.append('<th>DRPool Name</th>');
                $ZFSheaderRow.append('<th>DRAction Name</th>');
                $ZFSheaderRow.append('<th>DRID</th>');
                $ZFSheaderRow.append('<th>Action</th>');

                // Append the header row to the table
                $table.append($ZFSheaderRow[0]);

                // Create a data row
                for (let i = 0; i < formData.replicationZFSTable.length; i++) {
                    let $dataRow = $('<tr></tr>');
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].PRProject_Name}" requiredTableTextField="true" placeholder="Enter PRProject Name" type="text" name="PRProject_Name"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="select-field-error"><select class="ZFSSelectInput" requiredTableSelectField="true" placeholder="Select PRServer" id="ZFSPRServerEdit" name="PRServer"><option value="" hidden>Select PRServer</option><option ${formData.replicationZFSTable[i].PRServer === 'PRServer_prod' ? 'selected' : ''}>PRServer_prod</option></select></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].PRPool_Name}" requiredTableTextField="true" placeholder="Enter PRPool Name" type="text" name="PRPool_Name" /></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].PRAction_Name}" requiredTableTextField="true" placeholder="Enter PRAction Name" type="text" name="PRAction_Name" /></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].PRID}" requiredTableTextField="true" placeholder="Enter PRID" type="text" name="PRID" /></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].DRProject_Name}" requiredTableTextField="true" placeholder="Enter DRProject Name" type="text" name="DRProject_Name"/></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="select-field-error"><select class="ZFSSelectInput" requiredTableSelectField="true" placeholder="Select DRServer" id="ZFSDRServerEdit" name="DRServer"><option value="" hidden>Select DRServer</option><option ${formData.replicationZFSTable[i].DRServer === 'DRServer_dr' ? 'selected' : ''}>DRServer_dr</option></select></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].DRPool_Name}" requiredTableTextField="true" placeholder="Enter DRPool Name" type="text" name="DRPool_Name" /></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].DRAction_Name}" requiredTableTextField="true" placeholder="Enter DRAction Name" type="text" name="DRAction_Name" /></div></div></td>`);
                    $dataRow.append(`<td><div class="form-group"><div class="input-field-error"><input class="ZFSTextInput" autocomplete="off" value="${formData.replicationZFSTable[i].DRID}" requiredTableTextField="true" placeholder="Enter DRID" type="text" name="DRID" /></div></div></td>`);
                    $.ajax({
                        type: "GET",
                        url: RootUrl + 'Configuration/Server/GetServerListData',
                        dataType: 'json',
                        success: function (result) {
                            if (result.success) {
                                let PRServerData = result?.data?.filter(prserver => prserver.serverType && prserver.serverType.toLowerCase().includes('pr') && !prserver.serverType.toLowerCase().includes('near'));
                                let DRServerData = result?.data?.filter(drserver => drserver.serverType && drserver.serverType.toLowerCase().includes('dr') && !drserver.serverType.toLowerCase().includes('near'));
                                $('#ZFSPRServerEdit').empty().append($('<option>').val("").text("Select PRServer"));
                                $('#ZFSDRServerEdit').empty().append($('<option>').val("").text("Select DRServer"));
                                PRServerData?.forEach(function (item) {
                                    $('#ZFSPRServerEdit').append($('<option>').val(item.id).text(item.name));
                                });
                                DRServerData?.forEach(function (item) {
                                    $('#ZFSDRServerEdit').append($('<option>').val(item.id).text(item.name));
                                });
                                setTimeout(() => {
                                    $('#ZFSPRServerEdit').val(formData.replicationZFSTable[i].PRServer);
                                    $('#ZFSDRServerEdit').val(formData.replicationZFSTable[i].DRServer);
                                }, 500)

                            } else {
                                errorNotification(result)
                            }
                        }
                    });

                    // Add action buttons to the data row
                    let $actionCell = $('<td style="height:73px" class="d-flex align-items-center  gap-2"> </td>');
                    $actionCell.append(`<span role="button" title="Add" onclick="event.preventDefault(); handleAddZFSTable('${$tableId}', this.parentElement.parentElement, event)">
                            <i class="cp-add"></i>
                       </span>`);
                    i !== 0 && $actionCell.append(`<span role="button" title="Delete" class="delete-button">
                                       <i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i>
                                   </span>`);
                    $dataRow.append($actionCell);
                    $table?.append($dataRow[0]);

                    $(`#${$tableId}`).on('keyup', '.ZFSTextInput', function () {
                        let $this = $(this);
                        let error = '.input-field-error';
                        ZFSTableValidaiton($this, error);
                    });

                    $(`#${$tableId}`).on('change', '.ZFSSelectInput', function () {
                        let $this = $(this);
                        let error = '.select-field-error';
                        ZFSTableValidaiton($this, error);
                    });
                }
            }
        }
    });

    let encryption = $("input[type='password'][required]");
    encryption?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id).addEventListener("focus", async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await DecryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });

        document.getElementById(id).addEventListener('blur', async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await EncryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
    });
    btnCrudEnable('saveButton');
}

function LUNSTableValidaiton($this, error) {
    let value = $this.val();
    let associatedLabel = $this?.attr('placeholder');
    let toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
    let secondDiv = $this.closest(error);
    secondDiv.next(".field-validation-error").remove();
    if (value === "") {
        secondDiv.after("<span class='field-validation-error'> " + toLowerCase + "</span>");
    } else {
        if (/^[A-F0-9]+$/.test(value)) {
            return true;
        } else {
            secondDiv?.after("<span class='field-validation-error'> " + "A-F & 0-9" + "</span>");
            return false; // Indicate validation success
        }
    }
}

function ZFSTableValidaiton($this, error) {
    let value = $this.val();
    let associatedLabel = $this?.attr('placeholder');
    let toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
    let secondDiv = $this.closest(error);
    if (value === "") {
        secondDiv.next(".field-validation-error").remove();
        secondDiv.after("<span class='field-validation-error'> " + toLowerCase + "</span>");
    } else {
        secondDiv.next(".field-validation-error").remove();
    }
}

function sourceDirInput(sourceDir, replicationType, status = null) {
    //if change anything here change formbuildervalidation.js also sourceDestinationValidation()
    if (sourceDir?.length > 0) {
        validateSourceDir = [];
        let validate = [];
        sourceDir.each(function () {
            let $this = $(this);
            let $sourceFieldErrorSpan = $this.closest('.form-group').find('.sourceFieldError');
            let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
            let value = $this.val();
            $sourceFieldErrorSpan.text("").removeClass("field-validation-error");
            if (value) {
                if (replicationType.trim().toLowerCase().includes("rsync") ||
                    replicationType.trim().toLowerCase().includes("robocopy") ||
                    replicationType.trim().toLowerCase().includes("datasync")) {

                    if (rsyncRobocopyPath.test(value)) {
                        if (status) {
                            validate.push(true);
                        }
                        validateSourceDir.push(true);
                    } else {
                        $sourceFieldErrorSpan.text("Enter valid path").addClass("field-validation-error");
                        if (status) {
                            validate.push(false);
                        }
                        validateSourceDir.push(false);
                    }
                } else {
                    if (status) {
                        validate.push(true);
                    }
                    validateSourceDir.push(true);
                }
            } else {
                $sourceFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                if (status) {
                    validate.push(false);
                }
                validateSourceDir.push(false);
            }
        })
        if (status) {
            return validate;
        }
    }
}

function destinationDirInput(destinationDir, replicationType, status = null) {
    if (destinationDir?.length > 0) {
        validateDestinationDir = [];
        let validate = [];
        destinationDir.each(function () {
            let $this = $(this);
            let $destinationFieldErrorSpan = $this.closest('.form-group').find('.destinationFieldError');
            let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
            let value = $this.val();
            $destinationFieldErrorSpan.text("").removeClass("field-validation-error");
            if (value) {
                if (replicationType.trim().toLowerCase().includes("rsync") ||
                    replicationType.trim().toLowerCase().includes("robocopy") ||
                    replicationType.trim().toLowerCase().includes("datasync")) {

                    if (rsyncRobocopyPath.test(value)) {
                        if (status) {
                            validate.push(true);
                        }
                        validateDestinationDir.push(true);
                    } else {
                        $destinationFieldErrorSpan.text("Enter valid path").addClass("field-validation-error");
                        validateDestinationDir.push(false);
                        if (status) {
                            validate.push(false);
                        }
                    }
                } else {
                    if (status) {
                        validate.push(true);
                    }
                    validateDestinationDir.push(true);
                }

            } else {
                $destinationFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                if (status) {
                    validate.push(false);
                }
                validateDestinationDir.push(false);
            }
        });
        if (status) {
            return validate;
        }
    }
}

function rsyncRobocopy(id, newRow, event, action) {
    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
    const clonedRow = newRow.cloneNode(true);
    const inputElements = clonedRow.querySelectorAll('input');
    const selectElements = clonedRow.querySelectorAll('select');
    inputElements.forEach(input => {
        input.value = '';
        input.removeAttribute("disabled");
    });

    selectElements.forEach(select => {
        select.value = '';
        select.text = '';
        select.removeAttribute("disabled");
        select.classList.add("replicationProperties");
    });

    // Check if the cloned row already has a delete button
    const hasAddButton = newRow.querySelector('.cp-add');
    const hasDeleteButton = newRow.querySelector('.cp-Delete');
    const hasEdit = newRow.querySelector('.cp-edit');
    const clonedAdd = clonedRow.querySelector('.cp-add');
    const clonedDelete = clonedRow.querySelector('.cp-Delete');
    const clonedEdit = clonedRow.querySelector('.cp-edit');

    if (hasAddButton) {
        const inputs = newRow.querySelectorAll('input[type="text"]');
        const selects = newRow.querySelectorAll('select');

        if (inputs.length >= 2) {
            inputs[0].setAttribute('disabled', 'true');
            inputs[1].setAttribute('disabled', 'true');
        }

        if (selects.length >= 1) {
            selects[0].setAttribute('disabled', 'true');
        }

        hasAddButton.style.display = "none";
        if (!hasEdit) {
            let addEditButton = hasAddButton.closest('span[role="button"]');
            addEditButton.insertAdjacentHTML(
                'beforebegin',
                `<span role="button" title="Edit" onclick="editRow(event)" class="editTableRow">
                <i class="cp-edit"></i>
            </span>`
            );
        } else {
            hasEdit.style.display = '';
        }

        let rowCount = $(".replication-custom-table tr").length;

        if (!hasDeleteButton) {
            const lastTd = newRow.querySelector('td:last-child');
            lastTd.innerHTML += '<span role="button" title="Delete" class="deleteTableRow" onclick="handleDeleteTableRow(event)"><i class="cp-Delete"></i></span>';
        } else {
            hasDeleteButton.style.display = '';
        }

        let rsyncAndRobocopyLength = $('.replication-custom-table').find('tr').length;
        if (rsyncAndRobocopyLength > 1) {
            if (!clonedDelete) {
                const lastTd2 = clonedRow.querySelector('td:last-child');
                lastTd2.innerHTML += '<span role="button" title="Delete" class="deleteTableRow" onclick="handleDeleteTableRow(event)"><i class="cp-Delete"></i></span>';
            } else {
                clonedDelete.style.display = '';
            }
            if (clonedEdit) {
                clonedEdit.style.display = 'none';
            }
        }
    }
    table.appendChild(clonedRow);
}

function updateRow(event) {
    const currentRow = $(event.target).closest('tr');
    currentRow.find('.editTableRow').attr({ title: 'Edit', onclick: 'editRow(event)' });
    currentRow.find('.cp-up-linearrow').removeClass('cp-up-linearrow').addClass('cp-edit');
    currentRow.find('.cp-error').removeClass('cp-error text-danger').addClass('cp-Delete');
    currentRow.find('input, select').attr('disabled', true);
    currentRow.find('.deleteTableRow').attr({ title: 'Delete', onclick: 'handleDeleteTableRow(event)' });
}

function editRow(event) {
    const currentRow = $(event.target).closest('tr');
    const otherRows = currentRow.siblings('tr').not(':last');

    otherRows.find('input, select').attr('disabled', true);
    otherRows.find('.editTableRow').attr({ title: 'Edit', onclick: 'editRow(event)' });
    otherRows.find('.cp-up-linearrow').removeClass('cp-up-linearrow').addClass('cp-edit');
    otherRows.find('.deleteTableRow').attr({ title: 'Delete', onclick: 'handleDeleteTableRow(event)' });
    otherRows.find('.cp-error').removeClass('cp-error text-danger').addClass('cp-Delete');

    currentRow.find('input, select').removeAttr('disabled');
    currentRow.find('.editTableRow').attr({ title: 'Update', onclick: 'updateRow(event)' });
    currentRow.find('.cp-edit').removeClass('cp-edit').addClass('cp-up-linearrow');
    currentRow.find('.deleteTableRow').attr({ title: 'Cancel', onclick: 'updateRow(event)' });
    currentRow.find('.cp-Delete').removeClass('cp-Delete').addClass('cp-error text-danger');

    // Auto-focus on the 'sourceDirectory' input
    currentRow.find('input.sourceDirectory').trigger('focus');
}

function handleAddReplicationTable(id, newRow, event, action) {
    event.preventDefault();
    let replicationType = $('#ddlReplicationTypeID option:selected').text().toLowerCase();
    let sourceDir = $('.sourceDirectory');
    sourceDirInput(sourceDir, replicationType);
    let destinationDir = $('.destinationDirectory');
    destinationDirInput(destinationDir, replicationType);
    let propertyValue = $('select[class^="replicationProperties"]');//$('.replicationProperties');

    if (propertyValue?.length > 0) {
        validateProperties = [];
        propertyValue.each(function () {
            let $this = $(this);
            let $propertyFieldErrorSpan = $this.closest('.form-group').find('.propertyError');
            let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
            let value = $this.val();
            $propertyFieldErrorSpan.text("").removeClass("field-validation-error");
            if (value) {
                validateProperties.push(true);
            } else {
                validateProperties.push(false);
                $propertyFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
            }
        })
    }
    let validateSource = validateSourceDir.every(result => result);
    let validateDestination = validateDestinationDir.every(result => result);
    let validateProps = validateProperties.every(result => result);

    if (validateSource && validateDestination && validateProps) {
        rsyncRobocopy(id, newRow, event, action)
    }
}

function handleDeleteTableRow(event, tablename = null) {
    event.preventDefault(); // Prevent default form submission behavior   
    $("#DeleteModalRsyncTableRow").modal("show");
    const currentRow = $(event.target).closest('tr');
    const sourceDirectoryValue = currentRow.find('.sourceDirectory').val();
    const destinationDirectoryValue = currentRow.find('.destinationDirectory').val();
    const replicationPropertiesValue = currentRow.find('[class^="replicationProperties"] option:selected').val(); //without number.
    const target = event.currentTarget;
    const dataType = target.getAttribute('data-type');

    document.getElementById("deleteRsyncTableRow").addEventListener('click', async function () {
        $(event.target).closest('tr').remove();
        $("#DeleteModalRsyncTableRow").modal("hide");

        let rsyncAndRobocopy = $('.replication-custom-table').find('tr').length;
        if (rsyncAndRobocopy === 2) {
            const table = document.querySelector('.replication-custom-table');
            const addButton = table.querySelector('.cp-add');
            const editButton = table.querySelector('.cp-edit');
            const deleteButton = table.querySelector('.cp-Delete');

            let lastRow = $('.replication-custom-table tr:last').get(0);
            if (lastRow) {
                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 2) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('input.sourceDirectory').trigger('focus');
            }
            if (editButton) {
                editButton.style.display = 'none';
            }
            if (addButton) {
                addButton.style.display = '';
            }
            if (deleteButton) {
                deleteButton.style.display = 'none';
            }
        } else {
            let lastRow = $('.replication-custom-table tr:last').get(0);

            if (lastRow) {
                const hasAddButton = lastRow.querySelector('.cp-add');
                const hasDeleteButton = lastRow.querySelector('.cp-Delete');
                const hasEdit = lastRow.querySelector('.cp-edit');

                const inputs = lastRow.querySelectorAll('input[type="text"]');
                const selects = lastRow.querySelectorAll('select');

                // Disable the first two input elements, if available
                if (inputs.length >= 2) {
                    inputs[0].removeAttribute('disabled');
                    inputs[1].removeAttribute('disabled');
                }

                // Disable the first select element, if available
                if (selects.length >= 1) {
                    selects[0].removeAttribute('disabled');
                }

                // Trigger focus on the input with class "sourceDirectory"
                $(lastRow).find('input.sourceDirectory').trigger('focus');

                // Hide the edit button and show the add button, if they exist
                if (hasEdit.style.display !== "none") {
                    hasEdit.style.display = 'none';
                }
                if (hasAddButton.style.display === "none") {
                    hasAddButton.style.display = '';
                }
            }
        }

        if (dataType.includes("rsync") || dataType.includes("robocopy") || dataType.includes("datasync")) {
            if (replicationPropertiesValue && sourceDirectoryValue && destinationDirectoryValue) {
                $.ajax({
                    type: "POST",
                    url: RootUrl + 'Configuration/Replication/DeleteSubTypes',
                    data: {
                        "Type": dataType,
                        "optionId": replicationPropertiesValue,
                        "sourcePath": sourceDirectoryValue,
                        "targetPath": destinationDirectoryValue
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.success) {

                        } else {
                            errorNotification(response)
                        }
                    }
                })
            }
        }
    });
}

async function tableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this?.attr('placeholder');
            var toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
            let inputValue2 = $this?.val();
            $this.next(".dynamic-input-field").remove();
            if (inputValue2 === "") {
                $this.after("<div class='dynamic-input-field table-field-zfsvalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                return false; // Indicate validation failure
            }
            else {
                return true; // Indicate validation success
            }
        } else {
            return true;
        }
    }));

    // Check if any validation failed
    return validationResults.every(result => result);
}

async function handleAddZFSTable(id, newRow, event) {
    let textFieldValidation = true;
    let selectFieldValidation = true;
    let requiredTableSelectField = $("select[requiredtableselectfield]");
    if (requiredTableSelectField?.length > 0) {
        requiredTableSelectField?.each(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                selectFieldValidation = await zfsTableSelectValidation(); //await tableFieldValidation($this);
            }
        });
    }
    let requiredTableTextField = $("input[requiredTableTextField]");
    if (requiredTableTextField?.length > 0) {
        requiredTableTextField?.each(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                textFieldValidation = await zfsTableValidation(); //await tableFieldValidation($this);
            }
        });
    }
    setTimeout(() => {
        if (textFieldValidation && selectFieldValidation) {
            handleAddVmTable(id, newRow, event)
        }
    }, 300)
}

async function handleAddLunsTable(id, newRow, event) {
    let textFieldValidation = false;
    let requiredLunsTableTextField = $("input[requiredLunsTableTextField]");
    if (requiredLunsTableTextField?.length > 0) {
        let fieldValidationResult = await lunsTableFieldValidation(requiredLunsTableTextField);
        textFieldValidation = fieldValidationResult;
    }
    setTimeout(() => {
        if (textFieldValidation) {
            event.preventDefault();
            const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
            const clonedRow = newRow.cloneNode(true);
            const inputElements = clonedRow.querySelectorAll('input');
            const selectElements = clonedRow.querySelectorAll('select');
            inputElements.forEach(input => { input.value = ''; });
            selectElements.forEach(select => { select.value = ''; });
            const hasDeleteButton = clonedRow.querySelector('.delete-button');
            if (!hasDeleteButton) {
                const lastTd = clonedRow.querySelector('td:last-child');
                lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i></span>';
            }
            table.appendChild(clonedRow);
        }
    }, 500);
}

function handleAddVmTable(id, newRow, event) {
    event.preventDefault();
    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
    const clonedRow = newRow.cloneNode(true);
    const inputElements = clonedRow.querySelectorAll('input');
    const selectElements = clonedRow.querySelectorAll('select');
    inputElements.forEach(input => { input.value = ''; });
    selectElements.forEach(select => { select.value = ''; });
    const hasDeleteButton = clonedRow.querySelector('.delete-button');
    if (!hasDeleteButton) {
        const lastTd = clonedRow.querySelector('td:last-child');
        lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i></span>';
    }
    table.appendChild(clonedRow);
}

async function fetchRsyncData(dynamicurl) {
    const response = await fetch(RootUrl + `Configuration/Replication/${dynamicurl}`);

    if (!response.ok) {
        throw new Error('Network response was not ok');
    }
    const result = await response.text();
    let res = JSON?.parse(result);

    if (res?.success) {
        return res;
    } else {
        notificationAlert("warning", res.message);
    }
}

async function handleAddDynamicRow(id, newRow, event) {
    event.preventDefault();
    const table = document.querySelector(`#f-${id}`) ? document.querySelector(`#f-${id}`) : document.querySelector(`#${id}`);
    const clonedRow = newRow.cloneNode(true);
    const inputElements = clonedRow.querySelectorAll('input');
    inputElements.forEach(input => { input.value = ''; });

    // Check if the cloned row already has a delete button
    const hasDeleteButton = clonedRow.querySelector('.delete-button');
    if (!hasDeleteButton) {
        const lastTd = clonedRow.querySelector('td:last-child');
        lastTd.innerHTML += '<span role="button" title="Delete" class="delete-button"><i onclick="handleDeleteTableRow(event)" class="cp-Delete"></i></span>';
    }
    table.appendChild(clonedRow);
}

function saveReplicationFormFields() {
    let formData = {};
    let promises = [];

    // Iterate through form fields and populate formData object
    $('#formRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let $this = $(this);
        if ($this.is(':visible')) {
            let fieldName = $(element).find('input, select, textarea').attr('name');
            let fieldVal = $(element).find('input').attr('value');
            let fieldType = $(element).find('input, select, textarea').attr('type');
            let value;
            if (fieldType === "date") {
                value = $(element).find('input[type="date"]').val();
                formData[fieldName] = value;
            }
            const isTable = $(element).find('table').attr('type');
            const isTableName = $(element).find('table');
            const tableClassName = isTableName.attr('class');
            const tableName = isTableName.attr('name');
            if (fieldType === 'checkbox') {
                value = $(element).find('input[type="checkbox"]').prop('checked');
            } else if (fieldType === 'radio') {
                value = $(element).find('input[type="radio"]:checked').val();
                formData[value] = value;
            } else {
                value = $(element).find('input, select, textarea').val();
            }

            // Save field name as key and field value as value in formData object
            if (fieldType === "checkbox") {
                formData[fieldVal] = value;
            }
            if (fieldType === "password" && (value && value !== "") && value.length < 64) {
                promises.push(await EncryptPassword(value).then(encryptedPassword => {
                    formData[fieldName] = encryptedPassword;
                }));
            }
            else {
                formData[fieldName] = value;
            }
            if (isTable === "replicationtable") {
                let replicationType = $('#ddlReplicationTypeID option:selected').text().toLowerCase();
                if (replicationType.includes("rsync")) {
                    formData["RsyncTable"] = extractRepFormData($(element), promises)
                }
                if (replicationType.includes("robocopy")) {
                    formData["RobocopyTable"] = extractRepFormData($(element), promises)
                }
                if (replicationType.includes("datasync")) {
                    formData["DataSyncTable"] = extractRepFormData($(element), promises)
                }
            }
            if (isTable === "vmpathtable") {
                formData["VmPathTable"] = extractVmPathData($(element), promises)
            }
            if (isTable === "lunstable") {
                formData["lunstable"] = extractLunsTableData($(element), promises)
            }
            if (isTableName.length !== 0 && tableClassName === 'dynamic-custom-table') {
                formData[tableName] = extractDynamicFormData($(element), promises)
            }
            if (isTableName.length !== 0 && tableClassName === 'replicationZFSTable') {
                formData["replicationZFSTable"] = extractReplicationZFSTableData($(element), promises)
            }
        }
    });
    var formDataJson = JSON.stringify(formData);

    // Set the JSON string to a hidden input field (if needed)
    var hiddenInput = document.getElementById('Props');
    if (hiddenInput) {
        hiddenInput.value = formDataJson;
    }
    return formData;
}

function extractRepFormData(element, promises) {
    let formDataArray = [];
    const rows = $(element).find('tr:not(.header_row2)');
    const headerRow = $(element).find('th');
    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex) {
            const header = $(this).find('input, select').attr('name') || $(this).text();
            const value = ($(this).find('select') ? $(this).find('select').find('option:selected').val() ? $(this).find('select').find('option:selected').val() :
                $(this).find('input').val() : '') || '';

            if (header && header?.trim() !== '') {
                rowDataObj[header] = value;
            }
        });
        if (Object.keys(rowDataObj).length !== 0) {
            formDataArray.push(rowDataObj);
        }
    });
    formDataArray = formDataArray.filter(s => s.SubstituteAuthenticationType != '');
    return formDataArray;
}

function extractVmPathData(element, promises) {
    let formDataArray = [];
    const rows = $(element).find('tr:not(.header_row3)');
    const headerRow = $(element).find('th');
    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex) {
            const header = $(this).find('input').attr('name') || $(this).text();
            const value = ($(this).find('input').val() ? $(this).find('input').val() : '');
            if (header && header?.trim() !== '') {
                rowDataObj[header] = value;
            }
        });
        if (Object.keys(rowDataObj).length !== 0) {
            formDataArray.push(rowDataObj);
        }
    });
    return formDataArray;
}

function extractLunsTableData(element, promises) {
    let formDataArray = [];
    const rows = $(element).find('tr:not(.header_row4)');
    const headerRow = $(element).find('th');
    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex) {
            const header = $(this).find('input').attr('name') || $(this).text();
            const value = ($(this).find('input').val() ? $(this).find('input').val() : '');
            if (header && header?.trim() !== '') {
                rowDataObj[header] = value;
            }
        });
        if (Object.keys(rowDataObj).length !== 0) {
            formDataArray.push(rowDataObj);
        }
    });
    return formDataArray;
}

function extractDynamicFormData(element, promises) {
    let formDataArray = [];
    let TableData = [];
    let TableHeader = [];
    const rows = $(element).find('tr:not(header_row5)'); // Extracting data from each row (excluding header row)
    const headerRow = $(element).find('th');
    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex, data) {
            let header = $(this).find('input').attr('name') || $(this).text();
            const value = $(this).find('input').val();
            if (header && header?.trim() !== '') {
                header = header.toLowerCase().replaceAll(" ", "");
                rowDataObj[header] = value;
            }
        });
        if (Object.keys(rowDataObj).length !== 0) {
            TableData.push(rowDataObj);
        }
    });
    headerRow.each(async function (Index, data) {
        const headerText = data.innerHTML
        if (headerText && headerText?.trim() !== '') {
            TableHeader.push(headerText);;
        }
    });
    formDataArray.push(TableHeader);
    formDataArray.push(TableData);
    promises.push(true);
    return formDataArray;
}

function extractReplicationZFSTableData(element, promises) {
    let formDataArray = [];
    const rows = $(element).find('tr:not(.header_zfstable)');
    const headerRow = $(element).find('th');
    rows.each(async function () {
        const rowDataObj = {};
        const columns = $(this).find('td');
        columns.each(function (colIndex) {
            const header = $(this).find('input, select').attr('name') || $(this).text();
            const value = ($(this).find('select') ? $(this).find('select').find('option:selected').val() ? $(this).find('select').find('option:selected').val() :
                $(this).find('input').val() : '') || '';
            if (header && header?.trim() !== '') {
                rowDataObj[header] = value;
            }
        });
        if (Object.keys(rowDataObj).length !== 0) {
            formDataArray.push(rowDataObj);
        }
    });
    return formDataArray;
}
