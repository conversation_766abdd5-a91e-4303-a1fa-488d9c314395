﻿namespace ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;

public class
    GetInfrastructureSummaryQueryHandler : IRequestHandler<GetInfrastructureSummaryQuery,
        List<GetInfrastructureSummaryVm>>
{
    private readonly ICyberComponentRepository _cyberComponentRepository;
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public GetInfrastructureSummaryQueryHandler(ICyberComponentRepository cyberComponentRepository,
        IServerViewRepository serverViewRepository, IMapper mapper)
    {
        _cyberComponentRepository = cyberComponentRepository;
        _serverViewRepository = serverViewRepository;
        _mapper = mapper;
    }

    public async Task<List<GetInfrastructureSummaryVm>> Handle(GetInfrastructureSummaryQuery request,
        CancellationToken cancellationToken)
    {
        var cyberInfra = new List<GetInfrastructureSummaryVm>();

        var cyberComponents = await _cyberComponentRepository.ListAllAsync();

        var groupByComponentType = cyberComponents
            .GroupBy(x => x.ServerType.ToLower())
            .Select(x => new
            {
                EntityType = ToPascalCase(x.Key),
                Count = x.Count(),
                List = x.ToList()
            }).ToList();

        var allIds = new List<string>();

        groupByComponentType.ToList().ForEach(group =>
        {
            group.List.ToList().ForEach(cyberCom =>
            {
                var jArray = JArray.Parse(cyberCom.Properties);

                jArray.ToList().ForEach(item =>
                {
                    var id = item.SelectToken("$.id")?.ToString();
                    if (id.IsNotNullOrWhiteSpace())
                    {
                        allIds.Add(id);
                    }
                });
            });
        });
        if (!allIds.Any())
        {
            return cyberInfra;
        }

        var servers = await _serverViewRepository.GetByServerIdsAsync(allIds);

        groupByComponentType.ToList().ForEach(group =>
        {
            var cyberInfrastructureSummaryVms = new GetInfrastructureSummaryVm
            {
                EntityType = group.EntityType,
                Count = group.Count
            };

            group.List.ToList().ForEach(cyberCom =>
            {
                var jArray = JArray.Parse(cyberCom.Properties);

                jArray.ToList().ForEach(item =>
                {
                    var id = item.SelectToken("$.id")?.ToString();

                    if (id.IsNotNullOrWhiteSpace())
                    {
                        var server = servers.FirstOrDefault(s => s.ReferenceId == id);

                        if (server is not null)
                        {
                            var serverVm = _mapper.Map<ServerInfrastructureSummaryVm>(server);
                            cyberInfrastructureSummaryVms.ServerInfrastructureSummaryVms.Add(serverVm);
                        }
                    }
                });
            });

            cyberInfra.Add(cyberInfrastructureSummaryVms);
        });
        return cyberInfra;
        //foreach (var group in groupByComponentType)
        //{
        //    var cyberInfrastructureSummaryVms = new GetInfrastructureSummaryVm();

        //    cyberInfrastructureSummaryVms.EntityType = group.EntityType;
        //    cyberInfrastructureSummaryVms.Count = group.Count;

        //    foreach (var cyberCom in group.List)
        //    {
        //        var jArray = JArray.Parse(cyberCom.Properties);

        //        foreach (var item in jArray)
        //        {
        //            var id = item.SelectToken("$.id")?.ToString();

        //            if (id.IsNotNullOrWhiteSpace())
        //            {
        //                var server = await _serverViewRepository.GetByReferenceIdAsync(id);

        //                if (server is not null)
        //                {
        //                    var serverVm = _mapper.Map<ServerInfrastructureSummaryVm>(server);

        //                    cyberInfrastructureSummaryVms.ServerInfrastructureSummaryVms.Add(serverVm);
        //                }
        //            }
        //        }
        //    }

        //    cyberInfra.AddRangeAsync(cyberInfrastructureSummaryVms);
        //}

        //return cyberInfra;
    }

    public static string ToPascalCase(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        // Split by spaces or underscores (if required)
        return string.Join("", text.Split(new[] { ' ', '_' }, StringSplitOptions.RemoveEmptyEntries)
            .Select(word => char.ToUpper(word[0]) + word.Substring(1).ToLower()));
    }
}