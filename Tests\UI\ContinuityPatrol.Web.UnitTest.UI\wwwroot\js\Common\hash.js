
$(function () {    
   // localStorage.removeItem("password") 
    //$("#txtPassword").on('blur', function () {
    //    hashPassword("txtLoginName", "txtPassword");
    //});
})
let decryptpassword
async function hashPassword(loginName, password, isAdChecked = false, passwordCopy = '') {
    var name = $("#" + loginName).val();

    var plainPassword = isAdChecked ? passwordCopy : $("#" + password).val();
 
    if (plainPassword != "" && plainPassword.length > 0 && plainPassword.length < 64 && name != "" && name.length > 0) {       
        await $.ajax({
            type: "GET",
            url: RootUrl + "Account/HashPassword",
            data: {
                'loginName': name,
                'password': plainPassword,
                'adChecked': isAdChecked
            },
            dataType: "json",
            async: true,
            success: function (response) {
                if (response != "") {
                    $("#" + password).val(response.encrypt);
                    
                }
            }
        });
    }
     decryptpassword = $("#txtPassword").val()
    
}

   $('.toggle-password').on("click", function () {
    if ($("#txtPassword").val() != "") {
        $("#txtPassword").val(decryptpassword)
           document.getElementById("txtPassword").focus();
    }
   })
//$("#Login").on("click", function () {

//    decryptpassword=""
//        hashPassword("txtLoginName", "txtPassword");
//    })
function prevent_back() {
    window.history.forward()
}
setTimeout("prevent_back()", 0)
window.onunload = function () {null }