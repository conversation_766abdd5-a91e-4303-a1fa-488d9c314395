using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftParameterFixture : IDisposable
{
    public List<DriftParameterListVm> DriftParameterListVm { get; set; }
    public List<DriftParameter> DriftParameters { get; set; }
    public DriftParameterDetailVm DriftParameterDetailVm { get; set; }
    public CreateDriftParameterCommand CreateDriftParameterCommand { get; set; }
    public CreateDriftParameterResponse CreateDriftParameterResponse { get; set; }
    public UpdateDriftParameterCommand UpdateDriftParameterCommand { get; set; }
    public UpdateDriftParameterResponse UpdateDriftParameterResponse { get; set; }
    public DeleteDriftParameterCommand DeleteDriftParameterCommand { get; set; }
    public DeleteDriftParameterResponse DeleteDriftParameterResponse { get; set; }
    public GetDriftParameterPaginatedListQuery GetDriftParameterPaginatedListQuery { get; set; }
    public PaginatedResult<DriftParameterListVm> DriftParameterPaginatedResult { get; set; }
    public GetDriftParameterNameUniqueQuery GetDriftParameterNameUniqueQuery { get; set; }

    public DriftParameterFixture()
    {
        DriftParameterListVm = AutoDriftParameterFixture.Create<List<DriftParameterListVm>>();
        DriftParameterDetailVm = AutoDriftParameterFixture.Create<DriftParameterDetailVm>();
        CreateDriftParameterCommand = AutoDriftParameterFixture.Create<CreateDriftParameterCommand>();
        CreateDriftParameterResponse = AutoDriftParameterFixture.Create<CreateDriftParameterResponse>();
        UpdateDriftParameterCommand = AutoDriftParameterFixture.Create<UpdateDriftParameterCommand>();
        UpdateDriftParameterResponse = AutoDriftParameterFixture.Create<UpdateDriftParameterResponse>();
        DeleteDriftParameterCommand = AutoDriftParameterFixture.Create<DeleteDriftParameterCommand>();
        DeleteDriftParameterResponse = AutoDriftParameterFixture.Create<DeleteDriftParameterResponse>();
        DriftParameters = AutoDriftParameterFixture.Create<List<DriftParameter>>();
        GetDriftParameterPaginatedListQuery = AutoDriftParameterFixture.Create<GetDriftParameterPaginatedListQuery>();
        DriftParameterPaginatedResult = AutoDriftParameterFixture.Create<PaginatedResult<DriftParameterListVm>>();
        GetDriftParameterNameUniqueQuery = AutoDriftParameterFixture.Create<GetDriftParameterNameUniqueQuery>();
    }

    public Fixture AutoDriftParameterFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateDriftParameterCommand>(p => p.Name, 10));
            fixture.Customize<CreateDriftParameterCommand>(c => c.With(b => b.DriftCategoryId, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftParameterCommand>(c => c.With(b => b.DriftImpactTypeId, Guid.NewGuid().ToString));
            fixture.Customize<CreateDriftParameterCommand>(c => c.With(b => b.Severity, 1));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateDriftParameterCommand>(p => p.Name, 10));
            fixture.Customize<UpdateDriftParameterCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftParameterCommand>(c => c.With(b => b.DriftCategoryId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftParameterCommand>(c => c.With(b => b.DriftImpactTypeId, Guid.NewGuid().ToString));
            fixture.Customize<UpdateDriftParameterCommand>(c => c.With(b => b.Severity, 2));

            fixture.Customize<DeleteDriftParameterCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<CreateDriftParameterResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Success, true));

            fixture.Customize<UpdateDriftParameterResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.Success, true));

            fixture.Customize<DeleteDriftParameterResponse>(c => c
                .With(b => b.Success, true)
                .With(b => b.IsActive, false));

            fixture.Customize<DriftParameter>(c => c.With(b => b.IsActive, true));
            fixture.Customize<DriftParameter>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameter>(c => c.With(b => b.Severity, 1));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DriftParameterListVm>(p => p.Name, 10));
            fixture.Customize<DriftParameterListVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterListVm>(c => c.With(b => b.DriftCategoryId, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterListVm>(c => c.With(b => b.DriftImpactTypeId, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterListVm>(c => c.With(b => b.Severity, 1));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<DriftParameterDetailVm>(p => p.Name, 10));
            fixture.Customize<DriftParameterDetailVm>(c => c.With(b => b.Id, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterDetailVm>(c => c.With(b => b.DriftCategoryId, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterDetailVm>(c => c.With(b => b.DriftImpactTypeId, Guid.NewGuid().ToString));
            fixture.Customize<DriftParameterDetailVm>(c => c.With(b => b.Severity, 1));
            fixture.Customize<DriftParameterDetailVm>(c => c.With(b => b.IsActive, true));

            fixture.Customize<GetDriftParameterPaginatedListQuery>(c => c.With(b => b.PageNumber, 1));
            fixture.Customize<GetDriftParameterPaginatedListQuery>(c => c.With(b => b.PageSize, 10));

            fixture.Customize<GetDriftParameterNameUniqueQuery>(c => c.With(b => b.Id, Guid.NewGuid().ToString));

            fixture.Customize<PaginatedResult<DriftParameterListVm>>(c => c.With(b => b.Succeeded, true));
            fixture.Customize<PaginatedResult<DriftParameterListVm>>(c => c.With(b => b.PageSize, 10));
            fixture.Customize<PaginatedResult<DriftParameterListVm>>(c => c.With(b => b.CurrentPage, 1));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
