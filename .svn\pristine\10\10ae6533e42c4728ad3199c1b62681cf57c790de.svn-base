let mId = sessionStorage.getItem("monitorId");
let monitortype = 'OpenShift';
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => { Ocpmonitorstatus(mId, monitortype) }, 250)
//setTimeout(() => { msSQLServer(infraObjectId) }, 250)
//setTimeout(() => { monitoringSolution(infraObjectId) }, 250)

//$('#mssqlserver').hide();
//async function msSQLServer(id) {

//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);

//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        $('#mssqlserver').show();
//        bindMSSQLServer(mssqlServerData)
//    } else {
//        $('#mssqlserver').hide();
//    }

//}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

//function bindMSSQLServer(mssqlServerData) {
//    const rowsValue = mssqlServerData?.map((list, i) => {
//        let serverName = "";

//        if (list.workflowName && (list.isServiceUpdate?.toLowerCase() === "error" || list.isServiceUpdate?.toLowerCase() === "stopped")) {
//            serverName = checkAndReplace(list.failedActionName);
//        } else {
//            serverName = checkAndReplace(list.servicePath === null ? list.workflowName : list.workflowName === null ? list.servicePath : list.workflowName);
//        }

//        const ipAddress = checkAndReplace(list?.ipAddress);
//        const status = checkAndReplace(list?.isServiceUpdate);

//        const iconServer = serverName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";
//        const iconIp = ipAddress === "NA" ? "text-danger cp-disable" : "text-secondary cp-ip-address";

//        let iconStatus;
//        if (status?.toLowerCase() === "running") {
//            iconStatus = "text-success cp-reload cp-animate";
//        } else if (status?.toLowerCase() === "error" || status?.toLowerCase() === "stopped") {
//            iconStatus = "text-danger cp-fail-back";
//        } else {
//            iconStatus = "text-danger cp-disable";
//        }

//        return `<tr><td><i class="${iconServer} me-1 fs-6"></i><span>${serverName}</span></td><td><i class="${iconIp} me-1 fs-6"></i>${ipAddress}</td><td><i class="${iconStatus} me-1 fs-6"></i>${status}</td></tr>`;
//    }).join('');

//    $('#mssqlserverbody').append(rowsValue);
//}

async function getAysncWithHandlerOCP(url, data) {

    let dataArray = [];
    await $.get(url, data).done((response) => {
        if (response?.success) {
            dataArray = response?.data
        } else {
            errorNotification(response)
        }
    })
    return dataArray;
}

async function Ocpmonitorstatus(id, type) {

    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandlerOCP(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}

let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'

function propertiesData(value) {

    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties)
        console.log(data,'openshift')
        let dbDetail = data?.ClusterMonitoring;
        if (data?.VirtualizationMonitoring?.VirtualizationEnabled) {
            $("#Virtual").show();
            data?.VirtualizationMonitoring?.VirtualMachineMonitoring?.forEach((v) => {

                let virtualmachine = `
                   <tr>
                      <td> <i class="cp-name"></i> ${v.Name}</td>
                     <td>
                       <i class="cp-Stopped ${v?.Status === 'Running' ? 'text-success' : 'text-danger'} me-1"></i>
                      ${v?.Status}
                    </td>
                   </tr>`;


                $("#virtualData").append(virtualmachine);
            });
        }
        else {
            $("#Virtual").show();
        }

        if (data?.ComputeMonitoring?.MachineSetEnabled) {
            $("#Machine").show();
            data?.ComputeMonitoring?.MachineSetMonitoring?.forEach((v) => {

                let ComputeMonitor = `
                                  <tr>
                                       <td>
                                        <i class="cp-name"></i> ${v?.MachineSetName}
                                       </td>
                                       <td>
                                        <i class="cp-failure_count text-success me-1"></i>  ${v?.Machinecount}
                                       </td>
                                  </tr>
                                 `;


                $("#MachineSet").append(ComputeMonitor);
            });
        }
        else {
            $("#Machine").hide();
        }


        data?.NodeMonitorings?.forEach((v) => {

            let NodeMonitor = `<tr>
                                    <td >
                                        <i class="cp-network text-primary me-1"></i>${v?.NodeName}
                                    </td>
                                    <td>
                                        <i class="cp-success text-success me-1"></i>${v?.NodeStatus}
                                    </td>
                                    <td> <i class="cp-role text-success me-1"></i>${v?.NodeRole}</td>
                                </tr>
                                
                                 `;


            $("#NodeMonitor").append(NodeMonitor);
        });

        if (data?.WorkloadMonitoring?.StatefulSets?.StatefulSetEnabled || data?.WorkloadMonitoring?.ReplicaSets?.ReplicaSetEnabled || data?.WorkloadMonitoring?.DeploymentSets?.DeploymentSetEnabled) {
            $('#Workload').show()
           
            if (data?.WorkloadMonitoring?.StatefulSets?.StatefulSetEnabled) {
                $('#stateful').show();
                let workloadMonitoring = data?.WorkloadMonitoring;
                let statefulSetsContent = '';

                const totalRowSpan = workloadMonitoring?.StatefulSets?.StatefulSetsPods?.reduce((acc, statefulSet) => acc + (statefulSet?.SetPods?.length || 1), 1);
                statefulSetsContent += `
<tr>
    <td rowspan="${totalRowSpan || 1}">StatefulSets</td>
</tr>`;

                workloadMonitoring?.StatefulSets?.StatefulSetsPods?.forEach((statefulSet) => {
                    let podCount = statefulSet?.SetPods?.length;

                    statefulSetsContent += `
<tr>
    <td rowspan="${podCount || 1}"><i class="cp-platform-name text-primary me-1"></i>${statefulSet?.SetPodName || 'NA'}</td>
    <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[0]?.PodNames || 'NA'}</td>
    <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[0]?.ReadyStatus || 'NA'}</td>
</tr>`;

                    
                    for (let i = 1; i < podCount; i++) {
                        statefulSetsContent += `
<tr>
    <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[i]?.PodNames || 'NA'}</td>
    <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[i]?.ReadyStatus || 'NA'}</td>
</tr>`;
                    }

                    if (podCount === 0) {
                        statefulSetsContent += `
<tr>
    <td colspan="2">NA</td>
</tr>`;
                    }
                });

                $('#statefulSets').html(statefulSetsContent);

            } else {
                $('#stateful').hide();
            }



        //    if (data?.WorkloadMonitoring?.StatefulSets?.StatefulSetEnabled) {
        //        $('#stateful').show()
        //        let workloadMonitoring = data?.WorkloadMonitoring;
        //        let statefulSetsContent = '';

        //        workloadMonitoring?.StatefulSets?.StatefulSetsPods?.forEach((statefulSet) => {
        //            let podCount = statefulSet?.SetPods?.length;

        //            statefulSetsContent += `
        //<tr>
        //    <td rowspan="${podCount > 0 ? podCount : 1}">StatefulSets Pods</td>
        //    <td rowspan="${podCount > 0 ? podCount : 1}"><i class="cp-platform-name text-primary me-1"></i>
        //    ${statefulSet?.SetPodName||'NA'}</td>
        //    <td> <i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[0]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${statefulSet?.SetPods[0]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            for (let i = 1; i < podCount; i++) {
        //                statefulSetsContent += `
        //<tr>
        //    <td> <i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[i]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${statefulSet?.SetPods[i]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            }
        //        });
        //        $('#statefulSets').html(statefulSetsContent);

        //    }
        //    else {
        //        $('#stateful').hide()
            //    }

            if (data?.WorkloadMonitoring?.ReplicaSets?.ReplicaSetEnabled) {
                $('#Replica').show();
                let workloadMonitoring = data?.WorkloadMonitoring;
                let replicaSetsContent = '';

                const totalRowSpan = workloadMonitoring?.ReplicaSets?.ReplicaSetPods?.reduce((acc, statefulSet) => acc + (statefulSet?.SetPods?.length || 1), 1);
                replicaSetsContent += `
<tr>
    <td rowspan="${totalRowSpan || 1}">ReplicaSets</td>
</tr>`;

                workloadMonitoring?.ReplicaSets?.ReplicaSetPods?.forEach((statefulSet) => {
                    let podCount = statefulSet?.SetPods?.length;

                    replicaSetsContent += `
<tr>
    <td rowspan="${podCount || 1}"><i class="cp-platform-name text-primary me-1"></i>${statefulSet?.SetPodName || 'NA'}</td>
    <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[0]?.PodNames || 'NA'}</td>
    <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[0]?.ReadyStatus || 'NA'}</td>
</tr>`;

                    for (let i = 1; i < podCount; i++) {
                        replicaSetsContent += `
<tr>
    <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[i]?.PodNames || 'NA'}</td>
    <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[i]?.ReadyStatus || 'NA'}</td>
</tr>`;
                    }

                    if (podCount === 0) {
                        replicaSetsContent += `
<tr>
    <td colspan="2">NA</td>
</tr>`;
                    }
                });

                $('#ReplicaSet').html(replicaSetsContent);

            } else {
                $('#Replica').hide();
            }


        //    if (data?.WorkloadMonitoring?.ReplicaSets?.ReplicaSetEnabled) {
        //        $('#Replica').show()
        //        let workloadMonitoringReplicaSet = data?.WorkloadMonitoring;
        //        let replicaSetsContent = '';

        //        workloadMonitoring?.ReplicaSet?.ReplicaSets?.ReplicaSetPods?.forEach((replicaSet) => {
        //            let podCount = replicaSet?.SetPods?.length;
        //            replicaSetsContent += `
        //<tr>
        //    <td rowspan="${podCount > 0 ? podCount : 1}">ReplicaSets Pods</td>
        //    <td rowspan="${podCount > 0 ? podCount : 1}"><i class="cp-platform-name text-primary me-1"></i>
        //     ${replicaSet?.SetPodName||'NA'}</td>
        //    <td> <i class="cp-display-name text-primary me-1"></i> ${replicaSet?.SetPods[0]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${replicaSet?.SetPods[0]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            for (let i = 1; i < podCount; i++) {
        //                replicaSetsContent += `
        //<tr>
        //    <td> <i class="cp-display-name text-primary me-1"></i> ${replicaSet?.SetPods[i]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${replicaSet?.SetPods[i]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            }
        //        });
        //        $('#ReplicaSet').html(replicaSetsContent);
        //    }
        //    else {
        //        $('#Replica').hide()
        //    }

            if (data?.WorkloadMonitoring?.DeploymentSets?.DeploymentSetEnabled) {
                $('#podset').show();
                let workloadMonitoring = data?.WorkloadMonitoring;
                let deploymentSetsContent = '';

                const totalRowSpan = workloadMonitoring?.DeploymentSets?.DeploymentSetPods?.reduce((acc, statefulSet) => acc + (statefulSet?.SetPods?.length || 1), 1);

                deploymentSetsContent += `
    <tr>
        <td rowspan="${totalRowSpan || 1}">Deployment</td>
    </tr>`;

                workloadMonitoring?.DeploymentSets?.DeploymentSetPods?.forEach((statefulSet) => {
                    let podCount = statefulSet?.SetPods?.length;

                    if (podCount > 0) {
                        
                        deploymentSetsContent += `
            <tr>
                <td rowspan="${podCount || 1}"><i class="cp-platform-name text-primary me-1"></i>${statefulSet?.SetPodName || 'NA'}</td>
                <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[0]?.PodNames || 'NA'}</td>
                <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[0]?.ReadyStatus || 'NA'}</td>
            </tr>`;

                        for (let i = 1; i < podCount; i++) {
                            deploymentSetsContent += `
                <tr>
                    <td><i class="cp-display-name text-primary me-1"></i>${statefulSet?.SetPods[i]?.PodNames || 'NA'}</td>
                    <td><i class="cp-refresh me-1 text-success me-1"></i>${statefulSet?.SetPods[i]?.ReadyStatus || 'NA'}</td>
                </tr>`;
                        }
                    } else {
                        
                        deploymentSetsContent += `
            <tr>
                <td colspan="3">No Pods Available</td>
            </tr>`;
                    }
                });

                $('#ReplicaSetsPods').html(deploymentSetsContent);

            } else {
                $('#podset').hide();
            }

        //    if (data?.WorkloadMonitoring?.DeploymentSets?.DeploymentSetEnabled) {
        //        $("#podset").show()
        //        let workloadMonitoringDeploymentSet = data?.WorkloadMonitoring;
        //        let deploymentSetsContent = '';


        //        workloadMonitoring?.DeploymentSets?.DeploymentSetPods?.forEach((deploymentSet) => {
        //            let podCount = deploymentSet?.SetPods?.length
                    
        //            deploymentSetsContent += `
        //<tr>
        //    <td rowspan="${podCount > 0 ? podCount : 1}">DeploymentSet Pods</td>
        //    <td rowspan="${podCount > 0 ? podCount : 1}"><i class="cp-platform-name text-primary me-1"></i>
        //    ${deploymentSet?.SetPodName||'NA'}</td>
        //    <td> <i class="cp-display-name text-primary me-1"></i> ${deploymentSet?.SetPods[0]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${deploymentSet?.SetPods[0]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            for (let i = 1; i < podCount; i++) {
        //                deploymentSetsContent += `
        //<tr>
        //    <td> <i class="cp-display-name text-primary me-1"></i> ${deploymentSet?.SetPods[i]?.PodNames||'NA'}</td>
        //    <td> <i class="cp-refresh me-1 text-success me-1"></i> ${deploymentSet?.SetPods[i]?.ReadyStatus||'NA'}</td>
        //</tr>`;
        //            }
        //        });

        //        $('#ReplicaSetsPods').html(deploymentSetsContent);


        //    }
        //    else {
        //        $("#podset").hide()
        //    }


        }
        else {
            $('#Workload').hide()
        }
        //Database Details
        const dbDetailsProp = ["OpenShiftClusterFQDN", "ClusterResourceName", "ClusterStatus", "ClusterInfrastructure", "DistributionVersion", "ClusterAPIAddress",
            "ProjectsName", "ProjectsStatus",];
        if (dbDetail !== '' && dbDetail !== null && dbDetail !== undefined) {
            bindProperties(value, dbDetail, dbDetailsProp);
        }
        //  setPropData(value, data, [dbDetailsProp], dbDetail);

        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
    }
}

function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

function setPropData(value, data, propSets) {
    propSets?.forEach(properties => {
        bindProperties(value, data, properties);
    });
}



function bindProperties(value, data, properties) {

    properties?.forEach(property => {
        const value = data[property];
        const displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = getIconClass(displayedValue);
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}

function getIconClass(displayedValue) {
    const lowercaseValue = displayedValue?.toLowerCase();

    switch (lowercaseValue) {

        case 'local - cluster': return 'text-success cp-cluster-database';
        case 'openshift-clus.ptechno.com': return 'cp-up-linearrow text-success';
        case 'sol-project': return 'text-success cp-form-name';
        case 'https://api.openshift-clus.ptechno.com:6443': return 'cp-up-linearrow text-success';
        case 'OpenShift 4.15.15': return 'text-primary cp-list-prsite';
        case 'physical standby': return 'text-info cp-physical-drsite';
        case 'active': return 'text-success cp-active-inactive';
        case 'vMware vSphere': return 'text-success cp-infra-replication-mapping';
        case 'ready': return 'text-success cp-refresh';

        default: return 'text-success cp-refresh';
    }
}