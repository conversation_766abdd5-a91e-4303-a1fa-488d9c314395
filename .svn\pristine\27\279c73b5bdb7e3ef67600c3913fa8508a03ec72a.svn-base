﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ReportScheduleFilterSpecification : Specification<ReportSchedule>
{
    public ReportScheduleFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ReportName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("reportName=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ReportName.Contains(stringItem.Replace("reportName=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("reportType=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ReportType.Contains(stringItem.Replace("reportType=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("scheduleTime=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ScheduleTime.Contains(stringItem.Replace("scheduleTime=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    else if (stringItem.Contains("nodename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.NodeName.Contains(stringItem.Replace("nodename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ReportName.Contains(searchString) || p.ReportType.Contains(searchString)
                                                        || p.ScheduleTime.Contains(searchString) ||
                                                        p.NodeName.Contains(searchString);
            }
        }
    }
}