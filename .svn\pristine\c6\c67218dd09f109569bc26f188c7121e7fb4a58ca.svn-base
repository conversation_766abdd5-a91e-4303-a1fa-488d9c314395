using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowActionFieldMasterFixture : IDisposable
{
    public List<WorkflowActionFieldMaster> WorkflowActionFieldMasterPaginationList { get; set; }
    public List<WorkflowActionFieldMaster> WorkflowActionFieldMasterList { get; set; }
    public WorkflowActionFieldMaster WorkflowActionFieldMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowActionFieldMasterFixture()
    {
        var fixture = new Fixture();

        WorkflowActionFieldMasterList = fixture.Create<List<WorkflowActionFieldMaster>>();

        WorkflowActionFieldMasterPaginationList = fixture.CreateMany<WorkflowActionFieldMaster>(20).ToList();

        WorkflowActionFieldMasterDto = fixture.Create<WorkflowActionFieldMaster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
