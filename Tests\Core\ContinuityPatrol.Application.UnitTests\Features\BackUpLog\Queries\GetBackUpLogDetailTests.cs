using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Queries;

public class GetBackUpLogDetailTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly GetBackUpLogDetailsQueryHandler _handler;

    public GetBackUpLogDetailTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);

        _handler = new GetBackUpLogDetailsQueryHandler(
            _backUpLogFixture.Mapper,
            _mockBackUpLogRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnBackUpLogDetail_When_ValidId()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogDetailQuery { Id = existingBackUpLog.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BackUpLogDetailVm>();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.HostName.ShouldBe(existingBackUpLog.HostName);
        result.DatabaseName.ShouldBe(existingBackUpLog.DatabaseName);
        result.UserName.ShouldBe(existingBackUpLog.UserName);
        result.IsLocalServer.ShouldBe(existingBackUpLog.IsLocalServer);
        result.IsBackUpServer.ShouldBe(existingBackUpLog.IsBackUpServer);
        result.BackUpPath.ShouldBe(existingBackUpLog.BackUpPath);
        result.Type.ShouldBe(existingBackUpLog.Type);
        result.Status.ShouldBe(existingBackUpLog.Status);
        result.Properties.ShouldBe(existingBackUpLog.Properties);

        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(existingBackUpLog.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnFullBackupDetail_When_ValidFullBackupId()
    {
        // Arrange
        var fullBackup = _backUpLogFixture.BackUpLogs.First(x => x.Type == "Full");
        var query = new GetBackUpLogDetailQuery { Id = fullBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Type.ShouldBe("Full");
        result.Id.ShouldBe(fullBackup.ReferenceId);
        result.HostName.ShouldBe(fullBackup.HostName);
        result.DatabaseName.ShouldBe(fullBackup.DatabaseName);
        result.BackUpPath.ShouldBe(fullBackup.BackUpPath);
        result.Status.ShouldBe(fullBackup.Status);
    }

    [Fact]
    public async Task Handle_ReturnDifferentialBackupDetail_When_ValidDifferentialBackupId()
    {
        // Arrange
        // Add differential backup to the fixture
        var differentialBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "DiffDetailServer",
            DatabaseName = "DiffDetailDatabase",
            UserName = "DiffDetailUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\DiffDetailDatabase.bak",
            Type = "Differential",
            Status = "Completed",
            Properties = "{\"compression\":\"true\",\"size\":\"512MB\"}",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(differentialBackup);

        var query = new GetBackUpLogDetailQuery { Id = differentialBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Type.ShouldBe("Differential");
        result.Id.ShouldBe(differentialBackup.ReferenceId);
        result.HostName.ShouldBe("DiffDetailServer");
        result.DatabaseName.ShouldBe("DiffDetailDatabase");
        result.Properties.ShouldContain("compression");
        result.IsBackUpServer.ShouldBeTrue();
        result.IsLocalServer.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ReturnTransactionLogDetail_When_ValidTransactionLogId()
    {
        // Arrange
        // Add transaction log backup to the fixture
        var transactionLogBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "LogDetailServer",
            DatabaseName = "LogDetailDatabase",
            UserName = "LogDetailUser",
            IsLocalServer = true,
            IsBackUpServer = true,
            BackUpPath = @"E:\Logs\LogDetailDatabase.trn",
            Type = "Transaction Log",
            Status = "Completed",
            Properties = "{\"frequency\":\"15min\",\"size\":\"128MB\"}",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(transactionLogBackup);

        var query = new GetBackUpLogDetailQuery { Id = transactionLogBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Type.ShouldBe("Transaction Log");
        result.Id.ShouldBe(transactionLogBackup.ReferenceId);
        result.HostName.ShouldBe("LogDetailServer");
        result.BackUpPath.ShouldEndWith(".trn");
        result.Properties.ShouldContain("frequency");
        result.IsLocalServer.ShouldBeTrue();
        result.IsBackUpServer.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ReturnCompletedBackupDetail_When_ValidCompletedBackupId()
    {
        // Arrange
        var completedBackup = _backUpLogFixture.BackUpLogs.First(x => x.Status == "Completed");
        var query = new GetBackUpLogDetailQuery { Id = completedBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Status.ShouldBe("Completed");
        result.Id.ShouldBe(completedBackup.ReferenceId);
        result.HostName.ShouldBe(completedBackup.HostName);
        result.DatabaseName.ShouldBe(completedBackup.DatabaseName);
        result.Type.ShouldBe(completedBackup.Type);
    }

    [Fact]
    public async Task Handle_ReturnFailedBackupDetail_When_ValidFailedBackupId()
    {
        // Arrange
        // Add failed backup to the fixture
        var failedBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "FailedDetailServer",
            DatabaseName = "FailedDetailDatabase",
            UserName = "FailedDetailUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\FailedDetailDatabase.bak",
            Type = "Full",
            Status = "Failed",
            Properties = "{\"error\":\"Disk space insufficient\",\"retry_count\":\"3\"}",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(failedBackup);

        var query = new GetBackUpLogDetailQuery { Id = failedBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Status.ShouldBe("Failed");
        result.Id.ShouldBe(failedBackup.ReferenceId);
        result.HostName.ShouldBe("FailedDetailServer");
        result.Properties.ShouldContain("error");
        result.Properties.ShouldContain("retry_count");
    }

    [Fact]
    public async Task Handle_ReturnInProgressBackupDetail_When_ValidInProgressBackupId()
    {
        // Arrange
        // Add in-progress backup to the fixture
        var inProgressBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "ProgressDetailServer",
            DatabaseName = "ProgressDetailDatabase",
            UserName = "ProgressDetailUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ProgressDetailDatabase.bak",
            Type = "Differential",
            Status = "In Progress",
            Properties = "{\"progress\":\"45%\",\"estimated_time\":\"10min\"}",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(inProgressBackup);

        var query = new GetBackUpLogDetailQuery { Id = inProgressBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Status.ShouldBe("In Progress");
        result.Id.ShouldBe(inProgressBackup.ReferenceId);
        result.Type.ShouldBe("Differential");
        result.Properties.ShouldContain("progress");
        result.Properties.ShouldContain("estimated_time");
    }

    [Fact]
    public async Task Handle_ReturnRemoteBackupDetail_When_ValidRemoteBackupId()
    {
        // Arrange
        // Add remote backup to the fixture
        var remoteBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "RemoteDetailServer",
            DatabaseName = "RemoteDetailDatabase",
            UserName = "RemoteDetailUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\RemoteDetailServer\Backups\RemoteDetailDatabase.bak",
            Type = "Full",
            Status = "Completed",
            Properties = "{\"network_speed\":\"1Gbps\",\"compression\":\"true\"}",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(remoteBackup);

        var query = new GetBackUpLogDetailQuery { Id = remoteBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsLocalServer.ShouldBeFalse();
        result.IsBackUpServer.ShouldBeTrue();
        result.BackUpPath.ShouldStartWith(@"\\");
        result.Properties.ShouldContain("network_speed");
        result.Properties.ShouldContain("compression");
    }

    [Fact]
    public async Task Handle_ReturnBackupWithComplexProperties_When_ValidComplexBackupId()
    {
        // Arrange
        var complexProperties = "{\"compression\":\"true\",\"encryption\":\"AES256\",\"checksum\":\"enabled\",\"retention\":{\"days\":30,\"policy\":\"GFS\"},\"metadata\":{\"size\":\"2048MB\",\"duration\":\"15min\"}}";
        var complexBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "ComplexDetailServer",
            DatabaseName = "ComplexDetailDatabase",
            UserName = "ComplexDetailUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ComplexDetailDatabase.bak",
            Type = "Full",
            Status = "Completed",
            Properties = complexProperties,
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(complexBackup);

        var query = new GetBackUpLogDetailQuery { Id = complexBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Properties.ShouldBe(complexProperties);
        result.Properties.ShouldContain("compression");
        result.Properties.ShouldContain("encryption");
        result.Properties.ShouldContain("retention");
        result.Properties.ShouldContain("metadata");
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var query = new GetBackUpLogDetailQuery { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));

        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogIsNull()
    {
        // Arrange
        var nullId = Guid.NewGuid().ToString();
        _mockBackUpLogRepository.Setup(x => x.GetByReferenceIdAsync(nullId))
            .ReturnsAsync((Domain.Entities.BackUpLog)null);

        var query = new GetBackUpLogDetailQuery { Id = nullId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));

        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(nullId), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogIsInactive()
    {
        // Arrange
        // Add inactive backup log to the fixture
        var inactiveBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "InactiveServer",
            DatabaseName = "InactiveDatabase",
            UserName = "InactiveUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\InactiveDatabase.bak",
            Type = "Full",
            Status = "Completed",
            Properties = "{\"archived\":\"true\"}",
            IsActive = false // Inactive backup log
        };
        _backUpLogFixture.BackUpLogs.Add(inactiveBackup);

        var query = new GetBackUpLogDetailQuery { Id = inactiveBackup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_MapCorrectly_When_ValidBackUpLog()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogDetailQuery { Id = existingBackUpLog.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingBackUpLog.ReferenceId);
        result.HostName.ShouldBe(existingBackUpLog.HostName);
        result.DatabaseName.ShouldBe(existingBackUpLog.DatabaseName);
        result.UserName.ShouldBe(existingBackUpLog.UserName);
        result.IsLocalServer.ShouldBe(existingBackUpLog.IsLocalServer);
        result.IsBackUpServer.ShouldBe(existingBackUpLog.IsBackUpServer);
        result.BackUpPath.ShouldBe(existingBackUpLog.BackUpPath);
        result.Type.ShouldBe(existingBackUpLog.Type);
        result.Status.ShouldBe(existingBackUpLog.Status);
        result.Properties.ShouldBe(existingBackUpLog.Properties);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogDetailQuery { Id = existingBackUpLog.ReferenceId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(existingBackUpLog.ReferenceId), Times.Once);
        _mockBackUpLogRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_ReturnCorrectDetailType_When_ValidQuery()
    {
        // Arrange
        var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
        var query = new GetBackUpLogDetailQuery { Id = existingBackUpLog.ReferenceId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<BackUpLogDetailVm>();
        result.GetType().Name.ShouldBe("BackUpLogDetailVm");
    }
}
