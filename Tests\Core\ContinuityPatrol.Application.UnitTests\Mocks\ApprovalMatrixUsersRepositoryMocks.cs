using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public static class ApprovalMatrixUsersRepositoryMocks
{
    public static Mock<IApprovalMatrixUsersRepository> CreateApprovalMatrixUsersRepository(List<ApprovalMatrixUsers> approvalMatrixUsers)
    {
        var mockApprovalMatrixUsersRepository = new Mock<IApprovalMatrixUsersRepository>();

        mockApprovalMatrixUsersRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(approvalMatrixUsers);

        mockApprovalMatrixUsersRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => approvalMatrixUsers.FirstOrDefault(x => x.ReferenceId == id));

        mockApprovalMatrixUsersRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) => 
                approvalMatrixUsers.Any(x => x.UserName == name && x.ReferenceId != id && x.IsActive));

        mockApprovalMatrixUsersRepository.Setup(repo => repo.AddAsync(It.IsAny<ApprovalMatrixUsers>()))
            .ReturnsAsync((ApprovalMatrixUsers approvalMatrixUser) =>
            {
                approvalMatrixUser.ReferenceId = Guid.NewGuid().ToString();
                approvalMatrixUser.Id = approvalMatrixUsers.Count + 1;
                approvalMatrixUsers.Add(approvalMatrixUser);
                return approvalMatrixUser;
            });

        mockApprovalMatrixUsersRepository.Setup(repo => repo.AddRangeAsync(It.IsAny<List<ApprovalMatrixUsers>>()))
            .Returns((List<ApprovalMatrixUsers> approvalMatrixUsersList) =>
            {
                foreach (var user in approvalMatrixUsersList)
                {
                    user.ReferenceId = Guid.NewGuid().ToString();
                    user.Id = approvalMatrixUsers.Count + 1;
                    approvalMatrixUsers.Add(user);
                }
                return Task.CompletedTask;
            });

        mockApprovalMatrixUsersRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ApprovalMatrixUsers>()))
            .Returns((ApprovalMatrixUsers approvalMatrixUser) =>
            {
                var existingUser = approvalMatrixUsers.FirstOrDefault(x => x.ReferenceId == approvalMatrixUser.ReferenceId);
                if (existingUser != null)
                {
                    existingUser.UserId = approvalMatrixUser.UserId;
                    existingUser.UserName = approvalMatrixUser.UserName;
                    existingUser.Email = approvalMatrixUser.Email;
                    existingUser.MobileNumber = approvalMatrixUser.MobileNumber;
                    existingUser.BusinessServiceProperties = approvalMatrixUser.BusinessServiceProperties;
                    existingUser.UserType = approvalMatrixUser.UserType;
                    existingUser.AcceptType = approvalMatrixUser.AcceptType;
                    existingUser.IsLink = approvalMatrixUser.IsLink;
                    existingUser.IsActive = approvalMatrixUser.IsActive;
                }
                return Task.CompletedTask;
            });

        mockApprovalMatrixUsersRepository.Setup(repo => repo.DeleteAsync(It.IsAny<ApprovalMatrixUsers>()))
            .Returns((ApprovalMatrixUsers approvalMatrixUser) =>
            {
                approvalMatrixUsers.Remove(approvalMatrixUser);
                return Task.CompletedTask;
            });

        mockApprovalMatrixUsersRepository.Setup(repo => repo.GetPaginatedQuery())
            .Returns(approvalMatrixUsers.Where(x => x.IsActive).AsQueryable());

        return mockApprovalMatrixUsersRepository;
    }

    public static Mock<IUserActivityRepository> CreateUserActivityRepository(List<UserActivity> userActivities)
    {
        var mockUserActivityRepository = new Mock<IUserActivityRepository>();

        mockUserActivityRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
            .ReturnsAsync((UserActivity userActivity) =>
            {
                userActivity.Id = userActivities.Count + 1;
                userActivity.ReferenceId = Guid.NewGuid().ToString();
                userActivities.Add(userActivity);
                return userActivity;
            });

        mockUserActivityRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(userActivities);

        return mockUserActivityRepository;
    }
}
