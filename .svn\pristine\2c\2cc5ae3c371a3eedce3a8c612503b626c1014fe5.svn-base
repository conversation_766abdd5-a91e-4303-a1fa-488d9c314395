﻿//using ContinuityPatrol.Domain.Entities;
//using ContinuityPatrol.Domain.ViewModels.PRDRCompareModel;
//using ContinuityPatrol.Shared.Core.Contracts.Identity;
//using ContinuityPatrol.Shared.Services.Provider;
//using System.Text.Json;
//using ContinuityPatrol.Application.Features.PRDRCompare.GetPaginatedList;

//namespace ContinuityPatrol.Web.Areas.Configuration.Controllers
//{
//    [Area("Configuration")]
//    public class PRDRCompareController : BaseController
//    {
//        private readonly ILogger<PRDRCompare> _logger;
//        private readonly IMapper _mapper;
//        private readonly IDataProvider _provider;
//        private readonly ILoggedInUserService _loggedInUserService;
//        string impact_state = string.Empty;

        
//        public PRDRCompareController(IMapper mapper, ILogger<PRDRCompare> logger, IDataProvider dataProvider, ILoggedInUserService loggedInUserService)
//        {
//            _logger = logger;
//            _mapper = mapper;
//            _provider = dataProvider;
//            _loggedInUserService = loggedInUserService;

//        }

//        //[HttpGet("List")]
//        //[ActionName("impact")]
//        public async Task<IActionResult> List()
//        {
//            //var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();
//            ViewData["impact_data"]="all";

//            var finalcompare2 = await _provider.PRDRCompare.GetOsPaginatedList(new GetosPaginatedListQuery());
//            var finalcompare = new PRDRCompareViewModel();

//            // finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist;

//            finalcompare.PRDRServerImpactList = _mapper.Map<List<PRDRServerImpact>>(finalcompare2.Data);
//            // finalcompare.PRDRServerImpactList = finalcompare2.Data;
//            //  finalcompare.InfraObjectlist();
//            //if (PRDRComparelist.PRDRServerImpactList != null)
//            //{
//            //    finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.ReferenceId)).ToList();
//            //    finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.InfraObjectId)).ToList();
//            //}


//            //List<PRDRCOMViewModel> viewModelList = finalcompare.InfraObjectlist.Select(x => new PRDRCOMViewModel
//            //{
//            //    PRDRDETAILSLIST = finalcompare.InfraObjectlist.Select(x => x.DRServerName).ToList(),
//            //    List2Item = List2.Where(y => x.SomeProperty == y.SomeKey).FirstOrDefault().AnotherProperty
//            //}).ToList();


//            //foreach (InfraObject item in finalcompare.InfraObjectlist)
//            //{
//            //    PRDRCOMViewModel pddrdata = new PRDRCOMViewModel();


//            //    pddrdata.Name = item.Name;
//            //    pddrdata.PRServerName = item.PRServerName;
//            //    pddrdata.PRIPAddress = item.DRServerName;
//            //    pddrdata.DRServerName = item.DRServerName;
//            //    pddrdata.DRIPAddress = item.DRServerName;
//            //    pddrdata.BusinessServiceName = item.BusinessServiceName;
//            //    pddrdata.infraId = item.Id;
//            //    viewModelList.Add(pddrdata);

//            //}


//            //var serversList = new List<Domain.Entities.PRDRCompare>();

//            //serversList.AddRange(_loggedInUserService.IsAllInfra
//            //    ? PRDRComparelist
//            //    : finalcompare.InfraObjectlist.SelectMany(x =>
//            //        PRDRComparelist.Where(server => server.ReferenceId.Equals(x.PRServerId) ||
//            //                                server.ReferenceId.Equals(x.DRServerId) ||
//            //                                server.ReferenceId.Equals(x.NearDRServerId)).ToList()));


//            return View(finalcompare);





//        }

//        //public async Task<IActionResult> Details(int Id)
//        //{

//        //    var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();

//        //    var finalcompare = new PRDRCompareViewModel();

//        //    //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => PRDRComparelist.PRDROSLevelCompareStatusList.Any(y => y.InfraObjectId == Id)).ToList();
//        //    finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => x.InfraObjectId == Id).ToList();

//        //    return PartialView("Details", finalcompare);
//        //    //return View(PRDRComparelist);
//        //}



//        public async Task<IActionResult> impact()
//        {
//            return Json(await _provider.PRDRCompare.GetOsPaginatedList(new GetosPaginatedListQuery()));



//            // finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist;

//            //finalcompare.PRDRServerImpactList = _mapper.Map<List<PRDRServerImpact>>(finalcompare2.Data);

//            //var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();

//            //var finalcompare = new PRDRCompareViewModel();

//            ////finalcompare.PRDRServerImpactList = PRDRComparelist.PRDRServerImpactList.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.Id && y.IsImpact == 1)).ToList();
//            //finalcompare.PRDRServerImpactList = PRDRComparelist.PRDRServerImpactList.Where(x => x.IsImpact.Equals(1)).ToList();
//            //finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.ReferenceId && y.IsImpact == 1)).ToList();
//            //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => finalcompare.PRDRServerImpactList.Any(y => y.InfraObjectId == x.InfraObjectId)).ToList();


//            //return View("List", finalcompare);
//        }

//        //[ActionName("List")]
//        public async Task<IActionResult> non_impact()
//        {
//            impact_state = "non_impact";
//            var finalcompare2 = await _provider.PRDRCompare.GetOsPaginatedList(new GetosPaginatedListQuery());
//            var finalcompare = new PRDRCompareViewModel();

//            finalcompare.PRDRServerImpactList = _mapper.Map<List<PRDRServerImpact>>(finalcompare2.Data);

//            //var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();

//            //var finalcompare = new PRDRCompareViewModel();

//            ////finalcompare.PRDRServerImpactList = PRDRComparelist.PRDRServerImpactList.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.Id && y.IsImpact == 0)).ToList(); ;
//            //finalcompare.PRDRServerImpactList = PRDRComparelist.PRDRServerImpactList.Where(x => x.IsImpact.Equals(0)).ToList();
//            //finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.ReferenceId && y.IsImpact == 0)).ToList();
//            //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => finalcompare.PRDRServerImpactList.Any(y => y.InfraObjectId == x.InfraObjectId)).ToList();


//            return View("List", finalcompare);
//        }
//        //public async Task<JsonResult> GetPRDRCompareStatusbyinfraId(int Id)
//        //{
//        //    var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();

//        //    var finalcompare = new PRDRCompareViewModel();

//        //    //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => PRDRComparelist.PRDROSLevelCompareStatusList.Any(y => y.InfraObjectId == Id)).ToList();
//        //    finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => x.InfraObjectId == Id).ToList();


//        //    return Json(finalcompare);
//        //}
//        [HttpGet]
//        public async Task<PartialViewResult> Details(string Id)
//        {
//            var finalcompare = new PRDRCompareViewModel();
//            try
//            {


//                _logger.LogInformation("PRDRCompare infraref Id" + Id);
//                var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();



//                //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => PRDRComparelist.PRDROSLevelCompareStatusList.Any(y => y.InfraObjectId == Id)).ToList();
//                finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => x.InfraObjectId == Id.ToString()).ToList();

//                _logger.LogInformation("PRDROSLevelCompareStatusList count" + finalcompare.PRDROSLevelCompareStatusList.Count());
//                return PartialView("Details", finalcompare);

//            }
//            catch (Exception ex)
//            {
//                _logger.LogInformation("PRDROSLevelCompareStatusList ex" + ex);
//            }
//            return PartialView("Details", finalcompare);
//        }
//        [HttpGet]
//        public async Task<JsonResult> GetDetails(string id)
//        {
//            // Fetch details based on the selected ID (replace with your data retrieval logic)
//            var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();
//            //var finalcompare = new PRDROSLevelCompareStatus();
//            //finalcompare = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => x.InfraObjectId == id.ToString()).ToList(PRDROSLevelCompareStatus);

//            var finalcompare = new PRDROSLevelCompareStatus();

//            //var PRDROSLevelCompareStatusObject = PRDRComparelist.PRDROSLevelCompareStatusList.OrderBy(id)desc.FirstOrDefault(x => x.InfraObjectId == id.ToString());
//            var PRDROSLevelCompareStatusObject = PRDRComparelist.PRDROSLevelCompareStatusList
//    .OrderByDescending(x => x.LastModifiedDate)  // Assuming 'id' is the property by which you want to order in descending order
//    .FirstOrDefault(x => x.InfraObjectId == id.ToString());

//            //if (PRDROSLevelCompareStatusObject != null)
//            //{
//            //    PRDROSLevelCompareStatusObject.ToList();
//            //}

//            //if(PRDROSLevelCompareStatusObject.PROSName != PRDROSLevelCompareStatusObject.DROSName)
//            //{
//            //    PRDROSLevelCompareStatusObject.PROSName.CssString = "<span class=\"text - emphasis - color: red;\"></span>";
//            //}

//            var details = JsonSerializer.Serialize(PRDROSLevelCompareStatusObject);

//            if (details == null)
//            {
//                return null;
//            }

//            return Json(details); // Assuming details is a JSON-serializable object

//        }


//        public async Task<JsonResult> getdetailsforview()
//        {
//            var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();
//            var finalcompare = new PRDROSLevelCompareStatus();
//            var PRDROSLevelCompareStatusObject = PRDRComparelist.InfraObjectlist.ToList();

//            var details = JsonSerializer.Serialize(PRDROSLevelCompareStatusObject);

//            //return Json(await _provider.Site.GetosPaginatedList(query));

//            return Json(details); // Assuming details is a JSON-serializable object

//        }
//        //public async Task<JsonResult> GetPagination(GetInfraObjectPaginatedListQuery query)
//        //{
//        //    return Json(await _provider.InfraObject.GetPaginatedInfraObjects(query));
//        //}
//        public async Task<JsonResult> GetPagination(GetosPaginatedListQuery query)
//        {
//            //return Json(await _provider.PRDRCompare.GetosPaginatedList(query));

//            if (impact_state == "non_impact")
//            {
//                //var finalcompare2 = await _provider.PRDRCompare.GetosPaginatedList(query);


//                // finalcompare2 = finalcompare2.Data.Where(x => x.IsImpact.Equals(0));

//                //return finalcompare2;

//                // = _mapper.Map<List<PRDRServerImpact>>(finalcompare2);
//                return Json(await _provider.PRDRCompare.GetOsPaginatedList(query));
//            }
//            else if (impact_state == "impact")
//            {
//                //var finalcompare2 = await _provider.PRDRCompare.GetosPaginatedList(new GetosPaginatedListQuery());
//                //var finalcompare = new PRDRCompareViewModel();

//                //return finalcompare.PRDRServerImpactList = _mapper.Map<List<PRDRServerImpact>>(finalcompare2.Data);

//                return Json(await _provider.PRDRCompare.GetOsPaginatedList(query));
//            }
//            impact_state= string.Empty;
//            return Json(await _provider.PRDRCompare.GetOsPaginatedList(query));
          

//        }
//        public async Task<IActionResult> SeacrhItem(string searchTerm)
//        {


//            var PRDRComparelist = await _provider.PRDRCompare.GetPRDRCompareList();

//            var finalcompare = new PRDRCompareViewModel();
//            var PRDROSLevelCompareStatusObject = PRDRComparelist.InfraObjectlist.FirstOrDefault(x => x.BusinessServiceName.Contains(searchTerm.ToString()));
//            finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist.Where(x => x.BusinessServiceName == PRDROSLevelCompareStatusObject.BusinessServiceName).ToList();

//            finalcompare.PRDRServerImpactList = PRDRComparelist.PRDRServerImpactList.Where(x => x.InfraObjectId.Equals(PRDROSLevelCompareStatusObject.ReferenceId)).ToList();

//            //finalcompare.InfraObjectlist = PRDRComparelist.InfraObjectlist.Where(x => PRDRComparelist.PRDRServerImpactList.Any(y => y.InfraObjectId == x.ReferenceId && y.IsImpact == 0)).ToList();
//            //finalcompare.PRDROSLevelCompareStatusList = PRDRComparelist.PRDROSLevelCompareStatusList.Where(x => finalcompare.PRDRServerImpactList.Any(y => y.InfraObjectId == x.InfraObjectId)).ToList();

//            return View(finalcompare);
//        }

//        //[HttpGet]
//        //public async Task<JsonResult> GetCompanies()
//        //{
//        //    return Json(await _dataProvider.Company.GetCompanyNamesOnLogin());
//        //}

//    }
//}
