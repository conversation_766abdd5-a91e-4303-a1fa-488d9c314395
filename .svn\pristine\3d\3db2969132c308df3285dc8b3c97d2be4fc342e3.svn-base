﻿@using ContinuityPatrol.Shared.Services.Helper
@using Microsoft.AspNetCore.Mvc.TagHelpers
@Html.AntiForgeryToken()
@model ContinuityPatrol.Domain.ViewModels.BackUpModel.BackUpViewModel

@{
    ViewData["Title"] = "List";
}
@Html.AntiForgeryToken()

<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />
<style>
    .dataTables_scrollBody {
        max-height: calc(100vh - 275px);
        height: calc(100vh - 275px);
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-backup_data"></i><span>Backup Data </span></h6>
            <form class="d-flex gap-2 align-items-center">
                <div class="input-group w-auto">
                    <input type="search" class="form-control" id="search-inp" placeholder="Search" autocomplete="off">
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="backuppath=" id="BackupPath">
                                        <label class="form-check-label" for="BackupPath">
                                            Backup Path
                                        </label>
                                    </div>
                                </li>
                                @*  <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="displayName=" id="BackupType">
                                        <label class="form-check-label" for="BackupType">
                                            Backup Type
                                        </label>
                                    </div>
                                </li> *@
                                @* <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="webAddress=" id="WebAddress">
                                        <label class="form-check-label" for="WebAddress">
                                            Web Address
                                        </label>
                                    </div>
                                </li> *@
                            </ul>
                        </div>
                    </div>
                </div>
                @* <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="Company-CreateButton" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button> *@
            </form>
        </div>
        <div class="card-body py-0">
            <form id="BackupForm" asp-controller="BackupData" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-xl-3">
                        <div class="mb-3 form-group">
                            <div class="form-label">Server Name / IP Address</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-stand-storage"></i></span>
                                <input type="text" asp-for="HostName" id="txtHostName" readonly="true" autocomplete="off" class="form-control" placeholder="Enter Server Name / IP Address" />
                            </div>
                            <span asp-validation-for="HostName" id="HostName-error"></span>
                        </div>
                    </div>
                    <input asp-for="BackUpPath" id="backpth" type="hidden" class="form-control" />
                    <input asp-for="KeepBackUpLast" id="KPbackLast" type="hidden" class="form-control" />
                    <input asp-for="BackUpType" id="ChkValue" type="hidden" class="form-control" />
                    <input asp-for="ScheduleType" id="textScheduleType" type="hidden" class="form-control" />
                    <input asp-for="ScheduleTime" id="txtCronViewList" type="hidden" class="form-control" />
                    <input asp-for="CronExpression" id="textCronExpression" type="hidden" class="form-control" />
                    <input asp-for="Properties" id="textProperties" type="hidden" class="form-control" />
                    <input asp-for="IsLocalServer" type="hidden" id="txtLocalServer">
                    <input asp-for="IsBackUpServer" type="hidden" id="txtBackupServer">
                    <input asp-for="Id" id="txtServerId" type="hidden" class="form-control" />
                    <div class="col-xl-3">
                        <div class="mb-3 form-group">
                            <div class="form-label">Database Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-database-type"></i></span>
                                <input type="text" asp-for="DatabaseName" id="txtDatabaseName" readonly="true" autocomplete="off" class="form-control" placeholder="Enter Database Name" />
                            </div>
                            <span asp-validation-for="DatabaseName" id="DatabaseName-error"></span>
                        </div>
                    </div>
                    <div class="col">
                        <div class="mb-3 form-group">
                            <div class="form-label">Username</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user"></i></span>
                                <input type="text" asp-for="UserName" id="txtUserName" readonly="true" autocomplete="off" class="form-control" placeholder="Enter Username" />
                            </div>
                            <span asp-validation-for="UserName" id="UserName-error"></span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center col-xl-auto">
                        <div class="d-flex justify-content-end gap-2  ">
                            <button type="button" id="btnDiscover"
                                    class="btn btn-primary btn-sm">
                                <span class="btn-text">Discover</span>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#exampleModal" id="ScheduleId">Schedule</button>
                        </div>

                    </div>

                    <div style="height:calc(100vh - 186px);overflow:auto">
                        <table class="datatable table table-hover dataTable no-footer" id="backupDataTable">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Backup Path</th>
                                    <th>Backup Type</th>
                                    <th>Created Date</th>
                                    <th>Last Executed</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>
        </div>
        <div id="AdminBkCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
        @*   <div class="card-footer">
            <div class="d-flex justify-content-end align-items-center ">
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary btn-sm bkclass" data-dismiss="modal" id="btnExecute"><span class="btn-text">Execute</span></button>
                    <button type="button" id="btnSave" data-dismiss="modal" class="animation-btn btn btn-primary btn-sm bkclass"><span class="btn-text">Save</span></button>
                </div>
            </div>
        </div> *@
    </div>
</div>

<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="exampleModalLabel">
                    Backup Data Configuration
                </h1>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center gap-3 mb-2">
                    <div class="switches-container" style="width:15rem">
                        <input type="radio" id="switchMonthly" name="switchPlan" value="Cycle" checked="checked" asp-for="BackUpType" />
                        <input type="radio" id="switchYearly" name="switchPlan" value="Once" asp-for="BackUpType" />
                        <label for="switchMonthly">Scheduled Backup</label>
                        <label for="switchYearly">Backup Now</label>
                        <div class="switch-wrapper">
                            <div class="switch">
                                <div>Scheduled Backup</div>
                                <div style="width:96%"> Backup Now</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" asp-for="IsLocalServer" name="inlineRadioOptions" id="inlineRadio1" value="islocal" checked>
                        <label class="form-check-label" for="inlineRadio1">Local CP Server</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" asp-for="IsBackUpServer" name="inlineRadioOptions" id="inlineRadio2" value="isremote">
                        <label class="form-check-label" for="inlineRadio2">Database Backup Server</label>
                    </div>
                </div>
                <div class="w-25">
                    <div class="form-group">
                        <div class="form-label"><span>Keep Last Backup</span></div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-last-backup"></i></span>
                            <select class="form-select-modal" data-placeholder="Select Keep Last Backup" data-live-search="true" id="selectbackup" asp-for="KeepBackUpLast">
                                <option value=""></option>
                                <option value="1Week">1 Week</option>
                                <option value="2Week">2 Week</option>
                                <option value="3Week">3 Week</option>
                                <option value="1Month">1 Month</option>
                                <option value="2Month">2 Month</option>
                                <option value="3Month">3 Month</option>
                                <option value="6Month">6 Month</option>
                                <option value="1Year">1 Year</option>

                            </select>
                        </div>
                        <span asp-validation-for="KeepBackUpLast" id="Keepbackup-error"></span>
                    </div>
                </div>
                <div class="month" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                    <div class="card border border-secondary-subtle rounded-md mb-2">
                        <div class="container-fluid" id="backupHidePath">
                            <div class="row align-items-center mt-3">
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-3">
                                    <div class="mb-3 form-group">
                                        <div class="form-label">Backup Path</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-backup_data"></i></span>
                                            <input type="text" asp-for="BackUpPath" id="txtBackupPath" autocomplete="off" class="form-control"
                                                   placeholder="Enter Backup Path" />
                                        </div>
                                        <span asp-validation-for="BackUpPath" id="Backup-error"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="Local" id="pills-Local" style="display:none;">
                    <div class="card border border-secondary-subtle rounded-md mb-2">
                        <div class="container-fluid">
                            <div class="row align-items-center mt-3">
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">FTP Host Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-host-name"></i></span>
                                            <input type="text" class="form-control" asp-for="Properties" maxlength="100" autocomplete="off" id="ftpHostName"
                                                   placeholder="Enter FTP Host Name" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpHostNameError"></span>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">FTP Username</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-user"></i></span>
                                            <input type="text" class="form-control" asp-for="Properties" maxlength="100" autocomplete="off" id="ftpUserName" placeholder="Enter FTP Username" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpUserNameError"></span>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">FTP Password</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-lock"></i></span>
                                            <input type="password" class="form-control" asp-for="Properties" autocomplete="off" id="ftpPassword" placeholder="Enter FTP Password" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpPasswordError"></span>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">FTP Port</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-ports"></i></span>
                                            <input type="number" class="form-control" asp-for="Properties" min="0" maxlength="5" autocomplete="off" id="ftpPort" placeholder="Enter FTP Port" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpPortError"></span>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">Source Path</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-url"></i></span>
                                            <input type="url" class="form-control" asp-for="Properties" id="sourcePathUrl" autocomplete="off" placeholder="Enter Source Path" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpSourceError"></span>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-2">
                                    <div class="form-group">
                                        <div class="form-label">Target Path</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-url"></i></span>
                                            <input type="url" class="form-control" asp-for="Properties" id="ftpTargetPathUrl" autocomplete="off" placeholder="Enter Target Path" />
                                        </div>
                                        <span asp-validation-for="Properties" id="ftpTargetError"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="year" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab" tabindex="0">
                    <div class="card border border-secondary-subtle rounded-md mb-2">
                        <div class="mb-3">
                            <div>
                                <nav class="backupdata-tab">
                                    <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                        <button class="nav-link active" id="nav-Minutes-tab" data-bs-toggle="tab" name="Scheduler"
                                                data-bs-target="#nav-Minutes" type="button" role="tab"
                                                aria-controls="nav-Minutes" aria-selected="true">
                                            Minutes
                                        </button>
                                        <button class="nav-link" id="nav-Hourly-tab" data-bs-toggle="tab" name="Scheduler"
                                                data-bs-target="#nav-Hourly" type="button" role="tab"
                                                aria-controls="nav-Hourly" aria-selected="false">
                                            Hourly
                                        </button>
                                        <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="Scheduler"
                                                data-bs-target="#nav-Daily" type="button" role="tab"
                                                aria-controls="nav-Daily" aria-selected="false">
                                            Daily
                                        </button>
                                        <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="Scheduler"
                                                data-bs-target="#nav-Weekly" type="button" role="tab"
                                                aria-controls="nav-Weekly" aria-selected="false">
                                            Weekly
                                        </button>
                                        <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="Scheduler"
                                                data-bs-target="#nav-Monthly" type="button" role="tab"
                                                aria-controls="nav-Monthly" aria-selected="false">
                                            Monthly
                                        </button>
                                    </div>
                                </nav>
                                <div class="tab-content ms-3" id="nav-tabContent">
                                    <div class="tab-pane fade show active" id="nav-Minutes" role="tabpanel" aria-labelledby="nav-home-tab" tabindex="0">
                                        <div class="row mt-2 align-items-end">
                                            <div class="col-3">
                                                <div class="form-group">
                                                    <div class="form-label">Minites</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text pe-0"><i class="cp-calendar"></i></span>
                                                        @Html.TextBox("txtMins", null, new { id = "txtMins", type = "number", maxlength = "2", min = "0", max = "59", pattern = "d{2}", @class = "form-control", @placeholder = "Enter Mins", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        <span class="input-group-text small text-secondary">mins</span>
                                                    </div>
                                                    <span id="CronMin-error"></span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                        <div class="row mt-2">
                                            <div class="col-4">
                                                <div class="form-group">
                                                    <div class="form-label">Hours</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                        </span>
                                                        @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        <span class="input-group-text fs-8 ms-1 text-secondary">
                                                            hrs
                                                        </span>
                                                    </div>
                                                    <span id="CronHourly-error"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-6">
                                                <div class="form-group">
                                                    <div class="form-label">Minutes</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                        </span>
                                                        @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                        <span class="input-group-text form-label mb-0 text-secondary">mins</span>
                                                    </div>
                                                    <span id="CronHourMin-error"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                        <div class="row mt-2 align-items-center">
                                            <div class="col-4">
                                                <div class="form-group flex-fill ">
                                                    <label class="animation-label form-label">
                                                        Select Day Type
                                                    </label>
                                                    <div class="">
                                                        <span class="input-group-text"></span>
                                                        <div class="form-check form-check-inline">
                                                            <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover" value="everyday" cursorshover="true">
                                                            <label for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover" value="MON-FRI">
                                                            <label for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                        </div>
                                                    </div>
                                                    <span id="Crondaysevery-error"></span>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="form-group">
                                                    <div class="form-label">Starts at</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                        @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                    </div>
                                                    <span id="CroneveryHour-error"></span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                        <div class="row row-cols-2 mt-2">
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <label class="form-label custom-cursor-default-hover">Select Day</label>
                                                    <div class="bg-transparent input-group">
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="MON"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="TUE"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="WED"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="THU"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="FRI" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="SAT"><labelfor ="defaultCheck-6" class="form-check-label">Saturday</label>
                                                        </div>
                                                        <div class="form-check form-check-inline">
                                                            <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="SUN"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                        </div>
                                                    </div>
                                                    <span id="CronDay-error"></span>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="form-group">
                                                    <div class="form-label">Starts at</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                        @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                    </div>
                                                    <span id="CronddlHour-error"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                        <div class="row row-cols-2 mt-2">
                                            <div class="col-4">
                                                <div class="mb-3 form-group">
                                                    <div class="form-label">Select Month And Year</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                        </span>
                                                        <input name="month" autocomplete="off" type="month"
                                                               id="lblMonth"
                                                               class="form-control custom-cursor-default-hover"
                                                               cursorshover="true" />
                                                    </div>
                                                    <span id="CronMonthly-error"></span>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                    <div class="form-label mb-2">Select Date</div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                        <label class="form-check-label checklabel">1</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                        <label class="form-check-label checklabel">2</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                        <label class="form-check-label checklabel">3</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                        <label class="form-check-label checklabel">4</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                        <label class="form-check-label checklabel">5</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                        <label class="form-check-label checklabel">6</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                        <label class="form-check-label checklabel">7</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                        <label class="form-check-label checklabel">8</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                        <label class="form-check-label checklabel">9</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                        <label class="form-check-label checklabel">10</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                        <label class="form-check-label checklabel">11</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                        <label class="form-check-label checklabel">12</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                        <label class="form-check-label checklabel">13</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                        <label class="form-check-label checklabel">14</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                        <label class="form-check-label checklabel">15</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                        <label class="form-check-label checklabel">16</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                        <label class="form-check-label checklabel">17</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                        <label class="form-check-label checklabel">18</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                        <label class="form-check-label checklabel">19</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                        <label class="form-check-label checklabel">20</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                        <label class="form-check-label checklabel">21</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                        <label class="form-check-label checklabel">22</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                        <label class="form-check-label checklabel">23</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                        <label class="form-check-label checklabel">24</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                        <label class="form-check-label checklabel">25</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                        <label class="form-check-label checklabel">26</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                        <label class="form-check-label checklabel">27</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                        <label class="form-check-label checklabel">28</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                        <label class="form-check-label checklabel">29</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                        <label class="form-check-label checklabel">30</label>
                                                    </div>
                                                    <div class="form-check form-check-inline" style="width: 33px;">
                                                        <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                        <label class="form-check-label checklabel">31</label>
                                                    </div>
                                                    <div class="form-group">
                                                        <span id="CronMon-error"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="form-group">
                                                    <div class="form-label">Starts at</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-calendar"></i>
                                                        </span>
                                                        @Html.TextBox("MonthlyHours", null, new { id = "MonthlyHours", type = "time", min = "0", max = "23", @class = "form-control", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off" })
                                                    </div>
                                                    <span id="MonthlyHours-error"></span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-secondary btn-sm bkclass" data-dismiss="modal" id="btnExecute"><span class="btn-text">Execute</span></button>
                <button type="button" id="btnSave" data-dismiss="modal" class="animation-btn btn btn-primary btn-sm bkclass"><span class="btn-text">Save</span></button>
                @*  <button type="button" class="btn btn-primary">Save changes</button> *@
            </div>
        </div>
    </div>
</div>

<script>
    function Local_CP_Server() {
        document.getElementById('pills-Local').style.display = 'block';
    }

    function showPassword(input, icon) {
        input.attr("type", "text");
        icon.removeClass("bi-eye").addClass("bi-eye-slash");
    }

    function hidePassword(input, icon) {
        input.attr("type", "password");
        icon.removeClass("bi-eye-slash").addClass("bi-eye");
    }
    $(".toggle-password").click(async function () {
        debugger
        let input = $(this).prev();
        let icon = $(this).find("i");

        if (input.attr("type") === "password") {

            showPassword(input, icon);
            let encryptedPassword = input.val();
            if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
                let afterLoginName = await onfocusPassword(encryptedPassword);
                input.val(afterLoginName);

            }

        } else {

            hidePassword(input, icon);
        }
    });
</script>
<script src="~/js/common/commoncronexpressionjs.js"></script>
<script src="~/js/Admin/Backup Data/BackupData.js"></script>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
