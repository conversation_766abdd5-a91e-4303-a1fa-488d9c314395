﻿using ContinuityPatrol.Application.Features.Workflow.Events.Draft;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Draft;

public class UpdateWorkflowIsDraftCommandHandler : IRequestHandler<UpdateWorkflowIsDraftCommand, UpdateWorkflowIsDraftResponse>
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly IWindowsService _windowsService;
    private readonly IJobScheduler _client;
    private readonly IPublisher _publisher;
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;
    private readonly IApprovalMatrixRequestRepository _approvalMatrixRequestRepository;
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IWorkflowTempRepository _workflowTempRepository;
    private readonly IVersionManager _versionManager;

    public UpdateWorkflowIsDraftCommandHandler(IWorkflowRepository workflowRepository,
        IWindowsService windowsService, ILoadBalancerRepository loadBalancerRepository,
      IJobScheduler client, IPublisher publisher, IApprovalMatrixRepository approvalMatrixRepository,
        IApprovalMatrixRequestRepository approvalMatrixRequestRepository,
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository, 
        IWorkflowProfileInfoRepository workflowProfileInfoRepository, ILoggedInUserService loggedInUserService, 
        IWorkflowTempRepository workflowTempRepository, IVersionManager versionManager)
    {
        _workflowRepository = workflowRepository;
        _windowsService = windowsService;
        _loadBalancerRepository = loadBalancerRepository;
        _client = client;
        _publisher = publisher;
        _approvalMatrixRepository = approvalMatrixRepository;
        _approvalMatrixRequestRepository = approvalMatrixRequestRepository;
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _loggedInUserService = loggedInUserService;
        _workflowTempRepository = workflowTempRepository;
        _versionManager = versionManager;
    }

    public async Task<UpdateWorkflowIsDraftResponse> Handle(UpdateWorkflowIsDraftCommand request, CancellationToken cancellationToken)
    {
        var nodeConfig =
            await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null) throw new InvalidException("LoadBalancer not configured!.");

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

       // var monitorUrl = UrlHelper.GenerateMonitorCheckUrl(nodeConfig.TypeCategory, baseUrl);

        //var monitorResponse = await _windowsService.CheckWindowsService(monitorUrl);

        //if (!monitorResponse.Success) throw new WindowServiceException(monitorResponse.InActiveNodes, ServiceType.Monitor.ToString(), monitorResponse.Message);

        Guard.Against.InvalidGuidOrEmpty(request.Id, "Workflow Id");

        var eventToUpdate = await _workflowRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Workflow), request.Id);

        eventToUpdate.IsDraft = request.IsDraft;

        var workflowProfileInfo = await _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(request.Id);

        if (workflowProfileInfo is null)
        {
            throw new Exception($"Workflow '{eventToUpdate.Name}' is not attached to any profile.");
        }

        var approvalMatrixDto =
            await _approvalMatrixRepository.GetApprovalMatrixByBusinessFunctionId(
                workflowProfileInfo.BusinessFunctionId);

        if (approvalMatrixDto is null)
        {
            throw new Exception($"Workflow '{eventToUpdate.Name}' is not attached to approval matrix template.");
        }

        var version = await _versionManager.GetUpdateVersion(eventToUpdate.Version);

        var requestId = $"CP-REQ-{DateTime.UtcNow:yyyyMMddHHmmssfff}-{Guid.NewGuid().ToString("N").Substring(0, 6)}";

        await _workflowTempRepository.AddAsync(new Domain.Entities.WorkflowTemp
        {
            ApprovalMatrixId = approvalMatrixDto.ReferenceId,
            WorkflowId = eventToUpdate.ReferenceId,
            RequestId = requestId,
            Name = eventToUpdate.Name,
            Properties = request.Properties,
            Version = version
        });

        var userLists = approvalMatrixDto.Properties.IsNotNullOrWhiteSpace() 
            ? JArray.Parse(approvalMatrixDto.Properties)
            .Select((obj, index) => new
            {
                ProcessName = (string)obj["name"],
                Description = (string)obj["description"],
                Approvers = obj["userLists"]?.ToString(Formatting.None),
                IsApproval = index == 0
            }).ToList()
            :null;

        var userJson = userLists is not null ? JsonConvert.SerializeObject(userLists) : null;

       

        var approvalMatrixRequest = new Domain.Entities.ApprovalMatrixRequest
        {
            RequestId = requestId,
            ProcessName = requestId,
            ApprovalMatrixId = approvalMatrixDto.ReferenceId,
            Approvers = userJson,
            Status = "Waiting for Approval",
            UserName = _loggedInUserService.LoginName
        };

        await _approvalMatrixRequestRepository.AddAsync(approvalMatrixRequest);

        var approvalMatrixApprovalList = userLists!
            .SelectMany(user => JArray.Parse(user.Approvers)
                .Select(approver => new Domain.Entities.ApprovalMatrixApproval
                {
                    ApprovalMatrixId = approvalMatrixDto.ReferenceId,
                    RequestId = requestId,
                    ProcessName = user.ProcessName,
                    Description = user.Description,
                    UserId = _loggedInUserService.UserId,
                    UserName = _loggedInUserService.LoginName,
                    Status = "Waiting for Approval",
                    ApproverId = (string)approver["id"],
                    ApproverName = (string)approver["name"],
                    IsApproval = user.IsApproval
                }))
            .ToList();

        await _approvalMatrixApprovalRepository.AddRange(approvalMatrixApprovalList);

        await _workflowRepository.UpdateAsync(eventToUpdate);

        var url = UrlHelper.GenerateApprovalRequestUrl(baseUrl, requestId);

        await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });

        var response = new UpdateWorkflowIsDraftResponse
        {
            WorkflowId = request.Id,
            Message = $"workflow {eventToUpdate.Name} IsDraft Updated successfully."
        };

        await _publisher.Publish(new UpdateWorkflowDraftEvent { WorkflowId = eventToUpdate.ReferenceId, WorkflowName = eventToUpdate.Name, IsDraft = request.IsDraft }, cancellationToken);

        return response;
    }
}