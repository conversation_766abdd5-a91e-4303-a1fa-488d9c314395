using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;
using System.Data;
using System.Data.Common;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class SybaseRSHADRMonitorLogsRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly SybaseRSHADRMonitorLogsRepository _repository;
    private readonly SybaseRSHADRMonitorLogFixture _fixture;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public SybaseRSHADRMonitorLogsRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockConfiguration = new Mock<IConfiguration>();
        
        _repository = new SybaseRSHADRMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
        _fixture = new SybaseRSHADRMonitorLogFixture();

        // Setup default mock configuration
        _mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("encrypted_connection_string");
        _mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("encrypted_provider");
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnMatchingLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();

        var log1 = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test Sybase 1",
            workflowId: "WF_001",
            workflowName: "Sybase RSHADR Workflow 1",
            isActive: true
        );
        var log2 = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectId: "INFRA_002",
            infraObjectName: "Test Sybase 2",
            workflowId: "WF_002",
            workflowName: "Sybase RSHADR Workflow 2",
            isActive: true
        );
        var log3 = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_BACKUP",
            infraObjectId: "INFRA_003",
            infraObjectName: "Test Sybase 3",
            workflowId: "WF_003",
            workflowName: "Sybase Backup Workflow 3",
            isActive: true
        );

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);
        await _repository.AddAsync(log3);

        // Act
        var result = await _repository.GetDetailByType("SYBASE_RSHADR");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal("SYBASE_RSHADR", log.Type));
        Assert.Contains(result, log => log.InfraObjectName == "Test Sybase 1");
        Assert.Contains(result, log => log.InfraObjectName == "Test Sybase 2");
        Assert.DoesNotContain(result, log => log.Type == "SYBASE_BACKUP");
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSybaseRSHADRMonitorLog(type: "SYBASE_RSHADR", isActive: true);
        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType("NONEXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldOnlyReturnActiveLogs()
    {
        // Arrange
        await ClearDatabase();

        var activeLog = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectName: "Active Sybase",
            isActive: true
        );
        var inactiveLog = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectName: "Inactive Sybase",
            isActive: false
        );

        await _repository.AddAsync(activeLog);
        await _repository.AddAsync(inactiveLog);

        // Act
        var result = await _repository.GetDetailByType("SYBASE_RSHADR");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active Sybase", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectId: "INFRA_001",
            infraObjectName: "Test Sybase Object",
            workflowId: "WF_001",
            workflowName: "Test Workflow",
            properties: "{\"rpo\": \"15\", \"status\": \"running\"}",
            configuredRPO: "15",
            dataLagValue: "5",
            threshold: "10",
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetDetailByType("SYBASE_RSHADR");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultLog = result[0];
        
        Assert.Equal("SYBASE_RSHADR", resultLog.Type);
        Assert.Equal("INFRA_001", resultLog.InfraObjectId);
        Assert.Equal("Test Sybase Object", resultLog.InfraObjectName);
        Assert.Equal("WF_001", resultLog.WorkflowId);
        Assert.Equal("Test Workflow", resultLog.WorkflowName);
        Assert.Equal("{\"rpo\": \"15\", \"status\": \"running\"}", resultLog.Properties);
        Assert.Equal("15", resultLog.ConfiguredRPO);
        Assert.Equal("5", resultLog.DataLagValue);
        Assert.Equal("10", resultLog.Threshold);
        Assert.True(resultLog.IsActive);
        Assert.NotNull(resultLog.ReferenceId);
        Assert.True(resultLog.Id > 0);
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();

        var log = _fixture.CreateSybaseRSHADRMonitorLog(type: "SYBASE_RSHADR", isActive: true);
        await _repository.AddAsync(log);

        // Act
        var result1 = await _repository.GetDetailByType("SYBASE_RSHADR");
        var result2 = await _repository.GetDetailByType("sybase_rshadr");
        var result3 = await _repository.GetDetailByType("Sybase_RSHADR");

        // Assert
        Assert.NotNull(result1);
        Assert.Single(result1);
        
        Assert.NotNull(result2);
        Assert.Empty(result2);
        
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogsWithinDateRange_WhenNoBackupTable()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log1 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test Sybase 1",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );
        var log2 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test Sybase 2",
            createdDate: new DateTime(2024, 1, 20),
            lastModifiedDate: new DateTime(2024, 1, 20),
            isActive: true
        );
        var log3 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test Sybase 3",
            createdDate: new DateTime(2024, 2, 5), // Outside date range
            lastModifiedDate: new DateTime(2024, 2, 5),
            isActive: true
        );
        var log4 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: "INFRA_002", // Different infra object
            infraObjectName: "Different Sybase",
            createdDate: new DateTime(2024, 1, 10),
            lastModifiedDate: new DateTime(2024, 1, 10),
            isActive: true
        );

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);
        await _repository.AddAsync(log3);
        await _repository.AddAsync(log4);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal(infraObjectId, log.InfraObjectId));
        Assert.Contains(result, log => log.InfraObjectName == "Test Sybase 1");
        Assert.Contains(result, log => log.InfraObjectName == "Test Sybase 2");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "Test Sybase 3");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "Different Sybase");
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoLogsInDateRange()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            createdDate: new DateTime(2024, 2, 15), // Outside date range
            lastModifiedDate: new DateTime(2024, 2, 15),
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenInfraObjectIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        var log = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: "INFRA_001",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId("NONEXISTENT_INFRA", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldOnlyReturnActiveLogs()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var activeLog = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Active Sybase",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );
        var inactiveLog = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Inactive Sybase",
            createdDate: new DateTime(2024, 1, 20),
            lastModifiedDate: new DateTime(2024, 1, 20),
            isActive: false
        );

        await _repository.AddAsync(activeLog);
        await _repository.AddAsync(inactiveLog);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active Sybase", result[0].InfraObjectName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOrderedResults()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log1 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test Sybase 1",
            createdDate: new DateTime(2024, 1, 20),
            lastModifiedDate: new DateTime(2024, 1, 20),
            isActive: true
        );
        var log2 = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Test Sybase 2",
            createdDate: new DateTime(2024, 1, 10),
            lastModifiedDate: new DateTime(2024, 1, 10),
            isActive: true
        );

        await _repository.AddAsync(log1);
        await _repository.AddAsync(log2);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        // Results should be ordered by CreatedDate
        Assert.True(result[0].CreatedDate <= result[1].CreatedDate);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnAllPropertiesCorrectly()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var log = _fixture.CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectId: infraObjectId,
            infraObjectName: "Complete Sybase Object",
            workflowId: "WF_001",
            workflowName: "Complete Workflow",
            properties: "{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}",
            configuredRPO: "30",
            dataLagValue: "8",
            threshold: "15",
            createdDate: new DateTime(2024, 1, 15),
            lastModifiedDate: new DateTime(2024, 1, 15),
            isActive: true
        );

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultLog = result[0];

        Assert.Equal("SYBASE_RSHADR", resultLog.Type);
        Assert.Equal(infraObjectId, resultLog.InfraObjectId);
        Assert.Equal("Complete Sybase Object", resultLog.InfraObjectName);
        Assert.Equal("WF_001", resultLog.WorkflowId);
        Assert.Equal("Complete Workflow", resultLog.WorkflowName);
        Assert.Equal("{\"rpo\": \"30\", \"status\": \"healthy\", \"lastCheck\": \"2024-01-01T12:00:00Z\"}", resultLog.Properties);
        Assert.Equal("30", resultLog.ConfiguredRPO);
        Assert.Equal("8", resultLog.DataLagValue);
        Assert.Equal("15", resultLog.Threshold);
        Assert.True(resultLog.IsActive);
        Assert.NotNull(resultLog.ReferenceId);
        Assert.True(resultLog.Id > 0);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleDateBoundaries()
    {
        // Arrange
        await ClearDatabase();

        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var infraObjectId = "INFRA_001";

        var logOnStartDate = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Start Date Log",
            createdDate: new DateTime(2024, 1, 1),
            lastModifiedDate: new DateTime(2024, 1, 1),
            isActive: true
        );
        var logOnEndDate = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "End Date Log",
            createdDate: new DateTime(2024, 1, 31),
            lastModifiedDate: new DateTime(2024, 1, 31),
            isActive: true
        );
        var logBeforeStart = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "Before Start Log",
            createdDate: new DateTime(2023, 12, 31),
            lastModifiedDate: new DateTime(2023, 12, 31),
            isActive: true
        );
        var logAfterEnd = _fixture.CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: "After End Log",
            createdDate: new DateTime(2024, 2, 1),
            lastModifiedDate: new DateTime(2024, 2, 1),
            isActive: true
        );

        await _repository.AddAsync(logOnStartDate);
        await _repository.AddAsync(logOnEndDate);
        await _repository.AddAsync(logBeforeStart);
        await _repository.AddAsync(logAfterEnd);

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, log => log.InfraObjectName == "Start Date Log");
        Assert.Contains(result, log => log.InfraObjectName == "End Date Log");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "Before Start Log");
        Assert.DoesNotContain(result, log => log.InfraObjectName == "After End Log");
    }

    #endregion

    #region Helper Method Tests

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableName()
    {
        // Act
        var tableName = _repository.GetTableName<SybaseRSHADRMonitorLog>();

        // Assert
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableDoesNotExist()
    {
        // Arrange
        var nonExistentTableName = "NonExistentTable_bkp";
        var schemaName = "TestSchema";
        var providerName = "mssql";

        // Act
        var result = await _repository.IsTableExistAsync(nonExistentTableName, schemaName, providerName);

        // Assert
        Assert.False(result);
    }



    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.SybaseRSHADRMonitorLogs.RemoveRange(_dbContext.SybaseRSHADRMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }
}
