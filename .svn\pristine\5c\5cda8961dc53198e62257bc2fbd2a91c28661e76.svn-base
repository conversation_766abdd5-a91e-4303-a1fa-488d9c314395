﻿using ContinuityPatrol.Application.Features.PageWidget.Events.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.PageWidget.Events
{
    public class UpdatePageWidgetEventTests
    {
        private readonly Mock<ILogger<PageWidgetUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly PageWidgetUpdatedEventHandler _handler;

        public UpdatePageWidgetEventTests()
        {
            _mockLogger = new Mock<ILogger<PageWidgetUpdatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _handler = new PageWidgetUpdatedEventHandler(_mockLoggedInUserService.Object, _mockLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenEventIsHandledSuccessfully()
        {
            var pageWidgetEvent = new PageWidgetUpdatedEvent { Name = "TestWidget" };

            _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
            _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
            _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
            _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
            _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(pageWidgetEvent, CancellationToken.None);

            _mockLogger.Verify(
                l => l.LogInformation($"PageWidget '{pageWidgetEvent.Name}' updated successfully."),
                Times.Once);

            _mockUserActivityRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldSaveUserActivity_WhenEventIsHandledSuccessfully()
        {
            var pageWidgetEvent = new PageWidgetUpdatedEvent { Name = "TestWidget" };

            _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
            _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
            _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
            _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
            _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(pageWidgetEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(r =>
                r.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == "123" &&
                    ua.LoginName == "TestUser" &&
                    ua.RequestUrl == "http://test.url" &&
                    ua.CompanyId == "456" &&
                    ua.HostAddress == "127.0.0.1" &&
                    ua.Action == "Update PageWidget" &&
                    ua.Entity == "PageWidget" &&
                    ua.ActivityType == "Update" &&
                    ua.ActivityDetails == $"PageWidget '{pageWidgetEvent.Name}' updated successfully.")),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldHandleDefaultGuid_WhenUserIdIsNullOrEmpty()
        {
            var pageWidgetEvent = new PageWidgetUpdatedEvent { Name = "TestWidget" };

            _mockLoggedInUserService.Setup(u => u.UserId).Returns(string.Empty);
            _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
            _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
            _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
            _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(pageWidgetEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(r =>
                r.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    !string.IsNullOrEmpty(ua.UserId) &&
                    ua.UserId.Length > 0)),
                Times.Once);
        }
    }
}
