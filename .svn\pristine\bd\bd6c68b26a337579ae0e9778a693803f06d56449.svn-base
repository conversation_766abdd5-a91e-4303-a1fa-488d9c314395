using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetPaginatedList;

public class GetDriftImpactTypeMasterPaginatedListQueryHandler : IRequestHandler<
    GetDriftImpactTypeMasterPaginatedListQuery, PaginatedResult<DriftImpactTypeMasterListVm>>
{
    private readonly IDriftImpactTypeMasterRepository _driftImpactTypeMasterRepository;
    private readonly IMapper _mapper;

    public GetDriftImpactTypeMasterPaginatedListQueryHandler(IMapper mapper,
        IDriftImpactTypeMasterRepository driftImpactTypeMasterRepository)
    {
        _mapper = mapper;
        _driftImpactTypeMasterRepository = driftImpactTypeMasterRepository;
    }

    public async Task<PaginatedResult<DriftImpactTypeMasterListVm>> Handle(
        GetDriftImpactTypeMasterPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftImpactTypeMasterFilterSpecification(request.SearchString);

        var queryable = await _driftImpactTypeMasterRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftImpacttypeMasterList = _mapper.Map<PaginatedResult<DriftImpactTypeMasterListVm>>(queryable);
         
        return driftImpacttypeMasterList;
        //var queryable = _driftImpactTypeMasterRepository.GetPaginatedQuery();

        //var productFilterSpec = new DriftImpactTypeMasterFilterSpecification(request.SearchString);

        //var driftImpacttypeMasterList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftImpactTypeMasterListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftImpacttypeMasterList;
    }
}