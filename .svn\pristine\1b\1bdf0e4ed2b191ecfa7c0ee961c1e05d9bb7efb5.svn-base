﻿using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetMSSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class MssqlMonitorStatusService : BaseService, IMssqlMonitorStatusService
{
    public MssqlMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMSSQLMonitorStatusCommand createMssqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Create MSSQLMonitorStatus '{createMssqlMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createMssqlMonitorStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMSSQLMonitorStatusCommand updateMssqlMonitorStatusCommand)
    {
        Logger.LogDebug($"Update MSSQLMonitorStatus '{updateMssqlMonitorStatusCommand.InfraObjectName}'");

        return await Mediator.Send(updateMssqlMonitorStatusCommand);
    }

    public async Task<List<MSSQLMonitorStatusListVm>> GetAllMSSQLMonitorStatus()
    {
        Logger.LogDebug("Get All MSSQLMonitorStatus");

        return await Mediator.Send(new GetMSSQLMonitorStatusListQuery());
    }

    public async Task<MSSQLMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "MSSQLMonitorStatusById");

        Logger.LogDebug($"Get MSSQLMonitorStatus Detail By Id '{id}' ");

        return await Mediator.Send(new GetMSSQLMonitorStatusDetailQuery { Id = id });
    }

    public async Task<List<MSSQLMonitorStatusDetailByTypeVm>> GetMSSQLMonitorStatusByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "MSSQLMonitorStatus Type");

        Logger.LogDebug($"Get MSSQLMonitorStatus Detail by Type '{type}'");

        return await Mediator.Send(new GetMSSQLMonitorStatusDetailByTypeQuery { Type = type });
    }

    public async Task<PaginatedResult<MSSQLMonitorStatusListVm>> GetPaginatedMSSQLMonitorStatus(
        GetMSSQLMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in MSSQLMonitorStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllMSSQLMonitorStatusCacheKey,
            () => Mediator.Send(query));
    }

    public async Task<string> GetMSSQLMonitorStatusByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get MSSQLMonitorStatus by InfraObject Id '{infraObjectId}'");

        return await Mediator.Send(new GetMSSQLMonitorStatusByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }
}