namespace ContinuityPatrol.Application.Features.WorkflowDrCalender.Queries.GetNameUnique;

public class GetWorkflowDrCalenderNameUniqueQueryHandler : IRequestHandler<GetWorkflowDrCalenderNameUniqueQuery, bool>
{
    private readonly IWorkflowDrCalenderRepository _workflowDrCalenderRepository;

    public GetWorkflowDrCalenderNameUniqueQueryHandler(IWorkflowDrCalenderRepository workflowDrCalenderRepository)
    {
        _workflowDrCalenderRepository = workflowDrCalenderRepository;
    }

    public async Task<bool> Handle(GetWorkflowDrCalenderNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _workflowDrCalenderRepository.IsNameExist(request.Name, request.Id);
    }
}
