using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple specification for testing
public class RoboCopyFilterSpecification : Specification<RoboCopy>
{
    public RoboCopyFilterSpecification(string? searchString = null, string? replicationType = null)
    {
        Criteria = p => p.Name != null;

        if (!string.IsNullOrEmpty(searchString))
        {
            And(p => p.Name.Contains(searchString) ||
                     p.ReplicationType.Contains(searchString) ||
                     p.Properties.Contains(searchString));
        }

        if (!string.IsNullOrEmpty(replicationType))
        {
            And(p => p.ReplicationType == replicationType);
        }
    }
}

public class RoboCopyRepositoryTests : IClassFixture<RoboCopyFixture>
{
    private readonly RoboCopyFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RoboCopyRepository _repository;

    public RoboCopyRepositoryTests(RoboCopyFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new RoboCopyRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestRoboCopy", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntityAndIdIsValid()
    {
        // Arrange
        var roboCopy1 = new RoboCopy 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "Type1",
            Properties = "Properties1"
        };
        var roboCopy2 = new RoboCopy 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "Type2",
            Properties = "Properties2"
        };

        _dbContext.RoboCopys.AddRange(roboCopy1, roboCopy2);
        await _dbContext.SaveChangesAsync();

        // Act - Check if name exists for a different entity
        var result = await _repository.IsNameExist("TestRoboCopy", roboCopy1.ReferenceId);

        // Assert
        Assert.True(result); // Should return true because there's another entity with the same name
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsOnlyForSameEntityAndIdIsValid()
    {
        // Arrange
        var roboCopy = new RoboCopy 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "Type1",
            Properties = "Properties1"
        };

        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act - Check if name exists for the same entity
        var result = await _repository.IsNameExist("TestRoboCopy", roboCopy.ReferenceId);

        // Assert
        Assert.False(result); // Should return false because it's the same entity
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsValid()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("NonExistentName", Guid.NewGuid().ToString());

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameIsNullOrEmpty(string name)
    {
        // Act
        var result = await _repository.IsNameExist(name, Guid.NewGuid().ToString());

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsNameExist_ShouldReturnFalse_WhenIdIsNullOrEmpty(string id)
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("TestRoboCopy", id);

        // Assert
        Assert.True(result); // Should return true because id is invalid, so it checks for any existence
    }

    #endregion

    #region IsRoboCopyNameUnique Tests

    [Fact]
    public async Task IsRoboCopyNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRoboCopyNameUnique("TestRoboCopy");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsRoboCopyNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "TestRoboCopy";
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRoboCopyNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsRoboCopyNameUnique_ShouldReturnTrue_WhenMultipleEntitiesWithSameName()
    {
        // Arrange
        var roboCopy1 = new RoboCopy 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "Type1",
            Properties = "Properties1"
        };
        var roboCopy2 = new RoboCopy 
        { 
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "Type2",
            Properties = "Properties2"
        };

        _dbContext.RoboCopys.AddRange(roboCopy1, roboCopy2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsRoboCopyNameUnique("TestRoboCopy");

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsRoboCopyNameUnique_ShouldReturnFalse_WhenNameIsNullOrEmpty(string name)
    {
        // Act
        var result = await _repository.IsRoboCopyNameUnique(name);

        // Assert
        Assert.False(result);
    }

    //[Fact]
    //public async Task IsRoboCopyNameUnique_ShouldBeCaseInsensitive()
    //{
    //    // Arrange
    //    var roboCopy = _fixture.RoboCopyDto;
    //    roboCopy.Name = "TestRoboCopy";
    //    _dbContext.RoboCopys.Add(roboCopy);
    //    await _dbContext.SaveChangesAsync();

    //    // Act
    //    var result = await _repository.IsRoboCopyNameUnique("testrobocopy");

    //    // Assert
    //    Assert.True(result); // Assuming the database comparison is case-insensitive
    //}

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRoboCopy_WhenValidEntity()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        roboCopy.Name = "Test RoboCopy";
        roboCopy.ReplicationType = "File";
        roboCopy.Properties = "{\"source\":\"C:\\\\Source\",\"destination\":\"D:\\\\Destination\"}";

        // Act
        var result = await _repository.AddAsync(roboCopy);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roboCopy.Name, result.Name);
        Assert.Equal(roboCopy.ReplicationType, result.ReplicationType);
        Assert.Equal(roboCopy.Properties, result.Properties);
        Assert.Single(_dbContext.RoboCopys);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(roboCopy.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(roboCopy.Id, result.Id);
        Assert.Equal(roboCopy.Name, result.Name);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        var updatedName = "Updated RoboCopy Name";
        roboCopy.Name = updatedName;

        // Act
        var result = await _repository.UpdateAsync(roboCopy);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedName, result.Name);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var roboCopy = _fixture.RoboCopyDto;
        _dbContext.RoboCopys.Add(roboCopy);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(roboCopy);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(roboCopy.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WithBasicPagination()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>();
        for (int i = 1; i <= 15; i++)
        {
            roboCopies.Add(new RoboCopy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"RoboCopy{i}",
                ReplicationType = "File",
                Properties = $"Properties{i}"
            });
        }

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(10, result.Data.Count); // Page size
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(2, result.TotalPages); // 15 records / 10 per page = 2 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnSecondPage()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>();
        for (int i = 1; i <= 25; i++)
        {
            roboCopies.Add(new RoboCopy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"RoboCopy{i:D2}",
                ReplicationType = "File",
                Properties = $"Properties{i}"
            });
        }

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(2, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(25, result.TotalCount);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(3, result.TotalPages); // 25 records / 10 per page = 3 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterBySearchString()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "FileSync",
                ReplicationType = "File",
                Properties = "FileProperties"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DatabaseSync",
                ReplicationType = "Database",
                Properties = "DatabaseProperties"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "NetworkSync",
                ReplicationType = "Network",
                Properties = "NetworkProperties"
            }
        };

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification("File");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.TotalCount); // Only records matching search
        Assert.Single(result.Data);
        Assert.Contains(result.Data, r => r.Name == "FileSync");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterByReplicationType()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "FileRoboCopy1",
                ReplicationType = "File",
                Properties = "Properties1"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "FileRoboCopy2",
                ReplicationType = "File",
                Properties = "Properties2"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DatabaseRoboCopy",
                ReplicationType = "Database",
                Properties = "Properties3"
            }
        };

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification(replicationType: "File");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.TotalCount); // Only File replication type
        Assert.Equal(2, result.Data.Count);
        Assert.All(result.Data, r => Assert.Equal("File", r.ReplicationType));
        Assert.Contains(result.Data, r => r.Name == "FileRoboCopy1");
        Assert.Contains(result.Data, r => r.Name == "FileRoboCopy2");
        Assert.DoesNotContain(result.Data, r => r.Name == "DatabaseRoboCopy");
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldSortDescending()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "ARoboCopy",
                ReplicationType = "File",
                Properties = "Properties1"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "BRoboCopy",
                ReplicationType = "File",
                Properties = "Properties2"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "CRoboCopy",
                ReplicationType = "File",
                Properties = "Properties3"
            }
        };

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(3, result.Data.Count);
        // Should be sorted by Name descending
        Assert.Equal("CRoboCopy", result.Data[0].Name);
        Assert.Equal("BRoboCopy", result.Data[1].Name);
        Assert.Equal("ARoboCopy", result.Data[2].Name);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResult_WhenNoMatchingRecords()
    {
        // Arrange
        await ClearDatabase();

        var roboCopy = new RoboCopy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "File",
            Properties = "TestProperties"
        };

        await _repository.AddAsync(roboCopy);

        var specification = new RoboCopyFilterSpecification("NonExistentSearch");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(0, result.TotalCount);
        Assert.Empty(result.Data);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(0, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleNullSpecification()
    {
        // Arrange
        await ClearDatabase();

        var roboCopy = new RoboCopy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "File",
            Properties = "TestProperties"
        };

        await _repository.AddAsync(roboCopy);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null!, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.TotalCount);
        Assert.Single(result.Data);
        Assert.Equal("TestRoboCopy", result.Data[0].Name);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnCorrectFields()
    {
        // Arrange
        await ClearDatabase();

        var roboCopy = new RoboCopy
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestRoboCopy",
            ReplicationType = "File",
            Properties = "TestProperties"
        };

        await _repository.AddAsync(roboCopy);

        var specification = new RoboCopyFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);

        var returnedRoboCopy = result.Data[0];
        Assert.NotNull(returnedRoboCopy.ReferenceId);
        Assert.Equal("TestRoboCopy", returnedRoboCopy.Name);
        Assert.Equal("File", returnedRoboCopy.ReplicationType);
        Assert.Equal("TestProperties", returnedRoboCopy.Properties);
        Assert.True(returnedRoboCopy.Id > 0);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleLargePageSize()
    {
        // Arrange
        await ClearDatabase();

        var roboCopies = new List<RoboCopy>();
        for (int i = 1; i <= 5; i++)
        {
            roboCopies.Add(new RoboCopy
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"RoboCopy{i}",
                ReplicationType = "File",
                Properties = $"Properties{i}"
            });
        }

        foreach (var roboCopy in roboCopies)
        {
            await _repository.AddAsync(roboCopy);
        }

        var specification = new RoboCopyFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 100, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(5, result.TotalCount);
        Assert.Equal(5, result.Data.Count); // All records fit in one page
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(1, result.TotalPages);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RoboCopys.RemoveRange(_dbContext.RoboCopys);
        await _dbContext.SaveChangesAsync();
    }
}
