﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Helper;

public class BulkImportJob : IJob
{
    private const string Chars =
        "ABCDEFGHIJKPERPETUUITILMANISANKARNOPQRSTUVWXYZ0123456789ABCDEFGHIJKPERPETUUITILMNOPQRSTUVWXYZ";

    private readonly ILogger<BulkImportJob> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IJobScheduler _client;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly IVersionManager _versionManager;
    private ILoggedInUserService _loggedInUserService;

    public BulkImportJob(ILogger<BulkImportJob> logger, IHttpContextAccessor httpContextAccessor, IServiceScopeFactory scopeFactory, IJobScheduler client, IHubContext<NotificationHub> hubContext, IVersionManager versionManager)
    {
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _scopeFactory = scopeFactory;
        _client = client;
        _hubContext = hubContext;
        _versionManager = versionManager;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobData = context.MergedJobDataMap;

        var operationId = jobData.GetString("bulkImportOperationId");

        var companyId = jobData.GetString("CompanyId");

        var userId = jobData.GetString("UserId");

        var operationType = jobData.GetString("operationtype");

        _httpContextAccessor.HttpContext = new DefaultHttpContext
        {
            User = GetFakeUser(companyId, userId)
        };

        using var scope = _scopeFactory.CreateScope();


        _loggedInUserService = scope.ServiceProvider.GetRequiredService<ILoggedInUserService>();
       var  bulkImportOperationRepository = scope.ServiceProvider.GetRequiredService<IBulkImportOperationRepository>();
        var bulkImportOperationGroupRepository = scope.ServiceProvider.GetRequiredService<IBulkImportOperationGroupRepository>();
        
        var bulkImportOperation = await bulkImportOperationRepository.GetByReferenceIdAsync(operationId);

        bulkImportOperation.Status = "Running";

        await bulkImportOperationRepository.UpdateAsync(bulkImportOperation);
        try
        {
            if (operationType == "Next")
            {
                await using var innerScope = _scopeFactory.CreateAsyncScope();

                var operationGroupId = jobData.GetString("bulkImportOperationGroupId");

                using var nextInnerScope = _scopeFactory.CreateScope();
                var httpContextAccessor = scope.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
                httpContextAccessor.HttpContext = new DefaultHttpContext
                {
                    User = GetFakeUser(companyId, userId)
                };
                var services = new BulkImportScopedServices
                {
                    ServerRepo = innerScope.ServiceProvider.GetRequiredService<IServerRepository>(),
                    DatabaseRepo = innerScope.ServiceProvider.GetRequiredService<IDatabaseRepository>(),
                    ReplicationRepo = innerScope.ServiceProvider.GetRequiredService<IReplicationRepository>(),
                    SiteRepo = innerScope.ServiceProvider.GetRequiredService<ISiteRepository>(),
                    SiteTypeRepo = innerScope.ServiceProvider.GetRequiredService<ISiteTypeRepository>(),
                    LicenseRepo = innerScope.ServiceProvider.GetRequiredService<ILicenseManagerRepository>(),
                    LoadBalancerRepo = innerScope.ServiceProvider.GetRequiredService<ILoadBalancerRepository>(),
                    WorkflowRepo = innerScope.ServiceProvider.GetRequiredService<IWorkflowRepository>(),
                    WorkflowHistoryRepo = innerScope.ServiceProvider.GetRequiredService<IWorkflowHistoryRepository>(),
                    BulkImportOperationGroupRepo = innerScope.ServiceProvider.GetRequiredService<IBulkImportOperationGroupRepository>(),
                    BulkImportActionResultRepo = innerScope.ServiceProvider.GetRequiredService<IBulkImportActionResultRepository>(),
                    InfraObjectRepository = innerScope.ServiceProvider.GetRequiredService<IInfraObjectRepository>(),
                    Mapper = innerScope.ServiceProvider.GetRequiredService<IMapper>(),
                    Publisher = innerScope.ServiceProvider.GetRequiredService<IPublisher>(),
                    UserService = innerScope.ServiceProvider.GetRequiredService<ILoggedInUserService>()
                };


                await ProcessNextInBulkImportOperationGroupAsync(operationGroupId, CancellationToken.None,services);

            }
            else
            {
                var bulkImportOperationGroup = await bulkImportOperationGroupRepository.GetBulkImportOperationGroupByBulkImportOperationId(operationId);


                await Parallel.ForEachAsync(bulkImportOperationGroup, new ParallelOptions
                {
                    MaxDegreeOfParallelism = Math.Min(8, bulkImportOperationGroup.Count)
                },
                async (operationGroup, token) =>
                {
                    await using var innerScope = _scopeFactory.CreateAsyncScope();

                    var httpContextAccessor = innerScope.ServiceProvider.GetRequiredService<IHttpContextAccessor>();
                    httpContextAccessor.HttpContext = new DefaultHttpContext
                    {
                        User = GetFakeUser(companyId, userId)
                    };
                    try
                    {
           
                        var services = new BulkImportScopedServices
                        {
                            ServerRepo = innerScope.ServiceProvider.GetRequiredService<IServerRepository>(),
                            DatabaseRepo = innerScope.ServiceProvider.GetRequiredService<IDatabaseRepository>(),
                            ReplicationRepo = innerScope.ServiceProvider.GetRequiredService<IReplicationRepository>(),
                            SiteRepo = innerScope.ServiceProvider.GetRequiredService<ISiteRepository>(),
                            SiteTypeRepo = innerScope.ServiceProvider.GetRequiredService<ISiteTypeRepository>(),
                            LicenseRepo = innerScope.ServiceProvider.GetRequiredService<ILicenseManagerRepository>(),
                            LoadBalancerRepo = innerScope.ServiceProvider.GetRequiredService<ILoadBalancerRepository>(),
                            WorkflowRepo = innerScope.ServiceProvider.GetRequiredService<IWorkflowRepository>(),
                            WorkflowHistoryRepo = innerScope.ServiceProvider.GetRequiredService<IWorkflowHistoryRepository>(),
                            InfraObjectRepository = innerScope.ServiceProvider.GetRequiredService<IInfraObjectRepository>(),
                            BulkImportOperationGroupRepo = innerScope.ServiceProvider.GetRequiredService<IBulkImportOperationGroupRepository>(),
                            BulkImportActionResultRepo = innerScope.ServiceProvider.GetRequiredService<IBulkImportActionResultRepository>(),
                            Mapper = innerScope.ServiceProvider.GetRequiredService<IMapper>(),
                            Publisher = innerScope.ServiceProvider.GetRequiredService<IPublisher>(),
                            UserService = innerScope.ServiceProvider.GetRequiredService<ILoggedInUserService>()
                        };


                        operationGroup.Status = "Running";
                        await services.BulkImportOperationGroupRepo.UpdateAsync(operationGroup);

                        var deserializeBulkImport = JsonConvert.DeserializeObject<CreateBulkImportOperationListCommand>(operationGroup.Properties);

                        await ProcessBulkImportGroupAsync(deserializeBulkImport, operationGroup.BulkImportOperationId,
                            operationGroup.ReferenceId, token, services);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error processing BulkImport operationGroup for infraObject: {operationGroup.InfraObjectName}. Error: {ex.Message}");
                    }
                });

                
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "BulkImportJob encountered an error");
        }
        finally
        {
            try
            {
                await context.Scheduler.Interrupt(context.JobDetail.Key);

                if (await context.Scheduler.DeleteJob(context.JobDetail.Key))
                {
                    _logger?.LogDebug("Job with ID: {JobId} has been deleted after execution.", context.JobDetail.Key);
                }
                else
                {
                    _logger?.LogDebug("Job Deleted Failed.");

                }
            }
            catch (Exception ex)
            {
                _logger?.Exception($"Failed to delete job with ID: {context.JobDetail.Key}.", ex);
            }
        }

    }


    public async Task ProcessNextInBulkImportOperationGroupAsync(string operationGroupId, CancellationToken cancellationToken,BulkImportScopedServices services)
    {

        var bulkImportOperationGroup = await services.BulkImportOperationGroupRepo.GetByReferenceIdAsync(operationGroupId);

        Guard.Against.NullOrDeactive(bulkImportOperationGroup, nameof(BulkImportOperationGroup),
            new NotFoundException(nameof(BulkImportOperationGroup), operationGroupId));

        try
        {
            if (bulkImportOperationGroup.ConditionalOperation == 1)
            {
                var bulkImportActionResults =
                    await services.BulkImportActionResultRepo.GetByOperationIdAndOperationGroupId(
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId);

                var conditionalOperation = bulkImportActionResults.Where(x => x.ConditionalOperation == 1).ToList();

                foreach (var operationActionResult in conditionalOperation)
                {
                    operationActionResult.ConditionalOperation = 0;
                    await services.BulkImportActionResultRepo.UpdateAsync(operationActionResult);

                    var name = operationActionResult.EntityName;

                    var nameof = operationActionResult.EntityType.ToLower().Equals("infraobject")
                        ? "infraobject"
                        : $"{operationActionResult.EntityType}List";

                    var deserializeObject =
                        JsonConvert.DeserializeObject<CreateBulkImportOperationListCommand>(bulkImportOperationGroup
                            .Properties);

                    await RemoveListsExceptName(deserializeObject, name, nameof, bulkImportOperationGroup, cancellationToken, services);
                }
            }
           
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error occur on Bulk Import while Process Next operation:{ex}");
        }
       
    }
    public async Task RemoveListsExceptName(CreateBulkImportOperationListCommand command, string nameToFind,
       string listType, BulkImportOperationGroup bulkImportOperationGroup, CancellationToken cancellationToken, BulkImportScopedServices services)
    {
        switch (listType.ToLower())
        {
            case "serverlist":
                if (command.ServerList.Any(item =>
                        item.Name.Equals(nameToFind, StringComparison.CurrentCultureIgnoreCase)))
                {
                    var firstMatchIndex = command.ServerList.FindIndex(item =>
                        item.Name.Equals(nameToFind, StringComparison.OrdinalIgnoreCase));

                    if (firstMatchIndex != -1) command.ServerList.RemoveRange(0, firstMatchIndex + 1);

                    if (command.ServerList.Any())
                        await CreateServer(command.ServerList,
                            bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                           services, cancellationToken);

                    await CreateDatabase(command.DatabaseList,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                        services, cancellationToken);
                    await CreateReplication(command.ReplicationList,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                       services, cancellationToken);
                    await CreateInfraObject(command,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId, services,cancellationToken);
                }

                break;
            case "databaselist":
                if (command.DatabaseList.Any(item => item.Name == nameToFind))
                {
                    var firstMatchIndex = command.DatabaseList.FindIndex(item =>
                        item.Name.Equals(nameToFind, StringComparison.OrdinalIgnoreCase));

                    if (firstMatchIndex != -1) command.DatabaseList.RemoveRange(0, firstMatchIndex + 1);

                    if (command.DatabaseList.Any())
                        await CreateDatabase(command.DatabaseList,
                            bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                            services, cancellationToken);

                    await CreateReplication(command.ReplicationList,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                        services, cancellationToken);

                    await CreateInfraObject(command,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                        services, cancellationToken);
                }

                break;
            case "replicationlist":
                if (command.ReplicationList.Any(item => item.Name == nameToFind))
                {
                    var firstMatchIndex = command.ReplicationList.FindIndex(item =>
                        item.Name.Equals(nameToFind, StringComparison.OrdinalIgnoreCase));

                    if (firstMatchIndex != -1) command.ReplicationList.RemoveRange(0, firstMatchIndex + 1);

                    if (command.ReplicationList.Any())
                        await CreateReplication(command.ReplicationList,
                            bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId,
                            services, cancellationToken);

                    await CreateInfraObject(command,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId, services, cancellationToken);
                }

                break;
            case "infraobject":

                if (command.InfraObject.Name == nameToFind)
                    await CreateWorkflow(command,
                        bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId, services, cancellationToken);
                break;
            default:
                await CreateWorkflow(command,
                    bulkImportOperationGroup.BulkImportOperationId, bulkImportOperationGroup.ReferenceId, services, cancellationToken); 
                break;
        }
    }

    public async Task ProcessBulkImportGroupAsync(CreateBulkImportOperationListCommand importData, string operationId, string operationGroupId,CancellationToken cancellationToken, BulkImportScopedServices services)
    {
        if (importData.ServerList?.Any() == true)
        {
            await CreateServer(
                importData.ServerList,
                operationId,
                operationGroupId, services,
                cancellationToken);
        }

        if (importData.DatabaseList?.Any() == true)
        {
            await CreateDatabase(
            importData.DatabaseList,
            operationId,
            operationGroupId, services, cancellationToken);
        }

        if (importData.ReplicationList?.Any() == true)
        {
            await CreateReplication(
                importData.ReplicationList,
                operationId,
                operationGroupId, services,
                cancellationToken);
        }

        if (importData.InfraObject != null)
        {
            await CreateInfraObject(
                importData,
                operationId,
                operationGroupId, services, cancellationToken);
        }

        _logger.LogInformation("BulkImport - Create - Bulk Import created successfully.");

    }


    public async Task CreateServer(List<CreateBulkDataServerListCommand> servers, string operationId,
       string operationGroupId, BulkImportScopedServices services, CancellationToken cancellationToken)
    {
        foreach (var server in servers)
        {
            string serverId = "";
            try
            {
                var startDate = DateTime.Now;
                var serverName = await services.ServerRepo.GetServerByServerName(server.Name);
                serverId = serverName?.ReferenceId ?? "";
                var serverDto = services.Mapper.Map<Server>(server);

                var serverCommandHandler =
                    new CreateServerCommandHandler(services.Mapper, services.ServerRepo, services.Publisher, services.UserService,
                        services.LicenseRepo, services.SiteRepo, services.SiteTypeRepo);

                var mapServerCommand = services.Mapper.Map<CreateServerCommand>(server);

                var updateBaseResponse =
                    await serverCommandHandler.Handle(mapServerCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ServerId, serverDto.Name, "Server", "Success",
                        startDate, updateBaseResponse.Message, operationId, operationGroupId,services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult(serverId, server.Name, "Server", "Error",
                        DateTime.Now, exc.GetMessage(), operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
                var ex = new Exception("Server import failed.", exc);
                throw ex;
            }
        
        }
    }


    public async Task CreateDatabase(List<CreateBulkDataDataBaseListCommand> databases, string operationId,
      string operationGroupId, BulkImportScopedServices services, CancellationToken cancellationToken)
    {
        foreach (var dataBaseItem in databases)
        {
            try {
                //await Task.Delay(2000, cancellationToken);

                var startDate = DateTime.Now;


                var dataBase = services.Mapper.Map<Database>(dataBaseItem);

                var server = await services.ServerRepo.GetServerByServerName(dataBaseItem.ServerName);

                if (server.IsNull())
                    await CreateBulkImportActionResult("", dataBaseItem.Name, "Database", "Error", startDate,
                        $"Server '{dataBaseItem.ServerName}' not configured.", operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                dataBaseItem.ServerId = server?.ReferenceId ?? string.Empty;

                var databaseCommandHandler =
                    new CreateDatabaseCommandHandler(services.Mapper, services.DatabaseRepo, services.Publisher, _loggedInUserService,
                        services.LicenseRepo, services.ServerRepo, services.SiteRepo, services.SiteTypeRepo);

                var mapDatabaseCommand = services.Mapper.Map<CreateDatabaseCommand>(dataBaseItem);

                var updateBaseResponse =
                    await databaseCommandHandler.Handle(mapDatabaseCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.DatabaseId, dataBase?.Name, "Database",

                        "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId, services.BulkImportActionResultRepo , services.BulkImportOperationGroupRepo);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("", dataBaseItem.Name, "Database", "Error",
                        DateTime.Now, exc.GetMessage(), operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                var ex = new Exception("Database import failed.", exc);
                throw ex;

            }
        }
    }

    public async Task CreateReplication(List<CreateBulkDataReplicationListCommand> replications, string operationId,
        string operationGroupId, BulkImportScopedServices services,
         CancellationToken cancellationToken) 
    {
        foreach (var replicationItem in replications)
        {
            try
            {
                //await Task.Delay(2000, cancellationToken);

                var startDate = DateTime.Now;

                var replication = services.Mapper.Map<Replication>(replicationItem);

                var replicationCommandHandler = new CreateReplicationCommandHandler(services.Mapper, services.ReplicationRepo,
                    services.Publisher, _loggedInUserService, services.SiteRepo , services.SiteTypeRepo);

                var mapReplicationCommand = services.Mapper.Map<CreateReplicationCommand>(replicationItem);

                var updateBaseResponse =
                    await replicationCommandHandler.Handle(mapReplicationCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ReplicationId, replication?.Name,
                        "Replication", "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("", replicationItem.Name, "Replication", "Error",
                        DateTime.Now, exc.GetMessage(), operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
                var ex = new Exception("Replication import failed.", exc);
                throw ex;
            }
        }



    }

    public async Task CreateInfraObject(CreateBulkImportOperationListCommand bulkImport, string operationId,
      string operationGroupId, BulkImportScopedServices services, CancellationToken cancellationToken)
    {
        try
        {

            var startDate = DateTime.Now;

            foreach (var server in bulkImport.ServerList)
            {
                var serverName = await services.ServerRepo.GetServerByServerName(server.Name);

                if (serverName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the server '{server.Name}' not configured.",
                        operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                bulkImport.InfraObject.ServerProperties = bulkImport.InfraObject.ServerProperties
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);


                bulkImport.InfraObject.PRServerId = server.ServerType.ToLower().Contains("pr")
                    ? serverName.ReferenceId
                    : bulkImport.InfraObject.PRServerId;

                bulkImport.InfraObject.DRServerId = server.ServerType.ToLower().Contains("dr")
                    ? serverName.ReferenceId
                    : bulkImport.InfraObject.DRServerId;
            }

            foreach (var database in bulkImport.DatabaseList)
            {
                var databaseNames = await services.DatabaseRepo.GetDatabaseNames();

                var databaseName = databaseNames.FirstOrDefault(x => x.Name == database.Name);

                if (databaseName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the database '{database.Name}' not configured.",
                        operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                bulkImport.InfraObject.DatabaseProperties = bulkImport.InfraObject.DatabaseProperties
                    .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{databaseName.Name}", databaseName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{databaseName.Name}", databaseName.ReferenceId);


                bulkImport.InfraObject.PRDatabaseId = database.Type.ToLower().Contains("pr")
                    ? databaseName?.ReferenceId
                    : bulkImport.InfraObject.PRDatabaseId;

                bulkImport.InfraObject.DRDatabaseId = database.Type.ToLower().Contains("dr")
                    ? databaseName?.ReferenceId
                    : bulkImport.InfraObject.DRDatabaseId;
            }


            foreach (var replication in bulkImport.ReplicationList)
            {
                var replicationNames = await services.ReplicationRepo.GetReplicationNames();

                var replicationName = replicationNames.FirstOrDefault(x => x.Name == replication.Name);

                if (replicationName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the replication '{replication.Name}' not configured.",
                        operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                bulkImport.InfraObject.ReplicationProperties = bulkImport.InfraObject.ReplicationProperties
                    .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{replicationName.Name}", replicationName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{replicationName.Name}", replicationName.ReferenceId);


                bulkImport.InfraObject.PRReplicationId =
                    replicationName.Name.Equals(bulkImport.InfraObject.PRReplicationName)
                        ? replicationName?.ReferenceId
                        : bulkImport.InfraObject.PRReplicationId;

                bulkImport.InfraObject.DRReplicationId =
                    replicationName.Name.Equals(bulkImport.InfraObject.DRReplicationName)
                        ? replicationName?.ReferenceId
                        : bulkImport.InfraObject.DRReplicationId;
            }

            var infraObject = services.Mapper.Map<InfraObject>(bulkImport.InfraObject);

            var infraObjectCommandHandler = new CreateInfraObjectCommandHandler(services.Mapper, services.InfraObjectRepository,
                services.Publisher, _loggedInUserService, services.LoadBalancerRepo, _client);

            var mapInfraObjectCommand = services.Mapper.Map<CreateInfraObjectCommand>(bulkImport.InfraObject);

            var updateBaseResponse = await infraObjectCommandHandler.Handle(mapInfraObjectCommand, cancellationToken);

            if (updateBaseResponse.Success)
                await CreateBulkImportActionResult(updateBaseResponse.InfraObjectId, infraObject?.Name, "InfraObject",
                    "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

            if (bulkImport.IsFailBack || bulkImport.IsFailOver || bulkImport.IsSwitchBack || bulkImport.IsSwitchOver)
            {
               // await Task.Delay(2000, cancellationToken);

                await CreateWorkflow(bulkImport, operationId, operationGroupId, services, cancellationToken);
            }

        }
        catch (Exception exc)
        {
            if (!string.IsNullOrWhiteSpace(exc.Message))
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error",
                    DateTime.Now, exc.GetMessage(), operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
            var ex = new Exception("InfraObject import failed.", exc);
            throw ex;
        }
        
    }

    public async Task CreateWorkflow(CreateBulkImportOperationListCommand bulkImport, string operationId,
       string operationGroupId, BulkImportScopedServices services, CancellationToken cancellationToken)
    {
        var workflowCommandHandler = new CreateWorkflowCommandHandler(services.Mapper, services.WorkflowRepo,
               services.WorkflowHistoryRepo, _loggedInUserService, services.Publisher, _versionManager, _hubContext);

        var workflowActions = new List<(bool IsActive, string Prefix, string Properties)>
        {
            (bulkImport.IsSwitchOver, "SO", bulkImport.SwitchOverTemplate),
            (bulkImport.IsSwitchBack, "SB", bulkImport.SwitchBackTemplate),
            (bulkImport.IsFailOver, "FO", bulkImport.FailOverTemplate),
            (bulkImport.IsFailBack, "FB", bulkImport.FailBackTemplate)
        };

        var filterWorkflows = new List<(bool IsActive, string Prefix, string Properties)>();

        var activeWorkflowActions = workflowActions
            .Where(action => action.IsActive)
            .ToList();

        foreach (var removeAction in activeWorkflowActions)
        {
            var actionResultWorkflowRunningList =
                await services.BulkImportActionResultRepo.GetActionByOperationGroupIdAndEntityType(operationGroupId,
                    $"{removeAction.Prefix}Workflow");

            if (actionResultWorkflowRunningList is null) filterWorkflows.Add(removeAction);
        }

        foreach (var action in filterWorkflows)
        {
            var name = "";
            try
            {

                var infraObject = await services.InfraObjectRepository.GetInfraObjectByName(bulkImport.InfraObject.Name);

                var actionType = $"{action.Prefix}Workflow";

                if (infraObject.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, $"{action.Prefix}Workflow", "Error",
                        DateTime.Now,
                        $"The {action.Prefix} workflow was not created because the infraObject '{bulkImport.InfraObject.Name}' is not configured."
                        , operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);

                var randomString =
                    string.Concat(Enumerable.Range(0, 6).Select(_ => Chars[new Random().Next(Chars.Length)]));

                name = $"{bulkImport.InfraObject.ReplicationTypeName}_{action.Prefix}_{randomString}";

                var response = await workflowCommandHandler.Handle(new CreateWorkflowCommand
                {
                    Name = name,
                    Properties = action.Properties,
                    CompanyId = bulkImport.InfraObject.CompanyId,
                    IsPublish = true
                }, CancellationToken.None);

                if (response.Success)
                    await CreateBulkImportActionResult(response.WorkflowId, name, actionType, "Success", DateTime.Now,
                        response.Message, operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("",name, "Workflow", "Error",
                            DateTime.Now, exc.GetMessage(), operationId, operationGroupId, services.BulkImportActionResultRepo, services.BulkImportOperationGroupRepo);
            }
        }
    }
        


    private async Task CreateBulkImportActionResult(
    string entityId,
    string entityName,
    string entityType,
    string status,
    DateTime startTime,
    string error,
    string operationId ,
    string operationGroupId,
    IBulkImportActionResultRepository bulkImportActionResultRepository,
    IBulkImportOperationGroupRepository bulkImportOperationGroupRepository)
    {

        var group = await bulkImportOperationGroupRepository.GetByReferenceIdAsync(operationGroupId);
        var endTime = DateTime.Now;
        var actionResult = entityId.IsNotNullOrEmpty()
            ? await bulkImportActionResultRepository.GetByEntityIdAndBulkImportOperationId(entityId, operationId)
            : null;

        var isError = status.Equals("error", StringComparison.OrdinalIgnoreCase);

        if (actionResult != null)
        {
            actionResult.EntityId = entityId;
            actionResult.EntityName = entityName;
            actionResult.EntityType = entityType;
            actionResult.NodeId = group.NodeId;
            actionResult.Status = status;
            actionResult.StartTime = startTime;
            actionResult.EndTime = endTime;
            actionResult.ErrorMessage = error;

            await bulkImportActionResultRepository.UpdateAsync(actionResult);
        }
        else
        {
           
            await bulkImportActionResultRepository.AddAsync(new BulkImportActionResult
            {
                BulkImportOperationId = operationId,
                BulkImportOperationGroupId = operationGroupId,
                EntityId = entityId,
                EntityName = entityName,
                EntityType = entityType,
                NodeId = group.NodeId,
                Status = status,
                StartTime = startTime,
                EndTime = endTime,
                ErrorMessage = error
            });
               
        }

        var resultList = await bulkImportActionResultRepository.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        
            var split = group.ProgressStatus?.Split('/') ?? Array.Empty<string>();
            var updatedProgress = split.Length > 1 ? $"{resultList.Count}/{split[1]}" : group.ProgressStatus;

            group.ProgressStatus = isError ? group.ProgressStatus : updatedProgress;
            group.Status = resultList.Count==Convert.ToInt32(split[1])?"Completed": status;
            group.ErrorMessage = error;

        await _hubContext.Clients.All.SendAsync("notification", new
        {
            Group = "BulkImport",
            Status = group.Status ,
            Message = new
            {
                operationId = operationId,
                operationGroupId = operationGroupId,
                Message = error,
                progrssBar = group.ProgressStatus,
                status = group.Status,
                StartTime= startTime,
                EntityType = entityType
            }
        });
        await bulkImportOperationGroupRepository.UpdateAsync(group);
        

        if (isError)
        {
            _logger.LogError($"EntityName '{entityName}' - {error}");
            throw new InvalidException("");
        }

        _logger.LogInformation($"EntityName '{entityName}' - {error}");
    }



    private static ClaimsPrincipal GetFakeUser(string companyId,string userId) => new(new ClaimsIdentity(new[]
    {
        new Claim("uid", userId),
        new Claim("companyId", companyId),
        new Claim("companyName", "System"),
        new Claim("isParent", "true"),
        new Claim("isAllInfra", "true"),
        new Claim(ClaimTypes.Role, "SuperAdmin"),
        new Claim(ClaimTypes.Name, "BulkImport")
    }, "FakeAuth"));


    public class BulkImportScopedServices
    {
        public IServerRepository ServerRepo { get; init; }
        public IDatabaseRepository DatabaseRepo { get; init; }
        public IReplicationRepository ReplicationRepo { get; init; }
        public ISiteRepository SiteRepo { get; init; }
        public ISiteTypeRepository SiteTypeRepo { get; init; }
        public ILicenseManagerRepository LicenseRepo { get; init; }
        public ILoadBalancerRepository LoadBalancerRepo { get; init; }
        public IWorkflowRepository WorkflowRepo { get; init; }
      
        public IInfraObjectRepository InfraObjectRepository { get; init; }
        public IWorkflowHistoryRepository WorkflowHistoryRepo { get; init; }
        public IBulkImportOperationGroupRepository BulkImportOperationGroupRepo { get; init; }
        public IBulkImportActionResultRepository BulkImportActionResultRepo { get; init; }
        public IMapper Mapper { get; init; }
        public IPublisher Publisher { get; init; }
        public ILoggedInUserService UserService { get; init; }
    }


}