﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixRepository : BaseRepository<ApprovalMatrix>, IApprovalMatrixRepository
{
    private readonly ApplicationDbContext _dbContext;

    public ApprovalMatrixRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService) : base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
    }

    public async Task<bool> IsApprovalMatrixNameUnique(string name)
    {
        return await _dbContext.ApprovalMatrix.AnyAsync(e => e.Name.Equals(name));
    }

    public async Task<bool> IsApprovalMatrixNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.ApprovalMatrix.AnyAsync(e => e.Name == name);
        }
        return await _dbContext.ApprovalMatrix.AnyAsync(e => e.Name == name && e.ReferenceId != id);
    }

    public async Task<ApprovalMatrix> GetApprovalMatrixByBusinessFunctionId(string businessFunctionId)
    {
        return await _dbContext.ApprovalMatrix
            .AsNoTracking()
            .Active()
            .FirstOrDefaultAsync(x => x.BusinessFunctionId == businessFunctionId);
    }

    public override async Task<PaginatedResult<ApprovalMatrix>> PaginatedListAllAsync(int pageNumber, int pageSize,Specification<ApprovalMatrix> specification,string sortColumn,string sortOrder) 
    {
        return await Entities
            .AsNoTracking()
            .Active()
            .Specify(specification)
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}