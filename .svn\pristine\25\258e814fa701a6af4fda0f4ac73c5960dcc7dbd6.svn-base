
function toggleWizard(showFirst) {
    $('.firstWizard').toggleClass("d-none", !showFirst);
    $('.secondWizard').toggleClass("d-none", showFirst);
    $('#nextButton').toggleClass("d-none", !showFirst);
    $('#previousButton').toggleClass("d-none", showFirst);
    $('#saveProcessName').toggleClass("d-none", showFirst);
}

function removeObjectByPropsId(obj, targetPropsId) {

    if (!obj || typeof obj !== 'object') return obj;

    if (Array.isArray(obj.properties)) {
        obj.properties = obj.properties
            .map(child => removeObjectByPropsId(child, targetPropsId))
            .filter(child => child !== null);
    }

    if (obj.propsId === targetPropsId) {
        if (obj.properties && obj.properties.length > 0) {
            return obj.properties[0];
        }
        return null;
    }
    return obj;
}

function GetBusinessServiceList(UserApproval = null) {
    $.ajax({
        type: "GET",
        url: RootUrl + "Manage/Approval/GetBusinessServiceList",
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result) {

                if (result && result.length > 0) {
                    $('#selectOperationalService').append('<option value=""></option>');
                    result.forEach(item => {
                        $('#selectOperationalService').append('<option value="' + item.id + '">' + item.name + '</option>');
                    });
                }
            } else {
                errorNotification(result);
            }
        }
    });

    //if (UserApproval && Object.keys(UserApproval)) {
    //    $('#selectOperationalService').val(UserApproval.businessServiceProperties).trigger('change');
    //}
}

function replaceObjectIfEmptyName(obj, newObject) {
    let newObj = JSON.parse(JSON.stringify(obj)); 

    if (Array.isArray(newObj.properties)) {
        newObj.properties = newObj.properties.map(property => {
            if (property.name === "") {
                return newObject; 
            } else {
                return replaceObjectIfEmptyName(property, newObject);
            }
        });
    }
    return newObj;
}

function clearFieldsAfterSace() {
    $("#approvalProcessName, #approvalTemplateName").val("");
    $("#approvalProcessNameError, #approvalTemplateNameError")
        .text("")
        .removeClass("field-validation-error");
    $("#CreateModal, #saveprocessModal").modal("hide");
}

function updateByPropsId(obj, propsID, newData) {

    if (typeof obj !== 'object' || obj === null) return obj;

    if (obj.propsId === propsID) {
        return { ...obj, ...newData };
    }

    if (Array.isArray(obj)) {
        return obj.map(item => updateByPropsId(item, propsID, newData));
    }
    let updatedObj = { ...obj };

    for (let key in updatedObj) {
        updatedObj[key] = updateByPropsId(updatedObj[key], propsID, newData);
    }
    return updatedObj;
}

function addProcessNode(newName, textDescription, selectedUsers, enterDuration, selectDuration, approvalObject,
    rejectObject, notification, operationalService, operationalServiceName, tree = processNameArray) {

    if (!tree.properties) {
        tree.properties = [];
    }

    if (tree.name === "") {
        tree.name = newName;
        tree.propsId = getRandomId('propsId');
        tree.userLists = selectedUsers;
        tree.description = textDescription;
        tree.SLA = { duration: enterDuration, period: selectDuration };
        tree.ruleSet = [approvalObject, rejectObject];
        tree.notification = [notification];
        tree.businessServiceId = operationalService;
        tree.businessServiceName = operationalServiceName;
        tree.properties.push({ name: "", properties: [], userLists: [], ruleSet: [], SLA: {}, notification: [] });
        return true;
    }

    if (Array.isArray(tree.properties)) {
        for (let childNode of tree.properties) {
            if (addProcessNode(newName, textDescription, selectedUsers, enterDuration, selectDuration, approvalObject,
                rejectObject, notification, operationalService, operationalServiceName, childNode)) {
                return true;
            }
        }
    }
    return false;
}

async function IsNameExist(url, data, errorFunc) {
    return !data.name?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function templateNameValidation(value, id = null, nameExistURL, errorelement, nullerror) {

    if (!value) {
        errorelement.text(nullerror).addClass('field-validation-error');
        return false;
    }

    if (value.includes('<')) {
        errorelement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = { name: value, id: id };
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorelement, validationResults);
};

async function createEditTemplate(data, textDescription, selectedUsers, enterDuration, selectDuration,
    approvalObject, rejectObject, notification, operationalService, operationalServiceName) {

    if ($(".endButton").length) {
        $("#endsvg").remove();
        $(".endButton").remove();
    }
    let processName = data?.name ? data.name : data;
    let processBoxId = getRandomId('processBox');
    $("#closeOffcanvas").trigger("click");
    let container = $("#approvalContainer");
    container.scrollTop(0);

    if (startButtonValue === 1) {
        let startSVGId = getRandomId('startsvg');
        let startEndArrowId = getRandomId('arrow');
        let startPolylineId = getRandomId('polyline');
        let startSVG = `
            <div id="startTemplate"
                 style="margin-bottom:2.5rem;
                 border-radius:5px;                
                 font-weight:bold;
                 position:relative;">
                   <img title="Start" src="/img/input_Icons/Start.svg" width="30" height="30" draggable="false" loading="lazy" alt="Start">
            </div>`
        container.append(startSVG);

        let startBox = $("#startTemplate");
        //let relativePosition = startBox.offset();        
        let boxWidth = startBox.outerWidth();
        let boxHeight = startBox.outerHeight();
        let length = boxHeight + 2.5 * 16; //2.5 marginBottom of id="startTemplate"
        let relativePosition2 = startBox.position(); //parent relative

        //let parentleft = relativePosition.left - relativePosition2.left;
        //let chiledLeft = (relativePosition.left - parentleft) + (boxWidth / 2);

        let centerPoint = relativePosition2.left + (boxWidth / 2);
        let points = [[centerPoint, boxWidth], [centerPoint, length]];
        let svgLine = `
                    <svg id="${startSVGId}" 
                            style="overflow:visible; position:absolute; top:0; left:0;">
                        <defs>
                            <marker id="${startEndArrowId}" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                                <path d="M0,0 L10,5 L0,10 Z" fill="black" />
                            </marker>
                        </defs>                                                           
                        <polyline class="polyline${startPolylineId}" 
                            points="${points}" 
                            fill="none" stroke="green" stroke-width="1" 
                            stroke-dasharray="4,4" 
                            marker-end="url(#${startEndArrowId})"/>                        
                    </svg>`;
        container.append(svgLine);
        startButtonValue = 2;
    } else {
        let processLine = getRandomId("process-line");
        let processSvg = getRandomId("process-svg");
        let lastDivId = $("#approvalContainer div:last"); //.attr("id");       
        let lastDIVPosition = lastDivId.position();
        let lastDIVPosition2 = lastDivId.offset();

        let top = lastDIVPosition2.top - lastDIVPosition.top;
        let childTop = lastDIVPosition2.top - top;

        let lastDIVWidth = lastDivId.outerWidth();
        let lastDIVHeight = lastDivId.outerHeight();
        let heightOffset = 5;
        let fromCenter = {
            x: lastDIVPosition?.left, // + fromNameWidth / 2,
            y: childTop + lastDIVHeight + heightOffset //+ lastDIVHeight / 2 + heightOffset
        };      
        let polylinePoints = [[lastDIVWidth / 2, 0], [lastDIVWidth / 2, lastDIVHeight - 10]]; //margin bottom 40px for every level.

        container.append(`
                <svg id="${processSvg}" style="position: absolute; top: ${fromCenter.y}; left: ${fromCenter.x}; height: ${lastDIVHeight - 10}px; width: ${lastDIVWidth}px">
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="black"/>
                        </marker>
                    </defs>
                    <polyline id="${processLine}"
                        fill="none"
                        stroke="green"
                        stroke-width="1"
                        stroke-dasharray="4, 4"
                        marker-end="url(#arrow)" />
                </svg>`);

        $(`#${processLine}`).attr("points", polylinePoints);
    }

    let processBox = `
            <div id="${processBoxId}" 
                role="button"
                class="process-box" 
                title='${processName}'
                data-template ='${JSON.stringify(data)}'
                style="display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom:2.5rem;
                min-height: 40px;
                width:200px; 
                height:40px; 
                background-color:#dbf5ff; 
                border:1px solid #87afc6; 
                border-radius:8px; 
                cursor: pointer;
                font-weight:bold;">
                <span style="
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: block;
                    max-width: 80%;">
                    <i class="cp-process me-2"></i>
                    ${processName}
                </span>
            </div>`;

    container.append(processBox);
    const processBoxes = document.querySelectorAll('.process-box');
    const uniqueTitles = new Set();
    processBoxes.forEach(div => {
        uniqueTitles.add(div.getAttribute('title'));
    });
    titlesArray = Array.from(uniqueTitles);    
    container.scrollTop(container[0].scrollHeight);

    if ($("#saveProcessName").text() === addProcessName) {
        addProcessNode(processName, textDescription, selectedUsers, enterDuration, selectDuration,
            approvalObject, rejectObject, notification, operationalService, operationalServiceName);
    }  
}

$(document).on("click", '.edit-button', function () {
    $("#addTransition").attr("disabled", false);
    editWhileCreate = false;
    let value = $(this).attr('data-template');
    let parsedValue = JSON.parse(value);
    processNameArray = { propsId: "", name: "", businessServiceId: "", businessServiceName: "", straightLineData: "", properties: [], userLists: [], ruleSet: [], SLA: {}, notification: [] };

    if (parsedValue) {
        $("#saveProcessName").text("Update");
        $('#templateID').val(parsedValue.id);
        $("#approvalContainer").empty();
        $("#saveApprovalTemplate").css("visibility", "visible");
        $('#CreateModal').modal('show');
        $('#approvalTemplateName').val(parsedValue?.name);
        approvalTemplateName = parsedValue?.name;
        startButtonValue = 1;
        let props = JSON.parse(parsedValue.properties);
        editedData = props;
        propertiesID = "";
        editedProcessName = "";
        addTransition = false;
        setTimeout(() => {
            recursiveEdit(props);
        }, 500);
    }
});

function recursiveEdit(data, editWhileCreate = null) {

    if (data.name) {
        createEditTemplate(data);

        if (data.properties && Array.isArray(data.properties)) {
            data.properties.forEach(function (child) {
                recursiveEdit(child, editWhileCreate);
            });
        }
    } else if (!editWhileCreate) {
        $('#endTemplate').trigger("click");
        $("#saveApprovalTemplate").text("Update");
    }
}

function findObjectsByName(obj, targetName) {
    let results = [];

    if (obj.name === targetName) {
        results.push(obj);
    }

    if (Array.isArray(obj.properties)) {
        obj.properties.forEach(property => {
            results = results.concat(findObjectsByName(property, targetName));
        });
    }
    return results;
}

$(document).on('click', '.process-box', function () {
    toggleWizard(true);
    $("#approvalProcessNameError, #enterDurationError, #ApTwoError, #RjTwoError").text("").removeClass("field-validation-error");
    let $this = $(this);
    let data = JSON.parse($this.attr("data-template"));
    $("#saveProcessName").text("Update");

    if (typeof data === "string") {
        const results = findObjectsByName(processNameArray, data);
        data = results[0];
        editWhileCreate = true;
    }
    const idArray = [];
    data?.userLists?.forEach(item => idArray.push(item.id));
    propertiesID = data?.propsId;
    editedProcessName = data?.name;
    $("#offcanvasExample").offcanvas("show");
    $("#processDelete").removeClass("d-none");
    $("#approvalProcessName").val(data?.name);
    $("#userNameList").val(idArray).trigger("change");
    $("#textDescription").val(data?.description);
    $("#inputDuration").val(data?.SLA?.duration);
    $("#selectDuration").val(data?.SLA?.period).trigger("change");
    $("#ApOne").val(data?.ruleSet[0]?.type).trigger("change");
    $("#ApTwo").val(data?.ruleSet[0]?.ruleCount);
    $("#RjOne").val(data?.ruleSet[1]?.type).trigger("change");
    $("#RjTwo").val(data?.ruleSet[1]?.ruleCount);
    $("#selectOperationalService").val(data?.businessServiceId).trigger("change");
});

$(document).on("click", '.delete-button', function () {
    let templateID = $(this).attr('data-template-id');
    let templateName = $(this).attr('data-template-name');
    $('#deleteData').attr('title', templateName).text(templateName);
    $('#textDeleteId').val(templateID);
})

function commonInputValidation(value, errorelement, errormessage, status = null) {

    if (!value) {
        errorelement.text(errormessage).addClass('field-validation-error');
        return false;
    } else {
        if (status === "approver") {
            if (value === "All") {
                $("#ApTwo").val($("#ApThree").text());
                $("#ApTwo").prop("disabled", true);
            } else {
                $("#ApTwo").prop("disabled", false);
            }
        }

        if (status === "reject") {
            if (value === "All") {
                $("#RjTwo").val($("#RjThree").text());
                $("#RjTwo").prop("disabled", true);
            } else {
                $("#RjTwo").prop("disabled", false);
            }
        }       
        errorelement.text("").removeClass('field-validation-error');
        return true;
    }
}

//function addEditProcessLine(fromName, toName) {      //transitionName, transitionColour
//    let processLine = getRandomId("process-line");
//    let processSvg = getRandomId("process-svg");
//    let container = $("#approvalContainer");
//    let fromElement = $(`div[title='${fromName}']`);
//    let toElement = $(`div[title='${toName}']`);

//    let fromNamePosition = fromElement.position();
//    let fromNameWidth = fromElement.outerWidth();
//    let fromNameHeight = fromElement.outerHeight();

//    let toNamePosition = toElement.position();
//    let toNameWidth = toElement.outerWidth();
//    let toNameHeight = toElement.outerHeight();

//    // will be use
//    //let fromRightCenter = {
//    //    x: fromNamePosition.left + fromNameWidth,
//    //    y: fromNamePosition.top + fromNameHeight / 2
//    //};

//    //let toLeftCenter = {
//    //    x: toNamePosition.left + toNameWidth,
//    //    y: toNamePosition.top + toNameHeight / 2
//    //};

//    //let rightOffsetX = fromRightCenter.x + 200;

//    //let polylinePoints = `
//    //                        ${fromRightCenter.x},${fromRightCenter.y}
//    //                        ${rightOffsetX},${fromRightCenter.y}
//    //                        ${rightOffsetX},${toLeftCenter.y}
//    //                        ${toLeftCenter.x},${toLeftCenter.y}
//    //                    `.trim();

//    let heightOffset = 26;

//    let fromCenter = {
//        x: fromNamePosition?.left, // + fromNameWidth / 2,
//        y: fromNamePosition?.top + fromNameHeight / 2 + heightOffset
//    };

//    let toCenter = {
//        x: toNamePosition?.left + toNameWidth / 2,
//        y: toNamePosition?.top + toNameHeight / 2 - heightOffset
//    };

//    //let polylinePoints = `${fromCenter.x},${fromCenter.y} ${toCenter.x},${toCenter.y}`;
//    let polylinePoints = [[fromNameWidth / 2, 0], [fromNameWidth / 2, fromNameHeight - 10]]; //margin bottom 40px for every level.

//    //Note: if give height and width 100% for svg means level mouse click is not working.

//    //let svg = $(`#${processSvg}`);

//    //if (svg.length === 0) {
//    container.append(`
//        <svg id="${processSvg}" style="position: absolute; top: ${fromCenter.y}; left: ${fromCenter.x}; height: ${fromNameHeight - 10}px; width: ${fromNameWidth}px">
//            <defs>
//                <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
//                    <path d="M0,0 L10,5 L0,10 Z" fill="black"/>
//                </marker>
//            </defs>
//            <polyline id="${processLine}"
//                      fill="none"
//                      stroke="green"
//                      stroke-width="1"
//                      stroke-dasharray="4, 4"
//                      marker-end="url(#arrow)" />
//        </svg>
//    `);
//    //}

//    $(`#${processLine}`).attr("points", polylinePoints);

//    $("#addTransitionModal").modal("hide");
//}

//function addProcessLine(fromName, toName) {  //, transitionName, transitionColour
//    straightLineData.push({ lineId: getRandomId('propsId'), fromName: fromName, toName: toName }); //, transitionName: transitionName, transitionColour: transitionColour
//    //if (editedstraightLine.length) {
//    //    editedstraightLine.push({ lineId: getRandomId('propsId'), fromName: fromName, toName: toName }); //, transitionName: transitionName, transitionColour: transitionColour
//    //}
//    //addEditProcessLine(fromName, toName); //, transitionName, transitionColour
//}
