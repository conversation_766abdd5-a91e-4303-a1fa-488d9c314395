﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Create;

public class WorkflowCreatedEventHandler : INotificationHandler<WorkflowCreatedEvent>
{
    private readonly ILogger<WorkflowCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowCreatedEventHandler(ILoggedInUserService userService, ILogger<WorkflowCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{createdEvent.WorkflowName}' Created successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress ?? "::1",
            Action = $"{ActivityType.Create} {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Workflow '{createdEvent.WorkflowName}' Created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}