using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ApprovalMatrixFixture
{
    public List<ApprovalMatrixListVm> ApprovalMatrixListVm { get; }
    public ApprovalMatrixDetailVm ApprovalMatrixDetailVm { get; }
    public CreateApprovalMatrixCommand CreateApprovalMatrixCommand { get; }
    public UpdateApprovalMatrixCommand UpdateApprovalMatrixCommand { get; }

    public ApprovalMatrixFixture()
    {
        var fixture = new Fixture();

        // Create sample ApprovalMatrix list data
        ApprovalMatrixListVm = new List<ApprovalMatrixListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "john.doe",
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "Critical Business Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Financial Operations",
                Name = "Financial Approval Matrix",
                Description = "Approval matrix for financial transactions and budget approvals",
                Time = DateTime.Now.AddDays(-30),
                ActionType = "Approval",
                Properties = "{\"threshold\":10000,\"currency\":\"USD\",\"department\":\"Finance\"}",
                Emails = "<EMAIL>,<EMAIL>",
                Rule = "Amount > 10000 requires CFO approval",
                WorkflowModification = true,
                WorkflowProfileExecution = false,
                ProfileModification = true,
                TemplateName = "Financial Approval Template",
                CreatedBy = "system.admin",
                CreatedDate = DateTime.Now.AddDays(-30),
                status = "Active",
                Approvers = "finance.manager,cfo",
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now.AddDays(335),
                ApprovedBy = "board.director"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "jane.smith",
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "IT Security Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Security Operations",
                Name = "Security Approval Matrix",
                Description = "Approval matrix for security-related changes and access requests",
                Time = DateTime.Now.AddDays(-15),
                ActionType = "Security Review",
                Properties = "{\"clearanceLevel\":\"Secret\",\"accessType\":\"Privileged\"}",
                Emails = "<EMAIL>,<EMAIL>",
                Rule = "Privileged access requires CISO approval",
                WorkflowModification = false,
                WorkflowProfileExecution = true,
                ProfileModification = false,
                TemplateName = "Security Approval Template",
                CreatedBy = "security.admin",
                CreatedDate = DateTime.Now.AddDays(-15),
                status = "Active",
                Approvers = "security.manager,ciso",
                StartDate = DateTime.Now.AddDays(-15),
                EndDate = DateTime.Now.AddDays(350),
                ApprovedBy = "security.director"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserId = Guid.NewGuid().ToString(),
                UserName = "bob.wilson",
                BusinessServiceId = Guid.NewGuid().ToString(),
                BusinessServiceName = "HR Management Service",
                BusinessFunctionId = Guid.NewGuid().ToString(),
                BusinessFunctionName = "Human Resources",
                Name = "HR Approval Matrix",
                Description = "Approval matrix for HR processes including hiring and terminations",
                Time = DateTime.Now.AddDays(-7),
                ActionType = "HR Process",
                Properties = "{\"employeeLevel\":\"Manager\",\"processType\":\"Hiring\"}",
                Emails = "<EMAIL>,<EMAIL>",
                Rule = "Manager level hiring requires HR Director approval",
                WorkflowModification = true,
                WorkflowProfileExecution = true,
                ProfileModification = true,
                TemplateName = "HR Approval Template",
                CreatedBy = "hr.admin",
                CreatedDate = DateTime.Now.AddDays(-7),
                status = "Draft",
                Approvers = "hr.manager,hr.director",
                StartDate = DateTime.Now.AddDays(-7),
                EndDate = DateTime.Now.AddDays(358),
                ApprovedBy = "executive.team"
            }
        };

        // Create detailed ApprovalMatrix data
        ApprovalMatrixDetailVm = new ApprovalMatrixDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            UserId = Guid.NewGuid().ToString(),
            UserName = "admin.user",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Enterprise Management Service",
            Name = "Enterprise Approval Matrix",
            Description = "Comprehensive approval matrix for enterprise-wide operations",
            Time = DateTime.Now,
            ActionType = "Enterprise Approval",
            Properties = "{\"scope\":\"Enterprise\",\"priority\":\"High\",\"impact\":\"Critical\"}"
        };

        // Create command for creating ApprovalMatrix
        CreateApprovalMatrixCommand = new CreateApprovalMatrixCommand
        {
            Name = "New Approval Matrix",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "New Business Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "New Business Function",
            Description = "New approval matrix for testing purposes",
            Properties = "{\"testProperty\":\"testValue\"}",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(365)
        };

        // Create command for updating ApprovalMatrix
        UpdateApprovalMatrixCommand = new UpdateApprovalMatrixCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Approval Matrix",
            BusinessServiceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Updated Business Service",
            BusinessFunctionId = Guid.NewGuid().ToString(),
            BusinessFunctionName = "Updated Business Function",
            Description = "Updated approval matrix with new requirements",
            Properties = "{\"updatedProperty\":\"updatedValue\"}",
            StartDate = DateTime.Now,
            EndDate = DateTime.Now.AddDays(400)
        };
    }
}
