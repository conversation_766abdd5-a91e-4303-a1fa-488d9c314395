﻿using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Web.UnitTests.Areas.ITAutomation.Controllers
{
    public class WorkflowActionTypeControllerTests
    {
        private readonly WorkflowActionTypeController _controller;

        public WorkflowActionTypeControllerTests()
        {
            _controller = new WorkflowActionTypeController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            var result = _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Null(viewResult.ViewName);
        }
    }
}



