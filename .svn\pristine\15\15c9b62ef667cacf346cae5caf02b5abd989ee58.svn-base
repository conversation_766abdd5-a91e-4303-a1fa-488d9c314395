﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.Delete;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetDisplayNameUnique;
using ContinuityPatrol.Application.Features.Company.Queries.GetList;
using ContinuityPatrol.Application.Features.Company.Queries.GetNames;
using ContinuityPatrol.Application.Features.Company.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CompaniesControllerTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CompaniesController _controller;

    public CompaniesControllerTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;
        Mock<ICompanyRepository> companyRepoMock = new();

        var testBuilder = new ControllerTestBuilder<CompaniesController>();
        _controller = testBuilder.CreateController(
            _ => new CompaniesController(companyRepoMock.Object),
            out _mediatorMock);

    }


    [Fact]
    public async Task GetCompanies_ReturnsExpectedList()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_companyFixture.CompanyListVm);

        var result = await _controller.GetCompanies();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var companies = Assert.IsAssignableFrom<List<CompanyListVm>>(okResult.Value);
        Assert.Equal(3, companies.Count);
    }

    [Fact]
    public async Task GetCompanies_ReturnsEmptyList_WhenNoCompaniesExist()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyListQuery>(), default))
            .ReturnsAsync(new List<CompanyListVm>());

        var result = await _controller.GetCompanies();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<CompanyListVm>)okResult.Value!));
    }


    [Fact]
    public async Task GetCompanyById_ReturnsCompany_WhenIdIsValid()
    {
        var companyId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCompanyDetailQuery>(q => q.Id == companyId), default))
            .ReturnsAsync(_companyFixture.CompanyDetailVm);

        var result = await _controller.GetCompanyById(companyId);

        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task CreateCompany_Throws_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCompanyCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Name exists"));

        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateCompany(_companyFixture.CreateCompanyCommand));
    }
    [Fact]
    public async Task CreateCompany_Returns201Created()
    {
        var command = _companyFixture.CreateCompanyCommand;

        var expectedMessage = $"Company '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCompanyResponse
            {
                Message = expectedMessage
            });

        var result = await _controller.CreateCompany(command);

        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCompanyResponse>(createdAtActionResult.Value);

        Assert.Equal(expectedMessage, response.Message); // Verify message
    }

    [Fact]
    public async Task UpdateCompany_ReturnsOk()
    {
        var expectedMessage = $"Company '{_companyFixture.UpdateCompanyCommand.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCompanyCommand>(), default))
            .ReturnsAsync(new UpdateCompanyResponse
            {
                Message = expectedMessage
            });

        var result = await _controller.UpdateCompany(_companyFixture.UpdateCompanyCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCompanyResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCompany_ReturnsOk()
    {
        var expectedMessage = "Company 'PTS' has been deleted successfully!.";

        var companyId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCompanyCommand>(c => c.Id == companyId), default))
            .ReturnsAsync(new DeleteCompanyResponse
            {
                IsActive = false,
                Message = expectedMessage
            });


        var result = await _controller.DeleteCompany(companyId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);

        var response = Assert.IsType<DeleteCompanyResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetCompanyById_Throws_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetCompanyById("invalid-guid"));
    }

    [Fact]
    public async Task IsCompanyNameExist_ReturnsTrue_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        var result = await _controller.IsCompanyNameExist("ExistingName", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task ReturnsFalse_WhenNameDoesNotExist()
    {
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetCompanyNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        var result = await _controller.IsCompanyNameExist("NewName", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        var companyName = "";

        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsCompanyNameExist(companyName, null));
    }

    [Fact]
    public async Task IsCompanyDisplayNameExist_ReturnsTrue_WhenDisplayNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDisplayNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        var result = await _controller.IsDisplayNameExist("ExistingName", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task ReturnsFalse_WhenDisplayNameDoesNotExist()
    {
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetDisplayNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        var result = await _controller.IsDisplayNameExist("NewName", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task ThrowsInvalidArgumentException_WhenDisplayNameIsInvalid()
    {
        var companyName = "";

        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsDisplayNameExist(companyName, null));
    }

    [Fact]
    public async Task GetCompanyName_ReturnsExpectedList()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_companyFixture.CompanyNameVms);

        var result = await _controller.GetCompanyNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var companies = Assert.IsAssignableFrom<List<CompanyNameVm>>(okResult.Value);
        Assert.Equal(3, companies.Count);
    }

    [Fact]
    public async Task GetCompanyNames_ReturnsEmptyList_WhenNoCompanyNamesExist()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameQuery>(), default))
            .ReturnsAsync(new List<CompanyNameVm>());

        var result = await _controller.GetCompanyNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<CompanyNameVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetPaginatedCompanies_ReturnsExpectedPaginatedList()
    {
        var query = new GetCompanyPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _companyFixture.CompanyListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<CompanyListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        var result = await _controller.GetPaginatedCompanies(query);

        var actionResult = Assert.IsType<ActionResult<PaginatedResult<CompanyListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<CompanyListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task CreateDefaultCompany_ThrowsInvalidException_WhenCompaniesCountIsGreaterThanZero()
    {
        var command = _companyFixture.CreateDefaultCompanyCommand;

        var companyRepoMock = new Mock<ICompanyRepository>();
        var existingCompanies = new List<Company>
        {
            new() { Id = 1, ReferenceId = "comp-001", Name = "Existing Company 1" },
            new() { Id = 2, ReferenceId = "comp-002", Name = "Existing Company 2" }
        };

        companyRepoMock.Setup(r => r.GetAllCompanyNames())
            .ReturnsAsync(existingCompanies);

        var testBuilder = new ControllerTestBuilder<CompaniesController>();
        var controller = testBuilder.CreateController(
            _ => new CompaniesController(companyRepoMock.Object),
            out _);

        var exception = await Assert.ThrowsAsync<InvalidException>(() =>
            controller.CreateDefaultCompany(command));

        Assert.Equal("A default company cannot be created after one time!", exception.Message);

        companyRepoMock.Verify(r => r.GetAllCompanyNames(), Times.Once);

        Assert.Equal(2, existingCompanies.Count);
    }

    [Fact]
    public async Task CreateDefaultCompany_ThrowsInvalidException_WhenCompaniesAlreadyExist()
    {
        var command = _companyFixture.CreateDefaultCompanyCommand;
        var existingCompanies = new List<Company> { new() { Id = 1, ReferenceId = "1", Name = "Existing Company" } };

        var companyRepoMock = new Mock<ICompanyRepository>();
        companyRepoMock.Setup(r => r.GetAllCompanyNames())
            .ReturnsAsync(existingCompanies);

        var testBuilder = new ControllerTestBuilder<CompaniesController>();
        var controller = testBuilder.CreateController(
            _ => new CompaniesController(companyRepoMock.Object),
            out _);

        var exception = await Assert.ThrowsAsync<InvalidException>(() =>
            controller.CreateDefaultCompany(command));

        Assert.Equal("A default company cannot be created after one time!", exception.Message);
    }

    [Fact]
    public async Task CreateDefaultCompany_Created201()
    {
        var command = _companyFixture.CreateDefaultCompanyCommand;
        CreateCompanyCommand capturedCommand = null!;

        var companyRepoMock = new Mock<ICompanyRepository>();
        companyRepoMock.Setup(r => r.GetAllCompanyNames())
            .ReturnsAsync(new List<Company>());

        var mockMapper = new Mock<IMapper>();

        mockMapper.Setup(mapper => mapper.Map<CreateCompanyCommand>(command))
            .Returns(_companyFixture.CreateCompanyCommand);


        var testBuilder = new ControllerTestBuilder<CompaniesController>();
        var controller = testBuilder.CreateController(
            _ => new CompaniesController(companyRepoMock.Object),
            out var mediatorMock, mockMapper);

        mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCompanyCommand>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<CreateCompanyResponse>, CancellationToken>((request, _) =>
            {
                if (request is CreateCompanyCommand cmd)
                {
                    capturedCommand = cmd;
                }
            })
            .ReturnsAsync(new CreateCompanyResponse { Message = "Success" });

        var result = await controller.CreateDefaultCompany(command);

        Assert.NotNull(result);
        Assert.NotNull(capturedCommand);
        Assert.Equal(command.Name, capturedCommand.Name);
        Assert.Equal(command.DisplayName, capturedCommand.DisplayName);
    }



    [Fact]
    public async Task GetCompanyById_ThrowsInvalidArgumentException_WhenIdIsEmpty()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetCompanyById(string.Empty));
    }

    [Fact]
    public async Task GetCompanyById_ThrowsInvalidArgumentException_WhenIdIsWhitespace()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetCompanyById("   "));
    }

    [Fact]
    public async Task DeleteCompany_ThrowsInvalidArgumentException_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteCompany("invalid-guid"));
    }

    [Fact]
    public async Task DeleteCompany_ThrowsInvalidArgumentException_WhenIdIsEmpty()
    {
         
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteCompany(string.Empty));
    }

    [Fact]
    public async Task IsCompanyNameExist_ThrowsInvalidArgumentException_WhenNameIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsCompanyNameExist(null!, null));
    }

    [Fact]
    public async Task IsCompanyNameExist_ThrowsInvalidArgumentException_WhenNameIsWhitespace()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsCompanyNameExist("   ", null));
    }

    [Fact]
    public async Task IsDisplayNameExist_ThrowsInvalidArgumentException_WhenDisplayNameIsNull()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _controller.IsDisplayNameExist(null!, null));
    }

    [Fact]
    public async Task IsDisplayNameExist_ThrowsInvalidArgumentException_WhenDisplayNameIsWhitespace()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsDisplayNameExist("   ", null));
    }


    [Fact]
    public async Task CreateCompany_CallsCorrectCommand()
    {
        var command = _companyFixture.CreateCompanyCommand;
        CreateCompanyCommand capturedCommand = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCompanyCommand>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<CreateCompanyResponse>, CancellationToken>((request, _) =>
            {
                if (request is CreateCompanyCommand cmd)
                {
                    capturedCommand = cmd;
                }
            })
            .ReturnsAsync(new CreateCompanyResponse { Message = "Success" });

        await _controller.CreateCompany(command);

        Assert.NotNull(capturedCommand);
        Assert.Equal(command.Name, capturedCommand.Name);
        Assert.Equal(command.DisplayName, capturedCommand.DisplayName);
    }

    [Fact]
    public async Task UpdateCompany_CallsCorrectCommand()
    {
        var command = _companyFixture.UpdateCompanyCommand;
        UpdateCompanyCommand capturedCommand = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCompanyCommand>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<UpdateCompanyResponse>, CancellationToken>((request, _) =>
            {
                if (request is UpdateCompanyCommand cmd)
                {
                    capturedCommand = cmd;
                }
            })
            .ReturnsAsync(new UpdateCompanyResponse { Message = "Success" });

         
        await _controller.UpdateCompany(command);

        Assert.NotNull(capturedCommand);
        Assert.Equal(command.Name, capturedCommand.Name);
        Assert.Equal(command.Id, capturedCommand.Id);
    }

    [Fact]
    public async Task DeleteCompany_CallsCorrectCommand()
    {
        var companyId = Guid.NewGuid().ToString();
        DeleteCompanyCommand capturedCommand = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteCompanyCommand>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<DeleteCompanyResponse>, CancellationToken>((request, _) =>
            {
                if (request is DeleteCompanyCommand cmd)
                {
                    capturedCommand = cmd;
                }
            })
            .ReturnsAsync(new DeleteCompanyResponse { Message = "Success", IsActive = false });

         
        await _controller.DeleteCompany(companyId);

        Assert.NotNull(capturedCommand);
        Assert.Equal(companyId, capturedCommand.Id);
    }


    [Fact]
    public async Task CreateCompany_ThrowsException_WhenMediatorFails()
    {
        var command = _companyFixture.CreateCompanyCommand;
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateCompanyCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        await Assert.ThrowsAsync<Exception>(() => _controller.CreateCompany(command));
    }

    [Fact]
    public async Task UpdateCompany_ThrowsException_WhenMediatorFails()
    {
        var command = _companyFixture.UpdateCompanyCommand;
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateCompanyCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        await Assert.ThrowsAsync<Exception>(() => _controller.UpdateCompany(command));
    }

    [Fact]
    public async Task DeleteCompany_ThrowsException_WhenMediatorFails()
    {
        var companyId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteCompanyCommand>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        await Assert.ThrowsAsync<Exception>(() => _controller.DeleteCompany(companyId));
    }

    [Fact]
    public async Task GetCompanyById_ThrowsException_WhenMediatorFails()
    {
        var companyId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyDetailQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Database error"));

        await Assert.ThrowsAsync<Exception>(() => _controller.GetCompanyById(companyId));
    }



    [Fact]
    public async Task IsCompanyNameExist_IncludesIdInQuery_WhenProvided()
    {
        var companyName = "TestCompany";
        var companyId = Guid.NewGuid().ToString();
        GetCompanyNameUniqueQuery capturedQuery = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetCompanyNameUniqueQuery query)
                {
                    capturedQuery = query;
                }
            })
            .ReturnsAsync(false);

         
        await _controller.IsCompanyNameExist(companyName, companyId);

        Assert.NotNull(capturedQuery);
        Assert.Equal(companyName, capturedQuery.CompanyName);
        Assert.Equal(companyId, capturedQuery.CompanyId);
    }

    [Fact]
    public async Task IsCompanyNameExist_ExcludesIdFromQuery_WhenNotProvided()
    {
        var companyName = "TestCompany";
        GetCompanyNameUniqueQuery capturedQuery = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetCompanyNameUniqueQuery query)
                {
                    capturedQuery = query;
                }
            })
            .ReturnsAsync(false);

         
        await _controller.IsCompanyNameExist(companyName, null);

        Assert.NotNull(capturedQuery);
        Assert.Equal(companyName, capturedQuery.CompanyName);
        Assert.Null(capturedQuery.CompanyId);
    }

    [Fact]
    public async Task IsDisplayNameExist_IncludesIdInQuery_WhenProvided()
    {
        var displayName = "Test Display Name";
        var companyId = Guid.NewGuid().ToString();
        GetDisplayNameUniqueQuery capturedQuery = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDisplayNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<bool>, CancellationToken>((request, _) =>
            {
                if (request is GetDisplayNameUniqueQuery query)
                {
                    capturedQuery = query;
                }
            })
            .ReturnsAsync(false);

         
        await _controller.IsDisplayNameExist(displayName, companyId);

        Assert.NotNull(capturedQuery);
        Assert.Equal(displayName, capturedQuery.DisplayName);
        Assert.Equal(companyId, capturedQuery.CompanyId);
    }


    [Fact]
    public async Task GetPaginatedCompanies_ReturnsEmptyList_WhenNoDataExists()
    {
        var query = new GetCompanyPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<CompanyListVm>.Success(
                data: new List<CompanyListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

         
        var result = await _controller.GetPaginatedCompanies(query);

        var actionResult = Assert.IsType<ActionResult<PaginatedResult<CompanyListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<CompanyListVm>>(okResult.Value);

        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetPaginatedCompanies_CallsCorrectQuery()
    {
        var query = new GetCompanyPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 5,
            SearchString = "Test"
        };

        GetCompanyPaginatedListQuery capturedQuery = null!;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<PaginatedResult<CompanyListVm>>, CancellationToken>((request, _) =>
            {
                if (request is GetCompanyPaginatedListQuery paginatedQuery)
                {
                    capturedQuery = paginatedQuery;
                }
            })
            .ReturnsAsync(PaginatedResult<CompanyListVm>.Success(
                data: new List<CompanyListVm>(),
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        await _controller.GetPaginatedCompanies(query);

        Assert.NotNull(capturedQuery);
        Assert.Equal(query.PageNumber, capturedQuery.PageNumber);
        Assert.Equal(query.PageSize, capturedQuery.PageSize);
        Assert.Equal(query.SearchString, capturedQuery.SearchString);
    }



    [Fact]
    public async Task GetCompanyNames_ReturnsCorrectProperties()
    {
        var expectedNames = new List<CompanyNameVm>
        {
            new() { Id = "1", DisplayName = "Company One", IsParent = true },
            new() { Id = "2", DisplayName = "Company Two", IsParent = false }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedNames);

        var result = await _controller.GetCompanyNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var companies = Assert.IsAssignableFrom<List<CompanyNameVm>>(okResult.Value);

        Assert.Equal(2, companies.Count);
        Assert.Equal("Company One", companies[0].DisplayName);
        Assert.True(companies[0].IsParent);
        Assert.Equal("Company Two", companies[1].DisplayName);
        Assert.False(companies[1].IsParent);
    }

    [Fact]
    public async Task GetCompanies_ReturnsCorrectProperties()
    {
        var expectedCompanies = new List<CompanyListVm>
        {
            new() { Id = "1", Name = "Company One", DisplayName = "Display One" },
            new() { Id = "2", Name = "Company Two", DisplayName = "Display Two" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCompanyListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCompanies);

        var result = await _controller.GetCompanies();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var companies = Assert.IsAssignableFrom<List<CompanyListVm>>(okResult.Value);

        Assert.Equal(2, companies.Count);
        Assert.Equal("Company One", companies[0].Name);
        Assert.Equal("Display One", companies[0].DisplayName);
        Assert.Equal("Company Two", companies[1].Name);
        Assert.Equal("Display Two", companies[1].DisplayName);
    }
}