using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class RsyncMonitorLogRepositoryTests : IClassFixture<RsyncMonitorLogFixture>
{
    private readonly RsyncMonitorLogFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly RsyncMonitorLogRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public RsyncMonitorLogRepositoryTests(RsyncMonitorLogFixture fixture)
    {
        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = new Mock<IConfiguration>();

        // Setup mock configuration
        _mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("Server=localhost;Database=TestDB;Trusted_Connection=true;");
        _mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("mssql");

        _repository = new RsyncMonitorLogRepository(_dbContext, _mockConfiguration.Object);
    }

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var type = "Rsync_Test";
        var matchingLogs = new List<RsyncMonitorLog>
        {
            _fixture.CreateRsyncMonitorLogWithType(type),
            _fixture.CreateRsyncMonitorLogWithType(type)
        };
        var nonMatchingLog = _fixture.CreateRsyncMonitorLogWithType("Different_Type");

        _dbContext.RsyncMonitorLog.AddRange(matchingLogs);
        _dbContext.RsyncMonitorLog.Add(nonMatchingLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal(type, log.Type));
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var log = _fixture.CreateRsyncMonitorLogWithType("Different_Type");
        _dbContext.RsyncMonitorLog.Add(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("NON_EXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var type = "Active_Test_Type";
        var activeLog = _fixture.CreateRsyncMonitorLogWithProperties(type: type, isActive: true);
        var inactiveLog = _fixture.CreateRsyncMonitorLogWithProperties(type: type, isActive: false);

        _dbContext.RsyncMonitorLog.AddRange(activeLog, inactiveLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(type, result.First().Type);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeIsNullOrEmpty(string type)
    {
        // Arrange
        var log = _fixture.CreateRsyncMonitorLogWithType("VALID_TYPE");
        _dbContext.RsyncMonitorLog.Add(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnMatchingRecords_WhenRecordsExist()
    {
        // Arrange
        var infraObjectId = "INFRA_TEST_001";
        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        var matchingLogs = new List<RsyncMonitorLog>
        {
            _fixture.CreateRsyncMonitorLogWithInfraObjectId(infraObjectId),
            _fixture.CreateRsyncMonitorLogWithInfraObjectId(infraObjectId)
        };
        var nonMatchingLog = _fixture.CreateRsyncMonitorLogWithInfraObjectId("DIFFERENT_INFRA");

        _dbContext.RsyncMonitorLog.AddRange(matchingLogs);
        _dbContext.RsyncMonitorLog.Add(nonMatchingLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.Equal(infraObjectId, log.InfraObjectId));
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        var log = _fixture.CreateRsyncMonitorLogWithInfraObjectId("DIFFERENT_INFRA");
        _dbContext.RsyncMonitorLog.Add(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId("NON_EXISTENT_INFRA", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveRecords()
    {
        // Arrange
        var infraObjectId = "INFRA_ACTIVE_TEST";
        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        var activeLog = _fixture.CreateRsyncMonitorLogWithProperties(infraObjectId: infraObjectId, isActive: true);
        var inactiveLog = _fixture.CreateRsyncMonitorLogWithProperties(infraObjectId: infraObjectId, isActive: false);

        _dbContext.RsyncMonitorLog.AddRange(activeLog, inactiveLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result.First().IsActive);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenInfraObjectIdIsNullOrEmpty(string infraObjectId)
    {
        // Arrange
        var startDate = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        var log = _fixture.CreateRsyncMonitorLogWithInfraObjectId("VALID_INFRA");
        _dbContext.RsyncMonitorLog.Add(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByInfraObjectId(infraObjectId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetTableName Tests

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableName()
    {
        // Act
        var result = _repository.GetTableName<RsyncMonitorLog>();

        // Assert
        Assert.NotNull(result);
        Assert.Equal("RsyncMonitorLog", result);
    }

    #endregion

    #region IsTableExistAsync Tests

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnTrue_WhenTableExists()
    {
        // Arrange
        var tableName = "RsyncMonitorLog";
        var schemaName = "dbo";
        var databaseName = "TestDB";

        // Act
        var result = await _repository.IsTableExistAsync(tableName, schemaName, databaseName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableDoesNotExist()
    {
        // Arrange
        var tableName = "NonExistentTable";
        var schemaName = "dbo";
        var databaseName = "TestDB";

        // Act
        var result = await _repository.IsTableExistAsync(tableName, schemaName, databaseName);

        // Assert
        Assert.False(result);
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData(null)]
    public async Task IsTableExistAsync_ShouldReturnFalse_WhenTableNameIsNullOrEmpty(string tableName)
    {
        // Arrange
        var schemaName = "dbo";
        var databaseName = "TestDB";

        // Act
        var result = await _repository.IsTableExistAsync(tableName, schemaName, databaseName);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddRsyncMonitorLog_WhenValidEntity()
    {
        // Arrange
        var monitorLog = _fixture.RsyncMonitorLogDto;
        monitorLog.Type = "Rsync";
        monitorLog.InfraObjectId = "INFRA_TEST_001";
        monitorLog.InfraObjectName = "Test Infrastructure Object";
        monitorLog.WorkflowId = "WORKFLOW_TEST_001";
        monitorLog.WorkflowName = "Test Workflow";
        monitorLog.Properties = "{\"property1\":\"value1\",\"property2\":\"value2\"}";
        monitorLog.ConfiguredRPO = "4 hours";
        monitorLog.DataLagValue = "30 minutes";
        monitorLog.Threshold = "2 hours";

        // Act
        var result = await _repository.AddAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.Type, result.Type);
        Assert.Equal(monitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorLog.InfraObjectName, result.InfraObjectName);
        Assert.Equal(monitorLog.WorkflowId, result.WorkflowId);
        Assert.Equal(monitorLog.WorkflowName, result.WorkflowName);
        Assert.Equal(monitorLog.Properties, result.Properties);
        Assert.Equal(monitorLog.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Equal(monitorLog.DataLagValue, result.DataLagValue);
        Assert.Equal(monitorLog.Threshold, result.Threshold);
        Assert.Single(_dbContext.RsyncMonitorLog);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RsyncMonitorLogDto;
        _dbContext.RsyncMonitorLog.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(monitorLog.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(monitorLog.Id, result.Id);
        Assert.Equal(monitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Equal(monitorLog.Type, result.Type);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RsyncMonitorLogDto;
        _dbContext.RsyncMonitorLog.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        var updatedType = "Updated_Rsync";
        var updatedDataLagValue = "1 hour";
        monitorLog.Type = updatedType;
        monitorLog.DataLagValue = updatedDataLagValue;

        // Act
        var result = await _repository.UpdateAsync(monitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(updatedType, result.Type);
        Assert.Equal(updatedDataLagValue, result.DataLagValue);
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity_WhenExists()
    {
        // Arrange
        var monitorLog = _fixture.RsyncMonitorLogDto;
        _dbContext.RsyncMonitorLog.Add(monitorLog);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(monitorLog);

        // Assert
        var deletedEntity = await _repository.GetByIdAsync(monitorLog.Id);
        Assert.Null(deletedEntity);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.RsyncMonitorLog.RemoveRange(_dbContext.RsyncMonitorLog);
        await _dbContext.SaveChangesAsync();
    }
}
