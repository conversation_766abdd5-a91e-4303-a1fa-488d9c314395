using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataSetColumnsFixture : IDisposable
{
    public List<DataSetColumns> DataSetColumnsPaginationList { get; set; }
    public List<DataSetColumns> DataSetColumnsList { get; set; }
    public DataSetColumns DataSetColumnsDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string DataSetId = "DATASET_123";
    public const string TableName = "TestTable";
    public const string ColumnName = "TestColumn";

    public ApplicationDbContext DbContext { get; private set; }

    public DataSetColumnsFixture()
    {
        var fixture = new Fixture();

        DataSetColumnsList = fixture.Create<List<DataSetColumns>>();

        DataSetColumnsPaginationList = fixture.CreateMany<DataSetColumns>(20).ToList();

        DataSetColumnsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSetColumnsPaginationList.ForEach(x => x.IsActive = true);
        DataSetColumnsPaginationList.ForEach(x => x.DataSetId = DataSetId);
        DataSetColumnsPaginationList.ForEach(x => x.TableName = TableName);
        DataSetColumnsPaginationList.ForEach(x => x.ColumnName = ColumnName);

        DataSetColumnsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSetColumnsList.ForEach(x => x.IsActive = true);
        DataSetColumnsList.ForEach(x => x.DataSetId = DataSetId);
        DataSetColumnsList.ForEach(x => x.TableName = TableName);
        DataSetColumnsList.ForEach(x => x.ColumnName = ColumnName);

        DataSetColumnsDto = fixture.Create<DataSetColumns>();
        DataSetColumnsDto.ReferenceId = Guid.NewGuid().ToString();
        DataSetColumnsDto.IsActive = true;
        DataSetColumnsDto.DataSetId = DataSetId;
        DataSetColumnsDto.TableName = TableName;
        DataSetColumnsDto.ColumnName = ColumnName;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
