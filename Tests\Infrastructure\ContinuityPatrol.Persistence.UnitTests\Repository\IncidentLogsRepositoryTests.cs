using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class IncidentLogsRepositoryTests : IClassFixture<IncidentLogsFixture>, IDisposable
{
    private readonly IncidentLogsFixture _incidentLogsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly IncidentLogsRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public IncidentLogsRepositoryTests(IncidentLogsFixture incidentLogsFixture)
    {
        _incidentLogsFixture = incidentLogsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new IncidentLogsRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.IncidentLogs.RemoveRange(_dbContext.IncidentLogs);
        await _dbContext.SaveChangesAsync();
    }

    #region GetIncidentLogsByIncidentNumber Tests

    [Fact]
    public async Task GetIncidentLogsByIncidentNumber_ThrowsNotImplementedException()
    {
        // Arrange
        var incidentNumber = "INC-12345";

        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() => 
            _repository.GetIncidentLogsByIncidentNumber(incidentNumber));
    }

    #endregion

    #region GetIncidentLogsByInfraObjectId Tests

    [Fact]
    public async Task GetIncidentLogsByInfraObjectId_ThrowsNotImplementedException()
    {
        // Arrange
        var infraObjectId = "INFRA_123";

        // Act & Assert
        await Assert.ThrowsAsync<NotImplementedException>(() => 
            _repository.GetIncidentLogsByInfraObjectId(infraObjectId));
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddIncidentLogs_WhenValidIncidentLogs()
    {
        // Arrange
        await ClearDatabase();
        var incidentLogs = new IncidentLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentNumber = 12345,
            Severity = "High",
            Message = "Test incident log message",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(incidentLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incidentLogs.IncidentNumber, result.IncidentNumber);
        Assert.Equal(incidentLogs.Severity, result.Severity);
        Assert.Equal(incidentLogs.Message, result.Message);
        Assert.Equal(incidentLogs.InfraObjectId, result.InfraObjectId);
        Assert.Equal(incidentLogs.InfraObjectName, result.InfraObjectName);
        Assert.Single(_dbContext.IncidentLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenIncidentLogsIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsIncidentLogs_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var incidentLogs = new IncidentLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentNumber = 12345,
            Severity = "Medium",
            Message = "Test log message",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure",
            IsActive = true
        };

        await _dbContext.IncidentLogs.AddAsync(incidentLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(incidentLogs.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(incidentLogs.Id, result.Id);
        Assert.Equal(incidentLogs.IncidentNumber, result.IncidentNumber);
        Assert.Equal(incidentLogs.Severity, result.Severity);
        Assert.Equal(incidentLogs.Message, result.Message);
        Assert.Equal(incidentLogs.InfraObjectId, result.InfraObjectId);
        Assert.Equal(incidentLogs.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateIncidentLogs_WhenValidIncidentLogs()
    {
        // Arrange
        await ClearDatabase();
        var incidentLogs = new IncidentLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentNumber = 12345,
            Severity = "Low",
            Message = "Original message",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Original Infrastructure",
            IsActive = true
        };

        _dbContext.IncidentLogs.Add(incidentLogs);
        await _dbContext.SaveChangesAsync();

        incidentLogs.Severity = "High";
        incidentLogs.Message = "Updated message";
        incidentLogs.InfraObjectName = "Updated Infrastructure";

        // Act
        var result = await _repository.UpdateAsync(incidentLogs);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("High", result.Severity);
        Assert.Equal("Updated message", result.Message);
        Assert.Equal("Updated Infrastructure", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenIncidentLogsIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveIncidentLogs_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var incidentLogs = new IncidentLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            IncidentNumber = 12345,
            Severity = "Medium",
            Message = "Test message",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure",
            IsActive = true
        };

        await _dbContext.IncidentLogs.AddAsync(incidentLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(incidentLogs);

        // Assert
        var deletedLogs = await _dbContext.IncidentLogs.FindAsync(incidentLogs.Id);
        Assert.Null(deletedLogs);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleIncidentLogs_WhenValidList()
    {
        // Arrange
        await ClearDatabase();
        var incidentLogsList = new List<IncidentLogs>
        {
            new IncidentLogs 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                IncidentNumber = 12345,
                Severity = "High",
                Message = "First log message",
                InfraObjectId = "INFRA_123",
                InfraObjectName = "Infrastructure 1",
                IsActive = true 
            },
            new IncidentLogs 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                IncidentNumber = 12346,
                Severity = "Medium",
                Message = "Second log message",
                InfraObjectId = "INFRA_456",
                InfraObjectName = "Infrastructure 2",
                IsActive = true 
            },
            new IncidentLogs 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                IncidentNumber = 12347,
                Severity = "Low",
                Message = "Third log message",
                InfraObjectId = "INFRA_789",
                InfraObjectName = "Infrastructure 3",
                IsActive = true 
            }
        };

        // Act
        var result = await _repository.AddRangeAsync(incidentLogsList);

        // Assert
        Assert.Equal(3, result.Count());
        Assert.Equal(3, _dbContext.IncidentLogs.Count());
        Assert.Contains(result, x => x.Message == "First log message");
        Assert.Contains(result, x => x.Message == "Second log message");
        Assert.Contains(result, x => x.Message == "Third log message");
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    [Fact]
    public async Task AddRangeAsync_ShouldReturnEmpty_WhenListIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var emptyList = new List<IncidentLogs>();

        // Act
        var result = await _repository.AddRangeAsync(emptyList);

        // Assert
        Assert.Empty(result);
        Assert.Empty(_dbContext.IncidentLogs);
    }

    #endregion
}
