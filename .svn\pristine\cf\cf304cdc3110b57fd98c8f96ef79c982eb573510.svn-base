using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq.Protected;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FormTypeCategoryRepositoryTests : IClassFixture<FormTypeCategoryFixture>, IDisposable
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FormTypeCategoryRepository _repository;

    public FormTypeCategoryRepositoryTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FormTypeCategoryRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }
    

    [Fact]
    public async Task PaginatedListAllAsync_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        var listValue = _formTypeCategoryFixture.FormTypeCategoryPaginationList;
        foreach (var val in listValue)
            val.IsActive = true;
        await _dbContext.FormTypeCategories.AddRangeAsync(listValue);
        await _dbContext.SaveChangesAsync();

        string? searchString = null;
        var spec = new FormTypeCategoryFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 20,
            productFilterSpec: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );
    }
    #region GetFormTypeCategoryByFormTypeId Tests

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeId_ReturnsFormTypeCategory_WhenFormTypeIdExists()
    {
        // Arrange
        var formTypeId = Guid.NewGuid().ToString();
        var formTypeCategory = new Domain.Entities.FormTypeCategory
        {
            Name = "Survey Category",
            FormTypeId = formTypeId,
            FormTypeName = "Survey",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FormTypeCategories.AddAsync(formTypeCategory);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormTypeId(formTypeId);

        // Assert
        Assert.NotNull(result);
        // Note: FormTypeId may be null due to mapping with ComponentTypes table that doesn't exist in test
        Assert.Equal("Survey Category", result.Name);
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeId_ReturnsNull_WhenFormTypeIdDoesNotExist()
    {
        // Arrange
        var nonExistentFormTypeId = Guid.NewGuid().ToString();

        _formTypeCategoryFixture.FormTypeCategoryDto.FormTypeId = Guid.NewGuid().ToString();
        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormTypeId(nonExistentFormTypeId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetFormTypeCategoryByName Tests

    [Fact]
    public async Task GetFormTypeCategoryByName_ReturnsFormTypeCategories_WhenNameExists()
    {
        // Arrange
        var categoryName = "Assessment Category";
        var formTypeCategory1 = new Domain.Entities.FormTypeCategory
        {
            Name = categoryName,
            FormTypeId = Guid.NewGuid().ToString(),
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var formTypeCategory2 = new Domain.Entities.FormTypeCategory
        {
            Name = categoryName.ToUpper(), // Different case
            FormTypeId = Guid.NewGuid().ToString(),
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FormTypeCategories.AddRangeAsync(new[] { formTypeCategory1, formTypeCategory2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByName(categoryName);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(categoryName.ToLower(), x.Name.ToLower()));
    }

    [Fact]
    public async Task GetFormTypeCategoryByName_ReturnsEmpty_WhenNameDoesNotExist()
    {
        // Arrange
        var nonExistentName = "Non Existent Category";

        _formTypeCategoryFixture.FormTypeCategoryDto.Name = "Different Category";
        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByName(nonExistentName);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormTypeCategoryByFormTypeIdAndVersion Tests

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeIdAndVersion_ReturnsFormTypeCategory_WhenBothExist()
    {
        // Arrange
        var formTypeId = Guid.NewGuid().ToString();
        var version = "1.5";
        var formTypeCategory = new Domain.Entities.FormTypeCategory
        {
            Name = "Versioned Category",
            FormTypeId = formTypeId,
            Version = $"Version {version} Details",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FormTypeCategories.AddAsync(formTypeCategory);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormTypeIdAndVersion(formTypeId, version);

        // Assert
        Assert.NotNull(result);
        // Note: FormTypeId may be null due to mapping with ComponentTypes table that doesn't exist in test
        Assert.Contains(version, result.Version);
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormTypeIdAndVersion_ReturnsNull_WhenVersionDoesNotMatch()
    {
        // Arrange
        var formTypeId = Guid.NewGuid().ToString();
        var version = "2.0";
        var nonMatchingVersion = "3.0";

        _formTypeCategoryFixture.FormTypeCategoryDto.FormTypeId = formTypeId;
        _formTypeCategoryFixture.FormTypeCategoryDto.Version = $"Version {nonMatchingVersion}";
        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormTypeIdAndVersion(formTypeId, version);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region IsFormTypeCategoryNameExist Tests

    [Fact]
    public async Task IsFormTypeCategoryNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var formTypeName = "Survey Form Type";
        _formTypeCategoryFixture.FormTypeCategoryDto.FormTypeName = formTypeName;

        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeCategoryNameExist(formTypeName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormTypeCategoryNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Form Type";

        // Act
        var result = await _repository.IsFormTypeCategoryNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsFormTypeCategoryNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var formTypeName = "Assessment Form Type";

        _formTypeCategoryFixture.FormTypeCategoryDto.ReferenceId = existingId;
        _formTypeCategoryFixture.FormTypeCategoryDto.FormTypeName = formTypeName;

        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeCategoryNameExist(formTypeName, existingId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsFormTypeCategoryNameUnique Tests

    [Fact]
    public async Task IsFormTypeCategoryNameUnique_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var categoryName = "Unique Category";
        _formTypeCategoryFixture.FormTypeCategoryDto.Name = categoryName;

        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsFormTypeCategoryNameUnique(categoryName);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsFormTypeCategoryNameUnique_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var nonExistentName = "Non Existent Category";

        // Act
        var result = await _repository.IsFormTypeCategoryNameUnique(nonExistentName);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsFormNamesUnique Tests

    [Fact]
    public async Task IsFormNamesUnique_ReturnsMatchingNames_WhenSomeNamesExist()
    {
        // Arrange
        var categories = new[]
        {
            new Domain.Entities.FormTypeCategory { Name = "Category A", ReferenceId = Guid.NewGuid().ToString() },
            new Domain.Entities.FormTypeCategory { Name = "Category B", ReferenceId = Guid.NewGuid().ToString() },
            new Domain.Entities.FormTypeCategory { Name = "Category C", ReferenceId = Guid.NewGuid().ToString() }
        };

        await _dbContext.FormTypeCategories.AddRangeAsync(categories);
        await _dbContext.SaveChangesAsync();

        var namesToCheck = new List<string> { "Category A", "Category B", "Category D", "Category E" };

        // Act
        var result = await _repository.IsFormNamesUnique(namesToCheck);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains("Category A", result);
        Assert.Contains("Category B", result);
        Assert.DoesNotContain("Category C", result);
        Assert.DoesNotContain("Category D", result);
    }

    [Fact]
    public async Task IsFormNamesUnique_ReturnsEmpty_WhenNoNamesExist()
    {
        // Arrange
        var namesToCheck = new List<string> { "Category X", "Category Y", "Category Z" };

        // Act
        var result = await _repository.IsFormNamesUnique(namesToCheck);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormTypeCategoryByIds Tests

    [Fact]
    public async Task GetFormTypeCategoryByIds_ReturnsCategories_WhenIdsExist()
    {
        // Arrange
        var id1 = Guid.NewGuid().ToString();
        var id2 = Guid.NewGuid().ToString();
        var id3 = Guid.NewGuid().ToString();

        var categories = new[]
        {
            new Domain.Entities.FormTypeCategory { Name = "Category 1", ReferenceId = id1 },
            new Domain.Entities.FormTypeCategory { Name = "Category 2", ReferenceId = id2 },
            new Domain.Entities.FormTypeCategory { Name = "Category 3", ReferenceId = id3 }
        };

        await _dbContext.FormTypeCategories.AddRangeAsync(categories);
        await _dbContext.SaveChangesAsync();

        var idsToGet = new List<string> { id1, id2 };

        // Act
        var result = await _repository.GetFormTypeCategoryByIds(idsToGet);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.ReferenceId == id1);
        Assert.Contains(result, x => x.ReferenceId == id2);
        Assert.DoesNotContain(result, x => x.ReferenceId == id3);
    }

    [Fact]
    public async Task GetFormTypeCategoryByIds_ReturnsEmpty_WhenNoIdsExist()
    {
        // Arrange
        var nonExistentIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };

        // Act
        var result = await _repository.GetFormTypeCategoryByIds(nonExistentIds);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormTypeCategoryNames Tests

    [Fact]
    public async Task GetFormTypeCategoryNames_ReturnsActiveCategoriesWithProjection_WhenCategoriesExist()
    {
        // Arrange
        var uniqueName = $"TestCategory{Guid.NewGuid().ToString("N")[..8]}";

        var category = new Domain.Entities.FormTypeCategory
        {
            Name = uniqueName,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            FormId = "should-not-be-in-result",
            FormName = "should-not-be-in-result"
        };

        await _dbContext.FormTypeCategories.AddAsync(category);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryNames();

        // Assert
        var testCategory = result.FirstOrDefault(x => x.Name == uniqueName);
        Assert.NotNull(testCategory);
        Assert.Equal(uniqueName, testCategory.Name);
        Assert.NotNull(testCategory.ReferenceId);
        // Verify projection - these should be null because they're not selected
        Assert.Null(testCategory.FormId);
        Assert.Null(testCategory.FormName);
    }

    [Fact]
    public async Task GetFormTypeCategoryNames_ReturnsProjectedData_WhenCalled()
    {
        // Arrange
        var uniqueName = $"TestCategory{Guid.NewGuid().ToString("N")[..8]}";
        var category = new Domain.Entities.FormTypeCategory
        {
            Name = uniqueName,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true,
            FormId = Guid.NewGuid().ToString(), // This should not be in the result
            FormName = "Should not be in result"
        };

        await _dbContext.FormTypeCategories.AddAsync(category);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryNames();

        // Assert
        var testCategory = result.FirstOrDefault(x => x.Name == uniqueName);
        Assert.NotNull(testCategory);
        Assert.Equal(uniqueName, testCategory.Name);
        Assert.NotNull(testCategory.ReferenceId);
        // Verify projection - these should be null because they're not selected
        Assert.Null(testCategory.FormId);
        Assert.Null(testCategory.FormName);
    }

    #endregion

    #region GetFormTypeCategoryByFormId Tests

    [Fact]
    public async Task GetFormTypeCategoryByFormId_ReturnsCategories_WhenFormIdExists()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var formTypeCategory = new Domain.Entities.FormTypeCategory
        {
            Name = "Form Category",
            FormId = formId,
            FormName = "Test Form",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FormTypeCategories.AddAsync(formTypeCategory);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormId(formId);

        // Assert
        Assert.Single(result);
        // Note: FormId may be null due to mapping with Forms table that doesn't exist in test
        Assert.Equal("Form Category", result[0].Name);
    }

    [Fact]
    public async Task GetFormTypeCategoryByFormId_ReturnsEmpty_WhenFormIdDoesNotExist()
    {
        // Arrange
        var nonExistentFormId = Guid.NewGuid().ToString();

        _formTypeCategoryFixture.FormTypeCategoryDto.FormId = Guid.NewGuid().ToString();
        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormTypeCategoryByFormId(nonExistentFormId);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormTypeCategoriesByFormIds Tests

    [Fact]
    public async Task GetFormTypeCategoriesByFormIds_ReturnsCategories_WhenFormIdsExist()
    {
        // Arrange
        var formId1 = Guid.NewGuid().ToString();
        var formId2 = Guid.NewGuid().ToString();
        var formId3 = Guid.NewGuid().ToString();

        var categories = new[]
        {
            new Domain.Entities.FormTypeCategory { Name = "Category 1", FormId = formId1, ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Name = "Category 2", FormId = formId2, ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Name = "Category 3", FormId = formId3, ReferenceId = Guid.NewGuid().ToString(), IsActive = true }
        };

        await _dbContext.FormTypeCategories.AddRangeAsync(categories);
        await _dbContext.SaveChangesAsync();

        var formIdsToGet = new List<string> { formId1, formId2 };

        // Act
        var result = await _repository.GetFormTypeCategoriesByFormIds(formIdsToGet);

        // Assert
        Assert.Equal(2, result.Count);
        // Note: FormId may be null due to mapping with Forms table that doesn't exist in test
        // We can verify by checking the count and names instead
        Assert.True(result.Any(x => x.Name == "Category 1"));
        Assert.True(result.Any(x => x.Name == "Category 2"));
    }

    [Fact]
    public async Task GetFormTypeCategoriesByFormIds_ReturnsEmpty_WhenNoFormIdsExist()
    {
        // Arrange
        var nonExistentFormIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };

        // Act
        var result = await _repository.GetFormTypeCategoriesByFormIds(nonExistentFormIds);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFormTypeCategoriesByFormTypeIds Tests

    [Fact]
    public async Task GetFormTypeCategoriesByFormTypeIds_ReturnsCategories_WhenFormTypeIdsExist()
    {
        // Arrange
        var formTypeId1 = Guid.NewGuid().ToString();
        var formTypeId2 = Guid.NewGuid().ToString();
        var formTypeId3 = Guid.NewGuid().ToString();

        var categories = new[]
        {
            new Domain.Entities.FormTypeCategory { Name = "Category 1", FormTypeId = formTypeId1, ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Name = "Category 2", FormTypeId = formTypeId2, ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Name = "Category 3", FormTypeId = formTypeId3, ReferenceId = Guid.NewGuid().ToString(), IsActive = true }
        };

        await _dbContext.FormTypeCategories.AddRangeAsync(categories);
        await _dbContext.SaveChangesAsync();

        var formTypeIdsToGet = new List<string> { formTypeId1, formTypeId2 };

        // Act
        var result = await _repository.GetFormTypeCategoriesByFormTypeIds(formTypeIdsToGet);

        // Assert
        Assert.Equal(2, result.Count);
        // Note: FormTypeId may be null due to mapping with ComponentTypes table that doesn't exist in test
        // We can verify by checking the count and names instead
        Assert.True(result.Any(x => x.Name == "Category 1"));
        Assert.True(result.Any(x => x.Name == "Category 2"));
    }

    [Fact]
    public async Task GetFormTypeCategoriesByFormTypeIds_ReturnsEmpty_WhenNoFormTypeIdsExist()
    {
        // Arrange
        var nonExistentFormTypeIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };

        // Act
        var result = await _repository.GetFormTypeCategoriesByFormTypeIds(nonExistentFormTypeIds);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public void GetPaginatedQuery_ReturnsActiveFormTypeCategoriesOrderedById_WhenCalled()
    {
        // Arrange
        var categories = new[]
        {
            new Domain.Entities.FormTypeCategory { Id = 1, Name = "Category 1", ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Id = 2, Name = "Category 2", ReferenceId = Guid.NewGuid().ToString(), IsActive = true },
            new Domain.Entities.FormTypeCategory { Id = 3, Name = "Category 3", ReferenceId = Guid.NewGuid().ToString(), IsActive = false }
        };

        _dbContext.FormTypeCategories.AddRange(categories);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();

        // Verify only active categories are returned
        Assert.All(resultList, x => Assert.True(x.IsActive));

        // Verify ordering by Id descending (DescOrderById)
        if (resultList.Count > 1)
        {
            for (int i = 0; i < resultList.Count - 1; i++)
            {
                Assert.True(resultList[i].Id >= resultList[i + 1].Id);
            }
        }

        // Verify AsNoTracking is applied (entities should not be tracked)
        Assert.All(resultList, x => Assert.Equal(Microsoft.EntityFrameworkCore.EntityState.Detached, _dbContext.Entry(x).State));
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFormTypeCategory_WhenValidCategory()
    {
        // Arrange
        var category = _formTypeCategoryFixture.FormTypeCategoryDto;
        category.Name = "Test Category";
        category.FormId = Guid.NewGuid().ToString();
        category.FormName = "Test Form";
        category.FormTypeId = Guid.NewGuid().ToString();
        category.FormTypeName = "Test Form Type";
        category.Logo = "test-logo-data";
        category.Version = "1.0";
        category.Properties = "{\"key\":\"value\"}";
        category.FormVersion = "1.0";

        // Act
        var result = await _repository.AddAsync(category);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(category.Name, result.Name);
        Assert.Equal(category.FormId, result.FormId);
        Assert.Equal(category.FormTypeId, result.FormTypeId);
        Assert.Single(_dbContext.FormTypeCategories);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenCategoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsCategory_WhenExists()
    {
        // Arrange
        _formTypeCategoryFixture.FormTypeCategoryDto.Id = 1;
        await _dbContext.FormTypeCategories.AddAsync(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_formTypeCategoryFixture.FormTypeCategoryDto.Id, result.Id);
        Assert.Equal(_formTypeCategoryFixture.FormTypeCategoryDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateCategory_WhenValidCategory()
    {
        // Arrange
        _dbContext.FormTypeCategories.Add(_formTypeCategoryFixture.FormTypeCategoryDto);
        await _dbContext.SaveChangesAsync();

        _formTypeCategoryFixture.FormTypeCategoryDto.Name = "Updated Category Name";
        _formTypeCategoryFixture.FormTypeCategoryDto.FormName = "Updated Form Name";
        _formTypeCategoryFixture.FormTypeCategoryDto.Version = "2.0";

        // Act
        var result = await _repository.UpdateAsync(_formTypeCategoryFixture.FormTypeCategoryDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Category Name", result.Name);
        Assert.Equal("Updated Form Name", result.FormName);
        Assert.Equal("2.0", result.Version);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenCategoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsActiveCategories_WhenCategoriesExist()
    {
        // Arrange
        await _dbContext.FormTypeCategories.AddRangeAsync(_formTypeCategoryFixture.FormTypeCategoryList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion
}
