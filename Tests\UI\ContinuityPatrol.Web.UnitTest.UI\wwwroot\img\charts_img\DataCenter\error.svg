<svg width="83" height="83" viewBox="0 0 83 83" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2029_20834)">
<g filter="url(#filter0_d_2029_20834)">
<ellipse cx="41.6059" cy="41.0596" rx="36.6059" ry="36.0596" fill="white"/>
<path d="M77.7119 41.0596C77.7119 60.6915 61.5538 76.6191 41.6059 76.6191C21.6581 76.6191 5.5 60.6915 5.5 41.0596C5.5 21.4276 21.6581 5.5 41.6059 5.5C61.5538 5.5 77.7119 21.4276 77.7119 41.0596Z" stroke="#E3E3E3" stroke-opacity="0.5"/>
</g>
<path d="M42.1405 54.1615C49.3763 54.1615 55.242 48.5377 55.242 41.6003C55.242 34.6629 49.3763 29.0391 42.1405 29.0391C34.9048 29.0391 29.0391 34.6629 29.0391 41.6003C29.0391 48.5377 34.9048 54.1615 42.1405 54.1615Z" fill="white" stroke="#FF0000" stroke-width="1.5"/>
<path d="M38 37L47 46M47 37L38 46" stroke="#FF0000" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_d_2029_20834" x="-1" y="0" width="85.2109" height="84.1191" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2029_20834"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2029_20834" result="shape"/>
</filter>
<clipPath id="clip0_2029_20834">
<rect width="83" height="83" fill="white"/>
</clipPath>
</defs>
</svg>
