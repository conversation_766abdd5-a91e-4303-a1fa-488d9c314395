﻿namespace ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Create;

public class CreateMsSqlNativeLogShippingMonitorStatusCommandHandler : IRequestHandler<
    CreateMsSqlNativeLogShippingMonitorStatusCommand, CreateMssqlNativeLogShippingMonitorStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IMsSqlNativeLogShippingMonitorStatusRepository _mssqlNativeLogShippingMonitorStatusRepository;

    public CreateMsSqlNativeLogShippingMonitorStatusCommandHandler(IMapper mapper,
        IMsSqlNativeLogShippingMonitorStatusRepository mssqlNativeLogShippingMonitorStatusRepository)
    {
        _mapper = mapper;
        _mssqlNativeLogShippingMonitorStatusRepository = mssqlNativeLogShippingMonitorStatusRepository;
    }

    public async Task<CreateMssqlNativeLogShippingMonitorStatusResponse> Handle(
        CreateMsSqlNativeLogShippingMonitorStatusCommand request, CancellationToken cancellationToken)
    {
        var mssqlNativeLogShippingMonitorStatus = _mapper.Map<MsSqlNativeLogShippingMonitorStatus>(request);

        mssqlNativeLogShippingMonitorStatus =
            await _mssqlNativeLogShippingMonitorStatusRepository.AddAsync(mssqlNativeLogShippingMonitorStatus);

        var response = new CreateMssqlNativeLogShippingMonitorStatusResponse
        {
            Message = Message.Create(nameof(MsSqlNativeLogShippingMonitorStatus),
                mssqlNativeLogShippingMonitorStatus.ReferenceId),

            Id = mssqlNativeLogShippingMonitorStatus.ReferenceId
        };

        return response;
    }
}