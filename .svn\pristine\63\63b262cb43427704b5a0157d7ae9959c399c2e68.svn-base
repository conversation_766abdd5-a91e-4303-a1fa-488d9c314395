﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertRepository : BaseRepository<Alert>, IAlertRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext,
        loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<int> GetAlertByMaxId()
    {
        var lastAlert = await _dbContext.Alerts
            .AsNoTracking()
            .Active()
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync();

        var lastAlertId = lastAlert?.Id ?? 0;

        return lastAlertId;
    }


    public override async Task<Alert> GetByIdAsync(int id)
    {
        if (id != 0)
            return await _dbContext.Alerts.FindAsync(id);
        else
            return await _dbContext.Alerts.AsNoTracking().FirstOrDefaultAsync();
    }

    public override IQueryable<Alert> GetPaginatedQuery()
    {
        var alters = _loggedInUserService.IsParent
            ? base.QueryAll(infra => infra.IsActive).AsNoTracking()
            : base.QueryAll(x => x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.IsActive).AsNoTracking();

        if (_loggedInUserService.IsParent)
        {
            return _loggedInUserService.IsAllInfra
                ? alters.AsNoTracking().OrderByDescending(x => x.Id)
                : GetPaginatedInfraObject(alters).AsNoTracking().OrderByDescending(x => x.Id);
        }

        var infraObject = new List<InfraObject>();

        var alertList = alters.ToList();

        var uniqueInfraId = alertList.DistinctBy(infra => infra.InfraObjectId).ToList();

        uniqueInfraId.ForEach(infra =>
        {
            var infraDto = _dbContext.InfraObjects
                .FirstOrDefault(x => x.ReferenceId.Equals(infra.InfraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

            if (infraDto != null) infraObject.Add(infraDto);
        });

        var infraId = infraObject.Select(x => x.ReferenceId).ToList();

        var alertsDto = alters.Where(x => infraId.Contains(x.InfraObjectId)).AsNoTracking()
            .OrderByDescending(x => x.Id);

        return alertsDto;
    }

    public Task<List<Alert>> GetAlertByInfraObjectId(string infraObjectId, string entityId)
    {
        return _loggedInUserService.IsParent
            ? base.FilterBy(e => e.InfraObjectId.Equals(infraObjectId) && e.EntityId.Equals(entityId) && e.IsActive)
                .Active().AsNoTracking()
                .OrderByDescending(e => e.LastModifiedDate)
                .ToListAsync()
           : base.FilterBy(x => x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)
                                && x.InfraObjectId.Equals(infraObjectId)
                                && x.EntityId.Equals(entityId) && x.IsActive).OrderByDescending(x => x.LastModifiedDate).AsNoTracking().ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.EntityId.Equals(entityId) && e.IsActive)
        //.OrderByDescending(e => e.LastModifiedDate)
        //.ToListAsync();
    }

    public Task<List<Alert>> GetAlertByClientAlertId(string clientAlertId)
    {
        return _loggedInUserService.IsParent
            ? base.FilterBy(e => e.ClientAlertId.Equals(clientAlertId)).Active().AsNoTracking().ToListAsync()
            : base.FilterBy(x => x.ClientAlertId.Equals(clientAlertId)
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().ToListAsync();
        //_dbContext.Alerts
        //.Active()
        //.Where(e => e.ClientAlertId.Equals(clientAlertId))
        //.ToListAsync();
    }


    public async Task<List<Alert>> GetLastAlertByInfraObject(string infraObjectId, DateTime userLastAlertDate,
        DateTime alertDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= userLastAlertDate
                && x.LastModifiedDate <= alertDate).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= userLastAlertDate &&
                       x.LastModifiedDate <= alertDate && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= userLastAlertDate &&
        //            x.LastModifiedDate <= alertDate)
        //.ToListAsync();
    }

    public async Task<List<Alert>> GetByAlertId(int userLastAlertId, DateTime createdDate, DateTime lastModifiedDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.ReferenceId.Equals(userLastAlertId) ||
                        (x.CreatedDate >= createdDate && x.LastModifiedDate <= lastModifiedDate)).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.ReferenceId.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)
                        || (x.CreatedDate >= createdDate && x.LastModifiedDate <= lastModifiedDate)).Active().AsNoTracking().ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(x => x.ReferenceId.Equals(userLastAlertId) ||
        //            (x.CreatedDate >= createdDate && x.LastModifiedDate <= lastModifiedDate))
        //.ToListAsync();
    }

    public IQueryable<Alert> GetAlertByUserLastAlertIdAndDate(int userLastAlertId)
    {
        var alertPreviousList = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Id < userLastAlertId).Active().AsNoTracking().ToList()
            : base.FilterBy(x => x.Id < userLastAlertId
                            && x.CompanyId !=null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().ToList();
        //_dbContext.Alerts.Active().Where(x => x.Id < userLastAlertId).ToList();

        if (alertPreviousList.Count > 0)
            return _loggedInUserService.IsParent ?
                base.FilterBy(x => x.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(x => x.Id).Skip(1)
                : base.FilterBy(x => x.Id.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Active().AsNoTracking()
                .OrderBy(x => x.Id)
                .Skip(1);

        //_dbContext.Alerts.Active()
        //.Where(x => x.Id >= userLastAlertId)
        //.OrderBy(x => x.Id)
        //.Skip(1);
        return _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(x => x.Id)
            : base.FilterBy(x => x.Id.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Active().AsNoTracking()
            .OrderBy(x => x.Id);

        //_dbContext.Alerts.Active()
        //.Where(x => x.Id >= userLastAlertId)
        //.OrderBy(x => x.Id);
    }

    public IQueryable<Alert> GetAlertByUserLastInfraObjectIdAndDate(string infraObjectId, DateTime createdDate)
    {
        return _loggedInUserService.IsParent
             ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate
                        && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
        //_dbContext.Alerts.Active()
        //.Where(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate)
        //.OrderByDescending(x => x.Id);
    }

    public async Task<List<Alert>> GetAlertByUserLastAlertId(int userLastAlertId)
    {
        var alertPreviousList = _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.Id < userLastAlertId).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.Id < userLastAlertId
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                    .Active().AsNoTracking().ToListAsync();
        //_dbContext.Alerts.Active().Where(x => x.Id < userLastAlertId).ToList();

        if (alertPreviousList.Count > 0)
            return _loggedInUserService.IsParent
                ? await base.FilterBy(a => a.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(a => a.Id).Skip(1).ToListAsync()
                : await base.FilterBy(a => a.Id >= userLastAlertId && a.CompanyId != null && a.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Active().AsNoTracking()
                .OrderBy(a => a.Id)
                .Skip(1)
                .ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(a => a.Id >= userLastAlertId)
        //.OrderBy(a => a.Id)
        //.Skip(1)
        //.ToListAsync();
        return _loggedInUserService.IsParent
            ? await base.FilterBy(a => a.Id >= userLastAlertId).OrderBy(a => a.Id).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(a => a.Id >= userLastAlertId && a.CompanyId != null && a.CompanyId.Equals(_loggedInUserService.CompanyId))
              .Active().AsNoTracking()
              .OrderBy(a => a.Id)
              .ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(a => a.Id >= userLastAlertId)
        //.OrderBy(a => a.Id)
        //.ToListAsync();
    }

    public async Task<List<Alert>> GetAlertByUserLastInfraObjectId(string infraObjectId, DateTime createdDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate
             && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking()
            .Active()
            .ToListAsync();
        //_dbContext.Alerts.Active()
        //.Where(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate)
        // .ToListAsync();

    }

    public async Task<List<Alert>> GetAlertListFilterByDate(string startDate, string endDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).AsNoTracking().Active().ToListAsync()
            : await base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().Active().ToListAsync();
        //_dbContext.Alerts
        //.Where(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime())
        //.ToListAsync();

    }

    //
    public IQueryable<Alert> GetPaginatedByInfraObjectId(string infraObjectId)
    {
        var alert = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId)
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
        //_dbContext.Alerts.Active().AsNoTracking().Where(x => x.InfraObjectId.Equals(infraObjectId))
        //.OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alert : GetPaginatedInfraObject(alert);
    }

    public IQueryable<Alert> GetPaginatedBySeverity(string severity)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) &&
                     x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                        .Active().AsNoTracking()
                        .OrderByDescending(x => x.Id);
        //_dbContext.Alerts.Active().AsNoTracking().Where(x => x.Severity.Equals(severity))
        // .OrderByDescending(x => x.Id);
        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByType(string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Type.Equals(type) &&
                     x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Active().AsNoTracking().OrderByDescending(x => x.Id);
        //_dbContext.Alerts.Active().AsNoTracking().Where(x => x.Type.Equals(type))
        //.OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByCreateAndEndDate(string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                      && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
        //_dbContext.Alerts
        //.Where(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime())
        //.OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts
            : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndSeverity(string infraObjectId, string severity)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.CompanyId != null &&
                 x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking().Where(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity))
        //        .OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndType(string infraObjectId, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type)
                && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking().Where(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type))
        //        .OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndDates(string infraObjectId, string startDate, string endDate)
    {

        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().Active().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectSeverityType(string infraObjectId, string severity, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) && x.CompanyId != null
                 && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type))
        //        .OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectWithSeverityAndDates(string infraObjectId, string severity,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() &&
                            x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                            .Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) &&
        //                    x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectWithTypeAndDates(string infraObjectId, string type,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null &&
                             x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type) &&
        //                    x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedByAllUniqueData(string infraObjectId, string severity, string type,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) &&
                   x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking()
                .OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) &&
                   x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime() &&
                   x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking()
                .OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking().Where(x =>
        //            x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) &&
        //            x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime())
        //        .OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedBySeverityAndType(string severity, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) && x.CompanyId != null
                 && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking().Where(x => x.Severity.Equals(severity) && x.Type.Equals(type))
        //        .OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedSeverityByDates(string severity, string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.Severity.Equals(severity) && x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedBySeverityTypeAndDates(string severity, string type, string startDate,
        string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.Severity.Equals(severity) && x.Type.Equals(type) &&
        //                    x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public IQueryable<Alert> GetPaginatedTypeAndDate(string type, string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                           x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Type.Equals(type) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                           x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
        //var alerts = _dbContext.Alerts.Active();

        //return _loggedInUserService.IsAllInfra
        //    ? alerts.AsNoTracking()
        //        .Where(x => x.Type.Equals(type) && x.CreatedDate.Date >= startDate.ToDateTime() &&
        //                    x.LastModifiedDate.Date <= endDate.ToDateTime()).OrderByDescending(x => x.Id)
        //    : GetPaginatedInfraObject(alerts).AsNoTracking().OrderByDescending(x => x.Id);
    }
    //Filter
    public IQueryable<Alert> GetPaginatedInfraObject(IQueryable<Alert> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(businessService => businessService.AssignedBusinessFunctions)
            .SelectMany(businessFunction => businessFunction.AssignedInfraObjects)
            .Select(infraObject => infraObject.Id);

        return infraObjects.Where(infraObject => assignedInfraObjectIds.Contains(infraObject.InfraObjectId));
    }

    //public  async Task<PaginatedResult<Alert>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Alert> specification,Expression<Func<Alert,bool>> expression,string sortColumn,string sortOrder)
    //{
    //    if (_loggedInUserService.IsParent)
    //    {
    //        return await (_loggedInUserService.IsAllInfra
    //            ? Entities.Specify(specification).Where(expression).DescOrderById()
    //            : GetPaginatedInfraObject(Entities.Specify(specification).Where(expression).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);

    //    }
    //    return  await (_loggedInUserService.IsAllInfra
    //            ? Entities.Specify(specification).Where(expression).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
    //            : GetPaginatedInfraObject(Entities.Specify(specification).Where(expression).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

    //}

    public async Task<(PaginatedResult<Alert>,Dictionary<string,int>)> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Alert> specification, Expression<Func<Alert, bool>> expression, string sortColumn, string sortOrder)
    {
        #region Old Code

        //if (_loggedInUserService.IsParent)
        //{
        //    var res = _loggedInUserService.IsAllInfra
        //        ? Entities.DescOrderById().Specify(specification).Where(expression)
        //        : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression)).AsQueryable();

        //    var sever = res.ToList().GroupBy(x => x.Severity).ToDictionary(x => x.Key, x => x.Count());

        //    var paginat = await res.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        //    return (paginat, sever);
        //}
        //var reesiCom = _loggedInUserService.IsAllInfra
        //        ? Entities.DescOrderById().Specify(specification).Where(expression).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
        //        : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).AsQueryable();


        //var result = reesiCom.ToList().GroupBy(x => x.Severity).ToDictionary(x => x.Key, x => x.Count());

        //var pagi = await reesiCom.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        //return (pagi, result);
        #endregion
        IQueryable<Alert> query;

        if (_loggedInUserService.IsParent)
        {
            query = _loggedInUserService.IsAllInfra
                ? Entities.DescOrderById().Specify(specification).Where(expression) // Parent & AllInfra = True
                : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression)).AsQueryable(); // Parent = True, AllInfra = False
        }
        else
        {
            query = _loggedInUserService.IsAllInfra
                ? Entities.DescOrderById().Specify(specification).Where(expression)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId) // Parent = False, AllInfra = True
                : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId)).AsQueryable(); // Parent = False, AllInfra = False
        }

        var severityCounts = await query
            .GroupBy(x => x.Severity)
            .Select(g => new { g.Key, Count = g.Count() })
            .ToDictionaryAsync(g => g.Key, g => g.Count);

        var paginatedResult = await query.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        return (paginatedResult, severityCounts);
    }
}