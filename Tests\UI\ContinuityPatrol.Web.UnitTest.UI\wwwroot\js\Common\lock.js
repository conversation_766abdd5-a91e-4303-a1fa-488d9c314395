﻿
$("#Login").on("click", async function () {
    let loginUserName = $("#txtLoginName").data("loginname").toLowerCase();
    let plainPassword = $("#txtPassword").val(); 
    let authType = $("#txtAuthenticationType").val();
    let password = authType === 'In House' ? loginUserName + plainPassword : plainPassword;
    let encryptPassword = await EncryptPasswordForLock(password); 
    let token = $('input[name="__RequestVerificationToken"]').val();
    let isPassword = validateInput($("#txtPassword").val(), 'Enter password', $("#password_error"));

    let data = {
        UserName: loginUserName,
        Password: encryptPassword,
        AuthenticationType: authType
    };

    if (isPassword) {
        await $.ajax({
            type: "POST",
            url: RootUrl + "Admin/User/LockLogin",
            data: JSON.stringify(data),
            contentType: "application/json",
            headers: {
                'RequestVerificationToken': token
            },
            success: function (result) {
                if (result?.success) {
                    if (result?.validCount <= 3) {
                        window.location.href = document.referrer;
                    }
                } else {
                    if (result?.validCount < 3) {
                        notificationLockAlert("warning", "Invalid Credential");
                        $("#txtPassword").val("");
                    } else {
                        window.location.href = '/Account/Logout';
                    }
                }
            }
        });
    }
});

$("#txtPassword").on("input", async function (e) {
    if (e.type === "input") validate($(this).val(), "Enter password", $("#password_error"));
   
});
$("#txtPassword").on("keypress", async function (e) {
        if (e.which == 13) {
           $("#Login").trigger("click")
       }
   });
function validate(value, text, errorElement) {
    const hasError = !value;
    errorElement.text(hasError ? text : "").toggleClass('field-validation-error', hasError);
    return !hasError;
}

function validateInput(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('.feild-validation-error');
        return true;
    }
};

const EncryptPasswordForLock = async (text) => {

    const generateKey = async () => {
        const key = await window.crypto.subtle.generateKey({
            name: 'AES-GCM',
            length: 256
        },
            true, [
            'encrypt',
            'decrypt'
        ]);
        const exportedKey = await window.crypto.subtle.exportKey(
            'raw',
            key,
        );
        return bufferToBase64(exportedKey);
    }

    // arrayBuffer to base64
    const bufferToBase64 = (arrayBuffer) => {
        return window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    }

    // load a base64 encoded key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const cryptGcm = async (base64Key, bytes) => {
        const key = await loadKey(base64Key);
        const iv = window.crypto.getRandomValues(new Uint8Array(12));
        const algorithm = {
            iv,
            name: 'AES-GCM'
        };
        const cipherData = await window.crypto.subtle.encrypt(
            algorithm,
            key,
            bytes
        );

        // prepend the random IV bytes to raw cipherdata
        const cipherText = concatArrayBuffers(iv.buffer, cipherData);
        return bufferToBase64(cipherText);
    }

    // concatenate two array buffers
    const concatArrayBuffers = (buffer1, buffer2) => {
        let tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);
        tmp.set(new Uint8Array(buffer1), 0);
        tmp.set(new Uint8Array(buffer2), buffer1.byteLength);
        return tmp.buffer;
    }

    const plaintext = text;
    const plaintextBytes = (new TextEncoder()).encode(plaintext, 'utf-8');
    const encryptionKey = await generateKey();
    const ciphertext = await cryptGcm(encryptionKey, plaintextBytes);
    // console.log("plaintext: ", plaintext);
    // console.log("encryptionKey (base64):", encryptionKey);
    // console.log("ciphertext (base64):", ciphertext);

    if (encryptionKey && ciphertext) {
        return encryptionKey + "$" + ciphertext;
    }


}

const notificationLockAlert = (toastClass, data, mode = '') => {
    $('#mytoastrdata').toast('hide');
    $('#alertClass, #icon_Detail').removeClass();
    let alertClass = toastClass === "success" ? "success-toast" : toastClass === "warning" ? "warning-toast" : toastClass === "info" ? "info-toast"
        : toastClass === "error" || "unauthorised" ? "unauthorised-toast" : '';
    let icon = toastClass === "success" ? "cp-check" : toastClass === "warning" ? "cp-warning" : toastClass === "info" ? "cp-note"
        : toastClass === "error" ? "cp-close" : toastClass === "unauthorised" ? "cp-user" : '';


    if (mode?.toLowerCase() == 'execution') {
        $('#notificationAlertmessage').append(data)
    } else {
        $('#alertClass').addClass(alertClass)
        $("#icon_Detail").addClass(`${icon} toast_icon`)
        $('#notificationAlertmessage').text(data)
    }

    if (data) $('#mytoastrdata').toast('show');
    let hideTimeout = setTimeout(function () {
        $('#mytoastrdata').toast('hide');
    }, 2000);


    $('#mytoastrdata')
        .on('mouseenter', function () {
            clearTimeout(hideTimeout);
        })
        .on('mouseleave', function () {
            hideTimeout = setTimeout(function () {
                $('#mytoastrdata').toast('hide');
            }, 2000);
        });
}



//function validate(value, text, errorElement) {
//    if (!value) {
//        errorElement.text(text)
//            .addClass('field-validation-error')
//        return false;
//    } else {
//        errorElement.text('')
//            .removeClass('field-validation-error')
//        return true;
//    }
//}

//$(document).ready(function () {   
//    let baseUrl = document.referrer.split("https://localhost:7079")[1];
//    if (baseUrl && !baseUrl.toLowerCase().includes("/admin/user/lock")) {
//        sessionStorage.setItem("baseUrl", baseUrl); 
//    }
//});

//let decryptpassword = "";


//$("#txtPassword").on("input", function (event) {
//    const value = $(this).val()
//    const errorElement = $("#password_error")
  
//    validate(value, "Enter password", errorElement)
//});
//$("#txtPassword").on("keypress",async function(e) {
//    if (e.which == 13) {
//        $("#Login").trigger("click")
//    }
//});
//async function Lock_Password(loginName, password) {
//    let loginUserName = $("#txtLoginName").data("loginname").toLowerCase()
//    $("#txtLoginName").val(loginUserName)
//    let name = $("#" + loginName).val();
//    let plainPassword = $("#" + password).val();
//    let passwordcheck = validate($("#txtPassword").val(), "Enter password", $("#password_error"))
   
//    if (plainPassword != "" && plainPassword.length > 0 && plainPassword.length < 64 && name != "" && name.length > 0) {

//        let password = $("#txtAuthenticationType").val() === 'In House' ? name + plainPassword : plainPassword

//        let encryptPassword = await EncryptPasswordForLock(password)
//        $("#txtPassword").attr("type", "password");
//        $(".cp-password-hide").removeClass("cp-password-hide").addClass("cp-password-visible");
//        $("#txtPassword").val(encryptPassword);

//        $("#txtPassword").attr("type", "password");
//        $('.toggle-password i').removeClass('fs-6');
//        $("#txtPassword").siblings('.toggle-password').find('i').addClass('cp-password-visible fs-6');
//        let form = $("#btnlock")
//        let url = sessionStorage.getItem("baseUrl");     
//        $('#txtUrl').val(url);           
//        if (passwordcheck) {
//            form.trigger("submit");
//        }
//    }
// decryptpassword = $("#txtPassword").val()  
//}

//$("#Login").on("click", function () {
//    Lock_Password("txtLoginName", "txtPassword");
//})










