﻿//using ContinuityPatrol.Shared.Core.Extensions;
//using Google.Protobuf.WellKnownTypes;

//namespace ContinuityPatrol.Shared.Core.Specifications;

//public class BaseFilterSpecification<T> : Specification<T> where T : class
//{
//    public BaseFilterSpecification(string searchString, params Expression<Func<T, object>>[] searchableProperties)
//    {
//        if (string.IsNullOrEmpty(searchString))
//        {
//            // Default filter if search string is empty
//            Criteria = p => true;
//        }
//        else
//        {
//            var filters = searchString.Split(';');

//            foreach (var filter in filters)
//            {
//                var filterParts = filter.Split('=');
//                if (filterParts.Length == 2)
//                {
//                    var propertyName = filterParts[0].Trim();
//                    var value = filterParts[1].Trim().ToLower();

//                    ApplyFilter(propertyName, value);
//                }
//                else
//                {
//                    ApplyGeneralSearch(searchString);
//                }
//            }
//        }





//        //if (!string.IsNullOrEmpty(searchString))
//        //{
//        //    // Combine all searchable properties using OR logic
//        //    var parameter = Expression.Parameter(typeof(T), "e");
//        //    Expression combinedExpression = null;

//        //    foreach (var property in searchableProperties)
//        //    {
//        //        var member = Expression.Property(parameter, (property.Body as MemberExpression).Member.Name);
//        //        var searchValue = Expression.Constant(searchString);
//        //        var contains = Expression.Call(member, "Contains", null, searchValue);

//        //        combinedExpression = combinedExpression == null
//        //            ? contains
//        //            : Expression.OrElse(combinedExpression, contains);
//        //    }

//        //    if (combinedExpression != null)
//        //    {
//        //        var lambda = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);
//        //        Criteria = lambda;
//        //    }
//        //}
//        //else
//        //{
//        //    // If no search string, return all records
//        //    Criteria = e => true;
//        //}
//    }
//    private void ApplyFilter(string propertyName, string value)
//    {
//        var propertyInfo = typeof(T).GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

//        if (propertyInfo != null)
//        {
//            var parameter = Expression.Parameter(typeof(T), "p");
//            var propertyAccess = Expression.Property(parameter, propertyInfo);
//            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });

//            if (containsMethod != null)
//            {
//                var valueExpression = Expression.Constant(value);
//                var containsExpression = Expression.Call(propertyAccess, containsMethod, valueExpression);
//                var lambda = Expression.Lambda<Func<T, bool>>(containsExpression, parameter);

//                // Combine with existing Criteria (AND condition)
//                Criteria = Criteria == null ? lambda : Criteria.And(lambda);
//            }
//        }
//    }
//    private void ApplyGeneralSearch(string searchValue)
//    {
//        // Convert search value to lowercase to handle case-insensitive search
//        var value = searchValue.Trim().ToLower();

//        // Get all string properties of the entity using reflection
//        var stringProperties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
//            .Where(p => p.PropertyType == typeof(string))
//            .ToList();

//        // Create the expression parameter (p) representing the entity
//        var parameter = Expression.Parameter(typeof(T), "p");

//        // Start with a false condition, which will be updated with OR conditions
//        Expression combinedExpression = Expression.Constant(false);

//        foreach (var propertyInfo in stringProperties)
//        {
//            // Access the property value (e.g., p.WorkflowName)
//            var propertyAccess = Expression.Property(parameter, propertyInfo);

//            // Create an expression for the value to compare against
//            var valueExpression = Expression.Constant(value, typeof(string));

//            // Create the "Contains" method expression for case-insensitive comparison
//            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });

//            // Generate the comparison expression for "Contains"
//            var containsExpression = Expression.Call(propertyAccess, containsMethod, valueExpression);

//            // Combine the condition with OR (to match any field)
//            combinedExpression = Expression.OrElse(combinedExpression, containsExpression);
//        }

//        // Create the lambda expression
//        var lambda = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);

//        // Apply the combined filter to Criteria
//        Criteria = Criteria == null ? lambda : Criteria.And(lambda);
//    }


//}