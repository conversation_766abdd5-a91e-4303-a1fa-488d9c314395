﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.InfraReplicationMapping.Event.Delete;

public class InfraReplicationMappingDeletedEventHandler : INotificationHandler<InfraReplicationMappingDeletedEvent>
{
    private readonly ILogger<InfraReplicationMappingDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public InfraReplicationMappingDeletedEventHandler(ILogger<InfraReplicationMappingDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(InfraReplicationMappingDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.InfraReplicationMapping}",
            Entity = Modules.InfraReplicationMapping.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Infra-Replication Mapping '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Infra-Replication Mapping '{deletedEvent.Name}' deleted successfully.");
    }
}