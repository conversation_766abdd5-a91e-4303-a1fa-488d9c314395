﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class PageSolutionMappingRepository : BaseRepository<PageSolutionMapping>, IPageSolutionMappingRepository
{
    private readonly ApplicationDbContext _dbContext;
    
    public PageSolutionMappingRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;        
    }

    public Task<PageSolutionMapping> GetByReplicationCategoryTypeId(string replicationCategoryTypeId)
    {
        return Task.FromResult(_dbContext.PageSolutionMappings.Active().FirstOrDefault(e => e.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId)));
    }
    public Task<PageSolutionMapping> GetByReplicationTypeId(string replicationTypeId)
    {
        return Task.FromResult(_dbContext.PageSolutionMappings.Active().FirstOrDefault(e => e.ReplicationTypeId.Equals(replicationTypeId)));
    }

    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public override async Task<PaginatedResult<PageSolutionMapping>> PaginatedListAllAsync(int pagenumber, int pageSize, Specification<PageSolutionMapping> specification, string sortColumn, string sortOrder)
    {
        return await FilterRequiredField(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pagenumber, pageSize, sortColumn, sortOrder);
    }
    
    public override async Task<IReadOnlyList<PageSolutionMapping>> ListAllAsync()
    {
        return await FilterRequiredField(Entities.AsNoTracking().DescOrderById()).ToListAsync();
    }
    

    private IQueryable<PageSolutionMapping>FilterRequiredField(IQueryable<PageSolutionMapping> pageSolutionMappings)
    {
        return pageSolutionMappings.Select(x => new PageSolutionMapping
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            PageBuilderId=x.PageBuilderId,
            PageBuilderName=x.PageBuilderName,
            MonitorType=x.MonitorType,
            Type=x.Type,
            TypeName=x.TypeName,
            SubTypeId=x.SubTypeId,
            SubType=x.SubType,
            ReplicationTypeId=x.ReplicationTypeId,
            ReplicationTypeName=x.ReplicationTypeName,
            ReplicationCategoryType=x.ReplicationCategoryType,
            ReplicationCategoryTypeId = x.ReplicationCategoryTypeId
        });
    }
}
