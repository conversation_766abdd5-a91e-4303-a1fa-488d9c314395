﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Commands;

public class DeleteBusinessFunctionTests : IClassFixture<BusinessFunctionFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
    private readonly DeleteBusinessFunctionCommandHandler _handler;

    public DeleteBusinessFunctionTests(BusinessFunctionFixture businessFunctionFixture, InfraObjectFixture infraObjectFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.DeleteBusinessFunctionRepository(_businessFunctionFixture.BusinessFunctions);

        var mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();
        mockInfraObjectViewRepository.Setup(x => x.GetInfraObjectByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync(new List<Domain.Views.InfraObjectView>());

        _handler = new DeleteBusinessFunctionCommandHandler(_mockBusinessFunctionRepository.Object, mockPublisher.Object, mockInfraObjectViewRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_BusinessFunctionDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessFunctionFixture.BusinessFunctions[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteBusinessFunctionCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteBusinessFunctionResponse_When_BusinessFunctionDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessFunctionFixture.BusinessFunctions[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteBusinessFunctionCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteBusinessFunctionResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_BusinessFunctionDeleted()
    {
        var validGuid = Guid.NewGuid();

        _businessFunctionFixture.BusinessFunctions[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteBusinessFunctionCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var businessFunction = await _mockBusinessFunctionRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        businessFunction.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessFunctionId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteBusinessFunctionCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _businessFunctionFixture.BusinessFunctions[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteBusinessFunctionCommand { Id = _businessFunctionFixture.BusinessFunctions[0].ReferenceId }, CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessFunctionRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessFunction>()), Times.Once);
    }

}