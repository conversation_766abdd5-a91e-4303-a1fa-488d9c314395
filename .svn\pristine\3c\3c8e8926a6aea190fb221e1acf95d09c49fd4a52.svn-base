﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Commands;

public class UpdateTeamMasterTests : IClassFixture<TeamMasterFixture>
{
    private readonly TeamMasterFixture _teamMasterFixture;

    private readonly Mock<ITeamMasterRepository> _mockTeamMasterRepository;

    private readonly UpdateTeamMasterCommandHandler _handler;

    public UpdateTeamMasterTests(TeamMasterFixture teamMasterFixture)
    {
        _teamMasterFixture = teamMasterFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockTeamMasterRepository = TeamMasterRepositoryMocks.UpdateTeamMasterRepository(_teamMasterFixture.TeamMasters);

        _handler = new UpdateTeamMasterCommandHandler(_teamMasterFixture.Mapper, _mockTeamMasterRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidTeamMaster_UpdateReferenceIdAsync_ToTeamMastersRepo()
    {
        _teamMasterFixture.UpdateTeamMasterCommand.Id = _teamMasterFixture.TeamMasters[0].ReferenceId;

        var result = await _handler.Handle(_teamMasterFixture.UpdateTeamMasterCommand, CancellationToken.None);

        var teamMaster = await _mockTeamMasterRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_teamMasterFixture.UpdateTeamMasterCommand.GroupName, teamMaster.GroupName);
    }

    [Fact]
    public async Task Handle_Return_ValidTeamMasterResponse_WhenUpdate_TeamMaster()
    {
        _teamMasterFixture.UpdateTeamMasterCommand.Id = _teamMasterFixture.TeamMasters[0].ReferenceId;

        var result = await _handler.Handle(_teamMasterFixture.UpdateTeamMasterCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateTeamMasterResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_teamMasterFixture.UpdateTeamMasterCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidTeamMasterId()
    {
        _teamMasterFixture.UpdateTeamMasterCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_teamMasterFixture.UpdateTeamMasterCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _teamMasterFixture.UpdateTeamMasterCommand.Id = _teamMasterFixture.TeamMasters[0].ReferenceId;

        await _handler.Handle(_teamMasterFixture.UpdateTeamMasterCommand, CancellationToken.None);

        _mockTeamMasterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockTeamMasterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.TeamMaster>()), Times.Once);
    }
}
