﻿using ContinuityPatrol.Web.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace ContinuityPatrol.Web.UnitTests.TagHelpers;

public class MultiLoginTagHelperTests
{
    [Fact]
    public void Process_SetsCorrectTagNameAndContent_WithUserName()
    {
        // Arrange
        var tagHelper = new MultiLoginTagHelper
        {
            UserName = "TestUser"
        };

        var context = new TagHelperContext(
            tagName: "multilogin",
            allAttributes: new TagHelperAttributeList
            {
                { "user-name", "TestUser" }
            },
            items: new Dictionary<object, object>(),
            uniqueId: "test");

        var output = new TagHelperOutput(
            "multilogin",
            attributes: new TagHelperAttributeList(),
            getChildContentAsync: (useCachedResult, encoder) =>
            {
                var tagHelperContent = new DefaultTagHelperContent();
                tagHelperContent.SetContent("");
                return Task.FromResult<TagHelperContent>(tagHelperContent);
            });

        // Act
        tagHelper.Process(context, output);

        // Assert
        Assert.Equal("div", output.TagName);
        var html = output.Content.GetContent();
        Assert.Contains("TestUser", html);
        Assert.Contains("already logged in from another session", html);
        Assert.Contains("modal fade", html);
    }

    [Fact]
    public void Process_SetsDefaultMessage_WhenUserNameIsNull()
    {
        // Arrange
        var tagHelper = new MultiLoginTagHelper
        {
            UserName = null
        };

        var context = new TagHelperContext(
            tagName: "multilogin",
            allAttributes: new TagHelperAttributeList(),
            items: new Dictionary<object, object>(),
            uniqueId: "test");

        var output = new TagHelperOutput(
            "multilogin",
            attributes: new TagHelperAttributeList(),
            getChildContentAsync: (useCachedResult, encoder) =>
            {
                var tagHelperContent = new DefaultTagHelperContent();
                tagHelperContent.SetContent("");
                return Task.FromResult<TagHelperContent>(tagHelperContent);
            });

        // Act
        tagHelper.Process(context, output);

        // Assert
        Assert.Equal("div", output.TagName);
        var html = output.Content.GetContent();
        Assert.Contains("User already logged in same browser session", html);
    }
}