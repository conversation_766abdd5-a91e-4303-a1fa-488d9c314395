﻿using ContinuityPatrol.Application.Features.FormHistory.Commands.Create;
using ContinuityPatrol.Application.Features.FormHistory.Commands.Update;
using ContinuityPatrol.Application.Features.FormHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormHistory.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.FormHistoryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class FormHistoryController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<FormHistoryListVm>>> GetFormHistories()
    {
        Logger.LogDebug("Get All FormHistories");

        return Ok(await Mediator.Send(new GetFormHistoryListQuery()));
    }

    [HttpGet("formId")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<FormHistoryDetailVm>>> GetFormHistoryById(string formId,string? formVersion)
    {
        Guard.Against.InvalidGuidOrEmpty(formId, "Form History Id");

        Logger.LogDebug($"Get Form History Detail by Id '{formId}'");

        return Ok(await Mediator.Send(new GetFormHistoryDetailQuery { FormId = formId,Version = formVersion }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateFormHistoryResponse>> CreateFormHistory([FromBody] CreateFormHistoryCommand createFormHistoryCommand)
    {
        Logger.LogDebug($"Create Form History '{createFormHistoryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateFormHistory), await Mediator.Send(createFormHistoryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateFormHistoryResponse>> UpdateFormHistory([FromBody] UpdateFormHistoryCommand updateFormHistoryCommand)
    {
        Logger.LogDebug($"Update Form History '{updateFormHistoryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateFormHistoryCommand));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormHistoryCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormHistoryNamesCacheKey };

        ClearCache(cacheKeys);
    }
}

