using ContinuityPatrol.Application.Features.BackUpLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUpLog.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Commands;

public class DeleteBackUpLogTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteBackUpLogCommandHandler _handler;

    public DeleteBackUpLogTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteBackUpLogCommandHandler(
            _mockBackUpLogRepository.Object,
            _mockPublisher.Object);
    }

    //[Fact]
    //public async Task Handle_DeleteBackUpLog_When_ValidCommand()
    //{
    //    // Arrange
    //    var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
    //    var command = new DeleteBackUpLogCommand { Id = existingBackUpLog.ReferenceId };

    //    // Act
    //    var result = await _handler.Handle(command, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.IsActive.ShouldBeFalse();

    //    _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(existingBackUpLog.ReferenceId), Times.Once);
    //    _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => b.IsActive == false)), Times.Once);
    //    _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    //}

    [Fact]
    public async Task Handle_DeleteActiveBackUpLog_When_ValidCommand()
    {
        // Arrange
        var activeBackUpLog = _backUpLogFixture.BackUpLogs.First(x => x.IsActive);
        var command = new DeleteBackUpLogCommand { Id = activeBackUpLog.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.ReferenceId == activeBackUpLog.ReferenceId && 
            b.IsActive == false)), Times.Once);
    }

    //[Fact]
    //public async Task Handle_DeleteFullBackupLog_When_ValidCommand()
    //{
    //    // Arrange
    //    var fullBackupLog = _backUpLogFixture.BackUpLogs.First(x => x.Type == "Full");
    //    var command = new DeleteBackUpLogCommand { Id = fullBackupLog.ReferenceId };

    //    // Act
    //    var result = await _handler.Handle(command, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.IsActive.ShouldBeFalse();

    //    _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
    //        b.Type == "Full" && 
    //        b.IsActive == false)), Times.Once);
    //}

    [Fact]
    public async Task Handle_DeleteDifferentialBackupLog_When_ValidCommand()
    {
        // Arrange
        // Add a differential backup log to the fixture
        var differentialBackupLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "DiffServer",
            DatabaseName = "DiffDatabase",
            UserName = "DiffUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\DiffDatabase.bak",
            Type = "Differential",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(differentialBackupLog);

        var command = new DeleteBackUpLogCommand { Id = differentialBackupLog.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Type == "Differential" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteTransactionLogBackup_When_ValidCommand()
    {
        // Arrange
        // Add a transaction log backup to the fixture
        var transactionLogBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "LogServer",
            DatabaseName = "LogDatabase",
            UserName = "LogUser",
            IsLocalServer = true,
            IsBackUpServer = true,
            BackUpPath = @"E:\Logs\LogDatabase.trn",
            Type = "Transaction Log",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(transactionLogBackup);

        var command = new DeleteBackUpLogCommand { Id = transactionLogBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Type == "Transaction Log" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteFailedBackupLog_When_ValidCommand()
    {
        // Arrange
        // Add a failed backup log to the fixture
        var failedBackupLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "FailedServer",
            DatabaseName = "FailedDatabase",
            UserName = "FailedUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\FailedDatabase.bak",
            Type = "Full",
            Status = "Failed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(failedBackupLog);

        var command = new DeleteBackUpLogCommand { Id = failedBackupLog.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Status == "Failed" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBackUpLogCommand { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpLogDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpLogIsNull()
    {
        // Arrange
        var nullId = Guid.NewGuid().ToString();
        _mockBackUpLogRepository.Setup(x => x.GetByReferenceIdAsync(nullId))
            .ReturnsAsync((Domain.Entities.BackUpLog)null);

        var command = new DeleteBackUpLogCommand { Id = nullId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBackUpLogRepository.Verify(x => x.GetByReferenceIdAsync(nullId), Times.Once);
        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Never);
    }

    //[Fact]
    //public async Task Handle_SoftDelete_When_BackUpLogDeleted()
    //{
    //    // Arrange
    //    var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
    //    var originalIsActive = existingBackUpLog.IsActive;
    //    var command = new DeleteBackUpLogCommand { Id = existingBackUpLog.ReferenceId };

    //    // Act
    //    var result = await _handler.Handle(command, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.IsActive.ShouldBeFalse();
    //    originalIsActive.ShouldBeTrue(); // Verify it was originally active

    //    // Verify soft delete (IsActive set to false, not actual deletion)
    //    _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
    //        b.ReferenceId == existingBackUpLog.ReferenceId && 
    //        b.IsActive == false)), Times.Once);
    //    _mockBackUpLogRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.BackUpLog>()), Times.Never);
    //}

    //[Fact]
    //public async Task Handle_ReturnCorrectResponse_When_BackUpLogDeleted()
    //{
    //    // Arrange
    //    var existingBackUpLog = _backUpLogFixture.BackUpLogs.First();
    //    var command = new DeleteBackUpLogCommand { Id = existingBackUpLog.ReferenceId };

    //    // Act
    //    var result = await _handler.Handle(command, CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.ShouldBeOfType<DeleteBackUpLogResponse>();
    //    result.IsActive.ShouldBeFalse();
    //}

    [Fact]
    public async Task Handle_DeleteRemoteBackupLog_When_ValidCommand()
    {
        // Arrange
        // Add a remote backup log to the fixture
        var remoteBackupLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "RemoteServer",
            DatabaseName = "RemoteDatabase",
            UserName = "RemoteUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\RemoteServer\Backups\RemoteDatabase.bak",
            Type = "Full",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(remoteBackupLog);

        var command = new DeleteBackUpLogCommand { Id = remoteBackupLog.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.IsLocalServer == false && 
            b.IsBackUpServer == true &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteInProgressBackupLog_When_ValidCommand()
    {
        // Arrange
        // Add an in-progress backup log to the fixture
        var inProgressBackupLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "ProgressServer",
            DatabaseName = "ProgressDatabase",
            UserName = "ProgressUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\ProgressDatabase.bak",
            Type = "Differential",
            Status = "In Progress",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(inProgressBackupLog);

        var command = new DeleteBackUpLogCommand { Id = inProgressBackupLog.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpLogRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUpLog>(b => 
            b.Status == "In Progress" && 
            b.IsActive == false)), Times.Once);
    }
}
