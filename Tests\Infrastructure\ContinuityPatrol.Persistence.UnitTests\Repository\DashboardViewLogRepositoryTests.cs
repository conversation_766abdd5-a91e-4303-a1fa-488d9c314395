using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DashboardViewLogRepositoryTests : IClassFixture<DashboardViewLogFixture>,IClassFixture<BusinessServiceFixture>
{
    private readonly DashboardViewLogFixture _dashboardViewLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DashboardViewLogRepository _repository;
    private readonly DashboardViewLogRepository _repositoryNotParent;
    private readonly BusinessServiceFixture _businessServiceFixture;

    public DashboardViewLogRepositoryTests(DashboardViewLogFixture dashboardViewLogFixture, BusinessServiceFixture businessServiceFixture)
    {
        _dashboardViewLogFixture = dashboardViewLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DashboardViewLogRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DashboardViewLogRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _businessServiceFixture = businessServiceFixture;

    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        var bsList=_businessServiceFixture.BusinessServiceList;

        bsList[0].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        await _dbContext.BusinessServices.AddRangeAsync(bsList);
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
         dashboardViewLog.BusinessServiceId = bsList[0].ReferenceId;
        // Act
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
        Assert.Equal(dashboardViewLog.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(dashboardViewLog.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.DashboardViewLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var bsList = _businessServiceFixture.BusinessServiceList;

        bsList[0].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        bsList[0].Name = "Updated Service Name";
        await _dbContext.BusinessServices.AddRangeAsync(bsList);
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;

        dashboardViewLog.BusinessServiceId = bsList[0].ReferenceId;

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync(); 

        dashboardViewLog.BusinessServiceName = "Updated Service Name";

        // Act
         _dbContext.DashboardViewLogs.Update(dashboardViewLog);
         await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act

        dashboardViewLog.IsActive=false;
         _dbContext.DashboardViewLogs.Update(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByIdAsync(dashboardViewLog.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.Id, result.Id);
        Assert.Equal(dashboardViewLog.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;
        dashboardViewLog.CompanyId = "ChHILD_COMPANY_123";
        dashboardViewLog.ReferenceId = Guid.NewGuid().ToString();
        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
        Assert.Equal(dashboardViewLog.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLog = _dashboardViewLogFixture.DashboardViewLogDto;

        dashboardViewLog.CompanyId = "ChHILD_COMPANY_123";
        dashboardViewLog.ReferenceId = Guid.NewGuid().ToString();

        await _dbContext.DashboardViewLogs.AddAsync(dashboardViewLog);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(dashboardViewLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLog.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dashboardViewLogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        await _repositoryNotParent.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntitiesFromYesterday()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to today (should not be included)
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today);
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDashboardViewLogByLast30daysList Tests

    [Fact]
    public async Task GetDashboardViewLogByLast30daysList_ShouldReturnEntitiesFromLast30Days_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set some logs within last 30 days
        dashboardViewLogs.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-15));
        // Set some logs older than 30 days
        dashboardViewLogs.Skip(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-35));
        
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByLast30daysList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.LastModifiedDate.Date >= DateTime.Today.AddDays(-30)));
    }

    [Fact]
    public async Task GetDashboardViewLogByLast30daysList_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set some logs within last 30 days
        dashboardViewLogs.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-15));
       
        
        await _repositoryNotParent.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repositoryNotParent.GetDashboardViewLogByLast30daysList();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region GetDashboardViewLogByInfraObjectId Tests

    [Fact]
    public async Task GetDashboardViewLogByInfraObjectId_ShouldReturnEntitiesWithMatchingInfraObjectId()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate hour to 1 for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddHours(1));
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByInfraObjectId(DashboardViewLogFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DashboardViewLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(1, x.LastModifiedDate.Hour));
    }

    [Fact]
    public async Task GetDashboardViewLogByInfraObjectId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDashboardViewLogByInfraObjectId("non-existent-infra-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDataLagByOneDayReport Tests

    [Fact]
    public async Task GetDataLagByOneDayReport_ShouldReturnEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.GetDataLagByOneDayReport(DashboardViewLogFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DashboardViewLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(DateTime.Today.AddDays(-1).Date, x.LastModifiedDate.Date));
    }

    [Fact]
    public async Task GetDataLagByOneDayReport_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        // Set LastModifiedDate to yesterday for filtering
        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        dashboardViewLogs.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        dashboardViewLogs.ForEach(x => x.BusinessFunctionId = "BF1");
        dashboardViewLogs.ForEach(x => x.BusinessServiceId = "BS1");

        dashboardViewLogs.ForEach(x => x.InfraObjectId = "INFRA_1");

        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repositoryNotParent.GetDataLagByOneDayReport(dashboardViewLogs[0].InfraObjectId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;

        // Act
        var result = await _repository.AddRangeAsync(dashboardViewLogs);

        // Assert
        Assert.Equal(dashboardViewLogs.Count, result.Count());
        Assert.Equal(dashboardViewLogs.Count, _dbContext.DashboardViewLogs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogList;
        await _repository.AddRangeAsync(dashboardViewLogs);

        // Act
        var result = await _repository.RemoveRangeAsync(dashboardViewLogs);

        // Assert
        Assert.Equal(dashboardViewLogs.Count, result.Count());
        Assert.Empty(_dbContext.DashboardViewLogs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dashboardViewLogs = _dashboardViewLogFixture.DashboardViewLogPaginationList.Take(5).ToList();

        dashboardViewLogs.ForEach(x => x.LastModifiedDate = DateTime.Today.AddDays(-1));
        // Act - Add, then update some, then delete some
        _dbContext.DashboardViewLogs.AddRange(dashboardViewLogs);

        _dbContext.SaveChanges();
        var initialCount = dashboardViewLogs.Count;
        
        var toUpdate = dashboardViewLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.DataLagValue = "00:00:44");
         _dbContext.DashboardViewLogs.UpdateRange(toUpdate);
        _dbContext.SaveChanges();

        var toDelete = dashboardViewLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

       
   // Assert
   var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.DataLagValue == "00:00:44").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
