const container = document.getElementById("container");
const svgImageObject = document.getElementById("my-circle");
const svgContent = document.getElementById("svg-content");

/* ZOOM */
container.addEventListener("wheel", handleWheelEvent);

let scale = 1;

function getValidScale(stepScale = 0.1) {
    scale += stepScale;
    if (scale < 0.1) {
        scale = 0.1;
    }
    if (scale > 10) {
        scale = 10;
    }
    return scale;
}

function handleWheelEvent(event) {
 

    zoom(event.deltaY);
}

function zoom(zoomFlow) {
    if (zoomFlow < 0) {
        scale = getValidScale(0.1);
    } else if (zoomFlow > 0) {
        scale = getValidScale(-0.1);
    }
    svgImageObject.style.transform = `scale(${scale})`;
}

function zoomIn() {
    zoom(-1);
}

function zoomOut(e, value) {
    console.log("ZOOM OUT", e, value);
    zoom(1);
}

/* - ZOOM -  */

/* PANE */
let mousePosition;
let offset;
let isGrabbed;

svgContent.addEventListener(
    "mousedown",
    function (e) {
        isGrabbed = true;
        let x = svgContent.offsetLeft - e.clientX;
        let y = svgContent.offsetTop - e.clientY;
        offset = [x, y];
    },
    true
);

svgContent.addEventListener(
    "mouseup",
    function () {
        isGrabbed = false;
    },
    true
);

svgContent.addEventListener(
    "mousemove",
    function (event) {
        event.preventDefault();
        svgContent.style.cursor = 'move';
        if (isGrabbed) {
            mousePosition = {
                x: event.clientX,
                y: event.clientY
            };
            svgContent.style.left = mousePosition.x + offset[0] + "px";
            svgContent.style.top = mousePosition.y + offset[1] + "px";
        }
    },
    true
);
/* - PANE - */

function reset() {
    isGrabbed = false;
    svgContent.style.left = 0 + "px";
    svgContent.style.top = 0 + "px";
    offset = [0, 0];
    mousePosition = { x: 0, y: 0 };
    scale = 1;
    svgImageObject.style.transform = "scale(1)";
}