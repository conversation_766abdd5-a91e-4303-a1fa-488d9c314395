﻿using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Setting.Queries;

public class GetSettingPaginatedListQueryHandlerTests : IClassFixture<SettingFixture>
{
    private readonly SettingFixture _settingFixture;
    private readonly GetSettingPaginatedListQueryHandler _handler;
    private readonly Mock<ISettingRepository> _mockSettingRepository;

    public GetSettingPaginatedListQueryHandlerTests(SettingFixture settingFixture)
    {
        _settingFixture = settingFixture;

        _settingFixture.Settings[0].SKey = "Sharp";
        _settingFixture.Settings[0].SValue = "Abdul_1";
        _settingFixture.Settings[0].LoginUserId = 123.ToString();

        _settingFixture.Settings[1].SKey = "Sharp";
        _settingFixture.Settings[1].SValue = "Abdul_2";
        _settingFixture.Settings[1].LoginUserId = 123.ToString();

        _mockSettingRepository = SettingRepositoryMocks.GetPaginatedSettingRepository(_settingFixture.Settings);
        _handler = new GetSettingPaginatedListQueryHandler(_settingFixture.Mapper, _mockSettingRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetSettingPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SettingListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Settings_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetSettingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "skey=Sharp;svalue=Abdul_1;loginuserid=123" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SettingListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBe(_settingFixture.Settings[0].ReferenceId);

        result.Data[0].SKey.ShouldBe("Sharp");

        result.Data[0].SValue.ShouldBe("Abdul_1");

        result.Data[1].LoginUserId.ShouldBeGreaterThan(0.ToString());

    }

    [Fact]
    public async Task Handle_Return_PaginatedSettings_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetSettingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Abdul" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SettingListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<SettingListVm>();

        result.Data[0].LoginUserId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].SValue.ShouldBe("Abdul_1");

        result.Data[0].SKey.ShouldNotBeEmpty();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetSettingPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Req" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SettingListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetSettingPaginatedListQuery(), CancellationToken.None);

        _mockSettingRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}