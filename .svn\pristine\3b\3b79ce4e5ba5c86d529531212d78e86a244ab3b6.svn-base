using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberComponentMappingsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberComponentMappingListVm>>> GetCyberComponentMappings()
    {
        Logger.LogDebug("Get All CyberComponentMappings");

        return Ok(await Mediator.Send(new GetCyberComponentMappingListQuery()));
    }

    [HttpGet("{id}", Name = "GetCyberComponentMapping")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberComponentMappingDetailVm>> GetCyberComponentMappingById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentMapping Id");

        Logger.LogDebug($"Get CyberComponentMapping Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberComponentMappingDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Cyber.View)]
 public async Task<ActionResult<PaginatedResult<CyberComponentMappingListVm>>> GetPaginatedCyberComponentMappings([FromQuery] GetCyberComponentMappingPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in CyberComponentMapping Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Cyber.Create)]
    public async Task<ActionResult<CreateCyberComponentMappingResponse>> CreateCyberComponentMapping([FromBody] CreateCyberComponentMappingCommand createCyberComponentMappingCommand)
    {
        Logger.LogDebug($"Create CyberComponentMapping '{createCyberComponentMappingCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCyberComponentMapping), await Mediator.Send(createCyberComponentMappingCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateCyberComponentMappingResponse>> UpdateCyberComponentMapping([FromBody] UpdateCyberComponentMappingCommand updateCyberComponentMappingCommand)
    {
        Logger.LogDebug($"Update CyberComponentMapping '{updateCyberComponentMappingCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCyberComponentMappingCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Cyber.Delete)]
    public async Task<ActionResult<DeleteCyberComponentMappingResponse>> DeleteCyberComponentMapping(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentMapping Id");

        Logger.LogDebug($"Delete CyberComponentMapping Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCyberComponentMappingCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsCyberComponentMappingNameExist(string cyberComponentMappingName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(cyberComponentMappingName, "CyberComponentMapping Name");

     Logger.LogDebug($"Check Name Exists Detail by CyberComponentMapping Name '{cyberComponentMappingName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetCyberComponentMappingNameUniqueQuery { Name = cyberComponentMappingName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


