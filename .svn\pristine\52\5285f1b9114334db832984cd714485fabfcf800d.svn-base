﻿using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers
{
    public class CustomDashboardControllerTests
    {
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private CustomDashboardController _controller;

        public CustomDashboardControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new CustomDashboardController
            (_mockLoggedInUserService.Object,
             _mockDataProvider.Object);
        }

        [Fact]
        public async Task List_ReturnsRedirectToAction_WhenDashboardAndSubDashboardAreNull()
        {
            _mockLoggedInUserService.Setup(s => s.UserId).Returns("testUserId");
            _mockLoggedInUserService.Setup(s => s.Role).Returns("testRole");

            _mockDataProvider.Setup(p => p.DynamicDashboardMap.GetDefaultDashboardByUserId(It.IsAny<string>()))
                             .ReturnsAsync((DynamicDashboardMapListVm)null);

            _mockDataProvider.Setup(p => p.DynamicDashboardMap.GetDefaultDashboardByRoleId(It.IsAny<string>()))
                             .ReturnsAsync((DynamicDashboardMapListVm)null);
            var result = await _controller.List() as RedirectToActionResult;

            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
            Assert.Equal("ServiceAvailability", result.ControllerName);
            Assert.Equal("Dashboard", result.RouteValues["Area"]);
        }

        [Fact]
        public async Task List_ReturnsView_WhenSubDashboardIsNotNull()
        {
            var dashboard = new DynamicDashboardMapListVm { DashBoardSubId = "subDashboardId" };
            var subDashboard = new DynamicSubDashboardDetailVm();

            _mockLoggedInUserService.Setup(s => s.UserId).Returns("testUserId");
            _mockLoggedInUserService.Setup(s => s.Role).Returns("testRole");

            _mockDataProvider.Setup(p => p.DynamicDashboardMap.GetDefaultDashboardByUserId(It.IsAny<string>()))
                             .ReturnsAsync(dashboard);

            _mockDataProvider.Setup(p => p.DynamicSubDashboard.GetByReferenceId("subDashboardId"))
                             .ReturnsAsync(subDashboard);

            var result = await _controller.List() as ViewResult;
            var model = result?.Model as DynamicSubDashboardDetailVm;

            Assert.NotNull(result);
            Assert.Equal(subDashboard, model);
        }

        [Fact]
        public async Task DynamicDashboardList_ReturnsJsonResult_WhenSubDashboardIsFound()
        {
            var subDashboard = new DynamicSubDashboardDetailVm();

            _mockDataProvider.Setup(p => p.DynamicSubDashboard.GetByReferenceId(It.IsAny<string>()))
                             .ReturnsAsync(subDashboard);

            var result = await _controller.DynamicDashboardList("subDashboardId") as JsonResult;
            var model = result?.Value as DynamicSubDashboardDetailVm;

            Assert.NotNull(result);
            Assert.Equal(subDashboard, model);
        }
    }
}

