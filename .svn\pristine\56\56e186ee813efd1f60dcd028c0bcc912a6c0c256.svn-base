﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionResultModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetPaginatedList;

public class GetWorkflowActionResultPaginatedListQueryHandler : IRequestHandler<
    GetWorkflowActionResultPaginatedListQuery, PaginatedResult<WorkflowActionResultListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;

    public GetWorkflowActionResultPaginatedListQueryHandler(IMapper mapper,
        IWorkflowActionResultRepository workflowActionResultRepository)
    {
        _mapper = mapper;
        _workflowActionResultRepository = workflowActionResultRepository;
    }

    public async Task<PaginatedResult<WorkflowActionResultListVm>> Handle(
        GetWorkflowActionResultPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _workflowActionResultRepository.PaginatedListAllAsync();

        var productFilterSpec = new WorkflowActionResultFilterSpecification(request.SearchString);

        var workflowOperationInfraList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowActionResultListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowOperationInfraList;
    }
}