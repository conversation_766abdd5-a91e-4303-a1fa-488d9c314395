﻿using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Event.PaginatedView;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class GroupNodePolicyControllerTests
    {
        private readonly GroupNodePolicyController _controller;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<IGroupPolicyService> _mockGroupPolicyService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IDataProvider> _mockDataProvider;
        private readonly Mock<ILogger<GroupNodePolicyController>> _mockLogger;

        public GroupNodePolicyControllerTests()
        {
            _mockPublisher = new Mock<IPublisher>();
            _mockGroupPolicyService = new Mock<IGroupPolicyService>();
            _mockMapper = new Mock<IMapper>();
            _mockDataProvider = new Mock<IDataProvider>();
            _mockLogger = new Mock<ILogger<GroupNodePolicyController>>();

            _controller = new GroupNodePolicyController(
                _mockPublisher.Object,
                //_mockGroupPolicyService.Object,
                _mockMapper.Object,
                _mockDataProvider.Object,
                _mockLogger.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ShouldReturnViewResult()
        {
            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task List_Returns_View_With_Empty_Model_When_Exception_Occurs()
        {
            // Arrange
            _mockPublisher.Setup(p => p.Publish(It.IsAny<GroupPolicyPaginatedViewEvent>(), It.IsAny<CancellationToken>())).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            var model = Assert.IsType<GroupPolicyViewModel>(viewResult.Model);
            Assert.NotNull(model.GroupPolicyListVms);
            Assert.NotNull(model.NodesVm);
            Assert.Empty(model.GroupPolicyListVms);
            Assert.Empty(model.NodesVm);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnJsonResult_WhenCreateIsSuccessful()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { GroupName = "Test Group" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateGroupPolicyCommand();

            _mockMapper.Setup(m => m.Map<CreateGroupPolicyCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(x => x.GroupPolicy.CreateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":\"Created\"", json);
            _mockDataProvider.Verify(g => g.GroupPolicy.CreateAsync(It.IsAny<CreateGroupPolicyCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldReturnJsonResult_WhenUpdateIsSuccessful()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { Id = "1", GroupName = "Updated Policy" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateGroupPolicyCommand();

            _mockMapper.Setup(m => m.Map<UpdateGroupPolicyCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(x => x.GroupPolicy.UpdateAsync(command)).ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":\"Updated\"", json);
            _mockDataProvider.Verify(g => g.GroupPolicy.UpdateAsync(It.IsAny<UpdateGroupPolicyCommand>()), Times.Once);
        }

        [Fact]
        public async Task Delete_ShouldRedirectToAction_WithSuccessNotification()
        {

			_mockDataProvider.Setup(g => g.GroupPolicy.DeleteAsync("1"))
                                   .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            _controller.TempData = new Mock<ITempDataDictionary>().Object;


            var result = await _controller.Delete("1");

            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
			_mockDataProvider.Verify(g => g.GroupPolicy.DeleteAsync("1"), Times.Once);
        }

        [Fact]
        public async Task IsGroupPolicyNameExist_ShouldReturnTrueIfNameExists()
        {
			_mockDataProvider.Setup(g => g.GroupPolicy.IsGroupPolicyNameExist("Policy1", "1"))
                                   .ReturnsAsync(true);

            var result = await _controller.IsGroupPolicyNameExist("Policy1", "1");

            Assert.True(result);
        }

        [Fact]
        public async Task GetPagination_ShouldReturnJsonResult_WithPaginatedGroupPolicies()
        {
            var query = new GetGroupPolicyPaginatedListQuery();
            var page = new PaginatedResult<GroupPolicyListVm>();

			_mockDataProvider.Setup(g => g.GroupPolicy.GetPaginatedGroupPolicies(query))
                                   .ReturnsAsync(page );

            var result = await _controller.GetPagination(query);

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(page , jsonResult.Value);
        }

        [Fact]
        public async Task GetNodeData_ShouldReturnJsonResult_WithNodeList()
        {
            var node = new List<LoadBalancerListVm>();         
            _mockDataProvider.Setup(d => d.LoadBalancer.GetLoadBalancerList())
              .ReturnsAsync(node);

            var result = await _controller.GetLoadBalancerList();

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(node, jsonResult.Value);
        }

        [Fact]
        public async Task GetNodes_ShouldReturnJsonResult_WithGroupPoliciesByType()
        {
            var gvp = new List<GroupPolicyTypeVm>();
            _mockDataProvider.Setup(g => g.GroupPolicy.GetByType("Type1")).ReturnsAsync(gvp);

            var result = await _controller.GetGroupPolicyByType("Type1");

            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(gvp, jsonResult.Value);
        }

        // Exception handling tests
        [Fact]
        public async Task CreateOrUpdate_Returns_RedirectToAction_When_ValidationException_Occurs_Create()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { GroupName = "Test Group" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateGroupPolicyCommand();
            _mockMapper.Setup(m => m.Map<CreateGroupPolicyCommand>(viewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(x => x.GroupPolicy.CreateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_RedirectToAction_When_Exception_Occurs_Create()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { GroupName = "Test Group" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new CreateGroupPolicyCommand();
            _mockMapper.Setup(m => m.Map<CreateGroupPolicyCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(x => x.GroupPolicy.CreateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_RedirectToAction_When_ValidationException_Occurs_Update()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { GroupName = "Test Group" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateGroupPolicyCommand();
            _mockMapper.Setup(m => m.Map<UpdateGroupPolicyCommand>(viewModel)).Returns(command);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(x => x.GroupPolicy.UpdateAsync(command)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_RedirectToAction_When_Exception_Occurs_Update()
        {
            // Arrange
            var viewModel = new GroupPolicyViewModel { GroupName = "Test Group" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var command = new UpdateGroupPolicyCommand();
            _mockMapper.Setup(m => m.Map<UpdateGroupPolicyCommand>(viewModel)).Returns(command);
            _mockDataProvider.Setup(x => x.GroupPolicy.UpdateAsync(command)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_Returns_RedirectToAction_When_Exception_Occurs()
        {
            // Arrange
            var id = "testId";
            _mockDataProvider.Setup(g => g.GroupPolicy.DeleteAsync(id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsGroupPolicyNameExist_Returns_False_When_Exception_Occurs()
        {
            // Arrange
            var groupPolicyName = "testName";
            var id = "testId";
            _mockDataProvider.Setup(g => g.GroupPolicy.IsGroupPolicyNameExist(groupPolicyName, id)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.IsGroupPolicyNameExist(groupPolicyName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetPagination_Returns_EmptyJson_When_Exception_Occurs()
        {
            // Arrange
            var query = new GetGroupPolicyPaginatedListQuery();
            _mockDataProvider.Setup(g => g.GroupPolicy.GetPaginatedGroupPolicies(query)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetLoadBalancerList_Returns_EmptyJson_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(d => d.LoadBalancer.GetLoadBalancerList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetLoadBalancerList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }

        [Fact]
        public async Task GetGroupPolicyByType_Returns_EmptyJson_When_Exception_Occurs()
        {
            // Arrange
            var type = "testType";
            _mockDataProvider.Setup(g => g.GroupPolicy.GetByType(type)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetGroupPolicyByType(type);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal("", jsonResult.Value);
        }
    }
}

