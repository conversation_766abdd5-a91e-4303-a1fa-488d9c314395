using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Update;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftProfileControllerTests : IClassFixture<DriftProfileFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftProfilesController _controller;
    private readonly DriftProfileFixture _driftProfileFixture;

    public DriftProfileControllerTests(DriftProfileFixture driftProfileFixture)
    {
        _driftProfileFixture = driftProfileFixture;

        var testBuilder = new ControllerTestBuilder<DriftProfilesController>();
        _controller = testBuilder.CreateController(
            _ => new DriftProfilesController(),
            out _mediatorMock);
    }

    #region GetDriftProfiles Tests

    [Fact]
    public async Task GetDriftProfiles_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftProfileFixture.DriftProfileListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftProfiles();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftProfileListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise", item.Name));
        Assert.All(returnedList, item => Assert.NotNull(item.ComponentType));
        Assert.All(returnedList, item => Assert.NotNull(item.ComponentCategoryProperties));
    }

    [Fact]
    public async Task GetDriftProfiles_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftProfileListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftProfiles();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftProfileListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftProfiles_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftProfiles());
    }

    #endregion

    #region CreateDriftProfile Tests

    [Fact]
    public async Task CreateDriftProfile_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftProfileFixture.CreateDriftProfileCommand;
        var expectedResponse = _driftProfileFixture.CreateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftProfile(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftProfileResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.Contains("Enterprise", command.Name);
        Assert.NotNull(command.ComponentType);
        Assert.Contains("SOX", command.Properties);
    }

    [Fact]
    public async Task CreateDriftProfile_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftProfileCommand nullCommand = null;

        var successResponse = new CreateDriftProfileResponse
        {
            Success = true,
            Message = "DriftProfile created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftProfile(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftProfileResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftProfile_WithInvalidComponentTypeId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftProfileFixture.CreateDriftProfileCommand;
        command.ComponentTypeId = "invalid-guid"; // Invalid GUID

        var failureResponse = new CreateDriftProfileResponse
        {
            Success = false,
            Message = "Invalid ComponentTypeId format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftProfile(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftProfileResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftProfile Tests

    [Fact]
    public async Task UpdateDriftProfile_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftProfileFixture.UpdateDriftProfileCommand;
        var expectedResponse = _driftProfileFixture.UpdateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftProfileResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
        Assert.NotNull(command.Name);
        Assert.NotNull(command.Properties);
    }

    [Fact]
    public async Task UpdateDriftProfile_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        UpdateDriftProfileCommand nullCommand = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.UpdateDriftProfile(nullCommand));
    }

    [Fact]
    public async Task UpdateDriftProfile_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftProfileFixture.UpdateDriftProfileCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftProfileResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftProfileResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftProfileById Tests

    [Fact]
    public async Task GetDriftProfileById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftProfileFixture.DriftProfileDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftProfileDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftProfileById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftProfileDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("Enterprise", returnedDetail.Name);
        Assert.NotNull(returnedDetail.ComponentType);
        Assert.Contains("SOX_GDPR", returnedDetail.Properties);
    }

    [Fact]
    public async Task GetDriftProfileById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftProfileById(invalidId));
    }

    [Fact]
    public async Task GetDriftProfileById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftProfileById(nullId));
    }

    #endregion

    #region DeleteDriftProfile Tests

    [Fact]
    public async Task DeleteDriftProfile_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftProfileFixture.DeleteDriftProfileResponse;
        expectedResponse.IsActive=false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftProfileCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftProfile(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftProfileResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftProfile_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftProfile(invalidId));
    }

    [Fact]
    public async Task DeleteDriftProfile_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftProfile(nullId));
    }

    #endregion

    #region GetPaginatedDriftProfiles Tests

    [Fact]
    public async Task GetPaginatedDriftProfiles_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftProfileFixture.GetDriftProfilePaginatedListQuery;
        var expectedResult = _driftProfileFixture.DriftProfilePaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftProfiles(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftProfileListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
        Assert.All(returnedResult.Data, item => Assert.Contains("Enterprise", item.Name));
    }

    [Fact]
    public async Task GetPaginatedDriftProfiles_WithNullQuery_ThrowsArgumentNullException()
    {
        // Arrange
        GetDriftProfilePaginatedListQuery nullQuery = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetPaginatedDriftProfiles(nullQuery));
    }

    [Fact]
    public async Task GetPaginatedDriftProfiles_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftProfileFixture.GetDriftProfilePaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftProfileListVm>
        {
            Data = new List<DriftProfileListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftProfiles(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftProfileListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region IsDriftProfileNameUnique Tests

    [Fact]
    public async Task IsDriftProfileNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var profileName = "Unique Enterprise Profile";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftProfileNameUniqueQuery>(q => q.Name == profileName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftProfileNameExist(profileName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsDriftProfileNameUnique_WithDuplicateName_ReturnsFalse()
    {
        // Arrange
        var profileName = "Duplicate Enterprise Profile";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftProfileNameUniqueQuery>(q => q.Name == profileName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftProfileNameExist(profileName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsDriftProfileNameUnique_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        string nullName = null;
        var id = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDriftProfileNameExist(nullName, id));
    }

    [Fact]
    public async Task IsDriftProfileNameUnique_WithNullId_ReturnsValidResult()
    {
        // Arrange
        var profileName = "Enterprise Profile";
        string nullId = null;
        var existsResult = true;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftProfileNameUniqueQuery>(q => q.Name == profileName && q.Id == nullId), default))
            .ReturnsAsync(existsResult);

        // Act
        var result = await _controller.IsDriftProfileNameExist(profileName, nullId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }
  
   

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftProfile_WithMultipleComponentTypes_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftProfileFixture.CreateDriftProfileCommand;
        command.Name = "Multi-Component Enterprise Profile";
        command.ComponentType = "Server,Database,Network";
        command.ComponentCategoryProperties = "{\"servers\": {\"os\": \"windows\", \"version\": \"2019\"}, \"databases\": {\"type\": \"sql\", \"version\": \"2019\"}}";
        var expectedResponse = _driftProfileFixture.CreateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftProfile(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftProfileResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Multi-Component", command.Name);
        Assert.Contains("Server,Database,Network", command.ComponentType);
        Assert.Contains("servers", command.ComponentCategoryProperties);
        Assert.Contains("databases", command.ComponentCategoryProperties);
    }

    [Fact]
    public async Task UpdateDriftProfile_WithAdvancedConfiguration_ReturnsOkResult()
    {
        // Arrange
        var command = _driftProfileFixture.UpdateDriftProfileCommand;
        command.ComponentCategoryProperties = "{\"advanced_config\": {\"monitoring_interval\": 60, \"alert_thresholds\": {\"cpu\": 85, \"memory\": 80}, \"auto_remediation\": true}}";
        command.Name = "Advanced Enterprise Drift Profile";
        var expectedResponse = _driftProfileFixture.UpdateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftProfileResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("advanced_config", command.ComponentCategoryProperties);
        Assert.Contains("auto_remediation", command.ComponentCategoryProperties);
        Assert.Equal("Advanced Enterprise Drift Profile", command.Name);
    }

    [Fact]
    public async Task GetDriftProfileById_WithComplexComponentConfiguration_ReturnsDetailedProfile()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftProfileFixture.DriftProfileDetailVm;
        expectedDetail.Id = id; // Set the ID to match the test parameter
        expectedDetail.ComponentCategoryProperties = "{\"component_matrix\": {\"web_servers\": {\"count\": 5, \"load_balancer\": true}, \"app_servers\": {\"count\": 3, \"clustering\": true}}}";
        expectedDetail.ComponentType = "WebServer,ApplicationServer";
        expectedDetail.Name = "Enterprise Web Application Profile";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftProfileDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftProfileById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftProfileDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("component_matrix", returnedDetail.ComponentCategoryProperties);
        Assert.Contains("WebServer,ApplicationServer", returnedDetail.ComponentType);
        Assert.Equal("Enterprise Web Application Profile", returnedDetail.Name);
    }

    [Fact]
    public async Task GetPaginatedDriftProfiles_WithComponentTypeFiltering_ReturnsFilteredResults()
    {
        // Arrange
        var query = _driftProfileFixture.GetDriftProfilePaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 15;
        query.SearchString = "component_type:Database";
        var expectedResult = _driftProfileFixture.DriftProfilePaginatedResult;
        expectedResult.Data.ForEach(x => x.ComponentType = "Database");

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftProfiles(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftProfileListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.All(returnedResult.Data, item => Assert.Equal("Database", item.ComponentType));
        Assert.Equal("component_type:Database", query.SearchString);
    }

    [Fact]
    public async Task GetDriftProfiles_WithEnvironmentSpecificProfiles_ReturnsEnvironmentProfiles()
    {
        // Arrange
        var expectedList = _driftProfileFixture.DriftProfileListVm;
        expectedList.ForEach(x =>
        {
            x.Name = "Production Environment Profile";
            x.ComponentCategoryProperties = "{\"environment\": \"production\", \"compliance\": \"SOX\", \"backup_frequency\": \"daily\"}";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftProfiles();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftProfileListVm>>(okResult.Value);
        Assert.All(returnedList, item => Assert.Contains("Production Environment", item.Name));
        Assert.All(returnedList, item => Assert.Contains("environment", item.ComponentCategoryProperties));
        Assert.All(returnedList, item => Assert.Contains("production", item.ComponentCategoryProperties));
    }

    [Fact]
    public async Task IsDriftProfileNameExist_WithProfileNameVariations_ReturnsCorrectResult()
    {
        // Arrange
        var profileName = "Enterprise-Production-Profile-v2.1";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftProfileNameExist(profileName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task DeleteDriftProfile_WithAssociatedComponents_ReturnsSuccessResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftProfileFixture.DeleteDriftProfileResponse;
        expectedResponse.Message = "DriftProfile and all associated component configurations deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftProfileCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftProfile(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftProfileResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("associated component configurations", returnedResponse.Message);
    }

    [Fact]
    public async Task CreateDriftProfile_WithTemplateBasedConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftProfileFixture.CreateDriftProfileCommand;
        command.Name = "Template-Based Enterprise Profile";
        command.ComponentCategoryProperties = "{\"template\": {\"base\": \"enterprise_standard\", \"customizations\": {\"security_level\": \"high\", \"monitoring\": \"enhanced\"}}}";
        var expectedResponse = _driftProfileFixture.CreateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftProfile(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftProfileResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Template-Based", command.Name);
        Assert.Contains("template", command.ComponentCategoryProperties);
        Assert.Contains("enterprise_standard", command.ComponentCategoryProperties);
    }

    [Fact]
    public async Task GetDriftProfiles_WithActiveProfilesOnly_ReturnsActiveProfiles()
    {
        // Arrange
        var expectedList = _driftProfileFixture.DriftProfileListVm;
        expectedList.ForEach(x =>
        {
            x.Name = "Active Enterprise Profile";
        });

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftProfileListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftProfiles();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftProfileListVm>>(okResult.Value);
        Assert.All(returnedList, item => Assert.Contains("Active", item.Name));
        Assert.True(returnedList.Count > 0);
    }

    [Fact]
    public async Task UpdateDriftProfile_WithVersionControl_ReturnsOkResult()
    {
        // Arrange
        var command = _driftProfileFixture.UpdateDriftProfileCommand;
        command.ComponentCategoryProperties = "{\"version_control\": {\"version\": \"2.1.0\", \"changelog\": \"Added enhanced monitoring capabilities\", \"approved_by\": \"admin\"}}";
        command.Name = "Versioned Enterprise Profile v2.1";
        var expectedResponse = _driftProfileFixture.UpdateDriftProfileResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftProfile(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftProfileResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("version_control", command.ComponentCategoryProperties);
        Assert.Contains("2.1.0", command.ComponentCategoryProperties);
        Assert.Contains("v2.1", command.Name);
    }

    #endregion
}
