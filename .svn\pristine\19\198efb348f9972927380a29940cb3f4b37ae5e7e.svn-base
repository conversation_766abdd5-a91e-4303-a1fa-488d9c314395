﻿using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberJobWorkflowSchedulerService
{
    Task<PaginatedResult<CyberJobWorkflowSchedulerListVm>> GetPaginatedCyberJobWorkflowScheduler(GetCyberJobWorkflowSchedulerPaginatedListQuery query);
}
