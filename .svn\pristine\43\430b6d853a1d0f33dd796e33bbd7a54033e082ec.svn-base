﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Create;

public class FormCreatedEventHandler : INotificationHandler<FormCreatedEvent>
{
    private readonly ILogger<FormCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;


    public FormCreatedEventHandler(ILoggedInUserService userService, ILogger<FormCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Form '{createdEvent.FormName}' created successfully."
        };
        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Form '{createdEvent.FormName}' created successfully.");
    }
}