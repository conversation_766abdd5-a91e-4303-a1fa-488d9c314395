﻿using ContinuityPatrol.Application.Features.Job.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.JobModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;


public class GetJobListQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _jobFixture;

    private Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobListQueryHandler _handler;

    public GetJobListQueryHandlerTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetJobRepository(_jobFixture.Jobs);

        _handler = new GetJobListQueryHandler(_jobFixture.Mapper, _mockJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_JobsCount()
    {
        var result = await _handler.Handle(new GetJobListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<JobListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_JobsList()
    {
        var result = await _handler.Handle(new GetJobListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<JobListVm>>();
        result[0].Id.ShouldBe(_jobFixture.Jobs[0].ReferenceId);
        result[0].Name.ShouldBe(_jobFixture.Jobs[0].Name);
        result[0].InfraObjectProperties.ShouldBe(_jobFixture.Jobs[0].InfraObjectProperties);
        result[0].TemplateId.ShouldBe(_jobFixture.Jobs[0].TemplateId);
        result[0].TemplateName.ShouldBe(_jobFixture.Jobs[0].TemplateName);
        result[0].NodeId.ShouldBe(_jobFixture.Jobs[0].NodeId);
        result[0].NodeName.ShouldBe(_jobFixture.Jobs[0].NodeName);
        result[0].Status.ShouldBe(_jobFixture.Jobs[0].Status);
        result[0].CronExpression.ShouldBe(_jobFixture.Jobs[0].CronExpression);
        result[0].State.ShouldBe(_jobFixture.Jobs[0].State);
        //result[0].GroupPolicy.ShouldBe(_jobFixture.Jobs[0].GroupPolicy);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockJobRepository = JobRepositoryMocks.GetJobEmptyRepository();

        var handler = new GetJobListQueryHandler(_jobFixture.Mapper, _mockJobRepository.Object);

        var result = await handler.Handle(new GetJobListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetJobListQuery(), CancellationToken.None);

        _mockJobRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}