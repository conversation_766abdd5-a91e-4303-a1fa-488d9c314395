using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RpForVmCgEnableDisableStatusFixture : IDisposable
{
    public List<RpForVmCgEnableDisableStatus> RpForVmCgEnableDisableStatusPaginationList { get; set; }
    public List<RpForVmCgEnableDisableStatus> RpForVmCgEnableDisableStatusList { get; set; }
    public RpForVmCgEnableDisableStatus RpForVmCgEnableDisableStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string WorkflowOperationId = "WORKFLOW_OP_123";
    public const string WorkflowName = "Test Workflow";
    public const string CGName = "Test CG";
    public const string Type = "Enable";
    public const string JobId = "JOB_123";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RpForVmCgEnableDisableStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RpForVmCgEnableDisableStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.WorkflowOperationId, () => WorkflowOperationId)
            .With(x => x.WorkflowName, () => WorkflowName)
            .With(x => x.CGName, () => CGName)
            .With(x => x.Status, () => "Completed")
            .With(x => x.EnableStartTime, () => DateTime.UtcNow.AddHours(-2))
            .With(x => x.EnableEndTime, () => DateTime.UtcNow.AddHours(-1))
            .With(x => x.DisableStartTime, () => (DateTime?)null)
            .With(x => x.DisableEndTime, () => (DateTime?)null)
            .With(x => x.ErrorMessage, () => string.Empty)
            .With(x => x.Description, () => _fixture.Create<string>())
            .With(x => x.RemoteReplicaClusterName, () => _fixture.Create<string>())
            .With(x => x.ProductionClusterName, () => _fixture.Create<string>())
            .With(x => x.Type, () => Type)
            .With(x => x.JobId, () => JobId)
            .With(x => x.DisableStatus, () => string.Empty)
            .With(x => x.EnableRemarks, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RpForVmCgEnableDisableStatusList = _fixture.CreateMany<RpForVmCgEnableDisableStatus>(5).ToList();
        RpForVmCgEnableDisableStatusPaginationList = _fixture.CreateMany<RpForVmCgEnableDisableStatus>(20).ToList();
        RpForVmCgEnableDisableStatusDto = _fixture.Create<RpForVmCgEnableDisableStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RpForVmCgEnableDisableStatus CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(string workflowOperationId, string status = "Completed")
    {
        return _fixture.Build<RpForVmCgEnableDisableStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowOperationId, workflowOperationId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.CGName, CGName)
            .With(x => x.Status, status)
            .With(x => x.EnableStartTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.EnableEndTime, DateTime.UtcNow.AddHours(-1))
            .With(x => x.DisableStartTime, (DateTime?)null)
            .With(x => x.DisableEndTime, (DateTime?)null)
            .With(x => x.ErrorMessage, string.Empty)
            .With(x => x.Description, _fixture.Create<string>())
            .With(x => x.RemoteReplicaClusterName, _fixture.Create<string>())
            .With(x => x.ProductionClusterName, _fixture.Create<string>())
            .With(x => x.Type, Type)
            .With(x => x.JobId, JobId)
            .With(x => x.DisableStatus, string.Empty)
            .With(x => x.EnableRemarks, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCgEnableDisableStatus CreateRpForVmCgEnableDisableStatusWithJobId(string jobId, string workflowOperationId = null, string status = "Completed")
    {
        return _fixture.Build<RpForVmCgEnableDisableStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowOperationId, workflowOperationId ?? WorkflowOperationId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.CGName, CGName)
            .With(x => x.Status, status)
            .With(x => x.EnableStartTime, DateTime.UtcNow.AddHours(-2))
            .With(x => x.EnableEndTime, DateTime.UtcNow.AddHours(-1))
            .With(x => x.DisableStartTime, (DateTime?)null)
            .With(x => x.DisableEndTime, (DateTime?)null)
            .With(x => x.ErrorMessage, string.Empty)
            .With(x => x.Description, _fixture.Create<string>())
            .With(x => x.RemoteReplicaClusterName, _fixture.Create<string>())
            .With(x => x.ProductionClusterName, _fixture.Create<string>())
            .With(x => x.Type, Type)
            .With(x => x.JobId, jobId)
            .With(x => x.DisableStatus, string.Empty)
            .With(x => x.EnableRemarks, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RpForVmCgEnableDisableStatus CreateRpForVmCgEnableDisableStatusWithType(string type, DateTime? enableStartTime = null, DateTime? enableEndTime = null)
    {
        return _fixture.Build<RpForVmCgEnableDisableStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.WorkflowOperationId, WorkflowOperationId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.CGName, CGName)
            .With(x => x.Status, "Completed")
            .With(x => x.EnableStartTime, enableStartTime ?? DateTime.UtcNow.AddHours(-2))
            .With(x => x.EnableEndTime, enableEndTime ?? DateTime.UtcNow.AddHours(-1))
            .With(x => x.DisableStartTime, (DateTime?)null)
            .With(x => x.DisableEndTime, (DateTime?)null)
            .With(x => x.ErrorMessage, string.Empty)
            .With(x => x.Description, _fixture.Create<string>())
            .With(x => x.RemoteReplicaClusterName, _fixture.Create<string>())
            .With(x => x.ProductionClusterName, _fixture.Create<string>())
            .With(x => x.Type, type)
            .With(x => x.JobId, JobId)
            .With(x => x.DisableStatus, string.Empty)
            .With(x => x.EnableRemarks, _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public List<RpForVmCgEnableDisableStatus> CreateMultipleRpForVmCgEnableDisableStatusWithSameWorkflowOperationId(
        string workflowOperationId, int completedCount, int totalCount)
    {
        var statuses = new List<RpForVmCgEnableDisableStatus>();
        
        // Create completed statuses
        for (int i = 0; i < completedCount; i++)
        {
            statuses.Add(CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId, "completed"));
        }
        
        // Create non-completed statuses
        for (int i = 0; i < (totalCount - completedCount); i++)
        {
            statuses.Add(CreateRpForVmCgEnableDisableStatusWithWorkflowOperationId(workflowOperationId, "in-progress"));
        }
        
        return statuses;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
