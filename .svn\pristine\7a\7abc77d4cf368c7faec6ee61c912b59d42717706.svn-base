using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DRReadyStatusRepositoryTests : IClassFixture<DRReadyStatusFixture>
{
    private readonly DRReadyStatusFixture _drReadyStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DRReadyStatusRepository _repository;
    private readonly DRReadyStatusRepository _repositoryNotParent;

    public DRReadyStatusRepositoryTests(DRReadyStatusFixture drReadyStatusFixture)
    {
        _drReadyStatusFixture = drReadyStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DRReadyStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DRReadyStatusRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;

        // Act
        var result = await _repository.AddAsync(drReadyStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyStatus.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(drReadyStatus.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Single(_dbContext.DrReadyStatuses);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        await _repository.AddAsync(drReadyStatus);

        drReadyStatus.BusinessServiceName = "Updated Service Name";

        // Act
        var result = await _repository.UpdateAsync(drReadyStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        await _repository.AddAsync(drReadyStatus);

        // Act
        var result = await _repository.DeleteAsync(drReadyStatus);

        // Assert
        Assert.Equal(drReadyStatus.BusinessServiceId, result.BusinessServiceId);
        Assert.Empty(_dbContext.DrReadyStatuses);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        var addedEntity = await _repository.AddAsync(drReadyStatus);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsParent()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        await _repository.AddAsync(drReadyStatus);

        // Act
        var result = await _repository.GetByReferenceIdAsync(drReadyStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(drReadyStatus.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsNotParent()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        await _repositoryNotParent.AddAsync(drReadyStatus);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(drReadyStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyStatus.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyStatuses.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repositoryNotParent.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;

        // Act
        var result = await _repository.AddRangeAsync(drReadyStatuses);

        // Assert
        Assert.Equal(drReadyStatuses.Count, result.Count());
        Assert.Equal(drReadyStatuses.Count, _dbContext.DrReadyStatuses.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.RemoveRangeAsync(drReadyStatuses);

        // Assert
        Assert.Equal(drReadyStatuses.Count, result.Count());
        Assert.Empty(_dbContext.DrReadyStatuses);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetDrReadyStatusByBusinessServiceId Tests

    [Fact]
    public async Task GetDrReadyStatusByBusinessServiceId_ShouldReturnEntities_WhenIsAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.GetDrReadyStatusByBusinessServiceId(DRReadyStatusFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DRReadyStatusFixture.BusinessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetDrReadyStatusByBusinessServiceId_ShouldReturnFilteredEntities_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repositoryNotParent.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repositoryNotParent.GetDrReadyStatusByBusinessServiceId(DRReadyStatusFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDrReadyStatusByBusinessServiceId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetDrReadyStatusByBusinessServiceId("invalid-guid"));
    }

    #endregion

    #region GetDrReadyStatusByInfraObjectId Tests

    [Fact]
    public async Task GetDrReadyStatusByInfraObjectId_ShouldReturnEntity_WhenIsAllInfra()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;
        await _repository.AddAsync(drReadyStatus);

        // Act
        var result = await _repository.GetDrReadyStatusByInfraObjectId(DRReadyStatusFixture.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DRReadyStatusFixture.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetDrReadyStatusByInfraObjectId_ShouldReturnFilteredEntity_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyStatus = _drReadyStatusFixture.DRReadyStatusDto;

        await _repositoryNotParent.AddAsync(drReadyStatus);

        // Act
        var result = await _repositoryNotParent.GetDrReadyStatusByInfraObjectId(DRReadyStatusFixture.InfraObjectId);

        // Assert
        // Result should be filtered based on assigned infrastructure
        Assert.NotNull(result);
    }

    #endregion

    #region GetDrReadyStatusListByBusinessFunctionId Tests

    [Fact]
    public async Task GetDrReadyStatusListByBusinessFunctionId_ShouldReturnEntities_WhenIsAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.GetDrReadyStatusListByBusinessFunctionId(DRReadyStatusFixture.BusinessFunctionId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DRReadyStatusFixture.BusinessFunctionId, x.BusinessFunctionId));
    }

    [Fact]
    public async Task GetDrReadyStatusListByBusinessFunctionId_ShouldReturnFilteredEntities_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repositoryNotParent.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repositoryNotParent.GetDrReadyStatusListByBusinessFunctionId(DRReadyStatusFixture.BusinessFunctionId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDrReadyStatusListByBusinessFunctionId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetDrReadyStatusListByBusinessFunctionId("invalid-guid"));
    }

    #endregion

    #region GetDrReadyStatusByWorkflowId Tests

    [Fact]
    public async Task GetDrReadyStatusByWorkflowId_ShouldReturnEntities()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.GetDrReadyStatusByWorkflowId(DRReadyStatusFixture.WorkflowId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DRReadyStatusFixture.WorkflowId, x.WorkflowId));
    }

    [Fact]
    public async Task GetDrReadyStatusByWorkflowId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList;
        await _repository.AddRangeAsync(drReadyStatuses);

        // Act
        var result = await _repository.GetDrReadyStatusByWorkflowId("non-existent-workflow-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var drReadyStatuses = _drReadyStatusFixture.DRReadyStatusList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(drReadyStatuses);
        var initialCount = drReadyStatuses.Count;

        var toUpdate = drReadyStatuses.Take(2).ToList();
        toUpdate.ForEach(x => x.BusinessServiceName = "UpdatedServiceName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = drReadyStatuses.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.BusinessServiceName == "UpdatedServiceName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
