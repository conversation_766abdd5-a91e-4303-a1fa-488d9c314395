﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormType.Events.PaginatedView;

public class FormTypePaginatedViewEventHandler : INotificationHandler<FormTypePaginatedViewEvent>
{
    private readonly ILogger<FormTypePaginatedViewEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormTypePaginatedViewEventHandler(ILogger<FormTypePaginatedViewEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormTypePaginatedViewEvent paginatedViewEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.FormType.ToString(),
            Action = $"{ActivityType.View} {Modules.FormType}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = " Form Type viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Form Type viewed");
    }
}