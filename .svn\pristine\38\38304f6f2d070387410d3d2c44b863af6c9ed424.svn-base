﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Queries
{
    public class GetVeritasClusterDetailsQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IVeritasClusterRepository> _mockVeritasClusterRepository;
        private readonly GetVeritasClusterDetailsQueryHandler _handler;

        public GetVeritasClusterDetailsQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockVeritasClusterRepository = new Mock<IVeritasClusterRepository>();
            _handler = new GetVeritasClusterDetailsQueryHandler(_mockMapper.Object, _mockVeritasClusterRepository.Object);
        }

        [Fact]
        public async Task Handle_Returns_ClusterDetailVm_When_VeritasCluster_Found()
        {
            var veritasClusterId = Guid.NewGuid().ToString();
            var veritasCluster = new Domain.Entities.VeritasCluster
            {
                Id = 1,
            };

            var query = new GetVeritasClusterDetailQuery { Id = Guid.NewGuid().ToString() };
            var veritasClusterDetailVm = new VeritasClusterDetailVm
            {
                Id = Guid.NewGuid().ToString(),
                ClusterName = "Cluster1",
            };

            _mockVeritasClusterRepository.Setup(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()))
                .ReturnsAsync(veritasCluster);

            _mockMapper.Setup(m => m.Map<VeritasClusterDetailVm>(veritasCluster))
                .Returns(veritasClusterDetailVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(Guid.NewGuid().ToString(), result.Id);
            Assert.Equal("Cluster1", result.ClusterName);

            _mockVeritasClusterRepository.Verify(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()), Times.Once);
            _mockMapper.Verify(m => m.Map<VeritasClusterDetailVm>(veritasCluster), Times.Once);
        }

        [Fact]
        public async Task Handle_Throws_NotFoundException_When_VeritasCluster_Is_Not_Found()
        {
            var veritasClusterId = Guid.NewGuid().ToString();
            var query = new GetVeritasClusterDetailQuery { Id = Guid.NewGuid().ToString() };

            _mockVeritasClusterRepository.Setup(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()))
                .ReturnsAsync((Domain.Entities.VeritasCluster)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));
            Assert.Equal("VeritasCluster not found", exception.Message);

            _mockVeritasClusterRepository.Verify(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()), Times.Once);
        }

        [Fact]
        public async Task Handle_Throws_NotFoundException_When_VeritasCluster_Is_Deactive()
        {
            var veritasClusterId = Guid.NewGuid().ToString();
            var query = new GetVeritasClusterDetailQuery { Id = veritasClusterId.ToString() };

            var inactiveVeritasCluster = new Domain.Entities.VeritasCluster
            {
                Id = 1,
                ClusterName = "Cluster1",
                IsActive = false
            };

            _mockVeritasClusterRepository.Setup(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()))
                .ReturnsAsync(inactiveVeritasCluster);

            var expectedMessage = $"VeritasCluster ({veritasClusterId}) is not found or not authorized";

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));
            Assert.Equal(expectedMessage, exception.Message);

            _mockVeritasClusterRepository.Verify(r => r.GetByReferenceIdAsync(Guid.NewGuid().ToString()), Times.Once);
        }
    }
}
