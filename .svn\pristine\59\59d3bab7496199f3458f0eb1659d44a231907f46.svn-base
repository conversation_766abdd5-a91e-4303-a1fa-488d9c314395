using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using ProtoBufJsonConverter.Google.Protobuf.WellKnownTypes;
using Scriban;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class TemplateRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TemplateRepository _repository;
    private readonly TemplateFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public TemplateRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _repository = new TemplateRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new TemplateFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetTemplateNames Tests

    [Fact]
    public async Task GetTemplateNames_ShouldReturnAllActiveTemplates_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var template1 = _fixture.CreateTemplate(name: "Template Z", companyId: "COMPANY_123", isActive: true);
        var template2 = _fixture.CreateTemplate(name: "Template A", companyId: "COMPANY_456", isActive: true);
        var template3 = _fixture.CreateTemplate(name: "Template M", companyId: "COMPANY_789", isActive: true);
        var inactiveTemplate = _fixture.CreateTemplate(name: "Template D", companyId: "COMPANY_123", isActive: false);

        await _dbContext.Templates.AddRangeAsync(template1, template2, template3);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, template => Assert.True(template.IsActive));

        // Should be ordered by Name
        Assert.Equal("Template A", result[0].Name);
        Assert.Equal("Template M", result[1].Name);
        Assert.Equal("Template Z", result[2].Name);

        // Should only return ReferenceId and Name
        Assert.All(result, template =>
        {
            Assert.NotNull(template.ReferenceId);
            Assert.NotNull(template.Name);
            Assert.Null(template.Description);
        });
    }

    [Fact]
    public async Task GetTemplateNames_ShouldReturnCompanyTemplates_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var template1 = _fixture.CreateTemplate(name: "Template C", companyId: "COMPANY_123", isActive: true);
        var template2 = _fixture.CreateTemplate(name: "Template A", companyId: "COMPANY_456", isActive: true);
        var template3 = _fixture.CreateTemplate(name: "Template B", companyId: "COMPANY_123", isActive: true);

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);
        await _repository.AddAsync(template3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTemplateNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        // Should be ordered by Name
        Assert.Equal("Template B", result[0].Name);
        Assert.Equal("Template C", result[1].Name);

        // Should only return templates for the user's company
        Assert.All(result, template =>
        {
            Assert.NotNull(template.ReferenceId);
            Assert.NotNull(template.Name);
            Assert.Null(template.Description);
        });
    }

    [Fact]
    public async Task GetTemplateNames_ShouldReturnEmpty_WhenNoActiveTemplates()
    {
        // Arrange
        await ClearDatabase();

        var inactiveTemplate = _fixture.CreateTemplate(name: "Inactive Template", isActive: false);

        await _dbContext.Templates.AddRangeAsync(inactiveTemplate);
        _dbContext.SaveChanges();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsTemplateNameExist Tests

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Existing Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameExist("Existing Template", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Existing Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameExist("Non-Existent Template", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnFalse_WhenNameExistsButSameId()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Test Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameExist("Test Template", template.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTemplateNameExist_ShouldReturnTrue_WhenNameExistsWithDifferentId()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Test Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameExist("Test Template", Guid.NewGuid().ToString());

        // Assert
        Assert.True(result);
    }

    #endregion

    #region IsTemplateNameUnique Tests

    [Fact]
    public async Task IsTemplateNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Existing Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameUnique("Existing Template");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTemplateNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(name: "Existing Template");
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateNameUnique("Non-Existent Template");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetTemplateByReplicationTypeIdAndActionType Tests

    [Fact]
    public async Task GetTemplateByReplicationTypeIdAndActionType_ShouldReturnTemplate_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        // Create ComponentType for mapping
        var componentType = _fixture.CreateComponentType(
            componentName: "Test Component",
            referenceId: "REPL_001"
        );
        await _dbContext.ComponentTypes.AddAsync(componentType);
        await _dbContext.SaveChangesAsync();

        var template1 = _fixture.CreateTemplate(
            name: "Test Template 1",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        var template2 = _fixture.CreateTemplate(
            name: "Test Template 2",
            companyId: "COMPANY_456",
            replicationTypeId: "REPL_001",
            actionType: "UPDATE"
        );

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "CREATE");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Template 1", result.Name);
        Assert.Equal("REPL_001", result.ReplicationTypeId);
        Assert.Equal("CREATE", result.ActionType);
    }

    [Fact]
    public async Task GetTemplateByReplicationTypeIdAndActionType_ShouldReturnCompanyTemplate_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var template1 = _fixture.CreateTemplate(
            name: "Company Template",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        var template2 = _fixture.CreateTemplate(
            name: "Other Company Template",
            companyId: "COMPANY_456",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "CREATE");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Company Template", result.Name);
        Assert.Equal("COMPANY_123", result.CompanyId);
    }

    [Fact]
    public async Task GetTemplateByReplicationTypeIdAndActionType_ShouldReturnNull_WhenNotFound()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        await _repository.AddAsync(template);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_999", "CREATE");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetTemplateByReplicationTypeIdAndActionType_ShouldBeCaseInsensitive_ForActionType()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(
            name: "Test Template",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        await _repository.AddAsync(template);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result1 = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "CREATE");
        var result2 = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "create");
        var result3 = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "Create");

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
        Assert.Equal("Test Template", result1.Name);
        Assert.Equal("Test Template", result2.Name);
        Assert.Equal("Test Template", result3.Name);
    }

    #endregion

    #region GetTemplateByReplicationTypeId Tests

    [Fact]
    public async Task GetTemplateByReplicationTypeId_ShouldReturnAllTemplates_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        // Create ComponentTypes for mapping
        var componentType1 = _fixture.CreateComponentType(
            componentName: "Component 1",
            referenceId: "REPL_001"
        );
        var componentType2 = _fixture.CreateComponentType(
            componentName: "Component 2",
            referenceId: "REPL_002"
        );
        await _dbContext.ComponentTypes.AddAsync(componentType1);
        await _dbContext.ComponentTypes.AddAsync(componentType2);
        await _dbContext.SaveChangesAsync();

        var template1 = _fixture.CreateTemplate(
            name: "Template 1",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        var template2 = _fixture.CreateTemplate(
            name: "Template 2",
            companyId: "COMPANY_456",
            replicationTypeId: "REPL_001",
            actionType: "UPDATE"
        );
        var template3 = _fixture.CreateTemplate(
            name: "Template 3",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_002",
            actionType: "CREATE"
        );

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);
        await _repository.AddAsync(template3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeId("REPL_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, t => Assert.Equal("REPL_001", t.ReplicationTypeId));
        Assert.Contains(result, t => t.Name == "Template 1");
        Assert.Contains(result, t => t.Name == "Template 2");

        // Should be ordered by Id descending
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    [Fact]
    public async Task GetTemplateByReplicationTypeId_ShouldReturnCompanyTemplates_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var template1 = _fixture.CreateTemplate(
            name: "Company Template 1",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        var template2 = _fixture.CreateTemplate(
            name: "Other Company Template",
            companyId: "COMPANY_456",
            replicationTypeId: "REPL_001",
            actionType: "UPDATE"
        );
        var template3 = _fixture.CreateTemplate(
            name: "Company Template 2",
            companyId: "COMPANY_123",
            replicationTypeId: "REPL_001",
            actionType: "DELETE"
        );

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);
        await _repository.AddAsync(template3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeId("REPL_001");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, t => Assert.Equal("REPL_001", t.ReplicationTypeId));
        Assert.All(result, t => Assert.Equal("COMPANY_123", t.CompanyId));
        Assert.Contains(result, t => t.Name == "Company Template 1");
        Assert.Contains(result, t => t.Name == "Company Template 2");
    }

    [Fact]
    public async Task GetTemplateByReplicationTypeId_ShouldReturnEmpty_WhenReplicationTypeIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(replicationTypeId: "REPL_001");
        await _repository.AddAsync(template);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateByReplicationTypeId("REPL_999");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsTemplateByReplicationTypeIdAndActionTypeNameUnique Tests

    [Fact]
    public async Task IsTemplateByReplicationTypeIdAndActionTypeNameUnique_ShouldReturnTrue_WhenCombinationExists()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateByReplicationTypeIdAndActionTypeNameUnique("REPL_001", "CREATE");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTemplateByReplicationTypeIdAndActionTypeNameUnique_ShouldReturnFalse_WhenCombinationDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateByReplicationTypeIdAndActionTypeNameUnique("REPL_001", "UPDATE");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsTemplateByReplicationTypeIdAndActionTypeNameUnique_ShouldReturnFalse_WhenReplicationTypeIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        var template = _fixture.CreateTemplate(
            replicationTypeId: "REPL_001",
            actionType: "CREATE"
        );
        await _repository.AddAsync(template);

        // Act
        var result = await _repository.IsTemplateByReplicationTypeIdAndActionTypeNameUnique("REPL_999", "CREATE");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnCompanyTemplates()
    {
        // Arrange
        await ClearDatabase();

        var template1 = _fixture.CreateTemplate(name: "Template 1", companyId: "COMPANY_123");
        var template2 = _fixture.CreateTemplate(name: "Template 2", companyId: "COMPANY_456");
        var template3 = _fixture.CreateTemplate(name: "Template 3", companyId: "COMPANY_123");

        await _repository.AddAsync(template1);
        await _repository.AddAsync(template2);
        await _repository.AddAsync(template3);

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, t => Assert.Equal("COMPANY_123", t.CompanyId));
        Assert.Contains(result, t => t.Name == "Template 1");
        Assert.Contains(result, t => t.Name == "Template 3");
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task TemplateRepository_ShouldHandleComplexScenarios()
    {
        // Arrange
        await ClearDatabase();

        // Create ComponentTypes for mapping
        var componentTypes = new List<ComponentType>
        {
            _fixture.CreateComponentType(componentName: "Component 1", referenceId: "REPL_001"),
            _fixture.CreateComponentType(componentName: "Component 2", referenceId: "REPL_002")
        };

        foreach (var componentType in componentTypes)
        {
            await _dbContext.ComponentTypes.AddAsync(componentType);
        }
        await _dbContext.SaveChangesAsync();

        var templates = new List<Domain.Entities.Template>
        {
            _fixture.CreateTemplate(
                name: "Workflow Template",
                companyId: "COMPANY_123",
                replicationTypeId: "REPL_001",
                actionType: "CREATE",
                type: "WORKFLOW"
            ),
            _fixture.CreateTemplate(
                name: "Form Template",
                companyId: "COMPANY_123",
                replicationTypeId: "REPL_001",
                actionType: "UPDATE",
                type: "FORM"
            ),
            _fixture.CreateTemplate(
                name: "Report Template",
                companyId: "COMPANY_456",
                replicationTypeId: "REPL_002",
                actionType: "CREATE",
                type: "REPORT"
            )
        };

        foreach (var template in templates)
        {
            await _repository.AddAsync(template);
        }

        // Act & Assert - Parent user
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var allTemplateNames = await _repository.GetTemplateNames();
        Assert.Equal(3, allTemplateNames.Count);

        var repl001Templates = await _repository.GetTemplateByReplicationTypeId("REPL_001");
        Assert.Equal(2, repl001Templates.Count);

        var workflowTemplate = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "CREATE");
        Assert.NotNull(workflowTemplate);
        Assert.Equal("Workflow Template", workflowTemplate.Name);

        // Act & Assert - Non-parent user
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var companyTemplateNames = await _repository.GetTemplateNames();
        Assert.Equal(2, companyTemplateNames.Count);

        var companyRepl001Templates = await _repository.GetTemplateByReplicationTypeId("REPL_001");
        Assert.Equal(2, companyRepl001Templates.Count);

        var companyWorkflowTemplate = await _repository.GetTemplateByReplicationTypeIdAndActionType("REPL_001", "CREATE");
        Assert.NotNull(companyWorkflowTemplate);
        Assert.Equal("Workflow Template", companyWorkflowTemplate.Name);

        // Name uniqueness tests
        var nameExists = await _repository.IsTemplateNameExist("Workflow Template", Guid.NewGuid().ToString());
        Assert.True(nameExists);

        var nameUnique = await _repository.IsTemplateNameUnique("Workflow Template");
        Assert.True(nameUnique);

        var combinationUnique = await _repository.IsTemplateByReplicationTypeIdAndActionTypeNameUnique("REPL_001", "CREATE");
        Assert.True(combinationUnique);
    }

    #endregion
    [Fact]
    public async Task PaginatedListAllAsync_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        var listValue = _fixture.TemplatePaginationList;
        foreach (var val in listValue)
            val.IsActive = true;
        await _dbContext.Templates.AddRangeAsync(listValue);
        await _dbContext.SaveChangesAsync();

        string? searchString = null;
        var spec = new TemplateFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 20,
            productFilterSpec: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );
        Assert.NotNull(result);

    }


    [Fact]
    public void GetPaginatedQuery_ReturnsActiveFormsOrderedById()
    {
        ClearDatabase();
        // Arrange
        var Templates = new[]
        {
            new Domain.Entities.Template
            {
                 Id = 1,
                Name = "Form 1",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = TemplateFixture.CompanyId
            },
            new Domain.Entities.Template
            {
                Id = 2,
                Name = "Form 2",
                IsActive = true,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = TemplateFixture.CompanyId
            },
            new Domain.Entities.Template
            {
                Id = 3,
                Name = "Inactive Form",
                IsActive = false,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = TemplateFixture.CompanyId
            }
        };

        _dbContext.Templates.AddRange(Templates);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery().ToList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Equal(2, result[0].Id); // Should be ordered by Id descending
        Assert.Equal(1, result[1].Id);
    }

    private async Task ClearDatabase()
    {
        _dbContext.Templates.RemoveRange(_dbContext.Templates);
        _dbContext.ComponentTypes.RemoveRange(_dbContext.ComponentTypes);
        await _dbContext.SaveChangesAsync();
    }
}
