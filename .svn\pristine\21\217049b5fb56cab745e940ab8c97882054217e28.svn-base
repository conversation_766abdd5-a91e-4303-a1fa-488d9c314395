﻿using ContinuityPatrol.Application.Features.ServerLog.Commands.Create;
using ContinuityPatrol.Application.Features.ServerLog.Commands.Delete;
using ContinuityPatrol.Application.Features.ServerLog.Commands.Update;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetList;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ServerLogModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ServerLogsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<ServerLogListVm>>> GetServerLogList()
    {
        Logger.LogDebug("Get Server Log List");

        return Ok(await Mediator.Send(new GetServerLogListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateServerLogResponse>> CreateServerLog([FromBody] CreateServerLogCommand createServerLogCommand)
    {
        Logger.LogDebug($"Create Server Log '{createServerLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateServerLog), await Mediator.Send(createServerLogCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteServerLogResponse>> DeleteServerLog(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ServerLog Id");

        Logger.LogDebug($"Delete SereverLog Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteServerLogCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<ServerLogListVm>>> GetPaginatedSites([FromQuery] GetServerLogPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in ServerLog Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateServerLogResponse>> UpdateSite([FromBody] UpdateServerLogCommand updateSiteCommand)
    {
        Logger.LogDebug($"Update ServerLog '{updateSiteCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateSiteCommand));
    }

    [Route("name-unique"), HttpGet]
    public async Task<ActionResult> IsServerLogNameUnique(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Server Log Name");

        Logger.LogDebug($"Check Name Exists Detail by Server Log Name '{name}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetServerLogNameUniqueQuery { Name = name, Id = id }));
    }

    [HttpGet("{id}", Name = "GetServerLog")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetServerLogDetailVm>> GetServerLogDetail(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Server Log Id");

        Logger.LogDebug($"Get Server Log Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetServerLogDetailQuery { Id = id }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllServerLogsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllServerLogsNameCacheKey };

        ClearCache(cacheKeys);
    }
}
