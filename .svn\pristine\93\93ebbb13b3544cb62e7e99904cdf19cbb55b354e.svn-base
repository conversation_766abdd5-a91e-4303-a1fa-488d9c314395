namespace ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetNameUnique;

public class GetMenuBuilderNameUniqueQueryHandler : IRequestHandler<GetMenuBuilderNameUniqueQuery, bool>
{
    private readonly IMenuBuilderRepository _menuBuilderRepository;

    public GetMenuBuilderNameUniqueQueryHandler(IMenuBuilderRepository menuBuilderRepository)
    {
        _menuBuilderRepository = menuBuilderRepository;
    }

    public async Task<bool> Handle(GetMenuBuilderNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _menuBuilderRepository.IsNameExist(request.Name, request.Id);
    }
}
