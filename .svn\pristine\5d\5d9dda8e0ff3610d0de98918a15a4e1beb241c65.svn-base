﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class NodeWorkflowExecutionRepository : BaseRepository<NodeWorkflowExecution>, INodeWorkflowExecutionRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public NodeWorkflowExecutionRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<NodeWorkflowExecution>> ListAllAsync()
    {
        return FindByFilter(businessService => businessService.Status.Trim().ToLower().Equals("running"));
    }

    public Task<List<NodeWorkflowExecution>> GetNodeWorkflowExecutionByWorkflowOperationId(string workflowOperationId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowOperationId, "WorkflowOperationId",
            " WorkflowOperationId cannot be invalid");

        return _dbContext.NodeWorkflowExecutions
            .Active()
            .Where(e => e.WorkflowOperationId.Equals(workflowOperationId))
            .ToListAsync();
    }

    public async Task<NodeWorkflowExecution> GetNodeWorkflowExecutionByInfraSchedulerId(string infraSchedulerId)
    {
        var nodeWorkflowExecutionResult = await _dbContext.NodeWorkflowExecutions.Active()
            .Where(e => e.InfraObjectSchedulerId.Equals(infraSchedulerId))
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync();

        return nodeWorkflowExecutionResult;
    }

    public async Task<NodeWorkflowExecution> GetNodeWorkflowExecutionByJobId(string jobId)
    {
        var nodeWorkflowExecutionResult = await _dbContext.NodeWorkflowExecutions.Active()
            .Where(e => e.JobId.Equals(jobId))
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync();

        return nodeWorkflowExecutionResult;
    }
}