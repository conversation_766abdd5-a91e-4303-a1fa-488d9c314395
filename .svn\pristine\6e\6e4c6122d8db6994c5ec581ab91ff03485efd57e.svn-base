using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AdPasswordExpireFixture : IDisposable
{
    public List<AdPasswordExpire> AdPasswordExpirePaginationList { get; set; }
    public List<AdPasswordExpire> AdPasswordExpireList { get; set; }
    public AdPasswordExpire AdPasswordExpireDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public AdPasswordExpireFixture()
    {
        var fixture = new Fixture();

        AdPasswordExpireList = fixture.Create<List<AdPasswordExpire>>();

        AdPasswordExpirePaginationList = fixture.CreateMany<AdPasswordExpire>(20).ToList();


        AdPasswordExpireDto = fixture.Create<AdPasswordExpire>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
