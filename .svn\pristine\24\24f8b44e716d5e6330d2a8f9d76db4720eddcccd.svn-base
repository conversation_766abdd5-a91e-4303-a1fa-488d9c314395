﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Queries;

public class GetWorkflowCategoryListQueryHandlerTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    private readonly GetWorkflowCategoryListQueryHandler _handler;

    public GetWorkflowCategoryListQueryHandlerTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.GetWorkflowCategoryRepository(_workflowCategoryFixture.WorkflowCategories);

        _handler = new GetWorkflowCategoryListQueryHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowCategoriesList()
    {
        var result = await _handler.Handle(new GetWorkflowCategoryListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowCategoryListVm>>();

        result[0].Id.ShouldBe(_workflowCategoryFixture.WorkflowCategories[0].ReferenceId);
        result[0].Name.ShouldBe(_workflowCategoryFixture.WorkflowCategories[0].Name);
        result[0].Properties.ShouldBe(_workflowCategoryFixture.WorkflowCategories[0].Properties);
        result[0].Version.ShouldBe(_workflowCategoryFixture.WorkflowCategories[0].Version);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.GetWorkflowCategoryEmptyRepository();

        var handler = new GetWorkflowCategoryListQueryHandler(_workflowCategoryFixture.Mapper, _mockWorkflowCategoryRepository.Object);

        var result = await handler.Handle(new GetWorkflowCategoryListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowCategoryListQuery(), CancellationToken.None);

        _mockWorkflowCategoryRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}