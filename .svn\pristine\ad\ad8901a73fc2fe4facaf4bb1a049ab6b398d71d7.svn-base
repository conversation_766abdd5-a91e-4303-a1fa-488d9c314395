﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.Hubs;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;

public class UpdateWorkflowOperationGroupCommandHandler : IRequestHandler<UpdateWorkflowOperationGroupCommand,
    UpdateWorkflowOperationGroupResponse>
{
    private readonly IHubContext<WorkflowHub> _hubContext;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IJobScheduler _client;
    private readonly IWorkflowRunningActionRepository _workflowRunningActionRepository;

    public UpdateWorkflowOperationGroupCommandHandler(IMapper mapper,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository, IHubContext<WorkflowHub> hubContext,
        ILoadBalancerRepository nodeConfigurationRepository, IJobScheduler client, IWorkflowRunningActionRepository workflowRunningActionRepository)
    {
        _mapper = mapper;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _hubContext = hubContext;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _client = client;
        _workflowRunningActionRepository = workflowRunningActionRepository;
    }

    public async Task<UpdateWorkflowOperationGroupResponse> Handle(UpdateWorkflowOperationGroupCommand request,
        CancellationToken cancellationToken)
    {
        var isReload = false;

        if (request.ConditionalOperation == 3)
        {
            var runningWorkflowOperationGroups =
                await _workflowRunningActionRepository.GetWorkflowRunningActionsByWorkflowId(request.WorkflowId);

            await _workflowRunningActionRepository.RemoveRangeAsync(runningWorkflowOperationGroups);

            isReload = true;
        }

        await _hubContext.Clients.All.SendAsync("WorkflowOperationGroup Message", new
        {
            request.Id,
            request.CurrentActionName,
            request.Status,
            request.ProgressStatus,
            request.ActionMode,
            request.IsAbort,
            request.IsResume,
            request.IsPause,
            request.IsReExecute,
            request.WaitToNext,
            request.ConditionalOperation,
            isReload
        }, cancellationToken: cancellationToken);

        var eventToUpdate = await _workflowOperationGroupRepository.GetByReferenceIdAsync(request.Id);

        var failedCondition = eventToUpdate.ConditionalOperation;

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.WorkflowOperationGroup), request.Id);

        if (failedCondition != 7 || request.Status.ToLower().Equals("aborted"))
        {
            _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowOperationGroupCommand),
                typeof(Domain.Entities.WorkflowOperationGroup));

            await _workflowOperationGroupRepository.UpdateAsync(eventToUpdate);
        }

        await UpdateCompleted(eventToUpdate, cancellationToken);

        if (failedCondition == 7)
        {
            var (baseUrl, typeCategory) = await GetLoadBalancerNodeConfiguration();

            var url = UrlHelper.GenerateFailedActionUrl(typeCategory, baseUrl, eventToUpdate.WorkflowOperationId);

            var jobData = new Dictionary<string, string> { ["url"] = url };

            await _client.ScheduleJob(eventToUpdate.WorkflowOperationId, jobData);
        }


        if (eventToUpdate.IsPause == 1 || eventToUpdate.IsResume == 1)
        {
            var (baseUrl, _) = await GetLoadBalancerNodeConfiguration();

            var url = UrlHelper.GeneratePauseResumeUrl(baseUrl, eventToUpdate.ReferenceId);
            
            var jobData = new Dictionary<string, string> { ["url"] = url };

            await _client.ScheduleJob(eventToUpdate.WorkflowOperationId, jobData);
        }

        var response = new UpdateWorkflowOperationGroupResponse
        {
            Message = "Workflow operation Group updated successfully.",

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }

    private async Task UpdateCompleted(Domain.Entities.WorkflowOperationGroup eventToUpdate, CancellationToken cancellationToken)
    {
        var status = eventToUpdate.Status.Trim().ToLower();
        if (status.Equals("completed") || status.Equals("aborted"))
        {
            var workflowOperationGroups = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(eventToUpdate.WorkflowOperationId);

            var isCompleted = workflowOperationGroups.Where(x => x.Status.Trim().ToLower().Equals("completed") || x.Status.Trim().ToLower().Equals("aborted")).ToList();

            var isWorkflowCompleted = workflowOperationGroups.Count == isCompleted.Count;   

            var id = eventToUpdate.ReferenceId;

            await _hubContext.Clients.All.SendAsync("WorkflowOperationGroup Message", new
            {
                id,
                eventToUpdate.CurrentActionName,
                eventToUpdate.Status,
                eventToUpdate.ProgressStatus,
                eventToUpdate.ActionMode,
                eventToUpdate.IsAbort,
                eventToUpdate.IsResume,
                eventToUpdate.IsPause,
                eventToUpdate.IsReExecute,
                eventToUpdate.WaitToNext,
                eventToUpdate.ConditionalOperation,
                IsWorkflowCompleted = isWorkflowCompleted
            }, cancellationToken: cancellationToken);

        }
    }


    private async Task<(string baseUrl, string typeCategory)> GetLoadBalancerNodeConfiguration()
    {
        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.WorkflowService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null)
            throw new InvalidException("LoadBalancer not configured!.");

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        return (baseUrl, nodeConfig.TypeCategory);
    }
}