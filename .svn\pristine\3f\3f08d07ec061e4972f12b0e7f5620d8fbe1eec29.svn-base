﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Commands.Create;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringStatus.Commands
{
    public class CreateSVCGMMonitorStatusTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorStatusRepository> _mockSVCGMMonitorStatusRepository;
        private readonly Mock<ILogger<CreateSVCGMMonitorStatusCommandHandler>> _mockLogger;
        private readonly CreateSVCGMMonitorStatusCommandHandler _handler;

        public CreateSVCGMMonitorStatusTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorStatusRepository = new Mock<ISVCGMMonitorStatusRepository>();
            _mockLogger = new Mock<ILogger<CreateSVCGMMonitorStatusCommandHandler>>();
            _handler = new CreateSVCGMMonitorStatusCommandHandler(_mockSVCGMMonitorStatusRepository.Object, _mockMapper.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_ShouldSuccessfullyCreateSVCGMMonitorStatus_AndReturnResponse()
        {
            var command = new CreateSVCGMMonitorStatusCommand
            {
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            _mockMapper.Setup(m => m.Map<SVCGMMonitorStatus>(command))
                .Returns(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync(entity);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.Id);
            Assert.Contains("SVCGMMonitorStatus", result.Message);

            _mockMapper.Verify(m => m.Map<SVCGMMonitorStatus>(command), Times.Once);
            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAddAsync_Once()
        {
            var command = new CreateSVCGMMonitorStatusCommand
            {
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            _mockMapper.Setup(m => m.Map<SVCGMMonitorStatus>(command))
                .Returns(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync(entity);

            await _handler.Handle(command, CancellationToken.None);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallMapperOnce()
        {
            var command = new CreateSVCGMMonitorStatusCommand
            {
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            _mockMapper.Setup(m => m.Map<SVCGMMonitorStatus>(command))
                .Returns(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync(entity);

            await _handler.Handle(command, CancellationToken.None);

            _mockMapper.Verify(m => m.Map<SVCGMMonitorStatus>(command), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldLogCreationProcess()
        {
            var command = new CreateSVCGMMonitorStatusCommand
            {
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                WorkflowName = "Test Monitor",
                Type = "Active"
            };

            _mockMapper.Setup(m => m.Map<SVCGMMonitorStatus>(command))
                .Returns(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync(entity);

            await _handler.Handle(command, CancellationToken.None);
        }
    }
}
