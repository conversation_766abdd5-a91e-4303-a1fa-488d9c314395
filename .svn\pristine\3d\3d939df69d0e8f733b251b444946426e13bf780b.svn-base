using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Services.Api.Provider;

public class ApiProvider : IDataProvider
{
    public ICyberJobWorkflowSchedulerService CyberJobWorkflowScheduler { get; set; }
    public IGlobalVariableService GlobalVariable { get; set; }

    public ICGExecutionReportService CGExecutionReport { get; set; }
    public ITwoStepAuthenticationService TwoStepAuthentication { get; set; }
    public IFiaCostService FiaCost { get; set; }

    public IApprovalMatrixUsersService ApprovalMatrixUsers { get; set; }

    public IDataSyncJobService DataSyncJob { get; set; }
    public IComponentSaveAllService ComponentSaveAll { get; set; }
    public IWorkflowDrCalenderService WorkflowDrCalender { get; set; }

    public IAdPasswordJobService AdPasswordJob { get; set; }

    public IAdPasswordExpireService AdPasswordExpire { get; set; }

    public IFiaIntervalService FiaInterval { get; set; }

    public IFiaImpactTypeService FiaImpactType { get; set; }

    public IFiaImpactCategoryService FiaImpactCategory { get; set; }

    public ICyberAirGapService CyberAirGap { get; set; }

    public ICyberSnapsService CyberSnaps { get; set; }

    public ICyberAlertService CyberAlert { get; set; }
    public ICyberComponentMappingService CyberComponentMapping { get; set; }
    public ICyberAirGapLogService CyberAirGapLog { get; set; }

    public ICyberAirGapStatusService CyberAirGapStatus { get; set; }

    public IDriftEventService DriftEvent { get; set; }
    public ICyberJobManagementService CyberJobManagement { get; set; }

    public ICyberComponentGroupService CyberComponentGroup { get; set; }

    public ICyberComponentService CyberComponent { get; set; }


    public IDriftResourceSummaryService DriftResourceSummary { get; set; }

    public IDriftManagementMonitorStatusService DriftManagementMonitorStatus { get; set; }

    public IDriftJobService DriftJob { get; set; }

    public IApprovalMatrixApprovalService ApprovalMatrixApproval { get; set; }

    public IApprovalMatrixRequestService ApprovalMatrixRequest { get; set; }

    public IDriftProfileService DriftProfile { get; set; }

    public IDriftImpactTypeMasterService DriftImpactTypeMaster { get; set; }

    public IDriftCategoryMasterService DriftCategoryMaster { get; set; }

    public IDriftParameterService DriftParameter { get; set; }

    public IEmployeeService Employee { get; set; }

    public IBulkImportServices BulkImport { get; set; }
    public IBulkImportActionResultService BulkImportActionResult { get; set; }

    public IBulkImportOperationGroupService BulkImportOperationGroup { get; set; }

    public IBulkImportOperationService BulkImportOperation { get; set; }

    public IDynamicDashboardWidgetService DynamicDashboardWidget { get; set; }

    public IDynamicDashboardMapService DynamicDashboardMap { get; set; }

    public IDynamicSubDashboardService DynamicSubDashboard { get; set; }

    public IDynamicDashboardService DynamicDashboard { get; set; }

    public IVeritasClusterService VeritasCluster { get; set; }

    public IHacmpClusterService HacmpCluster { get; set; }

    public IFiaTemplateService FiaTemplate { get; set; }

    public IBiaRulesService BiaRule { get; set; }

    public IBackUpLogService BackUpLog { get; set; }

    public IInfraMasterService InfraMaster { get; set; }

    public IBackUpService BackUp { get; set; }

    public IArchiveService Archive { get; set; }

    public IPageWidgetService PageWidget { get; set; }

    public IRsyncOptionService RsyncOption { get; set; }

    public IRoboCopyService RoboCopy { get; set; }

    public IDataSyncOptionsService DataSync { get; set; }

    public IImpactActivityService ImpactActivity { get; set; }
    public IPageSolutionMappingService PageSolutionMapping { get; set; }
    public IPageBuilderService PageBuilder { get; set; }

    public ISiteLocationService SiteLocation { get; set; }
    public IIncidentManagementSummaryService IncidentManagementSummary { get; set; }
    public IIncidentManagementService IncidentManagement { get; set; }

    public IWorkflowActionFieldMasterService WorkflowActionFieldMaster { get; set; }

    public ICompanyService Company { get; set; }
    public ISolutionHistoryService SolutionHistory { get; set; }
    public IAlertInformationService AlertInformation { get; set; }
    public IAlertService Alerts { get; set; }
    public IAccountService Account { get; set; }
    public IUserService User { get; set; }
    public IServerService Server { get; set; }
    public IServerTypeService ServerType { get; set; }
    public ISiteService Site { get; set; }
    public IServerLogService ServerLog { get; set; }
    public IFormService Form { get; set; }
    public ILicenseManagerService LicenseManager { get; set; }
    public IWorkflowActionService WorkflowAction { get; set; }
    public IWorkflowCategoryService WorkflowCategory { get; set; }
    public IAccessManagerService AccessManager { get; set; }
    public IBusinessFunctionService BusinessFunction { get; set; }
    public IBusinessServiceService BusinessService { get; set; }
    public ICredentialProfileService CredentialProfile { get; set; }
    public IDatabaseService Database { get; set; }
    public IDataSetService DataSet { get; set; }
    public IInfraObjectService InfraObject { get; set; }
    public ILoadBalancerService LoadBalancer { get; set; }
    public INodeService Node { get; set; }
    public IReplicationService Replication { get; set; }
    public IMonitorService Monitor { get; set; }
    public IComponentTypeService ComponentType { get; set; }
    public ISettingService Setting { get; set; }
    public ISingleSignOnService SingleSignOn { get; set; }
    public ISiteTypeService SiteType { get; set; }
    public IFormTypeService FormType { get; set; }
    public IFormHistoryService FormHistory { get; set; }
    public ISmtpConfigurationService SmtpConfiguration { get; set; }
    public ITableAccessService TableAccess { get; set; }
    public IUserActivityService UserActivity { get; set; }
    public IUserRoleService UserRole { get; set; }
    public ITemplateService Template { get; set; }
    public IWorkflowHistoryService WorkflowHistory { get; set; }
    public IWorkflowInfraObjectService WorkflowInfraObject { get; set; }
    public IWorkflowProfileInfoService WorkflowProfileInfo { get; set; }
    public IWorkflowProfileService WorkflowProfile { get; set; }
    public IWorkflowOperationGroupService WorkflowOperationGroup { get; set; }
    public IWorkflowPermissionService WorkflowPermission { get; set; }
    public IMssqlAlwaysOnMonitorLogsService MssqlAlwaysOnMonitorLogs { get; set; }
    public IOracleMonitorLogsService OracleMonitorLogs { get; set; }
    public IOracleRACMonitorLogsService OracleRACMonitorLogs { get; set; }
    public IMssqlMonitorStatusService MssqlMonitorStatus { get; set; }
    public IMysqlMonitorLogsService MysqlMonitorLogs { get; set; }
    public IMssqlAlwaysOnMonitorStatusService MssqlAlwaysOnMonitorStatus { get; set; }
    public IMssqlMonitorLogsService MssqlMonitorLogs { get; set; }
    public IAlertNotificationService AlertNotification { get; set; }
    public IAlertMasterService AlertMasterService { get; set; }
    public IOracleMonitorStatusService OracleMonitorStatus { get; set; }
    public IOracleRACMonitorStatusService OracleRACMonitorStatus { get; set; }
    public IPostgresMonitorLogsService PostgresMonitorLogs { get; set; }
    public IPostgresMonitorStatusService PostgresMonitorStatus { get; set; }
    public IMysqlMonitorStatusService MysqlMonitorStatus { get; set; }
    public IWorkflowActionTypeService WorkflowActionTypes { get; set; }
    public IWorkflowOperationService WorkflowOperation { get; set; }
    public IPluginManagerService PluginManager { get; set; }
    public IDrReadyService DrReady { get; set; }
    public IAlertReceiverService AlertReceiver { get; set; }
    public IJobService JobService { get; set; }
    public IDataSetColumnsService DataSetColumns { get; set; }
    public IWorkflowService Workflow { get; set; }
    public IReplicationMasterService ReplicationMaster { get; set; }
    public IInfraReplicationMappingService InfraReplicationMapping { get; set; }
    public IAboutCpService AboutCP { get; set; }
    public IFormMappingService FormMapping { get; set; }
    public IMonitorServicesService MonitorServices { get; set; }
    public IWorkflowActionResultService WorkflowActionResult { get; set; }
    public IRiskMitigationService RiskMitigation { get; set; }
    public IUserInfraObjectService UserInfraObject { get; set; }
    public ISmsConfigurationService SmsConfiguration { get; set; }
    public IBusinessServiceAvailabilityService BusinessServiceAvailability { get; set; }
    public IBusinessServiceEvaluationService BusinessServiceEvaluation { get; set; }
    public ILicenseInfoService LicenseInfo { get; set; }
    public IBusinessServiceHealthStatusService BusinessServiceHealthStatus { get; set; }
    public IHeatMapStatusService HeatMapStatus { get; set; }
    public IDrReadyStatusService DrReadyStatus { get; set; }
    public IInfraObjectInfoService InfraObjectInfo { get; set; }
    public IInfraSummaryService InfraSummary { get; set; }
    public IImpactAvailabilityService ImpactAvailability { get; set; }
    public IDashboardViewService DashboardView { get; set; }
    public IWorkflowExecutionTempService WorkflowExecutionTemp { get; set; }
    public IDashboardViewLogService DashboardViewLog { get; set; }
    //public INodeConfigurationService NodeConfiguration { get; set; }
    public IRpoSlaDeviationReportService RpoSlaDeviationReport { get; set; }
    public IUserGroupService UserGroup { get; set; }
    public IManageWorkflowListService ManageWorkflowList { get; set; }
    public IFourEyeApproverService FourEyeApproverService { get; set; }
    public IGroupPolicyService GroupPolicy { get; set; }
    public IDrCalendarService DRCalenderService { get; set; }
    public IReportService Report { get; set; }
    public IReportScheduleService ReportSchedule { get; set; }
    public IDb2HaDrMonitorStatusService Db2HaDrMonitorStatus { get; set; }
    public IMsSqlNativeLogShippingMonitorStatusService MsSqlNativeLogShippingMonitorStatus { get; set; }
    public IMongoDbMonitorStatusService MongoDbMonitorStatus { get; set; }
    public ISvcMsSqlMonitorStatusService SvcMsSqlMonitorStatus { get; set; }
    public IWorkflowPredictionService WorkflowPredictionServices { get; set; }
    public IGlobalSettingService GlobalSettings { get; set; }
    public IMSSQLDbMirroringMonitorStatusService MSSQLDbMirroringMonitorStatus { get; set; }
    public IServerSubTypeService ServerSubType { get; set; }

    public ITeamMasterService TeamMasterService { get; set; }
    public ITeamResourceService TeamResourceService { get; set; }
    public IApprovalMatrixService approvalMatrixService { get; set; }
    public IUserLoginService UserLogin { get; set; }
    public IEscalationMatrixService EscalationService { get; set; }
    public IEscalationMatrixLevelService EscalationMatrixLevelService { get; set; }
    public IDb2HaDrMonitorLogService Db2HaDrMonitorLog { get; set; }
    public IRsyncJobService RsyncJob { get; set; }
    public IRoboCopyJobService RoboCopyJob { get; set; }
    public IReplicationJobService ReplicationJobService { get; set; }
    public IRpForVmMonitorStatusService RpForVmMonitorStatusService { get; set; }
    public IUserInfoService UserInfo { get; set; }
    public IRpForVmCGMonitorLogsService RpForVmCGMonitorLogs { get; set; }
    public IRpForVmCGMonitorStatusService RpForVmCGMonitorStatus { get; set; }
    public IFastCopyMonitorService FastCopyMonitor { get; set; }

    public ApiProvider(
        ICyberJobWorkflowSchedulerService cyberJobWorkflowScheduler,
		IGlobalVariableService globalVariable,


        IFiaCostService fiaCost,

        IApprovalMatrixUsersService approvalMatrixUsers,

        IWorkflowDrCalenderService workflowDrCalender,

        IAdPasswordJobService adPasswordJob,

        IAdPasswordExpireService adPasswordExpire,

        IFiaIntervalService fiaInterval,

        IFiaImpactTypeService fiaImpactType,

        IFiaImpactCategoryService fiaImpactCategory,

        ICyberAirGapService cyberAirGap,

        ICyberSnapsService cyberSnaps,

        ICyberAlertService cyberAlert,

        ICyberAirGapLogService cyberAirGapLog,

        ICyberAirGapStatusService cyberAirGapStatus,

        IDriftEventService driftEventService,
        ICompanyService companyService,
        ICyberJobManagementService cyberJobManagement,

        ICyberComponentGroupService cyberComponentGroup,

        ICyberComponentService cyberComponent,



        IDriftResourceSummaryService driftResourceSummary,

        IDriftManagementMonitorStatusService driftManagementMonitorStatus,

        IDriftJobService driftJob,

        IApprovalMatrixApprovalService approvalMatrixApproval,

        IApprovalMatrixRequestService approvalMatrixRequest,

        IDriftProfileService driftProfile,

        IDriftImpactTypeMasterService driftImpacttypeMaster,

        IDriftCategoryMasterService driftCategoryMaster,

        IDriftParameterService driftParameter,

        IEmployeeService employee,


        IBulkImportServices bulkImport,
        IBulkImportActionResultService bulkImportActionResult,

        IBulkImportOperationGroupService bulkImportOperationGroup,

        IBulkImportOperationService bulkImportOperation,

        IDynamicDashboardWidgetService dynamicDashboardWidget,

        IDynamicDashboardMapService dynamicDashboardMap,

        IDynamicSubDashboardService dynamicSubDashboard,

        IDynamicDashboardService dynamicDashboard,

        IVeritasClusterService veritasCluster,

        IHacmpClusterService hacmpCluster,

        IFiaTemplateService fiaTemplate,

        IBiaRulesService biaRule,

        IBackUpLogService backUpLog,

        IInfraMasterService infraMaster,

        IBackUpService backUp,

        IArchiveService archive,

        IPageWidgetService pageWidget,

        IRsyncOptionService rsyncOption,

        IRoboCopyService roboCopy,

        IDataSyncOptionsService dataSync,

        IImpactActivityService impactActivity,

        IPageBuilderService pageBuilder,

        ISiteLocationService siteLocation,
        IUserLoginService userLoginService,

        IIncidentManagementService incidentManagement,

        IIncidentManagementSummaryService incidentManagementSummary,

        IWorkflowActionFieldMasterService workflowActionFieldMaster,

        ISolutionHistoryService solutionHistoryService,
        IServerService server,
        IBusinessServiceService businessServiceService,
        IUserService user,
        IAccountService account,
        ISiteService site, IServerLogService serverLog,
        IFormService form, ILicenseManagerService licenseManager, IWorkflowActionService workflowAction, IWorkflowCategoryService workflowCategory, IAccessManagerService accessManager, IBusinessFunctionService businessFunction, IBusinessServiceService businessService, ICredentialProfileService credentialProfile, IDatabaseService database, IDataSetService dataSet, IInfraObjectService infraObject, ILoadBalancerService loadBalancer, INodeService node, IReplicationService replication, IComponentTypeService componentType, ISettingService setting, ISingleSignOnService singleSignOn, ISiteTypeService siteType, ISmtpConfigurationService smtpConfiguration, ITableAccessService tableAccess, IUserActivityService userActivity, IUserRoleService userRole, ITemplateService template, IWorkflowHistoryService workflowHistory, IWorkflowInfraObjectService workflowInfraObject, IWorkflowProfileInfoService workflowProfileInfo, IWorkflowProfileService workflowProfile, IAlertService alerts,

        IAlertInformationService alertInformation, IWorkflowPermissionService workflowPermission, IMssqlAlwaysOnMonitorLogsService mssqlAlwaysOnMonitorLogs, IOracleMonitorLogsService oracleMonitorLogs, IWorkflowOperationGroupService workflowOperationGroup, IMssqlAlwaysOnMonitorStatusService mssqlAlwaysOnMonitorStatus, IAlertNotificationService alertNotification, IAlertMasterService alertMasterService, IMssqlMonitorLogsService mssqlMonitorLogs, IOracleRACMonitorLogsService oracleRacMonitorLogs, IMssqlMonitorStatusService mssqlMonitorStatusService, IMssqlMonitorStatusService mssqlMonitorStatus, IMysqlMonitorLogsService mysqlMonitorLogs, IOracleMonitorStatusService oracleMonitorStatus, IOracleRACMonitorStatusService oracleRACMonitorStatus, IPostgresMonitorLogsService postgresMonitorLogs, IPostgresMonitorStatusService postgresMonitorStatus, IMysqlMonitorStatusService mysqlMonitorStatus, IWorkflowActionTypeService workflowActionType, IWorkflowOperationService workflowOperation, IPluginManagerService pluginManager, IJobService jobService, IDrReadyService drReady, IAlertReceiverService alertReceiver, IDataSetColumnsService dataSetColumns, IWorkflowService workflow, IReplicationMasterService replicationMaster, IInfraReplicationMappingService infraReplicationMapping, IAboutCpService aboutCp, IFormTypeService formType, IFormMappingService formMapping, IMonitorServicesService monitorServices, IWorkflowActionResultService workflowActionResult, IRiskMitigationService riskMitigation, IUserInfraObjectService userInfraObject, ISmsConfigurationService smsConfiguration, IBusinessServiceAvailabilityService businessServiceAvailability, IBusinessServiceEvaluationService businessServiceEvaluation, ILicenseInfoService licenseInfo, IBusinessServiceHealthStatusService businessServiceHealthStatus, IHeatMapStatusService heatMapStatus, IDrReadyStatusService drReadyStatus, IInfraObjectInfoService infraObjectInfo, IInfraSummaryService infraSummary, IImpactAvailabilityService impactAvailability, IDashboardViewService dashboardView, IMonitorService monitor, IWorkflowExecutionTempService workflowExecutionTemp, IDashboardViewLogService dashboardViewLog
         , IUserGroupService userGroup, IManageWorkflowListService manageWorkflowList,



        IFourEyeApproverService fourEyeApproverService, IGroupPolicyService groupPolicy,
        IRpoSlaDeviationReportService rpoSlaDeviationReport, IReportService report, IDb2HaDrMonitorStatusService db2HaDrMonitorStatus,
        IMsSqlNativeLogShippingMonitorStatusService msSqlNativeLogShippingMonitorStatus, IMongoDbMonitorStatusService mongoDbMonitorStatus,
        ISvcMsSqlMonitorStatusService svcMsSqlMonitorStatus, IDrCalendarService dRCalenderService, IWorkflowPredictionService workflowPredictionService,
        IGlobalSettingService globalSettings, IMSSQLDbMirroringMonitorStatusService mSsqlDbMirroringMonitorStatus, IServerTypeService serverType,
        IServerSubTypeService serverSubType, ITeamMasterService teamMasterService, ITeamResourceService teamResourceService,

        IApprovalMatrixService approvalMatrixService, IEscalationMatrixService escalationService, IEscalationMatrixLevelService escalationMatrixLevelService, IReportScheduleService reportSchedule, IDb2HaDrMonitorLogService db2HaDrMonitorLog, IPageSolutionMappingService pageSolutionMapping, IRsyncJobService rsyncJob, IRoboCopyJobService roboCopyJob, IFormHistoryService formHistory,
        IReplicationJobService replicationJobService, ICyberComponentMappingService cyberComponentMapping,
          IRpForVmMonitorStatusService rpForVmMonitorStatusService, IComponentSaveAllService componentSaveAll, IUserInfoService userInfoService, IDataSyncJobService dataSyncJob,
          IRpForVmCGMonitorStatusService rpForVmCGMonitorStatusService, IRpForVmCGMonitorLogsService rpForVmCGMonitorLogsService,
          IFastCopyMonitorService fastCopyMonitorService, ICGExecutionReportService cgExecutionReport, ITwoStepAuthenticationService twoStepAuthentication)


    {
        #region ApiProvider
        CyberJobWorkflowScheduler= cyberJobWorkflowScheduler;
		GlobalVariable = globalVariable;

        FiaCost = fiaCost;

        ApprovalMatrixUsers = approvalMatrixUsers;

        WorkflowDrCalender = workflowDrCalender;

        AdPasswordJob = adPasswordJob;

        AdPasswordExpire = adPasswordExpire;

        FiaInterval = fiaInterval;

        FiaImpactType = fiaImpactType;

        FiaImpactCategory = fiaImpactCategory;

        CyberAirGap = cyberAirGap;

        CyberSnaps = cyberSnaps;

        CyberAlert = cyberAlert;

        CyberAirGapLog = cyberAirGapLog;

        CyberAirGapStatus = cyberAirGapStatus;


        DriftEvent = driftEventService;
        CyberJobManagement = cyberJobManagement;

        CyberComponentGroup = cyberComponentGroup;

        CyberComponent = cyberComponent;



        DriftResourceSummary = driftResourceSummary;

        DriftManagementMonitorStatus = driftManagementMonitorStatus;

        DriftJob = driftJob;

        ApprovalMatrixApproval = approvalMatrixApproval;

        ApprovalMatrixRequest = approvalMatrixRequest;

        DriftProfile = driftProfile;

        DriftImpactTypeMaster = driftImpacttypeMaster;

        DriftCategoryMaster = driftCategoryMaster;

        DriftParameter = driftParameter;

        Employee = employee;


        BulkImport = bulkImport;

        BulkImportActionResult = bulkImportActionResult;

        BulkImportOperationGroup = bulkImportOperationGroup;

        BulkImportOperation = bulkImportOperation;

        DynamicDashboardWidget = dynamicDashboardWidget;

        DynamicDashboardMap = dynamicDashboardMap;

        DynamicSubDashboard = dynamicSubDashboard;

        DynamicDashboard = dynamicDashboard;

        VeritasCluster = veritasCluster;

        HacmpCluster = hacmpCluster;

        FiaTemplate = fiaTemplate;

        BiaRule = biaRule;

        BackUpLog = backUpLog;

        InfraMaster = infraMaster;

        BackUp = backUp;

        Archive = archive;

        PageWidget = pageWidget;

        RsyncOption = rsyncOption;

        RoboCopy = roboCopy;

        DataSync = dataSync;

        ImpactActivity = impactActivity;

        PageBuilder = pageBuilder;

        SiteLocation = siteLocation;

        UserLogin = userLoginService;

        IncidentManagement = incidentManagement;

        IncidentManagementSummary = incidentManagementSummary;

        WorkflowActionFieldMaster = workflowActionFieldMaster;

        Account = account;
        SolutionHistory = solutionHistoryService;
        Company = companyService;
        Alerts = alerts;
        Server = server;
        BusinessService = businessServiceService;
        User = user;
        Site = site;
        ServerLog = serverLog;
        Form = form;
        LicenseManager = licenseManager;
        WorkflowAction = workflowAction;
        WorkflowCategory = workflowCategory;
        AccessManager = accessManager;
        BusinessFunction = businessFunction;
        CredentialProfile = credentialProfile;
        Database = database;
        DataSet = dataSet;
        InfraObject = infraObject;
        LoadBalancer = loadBalancer;
        Node = node;
        Replication = replication;
        ComponentType = componentType;
        Setting = setting;
        SingleSignOn = singleSignOn;
        SiteType = siteType;
        SmtpConfiguration = smtpConfiguration;
        TableAccess = tableAccess;
        UserActivity = userActivity;
        UserRole = userRole;
        Template = template;
        WorkflowHistory = workflowHistory;
        WorkflowInfraObject = workflowInfraObject;
        WorkflowProfileInfo = workflowProfileInfo;
        WorkflowProfile = workflowProfile;
        AlertInformation = alertInformation;
        WorkflowPermission = workflowPermission;
        MssqlAlwaysOnMonitorLogs = mssqlAlwaysOnMonitorLogs;
        OracleMonitorLogs = oracleMonitorLogs;
        WorkflowOperationGroup = workflowOperationGroup;
        MssqlAlwaysOnMonitorStatus = mssqlAlwaysOnMonitorStatus;
        AlertNotification = alertNotification;
        AlertMasterService = alertMasterService;
        MssqlMonitorLogs = mssqlMonitorLogs;
        OracleRACMonitorLogs = oracleRacMonitorLogs;
        MssqlMonitorStatus = mssqlMonitorStatus;
        MysqlMonitorLogs = mysqlMonitorLogs;
        OracleMonitorStatus = oracleMonitorStatus;
        OracleRACMonitorStatus = oracleRACMonitorStatus;
        PostgresMonitorLogs = postgresMonitorLogs;
        PostgresMonitorStatus = postgresMonitorStatus;
        MysqlMonitorStatus = mysqlMonitorStatus;
        WorkflowActionTypes = workflowActionType;
        WorkflowOperation = workflowOperation;
        PluginManager = pluginManager;
        JobService = jobService;
        DrReady = drReady;
        AlertReceiver = alertReceiver;
        DataSetColumns = dataSetColumns;
        Workflow = workflow;
        ReplicationMaster = replicationMaster;
        InfraReplicationMapping = infraReplicationMapping;
        AboutCP = aboutCp;
        FormType = formType;
        FormMapping = formMapping;
        MonitorServices = monitorServices;
        WorkflowActionResult = workflowActionResult;
        RiskMitigation = riskMitigation;
        UserInfraObject = userInfraObject;
        SmsConfiguration = smsConfiguration;
        BusinessServiceAvailability = businessServiceAvailability;
        BusinessServiceEvaluation = businessServiceEvaluation;
        LicenseInfo = licenseInfo;
        BusinessServiceHealthStatus = businessServiceHealthStatus;
        HeatMapStatus = heatMapStatus;
        DrReadyStatus = drReadyStatus;
        InfraObjectInfo = infraObjectInfo;
        InfraSummary = infraSummary;
        ImpactAvailability = impactAvailability;
        DashboardView = dashboardView;
        Monitor = monitor;
        WorkflowExecutionTemp = workflowExecutionTemp;
        DashboardViewLog = dashboardViewLog;
        UserGroup = userGroup;
        ManageWorkflowList = manageWorkflowList;
        FourEyeApproverService = fourEyeApproverService;
        GroupPolicy = groupPolicy;

        DRCalenderService = dRCalenderService;
        RpoSlaDeviationReport = rpoSlaDeviationReport;
        Report = report;
        Db2HaDrMonitorStatus = db2HaDrMonitorStatus;
        MsSqlNativeLogShippingMonitorStatus = msSqlNativeLogShippingMonitorStatus;
        MongoDbMonitorStatus = mongoDbMonitorStatus;
        SvcMsSqlMonitorStatus = svcMsSqlMonitorStatus;
        WorkflowPredictionServices = workflowPredictionService;
        GlobalSettings = globalSettings;
        MSSQLDbMirroringMonitorStatus = mSsqlDbMirroringMonitorStatus;
        ServerType = serverType;
        ServerSubType = serverSubType;

        TeamMasterService = teamMasterService;
        TeamResourceService = teamResourceService;

        this.approvalMatrixService = approvalMatrixService;
        ReportSchedule = reportSchedule;
        Db2HaDrMonitorLog = db2HaDrMonitorLog;
        PageSolutionMapping = pageSolutionMapping;
        RsyncJob = rsyncJob;
        RoboCopyJob = roboCopyJob;
        FormHistory = formHistory;

        EscalationService = escalationService;

        EscalationMatrixLevelService = escalationMatrixLevelService;
        ReplicationJobService = replicationJobService;
        CyberComponentMapping = cyberComponentMapping;
        RpForVmMonitorStatusService = rpForVmMonitorStatusService;
        ComponentSaveAll = componentSaveAll;
        UserInfo = userInfoService;
        DataSyncJob = dataSyncJob;
        RpForVmCGMonitorStatus = rpForVmCGMonitorStatusService;
        RpForVmCGMonitorLogs = rpForVmCGMonitorLogsService;
        FastCopyMonitor = fastCopyMonitorService;
        CGExecutionReport = cgExecutionReport;
        TwoStepAuthentication = twoStepAuthentication;

        #endregion

    }

}
