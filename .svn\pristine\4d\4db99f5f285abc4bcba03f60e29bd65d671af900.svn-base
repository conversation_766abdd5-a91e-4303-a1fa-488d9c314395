using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DynamicDashboardWidgetsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<DynamicDashboardWidgetListVm>>> GetDynamicDashboardWidgets()
    {
        Logger.LogDebug("Get All DynamicDashboardWidgets");

        return Ok(await Mediator.Send(new GetDynamicDashboardWidgetListQuery()));
    }

    [HttpGet("{id}", Name = "GetDynamicDashboardWidget")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<DynamicDashboardWidgetDetailVm>> GetDynamicDashboardWidgetById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardWidget Id");

        Logger.LogDebug($"Get DynamicDashboardWidget Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDynamicDashboardWidgetDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<DynamicDashboardWidgetListVm>>> GetPaginatedDynamicDashboardWidgets([FromQuery] GetDynamicDashboardWidgetPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in DynamicDashboardWidget Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateDynamicDashboardWidgetResponse>> CreateDynamicDashboardWidget([FromBody] CreateDynamicDashboardWidgetCommand createDynamicDashboardWidgetCommand)
    {
        Logger.LogDebug($"Create DynamicDashboardWidget '{createDynamicDashboardWidgetCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDynamicDashboardWidget), await Mediator.Send(createDynamicDashboardWidgetCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateDynamicDashboardWidgetResponse>> UpdateDynamicDashboardWidget([FromBody] UpdateDynamicDashboardWidgetCommand updateDynamicDashboardWidgetCommand)
    {
        Logger.LogDebug($"Update DynamicDashboardWidget '{updateDynamicDashboardWidgetCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDynamicDashboardWidgetCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteDynamicDashboardWidgetResponse>> DeleteDynamicDashboardWidget(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardWidget Id");

        Logger.LogDebug($"Delete DynamicDashboardWidget Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDynamicDashboardWidgetCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDynamicDashboardWidgetNameExist(string dynamicDashboardWidgetName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(dynamicDashboardWidgetName, "DynamicDashboardWidget Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicDashboardWidget Name '{dynamicDashboardWidgetName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDynamicDashboardWidgetNameUniqueQuery { Name = dynamicDashboardWidgetName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


