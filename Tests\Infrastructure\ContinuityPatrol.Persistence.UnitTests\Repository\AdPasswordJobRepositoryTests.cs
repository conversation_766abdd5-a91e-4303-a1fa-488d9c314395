using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AdPasswordJobRepositoryTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AdPasswordJobRepository _repository;

    public AdPasswordJobRepositoryTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AdPasswordJobRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;

        // Act
      await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
      await _dbContext.SaveChangesAsync();
        var result=await _repository.GetByReferenceIdAsync(adPasswordJob.ReferenceId);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordJob.DomainServer, result.DomainServer);
        Assert.Single(_dbContext.AdPasswordJobs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;

        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        adPasswordJob.DomainServer = "UpdatedDomainServer";

         _dbContext.AdPasswordJobs.Update(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(adPasswordJob.ReferenceId);
        // Assert
        Assert.Equal("UpdatedDomainServer", result.DomainServer);
     
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;

        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        // Act
        adPasswordJob.IsActive = false;

        _dbContext.AdPasswordJobs.Update(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;

        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(adPasswordJob.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordJob.Id, result.Id);
        Assert.Equal(adPasswordJob.DomainServer, result.DomainServer);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;
        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetByReferenceIdAsync(adPasswordJob.ReferenceId);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordJob.ReferenceId, result.ReferenceId);
        Assert.Equal(adPasswordJob.DomainServer, result.DomainServer);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));

    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;
        await _repository.AddRangeAsync(adPasswordJobs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordJobs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;

        // Act
        var result = await _repository.AddRangeAsync(adPasswordJobs);

        // Assert
        Assert.Equal(adPasswordJobs.Count, result.Count());
        Assert.Equal(adPasswordJobs.Count, _dbContext.AdPasswordJobs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;
        await _repository.AddRangeAsync(adPasswordJobs);

        // Act
        var result = await _repository.RemoveRangeAsync(adPasswordJobs);

        // Assert
        Assert.Equal(adPasswordJobs.Count, result.Count());
        Assert.Empty(_dbContext.AdPasswordJobs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilterAsync Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;
        var targetDomainServer = "TEST_DOMAIN_SERVER";
        adPasswordJobs.First().DomainServer = targetDomainServer;
        await _repository.AddRangeAsync(adPasswordJobs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.DomainServer == targetDomainServer);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetDomainServer, result.First().DomainServer);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;
        await _repository.AddRangeAsync(adPasswordJobs);

        // Act
        var result = await _repository.FindByFilterAsync(x => x.DomainServer == "NON_EXISTENT_DOMAIN");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenDomainServerExistsAndIdIsInvalid()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;
        adPasswordJob.DomainServer = "ExistingDomainServer";
        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("ExistingDomainServer", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenDomainServerDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList;
        await _repository.AddRangeAsync(adPasswordJobs);

        // Act
        var result = await _repository.IsNameExist("NonExistentDomainServer", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenDomainServerExistsForSameEntity()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobDto;
        adPasswordJob.DomainServer = "SameDomainServer";
        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist("SameDomainServer", adPasswordJob.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestDomainServer", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var adPasswordJob = _adPasswordJobFixture.AdPasswordJobList;
        var adPasswordJob1 = adPasswordJob[0];
        var adPasswordJob2 = adPasswordJob[1];
      
        // Act
        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob1);

        await _dbContext.AdPasswordJobs.AddAsync(adPasswordJob2);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.NotNull(result[0]);
        Assert.NotNull(result[1]);
        Assert.Equal(2, _dbContext.AdPasswordJobs.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var adPasswordJobs = _adPasswordJobFixture.AdPasswordJobList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(adPasswordJobs);
        var initialCount = adPasswordJobs.Count;
        
        var toUpdate = adPasswordJobs.Take(2).ToList();
        toUpdate.ForEach(x => x.DomainServer = "UpdatedDomainServer");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = adPasswordJobs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.DomainServer == "UpdatedDomainServer").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
