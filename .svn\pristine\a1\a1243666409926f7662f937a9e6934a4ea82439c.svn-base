using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PostgresMonitorLogsFixture : IDisposable
{
    public List<PostgresMonitorLogs> PostgresMonitorLogsPaginationList { get; set; }
    public List<PostgresMonitorLogs> PostgresMonitorLogsList { get; set; }
    public PostgresMonitorLogs PostgresMonitorLogsDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public PostgresMonitorLogsFixture()
    {
        var fixture = new Fixture();

        PostgresMonitorLogsList = fixture.Create<List<PostgresMonitorLogs>>();

        PostgresMonitorLogsPaginationList = fixture.CreateMany<PostgresMonitorLogs>(20).ToList();

        PostgresMonitorLogsDto = fixture.Create<PostgresMonitorLogs>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
