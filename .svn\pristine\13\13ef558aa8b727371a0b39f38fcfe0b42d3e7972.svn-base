using ContinuityPatrol.Application.Features.FiaCost.Commands.Create;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaCost.Commands.Update;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetList;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FiaCost.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaCostModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class FiaCostsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<FiaCostListVm>>> GetFiaCosts()
    {
        Logger.LogDebug("Get All FiaCosts");

        return Ok(await Mediator.Send(new GetFiaCostListQuery()));
    }

    [HttpGet("{id}", Name = "GetFiaCost")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<FiaCostDetailVm>> GetFiaCostById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FiaCost Id");

        Logger.LogDebug($"Get FiaCost Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetFiaCostDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<FiaCostListVm>>> GetPaginatedFiaCosts([FromQuery] GetFiaCostPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in FiaCost Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateFiaCostResponse>> CreateFiaCost([FromBody] CreateFiaCostCommand createFiaCostCommand)
    {
        Logger.LogDebug($"Create FiaCost '{createFiaCostCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateFiaCost), await Mediator.Send(createFiaCostCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateFiaCostResponse>> UpdateFiaCost([FromBody] UpdateFiaCostCommand updateFiaCostCommand)
    {
        Logger.LogDebug($"Update FiaCost '{updateFiaCostCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateFiaCostCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteFiaCostResponse>> DeleteFiaCost(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FiaCost Id");

        Logger.LogDebug($"Delete FiaCost Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteFiaCostCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsFiaCostNameExist(string fiaCostName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(fiaCostName, "FiaCost Name");

     Logger.LogDebug($"Check Name Exists Detail by FiaCost Name '{fiaCostName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetFiaCostNameUniqueQuery { Name = fiaCostName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


