﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthLog.Queries;

public class GetBusinessServiceHealthLogDetailQueryHandlerTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly BusinessServiceHealthLogFixture _businessServiceHealthLogFixture;
    private readonly Mock<IBusinessServiceHealthLogRepository> _mockBusinessServiceHealthLogRepository;
    private readonly GetBusinessServiceHealthLogDetailQueryHandler _handler;

    public GetBusinessServiceHealthLogDetailQueryHandlerTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _businessServiceHealthLogFixture = businessServiceHealthLogFixture;

        _mockBusinessServiceHealthLogRepository = BusinessServiceHealthLogRepositoryMocks.GetBusinessServiceHealthLogRepository(_businessServiceHealthLogFixture.BusinessServiceHealthLogs);

        _handler = new GetBusinessServiceHealthLogDetailQueryHandler(_mockBusinessServiceHealthLogRepository.Object, _businessServiceHealthLogFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceHealthLogDetails_When_ValidId()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthLogDetailQuery { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<BusinessServiceHealthLogDetailVm>();
        result.Id.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId);
        result.ConfiguredCount.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ConfiguredCount);
        result.DRReadyCount.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].DRReadyCount);
        result.DRNotReadyCount.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].DRNotReadyCount);
        result.ProblemState.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ProblemState);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidBusinessServiceHealthLogId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetBusinessServiceHealthLogDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
        
        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceHealthLogDetailQuery { Id = _businessServiceHealthLogFixture.BusinessServiceHealthLogs[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceHealthLogRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}