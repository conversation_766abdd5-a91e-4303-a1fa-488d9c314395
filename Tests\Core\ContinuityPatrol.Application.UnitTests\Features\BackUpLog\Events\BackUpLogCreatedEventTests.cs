using ContinuityPatrol.Application.Features.BackUpLog.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Events;

public class BackUpLogCreatedEventTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BackUpLogCreatedEventHandler>> _mockLogger;
    private readonly BackUpLogCreatedEventHandler _handler;

    public BackUpLogCreatedEventTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockUserActivityRepository = BackUpLogRepositoryMocks.CreateUserActivityRepository(_backUpLogFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BackUpLogCreatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/backuplog");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BackUpLogCreatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BackUpLogCreatedEventReceived()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "ProductionDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_FullBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "FullBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

      
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BackUpLogCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "BackupUser123";
        var requestUrl = "/api/v6/backuplog/create";
        var ipAddress = "*************";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "CustomBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'CustomBackupDatabase' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCreatedByAndModifiedBy_When_BackUpLogCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);

        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.CreatedBy == userId &&
            ua.LastModifiedBy == userId)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdEmpty_When_BackUpLogCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(string.Empty);

        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

       
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdNull_When_BackUpLogCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns((string)null);

        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        
    }

    [Fact]
    public async Task Handle_LogInformation_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

      
        
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BackUpLogCreatedWithNullName()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = null };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BackUpLogCreatedWithEmptyName()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog '' created successfully.")), Times.Once);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexDatabaseName_When_BackUpLogCreated()
    {
        // Arrange
        var complexDatabaseName = "Complex_Production_Database_With_Special_Characters_123";
        var createdEvent = new BackUpLogCreatedEvent { Name = complexDatabaseName };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BackUpLog '{complexDatabaseName}' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BackUpLogCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TestDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldBeOfType<BackUpLogCreatedEvent>();
        createdEvent.ShouldBeAssignableTo<INotification>();
    }

    [Fact]
    public async Task Handle_CreateEventForDifferentialBackup_When_DifferentialBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "DifferentialBackupDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BackUpLog 'DifferentialBackupDatabase' created successfully." &&
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEventForTransactionLogBackup_When_TransactionLogBackupCreated()
    {
        // Arrange
        var createdEvent = new BackUpLogCreatedEvent { Name = "TransactionLogDatabase" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

    }
}
