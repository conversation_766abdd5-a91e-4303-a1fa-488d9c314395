using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberComponentFixture : IDisposable
{
    public List<CyberComponent> CyberComponentPaginationList { get; set; }
    public List<CyberComponent> CyberComponentList { get; set; }
    public CyberComponent CyberComponentDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberComponentFixture()
    {
        var fixture = new Fixture();

        CyberComponentList = fixture.Create<List<CyberComponent>>();

        CyberComponentPaginationList = fixture.CreateMany<CyberComponent>(20).ToList();

        CyberComponentPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CyberComponentPaginationList.ForEach(x => x.IsActive = true);

        CyberComponentList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CyberComponentList.ForEach(x => x.IsActive = true);

        CyberComponentDto = fixture.Create<CyberComponent>();
        CyberComponentDto.ReferenceId = Guid.NewGuid().ToString();
        CyberComponentDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
