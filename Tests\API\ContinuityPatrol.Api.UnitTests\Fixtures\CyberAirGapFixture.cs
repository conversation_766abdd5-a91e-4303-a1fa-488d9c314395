using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberAirGapFixture
{
    public CreateCyberAirGapCommand CreateCyberAirGapCommand { get; }
    public UpdateCyberAirGapCommand UpdateCyberAirGapCommand { get; }
    public DeleteCyberAirGapCommand DeleteCyberAirGapCommand { get; }
    public AirGapStatusUpdateCommand AirGapStatusUpdateCommand { get; }
    public AirGapAttachedCommand AirGapAttachedCommand { get; }
    public CyberAirGapListVm CyberAirGapListVm { get; }
    public CyberAirGapDetailVm CyberAirGapDetailVm { get; }
    public GetAirGapsStatusListVm GetAirGapsStatusListVm { get; }

    public CyberAirGapFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberAirGapCommand>(c => c
            .With(b => b.Name, "Enterprise Air Gap Replication")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "DR Site")
            .With(b => b.Port, 8443)
            .With(b => b.Source, "{\"servers\":[{\"name\":\"SRC-SVR-01\",\"ip\":\"*************\",\"role\":\"Primary\"}],\"storage\":{\"type\":\"SAN\",\"capacity\":\"10TB\"}}")
            .With(b => b.Target, "{\"servers\":[{\"name\":\"TGT-SVR-01\",\"ip\":\"**********\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"SAN\",\"capacity\":\"10TB\"}}")
            .With(b => b.Description, "Critical enterprise air gap replication for disaster recovery")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Primary Database Cluster")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Database Cluster")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Active")
            .With(b => b.StartTime, DateTime.Now.AddHours(-1))
            .With(b => b.EndTime, DateTime.Now.AddHours(23))
            .With(b => b.RPO, "15 minutes")
            .With(b => b.IsAttached, true)
            .With(b => b.Status, "Connected"));

        fixture.Customize<UpdateCyberAirGapCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Updated Enterprise Air Gap Replication")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Updated Primary Data Center")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Updated DR Site")
            .With(b => b.Port, 9443)
            .With(b => b.Source, "{\"servers\":[{\"name\":\"UPD-SRC-SVR-01\",\"ip\":\"*************\",\"role\":\"Primary\"}],\"storage\":{\"type\":\"NVMe\",\"capacity\":\"20TB\"}}")
            .With(b => b.Target, "{\"servers\":[{\"name\":\"UPD-TGT-SVR-01\",\"ip\":\"**********\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"NVMe\",\"capacity\":\"20TB\"}}")
            .With(b => b.Description, "Updated critical enterprise air gap replication")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Updated Primary Database Cluster")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "Updated DR Database Cluster")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Updated")
            .With(b => b.StartTime, DateTime.Now.AddHours(-2))
            .With(b => b.EndTime, DateTime.Now.AddHours(22))
            .With(b => b.RPO, "10 minutes")
            .With(b => b.IsAttached, false)
            .With(b => b.Status, "Disconnected"));

        fixture.Customize<DeleteCyberAirGapCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString));

        fixture.Customize<AirGapStatusUpdateCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Air Gap Status Update")
            .With(b => b.Status, "Maintenance"));

        fixture.Customize<AirGapAttachedCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.IsAttached, true));

        fixture.Customize<CyberAirGapListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, () => $"AirGap-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, () => $"Source-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, () => $"Target-Site-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.Port, () => fixture.Create<int>() % 9000 + 1000)
            .With(b => b.Source, () => $"{{\"server\":\"SRC-{fixture.Create<string>().Substring(0, 5)}\"}}")
            .With(b => b.Target, () => $"{{\"server\":\"TGT-{fixture.Create<string>().Substring(0, 5)}\"}}")
            .With(b => b.Description, () => $"Air gap replication - {fixture.Create<string>().Substring(0, 10)}")
            .With(b => b.ComponentMappingId, Guid.NewGuid().ToString)
            .With(b => b.ComponentMappingName, () => $"Mapping-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, () => $"Source-Component-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, () => $"Target-Component-{fixture.Create<string>().Substring(0, 6)}")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, () => fixture.Create<bool>() ? "Active" : "Inactive")
            .With(b => b.StartTime, () => DateTime.Now.AddHours(-(fixture.Create<int>() % 24)))
            .With(b => b.EndTime, () => DateTime.Now.AddHours(fixture.Create<int>() % 24))
            .With(b => b.RPO, () => $"{fixture.Create<int>() % 60 + 5} minutes")
            .With(b => b.IsAttached, () => fixture.Create<bool>())
            .With(b => b.Status, () => fixture.Create<bool>() ? "Connected" : "Disconnected")
            .With(b => b.ErrorMessage, () => fixture.Create<bool>() ? null : $"Error-{fixture.Create<string>().Substring(0, 10)}"));

        fixture.Customize<CyberAirGapDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Critical Air Gap System")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Primary Production Site")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "Disaster Recovery Site")
            .With(b => b.Port, 8443)
            .With(b => b.Description, "Mission-critical air gap replication system for enterprise data protection")
            .With(b => b.Source, "{\"infrastructure\":{\"servers\":[{\"name\":\"PROD-DB-01\",\"ip\":\"**************\",\"role\":\"Primary\",\"specs\":\"64GB RAM, 32 cores\"},{\"name\":\"PROD-DB-02\",\"ip\":\"**************\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"Enterprise SAN\",\"capacity\":\"50TB\",\"raid\":\"RAID 10\"},\"network\":{\"bandwidth\":\"10Gbps\",\"encryption\":\"AES-256\"}}}")
            .With(b => b.Target, "{\"infrastructure\":{\"servers\":[{\"name\":\"DR-DB-01\",\"ip\":\"***********\",\"role\":\"Primary\",\"specs\":\"64GB RAM, 32 cores\"},{\"name\":\"DR-DB-02\",\"ip\":\"***********\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"Enterprise SAN\",\"capacity\":\"50TB\",\"raid\":\"RAID 10\"},\"network\":{\"bandwidth\":\"10Gbps\",\"encryption\":\"AES-256\"}}}")
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Production Database Cluster")
            .With(b => b.ComponentMappingId, Guid.NewGuid().ToString)
            .With(b => b.ComponentMappingName, "DB-Cluster-Mapping")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "DR Database Cluster")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Active")
            .With(b => b.StartTime, DateTime.Now.AddHours(-2))
            .With(b => b.EndTime, DateTime.Now.AddHours(22))
            .With(b => b.RPO, "5 minutes")
            .With(b => b.IsAttached, true)
            .With(b => b.Status, "Connected")
            .With(b => b.ErrorMessage, (string)null));

        fixture.Customize<GetAirGapsStatusListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Air Gap Status Monitor")
            .With(b => b.SourceSiteId, Guid.NewGuid().ToString)
            .With(b => b.SourceSiteName, "Production Site")
            .With(b => b.TargetSiteId, Guid.NewGuid().ToString)
            .With(b => b.TargetSiteName, "DR Site")
            .With(b => b.Description, "Real-time air gap status monitoring")
            .With(b => b.Source, "{\"monitoring\":{\"enabled\":true,\"interval\":\"30s\"}}")
            .With(b => b.Target, "{\"monitoring\":{\"enabled\":true,\"interval\":\"30s\"}}")
            .With(b => b.Port, 8443)
            .With(b => b.SourceComponentId, Guid.NewGuid().ToString)
            .With(b => b.SourceComponentName, "Source Monitor")
            .With(b => b.TargetComponentId, Guid.NewGuid().ToString)
            .With(b => b.TargetComponentName, "Target Monitor")
            .With(b => b.EnableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.DisableWorkflowId, Guid.NewGuid().ToString)
            .With(b => b.WorkflowStatus, "Monitoring")
            .With(b => b.StartTime, DateTime.Now.AddHours(-1))
            .With(b => b.EndTime, DateTime.Now.AddHours(23))
            .With(b => b.RPO, "1 minute")
            .With(b => b.IsAttached, true)
            .With(b => b.Status, "Healthy")
            .With(b => b.ErrorMessage, (string)null)
            .With(b => b.GetAirGapsServeListVms, new List<GetAirGapsServeListVm>
            {
                new GetAirGapsServeListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Source Server",
                    SiteId = Guid.NewGuid().ToString(),
                    SiteName = "Production Site",
                    RoleType = "Primary",
                    OSType = "Linux",
                    IpAddress = "*************",
                    HostName = "prod-srv-01",
                    Type = "Database",
                    Port = "5432"
                }
            }));

        CreateCyberAirGapCommand = fixture.Create<CreateCyberAirGapCommand>();
        UpdateCyberAirGapCommand = fixture.Create<UpdateCyberAirGapCommand>();
        DeleteCyberAirGapCommand = fixture.Create<DeleteCyberAirGapCommand>();
        AirGapStatusUpdateCommand = fixture.Create<AirGapStatusUpdateCommand>();
        AirGapAttachedCommand = fixture.Create<AirGapAttachedCommand>();
        CyberAirGapListVm = fixture.Create<CyberAirGapListVm>();
        CyberAirGapDetailVm = fixture.Create<CyberAirGapDetailVm>();
        GetAirGapsStatusListVm = fixture.Create<GetAirGapsStatusListVm>();
    }
}
