﻿using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;


namespace ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetRunningList;

public class GetBulkImportOperationRunningListQueryHandler : IRequestHandler<GetBulkImportOperationRunningListQuery,
    List<BulkImportOperationRunningListVm>>
{
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly IBulkImportOperationRepository _bulkImportOperationRepository;
    private readonly IMapper _mapper;

    public GetBulkImportOperationRunningListQueryHandler(IBulkImportOperationRepository bulkImportOperationRepository,
        IMapper mapper, IBulkImportOperationGroupRepository bulkImportOperationGroupRepository,
        IBulkImportActionResultRepository bulkImportActionResultRepository)
    {
        _bulkImportOperationRepository = bulkImportOperationRepository;
        _mapper = mapper;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
    }

    public async Task<List<BulkImportOperationRunningListVm>> Handle(GetBulkImportOperationRunningListQuery request,
        CancellationToken cancellationToken)
    {
        var operationsRunningStatus = await _bulkImportOperationRepository.GetRunningStatus();

        var bulkImportOperation = _mapper.Map<List<BulkImportOperationRunningListVm>>(operationsRunningStatus);

        var bulkImportOperationIds = bulkImportOperation.Select(x => x.Id).ToList();
        
        var bulkImportOperationGroups = await _bulkImportOperationGroupRepository.GetBulkImportOperationGroupByBulkImportOperationIds(bulkImportOperationIds);

        var bulkImportOperationGroupsList = _mapper.Map<List<BulkImportOperationGroupList>>(bulkImportOperationGroups);
        
        bulkImportOperation.ForEach(operation => {           
            operation.BulkImportOperationGroup.AddRange(bulkImportOperationGroupsList.Where(group => group.BulkImportOperationId == operation.Id));
        });

        var bulkImportOperationGroupIds = bulkImportOperation.SelectMany(x => x.BulkImportOperationGroup.Select(g => g.Id)).ToList();

        var bulkImportActionResult = await _bulkImportActionResultRepository.GetByOperationIdsAndOperationGroupIds(bulkImportOperationIds, bulkImportOperationGroupIds);

        var bulkImportActionResultList = _mapper.Map<List<BulkImportActionResultListVm>>(bulkImportActionResult);

        bulkImportOperationGroupsList.ForEach(group =>
            group.BulkImportActionResultListVms = bulkImportActionResultList
                .Where(result => result.BulkImportOperationId == group.BulkImportOperationId && result.BulkImportOperationGroupId == group.Id).ToList()
        );
       
        return bulkImportOperation;
        //foreach (var operation in bulkImportOperation)
        //{
        //    var bulkImportOperationGroups1 =
        //        await _bulkImportOperationGroupRepository.GetBulkImportOperationGroupByBulkImportOperationId(
        //            operation.Id);

        //    operation.BulkImportOperationGroup =
        //        _mapper.Map<List<BulkImportOperationGroupList>>(bulkImportOperationGroups1);

        //    foreach (var operationGroup in operation.BulkImportOperationGroup)
        //    {
        //        var bulkImportActionResults =
        //            await _bulkImportActionResultRepository.GetByOperationIdAndOperationGroupId(
        //                operationGroup.BulkImportOperationId, operationGroup.Id);

        //        operationGroup.BulkImportActionResultListVms =
        //            _mapper.Map<List<BulkImportActionResultListVm>>(bulkImportActionResults);
        //    }
        //}
    }
}