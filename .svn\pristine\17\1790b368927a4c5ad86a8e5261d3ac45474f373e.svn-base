﻿using Microsoft.EntityFrameworkCore.Metadata;

namespace ContinuityPatrol.Shared.Infrastructure.Extensions;

public static class ModelBuilderExtensions
{
    public static void RemovePluralizingTableNameConvention(this ModelBuilder modelBuilder)
    {
        foreach (var entity in modelBuilder.Model.GetEntityTypes()) entity.SetTableName(entity.DisplayName());
    }

    public static void ApplyColumnNamesToUpper(this ModelBuilder modelBuilder)
    {
        var isOracle = modelBuilder.Model.GetAnnotations()
            .Any(a => a.Name.Contains("Oracle", StringComparison.OrdinalIgnoreCase));

        if (isOracle)
        {
            foreach (var entity in modelBuilder.Model.GetEntityTypes())
            {
                var tableId = StoreObjectIdentifier.Table(entity.GetTableName()!, entity.GetSchema());

                foreach (var prop in entity.GetProperties())
                {
                    var colName = prop.GetColumnName(tableId);
                    if (!string.IsNullOrEmpty(colName))
                        prop.SetColumnName(colName.ToUpperInvariant());
                }
            }
        }
    }
}