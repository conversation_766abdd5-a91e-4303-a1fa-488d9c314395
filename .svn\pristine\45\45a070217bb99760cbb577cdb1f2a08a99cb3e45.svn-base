﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Create;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Delete;
using ContinuityPatrol.Application.Features.SmsConfiguration.Commands.Update;
using ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SmsConfiguration.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.SmsConfigurationModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]

public class SmsConfigurationsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<SmsConfigurationListVm>>> GetSmsConfigurations()
    {
        Logger.LogDebug("Get All SmsConfigurations");

        return Ok(await Mediator.Send(new GetSmsConfigurationListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateSmsConfigurationResponse>> CreateSmsConfiguration([FromBody] CreateSmsConfigurationCommand createSmsConfigurationCommand)
    {
        Logger.LogDebug($"Create SmsConfiguration '{createSmsConfigurationCommand.UserName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateSmsConfiguration), await Mediator.Send(createSmsConfigurationCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateSmsConfigurationResponse>> UpdateSmsConfiguration([FromBody] UpdateSmsConfigurationCommand updateSmsConfigurationCommand)
    {
        Logger.LogDebug($"Update SmsConfiguration '{updateSmsConfigurationCommand.UserName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateSmsConfigurationCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteSmsConfigurationResponse>> DeleteSmsConfiguration(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SmsConfiguration Id");

        Logger.LogDebug($"Delete SmsConfiguration Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteSmsConfigurationCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetSmsConfiguration")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<SmsConfigurationDetailVm>> GetSmsConfigurationById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SmsConfiguration Id");

        Logger.LogDebug($"Get SmsConfiguration Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetSmsConfigurationDetailQuery { Id = id }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =  { ApplicationConstants.Cache.AllSmsConfigurationCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}