﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Queries;


public class GetInfraObjectSchedulerDetailQueryHandlerTests : IClassFixture<InfraObjectSchedulerFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private readonly Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

    private readonly GetInfraObjectSchedulerDetailQueryHandler _handler;

    public GetInfraObjectSchedulerDetailQueryHandlerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetInfraObjectSchedulerRepository(_infraObjectSchedulerFixture.InfraObjectSchedulers);

        _handler = new GetInfraObjectSchedulerDetailQueryHandler(_mockInfraObjectSchedulerRepository.Object, _infraObjectSchedulerFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_InfraObjectSchedulerDetails_When_ValidInfraObjectSchedulerId()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerDetailQuery { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<InfraObjectSchedulerDetailVm>();

        result.Id.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId);

        result.InfraObjectId.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].InfraObjectId);

        result.InfraObjectName.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].InfraObjectName);

        result.WorkflowTypeId.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].WorkflowTypeId);

        result.AfterSwitchOverWorkflowId.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].AfterSwitchOverWorkflowId);

        result.AfterSwitchOverWorkflowName.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].AfterSwitchOverWorkflowName);

        result.ScheduleType.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].ScheduleType);

        result.ScheduleTime.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].ScheduleTime);

        result.IsSchedule.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].IsSchedule);

        result.WorkflowVersion.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].WorkflowVersion);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidInfraObjectSchedulerId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetInfraObjectSchedulerDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetInfraObjectSchedulerDetailQuery { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        _mockInfraObjectSchedulerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}