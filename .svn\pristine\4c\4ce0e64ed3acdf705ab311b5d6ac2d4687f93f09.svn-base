﻿//Drill Analytics

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;

public class GetDrillAnalyticsQueryHandler : IRequestHandler<GetOperationalReadinessQuery, DrillAnalyticsDetailVm>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowOperationRepository _workflowOperation;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public GetDrillAnalyticsQueryHandler(
        IWorkflowProfileInfoRepository workflowProfileInfoRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowActionResultRepository workflowActionResultRepository, IInfraObjectRepository infraObjectRepository,
        IBusinessFunctionRepository businessFunctionRepository, IWorkflowOperationRepository workflowOperation)
    {
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _workflowOperation = workflowOperation;
    }

    public async Task<DrillAnalyticsDetailVm> Handle(GetOperationalReadinessQuery request,
        CancellationToken cancellationToken)
    {
        #region  Old Code
        //    var drillAnalytics = new DrillAnalyticsDetailVm
        //    {
        //        DrillAnalyticsDetailLists = new List<DrillAnalyticsDetailList>()
        //    };

        //    var profileList = await _workflowProfileInfoRepository.ListAllAsync();

        //    var configuredProfile = profileList.DistinctBy(x => x.ProfileId).ToList();

        //    drillAnalytics.ConfiguredProfileCount = configuredProfile.Count;

        //    var workflowOperation = await _workflowOperation.ListAllAsync();

        //    drillAnalytics.ExecutedProfileCount = configuredProfile
        //        .Count(cp => workflowOperation.Any(wo => wo.ProfileId == cp.ProfileId));

        //    var profileListByDate = workflowOperation.Where(x =>
        //            x.Status.ToLower().Equals("successwitherror") || x.Status.ToLower().Equals("success"))
        //        .GroupBy(x => x.StartTime.Date)
        //        .Select(x => new
        //        {
        //            DrillDate = DateOnly.FromDateTime(x.Key).ToString("yyyy-MM-dd"),
        //            Operations = x.ToList()
        //        }).ToList();

        //    foreach (var profile in profileListByDate)
        //    {
        //        var totalWorkflowCount = 0;
        //        var wfSuccessCount = 0;
        //        var wfFailCount = 0;
        //        var rtoCount = 0;

        //        var profileErrorCount = profile.Operations.Count(x => x.Status.ToLower().Equals("successwitherror"));
        //        var profileSuccessCount = profile.Operations.Count(x => x.Status.ToLower().Equals("success"));

        //        var totalProfileCount = profile.Operations.Count;

        //        foreach (var operation in profile.Operations)
        //        {
        //            drillAnalytics.ExecutedWorkflowCount++;

        //            var workflowOperationGroupVm = await _workflowOperationGroupRepository
        //                .GetWorkflowOperationByWorkflowOperationId(operation.ReferenceId);

        //            totalWorkflowCount += workflowOperationGroupVm.Count;

        //            wfSuccessCount += workflowOperationGroupVm.Count(x => x.Status.ToLower().Equals("completed"));
        //            wfFailCount += workflowOperationGroupVm.Count(x => !x.Status.ToLower().Equals("completed"));

        //            //OutOfRTO
        //            foreach (var workflow in workflowOperationGroupVm)
        //            {
        //                var workflowAction =
        //                    await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationId(
        //                        workflow.WorkflowOperationId);

        //                if (workflowAction.Any())
        //                {
        //                    var infra = await _infraObjectRepository
        //                        .GetByReferenceIdAsync(workflowAction.FirstOrDefault()?.InfraObjectId);

        //                    if (infra != null)
        //                    {
        //                        var businessFunction = await _businessFunctionRepository
        //                            .GetByReferenceIdAsync(infra.BusinessFunctionId);

        //                        if (businessFunction is not null)
        //                        {
        //                            var wfStartTime = workflowAction.Min(x => x.StartTime);
        //                            var wfEndTime = workflowAction.Max(x => x.EndTime);
        //                            var wfTotalTime = GetTotalTime(wfStartTime, wfEndTime);

        //                            if (wfTotalTime > TimeSpan.FromMinutes(int.Parse(businessFunction.ConfiguredRTO)))
        //                                rtoCount++;
        //                        }
        //                    }
        //                }
        //            }
        //        }

        //        var drillDetail = new DrillAnalyticsDetailList
        //        {
        //            DrillDate = profile.DrillDate,
        //            ProfileTotalCount = totalProfileCount,
        //            ProfileSuccessCount = profileSuccessCount,
        //            ProfiledFailedCount = profileErrorCount,
        //            WorkflowTotalCount = totalWorkflowCount,
        //            WorkflowSuccessCount = wfSuccessCount,
        //            WorkflowFailedCount = wfFailCount,
        //            OutOfRtoCount = rtoCount
        //        };

        //        drillAnalytics.DrillAnalyticsDetailLists.Add(drillDetail);
        //    }

        //    drillAnalytics.DrillAnalyticsDetailLists = drillAnalytics.DrillAnalyticsDetailLists
        //        .OrderByDescending(d => d.DrillDate)
        //        .ToList();

        //    return drillAnalytics;
        //}

        //private static TimeSpan GetTotalTime(DateTime startTime, DateTime endTime)
        //{
        //    return endTime - startTime;
        //}
        #endregion

        var drillAnalytics = new DrillAnalyticsDetailVm
        {
            DrillAnalyticsDetailLists = new List<DrillAnalyticsDetailList>()
        };

        var profileList = await _workflowProfileInfoRepository.ConfiguredProfileInfo();
        var configuredProfile = profileList.DistinctBy(x => x.ProfileId).ToList();
        drillAnalytics.ConfiguredProfileCount = configuredProfile.Count;

        var workflowOperations = await _workflowOperation.GetFilterListAsync();
        drillAnalytics.ExecutedProfileCount = configuredProfile
            .Count(cp => workflowOperations.Any(wo => wo.ProfileId == cp.ProfileId));

        var successfulOperations = workflowOperations
            .Where(x => x.Status.Equals("successwitherror", StringComparison.OrdinalIgnoreCase)
                        || x.Status.Equals("success", StringComparison.OrdinalIgnoreCase))
            .GroupBy(x => x.StartTime.Date)
            .Select(x => new
            {
                DrillDate = DateOnly.FromDateTime(x.Key).ToString("yyyy-MM-dd"),
                Operations = x.ToList()
            }).ToList();

        var allWorkflowOperationGroups = await _workflowOperationGroupRepository
            .GetWorkflowOperationGroupByWorkflowOperationIds(workflowOperations.Select(x => x.ReferenceId).ToList());

        var allWorkflowActionResults = await _workflowActionResultRepository
            .GetWorkflowActionResultByWorkflowOperationIds(allWorkflowOperationGroups.Select(x => x.WorkflowOperationId).ToList());

        var infraObjects = await _infraObjectRepository.GetByReferenceIdsAsync(
            allWorkflowActionResults.Where(x=>x.InfraObjectId.IsNotNullOrWhiteSpace())
            .Select(x => x.InfraObjectId).Distinct().ToList());

        var businessFunctions = await _businessFunctionRepository.GetByReferenceIdsAsync(
            infraObjects.Select(x => x.BusinessFunctionId).Distinct().ToList());

        foreach (var profile in successfulOperations)
        {
            var totalWorkflowCount = 0;
            var wfSuccessCount = 0;
            var wfFailCount = 0;
            var rtoCount = 0;

            var profileErrorCount = profile.Operations.Count(x => x.Status.Equals("successwitherror", StringComparison.OrdinalIgnoreCase));
            var profileSuccessCount = profile.Operations.Count(x => x.Status.Equals("success", StringComparison.OrdinalIgnoreCase));
            var totalProfileCount = profile.Operations.Count;

            foreach (var operation in profile.Operations)
            {
                drillAnalytics.ExecutedWorkflowCount++;

                var workflowOperationGroupVm = allWorkflowOperationGroups
                    .Where(x => x.WorkflowOperationId == operation.ReferenceId)
                    .ToList();

                totalWorkflowCount += workflowOperationGroupVm.Count;
                wfSuccessCount += workflowOperationGroupVm.Count(x => x.Status.Equals("completed", StringComparison.OrdinalIgnoreCase));
                wfFailCount += workflowOperationGroupVm.Count(x => !x.Status.Equals("completed", StringComparison.OrdinalIgnoreCase));

                // Calculate Out of RTO
                foreach (var workflow in workflowOperationGroupVm)
                {
                    var workflowActions = allWorkflowActionResults
                        .Where(x => x.WorkflowOperationId == workflow.WorkflowOperationId)
                        .ToList();

                    if (workflowActions.Any())
                    {
                        var wfStartTime = workflowActions.Min(x => x.StartTime);
                        var wfEndTime = workflowActions.Max(x => x.EndTime);
                        var wfTotalTime = GetTotalTime(wfStartTime, wfEndTime);

                        var infra = infraObjects.FirstOrDefault(x => x.ReferenceId == workflowActions.First().InfraObjectId);
                        var businessFunction = businessFunctions.FirstOrDefault(x => x.ReferenceId == infra?.BusinessFunctionId);

                        if (businessFunction != null && wfTotalTime > TimeSpan.FromMinutes(int.Parse(businessFunction.ConfiguredRTO)))
                        {
                            rtoCount++;
                        }
                    }
                }
            }

            // Add to Drill Analytics List
            drillAnalytics.DrillAnalyticsDetailLists.Add(new DrillAnalyticsDetailList
            {
                DrillDate = profile.DrillDate,
                ProfileTotalCount = totalProfileCount,
                ProfileSuccessCount = profileSuccessCount,
                ProfiledFailedCount = profileErrorCount,
                WorkflowTotalCount = totalWorkflowCount,
                WorkflowSuccessCount = wfSuccessCount,
                WorkflowFailedCount = wfFailCount,
                OutOfRtoCount = rtoCount
            });
        }

        // Sort Results by Drill Date
        drillAnalytics.DrillAnalyticsDetailLists = drillAnalytics.DrillAnalyticsDetailLists
            .OrderByDescending(d => d.DrillDate)
            .ToList();

        return drillAnalytics;


    }
    private static TimeSpan GetTotalTime(DateTime startTime, DateTime endTime)
    {
        return endTime - startTime;
    }


}