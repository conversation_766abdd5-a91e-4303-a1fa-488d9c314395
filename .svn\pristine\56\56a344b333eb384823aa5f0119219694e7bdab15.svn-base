using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraSummaryRepositoryTests : IClassFixture<InfraSummaryFixture>, IDisposable
{
    private readonly InfraSummaryFixture _infraSummaryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraSummaryRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraSummaryRepositoryTests(InfraSummaryFixture infraSummaryFixture)
    {
        _infraSummaryFixture = infraSummaryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraSummaryRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraSummaries.RemoveRange(_dbContext.InfraSummaries);
        await _dbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId_withEqual_Value()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        var Type = "Server";
        var businessServiceId = "BS_123";

        var infraSummaries = new List<InfraSummary>
        {
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Server Summary",
                Type = "Server",
                Count = 10,
                CompanyId = companyId,
                BusinessServiceId = "BS_123",
                IsActive = true
            }
        };

        await _dbContext.InfraSummaries.AddRangeAsync(infraSummaries);
        await _dbContext.SaveChangesAsync();
        // Act

        var result = _repository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(Type, businessServiceId, companyId);


        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task ListAllAsync_ReturnsFilteredByCompanyId_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraSummaries = new List<InfraSummary>
        {
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Server Summary",
                Type = "Server",
                Count = 10,
                CompanyId = companyId,
                BusinessServiceId = "BS_123",
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Database Summary",
                Type = "Database",
                Count = 5,
                CompanyId = companyId,
                BusinessServiceId = "BS_456",
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Different Company Summary",
                Type = "Server",
                Count = 8,
                CompanyId = "COMPANY_456", // Different company
                BusinessServiceId = "BS_789",
                IsActive = true
            }
        };

        await _dbContext.InfraSummaries.AddRangeAsync(infraSummaries);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, summary => Assert.Equal(companyId, summary.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsFilteredByAssignedBusinessServices_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";
        var businessServiceId1 = "BS_123";
        var businessServiceId2 = "BS_456";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);

        // Mock assigned business services
        var assignedEntity = new ContinuityPatrol.Shared.Core.Domain.AssignedEntity
        {
            AssignedBusinessServices = new List<ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices>
            {
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = businessServiceId1, Name = "Service 1" },
                new ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices { Id = businessServiceId2, Name = "Service 2" }
            }
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var infraSummaries = new List<InfraSummary>
        {
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Assigned Service 1 Summary",
                Type = "Server",
                Count = 10,
                CompanyId = companyId,
                BusinessServiceId = businessServiceId1, // Assigned
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Assigned Service 2 Summary",
                Type = "Database",
                Count = 5,
                CompanyId = companyId,
                BusinessServiceId = businessServiceId2, // Assigned
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Non-Assigned Service Summary",
                Type = "Server",
                Count = 8,
                CompanyId = companyId,
                BusinessServiceId = "BS_999", // Not assigned
                IsActive = true
            }
        };

        await _dbContext.InfraSummaries.AddRangeAsync(infraSummaries);
        await _dbContext.SaveChangesAsync();

        // Create a new repository instance with the mocked service
        var repository = new InfraSummaryRepository(_dbContext, _mockLoggedInUserService.Object);

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, summary => summary.BusinessServiceId == businessServiceId1);
        Assert.Contains(result, summary => summary.BusinessServiceId == businessServiceId2);
        Assert.DoesNotContain(result, summary => summary.BusinessServiceId == "BS_999");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);

        // Mock empty assigned business services
        var assignedEntity = new ContinuityPatrol.Shared.Core.Domain.AssignedEntity
        {
            AssignedBusinessServices = new List<ContinuityPatrol.Shared.Core.Domain.AssignedBusinessServices>()
        };

        var assignedInfrasJson = JsonConvert.SerializeObject(assignedEntity);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns(assignedInfrasJson);

        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Test Summary",
            Type = "Server",
            Count = 10,
            CompanyId = companyId,
            BusinessServiceId = "BS_123",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Create a new repository instance with the mocked service
        var repository = new InfraSummaryRepository(_dbContext, _mockLoggedInUserService.Object);

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraSummaryByType Tests

    [Fact]
    public async Task GetInfraSummaryByType_ReturnsMatchingSummary_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var type = "Server";

        var infraSummaries = new List<InfraSummary>
        {
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Server Summary",
                Type = type,
                Count = 10,
                CompanyId = "COMPANY_123",
                BusinessServiceId = "BS_123",
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Database Summary",
                Type = "Database", // Different type
                Count = 5,
                CompanyId = "COMPANY_123",
                BusinessServiceId = "BS_456",
                IsActive = true
            }
        };

        await _dbContext.InfraSummaries.AddRangeAsync(infraSummaries);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraSummaryByType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(type, result.Type);
        Assert.Equal("Server Summary", result.EntityName);
        Assert.Equal(10, result.Count);
    }

    [Fact]
    public async Task GetInfraSummaryByType_ReturnsNull_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentType = "NonExistentType";

        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Server Summary",
            Type = "Server",
            Count = 10,
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraSummaryByType(nonExistentType);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetInfraSummaryByType_ReturnsFirstMatch_WhenMultipleTypesExist()
    {
        // Arrange
        await ClearDatabase();
        var type = "Server";

        var infraSummaries = new List<InfraSummary>
        {
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "First Server Summary",
                Type = type,
                Count = 10,
                CompanyId = "COMPANY_123",
                BusinessServiceId = "BS_123",
                IsActive = true
            },
            new InfraSummary
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityName = "Second Server Summary",
                Type = type,
                Count = 15,
                CompanyId = "COMPANY_456",
                BusinessServiceId = "BS_456",
                IsActive = true
            }
        };

        await _dbContext.InfraSummaries.AddRangeAsync(infraSummaries);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraSummaryByType(type);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(type, result.Type);
        // Should return the first match found
        Assert.True(result.EntityName == "First Server Summary" || result.EntityName == "Second Server Summary");
    }

    [Fact]
    public async Task GetInfraSummaryByType_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var type = "Server";

        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Server Summary",
            Type = type,
            Count = 10,
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.GetInfraSummaryByType("Server");
        var resultDifferentCase = await _repository.GetInfraSummaryByType("server");

        // Assert
        Assert.NotNull(resultExactCase); // Exact case should match
        Assert.Null(resultDifferentCase); // Different case should not match (case sensitive)
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_AddsInfraSummarySuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Test Add Summary",
            Type = "Server",
            Count = 25,
            CompanyId = "COMPANY_ADD_TEST",
            BusinessServiceId = "BS_ADD_TEST",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraSummary);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Add Summary", result.EntityName);

        var savedEntity = await _dbContext.InfraSummaries
            .FirstOrDefaultAsync(x => x.EntityName == "Test Add Summary");
        Assert.NotNull(savedEntity);
    }

    [Fact]
    public async Task UpdateAsync_UpdatesInfraSummarySuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Test Update Summary",
            Type = "Server",
            Count = 20,
            CompanyId = "COMPANY_UPDATE_TEST",
            BusinessServiceId = "BS_UPDATE_TEST",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Modify the entity
        infraSummary.Count = 30;
        infraSummary.Type = "Database";

        // Act
        var result = await _repository.UpdateAsync(infraSummary);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(30, result.Count);
        Assert.Equal("Database", result.Type);
    }

    [Fact]
    public async Task DeleteAsync_DeletesInfraSummarySuccessfully()
    {
        // Arrange
        await ClearDatabase();
        var infraSummary = new InfraSummary
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityName = "Test Delete Summary",
            Type = "Server",
            Count = 15,
            CompanyId = "COMPANY_DELETE_TEST",
            BusinessServiceId = "BS_DELETE_TEST",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.DeleteAsync(infraSummary);

        // Assert
        Assert.NotNull(result);

        var deletedEntity = await _dbContext.InfraSummaries
            .FirstOrDefaultAsync(x => x.EntityName == "Test Delete Summary");
        Assert.Null(deletedEntity);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsMatchingSummary_WhenReferenceIdExists()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();

        var infraSummary = new InfraSummary
        {
            ReferenceId = referenceId,
            EntityName = "Test Reference Summary",
            Type = "Server",
            Count = 12,
            CompanyId = "COMPANY_123",
            BusinessServiceId = "BS_123",
            IsActive = true
        };

        await _dbContext.InfraSummaries.AddAsync(infraSummary);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Test Reference Summary", result.EntityName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion
}
