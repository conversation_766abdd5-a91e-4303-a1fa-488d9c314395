﻿namespace ContinuityPatrol.Application.Features.UserActivity.Commands.Create;

public class CreateUserActivityCommand : IRequest<CreateUserActivityResponse>
{
    public string LoginName { get; set; }

    public string Entity { get; set; }

    public string Action { get; set; }

    public string ActivityType { get; set; }

    public string CompanyId { get; set; }

    public string RequestUrl { get; set; }

    public string HostAddress { get; set; }

    public string ActivityDetails { get; set; }

    public string UserId { get; set; }

    public override string ToString()
    {
        return $"Name: {LoginName};";
    }
}