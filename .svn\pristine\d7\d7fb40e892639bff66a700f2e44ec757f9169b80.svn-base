using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DriftParameterRepository : BaseRepository<DriftParameter>, IDriftParameterRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DriftParameterRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }
    public override async Task<IReadOnlyList<DriftParameter>> ListAllAsync()
    {
        var driftParameters = base.QueryAll(x => x.IsActive);

        var parameters = MapDriftParameter(driftParameters);

        return await parameters.ToListAsync();
    }
    public override Task<DriftParameter> GetByReferenceIdAsync(string id)
    {
        var driftParameters = base.GetByReferenceId(id, x =>
                  x.ReferenceId.Equals(id));

        var parameters = MapDriftParameter(driftParameters);

        return parameters.FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<DriftParameter>>PaginatedListAllAsync(int pageNumber,int pageSize , Specification<DriftParameter> specification, string sortColumn, string sortOrder)
    {
        return await MapDriftParameter(Entities.Specify(specification).DescOrderById()).ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);
   
    }
    public override IQueryable<DriftParameter> GetPaginatedQuery()
    {
        var driftParameters = base.QueryAll(x => x.IsActive);

        var parameters = MapDriftParameter(driftParameters);

        return parameters.AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<List<DriftParameter>> GetDriftParameterByCategoryId(string categoryId)
    {
        var driftParameters = base.FilterBy(x => x.DriftCategoryId.Equals(categoryId));

        var parameters = MapDriftParameter(driftParameters);

        return await parameters.ToListAsync();
    }

    public async Task<List<DriftParameter>> GetDriftParameterByImpactTypeId(string impactTypeId)
    {
        var driftParameters = base.FilterBy(x => x.DriftImpactTypeId.Equals(impactTypeId));

        var parameters = MapDriftParameter(driftParameters);

        return await parameters.ToListAsync();
    }
    private IQueryable<DriftParameter> MapDriftParameter(IQueryable<DriftParameter> driftParameters)
    {
       return driftParameters.Select(x => new
        {
            DriftCategory = _dbContext.DriftCategoryMasters.FirstOrDefault(c=> c.ReferenceId.Equals(x.DriftCategoryId)),
            DriftImpactType = _dbContext.DriftImpactTypeMasters.FirstOrDefault(i => i.ReferenceId.Equals(x.DriftImpactTypeId)),
            DriftParameter=x
        })
        .Select(res => new DriftParameter
        {
            Id=res.DriftParameter.Id,
            ReferenceId=res.DriftParameter.ReferenceId,
            Name=res.DriftParameter.Name,
            DriftCategoryId=res.DriftCategory.ReferenceId ?? res.DriftParameter.DriftCategoryId,
            DriftCategoryName=res.DriftCategory.CategoryName ?? res.DriftParameter.DriftCategoryName,
            DriftImpactTypeId=res.DriftImpactType.ReferenceId ?? res.DriftParameter.DriftImpactTypeId,
            DriftImpactTypeName=res.DriftImpactType.ImpactType ?? res.DriftParameter.DriftImpactTypeName,
            Severity=res.DriftParameter.Severity,
            Properties=res.DriftParameter.Properties,
            IsActive = res.DriftParameter.IsActive,
            CreatedBy = res.DriftParameter.CreatedBy,
            CreatedDate = res.DriftParameter.CreatedDate,
            LastModifiedBy = res.DriftParameter.LastModifiedBy,
            LastModifiedDate = res.DriftParameter.LastModifiedDate
        });
       
    }

}
