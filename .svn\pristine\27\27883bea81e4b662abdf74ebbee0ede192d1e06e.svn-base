using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DatabaseViewFixture : IDisposable
{
    public List<DatabaseView> DatabaseViewPaginationList { get; set; }
    public List<DatabaseView> DatabaseViewList { get; set; }
    public DatabaseView DatabaseViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "BS_123";
    public const string ServerId = "SERVER_123";
    public const string DatabaseTypeId = "DBTYPE_123";
    public const string UserName = "testuser";

    public ApplicationDbContext DbContext { get; private set; }

    public DatabaseViewFixture()
    {
        var fixture = new Fixture();

        DatabaseViewList = fixture.Create<List<DatabaseView>>();

        DatabaseViewPaginationList = fixture.CreateMany<DatabaseView>(20).ToList();

        DatabaseViewPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatabaseViewPaginationList.ForEach(x => x.IsActive = true);
        DatabaseViewPaginationList.ForEach(x => x.CompanyId = CompanyId);
        DatabaseViewPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DatabaseViewPaginationList.ForEach(x => x.ServerId = ServerId);
        DatabaseViewPaginationList.ForEach(x => x.DatabaseTypeId = DatabaseTypeId);
        DatabaseViewPaginationList.ForEach(x => x.UserName = UserName);

        DatabaseViewList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DatabaseViewList.ForEach(x => x.IsActive = true);
        DatabaseViewList.ForEach(x => x.CompanyId = CompanyId);
        DatabaseViewList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        DatabaseViewList.ForEach(x => x.ServerId = ServerId);
        DatabaseViewList.ForEach(x => x.DatabaseTypeId = DatabaseTypeId);
        DatabaseViewList.ForEach(x => x.UserName = UserName);

        DatabaseViewDto = fixture.Create<DatabaseView>();
        DatabaseViewDto.ReferenceId = Guid.NewGuid().ToString();
        DatabaseViewDto.IsActive = true;
        DatabaseViewDto.CompanyId = CompanyId;
        DatabaseViewDto.BusinessServiceId = BusinessServiceId;
        DatabaseViewDto.ServerId = ServerId;
        DatabaseViewDto.DatabaseTypeId = DatabaseTypeId;
        DatabaseViewDto.UserName = UserName;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
