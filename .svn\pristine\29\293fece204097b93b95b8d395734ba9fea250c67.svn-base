﻿using ContinuityPatrol.Application.Features.PluginManager.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManager.Events;

public class PluginManagerDeletedEventHandlerTests : IClassFixture<PluginManagerFixture>
{
    private readonly PluginManagerFixture _pluginManagerFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly PluginManagerDeletedEventHandler _handler;

    public PluginManagerDeletedEventHandlerTests(PluginManagerFixture pluginManagerFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockPluginManagerEventLogger = new Mock<ILogger<PluginManagerDeletedEventHandler>>();

        _mockUserActivityRepository = PluginManagerRepositoryMocks.CreatePluginManagerEventRepository(_pluginManagerFixture.UserActivities);

        _handler = new PluginManagerDeletedEventHandler(mockLoggedInUserService.Object, mockPluginManagerEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeletePluginManagerEventDeleted()
    {
        _pluginManagerFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_pluginManagerFixture.PluginManagerDeletedEvent, CancellationToken.None);

        result.Equals(_pluginManagerFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_pluginManagerFixture.PluginManagerDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_DeletePluginManagerEventDeleted()
    {
        _pluginManagerFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_pluginManagerFixture.PluginManagerDeletedEvent, CancellationToken.None);

        result.Equals(_pluginManagerFixture.UserActivities[0].Id);

        result.Equals(_pluginManagerFixture.PluginManagerDeletedEvent.Name);

        await Task.CompletedTask;
    }
}