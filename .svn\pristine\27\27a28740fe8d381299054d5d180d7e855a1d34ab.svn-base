﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraSummary.Commands;

public class CreateInfraSummaryTests : IClassFixture<InfraSummaryFixture>
{
    private readonly InfraSummaryFixture _infraSummaryFixture;
    private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
    private readonly CreateInfraSummaryCommandHandler _handler;

    public CreateInfraSummaryTests(InfraSummaryFixture infraSummaryFixture)
    {
        _infraSummaryFixture = infraSummaryFixture;

        _mockInfraSummaryRepository = InfraSummaryRepositoryMocks.CreateInfraSummaryRepository(_infraSummaryFixture.InfraSummaries);

        _handler = new CreateInfraSummaryCommandHandler(_infraSummaryFixture.Mapper, _mockInfraSummaryRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseInfraSummaryCount_When_InfraSummaryCreated()
    {
        await _handler.Handle(_infraSummaryFixture.CreateInfraSummaryCommand, CancellationToken.None);

        var allCategories = await _mockInfraSummaryRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_infraSummaryFixture.InfraSummaries.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateInfraSummaryResponse_When_InfraSummaryCreated()
    {
        var result = await _handler.Handle(_infraSummaryFixture.CreateInfraSummaryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateInfraSummaryResponse));

        result.InfraSummaryId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_infraSummaryFixture.CreateInfraSummaryCommand, CancellationToken.None);

        _mockInfraSummaryRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
    }
}