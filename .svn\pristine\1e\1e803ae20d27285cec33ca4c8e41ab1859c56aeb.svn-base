﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class HeatMapStatusRepositoryMocks
{
    public static Mock<IHeatMapStatusRepository> CreateHeatMapStatusRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<HeatMapStatus>())).ReturnsAsync(
            (HeatMapStatus heatMapStatus) =>
            {
                heatMapStatus.Id = new Fixture().Create<int>();

                heatMapStatus.ReferenceId = new Fixture().Create<Guid>().ToString();

                heatMapStatusList.Add(heatMapStatus);

                return heatMapStatus;
            });

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> UpdateHeatMapStatusRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapStatusList.SingleOrDefault(x => x.ReferenceId == i));
        
        heatMapStatusRepository.Setup(repo => repo.GetHeatMapDetailByInfraObjectAndEntityId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => heatMapStatusList.FirstOrDefault(x => x.InfraObjectId == i && x.EntityId == j));
        
        heatMapStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HeatMapStatus>())).ReturnsAsync((HeatMapStatus heatMapStatus) =>
        {
            var index = heatMapStatusList.FindIndex(item => item.ReferenceId == heatMapStatus.ReferenceId);

            heatMapStatusList[index] = heatMapStatus;

            return heatMapStatus;
        });

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> DeleteHeatMapStatusRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapStatusList.SingleOrDefault(x => x.ReferenceId == i));

        heatMapStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<HeatMapStatus>())).ReturnsAsync((HeatMapStatus heatMapStatus) =>
        {
            var index = heatMapStatusList.FindIndex(item => item.ReferenceId == heatMapStatus.ReferenceId);

            heatMapStatus.IsActive = false;

            heatMapStatusList[index] = heatMapStatus;

            return heatMapStatus;
        });

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetHeatMapStatusRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.GetHeatMapDetailByInfraObjectAndEntityId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => heatMapStatusList.FirstOrDefault(x => x.InfraObjectId == i && x.EntityId == j));

        heatMapStatusRepository.Setup(repo => repo.GetHeatMapStatusByEntityId(It.IsAny<string>())).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => heatMapStatusList.SingleOrDefault(x => x.ReferenceId == i));

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetHeatMapDetailByInfraObjectAndEntityIdRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.GetHeatMapDetailByInfraObjectAndEntityId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => heatMapStatusList.FirstOrDefault(x => x.InfraObjectId == i && x.EntityId == j));

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetImpactDetailRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(heatMapStatusList);

        heatMapStatusRepository.Setup(repo => repo.GetImpactDetail()).ReturnsAsync(heatMapStatusList);

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetHeatMapStatusEmptyRepository()
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<HeatMapStatus>());

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetPaginatedHeatMapStatusRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        var queryableHeatMapStatus = heatMapStatusList.BuildMock();

        heatMapStatusRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableHeatMapStatus);

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetHeatMapStatusTypeRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        var queryableHeatMapStatus = heatMapStatusList.BuildMock();
       
        heatMapStatusRepository.Setup(repo => repo.GetHeatMapStatusType(It.IsAny<string>())).Returns(queryableHeatMapStatus);
       
        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetHeatMapListByBusinessServiceIdRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var heatMapStatusRepository = new Mock<IHeatMapStatusRepository>();

        heatMapStatusRepository.Setup(repo => repo.GetHeatMapListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => heatMapStatusList.Where(x => x.BusinessServiceId == i).ToList());

        heatMapStatusRepository.Setup(repo => repo.GetHeatMapListByBusinessFunctionId(It.IsAny<string>())).ReturnsAsync((string i) => heatMapStatusList.Where(x => x.BusinessFunctionId == i).ToList());

        return heatMapStatusRepository;
    }

    public static Mock<IHeatMapStatusRepository> GetBusinessServiceSummaryReportRepository(List<HeatMapStatus> heatMapStatusList)
    {
        var getBusinessServiceSummaryReportRepository = new Mock<IHeatMapStatusRepository>();

        getBusinessServiceSummaryReportRepository.Setup(repo => repo.GetHeatMapDetailByInfraObjectId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => heatMapStatusList.Where(x => x.InfraObjectId == i && x.HeatmapType == j).ToList());

        return getBusinessServiceSummaryReportRepository;
    }
}