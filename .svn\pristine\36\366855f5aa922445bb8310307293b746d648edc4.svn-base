﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;

public class CreateSingleSignOnCommand : IRequest<CreateSingleSignOnResponse>
{
    public string SignOnTypeId { get; set; }
    public string SignOnType { get; set; }
    public string ProfileName { get; set; }
    public string Properties { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string FormVersion { get; set; }


    public override string ToString()
    {
        return $"ProfileName: {ProfileName};";
    }
}