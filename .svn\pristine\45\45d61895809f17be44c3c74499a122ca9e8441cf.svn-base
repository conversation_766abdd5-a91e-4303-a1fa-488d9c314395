﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class UserGroupSpecification : Specification<UserGroup>
{
    public UserGroupSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.GroupName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("groupName=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.GroupName.Contains(stringItem.Replace("groupName=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    //else if (stringItem.Contains("GroupDescription=", StringComparison.InvariantCultureIgnoreCase))
                    //    Or(p => p.GroupDescription.Contains(stringItem.Replace("GroupDescription=", "",
                    //        StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("userproperties=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.UserProperties.Contains(stringItem.Replace("userproperties=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.GroupName.Contains(searchString) || p.GroupDescription.Contains(searchString)
                                                       || p.UserProperties.Contains(searchString);
                ;
            }
        }
    }
}