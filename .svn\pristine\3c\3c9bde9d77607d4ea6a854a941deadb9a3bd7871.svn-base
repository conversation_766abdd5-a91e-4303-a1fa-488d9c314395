﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class TeamMasterService : BaseService, ITeamMasterService
{
    public TeamMasterService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateTeamMasterCommand team)
    {
        Logger.LogDebug($"Creating Team '{team.GroupName}'");

        return await Mediator.Send(team);
    }


    public async Task<BaseResponse> UpdateAsync(UpdateTeamMasterCommand team)
    {
        Logger.LogDebug($"Updating Team '{team.GroupName}'");

        return await Mediator.Send(team);
    }

    public async Task<BaseResponse> DeleteAsync(string teamId)
    {
        Guard.Against.InvalidGuidOrEmpty(teamId, "Team Id");

        Logger.LogDebug($"Deleting Team Details by Id '{teamId}'");

        return await Mediator.Send(new DeleteTeamMasterCommand { Id = teamId });
    }

    public async Task<List<TeamMasterDetailVm>> GetAllTeamNames()
    {
        Logger.LogDebug("Get All Teams ");

        return await Mediator.Send(new GetTeamMasterListQuery());
    }

    public async Task<PaginatedResult<TeamMasterListVm>> GetTeamConfigurationList(GetTeamMasterPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in TeamConfiguration Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<TeamMasterDetailVm> GetTeamNameById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Team Id");

        Logger.LogDebug($"Get Company Detail by Id '{id}'");

        return await Mediator.Send(new GetTeamMasterDetailQuery { Id = id });
    }

    public async Task<TeamMasterDetailVm> GetTeamNameByName(string name)
    {
        Guard.Against.NullOrWhiteSpace(name, "GroupName");

        Logger.LogDebug($"Get Team Detail by GroupName '{name}'");

        return await Mediator.Send(new GetTeamMasterDetailQuery { GroupName = name });
    }

    public async Task<bool> IsTeamNameAlreadyExist(string name, string id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Display Name");

        Logger.LogDebug($"Check Name Exists Detail by Display Name '{name}'and Id '{id}'");

        return await Mediator.Send(new GetTeamMasterNameUniqueQuery { GroupName = name, Id = id });
    }

    public async Task<bool> IsTeamNameExist(string name)
    {
        Guard.Against.NullOrWhiteSpace(name, "Display Name");

        Logger.LogDebug($"Check Name Exists Detail by Display Name '{name}'");

        return await Mediator.Send(new GetTeamMasterNameUniqueQuery { GroupName = name });
    }
}