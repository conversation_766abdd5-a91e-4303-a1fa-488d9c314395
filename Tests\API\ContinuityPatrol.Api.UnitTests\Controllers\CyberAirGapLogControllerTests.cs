using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberAirGapLogControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberAirGapLogsController _controller;
    private readonly CyberAirGapLogFixture _cyberAirGapLogFixture;

    public CyberAirGapLogControllerTests()
    {
        _cyberAirGapLogFixture = new CyberAirGapLogFixture();

        var testBuilder = new ControllerTestBuilder<CyberAirGapLogsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberAirGapLogsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberAirGapLogs_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberAirGapLogs = new List<CyberAirGapLogListVm>
        {
            _cyberAirGapLogFixture.CyberAirGapLogListVm,
            _cyberAirGapLogFixture.CyberAirGapLogListVm,
            _cyberAirGapLogFixture.CyberAirGapLogListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberAirGapLogs);

        // Act
        var result = await _controller.GetCyberAirGapLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapLogs = Assert.IsAssignableFrom<List<CyberAirGapLogListVm>>(okResult.Value);
        Assert.Equal(3, cyberAirGapLogs.Count);
    }

    [Fact]
    public async Task GetCyberAirGapLogById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberAirGapLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogDetailQuery>(q => q.Id == cyberAirGapLogId), default))
            .ReturnsAsync(_cyberAirGapLogFixture.CyberAirGapLogDetailVm);

        // Act
        var result = await _controller.GetCyberAirGapLogById(cyberAirGapLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapLog = Assert.IsType<CyberAirGapLogDetailVm>(okResult.Value);
        Assert.Equal(_cyberAirGapLogFixture.CyberAirGapLogDetailVm.AirGapName, cyberAirGapLog.AirGapName);
    }

    [Fact]
    public async Task GetPaginatedCyberAirGapLogs_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberAirGapLogListVm>
        {
            _cyberAirGapLogFixture.CyberAirGapLogListVm,
            _cyberAirGapLogFixture.CyberAirGapLogListVm
        };
        var expectedResults = PaginatedResult<CyberAirGapLogListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGapLogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapLogListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberAirGapLog_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.CreateCyberAirGapLogCommand;
        var expectedMessage = "CyberAirGapLog has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGapLog_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapLogFixture.UpdateCyberAirGapLogCommand;
        var expectedMessage = "CyberAirGapLog has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberAirGapLog_ReturnsOk()
    {
        // Arrange
        var cyberAirGapLogId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberAirGapLog has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberAirGapLogCommand>(c => c.Id == cyberAirGapLogId), default))
            .ReturnsAsync(new DeleteCyberAirGapLogResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberAirGapLog(cyberAirGapLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberAirGapLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task IsCyberAirGapLogNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var airGapLogName = "Existing Air Gap Log";
        var airGapLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogNameUniqueQuery>(q => 
                q.Name == airGapLogName && q.Id == airGapLogId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberAirGapLogNameExist(airGapLogName, airGapLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberAirGapLogNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var airGapLogName = "Unique Air Gap Log Name";
        var airGapLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogNameUniqueQuery>(q => 
                q.Name == airGapLogName && q.Id == airGapLogId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberAirGapLogNameExist(airGapLogName, airGapLogId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task CreateCyberAirGapLog_ValidatesAirGapId()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = "", // Empty AirGapId should cause validation error
            AirGapName = "Test Air Gap Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Description = "Test log entry",
            Status = "Success"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("AirGapId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCyberAirGapLog(command));
    }

    [Fact]
    public async Task UpdateCyberAirGapLog_ValidatesLogExists()
    {
        // Arrange
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Non-existent Air Gap Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Description = "Test log entry",
            Status = "Success"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CyberAirGapLog not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCyberAirGapLog(command));
    }

    [Fact]
    public async Task CreateCyberAirGapLog_HandlesComplexLogEntry()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Enterprise Critical Air Gap Replication Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Production Data Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Disaster Recovery Site",
            Port = 8443,
            Description = "Comprehensive log entry for enterprise air gap replication with detailed metrics and status information",
            Source = "{\"logEntry\":{\"timestamp\":\"2024-01-15T14:30:00Z\",\"level\":\"INFO\",\"operation\":\"REPLICATION_CYCLE\",\"metrics\":{\"filesTransferred\":1250,\"totalSize\":\"15.7GB\",\"transferRate\":\"125MB/s\",\"compressionRatio\":\"3.2:1\",\"encryptionTime\":\"45ms\"},\"checksums\":{\"verified\":1250,\"failed\":0},\"performance\":{\"cpuUsage\":\"25%\",\"memoryUsage\":\"1.2GB\",\"diskIO\":\"450MB/s\",\"networkUtilization\":\"85%\"}}}",
            Target = "{\"logEntry\":{\"timestamp\":\"2024-01-15T14:35:00Z\",\"level\":\"INFO\",\"operation\":\"REPLICATION_RECEIVED\",\"metrics\":{\"filesReceived\":1250,\"totalSize\":\"15.7GB\",\"receiveRate\":\"120MB/s\",\"decompressionTime\":\"180ms\",\"decryptionTime\":\"50ms\"},\"validation\":{\"checksumVerification\":\"PASSED\",\"integrityCheck\":\"PASSED\",\"virusScan\":\"CLEAN\"},\"storage\":{\"diskUsage\":\"78%\",\"availableSpace\":\"2.1TB\",\"writeSpeed\":\"380MB/s\"}}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Production Replication Engine",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "DR Replication Receiver",
            WorkflowStatus = "Successfully Completed",
            StartTime = DateTime.Now.AddMinutes(-30),
            EndTime = DateTime.Now.AddMinutes(-25),
            RPO = "2 minutes",
            Status = "Success",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapLog has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGapLog_HandlesErrorScenario()
    {
        // Arrange
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Failed Air Gap Replication Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "DR Site",
            Port = 8443,
            Description = "Log entry for failed replication attempt",
            Source = "{\"error\":{\"timestamp\":\"2024-01-15T15:30:00Z\",\"level\":\"ERROR\",\"operation\":\"REPLICATION_FAILED\",\"errorCode\":\"REP_001\",\"message\":\"Network timeout during file transfer\"}}",
            Target = "{\"error\":{\"timestamp\":\"2024-01-15T15:30:30Z\",\"level\":\"ERROR\",\"operation\":\"RECEIVE_TIMEOUT\",\"errorCode\":\"REC_001\",\"message\":\"Connection lost from source\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Source Replication Agent",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Target Replication Agent",
            WorkflowStatus = "Failed",
            StartTime = DateTime.Now.AddMinutes(-60),
            EndTime = DateTime.Now.AddMinutes(-55),
            RPO = "5 minutes",
            Status = "Failed",
            IsFileTransfered = false
        };

        var expectedMessage = "CyberAirGapLog has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetCyberAirGapLogs_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberAirGapLogListVm>());

        // Act
        var result = await _controller.GetCyberAirGapLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGapLogs = Assert.IsAssignableFrom<List<CyberAirGapLogListVm>>(okResult.Value);
        Assert.Empty(cyberAirGapLogs);
    }

    [Fact]
    public async Task GetCyberAirGapLogById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberAirGapLogById(invalidId));
    }

    [Fact]
    public async Task GetCyberAirGapLogById_HandlesNotFound()
    {
        // Arrange
        var cyberAirGapLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogDetailQuery>(q => q.Id == cyberAirGapLogId), default))
            .ThrowsAsync(new NotFoundException("CyberAirGapLog", cyberAirGapLogId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberAirGapLogById(cyberAirGapLogId));
    }

    [Fact]
    public async Task GetPaginatedCyberAirGapLogs_HandlesFilteringByStatus()
    {
        // Arrange
        var query = new GetCyberAirGapLogPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Failed"
        };

        var expectedData = new List<CyberAirGapLogListVm>
        {
            new CyberAirGapLogListVm
            {
                Id = Guid.NewGuid().ToString(),
                AirGapName = "Failed Log Entry 1",
                Status = "Failed",
                IsFileTransfered = false
            },
            new CyberAirGapLogListVm
            {
                Id = Guid.NewGuid().ToString(),
                AirGapName = "Failed Log Entry 2",
                Status = "Failed",
                IsFileTransfered = false
            }
        };
        var expectedResults = PaginatedResult<CyberAirGapLogListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapLogPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize && q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGapLogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapLogListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, log => Assert.Equal("Failed", log.Status));
    }

    [Fact]
    public async Task CreateCyberAirGapLog_HandlesInvalidAirGapReference()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Non-existent Air Gap",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Description = "Test log entry",
            Status = "Success"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("CyberAirGap", command.AirGapId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.CreateCyberAirGapLog(command));
    }

    [Fact]
    public async Task DeleteCyberAirGapLog_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberAirGapLog(invalidId));
    }

    [Fact]
    public async Task CreateCyberAirGapLog_HandlesWarningScenario()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Warning Air Gap Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "DR Site",
            Port = 8443,
            Description = "Log entry with warnings",
            Source = "{\"warning\":{\"timestamp\":\"2024-01-15T14:30:00Z\",\"level\":\"WARN\",\"operation\":\"REPLICATION_SLOW\",\"message\":\"Transfer rate below threshold\",\"metrics\":{\"expectedRate\":\"100MB/s\",\"actualRate\":\"75MB/s\"}}}",
            Target = "{\"warning\":{\"timestamp\":\"2024-01-15T14:30:10Z\",\"level\":\"WARN\",\"operation\":\"RECEIVE_SLOW\",\"message\":\"Receive rate below threshold\",\"metrics\":{\"expectedRate\":\"95MB/s\",\"actualRate\":\"70MB/s\"}}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Source Agent",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Target Agent",
            WorkflowStatus = "Completed with Warnings",
            StartTime = DateTime.Now.AddMinutes(-45),
            EndTime = DateTime.Now.AddMinutes(-40),
            RPO = "5 minutes",
            Status = "Warning",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapLog has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGapLog_HandlesStatusTransition()
    {
        // Arrange
        var command = new UpdateCyberAirGapLogCommand
        {
            Id = Guid.NewGuid().ToString(),
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Status Transition Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Source Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Target Site",
            Port = 8443,
            Description = "Log entry showing status transition from Warning to Success",
            Source = "{\"statusTransition\":{\"from\":\"Warning\",\"to\":\"Success\",\"timestamp\":\"2024-01-15T15:00:00Z\",\"reason\":\"Network issues resolved\"}}",
            Target = "{\"statusTransition\":{\"from\":\"Warning\",\"to\":\"Success\",\"timestamp\":\"2024-01-15T15:00:05Z\",\"reason\":\"Connectivity restored\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Source Monitor",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "Target Monitor",
            WorkflowStatus = "Recovered",
            StartTime = DateTime.Now.AddMinutes(-30),
            EndTime = DateTime.Now.AddMinutes(-25),
            RPO = "3 minutes",
            Status = "Success",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapLog has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGapLog(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapLogResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task IsCyberAirGapLogNameExist_ThrowsException_WhenNameIsEmpty()
    {
        // Arrange
        var emptyName = "";
        var airGapLogId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsCyberAirGapLogNameExist(emptyName, airGapLogId));
    }

    [Fact]
    public async Task CreateCyberAirGapLog_HandlesLargeFileTransfer()
    {
        // Arrange
        var command = new CreateCyberAirGapLogCommand
        {
            AirGapId = Guid.NewGuid().ToString(),
            AirGapName = "Large File Transfer Log",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Data Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "DR Site",
            Port = 8443,
            Description = "Log entry for large file transfer operation",
            Source = "{\"largeTransfer\":{\"totalFiles\":50000,\"totalSize\":\"500GB\",\"transferDuration\":\"2h 30m\",\"averageSpeed\":\"55MB/s\",\"peakSpeed\":\"120MB/s\",\"compressionRatio\":\"4.2:1\",\"checksumVerification\":\"SHA-256\"}}",
            Target = "{\"largeReceive\":{\"receivedFiles\":50000,\"receivedSize\":\"500GB\",\"receiveDuration\":\"2h 32m\",\"averageSpeed\":\"54MB/s\",\"verificationTime\":\"15m\",\"integrityCheck\":\"PASSED\",\"storageUtilization\":\"85%\"}}",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "High-Capacity Transfer Agent",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "High-Capacity Receive Agent",
            WorkflowStatus = "Large Transfer Completed",
            StartTime = DateTime.Now.AddHours(-3),
            EndTime = DateTime.Now.AddMinutes(-30),
            RPO = "1 minute",
            Status = "Success",
            IsFileTransfered = true
        };

        var expectedMessage = "CyberAirGapLog has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapLogResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGapLog(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapLogResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }
}
