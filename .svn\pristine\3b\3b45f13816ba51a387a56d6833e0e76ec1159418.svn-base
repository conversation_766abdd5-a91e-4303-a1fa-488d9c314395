﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.Create;

namespace ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;

public class
    CreatePageSolutionMappingCommandHandler : IRequestHandler<CreatePageSolutionMappingCommand,
        CreatePageSolutionMappingResponse>
{
    private readonly IMapper _mapper;
    private readonly IPageSolutionMappingRepository _pageSolutionMappingRepository;
    private readonly IPublisher _publisher;

    public CreatePageSolutionMappingCommandHandler(IMapper mapper,
        IPageSolutionMappingRepository pageSolutionMappingRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _pageSolutionMappingRepository = pageSolutionMappingRepository;
        _publisher = publisher;
    }

    public async Task<CreatePageSolutionMappingResponse> Handle(CreatePageSolutionMappingCommand request,
        CancellationToken cancellationToken)
    {
        var pageSolution = _mapper.Map<Domain.Entities.PageSolutionMapping>(request);

        pageSolution = await _pageSolutionMappingRepository.AddAsync(pageSolution);

        var response = new CreatePageSolutionMappingResponse
        {
            Message = Message.Create(nameof(Domain.Entities.PageSolutionMapping), pageSolution.Name),
            Id = pageSolution.ReferenceId
        };

        await _publisher.Publish(new PageSolutionMappingCreatedEvent { Name = pageSolution.Name }, cancellationToken);

        return response;
    }
}