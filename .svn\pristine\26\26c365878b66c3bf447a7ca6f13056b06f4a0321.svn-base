namespace ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Queries.GetDetail
{
    public class GetWorkflowApprovalMappingDetailsQueryHandler : IRequestHandler<GetWorkflowApprovalMappingDetailQuery, WorkflowApprovalMappingDetailVm>
    {
        private readonly IWorkflowApprovalMappingRepository _workflowApprovalMappingRepository;
        private readonly IMapper _mapper;

        public GetWorkflowApprovalMappingDetailsQueryHandler(IMapper mapper, IWorkflowApprovalMappingRepository workflowApprovalMappingRepository)
        {
            _mapper = mapper;
            _workflowApprovalMappingRepository = workflowApprovalMappingRepository;
        }

        public async Task<WorkflowApprovalMappingDetailVm> Handle(GetWorkflowApprovalMappingDetailQuery request, CancellationToken cancellationToken)
        {
            var workflowApprovalMapping = await _workflowApprovalMappingRepository.GetByReferenceIdAsync(request.Id);

            Guard.Against.NullOrDeactive(workflowApprovalMapping, nameof(Domain.Entities.WorkflowApprovalMapping), new NotFoundException(nameof(Domain.Entities.WorkflowApprovalMapping), request.Id));

            var workflowApprovalMappingDetailDto = _mapper.Map<WorkflowApprovalMappingDetailVm>(workflowApprovalMapping);

            return workflowApprovalMappingDetailDto;
        }
    }
}
