﻿using ContinuityPatrol.Domain.ViewModels.CyberJobManagementModel;

namespace ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetJobStatus;

public class
    GetCyberJobManagementStatusQueryHanlder : IRequestHandler<GetCyberJobManagementStatusQuery,
        List<CyberJobManagementStatusVm>>
{
    private readonly ICyberJobManagementRepository _cyberJobManagementRepository;

    public GetCyberJobManagementStatusQueryHanlder(ICyberJobManagementRepository cyberJobManagementRepository)
    {
        _cyberJobManagementRepository = cyberJobManagementRepository;
    }

    public async Task<List<CyberJobManagementStatusVm>> Handle(GetCyberJobManagementStatusQuery request,
        CancellationToken cancellationToken)
    {
        var cyberJobmanagementList = await _cyberJobManagementRepository.ListAllAsync();

        var result = cyberJobmanagementList.GroupBy(x => x.Status.ToLower())
            .Select(x => new CyberJobManagementStatusVm { Status = x.Key, Count = x.Count() }).ToList();

        return result;
    }
}