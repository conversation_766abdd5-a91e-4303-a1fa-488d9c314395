﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Commands;

public class CreateBusinessServiceTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;
    private readonly CreateBusinessServiceCommandHandler _handler;

    public CreateBusinessServiceTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.CreateBusinessServiceRepository(businessServiceFixture.BusinessServices);

        _handler = new CreateBusinessServiceCommandHandler(businessServiceFixture.Mapper, _mockBusinessServiceRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseBusinessServiceCount_When_BusinessServiceCreated()
    {
        await _handler.Handle(_businessServiceFixture.CreateBusinessServiceCommand, CancellationToken.None);

        var allCategories = await _mockBusinessServiceRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_businessServiceFixture.BusinessServices.Count);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceResponse_When_BusinessServiceCreated()
    {
        var result = await _handler.Handle(_businessServiceFixture.CreateBusinessServiceCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBusinessServiceResponse));

        result.BusinessServiceId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceFixture.CreateBusinessServiceCommand, CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BusinessService>()), Times.Once);
    }
}