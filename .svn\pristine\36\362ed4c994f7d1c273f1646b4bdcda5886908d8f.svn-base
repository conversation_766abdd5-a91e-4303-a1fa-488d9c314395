﻿using ContinuityPatrol.Application.Features.EscalationMatrix.Events.Update;

namespace ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;

public class
    UpdateEscalationMatrixCommandHandler : IRequestHandler<UpdateEscalationMatrixCommand,
        UpdateEscalationMatrixResponse>
{
    private readonly IEscalationMatrixRepository _escalationMatrixRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateEscalationMatrixCommandHandler(IMapper mapper,
        IPublisher publisher, IEscalationMatrixRepository escalationMatrixRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _escalationMatrixRepository = escalationMatrixRepository;
    }

    public async Task<UpdateEscalationMatrixResponse> Handle(UpdateEscalationMatrixCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _escalationMatrixRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.EscalationMatrix), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateEscalationMatrixCommand),
            typeof(Domain.Entities.EscalationMatrix));

        await _escalationMatrixRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateEscalationMatrixResponse
        {
            Message = Message.Update(nameof(Domain.Entities.EscalationMatrix), eventToUpdate.EscMatName),

            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(new EscalationMatrixUpdatedEvent { EscMatName = eventToUpdate.EscMatName },
            cancellationToken);

        return response;
    }
}