namespace ContinuityPatrol.Domain.Entities;

public class ApprovalMatrixApproval : AuditableEntity
{
    public string RequestId { get; set; }
    public string ApprovalMatrixId { get; set; }
    public string ProcessName { get; set; }
	public string Description { get; set; }
    public string UserId { get; set; }
    public string UserName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string Status { get; set; }
	public string Message { get; set; }
    [Column(TypeName = "NCLOB")] public string Sla { get; set; }
	public bool IsApproval { get; set; }
    [Column(TypeName = "NCLOB")] public string ErrorMessage { get; set; }
    public string ApproverId { get; set; }
    public string ApproverName { get; set; }
	public DateTime StartDateTime { get; set; }
	public DateTime EndDateTime { get; set; }
		
}
