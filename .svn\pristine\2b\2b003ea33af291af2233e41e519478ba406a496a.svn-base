﻿
//GET
const getRequest = async (url) => {
    const response = await $.ajax({
        type: "GET",
        url: url,
        dataType: "json"
    });

    if (response?.success) {
        return response?.data;
    } else {
        errorNotification(response);
    }
};

//GET with data 
const getRequestWithData = async (url, data) => {
    const response = await $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json"
    });

    if (response?.success) {
        return response?.data;
    } else {
        errorNotification(response);
    }
};

//POST with data 
const postRequestWithData = async (url, data) => {
    const response = await $.ajax({
        type: "POST",
        url: url,
        data: data,
        dataType: "json"
    });

    if (response?.success) {
        return response?.data || response;
    } else {
        errorNotification(response);
    }
};

//FormBuilder isPublish
const isPublishWithData = async (url, data) => {
    const response = await $.ajax({
        type: "POST",
        url: url,
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: data,
        contentType: false,
        processData: false,
    });

    return response;
};

//CreateOrUpdate
const createOrUpdate = async (url, data) => {
    const response = await $.ajax({
        type: "POST",
        url: url,
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: data,
        contentType: false,
        processData: false,
    });

    return response;
};

//Delete
const deleteData = async (url, data) => {
    const response = await $.ajax({
        type: "DELETE",
        url: url,
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: data,
        contentType: false,
        processData: false,
    });

    return response;
};


const preventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

//only for component Type
const SpecialCharValidateCompType = (value) => {
    const regex = /^[a-zA-Z0-9_\s-]*$/;
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}

async function moduleNameValidation(type, value, nameexist, errorelement, errormessage, data) {
    if (value.includes('<')) {
        errorelement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    if (!value) {
        errorelement.text(errormessage).addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + nameexist;
    const validationResults = [
        await SpecialCharValidateCustom(value),
        //type === "componenttype" ? SpecialCharValidateCompType(value) : await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        await ShouldNotEndWithHyphen(value),
        await ShouldNotBeginWithHyphen(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsFormNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorelement, validationResults);
}

async function IsFormNameExist(url, data, errorFunc) {
    return !Object.values(data)[1].trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? "Name already exists" : true;
};

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data)
        .fail(errorFunc);
};