﻿using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;

namespace ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetDetail;

public class GetRoboCopyJobDetailQueryHandler : IRequestHandler<GetRoboCopyJobDetailQuery, RoboCopyJobDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IRoboCopyJobRepository _roboCopyJobRepository;

    public GetRoboCopyJobDetailQueryHandler(IRoboCopyJobRepository roboCopyJobRepository, IMapper mapper)
    {
        _roboCopyJobRepository = roboCopyJobRepository;
        _mapper = mapper;
    }

    public async Task<RoboCopyJobDetailVm> Handle(GetRoboCopyJobDetailQuery request,
        CancellationToken cancellationToken)
    {
        var roboCopyJob = await _roboCopyJobRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(roboCopyJob, nameof(Domain.Entities.RoboCopyJob),
            new NotFoundException(nameof(Domain.Entities.RoboCopyJob), request.Id));

        var result = _mapper.Map<RoboCopyJobDetailVm>(roboCopyJob);

        return result;
    }
}