﻿namespace ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;

public class ClearSessionUserLoginCommandHandler : IRequestHandler<ClearSessionUserLoginCommand, ClearSessionUserLoginResponse>
{
    private readonly IUserLoginRepository _userLoginRepository;

    public ClearSessionUserLoginCommandHandler(IUserLoginRepository userLoginRepository)
    {
        _userLoginRepository = userLoginRepository;
    }

    public async Task<ClearSessionUserLoginResponse> Handle(ClearSessionUserLoginCommand request, CancellationToken cancellationToken)
    {
        var userLogin = await _userLoginRepository.GetUserLoginByUserId(request.UserId);

        if(userLogin is null)
        {
            return new ClearSessionUserLoginResponse
            {
                UserId = request.UserId,
                Message = "User Not Found",
                Success = false
            };
        }

        userLogin.SessionId = string.Empty;
        userLogin.LastLoggedOutDate = DateTime.Now;
        userLogin.IsProperLoggedOut = true;

        await _userLoginRepository.UpdateAsync(userLogin);

        return new ClearSessionUserLoginResponse
        {
            UserId = userLogin.UserId,
            Message = "Session Cleared Successfully",
        };
    }
}