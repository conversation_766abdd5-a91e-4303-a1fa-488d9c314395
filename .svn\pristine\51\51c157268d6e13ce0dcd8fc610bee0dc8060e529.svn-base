﻿using ContinuityPatrol.Application.Features.WorkflowAction.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Events;

public class WorkflowActionUpdatedEventHandlerTests : IClassFixture<WorkflowActionFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowActionUpdatedEventHandler _handler;

    public WorkflowActionUpdatedEventHandlerTests(WorkflowActionFixture workflowActionFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowActionEventLogger = new Mock<ILogger<WorkflowActionUpdatedEventHandler>>();

        _mockUserActivityRepository = WorkflowActionRepositoryMocks.CreateWorkflowActionEventRepository(_workflowActionFixture.UserActivities);

        _handler = new WorkflowActionUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowActionEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowActionEventUpdated()
    {
        _workflowActionFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionFixture.WorkflowActionUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowActionFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowActionFixture.WorkflowActionUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowActionEventUpdated()
    {
        _workflowActionFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowActionFixture.WorkflowActionUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowActionFixture.UserActivities[0].Id);

        result.Equals(_workflowActionFixture.WorkflowActionUpdatedEvent.ActionName);

        await Task.CompletedTask;
    }
}