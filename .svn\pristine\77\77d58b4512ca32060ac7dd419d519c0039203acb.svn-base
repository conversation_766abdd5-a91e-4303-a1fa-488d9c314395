using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Events;

public class UpdateBulkImportOperationGroupEventTests : IClassFixture<BulkImportOperationGroupFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportOperationGroupUpdatedEventHandler _handler;

    public UpdateBulkImportOperationGroupEventTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportoperationgroup");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportOperationGroupEventLogger = new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>();

        _mockUserActivityRepository = BulkImportOperationGroupRepositoryMocks.CreateBulkImportOperationGroupEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportOperationGroupUpdatedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportOperationGroupEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateBulkImportOperationGroupEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };

        // Act
        var result = _handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };

        // Act
        await _handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Update BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Update");
        capturedUserActivity.ActivityDetails.ShouldContain("TestInfraObject");
        capturedUserActivity.ActivityDetails.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportOperationGroupUpdated()
    {
        // Arrange
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        var mockLogger = new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>();

        var handler = new BulkImportOperationGroupUpdatedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        bulkImportOperationGroupUpdatedEvent.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportOperationGroupUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "ProductionInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Update BulkImportOperationGroup");
        capturedUserActivity.Entity.ShouldBe("BulkImportOperationGroup");
        capturedUserActivity.ActivityType.ShouldBe("Update");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportOperationGroup 'ProductionInfraObject' updated successfully.");
    }

    [Fact]
    public async Task Handle_NotSetCreatedByAndLastModifiedBy_When_UpdateEvent()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportOperationGroupUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        // Update events don't set CreatedBy and LastModifiedBy unlike Create events
        capturedUserActivity.CreatedBy.ShouldBeNull();
        capturedUserActivity.LastModifiedBy.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("updated successfully");
    }

    [Fact]
    public async Task Handle_SetCorrectRequestUrl_When_UserServiceProvided()
    {
        // Arrange
        var testRequestUrl = "/api/bulkimportoperationgroup/update";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testRequestUrl);

        var handler = new BulkImportOperationGroupUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.RequestUrl.ShouldBe(testRequestUrl);
    }

    [Fact]
    public async Task Handle_SetCorrectHostAddress_When_UserServiceProvided()
    {
        // Arrange
        var testHostAddress = "********";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testHostAddress);

        var handler = new BulkImportOperationGroupUpdatedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportOperationGroupUpdatedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportOperationGroupUpdatedEvent = new BulkImportOperationGroupUpdatedEvent { Name = "TestInfraObject" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportOperationGroupUpdatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.HostAddress.ShouldBe(testHostAddress);
    }
}
