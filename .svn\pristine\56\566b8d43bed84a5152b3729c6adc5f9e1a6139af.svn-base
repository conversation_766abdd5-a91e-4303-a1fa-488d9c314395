using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class GroupPolicyFixture : IDisposable
{
    public List<GroupPolicy> GroupPolicyPaginationList { get; set; }
    public List<GroupPolicy> GroupPolicyList { get; set; }
    public GroupPolicy GroupPolicyDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public GroupPolicyFixture()
    {
        var fixture = new Fixture();

        GroupPolicyList = fixture.Create<List<GroupPolicy>>();

        GroupPolicyPaginationList = fixture.CreateMany<GroupPolicy>(20).ToList();

        // Setup proper test data for GroupPolicyPaginationList
        GroupPolicyPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        GroupPolicyPaginationList.ForEach(x => x.CompanyId = CompanyId);
        GroupPolicyPaginationList.ForEach(x => x.IsActive = true);

        // Setup proper test data for GroupPolicyList
        GroupPolicyList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        GroupPolicyList.ForEach(x => x.CompanyId = CompanyId);
        GroupPolicyList.ForEach(x => x.IsActive = true);

        GroupPolicyDto = fixture.Create<GroupPolicy>();
        GroupPolicyDto.ReferenceId = Guid.NewGuid().ToString();
        GroupPolicyDto.CompanyId = CompanyId;
        GroupPolicyDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
