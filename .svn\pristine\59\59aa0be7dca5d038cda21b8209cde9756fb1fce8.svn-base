﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Events.PaginatedView;

public class ApprovalMatrixPaginatedEventHandler : INotificationHandler<ApprovalMatrixPaginatedEvent>
{
    private readonly ILogger<ApprovalMatrixPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<ApprovalMatrixPaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ApprovalMatrixPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.ApprovalMatrix}",
            Entity = Modules.ApprovalMatrix.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Approval Request viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Approval Request viewed");
    }
}