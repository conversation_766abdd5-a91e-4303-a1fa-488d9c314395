﻿namespace ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetList;

public class
    GetMongoDBMonitorLogListQueryHandler : IRequestHandler<GetMongoDBMonitorLogListQuery, List<MongoDBMonitorLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorLogRepository _mongoDBMonitorLogRepository;

    public GetMongoDBMonitorLogListQueryHandler(IMongoDbMonitorLogRepository mongoDBMonitorLogRepository,
        IMapper mapper)
    {
        _mongoDBMonitorLogRepository = mongoDBMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<MongoDBMonitorLogListVm>> Handle(GetMongoDBMonitorLogListQuery request,
        CancellationToken cancellationToken)
    {
        var mongoDBMonitorLogRepositoryList = await _mongoDBMonitorLogRepository.ListAllAsync();

        return mongoDBMonitorLogRepositoryList.Count <= 0
            ? new List<MongoDBMonitorLogListVm>()
            : _mapper.Map<List<MongoDBMonitorLogListVm>>(mongoDBMonitorLogRepositoryList);
    }
}