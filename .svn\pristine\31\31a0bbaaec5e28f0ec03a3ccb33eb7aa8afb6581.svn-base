﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ServerTypeRepositoryMocks
{

    public static Mock<IServerTypeRepository> CreateServerTypeRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverTypes);

        mockServerTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<ServerType>())).ReturnsAsync(
            (ServerType serverType) =>
            {
                serverType.Id = new Fixture().Create<int>();

                serverType.ReferenceId = new Fixture().Create<Guid>().ToString();

                serverTypes.Add(serverType);

                return serverType;
            });

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> UpdateServerTypeRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverTypes);

        mockServerTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => serverTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockServerTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ServerType>())).ReturnsAsync((ServerType serverType) =>
        {
            var index = serverTypes.FindIndex(item => item.ReferenceId == serverType.ReferenceId);

            serverTypes[index] = serverType;

            return serverType;
        });

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> DeleteServerTypeRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverTypes);

        mockServerTypeRepository.Setup(repo => repo.GetServerTypeById(It.IsAny<string>())).ReturnsAsync((string i) => serverTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockServerTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ServerType>())).ReturnsAsync((ServerType serverType) =>
        {
            var index = serverTypes.FindIndex(item => item.ReferenceId == serverType.ReferenceId);

            serverType.IsActive = false;

            serverTypes[index] = serverType;

            return serverType;
        });

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> GetServerTypeRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(serverTypes);

        mockServerTypeRepository.Setup(repo => repo.GetServerTypeById(It.IsAny<string>())).ReturnsAsync((string i) => serverTypes.SingleOrDefault(x => x.ReferenceId == i));

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> GetServerTypeNameUniqueRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.IsServerTypeNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => serverTypes.Exists(x => x.Name == i && x.ReferenceId == j));

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> GetServerTypeEmptyRepository()
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ServerType>());

        return mockServerTypeRepository;
    }

    public static Mock<IServerTypeRepository> GetPaginatedServerTypeRepository(List<ServerType> serverTypes)
    {
        var mockServerTypeRepository = new Mock<IServerTypeRepository>();

        var queryableServer = serverTypes.BuildMock();

        mockServerTypeRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableServer);

        return mockServerTypeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateServerTypeEventRepository(List<UserActivity> userActivities)
    {
        var mockServerTypeRepository = new Mock<IUserActivityRepository>();

        mockServerTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockServerTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockServerTypeRepository;
    }
}