﻿ @using ContinuityPatrol.Shared.Core.Enums;
@using ContinuityPatrol.Shared.Services.Helper;
<link href="~/css/Menu.css" rel="stylesheet" type="text/css" onload="this.media='all'" />
<link href="~/fonts/cp-icons/cp-icon.css" rel="stylesheet" />
<nav class="navbar navbar-expand-lg">
    <div class="container-fluid justify-content-between Top_Menu">
        <a class="navbar-brand p-0" href="#">
            @* <img class="logo" src="~/img/logo/cplogo.svg" alt="Cp Logo" title="CP product logo" id="businessLogoNavigate" /> *@
            <svg id="businessLogoNavigate" width="198" height="34" viewBox="0 0 198 34" fill="none"><path d="M18.735 23.9493L8.21387 21.4447L13.2238 11.8244C13.725 10.8227 14.6266 10.0212 15.6291 9.52001C16.6308 9.01879 17.8334 8.9187 18.9359 9.21973L29.7573 11.8244L24.4463 21.5448C23.9459 22.5465 23.0435 23.2479 22.0418 23.649C21.0401 24.0501 19.8375 24.1502 18.735 23.9493Z" fill="#00C2FF" /><path d="M13.9279 19.9438L3.30664 17.6394L8.51752 6.61705C9.00313 5.5344 9.80726 4.62552 10.8227 4.01161C11.8898 3.41968 13.1264 3.20792 14.3297 3.41104L25.1519 5.81555L19.5399 16.938C19.0394 17.9404 18.2379 18.842 17.2354 19.3432C16.2337 20.0446 15.0311 20.144 13.9286 19.9438H13.9279Z" fill="#0492FF" /><path d="M11.622 15.0332L1 12.628L5.80976 2.90839C6.31098 1.90669 7.11248 1.10519 8.21501 0.603974C9.2167 0.102757 10.4193 0.00266143 11.5219 0.203593L22.3433 2.6081L17.1339 12.4271C16.6326 13.4295 15.8311 14.1309 14.8287 14.6314C13.827 15.1326 12.7245 15.2327 11.6227 15.0325L11.622 15.0332Z" fill="#0479FF" /><path d="M43.88 20.2417C43.0376 20.2603 42.2 20.1098 41.4168 19.799C40.6336 19.4883 39.9206 19.0236 39.3201 18.4326C38.7086 17.8385 38.2273 17.1237 37.9069 16.3335C37.5865 15.5434 37.434 14.6953 37.4591 13.843C37.4323 12.9873 37.5839 12.1353 37.9043 11.3413C38.2247 10.5473 38.7068 9.82872 39.3201 9.23124C39.9175 8.63444 40.6294 8.16457 41.413 7.84985C42.1966 7.53514 43.0358 7.38208 43.88 7.39987C45.1395 7.37743 46.3781 7.72297 47.4441 8.39415C48.4855 9.04807 49.2808 10.0287 49.7055 11.1827H47.2484C46.9326 10.5728 46.4457 10.0682 45.8474 9.73096C45.2491 9.39367 44.5653 9.2383 43.88 9.28388C43.3027 9.26441 42.7277 9.36559 42.1918 9.58097C41.6558 9.79635 41.1707 10.1212 40.7674 10.5347C39.9478 11.4361 39.5195 12.626 39.5767 13.843C39.5274 15.0499 39.9552 16.2277 40.7674 17.1217C41.1719 17.5332 41.6574 17.8561 42.1932 18.0702C42.729 18.2842 43.3034 18.3846 43.88 18.3651C44.5653 18.4107 45.2491 18.2553 45.8474 17.918C46.4457 17.5808 46.9326 17.0762 47.2484 16.4663H49.7204C49.2955 17.62 48.5002 18.6004 47.459 19.2541C46.385 19.9188 45.1428 20.262 43.88 20.2417ZM58.6125 17.6555C58.9252 17.3203 59.1666 16.925 59.3221 16.4938C59.4775 16.0625 59.5439 15.6042 59.5171 15.1465C59.546 14.691 59.4822 14.2344 59.3295 13.8042C59.1768 13.3741 58.9385 12.9794 58.6288 12.6441C58.3595 12.3527 58.0316 12.1215 57.6667 11.9656C57.3018 11.8097 56.908 11.7327 56.5113 11.7396C56.1178 11.7322 55.7273 11.8091 55.3661 11.9651C55.0048 12.1211 54.6811 12.3526 54.4167 12.6441C53.8142 13.3393 53.5083 14.2431 53.5648 15.1613C53.5105 16.0767 53.8102 16.9779 54.4019 17.6785C54.662 17.9636 54.9801 18.1898 55.3349 18.3419C55.6896 18.4939 56.0728 18.5683 56.4586 18.5601C56.8607 18.565 57.2595 18.4872 57.6302 18.3316C58.001 18.176 58.3358 17.9459 58.614 17.6555H58.6125ZM56.4579 20.301C55.7962 20.3214 55.1373 20.2063 54.5217 19.9628C53.9061 19.7193 53.3468 19.3524 52.8782 18.8849C52.4076 18.39 52.0422 17.8049 51.8039 17.165C51.5656 16.525 51.4594 15.8434 51.4917 15.1613C51.4894 14.153 51.7838 13.1662 52.3381 12.3239C52.8925 11.4816 53.6823 10.8209 54.6093 10.4241C55.5363 10.0273 56.5595 9.91193 57.5516 10.0923C58.5437 10.2727 59.4608 10.7408 60.1888 11.4385C60.6807 11.9184 61.0658 12.4966 61.319 13.1354C61.5723 13.7742 61.6879 14.4592 61.6584 15.1458C61.6849 15.8377 61.5642 16.5274 61.3044 17.1693C61.0446 17.8112 60.6514 18.3906 60.151 18.8693C59.6625 19.3428 59.0839 19.7135 58.4496 19.9594C57.8152 20.2054 57.1379 20.3215 56.4579 20.301ZM65.7341 10.1729V11.7403C66.0461 11.1803 66.5107 10.7202 67.0737 10.4136C67.6367 10.1071 68.2752 9.9665 68.9149 10.0083C69.4287 9.99215 69.9402 10.0837 70.4165 10.277C70.8927 10.4703 71.3233 10.7612 71.6805 11.1308C72.0656 11.5511 72.3622 12.0446 72.5527 12.5818C72.7431 13.1191 72.8235 13.6893 72.789 14.2582V20.1357H70.7389V14.5659C70.793 13.8245 70.555 13.0919 70.0753 12.5233C69.8427 12.2725 69.5586 12.0752 69.2424 11.9449C68.9263 11.8146 68.5856 11.7544 68.2439 11.7685C67.9013 11.7558 67.56 11.8166 67.2428 11.9468C66.9256 12.077 66.64 12.2735 66.4051 12.5233C65.9205 13.0888 65.6795 13.8232 65.7349 14.5659V20.1357H63.6685V10.1729H65.7341ZM80.2412 11.8456H77.8226V17.3619C77.7974 17.6555 77.8872 17.9477 78.071 18.1768C78.338 18.3584 78.6612 18.4415 78.983 18.4103H80.2412V20.1357H78.6212C76.7073 20.1357 75.7503 19.2138 75.7503 17.3701V11.8463H74.5751V10.1729H75.7481V7.69348H77.8286V10.1729H80.2472L80.2412 11.8456ZM84.4994 7.51998C84.5038 7.69495 84.472 7.86894 84.4059 8.031C84.3398 8.19306 84.2408 8.33967 84.1153 8.46162C83.9892 8.58929 83.8372 8.6884 83.6695 8.75223C83.5018 8.81606 83.3223 8.84313 83.1433 8.8316C82.9723 8.83616 82.8022 8.80502 82.6439 8.74017C82.4856 8.67532 82.3425 8.57818 82.2239 8.45495C82.0371 8.27179 81.909 8.03727 81.8558 7.78116C81.8026 7.52506 81.8267 7.25892 81.9251 7.01654C82.0236 6.77401 82.192 6.56626 82.409 6.41971C82.6259 6.27316 82.8815 6.19443 83.1433 6.19354C83.3177 6.19065 83.4909 6.22277 83.6527 6.28799C83.8145 6.35321 83.9616 6.4502 84.0853 6.57325C84.2089 6.69629 84.3067 6.84287 84.3727 7.00434C84.4387 7.16581 84.4717 7.33888 84.4697 7.51331L84.4994 7.51998ZM82.1482 20.1357V10.1729H84.2058V20.1357H82.1482ZM88.9762 10.1729V11.7403C89.2895 11.1816 89.7542 10.7226 90.3168 10.4162C90.8793 10.1098 91.5169 9.96842 92.1563 10.0083C92.6713 9.99192 93.1841 10.0833 93.6617 10.2766C94.1393 10.4699 94.5713 10.7609 94.93 11.1308C95.3126 11.5525 95.6071 12.0463 95.796 12.5834C95.985 13.1205 96.0647 13.6899 96.0303 14.2582V20.1357H93.9876V14.5659C94.0396 13.8235 93.7987 13.0903 93.3166 12.5233C93.0852 12.2731 92.8023 12.0761 92.4875 11.9458C92.1726 11.8155 91.8332 11.755 91.4927 11.7685C91.149 11.7562 90.8067 11.8171 90.4884 11.9472C90.1701 12.0774 89.8831 12.2737 89.6465 12.5233C89.1647 13.0904 88.9241 13.8236 88.9762 14.5659V20.1357H86.9113V10.1729H88.9762ZM105.623 10.1729H107.688V20.1357H105.623V18.5609C105.31 19.1188 104.845 19.5763 104.282 19.8804C103.719 20.1845 103.081 20.3227 102.443 20.2788C101.928 20.296 101.414 20.2073 100.935 20.018C100.455 19.8287 100.019 19.543 99.6545 19.1785C99.2759 18.7547 98.9851 18.2602 98.7988 17.7234C98.6125 17.1866 98.5344 16.6182 98.569 16.0511V10.1729H100.612V15.7419C100.564 16.4885 100.804 17.2248 101.283 17.7994C101.782 18.2545 102.434 18.5068 103.11 18.5068C103.786 18.5068 104.438 18.2545 104.937 17.7994C105.427 17.2301 105.673 16.4911 105.623 15.7419V10.1729ZM112.76 7.5274C112.764 7.7023 112.731 7.87606 112.665 8.03799C112.599 8.19993 112.5 8.34661 112.375 8.46904C112.253 8.59374 112.106 8.69168 111.944 8.75665C111.782 8.82161 111.608 8.85219 111.434 8.84643C111.263 8.85099 111.093 8.81985 110.934 8.755C110.776 8.69014 110.633 8.59301 110.514 8.46978C110.328 8.28662 110.2 8.0521 110.146 7.79599C110.093 7.53989 110.117 7.27375 110.216 7.03137C110.314 6.78884 110.483 6.58109 110.7 6.43454C110.916 6.28799 111.172 6.20926 111.434 6.20837C111.608 6.20548 111.782 6.2376 111.943 6.30282C112.105 6.36803 112.252 6.46503 112.376 6.58807C112.5 6.71112 112.597 6.8577 112.663 7.01917C112.729 7.18064 112.762 7.35296 112.76 7.5274ZM110.409 20.1357V10.1729H112.467V20.1357H110.409ZM120.001 11.8463H117.583V17.3627C117.557 17.6559 117.646 17.9476 117.831 18.1768C118.096 18.3577 118.416 18.44 118.736 18.4103H120.001V20.1357H118.374C116.46 20.1357 115.503 19.2138 115.503 17.3701V11.8463H114.327V10.1729H115.503V7.69348H117.583V10.1729H120.001V11.8463ZM124.652 19.9607L120.726 10.164H122.987L125.79 17.7579L128.707 10.164H130.838L124.727 24.8157H122.587L124.652 19.9607ZM142.194 7.55632C143.389 7.47781 144.568 7.86079 145.488 8.62622C145.862 8.9545 146.159 9.36096 146.358 9.81683C146.558 10.2727 146.655 10.7668 146.642 11.2643C146.65 11.7615 146.556 12.2552 146.367 12.7151C146.178 13.175 145.897 13.5916 145.542 13.9394C145.085 14.3347 144.553 14.6344 143.979 14.8208C143.404 15.0072 142.798 15.0766 142.196 15.0249H139.935V20.1357H137.855V7.56818L142.194 7.55632ZM139.934 13.4182H142.194C142.831 13.4677 143.462 13.2648 143.95 12.8532C144.149 12.6483 144.302 12.4042 144.401 12.1364C144.5 11.8686 144.541 11.5832 144.523 11.2984C144.541 11.0106 144.499 10.7222 144.401 10.4513C144.302 10.1803 144.149 9.93259 143.95 9.72356C143.461 9.3144 142.83 9.11389 142.194 9.16525H139.934V13.4182ZM155.194 17.5925C155.501 17.2644 155.74 16.8779 155.895 16.456C156.05 16.034 156.119 15.5852 156.098 15.1361C156.121 14.689 156.053 14.2417 155.898 13.8218C155.742 13.4018 155.503 13.018 155.194 12.6938C154.924 12.4048 154.597 12.1751 154.234 12.0194C153.871 11.8637 153.479 11.7853 153.084 11.7892C152.686 11.7853 152.292 11.8615 151.924 12.013C151.557 12.1646 151.223 12.3885 150.944 12.6715C150.642 12.9959 150.408 13.3779 150.256 13.7948C150.105 14.2116 150.039 14.6547 150.062 15.0976C150.021 16.0075 150.337 16.8975 150.944 17.577C151.218 17.8691 151.55 18.1015 151.918 18.2597C152.286 18.4179 152.683 18.4985 153.084 18.4963C153.475 18.4981 153.862 18.4193 154.221 18.265C154.58 18.1106 154.903 17.884 155.171 17.5992L155.194 17.5925ZM152.649 20.2936C152.02 20.3052 151.396 20.1821 150.819 19.9326C150.242 19.6831 149.725 19.313 149.303 18.8471C148.382 17.8283 147.895 16.4897 147.946 15.1168C147.916 14.4443 148.019 13.7725 148.25 13.1399C148.48 12.5073 148.833 11.9264 149.288 11.4304C149.714 10.9664 150.234 10.5988 150.814 10.352C151.393 10.1052 152.019 9.98501 152.649 9.99938C153.374 9.98368 154.089 10.1714 154.714 10.5414C155.295 10.8698 155.775 11.3521 156.1 11.9353V10.1729H158.181V20.1357H156.1V18.2732C155.776 18.8878 155.298 19.4081 154.714 19.7835C154.081 20.1497 153.357 20.3299 152.626 20.3033L152.649 20.2936ZM165.694 11.8456H163.275V17.3619C163.25 17.6554 163.339 17.9474 163.523 18.1768C163.79 18.3584 164.114 18.4415 164.435 18.4103H165.694V20.1357H164.066C162.152 20.1357 161.195 19.2138 161.195 17.3701V11.8463H160.012V10.1729H161.187V7.69348H163.268V10.1729H165.687L165.694 11.8456ZM169.658 10.1655V11.8975C169.91 11.3139 170.333 10.8202 170.871 10.4813C171.409 10.1424 172.037 9.97415 172.672 9.99864V12.1303H172.152C171.819 12.1056 171.484 12.1475 171.167 12.2536C170.85 12.3596 170.557 12.5276 170.305 12.7479C169.831 13.3441 169.6 14.0977 169.658 14.8573V20.1364H167.601V10.1729L169.658 10.1655ZM181.015 17.6489C181.327 17.3135 181.569 16.9181 181.724 16.4867C181.879 16.0553 181.946 15.5968 181.919 15.1391C181.948 14.6838 181.884 14.2273 181.731 13.7973C181.579 13.3673 181.341 12.9727 181.031 12.6374C180.763 12.3462 180.437 12.1151 180.073 11.9592C179.709 11.8033 179.317 11.7261 178.921 11.7329C178.526 11.7248 178.134 11.8014 177.772 11.9574C177.409 12.1134 177.084 12.3453 176.818 12.6374C176.216 13.3328 175.91 14.2365 175.967 15.1547C175.913 16.0699 176.212 16.9711 176.803 17.6719C177.063 17.9568 177.379 18.1833 177.733 18.3364C178.086 18.4894 178.468 18.5654 178.854 18.5594C179.257 18.5652 179.657 18.4879 180.029 18.3323C180.401 18.1767 180.737 17.9461 181.016 17.6548L181.015 17.6489ZM178.852 20.3018C178.19 20.3221 177.531 20.2068 176.916 19.9632C176.3 19.7195 175.741 19.3526 175.272 18.8849C174.798 18.3917 174.429 17.8075 174.187 17.1677C173.945 16.5278 173.835 15.8456 173.864 15.1621C173.861 14.1535 174.155 13.1664 174.71 12.3238C175.264 11.4812 176.054 10.8203 176.981 10.4234C177.908 10.0266 178.931 9.91133 179.923 10.0921C180.915 10.2727 181.832 10.7412 182.56 11.4393C183.051 11.9194 183.436 12.4977 183.69 13.1364C183.943 13.7751 184.059 14.46 184.03 15.1465C184.055 15.8382 183.933 16.5273 183.673 17.1689C183.414 17.8104 183.021 18.3898 182.522 18.8693C182.035 19.3388 181.461 19.7071 180.831 19.953C180.201 20.1989 179.528 20.3174 178.852 20.3018ZM186.063 20.1357V6.75111H188.128V20.1357H186.063Z" fill="#333333" /><path d="M192.12 6.8218H191.026V9.93735H190.542V6.8218H189.439V6.37767H192.12V6.8218ZM196 6.38286V9.93587H195.529V8.78811C195.529 8.33632 195.541 7.87737 195.566 7.41124L194.523 9.83726H194.073L193.045 7.43201C193.078 7.84227 193.095 8.29431 193.097 8.78811V9.93735H192.625V6.37841H193.08L194.306 9.29081L195.545 6.37619L196 6.38286Z" fill="#333333" /><path d="M186.562 31.1204C186.01 31.1204 185.562 30.938 185.216 30.5732C184.87 30.2089 184.697 29.7272 184.697 29.1282C184.697 28.5301 184.872 28.0508 185.222 27.6905C185.573 27.3257 186.027 27.1433 186.584 27.1433C187.144 27.1433 187.593 27.3163 187.93 27.6623C188.271 28.0034 188.442 28.4408 188.442 28.9747C188.442 29.1096 188.433 29.2357 188.414 29.3528H185.349C185.368 29.7359 185.487 30.0399 185.706 30.2648C185.931 30.4843 186.217 30.5942 186.562 30.5947C186.824 30.5947 187.044 30.5362 187.222 30.419C187.404 30.3019 187.537 30.1476 187.622 29.9563H188.309C188.188 30.2921 187.967 30.5832 187.677 30.7912C187.383 31.0107 187.011 31.1204 186.562 31.1204ZM187.776 28.8902C187.771 28.5204 187.654 28.2258 187.425 28.0063C187.196 27.7819 186.911 27.6697 186.57 27.6697C186.233 27.6697 185.952 27.7795 185.727 27.9989C185.503 28.2135 185.38 28.5105 185.356 28.8902H187.776ZM182.358 31.1204C181.806 31.1204 181.358 30.938 181.012 30.5732C180.666 30.2089 180.493 29.7295 180.493 29.1348C180.493 28.5367 180.666 28.0553 181.012 27.6905C181.358 27.3257 181.806 27.1433 182.358 27.1433C182.821 27.1433 183.198 27.2508 183.488 27.4658C183.782 27.6809 183.988 27.9873 184.105 28.3852H183.418C183.351 28.1719 183.215 27.9867 183.032 27.8588C182.85 27.7328 182.625 27.6697 182.358 27.6697C181.993 27.6697 181.699 27.7982 181.474 28.0553C181.255 28.3074 181.145 28.6675 181.145 29.1356C181.145 29.5982 181.255 29.9583 181.474 30.2159C181.699 30.4679 181.993 30.594 182.358 30.594C182.905 30.594 183.258 30.3557 183.418 29.8792H184.105C183.988 30.2574 183.78 30.5589 183.481 30.7838C183.186 31.0082 182.812 31.1204 182.358 31.1204ZM176.978 27.2063V27.8306C177.24 27.3724 177.659 27.1433 178.233 27.1433C178.659 27.1433 179.014 27.2859 179.3 27.5711C179.585 27.8519 179.727 28.254 179.727 28.7775V31.0574H179.097V28.8827C179.097 28.4952 179.003 28.2006 178.815 27.9989C178.629 27.7978 178.369 27.6972 178.037 27.6972C177.71 27.6972 177.451 27.7978 177.258 27.9989C177.072 28.2001 176.978 28.4947 176.978 28.8827V31.0574H176.34V27.2063H176.978ZM173.665 31.1204C173.113 31.1204 172.664 30.938 172.318 30.5732C171.972 30.2089 171.799 29.7272 171.799 29.1282C171.799 28.5301 171.974 28.0508 172.325 27.6905C172.676 27.3257 173.13 27.1433 173.686 27.1433C174.247 27.1433 174.696 27.3163 175.033 27.6623C175.374 28.0034 175.544 28.4408 175.545 28.9747C175.545 29.1096 175.536 29.2357 175.517 29.3528H172.452C172.47 29.7359 172.59 30.0399 172.809 30.2648C173.034 30.4843 173.319 30.5942 173.665 30.5947C173.927 30.5947 174.147 30.5362 174.325 30.419C174.506 30.3019 174.64 30.1476 174.724 29.9563H175.412C175.29 30.2921 175.07 30.5832 174.78 30.7912C174.486 31.0107 174.114 31.1204 173.665 31.1204ZM174.878 28.8902C174.873 28.5204 174.757 28.2258 174.528 28.0063C174.298 27.7819 174.013 27.6697 173.672 27.6697C173.335 27.6697 173.055 27.7795 172.83 27.9989C172.606 28.2135 172.482 28.5105 172.458 28.8902H174.878ZM170.993 26.4627C170.953 26.5038 170.905 26.5363 170.852 26.558C170.799 26.5797 170.742 26.5901 170.685 26.5887C170.628 26.5901 170.571 26.5797 170.518 26.5582C170.465 26.5367 170.417 26.5046 170.377 26.4638C170.336 26.423 170.305 26.3744 170.285 26.321C170.264 26.2677 170.255 26.2106 170.257 26.1535C170.257 26.0319 170.299 25.9291 170.383 25.845C170.422 25.8047 170.469 25.7727 170.521 25.751C170.573 25.7294 170.629 25.7185 170.685 25.719C170.806 25.719 170.909 25.761 170.993 25.845C171.077 25.9291 171.119 26.0319 171.119 26.1535C171.121 26.2108 171.11 26.2678 171.089 26.3209C171.067 26.374 171.034 26.4227 170.993 26.4627ZM170.362 31.0567V27.2056H171.001V31.0567H170.362ZM168.667 31.0581V25.868H169.306V31.0581H168.667ZM167.608 26.4627C167.568 26.5036 167.519 26.5358 167.466 26.5575C167.413 26.5792 167.356 26.5898 167.299 26.5887C167.241 26.5899 167.185 26.5793 167.132 26.5578C167.079 26.5362 167.031 26.5041 166.991 26.4633C166.951 26.4226 166.92 26.3741 166.899 26.3208C166.878 26.2675 166.869 26.2106 166.871 26.1535C166.871 26.0319 166.913 25.9291 166.998 25.845C167.036 25.8044 167.083 25.7723 167.135 25.7506C167.187 25.7289 167.242 25.7181 167.299 25.719C167.42 25.719 167.523 25.761 167.608 25.845C167.692 25.9291 167.734 26.0319 167.734 26.1535C167.735 26.2107 167.724 26.2676 167.702 26.3206C167.681 26.3737 167.649 26.4225 167.608 26.4627ZM166.976 31.0567V27.2056H167.614V31.0567H166.976ZM163.144 28.294C163.144 27.9713 163.278 27.6999 163.545 27.4799C163.816 27.2555 164.164 27.1433 164.589 27.1433C165.02 27.1433 165.357 27.2533 165.6 27.4732C165.843 27.6883 165.976 27.9851 166 28.3637H165.34C165.331 28.1487 165.258 27.9757 165.122 27.8447C164.987 27.7137 164.798 27.6482 164.555 27.6482C164.316 27.6482 164.126 27.7068 163.987 27.824C163.921 27.877 163.868 27.9444 163.833 28.0211C163.798 28.0977 163.78 28.1815 163.783 28.2659C163.783 28.4438 163.86 28.5817 164.015 28.6796C164.173 28.7775 164.362 28.8499 164.583 28.8968C164.807 28.9388 165.029 28.9902 165.249 29.051C165.473 29.1074 165.662 29.2174 165.817 29.381C165.976 29.5397 166.055 29.7688 166.055 30.0683C166.055 30.3629 165.92 30.613 165.649 30.8186C165.382 31.0198 165.034 31.1204 164.603 31.1204C163.958 31.1204 163.827 31.0129 163.565 30.7979C163.308 30.5829 163.168 30.286 163.144 29.9074H163.804C163.813 30.1224 163.888 30.2954 164.028 30.4264C164.173 30.5525 164.37 30.6155 164.618 30.6155C164.871 30.6155 165.067 30.5616 165.207 30.4539C165.352 30.3461 165.424 30.2107 165.424 30.0475C165.428 29.9725 165.415 29.8976 165.385 29.8283C165.356 29.759 165.312 29.6971 165.256 29.6472C165.135 29.5448 164.99 29.4749 164.835 29.444C164.667 29.4069 164.482 29.3625 164.281 29.3105C164.097 29.2623 163.915 29.2087 163.734 29.1497C163.567 29.0897 163.42 28.9826 163.313 28.8412C163.2 28.6959 163.144 28.5135 163.144 28.294ZM160.611 31.1204C160.059 31.1204 159.61 30.938 159.264 30.5732C158.918 30.2089 158.745 29.7272 158.745 29.1282C158.745 28.5301 158.921 28.0508 159.271 27.6905C159.622 27.3257 160.075 27.1433 160.632 27.1433C161.193 27.1433 161.642 27.3163 161.979 27.6623C162.32 28.0034 162.49 28.4408 162.49 28.9747C162.49 29.1096 162.481 29.2357 162.463 29.3528H159.397C159.416 29.7359 159.535 30.0399 159.755 30.2648C159.98 30.4843 160.265 30.5942 160.611 30.5947C160.873 30.5947 161.092 30.5362 161.27 30.419C161.447 30.3085 161.586 30.1473 161.67 29.9563H162.358C162.236 30.2921 162.016 30.5832 161.726 30.7912C161.432 31.0107 161.06 31.1204 160.611 31.1204ZM161.825 28.8902C161.82 28.5204 161.703 28.2258 161.474 28.0063C161.244 27.7819 160.959 27.6697 160.618 27.6697C160.281 27.6697 160 27.7795 159.776 27.9989C159.551 28.2135 159.428 28.5105 159.404 28.8902H161.825ZM155.305 29.0503V31.0567H154.667V26.1957H156.244C156.792 26.1957 157.208 26.3292 157.493 26.5961C157.783 26.8625 157.928 27.1898 157.928 27.5778C157.928 27.9614 157.823 28.2817 157.612 28.5387C157.402 28.7957 157.087 28.9571 156.666 29.0229L158.005 31.0567H157.205L155.929 29.051L155.305 29.0503ZM155.305 26.7081V28.5461H156.244C156.595 28.5461 156.855 28.4665 157.023 28.3074C157.196 28.1443 157.282 27.9174 157.282 27.6267C157.282 27.0143 156.936 26.7081 156.244 26.7081H155.305ZM150.873 31.0581V25.868H151.512V31.0581H150.873ZM148.791 30.1803C149.048 29.9183 149.176 29.5698 149.177 29.1348C149.177 28.6998 149.048 28.3516 148.791 28.0901C148.674 27.964 148.531 27.8639 148.373 27.7962C148.215 27.7285 148.044 27.6948 147.872 27.6972C147.516 27.6972 147.21 27.8235 146.953 28.076C146.701 28.3281 146.574 28.6719 146.574 29.1074C146.574 29.7606 146.703 29.8956 146.96 30.1662C147.217 30.438 147.523 30.574 147.879 30.574C148.234 30.574 148.538 30.443 148.791 30.181V30.1803ZM147.76 31.1204C147.236 31.1204 146.799 30.9333 146.448 30.5591C146.097 30.181 145.922 29.6993 145.922 29.1141C145.922 28.5254 146.095 28.0508 146.441 27.6905C146.792 27.3257 147.232 27.1433 147.76 27.1433C148.087 27.1433 148.372 27.2204 148.616 27.3746C148.863 27.5289 149.05 27.73 149.177 27.9782V27.2071H149.822V31.0574H149.177V30.2715C149.05 30.5246 148.855 30.7381 148.616 30.8891C148.372 31.0433 148.087 31.1204 147.76 31.1204ZM142.408 27.2063V27.8306C142.67 27.3724 143.088 27.1433 143.663 27.1433C144.088 27.1433 144.443 27.2859 144.729 27.5711C145.014 27.8519 145.157 28.254 145.157 28.7775V31.0574H144.526V28.8827C144.526 28.4952 144.432 28.2006 144.245 27.9989C144.058 27.7978 143.798 27.6972 143.466 27.6972C143.14 27.6972 142.88 27.7978 142.688 27.9989C142.501 28.2001 142.408 28.4947 142.408 28.8827V31.0574H141.769V27.2063H142.408ZM138.978 30.5947C139.337 30.5947 139.648 30.4662 139.91 30.2092C140.177 29.9467 140.31 29.5864 140.31 29.1282C140.31 28.4408 140.184 28.3126 139.931 28.0553C139.812 27.9304 139.668 27.8317 139.509 27.7653C139.35 27.6989 139.178 27.6664 139.006 27.6697C138.645 27.6697 138.344 27.7982 138.1 28.0553C137.857 28.3123 137.736 28.6724 137.736 29.1356C137.736 29.5938 137.853 29.9514 138.086 30.2084C138.32 30.4655 138.617 30.5947 138.978 30.5947ZM137.61 30.5732C137.259 30.2089 137.083 29.7295 137.083 29.1348C137.083 28.5417 137.268 28.0625 137.638 27.6972C138.007 27.3279 138.47 27.1433 139.027 27.1433C139.583 27.1433 140.046 27.3279 140.415 27.6972C140.785 28.062 140.969 28.5414 140.969 29.1356C140.969 29.7238 140.775 30.2033 140.387 30.574C140.004 30.9383 139.534 31.1204 138.978 31.1204C138.422 31.1204 137.966 30.938 137.61 30.5732ZM136.278 26.4627C136.238 26.5036 136.19 26.5358 136.136 26.5575C136.083 26.5792 136.026 26.5898 135.969 26.5887C135.912 26.5894 135.855 26.5786 135.803 26.5569C135.75 26.5352 135.702 26.503 135.662 26.4624C135.622 26.4217 135.591 26.3734 135.57 26.3203C135.549 26.2672 135.539 26.2105 135.541 26.1535C135.541 26.0319 135.583 25.9291 135.668 25.845C135.707 25.8044 135.753 25.7723 135.805 25.7506C135.857 25.7289 135.913 25.7181 135.969 25.719C136.09 25.719 136.193 25.761 136.278 25.845C136.363 25.9291 136.405 26.0319 136.404 26.1535C136.405 26.2107 136.394 26.2676 136.373 26.3206C136.351 26.3737 136.319 26.4225 136.278 26.4627ZM135.646 31.0567V27.2056H136.285V31.0567H135.646ZM134.414 30.5369H134.94V31.0559H134.295C133.626 31.0559 133.291 30.7262 133.291 30.0668V27.7239H132.793V27.2048H133.291V26.2506H133.937V27.2048H134.933V27.7239H133.937V30.0668C133.937 30.2398 133.972 30.3614 134.042 30.4316C134.112 30.5018 134.236 30.5369 134.414 30.5369ZM131.067 30.1803C131.324 29.9183 131.453 29.5698 131.453 29.1348C131.453 28.6998 131.324 28.3516 131.067 28.0901C130.95 27.964 130.808 27.8639 130.649 27.7962C130.491 27.7285 130.321 27.6948 130.148 27.6972C129.793 27.6972 129.486 27.8235 129.229 28.076C128.977 28.3281 128.851 28.6719 128.85 29.1074C128.849 29.7606 128.979 29.8956 129.236 30.1662C129.494 30.438 129.8 30.574 130.155 30.574C130.326 30.5759 130.496 30.5419 130.653 30.4742C130.81 30.4065 130.951 30.3066 131.067 30.181V30.1803ZM130.036 31.1204C129.512 31.1204 129.075 30.9333 128.724 30.5591C128.373 30.181 128.198 29.6993 128.198 29.1141C128.198 28.5254 128.371 28.0508 128.717 27.6905C129.068 27.3257 129.508 27.1433 130.036 27.1433C130.363 27.1433 130.648 27.2204 130.891 27.3746C131.14 27.5289 131.327 27.73 131.453 27.9782V27.2071H132.098V31.0574H131.453V30.2715C131.327 30.5245 131.14 30.7304 130.891 30.8891C130.648 31.0433 130.363 31.1204 130.036 31.1204ZM126.507 27.2063V27.8588C126.737 27.3818 127.127 27.1433 127.679 27.1433V27.8099H127.51C127.183 27.8099 126.933 27.8964 126.76 28.0694C126.592 28.2374 126.508 28.532 126.507 28.9532V31.0574H125.869V27.2063H126.507ZM123.194 31.1204C122.642 31.1204 122.194 30.938 121.848 30.5732C121.502 30.2089 121.329 29.7272 121.329 29.1282C121.329 28.5301 121.504 28.0508 121.855 27.6905C122.206 27.3257 122.659 27.1433 123.215 27.1433C123.777 27.1433 124.226 27.3163 124.562 27.6623C124.903 28.0034 125.074 28.4408 125.074 28.9747C125.074 29.1096 125.065 29.2357 125.047 29.3528H121.981C121.999 29.7359 122.119 30.0399 122.339 30.2648C122.563 30.4843 122.848 30.5942 123.194 30.5947C123.456 30.5947 123.676 30.5362 123.854 30.419C124.031 30.3085 124.17 30.1473 124.254 29.9563H124.941C124.82 30.2921 124.6 30.5832 124.31 30.7912C124.015 31.0107 123.644 31.1204 123.194 31.1204ZM124.408 28.8902C124.403 28.5204 124.286 28.2258 124.057 28.0063C123.828 27.7819 123.543 27.6697 123.202 27.6697C122.865 27.6697 122.584 27.7795 122.36 27.9989C122.135 28.2135 122.011 28.5105 121.988 28.8902H124.408ZM118.818 30.5732C119.178 30.5732 119.485 30.4398 119.737 30.1729C119.994 29.902 120.123 29.549 120.123 29.1141C120.123 28.6746 119.994 28.3286 119.737 28.076C119.617 27.954 119.474 27.8575 119.316 27.7925C119.158 27.7275 118.989 27.6953 118.818 27.6979C118.462 27.6979 118.156 27.8289 117.899 28.0909C117.647 28.3519 117.521 28.7001 117.52 29.1356C117.52 29.5656 117.646 29.9141 117.899 30.181C118.156 30.443 118.462 30.5732 118.818 30.5732ZM118.944 27.1433C119.472 27.1433 119.91 27.3257 120.256 27.6905C120.607 28.0508 120.782 28.5254 120.782 29.1141C120.782 29.6988 120.607 30.1808 120.256 30.5599C119.905 30.9336 119.468 31.1204 118.944 31.1204C118.617 31.1204 118.329 31.0433 118.081 30.8891C117.844 30.7354 117.651 30.5225 117.52 30.2715V32.8814H116.882V27.2063H117.52V27.9774C117.651 27.7303 117.838 27.5293 118.081 27.3746C118.341 27.2168 118.641 27.1366 118.944 27.1433ZM114.924 30.0097C115.266 29.6593 115.437 29.1964 115.437 28.621C115.437 28.0461 115.266 27.5857 114.924 27.2397C114.583 26.8887 114.146 26.7133 113.613 26.7133C113.084 26.7133 112.647 26.8887 112.301 27.2397C111.96 27.5857 111.789 28.0461 111.789 28.621C111.789 29.1964 111.962 29.6593 112.308 30.0097C112.654 30.3602 113.091 30.5357 113.62 30.5362C114.149 30.5362 114.583 30.3607 114.924 30.0097ZM113.62 31.1041C112.923 31.1041 112.336 30.8703 111.859 30.4027C111.382 29.9302 111.144 29.3363 111.144 28.621C111.144 27.9013 111.382 27.3099 111.859 26.8467C112.336 26.3791 112.923 26.1453 113.62 26.1453C114.317 26.1453 114.901 26.3791 115.374 26.8467C115.851 27.3094 116.089 27.9008 116.089 28.621C116.089 29.3417 115.851 29.9356 115.374 30.4027C114.901 30.8703 114.317 31.1041 113.62 31.1041Z" fill="#333333" /></svg>
        </a>
        <div class="collapse navbar-collapse justify-content-center" id="navbarSupportedContent">
            <ul class="navbar-nav gap-1">
                <li class="nav-item dropdown" id="DashboardNavbar">
                    <a class="nav-link Menu_Icon " aria-current="page" id="dashboard-link" asp-area="Dashboard" asp-controller="ServiceAvailability" asp-action="List">
                        <span class="icon cp-dashboard me-1"></span><span>Dashboard</span>
                    </a>
                  <ul class="dropdown-menu btnDropDown" id="dashboard-dropdownList">                     
                       <li class="nav-item">
                            <a asp-area="Dashboard" asp-controller="ServiceAvailability" asp-action="List" class="nav-link dropdown-item ">Service Availability</a>
                        </li>
                        <li class="nav-item"><a asp-area="Dashboard" asp-controller="ITResiliencyView" asp-action="List" class="nav-link dropdown-item ">IT Resiliency View</a></li>
                        <li class="nav-item"><a asp-area="Dashboard" asp-controller="Analytics" asp-action="Index" class="nav-link dropdown-item ">Operational Analytics</a></li>
                        <li class="nav-item"><a asp-area="Dashboard" asp-controller="ResiliencyMapping" asp-action="ResiliencyMapping" class="nav-link dropdown-item ">One View</a></li>
                      
                        <li class="nav-item dropend customDashboard d-none">
                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Custom Dashboard</a>
                            <ul class="dropdown-menu customDashboardDropdown">                              
                        </ul>
                        </li>
                        
                    </ul>
                </li>
                <input type="hidden" id="txtPermission" data-permission="@WebHelper.UserSession.Permissions"/>
                <input type="hidden" id="txtRole" data-role="@WebHelper.UserSession.RoleName"/>
                @if (@WebHelper.UserSession.RoleName == "Operator")
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon ">
                            <span class="icon cp-drift_dashboard me-1"></span><span>Drift</span>
                        </a>
                        
                    </li>
                }   
                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Drift.View)
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon drift" aria-current="page" id="dashboard-link" asp-area="Drift" asp-controller="DriftDashboard" asp-action="List">
                            <span class="icon cp-drift_dashboard me-1"></span><span>Drift</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item">
                                <a asp-area="Drift" asp-controller="DriftDashboard" asp-action="List" class="nav-link dropdown-item ">Drift Dashboard</a>
                            </li>
                            <li class="nav-item dropend disabled">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Drift Configuration</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li class="nav-item">
                                        <a asp-area="Drift" asp-controller="DriftParameter" asp-action="List" class="nav-link dropdown-item">Drift Parameter</a>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Drift" asp-controller="DriftProfile" asp-action="List" class="nav-link dropdown-item">Drift Profile</a>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Drift" asp-controller="DriftManagement" asp-action="List" class="nav-link dropdown-item">Drift Job Management</a>
                                    </li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link dropdown-item driftreport">Drift Report</a>
                            </li>
                        </ul>
                    </li>
                }
                else
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon">
                            <span class="icon cp-drift_dashboard me-1"></span><span>Drift</span>
                        </a>
                    </li>
                }
                @if (@WebHelper.UserSession.RoleName == "Operator")
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link icon-disabled Menu_Icon ">
                            <span class="icon cp-resiliency-readiness me-1"></span><span>Resiliency Readiness</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item">
                                <a class="nav-link dropdown-item disabled ">
                                    Resiliency Dashboard
                                </a>
                            </li>
                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Manage Resiliency Readiness</a></li>
                        </ul>
                    </li>
                }
                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.ResiliencyReadiness.View)
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon " aria-current="page" id="dashboard-link" asp-area="ResiliencyReadiness" asp-controller="ResiliencyDashboard" asp-action="List">
                            <span class="icon cp-resiliency-readiness me-1"></span><span>Resiliency Readiness</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="dashboard-dropdown">
                            <li class="nav-item">
                                <a asp-area="ResiliencyReadiness" asp-controller="ResiliencyDashboard" asp-action="List" class="nav-link dropdown-item">Resiliency Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a asp-area="ResiliencyReadiness" asp-controller="ManageResilienceReadiness" asp-action="List" class="nav-link dropdown-item">Manage Resiliency Readiness</a>
                            </li>
                        </ul>
                    </li>
                }
                else
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon">
                            <span class="icon cp-resiliency-readiness me-1"></span><span>Resiliency Readiness</span>
                        </a>
                    </li>
                }
                @if (@WebHelper.UserSession.RoleName == "Operator" )
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon disabled " id="">
                            <span class="icon cp-cyber-recovery me-1"></span><span>Cyber Resiliency</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="orchestration-dropdown">

                            <li class="nav-item dropend disabled">
                                <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Configuration</a>
                                <ul class="dropdown-menu btnDropDown">                                 
                                   @*  <li><a class="nav-link dropdown-item disabled ">Component</a></li>
                                    <li><a class="nav-link dropdown-item disabled ">Component Group</a></li>  *@
                                    <li><a class="nav-link dropdown-item disabled ">Airgap</a></li>
                                    <li><a class="nav-link dropdown-item disabled ">Manage</a></li>
                                    <li><a class="nav-link dropdown-item disabled ">Job Management</a></li>
                                    <li><a class="nav-link dropdown-item disabled ">Snap</a></li>
                                </ul>
                            </li>
                            
                          
                         @*    <li><a  class="nav-link dropdown-item disabled ">Alert</a></li>
                            <li><a class="nav-link dropdown-item disabled ">Report</a></li> *@
                            <li><a class="nav-link dropdown-item disabled ">CG Execution Report</a></li>
                        </ul>
                    </li>
                }
                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Cyber.View)
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon" asp-area="CyberResiliency" asp-controller="CyberResiliency" asp-action="Dashboard">
                            <span class="icon cp-cyber-recovery me-1"></span><span>Cyber Resiliency</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown">
                            <li><a asp-area="CyberResiliency" asp-controller="CyberResiliency" asp-action="Dashboard" class="nav-link dropdown-item">Dashboard</a></li>
                            <li class="nav-item dropend">
                                <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Configuration</a>
                                <ul class="dropdown-menu btnDropDown">
                                    <li><a asp-area="CyberResiliency" asp-controller="AirGap" asp-action="List" class="nav-link dropdown-item">Airgap</a></li>
                                    <li><a asp-area="CyberResiliency" asp-controller="Manage" asp-action="List" class="nav-link dropdown-item">Manage</a></li>
                                    <li><a asp-area="CyberResiliency" asp-controller="JobManagement" asp-action="List" class="nav-link dropdown-item">Job Management</a></li>
                                    <li><a asp-area="CyberResiliency" asp-controller="Snap" asp-action="List" class="nav-link dropdown-item">Snap</a></li>
                                </ul>
                            </li>
                            <li><a asp-area="CyberResiliency" asp-controller="CGExecutionReport" asp-action="List" class="nav-link dropdown-item">CG Execution Report</a></li>
                        </ul>
                    </li>
                }
                else
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon disabled">
                            <span class="icon cp-cyber-recovery me-1"></span><span>Cyber Resiliency</span>
                        </a>
                    </li>
                }
                @if (@WebHelper.UserSession.RoleName == "Operator")
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon" aria-current="page" id="orchestration-link" href="#">
                            <span class="icon cp-IT-automation me-1"></span><span>IT Automation</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="orchestration-dropdown">
                            <li class="nav-item">
                                <a class="nav-link dropdown-item disabled">Workflow Configuration</a>
                            </li>
                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Workflow Profile Management</a></li>
                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Workflow Execution</a></li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowList" asp-action="List" class="nav-link dropdown-item">Workflow List</a></li>
                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Workflow Templates</a></li>
                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">User Privileges</a></li>
                        </ul>
                    </li>
                }
                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.View)
                {
                    <li class="nav-item dropdown">
                        <a class="nav-link Menu_Icon" aria-current="page" id="orchestration-link" asp-area="ITAutomation" asp-controller="WorkflowConfiguration" asp-action="List">
                            <span class="icon cp-IT-automation me-1"></span><span>IT Automation</span>
                        </a>
                        <ul class="dropdown-menu btnDropDown" id="orchestration-dropdown">
                            <li class="nav-item">
                                <a asp-area="ITAutomation" asp-controller="WorkflowConfiguration" asp-action="List" class="nav-link dropdown-item">Workflow Configuration</a>
                            </li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowProfileManagement" asp-action="List" class="nav-link dropdown-item">Workflow Profile Management</a></li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowExecution" asp-action="List" class="nav-link dropdown-item">Workflow Execution</a></li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowList" asp-action="List" class="nav-link dropdown-item">Workflow List</a></li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowTemplate" asp-action="List" class="nav-link dropdown-item">Workflow Template</a></li>
                            <li class="nav-item"><a asp-area="ITAutomation" asp-controller="WorkflowScheduleExecutionHistory" asp-action="List" class="nav-link dropdown-item">Workflow Schedule Execution History</a></li>
                            @if (@WebHelper.UserSession.RoleName == "Administrator")
                            {
                                <li class="nav-item">
                                    <a class="nav-link icon-disabled Menu_Icon">
                                        <span class="me-1"></span><span>User Privileges</span>
                                    </a>
                                </li>
                            }
                            else
                            {
                                <li class="nav-item"><a asp-area="ITAutomation" asp-controller="UserPrivileges" asp-action="List" class="nav-link dropdown-item ">User Privileges</a></li>
                            }                           
                        </ul>
                    </li>
                }
                else
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon " aria-current="page" href="#">
                            <i class="icon cp-IT-automation me-1"></i><span>IT Automation</span>
                        </a>
                    </li>
                }
                @* @if (@WebHelper.UserSession.RoleName == "Operator" || @WebHelper.UserSession.RoleName == "Manager")
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon ">
                            <span class="icon cp-cloud-connect me-1"></span><span>Cloud Connect</span>
                        </a>
                    </li>
                }    *@              
                @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.CloudConnect.View)
                {
                    <li class="nav-item">
                        <a asp-area="Cloud" asp-controller="CloudConnect" asp-action="List" class="nav-link Menu_Icon">
                            <span class="icon cp-cloud-connect me-1"></span><span>Cloud Connect</span>
                        </a>
                    </li>
                }
                else
                {
                    <li class="nav-item">
                        <a class="nav-link icon-disabled Menu_Icon">
                            <span class="icon cp-cloud-connect me-1"></span><span>Cloud Connect</span>
                        </a>
                    </li>
                }
            </ul>
        </div>

        <ul class="navbar-nav align-items-center Active-none">
           
            <li class="nav-item dropdown">
                <a href="#" class="nav-link Menu_Icon" id="btnSetting" role="button" data-bs-toggle="dropdown" aria-expanded="false" aria-controls="menu">
                    <span class="icon cp-settings fw-semibold"></span>                  
                </a>
                <div class="dropdown-menu btnDropDown1 Mega-Menu end-0" style="width:45rem">
                    <ul class="list-group list-group-horizontal p-0">
                        <li class="list-group-item flex-fill p-2">
                            <ul class="ps-0 " style="list-style:none">
                                 @if (@WebHelper.UserSession.RoleName == "Operator")
                                {
                                <li class="Mega-Menu-header"><i class="cp-configure me-1"></i><span class="align-middle">Configure</span></li>

                                <li class="nav-item"><a asp-area="Configuration" asp-controller="Company" asp-action="List" class="nav-link dropdown-item ">Company</a></li>
                                <li class="nav-item dropend">
                                    <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Sites</a>
                                    <ul class="dropdown-menu btnDropDown">
                                        <li><a asp-area="Configuration" asp-controller="SiteLocation" asp-action="List" class="nav-link dropdown-item ">Site Location</a></li>
                                        <li><a asp-area="Configuration" asp-controller="SiteType" asp-action="List" class="nav-link dropdown-item ">Site Type</a></li>
                                        <li><a asp-area="Configuration" asp-controller="Site" asp-action="List" class="nav-link dropdown-item ">Site</a></li>
                                    </ul>
                                </li>                               
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="OperationalService" asp-action="List" class="nav-link dropdown-item ">Operational Service</a></li>
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="OperationalFunction" asp-action="List" class="nav-link dropdown-item ">Operational Function</a></li>
                                <li class="nav-item dropend">
                                    <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Infra Components</a>
                                    <ul class="dropdown-menu btnDropDown">
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="Server" asp-action="List" class="nav-link dropdown-item ">Server</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="Database" asp-action="List" class="nav-link dropdown-item ">Database</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="Replication" asp-action="List" class="nav-link dropdown-item ">Replication</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="SingleSignOn" asp-action="List" class="nav-link dropdown-item ">Single Sign-On</a></li>
                                        @* <li class="nav-item"><a asp-area="Configuration" asp-controller="Node" asp-action="List" class="nav-link dropdown-item ">Node</a></li> *@
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="VeritasCluster" asp-action="List" class="nav-link dropdown-item ">Veritas Cluster</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="HACMPCluster" asp-action="List" class="nav-link dropdown-item ">HACMP Cluster</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="DataSyncProperties" asp-action="List" class="nav-link dropdown-item ">DataSync Properties</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="RoboCopyOptions" asp-action="List" class="nav-link dropdown-item ">RoboCopy Options</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="RSyncOptions" asp-action="List" class="nav-link dropdown-item ">Rsync Options</a></li>
                                    </ul>
                                </li>
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="InfraObject" asp-action="List" class="nav-link dropdown-item ">InfraObject</a></li>
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkImport" asp-action="List" class="nav-link dropdown-item  ">Bulk Import</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkServerCredential" asp-action="List" class="nav-link dropdown-item ">Bulk Server Credential</a></li>
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkDatabaseCredential" asp-action="List" class="nav-link dropdown-item ">Bulk Database Credential</a></li>
                                <li class="nav-item"><a asp-area="Configuration" asp-controller="DRCalendar" asp-action="List" class="nav-link dropdown-item ">DR Calendar</a></li>
                                    
                                <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">FIA/BIA</a>
                                    <ul class="dropdown-menu btnDropDown">
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="FiaTemplates" asp-action="List" class="nav-link dropdown-item ">FIA Templates</a></li>                                       
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link dropdown-item border-0 ">BIA Rules</a></li>
                                    </ul>
                                </li>
                                <li class="nav-item  dropend">
                                    <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Incident</a>
                                    <ul class="dropdown-menu btnDropDown">
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="WhatifAnalysis" asp-action="List" class="nav-link dropdown-item ">What if Analysis</a></li>
                                        <li class="nav-item"><a asp-area="Configuration" asp-controller="IncidentDetails" asp-action="List" class="nav-link dropdown-item ">Incident Details</a></li>

                                    </ul>
                                </li>
                                }
                                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.View)
                                {
                                    <li class="Mega-Menu-header"><i class="cp-configure me-1"></i><span class="align-middle">Configure</span></li>

                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="Company" asp-action="List" class="nav-link dropdown-item ">Company</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle sites" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Sites</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Configuration" asp-controller="SiteLocation" asp-action="List" class="nav-link dropdown-item ">Site Location</a></li>
                                            <li><a asp-area="Configuration" asp-controller="SiteType" asp-action="List" class="nav-link dropdown-item ">Site Type</a></li>
                                            <li><a asp-area="Configuration" asp-controller="Site" asp-action="List" class="nav-link dropdown-item ">Site</a></li>
                                        </ul>
                                    </li>                                 
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="OperationalService" asp-action="List" class="nav-link dropdown-item ">Operational Service</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="OperationalFunction" asp-action="List" class="nav-link dropdown-item ">Operational Function</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Infra Components</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="Server" asp-action="List" class="nav-link dropdown-item ">Server</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="Database" asp-action="List" class="nav-link dropdown-item ">Database</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="Replication" asp-action="List" class="nav-link dropdown-item ">Replication</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="SingleSignOn" asp-action="List" class="nav-link dropdown-item ">Single Sign-On</a></li>
                                            @* <li class="nav-item"><a asp-area="Configuration" asp-controller="Node" asp-action="List" class="nav-link dropdown-item ">Node</a></li> *@
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="VeritasCluster" asp-action="List" class="nav-link dropdown-item ">Veritas Cluster</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="HACMPCluster" asp-action="List" class="nav-link dropdown-item ">HACMP Cluster</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="DataSyncProperties" asp-action="List" class="nav-link dropdown-item ">DataSync Properties</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="RoboCopyOptions" asp-action="List" class="nav-link dropdown-item ">RoboCopy Options</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="RSyncOptions" asp-action="List" class="nav-link dropdown-item ">Rsync Options</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="InfraObject" asp-action="List" class="nav-link dropdown-item ">InfraObject</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkImport" asp-action="List" class="nav-link dropdown-item ">Bulk Import</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkServerCredential" asp-action="List" class="nav-link dropdown-item ">Bulk Server Credential</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="BulkDatabaseCredential" asp-action="List" class="nav-link dropdown-item ">Bulk Database Credential</a></li>
                                    <li class="nav-item"><a asp-area="Configuration" asp-controller="DRCalendar" asp-action="List" class="nav-link dropdown-item ">DR Calendar</a></li>                                   
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">FIA/BIA</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="FiaTemplates" asp-action="List" class="nav-link dropdown-item ">FIA Templates</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="FIACost" asp-action="List" class="nav-link dropdown-item ">FIA Costs</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link dropdown-item border-0 ">BIA Rules</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Incident</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="WhatifAnalysis" asp-action="List" class="nav-link dropdown-item ">What if Analysis</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="IncidentDetails" asp-action="List" class="nav-link dropdown-item ">Incident Details</a></li>

                                        </ul>
                                    </li>
                                }                               
                                else
                                {
                                    <li class="Mega-Menu-header"><i class="cp-configure me-1"></i><span class="align-middle">Configure</span></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Company</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Sites</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Configuration" class="nav-link dropdown-item disabled ">Site</a></li>
                                            <li><a asp-area="Configuration" class="nav-link dropdown-item disabled ">Site Type</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Operational Service</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Operational Function</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Infra Components</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Server</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Database</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Replication</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Single Sign-On</a></li>
                                            @* <li class="nav-item"><a class="nav-link dropdown-item disabled ">Node</a></li> *@
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Veritas Cluster</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">HACMP Cluster</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">DataSync Properties</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">RoboCopy Options</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Rsync Options</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Infra Object</a></li>                                   
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Bulk Server Credential</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Bulk Database Credentials</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">DR Calendar</a></li>
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">FIA/BIA</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="FiaTemplates" asp-action="List" class="nav-link dropdown-item disabled ">FIA Templates</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="FTACosts" asp-action="List" class="nav-link dropdown-item disabled ">FIA Costs</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="BiaRules" asp-action="List" class="nav-link dropdown-item border-0 disabled ">BIA Rules</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item  dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Incident</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="WhatifAnalysis" asp-action="List" class="nav-link dropdown-item disabled ">What if Analysis</a></li>
                                            <li class="nav-item"><a asp-area="Configuration" asp-controller="IncidentDetails" asp-action="List" class="nav-link dropdown-item disabled ">Incident Details</a></li>

                                        </ul>
                                    </li>
                                }
                            </ul>
                        </li>
                        <li class="list-group-item flex-fill p-2">
                            <ul class="ps-0" style="list-style:none">
                                <li class="Mega-Menu-header"><i class="cp-manage me-1"></i><span class="align-middle">Manage</span></li>
                                @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.View)
                                {
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Job Management</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Manage" asp-controller="MonitoringJob" asp-action="List" class="nav-link dropdown-item ">Monitoring Job</a></li>
                                            <li><a asp-area="Manage" asp-controller="ReplicationJob" asp-action="List" class="nav-link dropdown-item ">Replication Job</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Dashboard Builder</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Manage" asp-controller="TileConfiguration" asp-action="List" class="nav-link dropdown-item ">Tile Configuration</a></li>
                                            <li><a asp-area="Manage" asp-controller="CustomDashboard" asp-action="List" class="nav-link dropdown-item ">Custom Dashboard</a></li>
                                            <li><a asp-area="Manage" asp-controller="UserMapping" asp-action="List" class="nav-link dropdown-item ">User Mapping</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Manage" asp-controller="NotificationManager" asp-action="List" class="nav-link dropdown-item ">Notification Manager</a>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Manage" asp-controller="ADPasswordExpire" asp-action="List" class="nav-link dropdown-item ">AD Password Expire</a>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Approval Matrix</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Manage" asp-controller="Approval" asp-action="List" class="nav-link dropdown-item ">Approvers</a></li>
                                            <li><a asp-area="Manage" asp-controller="Template" asp-action="List" class="nav-link dropdown-item ">Template</a></li>
                                            <li><a asp-area="Manage" asp-controller="ApprovalMatrix" asp-action="List" class="nav-link dropdown-item ">Request</a></li>                                           
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Manage" asp-controller="escalationmatrix" asp-action="List" class="nav-link dropdown-item">Escalation Matrix</a>
                                    </li>
                                 @*    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Escalation Matrix</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="configuration" asp-controller="TeamMaster" asp-action="List" class="nav-link dropdown-item ">Manage Team</a></li>
                                            <li><a asp-area="manage" asp-controller="escalationmatrix" asp-action="List" class="nav-link dropdown-item ">List</a></li>
                                        </ul>
                                    </li> *@
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">PreRequisites</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Manage" asp-controller="Parameter" asp-action="List" class="nav-link dropdown-item ">Parameter</a></li>
                                            <li><a asp-area="Manage" asp-controller="Profile" asp-action="List" class="nav-link dropdown-item ">Profile</a></li>
                                            <li><a asp-area="Manage" asp-controller="Scanner" asp-action="List" class="nav-link dropdown-item ">Scanner</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a asp-area="Manage" asp-controller="ManageOperationalService" asp-action="List" class="nav-link dropdown-item ">Manage Operational Service</a></li>
                                    <li class="nav-item"><a asp-area="Manage" asp-controller="MonitoringServices" asp-action="List" class="nav-link dropdown-item ">Monitoring Services</a></li>
                                }
                                else
                                {
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Job Management</a></li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled " href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">Dashboard Builder</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Manage" asp-controller="TileConfiguration" asp-action="List" class="nav-link dropdown-item disabled ">Tile Configuration</a></li>
                                            <li><a asp-area="Manage" asp-controller="CustomDashboard" asp-action="List" class="nav-link dropdown-item disabled ">Custom Dashboard</a></li>
                                            <li><a asp-area="Manage" asp-controller="UserMapping" asp-action="List" class="nav-link dropdown-item disabled ">User Mapping</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link dropdown-item disabled ">Notification Manager</a>
                                    </li>
                                    <li class="nav-item">
                                        <a asp-area="Manage" asp-controller="ADPasswordExpire" asp-action="List" class="nav-link dropdown-item disabled">AD Password Expire</a>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled ">Approval Matrix</a>
                                        <ul class="dropdown-menu btnDropDown disabled">
                                            <li><a asp-area="Manage" class="nav-link dropdown-item disabled ">Template</a></li>
                                            <li><a asp-area="Manage" class="nav-link dropdown-item disabled ">Request</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled ">Escalation Matrix</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a class="nav-link dropdown-item disabled ">Manage Team</a></li>
                                            <li><a class="nav-link dropdown-item disabled ">List</a></li>
                                        </ul>
                                    </li>

                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Manage Operational Service</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Monitoring Service List</a></li>
                                }
                            </ul>
                        </li>
                        <li class="list-group-item flex-fill p-2">
                            <ul class="ps-0" style="list-style:none">
                                <li class="Mega-Menu-header"><i class="cp-user me-1"></i><span class="align-middle">Admin</span></li>
                                @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.View)
                                {
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle " href="#" role="button" data-bs-toggle="dropdown"
                                           aria-expanded="false">
                                            Manager
                                        </a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Admin" asp-controller="AccessManager" asp-action="List" class="nav-link dropdown-item ">Access Manager</a></li>
                                            @* <li><a asp-area="Admin" asp-controller="LicenseManager" asp-action="List" class="nav-link dropdown-item">License Manager</a></li>                                             *@
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                                           aria-expanded="false">
                                            License
                                        </a>
                                        <ul class="dropdown-menu btnDropDown">

                                            <li><a asp-area="Admin" asp-controller="LicenseManager" asp-action="List" class="nav-link dropdown-item">License Manager</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                                           aria-expanded="false">
                                            User Management
                                        </a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Admin" asp-controller="User" asp-action="List" class="nav-link dropdown-item ">Users</a></li>
                                            <li><a asp-area="Admin" asp-controller="UserRole" asp-action="List" class="nav-link dropdown-item ">User Role</a></li>
                                            <li><a asp-area="Admin" asp-controller="UserGroup" asp-action="List" class="nav-link dropdown-item ">User Group</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                                           aria-expanded="false">
                                            Dataset
                                        </a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Admin" asp-controller="TableAccess" asp-action="List" class="nav-link dropdown-item ">Table Access</a></li>
                                            <li><a asp-area="Admin" asp-controller="Dataset" asp-action="List" class="nav-link dropdown-item">Configure Dataset</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="Archive" asp-action="List" class="nav-link dropdown-item ">Archive</a></li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="BackupData" asp-action="List" class="nav-link dropdown-item ">Backup Data</a></li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="Settings" asp-action="List" class="nav-link dropdown-item ">Settings</a></li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="GlobalSettings" asp-action="List" class="nav-link dropdown-item ">Global Settings</a></li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="LoadBalancer" asp-action="List" class="nav-link dropdown-item border-0 ">Load Balancer</a></li>
                                    <li class="nav-item"><a asp-area="Admin" asp-controller="GroupNodePolicy" asp-action="List" class="nav-link dropdown-item ">Group Node Policy</a></li>
                                }
                                else
                                {
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled manager">Manager</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a asp-area="Admin" class="nav-link dropdown-item disabled ">Access Manager</a></li>
                                            <li><a asp-area="Admin" class="nav-link dropdown-item disabled ">License Manager</a></li>
                                            <li><a asp-area="Admin" class="nav-link dropdown-item disabled ">State Monitoring</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled usermanagement">User Management</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a class="nav-link dropdown-item disabled ">Users</a></li>
                                            <li><a class="nav-link dropdown-item disabled ">User Role</a></li>
                                            <li><a class="nav-link dropdown-item disabled ">User Group</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item dropend ">
                                        <a class="nav-link dropdown-item dropdown-toggle disabled ">Dataset</a>
                                        <ul class="dropdown-menu btnDropDown">
                                            <li><a class="nav-link dropdown-item disabled ">Table Access</a></li>
                                            <li><a class="nav-link dropdown-item disabled ">Configure Dataset</a></li>
                                        </ul>
                                    </li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Archive</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Backup Data</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Settings</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Global Settings</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item border-0 disabled ">Load Balancer</a></li>
                                    <li class="nav-item"><a class="nav-link dropdown-item disabled ">Group Node Policy</a></li>

                                }
                            </ul>
                        </li>
                        <li class="list-group-item flex-fill p-2">
                            <ul class="list-group list-group-flush">
                                @if (WebHelper.UserSession.RoleName == "Operator" || WebHelper.UserSession.RoleName == "Manager")
                                {
                                    <li class="list-group-item p-0">
                                        <ul class="ps-0" style="list-style:none">
                                            <li class="Mega-Menu-header"><i class="cp-report me-1"></i><span class="align-middle">Report</span></li>

                                            <li class="nav-item"><a asp-area="Report" asp-controller="PreBuildReport" asp-action="List" class="nav-link dropdown-item ">Prebuild Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="CustomReport" asp-action="List" class="nav-link dropdown-item ">Custom Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="ReportScheduler" asp-action="List" class="nav-link dropdown-item border-0 ">Report Scheduler</a></li>
                                            <li class="nav-item d-none"><a asp-area="Report" asp-controller="CustomReport" asp-action="ReportDesignSpace" class="nav-link dropdown-item border-0"></a></li>
                                        </ul>
                                    </li>
                                }
                                else if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Reports.View)
                                {
                                    <li class="list-group-item p-0">
                                        <ul class="ps-0" style="list-style:none">
                                            <li class="Mega-Menu-header"><i class="cp-report me-1"></i><span class="align-middle">Report</span></li>

                                            <li class="nav-item"><a asp-area="Report" asp-controller="PreBuildReport" asp-action="List" class="nav-link dropdown-item ">Prebuild Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="CustomReport" asp-action="List" class="nav-link dropdown-item ">Custom Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="ReportScheduler" asp-action="List" class="nav-link dropdown-item border-0 ">Report Scheduler</a></li>
                                            <li class="nav-item d-none"><a asp-area="Report" asp-controller="CustomReport" asp-action="ReportDesignSpace" class="nav-link dropdown-item border-0"></a></li>

                                        </ul>
                                    </li>
                                }
                                else
                                {
                                    <li class="list-group-item p-0">
                                        <ul class="ps-0" style="list-style:none">
                                            <li class="Mega-Menu-header"><i class="cp-report me-1"></i><span class="align-middle">Report</span></li>

                                            <li class="nav-item"><a asp-area="Report" asp-controller="PreBuildReport" asp-action="List" class="nav-link dropdown-item disabled ">Prebuild Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="CustomReport" asp-action="List" class="nav-link dropdown-item disabled ">Custom Reports</a></li>
                                            <li class="nav-item"><a asp-area="Report" asp-controller="ReportScheduler" asp-action="List" class="nav-link dropdown-item border-0 disabled ">Report Scheduler</a></li>

                                        </ul>
                                    </li>
                                }
                                <li class="list-group-item px-0" style="margin-top:-1px;" id="alert-dropdown">
                                    <ul class="ps-0" style="list-style:none">
                                        <li class="Mega-Menu-header"><i class="cp-alerts-head me-1"></i><span class="align-middle">Alert</span></li>
                                        @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Alerts.View)
                                        {
                                            <li class="nav-item"><a asp-area="Alert" asp-controller="AlertDashboard" asp-action="List" class="nav-link dropdown-item ">Alert Dashboard</a></li>
                                            <li class="nav-item"><a asp-area="Alert" asp-controller="ManageAlert" asp-action="List" class="nav-link dropdown-item ">Manage Alert</a></li>                                           
                                        }
                                        else
                                        {
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Alert Dashboard</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled ">Manage Alert</a></li>
                                        }
                                    </ul>
                                </li>
                                <li class="list-group-item px-0" style="margin-top:-1px;" id="serverlog-dropdown">
                                    <ul class="ps-0" style="list-style:none">
                                        <li class="Mega-Menu-header"><i class="cp-server me-1"></i><span class="align-middle">Server Logs</span></li>
                                        @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.View)
                                        {
                                            <li class="nav-item"><a asp-area="configuration" asp-controller="ServerLog" asp-action="List" class="nav-link dropdown-item">Server Log</a></li>
                                            <li class="nav-item"><a asp-area="configuration" asp-controller="ServerLogHistory" asp-action="List" class="nav-link dropdown-item">Server Log History</a></li>
                                        }
                                        else
                                        {
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled serverlog">Server Log</a></li>
                                            <li class="nav-item"><a class="nav-link dropdown-item disabled serverloghistory">Sever Log History</a></li>
                                        }
                                    </ul>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link Menu_Icon d-none" aria-current="page" id="alertpage" role="button" title="alert_icon">
                    <span class="translate-middle badge blink alertcount"></span>
                    <i class="cp-fail-back alerticon"></i>
                </a>
            </li>
            <li class="nav-item dropdown">
                <a class="nav-link Menu_Icon d-flex align-items-center py-0" aria-current="page" href="#">
                    <img src="/img/input_Icons/user-profile.svg" class="me-2 rounded-circle" id="userProfileImage" width="20px" height="20px" />
                    @* <i class="cp-user-profile me-1"></i> *@
                    <div class="d-grid" style="width:120px; color:var(--bs-nav-link-color)">
                        <span id="userLoginName" class="align-middle text-truncate d-inline-block" userid="@WebHelper.UserSession.LoggedUserId" style="max-width:125px">@WebHelper.UserSession.LoginName</span>
                        <small id="time" class="align-middle" style="font-size:11px;"></small>
                    </div>
                </a>
                <ul class="dropdown-menu btnDropDown end-0">
                    <li class="nav-item"><a asp-controller="Account" asp-action="UserProfile" asp-area="" class="nav-link dropdown-item ">Profile</a></li>
                    <li class="nav-item"><a asp-controller="Account" asp-action="PatchList" asp-area="" class="nav-link dropdown-item ">Patch List</a></li>
                    @if (WebHelper.UserSession.AuthenticationType != AuthenticationType.AD.ToString())
                    {
                        <li class="nav-item"><a asp-controller="User" asp-action="ChangePassword" asp-area="Admin" class="nav-link dropdown-item ">Change Password</a></li>
                    }
                    else
                    {
                        <li class="nav-item"><a asp-controller="User" asp-action="ChangePassword" asp-area="Admin" class="nav-link dropdown-item disabled ">Change Password</a></li>
                    }
                    <li class="nav-item"><a asp-controller="Account" asp-action="About" asp-area="" class="nav-link dropdown-item ">About</a></li>
                    <li class="nav-item"><a asp-controller="User" asp-action="Lock" asp-area="Admin" class="nav-link dropdown-item ">Lock</a></li>
                    <li class="nav-item"><a class="nav-link dropdown-item help" aria-current="page" href="~/pdf/Help.pdf" target="_blank" title="User manual">Help</a></li>
                    <li class="nav-item"><a asp-controller="Account" asp-action="Logout" asp-area="" class="nav-link dropdown-item ">Logout</a></li>
                </ul>
            </li>
           
            <li class="nav-item dropdown">
                <img src="~/img/logo/pts_logo.png" height="24" width="125" alt="Customer Logo" title="Customer Logo" />
            </li>
           
        </ul>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
    <div>
        <span id="LoginId" style="display:none;">@WebHelper.UserSession.LoggedUserId</span>
        <span class="align-middle" id="userRole" style="display:none;">  @WebHelper.UserSession.RoleId</span>
        <span id="sessionExpirationTime" style="display:none;">@WebHelper.UserSession.Expires</span>

    </div>
</nav>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
@* <script src="~/js/common/lock.js"></script> *@
@* <script src="~/js/navbarPartial.js"></script> *@
<script src="~/js/common/navbarpartial.js"></script>