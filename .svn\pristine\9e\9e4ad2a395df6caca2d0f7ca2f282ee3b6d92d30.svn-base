using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AdPasswordJobFixture : IDisposable
{
    public List<AdPasswordJob> AdPasswordJobPaginationList { get; set; }
    public List<AdPasswordJob> AdPasswordJobList { get; set; }
    public AdPasswordJob AdPasswordJobDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public AdPasswordJobFixture()
    {
        var fixture = new Fixture();

        AdPasswordJobList = fixture.Create<List<AdPasswordJob>>();

        AdPasswordJobPaginationList = fixture.CreateMany<AdPasswordJob>(20).ToList();


        AdPasswordJobDto = fixture.Create<AdPasswordJob>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
