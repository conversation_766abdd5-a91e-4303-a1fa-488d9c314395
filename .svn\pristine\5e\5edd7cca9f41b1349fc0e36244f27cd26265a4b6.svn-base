﻿@model ContinuityPatrol.Domain.ViewModels.ReportScheduleModel.ReportScheduleViewModel
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Shared.Services.Helper
@Html.AntiForgeryToken()

@{
    ViewData["Title"] = "List";
}
<style>
    #reportScheduleTable th {
        white-space: nowrap; 
    }

    #reportScheduleTable_wrapper .d-flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }
    #reportScheduleTable_wrapper .left-section {
        text-align: left;
        margin-right: auto;
    }
    #reportScheduleTable_wrapper .center-section {
        text-align: center;
        flex: 1;
    }
    #reportScheduleTable_wrapper .right-section {
        text-align: right;
        margin-left: auto;
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
           @*  <div id="outer">
                <button id="inner">Click Me</button>
            </div> *@
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-scheduled-report"></i>
                        <span>Scheduled Report</span>
                    </h6>
                </div>
                <form class="d-flex align-items-center">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                        <div class="input-group-text">
                           @*  <div class="dropdown">
                                <span data-bs-toggle="dropdown" title="Filter" aria-expanded="false">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input " type="checkbox" value="reportName=" id="Namecheckbox">
                                            <label class="form-check-label" for="Namecheckbox">
                                                Report Name
                                            </label>
                                        </div>
                                        <div>
                                            <input class="form-check-input " type="checkbox" value="reportType=" id="Typecheckbox">
                                            <label class="form-check-label" for="Typecheckbox">
                                                Report Type
                                            </label>
                                        </div>

                                    </li>

                                </ul>
                            </div> *@
                            <div class="dropdown" title="Filter">
                                <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li><h6 class="dropdown-header">Filter Search</h6></li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="reportName=" id="Namecheckbox">
                                            <label class="form-check-label" for="Namecheckbox">
                                                Report Name
                                            </label>
                                        </div>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="reportType=" id="Typecheckbox">
                                            <label class="form-check-label" for="Typecheckbox">
                                                Report Type
                                            </label>
                                        </div>
                                    </li>
                                   
                                </ul>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="scheduleButton" data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Create
                    </button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">

            <table class="table table-hover dataTable" style="width:100%" id="reportSchedule">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Report Name</th>
                        <th>Report Type</th>
                        <th>Users/User Group</th>
                        <th>Scheduled Date/Time</th>
                        @* <th title="Schedule On">Schedule On</th>
                        <th title="Scheduled By">Scheduled By</th>*@
                        <th>Action</th>

                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>


        </div>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-scheduled-report"></i><span>Scheduled Report Configuration</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <div class="form-group">
                    <label class="form-label">
                        Report Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-prebuild-reports"></i></span>
                        <input id="reportName" type="text" class="form-control form-clear" name="reportName" onchange="handleOnChange(event)" placeholder="Enter Report Name" maxlength="100" autocomplete="off">
                    </div>
                    <span id="reportNameError"></span>
                </div>

                <div class="form-group">
                    <label class="form-label">
                        Report Type
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-prebuild-reports"></i></span>
                        <select class="form-select-modal form-clear" id="reportType" name="reportType" aria-label="Default select example" data-live-search="true" data-placeholder="Select Report"
                                onchange="handleOnChange(event)">
                            <option value="" disabled selected>Select Report</option>
                            <option value="RPO SLA Report" typeId="rpo" disableDiv='["infraDiv", "rpoDateDiv"]'>RPO SLA Report</option>
                            <option value="Datalag Status Report" typeId="datalag" disableDiv='[]'>Datalag Status Report</option>
                            @* <option value="DataSync Monitoring Report" typeId="datasync" disableDiv='["infraDiv", "jobDiv"]'>DataSync Monitoring Report</option> *@
                            <option value="InfraObject Summary" typeId="infraobject" disableDiv='["operationalServiceDiv"]'>InfraObject Summary</option>
                            <option value="Resiliency Readiness Execution Log Report" typeId="execution" disableDiv='["operationalServiceDiv", "rpoDateDiv"]'>Resiliency Readiness Execution Log Report</option>
                            <option value="Resilience Readiness Report" typeId="drready" disableDiv='["operationalServiceDiv"]'>Resilience Readiness Report</option>
                            <option value="Snap Report" typeId="snap" disableDiv='["snapDiv", "rpoDateDiv"]'>Snap Report</option>   
                            <option value="Infra Object Scheduler Log Report" typeId="infralog" disableDiv='["infralogdiv"]'>InfraObject Scheduler Log Report</option>
                            <option value="AirGap Report" typeId="airgap" disableDiv='["airgapid","airGapStartDate","airGapEndDate","airgapiddiv","rpoDateDiv"]'>AirGap Report</option>
                        </select>
                    </div>
                    <span id="reportTypeError"></span>
                </div>

                <div class="form-group d-none" id="operationalServiceDiv">
                    <label class="form-label" title="Operational Service Name">
                        Operational Service
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-business-service"></i></span>
                        <select id="operationService" class="form-select-modal form-clear" name="operationServiceId" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select Operational Service" onchange="handleOnChange(event, 'report_properties', 'operationServiceName')">
                            <option value="" disabled selected></option>
                            @foreach (var business in Model.BusinessServices)
                            {
                                <option value="@business.Value">@business.Text</option>
                            }
                        </select>
                    </div>
                    <span id="operationServiceError"></span>
                </div>
                <div class="row d-none" id="infralogdiv">
                    <div id="infralogStartDate" class="form-group-lg col-6" style="height: 55px;width: 191px;">
                        <div id="startDate" class="input-group w-auto ">
                            <span class="input-group-text form-label mb-0 pe-1">Start Date</span>
                            <input id="infralogStart" class="form-clear" name="infralogStart" type="date" placeholder="Select Date" style="border:none;margin-left:5px;margin-top:-2px;" min="2011-01-01" onkeydown="return false" onchange="handleOnChange(event, 'report_properties', 'startdate')" />
                        </div>
                        <span id="infralogStartError" style="width: 21%;">
                        </span>
                    </div>
                    <div id="infralogEndDate" class="form-group-lg col-6" style="height: 55px;width: 191px;">
                        <div id="endDate" class="input-group w-auto">
                            <span class="input-group-text form-label mb-0 pe-1">End Date</span>
                            <input id="infralogEnd" class="form-clear" type="date" name="infralogEnd" placeholder="Select Date" style="border:none;margin-left:5px;margin-top:-2px;" min="2011-01-01" onkeydown="return false" onchange="handleOnChange(event, 'report_properties', 'enddate')" />
                        </div>
                        <span id="infralogEndError" style="width: 21%;">
                        </span>
                    </div>    
                </div> 
            
                <div class="form-group d-none" id="airgapid">
                    <label class="form-label" title="AirGap Name">
                        AirGap Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-air-gap"></i></span>
                        <select class="form-select-modal form-clear" id="airgaplist" name="airgapids" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select AirGap Name" onchange="handleOnChange(event, 'report_properties', 'airGapName')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="airgapidError"></span>
                </div>

                <div class="form-group d-none" id="operationalFunctionDiv">
                    <label class="form-label" title="Operational Function Name">
                        Operational Function
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-job-management"></i></span>
                        <select class="form-select-modal form-clear" id="operationFunction" name="operationFunctionId" aria-label="Default select example" data-live-search="true" data-placeholder="Select Operational Function"
                                onchange="handleOnChange(event, 'report_properties', 'operationFunctionName')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="operationFunctionError"></span>
                </div>

                <div class="form-group d-none" id="infraDiv">
                    <label class="form-label" title="InfraObject Name">
                        InfraObject Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-infra-object"></i></span>
                        <select class="form-select-modal form-clear" id="infraList" name="infraObjectId" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select InfraObject Name" onchange="handleOnChange(event, 'report_properties', 'infraObjectName')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="infraListError"></span>
                </div>

                <div class="form-group d-none" id="snapDiv">
                    <label class="form-label" title="SnapTag Name">
                        SnapTag Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-snap-1"></i></span>
                        <select class="form-select-modal form-clear" id="snapList" name="snapId" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select SnapTag Name" onchange="handleOnChange(event, 'report_properties', 'snapName')">
                            <option value="" disabled selected></option>
                            <option value="all">All</option>
                            <option value="bad">Bad</option>
                            <option value="good">Good</option>
                        </select>
                    </div>
                    <span id="snapListError"></span>
                </div>

                <div class="form-group d-none" id="jobDiv">
                    <label class="form-label" title="Job Name">
                        Job Name
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-job-management"></i></span>
                        <select class="form-select-modal form-clear" id="job" name="jobId" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select Job Name" onchange="handleOnChange(event, 'report_properties', 'jobName')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="jobError"></span>
                </div>

                <div class="row row-cols-2 d-none" id="rpoDateDiv">
                    <div class="col">
                        <div class="form-group">
                            <div class="form-label">Report Cycle</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-job-management"></i></span>
                                <select class="form-select-modal form-clear" id="date_format" name="dateFormat" aria-label="Default select example" data-live-search="true"
                                        data-placeholder="Select Report Cycle" onchange="handleOnChange(event, 'report_properties')">
                                    <option value="" disabled selected></option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                            <span id="date_formatError"></span>
                        </div>

                    </div>
                    <div class="col">
                        <div class="form-group">
                            <div class="form-label">Report Duration</div>
                            <div class="input-group">
                                <span class="cp-calendar"><i></i></span>
                                <select class="form-select-modal form-clear" id="date" name="date" aria-label="Default select example" data-live-search="true"
                                        data-placeholder="Select Report Duration" onchange="handleOnChange(event, 'report_properties')">
                                    <option value="" disabled selected></option>
                                </select>
                            </div>
                            <span id="dateError"></span>
                        </div>

                    </div>
                    @*                    <div class="col">
                    <div class="form-group ">
                    <div class="form-label">Select Date</div>
                    <div class="input-group">
                    <span class="input-group-text"><i class="cp-job-management"></i></span>
                    <input type="date" class="form-control form-clear" id="date" name="date" onchange="handleOnChange(event, 'report_properties')" />
                    </div>
                    <span id="date-error"></span>
                    </div>
                    </div>*@

                </div>

                @* <div class="form-group d-none" id="startDiv">
                <label class="form-label" title="Start Date">
                Start Date
                </label>
                <div class="input-group">
                <span class="input-group-text"><i class="cp-job-management"></i></span>
                <input type="date" class="form-control form-clear" id="fromDate" name="fromDate" onchange="handleOnChange(event)" />
                </div>
                <span id="fromDate-error"></span>
                </div>

                <div class="form-group d-none" id="endDiv">
                <label class="form-label" title="End Date">
                End Date
                </label>
                <div class="input-group">
                <span class="input-group-text"><i class="cp-job-management"></i></span>
                <input type="date" class="form-control form-clear" id="toDate" name="toDate" onchange="handleOnChange(event)" />
                </div>
                <span id="toDate-error"></span>
                </div>
                *@
                <div class="form-group form-clear" id="userCategoryDiv">
                    <div class="gap-2 d-flex ">
                        <div class="form-label me-3">User Category</div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="user_group" value="user_group" onchange="handleOnChange(event)">
                            <label class="form-check-label" for="user_group">User Group</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="user" value="user" onchange="handleOnChange(event)">
                            <label class="form-check-label" for="user">User</label>
                        </div>
                    </div>
                    <span id="userCategoryDivError"></span>
                </div>

                <div class="form-group d-none" id="userGroupDiv">
                    <label class="form-label">
                        User Group
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-teams"></i></span>
                        <select class="form-select-modal form-clear" id="user_groupList" name="userGroup" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select User Group" multiple onchange="handleOnChange(event, 'user_properties')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="user_groupListError"></span>
                </div>

                <div class="form-group d-none" id="userDiv">
                    <label class="form-label">
                        User List
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-user"></i></span>
                        <select class="form-select-modal form-clear" id="users_list" name="user" aria-label="Default select example" data-live-search="true"
                                data-placeholder="Select User List" multiple onchange="handleOnChange(event, 'user_properties')">
                            <option value="" disabled selected></option>

                        </select>
                    </div>
                    <span id="users_listError"></span>
                </div>

                <input id="CompanyId" type="hidden" value="@WebHelper.UserSession.CompanyId" />

                <div class="form-group">
                    <div class="row mt-2 w-100 year">
                        <div class="year" id="yeargroup">
                            <div class="mb-3">
                                <div class="form-label">Scheduler</div>
                                <div>
                                    <nav>
                                        <div class="nav nav-tabs" id="nav-tab" role="tablist">

                                            <button class="nav-link active" id="nav-Hourly-tab" data-bs-toggle="tab" name="hourly"
                                                    data-bs-target="#nav-Hourly" type="button" role="tab"
                                                    aria-controls="nav-Hourly" aria-selected="false">
                                                Hourly
                                            </button>
                                            <button class="nav-link" id="nav-Daily-tab" data-bs-toggle="tab" name="daily"
                                                    data-bs-target="#nav-Daily" type="button" role="tab"
                                                    aria-controls="nav-Daily" aria-selected="false">
                                                Daily
                                            </button>
                                            <button class="nav-link" id="nav-Weekly-tab" data-bs-toggle="tab" name="weekly"
                                                    data-bs-target="#nav-Weekly" type="button" role="tab"
                                                    aria-controls="nav-Weekly" aria-selected="false">
                                                Weekly
                                            </button>
                                            <button class="nav-link" id="nav-Monthly-tab" data-bs-toggle="tab" name="monthly"
                                                    data-bs-target="#nav-Monthly" type="button" role="tab"
                                                    aria-controls="nav-Monthly" aria-selected="false">
                                                Monthly
                                            </button>
                                        </div>
                                    </nav>
                                    <div class="tab-content" id="nav-tabContent">

                                        @* <div class="tab-pane show active" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                            <div class="row mt-2">

                                                <div class="col-4" style="margin-top: 4px;">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("hourly", null, new { id = "hourly", type = "time", min = "0", max = "23", @class = "form-control validateInput", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off", errorId = "CronHourly" })
                                                        </div>
                                                        <span id="CronHourly-error"></span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div> *@
                                        <div class="tab-pane  show active" id="nav-Hourly" role="tabpanel" aria-labelledby="nav-Hourly-tab" tabindex="0">
                                            <div class="row mt-2">
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <div class="form-label">Every</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("txtHours", null, new { id = "txtHours", type = "number", min = "0", max = "23", @class = "form-control", @placeholder = "Enter Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false",maxlength = "2", @autocomplete = "off", errorId = "CronHourly" })
                                                            <span class="input-group-text fs-8 ms-1">
                                                                hrs
                                                            </span>
                                                        </div>
                                                        <span  id="CronHourlyError"></span>
                                                    </div>
                                                </div>
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <div class="form-label">Minutes</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("txtMinutes", null, new { id = "txtMinutes", type = "number", min = "0", max = "59", @class = "form-control", @placeholder = "Enter Min(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false",maxlength='2', @autocomplete = "off", errorId = "CronHourMin" })
                                                            <span class="input-group-text form-label mb-0 text-secondary">mins</span>
                                                        </div>
                                                        <span  id="CronHourMinError"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="nav-Daily" role="tabpanel" aria-labelledby="nav-Daily-tab" tabindex="0">
                                            <div class="row mt-2">
                                                <div class="col-4">
                                                    <div class="form-group flex-fill ">
                                                        <label class="animation-label form-label">
                                                            Select Day Type
                                                        </label>
                                                        <div class="" style="margin-top: 12px;">
                                                            <div class="form-check form-check-inline">
                                                                <input name="daysevery" aria-label="Every Day" type="radio" id="defaultCheck-everyday" class="form-check-input custom-cursor-default-hover validateInput"
                                                                       value="everyday" cursorshover="true" errorId="Crondaysevery">
                                                                <label title="" for="defaultCheck-everyday" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Day</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="daysevery" aria-label="Every Week Day" type="radio" id="defaultCheck-MON-FRI" class="form-check-input custom-cursor-default-hover validateInput"
                                                                       value="MON-FRI" errorId="Crondaysevery">
                                                                <label title="" for="defaultCheck-MON-FRI" class="form-check-label custom-cursor-default-hover" cursorshover="true">Every Week Day</label>
                                                            </div>
                                                        </div>
                                                        <span id="CrondayseveryError"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4" style="margin-top: 4px;">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("everyHours", null, new { id = "everyHours", type = "time", min = "0", max = "23", @class = "form-control validateInput", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off", errorId = "CroneveryHour" })
                                                        </div>
                                                        <span id="CroneveryHourError"></span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div class="tab-pane fade" id="nav-Weekly" role="tabpanel" aria-labelledby="nav-Weekly-tab" tabindex="0">
                                            <div class="row row-cols-2 mt-2">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="form-label custom-cursor-default-hover">Select Day(s)</label>
                                                        <div class="bg-transparent input-group">
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Monday" type="checkbox" id="defaultCheck-1" class="form-check-input" value="Mon"><label for="defaultCheck-1" class="form-check-label custom-cursor-default-hover">Monday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Tuesday" type="checkbox" id="defaultCheck-2" class="form-check-input" value="Tue"><label for="defaultCheck-2" class="form-check-label">Tuesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Wednesday" type="checkbox" id="defaultCheck-3" class="form-check-input" value="Wed"><label for="defaultCheck-3" class="form-check-label" cursorshover="true">Wednesday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Thursday" type="checkbox" id="defaultCheck-4" class="form-check-input" value="Thu"><label for="defaultCheck-4" class="form-check-label custom-cursor-default-hover" cursorshover="true">Thursday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Friday" type="checkbox" id="defaultCheck-5" class="form-check-input" value="Fri" cursorshover="true"><label for="defaultCheck-5" class="form-check-label">Friday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Saturday" type="checkbox" id="defaultCheck-6" class="form-check-input" value="Sat"><label for="defaultCheck-6" class="form-check-label">Saturday</label>
                                                            </div>
                                                            <div class="form-check form-check-inline">
                                                                <input name="weekDays" aria-label="Sunday" type="checkbox" id="defaultCheck-0" class="form-check-input" value="Sun"><label for="defaultCheck-0" class="form-check-label">Sunday</label>
                                                            </div>
                                                        </div>
                                                        <span id="CronDayError"></span>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text"><i class="cp-calendar"></i></span>
                                                            @Html.TextBox("ddlHours", null, new { id = "ddlHours", type = "time", min = "0", max = "23", @class = "form-control validateInput", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off", errorId = "CronddlHour" })
                                                        </div>
                                                        <span id="CronddlHourError"></span>
                                                    </div>
                                                </div>

                                            </div>

                                        </div>

                                        <div class="tab-pane fade" id="nav-Monthly" role="tabpanel" aria-labelledby="nav-Monthly-tab" tabindex="0">
                                            <div class="row row-cols-2 mt-2">
                                                <div class="col-4">
                                                    <div class="mb-3 form-group">
                                                        <div class="form-label">Select Month and Year</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            <input name="month" autocomplete="off" type="month"
                                                                   id="lblMonth"
                                                                   class="form-control custom-cursor-default-hover validateInput"
                                                                   cursorshover="true" min="2024-02" max="2101-02" errorId="CronMonth" onkeydown="return false" />
                                                        </div>
                                                        <span id="CronMonthError"></span>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="mb-3 form-group text-justify " style="display: inline-table;">
                                                        <div class="form-label mb-1">Select Date(s)</div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox1" value="1">
                                                            <label class="form-check-label" for="inlineCheckbox1">1</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox2" value="2">
                                                            <label class="form-check-label" for="inlineCheckbox2">2</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox3" value="3">
                                                            <label class="form-check-label" for="inlineCheckbox3">3</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox4" value="4">
                                                            <label class="form-check-label" for="inlineCheckbox4">4</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox5" value="5">
                                                            <label class="form-check-label" for="inlineCheckbox5">5</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox6" value="6">
                                                            <label class="form-check-label" for="inlineCheckbox6">6</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox7" value="7">
                                                            <label class="form-check-label" for="inlineCheckbox7">7</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox8" value="8">
                                                            <label class="form-check-label" for="inlineCheckbox8">8</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox9" value="9">
                                                            <label class="form-check-label" for="inlineCheckbox9">9</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox10" value="10">
                                                            <label class="form-check-label" for="inlineCheckbox10">10</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox11" value="11">
                                                            <label class="form-check-label" for="inlineCheckbox11">11</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox12" value="12">
                                                            <label class="form-check-label" for="inlineCheckbox12">12</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox13" value="13">
                                                            <label class="form-check-label" for="inlineCheckbox13">13</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox14" value="14">
                                                            <label class="form-check-label" for="inlineCheckbox14">14</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox15" value="15">
                                                            <label class="form-check-label" for="inlineCheckbox15">15</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox16" value="16">
                                                            <label class="form-check-label" for="inlineCheckbox16">16</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox17" value="17">
                                                            <label class="form-check-label" for="inlineCheckbox17">17</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox18" value="18">
                                                            <label class="form-check-label" for="inlineCheckbox18">18</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox19" value="19">
                                                            <label class="form-check-label" for="inlineCheckbox19">19</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox20" value="20">
                                                            <label class="form-check-label" for="inlineCheckbox20">20</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox21" value="21">
                                                            <label class="form-check-label" for="inlineCheckbox21">21</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox22" value="22">
                                                            <label class="form-check-label" for="inlineCheckbox22">22</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox23" value="23">
                                                            <label class="form-check-label" for="inlineCheckbox23">23</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox24" value="24">
                                                            <label class="form-check-label" for="inlineCheckbox24">24</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox25" value="25">
                                                            <label class="form-check-label" for="inlineCheckbox25">25</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox26" value="26">
                                                            <label class="form-check-label" for="inlineCheckbox26">26</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox27" value="27">
                                                            <label class="form-check-label" for="inlineCheckbox27">27</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox28" value="28">
                                                            <label class="form-check-label" for="inlineCheckbox28">28</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox29" value="29">
                                                            <label class="form-check-label" for="inlineCheckbox29">29</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox30" value="30">
                                                            <label class="form-check-label" for="inlineCheckbox30">30</label>
                                                        </div>
                                                        <div class="form-check form-check-inline" style="width: 30px;">
                                                            <input name="Monthyday" class="form-check-input" type="checkbox" id="inlineCheckbox31" value="31">
                                                            <label class="form-check-label" for="inlineCheckbox31">31</label>
                                                        </div>
                                                        <div class="form-group"> <span id="CronMonthlyDayError"></span></div>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <div class="form-label">Starts at</div>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="cp-calendar"></i>
                                                            </span>
                                                            @Html.TextBox("monthlyHours", null, new { id = "monthlyHours", type = "time", min = "0", max = "23", @class = "form-control validateInput", @placeholder = "Hour(s)", ondragstart = "return false;", ondrop = "return false", onkeydown = "return true;", onpaste = "return false", @autocomplete = "off", errorId = "CronMonthHrs" })
                                                        </div>
                                                        <span id="CronMonthHrsError"></span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>

            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                    <button type="button" id="btnSave" class="btn btn-primary btn-sm">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="reportCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Reports.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="reportDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Reports.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!-- Modal -->
<div class="modal fade" id="ReportScedulerModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-scheduled-report"></i><span>Report Execution Details</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table id="reportScheduleTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Sr. No.</th>
                            <th>Report Name</th>
                            <th>Report Type</th>
                            <th style="width:70px;">Users/User Group</th>
                            <th>Executed Date/Time</th>
                        </tr>
                    </thead>
                    <tbody id="reportScheduleDetails">
                        <tr id="reportScheduleNodata">
                            <td colspan="5" class="text-center">No Data Found</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            @*  <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
            <button type="button" id="btnSave" class="btn btn-primary btn-sm">Save</button>
            </div>
            </div> *@
        </div>
    </div>
</div>


<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script src="~/js/Report/ReportScheduler/ReportSceduler.js"></script>
<script src="~/js/Report/ReportScheduler/CronExpression.js"></script>

