using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetList;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class FiaImpactCategoryController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<FiaImpactCategoryListVm>>> GetFiaImpactCategorys()
    {
        Logger.LogDebug("Get All FiaImpactCategory");

        return Ok(await Mediator.Send(new GetFiaImpactCategoryListQuery()));
    }

    [HttpGet("{id}", Name = "GetFiaImpactCategory")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<FiaImpactCategoryDetailVm>> GetFiaImpactCategoryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FiaImpactCategory Id");

        Logger.LogDebug($"Get FiaImpactCategory Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetFiaImpactCategoryDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Configuration.View)]
 public async Task<ActionResult<PaginatedResult<FiaImpactCategoryListVm>>> GetPaginatedFiaImpactCategorys([FromQuery] GetFiaImpactCategoryPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in FiaImpactCategory Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateFiaImpactCategoryResponse>> CreateFiaImpactCategory([FromBody] CreateFiaImpactCategoryCommand createFiaImpactCategoryCommand)
    {
        Logger.LogDebug($"Create FiaImpactCategory '{createFiaImpactCategoryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateFiaImpactCategory), await Mediator.Send(createFiaImpactCategoryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateFiaImpactCategoryResponse>> UpdateFiaImpactCategory([FromBody] UpdateFiaImpactCategoryCommand updateFiaImpactCategoryCommand)
    {
        Logger.LogDebug($"Update FiaImpactCategory '{updateFiaImpactCategoryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateFiaImpactCategoryCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteFiaImpactCategoryResponse>> DeleteFiaImpactCategory(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "FiaImpactCategory Id");

        Logger.LogDebug($"Delete FiaImpactCategory Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteFiaImpactCategoryCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsFiaImpactCategoryNameExist(string fiaImpactCategoryName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(fiaImpactCategoryName, "FiaImpactCategory Name");

     Logger.LogDebug($"Check Name Exists Detail by FiaImpactCategory Name '{fiaImpactCategoryName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetFiaImpactCategoryNameUniqueQuery { Name = fiaImpactCategoryName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


