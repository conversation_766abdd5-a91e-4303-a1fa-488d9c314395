﻿using System.Globalization;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Events.PaginatedView;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class LicenseManagerController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _provider;
    private readonly ILogger<LicenseManagerController> _logger;
    private readonly IMapper _mapper;

    public LicenseManagerController(IPublisher publisher, IDataProvider provider, IMapper mapper, ILogger<LicenseManagerController> logger)
    {
        _publisher = publisher;
        _mapper = mapper;
        _provider = provider;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in LicenseManager");

        await _publisher.Publish(new LicenseManagerPaginatedEvent());
        var licenseDetails = await _provider.LicenseManager.LicenseManagerDetailView();
        var licenseInfoDetails = await _provider.LicenseInfo.GetLicenseInfo();
        var companyNames = await _provider.Company.GetCompanies();

        var removeParentCompany = companyNames.Where(x => !x.IsParent).ToList();
        var licenseMangerPoVm = await _provider.LicenseManager.GetAllPoNumbers();

        foreach (var lic in licenseMangerPoVm)
        {
            var childLicense = await _provider.LicenseManager.GetLicenseByParentIdAndParentPoNumber(lic.CompanyId, lic.PoNumber);

            removeParentCompany.RemoveAll(com => childLicense.Any(child => child.CompanyId.Equals(com.Id)));
        }

        var companies = _mapper.Map<List<SelectListItem>>(removeParentCompany);

        var licenseManager = new BaseLicenseViewModel
        {
            LicenseManagerDetails = licenseDetails,
            LicenseInfoLists = licenseInfoDetails,
            Companies = companies
        };

        return View(licenseManager);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(BaseLicenseViewModel baseLicense)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in LicenseManager");

        try
        {
            string licenseId = Request.Form["id"];

            var licenseKey = SecurityHelper.Decrypt(baseLicense.LicenseKey);
            _logger.LogDebug("License key decrypted successfully in LicenseManager.");

            var poNumber = licenseKey.Split("*");

            var licenses = await _provider.LicenseManager.GetLicenseDetailsByPONumber(poNumber[0]);

            if (licenses != null)
            {
                if (!poNumber[11].ToLower().Contains("tool"))
                {
                    _logger.LogWarning("LicenseKey rejected: 'Tool' key not found in PO number.");

                    return Json(new { success = false, message = "Invalid license key" });
                }
                licenseId = licenses.Id;
                baseLicense.Id = licenseId;
                _logger.LogDebug($"Existing license found for Id: {licenseId}");
            }

            if (licenseId.IsNullOrWhiteSpace())
            {
                if (!poNumber[11].ToLower().Contains("tool"))
                {
                    _logger.LogWarning("LicenseKey rejected: 'Tool' key not found in PO number.");

                    return Json(new { success = false, message = "Invalid license key" });
                }

                var licenseModel = _mapper.Map<CreateBaseLicenseCommand>(baseLicense);
                var result = await _provider.LicenseManager.CreateBaseLicense(licenseModel);
                _logger.LogDebug($"Creating Parent License '{licenseModel.LicenseKey}'");

                return Json(new { success = true, message = result.Message });
            }
            else
            {
                var licenseModel = _mapper.Map<UpdateBaseLicenseCommand>(baseLicense);
                var result = await _provider.LicenseManager.UpdateBaseLicense(licenseModel);
                _logger.LogDebug($"Updating Parent License '{licenseModel.LicenseKey}'");

                return Json(new { success = true, message = result.Message });
            }
        }
        catch (Exception ex)
        {
         
            _logger.Exception("Error occurred while processing the request for create or update.", ex);

            return ex.GetJsonException();
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<IActionResult> BaseLicenseCreateOrUpdate(BaseLicenseViewModel license)
    {
        _logger.LogDebug("Entering BaseLicenseCreateOrUpdate method in LicenseManager");
        try
        {
            if (license.Id.IsNullOrWhiteSpace())
            {
                var licenseModel = _mapper.Map<CreateBaseLicenseCommand>(license);
                var result = await _provider.LicenseManager.CreateBaseLicense(licenseModel);
                _logger.LogDebug($"Creating Base License '{licenseModel.LicenseKey}'");
                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var licenseModel = _mapper.Map<UpdateBaseLicenseCommand>(license);

                var result = await _provider.LicenseManager.UpdateBaseLicense(licenseModel);
                _logger.LogDebug($"Updating Base License '{licenseModel.LicenseKey}'");
                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("BaseLicenseCreateOrUpdate operation completed successfully in LicenseManager, returning view.");
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            _logger.LogError($"Validation error on license manager page in base license create or update: {ex.ValidationErrors.FirstOrDefault()}");

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {

            if (ex.Message.Contains("Index was outside the bounds of the array") || ex.Message.Contains("valid Base-64"))
            {
                TempData.NotifyWarning("Invalid LicenseKey");

                _logger.Exception("An error occurred on license manager page while processing the request for base license create or update", ex);
            }
            else
            {
                TempData.NotifyWarning(ex.Message);

                _logger.Exception("An error occurred on license manager page while processing the request for base license create or update", ex);
            }
            return RedirectToAction("List");
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> DerivedLicenseCreateOrUpdate(DerivedLicenseViewModel derivedLicense)
    {
        _logger.LogDebug("Entering DerivedLicenseCreateOrUpdate method in LicenseManager");

        var id = Request.Form["id"].ToString();
        try
        {
        
            if (id.IsNullOrWhiteSpace())
            {
               var derivedLicenseModel = _mapper.Map<CreateDerivedLicenseCommand>(derivedLicense);

                var result = await _provider.LicenseManager.CreateDerivedLicense(derivedLicenseModel);
                _logger.LogDebug($"Creating Derived License '{derivedLicense.LicenseKey}'");
                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var derivedLicenseModel = _mapper.Map<UpdateDerivedLicenseCommand>(derivedLicense);
                var result = await _provider.LicenseManager.UpdateDerivedLicense(derivedLicenseModel);
                _logger.LogDebug($"Updating Derived License '{derivedLicense.LicenseKey}'");
                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("DerivedLicenseCreateOrUpdate operation completed successfully in LicenseManager, returning view.");
            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            _logger.LogError($"Validation error on license manager page: {ex.ValidationErrors.FirstOrDefault()}");

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.Message);

            _logger.Exception("An error occurred on license manager page while processing the request for derived license create or update.", ex);

            return RedirectToAction("List");
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in LicenseManager");

        try
        {
            var response = await _provider.LicenseManager.DeleteAsync(id);

            TempData.NotifySuccess(response.Message);
            _logger.LogDebug("Successfully deleted base license record in LicenseManager");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting base license record on license manager.", ex);

            return RedirectToAction("List");
        }
    }

    public async Task<IActionResult> DeleteDerived(string id)
    {
        _logger.LogDebug("Entering DeleteDerived method in LicenseManager");

        try
        {
            var response = await _provider.LicenseManager.DeleteDerivedLicense(id);

            TempData.NotifySuccess(response.Message);
            _logger.LogDebug("Successfully deleted derived license record in LicenseManager");
            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting derived license record on license manager.", ex);

            return RedirectToAction("List");
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public ActionResult LoadPartialView(string name)
    {
        _logger.LogDebug("Entering LoadPartialView method in LicenseManager");

        try
        {
            var licenseDetails = _provider.LicenseManager.LicenseManagerDetailView().Result;

            _logger.LogDebug("Successfully retrieved license details");

            licenseDetails.ForEach(x =>
            {
                int id = 0;

                x.Id += id;
            });

            var licenseManager = new BaseLicenseViewModel
            {
                LicenseManagerDetails = licenseDetails
            };

            _logger.LogDebug("License details successfully processed and partial model prepared.");

            return PartialView(name, licenseManager);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while LoadPartialView method", ex);
            return RedirectToAction("");
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetLicenseManagerByPoNumber(string poNumber)
    {
        _logger.LogDebug("Entering GetLicenseManagerByPoNumber method in LicenseManager");

        try
        {
            var licenseData = await _provider.LicenseManager.GetLicenseManagerByPoNumber(poNumber);
            _logger.LogDebug($"Successfully retrieved license details by PoNumber '{poNumber}' in LicenseManager");
            return Json(licenseData);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on license manager page while retrieving the license details by PONumber '{poNumber}'.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetList()
    {
        _logger.LogDebug("Entering GetList method in LicenseManager");

        try
        {
            var licenseDetails = await _provider.LicenseManager.LicenseManagerDetailView();
            _logger.LogDebug("Successfully retrieved license list in LicenseManager");
            return Json(licenseDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the license list.", ex);

            return ex.GetJsonException();
        }

    }
    [HttpGet]
    public async Task<IActionResult> GetLicenseManagerCount()
    {
        _logger.LogDebug("Entering GetLicenseManagerCount method in LicenseManager");

        try
        {
            var licenseCount = await _provider.LicenseManager.GetLicenseManagerCount();
            _logger.LogDebug("Successfully retrieved license count in LicenseManager");
            return Json(licenseCount);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the license count.", ex);

            return ex.GetJsonException();
        }

    }
    //DB, Server, Replication.js
    public async Task<IActionResult> GetLicensesNames()
    {
        _logger.LogDebug("Entering GetLicensesNames method in LicenseManager");

        try
        {
            var licenseNames = await _provider.LicenseManager.GetAllPoNumbers();
            _logger.LogDebug("Successfully retrieved license names in LicenseManager");
            return Json(new { success = true, data = licenseNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the license names.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    //DB, Server, Replication.js
    public async Task<IActionResult> GetLicensesNamesWithCount(string type, string roleType, string siteId, string serverId, string replicationType, string databaseTypeId)
    {
        _logger.LogDebug("Entering GetLicensesNames method in LicenseManager");

        try
        {
            var licenseNames = await _provider.LicenseManager.GetPoNumber(type, roleType, siteId, serverId, replicationType, databaseTypeId);
            _logger.LogDebug("Successfully retrieved license names in LicenseManager");
            return Json(new { success = true, data = licenseNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the license names.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetExpiredLicensesList()
    {
        _logger.LogDebug("Entering GetExpiredLicensesList method in LicenseManager");

        try
        {
            var licenseNames = await _provider.LicenseManager.GetAllPoNumbers();

            var names = new List<LicenseManagerNameVm>();

            foreach (var license in licenseNames)
            {
              var isLicense =  DateTime.TryParseExact(license.ExpiryDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate) && expiryDate >= DateTime.UtcNow.Date
                    ? true
                    : false;

              if (!isLicense)
              {
                  names.Add(license);
              }

            }

            _logger.LogDebug("Successfully retrieved expired licenses names in LicenseManager");
            return Json(new { success = true, data = names });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the expired licenses.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }



    [HttpGet]
    public async Task<IActionResult> GetListById(string id)
    {
        _logger.LogDebug("Entering GetListById method in LicenseManager");

        try
        {
            var licenseDetails = await _provider.LicenseManager.GetLicenseManagerById(id);
            _logger.LogDebug($"Successfully retrieved license details by Id '{id}' in LicenseManager");
            return Json(licenseDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the license details by id.", ex);

            return ex.GetJsonException();
        }

    }
    [HttpGet]
    public async Task<IActionResult> GetEntityList(string licenseId, string entity, string entityType)
    {
        _logger.LogDebug("Entering GetEntityList method in LicenseManager");

        try
        {
            var licenseEntity = await _provider.LicenseInfo.GetLicenseInfoByEntity(licenseId, entity, entityType);
            _logger.LogDebug($"Successfully retrieved entity list by id '{licenseId}', entity '{entity}' and '{entityType}' in LicenseManager");
            return Json(licenseEntity);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the entity list by id, entity and entityType.", ex);

            return ex.GetJsonException();
        }

    }
    [HttpGet]
    public async Task<IActionResult> GetTypeList(string licenseId, string type, string entityType)
    {
        _logger.LogDebug("Entering GetTypeList method in LicenseManager");

        try
        {
            var licenseType = await _provider.LicenseInfo.GetLicenseInfoByType(licenseId, type, entityType);
            _logger.LogDebug($"Successfully retrieved license info by id '{licenseId}', type '{type}' and '{entityType}' in LicenseManager");

            return Json(licenseType);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving license info by licenseId, type and entityType.", ex);

            return ex.GetJsonException();
        }

    }
    //public async Task<IActionResult> ValidateLicenseExpiry()
    //{
    //    try
    //    {
    //        var expiry = await _provider.LicenseManager.ValidateLicenseExpiry();

    //        return Json(expiry);
    //    }
    //    catch(Exception ex) {
    //        _logger.LogInformation($"An error occurred on license key while processing the request.'{ex.Message}'");

    //        return ex.GetJsonException();   
    //    }

    //}

    [HttpGet]
    public async Task<IActionResult> GetDerivedList(string parentId, string parentPoNumber)
    {
        _logger.LogDebug("Entering GetDerivedList method in LicenseManager");

        try
        {
            var derivedDetails = await _provider.LicenseManager.GetLicenseByParentIdAndParentPoNumber(parentId, parentPoNumber);
            _logger.LogDebug($"Successfully retrieved derived license list by parentLicenseId '{parentId}' and parentPoNumber '{parentPoNumber}' in LicenseManager");
            return Json(derivedDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving derived license list by parentLicenseId and parentPoNumber.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetDecommissionList(string licenseId, string entityId, string entityType)
    {
        _logger.LogDebug("Entering GetDecommissionList method in LicenseManager");

        try
        {
            var decommissionDetails = await _provider.LicenseManager.GetDecommissionByIdAndEntityId(licenseId, entityId, entityType);
            _logger.LogDebug($"Successfully retrieved decommission list by licenseId '{licenseId}', entityId '{entityId}' and entityType '{entityType}' in LicenseManager");
            return Json(decommissionDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving decommission list by licenseId, entityId and entityType", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> DeleteDecommission(string entityId, string licenseId, string entityType, string entityName)
    {
        _logger.LogDebug("Entering DeleteDecommission method in LicenseManager");

        try
        {
            var deleteDecommission = await _provider.LicenseManager.DeleteDecommissionByEntityId(entityId, licenseId, entityType,entityName);
            _logger.LogDebug($"Successfully deleted decommission by licenseId '{licenseId}', entityId '{entityId}' and entityType '{entityType}' and entityName '{entityName}' on LicenseManager");
            return Json(deleteDecommission);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while deleting decommission", ex);

            return ex.GetJsonException();
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> DecommissionAuthentication(UserLockCommand command)
    {
        _logger.LogDebug("Entering DecommissionAuthentication method in LicenseManager");

        try
        {
            command.AuthenticationType = LoggedInAuthenticationType;

            var encrypt = SecurityHelper.Encrypt($"{command.UserName.ToLower()}{command.Password}");
            _logger.LogDebug($"Successfully encrypted '{command.UserName}' and '{command.Password}'");
            command.Password = encrypt;

            var response = await _provider.User.UsersAuthentication(command);
            _logger.LogDebug("Successfully authenticate decommission in LicenseManager");
            return Json(response);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while processing the decommission authentication", ex);
            return Json("");
        }
    }
    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> ReplaceLicenseDetails([FromBody] LicenseReplaceCommand licenseReplaceCommand)
    {
        _logger.LogDebug("Entering ReplaceLicenseDetails method in LicenseManager");

        try
        {
            var replace = await _provider.LicenseManager.ReplaceLicenseDetails(licenseReplaceCommand);
            _logger.LogDebug("Successfully replaced license details in LicenseManager");
            return Json(new { success = true, data = replace });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while replacing the license details", ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    public async Task<IActionResult> GetLicenseExpiresByCompanyId(string companyId)
    {
        _logger.LogDebug("Entering GetLicenseExpiresByCompanyId method in LicenseManager");

        try
        {
            var licenseExpired = await _provider.LicenseManager.GetLicenseExpiresByCompanyId(companyId);
            _logger.LogDebug($"Successfully retrieved expired license list by companyId '{companyId}' in LicenseManager");
            return Json(licenseExpired);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving the expired license list by companyId", ex);

            return ex.GetJsonException();
        }

    }
    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> UpdateLicenseState([FromBody] UpdateLicenseStateCommand updateLicenseStateCommand)
    {
        _logger.LogDebug("Entering UpdateLicenseState method in LicenseManager");

        try
        {
            var state = await _provider.LicenseManager.UpdateLicenseState(updateLicenseStateCommand);
            _logger.LogDebug($"Updating License State '{state}', LicenseId '{updateLicenseStateCommand.Id}' in LicenseManager");
            return Json(new { Success = true, data = state });
            //return Json(state);

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while updating license state.", ex);
            return ex.GetJsonException();
        }

    }
    public IActionResult LicenseLanding()
    {
        return View();
    }

    public IActionResult LicenseExpiredLanding()
    {
        return View();
    }

    [HttpGet]
    public async Task<IActionResult> GetLicenseGeneratorKeyById(string id, string type, string? renewalType)
    {
        _logger.LogDebug("Entering GetLicenseGeneratorKeyById method in LicenseManager");

        try
        {
            var licenseDetails = await _provider.LicenseManager.GetLicenseManagerById(id);

            if (licenseDetails == null)
            {
                _logger.LogDebug($"License not found for id '{id}' in LicenseManager");

                return Json(new { success = false, message = "License not found" });
            }

            var decryptedKey = SecurityHelper.Decrypt(licenseDetails.LicenseKey);

            var license = await SplitLicenseKey(decryptedKey);

            license.LicenseAction = renewalType ?? type;
            license.Request = $"request-for-{type}-UI";
            license.CompanyName = licenseDetails.CompanyName;

            var licenseText = license.PoNumber +
                              "*" + license.CpHostName +
                              "*" + license.IpAddress +
                              "*" + license.MacAddress +
                              "*" + license.LicenseCount +
                              "*" + license.LicenseType +
                              "*" + license.RandomString +
                              "*" + license.LicenseGeneratorDate +
                              "*" + license.LicenseActionType +
                              "*" + license.LicenseAction +
                              "*" + SecurityHelper.Decrypt(licenseDetails.AmcPlan) +//SupportPlan
                              "*" + license.Request +
                              "*" + license.CompanyName;

            if (license.AuthenticationId.IsNotNullOrWhiteSpace())
            {
                licenseText += "*" + license.AuthenticationId;
            }

            var encryptLicenseKey = SecurityHelper.Encrypt(licenseText);

            _logger.LogDebug($"Successfully decrypted license key for license id '{licenseDetails.Id}' in LicenseManager");

            //var keyParts = decryptedKey.Split('*');

            //if (keyParts.Length > 9)
            //{
            //    keyParts[9] = renewalType ?? type;
            //}
            //else
            //{
            //    var keyPartsList = keyParts.ToList();

            //    keyPartsList.Add(renewalType);

            //    keyParts = keyPartsList.ToArray();
            //}

            //if (keyParts.Length > 12)
            //{
            //    var keyPartsList = keyParts.ToList();
            //    keyPartsList.RemoveAt(12);
            //    keyPartsList.RemoveAt(11);
            //    keyParts = keyPartsList.ToArray();
            //}

            //var joinLicenseKey = string.Join("*", keyParts);

            //var updatedLicenseKey = string.Join("*", joinLicenseKey
            //   .Split('*')
            //   .Append($"request-for-{type}-UI").Append(licenseDetails.CompanyName));

            //var encryptLicenseKey = SecurityHelper.Encrypt(updatedLicenseKey);

            _logger.LogInformation($"Successfully generated license key by PoNumber '{SecurityHelper.Decrypt(licenseDetails.PoNumber)}' in LicenseManager");

            return Json(new { success = true, data = encryptLicenseKey });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on license manager page while retrieving or generating license key by id.", ex);

            return ex.GetJsonException();
        }

    }


    private Task<LicenseDto> SplitLicenseKey(string licenseKey)
    {
        var decryptLicenseKey = SecurityHelper.Decrypt(licenseKey);

        var splitLicenseKey = decryptLicenseKey.Split('*');

        var licenseDto = _mapper.Map<LicenseDto>(splitLicenseKey);

        return Task.FromResult(licenseDto);
    }

}