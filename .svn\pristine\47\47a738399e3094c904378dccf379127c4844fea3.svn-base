using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class ApprovalMatrixUsersService : BaseClient, IApprovalMatrixUsersService
{
    public ApprovalMatrixUsersService(IConfiguration config, IAppCache cache, ILogger<ApprovalMatrixUsersService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<ApprovalMatrixUsersListVm>> GetApprovalMatrixUsersList()
    {
        var request = new RestRequest("api/v6/approvalmatrixusers");

        return await GetFromCache<List<ApprovalMatrixUsersListVm>>(request, "GetApprovalMatrixUsersList");
    }

    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixUsersCommand createApprovalMatrixUsersCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixusers", Method.Post);

        request.AddJsonBody(createApprovalMatrixUsersCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixUsersCommand updateApprovalMatrixUsersCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrixusers", Method.Put);

        request.AddJsonBody(updateApprovalMatrixUsersCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixusers/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<ApprovalMatrixUsersDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrixusers/{id}");

        return await Get<ApprovalMatrixUsersDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsApprovalMatrixUsersNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/approvalmatrixusers/name-exist?approvalmatrixusersName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<ApprovalMatrixUsersListVm>> GetPaginatedApprovalMatrixUsers(GetApprovalMatrixUsersPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/approvalmatrixusers/paginated-list");

      return await Get<PaginatedResult<ApprovalMatrixUsersListVm>>(request);
  }
   #endregion
}
