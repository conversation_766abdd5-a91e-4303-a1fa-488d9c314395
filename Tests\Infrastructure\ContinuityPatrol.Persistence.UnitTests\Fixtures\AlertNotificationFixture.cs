using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertNotificationFixture : IDisposable
{
    public List<AlertNotification> AlertNotificationPaginationList { get; set; }
    public List<AlertNotification> AlertNotificationList { get; set; }
    public AlertNotification AlertNotificationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AlertNotificationFixture()
    {
        var fixture = new Fixture();

        AlertNotificationList = fixture.Create<List<AlertNotification>>();

        AlertNotificationPaginationList = fixture.CreateMany<AlertNotification>(20).ToList();

        AlertNotificationPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertNotificationPaginationList.ForEach(x => x.IsActive = true);

        AlertNotificationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertNotificationList.ForEach(x => x.IsActive = true);

        AlertNotificationDto = fixture.Create<AlertNotification>();
        AlertNotificationDto.ReferenceId = Guid.NewGuid().ToString();
        AlertNotificationDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
