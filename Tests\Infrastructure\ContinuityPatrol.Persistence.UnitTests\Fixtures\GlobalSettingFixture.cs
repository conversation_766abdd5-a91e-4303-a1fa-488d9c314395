using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class GlobalSettingFixture : IDisposable
{
    public List<GlobalSetting> GlobalSettingPaginationList { get; set; }
    public List<GlobalSetting> GlobalSettingList { get; set; }
    public GlobalSetting GlobalSettingDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public GlobalSettingFixture()
    {
        var fixture = new Fixture();

        GlobalSettingList = fixture.Create<List<GlobalSetting>>();

        GlobalSettingPaginationList = fixture.CreateMany<GlobalSetting>(20).ToList();

        // Setup proper test data for GlobalSettingPaginationList
        GlobalSettingPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        GlobalSettingPaginationList.ForEach(x => x.IsActive = true);

        // Setup proper test data for GlobalSettingList
        GlobalSettingList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        GlobalSettingList.ForEach(x => x.IsActive = true);

        GlobalSettingDto = fixture.Create<GlobalSetting>();
        GlobalSettingDto.ReferenceId = Guid.NewGuid().ToString();
        GlobalSettingDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
