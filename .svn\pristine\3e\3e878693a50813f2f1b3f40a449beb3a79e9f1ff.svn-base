﻿namespace ContinuityPatrol.Application.Features.Replication.Queries.GetType;

public class GetReplicationTypeQueryHandler : IRequestHandler<GetReplicationTypeQuery, List<ReplicationTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationViewRepository _replicationViewRepository;

    public GetReplicationTypeQueryHandler(IMapper mapper, IReplicationViewRepository replicationViewRepository)
    {
        _mapper = mapper;
        _replicationViewRepository = replicationViewRepository;
    }

    public async Task<List<ReplicationTypeVm>> Handle(GetReplicationTypeQuery request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.TypeId, $"Invalid Component Type Id '{request.TypeId}'");

        var replications = request.TypeId != null
            ? (await _replicationViewRepository.GetType(request.TypeId)).ToList()
            : await _replicationViewRepository.ListAllAsync();

        return replications.Count <= 0
            ? new List<ReplicationTypeVm>()
            : _mapper.Map<List<ReplicationTypeVm>>(replications);
    }
}