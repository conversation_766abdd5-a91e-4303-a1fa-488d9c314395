﻿using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;

namespace ContinuityPatrol.Application.Mappings;

public class BusinessServiceAvailabilityProfile : Profile
{
    public BusinessServiceAvailabilityProfile()
    {
        CreateMap<BusinessServiceAvailability, CreateBusinessServiceAvailabilityCommand>().ReverseMap();
        CreateMap<UpdateBusinessServiceAvailabilityCommand, BusinessServiceAvailability>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<BusinessServiceAvailability, BusinessServiceAvailabilityListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BusinessServiceAvailability, BusinessServiceAvailabilityDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        //CreateMap<Domain.Entities.BusinessServiceAvailability, BusinessServiceAvailabilityPaginatedListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}