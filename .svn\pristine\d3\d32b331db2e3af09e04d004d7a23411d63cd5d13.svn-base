﻿using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.FormType.Queries;

public class GetFormTypePaginatedListQueryHandlerTests : IClassFixture<FormTypeFixture>
{
    private readonly GetFormTypePaginatedListQueryHandler _handler;
    private readonly Mock<IFormTypeRepository> _mockFormTypeRepository;

    public GetFormTypePaginatedListQueryHandlerTests(FormTypeFixture formTypeFixture)
    {
        var formTypeNewFixture= formTypeFixture;
        
        _mockFormTypeRepository = FormTypeRepositoryMocks.GetPaginatedFormTypeRepository(formTypeNewFixture.FormTypes);

        _handler = new GetFormTypePaginatedListQueryHandler(formTypeNewFixture.Mapper, _mockFormTypeRepository.Object);

        formTypeNewFixture.FormTypes[0].FormTypeName = "Testing";

        formTypeNewFixture.FormTypes[1].FormTypeName = "Replication_Type";

        formTypeFixture.FormTypes[0].FormTypeLogo = "maxDB_icon";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetFormTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<FormTypeListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedFormTypes_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetFormTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<FormTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<FormTypeListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].FormTypeName.ShouldBe("Testing");

        result.Data[0].FormTypeLogo.ShouldBe("maxDB_icon");

        result.Data[0].IsDelete.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetFormTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<FormTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_FormTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetFormTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "formtypename=Replication" },CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<FormTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].FormTypeName.ShouldBe("Replication_Type");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetFormTypePaginatedListQuery(), CancellationToken.None);

        _mockFormTypeRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}