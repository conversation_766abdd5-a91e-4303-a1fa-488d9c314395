using ContinuityPatrol.Application.Features.DynamicDashboardMap.Events.Delete;

namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Delete;

public class
    DeleteDynamicDashboardMapCommandHandler : IRequestHandler<DeleteDynamicDashboardMapCommand,
        DeleteDynamicDashboardMapResponse>
{
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IPublisher _publisher;
    private readonly IUserRepository _userRepository;

    public DeleteDynamicDashboardMapCommandHandler(IDynamicDashboardMapRepository dynamicDashboardMapRepository,
        IPublisher publisher, IUserRepository userRepository)
    {
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;

        _publisher = publisher;
        _userRepository = userRepository;
    }

    public async Task<DeleteDynamicDashboardMapResponse> Handle(DeleteDynamicDashboardMapCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _dynamicDashboardMapRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DynamicDashboardMap),
            new NotFoundException(nameof(Domain.Entities.DynamicDashboardMap), request.Id));

        eventToDelete.IsActive = false;

        await _dynamicDashboardMapRepository.UpdateAsync(eventToDelete);

        if (eventToDelete.Type.Trim().ToLower().Equals("user"))
        {
            var user = await _userRepository.GetByReferenceIdAsync(eventToDelete.UserId);

            user.IsDefaultDashboard = false;

            await _userRepository.UpdateAsync(user);
        }
        else if (eventToDelete.Type.Trim().ToLower().Equals("role"))
        {
            var userRoles = await _userRepository.GetUsersByRoleId(eventToDelete.RoleId);

            userRoles.ForEach(x => x.IsDefaultDashboard = false);

            await _userRepository.UpdateRangeAsync(userRoles);
        }


        var response = new DeleteDynamicDashboardMapResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DynamicDashboardMap), eventToDelete.UserName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DynamicDashboardMapDeletedEvent { Name = eventToDelete.UserName },
            cancellationToken);

        return response;
    }
}