﻿using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Rto.Queries
{
    public class GetRTOByBusinessServiceIdQueryHandlerTests
    {
        private readonly Mock<IRtoRepository> _mockRtoRepository;
        private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
        private readonly Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
        private readonly Mock<IDashboardViewRepository> _mockDashboardViewRepository;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRTOByBusinessServiceIdQueryHandler _handler;

        public GetRTOByBusinessServiceIdQueryHandlerTests()
        {
            _mockRtoRepository = new Mock<IRtoRepository>();
            _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
            _mockBusinessFunctionRepository = new Mock<IBusinessFunctionRepository>();
            _mockDashboardViewRepository = new Mock<IDashboardViewRepository>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetRTOByBusinessServiceIdQueryHandler(_mockInfraObjectRepository.Object,_mockBusinessFunctionRepository.Object,_mockDashboardViewRepository.Object,_mockWorkflowOperationGroupRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsRTOByBusinessServiceIdVm_WhenRtoExists()
        {
            var query = new GetRTOByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };
            var rtoEntity = new Domain.Entities.Rto { Id = 1, BusinessServiceName = "Test RTO" };
            var rtoVm = new RTOByBusinessServiceIdVm { Id = Guid.NewGuid().ToString(), BusinessServiceName = "Test RTO" };

            _mockRtoRepository
                .Setup(repo => repo.GetRtoByBusinessServiceId(query.BusinessServiceId))
                .ReturnsAsync(rtoEntity);

            _mockMapper
                .Setup(mapper => mapper.Map<RTOByBusinessServiceIdVm>(rtoEntity))
                .Returns(rtoVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(rtoVm.Id, result.Id);
            Assert.Equal(rtoVm.BusinessServiceName, result.BusinessServiceName);

            _mockRtoRepository.Verify(repo => repo.GetRtoByBusinessServiceId(query.BusinessServiceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RTOByBusinessServiceIdVm>(rtoEntity), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenRtoDoesNotExist()
        {
            var query = new GetRTOByBusinessServiceIdQuery { BusinessServiceId = Guid.NewGuid().ToString() };

            _mockRtoRepository
                .Setup(repo => repo.GetRtoByBusinessServiceId(query.BusinessServiceId))
                .ReturnsAsync((Domain.Entities.Rto)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));

            Assert.Equal($"Entity \"Rto\" ({query.BusinessServiceId}) was not found.", exception.Message);
            _mockRtoRepository.Verify(repo => repo.GetRtoByBusinessServiceId(query.BusinessServiceId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RTOByBusinessServiceIdVm>(It.IsAny<Domain.Entities.Rto>()), Times.Never);
        }
    }
}
