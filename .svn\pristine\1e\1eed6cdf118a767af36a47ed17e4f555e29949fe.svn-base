﻿namespace ContinuityPatrol.Domain.Entities;

public class Job : AuditableEntity
{
    public string Name { get; set; }
    public string CompanyId { get; set; }
    [Column(TypeName = "NCLOB")] public string InfraObjectProperties { get; set; }
    public string TemplateId { get; set; }
    public string TemplateName { get; set; }
    public string SolutionTypeId { get; set; }
    public string SolutionType { get; set; }
    public string Type { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string Status { get; set; }
    public string CronExpression { get; set; }
    public string ScheduleTime { get; set; }
    public int ScheduleType { get; set; }
    public int IsSchedule { get; set; }
    public string State { get; set; }
    public string GroupPolicyId { get; set; }
    public string GroupPolicyName { get; set; }
    public string ExecutionPolicy { get; set; }
    public string LastExecutionTime { get; set; }
    [Column(TypeName = "NCLOB")] public string ExceptionMessage { get; set; }
}