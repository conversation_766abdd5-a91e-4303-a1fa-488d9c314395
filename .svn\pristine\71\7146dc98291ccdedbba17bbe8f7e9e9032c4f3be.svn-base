﻿using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using System.IO;
using System.Security.AccessControl;
using System.Security.Principal;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class ServerLogHistoryController : BaseController
{
    public static string _rootPath { get; set; }
    public static ILogger<ServerLogHistoryController> _logger;
    private readonly IDataProvider _provider;
    public ServerLogHistoryController(ILogger<ServerLogHistoryController> logger, IDataProvider provider)
    {
        _provider = provider;
        _logger = logger;
    }

    public IActionResult List()
    {
        return View();
    }
    [HttpGet]
    private void GrantFolderAccess(string path)
    {
        try
        {
            DirectoryInfo directoryInfo = new DirectoryInfo(path);
            DirectorySecurity security = directoryInfo.GetAccessControl();

            // Grant access to IIS_IUSRS
            SecurityIdentifier sid = new SecurityIdentifier(WellKnownSidType.BuiltinUsersSid, null);
            security.AddAccessRule(new FileSystemAccessRule(
                sid,
                FileSystemRights.Read | FileSystemRights.ListDirectory | FileSystemRights.ExecuteFile,
                InheritanceFlags.ContainerInherit | InheritanceFlags.ObjectInherit,
                PropagationFlags.None,
                AccessControlType.Allow));

            // Apply the new permissions
            directoryInfo.SetAccessControl(security);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to grant access: {ex.Message}");
        }
    }
    public async Task<IActionResult> GetServerLogList()
    {
        try
        {
            var listValue = await _provider.LogViewer.GetLogViewerList();

            return Json(new { success = true, data = listValue });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on ServerLogHistory page  while getting the ServerLoglist.", ex);
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
    public IActionResult GetFolderContents(string path)
    {
        try
        {
            if (string.IsNullOrEmpty(path))
            {
                path = "";
            }
            _logger.LogInformation($"Enter GetFolderContents method. path : {path}");

            var fullPath = Path.Combine(_rootPath, path);
            GrantFolderAccess(fullPath);
            if (!Directory.Exists(fullPath))
            {
                _logger.LogError("Directory does not exist.");
                return Json(new { error = "Directory does not exist." });
            }

            var folderContents = new List<object>();

            // Get subfolders
            var subfolders = Directory.GetDirectories(fullPath).Select(d => new
            {
                Type = "folder",
                Name = Path.GetFileName(d),
                Path = Path.Combine(path, Path.GetFileName(d)) // Relative path
               // LastModified = Directory.GetLastWriteTime(d) // Get the last modified date of the folder
            })/*.OrderByDescending(d => d.LastModified)*/; // Order by the last modified date of folders

            // Get files (only .txt and .log)
            var extensions = new[] { "*.txt", "*.log" };

            var files = extensions
                .SelectMany(ext => Directory.GetFiles(fullPath, ext))
                .Select(f => new
                {
                    Type = "file",
                    Name = Path.GetFileName(f),
                    Path = Path.Combine(path, Path.GetFileName(f)), // Relative path
                    LastModified = System.IO.File.GetLastWriteTime(f) // Get the last modified date of the file
                }).OrderByDescending(f => f.LastModified).ThenBy(f => f.Name);// Order by the last modified date of files


            folderContents.AddRange(subfolders);
            folderContents.AddRange(files);

            return Json(folderContents);
        }
        catch (Exception ex)
        {
            _logger.Exception($"Error retrieving folder contents: {ex.Message}", ex);
            return Json(new { error = $"Error retrieving folder contents: {ex.Message}" });
        }
    }

    [HttpPost]
    public IActionResult Download([FromBody] string filePath)
    {
        try
        {
            if (!filePath.StartsWith(_rootPath))
            {
                filePath = Path.Combine(_rootPath, filePath);
            }
            _logger.LogInformation($"Enter the Shared file download method. path : {filePath}");
            if (string.IsNullOrWhiteSpace(filePath) || !filePath.StartsWith(_rootPath))
            {
                _logger.LogError($"Invalid file path");
                return BadRequest("Invalid file path.");
            }

            if (System.IO.File.Exists(filePath))
            {
                var fileName = Path.GetFileName(filePath);
                byte[] fileBytes = GetFileBytes(filePath);
                return File(fileBytes, "application/octet-stream", fileName);
            }

            _logger.LogError("File not found.");
            return NotFound("File not found.");
        }
        catch (Exception ex)
        {
            _logger.Exception($"Shared file download failed : {ex.Message}", ex);
            return Json(new { error = $"Download failed: {ex.Message}" });
        }
    }

    private static byte[] GetFileBytes(string filePath)
    {
        try
        {
            using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            {
                byte[] fileBytes = new byte[fs.Length];
                fs?.Read(fileBytes, 0, (int)fs.Length);
                return fileBytes;
            }
        }
        catch (IOException ex)
        {
            Console.WriteLine($"Error reading file: {ex.Message}");
            _logger.LogError($"Error reading file: {ex.Message}");
            return null;
        }
    }

    [HttpGet]
    public IActionResult GetLogFileContents(string fileName)
    {
        try
        {
            if (string.IsNullOrEmpty(fileName))
            {
                _logger.LogError("File name path is Null or Empty value.");
                return Json("File name path is Null or Empty value.");
            }
            string filePath = Path.Combine(_rootPath, fileName.Trim());
            _logger.LogInformation($"Enter GetLogFile Content method. Path : {fileName}");

            if (System.IO.File.Exists(filePath))
            {
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                using (StreamReader reader = new StreamReader(fs))
                {
                    string content = reader.ReadToEnd();
                    return Json(string.IsNullOrWhiteSpace(content) ? "The file is currently empty." : content);
                }
            }

            _logger.LogError("File not found.");
            return Json("File not found.");
        }
        catch (Exception ex)
        {
            _logger.Exception($"Exception : {ex.Message}", ex);
            return Json(new { error = $"Exception: {ex.Message}" });
        }
    }
    [HttpPost]
    public async Task<IActionResult> GetSharedFolderPath([FromBody] string server)
    {
        string sharedFolderPath = "", hostName = "", username = "", password = "", driveLetter = "";

        if (server.Equals("primaryServer", StringComparison.OrdinalIgnoreCase))
        {
            sharedFolderPath = @"C:\CP";
            _rootPath = sharedFolderPath;
            hostName = "Primary Server";
            return Json(new { success = true, data = hostName });
        }
        else if (!server.Equals("primaryServer", StringComparison.OrdinalIgnoreCase))
        {
            var serverLogDetails = await _provider.LogViewer.GetLogViewerDetail(server);
            sharedFolderPath = $@"{serverLogDetails.FolderPath}";
            string[] parts = sharedFolderPath.TrimStart('\\').Split('\\');
            hostName = parts[0];

            // Get credentials securely from environment variables or config
            username = Environment.GetEnvironmentVariable(serverLogDetails.UserName.ToString()) ?? serverLogDetails.UserName.ToString();
            password = Environment.GetEnvironmentVariable(serverLogDetails.Password.ToString()) ?? serverLogDetails.Password.ToString();

            driveLetter = "Z";
        }
        else
        {
            return Json(new { success = false, data = "Invalid Server" });
        }

        // Try to map the network drive
        var result = MapNetworkDrive(driveLetter, sharedFolderPath, username, password);

        if (result.Success)
        {
            return Json(new { success = true, data = hostName });
        }

        return Json(new { success = false, data = result.Message });
    }

    // Function to map the network drive
    private (bool Success, string Message) MapNetworkDrive(string driveLetter, string networkPath, string username, string password)
    {
        try
        {
            password = SecurityHelper.Decrypt(password) ?? password;
            _logger.LogInformation($"Attempting to map network drive. Path: {networkPath}, Username: {username}");

            // Step 1: Disconnect existing connection for the specific drive
            string disconnectCmd = $"net use {driveLetter}: /delete /yes";
            var disconnectProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {disconnectCmd}",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                }
            };
            disconnectProcess.Start();
            disconnectProcess.WaitForExit();

            // Step 2: Map the network drive
            string netUseCmd = $"net use {driveLetter}: \"{networkPath}\" /user:\"{username}\" \"{password}\" /persistent:yes";
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c {netUseCmd}",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                }
            };

            process.Start();
            string output = process.StandardOutput.ReadToEnd();
            string error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            _logger.LogInformation($"Map Network Drive Output: {output}");

            if (!string.IsNullOrWhiteSpace(error))
            {
                _logger.LogError($"Network drive mapping error: {error}");
                return (false, error);
            }

            _rootPath = networkPath;
            _logger.LogInformation("Network drive mapped successfully.");
            return (true, "Network drive mapped successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError($"Exception while mapping network drive: {ex.Message}");
            return (false, $"Error: {ex.Message}");
        }
    }



    private string ExecuteCommand(string command)
    {
        try
        {
            ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", "/c " + command)
            {
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process process = Process.Start(psi))
            {
                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();
                process.WaitForExit();
                return string.IsNullOrWhiteSpace(error) ? output : error;
            }
        }
        catch (Exception ex)
        {
            return $"Error executing command: {ex.Message}";
        }
    }
}