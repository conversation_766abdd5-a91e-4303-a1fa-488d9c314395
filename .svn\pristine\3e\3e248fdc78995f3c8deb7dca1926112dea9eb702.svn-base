﻿@model ContinuityPatrol.Domain.ViewModels.LicenseManagerModel.BaseLicenseViewModel

@Html.AntiForgeryToken()

<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <form asp-controller="LicenseManager" asp-action="DeleteDerived" asp-route-id="DeleteId" enctype="multipart/form-data">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p>
                    You want to delete <span class="font-weight-bolder text-primary" id="deleteName"></span>
                    data?
                </p>
                <input asp-for="DerivedLicense.Id" type="hidden" id="DeleteId" name="id" class="form-control" />
               
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm">Yes</button>
            </div>
        </form>
    </div>
</div>
