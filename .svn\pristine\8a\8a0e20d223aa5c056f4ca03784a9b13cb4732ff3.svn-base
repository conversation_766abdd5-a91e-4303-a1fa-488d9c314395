using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SybaseRSHADRMonitorLogFixture : IDisposable
{
    public List<SybaseRSHADRMonitorLog> SybaseRSHADRMonitorLogPaginationList { get; set; }
    public List<SybaseRSHADRMonitorLog> SybaseRSHADRMonitorLogList { get; set; }
    public SybaseRSHADRMonitorLog SybaseRSHADRMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SybaseRSHADRMonitorLogFixture()
    {
        var fixture = new Fixture();

        SybaseRSHADRMonitorLogList = fixture.Create<List<SybaseRSHADRMonitorLog>>();

        SybaseRSHADRMonitorLogPaginationList = fixture.CreateMany<SybaseRSHADRMonitorLog>(20).ToList();

        SybaseRSHADRMonitorLogDto = fixture.Create<SybaseRSHADRMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLog(
        string type = "SYBASE_RSHADR",
        string infraObjectId = "INFRA_001",
        string infraObjectName = "Default Sybase Object",
        string workflowId = "WF_001",
        string workflowName = "Default Workflow",
        string properties = null,
        string configuredRPO = "15",
        string dataLagValue = "5",
        string threshold = "10",
        DateTime? createdDate = null,
        DateTime? lastModifiedDate = null,
        bool isActive = true,
        bool isDelete = false)
    {
        var now = DateTime.UtcNow;
        return new SybaseRSHADRMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            InfraObjectId = infraObjectId,
            InfraObjectName = infraObjectName,
            WorkflowId = workflowId,
            WorkflowName = workflowName,
            Properties = properties ?? "{\"rpo\": \"15\", \"status\": \"active\", \"lastSync\": \"2024-01-01T10:00:00Z\"}",
            ConfiguredRPO = configuredRPO,
            DataLagValue = dataLagValue,
            Threshold = threshold,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = createdDate ?? now,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = lastModifiedDate ?? now
        };
    }

    public List<SybaseRSHADRMonitorLog> CreateMultipleSybaseRSHADRMonitorLogs(int count, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SybaseRSHADRMonitorLog>();
        for (int i = 1; i <= count; i++)
        {
            logs.Add(CreateSybaseRSHADRMonitorLog(
                type: $"SYBASE_TYPE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Sybase Object {i}",
                workflowId: $"WF_{i:D3}",
                workflowName: $"Workflow {i}",
                configuredRPO: (15 + i).ToString(),
                dataLagValue: i.ToString(),
                threshold: (10 + i).ToString(),
                createdDate: DateTime.UtcNow.AddDays(-i)
            ));
        }
        return logs;
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogWithSpecificId(string referenceId, string type = "SYBASE_RSHADR")
    {
        return new SybaseRSHADRMonitorLog
        {
            ReferenceId = referenceId,
            Type = type,
            InfraObjectId = "INFRA_TEST",
            InfraObjectName = "Test Sybase Object",
            WorkflowId = "WF_TEST",
            WorkflowName = "Test Workflow",
            Properties = "{\"test\": true}",
            ConfiguredRPO = "15",
            DataLagValue = "5",
            Threshold = "10",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogForType(string type, string infraObjectId = "INFRA_001")
    {
        return CreateSybaseRSHADRMonitorLog(
            type: type,
            infraObjectId: infraObjectId,
            infraObjectName: $"Sybase Object for {type}",
            workflowName: $"Workflow for {type}"
        );
    }

    public List<SybaseRSHADRMonitorLog> CreateSybaseRSHADRMonitorLogsForTypes(List<string> types, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SybaseRSHADRMonitorLog>();
        foreach (var type in types)
        {
            logs.Add(CreateSybaseRSHADRMonitorLogForType(type, infraObjectId));
        }
        return logs;
    }

    public List<SybaseRSHADRMonitorLog> CreateSybaseRSHADRMonitorLogsWithStatus(int activeCount, int inactiveCount, string infraObjectId = "INFRA_001")
    {
        var logs = new List<SybaseRSHADRMonitorLog>();

        for (int i = 1; i <= activeCount; i++)
        {
            logs.Add(CreateSybaseRSHADRMonitorLog(
                type: $"SYBASE_ACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Active Sybase {i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            logs.Add(CreateSybaseRSHADRMonitorLog(
                type: $"SYBASE_INACTIVE_{i}",
                infraObjectId: infraObjectId,
                infraObjectName: $"Inactive Sybase {i}",
                isActive: false
            ));
        }

        return logs;
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogForInfraObject(string infraObjectId, string infraObjectName = null)
    {
        return CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            infraObjectName: infraObjectName ?? $"Sybase Object for {infraObjectId}",
            workflowName: $"Workflow for {infraObjectId}"
        );
    }

    public List<SybaseRSHADRMonitorLog> CreateSybaseRSHADRMonitorLogsForInfraObjects(List<string> infraObjectIds)
    {
        var logs = new List<SybaseRSHADRMonitorLog>();
        foreach (var infraObjectId in infraObjectIds)
        {
            logs.Add(CreateSybaseRSHADRMonitorLogForInfraObject(infraObjectId));
        }
        return logs;
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSybaseRSHADRMonitorLog(properties: propertiesJson);
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogWithComplexProperties()
    {
        var complexProperties = new Dictionary<string, object>
        {
            {"rpo", "15"},
            {"status", "running"},
            {"lastSync", "2024-01-01T10:00:00Z"},
            {"replicationDetails", new Dictionary<string, object>
                {
                    {"primaryServer", "SYBASE001"},
                    {"standbyServer", "SYBASE002"},
                    {"replicationMode", "async"},
                    {"compressionEnabled", true}
                }
            },
            {"performance", new Dictionary<string, object>
                {
                    {"throughput", "180MB/s"},
                    {"latency", "4ms"},
                    {"errorRate", "0.002%"}
                }
            },
            {"rshadrDetails", new Dictionary<string, object>
                {
                    {"replicationAgent", "RSHADR_AGENT_001"},
                    {"connectionStatus", "CONNECTED"},
                    {"lastHeartbeat", "2024-01-01T09:59:00Z"},
                    {"queueDepth", 0}
                }
            },
            {"PrSybaseWithHADRMonitoring", new Dictionary<string, object>
                {
                    {"ReplicationServerMonitoring", new Dictionary<string, object>
                        {
                            {"ServerIpAddress", "*************"},
                            {"ServerPort", "4100"},
                            {"Status", "ACTIVE"}
                        }
                    }
                }
            },
            {"SybaseWithHADRMonitoring", new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        {"ReplicationServerMonitoring", new Dictionary<string, object>
                            {
                                {"ServerIpAddress", "*************"},
                                {"ServerPort", "4101"},
                                {"Status", "STANDBY"}
                            }
                        }
                    }
                }
            }
        };

        return CreateSybaseRSHADRMonitorLogWithProperties(complexProperties);
    }

    public List<SybaseRSHADRMonitorLog> CreateSybaseRSHADRMonitorLogsForDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<SybaseRSHADRMonitorLog>();
        var dateRange = (endDate - startDate).TotalDays;
        var interval = dateRange / count;

        for (int i = 0; i < count; i++)
        {
            var logDate = startDate.AddDays(i * interval);
            logs.Add(CreateSybaseRSHADRMonitorLog(
                infraObjectId: infraObjectId,
                infraObjectName: $"Sybase Object {i + 1}",
                workflowName: $"Workflow {i + 1}",
                createdDate: logDate,
                lastModifiedDate: logDate
            ));
        }

        return logs;
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogForWorkflow(string workflowId, string workflowName = null, string infraObjectId = "INFRA_001")
    {
        return CreateSybaseRSHADRMonitorLog(
            infraObjectId: infraObjectId,
            workflowId: workflowId,
            workflowName: workflowName ?? $"Workflow {workflowId}",
            infraObjectName: $"Sybase Object for {workflowId}"
        );
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogWithRPOSettings(string configuredRPO, string dataLagValue, string threshold)
    {
        return CreateSybaseRSHADRMonitorLog(
            configuredRPO: configuredRPO,
            dataLagValue: dataLagValue,
            threshold: threshold,
            properties: $"{{\"rpo\": \"{configuredRPO}\", \"dataLag\": \"{dataLagValue}\", \"threshold\": \"{threshold}\"}}"
        );
    }

    public List<SybaseRSHADRMonitorLog> CreateSybaseRSHADRMonitorLogsForRPOTesting()
    {
        return new List<SybaseRSHADRMonitorLog>
        {
            CreateSybaseRSHADRMonitorLogWithRPOSettings("15", "5", "10"),
            CreateSybaseRSHADRMonitorLogWithRPOSettings("30", "10", "20"),
            CreateSybaseRSHADRMonitorLogWithRPOSettings("60", "25", "40"),
            CreateSybaseRSHADRMonitorLogWithRPOSettings("120", "50", "80")
        };
    }

    public SybaseRSHADRMonitorLog CreateSybaseReplicationLog(string infraObjectId = "INFRA_REPL")
    {
        return CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_REPLICATION",
            infraObjectId: infraObjectId,
            infraObjectName: "Sybase Replication Object",
            workflowName: "Replication Workflow",
            properties: "{\"type\": \"replication\", \"mode\": \"async\", \"status\": \"running\"}"
        );
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRLog(string infraObjectId = "INFRA_RSHADR")
    {
        return CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_RSHADR",
            infraObjectId: infraObjectId,
            infraObjectName: "Sybase RSHADR Object",
            workflowName: "RSHADR Workflow",
            properties: "{\"type\": \"rshadr\", \"agent\": \"RSHADR_AGENT\", \"status\": \"active\"}"
        );
    }

    public SybaseRSHADRMonitorLog CreateSybaseHADRLog(string infraObjectId = "INFRA_HADR")
    {
        return CreateSybaseRSHADRMonitorLog(
            type: "SYBASE_HADR",
            infraObjectId: infraObjectId,
            infraObjectName: "Sybase HADR Object",
            workflowName: "HADR Workflow",
            properties: "{\"type\": \"hadr\", \"primaryServer\": \"SYB001\", \"standbyServer\": \"SYB002\", \"status\": \"synchronized\"}"
        );
    }

    public List<SybaseRSHADRMonitorLog> CreateStandardSybaseMonitorLogs()
    {
        return new List<SybaseRSHADRMonitorLog>
        {
            CreateSybaseReplicationLog(),
            CreateSybaseRSHADRLog(),
            CreateSybaseHADRLog()
        };
    }

    public SybaseRSHADRMonitorLog CreateMinimalSybaseRSHADRMonitorLog()
    {
        return new SybaseRSHADRMonitorLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "SYBASE_MINIMAL",
            InfraObjectId = "MINIMAL_INFRA",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogForTesting(
        string testName,
        string type = null,
        string infraObjectId = null)
    {
        return CreateSybaseRSHADRMonitorLog(
            type: type ?? $"SYBASE_{testName.ToUpper()}",
            infraObjectId: infraObjectId ?? $"INFRA_{testName.ToUpper()}",
            infraObjectName: $"Test Sybase for {testName}",
            workflowName: $"Test Workflow for {testName}"
        );
    }

    public SybaseRSHADRMonitorLog CreateSybaseRSHADRMonitorLogWithReplicationServerDetails()
    {
        var properties = new Dictionary<string, object>
        {
            {"PrSybaseWithHADRMonitoring", new Dictionary<string, object>
                {
                    {"ReplicationServerMonitoring", new Dictionary<string, object>
                        {
                            {"ServerIpAddress", "*************"},
                            {"ServerPort", "4100"},
                            {"Status", "ACTIVE"},
                            {"LastUpdate", "2024-01-01T10:00:00Z"}
                        }
                    }
                }
            },
            {"SybaseWithHADRMonitoring", new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        {"ReplicationServerMonitoring", new Dictionary<string, object>
                            {
                                {"ServerIpAddress", "*************"},
                                {"ServerPort", "4101"},
                                {"Status", "STANDBY"},
                                {"LastUpdate", "2024-01-01T10:00:00Z"}
                            }
                        }
                    },
                    new Dictionary<string, object>
                    {
                        {"ReplicationServerMonitoring", new Dictionary<string, object>
                            {
                                {"ServerIpAddress", "*************"},
                                {"ServerPort", "4102"},
                                {"Status", "STANDBY"},
                                {"LastUpdate", "2024-01-01T10:00:00Z"}
                            }
                        }
                    }
                }
            }
        };

        return CreateSybaseRSHADRMonitorLogWithProperties(properties);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
