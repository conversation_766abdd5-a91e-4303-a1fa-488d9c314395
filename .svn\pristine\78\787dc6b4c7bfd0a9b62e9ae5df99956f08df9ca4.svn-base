using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MongoDbMonitorStatusRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly MongoDbMonitorStatusRepository _repository;
    private readonly MongoDbMonitorStatusFixture _fixture;
    private readonly Fixture _autoFixture;

    public MongoDbMonitorStatusRepositoryTests()
    {
        _context = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _fixture = new MongoDbMonitorStatusFixture();
        _autoFixture = new Fixture();

        _repository = new MongoDbMonitorStatusRepository(_context);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _fixture?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _context.MongoDbMonitorStatus.RemoveRange(_context.MongoDbMonitorStatus);
        await _context.SaveChangesAsync();
    }

    #region GetDetailByType Tests


    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeNotExists()
    {
        // Arrange
        await ClearDatabase();
        var existingLog = _fixture.CreateMongoDbMonitorStatusWithProperties(type: "Existing Type", isActive: true);
        await _context.MongoDbMonitorStatus.AddAsync(existingLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("Non-Existent Type");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenOnlyInactiveLogsExist()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "MongoDB Replica Set";
        var inactiveLog1 = _fixture.CreateInactiveMongoDbMonitorStatus(targetType);
        var inactiveLog2 = _fixture.CreateInactiveMongoDbMonitorStatus(targetType);

        await _context.MongoDbMonitorStatus.AddRangeAsync(inactiveLog1, inactiveLog2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);

    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var exactCaseLog = _fixture.CreateMongoDbMonitorStatusWithProperties(type: "MongoDB Replica Set", isActive: true);
        await _context.MongoDbMonitorStatus.AddAsync(exactCaseLog);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetDetailByType("mongodb replica set"); // lowercase
        var result2 = await _repository.GetDetailByType("MONGODB REPLICA SET"); // uppercase
        var result3 = await _repository.GetDetailByType("MongoDB Replica Set"); // exact case

        // Assert
        Assert.Empty(result1); // Case sensitive - no match
        Assert.Empty(result2); // Case sensitive - no match
        Assert.Single(result3); // Exact match
    }

  

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialCharLog = _fixture.CreateMongoDbMonitorStatusWithSpecialCharacters();
        specialCharLog.IsActive = true;
        await _context.MongoDbMonitorStatus.AddAsync(specialCharLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(specialCharLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialCharLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleUnicodeCharacters()
    {
        // Arrange
        await ClearDatabase();
        var unicodeLog = _fixture.CreateMongoDbMonitorStatusWithUnicodeCharacters();
        unicodeLog.IsActive = true;
        await _context.MongoDbMonitorStatus.AddAsync(unicodeLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(unicodeLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(unicodeLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceLog = _fixture.CreateMongoDbMonitorStatusWithWhitespace();
        whitespaceLog.IsActive = true;
        await _context.MongoDbMonitorStatus.AddAsync(whitespaceLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeLog = _fixture.CreateMongoDbMonitorStatusWithLongType(1000);
        longTypeLog.IsActive = true;
        await _context.MongoDbMonitorStatus.AddAsync(longTypeLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleMultipleCommonTypes()
    {
        // Arrange
        await ClearDatabase();
        var commonTypes = MongoDbMonitorStatusFixture.TestData.CommonTypes;
        var logs = new List<MongoDbMonitorStatus>();

        foreach (var type in commonTypes)
        {
            var log = _fixture.CreateMongoDbMonitorStatusWithProperties(type: type, isActive: true);
            logs.Add(log);
        }

        await _context.MongoDbMonitorStatus.AddRangeAsync(logs);
        await _context.SaveChangesAsync();

        // Act & Assert
        foreach (var type in commonTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

  

    #endregion

    #region GetMongoDbMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldReturnMatchingLog_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var targetInfraObjectId = Guid.NewGuid().ToString();
        var targetLog = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(targetInfraObjectId);
        var otherLog = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(Guid.NewGuid().ToString());

        await _context.MongoDbMonitorStatus.AddRangeAsync(targetLog, otherLog);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(targetInfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(targetInfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdNotExists()
    {
        // Arrange
        await ClearDatabase();
        var existingLog = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(Guid.NewGuid().ToString());
        await _context.MongoDbMonitorStatus.AddAsync(existingLog);
        await _context.SaveChangesAsync();

        var nonExistentInfraObjectId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(nonExistentInfraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleLogsWithSameInfraObjectIdExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var log1 = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(infraObjectId);
        var log2 = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        // Ensure different reference IDs to distinguish them
        log1.ReferenceId = Guid.NewGuid().ToString();
        log2.ReferenceId = Guid.NewGuid().ToString();

        await _context.MongoDbMonitorStatus.AddRangeAsync(log1, log2);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first match (FirstOrDefaultAsync behavior)
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldReturnActiveOrInactiveLog()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        //var inactiveLog = _fixture.CreateMongoDbMonitorStatusWithProperties(
        //    infraObjectId: infraObjectId, isActive: false);
        var MonitorList = _fixture.MongoDbMonitorStatusDto;
        MonitorList.InfraObjectId = infraObjectId;
        await _context.MongoDbMonitorStatus.AddAsync(MonitorList);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Method does not filter by IsActive, unlike GetDetailByType
    }



    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldAcceptValidGuid()
    {
        // Arrange
        await ClearDatabase();
        var validGuid = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(validGuid);

        // Assert
        Assert.Null(result); // No matching record, but no exception thrown
    }

    


    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var guid = Guid.NewGuid();
        var upperCaseGuid = guid.ToString().ToUpper();
        var log = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(upperCaseGuid);

        await _context.MongoDbMonitorStatus.AddAsync(log);
        await _context.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetMongoDbMonitorStatusByInfraObjectId(upperCaseGuid);
        var result2 = await _repository.GetMongoDbMonitorStatusByInfraObjectId(guid.ToString().ToLower());

        // Assert
        Assert.NotNull(result1); // Exact case match
        Assert.Null(result2);    // Different case - no match (string comparison is case-sensitive)
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldHandleSpecialCharactersInOtherProperties()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var log = _fixture.CreateMongoDbMonitorStatusWithSpecialCharacters();
        log.InfraObjectId = infraObjectId;

        await _context.MongoDbMonitorStatus.AddAsync(log);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should handle special characters in other properties correctly
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldReturnCompleteEntity()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var log = _fixture.CreateMongoDbMonitorStatusWithProperties(
            type: "MongoDB Replica Set",
            infraObjectId: infraObjectId,
            infraObjectName: "Test Infra Object",
            workflowId: Guid.NewGuid().ToString(),
            workflowName: "Test Workflow",
            properties: "Test Properties",
            configuredRPO: "1 Hour",
            dataLagValue: "5 Minutes",
            threshold: "10 Minutes",
            isActive: true
        );

        await _context.MongoDbMonitorStatus.AddAsync(log);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(log.Type, result.Type);
        Assert.Equal(log.InfraObjectId, result.InfraObjectId);
        Assert.Equal(log.InfraObjectName, result.InfraObjectName);
        Assert.Equal(log.WorkflowId, result.WorkflowId);
        Assert.Equal(log.WorkflowName, result.WorkflowName);
        Assert.Equal(log.Properties, result.Properties);
        Assert.Equal(log.ConfiguredRPO, result.ConfiguredRPO);
        Assert.Equal(log.DataLagValue, result.DataLagValue);
        Assert.Equal(log.Threshold, result.Threshold);
        Assert.Equal(log.IsActive, result.IsActive);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldHandlePerformanceWithManyRecords()
    {
        // Arrange
        await ClearDatabase();
        var targetInfraObjectId = Guid.NewGuid().ToString();
        var logs = new List<MongoDbMonitorStatus>();

        // Add many records with different InfraObjectIds
        for (int i = 0; i < 100; i++)
        {
            var log = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(Guid.NewGuid().ToString());
            logs.Add(log);
        }

        // Add the target record
        var targetLog = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(targetInfraObjectId);
        logs.Add(targetLog);

        await _context.MongoDbMonitorStatus.AddRangeAsync(logs);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(targetInfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(targetInfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldHandleUnicodeCharactersInOtherProperties()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var log = _fixture.CreateMongoDbMonitorStatusWithUnicodeCharacters();
        log.InfraObjectId = infraObjectId;

        await _context.MongoDbMonitorStatus.AddAsync(log);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should handle Unicode characters in other properties correctly
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_ShouldHandleNullPropertiesInEntity()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var log = _fixture.CreateMongoDbMonitorStatusWithSpecificInfraObjectId(infraObjectId);

        // Set some properties to null
        log.Type = null;
        log.InfraObjectName = null;
        log.Properties = null;

        await _context.MongoDbMonitorStatus.AddAsync(log);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Null(result.Type);
        Assert.Null(result.InfraObjectName);
        Assert.Null(result.Properties);
    }



    #endregion
}
