﻿using ContinuityPatrol.Application.Features.Server.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events;

public class DeleteServerEventTests : IClassFixture<ServerFixture>, IClassFixture<UserActivityFixture>, IClassFixture<HeatMapStatusFixture>
{
    private readonly ServerFixture _serverFixture;

    private readonly HeatMapStatusFixture _heatMapStatusFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

   // private readonly Mock<IHeatMapStatusRepository> _mockHeatmapStatusRepository;

    private readonly ServerDeletedEventHandler _handler;

    public DeleteServerEventTests(ServerFixture serverFixture, UserActivityFixture userActivityFixture, HeatMapStatusFixture heatMapStatusFixture)
    {
        _serverFixture = serverFixture;

        _heatMapStatusFixture = heatMapStatusFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockServerEventLogger = new Mock<ILogger<ServerDeletedEventHandler>>();

        _mockUserActivityRepository = ServerRepositoryMocks.CreateServerEventRepository(_userActivityFixture.UserActivities);

       // _mockHeatmapStatusRepository = HeatMapStatusRepositoryMocks.GetHeatMapStatusRepository(_heatMapStatusFixture.HeatMapStatusList);

        _handler = new ServerDeletedEventHandler(mockLoggedInUserService.Object, mockServerEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteServerEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_serverFixture.ServerDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_serverFixture.ServerDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}