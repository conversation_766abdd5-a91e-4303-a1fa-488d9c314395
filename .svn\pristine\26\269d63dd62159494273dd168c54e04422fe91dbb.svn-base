﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.AlertInformation.Queries.GetPaginatedList;

public class GetAlertInformationPaginatedListQueryHandler : IRequestHandler<GetAlertInformationPaginatedListQuery,
    PaginatedResult<AlertInformationListVm>>
{
    private readonly IAlertInformationRepository _alertInformationRepository;
    private readonly IMapper _mapper;

    public GetAlertInformationPaginatedListQueryHandler(IAlertInformationRepository alertInformationRepository,
        IMapper mapper)
    {
        _alertInformationRepository = alertInformationRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<AlertInformationListVm>> Handle(
        GetAlertInformationPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new AlertInformationFilterSpecification(request.SearchString);

        var queryable =await _alertInformationRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec,request.SortColumn,request.SortOrder);
        
        var reportsList=_mapper.Map<PaginatedResult<AlertInformationListVm>>(queryable);

        return reportsList;
        //var queryable = _alertInformationRepository.PaginatedListAllAsync();

        //var productFilterSpec = new AlertInformationFilterSpecification(request.SearchString);

        //var reportsList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<AlertInformationListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return reportsList;
    }
}