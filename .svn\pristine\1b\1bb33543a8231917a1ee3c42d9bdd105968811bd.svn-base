﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;

public class GetAlertMasterDetailQueryHandler : IRequestHandler<GetAlertMasterDetailQuery, AlertMasterDetailVm>
{
    private readonly IAlertMasterRepository _alertMasterRepository;
    private readonly IMapper _mapper;

    public GetAlertMasterDetailQueryHandler(IAlertMasterRepository alertMasterRepository, IMapper mapper)
    {
        _alertMasterRepository = alertMasterRepository;
        _mapper = mapper;
    }

    public async Task<AlertMasterDetailVm> Handle(GetAlertMasterDetailQuery request,
        CancellationToken cancellationToken)
    {
        var alertMaster = await _alertMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(alertMaster, nameof(Domain.Entities.AlertMaster),
            new NotFoundException(nameof(Domain.Entities.AlertMaster), request.Id));

        var alertMasterDetailDto = _mapper.Map<AlertMasterDetailVm>(alertMaster);

        return alertMasterDetailDto;
    }
}