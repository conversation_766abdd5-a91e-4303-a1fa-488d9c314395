﻿using ContinuityPatrol.Application.Features.ServerSubType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerSubType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ServerType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerType.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ServerTypeControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<IDataProvider> _mockProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<ILogger<ServerMappingController>> _mockLogger = new();
        private ServerMappingController _controller;

        public ServerTypeControllerShould()
        {
            Initialize();
        }

        public void Initialize()
        {
            _controller = new ServerMappingController(
                _mockLogger.Object,
                _mockProvider.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        // ===== LIST METHOD TESTS =====

        [Fact]
        public async Task List_ReturnsViewResult_AndPublishesEvent()
        {
            // Act
            var result = await _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<ServerTypePaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task List_LogsDebugMessage()
        {
            // Act
            await _controller.List();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering List method in  ServerType")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== GET PAGINATION METHOD TESTS =====

        [Fact]
        public async Task GetPagination_ReturnsJsonWithSuccess_WhenDataRetrievedSuccessfully()
        {
            // Arrange
            var query = new GetServerSubTypePaginatedListQuery();
            var expectedData = new PaginatedResult<ServerSubTypeListVm>(true, new List<ServerSubTypeListVm>(), null, 0, 1, 10);
            _mockProvider.Setup(p => p.ServerSubType.GetPaginatedServerSubTypes(query))
                .ReturnsAsync(expectedData);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var query = new GetServerSubTypePaginatedListQuery();
            var exception = new Exception("Test exception");
            _mockProvider.Setup(p => p.ServerSubType.GetPaginatedServerSubTypes(It.IsAny<GetServerSubTypePaginatedListQuery>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_LogsDebugAndExceptionMessages()
        {
            // Arrange
            var query = new GetServerSubTypePaginatedListQuery();
            var exception = new Exception("Test exception");
            _mockProvider.Setup(p => p.ServerSubType.GetPaginatedServerSubTypes(It.IsAny<GetServerSubTypePaginatedListQuery>()))
                .ThrowsAsync(exception);

            // Act
            await _controller.GetPagination(query);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Entering GetPagination method in  ServerType")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        // ===== SERVER SUB TYPE CREATE OR UPDATE METHOD TESTS =====

        [Fact]
        public async Task ServerSubTypeCreateOrUpdate_CreatesNewRecord_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new ServerSubTypeListVm { Name = "Test SubType" };
            var createCommand = new CreateServerSubTypeCommand { Name = "Test SubType" };
            var expectedResult = new BaseResponse { Success = true };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerSubTypeCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.ServerSubType.CreateAsync(createCommand)).ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.ServerSubTypeCreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            _mockProvider.Verify(p => p.ServerSubType.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task ServerSubTypeCreateOrUpdate_UpdatesExistingRecord_WhenIdIsProvided()
        {
            // Arrange
            var viewModel = new ServerSubTypeListVm { Name = "Updated SubType" };
            var updateCommand = new UpdateServerSubTypeCommand { Name = "Updated SubType" };
            var expectedResult = new BaseResponse { Success = true };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Existing ID for update
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<UpdateServerSubTypeCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.ServerSubType.UpdateAsync(updateCommand)).ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.ServerSubTypeCreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            _mockProvider.Verify(p => p.ServerSubType.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task ServerSubTypeCreateOrUpdate_ReturnsRedirectToList_WhenValidationExceptionOccurs()
        {
            // Arrange
            var viewModel = new ServerSubTypeListVm { Name = "Test SubType" };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerSubTypeCommand>(viewModel)).Returns(new CreateServerSubTypeCommand());
            _mockProvider.Setup(p => p.ServerSubType.CreateAsync(It.IsAny<CreateServerSubTypeCommand>()))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.ServerSubTypeCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task ServerSubTypeCreateOrUpdate_ReturnsRedirectToList_WhenGeneralExceptionOccurs()
        {
            // Arrange
            var viewModel = new ServerSubTypeListVm { Name = "Test SubType" };
            var exception = new Exception("Database error");

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerSubTypeCommand>(viewModel)).Returns(new CreateServerSubTypeCommand());
            _mockProvider.Setup(p => p.ServerSubType.CreateAsync(It.IsAny<CreateServerSubTypeCommand>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.ServerSubTypeCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        // ===== SERVER SUB TYPE IS NAME EXIST METHOD TESTS =====

        [Fact]
        public async Task ServerSubTypeIsNameExist_ReturnsTrue_WhenNameExists()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            _mockProvider.Setup(p => p.ServerSubType.IsServerSubTypeExist(serverType, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.serversubtypeIsNameExist(serverType, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ServerSubTypeIsNameExist_ReturnsFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            _mockProvider.Setup(p => p.ServerSubType.IsServerSubTypeExist(serverType, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.serversubtypeIsNameExist(serverType, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task ServerSubTypeIsNameExist_ReturnsFalse_WhenExceptionOccurs()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            var exception = new Exception("Database error");
            _mockProvider.Setup(p => p.ServerSubType.IsServerSubTypeExist(serverType, id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.serversubtypeIsNameExist(serverType, id);

            // Assert
            Assert.False(result);
        }

        // ===== SERVER SUB TYPE DELETE METHOD TESTS =====

        [Fact]
        public async Task ServerSubTypeDelete_ReturnsJsonWithSuccess_WhenDeleteSuccessful()
        {
            // Arrange
            var serverSubTypeId = "123";
            var expectedResult = new BaseResponse { Success = true };
            _mockProvider.Setup(p => p.ServerSubType.DeleteAsync(serverSubTypeId))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.serversubTypeDelete(serverSubTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task ServerSubTypeDelete_ReturnsJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var serverSubTypeId = "123";
            var exception = new Exception("Delete failed");
            _mockProvider.Setup(p => p.ServerSubType.DeleteAsync(serverSubTypeId))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.serversubTypeDelete(serverSubTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== GET SERVER TYPE LIST METHOD TESTS =====

        [Fact]
        public async Task GetServerTypeList_ReturnsJsonWithSuccess_WhenDataRetrievedSuccessfully()
        {
            // Arrange
            var expectedData = new List<ServerTypeListVm>();
            _mockProvider.Setup(p => p.ServerType.GetServerTypeList())
                .ReturnsAsync(expectedData);

            // Act
            var result = await _controller.GetServerTypeList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"data\":", json);
        }

        [Fact]
        public async Task GetServerTypeList_ReturnsJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockProvider.Setup(p => p.ServerType.GetServerTypeList())
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetServerTypeList();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== SERVER TYPE CREATE OR UPDATE METHOD TESTS =====

        [Fact]
        public async Task ServerTypeCreateOrUpdate_CreatesNewRecord_WhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new ServerTypeListVm { Name = "Test Type" };
            var createCommand = new CreateServerTypeCommand { Name = "Test Type" };
            var expectedResult = new BaseResponse { Success = true };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty for create
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerTypeCommand>(viewModel)).Returns(createCommand);
            _mockProvider.Setup(p => p.ServerType.CreateAsync(createCommand)).ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.serverTypeCreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            _mockProvider.Verify(p => p.ServerType.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task ServerTypeCreateOrUpdate_UpdatesExistingRecord_WhenIdIsProvided()
        {
            // Arrange
            var viewModel = new ServerTypeListVm { Name = "Updated Type" };
            var updateCommand = new UpdateServerTypeCommand { Name = "Updated Type" };
            var expectedResult = new BaseResponse { Success = true };

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Existing ID for update
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<UpdateServerTypeCommand>(viewModel)).Returns(updateCommand);
            _mockProvider.Setup(p => p.ServerType.UpdateAsync(updateCommand)).ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.serverTypeCreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
            _mockProvider.Verify(p => p.ServerType.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task ServerTypeCreateOrUpdate_ReturnsRedirectToList_WhenValidationExceptionOccurs()
        {
            // Arrange
            var viewModel = new ServerTypeListVm { Name = "Test Type" };
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerTypeCommand>(viewModel)).Returns(new CreateServerTypeCommand());
            _mockProvider.Setup(p => p.ServerType.CreateAsync(It.IsAny<CreateServerTypeCommand>()))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.serverTypeCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task ServerTypeCreateOrUpdate_ReturnsRedirectToList_WhenGeneralExceptionOccurs()
        {
            // Arrange
            var viewModel = new ServerTypeListVm { Name = "Test Type" };
            var exception = new Exception("Database error");

            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.ControllerContext.HttpContext.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateServerTypeCommand>(viewModel)).Returns(new CreateServerTypeCommand());
            _mockProvider.Setup(p => p.ServerType.CreateAsync(It.IsAny<CreateServerTypeCommand>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.serverTypeCreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        // ===== SERVER TYPE IS NAME EXIST METHOD TESTS =====

        [Fact]
        public async Task ServerTypeIsNameExist_ReturnsTrue_WhenNameExists()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            _mockProvider.Setup(p => p.ServerType.IsServerTypeExist(serverType, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.servertypeIsNameExist(serverType, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ServerTypeIsNameExist_ReturnsFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            _mockProvider.Setup(p => p.ServerType.IsServerTypeExist(serverType, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.servertypeIsNameExist(serverType, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task ServerTypeIsNameExist_ReturnsFalse_WhenExceptionOccurs()
        {
            // Arrange
            var serverType = "TestType";
            var id = "123";
            var exception = new Exception("Database error");
            _mockProvider.Setup(p => p.ServerType.IsServerTypeExist(serverType, id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.servertypeIsNameExist(serverType, id);

            // Assert
            Assert.False(result);
        }

        // ===== SERVER TYPE DELETE METHOD TESTS =====

        [Fact]
        public async Task ServerTypeDelete_ReturnsJsonWithSuccess_WhenDeleteSuccessful()
        {
            // Arrange
            var serverTypeId = "123";
            var expectedResult = new BaseResponse { Success = true };
            _mockProvider.Setup(p => p.ServerType.DeleteAsync(serverTypeId))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.serverTypeDelete(serverTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task ServerTypeDelete_ReturnsJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var serverTypeId = "123";
            var exception = new Exception("Delete failed");
            _mockProvider.Setup(p => p.ServerType.DeleteAsync(serverTypeId))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.serverTypeDelete(serverTypeId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        // ===== CONSTRUCTOR AND SETUP TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new ServerMappingController(
                _mockLogger.Object,
                _mockProvider.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(ServerMappingController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldImplementControllerBase()
        {
            // Act
            var controller = new ServerMappingController(
                _mockLogger.Object,
                _mockProvider.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );

            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        // ===== METHOD ATTRIBUTE TESTS =====

        [Fact]
        public void ServerSubTypeCreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(ServerMappingController);
            var method = controllerType.GetMethod("ServerSubTypeCreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void ServerTypeCreateOrUpdate_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(ServerMappingController);
            var method = controllerType.GetMethod("serverTypeCreateOrUpdate");

            // Act
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(httpPostAttribute);
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void ServerSubTypeDelete_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(ServerMappingController);
            var method = controllerType.GetMethod("serversubTypeDelete");

            // Act
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public void ServerTypeDelete_ShouldHaveCorrectAttributes()
        {
            // Arrange
            var controllerType = typeof(ServerMappingController);
            var method = controllerType.GetMethod("serverTypeDelete");

            // Act
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();
            var antiXssAttribute = method.GetCustomAttributes(typeof(AntiXssAttribute), false).FirstOrDefault();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
            Assert.NotNull(antiXssAttribute);
        }

        // ===== EDGE CASE AND ERROR HANDLING TESTS =====

        [Fact]
        public async Task List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public async Task GetPagination_WithNullQuery_ShouldNotThrowException()
        {
            // Arrange
            GetServerSubTypePaginatedListQuery query = null;

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _controller.GetPagination(query));
            Assert.Null(exception);
        }

        [Fact]
        public async Task ServerSubTypeIsNameExist_WithNullParameters_ReturnsFalse()
        {
            // Act
            var result1 = await _controller.serversubtypeIsNameExist(null, "123");
            var result2 = await _controller.serversubtypeIsNameExist("TestType", null);
            var result3 = await _controller.serversubtypeIsNameExist(null, null);

            // Assert
            Assert.False(result1);
            Assert.False(result2);
            Assert.False(result3);
        }

        [Fact]
        public async Task ServerTypeIsNameExist_WithNullParameters_ReturnsFalse()
        {
            // Act
            var result1 = await _controller.servertypeIsNameExist(null, "123");
            var result2 = await _controller.servertypeIsNameExist("TestType", null);
            var result3 = await _controller.servertypeIsNameExist(null, null);

            // Assert
            Assert.False(result1);
            Assert.False(result2);
            Assert.False(result3);
        }
    }
}
