using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberJobWorkflowSchedulerFixture : IDisposable
{

    public List<CyberJobWorkflowScheduler> CyberJobWorkflowSchedulerPaginationList { get; set; }
    public List<CyberJobWorkflowScheduler> CyberJobWorkflowSchedulerList { get; set; }
    public CyberJobWorkflowScheduler CyberJobWorkflowSchedulerDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberJobWorkflowSchedulerFixture()
    {
        var fixture = new Fixture();

        CyberJobWorkflowSchedulerList = fixture.Create<List<CyberJobWorkflowScheduler>>();
        CyberJobWorkflowSchedulerPaginationList = fixture.CreateMany<CyberJobWorkflowScheduler>(20).ToList();
       
        CyberJobWorkflowSchedulerDto = fixture.Create<CyberJobWorkflowScheduler>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
