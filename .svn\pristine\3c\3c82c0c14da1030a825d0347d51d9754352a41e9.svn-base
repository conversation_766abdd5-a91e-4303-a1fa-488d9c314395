using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Events.Delete;

public class BulkImportOperationDeletedEventHandler : INotificationHandler<BulkImportOperationDeletedEvent>
{
    private readonly ILogger<BulkImportOperationDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BulkImportOperationDeletedEventHandler(ILoggedInUserService userService,
        ILogger<BulkImportOperationDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BulkImportOperationDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} BulkImportOperation",
            Entity = "BulkImportOperation",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"BulkImportOperation '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BulkImportOperation '{deletedEvent.Name}' deleted successfully.");
    }
}