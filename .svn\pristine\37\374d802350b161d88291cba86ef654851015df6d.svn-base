﻿using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Delete;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Delete;
using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Delete;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace ContinuityPatrol.Application.Helper;

public class BulkImportHelperService
{
    private const string Chars =
        "ABCDEFGHIJKPERPETUUITILMANISANKARNOPQRSTUVWXYZ0123456789ABCDEFGHIJKPERPETUUITILMNOPQRSTUVWXYZ";

    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IInfraObjectSchedulerRepository _infraObjectSchedulerRepository;
    private readonly IJobRepository _jobRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<BulkImportHelperService> _logger;
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IJobScheduler _client;

    public BulkImportHelperService(IMapper mapper, IServerRepository serverRepository,
        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository,
        IInfraObjectRepository infraObjectRepository, IPublisher publisher, ILoggedInUserService loggedInUserService,
        ILicenseManagerRepository licenseManagerRepository, ISiteRepository siteRepository,
        ISiteTypeRepository siteTypeRepository, ILoadBalancerRepository loadBalancerRepository,
        IBulkImportActionResultRepository bulkImportActionResultRepository,
        ILicenseValidationService licenseValidationService,
        IBulkImportOperationGroupRepository bulkImportOperationGroupRepository, 
        IWorkflowRepository workflowRepository,
        INodeRepository nodeRepository, IJobRepository jobRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IInfraObjectSchedulerRepository infraObjectSchedulerRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository,
        IWorkflowHistoryRepository workflowHistoryRepository, IVersionManager versionManager,
        IHubContext<NotificationHub> hubContext, ILogger<BulkImportHelperService> logger,
        ICyberAirGapRepository cyberAirGapRepository, IJobScheduler client)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _replicationRepository = replicationRepository;
        _infraObjectRepository = infraObjectRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
        _licenseManagerRepository = licenseManagerRepository;
        _siteRepository = siteRepository;
        _siteTypeRepository = siteTypeRepository;
        _loadBalancerRepository = loadBalancerRepository;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
        _licenseValidationService = licenseValidationService;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
        _workflowRepository = workflowRepository;
        _nodeRepository = nodeRepository;
        _jobRepository = jobRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _infraObjectSchedulerRepository = infraObjectSchedulerRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _workflowHistoryRepository = workflowHistoryRepository;
        _versionManager = versionManager;
        _hubContext = hubContext;
        _logger = logger;
        _cyberAirGapRepository = cyberAirGapRepository;
        _client = client;
    }


    public async Task CreateServer(List<CreateBulkDataServerListCommand> servers, string operationId,
        string operationGroupId, CancellationToken cancellationToken)
    {
        foreach (var server in servers)
        {
            await Task.Delay(2000, cancellationToken);

            var startDate = DateTime.Now;

            var serverName = await _serverRepository.GetServerByServerName(server.Name);

            var serverDto = _mapper.Map<Server>(server);

            var serverValidation = new BulkImportCreateServerValidationCommand(_serverRepository);

            var validationResult = await serverValidation.ValidateAsync(server, cancellationToken);

            if (validationResult.Errors.Any())
                await CreateBulkImportActionResult(serverName?.ReferenceId ?? "", serverDto.Name, "Server", "Error",
                    startDate, validationResult.Errors?.FirstOrDefault()?.ToString(), operationId, operationGroupId);

            var isValid = await ValidateServerLicense(serverDto, operationId, operationGroupId);

            if (isValid)
            {
                var serverCommandHandler =
                    new CreateServerCommandHandler(_mapper, _serverRepository, _publisher, _loggedInUserService,
                        _licenseManagerRepository, _siteRepository, _siteTypeRepository);

                var mapServerCommand = _mapper.Map<CreateServerCommand>(server);

                var updateBaseResponse =
                    await serverCommandHandler.Handle(mapServerCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ServerId, serverDto.Name, "Server", "Success",
                        startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            else
            {
                await CreateBulkImportActionResult(serverName?.ReferenceId ?? "", serverDto.Name, "Server", "Error",
                    startDate, "Server count reached maximum limit.", operationId, operationGroupId);
            }
        }
    }


    public async Task CreateDatabase(List<CreateBulkDataDataBaseListCommand> databases, string operationId,
        string operationGroupId, CancellationToken cancellationToken)
    {
        foreach (var dataBaseItem in databases)
        {
            await Task.Delay(2000, cancellationToken);

            var startDate = DateTime.Now;

            var server = await _serverRepository.GetServerByServerName(dataBaseItem.ServerName);

            if (server.IsNull())
                await CreateBulkImportActionResult("", dataBaseItem.Name, "Database", "Error", startDate,
                    $"Server '{dataBaseItem.ServerName}' not configured.", operationId, operationGroupId);

            dataBaseItem.ServerId = server?.ReferenceId ?? string.Empty;

            var databaseValidation = new BulkImportCreateDatabaseCommandValidator(_databaseRepository);

            var validationResult = await databaseValidation.ValidateAsync(dataBaseItem, cancellationToken);

            var dataBase = _mapper.Map<Database>(dataBaseItem);

            if (validationResult.Errors.Any())
                await CreateBulkImportActionResult(dataBase?.ReferenceId ?? "", dataBase?.Name, "Database", "Error",
                    startDate, validationResult.Errors.FirstOrDefault()?.ToString(), operationId, operationGroupId);

            var isValid = await ValidateDatabaseLicense(dataBase, operationId, operationGroupId);

            if (isValid)
            {
                var databaseCommandHandler =
                    new CreateDatabaseCommandHandler(_mapper, _databaseRepository, _publisher, _loggedInUserService,
                        _licenseManagerRepository, _serverRepository, _siteRepository, _siteTypeRepository);

                var mapDatabaseCommand = _mapper.Map<CreateDatabaseCommand>(dataBaseItem);

                var updateBaseResponse =
                    await databaseCommandHandler.Handle(mapDatabaseCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.DatabaseId, dataBase?.Name, "Database",
                        "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            else
            {
                await CreateBulkImportActionResult(dataBase?.ReferenceId ?? "", dataBase?.Name, "Database", "Error",
                    startDate, "Database count reached maximum limit.", operationId, operationGroupId);
            }
        }
    }

    public async Task CreateReplication(List<CreateBulkDataReplicationListCommand> replications, string operationId,
        string operationGroupId, CancellationToken cancellationToken)
    {
        foreach (var replicationItem in replications)
        {
            await Task.Delay(2000, cancellationToken);

            var startDate = DateTime.Now;

            var replicationValidation = new BulkImportCreateReplicationCommandValidator(_replicationRepository);

            var validationResult = await replicationValidation.ValidateAsync(replicationItem, cancellationToken);

            var replication = _mapper.Map<Replication>(replicationItem);

            if (validationResult.Errors.Any())
                await CreateBulkImportActionResult(replication?.ReferenceId ?? "", replication?.Name, "Replication",
                    "Error", startDate, validationResult.Errors.FirstOrDefault()?.ToString(), operationId,
                    operationGroupId);

            var isValid = await ValidateReplicationLicense(replication, operationId, operationGroupId);

            if (isValid)
            {
                var replicationCommandHandler = new CreateReplicationCommandHandler(_mapper, _replicationRepository,
                    _publisher, _loggedInUserService, _siteRepository, _siteTypeRepository);

                var mapReplicationCommand = _mapper.Map<CreateReplicationCommand>(replicationItem);

                var updateBaseResponse =
                    await replicationCommandHandler.Handle(mapReplicationCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ReplicationId, replication?.Name,
                        "Replication", "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            else
            {
                await CreateBulkImportActionResult(replication?.ReferenceId ?? "", replication?.Name, "Replication",
                    "Error", startDate, "Replication count reached maximum limit.", operationId, operationGroupId);
            }
        }
    }

    public async Task CreateInfraObject(CreateBulkImportOperationListCommand bulkImport, string operationId,
        string operationGroupId, CancellationToken cancellationToken)
    {
        var startDate = DateTime.Now;

        ValidationResult validationResult;

        await Task.Delay(2000, cancellationToken);

        foreach (var server in bulkImport.ServerList)
        {
            var serverName = await _serverRepository.GetServerByServerName(server.Name);

            if (serverName.IsNull())
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                    $"The {bulkImport.InfraObject.Name} infraObject was not created because the server '{server.Name}' not configured.",
                    operationId, operationGroupId);

            bulkImport.InfraObject.ServerProperties = bulkImport.InfraObject.ServerProperties
                .Replace($"@{serverName.Name}", serverName.ReferenceId);

            if (bulkImport.IsSwitchOver)
                bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);

            if (bulkImport.IsSwitchBack)
                bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);

            if (bulkImport.IsFailOver)
                bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);

            if (bulkImport.IsFailBack)
                bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);


            bulkImport.InfraObject.PRServerId = server.ServerType.ToLower().Contains("pr")
                ? serverName.ReferenceId
                : bulkImport.InfraObject.PRServerId;

            bulkImport.InfraObject.DRServerId = server.ServerType.ToLower().Contains("dr")
                ? serverName.ReferenceId
                : bulkImport.InfraObject.DRServerId;
        }

        foreach (var database in bulkImport.DatabaseList)
        {
            var databaseNames = await _databaseRepository.GetDatabaseNames();

            var databaseName = databaseNames.FirstOrDefault(x => x.Name == database.Name);

            if (databaseName.IsNull())
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                    $"The {bulkImport.InfraObject.Name} infraObject was not created because the database '{database.Name}' not configured.",
                    operationId, operationGroupId);

            bulkImport.InfraObject.DatabaseProperties = bulkImport.InfraObject.DatabaseProperties
                .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);

            if (bulkImport.IsSwitchOver)
                bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                    .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


            if (bulkImport.IsSwitchBack)
                bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                    .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


            if (bulkImport.IsFailOver)
                bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                    .Replace($"@{databaseName.Name}", databaseName.ReferenceId);

            if (bulkImport.IsFailBack)
                bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                    .Replace($"@{databaseName.Name}", databaseName.ReferenceId);


            bulkImport.InfraObject.PRDatabaseId = database.Type.ToLower().Contains("pr")
                ? databaseName?.ReferenceId
                : bulkImport.InfraObject.PRDatabaseId;

            bulkImport.InfraObject.DRDatabaseId = database.Type.ToLower().Contains("dr")
                ? databaseName?.ReferenceId
                : bulkImport.InfraObject.DRDatabaseId;
        }


        foreach (var replication in bulkImport.ReplicationList)
        {
            var replicationNames = await _replicationRepository.GetReplicationNames();

            var replicationName = replicationNames.FirstOrDefault(x => x.Name == replication.Name);

            if (replicationName.IsNull())
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                    $"The {bulkImport.InfraObject.Name} infraObject was not created because the replication '{replication.Name}' not configured.",
                    operationId, operationGroupId);

            bulkImport.InfraObject.ReplicationProperties = bulkImport.InfraObject.ReplicationProperties
                .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);

            if (bulkImport.IsSwitchOver)
                bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                    .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


            if (bulkImport.IsSwitchBack)
                bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                    .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


            if (bulkImport.IsFailOver)
                bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                    .Replace($"@{replicationName.Name}", replicationName.ReferenceId);

            if (bulkImport.IsFailBack)
                bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                    .Replace($"@{replicationName.Name}", replicationName.ReferenceId);


            bulkImport.InfraObject.PRReplicationId =
                replicationName.Name.Equals(bulkImport.InfraObject.PRReplicationName)
                    ? replicationName?.ReferenceId
                    : bulkImport.InfraObject.PRReplicationId;

            bulkImport.InfraObject.DRReplicationId =
                replicationName.Name.Equals(bulkImport.InfraObject.DRReplicationName)
                    ? replicationName?.ReferenceId
                    : bulkImport.InfraObject.DRReplicationId;
        }


        var infraObjectValidation = new BulkImportCreateInfraObjectCommandValidator(_infraObjectRepository);

        //try
        //{
        validationResult = await infraObjectValidation.ValidateAsync(bulkImport.InfraObject, cancellationToken);
        //}
        //catch (Exception ex)
        //{
        //    await CreateBulkImportActionResult(string.Empty, bulkImport.InfraObject.Name, "InfraObject", "Error", startDate, ex.GetMessage(), operationId, operationGroupId);

        //    throw new InvalidException(ex.GetMessage());
        //}


        var infraObject = _mapper.Map<InfraObject>(bulkImport.InfraObject);

        if (validationResult.Errors.Any())
            await CreateBulkImportActionResult(infraObject?.ReferenceId ?? "", infraObject?.Name, "InfraObject",
                "Error", startDate, validationResult.Errors.FirstOrDefault()?.ToString(), operationId,
                operationGroupId);

        var infraObjectCommandHandler = new CreateInfraObjectCommandHandler(_mapper, _infraObjectRepository,
            _publisher, _loggedInUserService, _loadBalancerRepository,_client);

        var mapInfraObjectCommand = _mapper.Map<CreateInfraObjectCommand>(bulkImport.InfraObject);

        var updateBaseResponse = await infraObjectCommandHandler.Handle(mapInfraObjectCommand, cancellationToken);

        if (updateBaseResponse.Success)
            await CreateBulkImportActionResult(updateBaseResponse.InfraObjectId, infraObject?.Name, "InfraObject",
                "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);

        if (bulkImport.IsFailBack || bulkImport.IsFailOver || bulkImport.IsSwitchBack || bulkImport.IsSwitchOver)
        {
            await Task.Delay(2000, cancellationToken);

            await CreateWorkflow(bulkImport, operationId, operationGroupId, cancellationToken);
        }
    }


    public async Task CreateWorkflow(CreateBulkImportOperationListCommand bulkImport, string operationId,
        string operationGroupId, CancellationToken cancellationToken)
    {
        var workflowCommandHandler = new CreateWorkflowCommandHandler(_mapper, _workflowRepository,
            _workflowHistoryRepository, _loggedInUserService, _publisher, _versionManager, _hubContext);

        var actionType = string.Empty;

        var workflowActions = new List<(bool IsActive, string Prefix, string Properties)>
        {
            (bulkImport.IsSwitchOver, "SO", bulkImport.SwitchOverTemplate),
            (bulkImport.IsSwitchBack, "SB", bulkImport.SwitchBackTemplate),
            (bulkImport.IsFailOver, "FO", bulkImport.FailOverTemplate),
            (bulkImport.IsFailBack, "FB", bulkImport.FailBackTemplate)
        };

        var filterWorkflows = new List<(bool IsActive, string Prefix, string Properties)>();

        var activeWorkflowActions = workflowActions
            .Where(action => action.IsActive)
            .ToList();

        foreach (var removeAction in activeWorkflowActions)
        {
            var actionResultWorkflowRunningList =
                await _bulkImportActionResultRepository.GetActionByOperationGroupIdAndEntityType(operationGroupId,
                    $"{removeAction.Prefix}Workflow");

            if (actionResultWorkflowRunningList is null) filterWorkflows.Add(removeAction);
        }

        foreach (var action in filterWorkflows)
        {
            var infraObject = await _infraObjectRepository.GetInfraObjectByName(bulkImport.InfraObject.Name);

            actionType = $"{action.Prefix}Workflow";

            if (infraObject.IsNull())
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, $"{action.Prefix}Workflow", "Error",
                    DateTime.Now,
                    $"The {action.Prefix} workflow was not created because the infraObject '{bulkImport.InfraObject.Name}' is not configured."
                    , operationId, operationGroupId);

            var randomString =
                string.Concat(Enumerable.Range(0, 6).Select(_ => Chars[new Random().Next(Chars.Length)]));

            var name = $"{bulkImport.InfraObject.ReplicationTypeName}_{action.Prefix}_{randomString}";
            
            var response = await workflowCommandHandler.Handle(new CreateWorkflowCommand
            {
                Name = name,
                Properties = action.Properties,
                CompanyId = bulkImport.InfraObject.CompanyId,
                IsPublish = true
            }, CancellationToken.None);

            if (response.Success)
                await CreateBulkImportActionResult(response.WorkflowId, name, actionType, "Success", DateTime.Now,
                    response.Message, operationId, operationGroupId);
        }
    }

    public async Task DeleteServer(dynamic servers, CancellationToken cancellationToken)
    {
        foreach (var server in servers)
        {
            var startDate = DateTime.Now;

            var serverCommandHandler =
                new DeleteServerCommandHandler(_serverRepository, _databaseRepository, _publisher, _workflowRepository,
                    _nodeRepository, _cyberAirGapRepository);

            var response =
                await serverCommandHandler.Handle(new DeleteServerCommand { Id = server.ServerId }, cancellationToken);

            if (response.Success)
                await CreateBulkImportActionResult(server.ServerId, server.ServerName, "Server", "Success", startDate,
                    response.Message, server.OperationId, server.OperationGroupId);
        }
    }

    public async Task DeleteDatabase(dynamic databases, CancellationToken cancellationToken)
    {
        foreach (var database in databases)
        {
            var startDate = DateTime.Now;

            var serverCommandHandler =
                new DeleteDatabaseCommandHandler(_databaseRepository, _publisher, _infraObjectRepository,
                    _workflowRepository, _loggedInUserService);

            var response = await serverCommandHandler.Handle(new DeleteDatabaseCommand { Id = database.DatabaseId },
                cancellationToken);

            if (response.Success)
                await CreateBulkImportActionResult(database.DatabaseId, database.DatabaseName, "Database", "Success",
                    startDate, response.Message, database.OperationId, database.OperationGroupId);
        }
    }

    public async Task DeleteReplication(dynamic replications, CancellationToken cancellationToken)
    {
        foreach (var replication in replications)
        {
            var startDate = DateTime.Now;

            var serverCommandHandler =
                new DeleteReplicationCommandHandler(_replicationRepository, _publisher, _infraObjectRepository,
                    _workflowRepository);

            var response =
                await serverCommandHandler.Handle(new DeleteReplicationCommand { Id = replication.ReplicationId },
                    cancellationToken);

            if (response.Success)
                await CreateBulkImportActionResult(replication.ReplicationId, replication.ReplicationName,
                    "Replication", "Success", startDate, response.Message, replication.OperationId,
                    replication.OperationGroupId);
        }
    }

    public async Task DeleteInfraObject(dynamic infraObject, CancellationToken cancellationToken)
    {
        foreach (var infra in infraObject)
        {
            var startDate = DateTime.Now;

            var serverCommandHandler =
                new DeleteInfraObjectCommandHandler(_infraObjectRepository, _publisher, _jobRepository,
                    _workflowInfraObjectRepository, _infraObjectSchedulerRepository, _workflowProfileInfoRepository);

            var response = await serverCommandHandler.Handle(new DeleteInfraObjectCommand { Id = infra.InfraObjectId },
                cancellationToken);

            if (response.Success)
                await CreateBulkImportActionResult(infra.InfraObjectId, infra.InfraObjectName, "InfraObject", "Success",
                    startDate, response.Message, infra.OperationId, infra.OperationGroupId);
        }
    }

    private async Task CreateBulkImportActionResult(string entityId,
        string entityName, string entityType, string status,
        DateTime startTime, string error, string operationId = null, string operationGroupId = null)
    {
        //await Task.Delay(2000);
        var bulkImportGroup = await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(operationGroupId);

        if (entityId.IsNotNullOrEmpty())
        {
            var bulkImportActionResult =
                await _bulkImportActionResultRepository.GetByEntityIdAndBulkImportOperationId(entityId, operationId);
            if (bulkImportActionResult != null)
            {
                bulkImportActionResult.ReferenceId = bulkImportActionResult.ReferenceId;
                bulkImportActionResult.BulkImportOperationId = bulkImportActionResult.BulkImportOperationId;
                bulkImportActionResult.BulkImportOperationGroupId = bulkImportActionResult.BulkImportOperationGroupId;
                bulkImportActionResult.EntityId = entityId;
                bulkImportActionResult.EntityName = entityName;
                bulkImportActionResult.EntityType = entityType;
                bulkImportActionResult.NodeId = bulkImportGroup.NodeId;
                bulkImportActionResult.Status = status;
                bulkImportActionResult.StartTime = startTime;
                bulkImportActionResult.EndTime = DateTime.Now;
                bulkImportActionResult.ErrorMessage = error;

                await _bulkImportActionResultRepository.UpdateAsync(bulkImportActionResult);
            }
            else
            {
                await _bulkImportActionResultRepository.AddAsync(new BulkImportActionResult
                {
                    BulkImportOperationId = operationId,
                    BulkImportOperationGroupId = operationGroupId,
                    EntityId = entityId,
                    EntityName = entityName,
                    EntityType = entityType,
                    NodeId = bulkImportGroup.NodeId,
                    Status = status,
                    StartTime = startTime,
                    EndTime = DateTime.Now,
                    ErrorMessage = error
                });
            }
        }
        else
        {
            await _bulkImportActionResultRepository.AddAsync(new BulkImportActionResult
            {
                BulkImportOperationId = operationId,
                BulkImportOperationGroupId = operationGroupId,
                EntityId = entityId,
                EntityName = entityName,
                EntityType = entityType,
                NodeId = bulkImportGroup.NodeId,
                Status = status,
                StartTime = startTime,
                EndTime = DateTime.Now,
                ErrorMessage = error
            });
        }

        var bulkImportActionResultList =
            await _bulkImportActionResultRepository.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        var bulkImportOperationGroup =
            await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(operationGroupId);

        //await Task.Delay(2000);

        if (bulkImportOperationGroup is not null)
        {
            var splitProgressBar = bulkImportOperationGroup.ProgressStatus.IsNotNullOrWhiteSpace()
                ? bulkImportOperationGroup.ProgressStatus.Split('/')
                : Array.Empty<string>();

            var newProgressBar = splitProgressBar.Length > 0
                ? $"{bulkImportActionResultList.Count}/{splitProgressBar[1]}"
                : bulkImportOperationGroup.ProgressStatus;

            //await Task.Delay(2000);

            var eventToUpdate =
                await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(bulkImportOperationGroup.ReferenceId);
            eventToUpdate.ProgressStatus = status.Equals("error",StringComparison.OrdinalIgnoreCase) ? bulkImportOperationGroup.ProgressStatus : newProgressBar;
            eventToUpdate.Status = status;
            eventToUpdate.ErrorMessage = error;
            await _bulkImportOperationGroupRepository.UpdateAsync(eventToUpdate);
        }


        if (status.ToLower().Equals("error"))
            _logger.LogError($"EntityName '{entityName}' - {error}");
        else
            _logger.LogInformation($"EntityName '{entityName}' - {error}");


        if (status.ToLower().Equals("error")) throw new InvalidException(error);
    }

    private async Task<bool> ValidateServerLicense(Server serverDto, string operationId, string operationGroupId)
    {
        try
        {
            var site = await _siteRepository.GetByReferenceIdAsync(serverDto.SiteId);

            Guard.Against.NullOrDeactive(site, nameof(Site),
                new NotFoundException(nameof(Site), serverDto.SiteId));

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            Guard.Against.NullOrDeactive(siteType, nameof(SiteType),
                new NotFoundException(nameof(SiteType), site.TypeId));

            var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

            var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(serverDto.LicenseId);

            var serverCount =
                await _serverRepository.GetServerCountByLicenseKey(serverDto.LicenseId, serverDto.RoleType,
                    serverDto.SiteId);

            return await _licenseValidationService.IsServerLicenseCountExitMaxLimit(licenseManager, siteType,
                serverDto.RoleType, serverCount, index);
        }
        catch (Exception ex)
        {
            await CreateBulkImportActionResult(serverDto.ReferenceId, serverDto.Name, "Server", "Error", DateTime.Now,
                ex.GetMessage(), operationId, operationGroupId);

            throw new InvalidException(ex.GetMessage());
        }
    }

    private async Task<bool> ValidateDatabaseLicense(Database dataBase, string operationId, string operationGroupId)
    {
        try
        {
            if (dataBase.LicenseId.IsNullOrWhiteSpace()) return false;

            var server = await _serverRepository.GetByReferenceIdAsync(dataBase.ServerId);

            Guard.Against.NullOrDeactive(server, nameof(Server),
                new NotFoundException(nameof(Server), dataBase.ServerId));

            var site = await _siteRepository.GetByReferenceIdAsync(server.SiteId);

            Guard.Against.NullOrDeactive(site, nameof(Site),
                new NotFoundException(nameof(Site), server.SiteId));

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            Guard.Against.NullOrDeactive(siteType, nameof(SiteType),
                new NotFoundException(nameof(SiteType), site.TypeId));

            var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

            var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(dataBase.LicenseId) ??
                                 throw new InvalidException("License is null or empty.");

            var siteVm = await _siteRepository.GetSiteBySiteTypeId(siteType.ReferenceId);

            var siteIds = siteVm.Select(x => x.ReferenceId).ToList();

            var databaseCount =
                await _databaseRepository.GetDatabaseCountByLicenseKey(dataBase.LicenseId, siteIds);

            var licenseDtl =
                await _licenseValidationService.IsDatabaseLicenseCountExitMaxLimit(licenseManager, siteType,
                    databaseCount, index);

            return licenseDtl;
        }
        catch (Exception ex)
        {
            await CreateBulkImportActionResult(dataBase.ReferenceId, dataBase.Name, "Database", "Error", DateTime.Now,
                ex.GetMessage(), operationId, operationGroupId);

            throw new InvalidException(ex.GetMessage());
        }
    }

    public async Task<bool> ValidateReplicationLicense(Replication replication, string operationId,
        string operationGroupId)
    {
        try
        {
            if (!replication.Type.ToLower().Contains("perpetuuiti")) return true;

            if (replication.LicenseId.IsNullOrWhiteSpace()) return false;

            var site = await _siteRepository.GetByReferenceIdAsync(replication.SiteId);

            Guard.Against.NullOrDeactive(site, nameof(Site),
                new NotFoundException(nameof(Site), replication.SiteId));

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            Guard.Against.NullOrDeactive(siteType, nameof(SiteType),
                new NotFoundException(nameof(SiteType), site.TypeId));

            var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

            var license = await _licenseManagerRepository.GetLicenseDetailByIdAsync(replication.LicenseId);

            Guard.Against.NullOrDeactive(siteType, nameof(LicenseManager),
                new NotFoundException(nameof(LicenseManager), replication.LicenseId));

            var replicationCount =
                await _replicationRepository.GetReplicationCountByLicenseKey(replication.LicenseId, replication.SiteId);

            return await _licenseValidationService.IsReplicationLicenseCountExitMaxLimit(license, siteType,
                replicationCount, index);
        }
        catch (Exception ex)
        {
            await CreateBulkImportActionResult(replication.ReferenceId, replication.Name, "Replication", "Error",
                DateTime.Now, ex.GetMessage(), operationId, operationGroupId);

            throw new InvalidException(ex.GetMessage());
        }
    }
}