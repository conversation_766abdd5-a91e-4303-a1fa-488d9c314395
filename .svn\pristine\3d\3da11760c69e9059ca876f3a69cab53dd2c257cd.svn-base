﻿using ContinuityPatrol.Application.Features.DataSyncJob.Events.Update;

namespace ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;

public class UpdateDataSyncJobCommandHandler : IRequestHandler<UpdateDataSyncJobCommand, UpdateDataSyncJobResponse>
{
    private readonly IDataSyncJobRepository _dataSyncJobRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _Publisher;

    public UpdateDataSyncJobCommandHandler(IDataSyncJobRepository dataSyncJobRepository, IMapper mapper,
        IPublisher publisher)
    {
        _dataSyncJobRepository = dataSyncJobRepository;
        _mapper = mapper;
        _Publisher = publisher;
    }

    public async Task<UpdateDataSyncJobResponse> Handle(UpdateDataSyncJobCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dataSyncJobRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(DataSyncJob), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDataSyncJobCommand), typeof(Domain.Entities.DataSyncJob));

        await _dataSyncJobRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDataSyncJobResponse
        {
            Message = Message.Update(nameof(Domain.Entities.DataSyncJob), eventToUpdate.ReplicationName),

            Id = eventToUpdate.ReferenceId
        };
        await _Publisher.Publish(new DataSyncJobUpdatedEvent { Name = eventToUpdate.ReplicationName },
            cancellationToken);

        return response;
    }
}