﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.Update;

public class DatabaseUpdatedEventHandler : INotificationHandler<DatabaseUpdatedEvent>
{
    private readonly ILogger<DatabaseUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DatabaseUpdatedEventHandler(ILoggedInUserService userService, ILogger<DatabaseUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DatabaseUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Action = $"{ActivityType.Update} {Modules.Database}",
            Entity = Modules.Database.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Database '{updatedEvent.DatabaseName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Database '{updatedEvent.DatabaseName}' updated successfully.");
    }
}