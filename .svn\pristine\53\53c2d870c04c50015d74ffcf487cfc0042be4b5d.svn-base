﻿
//document.getElementById("tableData").innerHTML = counts;

let solutionChart, solutionSeries, icon, dbicon, tagicon1, tagicon2, activetag, icons1, icons2, tag;
function itViewSolutionDiagram(infraObjectData, moniterType) {
    console.log(infraObjectData, 'infraObjectData')
    let replicationType = infraObjectData?.replicationTypeName
    let type = infraObjectData?.typeName
    let drOperation = checkValue(infraObjectData?.drOperationStatus)

    // Create chart
    if (!solutionChart) {
        am4core.useTheme(am4themes_animated);
        am4core.options.autoSetClassName = true;

        solutionChart = am4core.create("ITView-SolutionDiagram", am4plugins_forceDirected.ForceDirectedTree);
        solutionChart.padding(-15, -15, -15, -15)
        solutionChart.defaultState.transitionDuration = 500;

        if (solutionChart.logo) {
            solutionChart.logo.disabled = true;
        }

        solutionSeries = solutionChart.series.push(
            new am4plugins_forceDirected.ForceDirectedSeries()
        );

        solutionSeries.fontSize = 11;
        solutionSeries.minRadius = 35;
        solutionSeries.maxRadius = 35;
        solutionSeries.defaultState.transitionDuration = 500;

        solutionSeries.tooltip.autoTextColor = false;
        solutionSeries.tooltip.getFillFromObject = false;
        solutionSeries.tooltip.label.fill = am4core.color("#1A1A1A");
        solutionSeries.tooltip.label.background.fill = am4core.color("#fff");

        solutionSeries.links.template.strokeWidth = 2;
        //series.links.template.strokeDasharray = "5,3";
        solutionSeries.nodes.template.circle.strokeWidth = 0;
        solutionSeries.nodes.template.circle.disabled = true;
        solutionSeries.nodes.template.outerCircle.disabled = true;

        icon = solutionSeries.nodes.template.createChild(am4core.Image);
        icon.horizontalCenter = "middle";
        icon.verticalCenter = "middle";
        icon.width = 55;
        icon.height = 55;

        dbicon = solutionSeries.nodes.template.createChild(am4core.Image);
        dbicon.horizontalCenter = "middle";
        dbicon.verticalCenter = "middle";
        dbicon.width = 30;
        dbicon.height = 30;

        tagicon1 = solutionSeries.nodes.template.createChild(am4core.Image);
        tagicon1.strokeWidth = 0;
        tagicon1.dy = 0;
        tagicon1.dx = -30
        tagicon1.zIndex = 10;
        tagicon1.width = '15px';
        tagicon1.height = '15px';
        tagicon1.verticalCenter = "top";
        tagicon1.textAlign = 'center'
        tagicon1.horizontalCenter = "left";

        tagicon2 = solutionSeries.nodes.template.createChild(am4core.Image);
        tagicon2.strokeWidth = 0;
        tagicon2.dy = 0;
        tagicon2.dx = -30
        tagicon2.zIndex = 10;
        tagicon2.width = '15px';
        tagicon2.height = '15px';
        tagicon2.verticalCenter = "top";
        tagicon2.textAlign = 'center'
        tagicon2.horizontalCenter = "left";

        activetag = solutionSeries.nodes.template.createChild(am4core.Label);
        activetag.strokeWidth = 0;
        activetag.background = new am4core.RoundedRectangle();
        activetag.background.cornerRadius(10, 10, 10, 10);
        activetag.background.fill = am4core.color("#fff");
        activetag.padding(2, 5, 2, 5);
        activetag.zIndex = 10;
        activetag.width = '10px';
        activetag.height = '10px';
        activetag.verticalCenter = "top";
        activetag.textAlign = 'center'
        activetag.horizontalCenter = "left";

        icons1 = solutionSeries.nodes.template.createChild(am4core.Image);
        icons1.horizontalCenter = "middle";
        icons1.verticalCenter = "middle";
        icons1.width = 28;
        icons1.height = 28;
        icons1.dy = 12;
        icons1.dx = 15

        icons2 = solutionSeries.nodes.template.createChild(am4core.Image);
        icons2.horizontalCenter = "middle";
        icons2.verticalCenter = "middle";
        icons2.width = 18;
        icons2.height = 18;
        icons2.dy = -35;
        icons2.dx = 0

        tag = solutionSeries.nodes.template.createChild(am4core.Label);
        tag.strokeWidth = 0;
        // tag = am4core.percent(50)
        tag.fill = am4core.color("#fff");
        tag.background = new am4core.RoundedRectangle();
        tag.background.cornerRadius(10, 10, 10, 10);
        tag.background.fill = am4core.color("#41c200");
        tag.padding(2, 4, 2, 4);
        tag.zIndex = 10;
        tag.width = '8px';
        tag.height = '10px';
        tag.fontSize = 8;
        tag.verticalCenter = "top";
        tag.textAlign = 'middle'
        tag.horizontalCenter = "left";
    }
    function setSolutionDiagramImage(infraObjectData, moniterType, replicationType) {
        let image = "/img/charts_img/DataCenter/defaultapplication.svg";

        if (!moniterType) return image;

        moniterType = moniterType?.toLowerCase()?.trim() || "";
        let subType = infraObjectData?.subType?.toLowerCase()?.trim() || "";
        let repType = replicationType?.toLowerCase()?.trim() || "";

        switch (true) {
            case moniterType.includes("goldengate") && subType.includes("oracle"):
                image = "/img/charts_img/DataCenter/oracle golden gate.svg";
                break;
            case moniterType.includes("azurestorage"):
                image = "/img/charts_img/DataCenter/Azure_storage.svg";
                break;
            case moniterType.includes("azuremssqlpaas"):
                image = "/img/charts_img/DataCenter/Azure_MSSQL.svg";
                break;
            case moniterType?.toLowerCase()?.includes("recoverpointforvm"):
                image = "/img/charts_img/DataCenter/Azure_RecoverPointVm.svg";
                break;
            case moniterType.includes("robocopy") || subType.includes("robocopy"):
                image = "/img/charts_img/DataCenter/RoboCopynew.svg";
                break;
            case moniterType?.toLowerCase()?.includes("oracle") || (infraObjectData?.subType)?.toLowerCase()?.includes("oracle"):
            case moniterType?.toLowerCase()?.includes("oracle_dataguard") || (infraObjectData?.subType)?.toLowerCase()?.includes("oracle_dataguard"):
            case moniterType?.toLowerCase()?.includes("rac") || (infraObjectData?.subType)?.toLowerCase()?.includes("oraclerac") || (infraObjectData?.subType)?.toLowerCase()?.includes("rac"):
                image = "/img/charts_img/DataCenter/oracle.svg";
                break;
            case moniterType?.toLowerCase()?.includes("mysql") || (infraObjectData?.subType)?.toLowerCase()?.includes("mysql"):
                image = "/img/charts_img/DataCenter/my_sql.svg";
                break;
            case moniterType?.toLowerCase()?.includes("mssql") || moniterType?.toLowerCase()?.includes("nls") || replicationType?.toLowerCase()?.includes('2kx') || (infraObjectData?.subType)?.toLowerCase()?.includes("2kx") || (infraObjectData?.subType)?.toLowerCase()?.includes("mssqlalwayson") || (infraObjectData?.subType)?.toLowerCase()?.includes("always") || (infraObjectData?.subType)?.toLowerCase()?.includes("mssql"):
                image = "/img/charts_img/DataCenter/MSSQL.svg";
                break;
            case moniterType?.toLowerCase()?.includes("postgres") || (infraObjectData?.subType)?.toLowerCase()?.includes("postgres"):
                image = "/img/charts_img/DataCenter/Postgresql.svg";
                break;
            case moniterType?.toLowerCase() === "svc" || (infraObjectData?.name)?.toLowerCase()?.includes("svc"):
                image = "/img/charts_img/DataCenter/IBM.svg";
                break;
            case moniterType?.toLowerCase() === "mongodb" || (infraObjectData?.subType)?.toLowerCase()?.includes("mongodb"):
                image = "/img/Database_Icon/cp_mongo_db.svg";
                break;
            case moniterType?.toLowerCase()?.includes("db2hadr") || (infraObjectData?.subType)?.toLowerCase()?.includes("db2hadr") || (infraObjectData?.subType)?.toLowerCase()?.includes("ibm"):
                image = "/img/charts_img/DataCenter/IBM.svg";
                break;
            case moniterType?.toLowerCase() === "mssqldbmirroring" || (infraObjectData?.subType)?.toLowerCase()?.includes("mirror"):
                image = "/img/charts_img/DataCenter/MSSQL.svg";
                break;
            case moniterType?.toLowerCase()?.includes("hyperv") || (infraObjectData?.subType)?.toLowerCase()?.includes("hyperv"):
                image = "/img/charts_img/DataCenter/windows-1.svg";
                break;
            case moniterType?.toLowerCase()?.includes("rsync") || (infraObjectData?.subType)?.toLowerCase()?.includes("rsync"):
                image = "/img/charts_img/DataCenter/rsync.svg";
                break;
            case moniterType?.toLowerCase()?.includes("activedirectory"):
                image = "/img/charts_img/DataCenter/windows-activedirectory.svg";
                break;
            case moniterType?.toLowerCase()?.includes("as400") || (infraObjectData?.subType)?.toLowerCase()?.includes("as400"):
                image = "/img/charts_img/DataCenter/IBM-AIX.svg";
                break;
            case moniterType?.toLowerCase()?.includes("netapp") || (infraObjectData?.subType)?.toLowerCase()?.includes("netapp"):
                image = "/img/DB-Logo/cp_netapp.svg";
                break;
            case moniterType?.toLowerCase()?.includes("hp3par") || (infraObjectData?.subType)?.toLowerCase()?.includes("hp3par"):
                image = "/img/charts_img/DataCenter/3PAR_logo.svg";
                break;
            case moniterType?.toLowerCase()?.includes("srm") || (infraObjectData?.subType)?.toLowerCase()?.includes("srm"):
                image = "/img/charts_img/DataCenter/srm_vmware.svg";
                break;
            case ["applicationnoreplication", "application - no replication", "application - no - replication", "application-no replication", "application-no-replication"].includes(repType):
                image = "/img/charts_img/DataCenter/replication-off.svg";
                break;
            default:
                image = "/img/charts_img/DataCenter/defaultapplication.svg";
                break;
        }

        return image;
    }

    let image = setSolutionDiagramImage(infraObjectData, moniterType, replicationType);

    let isSRM = moniterType?.toLowerCase() === 'srm';
    let SRMArray = infraObjectData?.serverDto?.filter((d) => d?.type === 'SRMServer');

    let PRServerArray = infraObjectData?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('pr'))
    let DRServerArray = infraObjectData?.serverDto?.filter((d) => d?.serverType?.toLowerCase()?.includes('dr'))
    let PRDBArray = infraObjectData?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('pr'))
    let DRDBArray = infraObjectData?.databaseDto?.filter((d) => d?.type?.toLowerCase()?.includes('dr'));
    let serverProperties = JSON?.parse(infraObjectData?.serverProperties)
    console.log(serverProperties, 'MonserverProperties')
    let serverDto = infraObjectData?.serverDto
    console.log(serverDto, 'MonserverDto')
    function getCurrentPR(serverprops) {
        if (infraObjectData?.subType?.toLowerCase()?.includes('ag')) {
            if (serverprops?.PR?.currentPR === true) {
                return {
                    id: serverprops?.PR?.id,
                    name: serverprops?.PR?.name,
                    type: serverprops?.PR?.type
                }
            }
            if (Array.isArray(serverprops?.DR?.currentPRDetails)) {
                const current = serverprops?.DR?.currentPRDetails?.find(dr => dr?.currentPR);
                if (current) {
                    return {
                        id: current.id,
                        name: current.name,
                        type: serverProperties.DR.type
                    };
                }
            }
        }
    }
    let currentPR = getCurrentPR(serverProperties)
    console.log(currentPR, 'prstate')
    function getServerId(serverDto, currentPR) {
        if (infraObjectData?.subType?.toLowerCase()?.includes('ag')) {
            return serverDto?.find(server => server.serverId === currentPR.id) || null
        }
    }
    const matchedServer = getServerId(serverDto, currentPR);
    console.log(matchedServer, 'matchID');
    function getNonCurrentPRServers(serverProps, serverList) {
        let falsePRIds = [];
        if (infraObjectData?.subType?.toLowerCase()?.includes('ag')) {
            if (serverProps?.PR && serverProps?.PR.currentPR === false) {
                falsePRIds.push(serverProps.PR?.id);
            }

            if (Array.isArray(serverProps?.DR?.currentPRDetails)) {
                const drFalseList = serverProps.DR.currentPRDetails.filter(item => item.currentPR === false);
                falsePRIds.push(...drFalseList.map(item => item.id));
            }

            return serverList.filter(server => falsePRIds.includes(server.serverId));
        }
    }

    let falsyServers = getNonCurrentPRServers(serverProperties, serverDto);
    console.log("Non-current PR Servers:", falsyServers);
    function getServerAndDBDetails(infraObjectData) {

        return {
            PR: {
                ipAddress: infraObjectData?.subType?.toLowerCase()?.includes('ag') ? checkValue(matchedServer?.ipAddress) : checkValue(PRServerArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRServerArray[0]?.hostName : PRServerArray[0]?.ipAddress),
                osType: checkValue(PRServerArray[0]?.osType),
                location: checkValue(PRServerArray[0]?.location),
                hostName: checkValue(PRServerArray[0]?.hostName),
                status: checkValue(PRServerArray[0]?.status),
                serverType: checkValue(PRServerArray[0]?.serverType),
                roleType: checkValue(PRServerArray[0]?.roleType),
                dbStatus: (type?.toLowerCase() !== "application" && type?.toLowerCase() !== "virtual")
                    ? checkValue(PRServerArray[0]?.status?.toLowerCase() !== "down" ? PRDBArray[0]?.status : PRServerArray[0]?.status)
                    : 'NA',
                database: {
                    sid: PRDBArray?.length ? checkValue(PRDBArray[0]?.sid) : '',
                    version: checkValue(PRDBArray[0]?.version),
                    type: checkValue(PRDBArray[0]?.databaseType)
                }
            },
            DR: {
                ipAddress: checkValue(DRServerArray[0]?.ipAddress),
                osType: checkValue(DRServerArray[0]?.osType),
                location: checkValue(DRServerArray[0]?.location),
                hostName: checkValue(DRServerArray[0]?.hostName),
                status: checkValue(DRServerArray[0]?.status),
                roleType: checkValue(infraObjectData.serverDto[1]?.roleType),
                dbStatus: checkValue(DRServerArray[0]?.status?.toLowerCase() !== "down" ? infraObjectData?.databaseDto[1]?.status : DRServerArray[0]?.status),
                database: {
                    sid: DRDBArray?.length ? checkValue(DRDBArray[0]?.sid) : '',
                    version: checkValue(DRDBArray[0]?.version),
                    type: checkValue(DRDBArray[0]?.databaseType)
                }
            }
        };

    }
    function commonServerDBValues() {
        let serverDetails = getServerAndDBDetails(infraObjectData);

        return {
            prroleType: serverDetails?.PR?.roleType,
            prdbStatus: serverDetails?.PR?.dbStatus,
            PRserverType: serverDetails?.PR?.serverType,
            pripaddress: serverDetails?.PR?.ipAddress,
            prOstype: serverDetails?.PR?.osType,
            prOslocation: serverDetails?.PR?.location,
            prhost: serverDetails?.PR?.hostName,
            prStatus: serverDetails?.PR?.status,
            prServer: serverDetails?.PR?.status,
            prDB: serverDetails?.PR?.status?.toLowerCase() !== "down" ? serverDetails?.PR?.dbStatus : serverDetails?.PR?.status,

            dripaddress: serverDetails?.DR?.ipAddress,
            drOstype: serverDetails?.DR?.osType,
            drOslocation: serverDetails?.DR?.location,
            drhost: serverDetails?.DR?.hostName,
            drStatus: serverDetails?.DR?.status,
            drdbStatus: serverDetails?.DR?.dbStatus,
            drroleType: serverDetails?.DR?.roleType,
            drServer: serverDetails?.DR?.status,
            drDB: serverDetails?.DR?.status?.toLowerCase() !== "down" ? serverDetails?.DR?.dbStatus : serverDetails?.DR?.status,

            prdatabase: serverDetails?.PR?.database?.sid,
            prversion: serverDetails?.PR?.database?.version,
            prdatabasetype: serverDetails?.PR?.database?.type,

            drdatabase: serverDetails?.DR?.database?.sid,
            drversion: serverDetails?.DR?.database?.version,
            drdatabasetype: serverDetails?.DR?.database?.type
        };
    }

    if (moniterType === "MssqlAlwaysOn" || moniterType === "Postgres" || moniterType === "Mysql" || moniterType === "Oracle" || moniterType === "MssqlNLS" || moniterType === "HyperV" || moniterType === "DB2HADR" || moniterType === "MongoDB" || moniterType === "RSyncAppReplication" || moniterType === "RoboCopy" || moniterType === "CyberRecover" || moniterType?.toLowerCase() === "mssqldbmirroring" || moniterType === "AS400" || moniterType?.toLowerCase() === "azuremysqlpaas" || moniterType === "AzurePostgresPaas" || moniterType === "AzureStorageAccount" || moniterType === "NetAppSnapMirror" || infraObjectData?.subType?.toLowerCase()?.includes("ibm") || replicationType?.toLowerCase() === "rpforvm replication" || infraObjectData?.subType?.toLowerCase()?.includes("mirror") || infraObjectData?.subType?.toLowerCase()?.includes("mssqlnls") || replicationType?.toLowerCase() === "application no-replication"
        || moniterType === "Hp3par" || (moniterType === "Oracle" && !infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oraclerac') || !infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oracle-rac')) || infraObjectData?.subType?.toLowerCase()?.includes("mysql") || infraObjectData?.subType?.toLowerCase()?.includes("mssqlalwayson") || infraObjectData?.subType?.toLowerCase()?.includes("postgres") || infraObjectData?.subType?.toLowerCase()?.includes("cyber") || infraObjectData?.subType?.toLowerCase()?.includes("hyperv") || infraObjectData?.subType?.toLowerCase()?.includes("mongodb") || infraObjectData?.subType?.toLowerCase()?.includes("as400")) {
        var {
            prroleType, prdbStatus, PRserverType, pripaddress, prOstype, prOslocation, prhost, prStatus,
            dripaddress, drOstype, drOslocation, drhost, drStatus, drdbStatus, drroleType,
            prdatabase, prversion, prdatabasetype, drdatabase, drversion, drdatabasetype, prServer, drServer, prDB, drDB
        } = commonServerDBValues();

    }
    if (moniterType === "SRM") {

        PRSRMServer = infraObjectData?.serverDto.filter((d) => d.serverType == 'PRESXIServer')
        DRSRMServer = infraObjectData?.serverDto.filter((d) => d.serverType == 'DRESXIServer')
        var srmip = infraObjectData?.serverDto[0]?.connectViaHostName?.toLowerCase() === "true" ? infraObjectData?.serverDto[0]?.hostName : infraObjectData?.serverDto[0]?.ipAddress
        var srmip1 = SRMArray[0]?.connectViaHostName?.toLowerCase() === "true" ? PRSRMServer[0].hostName : PRSRMServer[0].ipAddress
        var pripaddress = checkValue(srmip)
        var dripaddress = checkValue(infraObjectData?.serverDto[1]?.ipAddress)
        var prdatabase = checkValue(SRMArray[0].ipAddress)
        var drdatabase = checkValue(SRMArray[1]?.ipAddress)
        var prOstype = checkValue(PRSRMServer[0]?.osType)
        var drOstype = checkValue(DRSRMServer[0]?.osType)
        var prOslocation = checkValue(PRSRMServer[0]?.location)
        var drOslocation = checkValue(DRSRMServer[0]?.location)
        var prhost = checkValue(PRSRMServer[0]?.hostName)
        var drhost = checkValue(DRSRMServer[0]?.hostName)
        var prStatus = checkValue(PRSRMServer[0]?.status)
        var drStatus = checkValue(DRSRMServer[0]?.status)
        var prdbStatus = checkValue(SRMArray[0]?.status)
        var drdbStatus = checkValue(SRMArray[1]?.status)
        var prversion = checkValue(SRMArray[0]?.version)
        var drversion = checkValue(SRMArray[1]?.version)
        var prdatabasetype = checkValue(infraObjectData?.databaseDto[0]?.databaseType)
        var drdatabasetype = checkValue(infraObjectData?.databaseDto[1]?.databaseType)
        var prroleType = checkValue(PRSRMServer[0]?.roleType)
        var drroleType = checkValue(DRSRMServer[0]?.roleType)
        prServer = checkValue(PRSRMServer[0]?.status);
        prDB = checkValue(SRMArray[0]?.status)
        drServer = checkValue(DRSRMServer[0]?.status)
        drDB = checkValue(SRMArray[1]?.status)

        var prsrmHost = checkValue(PRSRMServer[0]?.hostName)
        var drsrmHost = checkValue(DRSRMServer[0]?.hostName)
        var prsrmLocation = checkValue(PRSRMServer[0]?.location)
        var drsrmLocation = checkValue(DRSRMServer[0]?.location)
        var prsrmOS = checkValue(PRSRMServer[0]?.osType)
        var drsrmOS = checkValue(DRSRMServer[0]?.osType)

    }

    else if ((moniterType?.toLowerCase()?.includes('oraclerac')) || infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oraclerac') || infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oracle-rac')) {

        if ((moniterType?.toLowerCase()?.includes('oraclerac')) || infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oraclerac') || infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oracle-rac')) {
            let nodeArray = [];
            let nodeArray1 = [];
            let nodeName = $('#nodeName option:selected')?.text() === "" ? "Node1" : $('#nodeName option:selected')?.text()
            if (nodeName?.toLowerCase()?.includes('1')) {
                infraObjectData?.serverDto.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('dr1') || item?.nodeName?.toLowerCase()?.includes('drdb_1')) {
                        nodeArray.push(item);
                    }
                });
                infraObjectData?.databaseDto?.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr1') || item?.nodeName?.toLowerCase()?.includes('prdb_1') || item?.nodeName?.toLowerCase()?.includes('drdb_1') || item?.nodeName?.toLowerCase()?.includes('dr1')) {
                        nodeArray1.push(item);
                    }
                });
            } else if (nodeName?.toLowerCase()?.includes('2')) {
                infraObjectData?.serverDto.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                        nodeArray.push(item);
                    }
                });
                infraObjectData?.databaseDto.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr2') || item?.nodeName?.toLowerCase()?.includes('prdb_2') || item?.nodeName?.toLowerCase()?.includes('drdb_2') || item?.nodeName?.toLowerCase()?.includes('dr2')) {
                        nodeArray1.push(item);
                    }
                });
            } else if (nodeName?.toLowerCase()?.includes('3')) {
                infraObjectData?.serverDto.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                        nodeArray.push(item);
                    }
                });
                infraObjectData?.databaseDto.forEach((item) => {
                    if (item?.nodeName?.toLowerCase()?.includes('pr3') || item?.nodeName?.toLowerCase()?.includes('dr3')) {
                        nodeArray1.push(item);
                    }
                });
            }
            infraObjectData.serverDto = nodeArray
            infraObjectData.databaseDto = nodeArray1
        }
        var {
            prroleType, prdbStatus, PRserverType, pripaddress, prOstype, prOslocation, prhost, prStatus,
            dripaddress, drOstype, drOslocation, drhost, drStatus, drdbStatus, drroleType,
            prdatabase, prversion, prdatabasetype, drdatabase, drversion, drdatabasetype, prServer, drServer, prDB, drDB
        } = commonServerDBValues();

        if (prStatus?.toLowerCase() === 'up') {
            $(".itView_PRClass").addClass('cp-up-linearrow me-1 text-success');
            PRServerStatus = 'cp-up-linearrow me-1 text-success';
        } else if (prStatus?.toLowerCase() === 'down') {
            $(".itView_PRClass").addClass('cp-down-linearrow me-1 text-danger');
            PRServerStatus = 'cp-down-linearrow me-1 text-danger';
        } else if (prStatus?.toLowerCase() === 'pending' || prStatus?.toLowerCase() === '' || prStatus === null || prStatus?.toLowerCase() === 'na') {
            $(".itView_PRClass").addClass('cp-pending  me-1 text-warning');
            PRServerStatus = 'cp-pending me-1 text-warning';
        }


        pripaddress1 = PRServerStatus ? PRServerStatus : 'itView_PRClass'

        $("#praddress").empty().append('<i class="' + pripaddress1 + '"></i> ' + pripaddressdata)

    }

    else {
        var {
            prroleType, prdbStatus, PRserverType, pripaddress, prOstype, prOslocation, prhost, prStatus,
            dripaddress, drOstype, drOslocation, drhost, drStatus, drdbStatus, drroleType,
            prdatabase, prversion, prdatabasetype, drdatabase, drversion, drdatabasetype, prServer, drServer, prDB, drDB
        } = commonServerDBValues();

    }
    function setDatabaseImage(prdatabasetype, drdatabasetype) {
        let databaseImage

        if (prdatabasetype || drdatabasetype) {
            const databaseType = (prdatabasetype || drdatabasetype)?.toLowerCase();

            switch (true) {
                case databaseType.includes("oracle"):
                    databaseImage = "/img/charts_img/DataCenter/oracle.svg";
                    break;
                case databaseType.includes("mysql"):
                    databaseImage = "/img/charts_img/DataCenter/my_sql.svg";
                    break;
                case databaseType.includes("mssql") || databaseType.includes("always") || databaseType.includes("ms-sql"):
                    databaseImage = "/img/charts_img/DataCenter/MSSQL.svg";
                    break;
                case databaseType.includes("postgres"):
                    databaseImage = "/img/charts_img/DataCenter/Postgresql.svg";
                    break;
                case databaseType.includes("ibm"):
                    databaseImage = "/img/charts_img/DataCenter/IBM.svg";
                    break;
                case databaseType.includes("mongodb"):
                    databaseImage = "/img/Database_Icon/cp_mongo_db.svg";
                    break;
            }
        }

        return databaseImage;
    }

    let databaseImage = setDatabaseImage(prdatabasetype, drdatabasetype);

    solutionChart.colors.list = [am4core.color("#ff9c0d"), am4core.color("#40c200")];

    solutionSeries.data = [{

        fixed: true,
        x: am4core.percent(12),
        y: am4core.percent(50),
        tagimage: prroleType?.toLowerCase() === "database" ? (prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
        ip: prdatabase,
        status: prdbStatus,
        type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
        version: prversion,
        value: 10,
        serverimage: databaseImage,
        dbimage: prroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application") ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg",
        children: [{
            name: "Primary",
            fixed: true,
            x: am4core.percent(30),
            y: am4core.percent(50),
            tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
            ip: pripaddress,
            hostname: prhost,
            os: prOstype,
            location: prOslocation,
            prstatus: prdbStatus,
            prostatus: prStatus,
            value: 10,
            activeicon: drOperation == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
            //image: "/img/charts_img/DataCenter/PR.svg",

            image: moniterType?.toLowerCase() !== 'srm' && (PRserverType === 'PRDBServer' || PRserverType === 'PRAPPServer' || PRserverType === 'PRVsphereServer') ? "/img/charts_img/DataCenter/PR.svg" : "/img/charts_img/DataCenter/far dr.svg",
            serverimage: prOstype?.toLowerCase().includes("windows") ? "/img/charts_img/DataCenter/windows-1.svg" : prOstype?.toLowerCase().includes("linux") ? "/img/charts_img/DataCenter/linux.svg" : prOstype?.toLowerCase().includes("azuremssqlpaas") ? "/img/charts_img/DataCenter/azure_mssql_os.svg" : "/img/charts_img/DataCenter/windows-1.svg",
            children: [
                {
                    dbname: replicationType,

                    status: prServer,
                    fixed: true,
                    x: am4core.percent(51),
                    y: am4core.percent(50),
                    mainimage: (infraObjectData.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                    value: 15,
                    image: image,
                    children: [

                    ],

                }
            ]
        }]

    },];
    if (moniterType?.toLowerCase() === 'srm') {
        solutionSeries.data = [{

            fixed: true,
            x: am4core.percent(12),
            y: am4core.percent(40),
            tagimage: prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg",
            ip: prdatabase,
            status: prdbStatus,
            type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
            version: prversion,
            value: 10,
            activeicon: drOperation == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
            serverimage: databaseImage,
            image: "/img/charts_img/DataCenter/PR.svg",
            children: [{
                name: "Primary",
                fixed: true,
                x: am4core.percent(30),
                y: am4core.percent(40),
                tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                ip: pripaddress,
                hostname: prhost,
                os: prOstype,
                location: prOslocation,
                prstatus: prdbStatus,
                prostatus: prStatus,
                value: 10,

                image: "/img/charts_img/DataCenter/esxi_server_icon.svg",
                serverimage: prOstype?.toLowerCase().includes("windows") ? "/img/charts_img/DataCenter/windows-1.svg" : prOstype?.toLowerCase().includes("linux") ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                children: [
                    {
                        dbname: replicationType,
                        status: prServer,
                        fixed: true,
                        x: am4core.percent(51),
                        y: am4core.percent(40),
                        mainimage: (infraObjectData.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                        value: 15,
                        image: image,
                        children: [
                            {
                                name: "DR",
                                fixed: true,
                                x: am4core.percent(70),
                                y: am4core.percent(40),
                                tagimage: (drServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                                ip: dripaddress,
                                hostname: drhost,
                                os: drOstype,
                                location: drOslocation,
                                status: drStatus,
                                value: 10,

                                serverimage: drOstype?.toLowerCase().includes("windows") ? "/img/charts_img/DataCenter/windows-1.svg" : drOstype?.toLowerCase().includes("linux") ? "/img/charts_img/DataCenter/linux.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                                image: "/img/charts_img/DataCenter/esxi_server_icon.svg",
                                children: [{

                                    fixed: true,
                                    x: am4core.percent(85),
                                    y: am4core.percent(40),
                                    tagimage: drDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg",
                                    ip: drdatabase,
                                    status: drdbStatus,
                                    type: drroleType?.toLowerCase() === "database" ? drdatabasetype : replicationType,
                                    version: drversion,
                                    value: 10,
                                    activeicon: drOperation == 2 ? "/img/charts_img/DataCenter/crown_1.svg" : "",
                                    serverimage: databaseImage,
                                    image: "/img/charts_img/DataCenter/DR.svg",
                                }],
                            }
                        ],
                    }
                ]
            }]

        },];
    }

    if (moniterType?.toLowerCase()?.includes('openshift')) {
        solutionSeries.data = [{

            fixed: true,
            x: am4core.percent(32),
            y: am4core.percent(45),
            tagimage: prroleType?.toLowerCase() === "database" || isSRM ? (prDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",

            location: prOslocation,
            status: prdbStatus,
            type: prroleType?.toLowerCase() === "database" ? prdatabasetype : replicationType,
            version: prversion,
            hostname: isSRM ? prsrmHost : '',
            location: isSRM ? prsrmLocation : '',
            os: isSRM ? prsrmOS : '',
            value: 10,
            serverimage: databaseImage,
            dbimage: prroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application") ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg",
            children: [{
                name: "Primary",
                fixed: true,
                x: am4core.percent(50),
                y: am4core.percent(45),
                tagimage: (prServer?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : prServer?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : prServer?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                ip: infraObjectData?.serverDto[0]?.hostName,
                hostname: infraObjectData?.serverDto[0]?.hostName,
                os: infraObjectData?.serverDto[0]?.osType,
                location: infraObjectData?.serverDto[0]?.location,
                prstatus: /*infraObjectData?.serverDto[0]?.status*/ prdbStatus,
                prostatus: prStatus,
                value: 10,
                // activeicon: drOperation == 2 ? "" : "/img/charts_img/DataCenter/crown_1.svg",
                image: "/img/charts_img/DataCenter/far dr.svg",
                serverimage: "/img/charts_img/DataCenter/linux.svg",
                children: [
                    {
                        dbname: /*infraObjectData?.replicationTypeName*/ replicationType,
                        status: prServer,
                        fixed: true,
                        x: am4core.percent(71),
                        y: am4core.percent(45),
                        mainimage: (infraObjectData?.state?.toLowerCase() !== "active" ? "/img/charts_img/DataCenter/maintenance.svg" : null),
                        value: 15,
                        image: "/img/charts_img/DataCenter/replication-off.svg",

                    }
                ]
            }]

        },];
    }

    let customDRServerArray = infraObjectData?.serverDto.filter((d) => !d?.serverType?.toLowerCase()?.includes('pr'))

    let customDRDBArray = infraObjectData?.databaseDto.filter((d) => !d?.type?.toLowerCase()?.includes('pr'))
    if (!moniterType?.toLowerCase()?.includes('srm')) {
        if (customDRServerArray?.length > 0) {
            customDRServerArray.forEach((d, index) => {
                falsyServers?.forEach((s, index) => {
                    let ipvalue = d?.connectViaHostName?.toLowerCase() === "true" ? d?.hostName : d?.ipAddress;
                    let currIp = s?.connectViaHostName?.toLowerCase() === "true" ? s?.hostName : s?.ipAddress
                    //var dripaddress = checkValue(ipvalue)
                    var dripaddress = infraObjectData?.subType?.toLowerCase()?.includes('ag') ? checkValue(currIp) : checkValue(ipvalue)
                    var drOstype = checkValue(d?.osType)
                    var drOslocation = checkValue(d?.location)
                    var drhost = checkValue(d?.hostName)
                    var drStatus = checkValue(d?.status)

                    var drdbStatus = (type?.toLowerCase() !== "application" && type?.toLowerCase() !== "virtual") ? (checkValue(d?.status?.toLowerCase() != "down" ? infraObjectData?.databaseDto[0]?.status : d?.status)) : 'NA'
                    var drroleType = checkValue(infraObjectData.serverDto[1]?.roleType)

                    var serverType = d?.serverType
                    let drArrayLength = customDRServerArray?.length
                    let yValue = []

                    //For DR DatabaseName add styles for Solution Diagram
                    const solutionDiagram = document.getElementById("ITView-SolutionDiagram");
                    const cardBody = document.getElementById("DRSolution");
                    if (drArrayLength === 1) {
                        yValue = [50]
                    } else if (drArrayLength === 2) {
                        yValue = [25, 65]
                        solutionDiagram.style.height = "213px";
                        cardBody.style.height = "calc(50vh - 188px)";
                        cardBody.style.overflow = "auto";
                    } else if (drArrayLength === 3) {
                        yValue = [20, 50, 80]
                    } else if (drArrayLength === 4) {
                        yValue = [20, 35, 65, 80]
                    } else if (drArrayLength === 5) {
                        yValue = [20, 35, 50, 65, 80]
                    } else if (drArrayLength === 6) {
                        yValue = [10, 25, 40, 60, 75, 90]
                    }
                    //Not drlength 2
                    if (drArrayLength !== 2) {
                        solutionDiagram.style.height = "132px";
                        cardBody.style.height = "";
                        cardBody.style.overflow = "";
                    }
                    let filterdTypeArray = []
                    if (
                        moniterType?.toLowerCase()?.includes('oraclerac') ||
                        infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oraclerac') ||
                        infraObjectData?.replicationTypeName?.toLowerCase()?.includes('oracle-rac')
                    ) {
                        filterdTypeArray = customDRDBArray.filter((x) => serverType?.toLowerCase().includes('dr'));
                    } else {
                        filterdTypeArray = customDRDBArray.filter((x) => x.type?.toLowerCase().includes('dr') && serverType?.toLowerCase().includes('dr'));
                    }


                    var drdatabase = customDRDBArray?.length ? checkValue(filterdTypeArray[0]?.sid) : ''
                    var drversion = checkValue(filterdTypeArray[0]?.version)
                    var drdatabasetype = checkValue(filterdTypeArray[0]?.databaseType)
                    let obj = {
                        name: d?.serverType,
                        fixed: true,
                        x: am4core.percent(70),
                        y: am4core.percent(yValue[index]),
                        tagimage: (drStatus?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drStatus?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drStatus?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg"),
                        ip: dripaddress,
                        hostname: drhost,
                        os: drOstype,
                        location: drOslocation,
                        prostatus: drStatus,
                        status: drStatus,
                        value: 10,
                        activeicon: (drOperation == 2 && serverType?.toLowerCase()?.includes('dr')) || (drOperation == 15 && !serverType?.toLowerCase()?.includes('dr')) ? "/img/charts_img/DataCenter/crown_1.svg" : "",
                        serverimage: drOstype?.toLowerCase()?.includes("windows") ? "/img/charts_img/DataCenter/windows-1.svg" : drOstype?.toLowerCase()?.includes("linux") ? "/img/charts_img/DataCenter/linux.svg" : drOstype?.toLowerCase()?.includes("azuremssqlpaas") ? "/img/charts_img/DataCenter/azure_mssql_os.svg" : "/img/charts_img/DataCenter/windows-1.svg",
                        image: (serverType === "DR" || serverType === "DRDBServer" || serverType === "DRAPPServer" || serverType === 'DRVsphereServer') ? "/img/charts_img/DataCenter/DR.svg" : "/img/charts_img/DataCenter/far dr.svg",
                        children: [{

                            fixed: true,
                            x: am4core.percent(85),
                            y: am4core.percent(yValue[index]),
                            tagimage: drroleType?.toLowerCase() === "database" ? (drDB?.toLowerCase() === "up" ? "/img/fill icon/svg/success.svg" : drDB?.toLowerCase() === "down" ? "/img/fill icon/svg/error.svg" : drDB?.toLowerCase() === "pending" ? "/img/fill icon/svg/pending.svg" : "/img/fill icon/svg/pending.svg") : "",
                            ip: drdatabase,
                            status: drdbStatus,
                            type: drroleType?.toLowerCase() === "database" ? drdatabasetype : replicationType,
                            version: drversion,
                            value: 10,
                            serverimage: databaseImage,
                            dbimage: drroleType?.toLowerCase() === "database" ? "/img/charts_img/DataCenter/database.svg" : (drroleType?.toLowerCase() === "virtualization" || drroleType?.toLowerCase() === "application") ? "" : "/img/charts_img/DataCenter/ApplicationRe.svg",
                        }],
                    }
                    solutionSeries.data[0].children[0].children[0].children.push(obj)
                })
            })
        }

    }

    setTimeout(() => {

        if (customDRServerArray?.length === 2) {
            //   $('.amcharts-ForceDirectedLink-group').eq(1).css('animation', 'am-moving-dashesIn 1s linear infinite')
            let stringArray
            if (drOperation === 2) {
                stringArray = [0, 1, 2, 3]
            } else if (drOperation === 15) {
                stringArray = [0, 1, 4, 5]
            }
            for (let i = 0; i < 6; i++) {
                if (stringArray?.includes(i)) {
                    $('.amcharts-ForceDirectedLink-group').eq(i).css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                }
            }

        } else if (customDRServerArray?.length === 3) {
            // $('.amcharts-ForceDirectedLink-group').eq(1).css('animation', 'am-moving-dashesIn 1s linear infinite')
            let stringArray
            if (drOperation === 2) {
                stringArray = [0, 1, 2, 3]
            } else if (drOperation === 15) {
                stringArray = [0, 1, 4, 5]
            }
            for (let i = 0; i < 8; i++) {
                if (stringArray?.includes(i)) {
                    $('.amcharts-ForceDirectedLink-group').eq(i).css('animation', 'am-moving-dashesIn 1s reverse linear infinite')
                }
            }

        }
    }, 500)

    // Set up data fields
    solutionSeries.dataFields.value = "value";
    solutionSeries.dataFields.fixed = "fixed";
    solutionSeries.dataFields.dbname = "dbname";
    solutionSeries.dataFields.activeicon = "activeicon";
    solutionSeries.dataFields.serverimage = "serverimage";
    solutionSeries.dataFields.name = "name";
    solutionSeries.dataFields.ip = "ip";
    solutionSeries.dataFields.Connection = "Connection";
    (solutionSeries.dataFields.Service = "Service"),
        (solutionSeries.dataFields.host = "host"),
        (solutionSeries.dataFields.Port = "Port"),
        (solutionSeries.dataFields.id = "id");
    solutionSeries.dataFields.children = "children";
    solutionSeries.dataFields.tag = "tag";
    solutionSeries.dataFields.linkWith = "link";
    solutionSeries.links.template.propertyFields.userClassName = "lineClass"
    solutionSeries.links.template.adapter.add("stroke", function (stroke, target) {
        if (target.dataItem && target.dataItem.dataContext) {
            const sourceNode = target.dataItem.dataContext;

            const sourceStatus = sourceNode.status;
            const prStatusSource = sourceNode.prstatus;

            // Set individual stroke colors 
            if (sourceStatus) {

                if (moniterType?.toLowerCase()?.includes('openshift')) {
                    if (moniterType === '' || moniterType === undefined) {
                        return am4core.color("grey");
                    } else {
                        if (infraObjectData?.serverDto[0]?.status?.toLowerCase() === "up") {
                            return am4core.color("#41c200");
                        } else if (infraObjectData?.serverDto[0]?.status?.toLowerCase() === "down") {
                            return am4core.color("red");
                        } else if (infraObjectData?.serverDto[0]?.status?.toLowerCase() === "pending") {
                            return am4core.color("grey");
                        } else {
                            return am4core.color("grey");
                        }
                    }
                }

                if (moniterType === '' || moniterType === undefined) {
                    return am4core.color("grey");
                } else {
                    if (sourceStatus?.toLowerCase() === "up") {
                        return am4core.color("#41c200");
                    } else if (sourceStatus?.toLowerCase() === "down") {
                        return am4core.color("red");
                    } else if (sourceStatus?.toLowerCase() === "pending") {
                        return am4core.color("grey");
                    } else if (drroleType?.toLowerCase() === "virtualization" || drroleType?.toLowerCase() === "application") {
                        return am4core.color("#FFFFFF");
                    } else {
                        return am4core.color("grey");
                    }
                }

            }
            if (prStatusSource) {
                if (moniterType === '' || moniterType === undefined) {
                    return am4core.color("grey");
                } else {
                    if (prStatusSource?.toLowerCase() === "up") {
                        return am4core.color("#41c200");
                    } else if (prStatusSource?.toLowerCase() === "down") {
                        return am4core.color("red");
                    } else if (prStatusSource?.toLowerCase() === "pending") {
                        return am4core.color("grey");
                    } else if (prroleType?.toLowerCase() === "virtualization" || prroleType?.toLowerCase() === "application") {
                        return am4core.color("#FFFFFF");
                    } else {
                        return am4core.color("grey");
                    }
                }

            }


        }
        return stroke;
    });
    solutionChart?.invalidateRawData();
    solutionSeries.dataFields.id = "name";
    solutionSeries.manyBodyStrength = -18;
    // Add labels
    solutionSeries.nodes.template.label.text = "{dbname}\n{ip}";
    solutionSeries.nodes.template.label.tooltipText = "{tooltiptext}"; // Fix incorrect `tooltiptext`
    solutionSeries.nodes.template.label.valign = "bottom";
    solutionSeries.nodes.template.label.wrap = true;  // Enable wrapping
    solutionSeries.nodes.template.label.hideOversized = false;  // Prevents hiding wrapped text
    solutionSeries.nodes.template.label.truncate = false; // Disable truncation
    solutionSeries.nodes.template.label.maxWidth = 110; // Set max width before wrapping
    solutionSeries.nodes.template.label.fill = am4core.color("#000");
    solutionSeries.nodes.template.label.dy = -2;
    solutionSeries.nodes.template.adapter.add("tooltipText", function (text, target) {

        if (!target.dataItem) return "";

        const dataItem = target.dataItem.dataContext;
        if (target?.dataItem?.name === "Primary" || target?.dataItem?.name === "DR" || target?.dataItem?.name) {
            const hostText = dataItem?.hostname ?? "NA";
            const osText = dataItem?.os ?? "NA";
            const osLocation = dataItem?.location ?? "NA";
            const statusText = (dataItem?.prostatus ?? "NA");

            return `[bold; #0479ff;]Hostname: [/]${hostText}\n[/] [bold; #0479ff;]Location: [/]${osLocation}\n[/] [bold; #0479ff;]OS: [/]${osText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;
        }
        else if (dataItem.type) {

            let typeText = dataItem?.type ?? "NA";
            let versionText = dataItem?.version ?? "NA";
            let statusText = dataItem?.status ?? "NA";

            return `[bold; #0479ff;]Type: [/]${typeText}\n[/] [bold; #0479ff;]Version: [/]${versionText}\n[/] [bold; #0479ff;]Status: [/]${statusText}`;

        }
        else if (replicationType) {
            const replicaText = dataItem?.dbname ?? "NA";
            return `[bold; #0479ff;]${replicaText}`;
        }

        else if (infraObjectData?.state?.toLowerCase() === "maintenance" ? "Maintenance" : null) {
            const mainText = infraObjectData?.state ?? "NA";
            return `[bold; #0479ff;]${mainText}`;
        }
        return "";
    });

    solutionSeries.dataFields.fixed = "fixed";
    solutionSeries.nodes.template.propertyFields.x = "x";
    solutionSeries.nodes.template.propertyFields.y = "y";

    // Add tag
    tag.text = "{tag}";

    tag.adapter.add("dy", function (dy, target) {
        return -target.parent.circle.radius + 40;
    });
    tag.adapter.add("dx", function (dy, target) {
        return target.parent.circle.radius - 65;
    });
    tag.adapter.add("textOutput", function (text, target) {
        if (text === "") {
            target.disabled = true;
        }
        return text;
    });

    tag.adapter.add("fill", function (fill, target) {
        if (target.dataItem && target.dataItem.tag == "✔") {
            return am4core.color("#fff");
        } else {
            return fill;
        }
    });
    tag.background.adapter.add("fill", function (fill, target) {
        if (target.dataItem && target.dataItem.tag == "✖") {
            return am4core.color("red");
        } else {
            return fill;
        }
    });
    tag.background.adapter.add("fill", function (fill, target) {
        if (target.dataItem && target.dataItem.tag == "❕") {
            return am4core.color("#FF9632");
        } else {
            return fill;
        }
    });
    // Change the padding values

    // Configure icons
    icon.propertyFields.href = "image";

    // Configure icons   
    dbicon.propertyFields.href = "dbimage";
    solutionSeries.centerStrength = 0.5;

    tagicon1.propertyFields.href = "tagimage";

    tagicon2.text = "{tagicon}";
    tagicon2.propertyFields.href = "mainimage";

    activetag.adapter.add("dy", function (dy, target) {
        return -target.parent.circle.radius + -8;
    });
    activetag.adapter.add("dx", function (dy, target) {
        return target.parent.circle.radius - 40;
    });
    activetag.background.adapter.add("fill", function (fill, target) {
        if (target.dataItem && target.dataItem.activetag == true) {
            return am4core.color("#41c200");
        } else {
            return fill;
        }
    });

    icons1.propertyFields.href = "serverimage";

    icons2.propertyFields.href = "activeicon";
}
function checkValue(value) {
    return (value !== null && value !== '' && value !== undefined) ? value : "NA";
}