using ContinuityPatrol.Application.Features.DriftCategoryMaster.Events.Update;

namespace ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;

public class
    UpdateDriftCategoryMasterCommandHandler : IRequestHandler<UpdateDriftCategoryMasterCommand,
        UpdateDriftCategoryMasterResponse>
{
    private readonly IDriftCategoryMasterRepository _driftCategoryMasterRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateDriftCategoryMasterCommandHandler(IMapper mapper,
        IDriftCategoryMasterRepository driftCategoryMasterRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _driftCategoryMasterRepository = driftCategoryMasterRepository;
        _publisher = publisher;
    }

    public async Task<UpdateDriftCategoryMasterResponse> Handle(UpdateDriftCategoryMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _driftCategoryMasterRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.DriftCategoryMaster), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDriftCategoryMasterCommand),
            typeof(Domain.Entities.DriftCategoryMaster));

        await _driftCategoryMasterRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDriftCategoryMasterResponse
        {
            Message = Message.Update(nameof(Domain.Entities.DriftCategoryMaster), eventToUpdate.CategoryName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new DriftCategoryMasterUpdatedEvent { Name = eventToUpdate.CategoryName },
            cancellationToken);

        return response;
    }
}