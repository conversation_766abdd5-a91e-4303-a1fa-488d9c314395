﻿using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Events.Paginated;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.CyberResiliency.Controllers;

[Area("CyberResiliency")]
public class ManageController : Controller
{
    private readonly ILogger<ManageController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    public ManageController(ILogger<ManageController> logger, IDataProvider dataProvider, IPublisher publisher, IMapper mapper)
    {
        _logger = logger;
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }
    public async Task<IActionResult> List()
    {
       await _publisher.Publish(new CyberMappingPaginatedEvent());
        return View();
    }

    public async Task<JsonResult> GetSiteList()
    {
        _logger.LogDebug("Entering GetSiteList method in Cyber mapping");

        try
        {
            var cyberZoneDetails = await _dataProvider.Site.GetSiteNames();

            _logger.LogDebug("Successfully retrieved site name in Cyber Mapping");

            return Json(new { Success = true, data = cyberZoneDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Mapping page while retrieving site name list.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetServersBySiteId(string siteId)
    {
        _logger.LogDebug("Entering GetComponentsBySiteId method in Cyber mapping");

        try
        {
            var cyberComponentList = await _dataProvider.Server.GetServerBySiteId(siteId);

            _logger.LogDebug("Successfully retrieved cyber component list in Cyber Mapping");

            return Json(new { Success = true, data = cyberComponentList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Mapping page while retrieving cyber component List.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetComponentsBySiteId(string siteId)
    {
        _logger.LogDebug("Entering GetComponentsBySiteId method in Cyber mapping");

        try
        {
            var cyberComponentList = await _dataProvider.CyberComponent.GetCyberComponentBySiteId(siteId);

            _logger.LogDebug("Successfully retrieved cyber component list in Cyber Mapping");

            return Json(new { Success = true, data = cyberComponentList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Mapping page while retrieving cyber component List.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetComponentsGroup()
    {
        _logger.LogDebug("Entering GetZoneList method in Cyber mapping");

        try
        {
            var cyberComponentlist = await _dataProvider.CyberComponentGroup.GetCyberComponentGroupList();

            _logger.LogDebug("Successfully retrieved Zone List in Cyber Mapping");

            return Json(new { Success = true, data = cyberComponentlist });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Component page while retrieving zone List.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetMappingList()
    {
        _logger.LogDebug("Entering GetMappingList method in Cyber mapping");

        try
        {
            var cyberMappingDetails = await _dataProvider.CyberComponentMapping.GetCyberComponentMappingList();

            _logger.LogDebug("Successfully retrieved cyber component mapping List in Cyber Mapping");

            return Json(new { Success = true, data = cyberMappingDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Mapping page while retrieving cyber component mapping list.", ex);

            return ex.GetJsonException();
        }
    }

    public async Task<JsonResult> GetAirGapList()
    {
        _logger.LogDebug("Entering GetZoneList method in Cyber mapping");

        try
        {
            var cyberAirGapDetails = await _dataProvider.CyberAirGap.GetCyberAirGapList();

            _logger.LogDebug("Successfully retrieved Zone List in Cyber Mapping");

            return Json(new { Success = true, data = cyberAirGapDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Component page while retrieving zone List.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    //[Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(CyberComponentMappingViewModel cyberComponentMappingViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Cyber Mapping");

        try
        {
            var mappingId = Request.Form["id"].ToString();

            if (mappingId.IsNullOrWhiteSpace())
            {
                var cyberMappingCommand = _mapper.Map<CreateCyberComponentMappingCommand>(cyberComponentMappingViewModel);

                var result = await _dataProvider.CyberComponentMapping.CreateAsync(cyberMappingCommand);

                _logger.LogDebug($"Creating Cyber Mapping '{cyberMappingCommand.Name}'");

                return Json(new { Success = true, data = result });
            }
            else
            {
                var cyberMappingCommand = _mapper.Map<UpdateCyberComponentMappingCommand>(cyberComponentMappingViewModel);

                var result = await _dataProvider.CyberComponentMapping.UpdateAsync(cyberMappingCommand);

                _logger.LogDebug($"Updating Cyber Mapping '{cyberMappingCommand.Name}'");

                return Json(new { Success = true, data = result });
            }


        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on cyber mapping page: {ex.ValidationErrors.FirstOrDefault()}");

            //TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return ex.GetJsonException();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on cyber mapping page while processing the request for create or update.", ex);

            //TempData.NotifyWarning(ex.Message);

            return ex.GetJsonException();
        }

    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Cyber AirGap");

        try
        {
            var result = await _dataProvider.CyberComponentMapping.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in Cyber Mapping");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
           

            _logger.Exception("An error occurred while deleting record on Cyber Mapping.", ex);

            return ex.GetJsonException();
        }
    }


    public async Task<JsonResult> GetCyberAirGapService(string airGapId)
    {
        _logger.LogDebug("Entering GetCyberAirGapServicebyAirgapId method in Cyber mapping");

        try
        {
            var cyberComponentList = await _dataProvider.CyberAirGap.GetByReferenceId(airGapId);

            _logger.LogDebug("Successfully retrieved cyber component list in Cyber Mapping");

            return Json(new { Success = true, data = cyberComponentList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Cyber Mapping page while retrieving cyber component List.", ex);

            return ex.GetJsonException();
        }
    }

}
