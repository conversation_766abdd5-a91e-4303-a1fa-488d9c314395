﻿namespace ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetDetail;

public class WorkflowExecutionEventLogDetailVm
{
    public string Id { get; set; }
    public string WorkflowActionName { get; set; }
    public string CompanyId { get; set; }
    public string LoginName { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; }
    public string UserEvent { get; set; }
    public string Message { get; set; }
    public string WorkflowOperationId { get; set; }
    public string WorkflowOperationGroupId { get; set; }
    public string InfraObjectId { get; set; }
    public string ActionId { get; set; }
    public int ConditionActionId { get; set; }
    public bool SkipStep { get; set; }
    public int IsReload { get; set; }
    public string Direction { get; set; }
}