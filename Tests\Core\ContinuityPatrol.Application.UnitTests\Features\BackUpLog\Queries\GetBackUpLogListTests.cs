using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUpLog.Queries;

public class GetBackUpLogListTests : IClassFixture<BackUpLogFixture>
{
    private readonly BackUpLogFixture _backUpLogFixture;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly GetBackUpLogListQueryHandler _handler;

    public GetBackUpLogListTests(BackUpLogFixture backUpLogFixture)
    {
        _backUpLogFixture = backUpLogFixture;
        _mockBackUpLogRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(_backUpLogFixture.BackUpLogs);

        _handler = new GetBackUpLogListQueryHandler(
            _backUpLogFixture.Mapper,
            _mockBackUpLogRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnBackUpLogList_When_BackUpLogsExist()
    {
        // Arrange
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<BackUpLogListVm>>();
        result.Count.ShouldBeGreaterThan(0);

        _mockBackUpLogRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoBackUpLogsExist()
    {
        // Arrange
        var emptyBackUpLogs = new List<Domain.Entities.BackUpLog>();
        var mockEmptyRepository = BackUpLogRepositoryMocks.CreateBackUpLogRepository(emptyBackUpLogs);
        var handler = new GetBackUpLogListQueryHandler(_backUpLogFixture.Mapper, mockEmptyRepository.Object);
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<BackUpLogListVm>>();
        result.Count.ShouldBe(0);

        mockEmptyRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectMappedData_When_BackUpLogsExist()
    {
        // Arrange
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        var firstResult = result.First();
        var firstBackUpLog = _backUpLogFixture.BackUpLogs.First();

        firstResult.ShouldNotBeNull();
        firstResult.Id.ShouldBe(firstBackUpLog.ReferenceId);
        firstResult.HostName.ShouldBe(firstBackUpLog.HostName);
        firstResult.DatabaseName.ShouldBe(firstBackUpLog.DatabaseName);
        firstResult.UserName.ShouldBe(firstBackUpLog.UserName);
        firstResult.IsLocalServer.ShouldBe(firstBackUpLog.IsLocalServer);
        firstResult.IsBackUpServer.ShouldBe(firstBackUpLog.IsBackUpServer);
        firstResult.BackUpPath.ShouldBe(firstBackUpLog.BackUpPath);
        firstResult.Type.ShouldBe(firstBackUpLog.Type);
        firstResult.Status.ShouldBe(firstBackUpLog.Status);
    }

    [Fact]
    public async Task Handle_ReturnAllActiveBackUpLogs_When_MultipleLogsExist()
    {
        // Arrange
        var activeBackUpLogs = _backUpLogFixture.BackUpLogs.Where(x => x.IsActive).ToList();
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(activeBackUpLogs.Count);

        foreach (var backUpLog in activeBackUpLogs)
        {
            var mappedLog = result.FirstOrDefault(x => x.Id == backUpLog.ReferenceId);
            mappedLog.ShouldNotBeNull();
            mappedLog.HostName.ShouldBe(backUpLog.HostName);
            mappedLog.DatabaseName.ShouldBe(backUpLog.DatabaseName);
            mappedLog.Type.ShouldBe(backUpLog.Type);
            mappedLog.Status.ShouldBe(backUpLog.Status);
        }
    }

    [Fact]
    public async Task Handle_ReturnFullBackupLogs_When_FullBackupsExist()
    {
        // Arrange
        var fullBackupLogs = _backUpLogFixture.BackUpLogs.Where(x => x.Type == "Full" && x.IsActive).ToList();
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var fullResults = result.Where(x => x.Type == "Full").ToList();
        fullResults.Count.ShouldBe(fullBackupLogs.Count);

        foreach (var fullBackupLog in fullBackupLogs)
        {
            var mappedLog = fullResults.FirstOrDefault(x => x.Id == fullBackupLog.ReferenceId);
            mappedLog.ShouldNotBeNull();
            mappedLog.Type.ShouldBe("Full");
        }
    }

    [Fact]
    public async Task Handle_ReturnDifferentialBackupLogs_When_DifferentialBackupsExist()
    {
        // Arrange
        // Add differential backup logs to the fixture
        var differentialBackupLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "DiffServer",
            DatabaseName = "DiffDatabase",
            UserName = "DiffUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\DiffDatabase.bak",
            Type = "Differential",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(differentialBackupLog);

        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var differentialResults = result.Where(x => x.Type == "Differential").ToList();
        differentialResults.Count.ShouldBeGreaterThan(0);

        var mappedDifferentialLog = differentialResults.FirstOrDefault(x => x.Id == differentialBackupLog.ReferenceId);
        mappedDifferentialLog.ShouldNotBeNull();
        mappedDifferentialLog.Type.ShouldBe("Differential");
        mappedDifferentialLog.DatabaseName.ShouldBe("DiffDatabase");
    }

    [Fact]
    public async Task Handle_ReturnTransactionLogBackups_When_TransactionLogBackupsExist()
    {
        // Arrange
        // Add transaction log backup to the fixture
        var transactionLogBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "LogServer",
            DatabaseName = "LogDatabase",
            UserName = "LogUser",
            IsLocalServer = true,
            IsBackUpServer = true,
            BackUpPath = @"E:\Logs\LogDatabase.trn",
            Type = "Transaction Log",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(transactionLogBackup);

        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var transactionLogResults = result.Where(x => x.Type == "Transaction Log").ToList();
        transactionLogResults.Count.ShouldBeGreaterThan(0);

        var mappedTransactionLog = transactionLogResults.FirstOrDefault(x => x.Id == transactionLogBackup.ReferenceId);
        mappedTransactionLog.ShouldNotBeNull();
        mappedTransactionLog.Type.ShouldBe("Transaction Log");
        mappedTransactionLog.BackUpPath.ShouldEndWith(".trn");
    }

    [Fact]
    public async Task Handle_ReturnCompletedBackups_When_CompletedBackupsExist()
    {
        // Arrange
        var completedBackups = _backUpLogFixture.BackUpLogs.Where(x => x.Status == "Completed" && x.IsActive).ToList();
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var completedResults = result.Where(x => x.Status == "Completed").ToList();
        completedResults.Count.ShouldBe(completedBackups.Count);

        foreach (var completedBackup in completedBackups)
        {
            var mappedLog = completedResults.FirstOrDefault(x => x.Id == completedBackup.ReferenceId);
            mappedLog.ShouldNotBeNull();
            mappedLog.Status.ShouldBe("Completed");
        }
    }

    [Fact]
    public async Task Handle_ReturnFailedBackups_When_FailedBackupsExist()
    {
        // Arrange
        // Add failed backup to the fixture
        var failedBackup = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "FailedServer",
            DatabaseName = "FailedDatabase",
            UserName = "FailedUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\FailedDatabase.bak",
            Type = "Full",
            Status = "Failed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(failedBackup);

        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var failedResults = result.Where(x => x.Status == "Failed").ToList();
        failedResults.Count.ShouldBeGreaterThan(0);

        var mappedFailedBackup = failedResults.FirstOrDefault(x => x.Id == failedBackup.ReferenceId);
        mappedFailedBackup.ShouldNotBeNull();
        mappedFailedBackup.Status.ShouldBe("Failed");
        mappedFailedBackup.DatabaseName.ShouldBe("FailedDatabase");
    }

    [Fact]
    public async Task Handle_ReturnLocalServerBackups_When_LocalServerBackupsExist()
    {
        // Arrange
        var localServerBackups = _backUpLogFixture.BackUpLogs.Where(x => x.IsLocalServer && x.IsActive).ToList();
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var localServerResults = result.Where(x => x.IsLocalServer).ToList();
        localServerResults.Count.ShouldBe(localServerBackups.Count);

        foreach (var localServerBackup in localServerBackups)
        {
            var mappedLog = localServerResults.FirstOrDefault(x => x.Id == localServerBackup.ReferenceId);
            mappedLog.ShouldNotBeNull();
            mappedLog.IsLocalServer.ShouldBeTrue();
        }
    }

    [Fact]
    public async Task Handle_ReturnBackupServerLogs_When_BackupServerLogsExist()
    {
        // Arrange
        // Add backup server log to the fixture
        var backupServerLog = new Domain.Entities.BackUpLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "BackupServer",
            DatabaseName = "BackupDatabase",
            UserName = "BackupUser",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\BackupServer\Backups\BackupDatabase.bak",
            Type = "Full",
            Status = "Completed",
            IsActive = true
        };
        _backUpLogFixture.BackUpLogs.Add(backupServerLog);

        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        
        var backupServerResults = result.Where(x => x.IsBackUpServer).ToList();
        backupServerResults.Count.ShouldBeGreaterThan(0);

        var mappedBackupServerLog = backupServerResults.FirstOrDefault(x => x.Id == backupServerLog.ReferenceId);
        mappedBackupServerLog.ShouldNotBeNull();
        mappedBackupServerLog.IsBackUpServer.ShouldBeTrue();
        mappedBackupServerLog.IsLocalServer.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ReturnLogsWithProperties_When_LogsHaveProperties()
    {
        // Arrange
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        var logsWithProperties = result.Where(x => !string.IsNullOrEmpty(x.Properties)).ToList();
        logsWithProperties.Count.ShouldBeGreaterThan(0);

        foreach (var log in logsWithProperties)
        {
            log.Properties.ShouldNotBeNullOrEmpty();
            // Verify it's valid JSON format
            log.Properties.ShouldStartWith("{");
            log.Properties.ShouldEndWith("}");
        }
    }

    [Fact]
    public async Task Handle_ReturnLogsWithPaths_When_LogsHavePaths()
    {
        // Arrange
        var query = new GetBackUpLogListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);

        foreach (var log in result)
        {
            log.BackUpPath.ShouldNotBeNullOrEmpty();
            
            // Verify path format (either local or UNC)
            var isLocalPath = log.BackUpPath.Length >= 3 && log.BackUpPath[1] == ':';
            var isUncPath = log.BackUpPath.StartsWith(@"\\");
            (isLocalPath || isUncPath).ShouldBeTrue();
        }
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_QueryExecuted()
    {
        // Arrange
        var query = new GetBackUpLogListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBackUpLogRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockBackUpLogRepository.VerifyNoOtherCalls();
    }
}
