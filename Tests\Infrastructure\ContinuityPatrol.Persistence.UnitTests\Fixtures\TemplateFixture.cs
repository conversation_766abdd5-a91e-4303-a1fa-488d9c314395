using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TemplateFixture : IDisposable
{
    public List<Template> TemplatePaginationList { get; set; }
    public List<Template> TemplateList { get; set; }
    public Template TemplateDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TemplateFixture()
    {
        var fixture = new Fixture();

        TemplateList = fixture.Create<List<Template>>();

        TemplatePaginationList = fixture.CreateMany<Template>(20).ToList();

        TemplatePaginationList.ForEach(x => x.CompanyId = CompanyId);

        TemplateList.ForEach(x => x.CompanyId = CompanyId);

        TemplateDto = fixture.Create<Template>();

        TemplateDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public Template CreateTemplate(
        string name = "Default Template",
        string companyId = "COMPANY_123",
        string properties = null,
        string icon = "default-icon.png",
        string version = "1.0",
        string type = "WORKFLOW",
        string replicationTypeId = "REPL_001",
        string replicationTypeName = "Default Replication",
        string replicationCategoryTypeId = "CAT_001",
        string replicationCategoryTypeName = "Default Category",
        string subTypeId = "SUB_001",
        string subTypeName = "Default SubType",
        string description = "Default Description",
        string actionType = "CREATE",
        bool isActive = true,
        bool isDelete = false)
    {
        return new Template
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            CompanyId = companyId,
            Properties = properties ?? "{\"type\": \"template\", \"status\": \"active\", \"lastModified\": \"2024-01-01T10:00:00Z\"}",
            Icon = icon,
            Version = version,
            Type = type,
            ReplicationTypeId = replicationTypeId,
            ReplicationTypeName = replicationTypeName,
            ReplicationCategoryTypeId = replicationCategoryTypeId,
            ReplicationCategoryTypeName = replicationCategoryTypeName,
            SubTypeId = subTypeId,
            SubTypeName = subTypeName,
            Description = description,
            ActionType = actionType,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<Template> CreateMultipleTemplates(int count, string companyId = "COMPANY_123")
    {
        var templates = new List<Template>();
        for (int i = 1; i <= count; i++)
        {
            templates.Add(CreateTemplate(
                name: $"Template {i}",
                companyId: companyId,
                version: $"{i}.0",
                replicationTypeId: $"REPL_{i:D3}",
                replicationTypeName: $"Replication {i}",
                actionType: i % 2 == 1 ? "CREATE" : "UPDATE"
            ));
        }
        return templates;
    }

    public Template CreateTemplateWithSpecificId(string referenceId, string name = "Test Template")
    {
        return new Template
        {
            ReferenceId = referenceId,
            Name = name,
            CompanyId = "COMPANY_123",
            Properties = "{\"test\": true}",
            Icon = "test-icon.png",
            Version = "1.0",
            Type = "WORKFLOW",
            ReplicationTypeId = "REPL_TEST",
            ReplicationTypeName = "Test Replication",
            ReplicationCategoryTypeId = "CAT_TEST",
            ReplicationCategoryTypeName = "Test Category",
            SubTypeId = "SUB_TEST",
            SubTypeName = "Test SubType",
            Description = "Test Description",
            ActionType = "CREATE",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public Template CreateTemplateForCompany(string companyId, string name = null)
    {
        return CreateTemplate(
            name: name ?? $"Template for {companyId}",
            companyId: companyId,
            description: $"Template for company {companyId}"
        );
    }

    public List<Template> CreateTemplatesForCompanies(List<string> companyIds)
    {
        var templates = new List<Template>();
        foreach (var companyId in companyIds)
        {
            templates.Add(CreateTemplateForCompany(companyId));
        }
        return templates;
    }

    public List<Template> CreateTemplatesWithStatus(int activeCount, int inactiveCount, string companyId = "COMPANY_123")
    {
        var templates = new List<Template>();

        for (int i = 1; i <= activeCount; i++)
        {
            templates.Add(CreateTemplate(
                name: $"Active Template {i}",
                companyId: companyId,
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            templates.Add(CreateTemplate(
                name: $"Inactive Template {i}",
                companyId: companyId,
                isActive: false
            ));
        }

        return templates;
    }

    public Template CreateWorkflowTemplate(string companyId = "COMPANY_123", string name = "Workflow Template")
    {
        return CreateTemplate(
            name: name,
            companyId: companyId,
            type: "WORKFLOW",
            actionType: "CREATE",
            icon: "workflow-icon.png",
            properties: "{\"type\": \"workflow\", \"steps\": 5, \"complexity\": \"medium\"}"
        );
    }

    public Template CreateFormTemplate(string companyId = "COMPANY_123", string name = "Form Template")
    {
        return CreateTemplate(
            name: name,
            companyId: companyId,
            type: "FORM",
            actionType: "CREATE",
            icon: "form-icon.png",
            properties: "{\"type\": \"form\", \"fields\": 10, \"validation\": true\"}"
        );
    }

    public Template CreateReportTemplate(string companyId = "COMPANY_123", string name = "Report Template")
    {
        return CreateTemplate(
            name: name,
            companyId: companyId,
            type: "REPORT",
            actionType: "CREATE",
            icon: "report-icon.png",
            properties: "{\"type\": \"report\", \"charts\": 3, \"tables\": 2}"
        );
    }

    public List<Template> CreateStandardTemplates(string companyId = "COMPANY_123")
    {
        return new List<Template>
        {
            CreateWorkflowTemplate(companyId),
            CreateFormTemplate(companyId),
            CreateReportTemplate(companyId)
        };
    }

    public Template CreateTemplateWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateTemplate(properties: propertiesJson);
    }

    public Template CreateTemplateForReplicationType(string replicationTypeId, string replicationTypeName = null, string actionType = "CREATE")
    {
        return CreateTemplate(
            name: $"Template for {replicationTypeId}",
            replicationTypeId: replicationTypeId,
            replicationTypeName: replicationTypeName ?? $"Replication {replicationTypeId}",
            actionType: actionType
        );
    }

    public List<Template> CreateTemplatesForReplicationType(string replicationTypeId, List<string> actionTypes)
    {
        var templates = new List<Template>();
        foreach (var actionType in actionTypes)
        {
            templates.Add(CreateTemplateForReplicationType(replicationTypeId, actionType: actionType));
        }
        return templates;
    }

    public ComponentType CreateComponentType(
        string componentName = "Default Component",
        string referenceId = null,
        bool isActive = true)
    {
        return new ComponentType
        {
            ReferenceId = referenceId ?? Guid.NewGuid().ToString(),
            ComponentName = componentName,
            FormTypeId = "FORM_001",
            FormTypeName = "Default Form Type",
            Properties = "{\"type\": \"component\", \"status\": \"active\"}",
            Logo = "component-logo.png",
            Version = "1.0",
            ComponentProperties = "{\"config\": \"default\"}",
            IsDatabase = false,
            IsReplication = true,
            IsServer = false,
            IsCustom = false,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<ComponentType> CreateComponentTypesForTemplates(List<string> replicationTypeIds)
    {
        var componentTypes = new List<ComponentType>();
        foreach (var replicationTypeId in replicationTypeIds)
        {
            componentTypes.Add(CreateComponentType(
                componentName: $"Component for {replicationTypeId}",
                referenceId: replicationTypeId
            ));
        }
        return componentTypes;
    }

    public Template CreateMinimalTemplate()
    {
        return new Template
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Minimal Template",
            CompanyId = "COMPANY_123",
            Type = "WORKFLOW",
            ActionType = "CREATE",
            Version = "1.0",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public Template CreateTemplateForTesting(
        string testName,
        string companyId = "COMPANY_123",
        string name = null)
    {
        return CreateTemplate(
            name: name ?? $"Test Template for {testName}",
            companyId: companyId,
            description: $"Template for {testName}",
            replicationTypeId: $"REPL_{testName.ToUpper()}",
            replicationTypeName: $"Replication for {testName}"
        );
    }

    public List<Template> CreateTemplatesForOrdering(string companyId = "COMPANY_123")
    {
        return new List<Template>
        {
            CreateTemplate(name: "Template Z", companyId: companyId),
            CreateTemplate(name: "Template A", companyId: companyId),
            CreateTemplate(name: "Template M", companyId: companyId),
            CreateTemplate(name: "Template B", companyId: companyId)
        };
    }

    public Template CreateTemplateWithNullValues(string companyId = "COMPANY_123")
    {
        return CreateTemplate(
            name: "Template with Nulls",
            companyId: companyId,
            properties: null,
            icon: null,
            description: null,
            replicationTypeName: null,
            replicationCategoryTypeName: null,
            subTypeName: null
        );
    }

    public Template CreateTemplateWithLongValues(string companyId = "COMPANY_123")
    {
        var longName = string.Join(" ", Enumerable.Repeat("VeryLongTemplateName", 5));
        var longDescription = string.Join(" ", Enumerable.Repeat("Very long description text", 20));

        return CreateTemplate(
            name: longName,
            companyId: companyId,
            description: longDescription
        );
    }

    public Template CreateTemplateWithSpecialCharacters(string companyId = "COMPANY_123")
    {
        return CreateTemplate(
            name: "Template-Name_With@Special#Characters",
            companyId: companyId,
            description: "Description with special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?",
            replicationTypeName: "Replication-Type_With@Special#Characters"
        );
    }

    public List<Template> CreateTemplatesForActionTypes(string replicationTypeId = "REPL_001", string companyId = "COMPANY_123")
    {
        return new List<Template>
        {
            CreateTemplate(name: "Create Template", companyId: companyId, replicationTypeId: replicationTypeId, actionType: "CREATE"),
            CreateTemplate(name: "Update Template", companyId: companyId, replicationTypeId: replicationTypeId, actionType: "UPDATE"),
            CreateTemplate(name: "Delete Template", companyId: companyId, replicationTypeId: replicationTypeId, actionType: "DELETE"),
            CreateTemplate(name: "Execute Template", companyId: companyId, replicationTypeId: replicationTypeId, actionType: "EXECUTE")
        };
    }

    public Template CreateTemplateWithAuditInfo(
        string createdBy = "CREATOR_USER",
        DateTime? createdDate = null,
        string lastModifiedBy = "MODIFIER_USER",
        DateTime? lastModifiedDate = null)
    {
        var now = DateTime.UtcNow;
        var template = CreateTemplate();
        template.CreatedBy = createdBy;
        template.CreatedDate = createdDate ?? now.AddDays(-1);
        template.LastModifiedBy = lastModifiedBy;
        template.LastModifiedDate = lastModifiedDate ?? now;
        return template;
    }

    public List<Template> CreateTemplatesForPagination(int count, string companyId = "COMPANY_123")
    {
        var templates = new List<Template>();
        for (int i = 1; i <= count; i++)
        {
            templates.Add(CreateTemplate(
                name: $"Pagination Template {i:D3}",
                companyId: companyId,
                version: $"{i}.0",
                description: $"Template {i} for pagination testing"
            ));
        }
        return templates;
    }

    public Template CreateTemplateForValidationTest(
        string name = null,
        string companyId = null,
        string replicationTypeId = null)
    {
        return new Template
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            CompanyId = companyId,
            ReplicationTypeId = replicationTypeId,
            Type = "WORKFLOW",
            ActionType = "CREATE",
            Version = "1.0",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
