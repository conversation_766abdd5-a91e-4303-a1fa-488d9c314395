﻿
using System.Linq;

namespace ContinuityPatrol.Application.Features.Job.Queries.GetSolutionType;

public class GetSolutionTypeListByPolicyNameQueryHandler:IRequestHandler<GetSolutionTypeListByPolicyNameQuery,List<SolutionTypeListByPolicyNameVm>>
{
    private readonly IComponentTypeRepository _componentTypeRepository;
    private readonly IJobRepository _jobRepository;
    private readonly IMapper _mapper;

    public GetSolutionTypeListByPolicyNameQueryHandler(IComponentTypeRepository componentTypeRepository,IJobRepository jobRepository,IMapper mappaer)
    {
        _componentTypeRepository = componentTypeRepository;
        _jobRepository = jobRepository;
        _mapper = mappaer;
        
    }

    public async  Task<List<SolutionTypeListByPolicyNameVm>> Handle(GetSolutionTypeListByPolicyNameQuery request, CancellationToken cancellationToken)
    {
        var componentTypes = await _componentTypeRepository.GetComponentTypeListByName("Replication");

        var result = _mapper.Map<List<SolutionTypeListByPolicyNameVm>>(componentTypes);

        if (request.Policy == "1" || request.Policy == "2")
        {
            var jobList = request.Policy == "2"
                ? await _jobRepository.ListAllAsync()
                : await _jobRepository.GetByPolicy("2");

            result.ForEach(c => c.IsConfigured = jobList.Any(x=>x.SolutionTypeId.Equals(c.Id)));
        }

        return result;
    }
}
