﻿let colorPickerVisible = false, globalUserRoleId = '', colorMap = {};

const userRoleURL = {
    createOrUpdate: "Admin/UserRole/CreateOrUpdate",
    nameExistUrl: "Admin/UserRole/IsRoleNameExist"
};

$(function () {
    const createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
    if (createPermission === 'false') {
        $(".btn-userrole-Create")
            .addClass('btn-disabled')
            .css("cursor", "not-allowed")
            .removeAttr('data-bs-toggle data-bs-target id');
    }

    btnCrudEnable('btnURSave');
    btnCrudEnable('confirmDeleteButton'); 

    $(".accessMGLink-button").on('click', function () {
        sessionStorage.setItem('userRoleId', $(this).data('user-id'));        
    });

    $('#totalListCount').text($(".roleData").length);

    var table = $('#UserRoleList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow"></i>',
                previous: '<i class="cp-left-arrow"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
    });

    $('#search-inp').on('keyup', function () {
        table.search($(this).val()).draw();
    });

    $('#btnCreateUserRole').on('click', function () {
        globalUserRoleId = '';       
        $("#cpRoleName").val('')
        $('#nameError').text('').removeClass('field-validation-error');
        $('#btnURSave').text('Save');
        $('#colorTable [type="radio"]').prop('checked', false)
        $('#multiCollapseExample1').collapse('hide');
        $('#CreateModal').modal('show');       
    });
    document.getElementById('cpRoleName').addEventListener('keypress', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
    });
    //Save Function
    $("#btnURSave").on('click', async function () {
        let roleName = $("#cpRoleName").val();
        let isNameValid = await validateName(roleName, globalUserRoleId, userRoleURL.nameExistUrl);
        let logo = $('.dynamicColor.active').css('background-color');

        if (isNameValid) {
            let data = {
                'Id': globalUserRoleId,
                'Role': roleName,
                'Logo': logo?.toString(),
                'IsDelete': true,
                __RequestVerificationToken: gettoken()
            };

            createOrUpdate(data);
            $('#CreateModal').modal('hide');

            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    });
    async function createOrUpdate(formData) {
        btnCrudDiasable('btnURSave');
        await $.ajax({
            url: RootUrl + userRoleURL.createOrUpdate,
            type: "POST",
            data: formData,
            async: true,
            success: function (result) {
                if (result) {
                    notificationAlert('success', result.data.message);
                }
            }
        });
        btnCrudEnable('btnURSave');
    }

    $('input[type="radio"][name="color"]').on('click', function () {
        let colorId = $(this).attr('id');
        let spanStyle = $('label[for="' + colorId + '"] span').css('background-color');
        let rgbArray = spanStyle.match(/\d+/g);
        let rgbString = 'rgb(' + rgbArray.join(', ') + ')';
        $('#textLogo').val(rgbString);
    });

    $('#colorTable input[type="radio"]').each(function () {
        let id = $(this).attr('id');
        let spanStyle = $('label[for="' + id + '"] span').css('background-color');
        let rgbArray = spanStyle.match(/\d+/g);
        let rgbValue = `${rgbArray[0]},${rgbArray[1]},${rgbArray[2]}`;
        colorMap[rgbValue] = id;
    });

    function getColorIdFromRGB(rgbValue, colorMap) {
        let rgbArray = rgbValue.match(/\d+/g);
        let r = parseInt(rgbArray[0]);
        let g = parseInt(rgbArray[1]);
        let b = parseInt(rgbArray[2]);
        return colorMap[`${r},${g},${b}`] || 'unknown';
    }


    //Update
    $('.edit-button').on('click', function () {
        $('#nameError').text('').removeClass('field-validation-error');
        let userData = $(this).data('role');
        populateModalFields(userData);
        $('#btnURSave').text('Update');
        $('#CreateModal').modal('show');
    });
    function populateModalFields(userData) {
        $('#cpRoleName').val(userData.role);
        globalUserRoleId = userData.id;
        let rgbString = userData.logo || 'rgb(0,0,255)';
        let convertedColorId = getColorIdFromRGB(rgbString, colorMap);
        $('#' + convertedColorId).prop('checked', true);
    }

    //Validation
    $(document).on('keyup input', '#cpRoleName', commonDebounce(async function () {
        let userRoleId = $('#textUpdateId').val();
        let value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, userRoleId, userRoleURL.nameExistUrl);
    }, 400));

    $('.delete-button').on('click', function () {
        let userRoleId = $(this).data('user-id');
        let userRole = $(this).data('user-role');
        $('#deleteData').text(userRole);
        $('#textDeleteId').val(userRoleId);
    });

});

async function validateName(value, id = null, url) {   
    const errorElement = $('#nameError');

    if (!value) {
        errorElement.text('Enter role name').addClass('field-validation-error');
        return false;
    } else {
        if (value.includes("<")) {
            errorElement.text('Special characters not allowed');
            errorElement.addClass('field-validation-error');
            return false;
        }

        let data = {
            'userRoleName': value,
            'id' : id
        };

        const validationResults = [
            SpecialCharValidate(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotBeginWithNumber(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxlength(value),
            secondChar(value),
            await IsNameExist(RootUrl + url, data, OnError)
        ];
        return CommonValidation(errorElement, validationResults)
    }
    
}

async function IsNameExist(url, data, OnError) {
    let nameExist = !data.userRoleName.trim() ? true : (await GetAsync(url, data, OnError)) ? "Name already exists" : true;
    return nameExist;
}
 
$('#multiCollapseExample1Color').on('click', function (e) {
    if (!colorPickerVisible) {
        $('#multiCollapseExample1').collapse('show');
        colorPickerVisible = true;                   
    } else {
        e.preventDefault();
    }
});

$(document).on('click', '.dynamicColor', function (e) {
    $('.dynamicColor').removeClass('active');
    $(this).addClass('active');              
});
   
$(document).on('click', function (event) {
    if (
        !$(event.target).closest('#multiCollapseExample1Color, #multiCollapseExample1').length &&
        colorPickerVisible
    ) {
        $('#multiCollapseExample1').collapse('hide');
        colorPickerVisible = false;
    }
});

