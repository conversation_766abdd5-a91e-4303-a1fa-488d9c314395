﻿using ContinuityPatrol.Application.Features.ServerSubType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ServerSubTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerSubType.Queries;


public class GetServerSubTypePaginatedListQueryHandlerTests : IClassFixture<ServerSubTypeFixture>
{
    private readonly ServerSubTypeFixture _serverSubTypeFixture;

    private readonly Mock<IServerSubTypeRepository> _mockServerSubTypeRepository;

    private readonly GetServerSubTypePaginatedListQueryHandler _handler;

    public GetServerSubTypePaginatedListQueryHandlerTests(ServerSubTypeFixture serverSubTypeFixture)
    {
        _serverSubTypeFixture = serverSubTypeFixture;

        _serverSubTypeFixture.ServerSubTypes[0].Name = "ServerSub_Type";

        _serverSubTypeFixture.ServerSubTypes[1].Name = "Testing_ServerSub_Type";

        _mockServerSubTypeRepository = ServerSubTypeRepositoryMocks.GetPaginatedServerSubTypeRepository(_serverSubTypeFixture.ServerSubTypes);

        _handler = new GetServerSubTypePaginatedListQueryHandler(_serverSubTypeFixture.Mapper, _mockServerSubTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetServerSubTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerSubTypeListVm>>();

        result.TotalCount.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetServerSubTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerSubTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_ServerSubTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetServerSubTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=ServerSub_Type" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerSubTypeListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBe(_serverSubTypeFixture.ServerSubTypes[0].ReferenceId);

        result.Data[0].Name.ShouldBe("ServerSub_Type");
    }

    [Fact]
    public async Task Handle_Return_PaginatedServerSubTypes_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetServerSubTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ServerSubTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ServerSubTypeListVm>();

        result.Data[0].Name.ShouldBe("Testing_ServerSub_Type");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetServerSubTypePaginatedListQuery(), CancellationToken.None);

        _mockServerSubTypeRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
