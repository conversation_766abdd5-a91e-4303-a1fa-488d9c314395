﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAlert.Events.Pagination;

public class CyberAlertPaginatedEventHandler : INotificationHandler<CyberAlertPaginatedEvent>
{
    private readonly ILogger<CyberAlertPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAlertPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<CyberAlertPaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberAlertPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.CyberAlert}",
            Entity = Modules.CyberAlert.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency Alert viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency Alert viewed");
    }
}