﻿using ContinuityPatrol.Application.Features.ReportSchedule.Event.Update;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;

public class
    UpdateReportScheduleCommandHandler : IRequestHandler<UpdateReportScheduleCommand, UpdateReportScheduleResponse>
{
    private readonly IJobScheduler _client;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public UpdateReportScheduleCommandHandler(IMapper mapper, IPublisher publisher,
        IReportScheduleRepository reportScheduleRepository, ILoadBalancerRepository nodeConfigurationRepository, IJobScheduler client)
    {
        _mapper = mapper;
        _publisher = publisher;
        _reportScheduleRepository = reportScheduleRepository;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _client = client;
    }

    public async Task<UpdateReportScheduleResponse> Handle(UpdateReportScheduleCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _reportScheduleRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ReportSchedule), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateReportScheduleCommand),
            typeof(Domain.Entities.ReportSchedule));
        await _reportScheduleRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(new ReportScheduleUpdateEvent { ReportName = eventToUpdate.ReportName },
            cancellationToken);

        var nodeConfig =
            await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _nodeConfigurationRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.MonitorService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is not null)
        {
            var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

            var url = UrlHelper.GenerateScheduleReportUrl(baseUrl, eventToUpdate.ReferenceId);

           await _client.ScheduleJob(eventToUpdate.ReferenceId, new Dictionary<string, string> { ["url"] = url });
        }

        var response = new UpdateReportScheduleResponse
        {
            Message = Message.Update("Report Scheduler", eventToUpdate.ReportName),

            Id = eventToUpdate.ReferenceId
        };
       

        return response;
    }
}