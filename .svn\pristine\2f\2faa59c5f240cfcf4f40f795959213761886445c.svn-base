﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.BusinessService.Queries.GetdetailByName;

public class
    GetBusinessServiceDetailByNameQueryHandler : IRequestHandler<GetBusinessServiceDetailByNameQuery,
        BusinessServiceDetailVm>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceDetailByNameQueryHandler(IMapper mapper,
        IBusinessServiceRepository businessServiceRepository)
    {
        _mapper = mapper;
        _businessServiceRepository = businessServiceRepository;
    }

    public Task<BusinessServiceDetailVm> Handle(GetBusinessServiceDetailByNameQuery request,
        CancellationToken cancellationToken)
    {
        var businessService =
            _businessServiceRepository.GetPaginatedQuery(); //GetBusinessServicesByName(request.Name);


        var businessServiceDetailDto = businessService
            .Where(m => m.Name.ToLower().Equals(request.Name))
            .Select(m => _mapper.Map<BusinessServiceDetailVm>(m)).SingleOrDefault();


        //  var businessServiceDetailDto = _mapper.Map<BusinessServiceDetailVm>(businessService);
        return Task.FromResult(businessServiceDetailDto ??
                               throw new NotFoundException(nameof(BusinessService), request.Name));
    }
}