using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class NodeWorkflowExecutionFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string NodeId => "NODE_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string JobId => "JOB_123";
    public static string InfraSchedulerId => "INFRA_SCHEDULER_123";
    public static string WorkflowOperationId => "WORKFLOW_OPERATION_123";

    public List<NodeWorkflowExecution> NodeWorkflowExecutionPaginationList { get; set; }
    public List<NodeWorkflowExecution> NodeWorkflowExecutionList { get; set; }
    public NodeWorkflowExecution NodeWorkflowExecutionDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public NodeWorkflowExecutionFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<NodeWorkflowExecution>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.NodeId, () => NodeId)
            .With(x => x.NodeName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.ProfileId, () => _fixture.Create<string>())
            .With(x => x.ProfileName, () => _fixture.Create<string>())
            .With(x => x.JobId, () => JobId)
            .With(x => x.JobName, () => _fixture.Create<string>())
            .With(x => x.InfraObjectSchedulerId, () => InfraSchedulerId)
            .With(x => x.InfraObjectSchedulerName, () => _fixture.Create<string>())
            .With(x => x.WorkflowOperationId, () => WorkflowOperationId)
            .With(x => x.Status, () => "Running")
            .With(x => x.Type, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
    );

        NodeWorkflowExecutionPaginationList = _fixture.CreateMany<NodeWorkflowExecution>(20).ToList();
        NodeWorkflowExecutionList = _fixture.CreateMany<NodeWorkflowExecution>(5).ToList();
        NodeWorkflowExecutionDto = _fixture.Create<NodeWorkflowExecution>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public NodeWorkflowExecution CreateNodeWorkflowExecutionWithProperties(
        string nodeId = null,
        string workflowId = null,
        string jobId = null,
        string infraSchedulerId = null,
        string workflowOperationId = null,
        string status = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<NodeWorkflowExecution>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.NodeId, nodeId ?? NodeId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.JobId, jobId ?? JobId)
            .With(x => x.InfraObjectSchedulerId, infraSchedulerId ?? InfraSchedulerId)
            .With(x => x.WorkflowOperationId, workflowOperationId ?? WorkflowOperationId)
            .With(x => x.Status, status ?? "Running")
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public List<NodeWorkflowExecution> CreateMultipleNodeWorkflowExecutionsWithSameWorkflowOperationId(string workflowOperationId, int count)
    {
        var executions = new List<NodeWorkflowExecution>();
        for (int i = 0; i < count; i++)
        {
            executions.Add(CreateNodeWorkflowExecutionWithProperties(workflowOperationId: workflowOperationId, isActive: true));
        }
        return executions;
    }

    public List<NodeWorkflowExecution> CreateNodeWorkflowExecutionsWithMixedActiveStatus(string workflowOperationId)
    {
        return new List<NodeWorkflowExecution>
        {
            CreateNodeWorkflowExecutionWithProperties(workflowOperationId: workflowOperationId, isActive: true),
            CreateNodeWorkflowExecutionWithProperties(workflowOperationId: workflowOperationId, isActive: false),
            CreateNodeWorkflowExecutionWithProperties(workflowOperationId: workflowOperationId, isActive: true)
        };
    }

    public List<NodeWorkflowExecution> CreateNodeWorkflowExecutionsWithDifferentStatuses()
    {
        return new List<NodeWorkflowExecution>
        {
            CreateNodeWorkflowExecutionWithProperties(status: "Running"),
            CreateNodeWorkflowExecutionWithProperties(status: "Completed"),
            CreateNodeWorkflowExecutionWithProperties(status: "Failed"),
            CreateNodeWorkflowExecutionWithProperties(status: "Pending")
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonStatuses = { "Running", "Completed", "Failed", "Pending", "Cancelled" };
        public static readonly string[] CommonNodeIds = { "NODE_001", "NODE_002", "NODE_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] CommonJobIds = { "JOB_001", "JOB_002", "JOB_003" };
    }
}
