﻿using System.Linq.Expressions;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;

namespace ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetByGroupNameAndLinkedStatus;

public class GetCyberSnapsByGroupNameAndLinkedStatusQueryHandler:IRequestHandler<GetCyberSnapsByGroupNameAndLinkedStatusQuery,List<CyberSnapsListVm>>
{
    private readonly ICyberSnapsRepository _cyberSnapsRepository;
    private readonly IMapper _mapper;

    public GetCyberSnapsByGroupNameAndLinkedStatusQueryHandler(ICyberSnapsRepository cyberSnapsRepository,IMapper mapper)
    {
        _cyberSnapsRepository = cyberSnapsRepository;
        _mapper=mapper;
    }

    public async Task<List<CyberSnapsListVm>> Handle(GetCyberSnapsByGroupNameAndLinkedStatusQuery request, CancellationToken cancellationToken)
    {
        var filterExpression = BuildFilterExpression(request);

        var cyberSnap = await _cyberSnapsRepository.GetCyberSnapsByStorageGroupNameAndLinkedStatus(filterExpression);

        if (cyberSnap.Any())
            return _mapper.Map<List<CyberSnapsListVm>>(cyberSnap);

        return new List<CyberSnapsListVm>();
    }
    private Expression<Func<Domain.Entities.CyberSnaps, bool>> BuildFilterExpression(GetCyberSnapsByGroupNameAndLinkedStatusQuery request)
    {

        Expression<Func<Domain.Entities.CyberSnaps, bool>> expression = x => true;

        if (request.SnapshotName.IsNotNullOrWhiteSpace())
        {
            expression = expression.And(x => x.SnapshotName.ToLower().Equals(request.SnapshotName.ToLower()));
        }
        if (request.StorageGroupName.IsNotNullOrWhiteSpace())
        {
            expression = expression.And(x => x.StorageGroupName.ToLower().Equals(request.StorageGroupName.ToLower()));
        }
        if (request.LinkedStatus.IsNotNullOrWhiteSpace())
        {
            expression = expression.And(x => x.LinkedStatus.ToLower().Equals(request.LinkedStatus.ToLower()));
        }
        return expression;
    }
}

