﻿using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Event.PaginatedView;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class GroupNodePolicyController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<GroupNodePolicyController> _logger;
    public GroupNodePolicyController(IPublisher publisher, IMapper mapper, IDataProvider dataProvider, ILogger<GroupNodePolicyController> logger)
    {
        _publisher = publisher;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _logger = logger;
    }
    [HttpGet]
    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in GroupNodePolicy");

        try
        {
            await _publisher.Publish(new GroupPolicyPaginatedViewEvent());

            _logger.LogDebug("Entering into view in GroupNodePolicy");

            return View();

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on group node policy page while processing the list request.", ex);

            return View(new GroupPolicyViewModel
            {
                GroupPolicyListVms = new List<GroupPolicyListVm>(),
                NodesVm = new List<LoadBalancerListVm>()
            });

        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Admin.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(GroupPolicyViewModel groupPolicyViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in GroupNodePolicy");

        var id = Request.Form["Id"].ToString();
        try
        {

            if (id.IsNullOrWhiteSpace())
            {
                var createGroup = _mapper.Map<CreateGroupPolicyCommand>(groupPolicyViewModel);
                var result = await _dataProvider.GroupPolicy.CreateAsync(createGroup);
                _logger.LogDebug($"Creating GroupNodePolicy '{createGroup.GroupName}'");
                 TempData.NotifySuccess(result.Message);
                return Json(new { Success = true, data = result.Message });
            }
            else
            {
                var updateGroup = _mapper.Map<UpdateGroupPolicyCommand>(groupPolicyViewModel);
                var result = await _dataProvider.GroupPolicy.UpdateAsync(updateGroup);
                _logger.LogDebug($"Updating GroupNodePolicy '{updateGroup.GroupName}'");
                TempData.NotifySuccess(result.Message);
                return Json(new { Success = true, data = result.Message });
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in groupNodePolicy, returning view.");
            // return RedirectToAction("List");
            
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on group node policy page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on group node policy page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in GroupNodePolicy");
        try
        {
            var response = await _dataProvider.GroupPolicy.DeleteAsync(id);
            TempData.NotifySuccess(response.Message);
            _logger.LogDebug("Successfully deleted record in GroupNodePolicy");
            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on group node policy.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }


    [HttpGet]
    public async Task<bool> IsGroupPolicyNameExist(string groupPolicyName, string id)
    {
        _logger.LogDebug("Entering IsGroupPolicyNameExist method in GroupNodePolicy");

        try
        {
            _logger.LogDebug("Returning result for IsGroupPolicyNameExist on GroupNodePolicy");
            return await _dataProvider.GroupPolicy.IsGroupPolicyNameExist(groupPolicyName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on group node policy page while checking if group name exists for : {groupPolicyName}.", ex);
            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetGroupPolicyPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in GroupNodePolicy");

        try
        {
            _logger.LogDebug("Successfully retrieved group node policy paginated list on GroupNodePolicy");
            return Json(await _dataProvider.GroupPolicy.GetPaginatedGroupPolicies(query));

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on group node policy page while processing the pagination request.", ex);
            return Json("");
        }
    }

    public async Task<JsonResult> GetLoadBalancerList()
    {
        _logger.LogDebug("Entering GetLoadBalancerList method in GroupNodePolicy");

        try
        {
            var result = await _dataProvider.LoadBalancer.GetLoadBalancerList();
            _logger.LogDebug("Successfully retrieved loadBalancer list on GroupNodePolicy");
            return Json(result);

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on group node policy page while retrieving the loadBalancer list.", ex);
            return Json("");
        }
    }

    public async Task<JsonResult> GetGroupPolicyByType(string type)
    {
        _logger.LogDebug("Entering GetGroupPolicyByType method in GroupNodePolicy");

        try
        {
            var result = await _dataProvider.GroupPolicy.GetByType(type);
            _logger.LogDebug($"Successfully retrieved groupNodePolicy list by type '{type}' on GroupNodePolicy");
            return Json(result);

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on group node policy while retrieving the groupNodePolicy by type '{type}'.", ex);
            return Json("");
        }
    }
}