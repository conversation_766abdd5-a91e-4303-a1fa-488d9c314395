﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByLicenseKey;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByOsType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByServerName;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByUserName;
using ContinuityPatrol.Application.Features.Server.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IServerService
{
    Task<List<ServerNameVm>> GetServerNames();
    Task<List<ServerListVm>> GetServerList();
    Task<BaseResponse> CreateAsync(CreateServerCommand createServerCommand);
    Task<BaseResponse> SaveAsServer(SaveAsServerCommand saveAsServerCommand);
    Task<BaseResponse> UpdateAsync(UpdateServerCommand updateServerCommand);
    Task<BaseResponse> DeleteAsync(string serverId);
    Task<ServerDetailVm> GetByReferenceId(string id);
    Task<bool> IsServerNameExist(string serverName, string id);
    Task<List<ServerTypeVm>> GetByType(string serverTypeId);
    Task<List<ServerByLicenseKeyVm>> GetByLicenseKey(string? licenseId);
    Task<List<GetServerByOsTypeVm>> GetByServerOsType(string? osTypeId);
    Task<ServerByServerNameVm> GetByServerName(string serverName);
    Task<List<ServerRoleTypeVm>> GetByRoleTypeAndServerType(string roleTypeId, string serverTypeId);
    Task<BaseResponse> ServerTestConnection(ServerTestConnectionCommand command);
    Task<PaginatedResult<ServerViewListVm>> GetPaginatedServers(GetServerPaginatedListQuery query);
    Task<BaseResponse> UpdateServerPassword(UpdateBulkPasswordCommand command);
    Task<List<ServerByUserNameVm>> GetServerByUserName(string userName, string osTypeId,bool substituteAuthentication);
    Task<BaseResponse> UpdateServerFormVersion(UpdateServerVersionCommand updateServerVersionCommand);
    Task<List<ServerListVm>> GetServerByIpAddress(string ipAddress);
    Task<BaseResponse> SaveAllServer(SaveAllServerCommand command);
    Task<List<ServerListVm>> GetServerBySiteId(string siteId);
   
}