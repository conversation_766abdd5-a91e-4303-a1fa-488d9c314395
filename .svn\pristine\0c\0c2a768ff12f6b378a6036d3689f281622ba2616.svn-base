﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupPaginatedListQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly GetWorkflowOperationGroupPaginatedListQueryHandler _handler;

    public GetWorkflowOperationGroupPaginatedListQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectName = "Test_Infra";
        _workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowName = "8.1";
        //_workflowOperationGroupFixture.WorkflowOperationGroups[0].JobName = "Running";
        _workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionName = "Passing";
        _workflowOperationGroupFixture.WorkflowOperationGroups[0].Status = "Stop";

        _workflowOperationGroupFixture.WorkflowOperationGroups[1].InfraObjectName = "Test_Infra123";
        _workflowOperationGroupFixture.WorkflowOperationGroups[1].WorkflowName = "8.1";
       // _workflowOperationGroupFixture.WorkflowOperationGroups[1].JobName = "Running123";
        _workflowOperationGroupFixture.WorkflowOperationGroups[1].CurrentActionName = "Pass123";
        _workflowOperationGroupFixture.WorkflowOperationGroups[1].Status = "Pass";

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetPaginatedWorkflowOperationGroupRepository(_workflowOperationGroupFixture.WorkflowOperationGroups);
        _handler = new GetWorkflowOperationGroupPaginatedListQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationGroupListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowOperationGroup_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "infraobjectname=Test_Infra;workflowname=8.1;jobname=Running;currentactionname=Pass;Status=Stop;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationGroupListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectId);
        result.Data[0].InfraObjectName.ShouldBe("Test_Infra");
        result.Data[0].WorkflowId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowId);
        result.Data[0].WorkflowName.ShouldBe("8.1");
        result.Data[0].CurrentActionId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionId);
        result.Data[0].CurrentActionName.ShouldBe("Passing");
        result.Data[0].Status.ShouldBe("Stop");
        result.Data[0].Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Message);
        result.Data[0].WorkflowOperationId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId);
        result.Data[0].ConditionalOperation.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ConditionalOperation);
        result.Data[0].ProgressStatus.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ProgressStatus);
       // result.Data[0].JobName.ShouldBe("Running");
        result.Data[0].IsResume.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsResume);
        result.Data[0].IsReExecute.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsReExecute);
        result.Data[0].IsPause.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsPause);
        result.Data[0].IsAbort.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsAbort);
        result.Data[0].WaitToNext.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WaitToNext);
        result.Data[0].WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowVersion);


        //result.Data[1].WorkflowName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].WorkflowName);
        //result.Data[1].WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].WorkflowVersion);
        //result.Data[1].Status.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].Status);
        //result.Data[1].Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].Message);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowOperationGroup_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test_Infra" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationGroupListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].InfraObjectId);
        result.Data[0].InfraObjectName.ShouldBe("Test_Infra");
        result.Data[0].WorkflowId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowId);
        result.Data[0].WorkflowName.ShouldBe("8.1");
        result.Data[0].CurrentActionId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionId);
        result.Data[0].CurrentActionName.ShouldBe("Passing");
        result.Data[0].Status.ShouldBe("Stop");
        result.Data[0].Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].Message);
        result.Data[0].WorkflowOperationId.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowOperationId);
        result.Data[0].ConditionalOperation.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ConditionalOperation);
        result.Data[0].ProgressStatus.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ProgressStatus);
       // result.Data[0].JobName.ShouldBe("Running");
        result.Data[0].IsResume.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsResume);
        result.Data[0].IsReExecute.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsReExecute);
        result.Data[0].IsPause.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsPause);
        result.Data[0].IsAbort.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].IsAbort);
        result.Data[0].WaitToNext.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WaitToNext);
        result.Data[0].WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].WorkflowVersion);

        //result.Data[1].WorkflowName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].WorkflowName);
        //result.Data[1].WorkflowVersion.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].WorkflowVersion);
        //result.Data[1].Status.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].Status);
        //result.Data[1].Message.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[1].Message);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Running" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationGroupListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowOperationGroupPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}