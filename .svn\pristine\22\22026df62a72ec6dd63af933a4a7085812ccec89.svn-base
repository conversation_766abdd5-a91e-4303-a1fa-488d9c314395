﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ComponentTypeFilterSpecification : Specification<ComponentType>
{
    public ComponentTypeFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ComponentName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                {
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ComponentName.Contains(stringItem.Replace("name=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.FormTypeName.Contains(stringItem.Replace("type=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    if (stringItem.Contains("version=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        var osTypeValue =
                            stringItem.Replace("version=", "", StringComparison.InvariantCultureIgnoreCase);
                        if (string.IsNullOrEmpty(osTypeValue))
                            Criteria = p => p.ComponentName != null;
                        else
                            AddJsonCriteria(s => s.Version, "version", osTypeValue);
                    }
                }
            }
            else
            {
                Criteria = p =>
                    p.FormTypeName.Contains(searchString) || p.ComponentName.Contains(searchString) ||
                    p.Version.Contains(searchString);
            }
        }
    }
}