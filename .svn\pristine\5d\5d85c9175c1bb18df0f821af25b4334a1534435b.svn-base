﻿namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.DriftOperationSummary;

public class DriftOperationSummaryQueryHandler : IRequestHandler<DriftOperationSummaryQuery, DriftOperationSummaryVm>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDriftResourceSummaryRepository _driftResourceSummaryRepository;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;


    public DriftOperationSummaryQueryHandler(IInfraObjectViewRepository infraObjectViewRepository,
        IBusinessServiceRepository businessServiceRepository,
        IDriftResourceSummaryRepository driftResourceSummaryRepository)
    {
        _infraObjectViewRepository = infraObjectViewRepository;
        _businessServiceRepository = businessServiceRepository;
        _driftResourceSummaryRepository = driftResourceSummaryRepository;
    }

    public async Task<DriftOperationSummaryVm> Handle(DriftOperationSummaryQuery request,
        CancellationToken cancellationToken)
    {
        #region Old Logic
        //var driftOperationSummaryVm = new DriftOperationSummaryVm();

        //var businessServices = await _businessServiceRepository.ListAllAsync();

        //var infraObjectDriftList= await _infraObjectViewRepository.GetDriftEnbledInfraByBusinessServiceIds(businessServices.Select(b=>b.ReferenceId).ToList());

        //var driftResourceSummary = await _driftResourceSummaryRepository.ListAllAsync();

        //var conflictInfra= infraObjectDriftList.Where(infraobj=> driftResourceSummary
        //.Any(drift=>infraobj.ReferenceId.Equals(drift.InfraObjectId) && drift.IsConflict)).ToList();

        //var conflictBService=conflictInfra.DistinctBy(x=>x.BusinessServiceId).ToList();
        //var notConflictBservice= businessServices.Where(x=>!conflictBService.Any(y=>y.BusinessServiceId.Equals(x.ReferenceId))).ToList();


        //driftOperationSummaryVm.TotalBusinessServiceCount = businessServices.Count;
        //driftOperationSummaryVm.TotalConflictCount = conflictBService.Count;
        //driftOperationSummaryVm.TotalNonConflictCount = notConflictBservice.Count;
        #endregion
        var driftOperationSummaryVm = new DriftOperationSummaryVm();

        var infraObjectDriftList = await _infraObjectViewRepository.GetDriftEnbledInfraObject();

        var totalBusinessServiceCount = infraObjectDriftList.Where(x=>x.BusinessServiceId != null).DistinctBy(x =>x.BusinessServiceId).ToList();


        var conflictInfraObjectIds = (await _driftResourceSummaryRepository.ListAllAsync())
            .Where(drift => drift.IsConflict)
            .Select(drift => drift.InfraObjectId)
            .ToList(); 


        var conflictBusinessServiceIds = infraObjectDriftList
            .Where(infra => conflictInfraObjectIds.Contains(infra.ReferenceId)) 
            .Select(infra => infra.BusinessServiceId)
            .Where(id => id != null) 
            .Distinct()
            .ToList(); 


        driftOperationSummaryVm.TotalBusinessServiceCount = totalBusinessServiceCount.Count;
        driftOperationSummaryVm.TotalConflictCount = conflictBusinessServiceIds.Count;
        driftOperationSummaryVm.TotalNonConflictCount = totalBusinessServiceCount.Count- conflictBusinessServiceIds.Count;

        return driftOperationSummaryVm;

    }
}