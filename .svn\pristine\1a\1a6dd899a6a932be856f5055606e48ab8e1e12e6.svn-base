﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Manage.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class SolutionTemplateControllerTests
    {
        private readonly SolutionTemplateController _controller;

        public SolutionTemplateControllerTests()
        {
            _controller = new SolutionTemplateController();
        }

        [Fact]
        public void List_Returns_ViewResult()
        {
            var result = _controller.List();

            Assert.IsType<ViewResult>(result);
        }
    }
}
