using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.IsAttached;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGap.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberAirGapControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberAirGapsController _controller;
    private readonly CyberAirGapFixture _cyberAirGapFixture;

    public CyberAirGapControllerTests()
    {
        _cyberAirGapFixture = new CyberAirGapFixture();

        var testBuilder = new ControllerTestBuilder<CyberAirGapsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberAirGapsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberAirGaps_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberAirGaps = new List<CyberAirGapListVm>
        {
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberAirGaps);

        // Act
        var result = await _controller.GetCyberAirGaps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGaps = Assert.IsAssignableFrom<List<CyberAirGapListVm>>(okResult.Value);
        Assert.Equal(3, cyberAirGaps.Count);
    }

    [Fact]
    public async Task GetCyberAirGapById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberAirGapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapDetailQuery>(q => q.Id == cyberAirGapId), default))
            .ReturnsAsync(_cyberAirGapFixture.CyberAirGapDetailVm);

        // Act
        var result = await _controller.GetCyberAirGapById(cyberAirGapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGap = Assert.IsType<CyberAirGapDetailVm>(okResult.Value);
        Assert.Equal(_cyberAirGapFixture.CyberAirGapDetailVm.Name, cyberAirGap.Name);
    }

    [Fact]
    public async Task GetPaginatedCyberAirGaps_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberAirGapPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = new List<CyberAirGapListVm>
        {
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm
        };
        var expectedResults = PaginatedResult<CyberAirGapListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGaps(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberAirGap_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberAirGapFixture.CreateCyberAirGapCommand;
        var expectedMessage = "CyberAirGap has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGap(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAirGap_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapFixture.UpdateCyberAirGapCommand;
        var expectedMessage = "CyberAirGap has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAirGapResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAirGap(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAirGapResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberAirGap_ReturnsOk()
    {
        // Arrange
        var cyberAirGapId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberAirGap has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberAirGapCommand>(c => c.Id == cyberAirGapId), default))
            .ReturnsAsync(new DeleteCyberAirGapResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberAirGap(cyberAirGapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberAirGapResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetAirGapsStatus_ReturnsExpectedList()
    {
        // Arrange
        var expectedStatusList = new List<GetAirGapsStatusListVm> { _cyberAirGapFixture.GetAirGapsStatusListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAirGapsStatusListQuery>(), default))
            .ReturnsAsync(expectedStatusList);

        // Act
        var result = await _controller.GetAirGapsStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsType<List<GetAirGapsStatusListVm>>(okResult.Value);
        Assert.Single(statusList);
    }

    [Fact]
    public async Task UpdateAirGapStatus_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapFixture.AirGapStatusUpdateCommand;
        var expectedMessage = "AirGap status has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new AirGapStatusUpdateResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateAirGapStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<AirGapStatusUpdateResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task AirGapIsAttached_ReturnsOk()
    {
        // Arrange
        var command = _cyberAirGapFixture.AirGapAttachedCommand;
        var expectedMessage = "AirGap attachment status has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new AirGapAttachedResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.AirGapIsAttached(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<AirGapAttachedResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task IsCyberAirGapNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        var airGapName = "Existing Air Gap";
        var airGapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapNameUniqueQuery>(q => 
                q.Name == airGapName && q.Id == airGapId), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsCyberAirGapNameExist(airGapName, airGapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.True(exists);
    }

    [Fact]
    public async Task IsCyberAirGapNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var airGapName = "Unique Air Gap Name";
        var airGapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapNameUniqueQuery>(q => 
                q.Name == airGapName && q.Id == airGapId), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsCyberAirGapNameExist(airGapName, airGapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var exists = Assert.IsType<bool>(okResult.Value);
        Assert.False(exists);
    }

    [Fact]
    public async Task CreateCyberAirGap_ValidatesAirGapName()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "", // Empty name should cause validation error
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Source = "{}",
            Target = "{}",
            Description = "Test air gap",
            Status = "Active"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Name is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateCyberAirGap(command));
    }

    [Fact]
    public async Task UpdateCyberAirGap_ValidatesAirGapExists()
    {
        // Arrange
        var command = new UpdateCyberAirGapCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Non-existent Air Gap",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Source = "{}",
            Target = "{}",
            Description = "Test air gap",
            Status = "Active"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CyberAirGap not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateCyberAirGap(command));
    }

    [Fact]
    public async Task CreateCyberAirGap_HandlesComplexAirGapConfiguration()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "Enterprise Multi-Site Air Gap Replication",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Primary Production Data Center",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Disaster Recovery Site",
            Port = 8443,
            Source = "{\"infrastructure\":{\"servers\":[{\"name\":\"PROD-AG-01\",\"ip\":\"**************\",\"role\":\"Primary\"},{\"name\":\"PROD-AG-02\",\"ip\":\"**************\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"Enterprise SAN\",\"capacity\":\"100TB\"},\"network\":{\"bandwidth\":\"10Gbps\",\"encryption\":\"AES-256\"}}}",
            Target = "{\"infrastructure\":{\"servers\":[{\"name\":\"DR-AG-01\",\"ip\":\"***********\",\"role\":\"Primary\"},{\"name\":\"DR-AG-02\",\"ip\":\"***********\",\"role\":\"Secondary\"}],\"storage\":{\"type\":\"Enterprise SAN\",\"capacity\":\"100TB\"},\"network\":{\"bandwidth\":\"10Gbps\",\"encryption\":\"AES-256\"}}}",
            Description = "Critical enterprise air gap replication system with multi-tier redundancy and real-time monitoring",
            SourceComponentId = Guid.NewGuid().ToString(),
            SourceComponentName = "Production Database Cluster",
            TargetComponentId = Guid.NewGuid().ToString(),
            TargetComponentName = "DR Database Cluster",
            EnableWorkflowId = Guid.NewGuid().ToString(),
            DisableWorkflowId = Guid.NewGuid().ToString(),
            WorkflowStatus = "Active",
            StartTime = DateTime.Now.AddHours(-1),
            EndTime = DateTime.Now.AddHours(23),
            RPO = "2 minutes",
            IsAttached = true,
            Status = "Connected"
        };

        var expectedMessage = "CyberAirGap has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGap(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateAirGapStatus_HandlesStatusTransition()
    {
        // Arrange
        var command = new AirGapStatusUpdateCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Critical Air Gap System",
            Status = "Maintenance"
        };

        var expectedMessage = "AirGap status has been updated to Maintenance successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new AirGapStatusUpdateResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateAirGapStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<AirGapStatusUpdateResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task AirGapIsAttached_HandlesAttachmentToggle()
    {
        // Arrange
        var command = new AirGapAttachedCommand
        {
            Id = Guid.NewGuid().ToString(),
            IsAttached = false
        };

        var expectedMessage = "AirGap has been detached successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new AirGapAttachedResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.AirGapIsAttached(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<AirGapAttachedResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetCyberAirGaps_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAirGapListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberAirGapListVm>());

        // Act
        var result = await _controller.GetCyberAirGaps();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAirGaps = Assert.IsAssignableFrom<List<CyberAirGapListVm>>(okResult.Value);
        Assert.Empty(cyberAirGaps);
    }

    [Fact]
    public async Task GetCyberAirGapById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberAirGapById(invalidId));
    }

    [Fact]
    public async Task GetCyberAirGapById_HandlesNotFound()
    {
        // Arrange
        var cyberAirGapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapDetailQuery>(q => q.Id == cyberAirGapId), default))
            .ThrowsAsync(new NotFoundException("CyberAirGap", cyberAirGapId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberAirGapById(cyberAirGapId));
    }

    [Fact]
    public async Task GetPaginatedCyberAirGaps_HandlesLargePageSize()
    {
        // Arrange
        var query = new GetCyberAirGapPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 100,
            SearchString = "Enterprise"
        };

        var expectedData = new List<CyberAirGapListVm>
        {
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm,
            _cyberAirGapFixture.CyberAirGapListVm
        };
        var expectedResults = PaginatedResult<CyberAirGapListVm>.Success(expectedData, 5, 1, 100);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAirGapPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize && q.SearchString == query.SearchString), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAirGaps(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAirGapListVm>>(okResult.Value);
        Assert.Equal(5, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(100, paginatedResult.PageSize);
        Assert.Equal(5, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task CreateCyberAirGap_HandlesDuplicateName()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "Duplicate Air Gap Name",
            SourceSiteId = Guid.NewGuid().ToString(),
            TargetSiteId = Guid.NewGuid().ToString(),
            Port = 8443,
            Source = "{}",
            Target = "{}",
            Description = "Test air gap",
            Status = "Active"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("CyberAirGap with this name already exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateCyberAirGap(command));
    }

    [Fact]
    public async Task DeleteCyberAirGap_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberAirGap(invalidId));
    }

    [Fact]
    public async Task DeleteCyberAirGap_HandlesAssociatedEntities()
    {
        // Arrange
        var cyberAirGapId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberAirGapCommand>(c => c.Id == cyberAirGapId), default))
            .ThrowsAsync(new InvalidOperationException("Cannot delete CyberAirGap with associated entities"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.DeleteCyberAirGap(cyberAirGapId));
    }

    [Fact]
    public async Task GetAirGapsStatus_HandlesMultipleStatuses()
    {
        // Arrange
        var expectedStatusList = new List<GetAirGapsStatusListVm>
        {
            _cyberAirGapFixture.GetAirGapsStatusListVm,
            new GetAirGapsStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Secondary Air Gap",
                Status = "Warning",
                IsAttached = false,
                GetAirGapsServeListVms = new List<GetAirGapsServeListVm>()
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAirGapsStatusListQuery>(), default))
            .ReturnsAsync(expectedStatusList);

        // Act
        var result = await _controller.GetAirGapsStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var statusList = Assert.IsType<List<GetAirGapsStatusListVm>>(okResult.Value);
        Assert.Equal(2, statusList.Count);
        Assert.Contains(statusList, s => s.Status == "Healthy");
        Assert.Contains(statusList, s => s.Status == "Warning");
    }

    [Fact]
    public async Task UpdateAirGapStatus_HandlesInvalidStatus()
    {
        // Arrange
        var command = new AirGapStatusUpdateCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Test Air Gap",
            Status = "InvalidStatus"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Invalid status value"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.UpdateAirGapStatus(command));
    }

    [Fact]
    public async Task AirGapIsAttached_HandlesNonExistentAirGap()
    {
        // Arrange
        var command = new AirGapAttachedCommand
        {
            Id = Guid.NewGuid().ToString(),
            IsAttached = true
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("CyberAirGap", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.AirGapIsAttached(command));
    }

    [Fact]
    public async Task IsCyberAirGapNameExist_ThrowsException_WhenNameIsEmpty()
    {
        // Arrange
        var emptyName = "";
        var airGapId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsCyberAirGapNameExist(emptyName, airGapId));
    }

    [Fact]
    public async Task CreateCyberAirGap_HandlesNetworkConnectivityIssues()
    {
        // Arrange
        var command = new CreateCyberAirGapCommand
        {
            Name = "Network Test Air Gap",
            SourceSiteId = Guid.NewGuid().ToString(),
            SourceSiteName = "Source Site",
            TargetSiteId = Guid.NewGuid().ToString(),
            TargetSiteName = "Target Site",
            Port = 8443,
            Source = "{\"network\":{\"connectivity\":\"failed\",\"lastTest\":\"2024-01-15T10:00:00Z\"}}",
            Target = "{\"network\":{\"connectivity\":\"failed\",\"lastTest\":\"2024-01-15T10:00:05Z\"}}",
            Description = "Air gap with network connectivity issues",
            Status = "Disconnected",
            IsAttached = false
        };

        var expectedMessage = "CyberAirGap has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAirGapResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAirGap(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAirGapResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }
}
