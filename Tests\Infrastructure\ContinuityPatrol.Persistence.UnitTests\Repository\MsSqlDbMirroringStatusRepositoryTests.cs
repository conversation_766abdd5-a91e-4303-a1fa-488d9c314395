using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MsSqlDbMirroringStatusRepositoryTests : IClassFixture<MSSQLDBMirroringStatusFixture>
{
    private readonly MSSQLDBMirroringStatusFixture _mssqlDbMirroringStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MsSqlDbMirroringStatusStatusRepository _repository;

    public MsSqlDbMirroringStatusRepositoryTests(MSSQLDBMirroringStatusFixture mssqlDbMirroringStatusFixture)
    {
        _mssqlDbMirroringStatusFixture = mssqlDbMirroringStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new MsSqlDbMirroringStatusStatusRepository(_dbContext);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MSsqldbmirroingStatuses.RemoveRange(_dbContext.MSsqldbmirroingStatuses);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }



    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnActiveStatuses_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithMixedActiveStatus(testType);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(statuses);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active statuses should be returned
        Assert.All(result, status => Assert.True(status.IsActive));
        Assert.All(result, status => Assert.Equal(testType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var activeStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(type: testType, isActive: true);
        var inactiveStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(type: testType, isActive: false);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(new[] { activeStatus, inactiveStatus });
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal(testType, result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithWhitespace();
        whitespaceStatus.IsActive = true;
        await _dbContext.MSsqldbmirroingStatuses.AddAsync(whitespaceStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithLongType(1000);
        longTypeStatus.IsActive = true;
        await _dbContext.MSsqldbmirroingStatuses.AddAsync(longTypeStatus);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeStatus.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeStatus.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialTypes = MSSQLDBMirroringStatusFixture.TestData.SpecialCharacterTypes;
        var statuses = new List<MSSQLDBMirroringStatus>();

        foreach (var type in specialTypes)
        {
            var status = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(type: type, isActive: true);
            statuses.Add(status);
        }

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in specialTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnMultipleStatuses_WhenMultipleActiveStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var testType = "TestType";
        var statuses = _mssqlDbMirroringStatusFixture.CreateMultipleMSSQLDBMirroringStatusWithSameType(testType, 5);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(testType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(5, result.Count);
        Assert.All(result, status => Assert.Equal(testType, status.Type));
        Assert.All(result, status => Assert.True(status.IsActive));
    }

    [Fact]
    public async Task GetDetailByType_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseType = "testtype";
        var upperCaseType = "TESTTYPE";

        var lowerCaseStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(type: lowerCaseType, isActive: true);
        var upperCaseStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(type: upperCaseType, isActive: true);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetDetailByType(lowerCaseType);
        var upperResult = await _repository.GetDetailByType(upperCaseType);

        // Assert
        Assert.Single(lowerResult);
        Assert.Single(upperResult);
        Assert.Equal(lowerCaseType, lowerResult.First().Type);
        Assert.Equal(upperCaseType, upperResult.First().Type);
    }

    #endregion

    #region GetMssqlDbMirroringStatusByInfraObjectId Tests

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "ebfd199c-01aa-4f36-a2af-3495af6fb166";
        var status = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(infraObjectId);

        await _dbContext.MSsqldbmirroingStatuses.AddAsync(status);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetMssqlDbMirroringStatusByInfraObjectId("ebfd199c-01aa-4f36-a2af-3495af6fb166");

        // Assert
        Assert.Null(result);
    }


    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "ebfd199c-01aa-4f36-a2af-3495af6fb166";
        var status1 = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(infraObjectId);
        var status2 = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(infraObjectId);

        // Add status1 first, then status2
        await _dbContext.MSsqldbmirroingStatuses.AddAsync(status1);
        _dbContext.SaveChanges();
        await _dbContext.MSsqldbmirroingStatuses.AddAsync(status2);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first one added (FirstOrDefaultAsync behavior)
        Assert.Equal(status1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldReturnActiveAndInactiveStatuses()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "ebfd199c-01aa-4f36-a2af-3495af6fb166";
        var inactiveStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithProperties(
            infraObjectId: infraObjectId,
            isActive: false);

        await _dbContext.MSsqldbmirroingStatuses.AddAsync(inactiveStatus);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.False(result.IsActive); // Should return inactive status as well
    }

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldHandleValidGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var validGuids = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString("D"),
            Guid.NewGuid().ToString("N"),
            Guid.NewGuid().ToString("B"),
            Guid.NewGuid().ToString("P")
        };

        var statuses = new List<MSSQLDBMirroringStatus>();
        foreach (var guid in validGuids)
        {
            var status = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(guid);
            statuses.Add(status);
        }

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(statuses);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var guid in validGuids)
        {
            var result = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(guid);
            Assert.NotNull(result);
            Assert.Equal(guid, result.InfraObjectId);
        }
    }

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var lowerCaseGuid = Guid.NewGuid().ToString().ToLower();
        var upperCaseGuid = lowerCaseGuid.ToUpper();

        var lowerCaseStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(lowerCaseGuid);
        var upperCaseStatus = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(upperCaseGuid);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(new[] { lowerCaseStatus, upperCaseStatus });
        await _dbContext.SaveChangesAsync();

        // Act
        var lowerResult = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(lowerCaseGuid);
        var upperResult = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(upperCaseGuid);

        // Assert
        Assert.NotNull(lowerResult);
        Assert.NotNull(upperResult);
        Assert.Equal(lowerCaseGuid, lowerResult.InfraObjectId);
        Assert.Equal(upperCaseGuid, upperResult.InfraObjectId);
        Assert.NotEqual(lowerResult.ReferenceId, upperResult.ReferenceId);
    }

    [Fact]
    public async Task GetMssqlDbMirroringStatusByInfraObjectId_ShouldHandleSpecialCharactersInGuid()
    {
        // Arrange
        await ClearDatabase();
        var guidWithDashes = "12345678-1234-1234-1234-123456789012";
        var guidWithBraces = "{12345678-1234-1234-1234-123456789013}";
        var guidWithParentheses = "(12345678-1234-1234-1234-123456789014)";

        var status1 = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(guidWithDashes);
        var status2 = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(guidWithBraces);
        var status3 = _mssqlDbMirroringStatusFixture.CreateMSSQLDBMirroringStatusWithSpecificInfraObjectId(guidWithParentheses);

        await _dbContext.MSsqldbmirroingStatuses.AddRangeAsync(new[] { status1, status2, status3 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(guidWithDashes);
        var result2 = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(guidWithBraces);
        var result3 = await _repository.GetMssqlDbMirroringStatusByInfraObjectId(guidWithParentheses);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
        Assert.Equal(guidWithDashes, result1.InfraObjectId);
        Assert.Equal(guidWithBraces, result2.InfraObjectId);
        Assert.Equal(guidWithParentheses, result3.InfraObjectId);
    }

    #endregion
}
