﻿namespace ContinuityPatrol.Application.Features.LogViewer.Queries.GetNameUnique;

public class GetLogViewerNameUniqueQueryHandler : IRequestHandler<GetLogViewerNameUniqueQuery, bool>
{
    private readonly ILogViewerRepository _logViewerRepository;
    public GetLogViewerNameUniqueQueryHandler(ILogViewerRepository logViewerRepository)
    {
        _logViewerRepository = logViewerRepository;
    }
    public async Task<bool> Handle(GetLogViewerNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _logViewerRepository.IsLogViewerNameUnique(request.Name, request?.Id);
    }
}