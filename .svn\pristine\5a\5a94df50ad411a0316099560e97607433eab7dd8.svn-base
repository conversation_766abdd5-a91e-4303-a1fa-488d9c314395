namespace ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;

public class
    GetCyberAirGapLogDetailsQueryHandler : IRequestHandler<GetCyberAirGapLogDetailQuery, CyberAirGapLogDetailVm>
{
    private readonly ICyberAirGapLogRepository _cyberAirGapLogRepository;
    private readonly IMapper _mapper;

    public GetCyberAirGapLogDetailsQueryHandler(IMapper mapper, ICyberAirGapLogRepository cyberAirGapLogRepository)
    {
        _mapper = mapper;
        _cyberAirGapLogRepository = cyberAirGapLogRepository;
    }

    public async Task<CyberAirGapLogDetailVm> Handle(GetCyberAirGapLogDetailQuery request,
        CancellationToken cancellationToken)
    {
        var cyberAirGapLog = await _cyberAirGapLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(cyberAirGapLog, nameof(Domain.Entities.CyberAirGapLog),
            new NotFoundException(nameof(Domain.Entities.CyberAirGapLog), request.Id));

        var cyberAirGapLogDetailDto = _mapper.Map<CyberAirGapLogDetailVm>(cyberAirGapLog);

        return cyberAirGapLogDetailDto;
    }
}