﻿namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetByInfraObjectAndActionId;

public class WorkflowActionResultByInfraObjectAndActionIdVm
{
    public string Id { get; set; }
    public string WorkflowActionName { get; set; }
    public string CompanyId { get; set; }
    public string WorkflowOperationId { get; set; }
    public string WorkflowOperationName { get; set; }
    public string WorkflowOperationGroupId { get; set; }
    public string WorkflowOperationGroupName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ActionId { get; set; }
    public string StepId { get; set; }
    public int StartRto { get; set; }
    public int ConditionActionId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Status { get; set; }
    public string Message { get; set; }
    public bool SkipStep { get; set; }
    public int IsReload { get; set; }
    public bool IsRetry { get; set; }
    public bool IsParallel { get; set; }
    public string Direction { get; set; }
    public string Version { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
}