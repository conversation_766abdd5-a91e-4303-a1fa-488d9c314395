using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SybaseRSHADRMonitorLogFixture : IDisposable
{
    public List<SybaseRSHADRMonitorLog> SybaseRSHADRMonitorLogPaginationList { get; set; }
    public List<SybaseRSHADRMonitorLog> SybaseRSHADRMonitorLogList { get; set; }
    public SybaseRSHADRMonitorLog SybaseRSHADRMonitorLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SybaseRSHADRMonitorLogFixture()
    {
        var fixture = new Fixture();

        SybaseRSHADRMonitorLogList = fixture.Create<List<SybaseRSHADRMonitorLog>>();

        SybaseRSHADRMonitorLogPaginationList = fixture.CreateMany<SybaseRSHADRMonitorLog>(20).ToList();

        SybaseRSHADRMonitorLogDto = fixture.Create<SybaseRSHADRMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
