﻿
let mId = sessionStorage.getItem("monitorId");
let monitortype = 'AzureStorageAccount';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { azurestoragemonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
setTimeout(() => { azureStorageServer(infraObjectId) }, 250)

$('#mssqlserver').hide();
async function azureStorageServer(id) {

    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
    let data = {}
    data.infraObjectId = id;
    let mssqlServerData = await getAysncWithHandler(url, data);

    if (mssqlServerData != null && mssqlServerData?.length > 0) {
        mssqlServerData?.forEach(data => {
            let value = data?.isServiceUpdate
            let parsed = []
            if (value && value !== 'NA') parsed = JSON?.parse(value)
            if (Array.isArray(parsed)) {
                parsed?.forEach(s => {
                    if (s?.Services?.length) {
                        $('#mssqlserver').show();
                        bindStorageServer(mssqlServerData)
                    }
                })
            }
        })

    } else {
        $('#mssqlserver').hide();
    }

}
function bindStorageServer(mssqlServerData) {

    let prType = { IpAddress: '--', Services: [] };
    let drType = { IpAddress: '--', Services: [] };

    // Loop through each item to find PR and DR entries
    mssqlServerData?.forEach(item => {
        let parsedServices = [];
        try {
            const value = item?.isServiceUpdate
            if (value && value !== 'NA') {
                parsedServices = JSON.parse(item?.isServiceUpdate)
            }
        } catch (e) {
            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
        }

        parsedServices?.forEach(serviceGroup => {
            if (serviceGroup?.Type === 'PR') {
                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
            } else if (serviceGroup?.Type === 'DR') {
                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
            }
        });
    });

    // Set header IPs
    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
    $('#drIp').text('DR (' + drType?.IpAddress + ')');

    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

    // Unique list of all service names from both PR and DR
    let allServiceNames = [...new Set([
        ...prType?.Services?.map(s => s?.ServiceName),
        ...drType?.Services?.map(s => s?.ServiceName)
    ])];

    // Build table rows
    let tbody = $('#mssqlserverbody');
    tbody.empty();

    allServiceNames?.forEach(serviceName => {
        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

        let prStatus = prService ? prService?.Status : '--';
        let drStatus = drService ? drService?.Status : '--';
        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

        let row = `
            <tr>
                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
                <td>${prIcon}${prStatus}</td>
                <td>${drIcon}${drStatus}</td>
            </tr>
        `;
        tbody.append(row);
    });
}
function getStatusSummary(arr) {
    let countMap = {};
    arr?.forEach(status => {
        countMap[status] = (countMap[status] || 0) + 1;
    });
    let total = arr?.length;
    let statusSummary = Object.entries(countMap)
        .map(([status, count]) => `${count} ${status}`)
        .join(', ');
    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
}
function getStatusIconClass(status) {
    if (!status) return "text-danger cp-disable";

    const lowerStatus = status.toLowerCase();
    if (lowerStatus === "running") {
        return "text-success cp-reload cp-animate";
    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
        return "text-danger cp-fail-back";
    } else {
        return "text-danger cp-disable";
    }
}

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function azurestoragemonitorstatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}

function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesData(value) {
    
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    else {
        let data = JSON?.parse(value?.properties);
       
        console.log(data, 'properties')
        let customSite = data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring
            ?.length > 1;
        
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();
        data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });

        if (data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring?.length > 0) {
            
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring[0]);
        }

        let defaultSite = data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring?.find(d => d?.Type === 'DR') || data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring[0];

        if (defaultSite) {
            
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0]?.id
            let getSiteName = $(`#${siteId} .siteName`).text()
            $('#customSite').html(getSiteName)

            let MonitoringModel = data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationMonitoring?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                
                displaySiteData(MonitoringModel);
            }
        });

        const replicationData = data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplication || {};
        
        const repProps = ["GeoReplicationType", "FailoverProgressState", "LastGeoFailoverTime", "LastSyncTime", "DataLag"];

        repProps?.forEach(prop => {
            const value = replicationData[prop] || 'NA';
            $(`#${prop}`).html(value).attr('title', value);
        });

        const accountData = data?.AzureStorageAccountMonitoring
        
        const accountProps = ["StorageAccountName", "StorageAccountResourceGroupName", "StorageAccountLocation", "Provisioningstate"];

        accountProps?.forEach(prop => {
            const value = accountData[prop] || 'NA';
            $(`#${prop}`).html(value).attr('title', value);
        });
        function displaySiteData(siteData) {
            
            let obj = {};
            // $('.dynamicSite-header').text(siteData.Type).attr('title', siteData.Type);
            
            for (let key in siteData?.Secondary) {
                obj[`DR_` + key] = siteData?.Secondary[key];
            }

            let MonitoringModelMssqlpass = [
                "DR_Location", "DR_Diskstate"
            ];

            if (Object.keys(obj)?.length > 0) {
                bindProperties(obj, MonitoringModelMssqlpass, value);
            }
        }

        let dbDetail = data?.AzureStorageReplicationMonitoring?.AzureStorageAccountReplicationPRMonitoring
?.Primary
        console.log(dbDetail, 'dbDetail')
        const dbDetailsProp = [
            "Location", "Diskstate"
        ];

        bindProperties(dbDetail, dbDetailsProp, value);

        //Database Details

        //Datalag
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);
            result = firstValue + ":" + min[1];
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }
        else if (dataLagValue?.includes("+")) {
            const value = dataLagValue.split(" ");
            result = value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }


    }
}
function setPropData(data, propSets, value) {
    
    propSets?.forEach(properties => {
        bindProperties(data, properties, value);
    });
}

function bindProperties(data, properties, value) {
    
    properties?.forEach(property => {
        const values = data[property];
        const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
        // Displayed value with icon

        //const iconHtml = getIconClass(displayedValue, property, data, value);
        //const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(displayedValue).attr('title', displayedValue);
    });

}
//function getIconClass(displayedValue, property, data, value) {
//}