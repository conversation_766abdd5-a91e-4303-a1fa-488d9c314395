﻿using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Delete;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetUpCompingDrillList;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;


[ApiVersion("6")]
public class DrCalendarController : CommonBaseController
{

    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<List<DrCalendarActivityListVm>> GetDrCalenderList()
    {
        Logger.LogDebug("Get DrCalender list");

        return await Mediator.Send(new GetDrCalendarListQuery());
    }
    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<BaseResponse> CreateDrCalender([FromForm] CreateDrCalendarCommand command)
    {
        Logger.LogDebug($"Create DrCalender '{command.ActivityName}'");

        return await Mediator.Send(command);
    }
    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<BaseResponse> UpdateDrCalender([FromBody] UpdateDrCalendarCommand command)
    {
        Logger.LogDebug($"Update DrCalender '{command.ActivityName}'");

        return await Mediator.Send(command);
    }
    [HttpDelete]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<BaseResponse> DeleteDrCalender(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DrCalender Id");

        Logger.LogDebug($"Delete DrCalender Id '{id}'");


        return await Mediator.Send(new DeleteDrCalendarCommand { Id = id });
    }

    [Authorize(Policy = Permissions.Configuration.View)]
    [HttpGet("{id}", Name = "GetDrCalendar")]
    public async Task<ActionResult<DrCalendarDetailVm>> GetDrCalendarById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DrCalendar Id");

        Logger.LogDebug($"Get DrCalendar Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDrCalendarDetailsQuery { Id = id }));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsDrCalendarNameExist(string activityName, string? id,DateTime scheduleStartTime)
    {
        Guard.Against.NullOrWhiteSpace(activityName, "Activity Name");

        Logger.LogDebug($"Check Name Exists Detail by activity Name '{activityName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetDrCalendarNameUniqueQuery { ActivityName = activityName, ActivityId = id, ScheduleStartTime= scheduleStartTime }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<DrCalendarActivityListVm>>> GetPaginatedDrCalendar([FromQuery] GetDrCalendarPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DrCalendar Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet, Route("drill-events")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<GetUpcomingDrillCountVm> GetDrCalendarDrillEvents()
    {
        Logger.LogDebug("Get DrCalender drill events");

        return await Mediator.Send(new GetDrCalendarDrillEventsQuery());
    }

    [HttpGet, Route("upcoming-drill")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<List<UpComingDrillListVm>> GetUpComingDrillList()
    {
        Logger.LogDebug("Get DrCalender upcoming drill event list");

        return await Mediator.Send(new GetUpComingDrillListQuery());
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllSitesCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllSitesNameCacheKey };

        ClearCache(cacheKeys);
    }
}
