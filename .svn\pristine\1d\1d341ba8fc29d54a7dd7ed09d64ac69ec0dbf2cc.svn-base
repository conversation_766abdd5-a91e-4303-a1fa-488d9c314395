﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Queries;

public class GetInfraObjectSchedulerNameQueryHandlerTests : IClassFixture<InfraObjectSchedulerFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

    private readonly GetInfraObjectSchedulerNameQueryHandler _handler;

    public GetInfraObjectSchedulerNameQueryHandlerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetInfraObjectSchedulerNamesRepository(_infraObjectSchedulerFixture.InfraObjectSchedulers);

        _handler = new GetInfraObjectSchedulerNameQueryHandler(_mockInfraObjectSchedulerRepository.Object, _infraObjectSchedulerFixture.Mapper);
    }


    [Fact]
    public async Task Handle_Return_Active_InfraObjects_Name()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<InfraObjectSchedulerNameVm>>();

        result[0].Id.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId);
        result[0].InfraObjectName.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].InfraObjectName);
    }

    [Fact]
    public async Task Handle_Return_Active_InfraObjectNamesCount()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<InfraObjectSchedulerNameVm>>();

        result.Count.ShouldBe(_infraObjectSchedulerFixture.InfraObjectSchedulers.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetInfraObjectSchedulerEmptyRepository();

        var handler = new GetInfraObjectSchedulerNameQueryHandler(_mockInfraObjectSchedulerRepository.Object, _infraObjectSchedulerFixture.Mapper);

        var result = await handler.Handle(new GetInfraObjectSchedulerNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetInfraObjectNamesMethod_OneTime()
    {
        await _handler.Handle(new GetInfraObjectSchedulerNameQuery(), CancellationToken.None);

        _mockInfraObjectSchedulerRepository.Verify(x => x.GetInfraObjectSchedulerNames(), Times.Once);
    }
}