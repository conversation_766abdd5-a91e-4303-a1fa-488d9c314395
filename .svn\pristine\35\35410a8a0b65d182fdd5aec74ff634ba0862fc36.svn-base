﻿using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetList;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetStartTimeEndTimeByUserId;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class UserActivityService : BaseService, IUserActivityService
{
    public UserActivityService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateUserActivityCommand createUserActivityCommand)
    {
        Logger.LogDebug($"Create User Activity '{createUserActivityCommand.LoginName}'");

        return await Mediator.Send(createUserActivityCommand);
    }

    public async Task<List<UserActivityListVm>> GetUserActivityList()
    {
        Logger.LogDebug("Get All User Activities");

        return await Mediator.Send(new GetUserActivityListQuery());
    }
    public async Task<List<UserActivityLoginNameVm>> GetUserActivityLoginName(string loginName)
    {
        Logger.LogDebug("Get UserActivity Login Name");

        return await Mediator.Send(new GetUserActivityLoginNameQuery
        {
            LoginName = loginName
        });
    }

    public async Task<List<UserActivityListVm>> GetStartTimeEndTimeByUser(string? loginName, string startDate, string endDate)
    {
        Logger.LogDebug("Get All User Activities");

        return await Mediator.Send(new GetStartTimeEndTimeByUserIdQuery
        {
            LoginName = loginName,
            StartDate = startDate,
            EndDate = endDate
        });
    }
}