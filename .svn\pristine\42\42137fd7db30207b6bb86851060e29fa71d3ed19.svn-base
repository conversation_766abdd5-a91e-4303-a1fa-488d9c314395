using ContinuityPatrol.Domain.ViewModels.IncidentManagementModel;

namespace ContinuityPatrol.Application.Features.IncidentManagement.Queries.GetList;

public class
    GetIncidentManagementListQueryHandler : IRequestHandler<GetIncidentManagementListQuery,
        List<IncidentManagementListVm>>
{
    private readonly IIncidentManagementRepository _incidentManagementRepository;
    private readonly IMapper _mapper;

    public GetIncidentManagementListQueryHandler(IMapper mapper,
        IIncidentManagementRepository incidentManagementRepository)
    {
        _mapper = mapper;
        _incidentManagementRepository = incidentManagementRepository;
    }

    public async Task<List<IncidentManagementListVm>> Handle(GetIncidentManagementListQuery request,
        CancellationToken cancellationToken)
    {
        var incidentManagements = await _incidentManagementRepository.ListAllAsync();

        if (incidentManagements.Count <= 0) return new List<IncidentManagementListVm>();

        return _mapper.Map<List<IncidentManagementListVm>>(incidentManagements);
    }
}