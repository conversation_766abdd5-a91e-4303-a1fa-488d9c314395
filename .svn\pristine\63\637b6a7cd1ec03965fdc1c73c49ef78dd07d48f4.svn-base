﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNames;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BusinessFunctionsControllerTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _fixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BusinessFunctionsController _controller;

    public BusinessFunctionsControllerTests(BusinessFunctionFixture fixture)
    {
        _fixture = fixture;

        var testBuilder = new ControllerTestBuilder<BusinessFunctionsController>();
        _controller = testBuilder.CreateController(
            _ => new BusinessFunctionsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetBusinessFunctions_ReturnsExpectedList()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessFunctionListVm);

        var result = await _controller.GetBusinessFunctions();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctions = Assert.IsAssignableFrom<List<BusinessFunctionListVm>>(okResult.Value);
        Assert.Equal(_fixture.BusinessFunctionListVm.Count, businessFunctions.Count);
    }

    [Fact]
    public async Task GetBusinessFunctions_ReturnsEmptyList_WhenNoBusinessFunctionsExist()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionListQuery>(), default))
            .ReturnsAsync(new List<BusinessFunctionListVm>());

        var result = await _controller.GetBusinessFunctions();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<BusinessFunctionListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetBusinessFunctionById_ReturnsBusinessFunction_WhenIdIsValid()
    {
        var businessFunctionId = "valid-guid";
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetBusinessFunctionDetailQuery>(q => q.Id == businessFunctionId), default))
            .ReturnsAsync(_fixture.BusinessFunctionDetailVm);

        var result = await _controller.GetBusinessFunctionById(businessFunctionId);

        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetBusinessFunctionById_Throws_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetBusinessFunctionById("invalid-guid"));
    }

    [Fact]
    public async Task CreateBusinessFunction_Throws_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateBusinessFunctionCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("Name exists"));

        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateBusinessFunction(_fixture.CreateBusinessFunctionCommand));
    }

    [Fact]
    public async Task CreateBusinessFunction_Returns201Created()
    {
        var command = _fixture.CreateBusinessFunctionCommand;
        var expectedMessage = $"BusinessFunction '{command.Name}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBusinessFunctionResponse
            {
                Message = expectedMessage,
                BusinessFunctionId = "new-guid"
            });

        var result = await _controller.CreateBusinessFunction(command);

        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateBusinessFunctionResponse>(createdAtActionResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateBusinessFunction_ReturnsOk()
    {
        var expectedMessage = $"BusinessFunction '{_fixture.UpdateBusinessFunctionCommand.Name}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateBusinessFunctionCommand>(), default))
            .ReturnsAsync(new UpdateBusinessFunctionResponse
            {
                Message = expectedMessage,
                BusinessFunctionId = _fixture.UpdateBusinessFunctionCommand.Id
            });

        var result = await _controller.UpdateBusinessFunction(_fixture.UpdateBusinessFunctionCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessFunction_ReturnsOk()
    {
        var expectedMessage = "BusinessFunction 'TestBusinessFunction' has been deleted successfully!.";
        var businessFunctionId = "valid-guid";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteBusinessFunctionCommand>(c => c.Id == businessFunctionId), default))
            .ReturnsAsync(new DeleteBusinessFunctionResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        var result = await _controller.DeleteBusinessFunction(businessFunctionId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteBusinessFunctionResponse>(okResult.Value);

        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteBusinessFunction_Throws_WhenIdIsInvalid()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteBusinessFunction("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedBusinessFunctions_ReturnsExpectedPaginatedList()
    {
        var query = new GetBusinessFunctionPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _fixture.BusinessFunctionListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<BusinessFunctionListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        var result = await _controller.GetPaginatedBusinessFunctions(query);

        var actionResult = Assert.IsType<ActionResult<PaginatedResult<BusinessFunctionListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<BusinessFunctionListVm>>(okResult.Value);

        Assert.Equal(expectedData.Count, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(expectedData.Count, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ReturnsExpectedList()
    {
        _controller.Cache.Remove("all-businessfunctions-name");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessFunctionNameVm);

        var result = await _controller.GetBusinessFunctionNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var businessFunctionNames = Assert.IsAssignableFrom<List<BusinessFunctionNameVm>>(okResult.Value);
        Assert.Equal(_fixture.BusinessFunctionNameVm.Count, businessFunctionNames.Count);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ReturnsEmptyList_WhenNoBusinessFunctionNamesExist()
    {
        _controller.Cache.Remove("all-businessfunctions-name");

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<BusinessFunctionNameQuery>(), default))
            .ReturnsAsync(new List<BusinessFunctionNameVm>());

        var result = await _controller.GetBusinessFunctionNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<BusinessFunctionNameVm>)okResult.Value!));
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ReturnsTrue_WhenNameExists()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        var result = await _controller.IsBusinessFunctionNameExist("ExistingBusinessFunction", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetBusinessFunctionNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        var result = await _controller.IsBusinessFunctionNameExist("NewBusinessFunction", null);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ThrowsInvalidArgumentException_WhenNameIsInvalid()
    {
        var businessFunctionName = "";

        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsBusinessFunctionNameExist(businessFunctionName, null));
    }
}