﻿using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Queries;

public class GetReplicationPaginatedListQueryHandlerTests : IClassFixture<ReplicationFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly ReplicationFixture _replicationFixture;

    private readonly InfraObjectFixture _infraObjectFixture;

    private readonly Mock<IReplicationRepository> _mockReplicationRepository;
    private readonly Mock<IReplicationViewRepository> _mockReplicationViewRepository;

    private readonly GetReplicationPaginatedListQueryHandler _handler;

    public GetReplicationPaginatedListQueryHandlerTests(ReplicationFixture replicationFixture, InfraObjectFixture infraObjectFixture)
    {
        _replicationFixture = replicationFixture;

        _infraObjectFixture = infraObjectFixture;

        var mockILoggedInUserService = new Mock<ILoggedInUserService>();

        _mockReplicationViewRepository = new Mock<IReplicationViewRepository>();

        _replicationFixture.Replications[0].Name = "TestCase";
        _replicationFixture.Replications[0].Type = "MYSQL";
        _replicationFixture.Replications[0].SiteName = "Site_DR";
        _replicationFixture.Replications[0].Properties = "{\"Name\": \"Status\", \"password\": \"Admin@4321\"}";

        _replicationFixture.Replications[1].Name = "TestBase";
        _replicationFixture.Replications[1].Type = "MSSQL";
        _replicationFixture.Replications[1].SiteId = 12.ToString();
        _replicationFixture.Replications[1].SiteName = "DemO_Site";

        _mockReplicationRepository = ReplicationRepositoryMocks.GetPaginatedReplicationRepository(_replicationFixture.Replications);
        _mockReplicationRepository = ReplicationRepositoryMocks.GetReplicationTypeRepository(_replicationFixture.Replications);
        var mockInfraObjectRepository = InfraObjectRepositoryMocks.GetPaginatedInfraObjectRepository(_infraObjectFixture.InfraObjects);

        _handler = new GetReplicationPaginatedListQueryHandler(_replicationFixture.Mapper, _mockReplicationViewRepository.Object);

        _replicationFixture.Replications[0].LicenseKey = "7OiVZ+aFxmcVHBrweWh3ZTx2e15l0jdG6ulLqnKi9uI=$XMfsb/bBKGBHBlIJN3ADJt8vTJr7cQAxLro7hyxPCBA=";
        _replicationFixture.Replications[1].LicenseKey = "7OiVZ+aFxmcVHBrweWh3ZTx2e15l0jdG6ulLqnKi9uI=$XMfsb/bBKGBHBlIJN3ADJt8vTJr7cQAxLro7hyxPCBA=";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[0].TypeId, PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationListVm>>();

        result.TotalCount.ShouldBe(0);
    }
    
    [Fact]
    public async Task Handle_Return_PaginatedReplications_When_QueryStringMatch_WithType()
    {
        _replicationFixture.Replications[0].ReferenceId = "ebb48597-7bc6-4329-9485-ee2d1e5299c0";

      //  _infraObjectFixture.InfraObjects[0].DRReplicationId = _replicationFixture.Replications[0].ReferenceId;

        var result = await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[0].TypeId, PageNumber = 1, PageSize = 10, SearchString = "SQL" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<ReplicationListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("TestCase");

        result.Data[0].Type.ShouldBe("MYSQL");
    }

    [Fact]
    public async Task Handle_Return_PaginatedReplications_When_QueryStringMatch()
    {
        _replicationFixture.Replications[1].ReferenceId = "ebb48597-7bc6-4329-9485-ee2d1e5299c0";

       // _infraObjectFixture.InfraObjects[1].DRReplicationId = _replicationFixture.Replications[1].ReferenceId;

        var result = await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[1].TypeId, PageNumber = 1, PageSize = 10, SearchString = "MS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ReplicationListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("TestBase");

        result.Data[0].SiteId.ShouldBe(_replicationFixture.Replications[1].SiteId);

        result.Data[0].SiteName.ShouldBe("DemO_Site");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        _replicationFixture.Replications[0].ReferenceId = "ebb48597-7bc6-4329-9485-ee2d1e5299c0";

      //  _infraObjectFixture.InfraObjects[0].DRReplicationId = _replicationFixture.Replications[0].ReferenceId;

        var result = await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[1].TypeId, PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Replications_With_MultipleQueryStringParameter()
    {
        _replicationFixture.Replications[0].ReferenceId = "ebb48597-7bc6-4329-9485-ee2d1e5299c0";

      //  _infraObjectFixture.InfraObjects[0].DRReplicationId = _replicationFixture.Replications[0].ReferenceId;

        var result = await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[0].TypeId, PageNumber = 1, PageSize = 10, SearchString = "name=Case;type=MY;sitename=Site;properties={\"Name\": \"Status\", \"password\": \"Admin@4321\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].Name.ShouldBe("TestCase");

        result.Data[0].Type.ShouldBe("MYSQL");

        result.Data[0].SiteName.ShouldBe("Site_DR");

        result.Data[0].Properties.ShouldBe("{\"Name\": \"Status\", \"password\": \"Admin@4321\"}");

        result.Data[0].Properties.ShouldBe(_replicationFixture.Replications[0].Properties);

       
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetReplicationPaginatedListQuery { TypeId = _replicationFixture.Replications[0].TypeId }, CancellationToken.None);

        _mockReplicationRepository.Verify(x => x.GetReplicationByType((It.IsAny<string>())), Times.Once());
        //_mockReplicationRepository.Verify(x => x.GetPaginatedQuery(), Times.Once());
    }
}