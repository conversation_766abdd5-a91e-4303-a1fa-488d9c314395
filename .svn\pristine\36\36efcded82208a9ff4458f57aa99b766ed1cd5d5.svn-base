﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowIdUnique;

public class GetWorkflowProfileInfoByWorkflowIdUniqueQueryHandler : IRequestHandler<
    GetWorkflowProfileInfoByWorkflowIdUniqueQuery, GetWorkflowProfileInfoByWorkflowIdVm>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public GetWorkflowProfileInfoByWorkflowIdUniqueQueryHandler(IMapper mapper,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository)
    {
        _mapper = mapper;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
    }

    public async Task<GetWorkflowProfileInfoByWorkflowIdVm> Handle(
        GetWorkflowProfileInfoByWorkflowIdUniqueQuery request, CancellationToken cancellationToken)
    {
        var workflowProfileInfo =
            await _workflowProfileInfoRepository.GetProfileIdAttachByWorkflowId(request.WorkflowId);

        var workflowProfileInfoDetail = _mapper.Map<GetWorkflowProfileInfoByWorkflowIdVm>(workflowProfileInfo);

        if (workflowProfileInfoDetail is null)
        {
            var workflowProfileInfoDto = new GetWorkflowProfileInfoByWorkflowIdVm
            {
                ProfileId = string.Empty,
                ProfileName = string.Empty,
                IsAttach = false
            };
            return workflowProfileInfoDto;
        }

        workflowProfileInfoDetail.IsAttach = true;

        return workflowProfileInfoDetail;
    }
}