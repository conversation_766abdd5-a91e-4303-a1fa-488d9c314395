﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;

public class
    GetDrDrillByBusinessServiceIdQueryHandler : IRequestHandler<GetDrDrillByBusinessServiceIdQuery,
        List<WorkflowOperationDrDrillVm>>
{
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IInfraDashboardViewRepository _infraDashboardViewRepository;
    private readonly IServerViewRepository _serverViewRepository;

    public GetDrDrillByBusinessServiceIdQueryHandler(
        IWorkflowOperationRepository workflowOperationRepository, 
        IInfraDashboardViewRepository infraDashboardViewRepository, 
        IServerViewRepository serverViewRepository)
    {
     
        _workflowOperationRepository = workflowOperationRepository;
        _infraDashboardViewRepository = infraDashboardViewRepository;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<WorkflowOperationDrDrillVm>> Handle(GetDrDrillByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var workflowOperation = await _workflowOperationRepository.GetDrillDetailsByBusinessServiceId(request.BusinessServiceId);

        if(workflowOperation.WorkflowOperationDrDrillVm.Count == 0)
            return new List<WorkflowOperationDrDrillVm>();

        var infraDashboard = await _infraDashboardViewRepository.GetInfraObjectViewByInfraObjectIds(workflowOperation.InfraIds);

        var infraServerComponent = infraDashboard
            .Where(x => x.ServerProperties.IsNotNullOrWhiteSpace())
            .ToDictionary(
                kvp => kvp.ReferenceId,
                kvp =>
                {
                    var json = JObject.Parse(kvp.ServerProperties);

                    var serverIds = new List<ServerIds>
                    {
                        new()
                        {
                            PrServerId = json.SelectToken("PR.id")?.ToString()
                                         ?? json.SelectToken("PR.Id")?.ToString(),
                            DrServerId = json.SelectToken("DR.id")?.ToString()
                                         ?? json.SelectToken("DR.Id")?.ToString()
                        }
                    };

                    return serverIds
                        .Where(ids => ids.PrServerId != null || ids.DrServerId != null)
                        .GroupBy(ids => new { ids.PrServerId, ids.DrServerId })
                        .Select(group => group.First())
                        .ToList();
                }
            );

        var serverIds = infraServerComponent
            .SelectMany(x => x.Value)
            .SelectMany(id => new[] { id.PrServerId, id.DrServerId })
            .Where(id => id != null)
            .ToList();

        var servers = await _serverViewRepository.GetServerTypeByIds(serverIds);

        var serversById = servers.ToDictionary(server => server.ReferenceId, server => server);

        foreach (var workflowOperationDrill in workflowOperation.WorkflowOperationDrDrillVm)
        {
            var configuredRtO = new HashSet<string>();

            foreach (var groupDrill in workflowOperationDrill.WorkflowOperationGroupDrDrillVms)
            {
                var infraObject = infraDashboard.FirstOrDefault(x => x.ReferenceId == groupDrill.InfraObjectId);

                var configuredRtoValue = infraObject?.ConfiguredRTO?.Trim();
                if (configuredRtoValue.IsNotNullOrWhiteSpace())
                {
                    configuredRtO.Add(configuredRtoValue);
                }

                if (infraServerComponent.TryGetValue(groupDrill.InfraObjectId, out var serverIdsList))
                {
                    var prServerIds = serverIdsList.Select(pr => pr.PrServerId).Where(id => id != null).ToList();
                    var drServerIds = serverIdsList.Select(dr => dr.DrServerId).Where(id => id != null).ToList();

                    foreach (var serverId in prServerIds)
                    {
                        var server = serversById.GetValueOrDefault(serverId);
                        if (server != null)
                        {
                            groupDrill.PrIpAddress = server.IpAddress;
                        }
                    }

                    foreach (var serverId in drServerIds)
                    {
                        var server = serversById.GetValueOrDefault(serverId);
                        if (server != null)
                        {
                            groupDrill.DrIpAddress = server.IpAddress;
                        }
                    }
                }
            }

            var configuredRto = configuredRtO.Max();
            workflowOperationDrill.ConfiguredRTO = configuredRto;
            workflowOperationDrill.RtoStatus = !workflowOperationDrill.WorkflowOperationGroupDrDrillVms.Any(x=>x.Status.Equals("Aborted"))&& (workflowOperationDrill.TotalTime <=
                                                TimeSpan.FromMinutes(int.Parse(configuredRto)));
        }

        return workflowOperation.WorkflowOperationDrDrillVm;







        //var tt = await _workflowOperationRepository.GetDrillDetailsByBusinessServiceId(request.BusinessServiceId);


        //var workflowOperationGroupList = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByBusinessServiceId(request.BusinessServiceId);

        //if (workflowOperationGroupList.Count == 0)
        //    throw new NotFoundException(nameof(Domain.Entities.WorkflowOperationGroup), request.BusinessServiceId);

        //var workflowOperationGroup = workflowOperationGroupList.FirstOrDefault();


        //var workflowOperation =
        //    await _workflowOperationRepository.GetByReferenceIdAsync(workflowOperationGroup?.WorkflowOperationId);

        //var workflowOperationGroupVm =
        //    await _workflowOperationGroupRepository.GetWorkflowOperationByWorkflowOperationId(workflowOperation
        //        .ReferenceId);

        //var workflowOpeartionDtos = _mapper.Map<WorkflowOperationDrDrillVm>(workflowOperation);

        //var workflowOperationGroupDto = _mapper.Map<List<WorkflowOperationGroupDrDrillVm>>(workflowOperationGroupVm);

        //workflowOpeartionDtos.TotalTime = GetTotalTime(workflowOperation.StartTime.ToString(),
        //    workflowOperation.EndTime.ToString());

        //workflowOperationGroupDto.ForEach(workflow =>
        //{
        //    var infraobject = _infraObjectRepository.GetByReferenceIdAsync(workflow.InfraObjectId).Result;

        //    var businessFunction = _businessFunctionRepository.GetByReferenceIdAsync(infraobject.BusinessFunctionId)
        //        .Result;

        //    workflowOpeartionDtos.ConfiguredRTO = businessFunction.ConfiguredRTO;

        //    workflowOpeartionDtos.RtoStatus = workflowOpeartionDtos.TotalTime <=
        //                                      TimeSpan.FromMinutes(int.Parse(workflowOpeartionDtos.ConfiguredRTO));

        //    workflow.TotalTime = GetTotalTime(workflow.StartTime.ToString(), workflow.EndTime.ToString());

        //    var prServer = _serverRepository.GetByReferenceIdAsync(infraobject.PRServerId).Result;

        //    workflow.PrIpAddress = prServer != null
        //        ? GetJsonProperties.GetIpAddressFromProperties(prServer.Properties)
        //        : "NA";

        //    var drServer = _serverRepository.GetByReferenceIdAsync(infraobject.DRServerId).Result;

        //    workflow.DrIpAddress = drServer != null
        //        ? GetJsonProperties.GetIpAddressFromProperties(drServer.Properties)
        //        : "NA";
        //});


        //workflowOpeartionDtos.WorkflowOperationGroupDrDrillVms = workflowOperationGroupDto;

        // return workflowOpeartionDtos;
    }
}