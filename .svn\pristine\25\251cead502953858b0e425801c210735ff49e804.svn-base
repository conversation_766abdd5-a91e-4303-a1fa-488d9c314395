using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftCategoryMaster.Events.Create;

public class DriftCategoryMasterCreatedEventHandler : INotificationHandler<DriftCategoryMasterCreatedEvent>
{
    private readonly ILogger<DriftCategoryMasterCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftCategoryMasterCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DriftCategoryMasterCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DriftCategoryMasterCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DriftCategoryMaster",
            Entity = "DriftCategoryMaster",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DriftCategoryMaster '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DriftCategoryMaster '{createdEvent.Name}' created successfully.");
    }
}