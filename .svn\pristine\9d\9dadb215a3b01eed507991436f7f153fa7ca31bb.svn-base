﻿using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Web.Attributes;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller;

public class WhatifAnalysisControllerShould
{
    private readonly WhatifAnalysisController _controller;

    public WhatifAnalysisControllerShould()
    {
        _controller = new WhatifAnalysisController();
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public void List_ReturnsViewResult()
    {
        // Act
        var result = _controller.List();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName); // Default view name
        Assert.Null(viewResult.Model); // No model passed
    }

    [Fact]
    public void List_ReturnsViewWithCorrectType()
    {
        // Act
        var result = _controller.List();

        // Assert
        Assert.IsType<ViewResult>(result);
        Assert.IsAssignableFrom<IActionResult>(result);
    }

    [Fact]
    public void List_HasAntiXssAttribute()
    {
        // Arrange
        var methodInfo = typeof(WhatifAnalysisController).GetMethod("List");

        // Act
        var antiXssAttribute = methodInfo?.GetCustomAttribute<AntiXssAttribute>();

        // Assert
        Assert.NotNull(antiXssAttribute);
    }

    [Fact]
    public void Controller_HasAreaAttribute()
    {
        // Arrange
        var controllerType = typeof(WhatifAnalysisController);

        // Act
        var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

        // Assert
        Assert.NotNull(areaAttribute);
        Assert.Equal("Configuration", areaAttribute.RouteValue);
    }

    [Fact]
    public void Controller_InheritsFromController()
    {
        // Act & Assert
        Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(_controller);
    }

    [Fact]
    public void List_CanBeCalledMultipleTimes()
    {
        // Act
        var result1 = _controller.List();
        var result2 = _controller.List();

        // Assert
        Assert.IsType<ViewResult>(result1);
        Assert.IsType<ViewResult>(result2);
        Assert.NotSame(result1, result2); // Different instances
    }

    [Fact]
    public void List_WithDifferentHttpContexts_ReturnsViewResult()
    {
        // Arrange
        var controller1 = new WhatifAnalysisController();
        var controller2 = new WhatifAnalysisController();

        controller1.ControllerContext.HttpContext = new DefaultHttpContext();
        controller2.ControllerContext.HttpContext = new DefaultHttpContext();

        // Act
        var result1 = controller1.List();
        var result2 = controller2.List();

        // Assert
        Assert.IsType<ViewResult>(result1);
        Assert.IsType<ViewResult>(result2);
    }

    [Fact]
    public void List_DoesNotThrowException()
    {
        // Act & Assert
        var exception = Record.Exception(() => _controller.List());
        Assert.Null(exception);
    }

    [Fact]
    public void List_ReturnsNonNullResult()
    {
        // Act
        var result = _controller.List();

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public void List_ViewResult_HasNullViewData()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.ViewData);
    }

    [Fact]
    public void List_ViewResult_HasNullTempData()
    {
        // Arrange
        _controller.TempData.Clear();

        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Empty(_controller.TempData);
    }

    [Fact]
    public void Controller_HasPublicListMethod()
    {
        // Arrange
        var controllerType = typeof(WhatifAnalysisController);

        // Act
        var listMethod = controllerType.GetMethod("List", BindingFlags.Public | BindingFlags.Instance);

        // Assert
        Assert.NotNull(listMethod);
        Assert.True(listMethod.IsPublic);
        Assert.Equal(typeof(IActionResult), listMethod.ReturnType);
        Assert.Empty(listMethod.GetParameters()); // No parameters
    }

    [Fact]
    public void List_Method_IsNotAsync()
    {
        // Arrange
        var methodInfo = typeof(WhatifAnalysisController).GetMethod("List");

        // Act & Assert
        Assert.NotNull(methodInfo);
        Assert.False(methodInfo.GetCustomAttributes(typeof(System.Runtime.CompilerServices.AsyncStateMachineAttribute), false).Any());
    }

    [Fact]
    public void Controller_CanBeInstantiatedWithoutParameters()
    {
        // Act & Assert
        var exception = Record.Exception(() => new WhatifAnalysisController());
        Assert.Null(exception);
    }

    [Fact]
    public void List_ViewResult_StatusCodeIsNull()
    {
        // Act
        var result = _controller.List() as ViewResult;

        // Assert
        Assert.NotNull(result);
        Assert.Null(result.StatusCode);
    }
}