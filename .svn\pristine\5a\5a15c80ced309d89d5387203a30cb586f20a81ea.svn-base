﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Commands;

public class DeleteWorkflowOperationGroupTests : IClassFixture<WorkflowOperationGroupFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly DeleteWorkflowOperationGroupCommandHandler _handler;

    public DeleteWorkflowOperationGroupTests(WorkflowOperationGroupFixture workflowOperationGroupFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.DeleteWorkflowOperationGroupRepository(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _handler = new DeleteWorkflowOperationGroupCommandHandler(_mockWorkflowOperationGroupRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_WorkflowOperationGroup()
    {
        var validGuid = Guid.NewGuid();

        _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteWorkflowOperationGroupCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowOperationGroupResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_WorkflowOperationGroup()
    {
        var validGuid = Guid.NewGuid();

        _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteWorkflowOperationGroupCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var workflowOperationGroup = await _mockWorkflowOperationGroupRepository.Object.GetByReferenceIdAsync(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);

        workflowOperationGroup.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_NotFoundException_When_Invalid_WorkflowOperationGroupId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteWorkflowOperationGroupCommand { Id = invalidGuid }, CancellationToken.None));
    }
}