﻿using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class IncidentManagementSummaryRepositoryTests
    {
        private readonly ApplicationDbContext _dbContext;

        public IncidentManagementSummaryRepositoryTests()
        {
            _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        }
        public void Dispose()
        {
            _dbContext?.Dispose();
        }
        [Fact]
        public void Constructor_ShouldInitializeRepository()
        {
            // Arrange

            // Act
            var repository = new IncidentManagementSummaryRepository(_dbContext, DbContextFactory.GetMockUserService());

            // Assert
            Assert.NotNull(repository);
        }
    }
}
