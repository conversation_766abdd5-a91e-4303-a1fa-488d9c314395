﻿using ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Create;
using ContinuityPatrol.Application.Features.Replication.Events.SaveAs;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;

public class SaveAsReplicationCommandHandler : IRequestHandler<SaveAsReplicationCommand, SaveAsReplicationResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;
    private readonly ISiteRepository _siteRepository;


    public SaveAsReplicationCommandHandler(IMapper mapper, IReplicationRepository replicationRepository,
        IPublisher publisher, ISiteTypeRepository siteTypeRepository, ISiteRepository siteRepository)
    {
        _mapper = mapper;
        _replicationRepository = replicationRepository;
        _publisher = publisher;
        _siteTypeRepository = siteTypeRepository;
        _siteRepository = siteRepository;
    }

    public async Task<SaveAsReplicationResponse> Handle(SaveAsReplicationCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.ReplicationId, $"Invalid Replication Id '{request.ReplicationId}'");

        var eventToUpdate = await _replicationRepository.GetByReferenceIdAsync(request.ReplicationId);

        eventToUpdate.Id = 0;

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Replication), request.ReplicationId);

        eventToUpdate.ReferenceId = "";

        eventToUpdate.LicenseKey = (eventToUpdate.LicenseId != "NA" || eventToUpdate.LicenseKey.IsNotNullOrWhiteSpace())
                     ? SecurityHelper.Encrypt(eventToUpdate.LicenseKey)
                     : eventToUpdate.LicenseKey;
        _mapper.Map(request, eventToUpdate, typeof(SaveAsReplicationCommand), typeof(Domain.Entities.Replication));

        var Replication =await _replicationRepository.AddAsync(eventToUpdate);

        var response = new SaveAsReplicationResponse
        {
            Id = eventToUpdate.ReferenceId,
            Message = Message.Create(nameof(Domain.Entities.Replication), eventToUpdate.Name)
        };

        if (Replication.Type.ToLower().Contains("perpetuuiti"))
        {

            var logo = GetJsonProperties.GetJsonValue(Replication.Properties, "icon");

            var site = await _siteRepository.GetByReferenceIdAsync(Replication.SiteId);

            var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

            if (siteType.Category.ToLower().Contains("primary"))
                await _publisher.Publish(new ReplicationLicenseInfoCreatedEvent
                {
                    EntityName = Replication.Name,
                    LicenseId = Replication.LicenseId,
                    PONumber = Replication.LicenseKey,
                    EntityId = Replication.ReferenceId,
                    EntityField = Replication.SiteName,
                    Type = Replication.Type,
                    BusinessServiceId = Replication.BusinessServiceId,
                    BusinessServiceName = Replication.BusinessServiceName,
                    Category = Replication.Type,
                    Logo = logo
                }, cancellationToken);
        }

        await _publisher.Publish(new SaveAsReplicationEvent { Name = eventToUpdate.Name }, cancellationToken);
        return response;
    }
}