using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetPaginatedList;

public class GetFiaImpactCategoryPaginatedListQueryHandler : IRequestHandler<GetFiaImpactCategoryPaginatedListQuery,
    PaginatedResult<FiaImpactCategoryListVm>>
{
    private readonly IFiaImpactCategoryRepository _fiaImpactCategoryRepository;
    private readonly IMapper _mapper;

    public GetFiaImpactCategoryPaginatedListQueryHandler(IMapper mapper,
        IFiaImpactCategoryRepository fiaImpactCategoryRepository)
    {
        _mapper = mapper;
        _fiaImpactCategoryRepository = fiaImpactCategoryRepository;
    }

    public async Task<PaginatedResult<FiaImpactCategoryListVm>> Handle(GetFiaImpactCategoryPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
         var productFilterSpec = new FiaImpactCategoryFilterSpecification(request.SearchString);

        var queryable = await _fiaImpactCategoryRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var fiaImpactCategoryList = _mapper.Map<PaginatedResult<FiaImpactCategoryListVm>>(queryable);
        //var queryable = _fiaImpactCategoryRepository.PaginatedListAllAsync();

        //var productFilterSpec = new FiaImpactCategoryFilterSpecification(request.SearchString);

        //var fiaImpactCategoryList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<FiaImpactCategoryListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return fiaImpactCategoryList;
    }
}