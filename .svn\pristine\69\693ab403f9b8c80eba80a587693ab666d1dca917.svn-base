using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BackUpRepositoryTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BackUpRepository _repository;

    public BackUpRepositoryTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BackUpRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;

        // Act
        var result = await _repository.AddAsync(backUp);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUp.HostName, result.HostName);
        Assert.Equal(backUp.BackUpType, result.BackUpType);
        Assert.Single(_dbContext.BackUps);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        await _repository.AddAsync(backUp);

        backUp.HostName = "UpdatedHostName";
        backUp.BackUpPath = "C:/UserPath";
        backUp.IsBackUpServer =false;

        // Act
        var result = await _repository.UpdateAsync(backUp);

        // Assert
        Assert.Equal("UpdatedHostName", result.HostName);
        Assert.Equal("C:/UserPath", result.BackUpPath);
        Assert.False(result.IsBackUpServer);
    }   

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        await _repository.AddAsync(backUp);

        // Act
        var result = await _repository.DeleteAsync(backUp);

        // Assert
        Assert.Equal(backUp.HostName, result.HostName);
        Assert.Empty(_dbContext.BackUps);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        var addedEntity = await _repository.AddAsync(backUp);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.HostName, result.HostName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        await _repository.AddAsync(backUp);

        // Act
        var result = await _repository.GetByReferenceIdAsync(backUp.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUp.ReferenceId, result.ReferenceId);
        Assert.Equal(backUp.HostName, result.HostName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        await _repository.AddRangeAsync(backUps);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(backUps.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;

        // Act
        var result = await _repository.AddRangeAsync(backUps);

        // Assert
        Assert.Equal(backUps.Count, result.Count());
        Assert.Equal(backUps.Count, _dbContext.BackUps.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        await _repository.AddRangeAsync(backUps);

        // Act
        var result = await _repository.RemoveRangeAsync(backUps);

        // Assert
        Assert.Equal(backUps.Count, result.Count());
        Assert.Empty(_dbContext.BackUps);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region FindByFilter Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        var targetHostName = "TEST_HOST";
        backUps.First().HostName = targetHostName;
        await _repository.AddRangeAsync(backUps);

        // Act
        var result = await _repository.FindByFilter(x => x.HostName == targetHostName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetHostName, result.First().HostName);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        await _repository.AddRangeAsync(backUps);

        // Act
        var result = await _repository.FindByFilter(x => x.HostName == "NON_EXISTENT_HOST");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenHostNameExistsAndIdIsInvalid()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        backUp.HostName = "ExistingHostName";
        await _repository.AddAsync(backUp);

        // Act
        var result = await _repository.IsNameExist("ExistingHostName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenHostNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        await _repository.AddRangeAsync(backUps);

        // Act
        var result = await _repository.IsNameExist("NonExistentHostName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenHostNameExistsForSameEntity()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        backUp.HostName = "SameHostName";
        await _repository.AddAsync(backUp);

        // Act
        var result = await _repository.IsNameExist("SameHostName", backUp.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestHostName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;
        var backUp1 = backUps[0];
        var backUp2 = backUps[1];

        // Act
        var task1 = _repository.AddAsync(backUp1);
        var task2 = _repository.AddAsync(backUp2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BackUps.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(backUps);
        var initialCount = backUps.Count;

        var toUpdate = backUps.Take(2).ToList();
        toUpdate.ForEach(x => x.DatabaseName = "CP_CORE");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = backUps.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.DatabaseName == "CP_CORE").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInHostNames()
    {
        // Arrange
        var backUp = _backUpFixture.BackUpDto;
        backUp.HostName = "Test@Host#123$%";

        // Act
        var addedBackUp = await _repository.AddAsync(backUp);
        var nameExists = await _repository.IsNameExist("Test@Host#123$%", "invalid-guid");

        // Assert
        Assert.NotNull(addedBackUp);
        Assert.Equal("Test@Host#123$%", addedBackUp.HostName);
        Assert.True(nameExists);
    }

    [Fact]
    public async Task Repository_ShouldHandleBackupTypeFiltering()
    {
        // Arrange
        var backUps = _backUpFixture.BackUpList;

        backUps[0].BackUpType= "Full";
        backUps[1].BackUpType = "Incremental";
        backUps[2].BackUpType = "Full";

       _dbContext.BackUps.AddRange(backUps);
        _dbContext.SaveChanges();

        // Act
        var fullBackups = await _repository.FindByFilter(x => x.BackUpType == "Full");
        var incrementalBackups = await _repository.FindByFilter(x => x.BackUpType == "Incremental");

        // Assert
        Assert.Equal(2, fullBackups.Count);
        Assert.Single(incrementalBackups);
        Assert.All(fullBackups, x => Assert.Equal("Full", x.BackUpType));
        Assert.All(incrementalBackups, x => Assert.Equal("Incremental", x.BackUpType));
    }

    #endregion
}
