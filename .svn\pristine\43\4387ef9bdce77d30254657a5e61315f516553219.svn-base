﻿using ContinuityPatrol.Application.Features.TableAccess.Queries.GetNameUnique;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Queries
{
    public class GetTableAccessNameUniqueQueryHandlerTests
    {
        private readonly Mock<ITableAccessRepository> _mockTableAccessRepository;
        private readonly GetTableAccessNameUniqueQueryHandler _handler;

        public GetTableAccessNameUniqueQueryHandlerTests()
        {
            _mockTableAccessRepository = new Mock<ITableAccessRepository>();
            _handler = new GetTableAccessNameUniqueQueryHandler(_mockTableAccessRepository.Object);
        }

        [Fact]
        public async Task Handle_TableAccessNameExists_ReturnsTrue()
        {
            var request = new GetTableAccessNameUniqueQuery
            {
                TableAccessName = "test_table",
                TableAccessId = Guid.NewGuid().ToString()
            };

            _mockTableAccessRepository
                .Setup(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId))
                .ReturnsAsync(true);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.True(result);
            _mockTableAccessRepository.Verify(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId), Times.Once);
        }

        [Fact]
        public async Task Handle_TableAccessNameDoesNotExist_ReturnsFalse()
        {
            var request = new GetTableAccessNameUniqueQuery
            {
                TableAccessName = "non_existent_table",
                TableAccessId = Guid.NewGuid().ToString()
            };

            _mockTableAccessRepository
                .Setup(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId))
                .ReturnsAsync(false);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.False(result);
            _mockTableAccessRepository.Verify(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId), Times.Once);
        }

        [Fact]
        public async Task Handle_RepositoryThrowsException_ThrowsException()
        {
            var request = new GetTableAccessNameUniqueQuery
            {
                TableAccessName = "test_table",
                TableAccessId = Guid.NewGuid().ToString()
            };

            _mockTableAccessRepository
                .Setup(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId))
                .ThrowsAsync(new System.Exception("Repository error"));

            var exception = await Assert.ThrowsAsync<System.Exception>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("Repository error", exception.Message);
            _mockTableAccessRepository.Verify(repo => repo.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId), Times.Once);
        }
    }
}
