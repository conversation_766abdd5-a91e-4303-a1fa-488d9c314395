﻿let replicJobUrls = {
    nameExistUrl: "Manage/ReplicationJob/IsReplicationJobNameExist",
    GetPagination: "/Manage/ReplicationJob/GetPagination",
    UpdateReplicationJobState: "/Manage/ReplicationJob/UpdateReplicationJobState",
    ResetJobStatus: "/Manage/ReplicationJob/ResetJobStatus",
    GetTemplateByReplicationTypeId: 'Manage/MonitoringJob/GetTemplateByReplicationTypeId',
/*    GetGroupPolicies:"Manage/JobManagement/GetGroupPolicies"*/
}
let replicaGroupPolicy = '', replicaArrayData = [], selectedValues = [], dataTable
const exceptThisSymbols = ["e", "E", "+", "-", "."];
function replicationJobBebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$(function () {
    let createPermission = $("#ReplicationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#ReplicationDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $(".CreateButtonPermission").removeClass('.CreateButtonPermission').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#tblJobManagement').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": replicJobUrls.GetPagination ,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 2 ? "name" : sortIndex === 3 ? "templateName" : sortIndex === 4 ? "nodeName" :
                        sortIndex === 5 ? "solutionType" : sortIndex === 6 ? "scheduleTime" : sortIndex === 7 ? "lastExecutionTime" : sortIndex === 8 ? "status" : sortIndex === 9 ? "state" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#replicaJobSearchInp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5, 6, 7, 8, 9],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {

                        return '<input type="checkbox" name="rowCheckbox" class="form-check-input ' + data.state + '" checkid="' + data.id + '" id="' + data.state + '">';

                    }
                },
                {
                    "data": "name", "name": "Job Name", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;

                    }
                },
                {
                    "data": "templateName", "name": "Template Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                        }
                        return data;
                    }
                }, {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "solutionType", "name": "Solution Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Schedule Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "Last Monitoring Time", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `<td><span title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;;

                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending" || data == null) {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data == null ? "Pending" : data}"> ${data == null ? "Pending" : data}</span></td>`;
                    }

                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {

                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-success me-1";
                        }
                        return `<td><i class="${iconClass}" id="icon" title="${data ?? "NA"}" ></i></td>
                              <td><span id="jobmanagestate"> ${data  ?? "NA"}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        let errorVisible = row?.exceptionMessage === null || row?.exceptionMessage === undefined || row?.exceptionMessage === '';
                        let errmsg = row?.exceptionMessage
                        if (createPermission == "true" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="delete-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="edit-button ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" id="reset" class="icon-disabled"
                                                       data-bs-target="#ViewHistory">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job_error_message="'${btoa(errmsg)}'" class=" Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-error-message"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                    }, "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#replicaJobSearchInp').on('keydown input', replicationJobBebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        } else {
            const JobNameCheckbox = $("#JobName");
            const TemplatenameCheckbox = $("#Templatename");
            const SolutiontypeCheckBox = $("#Solutiontype");
            const StateCheckBox = $("#State");
            const inputValue = $('#replicaJobSearchInp').val();
            if (JobNameCheckbox.is(':checked')) {
                selectedValues.push(JobNameCheckbox.val() + inputValue);
            }
            if (TemplatenameCheckbox.is(':checked')) {
                selectedValues.push(TemplatenameCheckbox.val() + inputValue);
            }
            if (SolutiontypeCheckBox.is(':checked')) {
                selectedValues.push(SolutiontypeCheckBox.val() + inputValue);
            }
            if (StateCheckBox.is(':checked')) {
                selectedValues.push(StateCheckBox.val() + inputValue);
            }
            var currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {
                    if (e.target.value && json?.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
        }
    }));
})
$('#selectExecutionPolicy').on('change', function () {
    if ($(this).val() == '1') {
        $('#groupPolicy').show();
    /*    SetGroupPolicy()*/
    }
    else {
        $('#groupPolicy').hide();
    }
})
$('#ReplicaJobActiveBtn').on('click',async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "Active") {
            datas.push({
                "Id": obj.getAttribute("checkid"),
                "State": "Active"
            })
        }
    })
    if (datas.length) {
       await $.ajax({
           url: replicJobUrls.UpdateReplicationJobState,
            type: 'PUT',
            data: { "UpdateReplicationJobStates": datas, __RequestVerificationToken: gettoken() },
            success: function (result) {
                if (result?.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {
            if (datas?.length == 0) {
                if (datas?.length == 0) {
                    notificationAlert("warning", "Jobs state has already updated to 'Active' state")
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                }
            }
        }
    }
})
$('#ReplicaJobInactiveBtn').on('click',async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "InActive") {
            datas.push({
                "Id": obj.getAttribute("checkid"),
                "State": "InActive"
            })
        }
    })
    if (datas.length) {
      await  $.ajax({
            url: replicJobUrls.UpdateReplicationJobState,
            type: 'PUT',
            data: { "UpdateReplicationJobStates": datas, __RequestVerificationToken: gettoken() },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {
            if (datas?.length == 0) {
                notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
                setTimeout(() => {
                    location.reload();
                }, 2000)
            }
        }
    }
})
$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        if (e.target.checked) {
            $('input[name="rowCheckbox"]').prop("checked", true);

        } else {
            $('input[name="rowCheckbox"]').prop("checked", false)
        }
    }, 100)

})
$("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
    $('input[name="checkboxAll"]').prop("checked", false)
})
$("#selectTemplateName").on('change',
    function () {
        var selectTemplateName = $("#selectTemplateName option:selected").attr('id');
        $('#textTemplateId').val(selectTemplateName);
        $('#textTemplateName').val($("#selectTemplateName option:selected").text());
        const value = $(this).val();
        var filtervalue = $.grep(value, function (value) {
            return value !== "";
        });
        validateJobDropDown(filtervalue, "Select template name", $('#TemplateName-error'));
    });
$('#selectExecutionPolicy').on('change', function () {
    replicaGroupPolicy = $(this).val()
    $("#selectGroupPolicy").val('').trigger('change');
    $('#GroupPolicy-error').text('').removeClass('field-validation-error');
    validateJobDropDown($(this).val(), "Select execution policy", $('#ExecutionPolicy-error'));
})
$("#selectInfraObjectName").on('change', function () {
    var selectInfraObjectName = $(this).find('option:selected');
    replicaArrayData = []
    let strArrayData = '';
    selectInfraObjectName?.each(function () {
        let option = $(this);
        let id = option.attr('id');
        let obj = { Id: id, Name: option.text() };
        replicaArrayData.push(obj)
    });
    strArrayData = JSON.stringify(replicaArrayData)
    $('#textInfraObjectProperties').val(strArrayData)
    validateJobDropDown($(this).val(), "Select infraObject name", $('#InfraObjectName-error'));
    cronshow()
});
function cronshow() {
    if ($("#selectInfraObjectName").val().length > 0) {
        $("#cron").show()
    } else {
        $("#cron").hide()
    }
}
cronshow()
// Error message
$('#tblJobManagement').on('click', '.Error-button', function () {
    let noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">'
    let job_error_message = $(this).attr('job-error_message')
    job_error_message = atob(job_error_message)
    if (!job_error_message || job_error_message == 'null') {
        $("#error_message").css('text-align', 'center')
            .html(noData);
    } else {
        $('#error_message').text(job_error_message);
    }
});
//Delete
$('#tblJobManagement').on('click', '.delete-button', function () {
    var jobId = $(this).data('job-id');
    var jobName = $(this).data('job-name');
    $("#deleteData").attr("title", jobName).text(jobName);
    $('#textDeleteId').val(jobId);
});
//Update   
$('#tblJobManagement').on('click', '.edit-button', function () {
    var jobData = $(this).data("job");
    populateModalFields(jobData);
    Tab_selection(jobData);
    Tab_schedule_type(jobData);
    $('#replicaJobSaveFunction').text("Update");
    ClearJobErrorElements();
    $('#CreateModal').modal('show');
});
//reset
$('#tblJobManagement').on('click', '.reset-button',async function () {
    var jobData = $(this).data('job')
    jobData.__RequestVerificationToken = gettoken()
   await $.ajax({
       url: replicJobUrls.ResetJobStatus ,
        type: 'POST',
        data: jobData,
        success: function (result) {
            if (result?.success) {
                notificationAlert("success", result?.data?.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                errorNotification(result)
            }
        }
    });
});

$('#textJobName').on('input', replicationJobBebounce(async function () {
    let value = await sanitizeInput($("#textJobName").val());
    $("#textJobName").val(value);
    await validateJobName(value, $('#textJobId').val(), replicJobUrls.nameExistUrl);
}));

$('#selectTemplateName').on('change', function (event) {
    validateJobDropDown($(this).val(), "Select workflow templates", $('#TemplateName-error'));
});

$('#selectSolutionType').on('change', function (event) {
    validateJobDropDown($(this).val(), "Select solution type", $('#SolutionType-error'));
    var value = $("#selectSolutionType option:selected").attr('id');
    getInfraObjectDetails(value)
    $("#solutionTypeId").val(value);
    $("#solutionType").val($(this).val());
});

$('#selectGroupPolicy').on('change', function (event) {
    var val = $("#selectExecutionPolicy").val();
    if (val == "1") {
        var Id = $("#selectGroupPolicy option:selected").attr('id');
        $('#policyid').val(Id);
        validateJobDropDown($(this).val(), "Select group node policy", $('#GroupPolicy-error'));
    }
    if (val == "2") {
        $('#policyid').val("");
    }
});

$('#textStateActive').on('click', function (event) {
    ValidateRadioButton($('#state-error'));
});

$('#textStateInactive').on('click', function (event) {
    ValidateRadioButton($('#state-error'));
});

$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});

$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() > 23 || $(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val() > 59) {
        $('#txtMinutes').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});
$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})
$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});

$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});
$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});

$('#ddlMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#ddlMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select hours", $('#CronddlMin-error'));
});

$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#MonthlyHours-error'));
});
function srvTime() {
    try { 
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}

$('.datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val())
    let currentdate = new Date(srvTime())
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});

$('#lblMonth').on("change", function () {
    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    var selectedDate = new Date($(this).val());

    var currentDate = new Date();
    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        var data = ""
        data = i + 1
        $('input[name="Monthyday"]')?.each(function () {
            var checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel")?.each(function () {
            var checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
        $("#CronMon-error").text("").removeClass("field-validation-error")
    } else {
        $('input[name="Monthyday"]')?.each(function () {
            var checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});

$('input[name=weekDays]').on('click', function () {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day(s)", $('#CronDay-error'));
});

$('input[name=Monthyday]').on('click', function () {
    var checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date(s)", $('#CronMon-error'));
});

$('.nav-link').on("click", function () {
    ClearCroneElements();
});

$('input[name = "switchPlan"]').on('click', function () {
    ClearCroneElements();
});

$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});
function populateModalFields(jobData) {
    $("#txtlastexecutetime").val(jobData?.lastExecutionTime)
    $('#selectInfraObjectName').empty()
    getInfraObjectDetails(jobData?.solutionTypeId)
    setTimeout(() => {
        $('#selectTemplateName').val(jobData?.templateId).trigger("change")
    }, 500)
    $("#datetimeCronlist").val(jobData?.type)
    $('#policyid').val(jobData?.groupPolicyId);
    $('#textJobId').val(jobData?.id);
    $('#textJobName').val(jobData?.name);
    $('#textTemplateId').val(jobData?.templateId);
    $('#selectSolutionType').val(jobData?.solutionType);
    $('#solutionTypeId').val(jobData?.solutionTypeId);
    $('#textNodeId').val(jobData?.nodeId);
    $('#textNodeName').val(jobData?.nodeName);
    $('#textcompanyId').val(jobData?.companyId);
    $('#textStatus').val("Pending");

    $('#selectExecutionPolicy').val(jobData?.executionPolicy);
    if (jobData?.executionPolicy == '1') {
        $('#groupPolicy').show();
        $('#selectGroupPolicy').val(jobData?.groupPolicyName);
    }
    else {
        $('#groupPolicy').hide();
    }
    var scheduleTime = jobData?.scheduleTime.split(" ")
    setTimeout(() => {
        if (jobData?.scheduleTime.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6])
        }
        if (jobData?.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5])
        }
        if (scheduleTime?.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }
        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (jobData?.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (jobData?.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4])
        }

        if (scheduleTime?.length >= 12) {
            var year = parseInt(scheduleTime[12])
            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;

            $("#lblMonth").val(newdate).trigger("change")
            scheduleTime[5]?.split(",").forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]);
        }
    }, 500)
    if (jobData?.state == "Active") {
        $("#textStateActive").prop("checked", true);
    }
    else {
        $("#textStateInactive").prop("checked", true);
    }
}

$('#replicaJobSaveFunction').on("click", async function () {

    var form = $("#CreateForm");

    GetIsSchedule();
    Get_ScheduleTypes();

    var isName = await validateJobName($("#textJobName").val(), $("#textJobId").val(), replicJobUrls.nameExistUrl);

    var isPolicy = validateJobDropDown($('#selectExecutionPolicy').val(), "Select execution policy", $('#ExecutionPolicy-error'));

    var isSolutionType = validateJobDropDown($('#selectSolutionType').val(), "Select solution type", $('#SolutionType-error'));

    var isGroupPolicy = validateJobDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#GroupPolicy-error'));

    var isWorkflow = validateJobDropDown($('#selectTemplateName').val(), "Select workflow templates", $('#TemplateName-error'));

    var isStateActive = ValidateRadioButton($('#state-error'));
    var isScheduler = CronValidation();
    let crontype = $("#datetimeCronlist").val()
    var { CronExpression, listcron } = JobCronExpression();
    document.getElementById("textCronExpression").value = CronExpression;
    document.getElementById("textScheduleTime").value = listcron;
    document.getElementById("cronexpresstype").value = crontype
    document.getElementById("textStatus").value = "Pending"
    if (isName && isSolutionType && isPolicy && isStateActive && isScheduler && isWorkflow && (replicaGroupPolicy == '1' ? isGroupPolicy : true)) {
        form.trigger("submit");
    }
});
$("#replicaJobCreateButton").on('click', function () {
    $('#replicaJobSaveFunction').text("Save");
    $('#groupPolicy').hide();
    $("#textStateActive").prop("checked", true)
    clearJobFields();
    jobOnce();
});
const clearJobFields = () => {
$("#textJobId,#textJobName,#textTemplateId,#selectTemplateName,#selectSolutionType,#selectInfraObjectName,#selectExecutionPolicy,#textInfraObjectId,#textNodeId,#textNodeName,#textCronExpression,#textStatus,#textState,#selectGroupPolicy,#selectExecution,#textIsSchedule,#textScheduleType").val('');
    $("#textStateInactive").prop("checked", false)
    $('#selectGroupPolicy option:first').prop('selected', 'selected');
    $('#replicaJobSaveFunction').text("Save");
    ClearJobErrorElements();
    ClearCroneElements();
}
async function validateJobName(value, id = null, url) {
    if (!value) {
        $('#Name-error').text('Enter job name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        $('#Name-error').text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        await SpecialCharValidateCustom(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        $('#Name-error').text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        $('#Name-error').text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() ? true : (await GetAsync(url, inputValue, OnError)) ? " Job name already exists" : true;
}
//async function SetGroupPolicy() {
//    var url = RootUrl + replicJobUrls.GetGroupPolicies;;
//    var data = {};

//    var result = await GetAsync(url, data, OnError);

//    for (var index = 0; index <= result.length; index++) {
//        $('#selectGroupPolicy').append('<option value="' + result[index].groupName + '">' + result[index].groupName + '</option>');
//    }
//}
function ClearJobErrorElements() {
    $("#Name-error,#TemplateName-error,#SolutionType-error,#GroupPolicy-error,#state-error,#CronMin-error,#CronHourly-error,#CroneveryHour-error,#ExecutionPolicy-error,#CronddlMin-error,#CronddlHour-error,#CronDay-error,#CronExpression-error,#Crondaysevery-error, #InfraObjectName-error,#MonthlyHours-error,#CronMon-error,#CronMonthly-error,#CronExpression-error").text('').removeClass('field-validation-error');
}
$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").on("click", function () {
    $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})
$("#nav-Monthly-tab").on("click", function () {
    if ($("#replicaJobSaveFunction").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})
function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours,#datetimeCron").val('');
    $('input[name=weekDays]').prop("checked", false);
    $('input[name=daysevery]').prop("checked", false);
    $('input[name=Monthyday]').prop("checked", false);
    $('input[name="Monthyday"]').prop('disabled', false);
}

function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    //var ddlHours = $('#ddlHours').val();
    var ddlMinutes = $('#ddlMinutes').val();
    var everyHours = $('#everyHours').val();
    var everyMinutes = $('#everyMinutes').val();
    var datetime = $('#datetimeCron').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var MonthlyMins = $('#MonthlyMins').val()
    var isScheduler = ''

    if (document.getElementById('switchMonthly').checked == true) {

        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();

        switch (Scheduler_types) {

            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;
            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;
            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day(s)", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                if (monthlymonth != "") {
                    isSchedulerDay = validateDayNumber(txtmonthday, "Select date(s)", $('#CronMon-error'));
                }
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        isScheduler = validateDayNumber(datetime, "Select schedule time", $('#CronExpression-error')) && validateprevNumber(datetime, "", $('#CronExpression-error'));
    }
    return isScheduler;
}
function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value?.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function ValidateRadioButton(errorElement) {
    if ($('input[name=state]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}
function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        $('#textIsSchedule').val(1);
    } else {
        $('#textIsSchedule').val(2);
    }
}

$(".replicaBtnCancel").on("click", function () {
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
})
function jobOnce() {
    Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    var elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    var elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
var monthInput = document.getElementById("lblMonth");
var today = new Date();
var currentYear = today.getFullYear();
var currentMonth = today.getMonth() + 1;
var minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
var maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth);

const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');
const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;

const datetimeInput = document.getElementById('datetimeCron');
datetimeInput.min = minformattedDate;
datetimeInput.max = maxformattedDate;
const getInfraObjectDetails = async (id) => {
    $("#selectTemplateName").empty();
    $("#solutionTypeId").val(id);
    let data = {}
    data.replicationTypeId = id
    await $.ajax({
        type: "GET",
        url: RootUrl + replicJobUrls.GetTemplateByReplicationTypeId ,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                let html = ''
                let data = result?.data;
                html += '<option value="">Select Workflow Templates</option>';
                data.forEach((item) => {
                    html += `<option id='${item.id}' value='${item.id}'>${item.name}</option>`;
                })
                $('#selectTemplateName').append(html)
            } else {
                errorNotification(result)
            }
        }
    })
}