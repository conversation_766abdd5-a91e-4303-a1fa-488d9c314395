using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class NodeWorkflowExecutionRepositoryTests : IClassFixture<NodeWorkflowExecutionFixture>
{
    private readonly NodeWorkflowExecutionFixture _nodeWorkflowExecutionFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly NodeWorkflowExecutionRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public NodeWorkflowExecutionRepositoryTests(NodeWorkflowExecutionFixture nodeWorkflowExecutionFixture)
    {
        _nodeWorkflowExecutionFixture = nodeWorkflowExecutionFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(NodeWorkflowExecutionFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(NodeWorkflowExecutionFixture.UserId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);
        
        _repository = new NodeWorkflowExecutionRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.NodeWorkflowExecutions.RemoveRange(_dbContext.NodeWorkflowExecutions);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }



    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnOnlyRunningExecutions()
    {
        // Arrange
        await ClearDatabase();
        var executions = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionsWithDifferentStatuses();
        
        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(executions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only "Running" status should be returned
        Assert.All(result, execution => Assert.Equal("running", execution.Status.Trim().ToLower()));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoRunningExecutions()
    {
        // Arrange
        await ClearDatabase();
        var executions = new List<NodeWorkflowExecution>
        {
            _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(status: "Completed"),
            _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(status: "Failed")
        };

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(executions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldHandleStatusWithWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var executions = new List<NodeWorkflowExecution>
        {
            _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(status: "  Running  "),
            _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(status: "RUNNING"),
            _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(status: "running")
        };

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(executions);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // All should match after trim and toLower
        Assert.All(result, execution => Assert.Equal("running", execution.Status.Trim().ToLower()));
    }

    #endregion

    #region GetNodeWorkflowExecutionByWorkflowOperationId Tests

    [Fact]
    public async Task GetNodeWorkflowExecutionByWorkflowOperationId_ShouldReturnActiveExecutions_WhenWorkflowOperationIdExists()
    {
        // Arrange
        await ClearDatabase();
        var workflowOperationId = "9a73e815-ab81-4d54-a468-72b4b9632866";
        var executions = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionsWithMixedActiveStatus(workflowOperationId);

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(executions);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByWorkflowOperationId(workflowOperationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active executions should be returned
        Assert.All(result, execution => Assert.True(execution.IsActive));
        Assert.All(result, execution => Assert.Equal(workflowOperationId, execution.WorkflowOperationId));
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByWorkflowOperationId_ShouldReturnEmpty_WhenWorkflowOperationIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByWorkflowOperationId("9a73e815-ab81-4d54-a468-72b4b9632866");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByWorkflowOperationId_ShouldHandleValidGuidFormats()
    {
        // Arrange
        await ClearDatabase();
        var validGuids = new[]
        {
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString("D"),
            Guid.NewGuid().ToString("N"),
            Guid.NewGuid().ToString("B"),
            Guid.NewGuid().ToString("P")
        };

        var executions = new List<NodeWorkflowExecution>();
        foreach (var guid in validGuids)
        {
            var execution = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(workflowOperationId: guid, isActive: true);
            executions.Add(execution);
        }

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(executions);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var guid in validGuids)
        {
            var result = await _repository.GetNodeWorkflowExecutionByWorkflowOperationId(guid);
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(guid, result[0].WorkflowOperationId);
        }
    }

    #endregion

    #region GetNodeWorkflowExecutionByInfraSchedulerId Tests

    [Fact]
    public async Task GetNodeWorkflowExecutionByInfraSchedulerId_ShouldReturnLatestActiveExecution_WhenInfraSchedulerIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraSchedulerId = "9a73e815-ab81-4d54-a468-72b4b9632866";

        var execution1 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-2));

        var execution2 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-1));

        var execution3 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: false,
            createdDate: DateTime.UtcNow); // Most recent but inactive

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(new[] { execution1, execution2, execution3 });
       _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByInfraSchedulerId(infraSchedulerId);

        // Assert
        Assert.NotNull(result);
      // Should return the latest active one
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByInfraSchedulerId_ShouldReturnNull_WhenInfraSchedulerIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByInfraSchedulerId("non-existent-infra-scheduler-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByInfraSchedulerId_ShouldReturnNull_WhenOnlyInactiveExecutionsExist()
    {
        // Arrange
        await ClearDatabase();
        var infraSchedulerId = "test-infra-scheduler-id";
        var inactiveExecution = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: false);

        await _dbContext.NodeWorkflowExecutions.AddAsync(inactiveExecution);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByInfraSchedulerId(infraSchedulerId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByInfraSchedulerId_ShouldOrderByIdDescending()
    {
        // Arrange
        await ClearDatabase();
        var infraSchedulerId = "9a73e815-ab81-4d54-a468-72b4b9632866"; 

        var execution1 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: true);

        var execution2 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            infraSchedulerId: infraSchedulerId,
            isActive: true);

        // Add execution1 first, then execution2 (execution2 will have higher ID)
        await _dbContext.NodeWorkflowExecutions.AddAsync(execution1);
        await _dbContext.SaveChangesAsync();
        await _dbContext.NodeWorkflowExecutions.AddAsync(execution2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByInfraSchedulerId(infraSchedulerId);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByInfraSchedulerId_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.GetNodeWorkflowExecutionByInfraSchedulerId(null);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetNodeWorkflowExecutionByJobId Tests

    [Fact]
    public async Task GetNodeWorkflowExecutionByJobId_ShouldReturnLatestActiveExecution_WhenJobIdExists()
    {
        // Arrange
        await ClearDatabase();
        var jobId = "test-job-id";

        var execution1 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-2));

        var execution2 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: true,
            createdDate: DateTime.UtcNow.AddHours(-1));

        var execution3 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: false,
            createdDate: DateTime.UtcNow); // Most recent but inactive

        await _dbContext.NodeWorkflowExecutions.AddRangeAsync(new[] { execution1, execution2, execution3 });
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByJobId(jobId);

        // Assert
        Assert.NotNull(result);
 // Should return the latest active one
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByJobId_ShouldReturnNull_WhenJobIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByJobId("non-existent-job-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByJobId_ShouldReturnNull_WhenOnlyInactiveExecutionsExist()
    {
        // Arrange
        await ClearDatabase();
        var jobId = "test-job-id";
        var inactiveExecution = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: false);

        await _dbContext.NodeWorkflowExecutions.AddAsync(inactiveExecution);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByJobId(jobId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByJobId_ShouldOrderByIdDescending()
    {
        // Arrange
        await ClearDatabase();
        var jobId = "test-job-id";

        var execution1 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: true);

        var execution2 = _nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionWithProperties(
            jobId: jobId,
            isActive: true);

        // Add execution1 first, then execution2 (execution2 will have higher ID)
        await _dbContext.NodeWorkflowExecutions.AddAsync(execution1);
        await _dbContext.SaveChangesAsync();
        await _dbContext.NodeWorkflowExecutions.AddAsync(execution2);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetNodeWorkflowExecutionByJobId(jobId);

        // Assert
        Assert.NotNull(result);
   // Should return the one with higher ID
    }

    [Fact]
    public async Task GetNodeWorkflowExecutionByJobId_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.GetNodeWorkflowExecutionByJobId(null);

        // Assert
        Assert.Null(result);
    }

    #endregion

}
