﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Events.PaginatedView;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class RSyncOptionsControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<RSyncOptionsController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private RSyncOptionsController _controller;

        public RSyncOptionsControllerShould()
        {
           
            _controller = new RSyncOptionsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.ControllerContext.HttpContext, "Test", "Test");

        }

        [Fact]
        public async Task List_PublishesEvent_And_ReturnsView()
        {
            // Arrange & Act
            var result = await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RsyncOptionPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            Assert.IsType<ViewResult>(result);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesWhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RsyncOptionViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", ""); // Empty ID for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateRsyncOptionCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateRsyncOptionCommand>(viewModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.RsyncOption.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockMapper.Verify(m => m.Map<CreateRsyncOptionCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.RsyncOption.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesWhenIdIsNotEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RsyncOptionViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "22"); // Non-empty ID for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateRsyncOptionCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };
            _mockMapper.Setup(m => m.Map<UpdateRsyncOptionCommand>(viewModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.RsyncOption.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockMapper.Verify(m => m.Map<UpdateRsyncOptionCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.RsyncOption.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_ReturnsJsonError()
        {
            // Arrange
            var viewModel = new RsyncOptionViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Property", "Validation error"));
            var validationException = new FluentValidation.ValidationException(validationResult.Errors);

            _mockMapper.Setup(m => m.Map<CreateRsyncOptionCommand>(viewModel))
                .Throws(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesContinuityPatrolValidationException_ReturnsJsonError()
        {
            // Arrange
            var viewModel = new RsyncOptionViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            // Create a FluentValidation ValidationResult with errors
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Name", "Name is required"));
            validationResult.Errors.Add(new ValidationFailure("Properties", "Properties cannot be empty"));

            // Create the ContinuityPatrol ValidationException (the one used in production code)
            var validationException = new ValidationException(validationResult);

            _mockDataProvider.Setup(dp => dp.RsyncOption.CreateAsync(It.IsAny<CreateRsyncOptionCommand>()))
                .ThrowsAsync(validationException);

            var createCommand = new CreateRsyncOptionCommand();
            _mockMapper.Setup(m => m.Map<CreateRsyncOptionCommand>(viewModel)).Returns(createCommand);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":\"Name is required\"", json);

            // Verify that the data provider was called
            _mockDataProvider.Verify(dp => dp.RsyncOption.CreateAsync(It.IsAny<CreateRsyncOptionCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesContinuityPatrolValidationExceptionOnUpdate_ReturnsJsonError()
        {
            // Arrange
            var viewModel = new RsyncOptionViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "123"); // Non-empty ID for update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            // Create a FluentValidation ValidationResult with errors
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Properties", "Invalid properties format"));

            // Create the ContinuityPatrol ValidationException
            var validationException = new ValidationException(validationResult);

            _mockDataProvider.Setup(dp => dp.RsyncOption.UpdateAsync(It.IsAny<UpdateRsyncOptionCommand>()))
                .ThrowsAsync(validationException);

            var updateCommand = new UpdateRsyncOptionCommand();
            _mockMapper.Setup(m => m.Map<UpdateRsyncOptionCommand>(viewModel)).Returns(updateCommand);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.Contains("\"Message\":\"Invalid properties format\"", json);

            // Verify that the data provider was called
            _mockDataProvider.Verify(dp => dp.RsyncOption.UpdateAsync(It.IsAny<UpdateRsyncOptionCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException_ReturnsJsonError()
        {
            // Arrange
            var viewModel = new RsyncOptionViewModel();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateRsyncOptionCommand>(viewModel))
                .Throws(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task Delete_DeletesSuccessfully()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.RsyncOption.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockDataProvider.Verify(dp => dp.RsyncOption.DeleteAsync(id), Times.Once);
        }

        

        [Fact]
        public async Task Delete_HandlesGeneralException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            
            var query = new GetRsyncOptionPaginatedListQuery();
            var expectedResult = new PaginatedResult<RsyncOptionListVm> ();
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetPaginatedRsyncOptions(query)).ReturnsAsync(expectedResult);

            
            var result = await _controller.GetPagination(query);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_HandlesException()
        {
            
            var query = new GetRsyncOptionPaginatedListQuery();
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetPaginatedRsyncOptions(query)).ThrowsAsync(exception);

            
            var result = await _controller.GetPagination(query);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetRsyncOptionsDataById_ReturnsJsonResult()
        {
            // Arrange
            var id = "1";
            var expectedData = new RsyncOptionDetailVm();
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetByReferenceId(id)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.GetRsyncOptionsDataById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }           

        [Fact]
        public async Task GetRsyncOptionsDataById_HandlesException()
        {
            
            var id = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetByReferenceId(id)).ThrowsAsync(exception);

            
            var result = await _controller.GetRsyncOptionsDataById(id);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsRsyncNameExist_ReturnsCorrectResult()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.RsyncOption.IsRsyncOptionNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsRsyncNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsRsyncNameExist_HandlesException_ReturnsFalse()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.RsyncOption.IsRsyncOptionNameExist(name, id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsRsyncNameExist(name, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetRsyncJobByReplicationId_ReturnsJsonResult()
        {
            // Arrange
            var replicationId = "1";
            var expectedJobs = new List<RsyncJobListVm>
            {
                new RsyncJobListVm { Id = "1", ReplicationId = replicationId },
                new RsyncJobListVm { Id = "2", ReplicationId = "2" }
            };
            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs()).ReturnsAsync(expectedJobs);

            // Act
            var result = await _controller.GetRsyncJobByReplicationId(replicationId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"success\":true", json);
            _mockDataProvider.Verify(dp => dp.RsyncJob.GetRsyncJobs(), Times.Once);
        }

        [Fact]
        public async Task GetRsyncJobByReplicationId_HandlesException()
        {
            // Arrange
            var replicationId = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetRsyncJobByReplicationId(replicationId);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(jsonResult.Value);
            Assert.Contains("\"Success\":false", json);
        }
    }
}
