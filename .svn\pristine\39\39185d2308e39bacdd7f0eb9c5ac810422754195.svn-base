﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ComponentTypeRepository : BaseRepository<ComponentType>, IComponentTypeRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public ComponentTypeRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService) : base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<string>> IsFormNamesUnique(List<string> name)
    {
        var matches = await _dbContext.ComponentTypes
            .AsNoTracking()
            .Where(e => name.Contains(e.ComponentName))
            .Select(e => e.ComponentName)
            .ToListAsync();

        return matches;
    }

    public async Task<List<ComponentType>> GetComponentTypeByIds(List<string> ids)
    {
        return await _dbContext.ComponentTypes
            .AsNoTracking()
            .Where(e => ids.Contains(e.ReferenceId))
            .ToListAsync();
    }

    public async  Task<ComponentType> GetComponentTypeById(string id)
    {
        var componentList = base.ListAllAsync(x=>x.IsActive);

        var mappedComponent=MapComponentType(componentList);

        return await mappedComponent .Where(s => s.ReferenceId.Equals(id))
            .FirstOrDefaultAsync();
    }
    public override async  Task<IReadOnlyList<ComponentType>> ListAllAsync()
    {
        //var componentList = base.ListAllAsync(x=>x.IsActive);

        //var result=  MapComponentType(componentList);
        //return  await result.ToListAsync();    
        var componentList = await FilterRequiredFields(MapComponentType(base.ListAllAsync(x => x.IsActive))).ToListAsync();
  
        return componentList;
    }

    public Task<bool> IsComponentTypeNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ComponentName.Equals(name))
            : Entities.Where(e => e.ComponentName.Equals(name)).ToList().Unique(id));
    }

    public async Task<List<ComponentType>> GetComponentTypeListByName(string name)
    {
        var componentList =await  FilterRequiredFields((base.ListAllAsync(x => x.IsActive))
            .Where(x => x.FormTypeName.Trim().ToLower().Equals(name.Trim().ToLower())).Select(x=> new ComponentType
            {
                ReferenceId = x.ReferenceId,
                ComponentName = x.ComponentName,
                Properties = x.Properties
            })).ToListAsync();

        return componentList;
    }

    public override async Task<PaginatedResult<ComponentType>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ComponentType> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await FilterRequiredFields(MapComponentType(Entities.Specify(productFilterSpec).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<ComponentType> PaginatedListAllAsync()
    { 
        var componentList = base.ListAllAsync(x=>x.IsActive);

        var result= MapComponentType(componentList);

        return result;
    }
    public IQueryable<ComponentType> MapComponentType(IQueryable<ComponentType> componentTypes)
    {
        var mappedData = componentTypes.Select(rep => new
        {
            FormType = _dbContext.FormTypes.AsNoTracking()
        .Active()
        .FirstOrDefault(x => !string.IsNullOrWhiteSpace(rep.FormTypeId) && x.ReferenceId.Equals(rep.FormTypeId)),
            componentTypes = rep
        });

        var result = mappedData
            .Select(x => new ComponentType
            {
                Id = x.componentTypes.Id,
                ReferenceId = x.componentTypes.ReferenceId,
                FormTypeId = string.IsNullOrWhiteSpace(x!.FormType!.ReferenceId)
                    ? x!.componentTypes!.FormTypeId
                    : x!.FormType!.ReferenceId,
                FormTypeName = string.IsNullOrWhiteSpace(x!.FormType!.FormTypeName)
                    ? x!.componentTypes!.FormTypeName
                    : x!.FormType!.FormTypeName,
                ComponentName = x.componentTypes.ComponentName,
                Properties = x.componentTypes.Properties,
                Logo = x.componentTypes.Logo,
                IsDatabase = x.componentTypes.IsDatabase,
                IsCustom = x.componentTypes.IsCustom,
                IsReplication = x.componentTypes.IsReplication,
                IsServer = x.componentTypes.IsServer,
                Version = x.componentTypes.Version,
                IsActive = x.componentTypes.IsActive,
                CreatedBy = x.componentTypes.CreatedBy,
                CreatedDate = x.componentTypes.CreatedDate,
                LastModifiedBy = x.componentTypes.LastModifiedBy,
                LastModifiedDate = x.componentTypes.LastModifiedDate
            });
        return result;
    
    }

    private IQueryable<ComponentType>FilterRequiredFields(IQueryable<ComponentType>  componentTypes)
    {
        return componentTypes.Select(x => new ComponentType
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            FormTypeId = x.FormTypeId,
            FormTypeName = x.FormTypeName,
            ComponentName = x.ComponentName,
            Properties = x.Properties,
            Logo = x.Logo,
            IsDatabase = x.IsDatabase,
            IsCustom = x.IsCustom,
            IsReplication = x.IsReplication,
            IsServer = x.IsServer,
            Version = x.Version
        });
    }
}