﻿
var form = $(".example-form");
var wizardLength = 3;

$(".finish_btn").hide();
$('.prev_btn').hide();
$('.clear_btn').hide();


$(function () {

    var settings = {

        headerTag: "h6",
        bodyTag: "section",
        autoFocus: true,
        transitionEffect: "slideLeft",
        titleTemplate: '<span class="step"><p class="img_#index#"></p></span> <span class="step_title">#title#</span>',

        onStepChanged: function (event, currentIndex, newIndex) {

            wizardLength = $(event.currentTarget).find("h6").not(".Header").length; //(in formBuilder we are using header tag)          

            switch (wizardLength) {

                case 0:
                    // code block
                    $('.prev_btn').hide();
                    $('.next_btn').hide()
                    $('.clear_btn').show();
                    $('.finish_btn').show()
                    break;

                case 1:
                    // code block
                    $('.prev_btn').hide();
                    $('.next_btn').hide()
                    $('.clear_btn').show();
                    $('.finish_btn').show()

                    break;

                case 2:
                    // code block
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();

                    }
                    break;

                case 3:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }
                    break;

                case 4:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {


                        $("#chkToggleDiv").show()
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $("#chkToggleDiv").hide()
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    break;

                case 5:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    break;
                case 6:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    break;
                case 7:
                case 8:
                case 9:
                case 10:
                case 11:
                case 12:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.clear_btn').hide();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.clear_btn').show();
                        $('.finish_btn').show();
                    }

                    break;
                default:
                // code block
            }

        },
    }
    form.steps(settings);


});


$(function () {

    var setting = {
        headerTag: "h6",
        bodyTag: "section",
        transitionEffect: "slideLeft",
        titleTemplate: '<span class="step"><p class="img_#index#"></p></span> <span class="step_title">#title#</span>',
        onStepChanged: function (event, currentIndex, newIndex) {
            console.log(currentIndex)
            wizardLength = $(event.currentTarget).find("h6").not(".Header").length; //(in formBuilder we are using header tag)

            switch (wizardLength) {
                case 0:
                    // code block
                    $('.prev_btn').hide();
                    $('.next_btn').hide()
                    $('.finish_btn').show()
                    break;

                case 1:
                    // code block
                    $('.prev_btn').hide();
                    $('.next_btn').hide()
                    $('.finish_btn').show()

                    break;

                case 2:
                    // code block
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.finish_btn').show();

                    }
                    break;

                case 3:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.finish_btn').show();
                    }
                    break;

                case 4:
                    if (currentIndex == 0) {



                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {


                        $("#chkToggleDiv").show()
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $("#chkToggleDiv").hide()
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.finish_btn').show();
                    }

                    break;

                case 5:
                    if (currentIndex == 0) {
                        $('.prev_btn').hide();
                        $('.next_btn').show();
                        $('.finish_btn').hide()

                    } else if (currentIndex === 1) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 2) {
                        $('.prev_btn').show();
                        $('.next_btn').show();
                        $('.finish_btn').hide();
                    }

                    else if (currentIndex === 3) {
                        $('.prev_btn').show();
                        $('.next_btn').hide();
                        $('.finish_btn').show();
                    }

                    break;

                default:
                // code block
            }

        },
    }
    $("#example-basic").steps(setting);


});



















//$(function () {

//    $("#example-basic").steps({
//        headerTag: "h6",
//        bodyTag: "section",
//        cssClass: "wizard",
//        transitionEffect: "slideLeft",
//        titleTemplate: '<span class="step"><p class="img_#index#"></p></span> <span class="step_title">#title#</span>',
//        autoFocus: true,
//        onStepChanging: function (e, currentIndex, newIndex) {
//            console.log(currentIndex) // returns many many 0s but cannot go to 1.
//            wizardLength = $(event.currentTarget).find("h6").length;

//            $("#example-basic").steps("next");
//        }
//    });

//})


