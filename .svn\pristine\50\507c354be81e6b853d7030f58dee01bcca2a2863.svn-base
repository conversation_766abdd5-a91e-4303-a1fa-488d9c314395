﻿namespace ContinuityPatrol.Application.Features.DataSetColumns.Commands.Create;

public class CreateDataSetColumnsCommandValidator : AbstractValidator<CreateDataSetColumnsCommand>
{
    private readonly IDataSetColumnsRepository _dataSetColumnsRepository;

    public CreateDataSetColumnsCommandValidator(IDataSetColumnsRepository dataSetColumnsRepository)
    {
        _dataSetColumnsRepository = dataSetColumnsRepository;


        RuleFor(p => p.TableName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");
    }
}