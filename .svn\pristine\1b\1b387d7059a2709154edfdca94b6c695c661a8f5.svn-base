const requestURL = {
    getRequestPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedRequestList",
    getApprovalPaginatedlist: "/Manage/ApprovalMatrix/GetPaginatedApprovalList",
}

let requestDataTable = "";
let approvalDataTable = "";
let selectedValues = [];

const approvalPreventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

const approvers = (approvers) => {
    const allNames = JSON.parse(approvers)
        .flatMap(item => JSON.parse(item.Approvers))
        .map(approver => approver.name);
    const nameString = allNames.join(", ");
    return nameString;
}

async function myRequestLiss() {
    requestDataTable = $('#tblMyRequest').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": requestURL.getRequestPaginatedlist,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#search-in-type').val();

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.OSTypeId = selectedType;
                    selectedValues.length = 0;
                    reportSearchStr = d?.searchString;
                },
                "dataSrc": function (json) {

                    if (json?.success) {
                        const { data } = json;
                        json.recordsTotal = data?.totalPages;
                        json.recordsFiltered = data?.totalCount;
                        const isEmpty = data?.data?.length === 0;
                        $(".pagination-column").toggleClass("disabled", isEmpty);
                        return data?.data;
                    }
                    else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 3],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                    "orderable": false
                },
                {
                    "data": "processName", "name": "processName", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "status", "name": "status", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "approvers", "name": "approvers", "autoWidth": true,
                    "render": function (data, type, row) {
                        const result = approvers(data);
                        return type === 'display' ? `<span title='${result || "NA"}'>  ${result || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "userName", "name": "userName", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        const id = row?.id;
                        const requestId = row?.requestId;
                        return `<div class="d-flex align-items-center gap-2">
                                             <span role="button" title="View" class="viewButton">
                                                  <i class="cp-password-visible text-primary fw-semibold me-2"></i> View                                                  
                                             </span>
                                             <span role="button" title="Withdraw" class="withdrawButton" data-withdraw-id="${id}" data-withdraw-request="${requestId}" data-withdraw-status="Withdraw">
                                                  <i class="cp-undo text-warning fw-semibold me-2"></i> Withdraw                                                 
                                             </span>
                                       </div>`;
                    },
                    "orderable": false
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    );

}

async function myApprovalLists() {
    approvalDataTable = $('#tblMyApproval').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": requestURL.getApprovalPaginatedlist,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    let selectedType = $('#search-in-type').val();

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.OSTypeId = selectedType;
                    selectedValues.length = 0;
                    reportSearchStr = d?.searchString;
                },
                "dataSrc": function (result) {

                    if (result?.success) {
                        const { data } = result;
                        result.recordsTotal = data?.totalPages;
                        result.recordsFiltered = data?.totalCount;
                        const isEmpty = data?.data?.length === 0;
                        $(".pagination-column").toggleClass("disabled", isEmpty);
                        return data?.data.reverse();
                    }
                    else {
                        errorNotification(result)
                    }
                }
            },
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        return type === 'display' ? meta.row + 1 : data;
                    },
                    "orderable": false
                },
                {
                    "data": "processName", "name": "processName", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "description", "name": "description", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "status", "name": "status", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "approver", "name": "approver", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "endDateTime", "name": "endDateTime", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "name", "name": "CreatedBy", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "data": "name", "name": "CreatedOn", "autoWidth": true,
                    "render": function (data, type, row) {
                        return type === 'display' ? `<span title='${data || "NA"}'>  ${data || "NA"} </span>` : data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        const approvalId = row?.id;
                        const approvalProcName = row?.processName;

                        return `<div class="d-flex align-items-center gap-2">                                     
                                     <span role="button" title="Approve" class="approvalButton" data-approval-id="${approvalId}"  data-approval-procname="${approvalProcName}" data-approval-status="Approved">
                                          <i class="cp-success text-success fw-semibold me-2"></i> <span class="">Approve</span>                                        
                                     </span>
                                     <span role="button" title="Reject" class="approvalButton" data-approval-id="${approvalId}"  data-approval-procname="${approvalProcName}" data-approval-status="Rejected">
                                          <i class="cp-error text-warning fw-semibold me-2"></i> <span class="">Reject</span>                                          
                                     </span>
                                     <span role="button" title="View" class="viewButton">
                                          <i class="cp-password-visible text-primary fw-semibold me-2"></i> <span class="">View</span>                                         
                                     </span>
                               </div>`;
                    },
                    "orderable": false
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

}

$(async function () {
    approvalPreventSpecialKeys('#search-inp');   
    myRequestLiss();

    $("#pills-myrequest-tab").on("click", function () {
        myRequestLiss();
    });

    $("#pills-myapproval-tab").on("click", function () {
        myApprovalLists();
    });
});

$("#tblMyRequest").on("click", ".withdrawButton", async function () {
    const id = $(this).data("withdraw-id");
    const requestId = $(this).data("withdraw-request");
    const status = $(this).data("withdraw-status");

    let command = {
        Id: id,
        RequestId: requestId,
        Status: status,
    };

    await $.ajax({
        url: RootUrl + "Manage/ApprovalMatrix/WithdrawRequest",
        data: command,
        type: "GET",
        dataType: "Json",
        success: function (response) {
            if (response?.success) {
                requestDataTable.ajax.reload();

            } else {
                requestDataTable.ajax.reload();
                errorNotification(response);
            }
        }
    });
})

$("#tblMyApproval").on("click", ".approvalButton", async function () {
    const id = $(this).data("approval-id");
    const processName = $(this).data("approval-procname");
    const status = $(this).data("approval-status");

    let command = {
        Id: id,
        ProcessName: processName,
        Status: status,
    };

    await $.ajax({
        url: RootUrl + "Manage/ApprovalMatrix/ApproveReject",
        data: command,
        type: "GET",
        dataType: "Json",
        success: function (response) {
            if (response?.success) {
                approvalDataTable.ajax.reload();

            } else {
                approvalDataTable.ajax.reload();
                errorNotification(response);
            }
        }
    });
})
