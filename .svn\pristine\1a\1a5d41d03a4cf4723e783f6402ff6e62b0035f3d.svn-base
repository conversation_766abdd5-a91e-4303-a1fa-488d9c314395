﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class TeamMasterController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<TeamMasterController> _logger;


    public TeamMasterController(IPublisher publisher, IMapper mapper, IDataProvider dataProvider, ILogger<TeamMasterController> logger)
    {
        _publisher = publisher;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _logger = logger;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in TeamMaster");

        await _publisher.Publish(new TeamMasterPaginatedEvent());
        var list = await _dataProvider.TeamMasterService.GetTeamConfigurationList(new GetTeamMasterPaginatedListQuery());
        var items = await _dataProvider.TeamMasterService.GetAllTeamNames();

        var teamResourceListVms = await _dataProvider.TeamResourceService.GetTeamMemberNames();


        var teamSelectionListVm = 
            (from item in items let teamMemberNameByTeam = teamResourceListVms.Where(x => x.TeamMasterName == item.GroupName)
                .ToList() select new TeamMasterListVm { Id = item.Id, GroupName = item.GroupName, Description = item.Description, MemberCount = teamMemberNameByTeam.Count > 0 ? teamMemberNameByTeam.Count().ToString() : "0" }).ToList();

        var teamMasterModel = new TeamMasterViewModel
        {
            PaginatedTeamConfigurations = list,
            TeamConfigurationsList = teamSelectionListVm

        };

        return View(teamMasterModel);
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public List<TeamMasterListVm> UpdateTeamList(List<TeamMasterListVm> teamSelectionList)
    {
        _logger.LogDebug("Entering UpdateTeamList method in TeamMaster");

        var teamSelectionListVm = new List<TeamMasterListVm>();

        foreach (var item in teamSelectionList)
        {
            var userSelectionList = new TeamMasterListVm
            {
                GroupName = item.GroupName,
                Description = item.Description
            };
            teamSelectionListVm.Add(userSelectionList);
        }
        return teamSelectionListVm;
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(TeamMasterViewModel teamGroup)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in TeamMaster");

        try
        {
            var teamId = Request.Form["Id"].ToString();
            if (teamId.IsNullOrWhiteSpace())
            {
                _logger.LogDebug($"Creating TeamMaster '{teamGroup.GroupName}'");
                var teamMasterCommand = _mapper.Map<CreateTeamMasterCommand>(teamGroup);
                var response = await _dataProvider.TeamMasterService.CreateAsync(teamMasterCommand);

               TempData.NotifySuccess(response.Message);
            }
            else
            {
                _logger.LogDebug($"Updating TeamMaster '{teamGroup.GroupName}'");
                var teamMasterCommand = _mapper.Map<UpdateTeamMasterCommand>(teamGroup);
                var response = await _dataProvider.TeamMasterService.UpdateAsync(teamMasterCommand);
                TempData.NotifySuccess(response.Message);
            }

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on TeamMaster page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on TeamMaster option page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());
            return RedirectToAction("List");
        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in TeamMaster");

        try
        {
           var response =  await _dataProvider.TeamMasterService.DeleteAsync(id);

           TempData.NotifySuccess(response.Message);

           _logger.LogDebug("Successfully deleted record in TeamMaster");

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on TeamMaster.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }


    [HttpGet]
    public async Task<bool> IsTeamNameAlreadyExist(string name, string id)

    {
        _logger.LogDebug("Entering IsTeamNameExist method in TeamMaster");

        try
        {
            var nameExist = await _dataProvider.TeamMasterService.IsTeamNameAlreadyExist(name, id);

            _logger.LogDebug("Returning result for IsTeamNameExist on TeamMaster");

            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on TeamMaster page while checking if TeamMaster name exists for : {name}.", ex);
            return false;
        }

       
    }
    [HttpGet]
    public async Task<JsonResult> GetPagination(GetTeamMasterPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in TeamMaster");

        try
        {
            var result = await _dataProvider.TeamMasterService.GetTeamConfigurationList(query);

            _logger.LogDebug("Successfully retrieved TeamMaster paginated list on TeamMaster page");

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on TeamMaster page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

}