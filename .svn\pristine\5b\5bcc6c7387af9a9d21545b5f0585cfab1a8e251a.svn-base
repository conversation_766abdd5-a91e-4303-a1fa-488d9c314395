﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries;

public class GetWorkflowProfileInfoDetailQueryHandlerTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;

    private readonly GetWorkflowProfileInfoDetailQueryHandler _handler;

    public GetWorkflowProfileInfoDetailQueryHandlerTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        _mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.GetWorkflowProfileInfoRepository(_workflowProfileInfoFixture.WorkflowProfileInfos);

        _handler = new GetWorkflowProfileInfoDetailQueryHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowProfileInfoRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_WorkflowProfileInfo_Details_When_ValidId()
    {
        var result = await _handler.Handle(new GetWorkflowProfileInfoDetailQuery { Id = _workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<WorkflowProfileInfoDetailVm>();

        result.Id.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId);
        result.ProfileId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileId);
        result.ProfileName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProfileName);
        result.InfraObjectId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].InfraObjectName);
        result.BusinessFunctionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionId);
        result.BusinessFunctionName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessFunctionName);
        result.BusinessServiceId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceId);
        result.BusinessServiceName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].BusinessServiceName);
        result.WorkflowId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowId);
        result.WorkflowName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowName);
        result.CurrentActionId.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionId);
        result.CurrentActionName.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].CurrentActionName);
        result.Message.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Message);
        result.ConditionalOperation.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ConditionalOperation);
        result.WorkflowType.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].WorkflowType);
        result.ActionMode.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ActionMode);
        result.Status.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].Status);
        result.ProgressStatus.ShouldBe(_workflowProfileInfoFixture.WorkflowProfileInfos[0].ProgressStatus);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowProfileInfoId()
    {
        var handler = new GetWorkflowProfileInfoDetailQueryHandler(_workflowProfileInfoFixture.Mapper, _mockWorkflowProfileInfoRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowProfileInfoDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowProfileInfoById_OneTime()
    {
        await _handler.Handle(new GetWorkflowProfileInfoDetailQuery { Id = _workflowProfileInfoFixture.WorkflowProfileInfos[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowProfileInfoRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once());
    }
}