namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetNameUnique;

public class
    GetApprovalMatrixApprovalNameUniqueQueryHandler : IRequestHandler<GetApprovalMatrixApprovalNameUniqueQuery, bool>
{
    private readonly IApprovalMatrixApprovalRepository _approvalMatrixApprovalRepository;

    public GetApprovalMatrixApprovalNameUniqueQueryHandler(
        IApprovalMatrixApprovalRepository approvalMatrixApprovalRepository)
    {
        _approvalMatrixApprovalRepository = approvalMatrixApprovalRepository;
    }

    public async Task<bool> Handle(GetApprovalMatrixApprovalNameUniqueQuery request,
        CancellationToken cancellationToken)
    {
        return await _approvalMatrixApprovalRepository.IsNameExist(request.Name, request.Id);
    }
}