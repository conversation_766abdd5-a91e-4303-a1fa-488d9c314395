﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Persistence.Repositories;

public class DRReadyStatusRepository : BaseRepository<DRReadyStatus>, IDrReadyStatusRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public DRReadyStatusRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DRReadyStatus>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService => businessService.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices);
    }

    public override Task<DRReadyStatus> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(drReady => drReady.ReferenceId.Equals(id)).Result.SingleOrDefault());
    }

    public async Task<List<DRReadyStatus>> GetDrReadyStatusByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var drReady =
            base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId));
        
        return _loggedInUserService.IsAllInfra
            ? await drReady.ToListAsync()
            : AssignedBusinessServices(drReady).ToList();
    }

    public async Task<DRReadyStatus> GetDrReadyStatusByInfraObjectId(string infraObjectId)
    {
        var infraObject =
            GetByInfraObjectIdAsync(infraObjectId, infraObject => infraObject.InfraObjectId.Equals(infraObjectId));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.FirstOrDefaultAsync()
            : GetByInfraObjectId(infraObject.FirstOrDefault());
    }

    public async Task<List<DRReadyStatus>> GetDrReadyStatusListByBusinessFunctionId(string businessFunctionId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessFunctionId, "BusinessFunctionId",
            "BusinessFunctionId cannot be invalid");

        var businessFunctions = base.QueryAll(businessService => businessService.IsActive);

        return _loggedInUserService.IsAllInfra
            ? await businessFunctions.Where(x => x.BusinessFunctionId.Equals(businessFunctionId)).ToListAsync()
            : AssignedBusinessFunctions(businessFunctions).Where(x => x.BusinessFunctionId.Equals(businessFunctionId))
                .ToList();
    }

    public async Task<List<DRReadyStatus>> GetDrReadyStatusByWorkflowId(string workflowId)
    {
        return await _dbContext.DrReadyStatuses
            .Active()
            .Where(x => x.WorkflowId.Equals(workflowId))
            .ToListAsync();
    }


    //Filters
    public IReadOnlyList<DRReadyStatus> AssignedBusinessServices(IQueryable<DRReadyStatus> businessServices)
    {
        var services = new List<DRReadyStatus>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                    where businessService.BusinessServiceId == assignedBusinessService.Id
                    select businessService);
        return services;
    }

    public IReadOnlyList<DRReadyStatus> AssignedBusinessFunctions(IQueryable<DRReadyStatus> businessFunctions)
    {
        var functions = new List<DRReadyStatus>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        foreach (var businessFunction in businessFunctions)
            if (assignedBusinessFunctions.Count > 0)
                functions.AddRange(from assignedBusinessFunction in assignedBusinessFunctions
                    where businessFunction.BusinessFunctionId == assignedBusinessFunction.Id
                    select businessFunction);
        return functions;
    }

    public IQueryable<DRReadyStatus> GetByInfraObjectIdAsync(string id,
        Expression<Func<DRReadyStatus, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.InfraObjectId.Equals(id))
            : FilterBy(expression);
    }

    public DRReadyStatus GetByInfraObjectId(DRReadyStatus infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }
}