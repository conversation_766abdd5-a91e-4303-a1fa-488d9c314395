﻿using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyLog.Commands;

public class CreateDrReadyLogCommandHandlerTests : IClassFixture<DrReadyLogFixture>
{
    private readonly DrReadyLogFixture _drReadyLogFixture;

    private readonly Mock<IDrReadyLogRepository> _drReadyLogRepositoryMock;

    private readonly CreateDRReadyLogCommandHandler _handler;

    public CreateDrReadyLogCommandHandlerTests(DrReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
    
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.CreateDrReadyLogRepository(_drReadyLogFixture.DrReadyLogs);
        
        _handler = new CreateDRReadyLogCommandHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_IncreaseDrReadyLogCount_When_DrReadyLogCreated()
    {
        await _handler.Handle(_drReadyLogFixture.CreateDrReadyLogCommand, CancellationToken.None);

        var result = await _drReadyLogRepositoryMock.Object.ListAllAsync();

        result.Count.ShouldBe(_drReadyLogFixture.DrReadyLogs.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateDrReadyLogResponse_When_DrReadyLogCreated()
    {
        var result = await _handler.Handle(_drReadyLogFixture.CreateDrReadyLogCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateDRReadyLogResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_drReadyLogFixture.CreateDrReadyLogCommand, CancellationToken.None);

        _drReadyLogRepositoryMock.Verify(x=>x.AddAsync(It.IsAny<Domain.Entities.DRReadyLog>()), Times.Once);
    }
}