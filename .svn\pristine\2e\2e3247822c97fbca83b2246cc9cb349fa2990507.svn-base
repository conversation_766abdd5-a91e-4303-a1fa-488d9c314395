﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class WorkflowActionTypeFilterSpecification : Specification<WorkflowActionType>
{
    public WorkflowActionTypeFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ActionType != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');
                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("actionType=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ActionType.Contains(stringItem.Replace("actionType=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ActionType.Contains(searchString);
            }
        }
    }
}
