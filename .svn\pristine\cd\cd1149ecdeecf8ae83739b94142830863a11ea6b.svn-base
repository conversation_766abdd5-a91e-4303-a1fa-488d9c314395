﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByCompanyId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByPONumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPoNumber;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class LicenseManagerService : BaseClient,ILicenseManagerService
{
    public LicenseManagerService(IConfiguration config, IAppCache cache, ILogger<LicenseManagerService> logger) : base(config, cache, logger)
    {
       
    }

    public async Task<List<LicenseManagerDetailViewVm>> LicenseManagerDetailView()
    {
        var request = new RestRequest("api/v6/licensemanager/detailview");

        return await Get<List<LicenseManagerDetailViewVm>>(request);
    }

    public async Task<List<LicenseManagerListVm>> GetLicenseManagerList()
    {
        var request = new RestRequest("api/v6/licensemanager");

        return await Get<List<LicenseManagerListVm>>(request);
    }

    public async Task<BaseResponse> CreateBaseLicense(CreateBaseLicenseCommand createBaseLicenseCommand)
    {
        var request = new RestRequest("api/v6/licensemanager/baselicense", Method.Post);

        request.AddJsonBody(createBaseLicenseCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateBaseLicense(UpdateBaseLicenseCommand updateBaseLicenseCommand)
    {
        var request = new RestRequest("api/v6/licensemanager/baselicense", Method.Put);

        request.AddJsonBody(updateBaseLicenseCommand);

        return await Put<BaseResponse>(request);
    }
  
    public async Task<BaseResponse> DeleteDecommissionByEntityId(string entityId,string licenseId,string entityType, string entityName)
    {
        var request = new RestRequest($"api/v6/licensemanager/decommission?entityId={entityId}&licenseId={licenseId}&entityType={entityType}&entityName{entityName}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }


    public async Task<BaseResponse> CreateDerivedLicense(CreateDerivedLicenseCommand createDerivedLicenseCommand)
    {
        var request = new RestRequest("api/v6/licensemanager/derivedlicense", Method.Post);

        request.AddJsonBody(createDerivedLicenseCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateDerivedLicense(UpdateDerivedLicenseCommand updateDerivedLicenseCommand)
    {
        var request = new RestRequest("api/v6/licensemanager/derivedlicense", Method.Put);

        request.AddJsonBody(updateDerivedLicenseCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/licensemanager/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<LicenseManagerDetailVm> GetLicenseManagerById(string id)
    {
        var request = new RestRequest($"api/v6/licensemanager/{id}");

        return await Get<LicenseManagerDetailVm>(request);
    }

    public async Task<BaseResponse> DeleteDerivedLicense(string id)
    {
        var request = new RestRequest($"api/v6/licensemanager/derivedlicense/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<LicenseManagerNameVm>> GetAllPoNumbers()
    {
        var request = new RestRequest("api/v6/licensemanager/ponumber");

        return await GetFromCache<List<LicenseManagerNameVm>>(request, "GetAllPoNumbers");
    }

    public async Task<List<ChildLicenseDetailByParentIdVm>> GetLicenseByParentIdAndParentPoNumber(string parentId, string parentPoNumber)
    {
        var request = new RestRequest($"api/v6/licensemanager/by/{parentId}?parentPO={parentPoNumber}");

        return await Get<List<ChildLicenseDetailByParentIdVm>>(request);
    }

    public async Task<LicenseManagerByPoNumberVm> GetLicenseManagerByPoNumber(string poNumber)
    {
        var request = new RestRequest($"api/v6/licensemanager/by/ponumber?poNumber={poNumber}");

        return await Get<LicenseManagerByPoNumberVm>(request);
    }
    public async Task<GetLicenseByPONumberVm> GetLicenseDetailsByPoNumber(string poNumber)
    {
        var request = new RestRequest($"api/v6/licensemanager/by/parentponumber?poNumber={poNumber}");

        return await Get<GetLicenseByPONumberVm>(request);
    }

    public async Task<object> GetDecommissionByIdAndEntityId(string licenseId,string entityId,string entityType)
    {
        var request = new RestRequest($"api/v6/licensemanager/decommission?entityId={entityId}&licenseId={licenseId}&entityType={entityType}");

        return await Get<object>(request);
    }

    public async Task<List<LicenseInfoByEntityListVm>> GetLicenseInfoByEntity(string licenseId, string entity)
    {
        var request = new RestRequest($"api/v6/licenseinfo/entity?licenseId={licenseId}&entity={entity}");

        return await Get<List<LicenseInfoByEntityListVm>>(request);
    }
    public async Task<LicenseCountVm> GetLicenseManagerCount()
    {
        var request = new RestRequest("api/v6/licensemanager/licensecount");

        return await Get<LicenseCountVm>(request);
    }
    public async Task<BaseResponse> ReplaceLicenseDetails(LicenseReplaceCommand command)
    {
        var request = new RestRequest("api/v6/licensemanager/replace", Method.Put);

        request.AddJsonBody(command);

        return await Put<BaseResponse>(request);
    }
    public Task<List<GetLicenseByCompanyIdVm>> GetLicenseByCompanyId(string companyId)
    {
        var request = new RestRequest($"api/v6/licensemanager/by/companyid?companyId={companyId}");

        return Get<List<GetLicenseByCompanyIdVm>>(request);
    }

    public Task<List<LicenseExpireListVm>> GetLicenseExpiresByCompanyId(string companyId)
    {
        var request = new RestRequest($"api/v6/licensemanager/license-expires?companyId={companyId}");

       return Get<List<LicenseExpireListVm>>(request);
    }
    public Task<List<LicenseExpireListVm>> GetLicenseAMCExpiresByCompanyId(string companyId)
    {
        var request = new RestRequest($"api/v6/licensemanager/licenseAmc-expires?companyId={companyId}");

       return Get<List<LicenseExpireListVm>>(request);
    }

    public async Task<BaseResponse> UpdateLicenseState(UpdateLicenseStateCommand updateLicenseStateCommand)
    {
        var request = new RestRequest($"api/v6/licensemanager/licensestate", Method.Put);
        
        request.AddJsonBody(updateLicenseStateCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<GetPoNumberListVm>> GetPoNumber(string type, string? roleType, string? siteId, string? serverId, string? replicationType, string? databaseTypeId)
    {
        var request = new RestRequest($"/api/v6/licensemanager/po-number?type={type}&roleType={roleType}&siteId={siteId}&serverId={serverId}&replicationType={replicationType}&databaseTypeId={databaseTypeId}");

        return await Get<List<GetPoNumberListVm>>(request);
    }
}