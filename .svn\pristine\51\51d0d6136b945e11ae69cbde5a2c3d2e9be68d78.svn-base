﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetPaginatedList;

public class GetWorkflowHistoryPaginatedListQueryHandler : IRequestHandler<GetWorkflowHistoryPaginatedListQuery,
    PaginatedResult<WorkflowHistoryListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowHistoryRepository _workflowHistoryRepository;

    public GetWorkflowHistoryPaginatedListQueryHandler(IMapper mapper,
        IWorkflowHistoryRepository workflowHistoryRepository)
    {
        _mapper = mapper;
        _workflowHistoryRepository = workflowHistoryRepository;
    }

    public async Task<PaginatedResult<WorkflowHistoryListVm>> Handle(GetWorkflowHistoryPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _workflowHistoryRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowHistoryFilterSpecification(request.SearchString);

        var workflowHistoryList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowHistoryListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowHistoryList;
    }
}