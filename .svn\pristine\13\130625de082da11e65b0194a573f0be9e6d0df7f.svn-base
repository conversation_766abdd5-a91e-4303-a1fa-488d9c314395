﻿var createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
var deletePermission = $("#AdminDelete").data("delete-permission").toLowerCase();
let activeGlobalId = "";
if (createPermission == 'false') {
    $(".btn-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target');
}
$(function () {
    $(".toggle-password").on('click',function () {
        let input = $(this).prev();
        let icon = $(this).find("i");
        if (input.attr("type") === "password") {
            input.attr("type", "text");
            icon.removeClass("cp-password-visible").addClass("cp-password-hide");
        } else {
            input.attr("type", "password");
            icon.removeClass("cp-password-hide").addClass("cp-password-visible");
        }
    });
    $('.delete-button').on('click', function () {
        let licenseId = $(this).data('license-id');
        let poNumber = $(this).data('license-name');
        $('#deleteData').text(poNumber);
        $('#textDeleteId').val(licenseId);
    });
    $('#licensekey').on('keyup', async function () {
        await validateLicenseKey($(this).val(), $('#licensekey').val());
    });
    $("#licensekey").on('keyup', function () {
        let licenseId = $('#licensekey').val();
        if (licenseId?.length > 30) {
            $("#SaveFunction").css({
                "cursor": "",
                "pointer-events": "",
                "opacity": ""
            });
        } else {
            $("#SaveFunction").css({
                "cursor": "none",
                "pointer-events": "none",
                "opacity": "0.5"
            });
        }
    });
    $('.edit-button').on('click', function () {
        $('#CreateModal').modal('show');
        let licenseData = $(this).data('license');
        populateModalFields(licenseData);
        $('#SaveFunction').text('Update')
        $("#SaveFunction").css({
            "cursor": "",
            "pointer-events": "",
            "opacity": ""
        });     
    });
    $("#SaveFunction").on('click',async function () {      
        let licenseKey = $("#licensekey").val();
        let isLicenseKey = await validateLicenseKey(licenseKey);
        if (isLicenseKey) {
            $("#SaveFunction").css({
                "cursor": "none",
                "pointer-events": "none",
                "opacity": "0.5"
            });
            $("#example-form").trigger('submit');
        }
    }); 
    $('#txtPassword').on('keydown keyup', async function () {
        const value = $(this).val();
        const sanitizedValue = value.replace(/\s+/g, '');
        $(this).val(sanitizedValue);
        await validateNewPassword(value);
    });
    async function validateLicenseKey(value) {
        if (!value) {
            $('#LicenseKey-error').text('Enter license key').addClass('field-validation-error');
            return false;
        }
        else if (value?.length < 30) {
            $('#LicenseKey-error').text('Invalid license key').addClass('field-validation-error');
            return false;
        }
        else {
            $('#LicenseKey-error').text('').removeClass('field-validation-error');
        }
        return true;
    }
    $("#CreateModal").on('click', function () {
        clearInputFields();
    });
    const clearInputFields = () => {
        $('#licensekey').val('');
        $('#SaveFunction').text('Save');
        $('#LicenseKey-error').text('').removeClass('field-validation-error');
    };
    const cleardecommissionFields = () => {
        $("#txtPassword").val('');
        $('#Password-error').text('').removeClass('field-validation-error');
    }
    $("#Authentication").on('click', function () {
        cleardecommissionFields();
    });
    $(document).on('click', "#Replace", function () {
       // $("#txtIpaddress").val("");
       // $("#txtHostname").val("");
        $("#Ipaddress-error,#HostName-error").text('').removeClass('field-validation-error');
    });
    function populateModalFields(licenseData) {      
        $('#licensekey').val();
        $('#licenseId').val(licenseData.id);
        $('#LicenseKey-error').text('').removeClass('field-validation-error')
    }
});
$(".childgroup").first().addClass("show")
$(".navgroup").first().addClass("show")
//$(".navgroup ").removeClass("active")
$(".fadegroup").first().addClass("active show")
$(".group").first().attr("aria-expanded", "true");
$(".loadPartialViewButton").on("click", async function(){
    $(".fadegroup, .navgroup").removeClass("active");
})
$('#BtnLicenseDownload').on('click', async function () {
    try {
        $("#BtnLicenseDownload").addClass("disabled");
        const url = "/Report/PreBuildReport/LicenseUtilizationReport";
        const params = new URLSearchParams({
            Businessserviceid: "",
            Licensepoid: "All",
            Derivedpoid: null,
            Type: "Dashboard"
        });
        const response = await fetch(`${url}?${params.toString()}`);
        if (response.ok) {
            const blob = await response.blob();
            var alertClass, iconClass, message;
            if (blob.size > 0) {
                let dateTime = new Date(srvTimes());
                let date = dateTime.toLocaleString('en-GB', {
                    day: 'numeric',
                    month: 'numeric',
                    year: 'numeric',
                    hour: 'numeric',
                    minute: 'numeric',
                    second: 'numeric',
                    hour12: true
                }).replace(/am|pm/i, match => match.toUpperCase());

                let parts = date.split(/[\/, :]+/);

                const formattedDateTime = `${parts[0]}${parts[1]}${parts[2]}_${parts[3]}${parts[4]}${parts[5]}${parts[6]}`;
               
                downloadLicense(blob, "LicenseReport_" + formattedDateTime + ".pdf", "application/pdf");
                alertClass = "success-toast";
                iconClass = "cp-check toast_icon";
                message = "LicenseReport Downloaded Successfully";
                $("#BtnLicenseDownload").removeClass("disabled");
            }
            else {
                alertClass = "warning-toast";
                iconClass = "cp-exclamation toast_icon";
                message = "LicenseReport Download Error";
                $("#BtnLicenseDownload").removeClass("disabled");
            }
        } else {
            alertClass = "warning-toast";
            iconClass = "cp-exclamation toast_icon";
            message = "LicenseReport Download Error";
            $("#BtnLicenseDownload").removeClass("disabled");
        }
        $('#alertClass').removeClass().addClass(alertClass);
        $('#icon').removeClass().addClass(iconClass);
        $('#notificationAlertmessage').text(message);
        $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
    }
    catch (error) {
        $("#BtnLicenseDownload").removeClass("disabled");
    }
});
function srvTimes() {
    try {
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (err3) {
                notificationAlert('warning', "Error occured while fetching the server time")
                return false;
            }
        }
    }
    xmlHttp?.open('HEAD', window.location.href.toString(), false);
    xmlHttp?.setRequestHeader("Content-Type", "text/html");
    xmlHttp?.send('');
    return xmlHttp?.getResponseHeader("Date");
}
function downloadLicense(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        errorNotification(error)
    }
}
function initializeDataTable(tableId) {
    $('#' + tableId).DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        lengthMenu: [
            [5, 25, 50, -1],
            [5, 25, 50, 'All']
        ]
    });
}
$(".parentcollapse a.nav-link").off('click').on('click', async function () {   
    try {
        let parentEntity = $(this).data("parententityId");
        let url = RootUrl + "Admin/LicenseManager/GetEntityList";
        let tableId = "tblCompany_" + parentEntity;
        let data = {
            licenseId: $(this).data("entity-id"),
            entity: $(this).data("entity-name"),
            entityType: $(this).data("entity-type")
        };
        let response = await GetAsync(url, data, OnError);
        $("." + parentEntity).empty();
        if (response && Array.isArray(response) && response?.length > 0) {
            let tableRows = '';
            let serverNames = response[0].entity;
            response?.forEach((list, i) => {
                let splitData = list?.entityField.split(",")
                let serverName = list?.entityName;
                let hostName = splitData[1];
                let databaseSid = splitData[2];
                let childsiteName = list?.entityField
                let serviceName = list?.businessServiceName;
                let type = list?.type;
                let icon = list?.logo && list?.logo !== "" && list?.logo !== null ? list?.logo.replace(/[^a-zA-Z0-9]/g, '_') + '.svg' :
                    serverNames?.toLowerCase() === "server" ? 'cp_server.svg' :
                        serverNames?.toLowerCase() === "database" ? 'cp_database.svg' :
                            serverNames?.toLowerCase() === "replication" ? 'cp_replication_rotate.svg' : null;

                let ipAddress = splitData[0] || "NA";
                let parts = ipAddress.split(".");
                let maskedIpAddress = parts?.length === 4 ? "xx.xx.xx." + parts[3] : ipAddress;
              
                tableRows += `
        <tr>
        <td>${i + 1}</td>
        <td class="truncate">
            ${serverNames.toLowerCase() === "server" ?
                        `<img src="/img/OS_Icon/${icon}" width="20">` :
                        (serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "replication" ?
                            `<img src="/img/DB-Logo/${icon}" width="20">` : '')
                    }
            ${type}
        </td>
        <td class="truncate  align-bottom" title="${serverName}">${serverName}</td>
        <td class="truncate" title="${serviceName}">${serviceName}</td>
        ${serverNames.toLowerCase() === "database" ?
                    `<td class="truncate" title="${databaseSid}">${databaseSid}</td>` :
                        (serverNames.toLowerCase() === "replication" ?
                        `<td class="truncate" title="${childsiteName}">${childsiteName}</td>` :
                            '<td></td>'
                        )
                    }
        ${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? `<td class="truncate">${maskedIpAddress}</td>` : '<td></td>'}          
        ${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? `<td class="truncate"  title="${hostName}">${hostName}</td>` : '<td></td>'}
        <td class="text-start truncate">
            <span type="button" title="Decommission" class="me-2" 
                data-decommissionid="${list?.licenseId}" 
                data-decommissionentity="${list?.entityId}" 
                data-decommissionname="${list?.entity}" 
                data-decommissiontype="${list?.entityType}" 
                onclick="decommissionTable(this)" 
                data-bs-toggle="modal" 
                data-bs-target="#serverattachedModal">
                <i class="cp-file-not-to-delete"></i>
            </span>                           
            <span type="button" title="Replace" 
                class="replace-button"
                data-replaceentityid="${list?.entityId}" 
                data-replaceentity="${list?.entity}" 
                data-replaceentityname="${list?.entityName}"        
                data-replaceipaddress="${splitData[0]}"
                data-replacesid="${splitData[2]}"
                data-replacehostname="${splitData[1]}"
                data-bs-toggle="modal" 
                id="Replace"
                data-bs-target="#ReplaceModal"
                ${list?.entity && list?.entity.toLowerCase() === 'replication' ? 'disabled' : ''} 
                style="${list?.entity && list?.entity.toLowerCase() === 'replication' ? 'pointer-events: none; opacity: 0.5;' : ''}"
            >
                <i class="cp-replace"></i>
            </span>
        </td>
    </tr>`;
            });

            let headerRow = `
<tr>
    <th class="SrNo_th">Sr.No.</th>
    <th>Type</th>
    <th>${serverNames} Name</th>
    <th>Service Name</th>
    <th>${serverNames.toLowerCase() === "database" ? "Database SID" : serverNames.toLowerCase() === "replication" ? "Site Name" : ""}</th>
    <th>${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? "IP Address" : ""}</th>
    <th>${serverNames.toLowerCase() === "database" || serverNames.toLowerCase() === "server" ? "Host Name" : ""}</th>
    <th>Action</th>
</tr>`;

            let tableHtml = `
<div class="card card-body bg-white mb-0">
    <table class="table table-hover serverlist_popup" style="width:100%" id="${tableId}">
        <thead>
            ${headerRow}
        </thead>
        <tbody class="server-list-table" style="max-height: 200px; overflow-y: auto;">
            ${tableRows}
        </tbody>
    </table>
</div>`;
            $("." + parentEntity).append(tableHtml);
            initializeDataTable(tableId);
        } else {
            displayNoDataMessage(parentEntity);
        }
    } catch (error) {
        errorNotification(error)
    }
});
function displayNoDataMessage(parentEntity) {
    let html = `
  <div class="card card-body bg-white mb-0">
    <img src="/img/isomatric/license_expired.svg" height="120" style="width: fit-content; }"/>
  </div>`;
    $("." + parentEntity).append(html);
}
const ipv4Pattern = /^(?:(?:1\d\d|2[0-4]\d|25[0-5]|([1-9]?\d))\.){3}(?:1\d\d|2[0-4]\d|25[0-5]|([1-9]?\d))$/;
async function validateIpAddress(value) {
    if (!value) {
        $('#Ipaddress-error').text('Enter IP address').addClass('field-validation-error');
        return false;
    } else if (!ipv4Pattern.test(value)) {
        $('#Ipaddress-error').text('Invalid IP address').addClass('field-validation-error');
        return false;
    }
    $('#Ipaddress-error').text('').removeClass('field-validation-error');
    return true;
}
async function validateHostName(value) {
    if (!value) {
        $('#HostName-error').text('Enter host name').addClass('field-validation-error');
        return false;
    }
    $('#HostName-error').text('').removeClass('field-validation-error');
    return true;
}
async function validateDatabaseSid(value) {
    if (!value) {
        $('#DatabaseName-error').text('Enter database sid').addClass('field-validation-error');
        return false;
    }
    $('#DatabaseName-error').text('').removeClass('field-validation-error');
    return true;
}
let dataReplace = {};
$(document).on('click', '.replace-button', async function () {   
    $('#licenceReplaceServerIP').prop('disabled', true);
    dataReplace.entityId = $(this).data('replaceentityid');
    dataReplace.entity = $(this).data('replaceentity');
    dataReplace.entityName = $(this).data('replaceentityname');
    dataReplace.ipaddress = $(this).data('replaceipaddress');
    dataReplace.hostname = $(this).data('replacehostname');
    dataReplace.sid = $(this).data('replacesid');
    if (dataReplace.entity.toLowerCase() == "database") {
        $("#hostnameshow").show();
        $("#databaseshow").show();
        $("#databasetitle").html("Database SID");
        dataReplace.sid = $("#txtDatabasesid").val(dataReplace.sid);
        $("#replacetitle").html("IP Address");
        dataReplace.ip = $("#txtIpaddress").val(dataReplace.ipaddress);
        $("#hosttitle").html("Host Name");
        dataReplace.host = $("#txtHostname").val(dataReplace.hostname);
        $('#txtDatabasesid').on('keydown keyup', async function () {
            $('#licenceReplaceServerIP').prop('disabled', false);  
            const value = $('#txtDatabasesid').val();
            const sanitizedValue = value.replace(/\s+/g, '');
            $('#txtDatabasesid').val(sanitizedValue);
            await validateDatabaseSid(value);
        });
    }
    else if (dataReplace.entity.toLowerCase() == "replication") {
        $("#hostnameshow").hide();
        $("#databaseshow").hide();
        $("#replacetitle").html("Site Name");
    }
    else {
        $("#replacetitle").html("IP Address");      
        dataReplace.ip = $("#txtIpaddress").val(dataReplace.ipaddress);
        $("#hosttitle").html("Host Name");
        dataReplace.host = $("#txtHostname").val(dataReplace.hostname);
        $("#hostnameshow").show();
        $("#databaseshow").hide();
        $('#txtIpaddress').on('keydown keyup', async function () {         
            $('#licenceReplaceServerIP').prop('disabled', false);       
            const value = $(this).val();
            const sanitizedValue = value.replace(/\s+/g, '');
            $(this).val(sanitizedValue);
            await validateIpAddress(value)
        });
        $('#txtHostname').on('keydown keyup', async function () {
            $('#licenceReplaceServerIP').prop('disabled', false);        
            const value = $(this).val();
            const sanitizedValue = value.replace(/\s+/g, '');
            $(this).val(sanitizedValue);
            await validateHostName(value)
        });
    }
});
$(document).on('click', '#licenceReplaceServerIP', async function () {   
    let isProcess = false;
    try {
        if (isProcess) {
            return;
        }
        isProcess = true;
        if (dataReplace.entity.toLowerCase() == "database") {
            dataReplace.sid = $("#txtDatabasesid").val().toString();
            dataReplace.ip = $("#txtIpaddress").val().toString();
            dataReplace.host = $("#txtHostname").val().toString();
            validateIpAddress(dataReplace.ip);
            validateHostName(dataReplace.host);
            validateDatabaseSid(dataReplace.sid)
        }
        else {
            dataReplace.ip = $("#txtIpaddress").val().toString();
            dataReplace.host = $("#txtHostname").val().toString();
            validateIpAddress(dataReplace.ip);
            validateHostName(dataReplace.host);
        }
        if (dataReplace.entity.toLowerCase() == "database" && dataReplace.sid !== "") {
            await prepareAndSave(dataReplace);
            return;
        }
        else if (dataReplace.ip !== "" && ipv4Pattern.test(dataReplace.ip)) {
            await prepareAndSave(dataReplace);
            return;
        }
    } catch (error) {
        errorNotification(error)
    } finally {
        isProcess = false;
    }
});
async function prepareAndSave(data) {   
    try {     
        let request = {
            EntityId: data.entityId,
            EntityName: data.entityName,
            EntityType: data.entity,
            IpAddress: data.ip,
            HostName: data.host,
            Sid: data.sid,   
        };
        let result = await $.ajax({
            type: 'PUT',
            url: RootUrl + 'Admin/LicenseManager/ReplaceLicenseDetails',
            data: JSON.stringify(request),
            dataType: 'json',
            headers: {
                'RequestVerificationToken': gettoken()
            },
            contentType: 'application/json',
        });
        if (result.success) {
            setTimeout(() => {
                $('#ReplaceModal').modal('hide');
            }, 1000);
            $('#ReplaceModal #txtIpaddress').val(data.ip)
            $('#ReplaceModal #txtHostname').val(data.host)
            $('#ReplaceModal #txtDatabasesid').val(data.sid)
            setTimeout(() => {
                window.location.reload();
            },1000)
            notificationAlert('success', result.data.message);
        } else {
            notificationAlert('error', result.message);
        }
    } catch (error) {
        errorNotification(error)
    }
}
async function decommissionTable(data) {   
    $('#attached').empty()
    let delicenseId = data?.dataset.decommissionid
    let deentityId = data?.dataset?.decommissionentity
    let deentityName = data?.dataset?.decommissionname
    let type = data?.dataset?.decommissiontype
    let dType = type?.toLowerCase();
    let dName = deentityName?.toLowerCase();
    if (dName) {
        dName = dName?.charAt(0)?.toUpperCase() + dName?.slice(1);
    }
    if (dType) {
        dType = dType?.charAt(0)?.toUpperCase() + dType?.slice(1);
    }
    let settype = dType !== "Null" && dType !== "" && dType !== "Database" ? dType : dName
    let seticon = dType === "Database" ? "cp-server" : dType === "Application" ? "cp-application" : dType === "Network" ? "cp-network" :
        dType === "Storage" ? "cp-storage" : dType === "Virtualization" ? "cp-virtualization" : dType === "ThirdParty" ? "cp-api" : dName === "Replication" ? "cp-replication-on" : dType === "Dns" ? "cp-DNS" : "cp-database"
    let url = RootUrl + "Admin/LicenseManager/GetDecommissionList";
    forcefullyDelete(delicenseId, deentityId, deentityName, type)
    $('#attached').empty()
    let requestData = {
        licenseId: delicenseId,
        entityId: deentityId,
        entityType: deentityName
    };
    $('#attached').empty()
    let value = await GetAsync(url, requestData, OnError);
    if (value.success === false) {
        notificationAlert('warning', value.message)
    }
    else {
        let decommissionTableBody = $('#decommissiondata');
        let headerattached = '<h6 class="page_title"><i class="' + seticon + '"></i><span>' + settype + ' Attached With</span></h6>' +
            '<button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>'
        $('#attached').append(headerattached);

        function addRow(iconClass, title, dataArray, keyName) {
            let rowContent = `<tr>
            <th><i class="${iconClass} me-1"></i>${title}</th>
            <td>`;

            if (Array.isArray(dataArray) && keyName) {
                dataArray.forEach((dataItem) => {
                    rowContent += `${dataItem[keyName]}<br>`;
                });
            } else if (dataArray !== null && keyName) {

                if (dataArray === "NA") {
                    rowContent += 'NA';
                }
                else {
                    rowContent += dataArray[keyName] !== "" && dataArray[keyName] !== null ? dataArray[keyName] : "NA" + '<br>';
                }

            } else {
                rowContent += 'NA';
            }
            rowContent += `</td></tr>`;
            decommissionTableBody.append(rowContent);
        }
        decommissionTableBody.empty();
        if (value?.serverName) {
            addRow('cp-server', 'Server', value?.serverName ? value : value?.decommissionServerDetailVm.length > 0 ? value?.decommissionServerDetailVm : "NA", 'serverName');
            addRow('cp-database', 'Database', value?.databaseName ? value : value?.decommissionDatabaseDetailVm?.length > 0 ? value?.decommissionDatabaseDetailVm : "NA", 'databaseName');
            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.databaseName) {
            addRow('cp-database', 'Database', value?.databaseName ? value : value?.decommissionDatabaseDetailVm.length > 0 ? value?.decommissionDatabaseDetailVm : "NA", 'databaseName');
            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.replicationName) {
            addRow('cp-replication-on', 'Replication', value?.replicationName ? value : value?.decommissionReplicationDetailVm?.length > 0 ? value?.decommissionReplicationDetailVm : "NA", 'replicationName');
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.infraObjectName) {
            addRow('cp-infra-object', 'Infra Object', value?.infraObjectName ? value : value?.decommissionInfraObjectDetailVm?.length > 0 ? value?.decommissionInfraObjectDetailVm : "NA", 'infraObjectName');
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
        if (value?.workflowName) {
            addRow('cp-workflow', 'Workflow', value?.workflowName ? value : value?.decommissionWorkflowDetailVms?.length > 0 ? value?.decommissionWorkflowDetailVms : "NA", 'workflowName');
            addRow('cp-workflow-profile', 'Workflow Profile', value?.profileName ? value : value?.decommissionWorkflowProfiles?.length > 0 ? value?.decommissionWorkflowProfiles : "NA", 'profileName');
        }
    }
}
async function validateNewPassword(value) {
    if (!value) {
        $('#Password-error').text('Enter password').addClass('field-validation-error');
        return false;
    } else if (value.length < 8) {
        $('#Password-error').text('Passwords must be 8 characters')
            .addClass('field-validation-error');
        return false;
    } else if (!/\d/.test(value)) {
        $('#Password-error').text('Password must contain at least one number')
            .addClass('field-validation-error');
        return false;
    } else if (!/[!@#$%^&*]/.test(value)) {
        $('#Password-error').text('Password must contain at least one symbol')
            .addClass('field-validation-error');
        return false;
    } else if (!/[A-Z]/.test(value)) {
        $('#Password-error').text('Password must contain at least one uppercase letter')
            .addClass('field-validation-error');
        return false;
    } else if (!/[a-z]/.test(value)) {
        $('#Password-error').text('Password must contain at least one lowercase letter')
            .addClass('field-validation-error');
        return false;
    } else {
        $('#Password-error').text('').removeClass('field-validation-error');
    }
    return true;
}
async function forcefullyDelete(delicenseId, deentityId, deentityName,type) {
    let isProcessing = false;
    $("#decommissionFunction").off('click').on('click', licenseDebounce(async function () {
        debugger
        try {
            if (isProcessing) {
                return;
            }
            isProcessing = true;
            const isValid = await validateNewPassword($("#txtPassword").val());
            if (!isValid) {
                return;
            }
            let newPassword = $("#txtPassword").val();
            let loginName = $("#loginName").data("loginnames");
            let url = RootUrl + "Admin/LicenseManager/DecommissionAuthentication";
            let command = {
                'UserName': loginName,
                Password: newPassword
            };
            var result = await PostAsync(url, command, OnError);
            let deleteAction = {
                licenseId: delicenseId,
                entityId: deentityId,
                entityType: type,
                entityName: deentityName
            }
            if (result.success === true) {
                await $.ajax({
                    url: RootUrl + "Admin/LicenseManager/DeleteDecommission",
                    dataType: "json",
                    data: deleteAction,
                    type: "GET",
                    success: function (data) {
                        setTimeout(() => {
                            $("#Authentication").modal('hide');
                        }, 1000);
                        window.location.reload();
                        notificationAlert("success", data.message);
                    },
                    error: function (result) {
                        errorNotification(result)
                    }
                });
            } else {
                notificationAlert("warning", "Invalid Credential");
            }
        } catch (error) {
            errorNotification(error)
        } finally {
            isProcessing = false;
        }
    },500));
}

let action = ""
let icon =""
//Active InActive
$(document).on('click', '.active_inactive', licenseDebounce(async function () {
    $("#confirm_active_main").modal("show")
     icon = $(this);
    let licensestate = icon.data('licensestate');
    let id = licensestate.id;
    let currentState = licensestate.isState;
    if (currentState == true) {
        $("#activeMaintenance").text("Active")
    } else {
        $("#activeMaintenance").text("Maintainance")
    }
    let newState = !currentState;
    action = {
        "Id": id,
        "IsState": newState,
    };
},500))

$(document).on('click', '.activeMaintenanceIcon', licenseDebounce(async function () {
    try {
        let result = await $.ajax({
            type: 'PUT',
            url: "/Admin/LicenseManager/UpdateLicenseState",
            dataType: "json",
            contentType: "application/json",
            headers: {
                'RequestVerificationToken': gettoken()
            },
            data: JSON.stringify(action),
            traditional: true
        });
        $("#confirm_active_main").modal("hide")
        if (result && result.success) {
           
            let datas = result?.data;
            window.location.reload()
            notificationAlert("success", datas?.message);
            let updatedIconStatus = datas?.message.toLowerCase().includes("active") 
                ? '<i class="cp-active-inactive text-success fw-semibold" title="InActive"></i>'
                : '<i class="cp-active-inactive text-danger fw-semibold" title="Active"></i>';
            icon.empty().append(updatedIconStatus);
            icon.data('licensestate', { id: id, isState: newState });
        }
        if (result.success == false) {
            window.location.reload()
            notificationAlert("warning", result?.message);
            icon.data('licensestate', { id: id, isState: currentState });
        }
    } catch (error) {
        icon.data('licensestate', { id: id, isState: currentState });
    }
},500));
$(function () {
    //AMC
    $('#amcRenew').addClass('d-none')
    $('#option3').on('click', function () {
        $('#amcRenew').removeClass('d-none')
    })
    $('#option1, #option2').on('click', function () {
        $("#amcRenew").addClass('d-none');
    });
    $('.amctype').on('change', function () {
        if ($(this).is(':checked')) {
            $('.amctype').not(this).prop('disabled', true);
        } else {
            if ($('.amctype:checked').length === 0) {
                $('.amctype').prop('disabled', false);
            }
        }
    });
    //Renewal
    $("#renewdate").addClass('d-none');
    $('#option1').on('click', function () {
        $('.renewtype').not(this).prop('disabled', false);
        $("#renewdate").removeClass('d-none');
        $("#amcRenew").addClass('d-none');
    }); 
    $('#option2, #option3').on('click', function () {
        $("#renewdate").addClass('d-none');
    });
    $('#option2').on('click', function () {
        $("#amcRenew").addClass('d-none');
    });
    $('#option3').on('click', function () {
        $("#amcRenew").removeClass('d-none');
        $('.amctype').not(this).prop('disabled', false);
    });
    $('.renewtype').on('change', function () {
        if ($(this).is(':checked')) {

            $('.renewtype').not(this).prop('disabled', true);
        } else {

            if ($('.renewtype:checked').length === 0) {
                $('.renewtype').prop('disabled', false);
            }
        }
    });
});
$(document).on('click', '.btn-check', function () {
    $('input[name="renewtype"]:checked').prop('checked', false);
    $('input[name="amctype"]:checked').prop('checked', false);
})
$(document).on('click', '#requestlicense', function () {
    $('input[name="type"]:checked').prop('checked', false);
    $('input[name="renewtype"]:checked').prop('checked', false); 
    $('input[name="amctype"]:checked').prop('checked', false);
    $("#renewdate").addClass('d-none');
    $("#copy_request").addClass('d-none');
    $("#amcRenew").addClass('d-none');
    $(".myDiv").show();
    $("#generate").show();
    $('.myDiv2').addClass('d-none'); 
  
    let licenseDataa = $(this).data('licenserequest');
    if (licenseDataa.isAmc == true) {
        $(".amcnew").hide()
        $(".amcrenewal").show()
    } else {
        $(".amcnew").show() 
        $(".amcrenewal").hide()
    }
    if (licenseDataa.validity == "Enterprise-Unlimited") {
        $(".renewchild").hide()
        $(".Upgrade").hide()
        $(".AMC").show()
    } else if (licenseDataa.validity.includes("POC") || licenseDataa.validity.includes("UAT")) {
        $(".renewchild").show()
        $(".AMC").hide()
        $(".renewchild").show()
        $(".Upgrade").show()
    } else {
        $(".AMC").show()
        $(".renewchild").show()
        $(".Upgrade").show()
        $(".renewchild").show()  
    }
    $('#generate').data('license-id', licenseDataa.id);  
});
function licenseDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
} 

$(document).on('click', '#generate', licenseDebounce(async function () {   
    let id = $(this).data('license-id'); 
    let selectedType = $('input[name="type"]:checked').val(); 
    let renewalType = $('input[name="renewtype"]:checked').val(); 
    let amcType = $('input[name="amctype"]:checked').val(); 
    if (!selectedType ) {
        setTimeout(() => {
            notificationAlert("warning", "Please select a type")
        },300)
    }
    if (selectedType) {
        if (selectedType != "Upgrade" &&!renewalType && !amcType) {
            setTimeout(() => {
                notificationAlert("warning", "Please select a type")
            }, 300)
        }
        if (renewalType || amcType || selectedType == "Upgrade") {
            await $.ajax({
                url: RootUrl + "Admin/LicenseManager/GetLicenseGeneratorKeyById",
                dataType: "json",
                data: { id: id, type: selectedType, renewalType: renewalType ? renewalType : amcType },
                type: "GET",
                success: function (response) {
                    if (response && response.success && response.data) {
                        $('#encryptlicense').text(response.data);
                        $(".myDiv").hide();
                        $("#generate").hide();
                        $("#copy_request").removeClass('d-none');
                        $("#copy_request").attr("title", "Copy")
                        $('.myDiv2').removeClass('d-none');
                    }
                },
                error: function (error) {
                    errorNotification(error)
                }
            });
        }
    }
}, 500));
$(document).on('click', '#copy_request', licenseDebounce(async function () {
    $("#copy_request").attr("title", "Copied")
}, 500));
$(document).on('click', '.cp-copy', function () {
    let licenseKey = $('#encryptlicense').text();
    if (licenseKey) {
        navigator.clipboard.writeText(licenseKey).then(() => {
            alert('License key copied to clipboard');
        })
    } 
});
$(function () {
    // Hide all lists except the outermost.
    $('ul.tree ul').show();
    $('.tree li > ul').each(function (i) {
        var $subUl = $(this);
        var $parentLi = $subUl.parent('li');
        var $toggleIcon = '<i class="js-toggle-icon">-</i>';

        $parentLi.addClass('has-children');

        $parentLi.prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
            $(this).text($(this).text() == '-' ? '+' : '-');
            $subUl.slideToggle('fast');
        });
    });
});