﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.Delete;

public class DatabaseDeletedEventHandler : INotificationHandler<DatabaseDeletedEvent>
{
   
    private readonly ILogger<DatabaseDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DatabaseDeletedEventHandler(ILoggedInUserService userService, ILogger<DatabaseDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        
    }

    public async Task Handle(DatabaseDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress ?? "::1",
            Action = $"{ActivityType.Delete} {Modules.Database}",
            Entity = Modules.Database.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Database '{deletedEvent.DatabaseName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Database '{deletedEvent.DatabaseName}' deleted successfully.");
    }
}