//functions

function commonValidationSSO(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    }
    $('#' + errorElement).text('').removeClass('field-validation-error');
    return true;
};

async function validateSingleSignOnType() {
    const $selectedOption = $('#SSOFormTypeID option:selected');
    const singleSignOnType = $selectedOption.text();   
    $('#SSOFormTypeID').val($selectedOption.attr('formTypeId'));
    $('#singleSignOnType').val(singleSignOnType);
    const singleSignOnlogo = $selectedOption.attr('ssologo');
    const iconClass = (singleSignOnlogo && singleSignOnlogo !== "d-flex") ? singleSignOnlogo : "cp-sign-on-type";
    $('#SSOTypeTitleIcon').attr('class', iconClass);
    singleSignOnIcon = iconClass;
    let element = document.getElementById('singleSignOnHeaderType');

    if (element) {
        element.textContent = singleSignOnType;
    }
    await commonValidationSSO(singleSignOnType, " Select single sign-on type", "signOnTypeError");
};

async function getSSOType(url) {
    await $.ajax({
        type: "GET",
        url: RootUrl + url,
        dataType: "json",
        success: function (result) {
            if (result?.success && (Array.isArray(result?.data) && result?.data?.length > 0)) {
                const selectedType = document.getElementById("selectType");
                const uniqueValues = new Set();
                result.data.forEach(form => {
                    if (!uniqueValues.has(form?.signOnTypeId)) {  // Check if the value is not already in the Set
                        const option = document.createElement("option");
                        option.value = form?.signOnTypeId;
                        option.textContent = form?.signOnType;
                        selectedType.appendChild(option);
                        uniqueValues.add(form?.signOnTypeId);
                    }
                });
            } else {
                errorNotification(result)
            }
        },
    });
}

async function singleSignOnProperties(id) {
    await $.ajax({
        url: RootUrl + "Admin/FormMapping/GetFormMappingByFormId",
        method: 'GET',
        data: { "formTypeId": id, "version": '' },
        dataType: 'json',
        success: async function (result) {
            if (result?.success) {
                nextButtonStyle('', '');  //Remove style for next button
                let form = result?.data;
                $('#singleSignOnFormRenderingArea').empty();

                try {
                    let parsedJsonData = JSON.parse(form?.properties);
                    var renderedForm = new FormeoRenderer({
                        renderContainer: document.querySelector("#singleSignOnFormRenderingArea")
                    });
                    await renderedForm.render(parsedJsonData);

                    //initial state
                    if (!isEditSSO) {
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            let field = parsedJsonData.fields[fieldId];
                            if (field.conditions && field.conditions.length > 0) {
                                field.conditions.forEach(function (condition) {
                                    condition.if.forEach(function (ifClause) {
                                        condition.then.forEach(function (thenClause) {
                                            if (thenClause.targetProperty === 'isVisible' && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                                if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                                    targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                                }
                                            }
                                        });
                                    });
                                });
                            }
                        });
                    }

                    setTimeout(() => {
                        let selectElements = document.querySelectorAll('.form-select-modal-dynamic');
                        selectElements.forEach(async function (selectElement) {
                            let $this = $(selectElement);
                            $this.select2({
                                dropdownParent: this1.find('.modal-content'),
                                placeholder: $this.attr('placeholder')
                            });
                        });
                        $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');

                        let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                        disableSelectTagTitle?.forEach(async function (selectElement) {
                            let $this = $(selectElement);
                            $this.attr('title', '');
                        });

                        onChangeFormBuilderValidation('singlesignon');
                    }, 500);

                    await populateFormbuilderDynamicFields(parsedJsonData.fields);

                    for (const key in parsedJsonData?.fields) {
                        if (parsedJsonData.fields.hasOwnProperty(key)) {
                            const field = parsedJsonData.fields[key];
                            setFieldAttrValues(field);
                        }
                    }

                    $('#singleSignOnFormRenderingArea').on('focus', '.formeo-render .f-field-group input', function (event) {
                        let selectedId = event.target.id;
                        let type = event.target.type;
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (selectedId == `f-${fieldId}`) {
                                if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                                    $.ajax({
                                        type: "POST",
                                        url: RootUrl + SSOURL.serverDataDecrypt,
                                        data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                                        dataType: 'text',
                                        success: function (decryptedValue) {
                                            event.target.value = decryptedValue;
                                        }
                                    });
                                }
                            }
                        })
                    });

                    $('#singleSignOnFormRenderingArea').on('blur', '.formeo-render .f-field-group input', function (event) {
                        let selectedId = event.target.id;
                        let type = event.target.type;
                        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                            var field = parsedJsonData.fields[fieldId];
                            if (selectedId == `f-${fieldId}`) {
                                if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                                    $.ajax({
                                        type: "POST",
                                        url: RootUrl + SSOURL.serverDataEncrypt,
                                        data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                                        dataType: 'text',
                                        success: function (encryptedValue) {
                                            event.target.value = encryptedValue;
                                        }
                                    });
                                }
                            }
                        })
                    });

                    //on`setconditionals
                    $('#singleSignOnFormRenderingArea').on('input', '.formeo-render .f-field-group input', function (event) {
                        formBuilderTextConditions(event, parsedJsonData);
                    });

                    $('#singleSignOnFormRenderingArea').on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
                        formBuilderSSOCondition(event, parsedJsonData);
                    });

                    if (isEditSSO && propsSSO) {
                        setTimeout(() => {
                            populateSSODynamicFields(propsSSO);
                        }, 250)
                    }
                } catch (error) {
                    notificationAlert("warning", "Form property is not valid format.");
                    nextButtonStyle('0.5', 'none');
                }
            }
            else {
                errorNotification(result);
                nextButtonStyle('0.5', 'none');
            }
        },
    });
}

function populateSSODynamicFields(data) {
    let formData = JSON.parse(data);
    $('#singleSignOnFormRenderingArea .formeo-render .f-field-group').each(function (index, element) {
        let fieldName = $(element).find('input, select, textarea').attr('name');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        let fieldType = $(element).find('input, select, textarea').attr('type');

        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {
            let value = formData[fieldName];
            let checkbox = $(element).find('input[type="checkbox"]').attr("value");
            let chkValue = formData[checkbox];

            if (fieldType == 'radio') {
                $(element).find('input[type="radio"]').map((index, radio) => {
                    let radioValue = $(radio).val();

                    if (radioValue === formData[radioValue]) {
                        $(radio).prop('checked', true);
                    }
                });
            }

            if (typeof value === "boolean") {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change");
            }
            else if (fieldVal) {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            else if (typeof value === "object") {

                if (value) {
                    const valuesArray = Object.values(value);
                    const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                    let labelsArray;

                    if (containsObject) {
                        labelsArray = valuesArray.map(item => item.value);
                    } else {
                        labelsArray = valuesArray.map(item => item);
                    }
                    const selectElement = $(element).find('select');
                    setTimeout(function () {
                        selectElement?.find('option').each(function () {
                            const optionValue = $(this).val();

                            if (labelsArray.includes(optionValue)) {
                                $(this).prop('selected', true);
                            }
                        });
                        selectElement.trigger('change');
                    }, 350)
                }
            }
            else {
                $(element).find('input, select, textarea').val(value);
                $(element).find('input, select, textarea').trigger("change");
            }
        }
    });
}

async function SSOSaveFormFields() {
    let formData = {};
    let promises = [];
    $('#singleSignOnFormRenderingArea .formeo-render .f-field-group').each(async function (index, element) {   // Iterate through form fields and populate formData object
        let $this = $(this);

        if ($this.is(':visible')) {
            let fieldName = $(element).find('input, select, textarea').attr('name');
            let multiple = $(element).find('select').attr('multiple');
            let fieldVal = $(element).find('input').attr('value');
            let fieldType = $(element).find('input, select, textarea').attr('type');
            let value;

            if (fieldType === "date") {
                value = $(element).find('input[type="date"]').val();
                formData[fieldName] = value;
            }

            if (fieldName) {

                if (fieldType === 'checkbox') {
                    value = $(element).find('input[type="checkbox"]').prop('checked');
                } else if (fieldType === 'radio') {
                    value = $(element).find('input[type="radio"]:checked').val();
                    formData[value] = value;
                } else {
                    value = $(element).find('input, select, textarea').val();
                }

                if (fieldType === "checkbox") {
                    formData[fieldVal] = value;
                }

                if (fieldType === "password" && (value && value !== "") && value?.length < 64) {
                    promises.push(await EncryptPassword(value).then(encryptedPassword => {
                        formData[fieldName] = encryptedPassword;
                    }));
                }
                else {
                    formData[fieldName] = value;
                }
            }
            if (fieldName?.toLowerCase()?.includes('nodes') && multiple === "multiple") {
                const selectedOptions = $(element).find('select').val(); // Get selected values
                const idToLabelMapping = {};
                $(element)
                    .find('select option')
                    .each(function () {
                        idToLabelMapping[this.value] = $(this).val();
                    });
                objects_list = [];
                const selectedKeyValuePairs = selectedOptions.reduce((acc, curr) => {
                    acc[curr] = idToLabelMapping[curr];
                    return acc;
                }, {});
                $.each(selectedKeyValuePairs, function (key, value) {
                    let obj = {
                        'label': value,
                        'value': key
                    };
                    objects_list.push(obj);
                });
                formData[fieldName] = objects_list;
            }
        }
    });

    await Promise.all(promises);
    let formDataJson = JSON.stringify(formData);
    let hiddenInput = document.getElementById('databaseProps');

    if (hiddenInput) {
        hiddenInput.value = formDataJson;
    }
    return formData;
}

function clearErrorMessage() {
    $("#profileNameError, #signOnTypeError").text("").removeClass('field-validation-error');
}

function populateSSOModalFields(ssoData) {
    $("#singleSignOnName").val(ssoData?.profileName);
    $("#singleSignOnType").val(ssoData?.signOnType);
    $('#SSOFormTypeID').val(ssoData?.signOnTypeId);
    $("#singleSignOnID").val(ssoData?.id);
    $("#businessServiceName").val(ssoData?.businessServiceName);
    setTimeout(() => {
        $('#SSOFormTypeID').trigger("change");
    }, 150);
    propsSSO = ssoData?.properties;
}