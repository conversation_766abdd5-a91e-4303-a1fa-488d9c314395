﻿let selectedValues = [], alertInfra = [], alertServerity = [], alertTypes = [], alertChartList = [];
let  dateModify = "",  alertCount = "";
let chartDatas = "", overallServerityChartdata = "", serverityChartdata = "", dataTable;
let x = localStorage.getItem("ID");
let alertUrls = {
    pagination: "/Alert/AlertDashboard/GetPagination",
    LoadReport: "/Alert/AlertDashboard/LoadReport",
    listWeek: "Alert/AlertDashboard/ListWeek"
}
let noData = `<img src="../../img/isomatric/no_data_found.svg" 
              class="Card_NoData_Img" style="padding:10px">`;

$(function () {
    $("#alertServerityValue").select2();
    $("#alertInfraValue").select2();
    $("#alertTypeValue").select2();
    $('#BtnAlertDownload').on('click', async function () {
        try {
            $("#BtnAlertDownload").addClass("disabled");
            await $.ajax({
                url: alertUrls.LoadReport,
                type: "GET",
                xhrFields: {
                    responseType: 'blob'
                },
                data: reportQueryAlert,
                success: function (blob) {
                    if (blob.size > 0) {
                        const DateTime = new Date().toLocaleString('en-US', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            fractionalSecondDigits: 3,
                            hour12: false
                        }).replace(/[^0-9]/g, '');
                        // Trigger file download
                        downloadAlert(blob, "AlertsReport_" + DateTime + ".pdf", "application/pdf");
                        notificationAlert("success", "Alert report downloaded successfully")
                    } else {
                        notificationAlert("warning", "AlertReport Download Error")
                    }
                    $("#BtnAlertDownload").removeClass("disabled");
                }
            });
        } catch (error) {
            $("#BtnAlertDownload").removeClass("disabled");
        }
    });
    function downloadAlert(blob, fileName, contentType) {
        try {
            const link = document.createElement("a");
            link.download = fileName;
            link.href = window.URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            notificationAlert("Error downloading file: " + error.message);
        }
    }
    dataTable = $('#alertTableData').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": alertUrls.pagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "type" : sortIndex === 2 ? "severity" : sortIndex === 3 ? "systemMessage" :
                        sortIndex === 4 ? "jobName" : sortIndex === 5 ? "infraObjectName" : sortIndex === 6 ? "createdDate" : "";   
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    var x = localStorage.getItem("ID");
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues?.length === 0 ? $('#alertSearchInp').val() : selectedValues.join(';');
                    d.alertId = x;
                    d.InfraObjectId = $('#alertInfraValue').val() === "All" ? '' : $('#alertInfraValue').val()
                    d.Severity = $('#alertServerityValue').val() === "All" ? '' : $('#alertServerityValue').val();
                    d.Type = $('#alertTypeValue').val() === "All" ? '' : $('#alertTypeValue').val()
                    d.CreateDate = $('#alertStartDateInp').find("input[type=date]").val() === "" ? '' : $('#alertStartDateInp').find("input[type=date]").val()
                    d.EndDate = $('#alertEndDateInp').find("input[type=date]").val() === "" ? '' : $('#alertEndDateInp').find("input[type=date]").val();
                    selectedValues.length = 0;
                    localStorage.removeItem("ID");
                    reportQueryAlert = d;
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        overallServerityChartdata = json?.data?.item1?.totalCount
                        serverityChartdata = json?.data?.item2
                        //if (json?.data?.item1?.data?.length == 0) {
                        //    console.log(json?.data?.item1?.data)
                        //    $("#alertStartDate").val("").prop('disabled', true);
                        //    $("#alertEndDate").val("").prop('disabled', true);
                        //} else {
                        //    $("#alertStartDate").prop('disabled', false);
                        //    $("#alertEndDate").prop('disabled', false);
                        //}
                        json.recordsTotal = json?.data?.item1?.totalPages || 0
                        json.recordsFiltered = json?.data?.item1?.totalCount;
                        if (json?.data?.item1?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        json?.data?.item1?.data?.forEach(function (item, i) {
                            alertInfra.push(item)
                            alertServerity.push(item.severity)
                            alertTypes.push(item.type)
                        });
                        alertInfra?.forEach(function (value, index) {
                            $('#alertInfraValue').append('<option title="' + value.infraObjectName + '" value="' + value.infraObjectId + '">' + value.infraObjectName + '</option>')
                        });
                        if (alertServerity?.length == 0) {
                            $('#alertServerityValue').empty()
                        }
                        alertTypes?.forEach(function (value, index) {
                            $('#alertTypeValue').append('<option value="' + value + '">' + value + '</option>')
                        })
                        $("#alertInfraValue option,#alertServerityValue option,#alertTypeValue option").each(function () {
                            $(this).siblings('[value="' + this.value + '"]').remove()
                        })
                        
                        return json.data.item1.data;
                    } else {
                        errorNotification(json.data.item1.data)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            //return meta.row + 1;
                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": "type", "name": "Alert Name", "autoWidth": true, "render": function (data, type, row) {
                        return `<td><span  title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                    }
                },
                {
                    "data": "severity",
                    "name": "Alert Priority",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass = data == "High" ? "fw-bold cp-up-doublearrow text-warning" : data == "Critical" ? "fw-bold cp-critical-level text-danger" : data == "Information" ? "cp-warning text-primary" : data == "Low" ? "fw-bold cp-down-doublearrow text-success":""
                        return `<td><i class="me-1 ${iconClass}"></i> ${data ?? "NA"} </td>`;
                    }
                },
                {
                    "data": "systemMessage",
                    "name": "Description",
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td class="truncate"><span  title="${data ?? "NA"}">${data ?? "NA"}</span></td>`;
                    }
                },
                { "data": "jobName", "name": "Job Name", "autoWidth": true },
                { "data": "infraObjectName", "name": "Infraobject", "autoWidth": true, },
                { "data": "createdDate", "name": "Created Date", "autoWidth": true },
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;

                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    alertTrigger()
    alertTriggerChart()
    $('#alertSearchInp').on('keydown input', alertdebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const inputValue = $('#alertSearchInp').val();
            selectedValues.push(inputValue);
            let currentPage = dataTable.page.info().page + 1;
            if (!isNaN(currentPage)) {
                dataTable.ajax.reload(function (json) {
                    if (e.target.value && json.recordsFiltered === 0) {
                        $('.dataTables_empty').text('No matching records found');
                    }
                }, false)
            }
    }, 500));
    $("#alertReset").on("click", function () {
        $("#alertInfraValue,#alertServerityValue,#alertTypeValue").val("All").trigger("change")
        $("#alertStartDate,#alertEndDate").val("").trigger("change")
        alertTriggerChart()
    })
    function alertdebounce(func, timeout = 300) {
        let timer;
        return (...args) => {
            clearTimeout(timer);
            timer = setTimeout(() => { func.apply(this, args); }, timeout);
        };
    }
    function alertTrigger() {
        setTimeout(() => {
            if (overallServerityChartdata != 0) {
                $('#BtnAlertDownload').removeClass("disabled");
                var overallData = overallServerityChartdata;
                const selectedSeverity = $('#alertServerityValue').val();
                let High = 0, Critical = 0, Low = 0, Information = 0
                if (selectedSeverity === "All" && $("#alertInfraValue").val() == "All" && $("#alertTypeValue").val() == "All" && $('#alertStartDateInp').find("input[type=date]").val() == "" && $('#alertEndDateInp').find("input[type=date]").val() == "") {
                    Object.keys(serverityChartdata).map(function (k) {
                        if (k.includes("High")) {
                            High = serverityChartdata.High
                        }
                        if (k.includes("Critical")) {
                            Critical = serverityChartdata.Critical
                        }
                        if (k.includes("Information")) {
                            Information = serverityChartdata.Information
                        }
                        if (k.includes("Low")) {
                            Low = serverityChartdata.Low
                        }
                    })
                } else {
                    Object.keys(serverityChartdata).map(function (k) {
                        if (k.includes("High")) {
                            High = serverityChartdata.High
                        }
                        if (k.includes("Critical")) {
                            Critical = serverityChartdata.Critical
                        }
                        if (k.includes("Information")) {
                            Information = serverityChartdata.Information
                        }
                        if (k.includes("Low")) {
                            Low = serverityChartdata.Low
                        }
                    })
                }
                am4core.useTheme(am4themes_animated);
                // Create chart instance
                var chart = am4core.create("alertChartDiv", am4charts.PieChart);
                if (chart.logo) {
                    chart.logo.disabled = true;
                }

                let filteredData = [];
                if (selectedSeverity === "All") {
                    filteredData = [
                        { "sector": `Critical (${Critical})`, "size": Critical },
                        { "sector": `High (${High})`, "size": High },
                        { "sector": `Low (${Low})`, "size": Low },
                        { "sector": `Information (${Information})`, "size": Information }
                    ];
                } else if (selectedSeverity === "High") {
                    filteredData = [{ "sector": `High (${High == 0 ? overallData : High})`, "size": High == 0 ? overallData : High }];
                } else if (selectedSeverity === "Low") {
                    filteredData = [{ "sector": `Low (${Low == 0 ? overallData : Low})`, "size": Low == 0 ? overallData : Low }];
                } else if (selectedSeverity === "Critical") {
                    filteredData = [{ "sector": `Critical (${Critical == 0 ? overallData : Critical})`, "size": Critical == 0 ? overallData : Critical }];
                } else if (selectedSeverity === "Information") {
                    filteredData = [{ "sector": `Information (${Information == 0 ? overallData : Information})`, "size": Information == 0 ? overallData : Information }];
                }
                // Add data
                chart.data = filteredData
                // chartRef.current = chart;
                // Add label
                chart.innerRadius = 70;
                var label = chart.seriesContainer.createChild(am4core.Label);
                label.text = "[bold]" + overallData + "[/]\n[font-size:13px]";
                label.horizontalCenter = "middle";
                label.verticalCenter = "middle";
                label.fontSize = 15;
                // Add and configure Series
                var pieSeries = chart.series.push(new am4charts.PieSeries());
                pieSeries.dataFields.value = "size";
                pieSeries.dataFields.category = "sector";
                pieSeries.labels.template.disabled = true;
                pieSeries.ticks.template.disabled = true;
                if (selectedSeverity === "All") {
                    pieSeries.colors.list = [
                        am4core.color("#ed253d"),
                        am4core.color("#ff960f"),
                        am4core.color("#00aa7f"),
                        am4core.color("#0479ff"),
                    ];
                }
                if (selectedSeverity === "High") {
                    pieSeries.colors.list = [
                        am4core.color("#ff960f")
                    ];
                }
                if (selectedSeverity === "Low") {
                    pieSeries.colors.list = [
                        am4core.color("#00aa7f")
                    ];
                }
                if (selectedSeverity === "Critical") {
                    pieSeries.colors.list = [
                        am4core.color("#ed253d")
                    ];
                }
                if (selectedSeverity === "Information") {
                    pieSeries.colors.list = [
                        am4core.color("#0479ff"),
                    ];
                }
                // Add a legend
                chart.legend = new am4charts.Legend();
                chart.legend.valueLabels.template.disabled = true;
                chart.legend.labels.template.text = "[font-size:12px {color}]{name}";
                // Position legend
                chart.legend.position = "right";
                var markerTemplate = chart.legend.markers.template;
                markerTemplate.width = 15;
                markerTemplate.height = 10;
            } else {
                $('#BtnAlertDownload').addClass("disabled");
                $("#alertChartDiv").css('text-align', 'center')
                    .html(noData);
            }
        }, 500)
    }
    $("#alertInfraValue,#alertServerityValue,#alertTypeValue,#alertStartDate,#alertEndDate").on('change', function () {
        alertTrigger()
        if ($('#alertStartDateInp').find("input[type=date]").val() == "" && $('#alertEndDateInp').find("input[type=date]").val() == "") {
            alertTriggerChart()
        }
        dataTable.ajax.reload()
        if ($('#alertStartDateInp').find("input[type=date]").val() == "" && $('#alertEndDateInp').find("input[type=date]").val()) {
            $("#alertStartDateError").text("Select the start date before end date").addClass('field-validation-error');
            return false;
        }
        else if ($('#alertEndDateInp').find("input[type=date]").val() != "" && $('#alertStartDateInp').find("input[type=date]").val() > $('#alertEndDateInp').find("input[type=date]").val()) {
            $("#alertStartDateError").text("Start date lesser than end date").addClass('field-validation-error');
            return false;
        } else if ($('#alertEndDateInp').find("input[type=date]").val() == "" || $('#alertStartDateInp').find("input[type=date]").val()) {
            $('#alertEndDateInp').find("input[type=date]").val()
            $("#alertStartDateError").text('').removeClass('field-validation-error');
            return true;
        } else {
            $("#alertStartDateError").text('').removeClass('field-validation-error');
            return true;
        }
    })
    // collapse btn style on click
    $('#alertCollapsebtn').on('click', function () {
        if (!$(this).data('clicked')) {
            $('.dataTables_scrollBody').css({ 'height': 'calc(100vh - 425px)' });
            $(this).data('clicked', true);
        }
        else {
            $('.dataTables_scrollBody').css({ 'height': 'calc(100vh - 269px)' });
            $(this).data('clicked', false);
        }
    });
    $("#alertStartDate,#alertEndDate").on("change", function () {
        if ($('#alertStartDateInp').find("input[type=date]").val() != "" && $('#alertEndDateInp').find("input[type=date]").val() != "") {
            alertTriggerChart()
        }
    })
    $("#alertStartDate,#alertEndDate").on("keypress", function (e) {
        e.preventDefault()
    })
    // collapse btn style on click end
    async function alertTriggerChart() {
        await $.ajax({
            type: "POST",
            url: RootUrl + alertUrls.listWeek,
            data: {
                startDate: $('#alertStartDateInp').find("input[type=date]").val() == "" ? "" : $('#alertStartDateInp').find("input[type=date]").val(),
                endDate: $('#alertEndDateInp').find("input[type=date]").val() == "" ? "" : $('#alertEndDateInp').find("input[type=date]").val()
            },
            dataType: "json",
            success: function (result) {
                if (result.success) {
                    alertChartList = []
                    let selectdate = []
                    var data = result.data
                    chartDatas = result.data
                    if (data.length == 0) {
                        setTimeout(() => {
                            dataTable.ajax.reload()
                        }, 2000)
                    }
                    if ($('#alertStartDateInp').find("input[type=date]").val() != "" && $('#alertEndDateInp').find("input[type=date]").val() != "") {
                        let startDate = new Date($('#alertStartDateInp').find("input[type=date]").val());
                        let endDate = new Date($('#alertEndDateInp').find("input[type=date]").val());
                        let dateArr = getDateArray(startDate, endDate);
                        dateArr?.forEach((x) => {
                            const date = x;
                            const formatter = new Intl.DateTimeFormat('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' });
                            const formattedDate = formatter.format(date);
                            let date_modify = formattedDate.split("/")
                            selectdate.push(date_modify[2] + "-" + date_modify[0] + "-" + date_modify[1])
                        })
                    }
                    if (selectdate.length == 0) {
                        data.forEach(function (x) {
                            let splitData = x.startOfWeeks.split("T")
                            dateModify = splitData[0].toString().split("-").join("-")
                            alertCount = Number(x.alertListCount)
                            let dataList = {
                                "date": dateModify,
                                "value": alertCount
                            }
                            alertChartList.push(dataList)
                        });
                    }
                    else {
                        selectdate.forEach((alldate) => {
                            data.forEach(function (x) {
                                let splitData = x.startOfWeeks.split("T")
                                dateModify = splitData[0].toString().split("-").join("-")
                                alertCount = Number(x.alertListCount)
                                if (alldate == dateModify) {
                                    let dataLists = {
                                        "date": dateModify,
                                        "value": alertCount
                                    }
                                    if ($('#alertStartDateInp').find("input[type=date]").val() != "" && $('#alertEndDateInp').find("input[type=date]").val() != "") {
                                        alertChartList.push(dataLists)
                                    }
                                }
                            });
                        })
                    }
                } else {
                    errorNotification(result)
                }
            }
        })
        // line chart start
        am4core.useTheme(am4themes_animated);
        const chartInstance = am4core.create(
            "severityLineChart",
            am4charts.XYChart
        );
        // add data
        setTimeout(() => {
            if (alertChartList.length > 0) {
                if (overallServerityChartdata.length != 0) {
                    chartInstance.data = alertChartList
                }
            } else {
                $("#severityLineChart").css('text-align', 'center')
                    .html(noData);
            }
        }, 1000)
        chartInstance.paddingRight = 20;
        if (chartInstance.logo) {
            chartInstance.logo.disabled = true;
        }
        const dateAxis = chartInstance.xAxes.push(new am4charts.DateAxis());
        dateAxis.renderer.minGridDistance = 50;
        dateAxis.renderer.grid.template.location = 0.5;
        setTimeout(() => {
            if (alertChartList.length == 1) {
                dateAxis.startLocation = 0.4;
                dateAxis.endLocation = 0.6;
            } else {
                dateAxis.startLocation = 0.5;
                dateAxis.endLocation = 0.5;
            }
        }, 1000)
        const valueAxis = chartInstance.yAxes.push(new am4charts.ValueAxis());
        const series = chartInstance.series.push(new am4charts.LineSeries());
        series.dataFields.valueY = "value";
        series.dataFields.dateX = "date";
        series.stroke = am4core.color("#fe7699");
        series.strokeWidth = 3;
        series.fill = am4core.color("#ffdbdf");
        series.fillOpacity = 0.7;
        series.tensionX = 0.8;
        series.tooltipText = "{dateX} : {valueY}";
        // Create a bullet for data points
        let bullet = series.bullets.push(new am4charts.CircleBullet());
        bullet.circle.strokeWidth = 2;
        bullet.circle.radius = 5;
        bullet.circle.fill = am4core.color("#fe7699");
        chartInstance.cursor = new am4charts.XYCursor();
        series.propertyFields.fill = "color";
        series.propertyFields.stroke = "color";
        series.propertyFields.strokeDasharray = "dash";
        // Zoom in and Zoom out 
        let buttonContainer = chartInstance.plotContainer.createChild(am4core.Container);
        buttonContainer.shouldClone = false;
        buttonContainer.align = "right";
        buttonContainer.valign = "top";
        buttonContainer.zIndex = Number.MAX_SAFE_INTEGER;
        buttonContainer.marginTop = 5;
        buttonContainer.marginRight = 5;
        buttonContainer.layout = "horizontal";
        let zoomInButton = buttonContainer.createChild(am4core.Button);
        zoomInButton.label.text = "+";
        zoomInButton.events.on("hit", function (ev) {
            let diff = dateAxis.maxZoomed - dateAxis.minZoomed;
            let delta = diff * 0.2;
            dateAxis.zoomToDates(new Date(dateAxis.minZoomed + delta), new Date(dateAxis.maxZoomed - delta));
        });
        let zoomOutButton = buttonContainer.createChild(am4core.Button);
        zoomOutButton.label.text = "-";
        zoomOutButton.events.on("hit", function (ev) {
            let diff = dateAxis.maxZoomed - dateAxis.minZoomed;
            let delta = diff * 0.2;
            dateAxis.zoomToDates(new Date(dateAxis.minZoomed - delta), new Date(dateAxis.maxZoomed + delta));

        });
    }
    let getDateArray = function (start, end) {
        let arr = new Array();
        let dt = new Date(start);
        while (dt <= end) {
            arr.push(new Date(dt));
            dt.setDate(dt.getDate() + 1);
        }
        return arr;
    }
})

