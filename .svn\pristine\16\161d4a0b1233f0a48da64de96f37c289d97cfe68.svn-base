﻿using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ReportScheduleModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ReportSchedule.Queries
{
    public class GetReportSchedulePaginatedListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
        private readonly GetReportSchedulePaginatedListQueryHandler _handler;

        public GetReportSchedulePaginatedListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
            _handler = new GetReportSchedulePaginatedListQueryHandler(
                _mockMapper.Object,
                _mockReportScheduleRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenDataExists()
        {
            var query = new GetReportSchedulePaginatedListQuery
            {
                SearchString = "Test Report",
                PageNumber = 1,
                PageSize = 10
            };

            var reportSchedules = new List<Domain.Entities.ReportSchedule>
            {
                new Domain.Entities.ReportSchedule {  },
                new Domain.Entities.ReportSchedule {  }
            };

            var paginatedResult = new PaginatedResult<Domain.Entities.ReportSchedule>
            {
                Data = reportSchedules,
                TotalCount = reportSchedules.Count
            };

            _mockReportScheduleRepository.Setup(r => r.PaginatedListAllAsync())
                .Returns(ToString);

            _mockMapper.Setup(m => m.Map<ReportScheduleListVm>(It.IsAny<Domain.Entities.ReportSchedule>()))
                .Returns(new ReportScheduleListVm {  });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(reportSchedules.Count, result.TotalCount);
            Assert.Equal(reportSchedules.Count, result.Data.Count);

            _mockReportScheduleRepository.Verify(r => r.PaginatedListAllAsync(), Times.Once);

            _mockMapper.Verify(m => m.Map<ReportScheduleListVm>(It.IsAny<Domain.Entities.ReportSchedule>()), Times.Exactly(reportSchedules.Count));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoDataExists()
        {
            var query = new GetReportSchedulePaginatedListQuery
            {
                SearchString = "Non-existent Report",
                PageNumber = 1,
                PageSize = 10
            };

            var paginatedResult = new PaginatedResult<Domain.Entities.ReportSchedule>
            {
                Data = new List<Domain.Entities.ReportSchedule>(),
                TotalCount = 0
            };

            _mockReportScheduleRepository.Setup(r => r.PaginatedListAllAsync())
                .Returns(ToString);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);

            _mockReportScheduleRepository.Verify(r => r.PaginatedListAllAsync(), Times.Once);

            _mockMapper.Verify(m => m.Map<ReportScheduleListVm>(It.IsAny<Domain.Entities.ReportSchedule>()), Times.Never);
        }
    }
}
