﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.Update;

namespace ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;

public class
    UpdatePageSolutionMappingCommandHandler : IRequestHandler<UpdatePageSolutionMappingCommand,
        UpdatePageSolutionMappingResponse>
{
    private readonly IMapper _mapper;
    private readonly IPageSolutionMappingRepository _pageSolutionMappingRepository;
    private readonly IPublisher _publisher;

    public UpdatePageSolutionMappingCommandHandler(IMapper mapper,
        IPageSolutionMappingRepository pageSolutionMappingRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _pageSolutionMappingRepository = pageSolutionMappingRepository;
        _publisher = publisher;
    }

    public async Task<UpdatePageSolutionMappingResponse> Handle(UpdatePageSolutionMappingCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _pageSolutionMappingRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.PageSolutionMapping), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdatePageSolutionMappingCommand),
            typeof(Domain.Entities.PageSolutionMapping));

        await _pageSolutionMappingRepository.UpdateAsync(eventToUpdate);

        var response = new UpdatePageSolutionMappingResponse
        {
            Message = Message.Update(nameof(Domain.Entities.PageSolutionMapping), eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new PageSolutionMappingUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}