﻿using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class LicenseHistoryControllerTests
    {
        private readonly LicenseHistoryController _controller;

        public LicenseHistoryControllerTests()
        {
            _controller = new LicenseHistoryController();
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void List_ShouldReturnViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithNoModel()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Model);
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithDefaultViewName()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.ViewName); // Default view name (null means it uses the action name)
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithNoTempData()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(_controller.TempData);
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithNoViewData()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.ViewData);
            Assert.Empty(result.ViewData);
        }

        [Fact]
        public void List_ShouldReturnViewResult_MultipleCallsReturnSameType()
        {
            // Act
            var result1 = _controller.List();
            var result2 = _controller.List();

            // Assert
            Assert.IsType<ViewResult>(result1);
            Assert.IsType<ViewResult>(result2);
        }

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(LicenseHistoryController);

            // Act
            var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldInheritFromBaseController()
        {
            // Arrange
            var controllerType = typeof(LicenseHistoryController);

            // Act & Assert
            Assert.True(controllerType.BaseType?.Name == "BaseController");
        }

        [Fact]
        public void List_Method_ShouldBePublic()
        {
            // Arrange
            var methodInfo = typeof(LicenseHistoryController).GetMethod("List");

            // Act & Assert
            Assert.NotNull(methodInfo);
            Assert.True(methodInfo.IsPublic);
        }

        [Fact]
        public void List_Method_ShouldReturnIActionResult()
        {
            // Arrange
            var methodInfo = typeof(LicenseHistoryController).GetMethod("List");

            // Act & Assert
            Assert.NotNull(methodInfo);
            Assert.Equal(typeof(IActionResult), methodInfo.ReturnType);
        }

        [Fact]
        public void List_Method_ShouldHaveNoParameters()
        {
            // Arrange
            var methodInfo = typeof(LicenseHistoryController).GetMethod("List");

            // Act & Assert
            Assert.NotNull(methodInfo);
            Assert.Empty(methodInfo.GetParameters());
        }

        [Fact]
        public void Controller_ShouldHaveParameterlessConstructor()
        {
            // Arrange
            var controllerType = typeof(LicenseHistoryController);

            // Act
            var constructors = controllerType.GetConstructors();
            var parameterlessConstructor = constructors.FirstOrDefault(c => c.GetParameters().Length == 0);

            // Assert
            Assert.NotNull(parameterlessConstructor);
            Assert.True(parameterlessConstructor.IsPublic);
        }

        [Fact]
        public void Controller_ShouldBeInstantiable()
        {
            // Act & Assert
            Assert.NotNull(_controller);
            Assert.IsType<LicenseHistoryController>(_controller);
        }

        [Fact]
        public void List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithCorrectControllerContext()
        {
            // Arrange
            var httpContext = new DefaultHttpContext();
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal(httpContext, _controller.HttpContext);
        }

        [Fact]
        public void List_ShouldReturnViewResult_WithStatusCode200()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.StatusCode); // ViewResult doesn't set status code explicitly, defaults to 200
        }
    }
}
