﻿using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BusinessServiceAvailabilityFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<BusinessServiceAvailability> BusinessServiceAvailabilities { get; set; }

    public CreateBusinessServiceAvailabilityCommand CreateBusinessServiceAvailabilityCommand { get; set; }

    public UpdateBusinessServiceAvailabilityCommand UpdateBusinessServiceAvailabilityCommand { get; set; }

    public BusinessServiceAvailabilityFixture()
    {
        BusinessServiceAvailabilities = AutoBusinessServiceAvailabilityFixture.Create<List<BusinessServiceAvailability>>();

        CreateBusinessServiceAvailabilityCommand = AutoBusinessServiceAvailabilityFixture.Create<CreateBusinessServiceAvailabilityCommand>();

        UpdateBusinessServiceAvailabilityCommand = AutoBusinessServiceAvailabilityFixture.Create<UpdateBusinessServiceAvailabilityCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BusinessServiceAvailabilityProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBusinessServiceAvailabilityFixture
    {
        get
        {
            var fixture = new Fixture();
           
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBusinessServiceAvailabilityCommand>(p => p.Id, 10));
            fixture.Customize<UpdateBusinessServiceAvailabilityCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<BusinessServiceAvailability>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
