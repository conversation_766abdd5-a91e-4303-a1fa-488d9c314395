﻿namespace ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetTypeByDatabaseIdAndReplicationId;

public class GetTypeByDatabaseIdAndReplicationIdVm
{
    public string Id { get; set; }

    public string DatabaseId { get; set; }

    public string DatabaseName { get; set; }

    public string ReplicationMasterId { get; set; }

    public string ReplicationMasterName { get; set; }

    public string Properties { get; set; }

    public string Type { get; set; }
}