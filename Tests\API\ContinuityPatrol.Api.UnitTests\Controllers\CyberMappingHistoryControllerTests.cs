using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberMappingHistory.Queries.GetCyberMappingHistoryById;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberMappingHistoryControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberMappingHistoryController _controller;
    private readonly CyberMappingHistoryFixture _cyberMappingHistoryFixture;

    public CyberMappingHistoryControllerTests()
    {
        _cyberMappingHistoryFixture = new CyberMappingHistoryFixture();

        var testBuilder = new ControllerTestBuilder<CyberMappingHistoryController>();
        _controller = testBuilder.CreateController(
            _ => new CyberMappingHistoryController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();

        var expectedHistoryList = new List<CyberMappingHistoryIdVm>
        {
            _cyberMappingHistoryFixture.CyberMappingHistoryIdVm,
            _cyberMappingHistoryFixture.CyberMappingHistoryIdVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(expectedHistoryList);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberMappingHistoryList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Equal(2, cyberMappingHistoryList.Count);
        Assert.Equal(_cyberMappingHistoryFixture.CyberMappingHistoryIdVm.CyberComponentMappingName, cyberMappingHistoryList.First().CyberComponentMappingName);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.CyberComponentMappingHistoryById(invalidId));
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesNotFound()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ThrowsAsync(new NotFoundException("CyberMappingHistory", cyberComponentMappingId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.CyberComponentMappingHistoryById(cyberComponentMappingId));
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesComplexHistoryEntry()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var complexHistoryList = new List<CyberMappingHistoryIdVm>
        {
            _cyberMappingHistoryFixture.CyberMappingHistoryIdVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(complexHistoryList);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberMappingHistoryList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(cyberMappingHistoryList);
        var cyberMappingHistory = cyberMappingHistoryList.First();
        Assert.Equal(_cyberMappingHistoryFixture.CyberMappingHistoryIdVm.CyberComponentMappingName, cyberMappingHistory.CyberComponentMappingName);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesEmptyHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(new List<CyberMappingHistoryIdVm>());

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Empty(historyList);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_ValidatesGuidFormat()
    {
        // Arrange
        var validGuid = Guid.NewGuid().ToString();
        var historyList = new List<CyberMappingHistoryIdVm>
        {
            _cyberMappingHistoryFixture.CyberMappingHistoryIdVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == validGuid), default))
            .ReturnsAsync(historyList);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(validGuid);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberMappingHistoryList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.NotEmpty(cyberMappingHistoryList);
        Assert.Single(cyberMappingHistoryList);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesMultipleHistoryEntries()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var multipleHistoryEntries = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Mapping v1.0",
                Properties = "{\"version\":\"1.0\",\"changes\":[\"initial_setup\"],\"timestamp\":\"2024-01-01T10:00:00Z\"}",
                Comments = "Initial mapping configuration"
            },
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Mapping v2.0",
                Properties = "{\"version\":\"2.0\",\"changes\":[\"security_enhancement\",\"performance_optimization\"],\"timestamp\":\"2024-01-15T14:30:00Z\"}",
                Comments = "Security and performance improvements"
            },
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Mapping v3.0",
                Properties = "{\"version\":\"3.0\",\"changes\":[\"compliance_update\",\"new_features\"],\"timestamp\":\"2024-02-01T09:15:00Z\"}",
                Comments = "Compliance updates and new feature additions"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(multipleHistoryEntries);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Equal(3, historyList.Count);
        Assert.Contains(historyList, h => h.Properties.Contains("\"version\":\"1.0\""));
        Assert.Contains(historyList, h => h.Properties.Contains("\"version\":\"2.0\""));
        Assert.Contains(historyList, h => h.Properties.Contains("\"version\":\"3.0\""));
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesAuditTrailHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var auditTrailHistory = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Security Mapping Audit Trail",
                Properties = "{\"auditTrail\":{\"action\":\"security_review\",\"findings\":[{\"severity\":\"high\",\"issue\":\"encryption_weakness\",\"remediation\":\"upgrade_to_aes256\"},{\"severity\":\"medium\",\"issue\":\"access_control\",\"remediation\":\"implement_rbac\"}],\"compliance\":{\"standards\":[\"SOX\",\"PCI-DSS\",\"GDPR\"],\"status\":\"non_compliant\",\"required_actions\":[\"encryption_upgrade\",\"access_control_enhancement\",\"audit_logging_improvement\"]},\"timeline\":{\"review_start\":\"2024-01-10T08:00:00Z\",\"review_end\":\"2024-01-12T17:00:00Z\",\"remediation_deadline\":\"2024-02-10T23:59:59Z\"}}}",
                Comments = "Comprehensive security audit identified critical vulnerabilities requiring immediate remediation to achieve compliance with enterprise security standards."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(auditTrailHistory);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(historyList);
        var auditEntry = historyList.First();
        Assert.Contains("security_review", auditEntry.Properties);
        Assert.Contains("encryption_weakness", auditEntry.Properties);
        Assert.Contains("SOX", auditEntry.Properties);
        Assert.Contains("<EMAIL>", auditEntry.UserName);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesPerformanceOptimizationHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var performanceHistory = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Performance Optimization Mapping",
                Properties = "{\"performanceOptimization\":{\"baseline\":{\"response_time\":\"2.5s\",\"throughput\":\"1000 tps\",\"cpu_utilization\":\"75%\",\"memory_usage\":\"8GB\"},\"optimizations\":[{\"type\":\"caching\",\"implementation\":\"redis_cluster\",\"improvement\":\"40% response time reduction\"},{\"type\":\"load_balancing\",\"implementation\":\"nginx_plus\",\"improvement\":\"60% throughput increase\"},{\"type\":\"database_tuning\",\"implementation\":\"index_optimization\",\"improvement\":\"30% query performance boost\"}],\"results\":{\"response_time\":\"1.5s\",\"throughput\":\"1600 tps\",\"cpu_utilization\":\"60%\",\"memory_usage\":\"6GB\"},\"testing\":{\"load_test_duration\":\"4 hours\",\"concurrent_users\":\"5000\",\"success_rate\":\"99.9%\",\"error_rate\":\"0.1%\"}}}",
                Comments = "Comprehensive performance optimization achieved significant improvements in response time, throughput, and resource utilization while maintaining system stability and reliability."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(performanceHistory);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(historyList);
        var performanceEntry = historyList.First();
        Assert.Contains("performanceOptimization", performanceEntry.Properties);
        Assert.Contains("redis_cluster", performanceEntry.Properties);
        Assert.Contains("1.5s", performanceEntry.Properties);
        Assert.Contains("<EMAIL>", performanceEntry.UserName);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesDisasterRecoveryHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var drHistory = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Disaster Recovery Mapping Configuration",
                Properties = "{\"disasterRecovery\":{\"primary_site\":{\"location\":\"New York Data Center\",\"capacity\":\"100%\",\"status\":\"active\"},\"dr_site\":{\"location\":\"Chicago Data Center\",\"capacity\":\"100%\",\"status\":\"standby\"},\"replication\":{\"method\":\"synchronous\",\"frequency\":\"real-time\",\"lag\":\"<100ms\",\"consistency\":\"strong\"},\"failover\":{\"rto\":\"15 minutes\",\"rpo\":\"0 minutes\",\"automation\":\"full\",\"testing\":\"monthly\"},\"components\":[{\"name\":\"database_cluster\",\"replication\":\"synchronous\",\"failover_time\":\"5 minutes\"},{\"name\":\"application_servers\",\"replication\":\"asynchronous\",\"failover_time\":\"10 minutes\"},{\"name\":\"load_balancers\",\"replication\":\"configuration_sync\",\"failover_time\":\"2 minutes\"}],\"testing\":{\"last_test\":\"2024-01-15T02:00:00Z\",\"result\":\"successful\",\"actual_rto\":\"12 minutes\",\"actual_rpo\":\"0 minutes\"}}}",
                Comments = "Disaster recovery configuration updated to meet enhanced RTO/RPO requirements with full automation and monthly testing validation."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(drHistory);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(historyList);
        var drEntry = historyList.First();
        Assert.Contains("disasterRecovery", drEntry.Properties);
        Assert.Contains("15 minutes", drEntry.Properties);
        Assert.Contains("synchronous", drEntry.Properties);
        Assert.Contains("<EMAIL>", drEntry.UserName);
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesLargeHistoryDataset()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var largeHistoryDataset = new List<CyberMappingHistoryIdVm>();

        for (int i = 0; i < 100; i++)
        {
            largeHistoryDataset.Add(new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = $"user{i}@enterprise.com",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = $"Enterprise Mapping History Entry {i + 1}",
                Properties = $"{{\"version\":\"{i + 1}.0\",\"change_type\":\"incremental\",\"timestamp\":\"{DateTime.Now.AddDays(-i):yyyy-MM-ddTHH:mm:ssZ}\"}}",
                Comments = $"History entry {i + 1} - incremental update"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(largeHistoryDataset);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Equal(100, historyList.Count);
        Assert.Contains(historyList, h => h.UserName == "<EMAIL>");
        Assert.Contains(historyList, h => h.UserName == "<EMAIL>");
        Assert.All(historyList, h => Assert.Contains("incremental", h.Properties));
    }

    [Fact]
    public void ClearDataCache_ClearsExpectedCacheKeys()
    {
        // Arrange & Act
        _controller.ClearDataCache();

        // Assert
        // Note: Since ClearDataCache is a void method that calls protected methods,
        // we verify it doesn't throw exceptions and completes successfully
        Assert.True(true); // Test passes if no exception is thrown
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesComplianceAuditHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var complianceAuditHistory = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Compliance Audit Mapping History",
                Properties = "{\"complianceAudit\":{\"audit_type\":\"annual_compliance_review\",\"standards\":[\"SOX\",\"PCI-DSS\",\"GDPR\",\"HIPAA\",\"ISO27001\"],\"findings\":[{\"severity\":\"critical\",\"category\":\"data_encryption\",\"issue\":\"Encryption at rest not implemented for sensitive data stores\",\"remediation\":\"Implement AES-256 encryption for all sensitive data repositories\",\"deadline\":\"2024-03-01\"},{\"severity\":\"high\",\"category\":\"access_control\",\"issue\":\"Privileged access not properly monitored\",\"remediation\":\"Deploy privileged access management solution with session recording\",\"deadline\":\"2024-02-15\"},{\"severity\":\"medium\",\"category\":\"network_security\",\"issue\":\"Network segmentation incomplete\",\"remediation\":\"Complete micro-segmentation implementation\",\"deadline\":\"2024-04-01\"}],\"compliance_score\":{\"overall\":\"72%\",\"data_protection\":\"65%\",\"access_management\":\"78%\",\"network_security\":\"75%\",\"monitoring\":\"80%\"},\"recommendations\":[\"Implement zero-trust architecture\",\"Enhance data loss prevention controls\",\"Upgrade security monitoring capabilities\",\"Conduct quarterly penetration testing\"],\"next_audit\":\"2025-01-15\"}}",
                Comments = "Annual compliance audit identified critical security gaps requiring immediate remediation to maintain regulatory compliance and enterprise security posture."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(complianceAuditHistory);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(historyList);
        var auditEntry = historyList.First();
        Assert.Contains("complianceAudit", auditEntry.Properties);
        Assert.Contains("annual_compliance_review", auditEntry.Properties);
        Assert.Contains("SOX", auditEntry.Properties);
        Assert.Contains("PCI-DSS", auditEntry.Properties);
        Assert.Contains("<EMAIL>", auditEntry.UserName);
        Assert.Contains("compliance audit", auditEntry.Comments.ToLower());
    }

    [Fact]
    public async Task CyberComponentMappingHistoryById_HandlesCloudMigrationHistory()
    {
        // Arrange
        var cyberComponentMappingId = Guid.NewGuid().ToString();
        var cloudMigrationHistory = new List<CyberMappingHistoryIdVm>
        {
            new CyberMappingHistoryIdVm
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                CyberComponentMappingId = cyberComponentMappingId,
                CyberComponentMappingName = "Enterprise Cloud Migration Mapping Configuration",
                Properties = "{\"cloudMigration\":{\"migration_type\":\"hybrid_cloud_transformation\",\"source_environment\":{\"type\":\"on_premises\",\"location\":\"Enterprise Data Center\",\"infrastructure\":{\"servers\":150,\"storage\":\"500TB\",\"network\":\"10Gbps\"},\"applications\":[\"ERP\",\"CRM\",\"Email\",\"File_Sharing\",\"Database\"]},\"target_environment\":{\"type\":\"hybrid_cloud\",\"providers\":[{\"name\":\"AWS\",\"region\":\"us-east-1\",\"services\":[\"EC2\",\"RDS\",\"S3\",\"VPC\"]},{\"name\":\"Azure\",\"region\":\"East US\",\"services\":[\"Virtual Machines\",\"SQL Database\",\"Blob Storage\",\"Virtual Network\"]}]},\"migration_phases\":[{\"phase\":1,\"name\":\"Assessment and Planning\",\"duration\":\"8 weeks\",\"status\":\"completed\"},{\"phase\":2,\"name\":\"Infrastructure Setup\",\"duration\":\"6 weeks\",\"status\":\"completed\"},{\"phase\":3,\"name\":\"Application Migration\",\"duration\":\"12 weeks\",\"status\":\"in_progress\"},{\"phase\":4,\"name\":\"Testing and Validation\",\"duration\":\"4 weeks\",\"status\":\"planned\"},{\"phase\":5,\"name\":\"Go-Live and Optimization\",\"duration\":\"2 weeks\",\"status\":\"planned\"}],\"benefits\":{\"cost_savings\":\"35%\",\"scalability\":\"unlimited\",\"availability\":\"99.9%\",\"disaster_recovery\":\"automated\"}}}",
                Comments = "Enterprise cloud migration project progressing successfully with hybrid cloud architecture providing optimal balance of performance, security, and cost efficiency."
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberMappingHistoryByIdQuery>(q => q.CyberComponentMappingId == cyberComponentMappingId), default))
            .ReturnsAsync(cloudMigrationHistory);

        // Act
        var result = await _controller.CyberComponentMappingHistoryById(cyberComponentMappingId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var historyList = Assert.IsType<List<CyberMappingHistoryIdVm>>(okResult.Value);
        Assert.Single(historyList);
        var migrationEntry = historyList.First();
        Assert.Contains("cloudMigration", migrationEntry.Properties);
        Assert.Contains("hybrid_cloud_transformation", migrationEntry.Properties);
        Assert.Contains("AWS", migrationEntry.Properties);
        Assert.Contains("Azure", migrationEntry.Properties);
        Assert.Contains("<EMAIL>", migrationEntry.UserName);
        Assert.Contains("cloud migration", migrationEntry.Comments.ToLower());
    }
}
