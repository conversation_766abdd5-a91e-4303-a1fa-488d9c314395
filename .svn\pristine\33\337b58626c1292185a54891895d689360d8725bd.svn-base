﻿using ContinuityPatrol.Application.Features.PluginManager.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.PluginManagerModel;

namespace ContinuityPatrol.Application.UnitTests.Features.PluginManager.Queries;

public class GetPluginManagerListQueryHandlerTests : IClassFixture<PluginManagerFixture>
{
    private readonly PluginManagerFixture _pluginManagerFixture;

    private Mock<IPluginManagerRepository> _mockPluginManagerRepository;

    private readonly GetPluginManagerListQueryHandler _handler;

    public GetPluginManagerListQueryHandlerTests(PluginManagerFixture pluginManagerFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;

        _mockPluginManagerRepository = PluginManagerRepositoryMocks.GetPluginManagerRepository(_pluginManagerFixture.PluginManagers);

        _handler = new GetPluginManagerListQueryHandler(_pluginManagerFixture.Mapper, _mockPluginManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_PluginManagersCount()
    {
        var result = await _handler.Handle(new GetPluginManagerListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<PluginManagerListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_PluginManagersList()
    {
        var result = await _handler.Handle(new GetPluginManagerListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<PluginManagerListVm>>();
        result[0].Id.ShouldBe(_pluginManagerFixture.PluginManagers[0].ReferenceId);
        result[0].Name.ShouldBe(_pluginManagerFixture.PluginManagers[0].Name);
        result[0].Properties.ShouldBe(_pluginManagerFixture.PluginManagers[0].Properties);
        result[0].Description.ShouldBe(_pluginManagerFixture.PluginManagers[0].Description);
        result[0].Version.ShouldBe(_pluginManagerFixture.PluginManagers[0].Version);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockPluginManagerRepository = PluginManagerRepositoryMocks.GetPluginManagerEmptyRepository();

        var handler = new GetPluginManagerListQueryHandler(_pluginManagerFixture.Mapper, _mockPluginManagerRepository.Object);

        var result = await handler.Handle(new GetPluginManagerListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetPluginManagerListQuery(), CancellationToken.None);

        _mockPluginManagerRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}