﻿using ContinuityPatrol.Application.Features.InfraObject.Events.DashboardViewEvent.Delete;
using ContinuityPatrol.Application.Features.InfraObject.Events.Delete;

namespace ContinuityPatrol.Application.Features.InfraObject.Commands.Delete;

public class DeleteInfraObjectCommandHandler : IRequestHandler<DeleteInfraObjectCommand, DeleteInfraObjectResponse>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IInfraObjectSchedulerRepository _infraObjectSchedulerRepository;
    private readonly IJobRepository _jobRepository;
    private readonly IPublisher _publisher;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public DeleteInfraObjectCommandHandler(IInfraObjectRepository infraObjectRepository, IPublisher publisher,
        IJobRepository jobRepository, IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IInfraObjectSchedulerRepository infraObjectSchedulerRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository)
    {
        _infraObjectRepository = infraObjectRepository;
        _publisher = publisher;
        _jobRepository = jobRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _infraObjectSchedulerRepository = infraObjectSchedulerRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
    }

    public async Task<DeleteInfraObjectResponse> Handle(DeleteInfraObjectCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "InfraObject Id");

        var job = await _jobRepository.GetJobsByInfraObjectId(request.Id);

        var workflowInfraObject =
            await _workflowInfraObjectRepository.GetWorkflowInfraObjectFromInfraObjectId(request.Id);

        var infraScheduler = await _infraObjectSchedulerRepository.GetInfraObjectSchedulerByInfraObjectId(request.Id);

        var workflowProfileInfo = await _workflowProfileInfoRepository.GetWorkflowProfileByInfraId(request.Id);

        if (job.Count > 0 || workflowInfraObject.Count > 0 || infraScheduler.Count > 0 || workflowProfileInfo.Count > 0)
            throw new InvalidException("The Infraobject is currently in use");

        var eventToDelete = await _infraObjectRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.InfraObject),
            new NotFoundException(nameof(Domain.Entities.InfraObject), request.Id));

        eventToDelete.IsActive = false;

        await _publisher.Publish(new InfraObjectDashboardViewDeletedEvent
        {
            InfraObjectId = eventToDelete.ReferenceId,
            InfraObjectName = eventToDelete.Name
        }, CancellationToken.None);


        await _infraObjectRepository.UpdateAsync(eventToDelete);

        var response = new DeleteInfraObjectResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.InfraObject), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new InfraObjectDeletedEvent { InfraObjectName = eventToDelete.Name },
            cancellationToken);

        return response;
    }
}