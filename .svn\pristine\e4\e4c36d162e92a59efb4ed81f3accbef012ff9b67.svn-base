﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Delete;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetIPAddressUnique;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetList;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetType;
using ContinuityPatrol.Application.Features.StateMonitorStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;
using ContinuityPatrol.Domain.ViewModels.StateMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class LoadBalancerService : BaseService, ILoadBalancerService
{
    public LoadBalancerService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateLoadBalancerCommand createNodeConfigurationCommand)
    {
        Logger.LogDebug($"Create NodeConfiguration '{createNodeConfigurationCommand.Name}'");

        return await Mediator.Send(createNodeConfigurationCommand);
    }

    public async Task<BaseResponse> TestConnection(
        LoadBalancerTestConnectionCommand nodeConfigurationTestConnectionCommand)
    {
        Logger.LogDebug($"Create NodeConfiguration '{nodeConfigurationTestConnectionCommand}'");

        return await Mediator.Send(nodeConfigurationTestConnectionCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateLoadBalancerCommand updateNodeConfigurationCommand)
    {
        Logger.LogDebug($"Update NodeConfiguration '{updateNodeConfigurationCommand.Name}'");

        return await Mediator.Send(updateNodeConfigurationCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "NodeConfiguration Id");

        Logger.LogDebug($"Delete NodeConfiguration Details by Id '{id}'");

        return await Mediator.Send(new DeleteLoadBalancerCommand { Id = id });
    }

    public async Task<List<LoadBalancerListVm>> GetLoadBalancerList()
    {
        Logger.LogDebug("Get All NodeConfigurations");

        return await Mediator.Send(new GetLoadBalancerListQuery());
    }

    public async Task<bool> IsLoadBalancerNameExist(string loadBalancerName, string id)
    {
        Guard.Against.NullOrWhiteSpace(loadBalancerName, "NodeConfiguration Name");

        Logger.LogDebug($"Check Name Exists Detail by NodeConfiguration Name '{loadBalancerName}' and Id '{id}'");

        return await Mediator.Send(new GetLoadBalancerNameUniqueQuery { Name = loadBalancerName, Id = id });
    }

    public async Task<LoadBalancerDetailVm> GetLoadBalancerById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "NodeConfiguration Id");

        Logger.LogDebug($"Get NodeConfiguration by Id '{id}'");

        return await Mediator.Send(new GetLoadBalancerDetailQuery { Id = id });
    }

    public async Task<bool> IsIpAddressAndPortExist(string ipAddress, int port, string id)
    { 
        Guard.Against.NullOrWhiteSpace(ipAddress, "IPAddress");
 

        Logger.LogDebug($"Check IPAddress and Port Exists Detail by IPAddress '{ipAddress}' and Port '{port}' and Id '{id}'");

        return await Mediator.Send(new GetIPAddressUniqueQuery { IPAddress = ipAddress, Port = port, Id = id });
    }

    public async Task<PaginatedResult<LoadBalancerListVm>> GetPaginatedNodeConfigurations(
        GetLoadBalancerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in NodeConfiguration Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<List<StateMonitorStatusListVm>> GetStateMonitorStatusList()
    {
        Logger.LogDebug("Get All stateMonitor Status.");

        return await Mediator.Send(new GetStateMonitorStatusListQuery());
    }

    public async Task<UpdateNodeStatusResponse> UpdateNodeStatus(UpdateNodeStatusCommand command)
    {
        Logger.LogDebug($"Update Node status '{command.Name}'");

        return await Mediator.Send(command);
    }

    public async Task<UpdateLoadBalancerDefaultResponse> UpdateDefault(UpdateLoadBalancerDefaultCommand command)
    {
        Logger.LogDebug($"Update default '{command.Name}'");

        return await Mediator.Send(command);
    }

    public async Task<List<LoadBalancerNameVm>> GetLoadBalancerNamesByType(string? type=null)
    {
        Logger.LogDebug("Get LoadBalancer Node Name.");

        return await Mediator.Send(new LoadBalancerNameQuery());
    }
}