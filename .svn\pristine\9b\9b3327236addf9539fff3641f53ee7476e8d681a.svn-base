﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="AutoMapper" Version="14.0.0" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
	  <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
	  <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
	  <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
	  <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Core\ContinuityPatrol.Domain\ContinuityPatrol.Domain.csproj" />
		<ProjectReference Include="..\..\Shared\ContinuityPatrol.Shared.Infrastructure\ContinuityPatrol.Shared.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="Images\AccountLock\abstract.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\AccountLock\account_locked.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\AccountLock\cp_logo.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ActionFail\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ActionFail\Action_fail.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ActionFail\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\Approval\abstract.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\Approval\approval_matrix_approved.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\Approval\cp_logo.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatabaseDown\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatabaseDown\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatabaseDown\Database_Down.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatalagExceed\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatalagExceed\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\DatalagExceed\DataLag_Exceed.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\DRCalendar\abstract.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\DRCalendar\cp_logo.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\DRCalendar\Drill-Activity.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\company_name.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\forgot_password1.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\password.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\username.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ForgotPassword\User_created.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\Reject\abstract.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\Reject\approval_matrix_rejected.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\Reject\cp_logo.png">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerDown\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerDown\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerDown\Server_Down.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerUP\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerUP\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\ServerUP\Server_UP.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\TestEmail\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\TestEmail\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\TestEmail\Test_Mail.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\company_name.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\password.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\username.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserCreate\User_created.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserUnLock\abstract.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserUnLock\cp_logo.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Images\UserUnLock\unlocked_image.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\Verify\abstract.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\Verify\confim_delete.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\Verify\cp_logo.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Images\Verify\password.png">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>