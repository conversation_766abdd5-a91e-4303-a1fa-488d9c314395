using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraMasterFixture : IDisposable
{
    public List<InfraMaster> InfraMasterPaginationList { get; set; }
    public List<InfraMaster> InfraMasterList { get; set; }
    public InfraMaster InfraMasterDto { get; set; }

    public const string InfraMasterName = "Test Infrastructure Master";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraMasterFixture()
    {
        var fixture = new Fixture();

        InfraMasterList = fixture.Create<List<InfraMaster>>();

        InfraMasterPaginationList = fixture.CreateMany<InfraMaster>(20).ToList();

        // Setup proper test data for InfraMasterPaginationList
        InfraMasterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraMasterPaginationList.ForEach(x => x.IsActive = true);
        InfraMasterPaginationList.ForEach(x => x.Name = $"Infrastructure Master {Guid.NewGuid().ToString()[..8]}");

        // Setup proper test data for InfraMasterList
        InfraMasterList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraMasterList.ForEach(x => x.IsActive = true);
        InfraMasterList.ForEach(x => x.Name = $"Infrastructure Master {Guid.NewGuid().ToString()[..8]}");

        InfraMasterDto = fixture.Create<InfraMaster>();
        InfraMasterDto.ReferenceId = Guid.NewGuid().ToString();
        InfraMasterDto.IsActive = true;
        InfraMasterDto.Name = InfraMasterName;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
