﻿using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.Features.Node.Commands.Update;
using ContinuityPatrol.Application.Features.Node.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class NodeService : BaseClient, INodeService
{
    public NodeService(IConfiguration config, IAppCache cacheService, ILogger<NodeService> logger) : base(config, cacheService, logger)
    {
    }

    public async Task<List<NodeNameVm>> GetNodeNames()
    {
        var request = new RestRequest("api/v6/nodes/names");

        return await Get<List<NodeNameVm>>(request);
    }

    public async Task<List<NodeListVm>> GetNodeList()
    {
        var request = new RestRequest("api/v6/nodes");

        return await Get<List<NodeListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateNodeCommand createNodeCommand)
    {
        var request = new RestRequest("api/v6/nodes", Method.Post);

        request.AddJsonBody(createNodeCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateNodeCommand updateNodeCommand)
    {
        var request = new RestRequest("api/v6/nodes", Method.Put);

        request.AddJsonBody(updateNodeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string nodeId)
    {
        var request = new RestRequest($"api/v6/nodes/{nodeId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<NodeDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/nodes/{id}");

        return await Get<NodeDetailVm>(request);
    }

    public async Task<bool> IsNodeNameExist(string nodeName, string? id)
    {
        var request = new RestRequest($"api/v6/nodes/name-exist?nodeName={nodeName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<PaginatedResult<NodeListVm>> GetPaginatedNodes(GetNodePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/nodes/paginated-list");

        return await Get<PaginatedResult<NodeListVm>>(request);
    }
}