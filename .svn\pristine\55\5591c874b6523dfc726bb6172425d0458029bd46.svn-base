﻿using ContinuityPatrol.Domain.ViewModels.UserActivityModel;

namespace ContinuityPatrol.Application.Features.UserActivity.Queries.GetLoginName;

public class
    GetUserActivityLoginNameQueryHandler : IRequestHandler<GetUserActivityLoginNameQuery, List<UserActivityLoginNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserActivityRepository _userActivityRepository;

    public GetUserActivityLoginNameQueryHandler(IMapper mapper, IUserActivityRepository userActivityRepository)
    {
        _mapper = mapper;
        _userActivityRepository = userActivityRepository;
    }

    public async Task<List<UserActivityLoginNameVm>> Handle(GetUserActivityLoginNameQuery request,
        CancellationToken cancellationToken)
    {
        var userActivitiesLoginName = request.LoginName.IsNotNullOrWhiteSpace()
            ? await _userActivityRepository.GetUserActivityByLoginName(request.LoginName)
            : await _userActivityRepository.ListAllAsync();


        //var userActivitiesLoginName = (await _userActivityRepository.ListAllAsync())
        //    .Where(x => x.LoginName.Equals(request.LoginName, StringComparison.CurrentCultureIgnoreCase))
        //    .OrderByDescending(x => x.Id).ToList();

        return userActivitiesLoginName.Count <= 0
            ? new List<UserActivityLoginNameVm>()
            : _mapper.Map<List<UserActivityLoginNameVm>>(userActivitiesLoginName);
    }
}