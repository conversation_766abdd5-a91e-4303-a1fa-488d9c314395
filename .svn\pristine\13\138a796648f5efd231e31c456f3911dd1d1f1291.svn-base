using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MonitorServiceFixture : IDisposable
{
    public List<MonitorService> MonitorServicePaginationList { get; set; }
    public List<MonitorService> MonitorServiceList { get; set; }
    public MonitorService MonitorServiceDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public MonitorServiceFixture()
    {
        var fixture = new Fixture();

        MonitorServiceList = fixture.Create<List<MonitorService>>();

        MonitorServicePaginationList = fixture.CreateMany<MonitorService>(20).ToList();

        MonitorServiceDto = fixture.Create<MonitorService>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
