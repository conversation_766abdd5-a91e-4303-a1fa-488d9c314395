﻿using ContinuityPatrol.Application.Features.MonitorService.Command.Create;
using ContinuityPatrol.Application.Features.MonitorService.Command.Update;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MonitorServicesService : BaseClient, IMonitorServicesService
{
    public MonitorServicesService(IConfiguration config, IAppCache cache, ILogger<MonitorServicesService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<List<MonitorServiceListVm>> GetMonitorServices()
    {
        var request = new RestRequest("api/v6/monitorservice");

        return await GetFromCache<List<MonitorServiceListVm>>(request, "GetMonitorServices");
    }

    public async Task<GetMonitorServiceDetailVm> GetMonitorServiceById(string id)
    {
        var request = new RestRequest($"api/v6/monitorservice/{id}");

        return await Get<GetMonitorServiceDetailVm>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateMonitorServiceCommand createMonitorServiceCommand)
    {
        var request = new RestRequest("api/v6/monitorservice", Method.Post);

        request.AddJsonBody(createMonitorServiceCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMonitorServiceCommand updateMonitorServiceCommand)
    {
        var request = new RestRequest("api/v6/monitorservice", Method.Put);

        request.AddJsonBody(updateMonitorServiceCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/monitorservice/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    //public async Task<PaginatedResult<GetMonitorServicePaginatedListVm>> GetPaginatedMonitorServices(GetMonitorServicePaginatedListQuery query)
    //{
    //    var request = new RestRequest("api/v6/monitorservice/paginated-list=");

    //    return await Get<PaginatedResult<GetMonitorServicePaginatedListVm>>(request);
    //}
}