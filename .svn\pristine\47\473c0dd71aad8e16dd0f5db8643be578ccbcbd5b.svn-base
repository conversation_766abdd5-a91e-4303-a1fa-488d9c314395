using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftCategoryMasterFixture : IDisposable
{
    public List<DriftCategoryMaster> DriftCategoryMasterPaginationList { get; set; }
    public List<DriftCategoryMaster> DriftCategoryMasterList { get; set; }
    public DriftCategoryMaster DriftCategoryMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string CategoryName = "TestCategory";
    public const string Description = "Test Category Description";
    public const string CategoryType = "Standard";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftCategoryMasterFixture()
    {
        var fixture = new Fixture();

        DriftCategoryMasterList = fixture.Create<List<DriftCategoryMaster>>();

        DriftCategoryMasterPaginationList = fixture.CreateMany<DriftCategoryMaster>(20).ToList();

        DriftCategoryMasterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftCategoryMasterPaginationList.ForEach(x => x.IsActive = true);
       
        DriftCategoryMasterList.ForEach(x => x.IsActive = true);
     

        DriftCategoryMasterDto = fixture.Create<DriftCategoryMaster>();
        DriftCategoryMasterDto.ReferenceId = Guid.NewGuid().ToString();
        DriftCategoryMasterDto.IsActive = true;
      
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
