﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class BusinessServiceHealthStatusFilterSpecification : Specification<BusinessServiceHealthStatus>
{
    public BusinessServiceHealthStatusFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ProblemState != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("problemState=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ProblemState.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.ProblemState.Contains(searchString);
            }
        }
    }
}