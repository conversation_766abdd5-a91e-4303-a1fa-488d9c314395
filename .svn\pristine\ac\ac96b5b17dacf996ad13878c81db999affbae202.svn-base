﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Org.BouncyCastle.Crypto;

namespace ContinuityPatrol.Persistence.Repositories;

public class SiteRepository : BaseRepository<Site>, ISiteRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public SiteRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<Site>> ListAllAsync()
    {
        var sites = base.QueryAll(site => site.CompanyId.Equals(_loggedInUserService.CompanyId));

        var siteDto = MapSite(sites);

        return await siteDto.ToListAsync();
    }

    public override async Task<Site> GetByReferenceIdAsync(string id)
    {
        var sites = base.GetByReferenceId(id,
            site => site.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                    site.ReferenceId.Equals(id));

        var siteDto = MapSite(sites);

        return await siteDto.SingleOrDefaultAsync();
    }
    public override async Task<PaginatedResult<Site>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Site> productFilterSpec, string sortColumn, string sortOrder)
    {
        return _loggedInUserService.IsParent
            ? await MapSite(SelectSite(Entities.Specify(productFilterSpec).DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder)
            : await MapSite(SelectSite(Entities.Specify(productFilterSpec)
                    .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<Site> GetPaginatedQuery()
    {
        var sites = base.QueryAll(site => site.CompanyId.Equals(_loggedInUserService.CompanyId));

        var siteDto = MapSite(sites);

        return siteDto;
    }

    public async  Task<List<Site>> GetSiteByCompanyId(string companyId)
    {
        var sites = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.CompanyId.Equals(companyId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.CompanyId.Equals(companyId));

        var site = MapSite(sites);

        return await site.ToListAsync();
    }

    public async Task<List<Site>> GetSiteBySiteType(string companyId, string siteTypeId)
    {
        var sites = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.CompanyId.Equals(companyId) && x.TypeId.Equals(siteTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.CompanyId.Equals(companyId) && x.TypeId.Equals(siteTypeId));

        var site = MapSite(sites);

        return await site.ToListAsync();
    }

    public async Task<bool> IsSiteNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
            return await _dbContext.Sites.AnyAsync(e => e.Name == name);

        return await _dbContext.Sites.AnyAsync(e => e.Name == name && e.ReferenceId != id);
    }
    public async Task<bool> IsSiteNameUnique(string name)
    {
        var exists = await _dbContext.Sites.AnyAsync(e => e.Name == name);
        return exists;
    }
    public async Task<Site> GetSitesById(string id)
    {
        return await base.FilterBy(x => x.ReferenceId.Equals(id))
            .Select(x => new Site { TypeId = x.TypeId }).FirstOrDefaultAsync();

    }

    public async Task<List<Site>> GetSitesByIds(List<string> ids)
    {
        return await (_loggedInUserService.IsParent
            ? base.FilterBy(x => ids.Contains(x.ReferenceId))
             : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && ids.Contains(x.ReferenceId)))
                .Select(x => new Site { ReferenceId = x.ReferenceId, Name = x.Name, Location = x.Location }).ToListAsync();
    }


    public async Task<List<Site>> GetSiteBySiteTypeId(string siteTypeId)
    {
        var sites = SelectSite(base.FilterBy(x => x.TypeId.Equals(siteTypeId)));

        return await sites.ToListAsync();
    }
    public async Task<List<Site>> GetSiteBySiteLocation(string siteLocation)
    {
        var sites = SelectSite(base.FilterBy(x => x.Location.Equals(siteLocation)));

        return await sites.ToListAsync();
    }
    public async Task<List<Site>> GetSiteNames()
    {
        var query = _dbContext.Sites.Active();

        if (!_loggedInUserService.IsParent)
            query = query.Where(x => x.CompanyId == _loggedInUserService.CompanyId);
        
        return await query
            .Select(x => new Site { ReferenceId = x.ReferenceId, Name = x.Name, TypeId = x.TypeId })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public override async Task<Site> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? await base.GetByIdAsync(id)
            : await base.FilterBy(site => site.Id.Equals(id) && site.CompanyId.Equals(_loggedInUserService.CompanyId)).FirstOrDefaultAsync();
    }

    public async Task<List<Site>> GetSitesBySiteTypeCategory(string category)
    {
        var siteTypes = await _dbContext.SiteTypes.Active().AsNoTracking().Where(e => e.Category.Trim().ToLower().Contains(category))
            .Select(x=>x.ReferenceId).ToListAsync();

        var result = await Entities.Active().AsNoTracking().Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && siteTypes.Contains(x.TypeId)).ToListAsync();
        
        return result;
        
    }



    private IQueryable<Site> MapSite(IQueryable<Site> sites)
    {
        return sites.Select(x => new
        {
            Company = _dbContext.Companies.Active().AsNoTracking().FirstOrDefault(comp => comp.ReferenceId.Equals(x.CompanyId)),
            Site = x,
            SiteType = _dbContext.SiteTypes.Active().AsNoTracking().FirstOrDefault(type => type.ReferenceId.Equals(x.TypeId)),
            SiteLocation = _dbContext.SiteLocations.Active().AsNoTracking().FirstOrDefault(loc => loc.ReferenceId.Equals(x.LocationId))
        })
        .Select(res => new Site
        {
            Id = res.Site.Id,
            ReferenceId = res.Site.ReferenceId,
            Name = res.Site.Name,
            LocationId = res!.Site!.LocationId ?? res.SiteLocation.ReferenceId,
            Location = res!.SiteLocation!.City ?? res.Site.Location,
            TypeId = res.SiteType.ReferenceId,
            Type = res.SiteType.Type,
            PlatformType = res.Site.PlatformType,
            CompanyId = res.Company.ReferenceId,
            CompanyName = res.Company.DisplayName,
            Lat = res.SiteLocation.Lat,
            Lng = res.SiteLocation.Lng,
            IsActive = res.Site.IsActive,
            CreatedBy = res.Site.CreatedBy,
            CreatedDate = res.Site.CreatedDate,
            LastModifiedBy = res.Site.LastModifiedBy,
            LastModifiedDate = res.Site.LastModifiedDate,
            DataTemperature = res.Site.DataTemperature
        });
    }
    private IQueryable<Site> SelectSite(IQueryable<Site> query)
    {
        return query.Select(x => new Site
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            CompanyName = x.CompanyName,
            DataTemperature = x.DataTemperature,
            Lat = x.Lat,
            Lng = x.Lng,
            Location = x.Location,
            LocationId = x.LocationId,
            Name = x.Name,
            PlatformType = x.PlatformType,
            Type = x.Type,
            TypeId = x.TypeId
        });
    }

    public async Task<Dictionary<string, List<Site>>> GetSiteGroupBySiteTypeId()
    {
        var siteGroupBySiteTypeId = await _dbContext.Sites
            .Active()
            .GroupBy(x => x.TypeId)
            .ToDictionaryAsync(x => x.Key, x => x.ToList());
            
        return siteGroupBySiteTypeId;
    }

}