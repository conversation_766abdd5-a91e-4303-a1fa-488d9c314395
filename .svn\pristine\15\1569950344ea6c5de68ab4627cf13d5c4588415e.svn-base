﻿using ContinuityPatrol.Application.Features.BusinessService.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Events;

public class DeleteBusinessServiceEventTests : IClassFixture<BusinessServiceFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly BusinessServiceDeletedEventHandler _handler;

    public DeleteBusinessServiceEventTests(BusinessServiceFixture businessServiceFixture, UserActivityFixture userActivityFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockBusinessServiceEventLogger = new Mock<ILogger<BusinessServiceDeletedEventHandler>>();

        _mockUserActivityRepository = BusinessServiceRepositoryMocks.CreateBusinessServiceEventRepository(_userActivityFixture.UserActivities);

        _handler = new BusinessServiceDeletedEventHandler(mockLoggedInUserService.Object, mockBusinessServiceEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteBusinessServiceEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_businessServiceFixture.BusinessServiceDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceFixture.BusinessServiceDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}