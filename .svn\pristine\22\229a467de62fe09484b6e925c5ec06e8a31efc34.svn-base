﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;

public class
    GetFormTypePaginatedListQueryHandler : IRequestHandler<GetFormTypePaginatedListQuery,
        PaginatedResult<FormTypeListVm>>
{
    private readonly IFormTypeRepository _formTypeRepository;
    private readonly IMapper _mapper;

    public GetFormTypePaginatedListQueryHandler(IMapper mapper, IFormTypeRepository formTypeRepository)
    {
        _mapper = mapper;
        _formTypeRepository = formTypeRepository;
    }

    public async Task<PaginatedResult<FormTypeListVm>> Handle(GetFormTypePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new FormTypeFilterSpecification(request.SearchString);

        var queryable =await _formTypeRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var formTypes = _mapper.Map<PaginatedResult<FormTypeListVm>>(queryable);
        //var queryable = _formTypeRepository.GetPaginatedQuery();

        //var productFilterSpec = new FormTypeFilterSpecification(request.SearchString);

        //var formTypes = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<FormTypeListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //await _publisher.Publish(new FormTypePaginatedViewEvent(), cancellationToken);

        return formTypes;
    }
}