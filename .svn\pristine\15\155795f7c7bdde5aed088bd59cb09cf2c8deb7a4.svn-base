using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberMappingHistoryFixture : IDisposable
{
   

    public List<CyberMappingHistory> CyberMappingHistoryPaginationList { get; set; }
    public List<CyberMappingHistory> CyberMappingHistoryList { get; set; }
    public CyberMappingHistory CyberMappingHistoryDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberMappingHistoryFixture()
    {
        var fixture = new Fixture();

        CyberMappingHistoryList = fixture.Create<List<CyberMappingHistory>>();
        CyberMappingHistoryPaginationList = fixture.CreateMany<CyberMappingHistory>(20).ToList();

        CyberMappingHistoryDto = fixture.Create<CyberMappingHistory>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
