using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FastCopyMonitorFixture : IDisposable
{
    public List<FastCopyMonitor> FastCopyMonitorPaginationList { get; set; }
    public List<FastCopyMonitor> FastCopyMonitorList { get; set; }
    public FastCopyMonitor FastCopyMonitorDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public FastCopyMonitorFixture()
    {
        var fixture = new Fixture();

        FastCopyMonitorList = fixture.Create<List<FastCopyMonitor>>();

        FastCopyMonitorPaginationList = fixture.CreateMany<FastCopyMonitor>(20).ToList();

        FastCopyMonitorPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FastCopyMonitorPaginationList.ForEach(x => x.IsActive = true);

        FastCopyMonitorList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FastCopyMonitorList.ForEach(x => x.IsActive = true);

        FastCopyMonitorDto = fixture.Create<FastCopyMonitor>();
        FastCopyMonitorDto.ReferenceId = Guid.NewGuid().ToString();
        FastCopyMonitorDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
