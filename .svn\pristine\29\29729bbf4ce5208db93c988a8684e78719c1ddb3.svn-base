using ContinuityPatrol.Application.Features.CyberComponent.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponent.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetBySiteId;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponent.Queries.GetInfrastructureSummary;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Domain.ViewModels.CyberComponentModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class CyberComponentProfile : Profile
{
    public CyberComponentProfile()
    {
        CreateMap<CyberComponent, CyberComponentListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CyberComponent, CyberComponentDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CyberComponent, CyberComponentBySiteIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<Server, ServerInfrastructureSummaryVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.IpAddress,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "IpAddress")))
            .ForMember(dest => dest.HostName,
                opt => opt.MapFrom(src => GetJsonProperties.GetJsonValue(src.Properties, "HostName")));

        CreateMap<CyberComponent, CreateCyberComponentCommand>().ReverseMap();
        CreateMap<CyberComponent, CyberComponentViewModel>().ReverseMap();

        CreateMap<CreateCyberComponentCommand, CyberComponentViewModel>().ReverseMap();
        CreateMap<UpdateCyberComponentCommand, CyberComponentViewModel>().ReverseMap();

        CreateMap<UpdateCyberComponentCommand, CyberComponent>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<CyberComponent>, PaginatedResult<CyberComponentListVm>>()
            .ForMember(x => x.Data, y => y.MapFrom(src=>src.Data));
    }
}