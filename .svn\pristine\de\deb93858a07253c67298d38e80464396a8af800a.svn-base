﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BusinessFunctionRepositoryMocks
{
    public static Mock<IBusinessFunctionRepository> CreateBusinessFunctionRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessFunctions);

        businessFunctionRepository.Setup(repo => repo.AddAsync(It.IsAny<BusinessFunction>())).ReturnsAsync(
            (BusinessFunction businessFunction) =>
            {
                businessFunction.Id = new Fixture().Create<int>();

                businessFunction.ReferenceId = new Fixture().Create<Guid>().ToString();

                businessFunctions.Add(businessFunction);

                return businessFunction;
            });

        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> UpdateBusinessFunctionRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessFunctions);

        businessFunctionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessFunctions.SingleOrDefault(x => x.ReferenceId == i));

        businessFunctionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessFunction>())).ReturnsAsync((BusinessFunction businessFunction) =>
        {
            var index = businessFunctions.FindIndex(item => item.ReferenceId == businessFunction.ReferenceId);

            businessFunctions[index] = businessFunction;

            return businessFunction;

        });
        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> DeleteBusinessFunctionRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessFunctions);

        businessFunctionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessFunctions.SingleOrDefault(x => x.ReferenceId == i));
        
        //businessFunctionRepository.Setup(repo => repo.GetBusinessFunctionListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(businessFunctions);

        businessFunctionRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessFunction>())).ReturnsAsync((BusinessFunction businessFunction) =>
        {
            var index = businessFunctions.FindIndex(item => item.ReferenceId == businessFunction.ReferenceId);

            businessFunction.IsActive = false;

            businessFunctions[index] = businessFunction;

            return businessFunction;
        });

        return businessFunctionRepository;
    }

    //public static Mock<IBusinessFunctionRepository> GetBusinessFunctionNameByBusinessServiceIdRepository(List<GetBusinessFunctionNameByBusinessServiceIdVm> businessFunctions)
    //{
    //    var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

    //    businessFunctionRepository.Setup(repo => repo.GetBusinessFunctionNameByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(businessFunctions);

    //    return businessFunctionRepository;
    //}

    public static Mock<IBusinessFunctionRepository> GetPaginatedBusinessFunctionRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        //var queryableBusinessFunction = businessFunctions.BuildMock();

        //businessFunctionRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableBusinessFunction);
        businessFunctionRepository.Setup(repo => repo.PaginatedListAllAsync(
         It.IsAny<int>(),
         It.IsAny<int>(),
         It.IsAny<Specification<BusinessFunction>>(),
         It.IsAny<string>(),
         It.IsAny<string>()))
       .ReturnsAsync((int pageNumber, int pageSize, Specification<BusinessFunction> spec, string sortColumn, string sortOrder) =>
       {
           var sortedBusinessfunction = businessFunctions.AsQueryable();

           if (spec.Criteria != null)
           {
               sortedBusinessfunction = sortedBusinessfunction.Where(spec.Criteria);
           }

           if (!string.IsNullOrWhiteSpace(sortColumn))
           {
               sortedBusinessfunction = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                 ? sortedBusinessfunction.OrderByDescending(c => c.Name)
                 : sortedBusinessfunction.OrderBy(c => c.Name);
           }

           var totalCount = sortedBusinessfunction.Count();
           var paginated = sortedBusinessfunction
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

           return PaginatedResult<BusinessFunction>.Success(paginated, totalCount, pageNumber, pageSize);
       });
        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> GetBusinessFunctionNameUniqueRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.IsBusinessFunctionNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => businessFunctions.Exists(x => x.Name == i && x.ReferenceId == j));

        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> GetBusinessFunctionNameRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.GetBusinessFunctionNames()).ReturnsAsync(businessFunctions);

        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> GetBusinessFunctionListByServiceId(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.GetBusinessFunctionListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(businessFunctions);

        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> GetBusinessFunctionRepository(List<BusinessFunction> businessFunctions)
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessFunctions);

        businessFunctionRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessFunctions.SingleOrDefault(x => x.ReferenceId == i));

        return businessFunctionRepository;
    }

    public static Mock<IBusinessFunctionRepository> GetBusinessFunctionEmptyRepository()
    {
        var businessFunctionRepository = new Mock<IBusinessFunctionRepository>();

        businessFunctionRepository.Setup(repo => repo
            .GetBusinessFunctionListByBusinessServiceId(It.IsAny<string>())).ReturnsAsync(new List<BusinessFunction>());

        businessFunctionRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<BusinessFunction>());

        return businessFunctionRepository;
    }

    //Events
    
    public static Mock<IUserActivityRepository> CreateBusinessFunctionEventRepository(List<UserActivity> userActivities)
    {
        var businessFunctionEventRepository = new Mock<IUserActivityRepository>();

        businessFunctionEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        businessFunctionEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return businessFunctionEventRepository;
    }
}