using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BulkImportActionResultRepositoryTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BulkImportActionResultRepository _repository;

    public BulkImportActionResultRepositoryTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BulkImportActionResultRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;

        // Act
        var result = await _repository.AddAsync(bulkImportActionResult);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportActionResult.EntityId, result.EntityId);
        Assert.Equal(bulkImportActionResult.BulkImportOperationId, result.BulkImportOperationId);
        Assert.Single(_dbContext.BulkImportActionResults);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        await _repository.AddAsync(bulkImportActionResult);

        bulkImportActionResult.EntityId = "UpdatedEntityId";
        bulkImportActionResult.Status = "UpdatedStatus";
        bulkImportActionResult.ErrorMessage = "UpdatedErrorMessage";

        // Act
        var result = await _repository.UpdateAsync(bulkImportActionResult);

        // Assert
        Assert.Equal("UpdatedEntityId", result.EntityId);
        Assert.Equal("UpdatedStatus", result.Status);
        Assert.Equal("UpdatedErrorMessage", result.ErrorMessage);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        await _repository.AddAsync(bulkImportActionResult);

        // Act
        var result = await _repository.DeleteAsync(bulkImportActionResult);

        // Assert
        Assert.Equal(bulkImportActionResult.EntityId, result.EntityId);
        Assert.Empty(_dbContext.BulkImportActionResults);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        var addedEntity = await _repository.AddAsync(bulkImportActionResult);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        await _repository.AddAsync(bulkImportActionResult);

        // Act
        var result = await _repository.GetByReferenceIdAsync(bulkImportActionResult.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportActionResult.ReferenceId, result.ReferenceId);
        Assert.Equal(bulkImportActionResult.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var bulkImportActionResults = _bulkImportActionResultFixture.BulkImportActionResultList;
        await _repository.AddRange(bulkImportActionResults);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(bulkImportActionResults.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByEntityIdAndBulkImportOperationId Tests

    [Fact]
    public async Task GetByEntityIdAndBulkImportOperationId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entityId = "ENTITY_001";
        var operationId = "OPERATION_001";
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        bulkImportActionResult.EntityId = entityId;
        bulkImportActionResult.BulkImportOperationId = operationId;
        bulkImportActionResult.IsActive = true;
        await _repository.AddAsync(bulkImportActionResult);

        // Act
        var result = await _repository.GetByEntityIdAndBulkImportOperationId(entityId, operationId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entityId, result.EntityId);
        Assert.Equal(operationId, result.BulkImportOperationId);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetByEntityIdAndBulkImportOperationId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var bulkImportActionResults = _bulkImportActionResultFixture.BulkImportActionResultList;
        await _repository.AddRange(bulkImportActionResults);

        // Act
        var result = await _repository.GetByEntityIdAndBulkImportOperationId("NON_EXISTENT_ENTITY", "NON_EXISTENT_OPERATION");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByEntityIdAndBulkImportOperationId_ShouldReturnNull_WhenInactive()
    {
        // Arrange
        var entityId = "ENTITY_001";
        var operationId = "OPERATION_001";
        var bulkImportActionResult = _bulkImportActionResultFixture.BulkImportActionResultDto;
        bulkImportActionResult.EntityId = entityId;
        bulkImportActionResult.BulkImportOperationId = operationId;
        bulkImportActionResult.IsActive = false;
        _dbContext.BulkImportActionResults.Add(bulkImportActionResult);

        // Act
        var result = await _repository.GetByEntityIdAndBulkImportOperationId(entityId, operationId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByOperationIdAndOperationGroupId Tests

    [Fact]
    public async Task GetByOperationIdAndOperationGroupId_ShouldReturnMatchingEntities()
    {
        // Arrange
        var operationId = "OPERATION_001";
        var operationGroupId = "GROUP_001";
        
        var bulkImportActionResults = new List<BulkImportActionResult>
        {
            new BulkImportActionResult 
            { 
                BulkImportOperationId = operationId, 
                BulkImportOperationGroupId = operationGroupId,
                EntityId = "Entity1",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            },
            new BulkImportActionResult 
            { 
                BulkImportOperationId = operationId, 
                BulkImportOperationGroupId = operationGroupId,
                EntityId = "Entity2",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            },
            new BulkImportActionResult 
            { 
                BulkImportOperationId = "OPERATION_002", 
                BulkImportOperationGroupId = operationGroupId,
                EntityId = "Entity3",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRange(bulkImportActionResults);

        // Act
        var result = await _repository.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(operationId, x.BulkImportOperationId));
        Assert.All(result, x => Assert.Equal(operationGroupId, x.BulkImportOperationGroupId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetByOperationIdAndOperationGroupId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var bulkImportActionResults = _bulkImportActionResultFixture.BulkImportActionResultList;
        await _repository.AddRange(bulkImportActionResults);

        // Act
        var result = await _repository.GetByOperationIdAndOperationGroupId("NON_EXISTENT_OPERATION", "NON_EXISTENT_GROUP");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var actionResults = _bulkImportActionResultFixture.BulkImportActionResultList;
        var actionResult1 = actionResults[0];
        var actionResult2 = actionResults[1];

        // Act
        var task1 = _repository.AddAsync(actionResult1);
        var task2 = _repository.AddAsync(actionResult2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BulkImportActionResults.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var actionResults = _bulkImportActionResultFixture.BulkImportActionResultList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(actionResults);
        var initialCount = actionResults.Count;

        var toUpdate = actionResults.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Updated");
        await _repository.UpdateRange(toUpdate);

        var toDelete = actionResults.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Updated").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.GetByEntityIdAndBulkImportOperationId(null, "OPERATION_001");
        var result2 = await _repository.GetByEntityIdAndBulkImportOperationId("ENTITY_001", null);

        Assert.Null(result1);
        Assert.Null(result2);
    }

    [Fact]
    public async Task Repository_ShouldHandleStatusFiltering()
    {
        // Arrange
        var actionResults = new List<BulkImportActionResult>
        {
            new BulkImportActionResult
            {
                EntityId = "Entity1",
                Status = "Success",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            },
            new BulkImportActionResult
            {
                EntityId = "Entity2",
                Status = "Failed",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            },
            new BulkImportActionResult
            {
                EntityId = "Entity3",
                Status = "Success",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BulkImportActionResultFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRange(actionResults);

        // Act
        var successResults = await _repository.FindByFilter(x => x.Status == "Success");
        var failedResults = await _repository.FindByFilter(x => x.Status == "Failed");

        // Assert
        Assert.Equal(2, successResults.Count);
        Assert.Single(failedResults);
        Assert.All(successResults, x => Assert.Equal("Success", x.Status));
        Assert.All(failedResults, x => Assert.Equal("Failed", x.Status));
    }

    #endregion
}
