﻿using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfraObject.Commands.Create;

namespace ContinuityPatrol.Application.Helper;

public class UserDataHelper
{
    public static CreateUserCommand GenerateDefaultUser(CreateUserCommand user)
    {
        // var keyValuePairs = default(UserRole).ToDictionary();

        var companyId = user.CompanyId;
        user.CompanyId = companyId;
        var companyName = user.CompanyName;
        user.CompanyName = companyName;
        user.LoginType = "In House";
        // user.Role = keyValuePairs[UserRole.SiteAdmin.ToString()];
        user.Role = "d635aff0-1f05-4d61-b942-e15c19e180b8";
        user.RoleName = "SiteAdmin";
        user.LoginName = user.LoginName;
        user.LoginPassword = user.LoginPassword;
        user.InfraObjectAllFlag = true;
        user.SessionTimeout = 60;
        user.TwoFactorAuthentication = "none";
        user.IsVerify = false;
        user.IsReset = false;
        user.CreatedBy = Guid.NewGuid().ToString();
        user.LastModifiedBy = Guid.NewGuid().ToString();
        user.UserInfoCommand = new CreateUserInfoCommand
        {
            UserName = user.LoginName,
            Mobile = "0123456789",
            Email = $"{user.LoginName.ToLower()}@ptechnosoft.com",
            AlertMode = string.Empty,
            IsPreferredMode = false,
            CreatedBy = Guid.NewGuid().ToString(),
            LastModifiedBy = Guid.NewGuid().ToString()
        };
        user.UserInfraObjectCommand = new CreateUserInfraObjectCommand
        {
            Properties = string.Empty,
            IsApplication = 0,
            CreatedBy = Guid.NewGuid().ToString(),
            LastModifiedBy = Guid.NewGuid().ToString()
        };

        return user;
    }
}