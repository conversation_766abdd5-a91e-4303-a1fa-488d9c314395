﻿using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.SvcMsSqlMonitorStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SvcMsSqlMonitorStatus.Queries
{
    public class SvcMsSqlMonitorStatusListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISvcMsSqlMonitorStatusRepository> _mockSvcMsSqlMonitorStatusRepository;
        private readonly SvcMsSqlMonitorStatusListQueryHandler _handler;

        public SvcMsSqlMonitorStatusListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSvcMsSqlMonitorStatusRepository = new Mock<ISvcMsSqlMonitorStatusRepository>();
            _handler = new SvcMsSqlMonitorStatusListQueryHandler(_mockMapper.Object, _mockSvcMsSqlMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnListVm_WhenEntitiesExist()
        {
            var query = new SvcMsSqlMonitorStatusListQuery();

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>
            {
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Critical", Type = "Active" },
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Warning", Type = "Inactive" }
            };

            var expectedViewModels = new List<SvcMsSqlMonitorStatusListVm>
            {
                new SvcMsSqlMonitorStatusListVm { Id = svcMsSqlMonitorStatusEntities[0].ReferenceId, WorkflowName = "Critical", Type = "Active" },
                new SvcMsSqlMonitorStatusListVm { Id = svcMsSqlMonitorStatusEntities[1].ReferenceId, WorkflowName = "Warning", Type = "Inactive" }
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(svcMsSqlMonitorStatusEntities);

            _mockMapper.Setup(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities))
                .Returns(expectedViewModels);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedViewModels.Count, result.Count);
            Assert.Equal(expectedViewModels[0].Id, result[0].Id);
            Assert.Equal(expectedViewModels[1].Id, result[1].Id);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoEntitiesExist()
        {
            var query = new SvcMsSqlMonitorStatusListQuery();

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>();

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(svcMsSqlMonitorStatusEntities);

            _mockMapper.Setup(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities))
                .Returns(new List<SvcMsSqlMonitorStatusListVm>());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallMapperOnce_ForNonEmptyRepositoryList()
        {
            var query = new SvcMsSqlMonitorStatusListQuery();

            var svcMsSqlMonitorStatusEntities = new List<Domain.Entities.SvcMsSqlMonitorStatus>
            {
                new Domain.Entities.SvcMsSqlMonitorStatus { ReferenceId = Guid.NewGuid().ToString(), WorkflowName = "Critical", Type = "Active" }
            };

            var expectedViewModels = new List<SvcMsSqlMonitorStatusListVm>
            {
                new SvcMsSqlMonitorStatusListVm { Id = svcMsSqlMonitorStatusEntities[0].ReferenceId, WorkflowName = "Critical", Type = "Active" }
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(svcMsSqlMonitorStatusEntities);

            _mockMapper.Setup(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities))
                .Returns(expectedViewModels);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<SvcMsSqlMonitorStatusListVm>>(svcMsSqlMonitorStatusEntities), Times.Once);
        }
    }
}
