﻿// QUnit test file: test-accessManager.js
QUnit.module("Access Manager Module Tests", {
    beforeEach: function () {
        this.sandbox = sinon.createSandbox();
        window.RootUrl = "/";
        $('#qunit-fixture').html(`
            <div class="modal-content">
                <select id="txtUserRoleName">
                    <option value="">Select Role</option>
                    <option id="1" data-role="TestRole">TestRole</option>
                    <option id="2" data-role="SuperAdmin">SuperAdmin</option>
                </select>
                <input type="hidden" id="userRoleRefId">
                <input type="checkbox" id="chkMonitor" class="form-check-input">
                <input type="checkbox" id="chkManagment" class="form-check-input">
                <input type="checkbox" id="chkConfigView" class="form-check-input">
                <button id="btnAccSave" disabled>Save</button>
                <button id="btnCancel" disabled>Cancel</button>
                <div id="selectUserRole-error"></div>
                <div id="treeList-error"></div>
                <input name="__RequestVerificationToken" value="test-token">
            </div>
        `);

        // Initialize the Access Manager module
        initializeAccessManager();

        // Stub AJAX calls
        this.ajaxStub = this.sandbox.stub($, 'ajax').callsFake((options) => {
            if (options.url.includes("GetRoleDetails")) {
                return {
                    done: function (callback) {
                        callback({
                            success: true,
                            data: {
                                id: "1",
                                roleName: "TestRole",
                                properties: JSON.stringify({
                                    Permissions: {
                                        Dashboard: { View: true, Monitor: false, Management: false },
                                        Configuration: { CreateAndEdit: false, Delete: false, View: false }
                                    }
                                })
                            }
                        });
                        return this;
                    }
                };
            } else if (options.url.includes("CreateOrUpdate")) {
                return {
                    done: function (callback) {
                        callback({
                            success: true,
                            data: "Operation successful"
                        });
                        return this;
                    }
                };
            }
            return $.Deferred().resolve({}).promise();
        });
    },

    afterEach: function () {
        // Restore all stubs/spies
        this.sandbox.restore();
        delete window.RootUrl;
    }
});

function initializeAccessManager() {
    // Simulate the initialization code from AccessManager.js
    $('#btnAccSave').prop('disabled', true);
    $('#btnCancel').prop('disabled', true);

    // Bind event handlers
    $('#txtUserRoleName').on('change', function () {
        const selectedOption = $(this).find("option:selected");
        const userRoleName = selectedOption.data('role');
        const userRoleId = selectedOption.attr('id');

        $("#btnAccSave, #btnCancel").toggle(
            !['Administrator', 'Manager', 'Operator', 'SuperAdmin'].includes(userRoleName)
        );
        updateCheckboxByRole(userRoleName);
        if (userRoleId) {
            getUserRoleById(userRoleId);
        }
    });

    $('.form-check-input').on('change', function () {
        const isChecked = anyCheckboxChecked();
        $("#btnCancel, #btnAccSave").prop("disabled", !isChecked);
    });
}

QUnit.test("updateCheckboxByRole - should disable checkboxes for predefined roles", function (assert) {
    // Arrange
    const predefinedRoles = ["SuperAdmin", "Administrator", "Operator", "Manager"];
    const testRoles = ["TestRole", "CustomRole"]; // Roles that shouldn't disable checkboxes

    // Test predefined roles
    predefinedRoles.forEach(role => {
        updateCheckboxByRole(role);
        const checkboxes = $('input[type="checkbox"]');

        assert.ok(checkboxes.prop('disabled'), `✅ ${role} should disable checkboxes`);
        assert.ok(checkboxes.hasClass('opacity-80'), `✅ ${role} should add opacity class`);
    });

    // Test non-predefined roles
    testRoles.forEach(role => {
        updateCheckboxByRole(role);
        const checkboxes = $('input[type="checkbox"]');

        assert.ok(!checkboxes.prop('disabled'), `✅ ${role} should not disable checkboxes`);
        assert.ok(!checkboxes.hasClass('opacity-80'), `✅ ${role} should not add opacity class`);
    });
});

QUnit.test("validateDropDown - should validate dropdown selection", function (assert) {
    // Arrange
    const errorElement = $('<div>');

    // Test empty value
    let result = validateDropDown("", "Error message", errorElement);
    assert.strictEqual(result, false, "✅ Should return false for empty value");
    assert.ok(errorElement.hasClass('field-validation-error'), "✅ Should add error class");
    assert.strictEqual(errorElement.text(), "Error message", "✅ Should set error message");

    // Test valid value
    result = validateDropDown("value", "Error message", errorElement);
    assert.strictEqual(result, true, "✅ Should return true for valid value");
    assert.ok(!errorElement.hasClass('field-validation-error'), "✅ Should remove error class");
    assert.strictEqual(errorElement.text(), "", "✅ Should clear error message");
});

QUnit.test("anyCheckboxChecked - should detect checked checkboxes", function (assert) {
    // Arrange
    const checkboxes = $('.form-check-input');

    // Test with no checkboxes checked
    checkboxes.prop('checked', false);
    assert.strictEqual(anyCheckboxChecked(), false, "✅ Should return false when no checkboxes are checked");

    // Test with at least one checkbox checked
    checkboxes.first().prop('checked', true);
    assert.strictEqual(anyCheckboxChecked(), true, "✅ Should return true when at least one checkbox is checked");
});

QUnit.test("getCheckedCategories - should validate at least one category is checked", function (assert) {
    // Arrange
    const testData = {
        Permissions: {
            Dashboard: { View: false, Monitor: false, Management: false },
            Configuration: { CreateAndEdit: false, Delete: false, View: false }
        }
    };
    const errorElement = $('#treeList-error');

    // Test no categories checked
    let result = getCheckedCategories(testData);
    assert.strictEqual(result, false, "✅ Should return false when no categories checked");
    assert.ok(errorElement.hasClass('field-validation-error'), "✅ Should add error class");
    assert.strictEqual(errorElement.text(), "Please check at least one checkbox", "✅ Should set error message");

    // Test with one category checked
    testData.Permissions.Dashboard.View = true;
    result = getCheckedCategories(testData);
    assert.strictEqual(result, true, "✅ Should return true when at least one category checked");
    assert.ok(!errorElement.hasClass('field-validation-error'), "✅ Should remove error class");
    assert.strictEqual(errorElement.text(), "", "✅ Should clear error message");
});

QUnit.test("getUserRoleById - should fetch role data and update checkboxes", function (assert) {
    const done = assert.async();

    // Arrange - Set up complete DOM structure
    $('#qunit-fixture').html(`
        <div class="modal-content">
            <input type="hidden" id="userRoleRefId">
            <button id="btnAccSave">Save</button>
            <input type="checkbox" id="chkMonitor">
            <input name="__RequestVerificationToken" value="test-token">
        </div>
    `);

    // Configure the existing stub for this specific test case
    this.ajaxStub.callsFake((options) => {
        if (options.url.includes("GetRoleDetails")) {
            return {
                done: function (callback) {
                    callback({
                        success: true,
                        data: {
                            id: "1",
                            roleName: "TestRole",
                            properties: JSON.stringify({
                                Permissions: {
                                    Dashboard: { View: true, Monitor: false, Management: false },
                                    Configuration: { CreateAndEdit: false, Delete: false, View: false }
                                }
                            })
                        }
                    });
                    return this;
                }
            };
        }
        return $.Deferred().resolve({}).promise();
    });

    // Act
    getUserRoleById("1");

    // Assert
    setTimeout(() => {
        try {
            // Verify AJAX was called with expected URL
            assert.ok(
                this.ajaxStub.calledWithMatch(sinon.match.has('url', sinon.match(/GetRoleDetails/))),
                "✅ Should call GetRoleDetails endpoint"
            );

            // Verify DOM updates
            assert.strictEqual($('#userRoleRefId').val(), "1", "✅ Should set userRoleRefId");
            assert.strictEqual($('#btnAccSave').text(), "Update", "✅ Should set button text to Update");
            assert.strictEqual($('#chkMonitor').prop('checked'), false, "✅ Should set checkbox state");

            done();
        } catch (error) {
            assert.ok(false, `❌ Test failed with error: ${error}`);
            done();
        }
    }, 200);
});


QUnit.test("Checkbox change events - should update button states", function (assert) {
    // Test enabling buttons when checkbox is checked
    $('.form-check-input').prop('checked', true).trigger('change');
    assert.ok(!$('#btnAccSave').prop('disabled'), "✅ Save button should be enabled when checkbox checked");
    assert.ok(!$('#btnCancel').prop('disabled'), "✅ Cancel button should be enabled when checkbox checked");

    // Test disabling buttons when no checkboxes are checked
    $('.form-check-input').prop('checked', false).trigger('change');
    assert.ok($('#btnAccSave').prop('disabled'), "✅ Save button should be disabled when no checkboxes checked");
    assert.ok($('#btnCancel').prop('disabled'), "✅ Cancel button should be disabled when no checkboxes checked");
});