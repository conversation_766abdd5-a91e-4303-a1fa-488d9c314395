﻿using ContinuityPatrol.Application.Features.HeatMapLog.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapLog.Commands;

public class UpdateHeatMapLogTests : IClassFixture<HeatMapLogFixture>
{
    private readonly HeatMapLogFixture _heatMapLogFixture;

    private readonly Mock<IHeatMapLogRepository> _heatMapLogRepositoryMock;

    private readonly UpdateHeatMapLogCommandHandler _handler;

    public UpdateHeatMapLogTests(HeatMapLogFixture heatMapLogFixture)
    {
        _heatMapLogFixture = heatMapLogFixture;
    
        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.UpdateHeatMapLogRepository(_heatMapLogFixture.HeatMapLogs);
        
        _handler = new UpdateHeatMapLogCommandHandler(_heatMapLogFixture.Mapper, _heatMapLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ValidHeatMapLog_UpdateToHeatMapLogRepo()
    {
        _heatMapLogFixture.UpdateHeatMapLogCommand.Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId;

        var result = await _handler.Handle(_heatMapLogFixture.UpdateHeatMapLogCommand, CancellationToken.None);

        var heatMapLog = await _heatMapLogRepositoryMock.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_heatMapLogFixture.UpdateHeatMapLogCommand.InfraObjectName, heatMapLog.InfraObjectName);
    }

    [Fact]
    public async Task Handle_Return_UpdateHeatMapLogResponse_When_HeatMapLogUpdated()
    {
        _heatMapLogFixture.UpdateHeatMapLogCommand.Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId;

        var result = await _handler.Handle(_heatMapLogFixture.UpdateHeatMapLogCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateHeatMapLogResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_heatMapLogFixture.UpdateHeatMapLogCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidHeatMapLog()
    {
        _heatMapLogFixture.UpdateHeatMapLogCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_heatMapLogFixture.UpdateHeatMapLogCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _heatMapLogFixture.UpdateHeatMapLogCommand.Id = _heatMapLogFixture.HeatMapLogs[0].ReferenceId;

        await _handler.Handle(_heatMapLogFixture.UpdateHeatMapLogCommand, CancellationToken.None);

        _heatMapLogRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.HeatMapLog>()), Times.Once);
    }
}