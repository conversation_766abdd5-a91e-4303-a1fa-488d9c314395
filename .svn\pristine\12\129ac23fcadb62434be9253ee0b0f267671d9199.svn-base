﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateDrOperation;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateStateToUnlock;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationCategoryType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessFunctionId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByDrReady;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IInfraObjectService
{
    Task<BaseResponse> CreateAsync(CreateInfraObjectCommand createInfraObjectCommand);
    Task<BaseResponse> UpdateAsync(UpdateInfraObjectCommand updateInfraObjectCommand);
    Task<BaseResponse> DeleteAsync(string infraObjectId);
    Task<List<InfraObjectListVm>> GetInfraObjectList();
    Task<bool> IsInfraObjectNameExist(string infraObjectName, string id);
    Task<DrReadyCount> GetInfraObjectDrReady();
    Task<InfraObjectDetailVm> GetInfraObjectById(string infraObjectId);
    Task<BaseResponse> UpdateInfraObjectState(UpdateInfraObjectStateCommand updateInfraObjectStateCommand);
    Task<PaginatedResult<InfraObjectListVm>> GetPaginatedInfraObjects(GetInfraObjectPaginatedListQuery query);
    Task<List<GetInfraObjectNameVm>> GetInfraObjectNames();
    Task<List<GetInfraObjectByBusinessServiceIdVm>> GetInfraObjectByBusinessServiceId(string businessServiceId);
    Task<List<GetInfraObjectByBusinessServiceIdVm>> GetInfraObjectByBusinessServiceName(string businessServiceName);
    Task<List<InfraObjectListByReplicationCategoryTypeVm>> GetInfraObjectListByReplicationCategoryType(string replicationCategoryType);
    Task<List<InfraObjectListByReplicationTypeVm>> GetInfraObjectListByReplicationTypeId(string replicationTypeId);
    Task<GetInfraObjectDetailByIdVm> GetInfraObjectDetailsById(string infraObjectId);
    Task<List<GetInfraObjectByBusinessFunctionIdVm>> GetInfraObjectByBusinessFunctionId(string businessFunctionId);
    Task<BaseResponse> UpdateInfraObjectStateUnlock(UpdateStateToUnlockCommand updateStateToUnlockCommand);
    Task<UpdateInfraObjectDrOperationResponse> UpdateDrOperationStatus(UpdateInfraObjectDrOperationCommand command);
}