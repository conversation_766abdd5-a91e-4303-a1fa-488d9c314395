﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Commands;

public class CreateApprovalMatrixTests : IClassFixture<ApprovalMatrixFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;

    private readonly Mock<IApprovalMatrixRepository> _mockApprovalMatrixRepository;

    private readonly CreateApprovalMatrixCommandHandler _handler;

    public CreateApprovalMatrixTests(ApprovalMatrixFixture approvalMatrixFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;

        var mockUserRepository = new Mock<IUserRepository>();

        _mockApprovalMatrixRepository = ApprovalMatrixRepositoryMocks.CreateApprovalMatrixRepository(_approvalMatrixFixture.ApprovalMatrices);

        _handler = new CreateApprovalMatrixCommandHandler(_approvalMatrixFixture.Mapper, _mockApprovalMatrixRepository.Object, mockUserRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_ApprovalMatrix()
    {
        await _handler.Handle(_approvalMatrixFixture.CreateApprovalMatrixCommand, CancellationToken.None);

        var allCategories = await _mockApprovalMatrixRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_approvalMatrixFixture.ApprovalMatrices.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulApprovalMatrixResponse_When_AddValidApprovalMatrix()
    {
        var result = await _handler.Handle(_approvalMatrixFixture.CreateApprovalMatrixCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateApprovalMatrixResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_approvalMatrixFixture.CreateApprovalMatrixCommand, CancellationToken.None);

        _mockApprovalMatrixRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.ApprovalMatrix>()), Times.Once);
    }
}