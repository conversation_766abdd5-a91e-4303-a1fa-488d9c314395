﻿using ContinuityPatrol.Domain.ViewModels.WorkflowModel;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetDetailByActionName;

public class GetDetailByActionNameQueryHandler : IRequestHandler<GetDetailByActionNameQuery, GetDetailByActionNameVm>
{
    private readonly IWorkflowRepository _workflowRepository;
    public GetDetailByActionNameQueryHandler(IWorkflowRepository workflowRepository)
    {
        _workflowRepository = workflowRepository;
    }

    public async Task<GetDetailByActionNameVm> Handle(GetDetailByActionNameQuery request, CancellationToken cancellationToken)
    {
        var workflow = await _workflowRepository.GetByReferenceIdAsync(request.WorkflowId);

        Guard.Against.NullOrDeactive(workflow, nameof(Domain.Entities.Workflow),
            new NotFoundException(nameof(Domain.Entities.Workflow), request.WorkflowId));

        return new GetDetailByActionNameVm
        {
            WorkflowName = workflow.Name,
            ActionName = request.ActionName,
            Properties = await GetSingleAction(request.ActionName, workflow.Properties) ??
            await GetParallelActions(request.ActionName, workflow.Properties) ??
            await GetGroupActions(request.ActionName, workflow.Properties) ??
            await GetParallelGroupActionChildren(request.ActionName, workflow.Properties)
        };
    }

    public Task<string> GetParallelGroupActionChildren(string actionName,string json)
    {
        var jsonObject = JObject.Parse(json);

        var result = jsonObject["nodes"]?
            .SelectMany(node => node["groupActions"] ?? Enumerable.Empty<JToken>())
            .SelectMany(node => node["children"] ?? Enumerable.Empty<JToken>())
            .Select(child => child["actionInfo"])
            .Where(actionInfo => (string)actionInfo?["actionName"] == actionName)
            .ToList();

        var resultString = result?.FirstOrDefault();

        var properties = resultString?.SelectToken("properties")?.ToString() ?? string.Empty;

        return Task.FromResult(properties);
    }

    public Task<string> GetParallelActions(string actionName, string json)
    {
        var jsonObject = JObject.Parse(json);

        var result = jsonObject["nodes"]?
            .SelectMany(node => node["children"] ?? Enumerable.Empty<JToken>())
            .Select(child => child["actionInfo"])
            .Where(actionInfo => (string)actionInfo?["actionName"] == actionName)
            .ToList();

        var resultString = result?.FirstOrDefault();

        var properties = resultString?.SelectToken("properties")?.ToString() ?? string.Empty;

        return Task.FromResult(properties);
    }

    public Task<string> GetGroupActions(string actionName, string json)
    {

        var jsonObject = JObject.Parse(json);

        var result = jsonObject["nodes"]?
            .SelectMany(node => node["groupActions"] ?? Enumerable.Empty<JToken>())
            .Select(child => child["actionInfo"])
            .Where(actionInfo => (string)actionInfo?["actionName"] == actionName)
            .ToList();

        var resultString = result?.FirstOrDefault();

        var properties = resultString?.SelectToken("properties")?.ToString() ?? string.Empty;

        return Task.FromResult(properties);
    }

    public Task<string> GetSingleAction(string actionName, string json)
    {
        var jsonObject = JObject.Parse(json);

        var result = jsonObject["nodes"]
            ?.FirstOrDefault(node => node["actionInfo"]?["actionName"]?.ToString() == actionName)?["actionInfo"];

        var properties = result?.SelectToken("properties")?.ToString() ?? string.Empty;

        return Task.FromResult(properties);
    }
}