﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.ApproveRequest;

public class ApproveApprovalMatrixRequestQueryHandler : IRequestHandler<ApproveApprovalMatrixRequestQuery, string>
{
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;

    //private readonly IApprovalMatrixTemplateRepository _approvalMatrixTemplateRepository;
    private readonly IMapper _mapper;
    private readonly IUserRepository _userRepository;

    public ApproveApprovalMatrixRequestQueryHandler(IMapper mapper, IApprovalMatrixRepository approvalMatrixRepository,
        /*IApprovalMatrixTemplateRepository approvalMatrixTemplateRepository,*/ IUserRepository userRepository)
    {
        _mapper = mapper;
        _approvalMatrixRepository = approvalMatrixRepository;
        // _approvalMatrixTemplateRepository = approvalMatrixTemplateRepository;
        _userRepository = userRepository;
    }

    public async Task<string> Handle(ApproveApprovalMatrixRequestQuery request, CancellationToken cancellationToken)
    {
        var status = string.Empty;
        var getDetails = await _approvalMatrixRepository.GetByReferenceIdAsync(request.Id);
        var user = await _userRepository.GetByReferenceIdAsync(request.UserId);
        var approver = getDetails.ApprovedBy + user.LoginName + "$";
        var properties = getDetails.Properties + user.LoginName + "$" + request.Status + "$" +
                         DateTime.Now.ToString("hh:mm tt") + "$";
        //// var templateDetails = await _approvalMatrixTemplateRepository.GetByTemplateName(getDetails.TemplateName.Trim());
        // var rule = templateDetails.Rule == "Any One" ? "1" : "2";
        var counter = Convert.ToInt32(getDetails.ApprovalFlag) + 1;
        //var rejectCounter = Convert.ToInt32(getDetails.RejectedFlag) + 1;
        //if (request.Status.Trim().Equals("Rejected"))
        //{
        //    if (rejectCounter.ToString().Equals(rule))
        //        status = await _approvalMatrixRepository.ApproveRequest(request.Id, request.Status, properties,
        //            approver);
        //    else
        //        //_rejectcounter = Convert.ToInt32(getDetails.RejectedFlag)+1;
        //        await _approvalMatrixRepository.SetCounterForReject(request.Id, rejectCounter.ToString(), properties,
        //            approver);
        //}
        //else
        //{
        //    if (counter.ToString().Equals(rule))
        //        status = await _approvalMatrixRepository.ApproveRequest(request.Id, request.Status, properties,
        //            approver);
        //    else
        //        //_counter = Convert.ToInt32(getDetails.ApprovalFlag)+1;
        //        await _approvalMatrixRepository.SetCounterForApproval(request.Id, counter.ToString(), properties,
        //            approver);
        //}

        return status;
    }
}