using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class CyberAlertProfile : Profile
{
    public CyberAlertProfile()
    {
        CreateMap<CyberAlert, CyberAlertListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<CyberAlert, CyberAlertDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        
        CreateMap< PaginatedResult<CyberAlert>, PaginatedResult<CyberAlertListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

        CreateMap<CyberAlert, CreateCyberAlertCommand>().ReverseMap();
        CreateMap<CyberAlert, CyberAlertViewModel>().ReverseMap();

        CreateMap<CreateCyberAlertCommand, CyberAlertViewModel>().ReverseMap();
        CreateMap<UpdateCyberAlertCommand, CyberAlertViewModel>().ReverseMap();

        CreateMap<UpdateCyberAlertCommand, CyberAlert>().ForMember(x => x.Id, y => y.Ignore());
    }
}