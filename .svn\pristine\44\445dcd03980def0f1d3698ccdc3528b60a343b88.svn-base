//Formbuilder validation for Server, Database, Single Sign-On, Node, Replication.
var pattern = /^[^!#$%^*()+={}\[\]:;,<>?\/\\~`|"' ]+$/; ///^[a-zA-Z0-9]+$/;
var abbrevationLettersForValidation = /\b(os|ip|sql|sid|pr|dr|asm|ssh|sso|wmi|id|wnn|lss|oauth|arcos|api|url|cmd|vm|esxi|dscli|hmc|vg)\b/ig; //By adding \b before and after each word in the regular expression, it ensures that the matched substrings are whole words and not parts of larger words.
const rsyncRobocopyPathForm = /^(\\\\[^\\\/]+\\[^\\\/]+(\\[^\\\/]*)*|[A-Za-z]:\\[^\\\/]+(\\[^\\\/]*)*|\\[^\\\/]+(\\[^\\\/]*)*|\/[^\/]+(\/[^\/]*)*|~\/[^\/]+(\/[^\/]*)*|https?:\/\/[^\/]+(\/[^\/]*)*|\\\\\.[\\\/][^\\\/]+|\\\\\?[\\\/][^\\\/]+)$/
const pathForServer = /^(\\(\\[^\\]+)+|([A-Za-z]:(\\)?|[A-Za-z]:(\\[^\\]+)+))(\\)?$/  //file and win network path.
//const postgresSQLDataBinDirectory = /^(?:(?:[a-zA-Z]:\\Program Files\\PostgreSQL\\\d+\\(?:data|bin)\\?)|(?:\/(?:var\/lib\/postgresql\/\d+\/main|usr\/(?:lib|local)\/postgresql\/\d+\/bin|usr\/local\/pgsql\/bin)\/?))$/;
const postgresSQLDataBinDirectory = /^(?:(?:[a-zA-Z]:\\Program Files\\PostgreSQL\\\d+\\(?:data|bin)\\?)|(?:\/(?:var\/lib\/(?:postgresql|pgsql)\/\d+\/(?:main|data)|usr\/(?:lib|local)\/postgresql\/\d+\/bin|usr\/local\/pgsql\/bin)\/?))$/;
const regexURL = /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(:\d+)?(\/[\w-._~:/?#[\]@!$&'()*+,;=%]*)?$/;  //supports port // /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(\/[\w-._~:/?#[\]@!$&'()*+,;=%]*)?$/;

//Replication
const networkPath = /^\\\\([^\\\/:*?"<>|]+)\\([^\\\/:*?"<>|]+(?:\\[^\\\/:*?"<>|]+)*)$/;
const localPath = /^(\/([a-zA-Z0-9_-]+(\/[a-zA-Z0-9_-]+)*))|(([a-zA-Z]:\\)([a-zA-Z0-9_-]+(\\[a-zA-Z0-9_-]+)*))$/;
const cmdPath = /^[A-Za-z]:\\(?:[^\\\n]*\\)*[^\\\n]+$/;


async function formBuilderRequiredPathValidation($this, modulename = null) {
    let result = await commonPlaceholder($this);
    let inputValue2 = $this?.val();
    $this.next(".dynamic-input-field").remove();
    if (inputValue2 === "") {
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + result + "</span></div>");
        return false;
    }
    else if (inputValue2 !== "") {
        $this.next(".dynamic-input-field").remove();
        let sanitizedValue = inputValue2.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces      
        $this?.val(sanitizedValue);
        if (modulename === "singlesignon") { // don't add database because text also will come.
            let result2 = await InvalidPathRegex(sanitizedValue);
            if (result2 === true) {
                $this.next(".dynamic-input-field").remove();
                return true;
            }
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + result2 + "</span></div>");
            return false;
        }
        if (modulename === "server") {
            if (pathForServer.test(sanitizedValue)) {
                $this.next(".dynamic-input-field").remove();
                return true;
            }
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
            return false;
        }
        //if (modulename === "database") {
        //    let validate = result.replaceAll(' ', '').toLowerCase();
        //    if (validate.includes('datadirectory') || validate.includes('bindirectory')) {
        //        if (postgresSQLDataBinDirectory.test(sanitizedValue)) {
        //            $this.next(".dynamic-input-field").remove();
        //            return true;
        //        }
        //        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
        //        return false;
        //    }
        //    if (validate.includes('databasebinpath')) {
        //        if (RegExp(/^(?:[a-zA-Z]:)?[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+(?:[\\/][\w\s!@#$%^&*()\-+=,.{}\[\]~`'";]+)*[\\/]?$/i).test(sanitizedValue)) {
        //            $this.next(".dynamic-input-field").remove();
        //            return true;
        //        }
        //        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
        //        return false;
        //    }
        //}
        //if (modulename === "replication") {
        //    const resultTrim = result.replace(/Enter/g, "").toLowerCase().replace(/\s+/g, "");
        //    if (resultTrim.includes("networkpath")) {
        //        if (networkPath.test(sanitizedValue)) {
        //            $this.next(".dynamic-input-field").remove();
        //            return true;
        //        }
        //        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
        //        return false;
        //    }
        //    if (resultTrim.includes("localpath")) {
        //        if (localPath.test(sanitizedValue)) {
        //            $this.next(".dynamic-input-field").remove();
        //            return true;
        //        }
        //        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
        //        return false;
        //    }
        //    if (resultTrim.includes("cmdpath")) {
        //        if (cmdPath.test(sanitizedValue)) {
        //            $this.next(".dynamic-input-field").remove();
        //            return true;
        //        }
        //        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + "Invalid path" + "</span></div>");
        //        return false;
        //    }
        //}
        return true;
    }
}

function validationRestrictRequiredInputs(restrictRequiredInputs) {
    restrictRequiredInputs?.each(function () {
        let $this = $(this);
        let id = $(this)?.attr("id");
        document.getElementById(id).addEventListener("keydown", function () {
            if ($this.is(':visible')) {
                $this.next(".dynamic-input").remove();
                var inputValue = this?.value;
                if (inputValue) {
                    if (!pattern.test(inputValue)) { //if entered special char it will become true                                                                                   
                        $this.after("<div class='dynamic-input field-validation-error'><span class='required-field-msg'>Only & _ @ . - special characters allowed.</span></div>");
                        restrictSpecialChars = false;
                    }
                }
            }
        });
    });
}

function commonPlaceholder($this) {
    let placeholderValue = $($this)?.attr('placeholder');
    const customCasing = {
        esxi: 'ESXi'
    };
    let result;
    if (placeholderValue) {
        let firstLetterCapital = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
        result = firstLetterCapital.replace(abbrevationLettersForValidation, function (match) {
            return match.toLowerCase() === "oauth" ? "OAuth" : customCasing[match.toLowerCase()] || match.toUpperCase();
        });
        //result = firstLetterCapital?.replace(abbrevationLettersForValidation, function (match) {
        //    return match?.toUpperCase();
        //});
    } else {
        let associatedLabel2 = $('label[for="' + $this.attr('id') + '"]');
        let labelText2 = associatedLabel2?.text()?.replace(/\*/g, '');
        result = labelText2?.toLowerCase();
    }
    return result;
}

const formBuilderRestrictSpaceatStart = (value) => {
    const regex = /^[^\s].*$/;
    return regex.test(value) ? true : "Should not begin with space";
}

const formBuilderRestrictSpaceatEnd = (value) => {
    const regex = /^(?!.*\s$).+$/;
    return regex.test(value) ? true : "Should not end with space";
}

async function formBuilderTextValidation($this, modulename = null) {
    let result = await commonPlaceholder($this);
    let inputValue2 = $this?.val();
    $this.next(".dynamic-input-field").remove();
    if (inputValue2 === "") {
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'>" + result + "</span></div>");
        return false;
    }
    else if (inputValue2 !== "") {
        if (inputValue2.includes('<')) {
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'>" + 'Special characters not allowed' + "</span></div>");
            return false;
        }
        $this.after(`<div class='dynamic-input-field' id='element${$this.attr('id')}'></div>`);
        const errorElement = $(`#element${$this.attr('id')}`);
        let validationResults = []
        let res;
        if ($this.attr('type') == 'text') {
            let nameField = $this?.val()
            let sanitizedValue = nameField.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
            $this?.val(sanitizedValue);

            //Don't add for server, DB, Repl because for some input need special char.
            //$this.attr('name').toLowerCase().trim(" ").includes("username") || 
            //don't add for username because it affect server page. special char will come.
            if (modulename === "singlesignon" && ($this.attr('name').toLowerCase().trim(" ").includes("appkey") ||
                $this.attr('name').toLowerCase().trim(" ").includes("apiuser") ||
                $this.attr('name').toLowerCase().trim(" ").includes("accountname") ||
                $this.attr('name').toLowerCase().trim(" ").includes("requestreason") ||
                $this.attr('name').toLowerCase().trim(" ").includes("sharedkey") ||
                $this.attr('name').toLowerCase().trim(" ").includes("servicetype") ||
                $this.attr('name').toLowerCase().trim(" ").includes("username"))) {
                validationResults = [
                    await SpecialCharValidate(inputValue2),
                    await ShouldNotBeginWithUnderScore(inputValue2),
                    await ShouldNotBeginWithSpace(inputValue2),
                    await OnlyNumericsValidate(inputValue2),
                    await ShouldNotBeginWithNumber(inputValue2),
                    await SpaceWithUnderScore(inputValue2),
                    await ShouldNotEndWithUnderScore(inputValue2),
                    await ShouldNotEndWithSpace(inputValue2),
                    await MultiUnderScoreRegex(inputValue2),
                    await SpaceAndUnderScoreRegex(inputValue2),
                ];
            } else {
                validationResults = [
                    await ShouldNotBeginWithUnderScore(inputValue2),
                    await ShouldNotEndWithUnderScore(inputValue2),
                    await formBuilderRestrictSpaceatStart(inputValue2),
                    await formBuilderRestrictSpaceatEnd(inputValue2),
                ];
            }
        } else if ($this.attr('type') == 'number') {
            validationResults = [
                await OnlyNum(inputValue2),
            ];
        }
        res = await CommonValidation(errorElement, validationResults);
        if (res !== true) {
            return false;
        }
        return res
    }
}

async function formBuilderIPAddressValidation($this) {
    let result = await commonPlaceholder($this);
    let inputValue2 = $this?.val();
    $this.next(".dynamic-input-field").remove();
    if (inputValue2 === "") {
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + result + "</span></div>");
        return false;
    }
    else if (inputValue2 !== "") {
        let nameField = $this?.val()
        let sanitizedValue = nameField.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
        let cleanedIp = sanitizedValue.replace(/\.+/g, '.');
        $this?.val(cleanedIp);
        if (cleanedIp.includes(" ")) {
            $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> Enter IP address without space</span></div>");
            return false;
        }
        else if (/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(cleanedIp)) {
            return true;
        }
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> Enter valid IP address</span></div>");
        return false;
    }
}

async function formBuilderRequiredURLValidation($this) {    
    let result = await commonPlaceholder($this);
    let inputValue2 = $this?.val();
    $this.next(".dynamic-input-field").remove();
    if (inputValue2 === "") {
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + result + "</span></div>");
        return false;
    }
    else if (inputValue2 !== "") {
        $this.next(".dynamic-input-field").remove();
        let sanitizedValue = inputValue2.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces      
        $this?.val(sanitizedValue);
        if (regexURL.test(sanitizedValue)) {
            return true;
        }
        let resultString = result.replace("Enter", "Invalid").trim();
        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'> " + resultString + "</span></div>");
        return false;
    }
}

async function formBuilderSelectFieldValidation($this, modulename) {
    let result = await commonPlaceholder($this);
    let inputValue3 = $this?.val();
    let inputValue2 = inputValue3?.replace(/\*/g, '')?.replace(/\s+/g, '');
    if (modulename === 'replication') {
        if (inputValue2 && result.includes('server')) {
            $.ajax({
                method: 'GET',
                url: RootUrl + 'Configuration/Replication/GetByReferenceId',
                data: { id: inputValue2 },
                datatype: "json",
                success: function (result) {
                    if (result.success) {
                        let data = result?.data;
                        let props = JSON.parse(data?.properties);
                        let IPAddress = props?.IpAddress ? props?.IpAddress : "NA";
                        let user = props?.SSHUser || props?.SSHKeyUser || props?.PowerShellUser || "NA";
                        let password = props?.SSHPassword || props?.SSHKeyPassword || props?.PowerShellPassword || "NA";
                        if (data?.siteName?.includes('PR')) {
                            let PRIpAddress = $(".formeo-render .f-field-group input[type='text']");
                            PRIpAddress?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'pripaddress' || name === "prmanagementconsoleip") {
                                        $this.val(IPAddress);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let PRUserName = $(".formeo-render .f-field-group input[type='text']");
                            PRUserName?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'prusername') {
                                        $this.val(user);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let PRPassword = $(".formeo-render .f-field-group input[type='password']");
                            PRPassword?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'prpassword') {
                                        $this.val(password);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });
                        }
                        if (data?.siteName.includes('DR')) {
                            let DRIpAddress = $(".formeo-render .f-field-group input[type='text']");
                            DRIpAddress?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'dripaddress' || name === "drmanagementconsoleip") {
                                        $this.val(IPAddress);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let DRUserName = $(".formeo-render .f-field-group input[type='text']");
                            DRUserName?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'drusername') {
                                        $this.val(user);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });

                            let DRPassword = $(".formeo-render .f-field-group input[type='password']");
                            DRPassword?.each(async function () {
                                let $this = $(this);
                                if ($this.is(':visible')) {
                                    let name = $this?.attr('name')?.toLowerCase();
                                    if (name === 'drpassword') {
                                        $this.val(password);
                                        $this.attr('disabled', 'disabled');
                                        $this.css("background", "#D3D3D3");
                                        $this.next(".dynamic-input-field").remove();
                                    }
                                }
                            });
                        }
                    }
                    else {
                        errorNotification(result);
                    }
                }
            })
        }
    }
    let value;
    if (Array.isArray(inputValue2)) {
        if (inputValue2?.length > 0) {
            inputValue2 = inputValue2?.filter(item => item !== '');
            value = inputValue2[0];
        } else {
            value = "";
        }
    }
    if (typeof inputValue2 === 'string') {
        value = inputValue2;
        if (value && value?.toLowerCase()?.includes('select')) value = ""
    }
    $this.next(".dynamic-select-tag").remove();
    $this.next(".dynamic-input-field").remove();

    if (value === undefined || value?.length === 0 || value === "" || value === " ") {
        $this.after(`<div class='dynamic-select-tag field-validation-error-selecttag'>
                        <span class='required-field-msg'>${result}</span>
                    </div>`);
        return false;
    } else {
        return true;
    }
}

async function inputRequired(modulename) { // [type='text']
    let validationResults = [];

    let requiredInputs = $(".formeo-render .f-field-group input[required][input-type='text'], input[required][type='number']")
        .not(".formeo-render .f-field-group input[minlength]")
        .not(".formeo-render .f-field-group input[restrict]");

    if (requiredInputs?.length > 0) {
        await Promise.all(requiredInputs.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderTextValidation($this, modulename);
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictInputs = $(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[required]")
        .not(".formeo-render .f-field-group input[minlength]");

    if (restrictInputs?.length > 0) {
        await Promise.all(restrictInputs.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                var inputValue = this.value;
                $this.next(".dynamic-input-field").remove();
                if (inputValue) {
                    if (!pattern.test(inputValue)) { //if entered special char it will become true                          
                        $this.next(".dynamic-input-field").remove();
                        $this.after(`<div class='dynamic-input-field field-validation-error'>
                                        <span class='required-field-msg'>Only & _ @ . - special characters allowed.</span>
                                    </div>`);
                        validationResults.push(false);
                    } else {
                        validationResults.push(true);
                    }
                } else {
                    validationResults.push(true); //because it's not req it's restrict only.
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let minlengthMaxlength = $(".formeo-render .f-field-group input[minlength][maxlength]")
        .not(".formeo-render .f-field-group input[required]")
        .not(".formeo-render .f-field-group input[restrict]");

    if (minlengthMaxlength?.length > 0) {
        await Promise.all(minlengthMaxlength.map(async function () {
            let $this = $(this);
            let minLength = $(this).attr("minLength");
            let maxLength = $(this).attr("maxLength");
            if ($this.is(':visible')) {
                let inputVal = this.value;
                $this.next(".dynamic-input-field").remove();
                if (inputVal.includes('<')) {
                    $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'>" + 'Special characters not allowed' + "</span></div>");
                    validationResults.push(false);
                    return;
                }
                if (inputVal.length > 0) {
                    $this.next(".dynamic-input-field").remove();
                    if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                        if (inputVal.length < minLength || inputVal.length > maxLength) {
                            $this.after(`<div class='dynamic-input-field field-validation-error'>
                                            <span class='required-field-msg'>Between ${minLength} to ${maxLength} characters</span>
                                         </div>`);
                            validationResults.push(false);
                        } else {
                            $this.next(".dynamic-input-field").remove();
                            validationResults.push(true);
                        }
                    }
                } else {
                    $this.next(".dynamic-input-field").remove();
                    validationResults.push(true); //because it's not req it's minlength, maxlength only.
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictRequiredNotMinlengthInputs = $(".formeo-render .f-field-group input[restrict][required]")
        .not(".formeo-render .f-field-group input[minlength]");

    if (restrictRequiredNotMinlengthInputs?.length > 0) {
        await Promise.all(restrictRequiredNotMinlengthInputs.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let inputVal = this?.value;
                if (inputVal) {
                    let fieldValidationResult = await formBuilderTextValidation($this, modulename);
                    validationResults.push(fieldValidationResult);
                } else {
                    $this.next(".dynamic-input-field").remove();
                    if (!pattern.test(inputVal)) {
                        $this.after(`<div class='dynamic-input-field field-validation-error'>
                                        <span class='required-field-msg'>Only & _ @ . - special characters allowed.</span>
                                      </div>`);
                        validationResults.push(false);
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictMinLengthNotRequiredInputs = $(".formeo-render .f-field-group input[restrict][minlength][maxlength]")
        .not(".formeo-render .f-field-group input[required]");

    if (restrictMinLengthNotRequiredInputs?.length > 0) {
        await Promise.all(restrictMinLengthNotRequiredInputs.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");
                if (inputVal) {
                    if (!pattern.test(inputVal)) {
                        $this.after(`<div class='dynamic-input-field field-validation-error'>
                                        <span class='required-field-msg'>Only & _ @ . - special characters allowed.</span>
                                      </div>`);
                        validationResults.push(false);
                    }
                    else {
                        if (inputVal.length > 0) {
                            if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(`<div class='dynamic-input-field field-validation-error'>
                                                    <span class='required-field-msg'>Between ${minLength} to ${maxLength} characters</span>
                                                  </div>`);
                                    validationResults.push(false);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                    validationResults.push(true);
                                }
                            }
                        }
                    }
                } else {
                    validationResults.push(true); //because it's not req it's restrict, minlength, maxlength only.
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredPath = $(".formeo-render .f-field-group input[required][input-type='path']"); //it's optional in 4.5

    if (requiredPath?.length > 0) {
        await Promise.all(requiredPath.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderRequiredPathValidation($this, modulename);
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredMinLengthNotRestrict = $(".formeo-render .f-field-group input[required][minlength][maxlength]")
        .not(".formeo-render .f-field-group input[restrict]");

    if (requiredMinLengthNotRestrict?.length > 0) {
        await Promise.all(requiredMinLengthNotRestrict.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");
                if (!inputVal) {
                    let fieldValidationResult = await formBuilderTextValidation($this, modulename);
                    validationResults.push(fieldValidationResult);
                }
                else {
                    $this.next(".dynamic-input-field").remove();
                    if (inputVal.includes('<')) {
                        $this.after("<div class='dynamic-input-field field-validation-error'><span class='required-field-msg'>" + 'Special characters not allowed' + "</span></div>");
                        validationResults.push(false);
                        return;
                    }
                    if (inputVal.length > 0) {
                        if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                            if (inputVal.length < minLength || inputVal.length > maxLength) {
                                $this.after(`<div class="dynamic-input-field field-validation-error">
                                                <span class="required-field-msg">
                                                  Between ${minLength} to ${maxLength} characters
                                                </span>
                                              </div>`);
                                validationResults.push(false);
                            } else {
                                $this.next(".dynamic-input-field").remove();
                                validationResults.push(true);
                            }
                        }
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredMinLengthRestrict = $(".formeo-render .f-field-group input[required][minlength][restrict][maxlength]");

    if (requiredMinLengthRestrict?.length > 0) {
        await Promise.all(requiredMinLengthRestrict.map(async function () {
            let $this = $(this);
            let id = $this.attr("id");
            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");
                if (!inputVal) {
                    let fieldValidationResult = await formBuilderTextValidation($this, modulename);
                    validationResults.push(fieldValidationResult);
                }
                else {
                    if (inputVal.length > 0) {
                        if (!pattern.test(inputVal)) {
                            $this.after(`<div class='dynamic-input-field field-validation-error'>
                                            <span class='required-field-msg'>
                                              Only & _ @ . - special characters allowed.
                                            </span>
                                          </div>`);
                            validationResults.push(false);
                        } else if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                            if (inputVal.length < minLength || inputVal.length > maxLength) {
                                $this.after(`<div class='dynamic-input-field field-validation-error'>
                                                <span class='required-field-msg'>
                                                  Between ${minLength} to ${maxLength} characters
                                                </span>
                                              </div>`);
                                validationResults.push(false);
                            } else {
                                $this.next(".dynamic-input-field").remove();
                                validationResults.push(true);
                            }
                        }
                    }
                }
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredURLInput = $(".formeo-render .f-field-group input[required][attrid='URLField']");

    if (requiredURLInput?.length > 0) {
        await Promise.all(requiredURLInput.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderRequiredURLValidation($this);
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredpassword = $(".formeo-render .f-field-group input[required][type='password']");

    if (requiredpassword?.length > 0) {
        await Promise.all(requiredpassword.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let placeholderValue = $($this)?.attr('placeholder');
                let fieldValidationResult = PasswordValidation(placeholderValue, $this, 'onsave');
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let restrictReqInputs = $(".formeo-render .f-field-group input[required][type='date']");

    if (restrictReqInputs?.length > 0) {
        await Promise.all(restrictReqInputs.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = dateFieldValidation(this.value, $this, 'onsave');
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredInputsTextArea = $(".formeo-render .f-field-group textarea[required]");

    if (requiredInputsTextArea?.length > 0) {
        await Promise.all(requiredInputsTextArea.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = textAreaFieldValidation($this, 'onsave');
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let requiredInputsIpAddress = $(".formeo-render .f-field-group input[required][attrid='ipAddressField']");

    if (requiredInputsIpAddress?.length > 0) {
        await Promise.all(requiredInputsIpAddress.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderIPAddressValidation($this);
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let selectInput = $(".formeo-render .f-field-group select[required]").not(".formeo-render .f-field-group select[multiple]");

    if (selectInput?.length > 0) {
        await Promise.all(selectInput.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let fieldValidationResult = await formBuilderSelectFieldValidation($this, modulename);
                validationResults.push(fieldValidationResult);
            }
        }));
    } else {
        validationResults.push(true);
    }

    let selectInput2 = $(".formeo-render .f-field-group select[multiple][required]");

    if (selectInput2?.length > 0) {
        await Promise.all(selectInput2.map(async function () {
            let $this = $(this);
            let id = $this?.attr("id");
            if ($this.is(':visible')) {
                let placeholderValue = $($this)?.attr('placeholder');
                let result;
                if (placeholderValue) {
                    result = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
                } else {
                    let labelText2 = "nodes";
                    result = labelText2?.toLowerCase();
                }
                let inputValue2 = $this?.val();
                inputValue2 = inputValue2?.filter(item => item !== '');
                let value;
                if (Array?.isArray(inputValue2)) {
                    if (inputValue2?.length > 0) {
                        value = "Not empty";
                    } else {
                        value = "";
                    }
                }
                if (typeof inputValue2 === 'string') {
                    value = $this?.val();
                    if (value && value?.toLowerCase()?.replace(/\*/g, '')?.includes('select')) value = ""
                }
                $this.next(".dynamic-select-tag").remove();

                if (value?.length === 0 || value === "") {
                    $this.after(`<div class='dynamic-select-tag field-validation-error-selecttag2'>
                                    <span class='required-field-msg'>${result}</span>
                                  </div>`);
                    validationResults.push(false);
                } else {
                    validationResults.push(true);
                }
            }
        }));
    } else {
        validationResults.push(true);
    }
    let allValid = validationResults.every(result => result === true);
    return allValid
};


function PasswordValidation(placeholderValue, $this, validation) {
    const customCasing = {
        esxi: 'ESXi'
    };
    let result;
    if (placeholderValue) {
        let firstLetterCapital = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
        result = firstLetterCapital?.replace(abbrevationLettersForValidation, function (match) {
            return customCasing[match.toLowerCase()] || match.toUpperCase();
        });
    } else {
        let associatedLabel2 = $('label[for="' + $this.attr('id') + '"]');
        let labelText2 = associatedLabel2?.text()?.replace(/\*/g, '');
        result = labelText2?.toLowerCase();
    }
    let passwordValue = $this?.val();
    $this.next(".dynamic-input-field").remove();
    if (passwordValue === "") {
        $this.after(`<div class='dynamic-input-field field-validation-error'>
                        <span class='required-field-msg'>${result}</span>
                      </div>`);
        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();
        if (validation === 'onsave') {
            return true;
        }
    }
}

function dateFieldValidation(value, $this, validation) {
    let inputValue = value;
    $this.next(".dynamic-input-field").remove();
    if (!inputValue) {
        $this.after(`<div class='dynamic-input-field field-validation-error'>
                        <span class='required-field-msg'>Select date</span>
                     </div>`);
        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();
        if (validation === 'onsave') {
            return true;
        }
    }
}

function textAreaFieldValidation($this, validation) {
    let inputValue = $this?.val();;
    $this.next(".dynamic-input-field").remove();
    if (!inputValue) {
        $this.after(`<div style="margin-top: -5px" class="dynamic-input-field field-validation-error">
                        <span class="required-field-msg">Enter text area</span>
                      </div>`);
        if (validation === 'onsave') {
            return false;
        }
    } else {
        $this.next(".dynamic-input-field").remove();
        if (validation === 'onsave') {
            return true;
        }
    }
}

async function requiredSudoSuTable() {
    let requiredSudoSuTable = $("table[required]");
    if (requiredSudoSuTable?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredSudoSuTable.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                var name = $(this).attr("name");
                if (name.toLowerCase().includes("sudo")) {
                    let type = await SubstituteAuthenticationType();
                    let user = await SubstituteAuthenticationUser();
                    if (type && user) {
                        validationResults.push(true);
                    } else {
                        validationResults.push(false);
                    }
                } else {
                    validationResults.push(true);
                }
            }
        }));
        let allValid = validationResults.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function SubstituteAuthenticationType() {
    let selectInput = $("select[name='SubstituteAuthenticationType']");
    if (selectInput?.length > 0) {
        const validationResults = await Promise.all(selectInput.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let associatedLabel = $this?.attr('data-placeholder');
                let toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
                $this.next(".dynamic-input-field").remove();
                let value = $this?.val()?.replace(/\s/g, "");
                if (!value) {
                    $this.after(`<div class="dynamic-input-field table-select-field-validation-error">
                                    <span class="required-field-msg">${toLowerCase}</span>
                                  </div>`);
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }));
        return validationResults.every(result => result);
    }
    return true;
}

async function tableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this?.attr('placeholder');
            var toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
            let inputValue2 = $this?.val();
            $this.next(".dynamic-input-field").remove();
            if (inputValue2 === "") {
                $this.after(`<div class="dynamic-input-field table-field-validation-error">
                                <span class="required-field-msg">${toLowerCase}</span>
                              </div>`);
                return false; // Indicate validation failure
            }
            else {
                return true; // Indicate validation success
            }
        } else {
            return true;
        }
    }));
    return validationResults.every(result => result);
}

async function SubstituteAuthenticationUser() {
    let requiredInputs = $("input[name='SubstituteAuthenticationUser']");
    if (requiredInputs?.length > 0) {
        return await tableFieldValidation(requiredInputs);
    }
    return true; // No inputs found, consider validation successful
}

async function zfsTableValidation() {
    let requiredTableField = $("input[requiredTableTextField]");
    if (requiredTableField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredTableField.map(async function () {
            let $this = $(this);
            let secondDiv = $this.closest('.input-field-error');
            if ($this.is(':visible')) {
                let fieldValidationResult = await zfstableFieldValidation($this, secondDiv);
                validationResults.push(fieldValidationResult);
            }
        }));
        let allValid = validationResults.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function zfstableFieldValidation(requiredInputs, selectDiv) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        if ($this.is(':visible')) {
            let associatedLabel = $this?.attr('placeholder');
            let toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
            let inputValue2 = $this?.val();
            selectDiv.next(".field-validation-error").remove();
            if (inputValue2 === "") {
                selectDiv.after("<span class='field-validation-error'> " + toLowerCase + "</span>");
                return false; // Indicate validation failure
            }
            else {
                return true; // Indicate validation success
            }
        } else {
            return true;
        }
    }));
    // Check if any validation failed
    return validationResults.every(result => result);
}

async function zfsTableSelectValidation() {
    let requiredTableField = $("select[requiredtableselectfield]");
    if (requiredTableField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredTableField.map(async function () {
            let $this = $(this);
            let secondDiv = $this.closest('.select-field-error');
            if ($this.is(':visible')) {
                let fieldValidationResult = await zfstableFieldValidation($this, secondDiv);
                validationResults.push(fieldValidationResult);
            }
        }));
        let allValid = validationResults.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function sourceDestinationValidation(className, error, replType) {
    let requiredTextField = $(className);
    if (requiredTextField?.length > 0) {
        let validateSourceDir = [];
        await Promise.all(requiredTextField.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let $sourceFieldErrorSpan = $this.closest('.form-group').find(error);
                let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
                //let placeHolder = $this.attr('placeholder');
                let value = $this.val();
                $sourceFieldErrorSpan.text("").removeClass("field-validation-error");
                if (value) {
                    if (replType.trim().toLowerCase().includes("rsync") ||
                        replType.trim().toLowerCase().includes("robocopy")) {
                        if (rsyncRobocopyPathForm.test(value)) {
                            validateSourceDir.push(true);
                        } else {
                            $sourceFieldErrorSpan.text("Enter valid path").addClass("field-validation-error");
                            validateSourceDir.push(false);
                        }
                    } else {
                        validateSourceDir.push(true);
                    }

                } else {
                    validateSourceDir.push(false);
                    $sourceFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                }
            }
        }));
        let allValid = validateSourceDir.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function propertiesSelectTag(propertyValue, error) {
    let requiredSelectField = $(propertyValue);
    if (requiredSelectField?.length > 0) {
        let validateProperty = [];
        await Promise.all(requiredSelectField.map(async function () {
            let $this = $(this);
            if ($this.is(':visible')) {
                let $propertyFieldErrorSpan = $this.closest('.form-group').find(error);
                let placeHolder = $this.attr('placeholder').charAt(0).toUpperCase() + $this.attr('placeholder').slice(1).toLowerCase();
                //let placeHolder = $this.attr('placeholder');
                let value = $this.val();
                $propertyFieldErrorSpan.text("").removeClass("field-validation-error");
                if (value) {
                    validateProperty.push(true);
                } else {
                    validateProperty.push(false);
                    $propertyFieldErrorSpan.text(placeHolder).addClass("field-validation-error");
                }
            }
        }));
        let allValid = validateProperty.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function lunsTableValidation() {
    let requiredLunsTableTextField = $("input[requiredLunsTableTextField]");
    if (requiredLunsTableTextField?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredLunsTableTextField.map(async function () {
            let $this = $(this);
            let secondDiv = $this.closest('.input-field-error');
            if ($this.is(':visible')) {
                let fieldValidationResult = await lunsTableFieldValidation(requiredLunsTableTextField, secondDiv);
                validationResults.push(fieldValidationResult);
            }
        }));
        let allValid = validationResults.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function lunsTableFieldValidation(requiredInputs) {
    const validationResults = await Promise.all(requiredInputs.map(async function () {
        let $this = $(this);
        let secondDiv = $this.closest('.input-field-error');
        if ($this.is(':visible')) {
            let associatedLabel = $this?.attr('placeholder');
            var toLowerCase = associatedLabel?.charAt(0)?.toUpperCase() + associatedLabel?.slice(1)?.toLowerCase();
            let inputValue2 = $this?.val();
            secondDiv?.next(".field-validation-error").remove();
            //$this.next(".dynamic-input-field").remove();
            if (inputValue2 === "") {
                secondDiv?.after("<span class='field-validation-error'> " + toLowerCase + "</span>");
                //$this.after("<div class='dynamic-input-field table-field-zfsvalidation-error'><span class='required-field-msg'> " + toLowerCase + "</span></div>");
                return false; // Indicate validation failure
            }
            else if (inputValue2 !== "") {
                if (/^[A-F0-9]+$/.test(inputValue2)) {
                    return true;
                } else {
                    secondDiv?.after("<span class='field-validation-error'> " + "A-F & 0-9" + "</span>");
                    //$this.after("<div class='dynamic-input-field table-field-zfsvalidation-error'><span class='required-field-msg'> " + "A-F & 0-9" + "</span></div>");
                    return false; // Indicate validation success
                }
            }
        } else {
            return true;
        }
    }));
    return validationResults.every(result => result);
}

//For Node.js source, target archieve log path
async function requiredsourcetargetPath() {
    let requiredsourcetargetPath = $(".formeo-render .f-field-group input[input-type='path']")
        .not(".formeo-render .f-field-group input[required][input-type='path']"); //[type='text']" it's optional in 4.5
    if (requiredsourcetargetPath?.length > 0) {
        let validationResults = [];
        await Promise.all(requiredsourcetargetPath.map(async function () {
            let $this = $(this);
            let id = $this.attr("id");
            let name = $this.attr("name").toLowerCase();
            if (name === "sourcearchivelogpath" || name === "targetarchivelogpath") {
                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let value = $this.val();
                    if (value) {
                        const regex = /^((?!.*\/\/.*)(?!.*\/ .*)\/{1}([^\\(){}:\*\?<>\|'])+.)$|[a-zA-Z]:(\\\w+)*([\\]|[.])?$/; //in CP-4.5
                        if (!regex.test(value)) {
                            $this.after(`<div class="dynamic-input-field field-validation-error">
                                            <span class="required-field-msg">Enter valid path</span>
                                          </div>`);
                            validationResults.push(false);
                        } else {
                            $this.next(".dynamic-input-field").remove();
                            validationResults.push(true);
                        }
                    } else {
                        $this.next(".dynamic-input-field").remove();
                        validationResults.push(true); //this is optional field
                    }
                }
            }
        }));
        let allValid = validationResults.every(result => result === true);
        return allValid;
    } else {
        return true;
    }
}

async function inputFormValidation(modulename, replType) {
    let inputReq = await inputRequired(modulename);
    //let sudoSuTable = true;
    let validateZfsTable = true;
    let selectValidationZfsTable = true;
    let validatelunsTable = true;
    let validateSourceTargetPath = true;
    let validateRsyncSource = true;
    let validateRsyncDestination = true;
    let validateRsyncProprty = true;
    if (modulename === 'replication') {
        validateZfsTable = await zfsTableValidation();
        selectValidationZfsTable = await zfsTableSelectValidation();
        validatelunsTable = await lunsTableValidation();
        if (replType === "rsync" || replType === "robocopy") {
            validateRsyncSource = await sourceDestinationValidation("input.sourceDirectory", '.sourceFieldError', replType);
            validateRsyncDestination = await sourceDestinationValidation("input.destinationDirectory", '.destinationFieldError', replType);
            validateRsyncProprty = await propertiesSelectTag('select[class^="replicationProperties"]', '.propertyError');
        }
    }
    //don't delete important.
    //if (modulename === 'server') {
    //    sudoSuTable = await requiredSudoSuTable(); //Previously not used(boomi asked to disable for save).
    //}
    if (modulename === "node") {
        validateSourceTargetPath = await requiredsourcetargetPath();
    }
    if (inputReq && validateZfsTable && selectValidationZfsTable && validatelunsTable &&
        validateSourceTargetPath && validateRsyncSource && validateRsyncDestination && validateRsyncProprty) {
        return true;
    } else {
        return false;
    }
}

function onChangeTextFieldValidation(modulename) {
    let requiredInputsMinLengthRestrict = $(".formeo-render .f-field-group input[required][input-type='text'][minlength][restrict]")
        .not(".formeo-render .f-field-group input[name='VirtualIPAddress']")
        .not(".formeo-render .f-field-group input[readonly='readonly']");

    if (requiredInputsMinLengthRestrict?.length) {
        requiredInputsMinLengthRestrict?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    let res = await formBuilderTextValidation($this, modulename);
                    if (res) {
                        if (inputVal.length > 0) {
                            if (!pattern.test(inputVal)) {
                                $this.after(`<div class="dynamic-input-field field-validation-error">
                                            <span class="required-field-msg">
                                              Only & _ @ . - special characters allowed.
                                            </span>
                                          </div>`);
                            } else if ((!minLength && !maxLength) || (minLength > 0 && maxLength > 0)) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(`<div class="dynamic-input-field field-validation-error">
                                                <span class="required-field-msg">
                                                  Between ${minLength} to ${maxLength} characters
                                                </span>
                                              </div>`);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {
                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;
                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let requiredInputsMinLength = $(".formeo-render .f-field-group input[required][input-type='text'][minlength]")
        .not(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[name='VirtualIPAddress']")
        .not(".formeo-render .f-field-group input[readonly='readonly']");

    if (requiredInputsMinLength?.length) {
        requiredInputsMinLength?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    const intMinValue = parseInt(minLength, 10);
                    const intMaxValue = parseInt(maxLength, 10);
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters'
                    $this.next(".dynamic-input-field").remove();
                    let res = await formBuilderTextValidation($this, modulename);
                    if (res) {
                        if ((!minLength && !maxLength) || (intMinValue >= 0 && intMaxValue > 0)) {
                            if (inputVal.length < intMinValue || inputVal.length > intMaxValue) {
                                $this.after(`<div class='dynamic-input-field field-validation-error'>
                                                <span class='required-field-msg'>
                                                  Between ${minLength} to ${maxLength} ${errorMessage}
                                                </span>
                                              </div>`);
                            } else {
                                $this.next(".dynamic-input-field").remove();
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {
                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;
                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let inputsMinLength = $(".formeo-render .f-field-group input[input-type='text'][minlength]")
        .not(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[required]")
        .not(".formeo-render .f-field-group input[name='VirtualIPAddress']")
        .not(".formeo-render .f-field-group input[readonly='readonly']");

    if (inputsMinLength?.length) {
        inputsMinLength?.each(function () {
            let $this = $(this);
            let id = $(this)?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", function (event) {
                if ($this.is(':visible')) {
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters';
                    $this.next(".dynamic-input-field").remove();
                    if (inputVal.includes('<')) {
                        $this.after(`<div class='dynamic-input-field field-validation-error'>
                                        <span class='required-field-msg'>" + 'Special characters not allowed' + "</span>
                                      </div>`);
                        return;
                    }
                    if (inputVal.length > 0) {
                        if (inputVal.length >= minLength && inputVal.length <= maxLength) {
                            $this.next(".dynamic-input-field").remove();
                            if ((!minLength && !maxLength) || (minLength > 0 && maxLength > 0)) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(`<div class='dynamic-input-field field-validation-error'>
                                                <span class='required-field-msg'>
                                                    Between ${minLength} to ${maxLength} ${errorMessage}
                                                </span>
                                             </div>`);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        } else {
                            event.preventDefault();
                        }
                    } else {
                        $this.next(".dynamic-input-field").remove();
                    }
                }
            });
        });
    }

    let requiredInputsText = $(".formeo-render .f-field-group input[required][input-type='text']")
        .not(".formeo-render .f-field-group input[minlength]")
        .not(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[name='VirtualIPAddress']")
        .not(".formeo-render .f-field-group input[readonly='readonly']");

    if (requiredInputsText?.length) {
        requiredInputsText?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    let value = $(this).val();
                    let sanitizedValue = value.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
                    $(this).val(sanitizedValue);
                    await formBuilderTextValidation($this, modulename);
                }
            });
        });
    }

    let requiredURLInput = $(".formeo-render .f-field-group input[required][attrid='URLField']");

    if (requiredURLInput?.length) {
        requiredURLInput?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    let value = $(this).val();
                    let sanitizedValue = value.replace(/\s{2,}/g, ' '); //Restrict Multiple spaces
                    $(this).val(sanitizedValue);
                    await formBuilderRequiredURLValidation($this)
                }
            });
        });
    }
}

function onChangeFormBuilderValidation(modulename) {
    if (modulename === 'server') {
        let requiredSudoSuTable = $("table[required]");
        if (requiredSudoSuTable?.length > 0) {
            requiredSudoSuTable?.each(function () {
                let $this = $(this);
                let className = $this.attr("class");
                var elements = document.getElementsByClassName(className);
                if (elements.length > 0) {
                    // Add event listener to the first element in the collection (elements[0])
                    elements[0].addEventListener("keyup", async function () {
                        // Inside the event listener function
                        if ($(this).is(':visible')) { // Using jQuery inside event listener
                            await SubstituteAuthenticationType();
                            await SubstituteAuthenticationUser();
                        }
                    });
                }
            });
        }
    }

    if (modulename === "node") {
        //For Node.js source, target archieve log path
        let requiredsourcetargetPath = $(".formeo-render .f-field-group input[input-type='path']")
            .not(".formeo-render .f-field-group input[required][input-type='path']"); //[type='text']" it's optional in 4.5
        requiredsourcetargetPath?.each(function () {
            let $this = $(this);
            let id = $this.attr("id");
            let name = $this.attr("name").toLowerCase();
            if (name === "sourcearchivelogpath" || name === "targetarchivelogpath") {
                document.getElementById(id)?.addEventListener("keyup", async function () {
                    if ($this.is(':visible')) {
                        $this.next(".dynamic-input-field").remove();
                        let value = $this.val();
                        if (value) {
                            const regex = /^((?!.*\/\/.*)(?!.*\/ .*)\/{1}([^\\(){}:\*\?<>\|'])+.)$|[a-zA-Z]:(\\\w+)*([\\]|[.])?$/; //in CP-4.5
                            if (!regex.test(value)) {
                                $this.next(".dynamic-input-field").remove();
                                $this.after(`<div class="dynamic-input-field field-validation-error">
                                                <span class="required-field-msg">Enter valid path</span>
                                              </div>`);
                            }
                        }
                    }
                });
            }
        });
    }

    //Text field req, res, min, max.
    onChangeTextFieldValidation(modulename);

    let requiredpassword = $(".formeo-render .f-field-group input[required][type='password']");
    requiredpassword?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id)?.addEventListener("keyup", async function () {
            if ($this.is(':visible')) {
                let placeholderValue = $($this)?.attr('placeholder');
                PasswordValidation(placeholderValue, $this, 'onchange');
            }
        });
    });

    let EncyDecrpassword = $(".formeo-render .f-field-group input[type='password']");//when it's optional also
    EncyDecrpassword?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id)?.addEventListener("focus", async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await DecryptPassword($thisval)//Common.js.
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });

        document.getElementById(id)?.addEventListener('blur', async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await EncryptPassword($thisval)//Common.js.
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
    });

    let requiredPath = $(".formeo-render .f-field-group input[required][input-type='path']")
        .not(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[minlength]")
        .not(".formeo-render .f-field-group input[maxlength]")
    if (requiredPath?.length > 0) {
        requiredPath?.each(function () {
            let $this = $(this);
            let id = $this.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    await formBuilderRequiredPathValidation($this, modulename);
                }
            });
        });
    }

    let requiredInputsMinLength = $(".formeo-render .f-field-group input[required][input-type='path'][maxlength]")
        .not(".formeo-render .f-field-group input[restrict]")

    if (requiredInputsMinLength?.length) {
        requiredInputsMinLength?.each(function () {
            let $this = $(this);
            let id = $this?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", async function () {
                if ($this.is(':visible')) {
                    $this.next(".dynamic-input-field").remove();
                    let inputVal = this.value;
                    let minLength = $(this)?.attr("minLength");
                    let maxLength = $(this)?.attr("maxLength");
                    const intMinValue = parseInt(minLength, 10);
                    const intMaxValue = parseInt(maxLength, 10);
                    let type = $(this)?.attr("type");
                    let errorMessage = type === 'number' ? 'digits' : 'characters'
                    $this.next(".dynamic-input-field").remove();
                    let pathRes = await formBuilderRequiredPathValidation($this, modulename);
                    if (pathRes) {
                        let res = await formBuilderTextValidation($this, modulename);
                        if (res) {
                            if ((!minLength && !maxLength) || (intMinValue >= 0 && intMaxValue > 0)) {
                                if (inputVal.length < intMinValue || inputVal.length > intMaxValue) {
                                    $this.after(`<div class='dynamic-input-field field-validation-error'>
                                                <span class='required-field-msg'>
                                                  Between ${minLength} to ${maxLength} ${errorMessage}
                                                </span>
                                              </div>`);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            });

            document.getElementById(id)?.addEventListener("keydown", async function (event) {
                if ($this.is(':visible')) {
                    if (event.key === ' ') {
                        const value = this.value;
                        const selectionStart = this.selectionStart;
                        const len = value.trim().length;
                        if (len < selectionStart) {
                            event.preventDefault(); // Prevent adding more than two consecutive spaces
                        }
                    }
                }
            });
        });
    }

    let requiredTextarea = $(".formeo-render .f-field-group textarea[required]");
    if (requiredTextarea?.length > 0) {
        requiredTextarea?.each(function () {
            let $this = $(this);
            let id = $(this)?.attr("id");
            document.getElementById(id)?.addEventListener("keyup", function () {
                if ($this.is(':visible')) {
                    textAreaFieldValidation($this, 'onchange');
                }
            });
        });
    }

    let restrictRequiredDate = $(".formeo-render .f-field-group input[required][type='date']");
    if (restrictRequiredDate?.length > 0) {
        restrictRequiredDate?.each(function () {
            let $this = $(this);
            let id = $(this)?.attr("id");
            document.getElementById(id)?.addEventListener("change", function () {
                if ($this.is(':visible')) {
                    dateFieldValidation(this.value, $this, 'onchange');
                }
            });
        });
    }

    let restrictRequiredInputs = $(".formeo-render .f-field-group input[restrict]")
        .not(".formeo-render .f-field-group input[required][minlength]");
    restrictRequiredInputs?.each(function () {
        let $this = $(this);
        let id = $(this)?.attr("id");
        document.getElementById(id)?.addEventListener("keyup", function () {
            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputValue = this.value;
                if (inputValue) {
                    if (!pattern.test(inputValue)) {
                        $this.after(`<div class="dynamic-input-field field-validation-error">
                                        <span class="required-field-msg">Only & _ @ . - special characters allowed.</span>
                                     </div>`);
                    }
                }
            }
        });
    });


    //[type='number'] //Shoule be seperate. if give input char e cleared input field.
    let requiredInputsNumber = $(".formeo-render .f-field-group input[required][type='number']")
        .not(".formeo-render .f-field-group input[minlength][restrict]")
        .not(".formeo-render .f-field-group input[name='VirtualIPAddress'][readonly='readonly']");
    requiredInputsNumber?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        function handleEvent(event) {
            if ($this.is(':visible')) {
                if (event.key === 'e' || event.key === 'E' || event.key === '-') {
                    event.preventDefault();
                } else {
                    formBuilderTextValidation($this, modulename);
                }
            }
        }

        let element = document.getElementById(id);
        element?.addEventListener("keyup", handleEvent);
        element?.addEventListener("keydown", handleEvent);
    });

    let restrictRequiredNotMinlengthInputs = $(".formeo-render .f-field-group input[restrict][required]")
        .not(".formeo-render .f-field-group input[minlength]");
    restrictRequiredNotMinlengthInputs?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        document.getElementById(id)?.addEventListener("keyup", async function () {
            if ($this.is(':visible')) {
                let inputVal = this.value;
                if (!inputVal) {
                    await formBuilderTextValidation($this, modulename);
                } else {
                    $this.next(".dynamic-input-field").remove();
                    if (!pattern.test(inputVal)) {
                        $this.after(`<div class='dynamic-input-field field-validation-error'>
                                        <span class='required-field-msg'>Only & _ @ . - special characters allowed.</span>
                                     </div>`);
                    } else {
                        $this.next(".dynamic-input-field").remove();
                    }
                }
            }
        });

        document.getElementById(id)?.addEventListener("keydown", async function (event) {
            if ($this.is(':visible')) {
                if (event.key === ' ') {
                    const value = this.value;
                    const selectionStart = this.selectionStart;
                    const len = value.trim().length;
                    if (len < selectionStart) {
                        event.preventDefault(); // Prevent adding more than two consecutive spaces
                    }
                }
            }
        });
    });

    let restrictMinLengthNotRequiredInputs = $(".formeo-render .f-field-group input[restrict][minlength]")
        .not(".formeo-render .f-field-group input[required]");
    restrictMinLengthNotRequiredInputs?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        document.getElementById(id)?.addEventListener("keyup", async function () {
            if ($this.is(':visible')) {
                $this.next(".dynamic-input-field").remove();
                let inputVal = this.value;
                let minLength = $(this)?.attr("minLength");
                let maxLength = $(this)?.attr("maxLength");
                if (inputVal) {
                    if (!pattern.test(inputVal)) {
                        $this.after(`<div class="dynamic-input-field field-validation-error">
                                        <span class="required-field-msg">
                                          Only & _ @ . - special characters allowed.
                                        </span>
                                      </div>`);
                    } else {
                        if (inputVal.length > 0) {
                            if (!minLength && !maxLength || minLength > 0 && maxLength > 0) {
                                if (inputVal.length < minLength || inputVal.length > maxLength) {
                                    $this.after(`<div class="dynamic-input-field field-validation-error">
                                                    <span class="required-field-msg">
                                                      Between ${minLength} to ${maxLength} characters
                                                    </span>
                                                  </div>`);
                                } else {
                                    $this.next(".dynamic-input-field").remove();
                                }
                            }
                        }
                    }
                }
            }
        });
    });

    //['ipAddressField']
    let requiredInputsIpAddress = $(".formeo-render .f-field-group input[required][attrid='ipAddressField']");
    requiredInputsIpAddress?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        document.getElementById(id)?.addEventListener("keyup", function () {
            if ($this.is(':visible')) {
                formBuilderIPAddressValidation($this);
            }
        });
    });

    function readonly(ipAddressHostName) {
        let inputs = document.querySelectorAll(ipAddressHostName);
        if (inputs.length > 0) {
            inputs?.forEach(function (input) {
                let id = input?.id;
                document.getElementById(id)?.addEventListener("keydown", async function (event) {
                    event.preventDefault();
                    input.setAttribute("readonly", "readonly");
                });
            });
        }
    }
    readonly("input[name='IpAddress'][readonly='readonly']");
    readonly("input[name='HostName'][readonly='readonly']");
    readonly("input[name='VirtualIPAddress'][readonly='readonly']");

    let requiredSelects = $(".formeo-render .f-field-group select[required]")
        .not(".formeo-render .f-field-group select[multiple]");
    requiredSelects?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        $("#" + id).on("change", async function () {
            if ($this.is(':visible')) {
                restrictSelectEmptyFields = true;
                let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                disableSelectTagTitle.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.attr('title', '');
                });

                restrictSelectEmptyFields = await formBuilderSelectFieldValidation($this, modulename);

                //Remove SSH Password Validation
                let SSHUser = $("input[name='SSHUser']");
                SSHUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let SSHPassword = $("input[name='SSHPassword']");
                SSHPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove SSH Key Validation
                let SSHKeyUser = $("input[name='SSHKeyUser']");
                SSHKeyUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let SSHKeyPath = $("input[name='SSHKeyPath']");
                SSHKeyPath?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove WMI Validation
                let WMIUser = $("input[name='WMIUser']");
                WMIUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let WMIPassword = $("input[name='WMIPassword']");
                WMIPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });

                //Remove PowerShell Validation
                let PowerShellUser = $("input[name='PowerShellUser']");
                PowerShellUser?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
                let PowerShellPassword = $("input[name='PowerShellPassword']");
                PowerShellPassword?.each(function () {
                    let $this2 = $(this);
                    $this2.next(".dynamic-input-field").remove();
                });
            }
        });
    });

    let requiredSelects2 = $(".formeo-render .f-field-group select[multiple][required]");
    requiredSelects2?.each(function () {
        let $this = $(this);
        let id = $this?.attr("id");
        $("#" + id).on("change", async function () {
            if ($this.is(':visible')) {
                restrictSelectEmptyFields = true;
                let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                disableSelectTagTitle.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.attr('title', '');
                });

                let placeholderValue = $($this)?.attr('placeholder');
                let result;
                if (placeholderValue) {
                    result = placeholderValue?.charAt(0)?.toUpperCase() + placeholderValue?.slice(1)?.toLowerCase();
                } else {
                    let labelText2 = "nodes";
                    result = labelText2?.toLowerCase();
                }
                let inputValue2 = $this?.val();
                inputValue2 = inputValue2?.filter(item => item !== '');
                let value;
                if (Array?.isArray(inputValue2)) {
                    if (inputValue2?.length > 0) {
                        value = 'Not empty';
                    } else {
                        value = "";
                    }
                }
                if (typeof inputValue2 === 'string') {
                    value = $this?.val();
                    if (value && value?.toLowerCase()?.replace(/\*/g, '')?.includes('select')) value = ""
                }
                $this.next(".dynamic-select-tag").remove();
                $this.next(".dynamic-input-field").remove();

                if (value?.length === 0 || value === "") {
                    $this.after(`<div class="dynamic-select-tag field-validation-error-selecttag2">
                                    <span class="required-field-msg">${result}</span>
                                  </div>`);
                    restrictSelectEmptyFields = false;
                }
            }
        });
    });
}