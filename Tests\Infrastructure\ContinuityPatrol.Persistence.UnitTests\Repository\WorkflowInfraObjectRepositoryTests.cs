using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowInfraObjectRepositoryTests : IClassFixture<WorkflowInfraObjectFixture>,IClassFixture<InfraObjectFixture>,IClassFixture<WorkflowFixture>
    {
        private readonly WorkflowInfraObjectFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowInfraObjectRepository _repositoryParent;
        private readonly WorkflowInfraObjectRepository _repositoryIsNotParent;
        private readonly WorkflowInfraObjectRepository _repositoryAllInfra;
        private readonly InfraObjectFixture _infraObjectFixture;
        private readonly WorkflowFixture _workflowFixture;

        public WorkflowInfraObjectRepositoryTests(WorkflowInfraObjectFixture fixture, InfraObjectFixture infraObjectFixture, WorkflowFixture workflowFixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repositoryParent = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
            _repositoryIsNotParent = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
            _repositoryAllInfra = new WorkflowInfraObjectRepository(_dbContext, DbContextFactory.GetMockUserService());
            _infraObjectFixture = infraObjectFixture;
            _workflowFixture = workflowFixture;
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAll_WhenIsAllInfra()
        {
            await _dbContext.WorkflowInfraObjects.AddRangeAsync(_fixture.WorkflowInfraObjectList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.ListAllAsync();

            Assert.Equal(_fixture.WorkflowInfraObjectList.Count, result.Count);
        }

        [Fact]
        public async Task ListAllAsync_ReturnsAssigned_WhenNotAllInfra()
        {
            await _dbContext.WorkflowInfraObjects.AddRangeAsync(_fixture.WorkflowInfraObjectList);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.ListAllAsync();

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenIsParent()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetByReferenceIdAsync_ReturnsEntity_WhenNotParent()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.CompanyId = "ChHILD_COMPANY_123";
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryIsNotParent.GetByReferenceIdAsync(entity.ReferenceId);

            Assert.NotNull(result);
            Assert.Equal(entity.ReferenceId, result.ReferenceId);
        }

        [Fact]
        public async Task GetInfraObjectIdAttachByWorkflowId_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);  
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetInfraObjectIdAttachByWorkflowId(entity.WorkflowId, entity.InfraObjectId, entity.ActionType);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.InfraObjectId, result.InfraObjectId);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;

            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectFromInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetResilienceWorkflowByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.ActionType = "Resiliency Ready";
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetResilienceWorkflowByInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectDetailByInfraObjectId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectDetailByInfraObjectId(entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetInfraObjectFromWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetInfraObjectFromWorkflowId(entity.WorkflowId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectFromWorkflowId_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectFromWorkflowId(entity.WorkflowId, entity.InfraObjectId);

            Assert.All(result, x => Assert.Equal(entity.WorkflowId, x.WorkflowId));
            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdAsync_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdAsync(entity.WorkflowId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetWorkflowIdAttachByActionType_ReturnsEntity_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowIdAttachByActionType(entity.WorkflowId, entity.ActionType);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
            Assert.Equal(entity.ActionType, result.ActionType);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByInfraObjectIdAndActionType_ReturnsList_WhenIsAllInfra()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.ActionType = "TestType";
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByInfraObjectIdAndActionType(entity.InfraObjectId, "TestType");

            Assert.All(result, x => Assert.Equal(entity.InfraObjectId, x.InfraObjectId));
            Assert.All(result, x => Assert.Equal("TestType", x.ActionType));
        }

        [Fact]
        public async Task WorkflowIdAndInfraObjectIdUnique_ReturnsTrue_WhenExists()
        {
            await _dbContext.Workflows.AddRangeAsync(_workflowFixture.WorkflowDto);
            await _dbContext.InfraObjects.AddAsync(_infraObjectFixture.InfraObjectDto);
            await _dbContext.SaveChangesAsync();

            var entity = _fixture.WorkflowInfraObjectDto;
            entity.WorkflowId = _workflowFixture.WorkflowDto.ReferenceId;
            entity.InfraObjectId = _infraObjectFixture.InfraObjectDto.ReferenceId;
            entity.IsAttach = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.WorkflowIdAndInfraObjectIdUnique(entity.WorkflowId, entity.InfraObjectId, entity.ActionType);

            Assert.True(result);
        }

        [Fact]
        public async Task WorkflowIdAndInfraObjectIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.WorkflowIdAndInfraObjectIdUnique("non-existent", "non-existent", "non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.IsWorkflowIdUnique(entity.WorkflowId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsWorkflowIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.IsWorkflowIdUnique("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task IsInfraObjectIdUnique_ReturnsTrue_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.IsInfraObjectIdUnique(entity.InfraObjectId);

            Assert.True(result);
        }

        [Fact]
        public async Task IsInfraObjectIdUnique_ReturnsFalse_WhenNotExists()
        {
            var result = await _repositoryAllInfra.IsInfraObjectIdUnique("non-existent");

            Assert.False(result);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdForWorkflowList_ReturnsEntity_WhenExists()
        {
            var entity = _fixture.WorkflowInfraObjectDto;
            entity.IsActive = true;
            await _dbContext.WorkflowInfraObjects.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdForWorkflowList(entity.WorkflowId);

            Assert.NotNull(result);
            Assert.Equal(entity.WorkflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetWorkflowInfraObjectByWorkflowIdForWorkflowList_ReturnsNull_WhenNotExists()
        {
            var result = await _repositoryAllInfra.GetWorkflowInfraObjectByWorkflowIdForWorkflowList("non-existent");

            Assert.Null(result);
        }
    }
}