using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SvcMsSqlMonitorStatusFixture : IDisposable
{
    public List<SvcMsSqlMonitorStatus> SvcMsSqlMonitorStatusPaginationList { get; set; }
    public List<SvcMsSqlMonitorStatus> SvcMsSqlMonitorStatusList { get; set; }
    public SvcMsSqlMonitorStatus SvcMsSqlMonitorStatusDto { get; set; }


    public ApplicationDbContext DbContext { get; private set; }

    public SvcMsSqlMonitorStatusFixture()
    {
        var fixture = new Fixture();

        SvcMsSqlMonitorStatusList = fixture.Create<List<SvcMsSqlMonitorStatus>>();

        SvcMsSqlMonitorStatusPaginationList = fixture.CreateMany<SvcMsSqlMonitorStatus>(20).ToList();

      SvcMsSqlMonitorStatusDto = fixture.Create<SvcMsSqlMonitorStatus>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
