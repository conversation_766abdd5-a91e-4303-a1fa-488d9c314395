using ContinuityPatrol.Application.Features.DriftProfile.Events.Delete;

namespace ContinuityPatrol.Application.Features.DriftProfile.Commands.Delete;

public class DeleteDriftProfileCommandHandler : IRequestHandler<DeleteDriftProfileCommand, DeleteDriftProfileResponse>
{
    private readonly IDriftProfileRepository _driftProfileRepository;
    private readonly IPublisher _publisher;

    public DeleteDriftProfileCommandHandler(IDriftProfileRepository driftProfileRepository, IPublisher publisher)
    {
        _driftProfileRepository = driftProfileRepository;

        _publisher = publisher;
    }

    public async Task<DeleteDriftProfileResponse> Handle(DeleteDriftProfileCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _driftProfileRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DriftProfile),
            new NotFoundException(nameof(Domain.Entities.DriftProfile), request.Id));

        eventToDelete.IsActive = false;

        await _driftProfileRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDriftProfileResponse
        {
            Message = Message.Delete("Drift Profile", eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DriftProfileDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}