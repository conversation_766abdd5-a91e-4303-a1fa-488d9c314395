﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class FormRepository : BaseRepository<Form>, IFormRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public FormRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<List<Form>> GetFormNames()
    {
        return _dbContext.Forms
            .Active()
            .Select(x => new Form { ReferenceId = x.ReferenceId, Name = x.Name, IsPublish = x.IsPublish })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }


    public Task<bool> IsFormNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsFormNameUnique(string name)
    {
        var matches = _dbContext.Forms.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public async Task<List<string>> IsFormNamesUnique(List<string> name)
    {
        var matches = await _dbContext.Forms
            .AsNoTracking()
            .Where(e => name.Contains(e.Name))
            .Select(e => e.Name)
            .ToListAsync();

        return matches;
    }

    public async Task<List<Form>> GetFormsByIds(List<string> ids)
    {
        return await _dbContext.Forms
            .AsNoTracking()
            .Where(e => ids.Contains(e.ReferenceId))
            .ToListAsync();
    }


    public Task<List<Form>> GetFormType(string type)
    {
        var matches =SelectForm(_dbContext.Forms.Active().Where(x => x.Type.ToLower().Equals(type) && x.IsPublish))
            .ToList();

        return Task.FromResult(matches);
    }

    public async Task<PaginatedResult<Form>> GetFormByTypeQueryable(string type, int pageNumber, int pageSize, Specification<Form> productFilterSpec,string sortColumn,string sortOrder)
    {
        return await SelectForm(Entities
                .Specify(productFilterSpec)
                .Where(x => x.Type.Equals(type))
                .DescOrderById())
                .ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }
    public override async Task<PaginatedResult<Form>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Form> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await SelectForm(Entities
                .Specify(productFilterSpec)
                .DescOrderById())
                .ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }
    public override IQueryable<Form> GetPaginatedQuery()
    {
       
        return Entities.Where(x => x.IsActive)
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }
    private IQueryable<Form> SelectForm(IQueryable<Form> query)
    {
        return query.Select(x => new Form
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            CompanyId = x.CompanyId,
            Type = x.Type,
            Properties = x.Properties,
            Version = x.Version,
            IsPublish = x.IsPublish,
            IsLock = x.IsLock,
            LastModifiedDate=x.LastModifiedDate

        });
    }
}