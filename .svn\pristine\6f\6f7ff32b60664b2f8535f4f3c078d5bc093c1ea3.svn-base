using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TableAccessFixture : IDisposable
{
    public List<TableAccess> TableAccessPaginationList { get; set; }
    public List<TableAccess> TableAccessList { get; set; }
    public TableAccess TableAccessDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string TestSchemaName = "dbo";
    public const string TestTableName = "Users";

    public ApplicationDbContext DbContext { get; private set; }

    public TableAccessFixture()
    {
        var fixture = new Fixture();

        TableAccessList = fixture.Create<List<TableAccess>>();

        TableAccessPaginationList = fixture.CreateMany<TableAccess>(20).ToList();

        TableAccessPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        TableAccessPaginationList.ForEach(x => x.IsActive = true);

        TableAccessList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        TableAccessList.ForEach(x => x.IsActive = true);

        TableAccessDto = fixture.Create<TableAccess>();
        TableAccessDto.ReferenceId = Guid.NewGuid().ToString();
        TableAccessDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public TableAccess CreateTableAccess(
        string schemaName = TestSchemaName,
        string tableName = TestTableName,
        bool isChecked = false,
        bool isActive = true)
    {
        return new TableAccess
        {
            ReferenceId = Guid.NewGuid().ToString(),
            SchemaName = schemaName,
            TableName = tableName,
            IsChecked = isChecked,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<TableAccess> CreateMultipleTableAccesses(
        int count,
        string schemaName = TestSchemaName,
        bool isActive = true)
    {
        var tableAccesses = new List<TableAccess>();
        for (int i = 1; i <= count; i++)
        {
            tableAccesses.Add(CreateTableAccess(
                schemaName: schemaName,
                tableName: $"Table_{i:D3}",
                isChecked: i % 2 == 0,
                isActive: isActive
            ));
        }
        return tableAccesses;
    }

    public TableInformation CreateTableInformation(
        string tableCatalog = "TestDB",
        string tableSchema = TestSchemaName,
        string tableName = TestTableName)
    {
        return new TableInformation
        {
            TableCatalog = tableCatalog,
            TableSchema = tableSchema,
            TableName = tableName
        };
    }

    public List<TableInformation> CreateMultipleTableInformations(
        int count,
        string tableCatalog = "TestDB",
        string tableSchema = TestSchemaName)
    {
        var tableInformations = new List<TableInformation>();
        for (int i = 1; i <= count; i++)
        {
            tableInformations.Add(CreateTableInformation(
                tableCatalog: tableCatalog,
                tableSchema: tableSchema,
                tableName: $"Table_{i:D3}"
            ));
        }
        return tableInformations;
    }

    public Archive CreateArchive(
        string companyId = CompanyId,
        string tableNameProperties = null)
    {
        return new Archive
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = companyId,
            TableNameProperties = tableNameProperties ?? Guid.NewGuid().ToString(),
            ArchiveProfileName = "Test Archive Profile",
            Count = 100,
            CronExpression = "0 0 12 * * ?",
            ScheduleTime = "12:00",
            ScheduleType = 1,
            BackUpType = "Full",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public DataSet CreateDataSet(
        string tableAccessId = null)
    {
        return new DataSet
        {
            ReferenceId = Guid.NewGuid().ToString(),
            TableAccessId = tableAccessId ?? Guid.NewGuid().ToString(),
            DataSetName = "Test DataSet",
            CompanyId = CompanyId,
            Description = "Test DataSet Description",
            StoredQuery = "SELECT * FROM TestTable",
            PrimaryTableName = "TestTable",
            PrimaryTablePKColumn = "Id",
            QueryType = "SELECT",
            StoredProcedureName = "",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}