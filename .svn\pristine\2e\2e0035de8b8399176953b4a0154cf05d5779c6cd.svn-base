﻿using ContinuityPatrol.Application.Features.MonitorService.Event.Update;

namespace ContinuityPatrol.Application.Features.MonitorService.Command.Update;

public class
    UpdateMonitorServiceCommandHandler : IRequestHandler<UpdateMonitorServiceCommand, UpdateMonitorServiceResponse>
{
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepository;
    private readonly IPublisher _publisher;

    public UpdateMonitorServiceCommandHandler(IMonitorServiceRepository monitorServiceRepository, IMapper mapper,
        IPublisher publisher)
    {
        _monitorServiceRepository = monitorServiceRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateMonitorServiceResponse> Handle(UpdateMonitorServiceCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _monitorServiceRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.MonitorService), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateMonitorServiceCommand),
            typeof(Domain.Entities.MonitorService));

        await _monitorServiceRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateMonitorServiceResponse
        {
            //Message = Message.Update(nameof(Domain.Entities.MonitorService), eventToUpdate.InfraObjectName),
            Message = Message.Update("Monitoring Service", eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new MonitorServiceUpdatedEvent { InfraObjectName = eventToUpdate.InfraObjectName },
            cancellationToken);

        return response;
    }
}