﻿using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Queries;

public class GetTeamMasterPaginatedListQueryHandlerTests : IClassFixture<TeamMasterFixture>, IClassFixture<TeamResourceFixture>
{
    private readonly GetTeamMasterPaginatedListQueryHandler _handler;

    private readonly Mock<ITeamMasterRepository> _mockTeamMasterRepository;

    public GetTeamMasterPaginatedListQueryHandlerTests(TeamMasterFixture teamMasterFixture, TeamResourceFixture teamResourceFixture)
    {
        var teamMasterNewFixture = teamMasterFixture;
        var teamResourceNewFixture = teamResourceFixture;

        teamMasterNewFixture.TeamMasters[0].GroupName = "Development_Team";
        teamMasterNewFixture.TeamMasters[0].Description = "Data_Analytics";

        teamMasterNewFixture.TeamMasters[1].GroupName = "Development_Team123";
        teamMasterNewFixture.TeamMasters[1].Description = "Data_Analytics123";

        _mockTeamMasterRepository = TeamMasterRepositoryMocks.GetPaginatedTeamMasterRepository(teamMasterNewFixture.TeamMasters);
        
        var mockTeamResourceRepository = TeamResourceRepositoryMocks.GetPaginatedTeamResourceRepository(teamResourceNewFixture.TeamResources);

        _handler = new GetTeamMasterPaginatedListQueryHandler(teamMasterNewFixture.Mapper, _mockTeamMasterRepository.Object, mockTeamResourceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetTeamMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Development_Team" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamMasterListVm>>();

        result.TotalCount.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Return_PaginatedTeamMasters_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetTeamMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Data_Analytics" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamMasterListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<TeamMasterListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].GroupName.ShouldBe("Development_Team");

        result.Data[0].Description.ShouldBe("Data_Analytics");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetTeamMasterPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamMasterListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_TeamMasters_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetTeamMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "groupname=Development_Team;description=Data_Analytics" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TeamMasterListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].GroupName.ShouldBe("Development_Team");

        result.Data[0].Description.ShouldBeGreaterThan(0.ToString());
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetTeamMasterPaginatedListQuery(), CancellationToken.None);

        _mockTeamMasterRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}
