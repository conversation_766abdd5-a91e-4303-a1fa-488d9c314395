﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessServiceAvailabilityRepository : BaseRepository<BusinessServiceAvailability>,
    IBusinessServiceAvailabilityRepository
{
    private readonly ApplicationDbContext _dbContext;

    public BusinessServiceAvailabilityRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
}