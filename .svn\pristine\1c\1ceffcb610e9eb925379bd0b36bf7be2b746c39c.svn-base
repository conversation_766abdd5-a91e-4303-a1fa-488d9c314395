﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class MssqlAlwaysOnMonitorLogsFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<MSSQLAlwaysOnMonitorLogs> MssqlAlwaysOnMonitorLogs { get; set; }
    public CreateMSSQLAlwaysOnMonitorLogCommand CreateMssqlAlwaysOnMonitorLogCommand { get; set; }

    public MssqlAlwaysOnMonitorLogsFixture()
    {
        MssqlAlwaysOnMonitorLogs = AutoMssqlAlwaysOnMonitorLogsFixture.Create<List<MSSQLAlwaysOnMonitorLogs>>();

        CreateMssqlAlwaysOnMonitorLogCommand = AutoMssqlAlwaysOnMonitorLogsFixture.Create<CreateMSSQLAlwaysOnMonitorLogCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MssqlAlwaysOnMonitorLogsProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoMssqlAlwaysOnMonitorLogsFixture
    {
        get
        {
            var fixture = new Fixture();
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateMSSQLAlwaysOnMonitorLogCommand>(p => p.InfraObjectName, 10));
            fixture.Customize<CreateMSSQLAlwaysOnMonitorLogCommand>(c => c.With(b => b.InfraObjectName, 0.ToString));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}