﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowCategoryRepositoryMocks 
{
    public static Mock<IWorkflowCategoryRepository> CreateWorkflowCategoryRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();
        workflowCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowCategories);
        workflowCategoryRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowCategory>())).ReturnsAsync(
            (WorkflowCategory workflowCategory) =>
            {
                workflowCategory.Id = new Fixture().Create<int>();
                workflowCategory.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowCategories.Add(workflowCategory);
                return workflowCategory;
            });

        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> UpdateWorkflowCategoryRepository(List<WorkflowCategory> workflowCategories)
    {

        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();
        workflowCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowCategories);

        workflowCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowCategories.SingleOrDefault(x => x.ReferenceId == i));

        workflowCategoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowCategory>())).ReturnsAsync((WorkflowCategory workflowCategory) =>
        {
            var index = workflowCategories.FindIndex(item => item.ReferenceId == workflowCategory.ReferenceId);

            workflowCategories[index] = workflowCategory;

            return workflowCategory;
        });


        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> DeleteWorkflowCategoryRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();
        workflowCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowCategories);

        workflowCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowCategories.SingleOrDefault(x => x.ReferenceId == i));

        workflowCategoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowCategory>())).ReturnsAsync((WorkflowCategory workflowCategory) =>
        {
            var index = workflowCategories.FindIndex(item => item.ReferenceId == workflowCategory.ReferenceId);

            workflowCategory.IsActive = false;
            workflowCategories[index] = workflowCategory;

            return workflowCategory;
        });

        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> GetWorkflowCategoryRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();

        workflowCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowCategories);

        workflowCategoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowCategories.SingleOrDefault(x => x.ReferenceId == i));

        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> GetWorkflowCategoryEmptyRepository()
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();

        workflowCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowCategory>());
        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> GetWorkflowCategoryNamesRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();
        workflowCategoryRepository.Setup(repo => repo.GetWorkflowCategoryNames()).ReturnsAsync(workflowCategories);

        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> GetWorkflowCategoryNameUniqueRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();

        workflowCategoryRepository.Setup(repo => repo.IsWorkflowCategoryNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) =>
        {
            return j == 0.ToString() ? workflowCategories.Exists(x => x.Name == i) : workflowCategories.Exists(x => x.Name == i && x.ReferenceId == j);
        });

        return workflowCategoryRepository;
    }

    public static Mock<IWorkflowCategoryRepository> GetPaginatedWorkflowCategoryRepository(List<WorkflowCategory> workflowCategories)
    {
        var workflowCategoryRepository = new Mock<IWorkflowCategoryRepository>();

        var queryableWorkflowCategory = workflowCategories.BuildMock();

        workflowCategoryRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableWorkflowCategory);

        return workflowCategoryRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowCategoryEventRepository(List<UserActivity> userActivities)
    {
        var workflowCategoryEventRepository = new Mock<IUserActivityRepository>();

        workflowCategoryEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowCategoryEventRepository;
    }
}