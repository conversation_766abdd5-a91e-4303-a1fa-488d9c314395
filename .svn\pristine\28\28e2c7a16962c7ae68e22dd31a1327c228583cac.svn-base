using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberJobManagementFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";

    public List<CyberJobManagement> CyberJobManagementPaginationList { get; set; }
    public List<CyberJobManagement> CyberJobManagementList { get; set; }
    public CyberJobManagement CyberJobManagementDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberJobManagementFixture()
    {
        var fixture = new Fixture();

        CyberJobManagementList = fixture.Create<List<CyberJobManagement>>();

        CyberJobManagementPaginationList = fixture.CreateMany<CyberJobManagement>(20).ToList();

        CyberJobManagementDto = fixture.Create<CyberJobManagement>();
       
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
