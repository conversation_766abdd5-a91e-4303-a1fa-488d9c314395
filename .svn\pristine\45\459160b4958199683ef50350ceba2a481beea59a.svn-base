﻿const validEmail = (value) => { return (!(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,63}$/).test(value)) ? "Invalid email" : true; }
const errorElements = ['#UserName-error', '#Email-error', '#Mobile-error', '#BusinessService-error', "#MobilePre-error"];
let btnDisable = false;
const nameExistUrl = "Manage/Approval/IsApprovalMatrixUsersNameExist";
let dataTable = "";
let newApproverData = "";
let addedApproverData = [];
let noDataFount = `<div class="d-flex flex-column align-items-center justify-content-center NoDataContainer mt-5">
                       <img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">
                       <span>No matching records found</span>
                   </div>`;

let approverdata = {
    ApprovalMatrixUsers: [

    ]
}
let countrycode;

$.getJSON('/json/CountryDailCode.json', function (response) {

    setTimeout(() => {
        response.countrycode.forEach(function (value) {
            $('#mobilepre').append('<option value="' + value.dial_code + '">' + value.dial_code + ' (' + value.name + ')</option>');
        });
    }, 500);
});


$(function () {
    let selectedValues = [];
    dataTable = $('#ApproverTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "fixedColumns": {
                left: 1,
                right: 1
            },
            "ajax": {
                "type": "GET",
                "url": "/Manage/Approval/GetPaginatedlist",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "userName" : sortIndex === 2 ? "userType" : sortIndex === 3 ?
                        "acceptType" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    const isEmpty = json?.data?.length === 0;
                    $(".pagination-column").toggleClass("disabled", isEmpty);
                    return json?.data;
                }
            },
            //"columnDefs": [
            //    {
            //        "targets": [0,1, 2, 3,4],
            //        "className": "truncate"
            //    }
            //],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false
                },
                {
                    "data": "userName", "name": "User Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title='${data || "NA"}'>  ${data || "NA"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "userType", "name": "Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span>  ${data || "NA"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "Description", "name": "Deligates to", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span>  ${data || "-"} </span>`;
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        return `<div class="d-flex align-items-center gap-2">
                                        <span role="button" title="Edit" class="edit-button ${row.userType.toLowerCase() === "cp-user" ? 'icon-disabled' : ""}" data-site='${JSON.stringify(row)}'>
                                            <i class="cp-edit"></i>
                                        </span>
                                        <span role="button" title="Delete" class="delete-button" data-site-id="${row.id}" data-site-name="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>                                        
                                       <span role="button" title="Deligate to" class="deligate-button" data-site-id="${row.id}" data-site-name="${row.userName}">
                                            <i class="cp-deligate"></i>
                                        </span>                                      
                              </div>`;
                    },
                    "orderable": false
                }
            ],

            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },

            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const NameCheckbox = $("#Name");

        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $(document).on("click", ".ADAddUser", commonDebounce(async function () {
        let $this = $(this);
        let className = $this.attr("class");
        let dataID = $this.attr("data-id");
        let dataName = $this.attr("data-name").replaceAll(" ", "").toLowerCase();
        if (className.includes("btn-primary")) {
            $this.removeClass("btn-primary").addClass("btn-danger").text("Remove");
            await $.ajax({
                type: "GET",
                url: RootUrl + "Manage/Approval/GetUserByID",
                data: { id: dataID },
                dataType: "json",
                success: function (response) {
                    if (response.success) {
                        let result = response.data;
                        let approval = {
                            "Id": "",
                            "UserName": result.loginName,
                            "Email": result.userInfo.email || "NA",
                            "MobileNumber": result.userInfo.mobile || "NA",
                            "BusinessServiceProperties": "NA",
                            "UserType": "CP-User",
                            "AcceptType": "NA",
                            "IsLink": true,
                        }
                        approverdata.ApprovalMatrixUsers.push(approval);
                    }
                }
            });
        }
        else {
            $this.removeClass("btn-danger").addClass("btn-primary").text("Add");
            if (approverdata.ApprovalMatrixUsers.length) {
                approverdata.ApprovalMatrixUsers = approverdata.ApprovalMatrixUsers.filter(user => user.UserName.replaceAll(" ", "").toLowerCase() !== dataName);
            }
        }
    }));

    $("#profile-tab").on("click", function () {
        $("#AMUserType").val("Anonymous");
        $("#searchElement").addClass("d-none")
    });

    $("#SaveFunction").on('click', async function () {

        if ($("#AMUserType").val() === "CP-User") {

            if (approverdata.ApprovalMatrixUsers.length) {
                await $.ajax({
                    type: 'Post',
                    url: RootUrl + "Manage/Approval/Create",
                    dataType: "json",
                    data: approverdata,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            dataTable.ajax.reload();
                            $("#adduserModal").modal("hide");
                            btnDisable = false;
                        } else {
                            errorNotification(result);
                            btnDisable = false;
                        }
                    },
                });
            }
        } else {
            let name = $("#UserName").val();
            let eMail = $("#mail").val();
            let businessService = $("#selectBusinessService").val();
            let countryCode = $('#mobilepre').val();
            let mobileNumber = $("#mobilenum").val();
            let mobileNumberWithCountryCode = countryCode + ' ' + mobileNumber;
            let isChecked = $("input[name='flexCheckDefault']").prop("checked") ? "true" : "false";
            let SiteSanitizeArray = ['UserName', 'mail', 'mobilenum', 'mobilepre,selectBusinessService'];

            const isName = await validateUserName(name, $('#userNameId').val(), $('#UserName-error'));
            const isEmail = await validateEmail(eMail, 'Enter email', $('#Email-error'));
            const isCountryCode = validateMobilePre(countryCode)
            const isMobileNumber = validateMobile(mobileNumber, 'Enter mobile number', $('#Mobile-error'));
            const isBusinessService = validateDropDown(businessService, 'Select operational service', $('#BusinessService-error'));

            sanitizeContainer(SiteSanitizeArray);

            $("#AMMobileNumber").val(mobileNumberWithCountryCode);
            $("#AMEMail").val(eMail);
            $("#AMUsername").val(name);
            $("#AMIsLink").val(isChecked);

            if (isName && isEmail && isMobileNumber && isBusinessService && isCountryCode && !btnDisable) {
                btnDisable = true;
                let anonymous = {
                    "Id": $("#userNameId").val(),
                    "MobileNumber": mobileNumberWithCountryCode,
                    "Email": eMail,
                    "UserName": name,
                    "IsLink": isChecked,
                    "UserType": $("#AMUserType").val(),
                    "AcceptType": $("#AMAccessType").val(),
                    "BusinessServiceProperties": businessService,
                }
                approverdata.ApprovalMatrixUsers.push(anonymous);
                let createOrUpdate = $("#userNameId").val() ? "Update" : "Create";
                let createOrUpdateData = createOrUpdate === "Update" ? anonymous : approverdata;

                await $.ajax({
                    type: 'Post',
                    url: RootUrl + `Manage/Approval/${createOrUpdate}`,
                    dataType: "json",
                    data: createOrUpdateData,
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    success: function (result) {
                        if (result?.success) {
                            let response = result?.data;
                            approverdata.ApprovalMatrixUsers = [];
                            notificationAlert("success", response?.message);
                            $("#adduserModal").modal("hide");
                            btnDisable = false;
                            dataTable.ajax.reload();
                        } else {
                            errorNotification(result);
                            btnDisable = false;
                        }
                    },
                });
            }
        }
    });

    // Edit
    $('#ApproverTable').on('click', '.edit-button', function () {
        updateSiteType($(this).data('site'));
        $('#SaveFunction').text('Update');
        $('#adduserModal').modal('show');
        $('#home-tab').removeClass('active').addClass('disabled');
    });

    //Delete
    $('#ApproverTable').on('click', '.delete-button', function () {
        const ApprovalName = $(this).data('site-name');
        $("#deleteData").attr("title", ApprovalName);
        $('#deleteData').text(ApprovalName);
        $('#textDeleteId').val($(this).data('site-id'));
    });

    $('#Company-CreateButton').on('click', function () {
        $('#UnaddedTitle').addClass('disabled');
        $('#home-tab').removeClass('disabled');
        $('#addserApprovalTag').hide();
        $('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');
        GetBusinessServiceList()
        clearInputFields('CreateForm', errorElements);
        $('#SaveFunction').text('Save');
        $("#AMUserType").val('CP-User');
        $("#home-tab").trigger("click");
        addCPUser();
    });

    $('#UserName').on('input', function () {
        siteId = $('#textSiteTypeId').val();
        validateUserName($(this).val(), $('#userNameId').val(), '#UserName-error');
    });

    $('#mobilenum').on('keypress keyup', function (event) {
        const value = $(this).val();

        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }

        if (!/^[0-9]+$/.test(event.key)) {
            event?.preventDefault();
        }
        validateMobile(value);
    });

    $('#mobilepre').on('change', function (event) {
        validateMobilePre($(this).val());
    });

    $('#mail').on('keyup', async function () {
        let value = $(this).val();
        $(this).val(value.replace(/\s{2,}/g, ' '));
        await validateEmail(value, $('#textLoginId').val());
    });

    $('#selectBusinessService').on('change', function (event) {
        validateDropDown($(this).val(), "Select operational service", $('#BusinessService-error'));
    });

    $("#home-tab").on("click", function () {
        clearInputFields('CreateForm', errorElements);
        $("#AMUserType").val('CP-User');
        $("#searchElement").removeClass("d-none");
    });

    $("#approverSearchInput").on("keyup input", function (e) {
        let value = $(this).val();
        const $addserApproval = $("#addserApproval");

        if (!value) {
            $(".AddedList").hide()
            approvarlists(newApproverData);
            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

        if (e.type === "keyup") {
            searchedApproverData();
        }
    });

    $("#searchApprovar").on("click", function () {
        searchedApproverData();
    });

    addCPUser();
})

function addCPUser() {
    $.ajax({
        type: "GET",
        url: RootUrl + "Manage/Approval/GetApprovalMatrixUsersList",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                newApproverData = result?.data;
                approvarlists(newApproverData);
            } else {
                errorNotification(result);
            }
        }
    });
}

function searchedApproverData() {
    let searchedData = $("#approverSearchInput").val();
    const $addserApproval = $("#addserApproval");

    if (searchedData) {
        const searchedApproverData = newApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));
        let filteredData = searchedApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if (addedApproverData?.length) {
            const searchedNewApproverData = addedApproverData.filter(user => user.loginName.toLowerCase().includes(searchedData.toLowerCase()));

            if (searchedNewApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);
            } else {
                $(".addedUsersList").remove();
            }
            searchedNewApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }

    } else {
        let filteredData = newApproverData.filter(a => !addedApproverData.some(b => b.id === a.id))
        approvarlists(filteredData);

        if (filteredData?.length === 0) {
            $('#addUserApproval').append(`<div class='usersList'>${noDataFount}</div>`);
        } else {
            $(".usersList").remove();
        }

        if ($addserApproval.children().length !== 0) {
            if (addedApproverData?.length === 0) {
                $addserApproval.append(`<div class='addedUsersList'>${noDataFount}</div>`);

            } 
        } else {
            $(".addedUsersList").remove();

            addedApproverData.forEach(function (data, index) {
                addNewApprover($addserApproval, data?.id, data?.loginName, data?.roleName, index);
            });
        }
        
    }


    //if ($addserApproval.children().length === 0) {

    //    if ($('#addserApprovalTag').children('.NoDataContainer').length === 0) {
    //        $('#addserApproval').append(`<div class='addedUsersList'>
    //                                        ${noDataFount}
    //                                     </div>`);
    //    }
    //} else {
    //    $('#addserApprovalTag').children('.NoDataContainer').remove();
    //}

    //if ($("#addUserApproval").children().length === 0) {

    //    if ($('#unAddedTag').children('.NoDataContainer').length === 0) {
    //        $('#addUserApproval').append(`<div class="usersList">
    //                                        ${noDataFount}
    //                                      </div>`);
    //    }
    //} else {
    //    $('#unAddedTag').children('.NoDataContainer').remove();
    //}
}

function approvarlists(newApproverData) {
    const $addUserApproval = $("#addUserApproval");
    $addUserApproval.empty();

    const $addserApproval = $("#addserApproval");
    $addserApproval.empty();

    // Check if newApproverData is empty before running the loop
    //if (newApproverData.length === 0) {
    //    $('#addUserApproval').append(`${noDataFount}`);
    //} else {
    newApproverData.forEach(function (data, index) {
        const html = `
            <div class="border border-light-subtle rounded p-2 my-2" id="user-${data.id}" data-index="${index}">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center gap-2">
                        <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                        <div>
                            <p class="mb-0">
                                <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${data.loginName}</span>
                                <span class="badge bg-success rounded-pill px-2 ms-2">${data.roleName}</span>
                            </p>
                            <span class="text-primary">All group approver</span>
                        </div>
                    </div>
                    <button type="button" data-id="${data.id}" data-name="${data.loginName}" data-role="${data.roleName}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                        Add
                    </button>
                </div>
            </div>`;
        $addUserApproval.append(html);
    });
    //}    
}

$(document).on("click", ".ADAddUser", function () {
    const $addserApproval = $("#addserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.data("name");
    const userRole = $this.data("role");
    const index = $this.data("index");

    let addedData = {
        id: userId,
        loginName: userName,
        roleName: userRole
    };

    if (!addedApproverData.some(item => item.id === addedData.id)) {
        addedApproverData.push(addedData);
    }

    $(".addedUsersList").remove();
    addNewApprover($addserApproval, userId, userName, userRole, index);
});

$(document).on("click", ".removeUser", function () {
    const $addserApproval = $("#addserApproval");
    const $addUserApproval = $("#addUserApproval");
    const $this = $(this);
    const userId = $this.data("id");
    const userName = $this.closest('div').find('.fw-semibold').text();
    const userRole = $this.closest('div').find('.badge').text();
    const index = $(`#user-${userId}`).data("index");

    addedApproverData = addedApproverData.filter(user => user.id !== userId);

    $(".usersList").remove();
    removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index);
});

function addNewApprover($addserApproval, userId, userName, userRole, index) {

    //if ($("#selected-user-" + userId).length === 0) {
    $(".unAddedtitle").removeClass('d-none')
    const userHtml = `
                <div class="border border-light-subtle rounded p-2 my-2" id="selected-user-${userId}">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center gap-2">
                            <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                            <div>
                                <p class="mb-0">
                                    <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                                    <span class="badge bg-success rounded-pill px-2 ms-2">${userRole}</span>
                                </p>
                                <span class="text-primary">All group approver</span>
                            </div>
                        </div>
                        <button type="button" data-id="${userId}" class="btn btn-danger btn-sm removeUser">
                            Remove
                        </button>
                    </div>
                </div>`;

    $addserApproval.append(userHtml);
    $("#user-" + userId).addClass('d-none');
    $('#addserApprovalTag').show();
    $('.dataTable').removeClass('row-cols-1').addClass('row-cols-2');
    // }
}

function removeNewApprover($addserApproval, $addUserApproval, userId, userName, userRole, index) {
    $(`#selected-user-${userId}`).remove();

    const userHtml = `
        <div class="border border-light-subtle rounded p-2 my-2" id="user-${userId}" data-index="${index}">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-2">
                    <span><img src="/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                    <div>
                        <p class="mb-0">
                            <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">${userName}</span>
                            <span class="badge bg-success rounded-pill px-2 ms-2">${userRole}</span>
                        </p>
                        <span class="text-primary">All group approver</span>
                    </div>
                </div>
                <button type="button" data-id="${userId}" data-name="${userName}" data-role="${userRole}" data-index="${index}" class="btn btn-primary btn-sm ADAddUser">
                    Add
                </button>
            </div>
        </div>`;

    $(`#user-${userId}`).removeClass('d-none');
    $addUserApproval.append(userHtml);
    $(".ADAddUser").text('Add').removeClass('btn-danger').addClass('btn-primary btn-sm');

    if ($addserApproval.children().length === 0) {

        $(".unAddedtitle").addClass('d-none')
        $('#addserApprovalTag').hide();
        $('.dataTable').removeClass('row-cols-2').addClass('row-cols-1');
    }
}

function GetBusinessServiceList(UserApproval = null) {
    $.ajax({
        type: "GET",
        url: RootUrl + "Manage/Approval/GetBusinessServiceList",
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result) {
                if (result && result.length > 0) {
                    $('#selectBusinessService').append('<option value=""></option>');
                    result.forEach(item => {
                        $('#selectBusinessService').append('<option id="' + item.id + '" value="' + item.name + '">' + item.name + '</option>');
                    });
                }
            } else {
                errorNotification(result);
            }
        }
    });

    if (UserApproval && Object.keys(UserApproval)) {
        $('#selectBusinessService').val(UserApproval.businessServiceProperties).trigger('change');
    }
}

async function validateUserName(value, id = null) {
    const errorElement = $('#UserName-error');

    if (!value) {
        errorElement.text('Enter username')
            .addClass('field-validation-error');
        return false;
    }

    if (value.includes('<')) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url = RootUrl + nameExistUrl;
    let data = {
        id: id,
        name: value
    };
    const validationResults = [
        SpecialCharValidate(value),
        ShouldNotBeginWithSpace(value),
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        ShouldNotAllowMultipleSpace(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        await IsFormNameExist(url, data, OnError)
    ];
    return CommonValidation(errorElement, validationResults);
}

async function IsFormNameExist(url, data, errorFunc) {
    return !data.name.trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? "Name already exists" : true;
};

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
};

function validateDropDown(value, errorMessage, errorElement) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

function validateMobilePre(value) {
    const errorElement = $('#MobilePre-error');

    if (!value) {
        errorElement.text('Select country code').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true
    }
}

function validateMobile(value) {
    const errorElement = $('#Mobile-error');

    if (!value) {
        errorElement.text('Enter mobile number')
            .addClass('field-validation-error');
        return false;
    }
    else if (value) {
        const minLength = 7;
        if (value.length < minLength) {
            errorElement.text('Mobile number must be above 6 digits')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
}

function updateSiteType(UserApproval) {

    if (UserApproval.userType === 'Anonymous') {
        $("#profile-tab").trigger("click");
        $('#userNameId').val(UserApproval.id);
        $('#UserName').val(UserApproval.userName);
        $('#mail').val(UserApproval.email);
        $("input[name='flexCheckDefault']").prop('checked', UserApproval.isLink);
        GetBusinessServiceList(UserApproval)
        let splitNumber = UserApproval?.mobileNumber?.split(' ');
        $('#mobilenum').val(splitNumber[1])
        $('#mobilepre').val(splitNumber[0]);
        let errorElement = ['#UserName-error', '#Email-error', '#MobilePre-error', '#Mobile-error', '#BusinessService-error']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    } else {
        $("#home-tab").trigger("click");
        $("#AMUserType").val('CP-User');
    }
}

async function validateEmail(value, id = null) {
    const errorElement = $('#Email-error');
    let format = /^[a-zA-Z0-9._]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

    if (!value) {
        errorElement.text('Enter email')
            .addClass('field-validation-error');
        return false;
    } else if (value.length >= 321) {
        errorElement.text('Enter the value less than 320 characters')
            .addClass('field-validation-error');
        return false;
    } else if (value.length) {
        if (format.test(value) == false) {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else if (value.charAt(0) == "." || value.charAt(0) == "_") {
            errorElement.text('Invalid email')
                .addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('')
                .removeClass('field-validation-error');
            return true;
        }
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
    const validationResults = [await emailRegex(value)];
    return await CommonValidation(errorElement, validationResults);
}

