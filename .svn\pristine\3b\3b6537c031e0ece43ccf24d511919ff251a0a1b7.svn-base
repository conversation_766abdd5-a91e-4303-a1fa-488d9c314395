﻿using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Commands.Discover;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetCGHealthStatus;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class RpForVmCGMonitorStatusService:BaseService, IRpForVmCGMonitorStatusService
{
    public RpForVmCGMonitorStatusService(IHttpContextAccessor accessor):base(accessor)
    {
            
    }

    public async Task<DiscoverRpForResponse> Discover(DiscoverRpForCommand command)
    {
        Logger.LogDebug($"Discover RpForVmCGMonitor '{command.Id}'");

        return await Mediator.Send(command);
    }


    public async Task<(PaginatedResult<RpForVmCGMonitorStatusListVm>, Dictionary<string, int>)> GetPagination(GetRpForVmCGMonitorStatusPaginatedQuery request)
    {
        Logger.LogDebug("Get Searching Details in RpforVmCg monitor status Paginated List");

        return await Mediator.Send(request);
    }

    public async Task<CGHealthStatusVm> CalculateCgEnableStatus()
    {
        Logger.LogDebug("Get Searching Details in RpForVmCGMonitor Paginated List");

        return await Mediator.Send(new GetCGHealthStatusQuery());
    }
    
}
