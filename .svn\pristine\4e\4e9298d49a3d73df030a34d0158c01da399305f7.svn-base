﻿namespace ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;

public class SaveAsWorkflowActionCommandValidator : AbstractValidator<SaveAsWorkflowActionCommand>
{
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public SaveAsWorkflowActionCommandValidator(IWorkflowActionRepository workflowActionRepository)
    {
        _workflowActionRepository = workflowActionRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");


        RuleFor(p => p)
            .MustAsync(WorkflowActionNameUnique)
            .WithMessage("A Same Name Already Exists.");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(SaveAsWorkflowActionCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.WorkflowActionId, "WorkflowAction Id");

        return Task.FromResult(true);
    }

    private async Task<bool> WorkflowActionNameUnique(SaveAsWorkflowActionCommand saveAsWorkflowActionCommand,
        CancellationToken token)
    {
        return !await _workflowActionRepository.IsWorkflowActionNameUnique(saveAsWorkflowActionCommand.Name);
    }
}