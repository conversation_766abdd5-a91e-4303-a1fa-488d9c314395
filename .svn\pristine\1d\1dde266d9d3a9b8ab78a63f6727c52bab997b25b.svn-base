﻿using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoFormDataAttribute : AutoDataAttribute
{
    public AutoFormDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateFormCommand>(p => p.Name, 10));
            fixture.Customize<CreateFormCommand>(c => c.With(b => b.Name, 10.ToString()));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateFormCommand>(p => p.Name, 10));
            fixture.Customize<UpdateFormCommand>(c => c.With(b => b.Id, 0.ToString()));

            return fixture;
        })
    {

    }
}