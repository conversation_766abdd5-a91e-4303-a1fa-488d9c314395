let FileDeleteData = ''
let FileExcludeData = ''
let FileIncludeData = ''
let SSHPrData = ''
let SSHDrData = ''
let FilterOption = 'None'
let PRShellPromptData = ''
let DRShellPromptData = ''
let ThreadsData = ''
let DataSync = {};
let isSSHPrivatekeyPR = false
let isSSHPrivatekeyDR = false
let isDeletionFilter = false
let isFolderPermission = false
let ParallelReplication = false
let isIncrementalReplication = false
let selectedValue;
let btnfalse = false;
const datSyncURL = {
    datSyncExistUrl :'Configuration/DataSyncProperties/IsDataSyncNameExist',
    dataSyncPaginatedUrl: "/Configuration/DataSyncProperties/GetPaginated"
}
var createPermission = $("#configurationdataCreate").data("create-permission")?.toLowerCase();
var deletePermission = $("#configurationdataDelete").data("delete-permission")?.toLowerCase();

if (createPermission == 'false') {
    $("#create").removeClass('#create').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
}
$(function () {

    let selectedValues = [];
    let dataTable = $('#DataSyncTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": datSyncURL.dataSyncPaginatedUrl ,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "replicationType" :
                    sortIndex === 3 ? "status" : "";
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "columnDefs": [
                {
                    "targets": [1],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, orderable: false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    orderable: false
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span class="text-truncate" title="${row.name}" style="max-width: 6em;display:inline-block"> ${row.name}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "replicationType", "name": "Replication Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<td><span title="${row.replicationType}" > ${row.replicationType}</span></td>`
                        }
                        return data;
                    }
                },
                {
                    "data": "properties", "name": "SSH&nbsp;Private&nbsp;Key&nbsp;Path", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.SSHPr);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "SSH&nbsp;Private&nbsp;Key&nbsp;Path (DR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.SSHDr);
                        let val = name[0]
                        if (val) {
                            let name = JSON.parse(data).map(userpre => userpre.SSHDr);
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "Retain&nbsp;Folder&nbsp;Permission", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.isFolderPermission);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "False";
                    }
                },

                {
                    "data": "properties", "name": "Incremental Replication", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.isIncrementalReplication);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "False";
                    }
                },
                {
                    "data": "properties", "name": "Shell&nbsp;Prompt&nbsp;(PR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.PRShellPrompt);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "data": "properties", "name": "Shell & nbsp; Prompt& nbsp; (DR)", "autoWidth": true,
                    "render": function (data, type, row) {
                        let name = JSON.parse(data).map(userpre => userpre.DRShellPrompt);
                        let val = name[0]
                        if (val) {
                            if (type === 'display') {
                                return '<span title="' + val + '">' + val + '</span>';
                            }
                        }
                        return val ? val : "NA";
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-datasync='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="delete-button" data-datasync-id="${row.id}" data-datasync-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;

                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-datasync='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="icon-disabled">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="delete-button" data-datasync-id="${row.id}" data-datasync-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>          
                                 <span role="button" title="Delete" class="icon-disabled">
                                 <i class="cp-Delete"></i>
                                </span>
                            </div>`;
                        }

                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });
    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const checkboxes = [$("#name"), $("#ReplicationType")];
        const inputValue = $('#search-inp').val();

        checkboxes.forEach(checkbox => {
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + inputValue);
            }
        });

        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if ($('#search-inp').val().length === 0) {
                    if (json?.data?.data?.length === 0) {
                        $('.dataTables_empty').text('No Data Found');
                    }
                } else if (json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
                //if (json && json.recordsFiltered === 0) {
                //    $('.dataTables_empty').text('No matching records found');
                //}
            }, false)
        }
    }, 500));



    //Update
    $('#DataSyncTable').on('click', '.edit-button', function () {
        DataSync = $(this).data("datasync");
        $('#CreateModal').modal('show');
        populateModalFieldss(DataSync);
        $('#SaveFunction').text("Update");
    });

    //Delete
    $('#DataSyncTable').on('click', '.delete-button', function () {
        const datasyncId = $(this).data("datasync-id");
        const datasyncName = $(this).data("datasync-name")
        $("#deleteData").attr("title", datasyncName);
        $('#textDeleteId').val(datasyncId);
        $('#deleteData').text(datasyncName);
    });

    //Clear data
    $('#create').on('click', function () {
        $("#FileDeleteClm,#checkbox1,#checkbox4").hide();
        $("#excludeClm,#IncludeClm1").hide()
        $("#radioBtn").hide();
        $("#ThreadsClm").hide();
        $("#SSHPrClm").hide();
        $("#SSHDrClm").hide();
        let errorElements = ['#datasyncName-error', '#ReplicationType-error', '#DRShellPrompt-error', '#PRShellPrompt-error', '#SSHDr-error', "#SSHPr-error", "#FileDelete-error", "#FileExclude-error"];
        clearInputFields('CreateForm', errorElements);
        $('#SaveFunction').text('Save');
    });

    $('#datasyncName').on('input', commonDebounce(async function () {
        let value = $(this).val();
        let datasyncId = $('#DataSyncId').val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, datasyncId, IsNameExist);
    }, 400));

    $('#datasyncReplicationType').on('change', function () {
        let value = $(this).val();
        validateDropDown(value, 'Select replication type ', 'ReplicationType-error');

        if (value == 'Application') {
            $("#checkbox1,#checkbox4").show();
            $("#radioBtn").show();

        } else {
            $("#excludeClm,#checkbox1").hide();
            $("#radioBtn").hide();
            $('#inlineRadio1').prop('checked', true);
            $("#excludeClm,#IncludeClm1").hide()
            $("#checkbox4").show();
        }
    });


    var initialValue = $("input[name='inlineRadioOptions']:checked").val();
    selectedValue = initialValue;
    $("input[name='inlineRadioOptions']").on('change', function () {

        selectedValue = $(this).val();
        FilterOption = selectedValue

        if (selectedValue == 'Exclude') {
            $('#IncludeClm1').hide();
            $('#excludeClm').show();
            $("#FileInclude").val('')
            FileIncludeData = ''
            $('#FileExclude-error').text('').removeClass('field-validation-error');

        } else if (selectedValue == 'Include') {
            $('#excludeClm').hide();
            $('#IncludeClm1').show();
            $("#FileExclude").val('')
            FileExcludeData=''
            $('#FileInclude-error').text('').removeClass('field-validation-error');
        } else {
            $("#excludeClm,#IncludeClm1").hide()
            $("#FileInclude,#FileExclude").val('')
        }

    });

    $('#FileDelete').on('input', function () {
        let value = $(this).val();      
        FileDeleteData = value;
        validateDropDown(value, 'Enter file not to delete', 'FileDelete-error');
    });

    $('#Threads').on('input',function () {
        let value = $(this).val();
        let sanitizedValue = value.replace(/[^0-9]/g, '');
        $(this).val(sanitizedValue);
        ThreadsData = sanitizedValue;
         validateDropDown(sanitizedValue, 'Enter number of threads', 'Threads-error');
    });
    $('#FileExclude').on('input',function () {
        let value = $(this).val();      
        FileExcludeData = value
        validateDropDown(value, 'Enter file to exclude ', 'FileExclude-error')
    })

    $('#FileInclude').on('input',function () {
        let value = $(this).val();      
        FileIncludeData = value
        validateDropDown(value, 'Enter file to include', 'FileInclude-error')
    })
    $('#SSHPr').on('input',function () {
        let value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');;
        $(this).val(sanitizedValue);
        SSHPrData = sanitizedValue
        let errorElement = $('#SSHPr-error');
        validatePath(sanitizedValue, 'Enter SSH private key path (production)', errorElement)
    })

    $('#SSHDr').on('input',function () {
        let value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        let errorElement = $('#SSHDr-error');
        SSHDrData = sanitizedValue
        validatePath(sanitizedValue, 'Enter SSH private key path (DR)', errorElement)
    })

    $('#PRShellPrompt').on('input',function () {
        let value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        PRShellPromptData = sanitizedValue;
         validateDropDown(sanitizedValue, 'Enter production shell prompt', 'PRShellPrompt-error');
    });

    $('#DRShellPrompt').on('input', function () {
        let value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        DRShellPromptData = sanitizedValue
        validateDropDown(sanitizedValue, 'Enter DR shell prompt', 'DRShellPrompt-error')
    })

    $(document).on('change', '.Checkbox', function (e) {
        let value = $(this).val();
        switch (value) {
            case "option1":

                if (e.target.checked) {
                    $("#FileDeleteClm").show();
                    isDeletionFilter = true
                    $('#FileDelete-error').text('').removeClass('field-validation-error');

                } else {
                    $("#FileDeleteClm").hide();
                    isDeletionFilter = false
                    $("#FileDelete").val('');
                    FileDeleteData = ''
                }
                break;
            case "option2":

                if (e.target.checked) {
                    $("#SSHPrClm").show();
                    isSSHPrivatekeyPR = true
                    $('#SSHPr-error').text('').removeClass('field-validation-error');
                } else {
                    $("#SSHPrClm").hide();
                    isSSHPrivatekeyPR = false
                    $("#SSHPr").val('');
                    SSHPrData = ''
                }
                break;
            case "option3":

                if (e.target.checked) {
                    $("#SSHDrClm").show();
                    isSSHPrivatekeyDR = true
                    $('#SSHDr-error').text('').removeClass('field-validation-error');
                } else {
                    $("#SSHDrClm").hide();
                    isSSHPrivatekeyDR = false
                    $("#SSHDr").val('');
                    SSHDrData = ''
                }
                break;
            case "option4":

                if (e.target.checked) {

                    isFolderPermission = true
                } else {
                    isFolderPermission = false
                }
                break;
        }
    });

    $(document).on('change', '.Checkboxs', function (e) {
        const value = $(this).val();
        if (value === 'options1') {
            if (e.target.checked) {
                $("#ThreadsClm").show();
                ParallelReplication = true;
                $('#Threads-error').text('').removeClass('field-validation-error');
            } else {
                $("#ThreadsClm").hide();
                ParallelReplication = false;
                $("#Threads").val('');
                ThreadsData = ''
            }
        }

        if (value === 'options2') {
            if (e.target.checked) {
                isIncrementalReplication = true;
            } else {
                isIncrementalReplication = false;
            }
        }
    });

    $("#SaveFunction").on('click', async function () {
        let form = $('#CreateForm');
        let datasyncName = $("#datasyncName").val();
        let dataId = $('#DataSyncId').val();
        let ReplicationType = $("#datasyncReplicationType").val();
        let FileExclude = $("#FileExclude").val();
        let Include = $("#FileInclude").val();
        let FileDelete = $("#FileDelete").val();
        let SSHPr = $("#SSHPr").val();
        let SSHDr = $("#SSHDr").val();
        let PRShellPrompt = $("#PRShellPrompt").val();
        let DRShellPrompt = $("#DRShellPrompt").val();
        let Threads = $("#Threads").val();
        let isName = await validateName(datasyncName, dataId, IsNameExist);
        let isReplicationType =  validateDropDown(ReplicationType, 'Select replication type', 'ReplicationType-error');
        let errorElementSSHPr = $('#SSHPr-error');
        let errorElementSShDr = $('#SSHDr-error');
        let isThreads =  validateDropDown(Threads, ' Enter number of threads', 'Threads-error')
        let isFileExclude =  validateDropDown(FileExclude, ' Enter file to exclude', 'FileExclude-error')
        let isFileInclude =  validateDropDown(Include, ' Enter file to include', 'FileInclude-error')
        let isFileDelete =  validateDropDown(FileDelete, ' Enter file not to delete', 'FileDelete-error')
        let isSSHPr =  await validatePath(SSHPr, 'Enter SSH private key path (production)', errorElementSSHPr)
        let isSSHDr =  await validatePath(SSHDr, 'Enter SSH private key path (DR)', errorElementSShDr)
        let isDRShellPrompt =  validateDropDown(DRShellPrompt, 'Enter DR shell prompt ', 'DRShellPrompt-error')
        let isPRShellPrompt =  validateDropDown(PRShellPrompt, 'Enter production shell prompt', 'PRShellPrompt-error')
        
        if (
            isName && isReplicationType && isPRShellPrompt && isDRShellPrompt && !btnfalse &&
            ($("#ThreadsClm").is(":visible") ? isThreads : true) && 
            ($("#IncludeClm1").is(":visible") ? isFileInclude : true) &&
            ($("#excludeClm").is(":visible") ? isFileExclude : true) &&
            ($("#FileDeleteClm").is(":visible") ? isFileDelete : true) &&
            ($("#SSHPrClm").is(":visible") ? isSSHPr : true) &&
            ($("#SSHDrClm").is(":visible") ? isSSHDr : true)
        ) {
            updateProperties();
            btnfalse = true
            form.trigger('submit');

        }
    });

    function updateProperties() {

        properties = [{
            FileDelete: FileDeleteData,
            FileExclude: FileExcludeData,
            FileInclude: FileIncludeData,
            SSHPr: SSHPrData,
            SSHDr: SSHDrData,
            PRShellPrompt: PRShellPromptData,
            DRShellPrompt: DRShellPromptData,
            isSSHPrivatekeyPR: isSSHPrivatekeyPR,
            isSSHPrivatekeyDR: isSSHPrivatekeyDR,
            isDeletionFilter: isDeletionFilter,
            isFolderPermission: isFolderPermission,
            ParallelReplication: ParallelReplication,
            isIncrementalReplication: isIncrementalReplication,
            FilterOption: FilterOption,
            ThreadsData: ThreadsData
        }];

        let propertiesVal = (JSON.stringify(properties));
        $('#DataSyncProperties').val(propertiesVal)
    }

    async function validateName(value, id = null) {

        const errorElement = $('#datasyncName-error');
        if (!value) {
            errorElement.text('Enter datasync properties name ')
                .addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        var url = RootUrl + datSyncURL.datSyncExistUrl;
        var data = {};
        data.name = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];
        return await CommonValidation(errorElement, validationResults);
    }
    async function IsNameExist(url, data, errorFunc) {
        return !data.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    async function validatePath(value, errorMsg, errorElement) {
        
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        }
        const validationResults = [
            await InvalidPathRegex(value)
        ];
        return await CommonValidation(errorElement, validationResults);
    }


    function validateDropDown(value, errorMessage, errorElement) {

        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }

    function populateModalFieldss(DataSync) {

        $('#DataSyncId').val(DataSync.id);
        $('#datasyncName').val(DataSync.name);
        $('#datasyncReplicationType').val(DataSync.replicationType);
        let DataSyncPropertie = JSON.parse(DataSync.properties);
        let firstObject = DataSyncPropertie[0];
        FilterOption = firstObject.FilterOption

        if (DataSync.replicationType === 'Application') {
            $("#radioBtn").show();

            if (firstObject.FilterOption === 'None') {
                $("#excludeClm, #IncludeClm1").hide();
                $("#inlineRadio1").prop("checked", true);
                $("#inlineRadio1").val(firstObject.FilterOption);
            } else if (firstObject.FilterOption === 'Exclude') {
                $("#excludeClm").show();
                $("#inlineRadio2").prop("checked", true);
                $("#inlineRadio2").val(firstObject.FilterOption);
                $("#FileExclude").val(firstObject.FileExclude);
                FileExcludeData = firstObject.FileExclude
                $("#IncludeClm1").hide();
            } else if (firstObject.FilterOption === 'Include') {
                $("#IncludeClm1").show();
                $("#inlineRadio3").prop("checked", true);
                $("#inlineRadio3").val(firstObject.FilterOption);
                $("#FileInclude").val(firstObject.FileInclude);
                FileIncludeData = firstObject.FileInclude
                $("#excludeClm").hide();
            }
        } else if (DataSync.replicationType === 'Database') {
            $("#radioBtn, #excludeClm, #IncludeClm1").hide();
        }

        if (DataSync.replicationType === 'Application') {
            $("#checkbox1, #checkbox4").show();
            if (firstObject.isDeletionFilter) {
                $("#FileDeleteClm").show()
                $("#inlineCheckbox1").prop("checked", firstObject.isDeletionFilter);
                $("#FileDelete").val(firstObject.FileDelete)
                isDeletionFilter = firstObject.isDeletionFilter
                FileDeleteData = firstObject.FileDelete

            } else {
                $("#FileDeleteClm").hide()
                $("#inlineCheckbox1").prop("checked", false);
            }
        } else if (DataSync.replicationType === 'Database') {
            $("#checkbox4").show();
            $("#checkbox1").hide();
            $("#FileDeleteClm").hide()

        }
        $("#PRShellPrompt").val(firstObject.PRShellPrompt)
        $("#DRShellPrompt").val(firstObject.DRShellPrompt)
        PRShellPromptData = firstObject.PRShellPrompt
        DRShellPromptData = firstObject.DRShellPrompt
        isSSHPrivatekeyPR = firstObject.isSSHPrivatekeyPR
        isSSHPrivatekeyDR = firstObject.isSSHPrivatekeyDR

        if (firstObject.isSSHPrivatekeyPR) {
            $("#inlineCheckbox2").prop("checked", firstObject.isSSHPrivatekeyPR);
            $("#SSHPrClm").show();
            $("#SSHPr").val(firstObject.SSHPr)
            SSHPrData = firstObject.SSHPr
        } else {
            $("#inlineCheckbox2").prop("checked", false);
            $("#SSHPrClm").hide();
        }
        if (firstObject.isSSHPrivatekeyDR) {
            $("#inlineCheckbox3").prop("checked", firstObject.isSSHPrivatekeyDR);
            $("#SSHDrClm").show();
            $("#SSHDr").val(firstObject.SSHDr)
            SSHDrData = firstObject.SSHDr
        } else {
            $("#inlineCheckbox3").prop("checked", false);
            $("#SSHDrClm").hide();
        }
        if (firstObject.isFolderPermission) {

            $("#inlineCheckbox4").prop("checked", firstObject.isFolderPermission);

            isFolderPermission = firstObject.isFolderPermission
        } else {
            $("#inlineCheckbox4").prop("checked", false);

        }

        ParallelReplication = firstObject.ParallelReplication
        if (firstObject.ParallelReplication) {
            $("#inlineCheckboxs1").prop("checked", firstObject.ParallelReplication);
            $("#ThreadsClm").show();
            $("#Threads").val(firstObject.ThreadsData);
            ThreadsData = firstObject.ThreadsData
        } else {
            $("#inlineCheckboxs1").prop("checked", false);
            $("#ThreadsClm").hide();
        }
        if (firstObject.isIncrementalReplication) {
            $("#inlineCheckboxs2").prop("checked", firstObject.isIncrementalReplication);

            isIncrementalReplication = firstObject.isIncrementalReplication
        } else {
            $("#inlineCheckboxs2").prop("checked", false);

        }
        let errorElement = ['#datasyncName-error', '#ReplicationType-error', '#DRShellPrompt-error', '#PRShellPrompt-error', '#SSHDr-error', "#SSHPr-error", "#FileDelete-error", "#FileExclude-error"];
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }

})
