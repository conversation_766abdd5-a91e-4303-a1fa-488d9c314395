﻿namespace ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;

public class GetFormTypeDetailQueryHandler : IRequestHandler<GetFormTypeDetailQuery, FormTypeDetailVm>
{
    private readonly IFormTypeRepository _formTypeRepository;
    private readonly IMapper _mapper;

    public GetFormTypeDetailQueryHandler(IMapper mapper, IFormTypeRepository formTypeRepository)
    {
        _mapper = mapper;
        _formTypeRepository = formTypeRepository;
    }

    public async Task<FormTypeDetailVm> Handle(GetFormTypeDetailQuery request, CancellationToken cancellationToken)
    {
        var formType = await _formTypeRepository.GetFormTypeById(request.Id);

        Guard.Against.NullOrDeactive(formType, nameof(Domain.Entities.FormType),
            new NotFoundException(nameof(Domain.Entities.FormType), request.Id));

        var formTypeDetailDto = _mapper.Map<FormTypeDetailVm>(formType);

        return formTypeDetailDto;
    }
}