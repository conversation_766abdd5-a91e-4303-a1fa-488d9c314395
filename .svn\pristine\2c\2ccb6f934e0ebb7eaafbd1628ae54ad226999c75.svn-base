﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class WorkflowActionFilterSpecification : Specification<WorkflowAction>
{
    public WorkflowActionFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("actionname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ActionName.Contains(stringItem.Replace("actionname=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("properties=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Properties.Contains(stringItem.Replace("properties=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("version=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Version.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ActionName.Contains(searchString) || p.Properties.Contains(searchString) ||
                    p.Version.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.ActionName != null;
        }
    }
}