﻿using ContinuityPatrol.Application.Features.Template.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Queries;

public class GetTemplatePaginatedListQueryHandlerTests : IClassFixture<TemplateFixture>
{
    private readonly TemplateFixture _templateFixture;
    private readonly Mock<ITemplateRepository> _mockTemplateRepository;
    private readonly GetTemplatePaginatedListQueryHandler _handler;

    public GetTemplatePaginatedListQueryHandlerTests(TemplateFixture templateFixture)
    {
        _templateFixture = templateFixture;

        _templateFixture.Templates[0].Name = "Test_Template";
        _templateFixture.Templates[0].Properties = "{\"Name\": \"admin\", \"password\": \"Admin@123\"}";

        _templateFixture.Templates[1].Name = "Testing_Base";
        _templateFixture.Templates[1].Properties = "{\"Names\": \"common\", \"passwords\": \"common@123\"}";

        _mockTemplateRepository = TemplateRepositoryMocks.GetPaginatedTemplateRepository(_templateFixture.Templates);
        _handler = new GetTemplatePaginatedListQueryHandler(_templateFixture.Mapper, _mockTemplateRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetTemplatePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TemplateListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Template_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetTemplatePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Test;properties={\"Name\": \"admin\", \"password\": \"Admin@123\"}" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TemplateListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Name.ShouldBe("Test_Template");
        result.Data[0].Properties.ShouldBe("{\"Name\": \"admin\", \"password\": \"Admin@123\"}");

    }

    [Fact]
    public async Task Handle_Return_PaginatedTemplate_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetTemplatePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TemplateListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].Name.ShouldBe(_templateFixture.Templates[0].Name);
        result.Data[0].Properties.ShouldBe(_templateFixture.Templates[0].Properties);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetTemplatePaginatedListQuery() { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<TemplateListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetTemplatePaginatedListQuery(), CancellationToken.None);

        _mockTemplateRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}
