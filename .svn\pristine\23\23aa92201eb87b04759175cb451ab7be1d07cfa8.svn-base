﻿using ContinuityPatrol.Application.Features.Job.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Events;

public class CreateJobEventTests : IClassFixture<JobFixture>, IClassFixture<UserActivityFixture>
{
    private readonly JobFixture _jobFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly JobCreatedEventHandler _handler;

    public CreateJobEventTests(JobFixture jobFixture, UserActivityFixture userActivityFixture)
    {
        _jobFixture = jobFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockInfraObjectSchedulerEventLogger = new Mock<ILogger<JobCreatedEventHandler>>();

        _mockUserActivityRepository = JobRepositoryMocks.CreateJobEventRepository(_userActivityFixture.UserActivities);

        _handler = new JobCreatedEventHandler(mockLoggedInUserService.Object, mockInfraObjectSchedulerEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateJobEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_jobFixture.JobCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_jobFixture.JobCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}