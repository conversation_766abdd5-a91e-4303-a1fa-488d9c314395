﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Helper;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Infrastructure.Impl;

public class LicenseValidationService : ILicenseValidationService
{
    private readonly ILogger<LicenseValidationService> _logger;

    public LicenseValidationService(ILogger<LicenseValidationService> logger)
    {
        _logger = logger;
    }

    public Task<int> ServerLicenseCount(LicenseManager licenseManager, SiteType siteType, string type, int index)
    {
        var serverType = type!.ToLower() == "thirdparty" ? "thirdPartyCount" : type.ToLower() + "Count";

        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = type!.Trim().ToLower().Equals("database")
                ? JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}databaseCount")
                : JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}{serverType}");

            return Task.FromResult(count);
        }

        var roleType = type!.Trim().ToLower().Equals("database")
            ? $"{siteType.Category.ToLower()}databaseCount"
            : $"{siteType.Category.ToLower()}{serverType}";

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, roleType);

        return Task.FromResult(dynamicSiteCount);
    }

    public Task<bool> IsServerLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType, string type, int serverCount, int index)
    {
        var serverType = type!.ToLower() == "thirdparty" ? "thirdPartyCount" : type.ToLower() + "Count";

        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = type!.Trim().ToLower().Equals("database")
               ? JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}databaseCount")
               : JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}{serverType}");

           return Task.FromResult(count > serverCount);
        }

        var roleType = type!.Trim().ToLower().Equals("database")
            ? $"{siteType.Category.ToLower()}databaseCount"
            : $"{siteType.Category.ToLower()}{serverType}";

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, roleType);

        return Task.FromResult(dynamicSiteCount > serverCount);
    }

    public Task<int> DatabaseLicenseCount(LicenseManager licenseManager, SiteType siteType, int index)
    {
        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}databaseCount");

            return Task.FromResult(count);
        }

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}databaseCount");

        return Task.FromResult(dynamicSiteCount);
    }


    public Task<bool> IsDatabaseLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType,int databaseCount,int index)
    {
        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}databaseCount");

            return Task.FromResult(count > databaseCount);
        }

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}databaseCount");

        return Task.FromResult(dynamicSiteCount > databaseCount);
    }


    public Task<int> DatabaseTypeLicenseCount(LicenseManager licenseManager, SiteType siteType, string databaseTypeId, int index)
    {
        var isDatabase = JsonHelper.GetJsonValueAsBool(licenseManager.Properties, "isDatabase");

        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            if (isDatabase)
            {
                var count = GetDatabaseTypeCounts(licenseManager.Properties, siteType.Category.ToLower(), databaseTypeId);

                return Task.FromResult(count);
            }
        }

        var dynamicSiteCount = isDatabase
            ? CustomDrDatabaseLicenseCountValidation(licenseManager.Properties, index, databaseTypeId)
            : CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}databaseCount");

        return Task.FromResult(dynamicSiteCount);
    }



    public Task<bool> IsDatabaseTypeLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType, string databaseTypeId,int databaseCount, int index)
    {
        var isDatabase = JsonHelper.GetJsonValueAsBool(licenseManager.Properties, "isDatabase");

        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            if (isDatabase)
            {
                var count = GetDatabaseTypeCounts(licenseManager.Properties, siteType.Category.ToLower(), databaseTypeId);

                return Task.FromResult(count > databaseCount);
            }
        }

        var dynamicSiteCount = isDatabase 
            ? CustomDrDatabaseLicenseCountValidation(licenseManager.Properties, index, databaseTypeId)
            : CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}databaseCount");

        return Task.FromResult(dynamicSiteCount > databaseCount);
    }

    public Task<int> ReplicationLicenseCount(LicenseManager licenseManager, SiteType siteType, int index)
    {
        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}replicationCount");

            return Task.FromResult(count);
        }

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}replicationCount");

        return Task.FromResult(dynamicSiteCount);
    }

    public Task<bool> IsReplicationLicenseCountExitMaxLimit(LicenseManager licenseManager, SiteType siteType,int replicationCount,int index)
    {
        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            var count = JsonHelper.GetLicenseJsonValue(licenseManager.Properties, $"{siteType.Category.ToLower()}replicationCount");

            return Task.FromResult(count > replicationCount);
        }

        var dynamicSiteCount = CustomDrLicenseCountValidation(licenseManager.Properties, index, $"{siteType.Category.ToLower()}replicationCount");

        return Task.FromResult(dynamicSiteCount > replicationCount);
    }

    public Task<bool> IsLicenseSiteAvailable(string properties, SiteType siteType, string type,int index, string serverRoleType = null)
    {
        if (siteType.Category.ToLower().Equals("primary") || siteType.Category.ToLower().Equals("dr"))
        {
            return Task.FromResult(true);
        }

        var propsType = string.Empty;

        if (type.ToLower().Equals("server"))
        {
            var serverType = serverRoleType!.ToLower() == "thirdparty" 
                ? "thirdPartyCount" 
                : serverRoleType.ToLower() + "Count";

            propsType = type!.Trim().ToLower().Equals("database")
                ? $"{siteType.Category.ToLower()}databaseCount"
                : $"{siteType.Category.ToLower()}{serverType}";
        }
        else if(type.ToLower().Equals("database"))
        {
            propsType = $"{siteType.Category.ToLower()}databaseCount";
        }
        else if(type.ToLower().Equals("replication"))
        {
            propsType = $"{siteType.Category.ToLower()}replicationCount";
        }

        return Task.FromResult(IsCustomDrSiteAvailable(properties, index, propsType));
    }


    public Task<bool> IsLicenseExpired(string date)
    {
        // License Expired -so  not applicable then return false 
        if (DateTime.TryParseExact(date, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                DateTimeStyles.None, out var expiryDate))
            if (expiryDate < DateTime.UtcNow.Date)
                return Task.FromResult(false);
        return Task.FromResult(true);
    }

    public Task<bool> IsCompanyNameValidate(string newCompanyName, string existCompanyName)
    {
        if (string.IsNullOrWhiteSpace(newCompanyName) || existCompanyName.IsNullOrWhiteSpace())
        {
            _logger.LogError("Company name cannot be null or empty.");
            return Task.FromResult(false);
        }

        if (!newCompanyName.Equals(existCompanyName, StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogError($"The company name '{newCompanyName}' provided for the renewal license does not match the existing parent company name '{existCompanyName}'");
            return Task.FromResult(false);
        }

        return Task.FromResult(true);
    }

    public Task<bool> IsRenewalLicenseValidate(string licenseType)
    {
        if (!licenseType.ToLower().Equals("new")) return Task.FromResult(true);
        _logger.LogError($"The specified license type '{licenseType}' is not supported for 'renewal'.");
        return Task.FromResult(false);
    }


    public async Task<bool> IsMacAddressValidAsync(string macAddresses)
    {
        var splitMac = macAddresses.Split(',');

        var systemMacAddress = await GetMacAddress();

        var isMacAddressValid = systemMacAddress.Any(m => splitMac.Contains(m));

        if (!isMacAddressValid)
            _logger.LogError("Base License MACAddress and CP Installed System MACAddress Mismatch.");
        

        return isMacAddressValid;
    }

    public async Task<bool> IsHostNameValidAsync(string hostName)
    {
        var splitHostName = hostName.Split(',');

        var systemHostName = await GetHostName();

        var isHostNameValid = splitHostName.Contains(systemHostName);

        if (!isHostNameValid)
            _logger.LogError("Base License Host Name and CP Installed System Host Name MisMatch.");
        
        return isHostNameValid;
    }

    public async Task<bool> IsIpAddressValidAsync(string ipAddress)
    {
        var splitIpAddress = ipAddress.Split(',');

        var systemIpAddress = await GetIpAddress();

        var isIpAddressValid = systemIpAddress.Any(m => splitIpAddress.Contains(m));

        if (!isIpAddressValid)
             _logger.LogError("Base LicenseKey IP Address and CP Installed System IP Address MisMatch.");
        
        return isIpAddressValid;
    }

    public Task<bool> ValidateLicenseKeyDate(string licenseGeneratorDate)
    {
        var date = DateTime.UtcNow.ToString();
        var currentDate = DateTime.Parse(date).Date;
        var generatorDate = DateTime.Parse(licenseGeneratorDate).Date;

       var difference = currentDate.Subtract(generatorDate).Days;

        //var difference = currentDate.Subtract(DateTime.Parse(licenseGeneratorDate)).Days;

        if (difference < 0)
        {
            _logger.LogError($"License Generated date is in the future. Difference={difference}");
            return Task.FromResult(false);
        }

        if (difference > 15)
        {
            _logger.LogError($"License Generated date and added day difference={difference}");

            return Task.FromResult(false);
        }
        return Task.FromResult(true);
    }

    public Task<bool> IsLicenseKeyFormatValid(string licenseKey)
    {
        var licenseSplit = licenseKey.Split('$');

        if (licenseSplit.Length < 2)
            return Task.FromResult(false);

        var pattern = new Regex("^(?:[a-zA-Z0-9+/]{4})*(?:|(?:[a-zA-Z0-9+/]{3}=)|(?:[a-zA-Z0-9+/]{2}==)|(?:[a-zA-Z0-9+/]{1}===))$");

        return Task.FromResult(licenseKey.Length > 75 && pattern.IsMatch(licenseSplit[1]));
    }


    public async Task<string> UpdateExpiryDate(LicenseDto newLicenseKey, LicenseManager eventToUpdate)
    {
        var startDate = DateTime.UtcNow;

        var jObject = JObject.Parse(newLicenseKey.LicenseCount);

        var startDateStr = jObject.SelectToken("startDate")?.ToString();
        var endDateStr = jObject.SelectToken("endDate")?.ToString();

        if (startDateStr.IsNotNullOrWhiteSpace() &&
            DateTime.TryParse(startDateStr,
                CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
        {
            startDate = parsedDate;
        }


        //if (newLicenseKey.LicenseType.Contains("Subscription"))
        //{
        //    var jObject = JObject.Parse(newLicenseKey.LicenseCount);

        //    var subscriptionStartDateStr = jObject.SelectToken("subscriptionStartDate")?.ToString();

        //    if (subscriptionStartDateStr.IsNotNullOrWhiteSpace() &&
        //        DateTime.TryParseExact(subscriptionStartDateStr, "yyyy-MM-dd",
        //            CultureInfo.InvariantCulture, DateTimeStyles.None,
        //            out var parsedDate))
        //    {
        //        startDate = parsedDate;
        //    }
        //}

        var updateExpiryDate = await GetDateByExpireTime(newLicenseKey.LicenseType, startDate, endDateStr);
        var expiryDate = DateTime.Parse(updateExpiryDate).ToString("dd MMMM yyyy");

        if (newLicenseKey.LicenseType.ToLower().Contains("poc") || newLicenseKey.LicenseType.ToLower().Contains("uat"))
        {
            return expiryDate;
        }

        if (newLicenseKey.LicenseType.ToLower().Contains("subscription"))
        {
            if (newLicenseKey.LicenseType.Contains(SecurityHelper.Decrypt(eventToUpdate.Validity)))
            {
                var isValid = DateTime.TryParseExact(SecurityHelper.Decrypt(eventToUpdate.ExpiryDate), "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate1) && expiryDate1 >= DateTime.UtcNow.Date;

                if (isValid)
                {
                    var eventToUpdateExpiryDate = DateTime.Parse(SecurityHelper.Decrypt(eventToUpdate.ExpiryDate));
                    var pendingValidity = eventToUpdateExpiryDate - DateTime.UtcNow;

                    var modifiedExpiryDate = DateTime.Parse(expiryDate) + pendingValidity;
                    expiryDate = modifiedExpiryDate.ToString("dd MMMM yyyy");

                    return expiryDate;
                }
            }
            
            return expiryDate;
        }
        return expiryDate;
    }

    public  Task<bool> IsAmc(string src)
    {
        if (src.IsNullOrWhiteSpace()) return Task.FromResult(false);

        var jObject = JObject.Parse(src);
        return Task.FromResult(jObject["AMC"]?.ToObject<long>() != 0);
    }

    public Task<bool> IsWarranty(string src)
    {
        if (src.IsNullOrWhiteSpace()) return Task.FromResult(false);

        var jObject = JObject.Parse(src);
        return Task.FromResult(jObject["Warranty"]?.ToObject<long>() != 0);
    }

    public async Task<string> AmcStartDate(string src)
    {
        if (await IsAmc(src))
        {
            var jObject = JObject.Parse(src);

            var amcStartDate = jObject["AMCStartDate"]?.ToString();

            if (amcStartDate.IsNotNullOrWhiteSpace())
            {
                var startDate = DateTime.Parse(amcStartDate!);

                return startDate.ToString("dd MMMM yyyy");
            }

            var currentDate = DateTime.Today;

            var formattedDate = currentDate.ToString("dd MMMM yyyy");

            return formattedDate;
        }

        return "NA";
    }

    public async Task<string> AmcEndDate(string expireTime)
    {
        var createDate = DateTime.Today;

        if (await IsAmc(expireTime))
        {
            var jObject = JObject.Parse(expireTime);
            var date = jObject["AMC"]?.ToString();

            var amcStartDate = jObject["AMCStartDate"]?.ToString();

            if (amcStartDate.IsNotNullOrWhiteSpace())
            {
                var startDate = DateTime.Parse(amcStartDate!);

                var amcExpiryDate = startDate.AddYears(int.Parse(date!)).ToString("dd MMMM yyyy");

                return amcExpiryDate;
            }

            var expiryDate = createDate.AddYears(int.Parse(date!)).ToString("dd MMMM yyyy");

            return expiryDate;
        }

        return "NA";
    }

    public Task<string> UpdateAmcDate(LicenseDto newLicenseKey, LicenseManager eventToUpdate)
    {
        var date = string.Empty;

        var amcEndDate = SecurityHelper.Decrypt(eventToUpdate.AmcEndDate);

        if (DateTime.TryParseExact(amcEndDate, "dd MMMM yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                out DateTime parsedDate))
        {
            var jObject = JObject.Parse(newLicenseKey.SupportPlan);

            var amcStartDate = jObject["AMCStartDate"]?.ToString();

            if (amcStartDate.IsNotNullOrWhiteSpace())
            {
                var startDate = DateTime.Parse(amcStartDate!).Date;

                var amcRemainingDays = (parsedDate - startDate).Days;

                var amcDate = jObject["AMC"]?.ToString();

                var expiryDate = startDate.AddYears(int.Parse(amcDate!));

                if (amcRemainingDays < 0)
                {
                    return Task.FromResult(expiryDate.ToString("dd MMMM yyyy"));
                }

                var amcAddDays = expiryDate.AddDays(amcRemainingDays);

                return Task.FromResult(amcAddDays.ToString("dd MMMM yyyy"));
            }


            var today = DateTime.Today;

            var remainingDays = (parsedDate - today).Days;

           // var newAmcEndDate = await AmcEndDate(newLicenseKey.SupportPlan);

            var tt = DateTime.Parse(amcEndDate);

            var dd = tt.AddDays(remainingDays);

            date = dd.ToString("dd MMMM yyyy");
        }

        return Task.FromResult(date);
    }


    #region Private Methods

    public Task<string> GetDateByExpireTime(string expireTime, DateTime createDate, string endDateStr)
    {
        var expiry = expireTime.Split("-");
        var date = expiry[1].Split(" ");
        var expiryDate = date[0] == "Unlimited"
            ? DateTime.MaxValue.ToString("dd MMMM yyyy")
            : date[1] switch
            {
                "Month" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddMonths(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                "Days" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddDays(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                "Year" => endDateStr.IsNullOrWhiteSpace() ? createDate.AddYears(int.Parse(date[0])).ToString("dd MMMM yyyy") : GetEndDate(createDate, endDateStr),
                _ => throw new Exception("LicenseKey Type not match.")
            };

        return Task.FromResult(expiryDate);
    }

    private string GetEndDate(DateTime createDate, string endDateStr)
    {
        var licenseEndDate = DateTime.Now;

        if (endDateStr.IsNotNullOrWhiteSpace() &&
            DateTime.TryParse(endDateStr,
                CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedEndDate))
        {
            licenseEndDate = parsedEndDate;
        }


        var currentYear = createDate.Year;
        var currentMonth = createDate.Month;
        var currentDay = createDate.Day;
        var baseDate = new DateTime(currentYear, currentMonth, currentDay);

        var daysToAdd = (licenseEndDate - createDate).Days;

        var newDate = baseDate.AddDays(daysToAdd);

        return newDate.ToString("dd MMMM yyyy");
    }


    //private Task<string> GetDateByExpireTime(string expireTime, DateTime createDate)
    //{
    //    var expiry = expireTime.Split("-");
    //    var date = expiry[1].Split(" ");
    //    var expiryDate = date[0] == "Unlimited"
    //        ? DateTime.MaxValue.ToString("dd MMMM yyyy")
    //        : date[1] switch
    //        {
    //            "Month" => createDate.AddMonths(int.Parse(date[0])).ToString("dd MMMM yyyy"),
    //            "Days" => createDate.AddDays(int.Parse(date[0])).ToString("dd MMMM yyyy"),
    //            "Year" => createDate.AddYears(int.Parse(date[0])).ToString("dd MMMM yyyy"),
    //            _ => throw new Exception("LicenseKey Type not match.")
    //        };

    //    return Task.FromResult(expiryDate);
    //}



    public async Task<List<string>> GetIpAddress()
    {
        var hostEntry = await Dns.GetHostEntryAsync(Dns.GetHostName());
        var ipAddresses = hostEntry.AddressList
            .Where(ip => ip.AddressFamily == AddressFamily.InterNetwork) // Filter only IPv4 addresses if needed
            .Select(ip => ip.ToString())
            .ToList();
        return ipAddresses;
    }

    private Task<string> GetHostName()
    {
        var hostname = Dns.GetHostName();
        return Task.FromResult(hostname);
    }

    private Task<List<string>> GetMacAddress()
    {
        var macAddress = (
            from nic in NetworkInterface.GetAllNetworkInterfaces()
            where nic.OperationalStatus == OperationalStatus.Up
            select nic.GetPhysicalAddress().ToString()).ToList();

        return Task.FromResult(macAddress);
    }


    private bool IsCustomDrSiteAvailable(string properties, int index, string roleType)
    {
        var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(properties);

        var customDrEntries = data.Where(kv => kv.Key.ToLower().Contains("custom dr"))
            .ToList();

        if(customDrEntries.Count == 0) return false;

        var chunkSize = 8;

        var chunkedLists = new List<List<KeyValuePair<string, object>>>();

        for (var i = 0; i < customDrEntries.Count; i += chunkSize)
        {
            var chunk = customDrEntries.Skip(i).Take(chunkSize).ToList();
            chunkedLists.Add(chunk);
        }

        var currentCount = chunkedLists[index];

        var kvp = currentCount.Any(k => k.Key.Contains(roleType, StringComparison.OrdinalIgnoreCase));
        
        return kvp;
    }


    private int CustomDrLicenseCountValidation(string properties, int index, string roleType)
    {
        var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(properties);

        var customDrEntries = data.Where(kv => kv.Key.ToLower().Contains("custom dr"))
            .ToList();

        if (customDrEntries.Count == 0) return 0;

            var chunkSize = 8;

        var chunkedLists = new List<List<KeyValuePair<string, object>>>();

        for (var i = 0; i < customDrEntries.Count; i += chunkSize)
        {
            var chunk = customDrEntries.Skip(i).Take(chunkSize).ToList();
            chunkedLists.Add(chunk);
        }

        var currentCount = chunkedLists[index];

        var kvp = currentCount.Where(k => k.Key.Contains(roleType, StringComparison.OrdinalIgnoreCase))
            .Select(k => k.Value)
            .FirstOrDefault();

        return Convert.ToInt32(kvp);
    }

    private int CustomDrDatabaseLicenseCountValidation(string properties, int index, string databaseTypeId)
    {
        try
        {
            var data = JsonConvert.DeserializeObject<Dictionary<string, object>>(properties);

            if (!data.TryGetValue("DatabaseDto", out var databaseDtoObj)) return 0;

            var databaseDto = JObject.FromObject(databaseDtoObj);

            var matchedCustomDrEntriesList = databaseDto.Properties()
                .Where(p => p.Name.Contains("custom dr") && p.Value.Type == JTokenType.Array)
                .Select(p => (JArray)p.Value)
                .ToList();

            if (!matchedCustomDrEntriesList.Any()) return 0;

            var currentCount = matchedCustomDrEntriesList[index];

            var matchingEntry = currentCount.FirstOrDefault(x => x["id"]?.ToString() == databaseTypeId);

            if (matchingEntry != null && int.TryParse(matchingEntry["count"]?.ToString(), out int count))
                return count;

            var othersEntry = currentCount.FirstOrDefault(x => x["type"]?.ToString() == "Others");

            return othersEntry != null && int.TryParse(othersEntry["count"]?.ToString(), out int othersCount)
                ? othersCount
                : 0;
        }
        catch (Exception ex)
        {
            return 0;
        }
    }

    private static int GetDatabaseTypeCounts(string json, string key, string databaseTypeId)
    {
        try
        {
            var jsonObject = JObject.Parse(json);

            var typeCounts = jsonObject.SelectToken($"DatabaseDto.{key}") as JArray;

            if (typeCounts == null)
                return 0;

            var matchingEntry = typeCounts.FirstOrDefault(x => x["id"]?.ToString() == databaseTypeId);

            if (matchingEntry != null && int.TryParse(matchingEntry["count"]?.ToString(), out int count))
                return count;

            var othersEntry = typeCounts.FirstOrDefault(x => x["type"]?.ToString() == "Others");

            return othersEntry != null && int.TryParse(othersEntry["count"]?.ToString(), out int othersCount)
                ? othersCount
                : 0;
        }
        catch (Exception ex)
        {
            return 0;
        }
    }

    public Task<(bool Success, List<string> TypeIds)> IsDatabaseTypeAsync(string json, string key, string databaseTypeId)
    {
        try
        {
            var jsonObject = JObject.Parse(json);

            var databaseDto = jsonObject["DatabaseDto"] as JObject;

            var typeCounts = key.ToLower().Equals("primary") || key.ToLower().Equals("dr")
                ? databaseDto?.Properties()
                    .Where(p => p.Name.Equals(key) && p.Value.Type == JTokenType.Array)
                    .Select(p => (JArray)p.Value)
                    .ToList()
                : databaseDto?.Properties()
                    .Where(p => p.Name.Contains(key) && p.Value.Type == JTokenType.Array)
                    .Select(p => (JArray)p.Value)
                    .ToList();

            if (typeCounts == null)
                return Task.FromResult((false, new List<string> { databaseTypeId }));

            var flattenedTypeCounts = typeCounts
                .SelectMany(arr => arr) // Flatten list of JArrays
                .Where(x => x != null && x["type"]?.ToString() != "Others") // Ensure valid filtering
                .ToList();

            var matchingEntry = flattenedTypeCounts
                .Any(x => x?["id"]?.ToString() == databaseTypeId); // Check if any match exists

            var typeIds = flattenedTypeCounts
                .Select(x => x["id"]?.ToString())
                .Where(id => !string.IsNullOrEmpty(id))
                .ToList();


            return Task.FromResult((matchingEntry, typeIds));
        }
        catch (Exception ex)
        {
            return Task.FromResult((false, new List<string> { databaseTypeId }));
        }

    }


    #endregion
}