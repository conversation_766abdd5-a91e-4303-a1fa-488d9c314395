﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullabel>disable</Nullabel>
	<SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>0c22fe69-46bc-4b21-9d8a-2af352e0427e</UserSecretsId>
    <DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <!--<SignAssembly>True</SignAssembly>-->
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Areas\Cyber\Views\AirGap\Create.cshtml.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Areas\Cyber\Views\AirGap\Create.cshtml" />
    <Content Remove="bundleconfig.json" />
    <Content Remove="wwwroot\js\Configuration\Account\ADConfiguration.js" />	  
    <Content Remove="wwwroot\js\cyberresiliency\AirGap.js" />
    <Content Remove="wwwroot\js\Drcalendar.js" />
    <Content Remove="wwwroot\js\report-charts\License_Utilization.js" />
    <Content Remove="wwwroot\js\Configuration\Account\User_Profile.js" />
    <Content Remove="wwwroot\js\WorkFlowConfiguration\HubService.js" />
    <Content Remove="wwwroot\js\WorkflowDiaram.js" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Areas\Dashboard\Controllers\ServiceAvailabilityController.cs~RF14eec769.TMP" />
    <None Remove="ThirdParty\devexpress\System.CodeDom.dll" />
    <None Remove="ThirdParty\devexpress\System.Configuration.ConfigurationManager.dll" />
    <None Remove="ThirdParty\devexpress\System.ServiceModel.NetTcp.dll" />
    <None Remove="ThirdParty\devexpress\System.ServiceModel.Security.dll" />
  </ItemGroup> 

	<ItemGroup>
    <_ContentIncludedByDefault Remove="wwwroot\js\Configuration\Account\ADConfiguration.js" />
    <_ContentIncludedByDefault Remove="wwwroot\js\cyberresiliency\AirGap.js" />
    <_ContentIncludedByDefault Remove="wwwroot\js\Drcalendar.js" />
    <_ContentIncludedByDefault Remove="wwwroot\js\report-charts\License_Utilization.js" />
    <_ContentIncludedByDefault Remove="wwwroot\js\Configuration\Account\User_Profile.js" />
  </ItemGroup>

  <ItemGroup>
    <None Include="bundleconfig.json" />
    <None Include="wwwroot\js\Configuration\Account\ADConfiguration.js" />
    <None Include="wwwroot\js\cyberresiliency\AirGap.js" />
    <None Include="wwwroot\js\Drcalendar.js" />
    <None Include="wwwroot\js\report-charts\License_Utilization.js" />
    <None Include="wwwroot\js\Configuration\Account\User_Profile.js" />
    <None Include="wwwroot\js\WorkFlowConfiguration\WorkflowConfiguration.js" />
    <None Include="wwwroot\lib\amcharts4\core.js" />
    <None Include="wwwroot\lib\jquery.steps\jquery.steps.js" />
    <None Include="wwwroot\lib\jquery.steps\jquery.steps.min.js" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.4" />
    <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />
    <PackageReference Include="MiniProfiler.EntityFrameworkCore" Version="4.5.4" />
    <PackageReference Include="NetEscapades.AspNetCore.SecurityHeaders" Version="1.0.0" />
    <PackageReference Include="NPOI" Version="2.7.3" />
    <PackageReference Include="WebMarkupMin.AspNetCore6" Version="2.18.1" />
    <PackageReference Include="WebMarkupMin.Core" Version="2.17.0" />
	  <!--<PackageReference Include="DevExpress.AspNetCore.Reporting.v23.1" Version="********"/>
	  <PackageReference Include="DevExpress.AspNetCore.Core.v23.1" Version="********"/>-->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\ContinuityPatrol.Domain\ContinuityPatrol.Domain.csproj" />
    <ProjectReference Include="..\..\Infrastructure\ContinuityPatrol.Persistence\ContinuityPatrol.Persistence.csproj" />
    <ProjectReference Include="..\..\Services\ContinuityPatrol.Services.Api\ContinuityPatrol.Services.Api.csproj" />
    <ProjectReference Include="..\..\Services\ContinuityPatrol.Services.Db\ContinuityPatrol.Services.Db.csproj" />
    <ProjectReference Include="..\..\Shared\ContinuityPatrol.Shared.Services\ContinuityPatrol.Shared.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Views\Account\Login.cshtml">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\bundles\js\" />
    <Folder Include="wwwroot\Report\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="ContinuityPatrol.CustomScriptValidator">
      <HintPath>ThirdParty\ContinuityPatrol.CustomScriptValidator.dll</HintPath>
    </Reference>
    <Reference Include="ContinuityPatrol.Language.Parser">
      <HintPath>ThirdParty\ContinuityPatrol.Language.Parser.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.AspNetCore.Common.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.AspNetCore.Common.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.AspNetCore.Core.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.AspNetCore.Core.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.AspNetCore.Reporting.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.AspNetCore.Reporting.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.AspNetCore.Resources.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.AspNetCore.Resources.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.Charts.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.CodeParser.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.CodeParser.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.Data.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.DataAccess.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataVisualization.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.DataVisualization.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Drawing.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.Drawing.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Office.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.Office.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.Pdf.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v23.1.Drawing">
      <HintPath>ThirdParty\devexpress\DevExpress.Pdf.v23.1.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.PivotGrid.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.PivotGrid.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.Printing.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.RichEdit.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v23.1.Export">
      <HintPath>ThirdParty\devexpress\DevExpress.RichEdit.v23.1.Export.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.Sparkline.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.Xpo.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.XtraCharts.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGauges.v23.1.Core">
      <HintPath>ThirdParty\devexpress\DevExpress.XtraGauges.v23.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v23.1">
      <HintPath>ThirdParty\devexpress\DevExpress.XtraReports.v23.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v23.1.Web">
      <HintPath>ThirdParty\devexpress\DevExpress.XtraReports.v23.1.Web.dll</HintPath>
    </Reference>
    <Reference Include="office">
      <HintPath>ThirdParty\office.dll</HintPath>
    </Reference>
    <Reference Include="PCPL">
      <HintPath>ThirdParty\PGlobalMirror\PCPL.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Common">
      <HintPath>ThirdParty\PGlobalMirror\Rebex.Common.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Networking">
      <HintPath>ThirdParty\PGlobalMirror\Rebex.Networking.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Sftp">
      <HintPath>ThirdParty\PGlobalMirror\Rebex.Sftp.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.SshShell">
      <HintPath>ThirdParty\PGlobalMirror\Rebex.SshShell.dll</HintPath>
    </Reference>
    <Reference Include="Rebex.Terminal">
      <HintPath>ThirdParty\PGlobalMirror\Rebex.Terminal.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <HintPath>ThirdParty\devexpress\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <HintPath>ThirdParty\devexpress\System.Drawing.Common.dll</HintPath>
    </Reference>
	  <Reference Include="System.Private.Windows.Core">
		  <HintPath>ThirdParty\devexpress\System.Private.Windows.Core.dll</HintPath>
	  </Reference>
    <Reference Include="System.Private.ServiceModel">
      <HintPath>ThirdParty\devexpress\System.Private.ServiceModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.MetadataLoadContext">
      <HintPath>ThirdParty\devexpress\System.Reflection.MetadataLoadContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Pkcs">
      <HintPath>ThirdParty\devexpress\System.Security.Cryptography.Pkcs.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData">
      <HintPath>ThirdParty\devexpress\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions">
      <HintPath>ThirdParty\devexpress\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel">
      <HintPath>ThirdParty\devexpress\System.ServiceModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>ThirdParty\devexpress\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>ThirdParty\devexpress\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.AccessControl">
      <HintPath>ThirdParty\devexpress\System.Threading.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Extensions">
      <HintPath>ThirdParty\devexpress\System.Windows.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Areas\Report\ReportTemplate\LicenseUtilizationOperationalServiceReport.cs">
      <SubType>XtraReport</SubType>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="ThirdParty\ContinuityPatrol.CustomScriptValidator.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ThirdParty\ContinuityPatrol.Language.Parser.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
