using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SingleSignOnFixture : IDisposable
{
    public List<SingleSignOn> SingleSignOnPaginationList { get; set; }
    public List<SingleSignOn> SingleSignOnList { get; set; }
    public SingleSignOn SingleSignOnDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SingleSignOnFixture()
    {
        var fixture = new Fixture();

        SingleSignOnList = fixture.Create<List<SingleSignOn>>();

        SingleSignOnPaginationList = fixture.CreateMany<SingleSignOn>(20).ToList();

        SingleSignOnPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SingleSignOnList.ForEach(x => x.CompanyId = CompanyId);

        SingleSignOnDto = fixture.Create<SingleSignOn>();

        SingleSignOnDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SingleSignOn CreateSingleSignOn(
        string profileName = "Default SSO Profile",
        string companyId = "COMPANY_123",
        string signOnTypeId = "TYPE_001",
        string signOnType = "SAML",
        string properties = "{}",
        string formVersion = "1.0",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SingleSignOn
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ProfileName = profileName,
            CompanyId = companyId,
            SignOnTypeId = signOnTypeId,
            SignOnType = signOnType,
            Properties = properties,
            FormVersion = formVersion,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SingleSignOn> CreateMultipleSingleSignOns(int count, string companyId = "COMPANY_123")
    {
        var singleSignOns = new List<SingleSignOn>();
        for (int i = 1; i <= count; i++)
        {
            singleSignOns.Add(CreateSingleSignOn(
                profileName: $"SSO Profile {i}",
                companyId: companyId,
                signOnTypeId: $"TYPE_{i:D3}",
                signOnType: $"Type {i}"
            ));
        }
        return singleSignOns;
    }

    public SingleSignOn CreateSingleSignOnWithSpecificId(string referenceId, string profileName = "Test SSO Profile")
    {
        return new SingleSignOn
        {
            ReferenceId = referenceId,
            ProfileName = profileName,
            CompanyId = "COMPANY_123",
            SignOnTypeId = "TYPE_001",
            SignOnType = "SAML",
            Properties = "{}",
            FormVersion = "1.0",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SingleSignOn CreateSingleSignOnForType(string signOnTypeId, string signOnType, string companyId = "COMPANY_123")
    {
        return CreateSingleSignOn(
            profileName: $"SSO_{signOnType}",
            companyId: companyId,
            signOnTypeId: signOnTypeId,
            signOnType: signOnType
        );
    }

    public SingleSignOn CreateSingleSignOnWithProperties(string properties, string companyId = "COMPANY_123")
    {
        return CreateSingleSignOn(
            profileName: "SSO_WithProperties",
            companyId: companyId,
            properties: properties
        );
    }

    public SingleSignOn CreateSingleSignOnWithFormVersion(string formVersion, string companyId = "COMPANY_123")
    {
        return CreateSingleSignOn(
            profileName: "SSO_WithFormVersion",
            companyId: companyId,
            formVersion: formVersion
        );
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
