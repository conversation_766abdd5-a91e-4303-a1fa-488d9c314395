﻿let selectedValues = [],
    createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase() === 'true',
    deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase() === 'true',
    globalUserName = $('#userLoginName').text().replace(/ /g, ''),
    workflowList = {
        getPagination: "/ITAutomation/WorkflowList/GetPagination",
        workflowConfiguration: '/ITAutomation/WorkflowConfiguration/List',
        getReport: "ITAutomation/WorkflowConfiguration/GetRunBookReport"
    };
$(function () {
    function renderSpan(value) {
        value = value || 'NA';
        return `<span title="${value}">${value}</span>`;
    }
    function applyColResizable() {
        $('#WorkFlowList').colResizable({
            liveDrag: true,
            fixed: false,
            partialRefresh: true,
            resizeMode: 'fit',
            headerOnly: false,
            minWidth: 50
        });
    }

    const dataTable = $('#WorkFlowList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: false,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": false,
        "filter": true,
        "order": [],     
        rowReorder: true,        
        ajax: {
            type: "GET",
            url: workflowList.getPagination,
            dataType: "json",
            data(d) {
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length ? selectedValues.join(';') : $('#wfListSearch').val();
                selectedValues.length = 0;
            },
            dataSrc(json) {
                json.recordsTotal = json.totalPages;
                json.recordsFiltered = json.totalCount;
                $(".pagination-column").toggleClass("disabled", !json.data.length);
                return json.data;
            }
        },
        columns: [
            {
                data: null,
                name: "Sr. No.",
                orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            { data: "workflowName", name: "Workflow Name", render: renderSpan },
            { data: "businessServiceName", name: "Business Service", render: renderSpan },
            { data: "profileName", name: "Profile Name", render: renderSpan },
            { data: "infraObjectName", name: "InfraObject Name", render: renderSpan },
            { data: "workflowType", name: "Type", render: renderSpan },
            { data: "lastExecutionDate", name: "LastExecution Date", render: renderSpan },
            { data: "createdName", name: "createdBy", render: renderSpan },
            {
                data: null,
                name: "Action",
                orderable: false,
                render: (data, type, row) => {
                    if (!row) return data;
                    const createdName = row.createdName || '',
                        isCreator = (createdName.includes('\\') ? createdName.split('\\')[1] : createdName) === globalUserName,
                        canView = row.isPublish || isCreator,
                        lockStatus = row.isLock ? `<span><i class="cp-lock text-danger me-1 ${!createPermission ? 'icon-disabled' : ''}" title="Lock" data-Workflow-id="${row.workflowId}" data-workflowName="${row.workflowName}" data-Workflow-isLock="${row.isLock}"></i></span>` : '',
                        reportBtn = `<span role='button' class="downloadReportButton me-1" title="Report" data-workflowId='${row.id}' data-workflowName='${row.workflowName}'><i class="cp-custom-reports"></i></span>`,
                        viewBtn = canView ? `<span role='button'><i class="cp-password-visible viewModeUnique text-primary" title="View" data-Workflow-id="${row.id}"></i></span>` : `<span role='button'><i class="cp-password-visible text-muted icon-disabled" title="View" style="pointer-events:none;"></i></span>`;
                    return `<div class="d-flex align-items-center gap-2">${viewBtn}${reportBtn}${lockStatus}</div>`;
                }
            }
        ],
        rowCallback(row, data, index) {
            $('td:eq(0)', row).html(this.api().context[0]._iDisplayStart + index + 1);
        },
        initComplete: applyColResizable,
        drawCallback: applyColResizable,
        columnDefs: [
            { orderable: false, targets: [-2, -1] },
            { targets: [0], width: "37px" },
            { targets: [0, 1, 2, 3, 4, 5, 6, 7], className: "truncate" }
        ]
    });

    $('#wfListSearch').on('keyup input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') return false;
        selectedValues = [];
        const inputVal = $('#wfListSearch').val(),
            keys = [
                { key: 'name', val: 'workflow' },
                { key: 'infraname', val: 'infraobject' },
                { key: 'profilename', val: 'profile' },
                { key: 'bsName', val: 'businessservice' },
                { key: 'bfName', val: 'businessfunction' },
                { key: 'wfType', val: 'workflowtype' }
            ];
        keys.forEach(x => { if ($(`#${x.key}`).is(':checked')) selectedValues.push(`${x.val}=${inputVal}`); });
        dataTable.ajax.reload(json => {
            if (json.recordsFiltered === 0) $('.dataTables_empty').text('No matching records found');
        });
    }, 200));

    $(document).on('click', '.viewModeUnique', function () {
        sessionStorage.setItem('WorkflowId', $(this).data("workflowId"));
        setTimeout(() => window.location.assign(workflowList.workflowConfiguration), 200);
    });

    // Report Download
    $('#WorkFlowList').on('click', '.downloadReportButton', async function () {
        let workflowId = $(this).data("workflowid");
        let workflowName = $(this).data("workflowname");
        try {
            const url = `${RootUrl + workflowList.getReport}?WorkflowId=${workflowId}`;
            const method = 'POST';
            const fetchResponse = await fetch(url, {
                method: method,
                headers: {
                    'RequestVerificationToken': await gettoken(),
                },
            });
            if (fetchResponse?.ok) {
                const response = await fetchResponse?.blob();
                if (response?.type == "application/pdf") {
                    const DateTime = new Date().toLocaleString('en-US', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                        hour12: false,
                    })?.replace(/[^0-9]/g, '');
                    const formattedDateTime = DateTime?.replace(/(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1$2$3_$4$5$6');
                    await downloadFileXls(response, workflowName + "_RunBook_" + formattedDateTime + ".xls", "application/xls");
                    message = workflowName + " RunBook downloaded successfully";
                    notificationAlert("success", message);
                }
                else {
                    message = workflowName + " RunBook downloaded failed!";
                    notificationAlert("error", message);
                }
            } else {
                message = workflowName + " RunBook downloaded failed!";
                notificationAlert("error", message);
            }
        } catch (error) {
            message = workflowName + " RunBook downloaded failed!";
            notificationAlert("error", message);
        }
    });

    async function downloadFileXls(blob, fileName, contentType) {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
});
