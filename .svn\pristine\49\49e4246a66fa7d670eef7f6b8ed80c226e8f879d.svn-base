﻿using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.Features.Node.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoNodeDataAttribute : AutoDataAttribute
{
    public AutoNodeDataAttribute() : base(() =>
    {
        var fixture = new Fixture();

        fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateNodeCommand>(p => p.Name, 10));

        fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateNodeCommand>(p => p.Name, 10));

        return fixture;
    })
    {

    }
}