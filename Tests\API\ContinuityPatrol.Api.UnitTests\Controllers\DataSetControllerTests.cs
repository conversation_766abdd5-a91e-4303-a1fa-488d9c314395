using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DataSet.Commands.Create;
using ContinuityPatrol.Application.Features.DataSet.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSet.Commands.Update;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDataSetById;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetNames;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DataSetControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DataSetController _controller;
    private readonly DataSetFixture _dataSetFixture;

    public DataSetControllerTests()
    {
        _dataSetFixture = new DataSetFixture();

        var testBuilder = new ControllerTestBuilder<DataSetController>();
        _controller = testBuilder.CreateController(
            _ => new DataSetController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDataSet_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dataSetFixture.CreateDataSetCommand;
        var expectedResponse = _dataSetFixture.CreateDataSetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSet(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetResponse>(createdResult.Value);
        Assert.Equal("Enterprise Business Analytics Dataset created successfully!", returnedResponse.Message);
       
        Assert.NotNull(returnedResponse.DataSetId);
    }

    [Fact]
    public async Task UpdateDataSet_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dataSetFixture.UpdateDataSetCommand;
        var expectedResponse = _dataSetFixture.UpdateDataSetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSet(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSetResponse>(okResult.Value);
        Assert.Equal("Updated Enterprise Analytics Dataset updated successfully!", returnedResponse.Message);
      
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDataSet_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSetId = Guid.NewGuid().ToString();
        var expectedResponse = _dataSetFixture.DeleteDataSetResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataSetCommand>(c => c.Id == dataSetId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataSet(dataSetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataSetResponse>(okResult.Value);
        Assert.Equal("Enterprise Analytics Dataset deleted successfully!", returnedResponse.Message);
    
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDataSets_ReturnsOkResult()
    {
        // Arrange
        var dataSetList = new List<DataSetListVm> { _dataSetFixture.DataSetListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetListQuery>(), default))
            .ReturnsAsync(dataSetList);

        // Act
        var result = await _controller.GetDataSets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise List Dataset", returnedList.First().DataSetName);
        Assert.Equal("Enterprise dataset for list operations", returnedList.First().Description);
        Assert.Equal("SELECT", returnedList.First().QueryType);
    }

    [Fact]
    public async Task GetDataSetById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSetId = Guid.NewGuid().ToString();
        var dataSetDetail = _dataSetFixture.DataSetDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetDetailQuery>(q => q.Id == dataSetId), default))
            .ReturnsAsync(dataSetDetail);

        // Act
        var result = await _controller.GetDataSetById(dataSetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataSetDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Dataset", returnedDetail.DataSetName);
        Assert.Equal("Detailed enterprise dataset for comprehensive analysis", returnedDetail.Description);
        Assert.Equal("Enterprise_Detail_Metrics", returnedDetail.PrimaryTableName);
        Assert.Equal("detail_metric_id", returnedDetail.PrimaryTablePKColumn);
    }

   

    [Fact]
    public async Task RunQuery_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var queryString = "Enterprise_Sample_Table";
        var runQueryResult = _dataSetFixture.DataSetRunQueryVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetRunQuery>(q => q.Table == queryString), default))
            .ReturnsAsync(runQueryResult);

        // Act
        var result = await _controller.RunQuery(queryString);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DataSetRunQueryVm>(okResult.Value);
        Assert.Equal("Enterprise_Run_Query_Results_JSON_Data", returnedResult.TableValue);
    }

    [Fact]
    public async Task GetPaginatedDatasets_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dataSetFixture.GetDataSetPaginatedListQuery;
        var paginatedResult = new PaginatedResult<DataSetListVm>
        {
            Data = new List<DataSetListVm> { _dataSetFixture.DataSetListVm },
            CurrentPage = 1,
            PageSize = 10,
            TotalCount = 1,
            TotalPages = 1,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDatasets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSetListVm>>(okResult.Value);
        Assert.Single(returnedResult.Data);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, returnedResult.PageSize);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.False(returnedResult.HasNextPage);
    }

    [Fact]
    public async Task IsDataDetNameExist_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var dataSetName = "Enterprise Test Dataset";
        var dataSetId = Guid.NewGuid().ToString();
        var nameExistsResult = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetNameUniqueQuery>(q => 
                q.DataSetName == dataSetName && q.DataSetId == dataSetId), default))
            .ReturnsAsync(nameExistsResult);

        // Act
        var result = await _controller.IsDataDetNameExist(dataSetName, dataSetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedResult);
    }

    [Fact]
    public async Task GetRunQueryById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSetId = Guid.NewGuid().ToString();
        var runQueryById = _dataSetFixture.GetDataSetByIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetByIdQuery>(q => q.Id == dataSetId), default))
            .ReturnsAsync(runQueryById);

        // Act
        var result = await _controller.GetRunQueryById(dataSetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<GetDataSetByIdVm>(okResult.Value);
        Assert.NotNull(returnedResult.QueryResult);
        Assert.Contains("Enterprise", returnedResult.QueryResult);
    }

    #endregion

    #region Error Handling

    [Fact]
    public async Task GetDataSetById_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDataSetById(invalidId));
    }

    [Fact]
    public async Task DeleteDataSet_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDataSet(invalidId));
    }

    [Fact]
    public async Task RunQuery_WithNullOrWhiteSpaceQuery_ThrowsArgumentException()
    {
        // Arrange
        var emptyQuery = "";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.RunQuery(emptyQuery));
    }

    [Fact]
    public async Task IsDataDetNameExist_WithNullOrWhiteSpaceName_ThrowsArgumentException()
    {
        // Arrange
        var emptyName = "";
        var validId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsDataDetNameExist(emptyName, validId));
    }

    [Fact]
    public async Task GetRunQueryById_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetRunQueryById(invalidId));
    }

    [Fact]
    public async Task CreateDataSet_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var command = _dataSetFixture.CreateDataSetCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Database query validation failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDataSet(command));
        Assert.Contains("Database query validation failed", exception.Message);
    }

    [Fact]
    public async Task UpdateDataSet_WhenMediatorThrowsNotFoundException_PropagatesException()
    {
        // Arrange
        var command = _dataSetFixture.UpdateDataSetCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("DataSet", command.Id));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDataSet(command));
        Assert.Contains("DataSet", exception.Message);
        Assert.Contains(command.Id, exception.Message);
    }

    #endregion

    #region ClearDataCache

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act
        _controller.ClearDataCache();

        // Assert - This test verifies the method executes without throwing exceptions
        // The actual cache clearing logic is tested in integration tests
        Assert.True(true);
    }

    #endregion

    

    [Fact]
    public async Task GetDataSets_HandlesEmptyList()
    {
        // Arrange
        var emptyList = new List<DataSetListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDataSets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSetListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDataSet_HandlesComplexEnterpriseDataSet()
    {
        // Arrange
        var complexCommand = new CreateDataSetCommand
        {
            DataSetName = "Enterprise Complex Multi-Table Analytics Dataset",
            Description = "Complex dataset combining multiple enterprise tables with advanced analytics capabilities",
            StoredQuery = @"
                SELECT
                    bs.BusinessServiceId,
                    bs.BusinessServiceName,
                    COUNT(bf.BusinessFunctionId) as FunctionCount,
                    AVG(pm.PerformanceScore) as AvgPerformance,
                    SUM(rm.RiskScore) as TotalRisk
                FROM Enterprise_Business_Services bs
                LEFT JOIN Enterprise_Business_Functions bf ON bs.BusinessServiceId = bf.BusinessServiceId
                LEFT JOIN Enterprise_Performance_Metrics pm ON bs.BusinessServiceId = pm.BusinessServiceId
                LEFT JOIN Enterprise_Risk_Metrics rm ON bs.BusinessServiceId = rm.BusinessServiceId
                WHERE bs.IsActive = 1
                    AND bs.CreatedDate >= DATEADD(month, -12, GETDATE())
                GROUP BY bs.BusinessServiceId, bs.BusinessServiceName
                HAVING COUNT(bf.BusinessFunctionId) > 5
                ORDER BY AvgPerformance DESC, TotalRisk ASC",
            TableAccessId = Guid.NewGuid().ToString(),
            PrimaryTableName = "Enterprise_Business_Services",
            PrimaryTablePKColumn = "BusinessServiceId",
            QueryType = "SELECT",
            StoredProcedureName = ""
        };

        var expectedResponse = new CreateDataSetResponse
        {
            DataSetId = Guid.NewGuid().ToString(),
            Message = "Enterprise Complex Multi-Table Analytics Dataset created successfully!",
        
        };

        _mediatorMock
            .Setup(m => m.Send(complexCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSet(complexCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSetResponse>(createdResult.Value);

        Assert.Equal("Enterprise Complex Multi-Table Analytics Dataset created successfully!", returnedResponse.Message);
       

        // Validate complex dataset properties
        Assert.Equal("Enterprise Complex Multi-Table Analytics Dataset", complexCommand.DataSetName);
        Assert.Contains("Complex dataset combining multiple enterprise tables", complexCommand.Description);
        Assert.Contains("LEFT JOIN", complexCommand.StoredQuery);
        Assert.Contains("GROUP BY", complexCommand.StoredQuery);
        Assert.Contains("HAVING", complexCommand.StoredQuery);
        Assert.Equal("Enterprise_Business_Services", complexCommand.PrimaryTableName);
        Assert.Equal("BusinessServiceId", complexCommand.PrimaryTablePKColumn);
        Assert.Equal("SELECT", complexCommand.QueryType);
    }

    [Fact]
    public async Task RunQuery_HandlesComplexQueryExecution()
    {
        // Arrange
        var complexQueryTable = "Enterprise_Complex_Analytics_View";
        var complexRunQueryResult = new DataSetRunQueryVm
        {
            TableValue = @"Complex analytics results including:
                - Business Service Performance Metrics
                - Risk Assessment Scores
                - Compliance Status Indicators
                - Resource Utilization Statistics
                - Trend Analysis Data"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetRunQuery>(q => q.Table == complexQueryTable), default))
            .ReturnsAsync(complexRunQueryResult);

        // Act
        var result = await _controller.RunQuery(complexQueryTable);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<DataSetRunQueryVm>(okResult.Value);

        // Validate complex result data
        Assert.Contains("Business Service Performance Metrics", returnedResult.TableValue);
        Assert.Contains("Risk Assessment Scores", returnedResult.TableValue);
        Assert.Contains("Compliance Status Indicators", returnedResult.TableValue);
        Assert.Contains("Resource Utilization Statistics", returnedResult.TableValue);
        Assert.Contains("Trend Analysis Data", returnedResult.TableValue);
    }

    [Fact]
    public async Task GetPaginatedDatasets_HandlesLargeDatasetWithFiltering()
    {
        // Arrange
        var largeDatasetQuery = new GetDataSetPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 25,
            SearchString = "Enterprise Analytics",
            SortColumn =  "DataSetName", 
           SortOrder ="CreatedDate DESC" 
        };

        var largePaginatedResult = new PaginatedResult<DataSetListVm>
        {
            Data = new List<DataSetListVm>
            {
                new DataSetListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    DataSetName = "Enterprise Analytics Dashboard Dataset",
                    Description = "Primary dataset for enterprise analytics dashboard",
                    StoredQuery = "SELECT * FROM Enterprise_Dashboard_Metrics",
                    PrimaryTableName = "Enterprise_Dashboard_Metrics",
                    QueryType = "SELECT"
                },
                new DataSetListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    DataSetName = "Enterprise Analytics Reporting Dataset",
                    Description = "Dataset for enterprise analytics reporting",
                    StoredQuery = "SELECT * FROM Enterprise_Reporting_Metrics",
                    PrimaryTableName = "Enterprise_Reporting_Metrics",
                    QueryType = "SELECT"
                },
                new DataSetListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    DataSetName = "Enterprise Analytics Trend Dataset",
                    Description = "Dataset for enterprise analytics trend analysis",
                    StoredQuery = "SELECT * FROM Enterprise_Trend_Metrics",
                    PrimaryTableName = "Enterprise_Trend_Metrics",
                    QueryType = "SELECT"
                }
            },
            CurrentPage = 2,
            PageSize = 25,
            TotalCount = 150,
            TotalPages = 6,
           
        };

        _mediatorMock
            .Setup(m => m.Send(largeDatasetQuery, default))
            .ReturnsAsync(largePaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDatasets(largeDatasetQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSetListVm>>(okResult.Value);

        Assert.Equal(3, returnedResult.Data.Count);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(25, returnedResult.PageSize);
        Assert.Equal(150, returnedResult.TotalCount);
        Assert.Equal(6, returnedResult.TotalPages);
        Assert.True(returnedResult.HasNextPage);
        Assert.True(returnedResult.HasPreviousPage);

        // Validate filtered results
        Assert.All(returnedResult.Data, dataset =>
            Assert.Contains("Enterprise Analytics", dataset.DataSetName));

        // Validate dataset types
        Assert.Contains(returnedResult.Data, d => d.DataSetName.Contains("Dashboard"));
        Assert.Contains(returnedResult.Data, d => d.DataSetName.Contains("Reporting"));
        Assert.Contains(returnedResult.Data, d => d.DataSetName.Contains("Trend"));

        // Validate all are SELECT queries
        Assert.All(returnedResult.Data, dataset =>
            Assert.Equal("SELECT", dataset.QueryType));
    }

    [Fact]
    public async Task GetDataSetNames_HandlesMultipleEnterpriseDatasets()
    {
        // Arrange
        var multipleDataSetNames = new List<DataSetNameVm>
        {
            new DataSetNameVm { Id = Guid.NewGuid().ToString(), DataSetName = "Enterprise Business Metrics Dataset" },
            new DataSetNameVm { Id = Guid.NewGuid().ToString(), DataSetName = "Enterprise Performance Analytics Dataset" },
            new DataSetNameVm { Id = Guid.NewGuid().ToString(), DataSetName = "Enterprise Risk Assessment Dataset" },
            new DataSetNameVm { Id = Guid.NewGuid().ToString(), DataSetName = "Enterprise Compliance Monitoring Dataset" },
            new DataSetNameVm { Id = Guid.NewGuid().ToString(), DataSetName = "Enterprise Resource Utilization Dataset" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSetNameQuery>(), default))
            .ReturnsAsync(multipleDataSetNames);

        // Act
        var result = await _controller.GetDataSetNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedNames = Assert.IsAssignableFrom<List<DataSetNameVm>>(okResult.Value);

        Assert.Equal(5, returnedNames.Count);

        // Validate dataset name categories
        Assert.Contains(returnedNames, n => n.DataSetName.Contains("Business Metrics"));
        Assert.Contains(returnedNames, n => n.DataSetName.Contains("Performance Analytics"));
        Assert.Contains(returnedNames, n => n.DataSetName.Contains("Risk Assessment"));
        Assert.Contains(returnedNames, n => n.DataSetName.Contains("Compliance Monitoring"));
        Assert.Contains(returnedNames, n => n.DataSetName.Contains("Resource Utilization"));

        // Validate all have enterprise prefix
        Assert.All(returnedNames, name =>
            Assert.StartsWith("Enterprise", name.DataSetName));

        // Validate all have valid IDs
        Assert.All(returnedNames, name =>
            Assert.True(Guid.TryParse(name.Id, out _)));
    }

    [Fact]
    public async Task UpdateDataSet_HandlesStoredProcedureDataSet()
    {
        // Arrange
        var storedProcCommand = _dataSetFixture.UpdateDataSetCommand;
        storedProcCommand.DataSetName = "Enterprise Stored Procedure Dataset";
        storedProcCommand.QueryType = "STORED_PROCEDURE";
        storedProcCommand.StoredProcedureName = "sp_GetEnterpriseMetrics";
        storedProcCommand.StoredQuery = "";

        var expectedResponse = _dataSetFixture.UpdateDataSetResponse;

        _mediatorMock
            .Setup(m => m.Send(storedProcCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSet(storedProcCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSetResponse>(okResult.Value);

        Assert.Equal("STORED_PROCEDURE", storedProcCommand.QueryType);
        Assert.Equal("sp_GetEnterpriseMetrics", storedProcCommand.StoredProcedureName);
        Assert.Equal("Enterprise Stored Procedure Dataset", storedProcCommand.DataSetName);
    }

    [Fact]
    public async Task IsDataDetNameExist_HandlesUniqueNameValidation()
    {
        // Arrange
        var uniqueDataSetName = "Unique Enterprise Dataset Name";
        var dataSetId = Guid.NewGuid().ToString();
        var nameExistsResult = true; // Name already exists

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSetNameUniqueQuery>(q =>
                q.DataSetName == uniqueDataSetName && q.DataSetId == dataSetId), default))
            .ReturnsAsync(nameExistsResult);

        // Act
        var result = await _controller.IsDataDetNameExist(uniqueDataSetName, dataSetId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedResult); // Name exists, so validation should return true
    }

    
}
