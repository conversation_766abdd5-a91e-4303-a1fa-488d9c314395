using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberComponentMappingService
{
    Task<List<CyberComponentMappingListVm>> GetCyberComponentMappingList();
    Task<BaseResponse> CreateAsync(CreateCyberComponentMappingCommand createCyberComponentMappingCommand);
    Task<BaseResponse> UpdateAsync(UpdateCyberComponentMappingCommand updateCyberComponentMappingCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<CyberComponentMappingDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsCyberComponentMappingNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<CyberComponentMappingListVm>> GetPaginatedCyberComponentMappings(GetCyberComponentMappingPaginatedListQuery query);
    #endregion
}
