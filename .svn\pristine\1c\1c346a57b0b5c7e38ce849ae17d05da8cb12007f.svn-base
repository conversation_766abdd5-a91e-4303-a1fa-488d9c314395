﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport.Models
{
    public class GetRPOSLAZertoVpgReportVm
    {
        public int SrNo { get; set; }
        public string Id { get; set; }
        public string PRIPAddress { get; set; }
        public string DRIPAddress { get; set; }
        public string PRSiteName { get; set; }
        public string DRSiteName { get; set; }
        public string PRVPGName { get; set; }
        public string DRVPGName { get; set; }
        public string PRVPGState { get; set; }
        public string DRVPGState { get; set; }
        public string DataLag { get; set; }
        public string ConfigureRPO { get; set; }
        public string Threshold { get; set; }
        public string TimeStamp { get; set; }
        public bool IsDataLagExceeded { get; set; }
        public bool IsThresholdExceeded { get; set; }
        public string Properties { get; set; }
    }
    public class GetRPOSLAZetroVpgBusinessServiceDetails 
    {
        public string InfraObjectId { get; set; }
        public string InfraObjectName { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
        public string BusinessServiceId { get; set; }
        public string BusinessServiceName { get; set; }
        public string PRIPAddress { get; set; }
        public string DRIPAddress { get; set; }
        public string ReportGeneratedBy { get; set; }
        public string Date { get; set; }
        public int TotalCount { get; set; }
        public int DataLagExceededCount { get; set; }
        public int ThresholdExceededCount { get; set; }
        public int UnderThresholdCount { get; set; }
        public string DateOption { get; set; }
        public string InfraObjectType { get; set; }
        public List<GetRPOSLAZertoVpgReportVm> GetRPOSLAZertoVpgReportVms { get; set; }
    }
}
