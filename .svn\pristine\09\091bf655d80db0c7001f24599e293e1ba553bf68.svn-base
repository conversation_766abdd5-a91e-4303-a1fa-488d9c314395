﻿namespace ContinuityPatrol.Application.Features.Company.Commands.Update;

public class UpdateCompanyCommandValidator : AbstractValidator<UpdateCompanyCommand>
{
    private readonly ICompanyRepository _companyRepository;

    public UpdateCompanyCommandValidator(ICompanyRepository companyRepository)
    {
        _companyRepository = companyRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.DisplayName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 15).WithMessage("{PropertyName} should contain between 3 to 15 characters.");

        RuleFor(x => x.WebAddress)
            .Matches(@"^((ftp|http|https):\/\/)?([a-zA-Z0-9]+(\.[a-zA-Z0-9]+)+.*)$",
                RegexOptions.IgnorePatternWhitespace).WithMessage("Enter the Valid WebAddress")
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull();

        RuleFor(e => e)
            .MustAsync(CompanyLogoMatches).WithMessage("Please Enter Valid CompanyLogo.");

        RuleFor(e => e)
            .MustAsync(CompanyNameUnique)
            .WithMessage("A company with the same name already exists.");

        RuleFor(e => e)
            .MustAsync(DisplayNameUnique)
            .WithMessage("A company with the same display name already exists.");

        RuleFor(e => e)
            .MustAsync(IsParentIdValid).WithMessage("Parent Id is invalid");
    }

    private Task<bool> IsParentIdValid(UpdateCompanyCommand updateCompanyCommand, CancellationToken token)
    {
        if (!updateCompanyCommand.IsParent)
        {
            Guard.Against.InvalidGuidOrEmpty(updateCompanyCommand.ParentId, "Parent Id");
            Guard.Against.InvalidGuidOrEmpty(updateCompanyCommand.Id, "Id");

            return Task.FromResult(true);
        }

        return Task.FromResult(true);
    }

    private async Task<bool> CompanyNameUnique(UpdateCompanyCommand updateCompanyCommand, CancellationToken token)
    {
        return !await _companyRepository.IsNameExist(updateCompanyCommand.Name, updateCompanyCommand.Id);
    }

    private async Task<bool> DisplayNameUnique(UpdateCompanyCommand updateCompanyCommand, CancellationToken token)
    {
        return !await _companyRepository.IsDisplayNameExist(updateCompanyCommand.DisplayName, updateCompanyCommand.Id);
    }

    private Task<bool> CompanyLogoMatches(UpdateCompanyCommand updateCompanyCommand, CancellationToken token)
    {
        if (updateCompanyCommand.CompanyLogo == string.Empty) return Task.FromResult(true);

        if (updateCompanyCommand.CompanyLogo != null)
        {
            var logo = updateCompanyCommand.CompanyLogo.Split(",").ToList();
            var index = logo[1];
            var companyLogo =
                new Regex(
                    @"^(?:[a-zA-Z0-9+/]{4})*(?:|(?:[a-zA-Z0-9+/]{3}=)|(?:[a-zA-Z0-9+/]{2}==)|(?:[a-zA-Z0-9+/]{1}===))$");
            companyLogo.Matches(index);
            return Task.FromResult(true);
        }

        return Task.FromResult(false);
    }
}