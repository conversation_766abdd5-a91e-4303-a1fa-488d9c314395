using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class EmployeeFixture : IDisposable
{
    public List<Employee> EmployeePaginationList { get; set; }
    public List<Employee> EmployeeList { get; set; }
    public Employee EmployeeDto { get; set; }

  

    public ApplicationDbContext DbContext { get; private set; }

    public EmployeeFixture()
    {
        var fixture = new Fixture();

        EmployeeList = fixture.Create<List<Employee>>();

        EmployeePaginationList = fixture.CreateMany<Employee>(20).ToList();

       
        EmployeePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EmployeePaginationList.ForEach(x => x.IsActive = true);

     
        EmployeeList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        EmployeeList.ForEach(x => x.IsActive = true);

        EmployeeDto = fixture.Create<Employee>();
        EmployeeDto.ReferenceId = Guid.NewGuid().ToString();
        EmployeeDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
