using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RsyncMonitorStatusFixture : IDisposable
{
    public List<RsyncMonitorStatus> RsyncMonitorStatusPaginationList { get; set; }
    public List<RsyncMonitorStatus> RsyncMonitorStatusList { get; set; }
    public RsyncMonitorStatus RsyncMonitorStatusDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_OBJ_123";
    public const string InfraObjectName = "Test Infrastructure Object";
    public const string WorkflowId = "WORKFLOW_123";
    public const string WorkflowName = "Test Workflow";
    public const string Type = "Rsync";
    public const string UserId = "USER_123";

    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public RsyncMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<RsyncMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => InfraObjectName)
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => WorkflowName)
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => "4 hours")
            .With(x => x.DataLagValue, () => "30 minutes")
            .With(x => x.Threshold, () => "2 hours")
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
        );

        RsyncMonitorStatusList = _fixture.CreateMany<RsyncMonitorStatus>(5).ToList();
        RsyncMonitorStatusPaginationList = _fixture.CreateMany<RsyncMonitorStatus>(20).ToList();
        RsyncMonitorStatusDto = _fixture.Create<RsyncMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public RsyncMonitorStatus CreateRsyncMonitorStatusWithInfraObjectId(string infraObjectId)
    {
        return _fixture.Build<RsyncMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, Type)
            .With(x => x.InfraObjectId, infraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.WorkflowId, WorkflowId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, "4 hours")
            .With(x => x.DataLagValue, "30 minutes")
            .With(x => x.Threshold, "2 hours")
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncMonitorStatus CreateRsyncMonitorStatusWithType(string type)
    {
        return _fixture.Build<RsyncMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type)
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.WorkflowId, WorkflowId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, "4 hours")
            .With(x => x.DataLagValue, "30 minutes")
            .With(x => x.Threshold, "2 hours")
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncMonitorStatus CreateRsyncMonitorStatusWithWorkflowId(string workflowId)
    {
        return _fixture.Build<RsyncMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, Type)
            .With(x => x.InfraObjectId, InfraObjectId)
            .With(x => x.InfraObjectName, InfraObjectName)
            .With(x => x.WorkflowId, workflowId)
            .With(x => x.WorkflowName, WorkflowName)
            .With(x => x.Properties, _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, "4 hours")
            .With(x => x.DataLagValue, "30 minutes")
            .With(x => x.Threshold, "2 hours")
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncMonitorStatus CreateRsyncMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string workflowId = null,
        string workflowName = null,
        string configuredRPO = null,
        string dataLagValue = null,
        string threshold = null,
        string properties = null,
        bool isActive = true)
    {
        return _fixture.Build<RsyncMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.InfraObjectName, infraObjectName ?? InfraObjectName)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.WorkflowName, workflowName ?? WorkflowName)
            .With(x => x.Properties, properties ?? _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, configuredRPO ?? "4 hours")
            .With(x => x.DataLagValue, dataLagValue ?? "30 minutes")
            .With(x => x.Threshold, threshold ?? "2 hours")
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .Create();
    }

    public RsyncMonitorStatus CreateRsyncMonitorStatusWithLongProperties(int propertyLength)
    {
        var longProperty = new string('A', propertyLength);
        return CreateRsyncMonitorStatusWithProperties(properties: longProperty);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "Rsync", "RoboCopy", "DataSync", "FastCopy" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] CommonConfiguredRPOs = { "1 hour", "2 hours", "4 hours", "8 hours", "24 hours" };
        public static readonly string[] CommonDataLagValues = { "5 minutes", "15 minutes", "30 minutes", "1 hour", "2 hours" };
        public static readonly string[] CommonThresholds = { "30 minutes", "1 hour", "2 hours", "4 hours" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
        
        public static readonly string ValidGuid = Guid.NewGuid().ToString();
        public static readonly string InvalidGuid = "INVALID_GUID";
        public static readonly string EmptyGuid = Guid.Empty.ToString();
    }
}
