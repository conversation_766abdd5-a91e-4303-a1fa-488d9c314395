using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactActivity.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;

namespace ContinuityPatrol.Application.Mappings;

public class ImpactActivityProfile : Profile
{
    public ImpactActivityProfile()
    {
        CreateMap<ImpactActivity, ImpactActivityListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ImpactActivity, ImpactActivityDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<ImpactActivity, CreateImpactActivityCommand>().ReverseMap();
        CreateMap<ImpactActivity, ImpactActivityViewModel>().ReverseMap();

        CreateMap<CreateImpactActivityCommand, ImpactActivityViewModel>().ReverseMap();
        CreateMap<UpdateImpactActivityCommand, ImpactActivityViewModel>().ReverseMap();

        CreateMap<UpdateImpactActivityCommand, ImpactActivity>().ForMember(x => x.Id, y => y.Ignore());
    }
}