﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;

public class
    CreateWorkflowInfraObjectCommandHandler : IRequestHandler<CreateWorkflowInfraObjectCommand,
        CreateWorkflowInfraObjectResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;

    public CreateWorkflowInfraObjectCommandHandler(IMapper mapper,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository, IPublisher publisher,
        ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateWorkflowInfraObjectResponse> Handle(CreateWorkflowInfraObjectCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var workflowInfraObject = _mapper.Map<Domain.Entities.WorkflowInfraObject>(request);

        var infraObject =
            await _workflowInfraObjectRepository.GetWorkflowInfraObjectDetailByInfraObjectId(request.InfraObjectId);

        var actionType = new List<string>
            { "custom", "resiliency ready", "monitoring", "resilience ready", "resiliency readiness" ,"cyber resiliency"};

        if (infraObject.Count > 0)
            infraObject.ForEach(infra =>
            {
                if (actionType.Contains(request.ActionType.Trim().ToLower())) return;

                if (request.ActionType.Trim().ToLower().Equals(infra.ActionType.Trim().ToLower()))
                    throw new InvalidException(
                        $"'{infra.InfraObjectName}' is already attached to '{infra.WorkflowName}' With '{request.ActionType}' Please De-Attach Workflow..!!!");
            });

        var workflowActionType = await _workflowInfraObjectRepository.GetWorkflowIdAttachByActionType(
            request.WorkflowId,
            request.ActionType);

        if (workflowActionType != null)
            if (workflowActionType.ActionType.Trim().ToLower().Contains(request.ActionType.Trim().ToLower()))
                await _workflowInfraObjectRepository.DeleteAsync(workflowActionType);

        workflowInfraObject = await _workflowInfraObjectRepository.AddAsync(workflowInfraObject);

        var response = new CreateWorkflowInfraObjectResponse
        {
            Message =
                $"Workflow {workflowInfraObject.WorkflowName} attached to infraobject {workflowInfraObject.InfraObjectName} successfully!",
            WorkflowInfraObjectId = workflowInfraObject.ReferenceId
        };

        await _publisher.Publish(
            new WorkflowInfraObjectCreatedEvent
            {
                InfraObjectName = workflowInfraObject.InfraObjectName,
                WorkflowName = workflowInfraObject.WorkflowName,
                ActionType = workflowInfraObject.ActionType
            },
            cancellationToken);

        return response;
    }
}