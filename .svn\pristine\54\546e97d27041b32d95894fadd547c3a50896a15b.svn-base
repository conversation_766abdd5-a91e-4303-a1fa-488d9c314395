using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class BulkImportActionResultService : BaseClient, IBulkImportActionResultService
{
    public BulkImportActionResultService(IConfiguration config, IAppCache cache, ILogger<BulkImportActionResultService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultList()
    {
        var request = new RestRequest("api/v6/bulkimportactionresults");

        return await GetFromCache<List<BulkImportActionResultListVm>>(request, "GetBulkImportActionResultList");
    }

    public async Task<BaseResponse> CreateAsync(CreateBulkImportActionResultCommand createBulkImportActionResultCommand)
    {
        var request = new RestRequest("api/v6/bulkimportactionresults", Method.Post);

        request.AddJsonBody(createBulkImportActionResultCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBulkImportActionResultCommand updateBulkImportActionResultCommand)
    {
        var request = new RestRequest("api/v6/bulkimportactionresults", Method.Put);

        request.AddJsonBody(updateBulkImportActionResultCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/bulkimportactionresults/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BulkImportActionResultDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/bulkimportactionresults/{id}");

        return await Get<BulkImportActionResultDetailVm>(request);
    }

    public async Task<List<BulkImportActionResultListVm>> GetByOperationIdAndOperationGroupId(string operationId, string operationGroupId)
    {
        var request = new RestRequest($"api/v6/bulkimportactionresults/operationid?operationId={operationId}&operationGroupId={operationGroupId}");

        return await Get<List<BulkImportActionResultListVm>>(request);
    }

    public async Task<List<BulkImportActionResultListVm>> GetBulkImportActionResultOperationGroupId(string operationGroupId)
    {
        var request = new RestRequest($"api/v6/bulkimportactionresults/operation-GroupId?operation-GroupId={operationGroupId}");

        return await Get<List<BulkImportActionResultListVm>>(request);
    }
    #region NameExist
    //  public async Task<bool> IsBulkImportActionResultNameExist(string name, string? id)
    //  {
    //     var request = new RestRequest($"api/v6/bulkimportactionresults/name-exist?bulkimportactionresultName={name}&id={id}");
    //
    //     return await Get<bool>(request);
    //  }
    #endregion

    #region Paginated
    //  public async Task<PaginatedResult<BulkImportActionResultListVm>> GetPaginatedBulkImportActionResults(GetBulkImportActionResultPaginatedListQuery query)
    //  {
    //      var request = new RestRequest("api/v6/bulkimportactionresults/paginated-list");
    //
    //      return await Get<PaginatedResult<BulkImportActionResultListVm>>(request);
    //  }
    #endregion
}
