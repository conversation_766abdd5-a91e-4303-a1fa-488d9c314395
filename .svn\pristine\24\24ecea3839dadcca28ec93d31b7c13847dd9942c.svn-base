﻿using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MsSqlNativeLogShippingMonitorStatusService : BaseClient, IMsSqlNativeLogShippingMonitorStatusService
{
    public MsSqlNativeLogShippingMonitorStatusService(IConfiguration config, IAppCache cache, ILogger<MsSqlNativeLogShippingMonitorStatusService> logger) 
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMsSqlNativeLogShippingMonitorStatusCommand createMsSqlNativeLogShippingMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mssqlnativelogshippingmonitorstatus", Method.Post);

        request.AddJsonBody(createMsSqlNativeLogShippingMonitorStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMssqlNativeLogShippingMonitorStatusCommand updateMsSqlNativeLogShippingMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/mssqlnativelogshippingmonitorstatus", Method.Put);

        request.AddJsonBody(updateMsSqlNativeLogShippingMonitorStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<MsSqlNativeLogShippingMonitorStatusListVm>> GetAllMsSqlNativeLogShippingMonitorStatus()
    {
        var request = new RestRequest("api/v6/mssqlnativelogshippingmonitorstatus");

        return await GetFromCache<List<MsSqlNativeLogShippingMonitorStatusListVm>>(request, "GetAllMsSqlNativeLogShippingMonitorStatus");
    }

    public async Task<MsSqlNativeLogShippingMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/mssqlnativelogshippingmonitorstatus/{id}");

        return await Get<MsSqlNativeLogShippingMonitorStatusDetailVm>(request);
    }

    public async Task<List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>> GetMsSqlNativeLogShippingMonitorStatusByType(string type)
    {
        var request = new RestRequest($"api/v6/mssqlnativelogshippingmonitorstatus/type?type={type}");

        return await Get<List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>>(request);
    }

    public async Task<PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>> GetPaginatedMsSqlNativeLogShippingMonitorStatus(GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/mssqlnativelogshippingmonitorstatus/paginated-list?SearchString=");

        return await Get<PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>>(request);
    }

    //public async Task<string> GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(string infraObjectId)
    //{
    //    var request = new RestRequest($"api/v6/mssqlnativelogshippingmonitorstatus/by/{infraObjectId}", Method.Get);

    //    return await Get<string>(request);
    //}
}