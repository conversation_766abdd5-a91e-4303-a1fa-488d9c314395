﻿using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Commands;

public class DeleteLoadBalancerTests : IClassFixture<LoadBalancerFixture>,IClassFixture<GroupPolicyFixture>
{
    private readonly LoadBalancerFixture _loadBalancerFixture;

    private GroupPolicyFixture _groupPolicyFixture;

    private readonly Mock<ILoadBalancerRepository> _mockLoadBalancerRepository;

    private readonly Mock<IGroupPolicyRepository> _mockGroupPolicyRepository;

    private readonly DeleteLoadBalancerCommandHandler _handler;

    public DeleteLoadBalancerTests(LoadBalancerFixture loadBalancerFixture, GroupPolicyFixture groupPolicyFixture)
    {
        _loadBalancerFixture = loadBalancerFixture;
        _groupPolicyFixture = groupPolicyFixture;

        Mock<IPublisher> mockPublisher = new();

        _mockLoadBalancerRepository = LoadBalancerRepositoryMocks.DeleteLoadBalancerRepository(_loadBalancerFixture.LoadBalancers);
        _mockGroupPolicyRepository = GroupPolicyRepositoryMocks.DeleteGroupPolicyRepository(_groupPolicyFixture.GroupPolicies);
        _handler = new DeleteLoadBalancerCommandHandler(_mockLoadBalancerRepository.Object, mockPublisher.Object, _mockGroupPolicyRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_LoadBalancerDeleted()
    {
        var grpPolicy = new List<Domain.Entities.GroupPolicy>
        {

        };

        _mockGroupPolicyRepository.Setup(dp => dp.GetGroupPolicyByLoadBalancerId(_loadBalancerFixture.LoadBalancers[0].ReferenceId)).ReturnsAsync(grpPolicy);
        var result = await _handler.Handle(new DeleteLoadBalancerCommand { Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteLoadBalancerResponse_When_LoadBalancerDeleted()
    {
        var grpPolicy = new List<Domain.Entities.GroupPolicy>
        {

        };

        _mockGroupPolicyRepository.Setup(dp => dp.GetGroupPolicyByLoadBalancerId(_loadBalancerFixture.LoadBalancers[0].ReferenceId)).ReturnsAsync(grpPolicy);

        var result = await _handler.Handle(new DeleteLoadBalancerCommand { Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteLoadBalancerResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_ReturnIsActive_False_WhenDeleteLoadBalancer()
    {
        var grpPolicy = new List<Domain.Entities.GroupPolicy>
        {

        };

        _mockGroupPolicyRepository.Setup(dp => dp.GetGroupPolicyByLoadBalancerId(_loadBalancerFixture.LoadBalancers[0].ReferenceId)).ReturnsAsync(grpPolicy);

        await _handler.Handle(new DeleteLoadBalancerCommand { Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId }, CancellationToken.None);

        var form = await _mockLoadBalancerRepository.Object.GetByReferenceIdAsync(_loadBalancerFixture.LoadBalancers[0].ReferenceId);

        form.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidLoadBalancerId()
    {
        var grpPolicy = new List<Domain.Entities.GroupPolicy>
        {

        };



        _mockGroupPolicyRepository.Setup(dp => dp.GetGroupPolicyByLoadBalancerId(_loadBalancerFixture.LoadBalancers[0].ReferenceId)).ReturnsAsync(grpPolicy);

        var result = _handler.Handle(new DeleteLoadBalancerCommand { Id = int.MaxValue.ToString() }, CancellationToken.None);

         Assert.Contains("The LoadBalancer is currently in use",result.Exception.InnerException.Message);
    }
    
    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var grpPolicy = new List<Domain.Entities.GroupPolicy> { 
                     
        };

        _mockGroupPolicyRepository.Setup(dp => dp.GetGroupPolicyByLoadBalancerId(_loadBalancerFixture.LoadBalancers[0].ReferenceId)).ReturnsAsync(grpPolicy);
        await _handler.Handle(new DeleteLoadBalancerCommand { Id = _loadBalancerFixture.LoadBalancers[0].ReferenceId }, CancellationToken.None);

        _mockLoadBalancerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockLoadBalancerRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LoadBalancer>()), Times.Once);
    }

}