using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Events;

public class CreateAdPasswordJobEventTests : IClassFixture<AdPasswordJobFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly AdPasswordJobCreatedEventHandler _handler;

    public CreateAdPasswordJobEventTests(AdPasswordJobFixture adPasswordJobFixture, UserActivityFixture userActivityFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/adpasswordjob");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockAdPasswordJobEventLogger = new Mock<ILogger<AdPasswordJobCreatedEventHandler>>();

        _mockUserActivityRepository = CompanyRepositoryMocks.CreateCompanyEventRepository(_userActivityFixture.UserActivities);

        _handler = new AdPasswordJobCreatedEventHandler(
            mockLoggedInUserService.Object, 
            mockAdPasswordJobEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateAdPasswordJobEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var adPasswordJobCreatedEvent = new AdPasswordJobCreatedEvent { Name = "TestDomainServer" };

        // Act
        var result = _handler.Handle(adPasswordJobCreatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var adPasswordJobCreatedEvent = new AdPasswordJobCreatedEvent { Name = "TestDomainServer" };

        // Act
        await _handler.Handle(adPasswordJobCreatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var adPasswordJobCreatedEvent = new AdPasswordJobCreatedEvent { Name = "TestDomainServer" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(adPasswordJobCreatedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Create AdPasswordJob");
        capturedUserActivity.Entity.ShouldBe("AdPasswordJob");
        capturedUserActivity.ActivityType.ShouldBe("Create");
        capturedUserActivity.ActivityDetails.ShouldContain("TestDomainServer");
        capturedUserActivity.ActivityDetails.ShouldContain("created successfully");
    }
}
