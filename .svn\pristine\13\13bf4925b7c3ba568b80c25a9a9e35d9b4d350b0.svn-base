﻿using ContinuityPatrol.Application.Features.ManageWorkflow.Queries;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectIdAndActionType;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowIdUnique;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Orchestration;

public class WorkflowInfraObjectService : BaseService, IWorkflowInfraObjectService
{
    public WorkflowInfraObjectService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowInfraObjectCommand createWorkflowInfraObject)
    {
        Logger.LogDebug($"Attach-InfraObject '{createWorkflowInfraObject.InfraObjectName}'");

        return await Mediator.Send(createWorkflowInfraObject);
    }

    public async Task<BaseResponse> DeleteAsync(DeleteWorkflowInfraObjectCommand deleteWorkflowInfraObject)
    {
        Logger.LogDebug($"De-Attach InfraObject '{deleteWorkflowInfraObject.InfraObjectId}'");

        return await Mediator.Send(deleteWorkflowInfraObject);
    }

    public async Task<List<WorkflowInfraObjectListVm>> GetWorkflowInfraObjectList()
    {
        Logger.LogDebug("Get All WorkflowInfraObject");

        return await Mediator.Send(new GetWorkflowInfraObjectListQuery());
    }

    public async Task<List<GetInfraObjectByWorkflowIdVm>> GetInfraObjectByWorkflowId(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Get InfraObject by Workflow Id '{workflowId}'");

        return await Mediator.Send(new GetInfraObjectByWorkflowIdQuery { WorkflowId = workflowId });
    }


    public async Task<bool> WorkflowInfraObjectByWorkflowIdExist(string workflowId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowId, "Workflow Id");

        Logger.LogDebug($"Check Workflow Id '{workflowId}'");

        return await Mediator.Send(new GetWorkflowInfraObjectByWorkflowIdUniqueQuery { WorkflowId = workflowId });
    }


    public async Task<List<ManageWorkflowModel>> GetWorkflowDetails()
    {
        Logger.LogDebug("Get All GetWorkflowDetails");

        return await Mediator.Send(new GetWorkflowlistQuery());
    }

    public async Task<List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>> GetWorkflowByInfraObjectIdAndActionType(
        string infraId, string actionType)
    {
        Guard.Against.InvalidGuidOrEmpty(infraId, "InfraObject Id");

        Logger.LogDebug(
            $"Get WorkflowInfraObjects by Infra Object Id: '{infraId}' and ActionType:'{actionType}'");

        return await Mediator.Send(new GetWorkflowInfraObjectByInfraObjectIdAndActionTypeQuery
            { InfraObjectId = infraId, ActionType = actionType });
    }

    public async Task<List<WorkflowInfraObjectByInfraObjectIdVm>> GetWorkflowByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObject Id");

        Logger.LogDebug($"Get WorkflowInfraObjects by Infra Object Id '{infraObjectId}'");

        return await Mediator.Send(new GetWorkflowInfraObjectByInfraObjectIdQuery { InfraObjectId = infraObjectId });
    }
}