﻿using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class OracleMonitorStatusService : BaseClient, IOracleMonitorStatusService
{
    public OracleMonitorStatusService(IConfiguration config, IAppCache cache, ILogger<OracleMonitorStatusService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateOracleMonitorStatusCommand createOracleMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/oraclemonitorstatus", Method.Post);

        request.AddJsonBody(createOracleMonitorStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateOracleMonitorStatusCommand updateOracleMonitorStatusCommand)
    {
        var request = new RestRequest("api/v6/oraclemonitorstatus", Method.Put);

        request.AddJsonBody(updateOracleMonitorStatusCommand);

        ClearCache("UpdateOracleMonitorStatus");

        return await Put<BaseResponse>(request);
    }

    public async Task<List<OracleMonitorStatusListVm>> GetOracleMonitorStatusList()
    {
        var request = new RestRequest("api/v6/oraclemonitorstatus");

        return await GetFromCache<List<OracleMonitorStatusListVm>>(request, "GetOracleMonitorStatusList");
    }

    public async Task<OracleMonitorStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/oraclemonitorstatus/{id}");

        return await Get<OracleMonitorStatusDetailVm>(request);
    }
    public async Task<PaginatedResult<OracleMonitorStatusListVm>> GetPaginatedOracleMonitorStatus(GetOracleMonitorStatusPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/oraclemonitorstatus/paginated-list");

        return await Get<PaginatedResult<OracleMonitorStatusListVm>>(request);
    }

    public async Task<List<OracleMonitorStatusDetailByTypeVm>> GetOracleMonitorStatusDetailByTypeVm(string type)
    {
        var request = new RestRequest($"api/v6/oraclemonitorstatus/type?type={type}");

        return await Get<List<OracleMonitorStatusDetailByTypeVm>>(request);
    }

    //public async Task<List<OracleMonitorStatusByInfraObjectIdVm>> GetOracleMonitorStatusByInfraObjectIdVm(string infraObjectId)
    //{
    //    var request = new RestRequest($"api/v6/oraclemonitorstatus/{infraObjectId}");

    //    return await Get<List<OracleMonitorStatusByInfraObjectIdVm>>(request);
    //}
}