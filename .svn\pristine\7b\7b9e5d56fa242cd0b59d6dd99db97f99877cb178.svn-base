using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ComponentTypeFixture : IDisposable
{
    public List<ComponentType> ComponentTypePaginationList { get; set; }
    public List<ComponentType> ComponentTypeList { get; set; }
    public ComponentType ComponentTypeDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ComponentTypeFixture()
    {
        var fixture = new Fixture();

        ComponentTypeList = fixture.Create<List<ComponentType>>();

        ComponentTypePaginationList = fixture.CreateMany<ComponentType>(20).ToList();

        ComponentTypeDto = fixture.Create<ComponentType>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
