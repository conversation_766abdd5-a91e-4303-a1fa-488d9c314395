﻿namespace ContinuityPatrol.Application.Features.ServerLog.Queries.GetNameUnique;

public class GetServerLogNameUniqueQueryHandler : IRequestHandler<GetServerLogNameUniqueQuery, bool>
{
    private readonly IServerLogRepository _serverLogRepository;
    public GetServerLogNameUniqueQueryHandler(IServerLogRepository serverLogRepository)
    {
        _serverLogRepository = serverLogRepository;
    }
    public async Task<bool> Handle(GetServerLogNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _serverLogRepository.IsServerLogNameUnique(request.Name, request?.Id);
    }
}