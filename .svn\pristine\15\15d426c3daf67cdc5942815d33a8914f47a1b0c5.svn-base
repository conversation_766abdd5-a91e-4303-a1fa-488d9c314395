using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Events.Create;

public class HacmpClusterCreatedEventHandler : INotificationHandler<HacmpClusterCreatedEvent>
{
    private readonly ILogger<HacmpClusterCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public HacmpClusterCreatedEventHandler(ILoggedInUserService userService,
        ILogger<HacmpClusterCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(HacmpClusterCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} HACMPCluster",
            Entity = "HacmpCHACMPClusterluster",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"HacmpCluster '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"HacmpCluster '{createdEvent.Name}' created successfully.");
    }
}