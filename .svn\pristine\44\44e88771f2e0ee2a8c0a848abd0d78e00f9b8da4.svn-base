﻿using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetAMCExpiredList;

public class GetLicenseManagerAMCExpiryListQueryHandler : IRequestHandler<GetLicenseManagerAMCExpiryListQuery, List<LicenseExpireListVm>>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILogger<GetLicenseManagerAMCExpiryListQueryHandler> _logger;
    public GetLicenseManagerAMCExpiryListQueryHandler(ILicenseManagerRepository licenseManagerRepository,ILogger<GetLicenseManagerAMCExpiryListQueryHandler> logger)
    {
        _licenseManagerRepository=licenseManagerRepository;
        _logger = logger;
    }

    public async Task<List<LicenseExpireListVm>> Handle(GetLicenseManagerAMCExpiryListQuery request, CancellationToken cancellationToken)
    {
        var message = new List<string>();

        var licenses = await _licenseManagerRepository.GetLicenseDetailByCompanyId(request.CompanyId);

        var amcExpiryLicense = licenses.Where(x =>x.IsAmc).ToList();

        foreach (var license in amcExpiryLicense)
            if (DateTime.TryParseExact(license.AmcEndDate, "dd MMMM yyyy", CultureInfo.GetCultureInfo("en-IN"),
                    DateTimeStyles.None, out var expiryDate))
            {
                var amcplandtl = await GetAmcPlanDtl(license.AmcPlan);

                var currentDate = DateTime.Now.Date;

                expiryDate=DateTime.Now.Date.AddDays(1);
                if (expiryDate >= currentDate)
                {
                    var remainingDays = (expiryDate - currentDate).TotalDays;
     
                    if (remainingDays >= 0)
                    {
                        if (remainingDays <= 30)
                        {
                            var remainingDay = (int)remainingDays;

                            if (remainingDay == 0)
                            {
                                message.Add($"Your license '{license.PoNumber}' AMC '{amcplandtl}' Year Plan is going to expire 'Today'.");
                                _logger.LogWarning($"Your license '{license.PoNumber}' AMC '{amcplandtl}' Year Plan is going to  expire 'Today'.");
                            }
                            else
                            {
                                message.Add(
                                    $"Your license '{license.PoNumber}'  AMC '{amcplandtl}' Year Plan will expire in '{remainingDay}' days. Please renew your license.");

                                _logger.LogWarning(
                                    $"Your license '{license.PoNumber}'AMC '{amcplandtl}' Year Plan will expire in '{remainingDay}' days. Please renew your license.");
                            }
                        }
                    }
                    else
                    {
                        message.Add($"Your license '{license.PoNumber}' AMC '{amcplandtl}' Year Plan Expired.");

                        _logger.LogWarning($"Your license ' {license.PoNumber}' AMC '{amcplandtl}' Year Plan Expired.");
                    }
                }
                else
                {
                    message.Add($"Your license ' {license.PoNumber} ' AMC '{amcplandtl}' Year Plan Expired.");

                    _logger.LogWarning($"Your license ' {license.PoNumber} ' AMC '{amcplandtl}' Year Plan Expired.");
                }
            }


        return message.Select(x => new LicenseExpireListVm { Message = x }).ToList();
    }
    private static Task<string>GetAmcPlanDtl(string amcPlan)
    {
        var plan= JsonConvert.DeserializeObject<dynamic>(amcPlan);

        var result = plan["AMC"] != null
           ? plan["AMC"].Value.ToString() // Convert to string if not null
           : string.Empty; // Return

        return Task.FromResult(result);
    }
}
