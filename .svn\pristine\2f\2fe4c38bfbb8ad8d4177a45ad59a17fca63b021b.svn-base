﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Events;

public class WorkflowCategoryUpdatedEventHandlerTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowCategoryUpdatedEventHandler _handler;

    public WorkflowCategoryUpdatedEventHandlerTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowCategoryEventLogger = new Mock<ILogger<WorkflowCategoryUpdatedEventHandler>>();

        _mockUserActivityRepository = WorkflowCategoryRepositoryMocks.CreateWorkflowCategoryEventRepository(_workflowCategoryFixture.UserActivities);

        _handler = new WorkflowCategoryUpdatedEventHandler(mockLoggedInUserService.Object, mockWorkflowCategoryEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateWorkflowCategoryEventUpdated()
    {
        _workflowCategoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowCategoryFixture.WorkflowCategoryUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowCategoryFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowCategoryFixture.WorkflowCategoryUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateWorkflowCategoryEventUpdated()
    {
        _workflowCategoryFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowCategoryFixture.WorkflowCategoryUpdatedEvent, CancellationToken.None);

        result.Equals(_workflowCategoryFixture.UserActivities[0].Id);

        result.Equals(_workflowCategoryFixture.WorkflowCategoryUpdatedEvent.Name);

        await Task.CompletedTask;
    }
}