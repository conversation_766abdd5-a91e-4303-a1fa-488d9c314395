﻿namespace ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;

public class
    UpdateServerVersionCommandHandler : IRequestHandler<UpdateServerVersionCommand, UpdateServerVersionResponse>
{
    private readonly IServerViewRepository _serverViewRepository;
    private readonly IServerRepository _serverRepository;
    private readonly IMapper _mapper;

    public UpdateServerVersionCommandHandler(IServerViewRepository serverViewRepository, IServerRepository serverRepository, IMapper mapper)
    {
        _serverViewRepository = serverViewRepository;
        _serverRepository = serverRepository;
        _mapper = mapper;
    }

    public async Task<UpdateServerVersionResponse> Handle(UpdateServerVersionCommand request,
        CancellationToken cancellationToken)
    {
        if (request.IsUpdateAll)
        {
            var eventToUpdateOsType =
                await _serverViewRepository.GetServerByOsTypeIdAndFormVersion(request.OsTypeId, request.OldFormVersion);

            eventToUpdateOsType.ForEach(x => x.FormVersion = request.NewFormVersion);

            var serverDto = _mapper.Map<List<Domain.Entities.Server>>(eventToUpdateOsType);

            await _serverRepository.UpdateRangeAsync(serverDto);

            return new UpdateServerVersionResponse
            {
                Message = "All Server version updated successfully!"
            };
        }

        var eventToUpdateServer = await _serverRepository.GetByReferenceIdAsync(request.Id);

        eventToUpdateServer.FormVersion = request.NewFormVersion;

        await _serverRepository.UpdateAsync(eventToUpdateServer);

        var response = new UpdateServerVersionResponse
        {
            Message = "Server version updated successfully!"
        };

        return response;
    }
}