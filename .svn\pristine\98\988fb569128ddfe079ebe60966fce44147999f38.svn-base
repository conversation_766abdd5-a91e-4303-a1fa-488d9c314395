using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraObjectSchedulerRepositoryTests : IClassFixture<InfraObjectSchedulerFixture>, IDisposable
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraObjectSchedulerRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly InfraObjectSchedulerRepository _repositoryNotParent;

    public InfraObjectSchedulerRepositoryTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraObjectSchedulerRepository(_dbContext, _mockLoggedInUserService.Object);
        _repositoryNotParent = new InfraObjectSchedulerRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraObjectSchedulers.RemoveRange(_dbContext.InfraObjectSchedulers);
        await _dbContext.SaveChangesAsync();
    }

    #region GetInfraObjectSchedulerNames Tests

    [Fact]
    public async Task GetInfraObjectSchedulerNames_ReturnsAllSchedulers_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraObjectSchedulers = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = companyId,
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Infrastructure 1",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = companyId,
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Infrastructure 2",
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjectSchedulers);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.Equal(2, result.Count);

    }

    [Fact]
    public async Task GetInfraObjectSchedulerNames_FiltersCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraObjectSchedulers = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = companyId,
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Company Infrastructure",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_456", // Different company
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Other Company Infrastructure",
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjectSchedulers);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.Single(result);
        Assert.Equal(companyId, result.First().CompanyId);
    }

    #endregion

    #region IsInfraObjectSchedulerNameExist Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, invalidId);

        // Assert
        Assert.True(result); // Should return true when name exists and ID is invalid
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Different Infrastructure Scheduler",
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(nonExistentName, invalidId);

        // Assert
        Assert.False(result); // Should return false when name doesn't exist
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsTrue_WhenNameExistsWithDifferentValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, differentId);

        // Assert
        Assert.True(result); // Should return true when name exists with different ID
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsFalse_WhenNameExistsWithSameValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var existingId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, existingId);

        // Assert
        Assert.False(result); // Should return false when name exists with same ID (editing same record)
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsInfraObjectSchedulerNameExist("Test Infrastructure Scheduler", invalidId);
        var resultDifferentCase = await _repository.IsInfraObjectSchedulerNameExist("test infrastructure scheduler", invalidId);

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    #endregion

    #region GetInfraObjectSchedulerByInfraObjectId Tests

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_ExecutesWithoutError_WhenIsParentTrue_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_UsesGetPaginatedInfraObjectScheduler_WhenIsParentTrue_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var infra = _infraObjectSchedulerFixture.InfraObjectSchedulerDto;
        infra.InfraObjectId = infraObjectId;    
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
        // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_ExecutesWithoutError_WhenIsParentFalse_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_UsesGetPaginatedInfraObjectScheduler_WhenIsParentFalse_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
        // This path USES GetPaginatedInfraObjectScheduler
    }

    #endregion

    #region GetInfraObjectSchedulerByWorkflowId Tests

    [Fact]
    public async Task GetInfraObjectSchedulerByWorkflowId_ReturnsMatchingSchedulers_WhenWorkflowIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var workflowId = "WORKFLOW_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var infraObjectSchedulers = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Infrastructure 1",
                AfterSwitchOverWorkflowId = workflowId, // Matches
                BeforeSwitchOverWorkflowId = "WORKFLOW_456",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Infrastructure 2",
                AfterSwitchOverWorkflowId = "WORKFLOW_789",
                BeforeSwitchOverWorkflowId = workflowId, // Matches
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_003",
                InfraObjectName = "Infrastructure 3",
                AfterSwitchOverWorkflowId = "WORKFLOW_999",
                BeforeSwitchOverWorkflowId = "WORKFLOW_888", // No match
                WorkflowType = "Test Workflow",
                Status = "Active",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjectSchedulers);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectSchedulerByWorkflowId(workflowId);

        // Assert
        Assert.Equal(2, result.Count);

    }

    #endregion

    #region IsInfraObjectSchedulerNameUnique Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_ReturnsTrue_WhenCombinationExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameUnique(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName);

        // Assert
        Assert.True(result); // Should return true when combination exists
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_ReturnsFalse_WhenCombinationDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Different Infrastructure Scheduler",
            AfterSwitchOverWorkflowName = "Different After Workflow",
            BeforeSwitchOverWorkflowName = "Different Before Workflow",
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameUnique(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName);

        // Assert
        Assert.False(result); // Should return false when combination doesn't exist
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsInfraObjectSchedulerNameUnique("Test Infrastructure Scheduler", "After Workflow", "Before Workflow");
        var resultDifferentCase = await _repository.IsInfraObjectSchedulerNameUnique("test infrastructure scheduler", "after workflow", "before workflow");

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    #endregion

    #region IsInfraObjectSchedulerNameExist (with workflows) Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_WithWorkflows_ReturnsTrue_WhenCombinationExistsWithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName, differentId);

        // Assert
        Assert.True(result); // Should return true when combination exists with different ID
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_WithWorkflows_ReturnsFalse_WhenCombinationExistsWithSameId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";
        var existingId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName, existingId);

        // Assert
        Assert.False(result); // Should return false when combination exists with same ID (editing same record)
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_WithWorkflows_ReturnsFalse_WhenCombinationDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";
        var testId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Different Infrastructure Scheduler",
            AfterSwitchOverWorkflowName = "Different After Workflow",
            BeforeSwitchOverWorkflowName = "Different Before Workflow",
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName, testId);

        // Assert
        Assert.False(result); // Should return false when combination doesn't exist
    }

    #endregion

    #region Overridden Repository Methods Tests

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
    }

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentReferenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result); // Should return null when reference ID doesn't exist
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ExecutesWithoutError_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.Null(result); // Should return null when no data exists
    }

    [Fact]
    public async Task PaginatedListAllAsync_ExecutesWithoutError_WhenIsParentTrue_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no data exists
        Assert.Empty(result.Data);
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_UsesGetPaginatedInfraObjectScheduler_WhenIsParentTrue_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no assigned infras
        Assert.Empty(result.Data);
        // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_ExecutesWithoutError_WhenIsParentFalse_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no data exists
        Assert.Empty(result.Data);
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_UsesGetPaginatedInfraObjectScheduler_WhenIsParentFalse_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no assigned infras
        Assert.Empty(result.Data);
        // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetPaginatedQuery_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetPaginatedQuery_UsesGetPaginatedInfraObjectScheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
        // This path USES GetPaginatedInfraObjectScheduler
    }

    #endregion

    #region GetInfraObjectSchedulerNames Tests - GetAssignedInfraObjectSheduler Coverage

    [Fact]
    public async Task GetInfraObjectSchedulerNames_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
        // This path does NOT use GetAssignedInfraObjectSheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerNames_UsesGetAssignedInfraObjectSheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
        // This path USES GetAssignedInfraObjectSheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerNames_HandlesNullAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{\"AssignedBusinessServices\":null}");

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should handle null assigned business services gracefully
    }

    #endregion

    #region ListAllAsync Tests - GetAssignedInfraObjectSheduler Coverage

    [Fact]
    public async Task ListAllAsync_UsesGetAssignedInfraObjectSheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
        // This path USES GetAssignedInfraObjectSheduler
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddInfraObjectScheduler_WhenValidInfraObjectScheduler()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            WorkflowTypeId = "WT_123",
            WorkflowType = "Disaster Recovery",
            BeforeSwitchOverWorkflowId = "WORKFLOW_123",
            BeforeSwitchOverWorkflowName = "Before Switchover Workflow",
            AfterSwitchOverWorkflowId = "WORKFLOW_456",
            AfterSwitchOverWorkflowName = "After Switchover Workflow",
            ScheduleType = 1,
            Status = "Active",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraObjectScheduler);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectScheduler.CompanyId, result.CompanyId);
        Assert.Equal(infraObjectScheduler.InfraObjectId, result.InfraObjectId);
        Assert.Equal(infraObjectScheduler.InfraObjectName, result.InfraObjectName);
        Assert.Equal(infraObjectScheduler.WorkflowType, result.WorkflowType);
        Assert.Single(_dbContext.InfraObjectSchedulers);
    }

    #endregion
}
