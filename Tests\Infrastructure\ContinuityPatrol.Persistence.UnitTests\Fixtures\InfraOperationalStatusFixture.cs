using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraOperationalStatusFixture : IDisposable
{
    public ApplicationDbContext DbContext { get; private set; }

    public InfraOperationalStatusFixture()
    {
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
