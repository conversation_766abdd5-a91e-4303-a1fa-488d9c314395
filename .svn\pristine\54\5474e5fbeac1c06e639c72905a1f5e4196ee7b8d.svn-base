﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowCategory.Commands;

public class DeleteWorkflowCategoryTests : IClassFixture<WorkflowCategoryFixture>
{
    private readonly WorkflowCategoryFixture _workflowCategoryFixture;

    private readonly Mock<IWorkflowCategoryRepository> _mockWorkflowCategoryRepository;

    private readonly DeleteWorkflowCategoryCommandHandler _handler;

    public DeleteWorkflowCategoryTests(WorkflowCategoryFixture workflowCategoryFixture)
    {
        _workflowCategoryFixture = workflowCategoryFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockWorkflowCategoryRepository = WorkflowCategoryRepositoryMocks.DeleteWorkflowCategoryRepository(_workflowCategoryFixture.WorkflowCategories);

        _handler = new DeleteWorkflowCategoryCommandHandler(_mockWorkflowCategoryRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_WorkflowCategoryDeleted()
    {
        var validGuid = Guid.NewGuid();

        _workflowCategoryFixture.WorkflowCategories[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteWorkflowCategoryCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();

        _mockWorkflowCategoryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowCategory>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_DeleteWorkflowCategoryResponse_When_WorkflowCategoryDeleted()
    {
        var validGuid = Guid.NewGuid();

        _workflowCategoryFixture.WorkflowCategories[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteWorkflowCategoryCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteWorkflowCategoryResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_WhenDeleteWorkflowCategory()
    {
        var validGuid = Guid.NewGuid();

        _workflowCategoryFixture.WorkflowCategories[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteWorkflowCategoryCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var workflowCategory = await _mockWorkflowCategoryRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        workflowCategory.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidWorkflowCategoryId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteWorkflowCategoryCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _workflowCategoryFixture.WorkflowCategories[0].ReferenceId = validGuid.ToString();

        var workflowCategory = _workflowCategoryFixture.WorkflowCategories[0];

        var ValidGuid = _workflowCategoryFixture.WorkflowCategories[0].ReferenceId.ToString();

        _workflowCategoryFixture.WorkflowCategories[0].ReferenceId = Guid.NewGuid().ToString();

        _mockWorkflowCategoryRepository.Setup(x => x.GetByReferenceIdAsync(ValidGuid)).ReturnsAsync(workflowCategory);

        var result = await _handler.Handle(new DeleteWorkflowCategoryCommand { Id = validGuid.ToString() }, CancellationToken.None);

        _mockWorkflowCategoryRepository.Verify(x => x.GetByReferenceIdAsync(ValidGuid), Times.Once);

        _mockWorkflowCategoryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.WorkflowCategory>()), Times.Once);
    }
}