﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Events
{
    public class CreateRoboCopyJobEventTests
    {
        private readonly Mock<ILogger<RoboCopyJobCreatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly RoboCopyJobCreatedEventHandler _handler;

        public CreateRoboCopyJobEventTests()
        {
            _mockLogger = new Mock<ILogger<RoboCopyJobCreatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new RoboCopyJobCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ValidEvent_AddsUserActivityAndLogsInformation()
        {
            var createdEvent = new RoboCopyJobCreatedEvent
            {
                ReplicationName = "TestReplication"
            };

            _mockUserService.Setup(s => s.UserId).Returns("TestUserId");
            _mockUserService.Setup(s => s.LoginName).Returns("TestLoginName");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("TestCompanyId");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo =>
                repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                    activity.UserId == "TestUserId" &&
                    activity.LoginName == "TestLoginName" &&
                    activity.RequestUrl == "http://testurl.com" &&
                    activity.CompanyId == "TestCompanyId" &&
                    activity.HostAddress == "127.0.0.1" &&
                    activity.Action == "Create RoboCopyJob" &&
                    activity.Entity == "RoboCopyJob" &&
                    activity.ActivityType == "Create" &&
                    activity.ActivityDetails == "RoboCopy Job 'TestReplication' created successfully." &&
                    activity.CreatedBy == "TestUserId" &&
                    activity.LastModifiedBy == "TestUserId"
                )),
                Times.Once);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(msg => msg == "RoboCopy Job 'TestReplication' created successfully.")),
                Times.Once);
        }

        [Fact]
        public async Task Handle_NullOrEmptyUserId_GeneratesGuidForCreatedByAndLastModifiedBy()
        {
            var createdEvent = new RoboCopyJobCreatedEvent
            {
                ReplicationName = "TestReplication"
            };

            _mockUserService.Setup(s => s.UserId).Returns(string.Empty);
            _mockUserService.Setup(s => s.LoginName).Returns("TestLoginName");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl.com");
            _mockUserService.Setup(s => s.CompanyId).Returns("TestCompanyId");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo =>
                repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                    !string.IsNullOrEmpty(activity.CreatedBy) &&
                    !string.IsNullOrEmpty(activity.LastModifiedBy) &&
                    IsValidGuid(activity.CreatedBy) &&
                    IsValidGuid(activity.LastModifiedBy)
                )),
                Times.Once);
        }

        private bool IsValidGuid(string value)
        {
            return Guid.TryParse(value, out _);
        }

        [Fact]
        public async Task Handle_LogsCorrectInformation()
        {
            var createdEvent = new RoboCopyJobCreatedEvent
            {
                ReplicationName = "ReplicationTest"
            };

            _mockUserService.Setup(s => s.UserId).Returns("TestUserId");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(createdEvent, CancellationToken.None);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(msg => msg == "RoboCopy Job 'ReplicationTest' created successfully.")),
                Times.Once);
        }

        [Fact]
        public async Task Handle_RepositoryThrowsException_PropagatesException()
        {
            var createdEvent = new RoboCopyJobCreatedEvent
            {
                ReplicationName = "TestReplication"
            };

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new Exception("Database error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(createdEvent, CancellationToken.None));
            Assert.Equal("Database error", exception.Message);

            _mockLogger.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Never);
        }
    }
}
