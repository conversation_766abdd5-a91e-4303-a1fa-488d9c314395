using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Queries;

public class GetBulkImportOperationGroupListQueryTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportOperationGroupListQueryHandler _handler;

    public GetBulkImportOperationGroupListQueryTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateQueryBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()))
            .Returns((List<Domain.Entities.BulkImportOperationGroup> entities) => entities.Select(entity => new BulkImportOperationGroupListVm
            {
                Id = entity.ReferenceId,
                BulkImportOperationId = entity.BulkImportOperationId,
                CompanyId = entity.CompanyId,
                Properties = entity.Properties,
                ProgressStatus = entity.ProgressStatus,
                Status = entity.Status,
                ErrorMessage = entity.ErrorMessage,
                ConditionalOperation = entity.ConditionalOperation,
                NodeId = entity.NodeId,
                InfraObjectName = entity.InfraObjectName
            }).ToList());

        _handler = new GetBulkImportOperationGroupListQueryHandler(
            _mockMapper.Object,
            _mockBulkImportOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationGroupListVm_When_BulkImportOperationGroupsExist()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().Id.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_Call_ListAllAsync_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoBulkImportOperationGroupsExist()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();
        _mockBulkImportOperationGroupRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        _mockMapper.Setup(m => m.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()))
            .Returns(new List<BulkImportOperationGroupListVm>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        testGroup.BulkImportOperationId = "TestOperationId";
        testGroup.CompanyId = "TestCompanyId";
        testGroup.Properties = "{\"test\":\"value\"}";
        testGroup.Status = "Pending";
        testGroup.ProgressStatus = "0/5";
        testGroup.ErrorMessage = "";
        testGroup.ConditionalOperation = 1;
        testGroup.NodeId = "Node001";
        testGroup.InfraObjectName = "TestInfraObject";

        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testGroup.ReferenceId);
        firstItem.BulkImportOperationId.ShouldBe("TestOperationId");
        firstItem.CompanyId.ShouldBe("TestCompanyId");
        firstItem.Properties.ShouldBe("{\"test\":\"value\"}");
        firstItem.Status.ShouldBe("Pending");
        firstItem.ProgressStatus.ShouldBe("0/5");
        firstItem.ErrorMessage.ShouldBe("");
        firstItem.ConditionalOperation.ShouldBe(1);
        firstItem.NodeId.ShouldBe("Node001");
        firstItem.InfraObjectName.ShouldBe("TestInfraObject");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportOperationGroupListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportOperationGroupListVm>));
    }

    [Fact]
    public async Task Handle_ReturnAllActiveItems_When_RepositoryHasData()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();
        var expectedCount = _bulkImportOperationGroupFixture.BulkImportOperationGroups.Count;

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(expectedCount);
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();
        _mockBulkImportOperationGroupRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_MapProgressProperties_WithCorrectValues()
    {
        // Arrange
        var testGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        testGroup.Status = "In Progress";
        testGroup.ProgressStatus = "3/10";
        testGroup.ErrorMessage = "Some error occurred";

        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.Status.ShouldBe("In Progress");
        firstItem.ProgressStatus.ShouldBe("3/10");
        firstItem.ErrorMessage.ShouldBe("Some error occurred");
    }

    [Fact]
    public async Task Handle_MapConditionalOperationAndNode_WithCorrectValues()
    {
        // Arrange
        var testGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        testGroup.ConditionalOperation = 2;
        testGroup.NodeId = "Node123";
        testGroup.InfraObjectName = "ProductionInfra";

        var query = new GetBulkImportOperationGroupListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        var firstItem = result.First();
        firstItem.ConditionalOperation.ShouldBe(2);
        firstItem.NodeId.ShouldBe("Node123");
        firstItem.InfraObjectName.ShouldBe("ProductionInfra");
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var query = new GetBulkImportOperationGroupListQuery();
        _mockBulkImportOperationGroupRepository.Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }
}
