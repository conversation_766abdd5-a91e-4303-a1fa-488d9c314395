﻿namespace ContinuityPatrol.Shared.Core.Exceptions;

public class WindowServiceException : ApplicationException
{
    public WindowServiceException(List<string> nodes,string type,string message) 
        : base(nodes != null && nodes.Count > 0
                ? string.Join(Environment.NewLine, nodes.ConvertAll(node =>
                    $"{type} service '{node}' is not started" +
                    (!string.IsNullOrEmpty(message) ? $". Error: {message}" : "")))
                : $"{type} service is not started")
    {
        
    }
}
