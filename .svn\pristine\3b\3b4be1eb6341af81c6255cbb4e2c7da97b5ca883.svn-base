﻿
<div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered">

    <div class="modal-content">
        <div class="modal-header">
            <h6 class="modal-title"><i class="cp-upload"></i> Upload Workflow Template</h6>
            <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close" onclick="clearFields()"></button>
        </div>
        <div class="modal-body">
            <form id="example-form">
                <div class="form-group">
                    <label class="form-label">Workflow Template Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" class="form-control" placeholder="Enter Workflow Template Name" id="templateName" maxlength="100" minlength="3" />
                        <span class="input-group-text ps-1" data-bs-toggle="collapse" href="#collapseIcon" aria-expanded="false" aria-controls="collapseIcon"><i role="button" class="imageSubSelected cp-images" title="Choose Icon" id="imageSubSelected"></i></span>
                    </div>
                    <span id="templateNameError"></span>
                </div>
                <div class="collapse mb-3" id="collapseIcon">
                    <div class="form-label">Category Icon</div>
                    <div class="Category_Icon">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                    <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                    <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                    <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                    <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                    <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                    <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="MailingSystem" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Golden Gate" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Infoblox" class="cp-infoblox custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                    <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                    <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                    <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                    <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                    <td><i title="HDS" class="cp-hds custom-cursor-on-hover"></i></td>
                                    <td><i title="NetApp" class="cp-netapp" cursorshover="true"></i></td>
                                    <td><i title="System Management Tool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                    <td><i title="WMI" class="cp-wmi custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Oracle Ops Center" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                    <td><i title="Sun ILOM" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                    <td><i title="Veritas Cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                    <td><i title="Virtualization" class="cp-virtualization_new custom-cursor-on-hover"></i></td>
                                    <td><i title="AIX" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="VMware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Web" class="cp-web custom-cursor-on-hover"></i></td>
                                    <td><i title="Power-CLI" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                    <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="String Utility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                    <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                    <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                    <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                    <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                    <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                    <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                    <td><i title="Error-Handling " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                    <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                    <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                    <td><i title="Circle-Switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="Data-Source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                    <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                    <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                    <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                    <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                    <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                    <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                    <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                    <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                    <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                    <td><i title="PR Site" class="cp-prsite custom-cursor-on-hover"></i></td>
                                    <td><i title="Firewall" class="cp-firewall custom-cursor-on-hover"></i></td>
                                    <td><i title="Mainframe" class="cp-server-cloud custom-cursor-on-hover"></i></td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-label">Import Workflow Template</div>
                    <div class="input-group">
                        <input type="file" class="form-control" name="ImportTemplate" id="ImportTemplate" accept="application/JSON" placeholder="Import Workflow Template" />
                    </div>
                    <span id="ImportTemplateError"></span>
                </div>

            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="ImportCancelTemplate">Cancel</button>
            <button type="button" class="btn btn-primary" id="loadImportTemplate">Import</button>
        </div>
    </div>
</div>
