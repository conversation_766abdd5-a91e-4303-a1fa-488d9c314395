﻿.input-group, .input-group:focus {
    color: var(--bs-body-color);
    background-color: transparent
}

    .form-control:focus, .input-group, .input-group:focus {
        background-color: transparent
    }

.icon-disabled .cp-Delete, .icon-disabled .cp-edit {
    pointer-events: none;
    color: #8d8d8d !important
}

.form-group {
    position: relative;
    margin-bottom: 1rem
}

input:-internal-autofill-selected {
    background-color: -internal-light-dark(#fff,rgba(255,255,255,.4)) !important
}

.input-group textarea {
    resize: none
}

textarea.form-control {
    height:34px;
}

.input-group {
    border-bottom: 1px solid var(--bs-gray-300);
    border-radius: 0;
    align-items: center;
    flex-wrap: inherit;
    height: 35px
}

    .input-group:focus {
        border-bottom: 1px solid var(--bs-primary)
    }

    .input-group:focus-within {
        color: var(--bs-body-color);
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary)
    }

.input-group-text:focus-within {
    color: var(--bs-body-color);
    background-color: transparent;
    border-bottom: 1px solid var(--bs-primary);
    border: none
}

.form-control {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: #fff;
    border: none
}

    .form-control:focus {
        border: none;
        outline: 0;
        box-shadow: none
    }

    .form-control::-moz-placeholder {
        color: var(--bs-placeholder-font-color) !important;
        font-size: var(--bs-body-font-size-small);
        opacity: 1
    }

    .form-control::placeholder {
        color: var(--bs-placeholder-font-color) !important;
        font-size: var(--bs-body-font-size-small);
        opacity: 1
    }

.form-select, .form-select-modal, .select2-container--default .select2-search--dropdown .select2-search__field {
    font-size: var(--bs-body-font-size);
    color: var(--bs-gray-700);
    background-color: var(--bs-gray-100);
    border: none
}

    .form-select::placeholder {
        color: var(--bs-placeholder-font-color) !important;
        opacity: 1;
        font-size: var(--bs-body-font-size-small);
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small)
    }

    .form-select-modal:focus, .form-select:focus {
        background-color: var(--bs-gray-200);
        border: none;
        outline: 0;
        box-shadow: none
    }

    .form-select::-moz-placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small)
    }

    .form-select-modal::placeholder {
        color: var(--bs-gray-500) !important;
        font-size: var(--bs-body-font-size-small);
        opacity: 1;
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small)
    }

    .form-select-modal::-moz-placeholder {
        color: var(--bs-gray-700);
        font-size: var(--bs-body-font-size-small)
    }

.input-group-text {
    font-size: var(--bs-input-icon-font-size);
    color: var(--bs-body-color);
    background-color: transparent;
    border: none;
    padding: 4px 2px 4px 0
}

.form-check-label, .form-label {
    color: var(--bs-body-color);
    font-weight: var(--bs-form-font-weight);
    font-size: var(--bs-body-font-size-small)
}

.form-label {
    margin-bottom: 0
}

.form-check-label {
    text-decoration: none
}

.field-validation-error, .lunsTableNote-field-validation-error {
    font-size: 11px;
    position: absolute;
    color: var(--bs-form-invalid-color)
}

:focus-visible {
    outline: -webkit-focus-ring-color auto 0
}

.form-check-input {
    border-color: var(--bs-gray-400);
    margin-right: 5px
}

@-moz-document url-prefix() {
    input[type=password]::placeholder {
        font-size: var(--bs-body-font-size);
        color: var(--bs-gray-700)
    }
}


.dropdown-item:active, .filter-dropdown .dropdown-item.active {
    background-color: transparent
}

.bootstrap-select .dropdown-toggle:focus, .bootstrap-select > select.mobile-device:focus + .dropdown-toggle {
    outline: -webkit-focus-ring-color auto 0 !important;
    outline-offset: 0px
}

.dataTables_filter {
    display: none
}

.field-validation-error {
    width: 100%;
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    margin-top: -1px
}

.lunsTableNote-field-validation-error {
    text-align: start
}

.table-field-validation-error, .table-field-zfsvalidation-error, .table-fieldRSync-error {
    font-size: 11px;
    position: absolute;
    color: var(--bs-form-invalid-color);
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    margin-top: -1px
}

.table-fieldRSync-error {
    width: 25%
}

.table-field-validation-error {
    width: 22%
}

.table-field-deploymentsNamevalidation-error {
    width: 39%
}

.table-field-deploymentsReplicaNamevalidation-error {
    width: 43.5%
}

.table-select-field-validation-error {
    width: 18%;
    font-size: 11px;
    position: absolute;
    color: var(--bs-form-invalid-color);
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    margin-top: -1px
}

.field-validation-error-selecttag, .field-validation-error-selecttag2, .field-validation-versionError {
    width: 100%;
    font-size: 11px;
    color: var(--bs-form-invalid-color);
    text-align: end;
    border-top: 1px solid var(--bs-form-invalid-color);
    position: absolute
}

.field-validation-versionError {
    top: 33px
}

.field-validation-error-selecttag {
    margin-top: 1.75rem
}

.field-validation-error-selecttag2 {
    top: 40px;
    z-index: 1
}

.input-group .select2-container {
    width: 100% !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container--default .select2-selection--single {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0;
    top: 5px
}

.select2-dropdown {
    background-color: #fff;
    border: 0 solid #aaa;
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important
}

.selectize-input, .selectize-input.focus {
    box-shadow: none !important
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: 0 solid #000 !important;
    border-bottom: 0 solid var(--bs-primary) !important
}

.select2-container--default .select2-selection--multiple {
    background-color: transparent;
    border: 0 solid #aaa
}

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        border: 0 solid #aaa;
        border-radius: 0;
        margin-left: 4px;
        margin-top: 0;
        padding-left: 14px
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        border-right: 0px solid #aaa
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__display {
        cursor: default;
        padding-left: 2px;
        padding-right: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100px;
        display: block
    }

.select2-selection--multiple ::placeholder {
    color: var(--bs-placeholder-font-color) !important;
    font-size: var(--bs-body-font-size-small);
    opacity: 1
}

input::-webkit-calendar-picker-indicator {
    cursor: pointer
}

.select2-container .select2-search--inline .select2-search__field {
    font-family: var(--bs-font-family-Light);
    padding: 2px 0
}

.select2-container--default .select2-selection--multiple .select2-selection__arrow b {
    right: 1% !important
}

.selectize-input.full {
    background-color: transparent !important
}

.selectize-input {
    border: 0 solid #d0d0d0 !important;
    border-radius: 0 !important
}

.selectize-control.single .selectize-input.input-active, .selectize-input {
    background: 0 0 !important
}

    .selectize-input.dropdown-active {
        border-radius: 0 !important
    }

    .selectize-dropdown, .selectize-input, .selectize-input input {
        font-size: var(--bs-body-font-size) !important;
        color: var(--bs-gray-700) !important;
        font-family: var(--bs-body-font-family) !important
    }

.selectize-dropdown {
    border: 0 solid #d0d0d0 !important;
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important
}

    .selectize-dropdown .selected {
        background-color: #ddd !important;
        border: 0 dashed var(--bs-primary) !important;
        box-shadow: none !important
    }

    .selectize-dropdown .active, .selectize-dropdown .active:not(.selected) {
        background: #5897fb !important;
        color: #fff !important
    }

select[multiple].input-group:focus-within {
    height: auto !important;
}

.table-select2 select {
    width: 60px;
    height: 32px;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0 !important
}

.dashboard-nav li.nav-item, .page_count {
    margin: 0 5px
}

.wf_profile .select2 {
    width: 250px !important
}

input[type=file i] {
    border-bottom: 0 solid var(--bs-gray-300);
    background-color: #fff
}

.file-input__input {
    width: .1px;
    height: .1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1
}

.file-input__label {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    border-radius: 20px;
    color: #fff;
    font-size: 13px;
    padding: 10px 12px;
    background-color: #707e87
}

:checked + .site_type {
    border-color: #f0e9eb;
    border-top-right-radius: 0;
    transition: .2s
}

    :checked + .site_type:before {
        content: "✓";
        transform: scale(1);
        font-weight: 800
    }

.site_type:before {
    background-color: #0492ff !important;
    color: #fff;
    content: "\f2bc";
    display: block;
    border-radius: 25% 0 25% 25%;
    border: 0 solid grey;
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    text-align: center;
    transition-duration: .4s;
    transform: scale(0)
}

.site_type {
    padding: 18px;
    display: block;
    position: relative;
    cursor: pointer;
    border: 1px solid #f0e9eb;
    border-radius: 15%;
    transition: .2s
}

.btn-disabled, .icon-disabled, input[readonly=readonly] {
    cursor: not-allowed;
    opacity: var(--bs-btn-disabled-opacity)
}

.btn-disabled {
    background-color: #ced4da !important;
    border: 1px solid #ced4da !important;
    color: var(--bs-white)
}

.icon-disabled, input[readonly=readonly] {
    color: #8d8d8d !important
}

.dropdown button {
    border-color: transparent !important
}

.formeo-render label span {
    font-family: var(--bs-body-font-family) !important;
    font-size: var(--bs-body-font-size-small) !important;
    background-color: transparent !important
}

.formeo-column .select2-container--default .select2-selection--single {
    border-bottom: 1px solid var(--bs-gray-300)
}

input[type=search] {
    padding-right: 0
}

    input[type=search]::-webkit-search-cancel-button {
        margin-left: 8px
    }

.font-monospace {
    color: var(--bs-gray-600);
    font-size: 10px
}

input[type=checkbox] {
    accent-color: #0d6efd
}

.modal-footer small {
    display: flex;
    align-items: center
}

.btn-size {
    min-width: 80px
}

.formeo.formeo-render .formeo-row {
    gap: 20px !important
}

