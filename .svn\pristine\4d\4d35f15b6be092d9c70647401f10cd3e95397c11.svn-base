﻿using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class UserInfraObjectsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GetUserInfraObjectByBusinessServiceVm>>> GetUserInfraObjectByBusinessServiceId(string? companyId)
    {
        Logger.LogDebug($"Get UserInfraObject by Business Services ");

        return Ok(await Mediator.Send(new GetUserInfraObjectByBusinessServiceQuery { CompanyId = companyId}));
    }

    [HttpGet("{userId}", Name = "GetUserInfraObject")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetUserInfraObjectByUserIdVm>> GetUserById(string userId)
    {
        Guard.Against.InvalidGuidOrEmpty(userId, "UserInfraObject UserId");

        Logger.LogDebug($"Get UserInfraObject Detail by userId '{userId}'");

        return Ok(await Mediator.Send(new GetUserInfraObjectByUserIdQuery { UserId = userId }));
    }

    [HttpGet("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<GetByUserIdAndPropertiesVm>> GetByUserIdAndProperties()
    {
        Logger.LogDebug("Get All UserInfraObject Names");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllUserInfraObjectsNameCacheKey, () => Mediator.Send(new GetByUserIdAndPropertiesQuery()), CacheExpiry));

        return Ok(await Mediator.Send(new GetByUserIdAndPropertiesQuery()));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllUserInfraObjectsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllUserInfraObjectsNameCacheKey };

        ClearCache(cacheKeys);
    }
}
