﻿using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Queries
{
    public class GetRoboCopyDetailsQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
        private readonly GetRoboCopyDetailsQueryHandler _handler;

        public GetRoboCopyDetailsQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();

            _handler = new GetRoboCopyDetailsQueryHandler(_mockMapper.Object, _mockRoboCopyRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnRoboCopyDetailVm_WhenRoboCopyExists()
        {
            var requestId = Guid.NewGuid().ToString();
            var roboCopy = new Domain.Entities.RoboCopy
            {
                Id = 1,
                Name = "Test RoboCopy",
                IsActive = true
            };
            var expectedVm = new RoboCopyDetailVm
            {
                Id = 2,
                Name = "Test RoboCopy"
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(requestId))
                .ReturnsAsync(roboCopy);

            _mockMapper
                .Setup(mapper => mapper.Map<RoboCopyDetailVm>(roboCopy))
                .Returns(expectedVm);

            var query = new GetRoboCopyDetailQuery { Id = requestId };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedVm.Id, result.Id);
            Assert.Equal(expectedVm.Name, result.Name);

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(requestId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RoboCopyDetailVm>(roboCopy), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRoboCopyDoesNotExist()
        {
            var requestId = Guid.NewGuid().ToString();
            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(requestId))
                .ReturnsAsync((Domain.Entities.RoboCopy)null);

            var query = new GetRoboCopyDetailQuery { Id = requestId };

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));

            Assert.Equal($"Entity \"RoboCopy\" ({requestId}) was not found.", exception.Message);

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(requestId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RoboCopyDetailVm>(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRoboCopyIsInactive()
        {
            var requestId = Guid.NewGuid().ToString();
            var inactiveRoboCopy = new Domain.Entities.RoboCopy
            {
                Id = 1,
                Name = "Inactive RoboCopy",
                IsActive = false
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(requestId))
                .ReturnsAsync(inactiveRoboCopy);

            var query = new GetRoboCopyDetailQuery { Id = requestId };

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));

            Assert.Equal($"Entity \"RoboCopy\" ({requestId}) was not found.", exception.Message);

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(requestId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RoboCopyDetailVm>(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentNullException_WhenRequestIsNull()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _handler.Handle(null, CancellationToken.None));
        }
    }
}
