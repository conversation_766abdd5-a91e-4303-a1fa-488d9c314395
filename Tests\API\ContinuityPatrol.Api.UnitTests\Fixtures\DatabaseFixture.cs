using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.Delete;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByLicenseKey;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByUserName;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetList;
using ContinuityPatrol.Application.Features.Database.Queries.GetNames;
using ContinuityPatrol.Application.Features.Database.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DatabaseFixture
{
    public CreateDatabaseCommand CreateDatabaseCommand { get; }
    public CreateDatabaseResponse CreateDatabaseResponse { get; }
    public UpdateDatabaseCommand UpdateDatabaseCommand { get; }
    public UpdateDatabaseResponse UpdateDatabaseResponse { get; }
    public DeleteDatabaseCommand DeleteDatabaseCommand { get; }
    public DeleteDatabaseResponse DeleteDatabaseResponse { get; }
    public SaveAsDatabaseCommand SaveAsDatabaseCommand { get; }
    public SaveAsDatabaseResponse SaveAsDatabaseResponse { get; }
    public DatabaseTestConnectionCommand DatabaseTestConnectionCommand { get; }
    public DatabaseTestConnectionResponse DatabaseTestConnectionResponse { get; }
    public UpdateDatabasePasswordCommand UpdateDatabasePasswordCommand { get; }
    public UpdateDatabasePasswordResponse UpdateDatabasePasswordResponse { get; }
    public UpdateDatabaseVersionCommand UpdateDatabaseVersionCommand { get; }
    public UpdateDatabaseVersionResponse UpdateDatabaseVersionResponse { get; }
    public DatabaseDetailVm DatabaseDetailVm { get; }
    public DatabaseListVm DatabaseListVm { get; }
    public DatabaseTypeVm DatabaseTypeVm { get; }
    public GetDatabaseByTypeVm GetDatabaseByTypeVm { get; }
    public DatabaseNameVm DatabaseNameVm { get; }
    public GetDatabaseByServerIdVm GetDatabaseByServerIdVm { get; }
    public GetDatabaseByLicenseKeyListVm GetDatabaseByLicenseKeyListVm { get; }
    public GetDatabaseByUserNameVm GetDatabaseByUserNameVm { get; }
    public GetDatabaseListQuery GetDatabaseListQuery { get; }
    public GetDatabaseDetailQuery GetDatabaseDetailQuery { get; }
    public GetDatabaseNameQuery GetDatabaseNameQuery { get; }
    public GetDatabaseNameUniqueQuery GetDatabaseNameUniqueQuery { get; }
    public GetDatabasePaginatedListQuery GetDatabasePaginatedListQuery { get; }
    public GetDatabaseTypeQuery GetDatabaseTypeQuery { get; }
    public GetDatabaseByServerIdQuery GetDatabaseByServerIdQuery { get; }
    public GetDatabaseByLicenseKeyQuery GetDatabaseByLicenseKeyQuery { get; }
    public GetDatabaseByUserNameQuery GetDatabaseByUserNameQuery { get; }

    public DatabaseFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDatabaseCommand>(c => c
            .With(b => b.Name, "Enterprise Production Database")
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server")
            .With(b => b.Type, "Production")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-01")
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Properties, "{\"connectionString\":\"Server=PROD-SQL-01;Database=EnterpriseDB;Integrated Security=true;\"}")
            .With(b => b.Logo, "sql-server-logo.png")
            .With(b => b.ModeType, "Production")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.LicenseKey, "ENTERPRISE-LICENSE-KEY-2024")
            .With(b => b.Version, "2022")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service")
            .With(b => b.ExceptionMessage, "")
            .With(b => b.FormVersion, "1.0"));

        fixture.Customize<CreateDatabaseResponse>(c => c
            .With(b => b.DatabaseId, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Production Database created successfully!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDatabaseCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Updated Database")
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server")
            .With(b => b.Type, "Production")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-02")
            .With(b => b.Properties, "{\"connectionString\":\"Server=PROD-SQL-02;Database=EnterpriseDB;Integrated Security=true;\"}")
            .With(b => b.Logo, "sql-server-logo.png")
            .With(b => b.ModeType, "Production")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.LicenseKey, "UPDATED-LICENSE-KEY-2024")
            .With(b => b.Version, "2022")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Updated Enterprise Business Service")
            .With(b => b.ExceptionMessage, "")
            .With(b => b.FormVersion, "1.1"));

        fixture.Customize<UpdateDatabaseResponse>(c => c
            .With(b => b.DatabaseId, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Database updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DeleteDatabaseCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<DeleteDatabaseResponse>(c => c
            .With(b => b.IsActive, false)
            .With(b => b.Message, "Enterprise Database deleted successfully!")
            .With(b => b.Success, true));

        fixture.Customize<SaveAsDatabaseCommand>(c => c
            .With(b => b.Name, "Enterprise Database Copy")
            .With(b => b.DatabaseId, Guid.NewGuid().ToString())
         );

        fixture.Customize<SaveAsDatabaseResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Database saved as copy successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DatabaseTestConnectionCommand>(c => c
            .With(b => b.Id, new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() }));

        fixture.Customize<DatabaseTestConnectionResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Database connection test successful!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDatabasePasswordCommand>(c => c
            .With(b => b.Password, "NewSecurePassword123!")
            .With(b => b.PasswordList, new List<UpdateDatabasePasswordList>
            {
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 1",
                    UserName = "enterprise_admin"
                },
                new UpdateDatabasePasswordList
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Database 2",
                    UserName = "enterprise_admin"
                }
            }));

        fixture.Customize<UpdateDatabasePasswordResponse>(c => c
            .With(b => b.Message, "Database passwords updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDatabaseVersionCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.OldFormVersion, "1.0")
            .With(b => b.NewFormVersion, "2.0")
            .With(b => b.IsUpdateAll, false));

        fixture.Customize<UpdateDatabaseVersionResponse>(c => c
            .With(b => b.Message, "Database versions updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DatabaseDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Production Database Detail")
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server")
            .With(b => b.Type, "Production")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-01")
            .With(b => b.Properties, "{\"connectionString\":\"Server=PROD-SQL-01;Database=EnterpriseDB;Integrated Security=true;\"}")
            .With(b => b.ModeType, "Production")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.LicenseKey, "ENTERPRISE-LICENSE-KEY-2024")
            .With(b => b.Version, "2022")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service")
            .With(b => b.ExceptionMessage, "")
            .With(b => b.FormVersion, "1.0"));

        fixture.Customize<DatabaseListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.CompanyId, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Database List Item")
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server")
            .With(b => b.Type, "Production")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-01")
            .With(b => b.ModeType, "Production")
            .With(b => b.Properties, "{\"connectionString\":\"Server=PROD-SQL-01;Database=EnterpriseDB;Integrated Security=true;\"}")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.LicenseKey, "ENTERPRISE-LICENSE-KEY-2024")
            .With(b => b.Version, "2022")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service")
            .With(b => b.ExceptionMessage, "")
            .With(b => b.FormVersion, "1.0"));

        fixture.Customize<DatabaseTypeVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "SQL Server Enterprise")
            .With(b => b.Type, "Production"));

        fixture.Customize<GetDatabaseByTypeVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Database by Type")
            .With(b => b.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server")
            .With(b => b.Type, "Production")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-01")
            .With(b => b.ModeType, "Production")
            .With(b => b.Properties, "{\"connectionString\":\"Server=PROD-SQL-01;Database=EnterpriseDB;Integrated Security=true;\"}")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.LicenseKey, "ENTERPRISE-LICENSE-KEY-2024")
            .With(b => b.Version, "2022")
            .With(b => b.BusinessServiceId, Guid.NewGuid().ToString())
            .With(b => b.BusinessServiceName, "Enterprise Business Service")
            .With(b => b.ExceptionMessage, "")
            .With(b => b.FormVersion, "1.0"));

        fixture.Customize<DatabaseNameVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Database Name"));

        fixture.Customize<GetDatabaseByServerIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Database by Server")
            .With(b => b.ServerId, Guid.NewGuid().ToString())
            .With(b => b.ServerName, "PROD-SQL-01"));

        fixture.Customize<GetDatabaseByLicenseKeyListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Database by License")
            .With(b => b.LicenseId, Guid.NewGuid().ToString())
            .With(b => b.DatabaseType, "SQL Server Enterprise"));

        fixture.Customize<GetDatabaseByUserNameVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Database by Username")
            .With(b => b.UserName, "enterprise_admin")
            .With(b => b.DatabaseType, "SQL Server Enterprise"));

        // Query objects
        fixture.Customize<GetDatabaseListQuery>(c => c);
        fixture.Customize<GetDatabaseDetailQuery>(c => c.With(q => q.Id, Guid.NewGuid().ToString()));
       
        fixture.Customize<GetDatabaseNameUniqueQuery>(c => c
            .With(q => q.DatabaseName, "Enterprise Database")
            .With(q => q.DatabaseId, Guid.NewGuid().ToString()));
        fixture.Customize<GetDatabasePaginatedListQuery>(c => c
            .With(q => q.DatabaseTypeId, Guid.NewGuid().ToString())
            .With(q => q.SearchString, "Enterprise"));
        fixture.Customize<GetDatabaseTypeQuery>(c => c.With(q => q.TypeId, Guid.NewGuid().ToString()));
        fixture.Customize<GetDatabaseByServerIdQuery>(c => c.With(q => q.ServerId, Guid.NewGuid().ToString()));
        fixture.Customize<GetDatabaseByLicenseKeyQuery>(c => c.With(q => q.LicenseId, "ENTERPRISE-LICENSE-KEY-2024"));
        fixture.Customize<GetDatabaseByUserNameQuery>(c => c
            .With(q => q.UserName, "enterprise_admin")
            .With(q => q.DatabaseTypeId, "SQL Server"));

        CreateDatabaseCommand = fixture.Create<CreateDatabaseCommand>();
        CreateDatabaseResponse = fixture.Create<CreateDatabaseResponse>();
        UpdateDatabaseCommand = fixture.Create<UpdateDatabaseCommand>();
        UpdateDatabaseResponse = fixture.Create<UpdateDatabaseResponse>();
        DeleteDatabaseCommand = fixture.Create<DeleteDatabaseCommand>();
        DeleteDatabaseResponse = fixture.Create<DeleteDatabaseResponse>();
        SaveAsDatabaseCommand = fixture.Create<SaveAsDatabaseCommand>();
        SaveAsDatabaseResponse = fixture.Create<SaveAsDatabaseResponse>();
        DatabaseTestConnectionCommand = fixture.Create<DatabaseTestConnectionCommand>();
        DatabaseTestConnectionResponse = fixture.Create<DatabaseTestConnectionResponse>();
        UpdateDatabasePasswordCommand = fixture.Create<UpdateDatabasePasswordCommand>();
        UpdateDatabasePasswordResponse = fixture.Create<UpdateDatabasePasswordResponse>();
        UpdateDatabaseVersionCommand = fixture.Create<UpdateDatabaseVersionCommand>();
        UpdateDatabaseVersionResponse = fixture.Create<UpdateDatabaseVersionResponse>();
        DatabaseDetailVm = fixture.Create<DatabaseDetailVm>();
        DatabaseListVm = fixture.Create<DatabaseListVm>();
        DatabaseTypeVm = fixture.Create<DatabaseTypeVm>();
        GetDatabaseByTypeVm = fixture.Create<GetDatabaseByTypeVm>();
        DatabaseNameVm = fixture.Create<DatabaseNameVm>();
        GetDatabaseByServerIdVm = fixture.Create<GetDatabaseByServerIdVm>();
        GetDatabaseByLicenseKeyListVm = fixture.Create<GetDatabaseByLicenseKeyListVm>();
        GetDatabaseByUserNameVm = fixture.Create<GetDatabaseByUserNameVm>();
        GetDatabaseListQuery = fixture.Create<GetDatabaseListQuery>();
        GetDatabaseDetailQuery = fixture.Create<GetDatabaseDetailQuery>();
        GetDatabaseNameQuery = fixture.Create<GetDatabaseNameQuery>();
        GetDatabaseNameUniqueQuery = fixture.Create<GetDatabaseNameUniqueQuery>();
        GetDatabasePaginatedListQuery = fixture.Create<GetDatabasePaginatedListQuery>();
        GetDatabaseTypeQuery = fixture.Create<GetDatabaseTypeQuery>();
        GetDatabaseByServerIdQuery = fixture.Create<GetDatabaseByServerIdQuery>();
        GetDatabaseByLicenseKeyQuery = fixture.Create<GetDatabaseByLicenseKeyQuery>();
        GetDatabaseByUserNameQuery = fixture.Create<GetDatabaseByUserNameQuery>();
    }
}
