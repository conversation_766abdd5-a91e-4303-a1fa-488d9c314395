﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixTemplate.Queries.IsApproversForTemplate;

public class IsApproversForTemplateQueryHandler : IRequestHandler<IsApproversForTemplateQuery, string>
{
    private readonly IApprovalMatrixTemplateRepository _approvalMatrixTemplateRepository;

    public IsApproversForTemplateQueryHandler(IApprovalMatrixTemplateRepository approvalMatrixTemplateRepository)
    {
        _approvalMatrixTemplateRepository = approvalMatrixTemplateRepository;
    }


    public async Task<string> Handle(IsApproversForTemplateQuery request, CancellationToken cancellationToken)
    {
        var status = string.Empty;
        var entity = await _approvalMatrixTemplateRepository.GetByTemplateName(request.Template);
        
        if (entity.WorkflowModification.Equals("True")) status = "WorkflowModification&";
        if (entity.ProfileModification.Equals("True")) status = status + "ProfileModification&";
        if (entity.WorkflowProfileExecution.Equals("True")) status = status + "WorkflowProfileExecution";

        return status;
    }
}