using ContinuityPatrol.Application.Features.IncidentManagementSummary.Events.Update;

namespace ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Update;

public class UpdateIncidentManagementSummaryCommandHandler : IRequestHandler<UpdateIncidentManagementSummaryCommand,
    UpdateIncidentManagementSummaryResponse>
{
    private readonly IIncidentManagementSummaryRepository _incidentManagementSummaryRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateIncidentManagementSummaryCommandHandler(IMapper mapper,
        IIncidentManagementSummaryRepository incidentManagementSummaryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _incidentManagementSummaryRepository = incidentManagementSummaryRepository;
        _publisher = publisher;
    }

    public async Task<UpdateIncidentManagementSummaryResponse> Handle(UpdateIncidentManagementSummaryCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _incidentManagementSummaryRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.IncidentManagementSummary), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateIncidentManagementSummaryCommand),
            typeof(Domain.Entities.IncidentManagementSummary));

        await _incidentManagementSummaryRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateIncidentManagementSummaryResponse
        {
            Message = Message.Update(nameof(Domain.Entities.IncidentManagementSummary),
                eventToUpdate.ParentBusinessServiceImpactId),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(
            new IncidentManagementSummaryUpdatedEvent { Name = eventToUpdate.ParentBusinessServiceImpactId },
            cancellationToken);

        return response;
    }
}