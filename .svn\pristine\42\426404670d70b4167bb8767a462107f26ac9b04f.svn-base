﻿using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;

public class GetAirGapsStatusListVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string SourceSiteId { get; set; }
    public string SourceSiteName { get; set; }
    public string TargetSiteName { get; set; }
    public string TargetSiteId { get; set; }
    public string Description { get; set; }
    [Column(TypeName = "NCLOB")] public string Source { get; set; }
    [Column(TypeName = "NCLOB")] public string Target { get; set; }
    public int Port { get; set; }
    public string SourceComponentId { get; set; }
    public string SourceComponentName { get; set; }
    public string TargetComponentId { get; set; }
    public string TargetComponentName { get; set; }
    public string EnableWorkflowId { get; set; }
    public string DisableWorkflowId { get; set; }
    public string WorkflowStatus { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string RPO { get; set; }
    public bool IsAttached { get; set; }
    public string Status { get; set; }
    public string ErrorMessage { get; set; }

    public List<GetAirGapsServeListVm> GetAirGapsServeListVms { get; set; } = new();
}

public class GetAirGapsServeListVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string RoleType { get; set; }
    public string OSType { get; set; }
    public string IpAddress { get; set; }
    public string HostName { get; set; }
    public string Type { get; set; } = string.Empty;
    public string Port { get; set; } = string.Empty;
}