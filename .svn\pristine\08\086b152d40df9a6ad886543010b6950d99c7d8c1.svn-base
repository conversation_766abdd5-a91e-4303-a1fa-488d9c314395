﻿//using ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;
//using ContinuityPatrol.Domain.ViewModels.HeatMapLogModel;
//using ContinuityPatrol.Shared.Core.Wrapper;

//namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapLog.Queries;

//public class GetHeatMapLogPaginatedListQueryHandlerTests : IClassFixture<HeatMapLogFixture>
//{
//    private readonly HeatMapLogFixture _heatMapLogFixture;

//    private readonly Mock<IHeatMapLogRepository> _heatMapLogRepositoryMock;

//    private readonly GetHeatMapLogPaginatedListQueryHandler _handler;

//    public GetHeatMapLogPaginatedListQueryHandlerTests(HeatMapLogFixture heatMapLogFixture)
//    {
//        _heatMapLogFixture = heatMapLogFixture;
    
//        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.GetPaginatedHeatMapLogRepository(_heatMapLogFixture.HeatMapLogs);
        
//        _handler = new GetHeatMapLogPaginatedListQueryHandler(_heatMapLogFixture.Mapper, _heatMapLogRepositoryMock.Object);

//        _heatMapLogFixture.HeatMapLogs[0].BusinessServiceName = "BS_Test_01";
//        _heatMapLogFixture.HeatMapLogs[0].BusinessFunctionName = "BF_Test_01";
//        _heatMapLogFixture.HeatMapLogs[0].InfraObjectName = "IO_Test_01";
//        _heatMapLogFixture.HeatMapLogs[0].HeatmapType = "Server";

//        _heatMapLogFixture.HeatMapLogs[1].BusinessServiceName = "BS_Test_02";
//        _heatMapLogFixture.HeatMapLogs[1].BusinessFunctionName = "BF_Test_02";
//        _heatMapLogFixture.HeatMapLogs[1].InfraObjectName = "IO_Test_02";
//        _heatMapLogFixture.HeatMapLogs[1].HeatmapType = "Database";
//    }

//    [Fact]
//    public async Task Handle_Return_TotalPage_ShouldRequested()
//    {
//        var result = await _handler.Handle(new GetHeatMapLogPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapLogListVm>>();

//        result.TotalCount.ShouldBe(3);

//        result.TotalPages.ShouldBe(1);
//    }

//    [Fact]
//    public async Task Handle_Return_HeatMapLogList_With_MultipleQueryStringParameter()
//    {
//        var result = await _handler.Handle(new GetHeatMapLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BusinessServiceName=BS_Test_01;businessFunctionName=BF_Test_01;infraObjectName=IO_Test_01;HeatMapType=Server" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapLogListVm>>();

//        result.TotalCount.ShouldBe(1);

//        result.Data[0].BusinessServiceName.ShouldBe("BS_Test_01");

//        result.Data[0].BusinessFunctionName.ShouldBe("BF_Test_01");

//        result.Data[0].InfraObjectName.ShouldBe("IO_Test_01");

//        result.Data[0].HeatmapType.ShouldBe("Server");
//    }

//    [Fact]
//    public async Task Handle_Return_PaginatedHeatMapLogList_When_QueryStringMatch()
//    {
//        var result = await _handler.Handle(new GetHeatMapLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Server" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapLogListVm>>();

//        result.TotalCount.ShouldBe(1);

//        result.Data[0].ShouldBeOfType<HeatMapLogListVm>();

//        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

//        result.Data[0].BusinessServiceName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessServiceName);

//        result.Data[0].BusinessFunctionName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessFunctionName);

//        result.Data[0].InfraObjectName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].InfraObjectName);

//        result.Data[0].HeatmapType.ShouldBe("Server");
//    }

//    [Fact]
//    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
//    {
//        var result = await _handler.Handle(new GetHeatMapLogPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapLogListVm>>();

//        result.TotalCount.ShouldBe(0);
//    }

//    [Fact]
//    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(new GetHeatMapLogPaginatedListQuery(), CancellationToken.None);

//        _heatMapLogRepositoryMock.Verify(x => x.PaginatedListAllAsync(), Times.Once);
//    }
//}