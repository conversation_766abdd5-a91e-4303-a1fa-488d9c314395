﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Create;

public class DatabaseLicenseInfoCreatedEventHandler : INotificationHandler<DatabaseLicenseInfoCreatedEvent>
{
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<DatabaseLicenseInfoCreatedEventHandler> _logger;

    public DatabaseLicenseInfoCreatedEventHandler(ILicenseInfoRepository licenseInfoRepository,
        ILogger<DatabaseLicenseInfoCreatedEventHandler> logger, ILoggedInUserService loggedInUserService)
    {
        _licenseInfoRepository = licenseInfoRepository;
        _logger = logger;
        _loggedInUserService = loggedInUserService;
    }

    public async Task Handle(DatabaseLicenseInfoCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        await _licenseInfoRepository.AddAsync(new Domain.Entities.LicenseInfo
        {
            LicenseId = createdEvent.LicenseId,
            PONumber = createdEvent.PONumber,
            CompanyId = _loggedInUserService.CompanyId,
            EntityName = createdEvent.EntityName,
            EntityId = createdEvent.EntityId,
            Entity = Modules.Database.ToString(),
            EntityType = string.Empty,
            Type = createdEvent.Type,
            EntityField = createdEvent.EntityField,
            BusinessServiceId = createdEvent.BusinessServiceId,
            BusinessServiceName = createdEvent.BusinessServiceName,
            Category = createdEvent.Category,
            Logo = createdEvent.Logo
        });
        _logger.LogDebug($"'{createdEvent.EntityName}' added successfully in license Info.");
    }
}