using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraObjectViewRepositoryTests : IClassFixture<InfraObjectViewFixture>, IDisposable
{
    private readonly InfraObjectViewFixture _infraObjectViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraObjectViewRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraObjectViewRepositoryTests(InfraObjectViewFixture infraObjectViewFixture)
    {
        _infraObjectViewFixture = infraObjectViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraObjectViewRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraObjectViews.RemoveRange(_dbContext.InfraObjectViews);
        await _dbContext.SaveChangesAsync();
    }

    #region GetInfraObjectByName Tests

    [Fact]
    public async Task GetInfraObjectByName_ReturnsNull_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Infrastructure Object";

        // Act
        var result = await _repository.GetInfraObjectByName(nonExistentName);

        // Assert
        Assert.Null(result); // Should return null when no matching name found in view
    }

    [Fact]
    public async Task GetInfraObjectByName_CallsCorrectMethod()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Object";

        // Act
        var result = await _repository.GetInfraObjectByName(infraObjectName);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns null when no data exists
        Assert.Null(result);
    }

    #endregion

    #region GetInfraObjectByBusinessFunctionId Tests

    [Fact]
    public async Task GetInfraObjectByBusinessFunctionId_ExecutesWithoutError_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessFunctionId = "BF_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByBusinessFunctionId(businessFunctionId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByBusinessFunctionId_ExecutesWithoutError_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var businessFunctionId = "BF_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByBusinessFunctionId(businessFunctionId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectByServerId Tests

    [Fact]
    public async Task GetInfraObjectByServerId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var serverId = "SERVER_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByServerId(serverId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectByReplicationId Tests

    [Fact]
    public async Task GetInfraObjectByReplicationId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var replicationId = "REPLICATION_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByReplicationId(replicationId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectByDatabaseId Tests

    [Fact]
    public async Task GetInfraObjectByDatabaseId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DATABASE_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByDatabaseId(databaseId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectByNodeId Tests

    [Fact]
    public async Task GetInfraObjectByNodeId_ExecutesWithoutError_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = "NODE_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByNodeId(nodeId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByNodeId_ExecutesWithoutError_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = "NODE_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByNodeId(nodeId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectListByReplicationTypeId Tests

    [Fact]
    public async Task GetInfraObjectListByReplicationTypeId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var replicationTypeId = "RT_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectListByReplicationTypeId(replicationTypeId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetInfraObjectByBusinessServiceId Tests

    [Fact]
    public async Task GetInfraObjectByBusinessServiceId_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByBusinessServiceId(businessServiceId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns empty list when no data exists
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenReferenceIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentReferenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result); // Should return null when no matching reference ID found in view
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns null when no data exists
        Assert.Null(result);
    }

    #endregion

    #region View-Specific Tests

    [Fact]
    public async Task GetInfraObjectByName_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Object";

        // Act
        var result = await _repository.GetInfraObjectByName(infraObjectName);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetInfraObjectByBusinessFunctionId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var businessFunctionId = "BF_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByServerId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var serverId = "SERVER_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByServerId(serverId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByReplicationId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var replicationId = "REPLICATION_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByReplicationId(replicationId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByDatabaseId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DATABASE_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByDatabaseId(databaseId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByNodeId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var nodeId = "NODE_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByNodeId(nodeId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectListByReplicationTypeId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var replicationTypeId = "RT_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectListByReplicationTypeId(replicationTypeId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraObjectByBusinessServiceId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var businessServiceId = "BS_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion
}
