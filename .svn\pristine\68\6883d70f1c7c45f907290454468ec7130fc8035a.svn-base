﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class ComponentTypeRepositoryMocks
{

    public static Mock<IComponentTypeRepository> CreateComponentTypeRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(componentTypes);

        mockComponentTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<ComponentType>())).ReturnsAsync(
            (ComponentType componentType) =>
            {
                componentType.Id = new Fixture().Create<int>();

                componentType.ReferenceId = new Fixture().Create<Guid>().ToString();

                componentTypes.Add(componentType);

                return componentType;
            });

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> UpdateComponentTypeRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(componentTypes);

        mockComponentTypeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => componentTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockComponentTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ComponentType>())).ReturnsAsync((ComponentType componentType) =>
        {
            var index = componentTypes.FindIndex(item => item.ReferenceId == componentType.ReferenceId);

            componentTypes[index] = componentType;

            return componentType;
        });

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> DeleteComponentTypeRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(componentTypes);

        mockComponentTypeRepository.Setup(repo => repo.GetComponentTypeById(It.IsAny<string>())).ReturnsAsync((string i) => componentTypes.SingleOrDefault(x => x.ReferenceId == i));

        mockComponentTypeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<ComponentType>())).ReturnsAsync((ComponentType componentType) =>
        {
            var index = componentTypes.FindIndex(item => item.ReferenceId == componentType.ReferenceId);

            componentType.IsActive = false;

            componentTypes[index] = componentType;

            return componentType;
        });

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> GetComponentTypeRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(componentTypes);

        mockComponentTypeRepository.Setup(repo => repo.GetComponentTypeById(It.IsAny<string>())).ReturnsAsync((string i) => componentTypes.SingleOrDefault(x => x.ReferenceId == i));

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> GetComponentTypeNameUniqueRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.IsComponentTypeNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => componentTypes.Exists(x => x.ComponentName == i && x.ReferenceId == j));

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> GetComponentTypeEmptyRepository()
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<ComponentType>());

        return mockComponentTypeRepository;
    }

    public static Mock<IComponentTypeRepository> GetPaginatedComponentTypeRepository(List<ComponentType> componentTypes)
    {
        var mockComponentTypeRepository = new Mock<IComponentTypeRepository>();

        var queryableComponentType = componentTypes.BuildMock();

        mockComponentTypeRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableComponentType);

        return mockComponentTypeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateComponentTypeEventRepository(List<UserActivity> userActivities)
    {
        var mockComponentTypeRepository = new Mock<IUserActivityRepository>();

        mockComponentTypeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockComponentTypeRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockComponentTypeRepository;
    }
}