using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class BusinessServiceHealthStatusFixture : IDisposable
{
    public CreateBusinessServiceHealthStatusCommand CreateBusinessServiceHealthStatusCommand { get; set; }
    public UpdateBusinessServiceHealthStatusCommand UpdateBusinessServiceHealthStatusCommand { get; set; }
    public List<BusinessServiceHealthStatusListVm> BusinessServiceHealthStatusListVm { get; set; }
    public BusinessServiceHealthStatusDetailVm BusinessServiceHealthStatusDetailVm { get; set; }

    public BusinessServiceHealthStatusFixture()
    {
        CreateBusinessServiceHealthStatusCommand = AutoBusinessServiceHealthStatusFixture.Create<CreateBusinessServiceHealthStatusCommand>();
        UpdateBusinessServiceHealthStatusCommand = AutoBusinessServiceHealthStatusFixture.Create<UpdateBusinessServiceHealthStatusCommand>();
        BusinessServiceHealthStatusListVm = AutoBusinessServiceHealthStatusFixture.CreateMany<BusinessServiceHealthStatusListVm>(3).ToList();
        BusinessServiceHealthStatusDetailVm = AutoBusinessServiceHealthStatusFixture.Create<BusinessServiceHealthStatusDetailVm>();
    }

    public Fixture AutoBusinessServiceHealthStatusFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateBusinessServiceHealthStatusCommand>(c => c
                .With(b => b.ConfiguredCount, 50)
                .With(b => b.DRReadyCount, 45)
                .With(b => b.DRNotReadyCount, 5)
                .With(b => b.ProblemState, "Minor Issues"));

            fixture.Customize<UpdateBusinessServiceHealthStatusCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, 60)
                .With(b => b.DRReadyCount, 55)
                .With(b => b.DRNotReadyCount, 5)
                .With(b => b.ProblemState, "All Systems Operational"));

            fixture.Customize<BusinessServiceHealthStatusListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, () => fixture.Create<int>() % 100 + 20)
                .With(b => b.DRReadyCount, () => fixture.Create<int>() % 80 + 15)
                .With(b => b.DRNotReadyCount, () => fixture.Create<int>() % 20 + 1)
                .With(b => b.ProblemState, () => fixture.Create<bool>() ? "Operational" : "Issues Detected"));

            fixture.Customize<BusinessServiceHealthStatusDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.ConfiguredCount, 75)
                .With(b => b.DRReadyCount, 68)
                .With(b => b.DRNotReadyCount, 7)
                .With(b => b.ProblemState, "Critical Issues - Immediate Attention Required"));
               

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
