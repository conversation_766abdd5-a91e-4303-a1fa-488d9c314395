﻿//namespace ContinuityPatrol.Application.Features.InfraObject.Commands.CreateBulkData.Helper;

//public class CreateBulkDataDataBaseListCommand
//{
//    public string Name { get; set; }

//    public string DatabaseType { get; set; }

//    public string Type { get; set; }

//    public string ServerId { get; set; }

//    public string ServerName { get; set; }

//    public string CompanyId { get; set; }

//    public string Properties { get; set; }

//    public string ModeType { get; set; }

//    public int Isracdbnode { get; set; }

//    public string DatabaseConnectivity { get; set; }

//    public int IsVerified { get; set; }

//    public string ErrorMessage { get; set; }

//    public int Port { get; set; }

//    public string LicenseKey { get; set; }

//    public override string ToString()
//    {
//        return $"Name: {Name};";
//    }
//}

