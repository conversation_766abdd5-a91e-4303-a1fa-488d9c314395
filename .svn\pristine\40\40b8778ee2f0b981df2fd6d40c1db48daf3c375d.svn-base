﻿namespace ContinuityPatrol.Shared.Core.Permissions;

public static class Permissions
{
    public static List<string> GeneratePermissionsList(string module)
    {
        var access = string.Empty;

        if (module == Modules.Orchestration.ToString())
        {
            access = $"Permissions.{module}.Execute";
        }

        var permissions = new List<string>();

        if (module == Modules.Dashboard.ToString())
        {
            permissions.AddRange(new[]
            {   
                $"Permissions.{module}.View",
                $"Permissions.{module}.Monitor",
                $"Permissions.{module}.Management"
            });
        }
        else
        {
            permissions.AddRange(new[]
            {
                $"Permissions.{module}.Create",
                $"Permissions.{module}.View",
                $"Permissions.{module}.Edit",
                $"Permissions.{module}.Delete",
                $"Permissions.{module}.CreateAndEdit",
                access
             });
        }

        permissions.RemoveAll(string.IsNullOrEmpty);

        return permissions;

    }

    public static List<string> All()
    {
        var allPermissions = new List<string>();

        var modules = Enum.GetValues(typeof(Modules));

        foreach (var module in modules) allPermissions.AddRange(GeneratePermissionsList(module.ToString()));

        return allPermissions;
    }

    //Operator-Permissions
    public static List<string> GenerateOperatorPermissionsList()
    {
        return new List<string>
        {
            $"Permissions.Configuration.View",
            $"Permissions.Alerts.View",
            $"Permissions.Reports.View",
            $"Permissions.Dashboard.View",
            $"Permissions.Dashboard.Monitor",
            $"Permissions.Orchestration.View",
            $"Permissions.Cyber.View",
            $"Permissions.Drift.View",
            $"Permissions.ResiliencyReadiness.View",
            $"Permissions.CloudConnect.View"
        };
    }

    //Manage-Permissions
    public static List<string> GenerateManagerPermissionsList()
    {
        return new List<string>
        {
            $"Permissions.Alerts.View",
            $"Permissions.Reports.View",
            $"Permissions.Dashboard.View",
            $"Permissions.Dashboard.Monitor",
            $"Permissions.Manage.View",
            $"Permissions.Cyber.View",
            $"Permissions.Drift.View",
            $"Permissions.ResiliencyReadiness.View",
            $"Permissions.CloudConnect.View"
        };
    }

    public static List<string> OperatorAll()
    {
        var allPermissions = new List<string>();

        // foreach (var module in modules)
        allPermissions.AddRange(GenerateOperatorPermissionsList());

        return allPermissions;
    }

    public static List<string> ManageAll()
    {
        var allPermissions = new List<string>();

        // foreach (var module in modules)
        allPermissions.AddRange(GenerateManagerPermissionsList());

        return allPermissions;
    }



    public static class Configuration
    {
        public const string View = "Permissions.Configuration.View";
        public const string Create = "Permissions.Configuration.Create";
        public const string Edit = "Permissions.Configuration.Edit";
        public const string Delete = "Permissions.Configuration.Delete";
        public const string CreateAndEdit = "Permissions.Configuration.CreateAndEdit";
    }
    public static class Admin
    {
        public const string View = "Permissions.Admin.View";
        public const string Create = "Permissions.Admin.Create";
        public const string Edit = "Permissions.Admin.Edit";
        public const string Delete = "Permissions.Admin.Delete";
        public const string CreateAndEdit = "Permissions.Admin.CreateAndEdit";
    }
    public static class Manage
    {
        public const string View = "Permissions.Manage.View";
        public const string Create = "Permissions.Manage.Create";
        public const string Edit = "Permissions.Manage.Edit";
        public const string Delete = "Permissions.Manage.Delete";
        public const string CreateAndEdit = "Permissions.Manage.CreateAndEdit";
    }
    public static class Dashboard
    {
        public const string Create = "Permissions.Configuration.Create";
        public const string Edit = "Permissions.Configuration.Edit";
        public const string Delete = "Permissions.Configuration.Delete";
        public const string View = "Permissions.Dashboard.View";
        public const string Monitor = "Permissions.Dashboard.Monitor";
        public const string Management = "Permissions.Dashboard.Management";
        public const string CreateAndEdit = "Permissions.Configuration.CreateAndEdit";
    }
    public static class Orchestration
    {
        public const string View = "Permissions.Orchestration.View";
        public const string Create = "Permissions.Orchestration.Create";
        public const string Edit = "Permissions.Orchestration.Edit";
        public const string Delete = "Permissions.Orchestration.Delete";
        public const string CreateAndEdit = "Permissions.Orchestration.CreateAndEdit";
    }
    public static class Reports
    {
        public const string View = "Permissions.Reports.View";
        public const string Create = "Permissions.Reports.Create";
        public const string Edit = "Permissions.Reports.Edit";
        public const string Delete = "Permissions.Reports.Delete";
        public const string CreateAndEdit = "Permissions.Reports.CreateAndEdit";
    }
    public static class Alerts
    {
        public const string View = "Permissions.Alerts.View";
        public const string Create = "Permissions.Reports.Create";
        public const string Edit = "Permissions.Reports.Edit";
        public const string Delete = "Permissions.Reports.Delete";
        public const string CreateAndEdit = "Permissions.Reports.CreateAndEdit";
    }
    public static class Cyber
    {
        public const string View = "Permissions.Cyber.View";
        public const string Create = "Permissions.Cyber.Create";
        public const string Edit = "Permissions.Cyber.Edit";
        public const string Delete = "Permissions.Cyber.Delete";
        public const string CreateAndEdit = "Permissions.Cyber.CreateAndEdit";
    }

    public static class Drift
    {
        public const string View = "Permissions.Drift.View";
        public const string Create = "Permissions.Drift.Create";
        public const string Edit = "Permissions.Drift.Edit";
        public const string Delete = "Permissions.Drift.Delete";
        public const string CreateAndEdit = "Permissions.Drift.CreateAndEdit";
    }
    public static class ResiliencyReadiness
    {
        public const string View = "Permissions.ResiliencyReadiness.View";
        public const string Create = "Permissions.ResiliencyReadiness.Create";
        public const string Edit = "Permissions.ResiliencyReadiness.Edit";
        public const string Delete = "Permissions.ResiliencyReadiness.Delete";
        public const string CreateAndEdit = "Permissions.ResiliencyReadiness.CreateAndEdit";
    }
    public static class CloudConnect
    {
        public const string View = "Permissions.CloudConnect.View";
       
    }
}