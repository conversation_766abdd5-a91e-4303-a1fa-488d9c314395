﻿using ContinuityPatrol.Application.Features.UserRole.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.UserRoleModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Queries;

public class GetUserRolePaginatedListQueryHandlerTests : IClassFixture<UserRoleFixture>
{
    private readonly UserRoleFixture _userRoleFixture;

    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;

    private readonly GetUserRolePaginatedListQueryHandler _handler;

    public GetUserRolePaginatedListQueryHandlerTests(UserRoleFixture userRoleFixture)
    {
        _userRoleFixture = userRoleFixture;

        _mockUserRoleRepository = UserRoleRepositoryMocks.GetPaginatedUserRoleRepository(_userRoleFixture.UserRoles);

        _handler = new GetUserRolePaginatedListQueryHandler(_userRoleFixture.Mapper, _mockUserRoleRepository.Object);

        _userRoleFixture.UserRoles[0].Role = "Admin";
        _userRoleFixture.UserRoles[1].Role = "CpAdmin";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetUserRolePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserRoleListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Users_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetUserRolePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "role=Admin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserRoleListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Role.ShouldBe("Admin");
    }

    [Fact]
    public async Task Handle_Return_PaginatedUserRoles_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetUserRolePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Admin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserRoleListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<UserRoleListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Role.ShouldBe(_userRoleFixture.UserRoles[0].Role);

        result.Data[0].IsDelete.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetUserRolePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserRoleListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetUserRolePaginatedListQuery(), CancellationToken.None);

        _mockUserRoleRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}