using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DRReadyLogRepositoryTests : IClassFixture<DRReadyLogFixture>
{
    private readonly DRReadyLogFixture _drReadyLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DRReadyLogRepository _repository;
    private readonly DRReadyLogRepository _repositoryNotParent;

    public DRReadyLogRepositoryTests(DRReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DRReadyLogRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DRReadyLogRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;

        // Act
        var result = await _repository.AddAsync(drReadyLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyLog.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(drReadyLog.BusinessFunctionId, result.BusinessFunctionId);
        Assert.Single(_dbContext.DrReadyLog);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repository.AddAsync(drReadyLog);

        drReadyLog.BusinessServiceName = "Updated Service Name";

        // Act
        var result = await _repository.UpdateAsync(drReadyLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repository.AddAsync(drReadyLog);

        // Act
        var result = await _repository.DeleteAsync(drReadyLog);

        // Assert
        Assert.Equal(drReadyLog.BusinessServiceId, result.BusinessServiceId);
        Assert.Empty(_dbContext.DrReadyLog);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        var addedEntity = await _repository.AddAsync(drReadyLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsParent()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repository.AddAsync(drReadyLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(drReadyLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyLog.ReferenceId, result.ReferenceId);
        Assert.Equal(drReadyLog.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists_AndIsNotParent()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repositoryNotParent.AddAsync(drReadyLog);

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(drReadyLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyLog.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfra()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(drReadyLogs.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        await _repositoryNotParent.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;

        // Act
        var result = await _repository.AddRangeAsync(drReadyLogs);

        // Assert
        Assert.Equal(drReadyLogs.Count, result.Count());
        Assert.Equal(drReadyLogs.Count, _dbContext.DrReadyLog.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.RemoveRangeAsync(drReadyLogs);

        // Assert
        Assert.Equal(drReadyLogs.Count, result.Count());
        Assert.Empty(_dbContext.DrReadyLog);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region GetDrReadyLogForDrReadyReportByBusinessServiceId Tests

    [Fact]
    public async Task GetDrReadyLogForDrReadyReportByBusinessServiceId_ShouldReturnEntities_WhenIsAllInfra()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.GetDrReadyLogForDrReadyReportByBusinessServiceId(DRReadyLogFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.Equal(DRReadyLogFixture.BusinessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetDrReadyLogForDrReadyReportByBusinessServiceId_ShouldReturnFilteredEntities_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        await _repositoryNotParent.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repositoryNotParent.GetDrReadyLogForDrReadyReportByBusinessServiceId(DRReadyLogFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    [Fact]
    public async Task GetDrReadyLogForDrReadyReportByBusinessServiceId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetDrReadyLogForDrReadyReportByBusinessServiceId("invalid-guid"));
    }

    #endregion

    #region GetDrReadyLogByBusinessServiceId Tests

    [Fact]
    public async Task GetDrReadyLogByBusinessServiceId_ShouldReturnEntity_WhenIsAllInfra()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repository.AddAsync(drReadyLog);

        // Act
        var result = await _repository.GetDrReadyLogByBusinessServiceId(DRReadyLogFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DRReadyLogFixture.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetDrReadyLogByBusinessServiceId_ShouldReturnFilteredEntity_WhenIsNotAllInfra()
    {
        // Arrange
        var drReadyLog = _drReadyLogFixture.DRReadyLogDto;
        await _repositoryNotParent.AddAsync(drReadyLog);

        // Act
        var result = await _repositoryNotParent.GetDrReadyLogByBusinessServiceId(DRReadyLogFixture.BusinessServiceId);

        // Assert
        // Result should be filtered based on assigned infrastructure
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetDrReadyLogByBusinessServiceId_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetDrReadyLogByBusinessServiceId("invalid-guid"));
    }

    #endregion

    #region GetDrReadyLogByLast7Days Tests

    [Fact]
    public async Task GetDrReadyLogByLast7Days_ShouldReturnRecentEntities()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        // Set some logs to be within last 7 days
        drReadyLogs.Take(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-3));
        // Set some logs to be older than 7 days
        drReadyLogs.Skip(3).ToList().ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-10));

        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.GetDrReadyLogByLast7Days();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.LastModifiedDate.Date >= DateTime.Now.Date.AddDays(-7)));
    }

    [Fact]
    public async Task GetDrReadyLogByLast7Days_ShouldReturnEmptyList_WhenNoRecentEntities()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        // Set all logs to be older than 7 days
        drReadyLogs.ForEach(x => x.LastModifiedDate = DateTime.Now.AddDays(-10));

        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.GetDrReadyLogByLast7Days();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDrReadyLogListByStartTimeAndEndTime Tests

    [Fact]
    public async Task GetDrReadyLogListByStartTimeAndEndTime_ShouldReturnEntitiesInDateRange()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);

        // Set some logs to be within date range
        drReadyLogs.Take(3).ToList().ForEach(x => x.CreatedDate = startDate.AddDays(1));
        // Set some logs to be outside date range
        drReadyLogs.Skip(3).ToList().ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-10));

        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.GetDrReadyLogListByStartTimeAndEndTime(
            startDate.ToString("yyyy-MM-dd"),
            endDate.ToString("yyyy-MM-dd"),
            DRReadyLogFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= startDate.Date && x.CreatedDate.Date <= endDate.Date));
        Assert.All(result, x => Assert.Equal(DRReadyLogFixture.BusinessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetDrReadyLogListByStartTimeAndEndTime_ShouldReturnEmptyList_WhenNoEntitiesInRange()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList;
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);

        // Set all logs to be outside date range
        drReadyLogs.ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-10));

        await _repository.AddRangeAsync(drReadyLogs);

        // Act
        var result = await _repository.GetDrReadyLogListByStartTimeAndEndTime(
            startDate.ToString("yyyy-MM-dd"),
            endDate.ToString("yyyy-MM-dd"),
            DRReadyLogFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var drReadyLogs = _drReadyLogFixture.DRReadyLogList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(drReadyLogs);
        var initialCount = drReadyLogs.Count;

        var toUpdate = drReadyLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.BusinessServiceName = "UpdatedServiceName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = drReadyLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.BusinessServiceName == "UpdatedServiceName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
