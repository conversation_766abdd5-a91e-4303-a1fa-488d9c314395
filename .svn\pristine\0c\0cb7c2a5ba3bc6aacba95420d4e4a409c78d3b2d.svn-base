﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Workflow.Events.Freeze;

public class UpdateWorkflowFourEyeStatusEventHandler : INotificationHandler<UpdateWorkflowFreezeEvent>
{
    private readonly ILogger<UpdateWorkflowFreezeEvent> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UpdateWorkflowFourEyeStatusEventHandler(ILogger<UpdateWorkflowFreezeEvent> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(UpdateWorkflowFreezeEvent updatedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow '{updatedEvent.WorkflowName}' {(updatedEvent.IsFreeze ? "Freeze" : "unfreeze")} successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{(updatedEvent.IsFreeze ? "Freeze" : "unfreeze")} {Modules.Workflow}",
            Entity = Modules.Workflow.ToString(),
            ActivityType = updatedEvent.IsFreeze ? "Freeze" : "unfreeze",
            ActivityDetails = $"Workflow '{updatedEvent.WorkflowName}' {(updatedEvent.IsFreeze ? "Freeze" : "unfreeze")} successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}