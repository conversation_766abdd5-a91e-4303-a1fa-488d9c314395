﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.WorkflowExecutionEventLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetPaginatedList;

public class GetWorkflowExecutionEventLogPaginatedListQueryHandler : IRequestHandler<
    GetWorkflowExecutionEventLogPaginatedListQuery, PaginatedResult<WorkflowExecutionEventLogListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowExecutionEventLogRepository _workflowExecutionEventLogRepository;

    public GetWorkflowExecutionEventLogPaginatedListQueryHandler(IMapper mapper,
        IWorkflowExecutionEventLogRepository workflowExecutionEventLogRepository)
    {
        _mapper = mapper;
        _workflowExecutionEventLogRepository = workflowExecutionEventLogRepository;
    }

    public async Task<PaginatedResult<WorkflowExecutionEventLogListVm>> Handle(
        GetWorkflowExecutionEventLogPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _workflowExecutionEventLogRepository.GetPaginatedQuery();

        var productFilterSpec = new WorkflowExecutionEventLogFilterSpecification(request.SearchString);

        var workflowExecutionEventLogList = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<WorkflowExecutionEventLogListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return workflowExecutionEventLogList;
    }
}