using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaImpactType.Events.Create;

public class FiaImpactTypeCreatedEventHandler : INotificationHandler<FiaImpactTypeCreatedEvent>
{
    private readonly ILogger<FiaImpactTypeCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaImpactTypeCreatedEventHandler(ILoggedInUserService userService,
        ILogger<FiaImpactTypeCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FiaImpactTypeCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} FiaImpactType",
            Entity = "FiaImpactType",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"FiaImpactType '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaImpactType '{createdEvent.Name}' created successfully.");
    }
}