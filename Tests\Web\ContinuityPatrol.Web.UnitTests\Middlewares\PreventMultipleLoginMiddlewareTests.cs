using ContinuityPatrol.Application.Models;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Tests.Helper;
using ContinuityPatrol.Web.Middlewares;

namespace ContinuityPatrol.Web.UnitTests.Middlewares;

public class PreventMultipleLoginMiddlewareTests
{
    private readonly Mock<ILogger<PreventMultipleLoginMiddleware>> _loggerMock;
    private readonly Mock<IDistributedCache> _cacheMock;

    public PreventMultipleLoginMiddlewareTests()
    {
        _loggerMock = new Mock<ILogger<PreventMultipleLoginMiddleware>>();
        _cacheMock = new Mock<IDistributedCache>();

    }

    private DefaultHttpContext CreateHttpContext(string path, string userName, bool isAuthenticated = true)
    {

        var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();

        var context = new DefaultHttpContext();
        context.Request.Path = path;


        var authServiceMock = new Mock<IAuthenticationService>();
        authServiceMock
            .Setup(x => x.SignOutAsync(It.IsAny<HttpContext>(), It.IsAny<string>(), It.IsAny<AuthenticationProperties>()))
            .Returns(Task.CompletedTask);

        var services = new ServiceCollection();
        services.AddSingleton(authServiceMock.Object); // inject mocked IAuthenticationService
        context.RequestServices = services.BuildServiceProvider();

        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, userName)
        };

        var identity = new ClaimsIdentity(claims, isAuthenticated ? "TestAuth" : null);
        var principal = new ClaimsPrincipal(identity);
        context.User = principal;

        context.Session = new TestSession();
        mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(context);
        WebHelper.Configure(mockHttpContextAccessor.Object);
        return context;
    }


   



    private PreventMultipleLoginMiddleware CreateMiddleware(RequestDelegate next)
    {
        return new PreventMultipleLoginMiddleware(next, _loggerMock.Object, _cacheMock.Object);
    }


    [Fact]
    public async Task Invoke_ShouldBypass_ForApiAndErrorPaths()
    {
        // Arrange
        var middleware = CreateMiddleware(ctx =>
        {
            ctx.Items["NextCalled"] = true;
            return Task.CompletedTask;
        });

        var context = CreateHttpContext("/api/values","Admin");

        // Act
        await middleware.Invoke(context);

        // Assert
        Assert.True(context.Items.ContainsKey("NextCalled"));
    }

    [Fact]
    public async Task Invoke_ShouldSignOut_IfCacheMissAndAuthenticated()
    {
        var userName = "user1";
       
        _cacheMock.Setup(c => c.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((byte[]?)null);

        var context = CreateHttpContext("/home", userName);

        var signOutCalled = false;

        var middleware = CreateMiddleware(_ =>
        {
            signOutCalled = true;
            return Task.CompletedTask;
        });

        var userSession = new UserSession
        {
            LoginName = userName,
            AuthenticationType = "In House"
        };

        var json = JsonConvert.SerializeObject(userSession); 
        var bytes = Encoding.UTF8.GetBytes(json);

        context.Session.Set("SESSION", bytes); 
        
        await middleware.Invoke(context);

        Assert.True(signOutCalled);
    }

    [Fact]
    public async Task Invoke_ShouldThrowSessionExpired_WhenCacheSessionMismatch()
    {
        var userName = "user1";
        var loginName = "user1";

        var context = CreateHttpContext("/home", userName);

        var fakeSessionId = "this-is-a-different-session-id";
        var bytes = Encoding.UTF8.GetBytes(fakeSessionId);

        _cacheMock.Setup(c => c.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bytes);

        var userSession = new UserSession
        {
            LoginName = loginName,
            AuthenticationType = "In House"
        };

        var sessionJson = JsonConvert.SerializeObject(userSession);
        var sessionBytes = Encoding.UTF8.GetBytes(sessionJson);
        context.Session.Set("SESSION", sessionBytes); 

        var middleware = CreateMiddleware(_ => Task.CompletedTask);

        // Act & Assert
        var ex = await Assert.ThrowsAsync<SessionExpiredException>(() => middleware.Invoke(context));
        Assert.Contains("Session logged out", ex.Message);
    }

    [Fact]
    public async Task Invoke_ShouldNotThrow_WhenCacheSessionAndLoginNameMatch()
    {
        var userName = "user1";
        var loginName = "user1"; 

        var context = CreateHttpContext("/home", userName);

        
        var sessionId = Guid.NewGuid().ToString();
        ((TestSession)context.Session).Id = sessionId;

        
        var bytes = Encoding.UTF8.GetBytes(sessionId);
        _cacheMock.Setup(c => c.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(bytes);

       
        var userSession = new UserSession
        {
            LoginName = loginName,
            AuthenticationType = "In House"
        };

        var json = JsonConvert.SerializeObject(userSession);
        context.Session.Set("SESSION", Encoding.UTF8.GetBytes(json));

        WebHelper.Configure(new HttpContextAccessor { HttpContext = context });

        var middleware = CreateMiddleware(_ => Task.CompletedTask);

        
        await middleware.Invoke(context);
    }


}

public class PreventMultipleLoginMiddlewareExtensionTests
{
    [Fact]
    public void UsePreventMultipleLogin_ShouldRegisterMiddleware()
    {
        // Arrange
        var app = new Mock<IApplicationBuilder>();
        app.Setup(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()))
            .Returns(app.Object);

        // Act
        var result = app.Object.UsePreventMultipleLogin();

        // Assert
        result.Should().Be(app.Object);
        app.Verify(x => x.Use(It.IsAny<Func<RequestDelegate, RequestDelegate>>()), Times.Once);
    }
}
