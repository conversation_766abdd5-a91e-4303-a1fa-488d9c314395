﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FourEyeApprovers.Event;

public class FourEyeApproversCreatedEventHandler : INotificationHandler<FourEyeApproversCreatedEvent>
{
    private readonly ILogger<FourEyeApproversCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FourEyeApproversCreatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<FourEyeApproversCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(FourEyeApproversCreatedEvent createedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.Company}",
            Entity = Modules.Company.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Four Eye Approvers for '{createedEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"User Group '{createedEvent.Name}' created successfully.");
    }
}