﻿using ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;

public class GetJobPaginatedListQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobPaginatedListQueryHandler _handler;

    public GetJobPaginatedListQueryHandlerTests(JobFixture jobFixture)
    {
        var jobNewFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetPaginatedJobRepository(jobNewFixture.Jobs);

        _handler = new GetJobPaginatedListQueryHandler(jobNewFixture.Mapper, _mockJobRepository.Object);

        jobNewFixture.Jobs[0].NodeName = "Node_Test";
        jobNewFixture.Jobs[0].InfraObjectProperties = "Infra_Job";
        jobNewFixture.Jobs[0].TemplateName = "Oracle";
        jobNewFixture.Jobs[0].Name = "Work_Test";
        jobNewFixture.Jobs[0].Status = "Updated";
            


        jobNewFixture.Jobs[1].NodeName = "Job_Nodes";
        jobNewFixture.Jobs[1].InfraObjectProperties = "Infra_Type123";
        jobNewFixture.Jobs[1].TemplateName = "MYSQL";
        jobNewFixture.Jobs[1].Name = "Work_Job";
        jobNewFixture.Jobs[1].Status = "Pending";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetJobPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<JobListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedInfraObjectSchedulers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetJobPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Node_Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<JobListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<JobListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectProperties.ShouldBe("Infra_Job");

        result.Data[0].TemplateName.ShouldBe("Oracle");

        result.Data[0].NodeName.ShouldBe("Node_Test");

        result.Data[0].Name.ShouldBe("Work_Test");

        result.Data[0].Status.ShouldBe("Updated");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetJobPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<JobListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_InfraObjects_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetJobPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "infraObjectName=Infra_Type;templatename=Work_Infra;nodename=Node_Test;Status=Updated;name=Work_Test;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<JobListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectProperties.ShouldBe("Infra_Job");

        result.Data[0].TemplateName.ShouldBe("Oracle");

        result.Data[0].NodeName.ShouldBe("Node_Test");

        result.Data[0].Name.ShouldBe("Work_Test");

        result.Data[0].Status.ShouldBe("Updated");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetJobPaginatedListQuery(), CancellationToken.None);

        _mockJobRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}