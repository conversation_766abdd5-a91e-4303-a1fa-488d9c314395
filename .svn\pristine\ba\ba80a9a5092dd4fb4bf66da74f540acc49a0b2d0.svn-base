using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CredentialProfileFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";

    public List<CredentialProfile> CredentialProfilePaginationList { get; set; }
    public List<CredentialProfile> CredentialProfileList { get; set; }
    public CredentialProfile CredentialProfileDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CredentialProfileFixture()
    {
        var fixture = new Fixture();

        CredentialProfileList = fixture.Create<List<CredentialProfile>>();

        CredentialProfilePaginationList = fixture.CreateMany<CredentialProfile>(20).ToList();

        CredentialProfilePaginationList.ForEach(x => x.CompanyId = CompanyId);
        CredentialProfilePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CredentialProfilePaginationList.ForEach(x => x.IsActive = true);

        CredentialProfileList.ForEach(x => x.CompanyId = CompanyId);
        CredentialProfileList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        CredentialProfileList.ForEach(x => x.IsActive = true);

        CredentialProfileDto = fixture.Create<CredentialProfile>();
        CredentialProfileDto.CompanyId = CompanyId;
        CredentialProfileDto.ReferenceId = Guid.NewGuid().ToString();
        CredentialProfileDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
