﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraReplicationMapping.Commands;

public class CreateInfraReplicationMappingTests : IClassFixture<InfraReplicationMappingFixture>
{
    private readonly InfraReplicationMappingFixture _infraReplicationMappingFixture;
    private readonly Mock<IInfraReplicationMappingRepository> _mockInfraReplicationMappingRepository;
    private readonly Mock<IPublisher> _publisher;
    private readonly CreateInfraReplicationMappingCommandHandler _handler;

    public CreateInfraReplicationMappingTests(InfraReplicationMappingFixture infraReplicationMappingFixture)
    {
        _infraReplicationMappingFixture = infraReplicationMappingFixture;

        _publisher = new Mock<IPublisher>();

        _mockInfraReplicationMappingRepository = InfraReplicationMappingRepositoryMocks.CreateInfraReplicationMappingRepository(_infraReplicationMappingFixture.InfraReplicationMappings);

        _handler = new CreateInfraReplicationMappingCommandHandler(_infraReplicationMappingFixture.Mapper, _mockInfraReplicationMappingRepository.Object, _publisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseInfraReplicationMappingCount_When_InfraReplicationMappingCreated()
    {
        await _handler.Handle(_infraReplicationMappingFixture.CreateInfraReplicationMappingCommand, CancellationToken.None);

        var allCategories = await _mockInfraReplicationMappingRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_infraReplicationMappingFixture.InfraReplicationMappings.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateInfraReplicationMappingResponse_When_InfraReplicationMappingCreated()
    {
        var result = await _handler.Handle(_infraReplicationMappingFixture.CreateInfraReplicationMappingCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateInfraReplicationMappingResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_infraReplicationMappingFixture.CreateInfraReplicationMappingCommand, CancellationToken.None);

        _mockInfraReplicationMappingRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.InfraReplicationMapping>()), Times.Once);
    }
}