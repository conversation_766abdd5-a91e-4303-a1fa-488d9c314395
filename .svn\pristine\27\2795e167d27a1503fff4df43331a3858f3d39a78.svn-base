using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowOperationFixture : IDisposable
{
    public List<WorkflowOperation> WorkflowOperationPaginationList { get; set; }
    public List<WorkflowOperation> WorkflowOperationList { get; set; }
    public WorkflowOperation WorkflowOperationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowOperationFixture()
    {
        var fixture = new Fixture();

        WorkflowOperationList = fixture.Create<List<WorkflowOperation>>();

        WorkflowOperationPaginationList = fixture.CreateMany<WorkflowOperation>(20).ToList();

        WorkflowOperationPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowOperationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowOperationDto = fixture.Create<WorkflowOperation>();

        WorkflowOperationDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
