﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = 'DB2HADR';
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { db2hadrmonitorstatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
//setTimeout(() => { db2hadrServer(infraObjectId) }, 250)

//$('#mssqlserver').hide();
//async function db2hadrServer(id) {

//    let url = "/Monitor/Monitoring/GetMonitorServiceByInfraObjectId";
//    let data = {}
//    data.infraObjectId = id;
//    let mssqlServerData = await getAysncWithHandler(url, data);

//    if (mssqlServerData != null && mssqlServerData?.length > 0) {
//        mssqlServerData?.forEach(data => {
//            let value = data?.isServiceUpdate
//            let parsed = []
//            if (value && value !== 'NA') parsed = JSON?.parse(value)
//            if (Array.isArray(parsed)) {
//                parsed?.forEach(s => {
//                    if (s?.Services?.length) {
//                        $('#mssqlserver').show();
//                        bindDb2hadrServerServer(mssqlServerData)
//                    }
//                })
//            }
//        })

//    } else {
//        $('#mssqlserver').hide();
//    }

//}
//function bindDb2hadrServerServer(mssqlServerData) {

//    let prType = { IpAddress: '--', Services: [] };
//    let drType = { IpAddress: '--', Services: [] };

//    // Loop through each item to find PR and DR entries
//    mssqlServerData?.forEach(item => {
//        let parsedServices = [];
//        try {
//            const value = item?.isServiceUpdate
//            if (value && value !== 'NA') {
//                parsedServices = JSON.parse(item?.isServiceUpdate)
//            }
//        } catch (e) {
//            console.error("Invalid JSON in isServiceUpdate:", item?.isServiceUpdate);
//        }

//        parsedServices?.forEach(serviceGroup => {
//            if (serviceGroup?.Type === 'PR') {
//                prType.IpAddress = serviceGroup?.IpAddress || prType.IpAddress;
//                prType.Services = [...prType.Services, ...(serviceGroup?.Services || [])];
//            } else if (serviceGroup?.Type === 'DR') {
//                drType.IpAddress = serviceGroup?.IpAddress || drType?.IpAddress;
//                drType.Services = [...drType.Services, ...(serviceGroup?.Services || [])];
//            }
//        });
//    });

//    // Set header IPs
//    $('#prIp').text('Primary (' + prType?.IpAddress + ')');
//    $('#drIp').text('DR (' + drType?.IpAddress + ')');

//    const prStatusSummary = getStatusSummary(prType.Services.map(s => s.Status).filter(Boolean));
//    const drStatusSummary = getStatusSummary(drType.Services.map(s => s.Status).filter(Boolean));
//    $('#prIp').html(`Primary (${prType.IpAddress}) <br ><span class="status-summary text-muted small">${prStatusSummary}</span>`);
//    $('#drIp').html(`DR (${drType.IpAddress}) <br ><span class="status-summary text-muted small">${drStatusSummary}</span>`);

//    // Unique list of all service names from both PR and DR
//    let allServiceNames = [...new Set([
//        ...prType?.Services?.map(s => s?.ServiceName),
//        ...drType?.Services?.map(s => s?.ServiceName)
//    ])];

//    // Build table rows
//    let tbody = $('#mssqlserverbody');
//    tbody.empty();

//    allServiceNames?.forEach(serviceName => {
//        let prService = prType?.Services?.find(s => s?.ServiceName === serviceName);
//        let drService = drType?.Services?.find(s => s?.ServiceName === serviceName);

//        let prStatus = prService ? prService?.Status : '--';
//        let drStatus = drService ? drService?.Status : '--';
//        let prIcon = prStatus !== '--' ? `<i class="${getStatusIconClass(prStatus)} me-2 fs-6"></i>` : '';
//        let drIcon = drStatus !== '--' ? `<i class="${getStatusIconClass(drStatus)} me-2 fs-6"></i>` : '';
//        const iconService = serviceName === "NA" ? "text-danger cp-disable" : "text-secondary cp-monitoring-services";

//        let row = `
//            <tr>
//                <td><i class="${iconService} me-2 fs-6"></i><span>${serviceName}</span></td>
//                <td>${prIcon}${prStatus}</td>
//                <td>${drIcon}${drStatus}</td>
//            </tr>
//        `;
//        tbody.append(row);
//    });
//}
//function getStatusSummary(arr) {
//    let countMap = {};
//    arr?.forEach(status => {
//        countMap[status] = (countMap[status] || 0) + 1;
//    });
//    let total = arr?.length;
//    let statusSummary = Object.entries(countMap)
//        .map(([status, count]) => `${count} ${status}`)
//        .join(', ');
//    return statusSummary ? `${statusSummary} out of ${total} services` : '--';
//}
//function getStatusIconClass(status) {
//    if (!status) return "text-danger cp-disable";

//    const lowerStatus = status.toLowerCase();
//    if (lowerStatus === "running") {
//        return "text-success cp-reload cp-animate";
//    } else if (lowerStatus === "error" || lowerStatus === "stopped") {
//        return "text-danger cp-fail-back";
//    } else {
//        return "text-danger cp-disable";
//    }
//}
function db2hadrmonitorstatus(id, type) {
    $.ajax({
        url: "/Monitor/DB2HADRLinux/GetMonitorServiceStatusByIdAndType",
        method: 'GET',
        data: {
            monitorId: id,
            type: type,
        },
        dataType: 'json',
        async: true,
        success: function (data) {
            //console.log(data);
            infraDataa(data);
            propertiesDataa(data);
            let dataValue = JSON?.parse(data?.properties);
            // db2hadrSolutionDiagram(dataValue, data?.type);
        },
        error: function (error) {
          
        }
    });
}
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
function infraDataa(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}
function propertiesDataa(value) {
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html("NA");
    }
    else {
        let data = JSON?.parse(value?.properties);
        
        let customSite = data?.DB2HADRMonitoringModels?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }


        $(".siteContainer").empty();


        data?.DB2HADRMonitoringModels?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        });


        if (data?.DB2HADRMonitoringModels?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.DB2HADRMonitoringModels[0]);
        }



        let defaultSite = data?.DB2HADRMonitoringModels?.find(d => d?.Type === 'DR') || data?.DB2HADRMonitoringModels[0];
        if (defaultSite) {
            displaySiteData(defaultSite);
        }

        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0].id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.DB2HADRMonitoringModels?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel, getSiteName);
            }
            bindMonitoringServices(globalMSSQLServerData, getSiteName);
        });
        function displaySiteData(siteData) {
            let obj = {};
            $('.dynamicSite-header').text(siteData?.Type).attr('title', siteData?.Type);

            for (let key in siteData?.HADResult) {
                obj[key] = siteData?.HADResult[key];
            }

            let MonitoringModelPropDb2hadr = [
                "DRIp", "DRDatabaseInstance", "DRDatabaseStatus", "DRLogFile", "DRLSN", "DRTimestamp", "DRDatabaseVersion"
                , "Datalag", "DRDBSize"
            ];

            if (Object.keys(obj).length > 0) {
                bindProperties(obj, MonitoringModelPropDb2hadr, value);
            }
            let obj1 = {};
            for (let key in siteData?.HADRReplications) {
                obj1[key] = siteData?.HADRReplications[key];
            }

            let MonitoringModelPropDb2hadr1 = [
                "HRole", "State", "SyncMode", "ConnectionStatus", "HeartbeatsMissed", "LocalHost",
                "LocalService", "RemoteHost", "RemoteService", "Timeout", "LogGap"
            ];
            if (Object.keys(obj1)?.length > 0) {
                bindProperties(obj1, MonitoringModelPropDb2hadr1, value);
            }
        
        }

        let db2monitor = data?.MonitoringPRModel?.PRDB2HADRMonitoringModel?.PRHADResult
        
   
        const db2monitorProp = ["PRIp", "PRDatabaseInstance", "PRDatabaseStatus", "PRLogFile", "PRDatabaseVersion",
            "PRLSN", "PRTimestamp", "PRDBSize"
        ];
      
      
        //bindProperties(db2version);
        if (db2monitor !== '' && db2monitor !== null && db2monitor !== undefined) {
            bindProperties(db2monitor, db2monitorProp);
        } else {
            $("#db2monitor").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>DB2 Monitoring Details is not available</span>");
        }

        //Replication Monitoring
        let db2repli = data?.HADRReplications;
        const db2replicaMonitor = ["HRole", "State","SyncMode","ConnectionStatus", "HeartbeatsMissed", "LocalHost",
            "LocalService", "RemoteHost", "RemoteService", "Timeout", "LogGap"];

        if (db2repli !== '' && db2repli !== null && db2repli !== undefined) {
            bindProperties(db2repli, db2replicaMonitor);
        } else {
            $("#db2repli").css('text-align', 'center').html(noDataImage + "<br><span class='text-danger'>DB2 Replication Monitoring Details is not available</span>");
        }
        
        //DB Size
        //$('#PR_Dbsize').text(checkAndReplace(db2monitor.PRDBSize) + " MB").attr('title', checkAndReplace(db2monitor.PRDBSize));
        //$('#DR_Dbsize').text(checkAndReplace(db2monitor.DRDBSize) + " MB").attr('title', checkAndReplace(db2monitor.DRDBSize));

        //var prDb2sizeValue = checkAndReplace(db2monitor.PRDBSize);
        //var drDb2sizeValue = checkAndReplace(db2monitor.DRDBSize);

        //if (prDb2sizeValue === 'NA') {
        //    $('#PR_Dbsize').text(prDb2sizeValue).attr('title', prDb2sizeValue);
        //} else if (prDb2sizeValue?.includes("MB")) {
        //    $('#PR_Dbsize').text(prDb2sizeValue).attr('title', prDb2sizeValue);
        //}
        //else {
        //    $('#PR_Dbsize').text(prDb2sizeValue + " MB").attr('title', prDb2sizeValue + " MB");
        //}
        //if (drDb2sizeValue === 'NA') {
        //    $('#DR_Dbsize').text(drDb2sizeValue).attr('title', drDb2sizeValue);
        //}
        //else if (drDb2sizeValue?.includes("MB")) {
        //    $('#DR_Dbsize').text(drDb2sizeValue).attr('title', drDb2sizeValue);
        //} else {
        //    $('#DR_Dbsize').text(drDb2sizeValue + " MB").attr('title', drDb2sizeValue + " MB");
        //}
        // setPropData(data, [db2monitorProp, db2replicaMonitor]);
        const datalag = checkAndReplace(data?.PR_Datalag);
        let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : '00:00';

        var result = "";

        if (dataLagValue?.includes(".")) {
            var value = dataLagValue?.split(".");
            var hours = value[0] * 24;
            var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
            var min = minutes?.split(':');
            var firstValue = parseInt(min[0]) + parseInt(hours);

            result = firstValue + ":" + min[1];
           // $('#PR_Datalag').text(result).attr('title', result);
            const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

        }
        else {
            var value = dataLagValue?.includes("+") ? dataLagValue?.split(" ") : dataLagValue?.split(':')?.slice(0, 2)?.join(':');

            result = value.length === 5 ? value : value[1]?.split(':')?.slice(0, 2)?.join(':');
            const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
            const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
            //$('#PR_Datalag').text(result).attr('title', result);
            minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
        }

    }
}
function setPropData(data, propSets) {
    propSets.forEach(properties => {
        bindProperties(data, properties);
    });
}
function bindProperties(data, properties) {
    //console.log(data);
    //console.log(properties);

    const iconMapping = {
        "PRIp": "text-success cp-up-linearrow me-1",
        "DRIp": "text-success cp-up-linearrow me-1",
        "PRDatabaseInstance": "text-success cp-database-unique-name",
        "DRDatabaseInstance": "text-success cp-database-unique-name",
        //"PRDatabaseStatus": "text-success cp-dataguard-status",
        //"DRDatabaseStatus": "text-success cp-dataguard-status",
        "PRLogFile": "text-success cp-log-file-name",
        "DRLogFile": "text-success cp-log-file-name",
        "PRLSN": "text-success cp-monitoring",
        "DRLSN": "text-success cp-monitoring",
        "PRTimestamp": "text-success cp-time",
        "DRTimestamp": "text-success cp-time",
        "PRDatabaseVersion": "text-primary cp-database",
        "DRDatabaseVersion": "text-primary cp-database",      
        "HRole": "text-success cp-physical-drsite me-1 fs-6",
        "State": "text-success cp-relationship-state",
        "ConnectionStatus": "text-success cp-connection-sucess",
        "HeartbeatsMissed": "text-success cp-Job-status",
        "LocalHost": "text-success cp-host-name",
        "LocalService": "text-success cp-monitoring-services",
        "RemoteHost": "text-success cp-host-name",
        "RemoteService": "text-success cp-monitoring-services",
        "Timeout": "text-success cp-Timeout",
        "LogGap": "text-success cp-Timeout me-1",
        "SyncMode":"text-success cp-refresh"

    };
    properties.forEach(property => {
        const value = data[property];
        let displayedValue = value !== undefined ? checkAndReplace(value) : 'NA';
        let iconClass = iconMapping[property] || '';

        // Add icons based on conditions
        if (displayedValue === 'NA') {
            iconClass = 'text-danger cp-disable';
        }
        else if (displayedValue === 'Streaming') {
            iconClass = 'text-success cp-refresh';
        }
        else if (displayedValue?.toLowerCase()?.includes('active')) {
            iconClass = 'cp-success text-success';
        }
        else if (displayedValue?.toLowerCase()==='standby' || displayedValue?.toLowerCase()?.includes('inactive') || displayedValue?.toLowerCase().includes("quiesce")) {
            iconClass = 'text-danger cp-end';
        }
        else if (displayedValue?.toLowerCase()?.includes('running')) {
            iconClass = 'text-primary cp-thunder';
        }
        else if (displayedValue?.includes('stopped') || displayedValue?.includes('stop')) {
            iconClass = 'text-danger cp-Stopped';
        }
        else if (displayedValue?.includes('production')) {
            iconClass = 'text-warning cp-log-archive-config';
        }
        else if (displayedValue?.includes('archive recovery')) {
            iconClass = 'text-warning cp-log-archive-config';
        }
        else if (displayedValue === 'f' || displayedValue === 'false') {
            iconClass = 'text-danger cp-error';
        }
        else if (displayedValue === 't' || displayedValue === 'true') {
            iconClass = 'text-success cp-success';
        }
        // Displayed value with icon
        const iconHtml = iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
        const mergeValue = `${iconHtml}${displayedValue}`;
        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
    });
}


