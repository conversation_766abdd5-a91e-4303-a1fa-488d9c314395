﻿namespace ContinuityPatrol.Application.Features.ReplicationJob.Queries.IsNameUnique;

public class GetReplicationJobNameUniqueQueryHandler : IRequestHandler<GetReplicationJobNameUniqueQuery, bool>
{
    private readonly IReplicationJobRepository _replicationJobRepository;


    public GetReplicationJobNameUniqueQueryHandler(IReplicationJobRepository replicationJobRepository)
    {
        _replicationJobRepository = replicationJobRepository;
    }

    public async Task<bool> Handle(GetReplicationJobNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _replicationJobRepository.IsReplicationJobNameExist(request.Id, request.Name);
    }
}