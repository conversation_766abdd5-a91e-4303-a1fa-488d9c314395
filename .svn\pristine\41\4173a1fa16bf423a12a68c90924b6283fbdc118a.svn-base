﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-BIA-cost"></i>
                        <span>FIA Cost</span>
                    </h6>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%" id="fiaCost_table">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Business Function Name</th>
                        <th>Selected FIA Template</th>
                        <th>Business Service Name</th>
                        <th>Criticality Level</th>
                        <th>Configured RTO</th>
                        <th>Configured RPO</th>
                        <th>Manage Dependency Rules</th>
                        <th>View Report</th>
                        <th>View RTO</th>
                        <th>Perform FIA</th>
                    </tr>
                </thead>
                <tbody>
                    
                </tbody>
            </table>
            
        </div>
    </div>
    <!-- Perform Quantitative Financial Impact - Modal Start -->
    <div class="modal fade" id="PerformQuantitativeModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title"><i class="cp-BIA-cost"></i><span>Perform Quantitative Financial Impact</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height:25rem ">
                    <div class="d-flex align-items-center justify-content-between gap-3">
                        <div class="form-group w-100">
                            <div class="form-label">Impact Severity</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal" data-placeholder="Select Impact Severity" id="impactSeveritySelect">
                                    <option></option>
                                    <option value="3">Low</option>
                                    <option value="2">Major</option>
                                    <option value="1">Critical</option>
                                </select>
                            </div>
                            <span id="impactSeverity_error"></span>
                        </div>
                        <div class="form-group w-100">
                            <div class="form-label">Select FIA Templates</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-Impact"></i></span>
                                <select class="form-select-modal mb-0" data-placeholder="Select FIA Template" id="fiaTemplateSelect">
                                </select>
                            </div>
                            <span id="fiaTemplate_error"></span>
                        </div>
                    </div>
                    <div>
                        <table class="table table-info" id="FIATableContainer" style="display:none">
                            <thead>
                                <tr id="impactTableHeader">
                                   
                                </tr>
                            </thead>

                            @* <tbody>
                            <div class="accordion" id="accordionExample">
                            <div class="accordion-item">
                            <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            Financial Impact
                            </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                            <div class="accordion-body">

                            <tr>
                            <td>Productivity Loss</td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            </tr>
                            <tr>
                            <td>Productivity Loss</td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            </tr>
                            <tr>
                            <td>Productivity Loss</td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            </tr>
                            <tr>
                            <td>Productivity Loss</td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            <td><div class="input-group"><input type="text" class="form-control" /></div></td>
                            </tr>

                            </div>
                            </div>
                            </div>
                            </div>

                            </tbody> *@
                            <tbody id="impactTableContainer">
                              @*   <tr>
                                    <td colspan="5" class="shadow-none">
                                        <i class="cp-circle-plus text-primary me-1" role="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample"></i>
                                        Financial Impact
                                    </td>
                                </tr>
                                <tr class="collapse" id="collapseExample">
                                  
                                    <td>
                                        Major Impact
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" />
                                        </div>
                                    </td>
                                </tr> *@
                                @*  <tr>
                                <td colspan="3" class="p-0">
                                <div class="accordion" id="accordionExample">
                                <div class="accordion-item">
                                <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                Financial Impact
                                </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                                <div class="accordion-body">


                                <table class="table " style="vertical-align:bottom">
                                <tbody>
                                <tr>
                                <td>
                                Major Impact
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                </tr>
                                <tr>
                                <td>
                                Total Impact
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                </tr>
                                <tr>
                                <td>
                                Minor Impact
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                <td>
                                <div class="input-group">
                                <input type="text" class="form-control" />
                                </div>
                                </td>
                                </tr>
                                </tbody>
                                </table>
                                </div>
                                </div>
                                </div>
                                </div>
                                </td>
                                </tr> *@
                            </tbody>


                            <tfoot id="impactTableFooter">
                                @* <tr>
                                    <th scope="row">Total</th>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="0.00" disabled />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control" placeholder="0.00" disabled />
                                        </div>
                                    </td>
                                </tr> *@
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save" id="btnSaveFIACost">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- Perform Quantitative Financial Impact - Modal End -->
</div>
<script src="~/js/configuration/fia-bia/fia costs/fiacost.js"></script>