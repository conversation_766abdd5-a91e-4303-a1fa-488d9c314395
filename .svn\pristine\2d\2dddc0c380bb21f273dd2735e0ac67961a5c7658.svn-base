﻿namespace ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Update;

public class
    UpdateImpactAvailabilityCommandHandler : IRequestHandler<UpdateImpactAvailabilityCommand,
        UpdateImpactAvailabilityResponse>
{
    private readonly IImpactAvailabilityRepository _impactAvailabilityRepository;
    private readonly IMapper _mapper;

    public UpdateImpactAvailabilityCommandHandler(IImpactAvailabilityRepository impactAvailabilityRepository,
        IMapper mapper)
    {
        _impactAvailabilityRepository = impactAvailabilityRepository;
        _mapper = mapper;
    }

    public async Task<UpdateImpactAvailabilityResponse> Handle(UpdateImpactAvailabilityCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _impactAvailabilityRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ImpactAvailability), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateImpactAvailabilityCommand),
            typeof(Domain.Entities.ImpactAvailability));

        await _impactAvailabilityRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateImpactAvailabilityResponse
        {
            Message = Message.Update(nameof(Domain.Entities.BusinessServiceEvaluation), eventToUpdate.ReferenceId),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}