﻿using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetActionId;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowPredictionProfile : Profile
{
    public WorkflowPredictionProfile()
    {
        CreateMap<WorkflowPrediction, CreateWorkflowPredictionCommand>().ReverseMap();
        CreateMap<UpdateWorkflowPredictionCommand, WorkflowPrediction>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<WorkflowPrediction, WorkflowPredictionListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowPrediction, WorkflowPredictionListByNextPossibleIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowPrediction, WorkflowPredictionListByActionIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}