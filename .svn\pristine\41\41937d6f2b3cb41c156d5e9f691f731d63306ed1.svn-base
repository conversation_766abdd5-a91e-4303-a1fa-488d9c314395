﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Helper;
using UserRole = ContinuityPatrol.Shared.Core.Enums.UserRole;

namespace ContinuityPatrol.Persistence.Repositories;

public class UserActivityRepository : BaseRepository<UserActivity>, IUserActivityRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public UserActivityRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<UserActivity>> GetStartTimeEndTimeByUser(string userId, string createDate,
        string lastModifiedDate)        
    {
        var keyValuePairs = default(UserRole).ToDictionary();

        var userRole = _loggedInUserService.IsParent
            ? await _dbContext.UserActivities.Active()
                .Where(x => x.UserId.Equals(userId) && x.CreatedDate.Date >= createDate.ToDateTime() &&
                            x.CreatedDate.Date <= lastModifiedDate.ToDateTime())
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync()
            : await _dbContext.UserActivities.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.UserId.Equals(userId) &&
                            x.CreatedDate.Date >= createDate.ToDateTime() &&
                            x.CreatedDate.Date <= lastModifiedDate.ToDateTime())
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();

        var user = _dbContext.Users.Active()
            .AsNoTracking().FirstOrDefault(x => x.Role.Equals(keyValuePairs[UserRole.SiteAdmin.ToString()]));

        return userRole.Where(x => !x.UserId.Equals(user?.ReferenceId)).ToList();
    }

    public async Task<List<UserActivity>> ListAllUserActivityAsync(string createDate, string lastModifiedDate)
    {
        var keyValuePairs = default(UserRole).ToDictionary();

        var userRole = _loggedInUserService.IsParent
            ? await _dbContext.UserActivities.Active()
                .Where(x => x.CreatedDate.Date >= createDate.ToDateTime() &&
                            x.CreatedDate.Date <= lastModifiedDate.ToDateTime())
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync()
            : await _dbContext.UserActivities.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            x.CreatedDate.Date >= createDate.ToDateTime() &&
                            x.CreatedDate.Date <= lastModifiedDate.ToDateTime())
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();

        var user = _dbContext.Users.Active()
            .AsNoTracking().Where(x => x.Role.Equals(keyValuePairs[UserRole.SiteAdmin.ToString()]))
            .Select(x => x.ReferenceId);

        return userRole.Where(x => x.UserId != null && !user.Contains(x.UserId)).ToList();

        //return userRole.Where(x => x.UserId != null && !x.UserId.Equals(user?.ReferenceId)).ToList();
    }
    public async Task<List<UserActivity>> loginnameUserActivityAsync(string loginname, string createDate, string lastModifiedDate)
    {
        var keyValuePairs = default(UserRole).ToDictionary();
        if (!DateTime.TryParse(createDate, out DateTime startDate) ||
            !DateTime.TryParse(lastModifiedDate, out DateTime endDate))
        {
            return new List<UserActivity>();
        }
        endDate = endDate.AddDays(1);
        var siteAdminIds = (await _dbContext.Users.Active()
            .AsNoTracking()
            .Where(x => x.Role != null && x.Role.Contains(keyValuePairs[UserRole.SiteAdmin.ToString()]))
            .Select(x => x.ReferenceId)
            .ToListAsync())
            .ToHashSet();
        var query = _dbContext.UserActivities.Active().AsNoTracking()
            .Where(x => x.LoginName != null
                        && x.LoginName==loginname
                        && x.CreatedDate >= startDate
                        && x.CreatedDate < endDate);

        if (!_loggedInUserService.IsParent)
        {
            query = query.Where(x => x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId));
        }
        var userActivities = await query
            .OrderByDescending(x => x.CreatedDate)
            .ToListAsync();
        return userActivities.Where(x => x.UserId != null && !siteAdminIds.Contains(x.UserId)).ToList();
    }

    public async Task<List<UserActivity>> GetUserActivityByLoginName(string loginName)
    {
        var keyValuePairs = default(UserRole).ToDictionary();

        var userRole = _loggedInUserService.IsParent
            ? await _dbContext.UserActivities.Active()
                .Where(x =>x.LoginName!=null&& x.LoginName.Equals(loginName))
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync()
            : await _dbContext.UserActivities.Active()
                .Where(x =>x.CompanyId!=null && x.CompanyId.Equals(_loggedInUserService.CompanyId) &&x.LoginName!=null && x.LoginName.Equals(loginName))
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();

        //var user = _dbContext.Users.Active()
        //    .AsNoTracking().FirstOrDefault(x => x.Role.Equals(keyValuePairs[UserRole.SiteAdmin.ToString()]));
        var user = _dbContext.Users.Active()
            .AsNoTracking().Where(x => x.Role.Equals(keyValuePairs[UserRole.SiteAdmin.ToString()]))
            .Select(x => x.ReferenceId);

        return userRole.Where(x => x.UserId != null && !user.Contains(x.UserId)).ToList();
        //return userRole.Where(x => x.UserId != null && !x.UserId.Equals(user?.ReferenceId)).ToList();
    }
    public async Task<List<UserActivity>> GetloginUserActivityByLoginName(string loginName)
    {
        var keyValuePairs = default(UserRole).ToDictionary();
        var today = DateTime.Today;
        var siteAdminIds = (await _dbContext.Users.Active()
            .AsNoTracking()
            .Where(x => x.Role != null && x.Role.Contains(keyValuePairs[UserRole.SiteAdmin.ToString()])) // Use Contains instead of Equals
            .Select(x => x.ReferenceId)
            .ToListAsync())
            .ToHashSet();
        var query = _dbContext.UserActivities.Active().AsNoTracking()
            .Where(x => x.LoginName != null
                        && x.LoginName.Contains(loginName) 
                        && x.CreatedDate >= today
                        && x.CreatedDate < today.AddDays(1));
        if (!_loggedInUserService.IsParent)
        {
            query = query.Where(x => x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId));
        }
        var userActivities = await query
            .OrderByDescending(x => x.CreatedDate)
            .ToListAsync();

        return userActivities.Where(x => x.UserId != null && !siteAdminIds.Contains(x.UserId)).ToList();
    }

}