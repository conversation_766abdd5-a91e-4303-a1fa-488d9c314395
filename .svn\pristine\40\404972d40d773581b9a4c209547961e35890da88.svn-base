﻿using ContinuityPatrol.Application.Features.SmsConfiguration.Events.TestSms;

namespace ContinuityPatrol.Application.UnitTests.Features.SmsConfiguration.Events
{
    public class SmsConfigurationTestEventTests
    {
        private readonly Mock<ILogger<SmsConfigurationTestEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly SmsConfigurationTestEventHandler _handler;

        public SmsConfigurationTestEventTests()
        {
            _loggerMock = new Mock<ILogger<SmsConfigurationTestEventHandler>>();
            _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _handler = new SmsConfigurationTestEventHandler(
                _loggerMock.Object,
                _userActivityRepositoryMock.Object,
                _userServiceMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogAndAddUserActivity_WhenEventIsHandled()
        {
            var notification = new SmsConfigurationTestEvent
            {
                UserName = "TestUser"
            };
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");
            _userServiceMock.Setup(service => service.LoginName).Returns("TestLogin");
            _userServiceMock.Setup(service => service.RequestedUrl).Returns("http://test.com");
            _userServiceMock.Setup(service => service.CompanyId).Returns("67890");
            _userServiceMock.Setup(service => service.IpAddress).Returns("127.0.0.1");

            await _handler.Handle(notification, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "12345" &&
                activity.LoginName == "TestLogin" &&
                activity.RequestUrl == "http://test.com" &&
                activity.CompanyId == "67890" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Action == "TestSms SmsConfiguration" &&
                activity.Entity == "SmsConfiguration" &&
                activity.ActivityType == "TestSms" &&
                activity.ActivityDetails == "Test Sms sent successfully."
            )), Times.Once);

            _loggerMock.Verify(logger => logger.LogInformation("Test Sms sent successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenEventIsHandledSuccessfully()
        {
            var notification = new SmsConfigurationTestEvent
            {
                UserName = "TestUser"
            };
            var cancellationToken = CancellationToken.None;

            _userServiceMock.Setup(service => service.UserId).Returns("12345");

            await _handler.Handle(notification, cancellationToken);

            _userActivityRepositoryMock.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _loggerMock.Verify(logger => logger.LogInformation(It.IsAny<string>()), Times.Once);
        }
    }
}
