using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DataSyncOptionsControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DataSyncOptionsController _controller;
    private readonly DataSyncOptionsFixture _dataSyncOptionsFixture;

    public DataSyncOptionsControllerTests()
    {
        _dataSyncOptionsFixture = new DataSyncOptionsFixture();

        var testBuilder = new ControllerTestBuilder<DataSyncOptionsController>();
        _controller = testBuilder.CreateController(
            _ => new DataSyncOptionsController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDataSync_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dataSyncOptionsFixture.CreateDataSyncOptionsCommand;
        var expectedResponse = _dataSyncOptionsFixture.CreateDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSync(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncOptionsResponse>(createdResult.Value);
        Assert.Equal("Enterprise Real-time Sync Options created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDataSync_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dataSyncOptionsFixture.UpdateDataSyncOptionsCommand;
        var expectedResponse = _dataSyncOptionsFixture.UpdateDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSync(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncOptionsResponse>(okResult.Value);
        Assert.Equal("Enterprise Updated Sync Options updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDataSync_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSyncId = Guid.NewGuid().ToString();
        var expectedResponse = _dataSyncOptionsFixture.DeleteDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataSyncOptionsCommand>(c => c.Id == dataSyncId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataSync(dataSyncId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataSyncOptionsResponse>(okResult.Value);
        Assert.Equal("Enterprise Sync Options deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDataSyncs_ReturnsOkResult()
    {
        // Arrange
        var dataSyncList = new List<DataSyncOptionsListVm> { _dataSyncOptionsFixture.DataSyncOptionsListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncOptionsListQuery>(), default))
            .ReturnsAsync(dataSyncList);

        // Act
        var result = await _controller.GetDataSyncs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSyncOptionsListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise List Sync Options", returnedList.First().Name);
        Assert.Equal("Scheduled Synchronization", returnedList.First().ReplicationType);
        Assert.Contains("\"syncMode\": \"scheduled\"", returnedList.First().Properties);
    }

    [Fact]
    public async Task GetDataSyncById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSyncId = Guid.NewGuid().ToString();
        var dataSyncDetail = _dataSyncOptionsFixture.DataSyncOptionsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncOptionsDetailQuery>(q => q.Id == dataSyncId), default))
            .ReturnsAsync(dataSyncDetail);

        // Act
        var result = await _controller.GetDataSyncById(dataSyncId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataSyncOptionsDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Sync Options", returnedDetail.Name);
        Assert.Equal("Continuous Synchronization", returnedDetail.ReplicationType);
        Assert.True(returnedDetail.IsActive);
        Assert.Equal("Enterprise Admin", returnedDetail.CreatedBy);
        Assert.Equal("Enterprise Admin", returnedDetail.LastModifiedBy);
        Assert.Contains("\"syncMode\": \"continuous\"", returnedDetail.Properties);
    }

    [Fact]
    public async Task GetPaginatedDataSyncs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dataSyncOptionsFixture.GetDataSyncOptionsPaginatedListQuery;
        var paginatedResult = new PaginatedResult<DataSyncOptionsListVm>
        {
            Data = new List<DataSyncOptionsListVm> { _dataSyncOptionsFixture.DataSyncOptionsListVm },
            CurrentPage = 1,
            PageSize = 10,
            TotalCount = 1,
            TotalPages = 1,
           
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDataSyncs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSyncOptionsListVm>>(okResult.Value);
        Assert.Single(returnedResult.Data);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, returnedResult.PageSize);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.False(returnedResult.HasNextPage);
    }

    [Fact]
    public async Task IsDataSyncNameExist_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var dataSyncName = "Enterprise Test Sync Options";
        var dataSyncId = Guid.NewGuid().ToString();
        var nameExistsResult = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncOptionsNameUniqueQuery>(q => 
                q.Name == dataSyncName && q.Id == dataSyncId), default))
            .ReturnsAsync(nameExistsResult);

        // Act
        var result = await _controller.IsDataSyncNameExist(dataSyncName, dataSyncId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedResult);
    }

    #endregion

    #region Error Handling

    [Fact]
    public async Task GetDataSyncById_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDataSyncById(invalidId));
    }

    [Fact]
    public async Task DeleteDataSync_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDataSync(invalidId));
    }

    [Fact]
    public async Task IsDataSyncNameExist_WithNullOrWhiteSpaceName_ThrowsArgumentException()
    {
        // Arrange
        var emptyName = "";
        var validId = Guid.NewGuid().ToString();

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsDataSyncNameExist(emptyName, validId));
    }

    [Fact]
    public async Task CreateDataSync_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var command = _dataSyncOptionsFixture.CreateDataSyncOptionsCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Sync options configuration validation failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDataSync(command));
        Assert.Contains("Sync options configuration validation failed", exception.Message);
    }

    [Fact]
    public async Task UpdateDataSync_WhenMediatorThrowsNotFoundException_PropagatesException()
    {
        // Arrange
        var command = _dataSyncOptionsFixture.UpdateDataSyncOptionsCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("DataSyncOptions", command.Id));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDataSync(command));
        Assert.Contains("DataSyncOptions", exception.Message);
        Assert.Contains(command.Id, exception.Message);
    }

    #endregion

    #region ClearDataCache

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act
        _controller.ClearDataCache();

        // Assert - This test verifies the method executes without throwing exceptions
        // The actual cache clearing logic is tested in integration tests
        Assert.True(true);
    }

    #endregion

   

    [Fact]
    public async Task GetDataSyncs_HandlesEmptyList()
    {
        // Arrange
        var emptyList = new List<DataSyncOptionsListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncOptionsListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDataSyncs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSyncOptionsListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDataSync_HandlesAdvancedEnterpriseConfiguration()
    {
        // Arrange
        var advancedCommand = new CreateDataSyncOptionsCommand
        {
            Name = "Enterprise Advanced Multi-Protocol Sync Options",
            ReplicationType = "Hybrid Multi-Protocol Synchronization",
            Properties = @"{
                ""syncMode"": ""hybrid"",
                ""protocols"": [""TCP"", ""UDP"", ""HTTP/2"", ""WebSocket""],
                ""conflictResolution"": ""businessRuleBased"",
                ""compressionLevel"": ""adaptive"",
                ""encryptionEnabled"": true,
                ""encryptionAlgorithm"": ""AES-256-GCM"",
                ""batchSize"": 5000,
                ""retryAttempts"": 20,
                ""timeoutSeconds"": 900,
                ""checksumValidation"": true,
                ""deltaSync"": true,
                ""bandwidthThrottling"": ""intelligent"",
                ""loadBalancing"": {
                    ""enabled"": true,
                    ""algorithm"": ""roundRobin"",
                    ""healthCheck"": true
                },
                ""monitoring"": {
                    ""metricsEnabled"": true,
                    ""alerting"": true,
                    ""performanceTracking"": true
                },
                ""security"": {
                    ""authenticationRequired"": true,
                    ""certificateValidation"": true,
                    ""ipWhitelist"": [""10.0.0.0/8"", ""**********/12"", ""***********/16""]
                },
                ""optimization"": {
                    ""caching"": true,
                    ""prefetching"": true,
                    ""compression"": ""lz4"",
                    ""deduplication"": true
                }
            }"
        };

        var expectedResponse = new CreateDataSyncOptionsResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "Enterprise Advanced Multi-Protocol Sync Options created successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(advancedCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSync(advancedCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncOptionsResponse>(createdResult.Value);

        Assert.Equal("Enterprise Advanced Multi-Protocol Sync Options created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate advanced configuration
        Assert.Equal("Enterprise Advanced Multi-Protocol Sync Options", advancedCommand.Name);
        Assert.Equal("Hybrid Multi-Protocol Synchronization", advancedCommand.ReplicationType);

        // Validate JSON properties contain expected advanced features
        Assert.Contains("\"syncMode\": \"hybrid\"", advancedCommand.Properties);
        Assert.Contains("\"protocols\":", advancedCommand.Properties);
        Assert.Contains("\"TCP\"", advancedCommand.Properties);
        Assert.Contains("\"HTTP/2\"", advancedCommand.Properties);
        Assert.Contains("\"WebSocket\"", advancedCommand.Properties);
        Assert.Contains("\"conflictResolution\": \"businessRuleBased\"", advancedCommand.Properties);
        Assert.Contains("\"encryptionAlgorithm\": \"AES-256-GCM\"", advancedCommand.Properties);
        Assert.Contains("\"loadBalancing\":", advancedCommand.Properties);
        Assert.Contains("\"monitoring\":", advancedCommand.Properties);
        Assert.Contains("\"security\":", advancedCommand.Properties);
        Assert.Contains("\"optimization\":", advancedCommand.Properties);
        Assert.Contains("\"deduplication\": true", advancedCommand.Properties);
    }

    [Fact]
    public async Task GetPaginatedDataSyncs_HandlesMultipleSyncOptionsWithFiltering()
    {
        // Arrange
        var complexQuery = new GetDataSyncOptionsPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 15,
            SearchString = "Enterprise Real-time",
            SortColumn = "Name",
            SortOrder="ReplicationType DESC"
        };

        var multipleSyncOptions = new PaginatedResult<DataSyncOptionsListVm>
        {
            Data = new List<DataSyncOptionsListVm>
            {
                new DataSyncOptionsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Real-time Primary Sync Options",
                    ReplicationType = "Real-time Bidirectional",
                    Properties = @"{""syncMode"": ""realtime"", ""priority"": ""high""}"
                },
                new DataSyncOptionsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Real-time Secondary Sync Options",
                    ReplicationType = "Real-time Unidirectional",
                    Properties = @"{""syncMode"": ""realtime"", ""priority"": ""medium""}"
                },
                new DataSyncOptionsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Real-time Backup Sync Options",
                    ReplicationType = "Real-time Cascading",
                    Properties = @"{""syncMode"": ""realtime"", ""priority"": ""low""}"
                }
            },
            CurrentPage = 2,
            PageSize = 15,
            TotalCount = 45,
            TotalPages = 3,
           
        };

        _mediatorMock
            .Setup(m => m.Send(complexQuery, default))
            .ReturnsAsync(multipleSyncOptions);

        // Act
        var result = await _controller.GetPaginatedDataSyncs(complexQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSyncOptionsListVm>>(okResult.Value);

        Assert.Equal(3, returnedResult.Data.Count);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(15, returnedResult.PageSize);
        Assert.Equal(45, returnedResult.TotalCount);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.True(returnedResult.HasNextPage);
        Assert.True(returnedResult.HasPreviousPage);

        // Validate filtered results
        Assert.All(returnedResult.Data, syncOption =>
            Assert.Contains("Enterprise Real-time", syncOption.Name));

        // Validate sync option types
        Assert.Contains(returnedResult.Data, s => s.ReplicationType.Contains("Bidirectional"));
        Assert.Contains(returnedResult.Data, s => s.ReplicationType.Contains("Unidirectional"));
        Assert.Contains(returnedResult.Data, s => s.ReplicationType.Contains("Cascading"));

        // Validate all are real-time
        Assert.All(returnedResult.Data, syncOption =>
            Assert.Contains("\"syncMode\": \"realtime\"", syncOption.Properties));

        // Validate priority distribution
        Assert.Contains(returnedResult.Data, s => s.Properties.Contains("\"priority\": \"high\""));
        Assert.Contains(returnedResult.Data, s => s.Properties.Contains("\"priority\": \"medium\""));
        Assert.Contains(returnedResult.Data, s => s.Properties.Contains("\"priority\": \"low\""));
    }

    [Fact]
    public async Task IsDataSyncNameExist_HandlesComplexNameValidation()
    {
        // Arrange
        var complexSyncName = "Enterprise Multi-Site Real-time Bidirectional Sync Options with Advanced Conflict Resolution";
        var syncId = Guid.NewGuid().ToString();
        var nameExistsResult = true; // Name already exists

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncOptionsNameUniqueQuery>(q =>
                q.Name == complexSyncName && q.Id == syncId), default))
            .ReturnsAsync(nameExistsResult);

        // Act
        var result = await _controller.IsDataSyncNameExist(complexSyncName, syncId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedResult = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedResult); // Name exists

        // Validate complex name handling
        Assert.True(complexSyncName.Length > 50); // Long name
        Assert.Contains("Multi-Site", complexSyncName);
        Assert.Contains("Real-time", complexSyncName);
        Assert.Contains("Bidirectional", complexSyncName);
        Assert.Contains("Advanced Conflict Resolution", complexSyncName);
    }

    [Fact]
    public async Task UpdateDataSync_HandlesDisasterRecoveryConfiguration()
    {
        // Arrange
        var drCommand = new UpdateDataSyncOptionsCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Enterprise Disaster Recovery Sync Options",
            ReplicationType = "Disaster Recovery Synchronization",
            Properties = @"{
                ""syncMode"": ""disasterRecovery"",
                ""conflictResolution"": ""drSiteWins"",
                ""compressionLevel"": ""maximum"",
                ""encryptionEnabled"": true,
                ""batchSize"": 10000,
                ""retryAttempts"": 50,
                ""timeoutSeconds"": 1800,
                ""checksumValidation"": true,
                ""deltaSync"": false,
                ""bandwidthThrottling"": ""none"",
                ""drConfiguration"": {
                    ""rpoMinutes"": 15,
                    ""rtoMinutes"": 60,
                    ""failoverAutomatic"": true,
                    ""failbackManual"": true,
                    ""healthCheckInterval"": 30,
                    ""replicationLag"": 5
                },
                ""monitoring"": {
                    ""alertOnFailure"": true,
                    ""alertOnLag"": true,
                    ""alertThresholdMinutes"": 10,
                    ""dashboardEnabled"": true
                },
                ""testing"": {
                    ""scheduledTests"": true,
                    ""testFrequency"": ""monthly"",
                    ""automatedValidation"": true
                }
            }"
        };

        var expectedResponse = new UpdateDataSyncOptionsResponse
        {
            Id = drCommand.Id,
            Message = "Enterprise Disaster Recovery Sync Options updated successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(drCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSync(drCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncOptionsResponse>(okResult.Value);

        Assert.Equal("Enterprise Disaster Recovery Sync Options updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate DR configuration
        Assert.Equal("Enterprise Disaster Recovery Sync Options", drCommand.Name);
        Assert.Equal("Disaster Recovery Synchronization", drCommand.ReplicationType);

        // Validate DR-specific properties
        Assert.Contains("\"syncMode\": \"disasterRecovery\"", drCommand.Properties);
        Assert.Contains("\"conflictResolution\": \"drSiteWins\"", drCommand.Properties);
        Assert.Contains("\"drConfiguration\":", drCommand.Properties);
        Assert.Contains("\"rpoMinutes\": 15", drCommand.Properties);
        Assert.Contains("\"rtoMinutes\": 60", drCommand.Properties);
        Assert.Contains("\"failoverAutomatic\": true", drCommand.Properties);
        Assert.Contains("\"failbackManual\": true", drCommand.Properties);
        Assert.Contains("\"monitoring\":", drCommand.Properties);
        Assert.Contains("\"alertOnFailure\": true", drCommand.Properties);
        Assert.Contains("\"testing\":", drCommand.Properties);
        Assert.Contains("\"scheduledTests\": true", drCommand.Properties);
        Assert.Contains("\"automatedValidation\": true", drCommand.Properties);
    }


    [Fact]
    public async Task CreateDataSyncOptions_HandlesCloudSyncConfiguration()
    {
        // Arrange
        var cloudSyncCommand = _dataSyncOptionsFixture.CreateDataSyncOptionsCommand;
        cloudSyncCommand.Name = "Enterprise Cloud Sync Configuration";
        cloudSyncCommand.ReplicationType = "CLOUD_SYNC";
        cloudSyncCommand.Properties = @"{
            ""cloudProvider"": ""Azure"",
            ""region"": ""East US 2"",
            ""storageAccount"": ""enterprisestorage"",
            ""encryptionEnabled"": true,
            ""compressionLevel"": ""high"",
            ""retentionDays"": 365
        }";

        var expectedResponse = _dataSyncOptionsFixture.CreateDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(cloudSyncCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSync(cloudSyncCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncOptionsResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Cloud Sync Configuration", cloudSyncCommand.Name);
        Assert.Equal("CLOUD_SYNC", cloudSyncCommand.ReplicationType);
        Assert.Contains("Azure", cloudSyncCommand.Properties);
        Assert.Contains("encryptionEnabled", cloudSyncCommand.Properties);
    }

    [Fact]
    public async Task GetDataSyncOptionsList_HandlesEmptyConfiguration()
    {
        // Arrange
        var emptyOptionsList = new List<DataSyncOptionsListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncOptionsListQuery>(), default))
            .ReturnsAsync(emptyOptionsList);

        // Act
        var result = await _controller.GetDataSyncs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DataSyncOptionsListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task UpdateDataSyncOptions_HandlesSecurityConfigurationUpdate()
    {
        // Arrange
        var securityUpdateCommand = _dataSyncOptionsFixture.UpdateDataSyncOptionsCommand;
        securityUpdateCommand.Name = "Enhanced Security Sync Options";
        securityUpdateCommand.ReplicationType = "SECURE_REPLICATION";
        securityUpdateCommand.Properties = @"{
            ""encryption"": {
                ""algorithm"": ""AES-256"",
                ""keyRotationDays"": 30,
                ""certificateValidation"": true
            },
            ""authentication"": {
                ""method"": ""OAuth2"",
                ""tokenExpiry"": 3600,
                ""refreshTokenEnabled"": true
            },
            ""audit"": {
                ""logLevel"": ""detailed"",
                ""retentionDays"": 90,
                ""complianceMode"": true
            }
        }";

        var expectedResponse = _dataSyncOptionsFixture.UpdateDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(securityUpdateCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSync(securityUpdateCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncOptionsResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enhanced Security Sync Options", securityUpdateCommand.Name);
        Assert.Equal("SECURE_REPLICATION", securityUpdateCommand.ReplicationType);
        Assert.Contains("AES-256", securityUpdateCommand.Properties);
        Assert.Contains("OAuth2", securityUpdateCommand.Properties);
        Assert.Contains("complianceMode", securityUpdateCommand.Properties);
    }

    [Fact]
    public async Task GetPaginatedDataSyncOptions_HandlesAdvancedFiltering()
    {
        // Arrange
        var advancedFilterQuery = new GetDataSyncOptionsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 15,
            SearchString = "Enterprise",
            SortColumn = "ReplicationType",
            SortOrder = "Name ASC"
        };

        var filteredResults = new PaginatedResult<DataSyncOptionsListVm>
        {
            Data = new List<DataSyncOptionsListVm>
            {
                new DataSyncOptionsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Backup Sync",
                    Properties = @"{
            ""cloudProvider"": ""Azure"",
            ""region"": ""East US 2"",
            ""storageAccount"": ""enterprisestorage"",
            ""encryptionEnabled"": true,
            ""compressionLevel"": ""high"",
            ""retentionDays"": 365
        }",
                    ReplicationType = "BACKUP_REPLICATION"
                },
                new DataSyncOptionsListVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Enterprise Cloud Sync",
                    Properties = @"{
            ""cloudProvider"": ""Azure"",
            ""region"": ""East US 2"",
            ""storageAccount"": ""enterprisestorage"",
            ""encryptionEnabled"": true,
            ""compressionLevel"": ""high"",
            ""retentionDays"": 365
        }",
                    ReplicationType = "CLOUD_SYNC"
                }
            },
            CurrentPage = 1,
            PageSize = 15,
            TotalCount = 2,
            TotalPages = 1,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(advancedFilterQuery, default))
            .ReturnsAsync(filteredResults);

        // Act
        var result = await _controller.GetPaginatedDataSyncs(advancedFilterQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSyncOptionsListVm>>(okResult.Value);

        Assert.Equal(2, returnedResult.Data.Count);
        Assert.All(returnedResult.Data, option =>
            Assert.Contains("Enterprise", option.Name));
        Assert.Contains(returnedResult.Data, o => o.ReplicationType == "BACKUP_REPLICATION");
        Assert.Contains(returnedResult.Data, o => o.ReplicationType == "CLOUD_SYNC");
        Assert.True(returnedResult.Succeeded);
    }

    [Fact]
    public async Task GetDataSyncOptionsDetail_HandlesComplexConfiguration()
    {
        // Arrange
        var optionId = Guid.NewGuid().ToString();
        var complexDetail = _dataSyncOptionsFixture.DataSyncOptionsDetailVm;
        complexDetail.Name = "Complex Enterprise Sync Configuration";
        complexDetail.Properties = @"{
            ""syncMode"": ""disasterRecovery"",
            ""priority"": ""high"",
            ""retentionDays"": 30,
            ""compressionEnabled"": true,
            ""encryptionLevel"": ""AES256""
        }";
        complexDetail.ReplicationType = "HYBRID_REPLICATION";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncOptionsDetailQuery>(q => q.Id == optionId), default))
            .ReturnsAsync(complexDetail);

        // Act
        var result = await _controller.GetDataSyncById(optionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataSyncOptionsDetailVm>(okResult.Value);

        Assert.Equal("Complex Enterprise Sync Configuration", returnedDetail.Name);
        Assert.Contains("\"syncMode\": \"disasterRecovery\"", returnedDetail.Properties);
        Assert.Contains("\"priority\": \"high\"", returnedDetail.Properties);
        Assert.Contains("\"encryptionLevel\": \"AES256\"", returnedDetail.Properties);
        Assert.Equal("HYBRID_REPLICATION", returnedDetail.ReplicationType);
    }

    [Fact]
    public async Task DeleteDataSyncOptions_HandlesConfigurationCleanup()
    {
        // Arrange
        var optionId = Guid.NewGuid().ToString();
        var expectedResponse = _dataSyncOptionsFixture.DeleteDataSyncOptionsResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataSyncOptionsCommand>(c => c.Id == optionId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataSync(optionId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataSyncOptionsResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Contains("successfully", returnedResponse.Message);
    }

   
}
