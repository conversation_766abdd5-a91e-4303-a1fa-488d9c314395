using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SingleSignOnFixture : IDisposable
{
    public List<SingleSignOn> SingleSignOnPaginationList { get; set; }
    public List<SingleSignOn> SingleSignOnList { get; set; }
    public SingleSignOn SingleSignOnDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SingleSignOnFixture()
    {
        var fixture = new Fixture();

        SingleSignOnList = fixture.Create<List<SingleSignOn>>();

        SingleSignOnPaginationList = fixture.CreateMany<SingleSignOn>(20).ToList();

        SingleSignOnPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SingleSignOnList.ForEach(x => x.CompanyId = CompanyId);

        SingleSignOnDto = fixture.Create<SingleSignOn>();

        SingleSignOnDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
