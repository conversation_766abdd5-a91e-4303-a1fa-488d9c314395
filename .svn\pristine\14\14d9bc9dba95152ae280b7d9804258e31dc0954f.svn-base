using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;

public class
    GetDriftProfilePaginatedListQueryHandler : IRequestHandler<GetDriftProfilePaginatedListQuery,
        PaginatedResult<DriftProfileListVm>>
{
    private readonly IDriftProfileRepository _driftProfileRepository;
    private readonly IMapper _mapper;

    public GetDriftProfilePaginatedListQueryHandler(IMapper mapper, IDriftProfileRepository driftProfileRepository)
    {
        _mapper = mapper;
        _driftProfileRepository = driftProfileRepository;
    }

    public async Task<PaginatedResult<DriftProfileListVm>> Handle(GetDriftProfilePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DriftProfileFilterSpecification(request.SearchString);

        var queryable =await _driftProfileRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var driftProfileList = _mapper.Map<PaginatedResult<DriftProfileListVm>>(queryable);
            
        return driftProfileList;
        //var queryable = _driftProfileRepository.PaginatedListAllAsync();

        //var productFilterSpec = new DriftProfileFilterSpecification(request.SearchString);

        //var driftProfileList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DriftProfileListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return driftProfileList;
    }
}