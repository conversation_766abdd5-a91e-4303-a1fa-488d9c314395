using ContinuityPatrol.Application.Features.DRCalendar.Commands.Create;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Delete;
using ContinuityPatrol.Application.Features.DRCalendar.Commands.Update;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetList;
using ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DrCalendarFixture : IDisposable
{
    public CreateDrCalendarCommand CreateDrCalendarCommand { get; }
    public CreateDrCalendarResponse CreateDrCalendarResponse { get; }
    public UpdateDrCalendarCommand UpdateDrCalendarCommand { get; }
    public UpdateDrCalendarResponse UpdateDrCalendarResponse { get; }
    public DeleteDrCalendarCommand DeleteDrCalendarCommand { get; }
    public DeleteDrCalendarResponse DeleteDrCalendarResponse { get; }
    public GetDrCalendarDetailsQuery GetDrCalendarDetailQuery { get; }
    public DrCalendarDetailVm DrCalendarDetailVm { get; }
    public GetDrCalendarListQuery GetDrCalendarListQuery { get; }
    public List<DrCalendarActivityListVm> DrCalendarListVm { get; }
    public GetDrCalendarPaginatedListQuery GetDrCalendarPaginatedListQuery { get; }
    public PaginatedResult<DrCalendarActivityListVm> DrCalendarPaginatedResult { get; }

    public DrCalendarFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise DR calendar scenarios
        fixture.Customize<CreateDrCalendarCommand>(c => c
            .With(x => x.ActivityName, "Enterprise DR Calendar Activity")
            .With(x => x.Description, "Enterprise disaster recovery calendar activity for critical systems")
            .With(x => x.ScheduledStartDate, DateTime.UtcNow.AddDays(7))
            .With(x => x.ScheduledEndDate, DateTime.UtcNow.AddDays(8))
            .With(x => x.ActivityType, "DR_DRILL")
            .With(x => x.ActivityStatus, "SCHEDULED")
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.CompanyId, Guid.NewGuid().ToString())
            .With(x => x.Location, "Enterprise Data Center")
            .With(x => x.Responsibility, "IT Operations Team")
            .With(x => x.InvitationNo, 1)
            .With(x => x.RecipientTwo, "<EMAIL>")
            .With(x => x.SetReminders, "24_hours")
            .With(x => x.WorkflowProfiles, "Enterprise_DR_Profile"));

        fixture.Customize<CreateDrCalendarResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Calendar event created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDrCalendarCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ActivityName, "Enterprise DR Calendar Activity Update")
            .With(x => x.Description, "Updated enterprise disaster recovery calendar activity")
            .With(x => x.ScheduledStartDate, DateTime.UtcNow.AddDays(14))
            .With(x => x.ScheduledEndDate, DateTime.UtcNow.AddDays(15))
            .With(x => x.ActivityType, "DR_TEST")
            .With(x => x.ActivityStatus, "RESCHEDULED")
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.CompanyId, Guid.NewGuid().ToString())
            .With(x => x.Location, "Enterprise Backup Data Center")
            .With(x => x.Responsibility, "Database Team")
            .With(x => x.InvitationNo, 2)
            .With(x => x.RecipientTwo, "<EMAIL>")
            .With(x => x.SetReminders, "12_hours")
            .With(x => x.WorkflowProfiles, "Enterprise_DB_Profile"));

        fixture.Customize<UpdateDrCalendarResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise DR Calendar event updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDrCalendarResponse>(c => c
            .With(x => x.Message, "Enterprise DR Calendar event deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DrCalendarDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ActivityName, "Enterprise DR Calendar Detail Activity")
            .With(x => x.Description, "Detailed enterprise disaster recovery calendar activity")
            .With(x => x.ScheduledStartDate, DateTime.UtcNow.AddDays(21))
            .With(x => x.ScheduledEndDate, DateTime.UtcNow.AddDays(22))
            .With(x => x.ActivityType, "DR_DRILL")
            .With(x => x.ActivityStatus, "SCHEDULED")
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.Location, "Enterprise Detail Data Center")
            .With(x => x.Responsibility, "Enterprise Operations Team")
            .With(x => x.InvitationNo, 3)
            .With(x => x.RecipientTwo, "<EMAIL>")
            .With(x => x.SetReminders, "48_hours")
            .With(x => x.WorkflowProfiles, "Enterprise_Operations_Profile"));

        fixture.Customize<DrCalendarActivityListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.ActivityName, "Enterprise DR Calendar List Activity")
            .With(x => x.Description, "Enterprise DR calendar list activity")
            .With(x => x.ScheduledStartDate, DateTime.UtcNow.AddDays(28))
            .With(x => x.ScheduledEndDate, DateTime.UtcNow.AddDays(29))
            .With(x => x.ActivityType, "DR_TEST")
            .With(x => x.ActivityStatus, "SCHEDULED")
            .With(x => x.BusinessServiceId, Guid.NewGuid().ToString())
            .With(x => x.Location, "Enterprise List Data Center")
            .With(x => x.Responsibility, "Enterprise List Team"));

        fixture.Customize<GetDrCalendarPaginatedListQuery>(c => c
            .With(x => x.SearchString, "Enterprise DR")
            .With(x => x.PageNumber, 1)
            .With(x => x.PageSize, 10)
            .With(x => x.SortColumn, "ScheduledStartDate")
            .With(x => x.SortOrder, "ASC"));

        // Create instances
        CreateDrCalendarCommand = fixture.Create<CreateDrCalendarCommand>();
        CreateDrCalendarResponse = fixture.Create<CreateDrCalendarResponse>();

        UpdateDrCalendarCommand = fixture.Create<UpdateDrCalendarCommand>();
        UpdateDrCalendarResponse = fixture.Create<UpdateDrCalendarResponse>();

        DeleteDrCalendarCommand = new DeleteDrCalendarCommand { Id = Guid.NewGuid().ToString() };
        DeleteDrCalendarResponse = fixture.Create<DeleteDrCalendarResponse>();

        GetDrCalendarDetailQuery = new GetDrCalendarDetailsQuery { Id = Guid.NewGuid().ToString() };
        DrCalendarDetailVm = fixture.Create<DrCalendarDetailVm>();

        GetDrCalendarListQuery = new GetDrCalendarListQuery();
        DrCalendarListVm = fixture.CreateMany<DrCalendarActivityListVm>(5).ToList();

        GetDrCalendarPaginatedListQuery = fixture.Create<GetDrCalendarPaginatedListQuery>();
        DrCalendarPaginatedResult = new PaginatedResult<DrCalendarActivityListVm>
        {
            Data = fixture.CreateMany<DrCalendarActivityListVm>(8).ToList(),
            TotalCount = 8,
            PageSize = 10,
            Succeeded = true
        };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
