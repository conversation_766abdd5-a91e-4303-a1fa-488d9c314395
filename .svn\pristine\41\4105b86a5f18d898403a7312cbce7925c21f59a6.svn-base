﻿namespace ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetAirGapsStatus;

public class GetAirGapsStatusListQueryHandler : IRequestHandler<GetAirGapsStatusListQuery, List<GetAirGapsStatusListVm>>
{
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly ICyberComponentRepository _cyberComponentRepository;
    private readonly IMapper _mapper;
    private readonly IServerViewRepository _serverViewRepository;

    public GetAirGapsStatusListQueryHandler(IMapper mapper, IServerViewRepository serverViewRepository,
        ICyberAirGapRepository cyberAirGapRepository, ICyberComponentRepository cyberComponentRepository)
    {
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
        _cyberAirGapRepository = cyberAirGapRepository;
        _cyberComponentRepository = cyberComponentRepository;
    }

    public async Task<List<GetAirGapsStatusListVm>> Handle(GetAirGapsStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var airGaps = await _cyberAirGapRepository.ListAllAsync();

        var mapAirGaps = _mapper.Map<List<GetAirGapsStatusListVm>>(airGaps);

        var allSourceIds = mapAirGaps.Where(x => x.Source.IsNotNullOrWhiteSpace())
                              .SelectMany(airGap => GetAirGapJsonObjectValue(airGap.Source))
                              .Distinct()
                              .ToList();

        var allTargetIds = mapAirGaps.Where(x => x.Target.IsNotNullOrWhiteSpace())
                                      .SelectMany(airGap => GetAirGapJsonObjectValue(airGap.Target))
                                      .Distinct()
                                      .ToList();
       
        var sourceServers = allSourceIds.Any() ? await _serverViewRepository.GetByServerIdsAsync(allSourceIds)
                                                : new List<Domain.Views.ServerView>();

        var targetServers = allTargetIds.Any() ? await _serverViewRepository.GetByServerIdsAsync(allTargetIds)
                                                : new List<Domain.Views.ServerView>();
        
        mapAirGaps.ForEach(airGap =>
        {
            if (airGap is not null)
            {
               
                if (airGap.Source.IsNotNullOrWhiteSpace())
                {
                    var sourceIds = GetAirGapJsonObjectValue(airGap.Source);
                    var relevantServers = sourceServers.Where(s => sourceIds.Contains(s.ReferenceId)).ToList();
                    if (relevantServers.Any())
                    {
                        var mapSourceServers = _mapper.Map<List<GetAirGapsServeListVm>>(relevantServers);
                        mapSourceServers.ForEach(x => x.Type = "Source");
                        airGap.GetAirGapsServeListVms.AddRange(mapSourceServers);
                    }
                }

                
                if (airGap.Target.IsNotNullOrWhiteSpace())
                {
                    var targetIds = GetAirGapJsonObjectValue(airGap.Target);
                    var relevantServers = targetServers.Where(s => targetIds.Contains(s.ReferenceId)).ToList();
                    if (relevantServers.Any())
                    {
                        var mapTargetServers = _mapper.Map<List<GetAirGapsServeListVm>>(relevantServers);
                        mapTargetServers.ForEach(x => x.Type = "Target");
                        airGap.GetAirGapsServeListVms.AddRange(mapTargetServers);
                    }
                }
            }
        });

        return mapAirGaps;
    }
    private List<string> GetAirGapJsonObjectValue(string airGapSource)
    {
        var result = new List<string>();
        try
        {
            return JObject.Parse(airGapSource)
                        .Properties()
                        .SelectMany(prop => prop.Value is JArray array
                            ? array.Where(obj => obj["id"] != null)
                            : Enumerable.Empty<JToken>())
                        .Select(obj => obj["id"]?.ToString())
                        .Where(id => !string.IsNullOrWhiteSpace(id))
                        .ToList();
        }
        catch
        {
            try
            {
                result = JArray.Parse(airGapSource).Where(item => item is JObject && item["id"] != null).Select(item => item["id"]?.ToString()).Where(id => !string.IsNullOrWhiteSpace(id)).ToList();

                return result;
            }
            catch
            {
                return result;
            }
        }

    }
}