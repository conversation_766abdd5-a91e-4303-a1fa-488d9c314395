using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DynamicSubDashboard.Events.Update;

public class DynamicSubDashboardUpdatedEventHandler : INotificationHandler<DynamicSubDashboardUpdatedEvent>
{
    private readonly ILogger<DynamicSubDashboardUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DynamicSubDashboardUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<DynamicSubDashboardUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(DynamicSubDashboardUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DynamicSubDashboard",
            Entity = "DynamicSubDashboard",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DynamicSubDashboard '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DynamicSubDashboard '{updatedEvent.Name}' updated successfully.");
    }
}