using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SvcMsSqlMonitorStatusFixture : IDisposable
{
    public List<SvcMsSqlMonitorStatus> SvcMsSqlMonitorStatusPaginationList { get; set; }
    public List<SvcMsSqlMonitorStatus> SvcMsSqlMonitorStatusList { get; set; }
    public SvcMsSqlMonitorStatus SvcMsSqlMonitorStatusDto { get; set; }


    public ApplicationDbContext DbContext { get; private set; }

    public SvcMsSqlMonitorStatusFixture()
    {
        var fixture = new Fixture();

        SvcMsSqlMonitorStatusList = fixture.Create<List<SvcMsSqlMonitorStatus>>();

        SvcMsSqlMonitorStatusPaginationList = fixture.CreateMany<SvcMsSqlMonitorStatus>(20).ToList();

      SvcMsSqlMonitorStatusDto = fixture.Create<SvcMsSqlMonitorStatus>();


        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatus(
        string type = "MSSQL_REPLICATION",
        string infraObjectId = "INFRA_001",
        string infraObjectName = "Default MSSQL Object",
        string workflowId = "WF_001",
        string workflowName = "Default Workflow",
        string properties = null,
        string configuredRPO = "15",
        string dataLagValue = "5",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SvcMsSqlMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type,
            InfraObjectId = infraObjectId,
            InfraObjectName = infraObjectName,
            WorkflowId = workflowId,
            WorkflowName = workflowName,
            Properties = properties ?? "{\"rpo\": \"15\", \"status\": \"active\", \"lastSync\": \"2024-01-01T10:00:00Z\"}",
            ConfiguredRPO = configuredRPO,
            DataLagValue = dataLagValue,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SvcMsSqlMonitorStatus> CreateMultipleSvcMsSqlMonitorStatuses(int count, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SvcMsSqlMonitorStatus>();
        for (int i = 1; i <= count; i++)
        {
            statuses.Add(CreateSvcMsSqlMonitorStatus(
                type: $"MSSQL_TYPE_{i}",
                infraObjectId: $"{infraObjectId}_{i}",
                infraObjectName: $"MSSQL Object {i}",
                workflowId: $"WF_{i:D3}",
                workflowName: $"Workflow {i}",
                configuredRPO: (15 + i).ToString(),
                dataLagValue: i.ToString()
            ));
        }
        return statuses;
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusWithSpecificId(string referenceId, string type = "MSSQL_REPLICATION")
    {
        return new SvcMsSqlMonitorStatus
        {
            ReferenceId = referenceId,
            Type = type,
            InfraObjectId = "INFRA_TEST",
            InfraObjectName = "Test MSSQL Object",
            WorkflowId = "WF_TEST",
            WorkflowName = "Test Workflow",
            Properties = "{\"test\": true}",
            ConfiguredRPO = "15",
            DataLagValue = "5",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusForType(string type, string infraObjectId = "INFRA_001")
    {
        return CreateSvcMsSqlMonitorStatus(
            type: type,
            infraObjectId: infraObjectId,
            infraObjectName: $"MSSQL Object for {type}",
            workflowName: $"Workflow for {type}"
        );
    }

    public List<SvcMsSqlMonitorStatus> CreateSvcMsSqlMonitorStatusesForTypes(List<string> types, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SvcMsSqlMonitorStatus>();
        foreach (var type in types)
        {
            statuses.Add(CreateSvcMsSqlMonitorStatusForType(type, infraObjectId));
        }
        return statuses;
    }

    public List<SvcMsSqlMonitorStatus> CreateSvcMsSqlMonitorStatusesWithStatus(int activeCount, int inactiveCount, string infraObjectId = "INFRA_001")
    {
        var statuses = new List<SvcMsSqlMonitorStatus>();

        for (int i = 1; i <= activeCount; i++)
        {
            statuses.Add(CreateSvcMsSqlMonitorStatus(
                type: $"MSSQL_ACTIVE_{i}",
                infraObjectId: $"{infraObjectId}_{i}",
                infraObjectName: $"Active MSSQL {i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            statuses.Add(CreateSvcMsSqlMonitorStatus(
                type: $"MSSQL_INACTIVE_{i}",
                infraObjectId: $"{infraObjectId}_{i + activeCount}",
                infraObjectName: $"Inactive MSSQL {i}",
                isActive: false
            ));
        }

        return statuses;
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusForInfraObject(string infraObjectId, string infraObjectName = null)
    {
        return CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            infraObjectName: infraObjectName ?? $"MSSQL Object for {infraObjectId}",
            workflowName: $"Workflow for {infraObjectId}"
        );
    }

    public List<SvcMsSqlMonitorStatus> CreateSvcMsSqlMonitorStatusesForInfraObjects(List<string> infraObjectIds)
    {
        var statuses = new List<SvcMsSqlMonitorStatus>();
        foreach (var infraObjectId in infraObjectIds)
        {
            statuses.Add(CreateSvcMsSqlMonitorStatusForInfraObject(infraObjectId));
        }
        return statuses;
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSvcMsSqlMonitorStatus(properties: propertiesJson);
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusWithComplexProperties()
    {
        var complexProperties = new Dictionary<string, object>
        {
            {"rpo", "15"},
            {"status", "healthy"},
            {"lastSync", "2024-01-01T10:00:00Z"},
            {"databaseDetails", new Dictionary<string, object>
                {
                    {"primaryDatabase", "DB001"},
                    {"secondaryDatabase", "DB002"},
                    {"replicationMode", "sync"},
                    {"compressionEnabled", true}
                }
            },
            {"performance", new Dictionary<string, object>
                {
                    {"throughput", "250MB/s"},
                    {"latency", "1ms"},
                    {"errorRate", "0.0005%"}
                }
            },
            {"monitoring", new Dictionary<string, object>
                {
                    {"alertsEnabled", true},
                    {"thresholdBreaches", 0},
                    {"lastHealthCheck", "2024-01-01T09:55:00Z"}
                }
            },
            {"alwaysOnDetails", new Dictionary<string, object>
                {
                    {"availabilityGroup", "AG001"},
                    {"replica", "REPLICA001"},
                    {"synchronizationState", "SYNCHRONIZED"},
                    {"failoverMode", "AUTOMATIC"}
                }
            }
        };

        return CreateSvcMsSqlMonitorStatusWithProperties(complexProperties);
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusForWorkflow(string workflowId, string workflowName = null, string infraObjectId = "INFRA_001")
    {
        return CreateSvcMsSqlMonitorStatus(
            infraObjectId: infraObjectId,
            workflowId: workflowId,
            workflowName: workflowName ?? $"Workflow {workflowId}",
            infraObjectName: $"MSSQL Object for {workflowId}"
        );
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusWithRPOSettings(string configuredRPO, string dataLagValue)
    {
        return CreateSvcMsSqlMonitorStatus(
            configuredRPO: configuredRPO,
            dataLagValue: dataLagValue,
            properties: $"{{\"rpo\": \"{configuredRPO}\", \"dataLag\": \"{dataLagValue}\"}}"
        );
    }

    public List<SvcMsSqlMonitorStatus> CreateSvcMsSqlMonitorStatusesForRPOTesting()
    {
        return new List<SvcMsSqlMonitorStatus>
        {
            CreateSvcMsSqlMonitorStatusWithRPOSettings("15", "5"),
            CreateSvcMsSqlMonitorStatusWithRPOSettings("30", "10"),
            CreateSvcMsSqlMonitorStatusWithRPOSettings("60", "25"),
            CreateSvcMsSqlMonitorStatusWithRPOSettings("120", "50")
        };
    }

    public SvcMsSqlMonitorStatus CreateMSSQLAlwaysOnStatus(string infraObjectId = "INFRA_ALWAYSON")
    {
        return CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_ALWAYSON",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL AlwaysOn Object",
            workflowName: "AlwaysOn Workflow",
            properties: "{\"type\": \"alwayson\", \"availabilityGroup\": \"AG001\", \"status\": \"synchronized\"}"
        );
    }

    public SvcMsSqlMonitorStatus CreateMSSQLReplicationStatus(string infraObjectId = "INFRA_REPL")
    {
        return CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_REPLICATION",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL Replication Object",
            workflowName: "Replication Workflow",
            properties: "{\"type\": \"replication\", \"mode\": \"transactional\", \"status\": \"running\"}"
        );
    }

    public SvcMsSqlMonitorStatus CreateMSSQLLogShippingStatus(string infraObjectId = "INFRA_LOGSHIP")
    {
        return CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_LOGSHIPPING",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL Log Shipping Object",
            workflowName: "Log Shipping Workflow",
            properties: "{\"type\": \"logshipping\", \"schedule\": \"15min\", \"status\": \"active\"}"
        );
    }

    public SvcMsSqlMonitorStatus CreateMSSQLMirroringStatus(string infraObjectId = "INFRA_MIRROR")
    {
        return CreateSvcMsSqlMonitorStatus(
            type: "MSSQL_MIRRORING",
            infraObjectId: infraObjectId,
            infraObjectName: "MSSQL Mirroring Object",
            workflowName: "Mirroring Workflow",
            properties: "{\"type\": \"mirroring\", \"mode\": \"high_safety\", \"status\": \"synchronized\"}"
        );
    }

    public List<SvcMsSqlMonitorStatus> CreateStandardMSSQLMonitorStatuses()
    {
        return new List<SvcMsSqlMonitorStatus>
        {
            CreateMSSQLAlwaysOnStatus(),
            CreateMSSQLReplicationStatus(),
            CreateMSSQLLogShippingStatus(),
            CreateMSSQLMirroringStatus()
        };
    }

    public SvcMsSqlMonitorStatus CreateMinimalSvcMsSqlMonitorStatus()
    {
        return new SvcMsSqlMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "MSSQL_MINIMAL",
            InfraObjectId = "MINIMAL_INFRA",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public SvcMsSqlMonitorStatus CreateSvcMsSqlMonitorStatusForTesting(
        string testName,
        string type = null,
        string infraObjectId = null)
    {
        return CreateSvcMsSqlMonitorStatus(
            type: type ?? $"MSSQL_{testName.ToUpper()}",
            infraObjectId: infraObjectId ?? $"INFRA_{testName.ToUpper()}",
            infraObjectName: $"Test MSSQL for {testName}",
            workflowName: $"Test Workflow for {testName}"
        );
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
