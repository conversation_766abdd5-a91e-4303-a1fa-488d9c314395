﻿using ContinuityPatrol.Application.Features.LoadBalancer.Events.Delete;

namespace ContinuityPatrol.Application.Features.LoadBalancer.Commands.Delete;

public class
    DeleteLoadBalancerCommandHandler : IRequestHandler<DeleteLoadBalancerCommand,
        DeleteLoadBalancerResponse>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;

    public DeleteLoadBalancerCommandHandler(ILoadBalancerRepository nodeConfigurationRepository, IPublisher publisher,
        IGroupPolicyRepository groupPolicyRepository)
    {
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _publisher = publisher;
        _groupPolicyRepository = groupPolicyRepository;
    }

    public async Task<DeleteLoadBalancerResponse> Handle(DeleteLoadBalancerCommand request,
        CancellationToken cancellationToken)
    {
        var groupPolicy = await _groupPolicyRepository.GetGroupPolicyByLoadBalancerId(request.Id);

        var isNodeInUse = _nodeConfigurationRepository.IsNodeInUse(request.Id);

        if (groupPolicy.Count > 0 || isNodeInUse)
            throw new InvalidException("The LoadBalancer is currently in use");

        var eventToDelete = await _nodeConfigurationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.LoadBalancer),
            new NotFoundException(nameof(Domain.Entities.LoadBalancer), request.Id));

        eventToDelete.IsActive = false;

        await _nodeConfigurationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteLoadBalancerResponse
        {
            Message = Message.Delete(eventToDelete.TypeCategory, eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(
            new LoadBalancerDeletedEvent { TypeCategory = eventToDelete.TypeCategory, Name = eventToDelete.Name },
            cancellationToken);

        return response;
    }
}