﻿namespace ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetByType;

public class GetPostgresMonitorStatusDetailByTypeQueryHandler : IRequestHandler<
    GetPostgresMonitorStatusDetailByTypeQuery, List<PostgresMonitorStatusDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorStatusRepository _postgresMonitorStatusRepository;

    public GetPostgresMonitorStatusDetailByTypeQueryHandler(
        IPostgresMonitorStatusRepository postgresMonitorStatusRepository, IMapper mapper)
    {
        _postgresMonitorStatusRepository = postgresMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<PostgresMonitorStatusDetailByTypeVm>> Handle(
        GetPostgresMonitorStatusDetailByTypeQuery request, CancellationToken cancellationToken)
    {
        var postgresMonitorStatus = await _postgresMonitorStatusRepository.GetDetailByType(request.Type);

        return postgresMonitorStatus.Count <= 0
            ? new List<PostgresMonitorStatusDetailByTypeVm>()
            : _mapper.Map<List<PostgresMonitorStatusDetailByTypeVm>>(postgresMonitorStatus);
    }
}