﻿using ContinuityPatrol.Domain.ViewModels.CompanyModel;

namespace ContinuityPatrol.Application.Features.Company.Queries.GetNames;

public class GetCompanyNameQueryHandler : IRequestHandler<GetCompanyNameQuery, List<CompanyNameVm>>
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IMapper _mapper;

    public GetCompanyNameQueryHandler(ICompanyRepository companyRepository, IMapper mapper)
    {
        _companyRepository = companyRepository;
        _mapper = mapper;
    }

    public async Task<List<CompanyNameVm>> Handle(GetCompanyNameQuery request, CancellationToken cancellationToken)
    {
        var companies = await _companyRepository.GetAllCompanyNames();

        var companyDto = _mapper.Map<List<CompanyNameVm>>(companies);

        return companyDto;
    }
}