using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftManagementMonitorStatusRepositoryTests : IClassFixture<DriftManagementMonitorStatusFixture>
{
    private readonly DriftManagementMonitorStatusFixture _driftManagementMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftManagementMonitorStatusRepository _repository;
    private readonly DriftManagementMonitorStatusRepository _repositoryNotParent;

    public DriftManagementMonitorStatusRepositoryTests(DriftManagementMonitorStatusFixture driftManagementMonitorStatusFixture)
    {
        _driftManagementMonitorStatusFixture = driftManagementMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftManagementMonitorStatusRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DriftManagementMonitorStatusRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;

        // Act
        var result = await _repository.AddAsync(driftManagementMonitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftManagementMonitorStatus.InfraObjectName, result.InfraObjectName);
        Assert.Equal(driftManagementMonitorStatus.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.DriftManagementMonitorStatuss);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        await _repository.AddAsync(driftManagementMonitorStatus);

        driftManagementMonitorStatus.InfraObjectName = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(driftManagementMonitorStatus);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.DeleteAsync(driftManagementMonitorStatus);

        // Assert
        Assert.Equal(driftManagementMonitorStatus.InfraObjectName, result.InfraObjectName);
        Assert.Empty(_dbContext.DriftManagementMonitorStatuss);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraTrue()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        var addedEntity = await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists_AndIsAllInfraFalse()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        var addedEntity = await _repositoryNotParent.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repositoryNotParent.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftManagementMonitorStatus.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftManagementMonitorStatus.ReferenceId, result.ReferenceId);
        Assert.Equal(driftManagementMonitorStatus.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsAllInfraTrue()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(statuses.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredEntities_WhenIsAllInfraFalse()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        await _repositoryNotParent.AddRangeAsync(statuses);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be filtered based on assigned infrastructure
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.IsNameExist(driftManagementMonitorStatus.InfraObjectName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentInfraObjectName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;
        await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.IsNameExist(driftManagementMonitorStatus.InfraObjectName, driftManagementMonitorStatus.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetDriftManagementStatusByInfraObjectId Tests

    [Fact]
    public async Task GetDriftManagementStatusByInfraObjectId_ShouldReturnEntitiesWithMatchingInfraObjectId()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        statuses[0].InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        statuses[1].InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDriftManagementStatusByInfraObjectId("70bb97c9-1193-4e86-98ab-bebc88fb438c");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal("70bb97c9-1193-4e86-98ab-bebc88fb438c", x.InfraObjectId));
    }

    [Fact]
    public async Task GetDriftManagementStatusByInfraObjectId_ShouldReturnEmptyList_WhenNoMatches()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
      
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDriftManagementStatusByInfraObjectId("non-existent-infra-object-id");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetByInfraObjectIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftManagementMonitorStatus = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusDto;

        await _repository.AddAsync(driftManagementMonitorStatus);

        // Act
        var result = await _repository.GetByInfraObjectIdAsync(driftManagementMonitorStatus.InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftManagementMonitorStatus.InfraObjectId, result.InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
     
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetByInfraObjectIdAsync("non-existent-infra-object-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByInfraObjectIdAsync_ShouldReturnCorrectEntity_WhenMultipleExist()
    {
        // Arrange
        var statuses =_driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetByInfraObjectIdAsync(statuses[0].InfraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(statuses[0].InfraObjectId, result.InfraObjectId);
        Assert.Equal(statuses[0].InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetByInfraObjectIdAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetByInfraObjectIdAsync("INFRA_123");

        // Assert
        Assert.Null(result); // Should not return inactive entities
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var statuses = _driftManagementMonitorStatusFixture.DriftManagementMonitorStatusList;
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(statuses);
        var initialCount = statuses.Count;
        
        var toUpdate = statuses.Take(2).ToList();
        toUpdate.ForEach(x => x.InfraObjectName = "UpdatedInfraObjectName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = statuses.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.InfraObjectName == "UpdatedInfraObjectName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
