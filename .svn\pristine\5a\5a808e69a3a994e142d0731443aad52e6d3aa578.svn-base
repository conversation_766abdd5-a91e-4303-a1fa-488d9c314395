﻿namespace ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetScheculeExecutionDetails;

public class GetReportScheduleDetailExecutionQueryHandler : IRequestHandler<GetReportScheduleExecutionDetailQuery, List<ReportScheduleExecutionDetailVm>>
{
    private readonly IMapper _mapper;
    private readonly IReportScheduleExecutionRepository _reportScheduleExecutionRepository;

    public GetReportScheduleDetailExecutionQueryHandler(IMapper mapper, IReportScheduleExecutionRepository reportScheduleExecutionRepository)
    {
        _mapper = mapper;
        _reportScheduleExecutionRepository = reportScheduleExecutionRepository;
    }

    public async Task<List<ReportScheduleExecutionDetailVm>> Handle(GetReportScheduleExecutionDetailQuery request,
        CancellationToken cancellationToken)
    {
        var reportSchedules = await _reportScheduleExecutionRepository.GetReportSchedulerExecutionByReportId(request.Id);

        Guard.Against.NullOrDeactive(reportSchedules, nameof(Domain.Entities.ReportScheduleExecution),
            new NotFoundException(nameof(Domain.Entities.ReportScheduleExecution), request.Id));

        var reportScheduleDtos = _mapper.Map<List<ReportScheduleExecutionDetailVm>>(reportSchedules);

        return reportScheduleDtos;
    }
}
