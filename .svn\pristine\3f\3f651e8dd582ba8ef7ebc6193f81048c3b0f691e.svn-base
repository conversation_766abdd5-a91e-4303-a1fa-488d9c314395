﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Queries;

public class GetApprovalMatrixPaginatedListQueryHandlerTests : IClassFixture<ApprovalMatrixFixture>, IClassFixture<UserFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;

    private readonly Mock<IApprovalMatrixRepository> _mockApprovalMatrixRepository;

    private readonly Mock<IUserRepository> _mockUserRepository;

    private readonly GetApprovalMatrixPaginatedListQueryHandler _handler;

    public GetApprovalMatrixPaginatedListQueryHandlerTests(ApprovalMatrixFixture approvalMatrixFixture, UserFixture userFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;

        _approvalMatrixFixture.ApprovalMatrices[0].Name = "Admin";
        _approvalMatrixFixture.ApprovalMatrices[0].Description = "Create the Matrix";
        _approvalMatrixFixture.ApprovalMatrices[0].BusinessServiceId = "304dc618-a989-4fa6-824b-372e769bef94";
        _approvalMatrixFixture.ApprovalMatrices[0].BusinessServiceName = "Data_BS12345";
        _approvalMatrixFixture.ApprovalMatrices[0].Properties = "{\\\"nodes\\\":[{\\\"id\\\":\\\"node_1_496\\\",\\\"offsetY\\\":111,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"offsetY\\\":158,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"description\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"actionType\\\":\\\"5a69a2d0-5a81-4ca7-8a70-e9e7293d585e\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"5f2d6eac-4e8e-4a9d-87c7-7555a625b2bd\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"5754e28f-f676-4394-9247-fa40f81ed2d6\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_1_496RL5d0\\\",\\\"offsetY\\\":205,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_2_193BdpmW\\\",\\\"offsetY\\\":252,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"description\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"actionType\\\":\\\"5a69a2d0-5a81-4ca7-8a70-e9e7293d585e\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"5f2d6eac-4e8e-4a9d-87c7-7555a625b2bd\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"5754e28f-f676-4394-9247-fa40f81ed2d6\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_1_496RL5d0btumk\\\",\\\"offsetY\\\":299,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}}],\\\"connectors\\\":[{\\\"id\\\":\\\"connector0_873\\\",\\\"sourceID\\\":\\\"start\\\",\\\"targetID\\\":\\\"node_1_496\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector3_90\\\",\\\"sourceID\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"targetID\\\":\\\"node_1_496RL5d0\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector2_307\\\",\\\"sourceID\\\":\\\"node_1_496RL5d0\\\",\\\"targetID\\\":\\\"node_2_193BdpmW\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector3_427\\\",\\\"sourceID\\\":\\\"node_2_193BdpmW\\\",\\\"targetID\\\":\\\"node_1_496RL5d0btumk\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector4_638\\\",\\\"sourceID\\\":\\\"node_1_496\\\",\\\"targetID\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"visible\\\":true}]}";

        _approvalMatrixFixture.ApprovalMatrices[1].Name = "Admin_123";
        _approvalMatrixFixture.ApprovalMatrices[1].Description = "Create the Matrix_123";
        _approvalMatrixFixture.ApprovalMatrices[1].BusinessServiceId = "304dc618-a989-4fa6-824b-372e769bef94";
        _approvalMatrixFixture.ApprovalMatrices[1].BusinessServiceName = "Data_BS12345";

        _mockApprovalMatrixRepository = ApprovalMatrixRepositoryMocks.GetPaginatedApprovalMatrixRepository(_approvalMatrixFixture.ApprovalMatrices);

        _mockUserRepository = UserRepositoryMocks.GetUserNamesRepository(userFixture.Users);

        _handler = new GetApprovalMatrixPaginatedListQueryHandler(_approvalMatrixFixture.Mapper, _mockApprovalMatrixRepository.Object, _mockUserRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetApprovalMatrixPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ApprovalMatrixListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_ApprovalMatrix_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetApprovalMatrixPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Admin;description=Create the Matrix" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ApprovalMatrixListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<ApprovalMatrixListVm>();

        result.Data[0].Name.ShouldBe("Admin");
        result.Data[0].Description.ShouldBe("Create the Matrix");
        result.Data[0].BusinessServiceId.ShouldBe("304dc618-a989-4fa6-824b-372e769bef94");
        result.Data[0].BusinessServiceName.ShouldBe("Data_BS12345");
        result.Data[0].Properties.ShouldBe("{\\\"nodes\\\":[{\\\"id\\\":\\\"node_1_496\\\",\\\"offsetY\\\":111,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"offsetY\\\":158,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"description\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"actionType\\\":\\\"5a69a2d0-5a81-4ca7-8a70-e9e7293d585e\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"5f2d6eac-4e8e-4a9d-87c7-7555a625b2bd\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"5754e28f-f676-4394-9247-fa40f81ed2d6\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_1_496RL5d0\\\",\\\"offsetY\\\":205,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_2_193BdpmW\\\",\\\"offsetY\\\":252,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"description\\\":\\\"VerifyMaxSequenceNumber\\\",\\\"actionType\\\":\\\"5a69a2d0-5a81-4ca7-8a70-e9e7293d585e\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"5f2d6eac-4e8e-4a9d-87c7-7555a625b2bd\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"5754e28f-f676-4394-9247-fa40f81ed2d6\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}},{\\\"id\\\":\\\"node_1_496RL5d0btumk\\\",\\\"offsetY\\\":299,\\\"offsetX\\\":549.***********,\\\"actionInfo\\\":{\\\"actionName\\\":\\\"VerifyDbmodeandRole\\\",\\\"description\\\":\\\"VerifyDbmodeandRole\\\",\\\"actionType\\\":\\\"8272aedc-e6d7-401c-adb4-a776bff35232\\\",\\\"properties\\\":{\\\"PRServerName\\\":\\\"8f32287e-3aee-49a9-9897-0a28ac2a7c87\\\",\\\"PRDBName\\\":\\\"515def4b-1725-4ec6-8f7b-ccc692676c4b\\\",\\\"DRServerName\\\":\\\"4b901f61-ee5b-4baa-b873-203e228f4b61\\\",\\\"DRDBName\\\":\\\"8f1df0d7-ace7-4bee-ac5a-1c8ba9660ab5\\\"},\\\"nodeId\\\":\\\"9a1c2a11-7fbd-4d3f-9404-c9641244b73c\\\",\\\"IsEmail\\\":false,\\\"IsSms\\\":false,\\\"IsParellel\\\":false,\\\"connectionTimeout\\\":\\\"5\\\",\\\"icon\\\":\\\"\\\"}}],\\\"connectors\\\":[{\\\"id\\\":\\\"connector0_873\\\",\\\"sourceID\\\":\\\"start\\\",\\\"targetID\\\":\\\"node_1_496\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector3_90\\\",\\\"sourceID\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"targetID\\\":\\\"node_1_496RL5d0\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector2_307\\\",\\\"sourceID\\\":\\\"node_1_496RL5d0\\\",\\\"targetID\\\":\\\"node_2_193BdpmW\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector3_427\\\",\\\"sourceID\\\":\\\"node_2_193BdpmW\\\",\\\"targetID\\\":\\\"node_1_496RL5d0btumk\\\",\\\"visible\\\":true},{\\\"id\\\":\\\"connector4_638\\\",\\\"sourceID\\\":\\\"node_1_496\\\",\\\"targetID\\\":\\\"node_2_193BdpmWwUP5TVf248\\\",\\\"visible\\\":true}]}");
    }

    [Fact]
    public async Task Handle_Return_PaginatedApprovalMatrix_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetApprovalMatrixPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Admin" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ApprovalMatrixListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());
        result.Data[0].Name.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].Name);
        result.Data[0].Description.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].Description);
        result.Data[0].BusinessServiceId.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].BusinessServiceId);
        result.Data[0].BusinessServiceName.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].BusinessServiceName);
        //result.Data[0].ActionType.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].ActionType);
      //  result.Data[0].TemplateName.ShouldBe(_approvalMatrixFixture.ApprovalMatrices[0].TemplateName);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetApprovalMatrixPaginatedListQuery() { PageNumber = 1, PageSize = 10, SearchString = "zyx" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ApprovalMatrixListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetApprovalMatrixPaginatedListQuery(), CancellationToken.None);
                
        _mockApprovalMatrixRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
        It.IsAny<int>(), It.IsAny<ApprovalMatrixFilterSpecification>(),
        It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }
}
