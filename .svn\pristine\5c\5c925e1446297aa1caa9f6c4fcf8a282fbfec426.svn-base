﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">

        <h6 class="page_title" title=" MSSQL AlwaysOn Monitoring">
            <i class="cp-monitoring"></i><span>
                SybaseWithRSHADR Detail Monitoring:
                <span id="infraName"></span>
            </span>
        </h6>
        @* <span><i class="cp-time me-2"></i>Last Monitored Time : <span id="modifiedTime"></span></span> *@
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-time me-2"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span>
                            SybaseWithRSHADR ASE
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2" style="height: calc(100vh - 240px);overflow:auto">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>Parameter</th>
                                    <th class="text-primary">Primary</th>
                                    <th class="text-secondary customServer">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="text-secondary cp-server me-1"></i>Data Server</td>
                                    <td><span id="DataServer"></span></td>
                                    <td><span id="DR_DataServer"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-synbase-backup-server me-1"></i>Backup Server</td>
                                    <td><span id="BackupServer"></span></td>
                                    <td><span id="DR_BackupServer"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-datas me-1"></i>ASE Version</td>
                                    <td><span id="ASEVersion"></span></td>
                                    <td><span id="DR_ASEVersion"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-control-file-name me-1"></i>Database Name</td>
                                    <td><span id="DatabaseName"></span></td>
                                    <td><span id="DR_DatabaseName"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-database-success me-1"></i>Database Status</td>
                                    <td><span id="DatabaseStatus"></span></td>
                                    <td><span id="DR_DatabaseStatus"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-right-left me-1"></i>Database Logging Status</td>
                                    <td><span id="DatabaseLoggingStatus"></span></td>
                                    <td><span id="DR_DatabaseLoggingStatus"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-dataguard-status me-1"></i>Data Device Details</td>
                                    <td>
                                        <ul class="list-group list-group-flush" id="DataDeviceDetails">                                           
                                        </ul>
                                    </td>
                                    <td>
                                        <ul class="list-group list-group-flush" id="DR_DataDeviceDetails">
                                        </ul>
                                    </td>
                                    @* <td><span id="DR_DataDeviceDetails"></span></td> *@
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-folder-server me-1"></i>Data Space Used%</td>
                                    <td><span id="Dataspaceused"></span></td>
                                    <td><span id="DR_Dataspaceused"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-log-file-name me-1"></i>Log Device Details</td>
                                    @* <td><span id="LoggingDeviceDetails"></span></td> *@
                                    <td>
                                        <ul class="list-group list-group-flush" id="LoggingDeviceDetails">
                                        </ul>
                                    </td>
                                    <td><span id="DR_LoggingDeviceDetails"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-log-file-name me-1"></i>Log Device space Used %</td>
                                    <td><span id="LogSpaceUsed"></span></td>
                                    <td><span id="DR_LogSpaceUsed"></span></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
           
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        @* <img src="~/img/isomatric/solutiondiagram.svg" height="159px;" /> *@
                        <div id="Solution_Diagram" class="w-100"></div>
                    </div>
                </div>

                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span>
                            SybaseWithRSHADR Replication Monitoring
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2" style="height: calc(50vh - 120px);overflow:auto">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead class="position-sticky top-0 z-3">
                                <tr>
                                    <th>Replication</th>
                                    <th>PR</th>
                                    <th class="customServer">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="text-secondary cp-server me-1"></i>Replication Server IP</td>
                                    <td><span id="ServerIpAddress"></span></td>
                                    <td><span id="DR_ServerIpAddress"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-product-version-icon me-1"></i>Replication Server Name</td>
                                    <td><span id="ServerName"></span></td>
                                    <td><span id="DR_ServerName"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-datas me-1"></i>Replication Server Version</td>
                                    <td><span id="ServerVersion"></span></td>
                                    <td><span id="DR_ServerVersion"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-server-times me-1"></i>RMA Host</td>
                                    <td>
                                        <span id="RMAHost"></span>
                                    </td>
                                    <td><span id="DR_RMAHost"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-server-times me-1"></i>Logical Host Name</td>
                                    <td>
                                        <span id="LogicalHostname"></span>
                                    </td>
                                    <td><span id="DR_LogicalHostname"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-folder me-1"></i>HADR Mode</td>
                                    <td><span id="HADRMode"></span></td>
                                    <td><span id="DR_HADRMode"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-database-sid me-1"></i>HADR State</td>
                                    <td><span id="HADRState"></span></td>
                                    <td><span id="DR_HADRState"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-datalog me-1"></i>Synchronization Mode</td>
                                    <td><span id="SynchronizationMode"></span></td>
                                    <td><span id="DR_SynchronizationMode"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-control-file-name me-1"></i>Synchronization State</td>
                                    <td><span id="SynchronizationState"></span></td>
                                    <td><span id="DR_SynchronizationState"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-ohas-status me-1"></i>Distribution Mode</td>
                                    <td><span id="DistributionMode"></span></td>
                                    <td><span id="DR_DistributionMode"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-active-dg-enable me-1"></i>Replication Server Status</td>
                                    <td><span id="ServerStatus"></span></td>
                                    <td><span id="DR_ServerStatus"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-replication-source me-1"></i>Replication Path</td>
                                    <td><span id="Path"></span></td>
                                    <td><span id="DR_Path"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-relationship-progress me-1"></i>State</td>
                                    <td><span id="State"></span></td>
                                    <td><span id="DR_State"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-database-time me-1"></i>Latency</td>
                                    <td><span id="Latency"></span></td>
                                    <td><span id="DR_Latency"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-estimated-time me-1"></i>Commit Time</td>
                                    <td><span id="CommitTime"></span></td>
                                    <td><span id="DR_CommitTime"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-replication-source me-1"></i>Replication Path Master DB</td>
                                    <td><span id="MasterDB_Path"></span></td>
                                    <td><span id="DR_MasterDB_Path"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-standby-redo-logs me-1"></i>State</td>
                                    <td><span id="MasterDB_State"></span></td>
                                    <td><span id="DR_MasterDB_State"></span></td>
                                </tr>
                                <tr>
                                    <td><i class="text-secondary cp-database-time me-1"></i>Latency</td>
                                    <td><span id="MasterDB_Latency"></span></td>
                                    <td><span id="DR_MasterDB_Latency"></span></td>
                                <tr>
                                    <td><i class="text-secondary cp-estimated-time me-1"></i>Commit Time</td>
                                    <td><span id="MasterDB_CommitTime"></span></td>
                                    <td><span id="DR_MasterDB_CommitTime"></span></td>
                                </tr>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>




    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/Monitoring/SybaseWithRSHADR.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>

