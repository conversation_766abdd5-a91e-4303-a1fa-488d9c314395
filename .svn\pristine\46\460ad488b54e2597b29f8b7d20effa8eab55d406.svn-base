﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class GlobalSettingService : BaseClient, IGlobalSettingService
{
    public GlobalSettingService(IConfiguration config, IAppCache cache, ILogger<GlobalSettingService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateGlobalSettingCommand createGlobalSettingCommand)
    {
        var request = new RestRequest("api/v6/globalsettings", Method.Post);

        request.AddJsonBody(createGlobalSettingCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateGlobalSettingCommand updateGlobalSettingCommand)
    {
        var request = new RestRequest("api/v6/globalsettings", Method.Put);

        request.AddJsonBody(updateGlobalSettingCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string globalSettingId)
    {
        var request = new RestRequest($"api/v6/globalsettings/{globalSettingId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<PaginatedResult<GlobalSettingListVm>> GetGlobalSettingPaginatedList(GetGlobalSettingPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/globalsettings/paginated-list?SearchString=");

        return await Get<PaginatedResult<GlobalSettingListVm>>(request);
    }

    public async Task<bool> IsGlobalSettingNameExist(string globalSettingName, string? id)
    {
        var request = new RestRequest($"globalsettings/name-exist?key={globalSettingName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<GlobalSettingListVm>> GetGlobalSettingsList()
    {
        var request = new RestRequest("api/v6/globalsettings");

        return await Get<List<GlobalSettingListVm>>(request);
    }
   
    public async Task<GlobalSettingDetailVm> GetGlobalSettingById(string globalSettingId)
    {
        var request = new RestRequest($"api/v6/globalsettings/{globalSettingId}");

        return await Get<GlobalSettingDetailVm>(request);
    }
    public async Task<BaseResponse> Authentication(AuthenticationCommand command)
    {
        var request = new RestRequest("api/v6/globalsettings/authentication", Method.Post);

        request.AddJsonBody(command);

        return await Post<BaseResponse>(request);
    }
}