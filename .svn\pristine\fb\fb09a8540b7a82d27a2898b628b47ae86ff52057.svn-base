using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberJobManagementRepositoryTests : IClassFixture<CyberJobManagementFixture>
{
    private readonly CyberJobManagementFixture _cyberJobManagementFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberJobManagementRepository _repository;

    public CyberJobManagementRepositoryTests(CyberJobManagementFixture cyberJobManagementFixture)
    {
        _cyberJobManagementFixture = cyberJobManagementFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberJobManagementRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetCyberJobByAirGapId Tests

    [Fact]
    public async Task GetCyberJobByAirGapId_ShouldReturnJobsForAirGap()
    {
        // Arrange
        var airgapId = "e7efe010-687a-4a71-85c1-7a47e29367f1";
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        jobs[0].AirgapId = airgapId;
        jobs[1].AirgapId = airgapId;

      await  _repository.AddRangeAsync(jobs);


        // Act
        var result = await _repository.GetCyberJobByAirGapId(airgapId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(airgapId, x.AirgapId));
    }

    [Fact]
    public async Task GetCyberJobByAirGapId_ShouldReturnEmpty_WhenNoJobsForAirGap()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByAirGapId("NON_EXISTENT_AIRGAP");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetCyberJobByWorkflowId Tests

    [Fact]
    public async Task GetCyberJobByWorkflowId_ShouldReturnJobsForWorkflow()
    {
        // Arrange
        var workflowId = "5d390aad-142c-4b5a-9a74-5bb05140b969";
      var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        jobs[0].WorkflowId = workflowId;
        jobs[1].WorkflowId = workflowId;

        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByWorkflowId(workflowId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(workflowId, x.WorkflowId));
    }

    [Fact]
    public async Task GetCyberJobByWorkflowId_ShouldReturnEmpty_WhenNoJobsForWorkflow()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.GetCyberJobByWorkflowId("NON_EXISTENT_WORKFLOW");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var job = _cyberJobManagementFixture.CyberJobManagementDto;
        job.Name = "ExistingJobName";
        await _dbContext.CyberJobManagements.AddAsync(job);
         _dbContext.SaveChanges();
        // Act
        var result = await _repository.IsNameExist("ExistingJobName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var jobs = _cyberJobManagementFixture.CyberJobManagementList;
        await _repository.AddRangeAsync(jobs);

        // Act
        var result = await _repository.IsNameExist("NonExistentJobName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var job = _cyberJobManagementFixture.CyberJobManagementDto;
        job.Name = "SameJobName";
        await _dbContext.CyberJobManagements.AddAsync(job);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = !await _repository.IsNameExist("SameJobName", job.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

   
}
