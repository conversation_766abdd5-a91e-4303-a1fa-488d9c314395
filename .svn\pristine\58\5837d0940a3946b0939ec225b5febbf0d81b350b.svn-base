﻿using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using FluentValidation.TestHelper;

namespace ContinuityPatrol.Application.UnitTests.Features.AccessManager.Validators;

public class CreateAccessManagerValidatorTests
{
    public List<Domain.Entities.AccessManager> AccessManagers { get; set; }

    private readonly CreateAccessManagerCommandValidator _validator;

    public CreateAccessManagerValidatorTests()
    {
        AccessManagers = new Fixture().Create<List<Domain.Entities.AccessManager>>();

        Mock<IAccessManagerRepository> mockAccessManagerRepository = new();

        mockAccessManagerRepository
            .Setup(repo => repo.IsAccessManagerRoleExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);

        _validator = new CreateAccessManagerCommandValidator(mockAccessManagerRepository.Object);

        AccessManagerRepositoryMocks.CreateAccessManagerRepository(AccessManagers);
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_RoleName_InAccessManager_WithEmpty(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Role Name is Required.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_RoleName_InAccessManager_IsEmpty(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = null;
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("'Role Name' must not be empty.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_RoleName_InAccessManager_MiniMumRange(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "QR";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.RoleName);
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_RoleName_InAccessManager_MaximumRange(CreateAccessManagerCommand createAccessManagerCommand)
    {

        createAccessManagerCommand.RoleName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldNotHaveValidationErrorFor(x => x.RoleName);
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_RoleName_InAccessManager_Valid(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "  CTS  ";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_SingleSpace_InFront(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "  CTS";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_SingleSpace_InBack(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "CTS ";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_DoubleSpace_InBetween(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "CTS  Technology";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_SpecialCharacters_InFront(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "#$%CTS Technology";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_SpecialCharacters_InBetween(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "CTS#$%^Technology";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_Only_SpecialCharacters(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "@$^*%$#$%^";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_UnderScore_InFront(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "_CTSTechnology";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_Numbers_InFront(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "124CTSTechnology";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }

    [Theory]
    [AutoAccessManagerData]
    public async Task Verify_Create_ValidRoleName_InAccessManager_With_Numbers_Only(CreateAccessManagerCommand createAccessManagerCommand)
    {
        createAccessManagerCommand.RoleName = "24739875342";
        createAccessManagerCommand.RoleId = "cf620855-cd6e-4285-8336-3ff490b9dd04";

        var result = await _validator.TestValidateAsync(createAccessManagerCommand);
        result.ShouldHaveValidationErrorFor(x => x.RoleName)
            .WithErrorMessage("Please Enter Valid Role Name.");
    }
}