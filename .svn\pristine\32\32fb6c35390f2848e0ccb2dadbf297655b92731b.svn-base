﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.CyberJobManagementLogsModel;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;

public class GetCyberJobWorkflowSchedulerPaginatedListQueryHandler : IRequestHandler<GetCyberJobWorkflowSchedulerPaginatedListQuery,
    PaginatedResult<CyberJobWorkflowSchedulerListVm>>
{
    public readonly ICyberJobWorkflowSchedulerRepository _cyberJobWorkflowSchedulerRepository;
    public readonly IMapper _mapper;

    public GetCyberJobWorkflowSchedulerPaginatedListQueryHandler(IMapper mapper, ICyberJobWorkflowSchedulerRepository cyberJobWorkflowSchedulerRepository)
    {
        _mapper = mapper;
        _cyberJobWorkflowSchedulerRepository = cyberJobWorkflowSchedulerRepository;
    }

    public async Task<PaginatedResult<CyberJobWorkflowSchedulerListVm>> Handle(GetCyberJobWorkflowSchedulerPaginatedListQuery request,
    CancellationToken cancellationToken)
    {
        var productFilterSpec = new CyberJobWorkflowSchedulerFilterSpecification(request.SearchString);

        var queryable = await _cyberJobWorkflowSchedulerRepository.GetCyberJobWorkflowSchedulerPagination(request.PageNumber, request.PageSize, productFilterSpec, request.StartDate, request.EndDate);

        var cyberJobWorkflowSchedulerList = _mapper.Map<PaginatedResult<CyberJobWorkflowSchedulerListVm>>(queryable);

        return cyberJobWorkflowSchedulerList;

    }
}
