﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;

public class GetDescriptionByStartTimeAndEndTimeListQueryHandler : IRequestHandler<
    GetDescriptionByStartTimeAndEndTimeListQuery, List<GetDescriptionByStartTimeAndEndTimeListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public GetDescriptionByStartTimeAndEndTimeListQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
    }

    public async Task<List<GetDescriptionByStartTimeAndEndTimeListVm>> Handle(
        GetDescriptionByStartTimeAndEndTimeListQuery request, CancellationToken cancellationToken)
    {
        var workflowOperation = request.RunMode.IsNotNullOrWhiteSpace()
            ? _workflowOperationRepository.GetDescriptionByStartTimeAndEndTime(request.StartTime,
                    request.EndTime).Result.Where(x =>
                    x.RunMode.IsNotNullOrWhiteSpace() && x.RunMode.Trim().ToLower() == request.RunMode.Trim().ToLower())
                .ToList()
            : await _workflowOperationRepository.GetDescriptionByStartTimeAndEndTime(request.StartTime,
                request.EndTime);

        var workflowOperationGroup = _workflowOperationGroupRepository.ListAllAsync().Result;

        var filteredWorkflow = workflowOperation.Where(workflowOperationRefId =>
            workflowOperationGroup.Any(workflowgroupOprId =>
                workflowgroupOprId.WorkflowOperationId == workflowOperationRefId.ReferenceId)).ToList();

        var workflowOperationDescription = filteredWorkflow.OrderByDescending(x => x.StartTime).ToList();

        return workflowOperationDescription.Count == 0
            ? new List<GetDescriptionByStartTimeAndEndTimeListVm>()
            : _mapper.Map<List<GetDescriptionByStartTimeAndEndTimeListVm>>(workflowOperationDescription);
    }
}