﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;

public class GetBusinessFunctionPaginatedListQueryHandler : IRequestHandler<GetBusinessFunctionPaginatedListQuery,
    PaginatedResult<BusinessFunctionListVm>>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IMapper _mapper;

    public GetBusinessFunctionPaginatedListQueryHandler(IMapper mapper,
        IBusinessFunctionRepository businessFunctionRepository)
    {
        _mapper = mapper;
        _businessFunctionRepository = businessFunctionRepository;
    }

    public async Task<PaginatedResult<BusinessFunctionListVm>> Handle(
        GetBusinessFunctionPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new BusinessFunctionFilterSpecification(request.SearchString);

        var queryable =await  _businessFunctionRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var businessFunctionList = _mapper.Map<PaginatedResult<BusinessFunctionListVm>>(queryable);
          
        return businessFunctionList;
        //var queryable = _businessFunctionRepository.GetPaginatedQuery();

        //var productFilterSpec = new BusinessFunctionFilterSpecification(request.SearchString);

        //var businessFunctionList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<BusinessFunctionListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //await _publisher.Publish(new BusinessFunctionPaginatedEvent());

        //return businessFunctionList;
    }
}