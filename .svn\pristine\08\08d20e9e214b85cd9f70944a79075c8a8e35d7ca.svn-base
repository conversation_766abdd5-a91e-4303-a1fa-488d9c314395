﻿using ContinuityPatrol.Application.Features.DashboardView.Event.OneView;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Areas.Dashboard.Controllers;

[Area("Dashboard")]

public class ResiliencyMappingController : BaseController
{
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<ResiliencyMappingController> _logger;
    private readonly IPublisher _publisher;
    public ResiliencyMappingController(IDataProvider dataProvider, ILogger<ResiliencyMappingController> logger, IPublisher publisher)
    {

        _dataProvider = dataProvider;
        _logger = logger;
        _publisher = publisher;
    }


    public IActionResult List()
    {
        return View();
    }

    [HttpGet]
    public async Task<JsonResult> GetDrCalendarDrillEvents()
    {
        try
        {
            var DrillDetails = await _dataProvider.DRCalenderService.GetDrCalendarDrillEvents();
            return Json(new { Success = true, data = DrillDetails });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    public async Task<IActionResult> ResiliencyMapping()
    {
        await _publisher.Publish(new OneViewEvent());
        return View();
    }



    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceList(string? siteId)
    {
        try
        {
            var DCMapping = await _dataProvider.DashboardView.GetDcMappingDetails(siteId);
            return Json(new { Success = true, data = DCMapping });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
    [HttpGet]
    public async Task<JsonResult> GetImpactList()
    {
        try
        {
            var impactservice = await _dataProvider.ImpactActivity.GetImpactActivityList();
            return Json(new { Success = true, data = impactservice });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }
    [HttpGet]
    public async Task<JsonResult> GetImpactDetailCount(string? businessServiceId)
    {
        try
        {
            var ImpactDetailservice = await _dataProvider.HeatMapStatus.GetImpactDetail(businessServiceId);
            return Json(new { Success = true, data = ImpactDetailservice });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceTopology(string? businessServiceId)
    {
        try
        {
            var Topologyservice = await _dataProvider.DashboardView.GetBusinessServiceTopologyByBusinessServiceId(businessServiceId);
            return Json(new { Success = true, data = Topologyservice });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetSites()
    {
        try
        {
            var siteLists = await _dataProvider.Site.GetSites();
            return Json(new { success = true, data = siteLists });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceAvailabilityList()
    {
        try
        {
            var businessService = await _dataProvider.BusinessServiceAvailability.GetBusinessServiceAvailabilityList();
            return Json(new { success = true, data = businessService });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetDrReadyStatusByBusinessServiceId(string businessServiceId)
    {
        try
        {
            var businessServiceData = await _dataProvider.DrReadyStatus.GetDrReadyStatusByBusinessServiceId(businessServiceId);
            return Json(new { success = true, data = businessServiceData });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBusinessServiceDrReady(string businessServiceId)
    {
        try
        {
            var businessServiceData = await _dataProvider.DrReadyStatus.GetBusinessServiceDrReady(businessServiceId);
            return Json(new { success = true, data = businessServiceData });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetDcMappingSiteDetails()
    {
        try
        {
            var businessServiceData = await _dataProvider.DashboardView.GetDcMappingSiteDetails();
            return Json(new { success = true, data = businessServiceData });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetVerifiedWorkflowList()
    {
        try
        {
            var VerifiedWorkflowList = await _dataProvider.DashboardView.GetVerifiedWorkflowList();
            return Json(new { success = true, data = VerifiedWorkflowList });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetBreachDetails()
    {
        try
        {
            var BreachDetails = await _dataProvider.DashboardView.GetBreachDetails();
            return Json(new { success = true, data = BreachDetails });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetLastDrillDetails()
    {
        try
        {
            var LastDrillDetails = await _dataProvider.DashboardView.GetLastDrillDetails();
            return Json(new { success = true, data = LastDrillDetails });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetOneViewEntitiesEventViewList()
    {
        try
        {
            var OneViewEntitiesEventViewList = await _dataProvider.DashboardView.GetOneViewEntitiesEventViewList();
            return Json(new { success = true, data = OneViewEntitiesEventViewList });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on One View while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

}