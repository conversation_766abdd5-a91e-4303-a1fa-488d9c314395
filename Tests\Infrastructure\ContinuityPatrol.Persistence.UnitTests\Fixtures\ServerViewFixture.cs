using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerViewFixture
{
    public ServerView CreateServerView(
        string name = "Default Server",
        string companyId = "COMPANY_123",
        string serverTypeId = "TYPE_001",
        string serverType = "Web Server",
        string roleTypeId = "ROLE_001",
        string roleType = "Primary",
        string siteId = "SITE_001",
        string businessServiceId = "BS_001",
        string osTypeId = "OS_001",
        string osType = "Windows",
        string ipAddress = "*************",
        string properties = "{}",
        bool isActive = true,
        bool isDelete = false)
    {
        return new ServerView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            CompanyId = companyId,
            ServerTypeId = serverTypeId,
            ServerType = serverType,
            RoleTypeId = roleTypeId,
            RoleType = roleType,
            SiteId = siteId,
            BusinessServiceId = businessServiceId,
            OSTypeId = osTypeId,
            OSType = osType,
            IpAddress = ipAddress,
            Properties = properties,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public List<ServerView> CreateMultipleServerViews(int count, string companyId = "COMPANY_123")
    {
        var servers = new List<ServerView>();
        for (int i = 1; i <= count; i++)
        {
            servers.Add(CreateServerView(
                name: $"Server{i}",
                companyId: companyId,
                serverTypeId: $"TYPE_{i:D3}",
                businessServiceId: $"BS_{i:D3}",
                ipAddress: $"192.168.1.{100 + i}"
            ));
        }
        return servers;
    }

    public ServerView CreateServerViewWithSpecificId(string referenceId, string name = "Test Server")
    {
        return new ServerView
        {
            ReferenceId = referenceId,
            Name = name,
            CompanyId = "COMPANY_123",
            ServerTypeId = "TYPE_001",
            ServerType = "Web Server",
            RoleTypeId = "ROLE_001",
            RoleType = "Primary",
            SiteId = "SITE_001",
            BusinessServiceId = "BS_001",
            OSTypeId = "OS_001",
            OSType = "Windows",
            IpAddress = "*************",
            Properties = "{}",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
        };
    }

    public ServerView CreateServerViewForBusinessService(string businessServiceId, string companyId = "COMPANY_123")
    {
        return CreateServerView(
            name: $"Server_{businessServiceId}",
            companyId: companyId,
            businessServiceId: businessServiceId
        );
    }

    public ServerView CreateServerViewForOsType(string osTypeId, string osType, string companyId = "COMPANY_123")
    {
        return CreateServerView(
            name: $"Server_{osType}",
            companyId: companyId,
            osTypeId: osTypeId,
            osType: osType
        );
    }

    public ServerView CreateServerViewForSite(string siteId, string companyId = "COMPANY_123")
    {
        return CreateServerView(
            name: $"Server_{siteId}",
            companyId: companyId,
            siteId: siteId
        );
    }

    public ServerView CreateServerViewWithProperties(string properties, string companyId = "COMPANY_123")
    {
        return CreateServerView(
            name: "Server_WithProperties",
            companyId: companyId,
            properties: properties
        );
    }
}
