﻿<div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollabel">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-business-service"></i><span>Service Availability Overview</span></h6>
            <button type="button" class="btn cp-card ms-auto AvailabilityList" role="button" title="Card View" id="cardData"></button>
            <div class="input-group w-auto me-2">
                <input id="search-input" type="search" placeholder="Search" class="form-control" />
                <div class="input-group-text">
                    <div class="dropdown">
                        <i type="button" class="cp-filter" title="Filter" data-bs-toggle="dropdown"  data-bs-auto-close="outside"></i>
                        <form class="dropdown-menu p-0">
                            <div class="accordion accordion-flush filter-accordion" id="accordionFlushExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button id="btnAllValue" class="btn btn-sm btn-tresprent collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                            All
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body p-0">
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#table-flush-collapseTwo" aria-expanded="false" aria-controls="table-flush-collapseTwo">
                                            Status
                                        </button>
                                    </h2>
                                    <div id="table-flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body p-0">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item" role="button" id="filterValue"><i class="cp-success me-2 text-success"></i>Available</li>
                                                <li class="list-group-item" role="button" id="filterValue"><i class="cp-warning me-2 text-warning"></i>Not Available</li>
                                                <li class="list-group-item" role="button" id="filterValue"><i class="cp-Impact me-2 text-danger"></i>MajorImpact</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#table-flush-collapseThree" aria-expanded="false"
                                                aria-controls="table-flush-collapseThree">
                                            Priority
                                        </button>
                                    </h2>
                                    <div id="table-flush-collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                        <div class="accordion-body p-0">
                                            <ul class="list-group list-group-flush">
                                                <li class="list-group-item" role="button"><i class="cp-up-doublearrow me-2 text-danger"></i>High</li>
                                                <li class="list-group-item" role="button"><i class="cp-equal me-2 text-warning"></i>Medium</li>
                                                <li class="list-group-item" role="button"><i class="cp-down-doublearrow me-2 text-info"></i>Low</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <button type="button" id="BtnBusinessViewDownload" class="btn btn-sm btn-primary" title="Export"><i class="cp-download fs-7"></i></button>
            <button type="button" class="btn-close ms-2 closeOverview" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body" style="min-height: 400px;">
            <table id="ServiceOverviewtable" class="table datatable" style="line-height:normal;">
                <thead>
                    <tr>
                        <th class="text-center" style="width:5%;">Sr.No</th>
                        <th>Operational Services</th>
                        <th>
                            <table class="w-100">                                
                                <tr>
                                    <td class="small" style="width:40%;">RPO Threshold</td>
                                    <td class="small" style="width:30%;">RPO Configured</td>
                                    <td class="small" style="width:30%;">RPO Computed</td>
                                </tr>
                            </table>
                        </th>
                        <th>
                            <table class="w-100">                               
                                <tr>
                                    <td class="small w-50">RTO Configured</td>
                                    <td class="small w-50">RTO Computed</td>
                                </tr>
                            </table>
                        </th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody class="fw-normal" id="ServiceOverviewTableBody">
                   
                </tbody>
            </table>
            <div class="row row-cols-4 g-3" id="collaps">
                
            </div>
        </div>
        @* <div class="modal-footer">
            <button type="button" class="btn closeOverview btn-secondary" data-bs-dismiss="modal">Close</button>
        </div> *@
    </div>
</div>