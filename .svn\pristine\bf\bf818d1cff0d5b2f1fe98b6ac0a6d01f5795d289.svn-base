using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DriftImpactTypeMasterFixture : IDisposable
{
    public List<DriftImpactTypeMaster> DriftImpactTypeMasterPaginationList { get; set; }
    public List<DriftImpactTypeMaster> DriftImpactTypeMasterList { get; set; }
    public DriftImpactTypeMaster DriftImpactTypeMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string ImpactTypeName = "TestImpactType";
    public const string Description = "Test Impact Type Description";
    public const string Severity = "High";
    public const string Category = "Performance";

    public ApplicationDbContext DbContext { get; private set; }

    public DriftImpactTypeMasterFixture()
    {
        var fixture = new Fixture();

        DriftImpactTypeMasterList = fixture.Create<List<DriftImpactTypeMaster>>();

        DriftImpactTypeMasterPaginationList = fixture.CreateMany<DriftImpactTypeMaster>(20).ToList();

        DriftImpactTypeMasterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftImpactTypeMasterPaginationList.ForEach(x => x.IsActive = true);
       
        DriftImpactTypeMasterList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DriftImpactTypeMasterList.ForEach(x => x.IsActive = true);
       

        DriftImpactTypeMasterDto = fixture.Create<DriftImpactTypeMaster>();
        DriftImpactTypeMasterDto.ReferenceId = Guid.NewGuid().ToString();
        DriftImpactTypeMasterDto.IsActive = true;
       

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
