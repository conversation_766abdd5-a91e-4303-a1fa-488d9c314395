﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatusList;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries
{
    public class GetWorkflowOperationGroupRunningStatusListQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;
        private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;
        private readonly GetWorkflowOperationGroupRunningStatusListQueryHandler _handler;

        public GetWorkflowOperationGroupRunningStatusListQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockWorkflowOperationRepository = new Mock<IWorkflowOperationRepository>();
            _mockWorkflowActionResultRepository = new Mock<IWorkflowActionResultRepository>();
            _handler = new GetWorkflowOperationGroupRunningStatusListQueryHandler(
                _mockMapper.Object,
                _mockWorkflowOperationGroupRepository.Object,
                _mockWorkflowOperationRepository.Object, _mockWorkflowActionResultRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedList_WhenDataExists()
        {
            var request = new GetWorkflowOperationGroupRunningStatusListQuery();
            var workflowOperationList = new List<Domain.Entities.WorkflowOperation>
            {
                new Domain.Entities.WorkflowOperation { ReferenceId = "Guid.NewGuid().ToString()" },
                new Domain.Entities.WorkflowOperation { ReferenceId = "Guid.NewGuid().ToString()" }
            };

            var workflowOperationGroupList = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup { Id = 1, WorkflowName = "Group 1" },
                new Domain.Entities.WorkflowOperationGroup { Id = 2, WorkflowName = "Group 2" }
            };

            var expectedResult = new List<WorkflowOperationGroupRunningStatusListVm>
            {
                new WorkflowOperationGroupRunningStatusListVm { Id = Guid.NewGuid().ToString(), CurrentActionName = "Group 1" },
                new WorkflowOperationGroupRunningStatusListVm { Id = Guid.NewGuid().ToString(), CurrentActionName = "Group 2" }
            };

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(workflowOperationList);

            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(workflowOperationGroupList);

            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowOperationGroupRunningStatusListVm>>(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()))
                .Returns(expectedResult);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedResult.Count, result.Count);
            Assert.Equal(expectedResult[0].Id, result[0].WorkflowOperationId);
            Assert.Equal(expectedResult[1].CurrentActionName, result[1].WorkflowOperationId);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByRunningStatus(), Times.Once);
            _mockWorkflowOperationGroupRepository.Verify(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Exactly(2)); 
            _mockMapper.Verify(mapper => mapper.Map<List<WorkflowOperationGroupRunningStatusListVm>>(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoDataExists()
        {
            var request = new GetWorkflowOperationGroupRunningStatusListQuery();

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ReturnsAsync(new List<Domain.Entities.WorkflowOperation>());

            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(new List<Domain.Entities.WorkflowOperationGroup>());

            _mockMapper
                .Setup(mapper => mapper.Map<List<WorkflowOperationGroupRunningStatusListVm>>(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()))
                .Returns(new List<WorkflowOperationGroupRunningStatusListVm>());

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByRunningStatus(), Times.Once);
            _mockWorkflowOperationGroupRepository.Verify(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Never);
            _mockMapper.Verify(mapper => mapper.Map<List<WorkflowOperationGroupRunningStatusListVm>>(It.IsAny<List<Domain.Entities.WorkflowOperationGroup>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenErrorOccurs()
        {
            var request = new GetWorkflowOperationGroupRunningStatusListQuery();
            var exception = new Exception("Something went wrong");

            _mockWorkflowOperationRepository
                .Setup(repo => repo.GetWorkflowOperationByRunningStatus())
                .ThrowsAsync(exception);

            var ex = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("Something went wrong", ex.Message);

            _mockWorkflowOperationRepository.Verify(repo => repo.GetWorkflowOperationByRunningStatus(), Times.Once);
            _mockWorkflowOperationGroupRepository.Verify(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()), Times.Never);
        }
    }
}
