let form = $('#CreateSMTPForm')
let smtpConfURL = {
    getSmtpList: "Admin/SmtpConfiguration/GetSmtpList",
    smtpDecrypt: "Admin/SmtpConfiguration/DecryptPassword",
    encryptPassword: "Configuration/Server/HashPassword",
    sendTestMail: "/Admin/SmtpConfiguration/SendTestMail"
}

$(function () {

    var createPermission = $("#adminCreate").data("create-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#btnSmtpSave").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
        $("#btnSmtpTest").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
        //&& $("#btnSmtpTest").removeClass("btn-primary").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }

    $('#EmailDiv,#maskDiv').hide()
   
    $('#btnSmtpSave').on('click', async function () {
       
        //$('#btnSmtpSave').prop('disabled', true);
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))
        let smtpHost = $('#smtpConfigHost').val();
        let port = $('#smtpConfigPort').val();
        let userName = $('#smtpConfigUserName').val();
        let password = $('#smtpConfigPassword').val();
        let ennablepassword = $('#paswordcheck').is(':checked');       
        $('#chk-enableSSl').is(':checked');
        $('#chk-isBodyHtml').is(':checked');        
        let enableMaskAddress = $('#chkMask').is(':checked');
        let maskAddress = enableMaskAddress ?  $('#smtpConfigMask').val() : '';
        $('#smtpConfigId').val();
        let companyId = $('#smtpConfigCompanyId').data('company');
        $('#smtpConfigCompanyId').val(companyId);        
        let host = await validateHost(smtpHost);
        let Port = await validatePort(port);
        let email = await validateEmail(userName);
        let pass = await validatePassword(password);
        let mask = enableMaskAddress ?  await validateMaskAddress(maskAddress) : true
      
        if (($('#btnSmtpSave').text() == 'Update' || $('#btnSmtpSave').text() == 'Save') && password != "") {

            $('#smtpConfigPasswordError').text('').removeClass('field-validation-error');
            pass = true
        }           
         if (ennablepassword === true) {
             if (host && Port && email) {
                 form.trigger('submit')
             }   
         }
       
       else if (host && Port && email && pass && mask) {
             
           form.trigger('submit')
        }
        
    }) 

    //TestEmail
    let formData;
    $('#btnSmtpTest').on('click', commonDebounce(async function () { 
        
        localStorage.setItem("id", $(".nav .nav-link.active").attr("id"))
        let smtpHost = $('#smtpConfigHost').val();
        let port = $('#smtpConfigPort').val();
        let userName = $('#smtpConfigUserName').val();
        let password = $('#smtpConfigPassword').val();
        let ennableSSL= $('#chk-enableSSl').is(':checked');       
        let isBodyHtml = $('#chk-isBodyHtml').is(':checked');
        let ennablepassword = $('#paswordcheck').is(':checked');
        let enableEmail = $('#chkEmail').is(':checked');
        let ToEmail = $('#txtSmtpEmail').val();
        let enableMask = $('#chkMask').is(':checked');
        let mask = enableMask ?  $('#smtpConfigMask').val() : ''    
        $('#smtpConfigIsPassword').val(ennablepassword);
        $('#smtpConfigId').val();
        let companyId = $('#smtpConfigCompanyId').data('company');
        $('#smtpConfigCompanyId').val(companyId);       
        $('#smtpConfigTestSmtp').val(smtpHost);
        $('#smtpConfigTestPort').val(port);
        $('#smtpConfigTestUserName').val(userName);
        $('#smtpConfigTestPassword').val(password);
        $('#smtpConfigTestEnableSSL').val(ennableSSL);
        $('#smtpConfigTestIsBodyHTML').val(isBodyHtml);
        $('#smtpConfigTestCompanyIdsBodyHTML').val(companyId);
        $('#smtpConfigToEmail').val(ToEmail);
        $('#smtpConfigIsEmail').val(enableEmail);
        $('#smtpConfigIsMask').val(enableMask)
        $('#smtpConfigMaskAddress').val(mask)
        let host = await validateHost(smtpHost);
        let Port = await validatePort(port);
        let email = await validateEmail(userName);
        let pass = await validatePassword(password);
        let address = enableMask ? await validateMaskAddress(mask) : true
        //var ToEmail = await validateEmail(Email);
        //$('#SmtpPassword-error').text('').removeClass('field-validation-error'); 
         formData = {
            smtpHost: smtpHost,
            port: port,
            userName: userName,
            password: password,
            companyId: companyId,
            enableSSL: ennableSSL,
            isBodyHTML: isBodyHtml,
            isPasswordLess: ennablepassword,
            isEmail: enableEmail,
            toEmail: ToEmail,
            isMask: enableMask,
            maskFromAddress: mask
        };
        
        if ($('#btnSmtpTest').text().trim().replace(/\s+/g, ' ').trim() == 'Test Mail' && password != "" ) {

            $('#smtpConfigPasswordError').text('').removeClass('field-validation-error');
            pass = true
        }
       
        if (ennablepassword === true) {
            if (host && Port && email) {
                testMail()
               
            }
        }
      
        else if (host && Port && email && pass && address) {
            testMail()
           
        } 
    }, 300)); 
    function testMail() {
        
        $.ajax({
            url: smtpConfURL.sendTestMail,
            data: formData,
            dataType: "json",
            traditional: true,
            headers: {
                'RequestVerificationToken': gettoken()
            },
            type: 'POST',
            success: function (result) {
                if (result.success) {
                    notificationAlert('success', result.data.message)
                } else {
                    errorNotification(result)
                }
            }
        })
    }
    $("#v-pills-smtp-tab").on('click',function () {
        ['#smtpConfigPasswordError', '#smtpConfigEmailError', '#smtpConfigPortError', '#smtpConfigHostError'].forEach(clearErrorMessages);
        getSmtpDetails();
    });
    function clearErrorMessages(errorSelector) {
        $(errorSelector).text('').removeClass('field-validation-error');
    }
    function showPassword(input, icon) {
        input.attr("type", "text");
        icon.removeClass("cp-password-visible").addClass("cp-password-hide")
        var icon = $(".cp-password-hide");
        icon.attr("title", "Show Password");
        $(".cp-password-hide").on('mouseover',function () {
            $(this).attr("title", "Hide Password");
        });
    }

    function hidePassword(input, icon) {
        input.attr("type", "password");
        icon.removeClass("cp-password-hide").addClass("cp-password-visible");
        var icon = $(".cp-password-visible");
        icon.attr("title", "Hide Password");
        $(".cp-password-visible").on('mouseover',function () {
            $(this).attr("title", "Show Password");
        });
    }
    $("#smtpConfigPassword").on('focus',async function () {

        let encryptedPassword = $(this).val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 60) {
            let focusPassword = await onfocusPassword(encryptedPassword);
            $(this).val(focusPassword.decrypt);
        }

    });
    async function EncryptPassword(password) {
        try {
            const response = await $.ajax({
                url: RootUrl + smtpConfURL.encryptPassword,
                method: 'GET',
                data: { Password: password },
                dataType: 'json'
            });
            return response.encrypt;
        } catch (error) {
            console.error('Error fetching data from the encryption API:', error);
            throw error;
        }
    }

    $("#smtpConfigPassword").on('blur',async function () {
        let decryptedPassword = $(this).val();
        if (decryptedPassword !== "" && decryptedPassword.length <= 60) {
            let encryptedPassword = await EncryptPassword(decryptedPassword);
            $(this).val(encryptedPassword);
            $(this).attr("type", "password");
            $(".cp-password-hide").removeClass("cp-password-hide").addClass("cp-password-visible");
        }
    });

    $(".toggle-password-SMTP").on('click',async function () {   
        let input = $(this).prev();
        let icon = $(this).find("i");
       
        if (input.attr("type") === "password") {                      
            let encryptedPassword = input.val();
            if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 60) {
                let smtpPassword = await onfocusPassword(encryptedPassword);
                input.val(smtpPassword.decrypt);
            }
            showPassword(input, icon);  
        }
        else {          
            hidePassword(input, icon);            
        }
    });

    //togglePasswordField();
    function togglePasswordField() {
       
        const errorElement = $('#smtpConfigPasswordError');
            if ($('#paswordcheck').is(':checked')) {
                //$('#smtpConfigPassword').prop('disabled', true);            
                $('#smtpConfigPassword').val('');
                $('#txtPassword,.toggle-password-SMTP').hide(); 
                errorElement.text('')
                    .removeClass('field-validation-error');
              
            } else {
                //$('#smtpConfigPassword').prop('disabled', false);
                $('#txtPassword,.toggle-password-SMTP').show();
                
            }
        }      
              
    $('#chkEmail').on('change', function () {
        if ($(this).is(':checked')) {           
            $('#EmailDiv').show();
        } else {            
            $('#EmailDiv').hide()
            $('#txtSmtpEmail').val('');
        }
            
    });
    function toggleMask() {
        const errorElement = $('#SmtpMaskAddress-error');
        if ($('#chkMask').is(':checked')) {
            $('#maskDiv').show();
        } else {
            $('#maskDiv').hide()
            $('#smtpConfigMask').val('');
            errorElement.text('').removeClass('field-validation-error');
        }
    }
    $('#chkMask').on('change', function () {
       
        toggleMask()
    });
    $('#paswordcheck').on('change', function () {
        if ($(this).is(':checked')) {
            $('#txtPassword').hide();
        } else {
            $('#txtPassword').show()
        }
        togglePasswordField();
    });

    $(".cp-password-hide").on('mouseover',function () {
        $(this).attr("title", "Hide Password");
    });

    $(".cp-password-visible").on('mouseover',function () {
        $(this).attr("title", "Show Password");
    });



    async function decryptPassword(password, inputSelector) {

        let data = {

            password: password
        };        
        try {
            const response = await $.ajax({
                type: "GET",
                url: RootUrl + smtpConfURL.smtpDecrypt,
                data: data,
                dataType: "json"
            });
            if (response) {
                $(inputSelector).val(response);
                return response;
            }

        } catch (error) {
            console.error("Error hashing password: " + error);
        }
    }
    async function onfocusPassword(encryptedPassword) {
        
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 60) {
            try {

                let decryptedPassword = await decryptPassword(encryptedPassword);                                              
                return decryptedPassword

            } catch (error) {
                console.error("Error decrypting password on focus: " + error);
            }
        }

        return null;
    }
    function getSmtpDetails() {
        $.ajax({
            type: "GET",
            url: RootUrl + smtpConfURL.getSmtpList,
            async: true,
            success: function (response) {
                if (response) {
                    
                    if (Object.keys(response).length && typeof (response) != 'string') {
                        //$('#btnSmtpSave').prop('disabled', true);
                        $('#btnSmtpSave').text('Update');
                        //$('#btnSmtpTest').prop('disabled', false);
                        $('#smtpConfigHost').val(response.smtpHost);
                        $('#smtpConfigPort').val(response.port);
                        $('#smtpConfigUserName').val(response.userName);
                        $('#smtpConfigPassword').val(response.password);
                        $('#chk-enableSSl').prop('checked', response.enableSSL);
                        $('#chk-isBodyHtml').prop('checked', response.isBodyHTML);
                        $('#paswordcheck').prop('checked', response.isPasswordLess);
                        $('#chkMask').prop('checked', response.isMask)
                        $('#smtpConfigMask').val(response.maskFromAddress)
                        togglePasswordField()
                        toggleMask()
                        $('#smtpConfigId').val(response.id);
                        $('#smtpConfigCompanyId').val(response.companyId);

                    }

                    else {
                        $('#btnSmtpSave').text('Save');
                        //$('#btnSmtpTest').prop('disabled', true);

                    }
                    $(".nav .nav-link").removeClass("active")
                    var id = localStorage.getItem("id")
                    if (id !== null) {
                        $("#" + id).removeClass("active")
                        $("#" + id).addClass("nav-link active")
                        var splitId = id.split("-")
                        var slicevalue = splitId.slice(0, splitId.length - 1).join("-");
                        $(".tab-content .tab-pane").removeClass("active")
                        $("#" + slicevalue).removeClass("tab-pane fade")
                        $("#" + slicevalue).addClass("tab-pane fade active show")
                    }
                }
            },
            error: function (xhr, status, error) {
                console.log('Error:', error);
            }

        })
    }
    getSmtpDetails();
    $('#smtpConfigHost').on('keyup', async function (event) {
        const value = $(this).val();
       
        if (event.keyCode == 32) {
            event.preventDefault();
        } if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateHost(value)
    })
    const exceptThisSymbols = ["e", "E", "+", "-", "."];
    $('#smtpConfigPort').on('keypress keyup paste', async function (event) {
        const value = $(this).val()
        
        if (!/[+0-9-]/.test(event.key) || exceptThisSymbols.includes(event.key) || event.type === 'paste') {
            event.preventDefault();
        }       
        await validatePort(value)
    })
    $('#smtpConfigUserName').on('keyup', async function (event) {
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue); 
        if (event.keyCode == 32 ) {
            event.preventDefault();
        }
        await validateEmail(value)
    })
    $('#smtpConfigMask').on('keyup', async function (event) {
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        if (event.keyCode == 32) {
            event.preventDefault();
        }
        await validateMaskAddress(value)
    })
    $('#smtpConfigPassword').on('keyup paste', async function (event) {
        const value = $(this).val();
        if (event.keyCode == 32 || event.type === 'paste') {
            event.preventDefault();
        }
        await validatePassword(value)
    })
    
    async function validatePassword(value) {
        const errorElement = $('#smtpConfigPasswordError');
        const isPasswordLess = $('#paswordcheck').is(':checked');
        const host = $('#smtpConfigHost').val(); 
        
        if (!value && !isPasswordLess) {          
            errorElement.text('Enter SMTP password')
                .addClass('field-validation-error');            
            return false;
        } else if (value) {
            errorElement.text('')
                .removeClass('field-validation-error');
        }
       
        if (!isPasswordLess && host !== 'smtp.gmail.com' && host !== 'smtp.mail.yahoo.com') {
            const validationResults = [
                await passwordRegex(value)
            ];
            const isValid = await CommonValidation(errorElement, validationResults);
            //if (!isValid) {
            //    $("#btnSmtpSave").prop('disabled', true); // Disable save button on validation error
            //} else {
            //    $("#btnSmtpSave").prop('disabled', false); // Enable save button when password is in correct format
            //}
            return isValid;
        } else {
            //$("#btnSmtpSave").prop('disabled', false); // Enable save button for password-less mode
        }
        
    }

    const minMaxlengthSMTP = (value, maxlength = 100) => {
        return value.length > 2 && value.length < maxlength + 1 ? true : `Username between 3 to ${maxlength} characters`
    }
    async function validateInput(value, errorSelector, inputSelector, isEmail) {
        const errorElement = $(errorSelector);
        let format = /^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

        if (!value) {
            errorElement.text(`Enter ${isEmail ? 'SMTP email / username' : 'SMTP mask address'}`)
                .addClass('field-validation-error');
            return false;
        }

        if (value.includes("<")) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }

        if (containsAtSymbol(value)) {
            $(inputSelector).prop('maxLength', 320);
            if (!format.test(value)) {
                errorElement.text('Invalid email')
                    .addClass('field-validation-error');
                return false;
            } else {
                errorElement.text('').removeClass('field-validation-error');
                return true;
            }
        } else {
            $(inputSelector).prop('maxLength', 100);

            const validationResults = [
                await SpecialCharValidate(value),
                await ShouldNotBeginWithUnderScore(value),
                await ShouldNotBeginWithSpace(value),
                await OnlyNumericsValidate(value),
                await SpaceWithUnderScore(value),
                await ShouldNotEndWithUnderScore(value),
                await ShouldNotEndWithSpace(value),
                await MultiUnderScoreRegex(value),
                await SpaceAndUnderScoreRegex(value),
                await ShouldNotBeginWithNumber(value),
                await minMaxlengthSMTP(value, 100),
                await secondChar(value),
            ];
            return await CommonValidation(errorElement, validationResults);
        }
    }
    async function validateMaskAddress(value) {
        return await validateInput(value, '#SmtpMaskAddress-error', '#smtpConfigMask', false);
    }
    async function validateEmail(value) {
        return await validateInput(value, '#smtpConfigEmailError', '#smtpConfigUserName', true);
    }
    async function validatePort(value) {
        const errorElement = $('#smtpConfigPortError');
        if (!value) {
            errorElement.text('Enter SMTP port')
                .addClass('field-validation-error')
            return false;
        }
        const validationResults = [
            await OnlyNum(value),
            await PortReg(value),
        ];
        return await CommonValidation(errorElement, validationResults)
    }
  
    async function validateHost(value) {
        const errorElement = $('#smtpConfigHostError');
        if (!value) {
            errorElement.text('Enter SMTP host / IP address')
                .addClass('field-validation-error')
            return false;
        }
        if (isAllNumerics(value)) {
            errorElement.text('Invalid host')
                .addClass('field-validation-error')
            return false;
        }
        if (!isFirstCharacterNumber(value)) {
            const validationResults = [
                await ShouldNotBeginWithUnderScore(value),
                await OnlyNumericsValidate(value),
                await ShouldNotEndWithUnderScore(value),
                await MultiUnderScoreRegex(value),
                await SmtpHost(value),
                await ShouldNotBeginWithNumber(value),

            ];
            return await CommonValidation(errorElement, validationResults)
        } else {
            if (!isValidIPv4(value)) {
                console.log(isValidIPv4(value));
                errorElement.text('Enter Valid IP Address')
                    .addClass('field-validation-error')
                return false;
            } else {
                errorElement.text('')
                    .removeClass('field-validation-error')
                return true;
            }   
        }           
    }
})

function isValidIPv4(ip) {
    const ipv4Pattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Pattern.test(ip);
}
function isAllNumerics(str) {
    return /^\d+$/.test(str); // Checks if the string is made up of only numbers
}
function isFirstCharacterNumber(str) {
    const firstChar = str.charAt(0);
    return /\d/.test(firstChar);
}

function containsAtSymbol(str) {
    return str.includes('@');
}
