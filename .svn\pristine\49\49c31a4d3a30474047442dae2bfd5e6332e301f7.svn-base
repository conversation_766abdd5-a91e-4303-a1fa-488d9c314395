﻿using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Commands.Discover;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetCGHealthStatus;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class RpForVmCGMonitorStatusController : CommonBaseController
{

    [HttpPut("discover")]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<DiscoverRpForResponse>> Discover([FromBody] DiscoverRpForCommand command)
    {
        Logger.LogDebug($"Discover RpForVmCGMonitor '{command.Id}'");

       // ClearDataCache();

        return Ok(await Mediator.Send(command));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<PaginatedResult<RpForVmCGMonitorStatusListVm>>> GetRpForVmCGMonitorStatus([FromQuery] GetRpForVmCGMonitorStatusPaginatedQuery query)
    {
        Logger.LogDebug("Get Searching Details in RpForVmCGMonitor Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("CalculateCgEnableStatus"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<CGHealthStatusVm>> CalculateCgEnableStatus()
    {
        Logger.LogDebug("Get Searching Details in RpForVmCGMonitor Paginated List");

        return Ok(await Mediator.Send(new GetCGHealthStatusQuery()));
    }

   


    [NonAction]
    public override void ClearDataCache()
    {
        throw new NotImplementedException();
    }
}