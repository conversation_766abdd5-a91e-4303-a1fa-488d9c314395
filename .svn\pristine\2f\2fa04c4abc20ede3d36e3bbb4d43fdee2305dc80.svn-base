﻿using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseManager.Queries;

public class GetLicenseManagerPaginatedListQueryHandlerTests : IClassFixture<LicenseManagerFixture>
{
    private GetLicenseManagerPaginatedListQueryHandler _handler;
    private readonly Mock<ILicenseManagerRepository> _licenseManagerRepository = new();
    private readonly Mock<IMapper> _mockMapper = new();
    
    

    public GetLicenseManagerPaginatedListQueryHandlerTests(LicenseManagerFixture licenseManagerFixture)
    {
        var licenseManagerNewFixture = licenseManagerFixture;
        _handler =new GetLicenseManagerPaginatedListQueryHandler(_mockMapper.Object, _licenseManagerRepository.Object);
        licenseManagerNewFixture.LicenseManagers[0].CompanyId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        //licenseManagerNewFixture.LicenseManagers[0].PONumber = "DDSE4";
        //licenseManagerNewFixture.LicenseManagers[0].CPHostName = "CDfr";
        //licenseManagerNewFixture.LicenseManagers[0].IPAddress = "***********";
        licenseManagerNewFixture.LicenseManagers[0].ReferenceId = "8970d5ed-5739-4705-8b5a-d004b668cc82";

        licenseManagerNewFixture.LicenseManagers[1].CompanyId = "acbeec2d-8350-4408-9d4b-c81aa1a36c3e";
        //licenseManagerNewFixture.LicenseManagers[1].PONumber = "DDSE4123";
        //licenseManagerNewFixture.LicenseManagers[1].CPHostName = "CDfr";
        licenseManagerNewFixture.LicenseManagers[1].ReferenceId = "8970d5ed-5739-4705-8b5a-d004b668cc82";

        //var mockLicenseManagerRepository = LicenseManagerRepositoryMocks.GetPaginatedLicenseManagerRepository(licenseManagerNewFixture.LicenseManagers);

        //_handler = new GetLicenseManagerPaginatedListQueryHandler(licenseManagerNewFixture.Mapper, mockLicenseManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var request = new GetLicenseManagerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10
        };

        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();
        var _licenseManagers = licenseManagers.BuildMock();
        _licenseManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(_licenseManagers);

        var result = await _handler.Handle(request, CancellationToken.None);

        
        Assert.NotNull(result);
        Assert.Equal(3, result.TotalCount); 


    }

    [Fact]
    public async Task Handle_Return_PaginatedLicenseManagers_When_QueryStringMatch()
    {
        var request = new GetLicenseManagerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10
        };

        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();
        var _licenseManagers = licenseManagers.BuildMock();
        _licenseManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(_licenseManagers);
        var result = await _handler.Handle(request, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LicenseManagerListVm>>();

        result.TotalCount.ShouldBe(3);

       

       
    }
    
    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var request = new GetLicenseManagerPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10
        };

        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();
        var _licenseManagers = licenseManagers.BuildMock();
        _licenseManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(_licenseManagers);
        var result = await _handler.Handle(new GetLicenseManagerPaginatedListQuery {  PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<LicenseManagerListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_LicenseManagers_With_MultipleQueryStringParameter()
    {
        var query = new Fixture().Create<GetLicenseManagerPaginatedListQuery>();
        var licenseManagers = new Fixture().Create<List<Domain.Entities.LicenseManager>>();
        var _licenseManagers = licenseManagers.BuildMock();
        var request = new GetLicenseManagerPaginatedListQuery
        {
            SearchString = "PTS",
            PageNumber = 1,
            PageSize = 10
        };

        var licensemanager = new LicenseManagerListVm { 
         
             Id="2",
             CompanyId= licenseManagers[0].CompanyId

        };
        _licenseManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(_licenseManagers);
        _mockMapper.Setup(x => x.Map<LicenseManagerListVm>(licenseManagers)).Returns(licensemanager);
        var result = await _handler.Handle(request, CancellationToken.None);
        result.Data.Add(licensemanager);

        result.ShouldBeOfType<PaginatedResult<LicenseManagerListVm>>();

        result.TotalCount.ShouldBe(0);

        result.Data[0].ShouldBeOfType<LicenseManagerListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].CompanyId.ShouldBe(licenseManagers[0].CompanyId);

       
    }
}