using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;

namespace ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetList;

public class GetBulkImportOperationGroupListQueryHandler : IRequestHandler<GetBulkImportOperationGroupListQuery,
    List<BulkImportOperationGroupListVm>>
{
    private readonly IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private readonly IMapper _mapper;

    public GetBulkImportOperationGroupListQueryHandler(IMapper mapper,
        IBulkImportOperationGroupRepository bulkImportOperationGroupRepository)
    {
        _mapper = mapper;
        _bulkImportOperationGroupRepository = bulkImportOperationGroupRepository;
    }

    public async Task<List<BulkImportOperationGroupListVm>> Handle(GetBulkImportOperationGroupListQuery request,
        CancellationToken cancellationToken)
    {
        var bulkImportOperationGroups = await _bulkImportOperationGroupRepository.ListAllAsync();

        if (bulkImportOperationGroups.Count <= 0) return new List<BulkImportOperationGroupListVm>();

        return _mapper.Map<List<BulkImportOperationGroupListVm>>(bulkImportOperationGroups);
    }
}