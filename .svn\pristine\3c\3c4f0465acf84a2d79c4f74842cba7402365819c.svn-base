﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectIdAndActionType;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetInfraObjectByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;
using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByWorkflowId;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;

namespace ContinuityPatrol.Application.Mappings;

public class WorkflowInfraObjectProfile : Profile
{
    public WorkflowInfraObjectProfile()
    {
        CreateMap<WorkflowInfraObject, CreateWorkflowInfraObjectCommand>().ReverseMap();

        CreateMap<WorkflowInfraObject, WorkflowInfraObjectListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowInfraObject, WorkflowInfraObjectDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowInfraObject, WorkflowInfraObjectByInfraObjectIdVm>()
            .ForMember(dest => dest.WorkflowId, opt => opt.MapFrom(src => src.WorkflowId));
        CreateMap<WorkflowInfraObject, WorkflowInfraObjectByWorkflowIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<WorkflowInfraObject, GetInfraObjectByWorkflowIdVm>()
            .ForMember(dest => dest.WorkflowId, opt => opt.MapFrom(src => src.WorkflowId));
        CreateMap<WorkflowInfraObject, WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>();
        CreateMap<WorkflowInfraObject, WorkflowInfraObjectDetailByInfraObjectIdVm>();
    }
}