using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertMasterFixture : IDisposable
{
    public List<AlertMaster> AlertMasterPaginationList { get; set; }
    public List<AlertMaster> AlertMasterList { get; set; }
    public AlertMaster AlertMasterDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public AlertMasterFixture()
    {
        var fixture = new Fixture();

        AlertMasterList = fixture.Create<List<AlertMaster>>();

        AlertMasterPaginationList = fixture.CreateMany<AlertMaster>(20).ToList();

        AlertMasterDto = fixture.Create<AlertMaster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
