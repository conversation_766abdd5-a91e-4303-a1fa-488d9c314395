using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ImpactActivityFixture : IDisposable
{
    public List<ImpactActivity> ImpactActivityPaginationList { get; set; }
    public List<ImpactActivity> ImpactActivityList { get; set; }
    public ImpactActivity ImpactActivityDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ImpactActivityFixture()
    {
        var fixture = new Fixture();

        ImpactActivityList = fixture.Create<List<ImpactActivity>>();

        ImpactActivityPaginationList = fixture.CreateMany<ImpactActivity>(20).ToList();

        ImpactActivityDto = fixture.Create<ImpactActivity>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
