﻿namespace ContinuityPatrol.Persistence.Services.Helper;

public static class PermissionAccess
{
    public static List<string> GetPermissionAccessList(string json)
    {
        var permissions = new List<string>();

        var deserializeJson = JsonConvert.DeserializeObject<dynamic>(json)?.Permissions;

        if (deserializeJson == null) return permissions;

        foreach (var item in deserializeJson)
        foreach (var innerValue in item)
        foreach (var accessValue in innerValue)
            if (((JProperty)accessValue).Value.ToString().ToLower().Contains("true"))
                permissions.Add(accessValue.Path);

        return permissions;
    }
}