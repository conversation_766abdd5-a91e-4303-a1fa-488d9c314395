var createPermission = $("#ManageExCreate").data("create-permission").toLowerCase();
var deletePermission = $("#ManageExDelete").data("delete-permission").toLowerCase();
const nameExistUrl = 'Manage/EscalationMatrix/IsEscalationMatrixNameExist';



if (createPermission == 'false') {
    $("#creatematrix").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
   
}
$(function () {

    console.log("Document ready!");

    var selectedValues = [];

    var dataTable = $('#tablebody').DataTable({
        // ... Existing DataTable configuration ...
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        "processing": true,
        "serverSide": true,
        "filter": true,
        "ajax": {
            // ... Existing ajax configuration ...
            "type": "GET",
            "url": "/Manage/EscalationMatrix/GetPagination",
            "dataType": "json",
            "data": function (d) {
                console.log("Ajax data function:", d);
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                selectedValues.length = 0;
            },

            "dataSrc": function (json) {
                //var test = JSON.parse(json);
                console.log("Ajax dataSrc function:", json);
                json.recordsTotal = json?.totalPages;
                json.recordsFiltered = json?.totalCount;
                if (json.data.length === 0) {
                    $(".pagination-column").addClass("disabled")
                    /*  $(".TableThead").addClass("d-none")*/

                }
                else {
                    $(".pagination-column").removeClass("disabled")
                    //$(".TableThead").removeClass("d-none")
                }
                return json?.data;
            }
        },

        "columns": [
            {
                "data": null,
                "name": "Sr. No.",
                "autoWidth": true,
                "render": function (data, type, row, meta) {
                    if (type === 'display') {
                        /*return meta.row + 1;*/
                        var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                        return (page * meta.settings._iDisplayLength) + meta.row + 1;
                    }
                    return data;
                }
            },
            {
                "data": "escMatName", "name": "Matrix Code &amp Name", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return row.escMatCode + ' ' + row.escMatName;
                    }
                    return data;
                }
            },

            {
                "data": "", "name": "Matrix Used", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {
                        return '<span title="Escalation Matrix has been attached 0 times ">' + "Escalation Matrix has been attached 0 times" + '</span>';
                    }
                    return data;
                }
            },
            {
                "data": "createdDate", "name": "Matrix Create Update Details", "autoWidth": true,
                "render": function (data, type, row) {
                    if (type === 'display') {

                        return row.createdDate + '<br/>' + row.lastModifiedDate;
                    }
                    return data;
                }
            },
            {
                "render": function (data, type, row) {
                    if (createPermission === 'true' && deletePermission === "true") {
                        const isParent = row.isParent;
                        return `<div class="d-flex align-items-center  gap-2"> 
                                    <span role="button" title="Update Escalation Matrix" class="btnMatleveCon" data-bs-target="#levelModal" data-escalation='${JSON.stringify(row)}'  id="${row.id}" data-bs-toggle="modal">
                                        <i class="cp-refresh"></i>
                                    </span>
                                <span role="button" title="Edit"  class="edit-button" data-escalation='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title=""  opacity:0.50;" class="delete-button ">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                `
                                    <span role="button" title="Delete" class="delete-button" data-escalation-id="${row.id}" data-escalation-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`
                            }
                            </div>
                     `;
                    }
                    else if (createPermission === 'true' && deletePermission === "false") {
                        return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit-button" data-escalation='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                       
                                            
                       
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                  
                                            
                                </div>`;
                    }
                    else if (createPermission === 'false' && deletePermission === "true") {
                        return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>



                                <span role="button" title="Delete" class="delete-button" data-escalation-id="${row.id}" data-escalation-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                    }
                    else {
                        return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>



                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                    }
                },
                "orderable": false
            }
        ],
        order: [[0, 'asc']],
        columnDefs: [
            {
                targets: 0,
                type: 'num'
            }
        ],
        "drawCallback": function (settings) {
            const randomColor = () => {
                return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
            }

            const namelist = document.querySelectorAll("#companyname");
            namelist.forEach((name) => {
                name.style.backgroundColor = randomColor();
            });
        }

    });


    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });
    $(document).on('click', '.btnMatleveCon', function () {
        let id = $(this)[0].id;
        sessionStorage.setItem('teamResourceId', id);
        var companyData = $(this).data('escalation');
        $('#textgroupId').val(companyData.id);

        // $('#levelModal').modal('show');
        Escmatlevdis(id);

    });
    $(document).on('keyup', '#search-inp', function () {
        // console.log("Keyup event triggered!");
        $('input[type="checkbox"]').each(function () {
            if ($(this).is(':checked')) {
                var checkboxValue = $(this).val();
                var inputValue = $('#search-inp').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });
        dataTable.search($(this).val()).draw();
    });

    $('#tablebody').on('click', '.edit-button', function () {

        var companyData = $(this).data('escalation');
        populateModalFields(companyData);
        $('#btnClick').text('Update')
        $('#btnClick').attr('title', 'Update')
        $('#CreateModal').modal('show');
    });

    //delete
    $('#tablebody').on('click', '.delete-button', function () {

        var escId = $(this).data('escalation-id');
        var escName = $(this).data('escalation-name');
        $('#textDeleteId').val(escId);
        $('#deleteData').text(escName);
    });



});

$(document).on('input', '.EscalMatSearch', function () {
    let value = $(this).val().toLowerCase();

    $('#tblesclevUser').children().last().children().each(function () {
        $(this).toggle($(this).find('.EM_UserName').text().toLowerCase().indexOf(value) > -1);
    })
});

$(document).on('input', '.EscalTeamSearch', function () {
    let value = $(this).val().toLowerCase();

    $('#tblesclevTeam').children().last().children().each(function () {
        $(this).toggle($(this).find('.EM_TeamName').text().toLowerCase().indexOf(value) > -1);
    })
});

//function Binddata(id) {
//    let value = $(id).val().toLowerCase();

//    $('#tblesclevUser').children().last().children().each(function () {
//        $(this).toggle($(this).find('.EM_UserName').text().toLowerCase().indexOf(value) > -1);
//    });
//}



function populateModalFields(escData) {
    $('#textgroupId').val(escData.id);
    $('#matcodeId').val(escData.escMatCode);
    $('#companyId').val(escData.companyId);
    $('#createdateId').val(escData.createdDate);
    $('#approverId').val(escData.approverID);
    $('#EscNameId').val(escData.escMatName);
    $('#EscDesId').val(escData.escMatDesc);
    $('#EscTypeId').val(escData.escMatType);
    $('#EscOwnId').val(escData.ownerID);

}
$("#btnClick").click(async function () {
    const value = $('#EscNameId').val();
    const Desc = $('#EscDesId').val();
    
    var IsName = await validatemscName(value, nameExistUrl);
    var IsDesc = await validateDescription(Desc);
    var IsType = await validaDateType();
    

    

    if (IsName && IsDesc && IsType) {
        $('#CreateForm').submit();
    }


});

async function validaDateType() {
    const errorElement = $('#mtType-error');
    var e = document.getElementById("EscTypeId");
    var text = e.options[e.selectedIndex].text;

    if (text == "") {
        errorElement.text('Select Escalation-Matrix Type')
            .addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('')
            .removeClass('field-validation-error');
        return true;
    }
       

    
}



$('#EscNameId').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validatemscName(value, nameExistUrl);
});

async function validatemscName(value, url) {
    const errorElement = $('#escmat-error');

    if (!value) {
        errorElement.text('Enter Escalation-Matrix Name')
            .addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.TeamName = value;


    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsTeamNameExist(url, data, OnError)
    ];
    
    return await CommonValidation(errorElement, validationResults);
}


$('#EscDesId').on('keydown keyup', async function () {

    const value = $(this).val();
    var sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await validateDescription(value);
});


async function validateDescription(value) {
    const errorElement = $('#des-error');

    if (!value) {
        errorElement.text('Enter Escalation-Matrix Description')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
    ];
    return await CommonValidation(errorElement, validationResults);
}

async function IsTeamNameExist(url, data, errorFunc) {

    return !data.TeamName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Escalation-Matrix Name already exists" : true;
}


$('#EscTypeId').on('change', async function () {
    await validaDateType();
})