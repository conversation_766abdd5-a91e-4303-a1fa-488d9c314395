﻿using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class UserActivityFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<UserActivity> UserActivities { get; set; }

    public CreateUserActivityCommand CreateUserActivityCommand { get; set; }

    public UserActivityFixture()
    {
        UserActivities = AutoUserActivityFixture.Create<List<UserActivity>>();

        CreateUserActivityCommand = AutoUserActivityFixture.Create<CreateUserActivityCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<UserActivityProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoUserActivityFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateUserActivityCommand>(p => p.LoginName, 10));
            fixture.Customize<UserActivity>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}