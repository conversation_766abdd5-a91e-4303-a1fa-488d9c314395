﻿QUnit.test("Site Configuration Module: Validate Name random input - Show exact failed validation rules", async assert => {
    const done = assert.async();
    const total = 200;
    let completed = 0;

    window.getAysncWithHandler = async () => false; // mock ajax call

    function generateEdgeCaseName() {
        const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        const length = Math.floor(Math.random() * 48) + 3;
        let name = "";

        for (let i = 0; i < length; i++) {
            name += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        if (Math.random() < 0.2) name = "_" + name;
        if (Math.random() < 0.2) name = "  " + name;
        if (Math.random() < 0.2) name += "--";
        if (Math.random() < 0.2) name += "..";
        if (Math.random() < 0.2) name += "_ _";
        if (Math.random() < 0.2) name += "_\t";
        if (Math.random() < 0.2) name = name.replace(/\s/g, "  ");
        if (Math.random() < 0.2) name += "@";
        if (Math.random() < 0.2) name = "<" + name;

        return name.trim();
    }

    function runAllValidationRules(value, isNameExist) {
        let results = [
            SpecialCharValidateCustom(value),  ShouldNotBeginWithUnderScore(value),  ShouldNotBeginWithNumber(value),
            ShouldNotBeginWithDotAndHyphen(value),ShouldNotConsecutiveDotAndHyphen(value), OnlyNumericsValidate(value),
            ShouldNotBeginWithSpace(value), SpaceWithUnderScore(value), ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),MultiUnderScoreRegex(value),SpaceAndUnderScoreRegex(value), minMaxCompanylength(value),secondChar(value)
        ];
        return results.filter(r => r !== true); // collect only failed messages
    }

    for (let i = 0; i < total; i++) {
        const name = generateEdgeCaseName();
        const $errorElement = $('<div></div>');

        try {
            const result = await validateName(name, 123, '/mock-url', $errorElement, 'Enter name', false);
            const failedMessages = runAllValidationRules(name);

            if (result) {
                assert.ok(true, `✅ Passed: "${name}"`);
            } else {
                const msgText = failedMessages.length > 0
                    ? failedMessages.join(" | ")
                    : $errorElement.text().trim() || "Unknown error";
                assert.ok(true, `❌ Failed: "${name}" → ${msgText}`);
            }

        } catch (err) {
            assert.notOk(true, `❌ Exception for "${name}": ${err.message}`);
        } finally {
            completed++;
            if (completed === total) done();
        }
    }
});


QUnit.module("Site Configuration Module", hooks => {
    let $fixture;
    let originalAjax, originalGetAsync;

    hooks.before(() => {
        // Add fixture
        $fixture = $('#qunit-fixture');
        $fixture.append($('#CreateForm').clone().attr('id', 'TestForm'));
        $fixture.append($('#siteTable').clone().attr('id', 'TestTable'));
        $fixture.append($('#siteName').clone());
        $fixture.append($('#selectLocation').clone());
        $fixture.append($('#companyNameDropdown').clone());
        $fixture.append($('#platformSelect').clone());
        $fixture.append($('#siteTypeCustom').clone());
        $fixture.append($('#drSiteTypeSelect').clone());

        // Mock AJAX
        originalAjax = $.ajax;
        originalGetAsync = window.getAysncWithHandler;

        $.ajax = sinon.stub().resolves({ success: true, data: { message: "Saved" } });
        window.getAysncWithHandler = sinon.stub().resolves(false);  // Site name does not exist
    });

    hooks.after(() => {
        $.ajax = originalAjax;
        window.getAysncWithHandler = originalGetAsync;
    });

    QUnit.test("validateName with empty input fails", async assert => {
        const result = await validateName("", null, "/dummy/url");
        assert.notOk(result, "❌ Empty name fails validation");
    });

    QUnit.test("validateDropDown works for empty and filled values", assert => {
        const $error = $('<span>');
        assert.notOk(validateDropDown("", "Select something", $error), "❌ Empty dropdown fails");
        assert.ok(validateDropDown("value", "Select something", $error), "✅ Filled dropdown passes");
    });

    QUnit.test("ValidateRadioButton detects selection", assert => {
        const $error = $('<span>');
        $('#siteTypeCustom .siteTypeRadio').first().prop('checked', false);
        assert.notOk(ValidateRadioButton($error), "❌ No selection fails");
        $('#siteTypeCustom .siteTypeRadio').first().prop('checked', true);
        assert.ok(ValidateRadioButton($error), "✅ Selection passes");
    });

    QUnit.test("applyPlatformIcons applies correct classes", assert => {
        const pr = $('<i>'), dr = $('<i>'), near = $('<i>');
        applyPlatformIcons('Physical', pr, dr, near);
        assert.ok(pr.hasClass('cp-prsites'), "✅ Physical PR icon applied");
        applyPlatformIcons('Virtual', pr, dr, near);
        assert.ok(pr.hasClass('cp-virtual-prsite'), "✅ Virtual PR icon applied");
    });

    QUnit.test("populateSiteFields sets values correctly", assert => {
        const data = {
            name: "Test Site",
            id: "1",
            location: "City",
            lat: "12.34",
            lng: "56.78",
            locationId: "loc1",
            companyName: "Comp",
            companyId: "comp1",
            platformType: "Physical",
            dataTemperature: "Hot",
            type: "prsite",
            typeId: "1738f813-6090-40cc-8869-25741a156f73"
        };
        populateSiteFields(data);
        assert.equal($('#siteName').val(), "Test Site", "✅ Name populated");
        assert.equal($('#selectLocation').val(), "City", "✅ Location populated");
        assert.equal($('#platformSelect').val(), "Physical", "✅ Platform populated");
    });

    QUnit.test("Create button clears form", assert => {
        $('#siteName').val("Filled");
        $('#platformSelect').val("Physical");
        $('#companyNameDropdown').val("Company");
        $('#selectLocation').val("City");

        function clearCreateForm() {
            $('#siteName').val('');
            $('#platformSelect').val('');
            $('#companyNameDropdown').val('');
            $('#selectLocation').val(''); 
        }
        clearCreateForm();

        assert.equal($('#siteName').val(), "", "✅ Name cleared");
        assert.equal($('#platformSelect').val(), null, "✅ Platform cleared");
        assert.equal($('#companyNameDropdown').val(), null, "✅ Company cleared");
        assert.equal($('#selectLocation').val(), null, "✅ Location cleared");
    });

    QUnit.test("Platform dropdown triggers icon updates", assert => {
        $('#platformSelect').val("Virtual").trigger("change");
        assert.ok(true, "✅ Change event triggered successfully (icons logic tested separately)");
    });
});