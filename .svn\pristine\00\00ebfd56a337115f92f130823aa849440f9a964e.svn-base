﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()

<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                MongoDB Detail Monitoring :
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
      
    </div>
    <div class="monitor_pages mt-1">
       <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div> 
        <div class="row g-2">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Operating System  Level Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                     <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>IP Address/Hostname</td>
                                    <td class="text-truncate"><span id="PR_Server_IpAddress"></span></td>
                                      <td class="text-truncate"><span  id="Server_IpAddress"></span></td>

                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mysql-data me-1"></i>MongoDB Status</td>
                                      <td class="text-truncate"><span  id="PRMongodbStatus"></span></td>
                                      <td class="text-truncate"><span  id="MongodbStatus"></span></td>
                                </tr>
                                
                            </tbody>

                        </table>
                    </div>
                </div>
                    <div class="card Card_Design_None"> 
                        <div class="card-header card-title">Database Level Monitoring</div>
                        <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                                <thead>
                                    <tr>
                                        <th title="Component">Component</th>
                                        <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                          <td class="text-truncate fw-semibold"><i class="text-secondary cp-database-success me-1"></i>Host Name</td>
                                    <td class="text-truncate"><span id="PR_Server_HostName"></span></td>
                                      <td class="text-truncate" ><span  id="Server_HostName"></span></td>
                                    </tr>
                                    <tr>
                                          <td  class="text-truncate fw-semibold"><i class="text-secondary cp-control-file-name me-1"></i>Version</td>
                                          <td  class="text-truncate"><span  id="PRDBVersion"></span></td>
                                         <td class="text-truncate"><span  id="DBVersion"></span></td>
                                    </tr>
                                    <tr>
                                         <td class="text-truncate fw-semibold"><i class="text-secondary cp-last-copied-transaction me-1"></i>Database Name </td>
                                         <td class="text-truncate"><span  id="PRDBName"></span></td>
                                         <td class="text-truncate"><span  id="DBName"></span></td>
                                    </tr>
                                    <tr>
                                         <td class="text-truncate fw-semibold"><i class="text-secondary cp-apply-finish-time me-1"></i>State Description</td>
                                         <td class="text-truncate"><span  id="PRStateDescription"></span></td>
                                         <td class="text-truncate"><span  id="StateDescription"></span></td>
                                    </tr>
                                    <tr>
                                         <td class="text-truncate fw-semibold"><i class="text-secondary cp-datas me-1"></i>Health</td>
                                         <td class="text-truncate"><span  id="PRhealth"></span></td>
                                         <td class="text-truncate"><span  id="Health"></span></td>
                                    </tr>
                                    <tr>
                                         <td class="text-truncate fw-semibold"><i class="text-secondary cp-data-lag me-1"></i>lastHeartbeatmessage</td>
                                         <td class="text-truncate"><span  id="PRlastHeartbeatMessage"></span></td>
                                    <td class="text-truncate"><span id="LastHeartbeatMessage"></span></td>
                                        
                                    </tr>

                                    <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-data-lag me-1"></i>Datalag</td>
                                    @* <td class="text-truncate"> <i class="cp-time text-primary mt-2"></i> <span id="PR_Datalag"></span></td> *@
                                         <td class="text-truncate"><span  id="Datalag"></span></td>
                                   
                                    </tr>


                                </tbody>
                            </table>
                        </div>
                    </div>
                 </div> 
            
            <div class="col-5 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                    </div>
                </div>
                
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Replication Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                     <td class="text-truncate fw-semibold"><i class="text-secondary cp-database-success me-1"></i>ReplicaSet Name </td>
                                     <td class="text-truncate"><span  id="PRreplicaSetName"></span></td>
                                    <td class="text-truncate"><span id="ReplicaSetName"></span></td>
                                </tr>
                                <tr>
                                     <td class="text-truncate fw-semibold"><i class="text-secondary cp-control-file-name me-1"></i>MemberID</td>
                                     <td class="text-truncate"><span  id="PRmemberID"></span></td>
                                     <td class="text-truncate"><span  id="MemberID"></span></td>
                                </tr>
                                <tr>
                                     <td class="text-truncate fw-semibold"><i class="text-secondary cp-last-copied-transaction me-1"></i>Current Priority </td>
                                     <td class="text-truncate"><span  id="PRCurrentPriority"></span></td>
                                     <td class="text-truncate"><span  id="CurrentPriority"></span></td>
                                </tr>
                                

                                @* <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-data-lag me-1"></i>Datalag</td>
                                     <td class="text-truncate"><span  id="PR_Datalag"></span></td>
                                     <td class="text-truncate"><span  id="PR_Datalag"></span></td>
                                   
                                </tr> *@


                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
            <div class="col-xl-6 d-grid">
                <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                    <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                              
                            </tbody>

                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
@* <script src="~/js/Monitoring/MonitoringDb2hadr.js"></script> *@
<script src="~/js/monitoring/monitoringmongodb.js"></script>
