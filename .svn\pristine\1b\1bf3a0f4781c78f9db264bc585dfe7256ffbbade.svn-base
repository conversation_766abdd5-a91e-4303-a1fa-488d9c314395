﻿using ContinuityPatrol.Application.Features.ImpactActivity.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ImpactActivity.Events;

public class CreateImpactActivityEventTests : IClassFixture<ImpactActivityFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ImpactActivityFixture _impactActivityFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ImpactActivityCreatedEventHandler _handler;

    public CreateImpactActivityEventTests(ImpactActivityFixture impactActivityFixture,
        UserActivityFixture userActivityFixture)
    {
        _impactActivityFixture = impactActivityFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");

        var mockImpactActivityEventLogger = new Mock<ILogger<ImpactActivityCreatedEventHandler>>();

        _mockUserActivityRepository = ImpactActivityRepositoryMocks.CreateImpactActivityEventRepository(_userActivityFixture.UserActivities);

        _handler = new ImpactActivityCreatedEventHandler(mockLoggedInUserService.Object,
            mockImpactActivityEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateImpactActivityEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_impactActivityFixture.ImpactActivityCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_impactActivityFixture.ImpactActivityCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}