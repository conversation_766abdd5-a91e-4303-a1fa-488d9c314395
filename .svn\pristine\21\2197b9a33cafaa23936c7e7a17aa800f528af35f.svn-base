﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class RoboCopyJobProfile : Profile
{
    public RoboCopyJobProfile()
    {
        CreateMap<CreateRoboCopyJobCommand, RoboCopyJobViewModel>().ReverseMap();
        CreateMap<UpdateRoboCopyJobCommand, RoboCopyJobViewModel>().ReverseMap();

        CreateMap<CreateRoboCopyJobCommand, RoboCopyJob>().ReverseMap();
        CreateMap<UpdateRoboCopyJobCommand, RoboCopyJob>().ForMember(desc => desc.Id, y => y.Ignore());

        CreateMap<RoboCopyJob, RoboCopyJobListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<RoboCopyJob, RoboCopyJobDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<RoboCopyJob>,PaginatedResult<RoboCopyJobListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}