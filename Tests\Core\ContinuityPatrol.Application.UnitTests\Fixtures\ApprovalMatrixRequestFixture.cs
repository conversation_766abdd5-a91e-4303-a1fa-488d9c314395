using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Withdraw;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ApprovalMatrixRequestFixture : IDisposable
{
    public List<ApprovalMatrixRequest> ApprovalMatrixRequests { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateApprovalMatrixRequestCommand CreateApprovalMatrixRequestCommand { get; set; }
    public UpdateApprovalMatrixRequestCommand UpdateApprovalMatrixRequestCommand { get; set; }
    public DeleteApprovalMatrixRequestCommand DeleteApprovalMatrixRequestCommand { get; set; }
    public WithdrawApprovalMatrixRequestCommand WithdrawApprovalMatrixRequestCommand { get; set; }
    public IMapper Mapper { get; set; }

    public ApprovalMatrixRequestFixture()
    {
        ApprovalMatrixRequests = new List<ApprovalMatrixRequest>
        {
            new ApprovalMatrixRequest
            {
                ReferenceId = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ApprovalMatrixId = Guid.NewGuid().ToString(),
                ProcessName = "TestProcess1",
                Description = "Test approval request description",
                UserName = "TestUser1",
                Status = "Pending",
                Approvers = "Manager1, Director1",
                Message = "Approval request submitted",
                StartDateTime = DateTime.Now,
                EndDateTime = DateTime.Now.AddDays(7),
                IsRequest = true,
                IsActive = true
            }
        };

        ApprovalMatrixRequests = AutoApprovalMatrixRequestFixture.Create<List<ApprovalMatrixRequest>>();
        UserActivities = AutoApprovalMatrixRequestFixture.Create<List<UserActivity>>();
        CreateApprovalMatrixRequestCommand = AutoApprovalMatrixRequestFixture.Create<CreateApprovalMatrixRequestCommand>();
        UpdateApprovalMatrixRequestCommand = AutoApprovalMatrixRequestFixture.Create<UpdateApprovalMatrixRequestCommand>();
        DeleteApprovalMatrixRequestCommand = AutoApprovalMatrixRequestFixture.Create<DeleteApprovalMatrixRequestCommand>();
        WithdrawApprovalMatrixRequestCommand = AutoApprovalMatrixRequestFixture.Create<WithdrawApprovalMatrixRequestCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ApprovalMatrixRequestProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoApprovalMatrixRequestFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateApprovalMatrixRequestCommand>(p => p.ProcessName, 100));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.ProcessName, "TestProcess"));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.Description, "Test approval request description"));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.UserName, "TestUser"));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.Status, "Pending"));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.Approvers, "Manager1, Director1"));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.StartDateTime, DateTime.Now));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.EndDateTime, DateTime.Now.AddDays(7)));
            fixture.Customize<CreateApprovalMatrixRequestCommand>(c => c.With(a => a.IsRequest, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateApprovalMatrixRequestCommand>(p => p.ProcessName, 100));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.ProcessName, "UpdatedProcess"));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.Description, "Updated approval request description"));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.UserName, "UpdatedUser"));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.Status, "InProgress"));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.Approvers, "Manager2, Director2"));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.StartDateTime, DateTime.Now));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.EndDateTime, DateTime.Now.AddDays(14)));
            fixture.Customize<UpdateApprovalMatrixRequestCommand>(c => c.With(a => a.IsRequest, true));

            fixture.Customize<DeleteApprovalMatrixRequestCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));

            fixture.Customize<WithdrawApprovalMatrixRequestCommand>(c => c.With(a => a.Id, Guid.NewGuid().ToString()));

            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.RequestId, Guid.NewGuid().ToString()));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.ApprovalMatrixId, Guid.NewGuid().ToString()));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.IsActive, true));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.ProcessName, "TestProcess"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.Description, "Test approval request description"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.UserName, "TestUser"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.Status, "Pending"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.Approvers, "Manager1, Director1"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.Message, "Approval request submitted"));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.StartDateTime, DateTime.Now));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.EndDateTime, DateTime.Now.AddDays(7)));
            fixture.Customize<ApprovalMatrixRequest>(c => c.With(a => a.IsRequest, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
