﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionResultModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries;

public class GetWorkflowActionResultPaginatedListQueryHandlerTests : IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowActionResultFixture _workflowActionResultFixture;

    private readonly Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly GetWorkflowActionResultPaginatedListQueryHandler _handler;

    public GetWorkflowActionResultPaginatedListQueryHandlerTests(WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowActionResultFixture = workflowActionResultFixture;

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetPaginatedWorkflowActionResultRepository(_workflowActionResultFixture.WorkflowActionResults);

        _handler = new GetWorkflowActionResultPaginatedListQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);

        _workflowActionResultFixture.WorkflowActionResults[0].WorkflowActionName = "WorkflowActionResult_Test";
        _workflowActionResultFixture.WorkflowActionResults[0].Status = "Pending";

        _workflowActionResultFixture.WorkflowActionResults[1].WorkflowActionName = "Co-Domains";
        _workflowActionResultFixture.WorkflowActionResults[1].Status = "Stop";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionResultListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowActionResult_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Stop" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionResultListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowActionName.ShouldBe("Co-Domains");

        result.Data[0].Status.ShouldBe("Stop");

        result.Data[0].Message.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowOperationId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowOperationGroupId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ActionId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ConditionActionId.ShouldBeGreaterThan(0);

        result.Data[0].SkipStep.ShouldBeTrue();

        result.Data[0].IsReload.ShouldBeGreaterThan(0);

        result.Data[0].Direction.ShouldBeGreaterThan(0.ToString());
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionResultListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_WorkflowActionResult_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "workflowactionname=WorkflowActionResult_Test;Status=Pending" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowActionResultListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowActionName.ShouldBe("WorkflowActionResult_Test");

        result.Data[0].Status.ShouldBe("Pending");

        result.Data[0].Message.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Message);

        result.Data[0].WorkflowOperationId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationId);

        result.Data[0].WorkflowOperationGroupId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationGroupId);

        result.Data[0].InfraObjectId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].InfraObjectId);

        result.Data[0].ActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ActionId);

        result.Data[0].ConditionActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ConditionActionId);

        result.Data[0].SkipStep.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].SkipStep);

        result.Data[0].IsReload.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].IsReload);

        result.Data[0].Direction.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Direction);
    }


    [Fact]
    public async Task Handle_Call_PaginatedListAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowActionResultPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowActionResultRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}