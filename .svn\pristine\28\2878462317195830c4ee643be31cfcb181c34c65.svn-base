﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetRunningUserList;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowOperationService
{
    Task<List<WorkflowOperationListVm>> GetWorkflowOperationList();
    Task<BaseResponse> DeleteAsync(string id);
    Task<BaseResponse> CreateAsync(CreateWorkflowOperationCommand createWorkflowOperationCommand);
    Task<BaseResponse> UpdateAsync(UpdateWorkflowOperationCommand updateWorkflowOperationCommand);
    Task<WorkflowOperationDetailVm> GetWorkflowOperationById(string id);      
    Task<ProfileExecutorByBusinessServiceIdVm> GetProfileExecutorByBusinessServiceId(string businessServiceId);
    Task<List<GetDescriptionByStartTimeAndEndTimeListVm>> GetDescriptionByStartTimeAndEndTime(string startTime, string endTime,string runMode);
    Task<List<WorkflowOperationRunningUserListVm>> GetWorkflowOperationByRunningUserList();
    Task<List<WorkflowOperationDrDrillVm>> GetDrDrillDetailsByBusinessServiceId(string businessServiceId);
    Task<PaginatedResult<WorkflowOperationListVm>> GetPaginatedWorkflowOperation(GetWorkflowOperationPaginatedListQuery query);
}