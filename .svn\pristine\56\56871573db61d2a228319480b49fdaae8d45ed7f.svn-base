using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DriftCategoryMasterFixture : IDisposable
{
    public CreateDriftCategoryMasterCommand CreateDriftCategoryMasterCommand { get; }
    public CreateDriftCategoryMasterResponse CreateDriftCategoryMasterResponse { get; }
    public UpdateDriftCategoryMasterCommand UpdateDriftCategoryMasterCommand { get; }
    public UpdateDriftCategoryMasterResponse UpdateDriftCategoryMasterResponse { get; }
    public DeleteDriftCategoryMasterCommand DeleteDriftCategoryMasterCommand { get; }
    public DeleteDriftCategoryMasterResponse DeleteDriftCategoryMasterResponse { get; }
    public DriftCategoryMasterDetailVm DriftCategoryMasterDetailVm { get; }
    public List<DriftCategoryMasterListVm> DriftCategoryMasterListVm { get; }
    public GetDriftCategoryMasterPaginatedListQuery GetDriftCategoryMasterPaginatedListQuery { get; }
    public PaginatedResult<DriftCategoryMasterListVm> DriftCategoryMasterPaginatedResult { get; }
    public GetDriftCategoryMasterNameUniqueQuery GetDriftCategoryMasterNameUniqueQuery { get; }

    public DriftCategoryMasterFixture()
    {
        var fixture = new Fixture();

        // Configure fixture for enterprise drift category scenarios
        fixture.Customize<CreateDriftCategoryMasterCommand>(c => c
            .With(x => x.CategoryName, "Enterprise Configuration Drift")
            .With(x => x.Logo, "enterprise_drift_logo.png"));

        fixture.Customize<CreateDriftCategoryMasterResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Category Master created successfully!")
            .With(x => x.Success, true));

        fixture.Customize<UpdateDriftCategoryMasterCommand>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.CategoryName, "Enterprise Updated Configuration Drift")
            .With(x => x.Logo, "enterprise_updated_drift_logo.png"));

        fixture.Customize<UpdateDriftCategoryMasterResponse>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.Message, "Enterprise Drift Category Master updated successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DeleteDriftCategoryMasterResponse>(c => c
            .With(x => x.IsActive, false)
            .With(x => x.Message, "Enterprise Drift Category Master deleted successfully!")
            .With(x => x.Success, true));

        fixture.Customize<DriftCategoryMasterDetailVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.CategoryName, "Enterprise Detail Configuration Drift")
            .With(x => x.Logo, "enterprise_detail_drift_logo.png"));

        fixture.Customize<DriftCategoryMasterListVm>(c => c
            .With(x => x.Id, Guid.NewGuid().ToString())
            .With(x => x.CategoryName, "Enterprise List Configuration Drift")
            .With(x => x.Logo, "enterprise_list_drift_logo.png"));

        // Initialize properties
        CreateDriftCategoryMasterCommand = fixture.Create<CreateDriftCategoryMasterCommand>();
        CreateDriftCategoryMasterResponse = fixture.Create<CreateDriftCategoryMasterResponse>();
        UpdateDriftCategoryMasterCommand = fixture.Create<UpdateDriftCategoryMasterCommand>();
        UpdateDriftCategoryMasterResponse = fixture.Create<UpdateDriftCategoryMasterResponse>();
        DeleteDriftCategoryMasterCommand = new DeleteDriftCategoryMasterCommand { Id = Guid.NewGuid().ToString() };
        DeleteDriftCategoryMasterResponse = fixture.Create<DeleteDriftCategoryMasterResponse>();
        DriftCategoryMasterDetailVm = fixture.Create<DriftCategoryMasterDetailVm>();
        DriftCategoryMasterListVm = fixture.CreateMany<DriftCategoryMasterListVm>(5).ToList();
        
        GetDriftCategoryMasterPaginatedListQuery = fixture.Create<GetDriftCategoryMasterPaginatedListQuery>();
        DriftCategoryMasterPaginatedResult = new PaginatedResult<DriftCategoryMasterListVm>
        {
            Data = fixture.CreateMany<DriftCategoryMasterListVm>(10).ToList(),
            TotalCount = 10,
            PageSize = 10,
            Succeeded = true
        };
        
        GetDriftCategoryMasterNameUniqueQuery = new GetDriftCategoryMasterNameUniqueQuery { Name = "Enterprise Configuration Drift" };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
