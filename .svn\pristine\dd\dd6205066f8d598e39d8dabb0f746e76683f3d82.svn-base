﻿namespace ContinuityPatrol.Application.Features.BusinessService.Events.DashboardViewEvent.Delete;

public class
    BusinessServiceDashboardViewDeletedEventHandler : INotificationHandler<BusinessServiceDashboardViewDeletedEvent>
{
    private readonly IDashboardViewRepository _dashboardViewRepository;
    private readonly ILogger<BusinessServiceDashboardViewDeletedEventHandler> _logger;

    public BusinessServiceDashboardViewDeletedEventHandler(IDashboardViewRepository dashboardViewRepository,
        ILogger<BusinessServiceDashboardViewDeletedEventHandler> logger)
    {
        _dashboardViewRepository = dashboardViewRepository;
        _logger = logger;
    }

    public async Task Handle(BusinessServiceDashboardViewDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var dashboardViewList =
            await _dashboardViewRepository.GetBusinessViewListByBusinessServiceId(deletedEvent.BusinessServiceId);

        dashboardViewList.ForEach(dashboard => dashboard.IsActive = false );

        await _dashboardViewRepository.UpdateRangeAsync(dashboardViewList);

        _logger.LogInformation(
            $"OperationalService :: DashboardViewDeleteEvent '{deletedEvent.BusinessServiceName}' deleted successfully.");
    }
}