﻿using AutoFixture;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class GlobalSettingControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<ILogger<GlobalSettingsController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  GlobalSettingsController _controller;

        public GlobalSettingControllerShould()
        {
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new GlobalSettingsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockLoggedInUserService.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }
        [Fact]
        public async Task List_ReturnsView()
        {
            var result = await _controller.List() as ViewResult;

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetList_ReturnsJsonResultWithGlobalSettings()
        {
            var globalSettingsList = new List<GlobalSettingListVm>();
            _mockDataProvider.Setup(dp => dp.GlobalSettings.GetGlobalSettingsList()).ReturnsAsync(globalSettingsList);

            var result = await _controller.GetGlobalSettingList() as JsonResult;
            var data = result?.Value as List<GlobalSettingModel>;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesGlobalSettingSuccessfully()
        {
            // Arrange
            var globalSettingModel = new AutoFixture.Fixture().Create<GlobalSettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateGlobalSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesGlobalSettingSuccessfully()
        {
            // Arrange
            var globalSettingModel = new AutoFixture.Fixture().Create<GlobalSettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateGlobalSettingCommand ();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;
            var json = JsonConvert.SerializeObject(result);
            Assert.IsType<JsonResult>(result);
            
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException()
        {
            
            var globalSettingModel = new AutoFixture.Fixture().Create<GlobalSettingModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(new CreateGlobalSettingCommand());
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(It.IsAny<CreateGlobalSettingCommand>())).ThrowsAsync(new Exception("General error"));

            
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;
            var jsonResponse = result?.Value as dynamic;
            var json = JsonConvert.SerializeObject(jsonResponse);
            Assert.IsType<JsonResult>(result);
            Assert.Contains("\"message\":\"General error\"", json);
            Assert.NotNull(result);
            
            
        }

        [Fact]
        public void GetRandomString_ReturnsStringOfSpecifiedLength()
        {
            // Arrange
            var length = 10;

            // Act
            var result = _controller.GetRandomString(length);

            // Assert
            Assert.Equal(length, result.Length);
        }

        // Exception handling tests
        [Fact]
        public async Task GetGlobalSettingList_Returns_EmptyJson_When_Exception_Occurs()
        {
            // Arrange
            _mockDataProvider.Setup(dp => dp.GlobalSettings.GetGlobalSettingsList()).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetGlobalSettingList() as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Create()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel { GlobalSettingKey = "TestKey" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", ""); // Empty id for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateGlobalSettingCommand();
            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(createCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(createCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_ValidationException_Occurs_Update()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel { GlobalSettingKey = "TestKey" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateGlobalSettingCommand();
            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Field", "Validation error occurred"));
            var validationException = new ValidationException(validationResult);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Returns_Json_With_Error_When_Exception_Occurs_Update()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel { GlobalSettingKey = "TestKey" };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateGlobalSettingCommand();
            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":false", json);
            Assert.Contains("\"message\":", json);
        }

        [Fact]
        public async Task CreateOrUpdate_Handles_SecurityKey_With_Short_Password()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "SecurityKey",
                PasswordProtection = "shortpass" // Less than 44 characters
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Verify that PasswordProtection was processed (encrypted + random string)
            Assert.True(globalSettingModel.PasswordProtection.Length >= 44);
            Assert.Contains("$", globalSettingModel.PasswordProtection);
        }

        [Fact]
        public async Task CreateOrUpdate_Handles_SecurityKey_With_Long_Password()
        {
            // Arrange
            var longPassword = new string('a', 50); // More than 44 characters
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "SecurityKey",
                PasswordProtection = longPassword
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Verify that PasswordProtection was not modified (already long enough)
            Assert.Equal(longPassword, globalSettingModel.PasswordProtection);
        }

        [Fact]
        public async Task CreateOrUpdate_Handles_NonSecurityKey_ClearsPasswordProtection()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "SomeOtherKey",
                PasswordProtection = "somepassword"
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var createCommand = new CreateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Created" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<CreateGlobalSettingCommand>(globalSettingModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Verify that PasswordProtection was cleared for non-SecurityKey
            Assert.Equal("", globalSettingModel.PasswordProtection);
        }

        // ===== ADDITIONAL TESTS TO COVER UNCOVERED LINES =====

        [Fact]
        public async Task CreateOrUpdate_UpdatesGlobalSettingSuccessfully_CoverUpdatePath()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "TestKey",
                PasswordProtection = "testpassword"
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "123"); // Non-empty id to trigger update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
            Assert.Contains("\"Message\":\"Updated\"", json);

            // Verify the update path was called
            _mockDataProvider.Verify(dp => dp.GlobalSettings.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public void GetRandomString_HandlesExceptionAndReturnsEmptyString()
        {
            // Arrange
            var length = -1; // This should cause an exception in the random string generation

            // Act
            var result = _controller.GetRandomString(length);

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void GetRandomString_HandlesLargeLength()
        {
            // Arrange
            var length = 0; // Edge case that might cause issues

            // Act
            var result = _controller.GetRandomString(length);

            // Assert
            Assert.Equal(string.Empty, result.Length == 0 ? string.Empty : result);
        }

        [Fact]
        public void GetRandomString_ReturnsValidCharacters()
        {
            // Arrange
            var length = 20;
            const string validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            // Act
            var result = _controller.GetRandomString(length);

            // Assert
            Assert.Equal(length, result.Length);
            Assert.All(result, c => Assert.Contains(c, validChars));
        }

        [Fact]
        public async Task CreateOrUpdate_SecurityKeyWithUpdatePath()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "SecurityKey",
                PasswordProtection = "shortpass" // Less than 44 characters
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "456"); // Non-empty id to trigger update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Verify that PasswordProtection was processed (encrypted + random string)
            Assert.True(globalSettingModel.PasswordProtection.Length >= 44);
            Assert.Contains("$", globalSettingModel.PasswordProtection);

            // Verify the update path was called
            _mockDataProvider.Verify(dp => dp.GlobalSettings.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_NonSecurityKeyWithUpdatePath()
        {
            // Arrange
            var globalSettingModel = new GlobalSettingModel
            {
                GlobalSettingKey = "SomeOtherKey",
                PasswordProtection = "somepassword"
            };
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("Id", "789"); // Non-empty id to trigger update path
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var updateCommand = new UpdateGlobalSettingCommand();
            var response = new BaseResponse { Success = true, Message = "Updated" };

            _mockLoggedInUserService.Setup(service => service.UserId).Returns("user-id");
            _mockMapper.Setup(mapper => mapper.Map<UpdateGlobalSettingCommand>(globalSettingModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.GlobalSettings.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(globalSettingModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            // Verify that PasswordProtection was cleared for non-SecurityKey
            Assert.Equal("", globalSettingModel.PasswordProtection);

            // Verify the update path was called
            _mockDataProvider.Verify(dp => dp.GlobalSettings.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public void GetRandomString_HandlesExceptionWithExtremeValues()
        {
            // Arrange - Test with extreme values that might cause exceptions
            var extremeLength = int.MaxValue;

            // Act
            var result = _controller.GetRandomString(extremeLength);

            // Assert
            // Should return empty string when exception occurs
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void GetRandomString_HandlesNegativeLength()
        {
            // Arrange
            var negativeLength = -10;

            // Act
            var result = _controller.GetRandomString(negativeLength);

            // Assert
            // Should return empty string when exception occurs due to negative length
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void GetRandomString_ReturnsEmptyStringForZeroLength()
        {
            // Arrange
            var zeroLength = 0;

            // Act
            var result = _controller.GetRandomString(zeroLength);

            // Assert
            Assert.Equal(0, result.Length);
        }

        [Fact]
        public void GetRandomString_GeneratesDifferentStringsOnMultipleCalls()
        {
            // Arrange
            var length = 15;

            // Act
            var result1 = _controller.GetRandomString(length);
            var result2 = _controller.GetRandomString(length);
            var result3 = _controller.GetRandomString(length);

            // Assert
            Assert.Equal(length, result1.Length);
            Assert.Equal(length, result2.Length);
            Assert.Equal(length, result3.Length);

            // While theoretically possible to be the same, it's extremely unlikely
            // This tests that the random generation is working
            Assert.True(result1 != result2 || result2 != result3 || result1 != result3);
        }
    }
}
