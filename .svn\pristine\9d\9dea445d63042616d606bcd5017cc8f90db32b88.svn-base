using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowExecutionTempRepositoryTests : IClassFixture<WorkflowExecutionTempFixture>
    {
        private readonly WorkflowExecutionTempFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowExecutionTempRepository _repository;

        public WorkflowExecutionTempRepositoryTests(WorkflowExecutionTempFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            var config = new ConfigurationBuilder().Build();
            _repository = new WorkflowExecutionTempRepository(_dbContext, config);
        }

        [Fact]
        public async Task FilterByWorkflowId_ReturnsMatchingEntities()
        {
            await _dbContext.WorkflowExecutionTemps.AddRangeAsync(_fixture.WorkflowExecutionTempList);
            await _dbContext.SaveChangesAsync();

            var workflowId = _fixture.WorkflowExecutionTempList.First().WorkflowId;
            var result = await _repository.FilterByWorkflowId(workflowId);

            Assert.All(result, x => Assert.Equal(workflowId, x.WorkflowId));
        }

        [Fact]
        public async Task GetFirstMatchedIdAndExistenceAsync_ReturnsCorrectTuples()
        {
            await _dbContext.WorkflowExecutionTemps.AddRangeAsync(_fixture.WorkflowExecutionTempList);
            await _dbContext.SaveChangesAsync();

            var workflowIds = _fixture.WorkflowExecutionTempList.Select(x => x.WorkflowId).ToList();
            var result = await _repository.GetFirstMatchedIdAndExistenceAsync(workflowIds);

            Assert.All(result, x => Assert.Contains(workflowIds, id => id == x.WorkflowId));
            Assert.All(result, x => Assert.True(x.Exists));
        }

        [Fact]
        public async Task GetByWorkflowIdAsync_ReturnsLastEntity()
        {
            var workflowId = Guid.NewGuid().ToString();
            var entities = _fixture.WorkflowExecutionTempList.Select(x =>
            {
                x.WorkflowId = workflowId;
                return x;
            }).ToList();
            await _dbContext.WorkflowExecutionTemps.AddRangeAsync(entities);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetByWorkflowIdAsync(workflowId);

            Assert.NotNull(result);
            Assert.Equal(workflowId, result.WorkflowId);
        }

        [Fact]
        public async Task GetByWorkflowIdAsync_ReturnsNull_WhenNotExists()
        {
            var result = await _repository.GetByWorkflowIdAsync("non-existent-id");

            Assert.Null(result);
        }
    }
}