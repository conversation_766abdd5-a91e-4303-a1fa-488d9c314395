using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ICyberAirGapRepository : IRepository<CyberAirGap>
{
    Task<bool> IsNameExist(string name, string id);
    Task<List<CyberAirGap>> GetAirGapBySiteId(string id);
    Task<List<CyberAirGap>> GetAirGapByServerId(string id);
    Task<List<CyberAirGap>> GetAirGapByComponentId(string id);
}