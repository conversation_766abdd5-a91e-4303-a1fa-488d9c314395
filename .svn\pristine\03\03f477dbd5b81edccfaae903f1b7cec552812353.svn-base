﻿using ContinuityPatrol.Application.Features.AccessManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AccessManager.Queries;

public class GetAccessManagerDetailQueryHandlerTests : IClassFixture<AccessManagerFixture>, IClassFixture<CompanyFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<IAccessManagerRepository> _mockAccessManagerRepository;
    private readonly GetAccessManagerDetailQueryHandler _handler;
    private readonly GetCompanyListQueryHandler _companyName;

    public GetAccessManagerDetailQueryHandlerTests(AccessManagerFixture accessManagerFixture, CompanyFixture companyFixture)
    {
        _accessManagerFixture = accessManagerFixture;
        _companyFixture = companyFixture;

        _mockAccessManagerRepository = AccessManagerRepositoryMocks.GetAccessManagerRepository(_accessManagerFixture.AccessManagers);
        var mockCompanyRepository = CompanyRepositoryMocks.GetCompanyRepository(_companyFixture.Companies);

        _handler = new GetAccessManagerDetailQueryHandler(_accessManagerFixture.Mapper, _mockAccessManagerRepository.Object);
        _companyName = new GetCompanyListQueryHandler(_companyFixture.Mapper, mockCompanyRepository.Object);

        _companyFixture.Companies[0].ReferenceId = "5287bf71-be04-4c55-97e8-a65b7ff17114";
        _companyFixture.Companies[1].ParentId = _companyFixture.Companies[0].ReferenceId;

    }

    [Fact]
    public async Task Handle_Return_AccessManagerDetails_When_ValidAccessManagerUserId()
    {
        var result = await _handler.Handle(new GetAccessManagerDetailQuery { Id = _accessManagerFixture.AccessManagers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<AccessManagerDetailVm>();

        result.Id.ShouldBe(_accessManagerFixture.AccessManagers[0].ReferenceId);

        result.RoleName.ShouldBe(_accessManagerFixture.AccessManagers[0].RoleName);

        result.Properties.ShouldBe(_accessManagerFixture.AccessManagers[0].Properties);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidAccessManagerUserId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetAccessManagerDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAccessManagerDetailQuery { Id = _accessManagerFixture.AccessManagers[0].ReferenceId }, CancellationToken.None);

        _mockAccessManagerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_ChildCompanies()
    {
        var childCompany = await _companyName.Handle(new GetCompanyListQuery(), CancellationToken.None);

        foreach (var list in childCompany)
        {
            var compare = _companyFixture.Companies[0].IsParent = false == list.IsParent;

            if (compare.Equals(true))
            {
                _companyFixture.Companies[0].DisplayName = list.DisplayName; // Child Company
            }
            else
            {
                list.IsParent.ShouldBe(true);
                _companyFixture.Companies[0].DisplayName = list.DisplayName; // Parent Company
            }
        }
    }
}