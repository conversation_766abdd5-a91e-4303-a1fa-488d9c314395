﻿using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Queries
{
    public class GetRoboCopyPaginatedListQueryHandlerTests
    {
        private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetRoboCopyPaginatedListQueryHandler _handler;

        public GetRoboCopyPaginatedListQueryHandlerTests()
        {
            _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetRoboCopyPaginatedListQueryHandler(_mockMapper.Object, _mockRoboCopyRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenDataExists()
        {
            var query = new GetRoboCopyPaginatedListQuery { PageNumber = 1, PageSize = 5, SearchString = "test" };

            var roboCopies = new List<Domain.Entities.RoboCopy>
            {
                new Domain.Entities.RoboCopy { Id = 1, Name = "Test1" },
                new Domain.Entities.RoboCopy { Id = 2, Name = "Test2" }
            };

            var paginatedResult = new PaginatedResult<RoboCopyListVm>
            {
                TotalCount = roboCopies.Count,
                Data = roboCopies.Select(rc => new RoboCopyListVm { Id = rc.Name, Name = rc.Name }).ToList()
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopies.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<RoboCopyListVm>(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns((Domain.Entities.RoboCopy source) => new RoboCopyListVm { Id = source.Name, Name = source.Name });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal(roboCopies.Count, result.TotalCount);
            _mockRoboCopyRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<RoboCopyListVm>(It.IsAny<Domain.Entities.RoboCopy>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyList_WhenNoDataExists()
        {
            var query = new GetRoboCopyPaginatedListQuery { PageNumber = 1, PageSize = 5, SearchString = "test" };

            var roboCopies = new List<Domain.Entities.RoboCopy>();

            var paginatedResult = new PaginatedResult<RoboCopyListVm>
            {
                TotalCount = 0,
                Data = new List<RoboCopyListVm>()
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopies.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<RoboCopyListVm>(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns((Domain.Entities.RoboCopy source) => new RoboCopyListVm { Id = source.Name, Name = source.Name });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);
            _mockRoboCopyRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldApplySearchFilter_WhenSearchStringIsProvided()
        {
            var query = new GetRoboCopyPaginatedListQuery { PageNumber = 1, PageSize = 5, SearchString = "Test" };

            var roboCopies = new List<Domain.Entities.RoboCopy>
            {
                new Domain.Entities.RoboCopy { Id = 1, Name = "Test1" },
                new Domain.Entities.RoboCopy { Id = 2, Name = "Another" }
            };

            var paginatedResult = new PaginatedResult<RoboCopyListVm>
            {
                TotalCount = 1,
                Data = roboCopies.Where(rc => rc.Name.Contains("Test"))
                                  .Select(rc => new RoboCopyListVm { Id = rc.ReferenceId, Name = rc.Name })
                                  .ToList()
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopies.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<RoboCopyListVm>(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns((Domain.Entities.RoboCopy source) => new RoboCopyListVm { Id = source.Name, Name = source.Name });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal(1, result.TotalCount);
            _mockRoboCopyRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldHandleEmptySearchString_WhenNoSearchProvided()
        {
            var query = new GetRoboCopyPaginatedListQuery { PageNumber = 1, PageSize = 5, SearchString = "" };

            var roboCopies = new List<Domain.Entities.RoboCopy>
            {
                new Domain.Entities.RoboCopy { Id = 1, Name = "Test1" },
                new Domain.Entities.RoboCopy { Id = 2, Name = "Test2" }
            };

            var paginatedResult = new PaginatedResult<RoboCopyListVm>
            {
                TotalCount = roboCopies.Count,
                Data = roboCopies.Select(rc => new RoboCopyListVm { Id = rc.Name, Name = rc.Name }).ToList()
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetPaginatedQuery())
                .Returns(roboCopies.AsQueryable());

            _mockMapper
                .Setup(mapper => mapper.Map<RoboCopyListVm>(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns((Domain.Entities.RoboCopy source) => new RoboCopyListVm { Id = source.Name, Name = source.Name });

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal(roboCopies.Count, result.TotalCount);
            _mockRoboCopyRepository.Verify(repo => repo.GetPaginatedQuery(), Times.Once);
        }
    }
}
