using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftCategoryMasterRepositoryTests
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftCategoryMasterRepository _repository;

    public DriftCategoryMasterRepositoryTests()
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DriftCategoryMasterRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftCategoryMaster = new DriftCategoryMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CategoryName = "TestCategory",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(driftCategoryMaster);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftCategoryMaster.CategoryName, result.CategoryName);
        Assert.Single(_dbContext.DriftCategoryMasters);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var driftCategoryMaster = new DriftCategoryMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CategoryName = "ExistingCategory",
            IsActive = true
        };
        await _repository.AddAsync(driftCategoryMaster);

        // Act
        var result = await _repository.IsNameExist("ExistingCategory", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Act
        var result = await _repository.IsNameExist("NonExistentCategory", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var driftCategoryMaster = new DriftCategoryMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CategoryName = "SameCategory",
            IsActive = true
        };
        await _repository.AddAsync(driftCategoryMaster);

        // Act
        var result = await _repository.IsNameExist("SameCategory", driftCategoryMaster.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var categories = new List<DriftCategoryMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category3", IsActive = false }
        };
         _dbContext.DriftCategoryMasters.AddRange(categories);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftCategoryMaster = new DriftCategoryMaster
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CategoryName = "TestCategory",
            IsActive = true
        };
        await _repository.AddAsync(driftCategoryMaster);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftCategoryMaster.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftCategoryMaster.ReferenceId, result.ReferenceId);
        Assert.Equal(driftCategoryMaster.CategoryName, result.CategoryName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var categories = new List<DriftCategoryMaster>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category1", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category2", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), CategoryName = "Category3", IsActive = true }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(categories);
        var initialCount = categories.Count;
        
        var toUpdate = categories.Take(2).ToList();
        toUpdate.ForEach(x => x.CategoryName = "UpdatedCategory");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = categories.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.CategoryName == "UpdatedCategory").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
