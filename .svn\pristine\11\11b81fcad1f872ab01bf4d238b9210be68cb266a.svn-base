﻿using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Queries;

public class GetSitePaginatedListQueryHandlerTests : IClassFixture<SiteFixture>
{
    private readonly GetSitePaginatedListQueryHandler _handler;

    private readonly Mock<ISiteRepository> _mockSiteRepository;

    public GetSitePaginatedListQueryHandlerTests(SiteFixture siteFixture)
    {
        var siteNewFixture = siteFixture;

        siteNewFixture.Sites[0].Name = "BinomialSite";
        siteNewFixture.Sites[0].CompanyName = "PTS";
        siteNewFixture.Sites[0].Type = "NearDR";
        siteNewFixture.Sites[0].Location = "Chennai";

        siteNewFixture.Sites[1].Name = "BinomialSite123";
        siteNewFixture.Sites[1].CompanyName = "PTS123";
        siteNewFixture.Sites[0].Type = "NearDR123";
        siteNewFixture.Sites[0].Location = "Chennai123";

        _mockSiteRepository = SiteRepositoryMocks.GetPaginatedSiteRepository(siteNewFixture.Sites);

        _handler = new GetSitePaginatedListQueryHandler(siteNewFixture.Mapper, _mockSiteRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetSitePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Vas" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SiteListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedSites_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetSitePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SiteListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<SiteListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("BinomialSite");

        result.Data[0].CompanyName.ShouldBe("PTS");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetSitePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SiteListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Sites_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetSitePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Site;companyname=PTS;type=Near;location=nai" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<SiteListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("BinomialSite");

        result.Data[0].CompanyId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].CompanyName.ShouldBe("PTS");

        result.Data[0].Location.ShouldBe("Chennai123");

        result.Data[0].Type.ShouldBe("NearDR123");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetSitePaginatedListQuery(), CancellationToken.None);

        _mockSiteRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}