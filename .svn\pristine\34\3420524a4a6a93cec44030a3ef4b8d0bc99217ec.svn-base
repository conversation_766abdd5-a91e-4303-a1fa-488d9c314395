$(async function () {

    //SaveAs 
    $(document).on('click', '#saveAsDatabaseData', async function () {
        clearCloneDatabaseErrorMessage();
        flagEdit = true;
        $("#totalCloneServer").text(0);
        $("#databaseSaveAs").css({ "opacity": "0.5", "pointer-events": "none" });
        $("#cloneDataTable").empty();
        $("#cloneTable").hide();

        getDatabaseDataForSaveAsAndClone($("#cloneDatabaseName"));

        let databaseServerNameList = await infraGetRequest(RootUrl + databaseURL.databaseServerNameList);
        if (Array.isArray(databaseServerNameList) && databaseServerNameList?.length) {
            let $cloneServerType = $('#cloneServerType');
            $cloneServerType.empty().append(`<option value=""></option>`);
            let options = [];
            const sortedData = databaseServerNameList?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
            sortedData.forEach(function (item) {
                options.push($('<option>').val(item.id).text(item.name));
            });
            $cloneServerType.append(options);
        }

        let OSNameLists = await infraGetRequestWithData(RootUrl + databaseURL.getFormMappingListByName, { name: "database" });
        if (Array.isArray(OSNameLists) && OSNameLists?.length) {
            versions = OSNameLists;
            let $cloneDatabaseType = $('#cloneDatabaseType');
            $cloneDatabaseType.empty().append(`<option value=""></option>`);
            let options = [];
            const sortedData = OSNameLists?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
            sortedData.forEach(function (item) {
                options.push($('<option>').val(item.formTypeId).text(item.formTypeName));
            });
            $cloneDatabaseType.append(options);
        }       
    });

    $("#cloneDatabaseName").on("change", async function () {
        commonDatabaseValidation($(this).val(), "Select clone name", "cloneDatabaseNameError");
        if ($(this).val()) {
            clonedDatabaseRowData = await getDatabaseData($(this).val());
        }
    })

    $("#cloneDatabaseType").on("change", function () {
        commonDatabaseValidation($(this).val(), "Select database type", "cloneDatabaseTypeError");
        licenseCountSaveAs()
    })

    $("#cloneServerType").on("change", function () {
        commonDatabaseValidation($(this).val(), "Select server type", "cloneServerTypeError");
        licenseCountSaveAs()
    })

    $("#cloneDatabaseLicenseKey").on("change", function () {
        commonDatabaseValidation($(this).val(), "Select PO", "cloneLicenseKeyError");
    })

    $("#cloneDatabaseNameInput").on("input", async function () {
        let validation = await InfraNameValidation($(this).val(), "", "Configuration/Database/IsDatabaseNameExist",
            $("#cloneDatabaseNameInputError"), "Enter name", 'Special characters not allowed', 'DatabaseName');

        if (flagEdit) {
            cloneDatabaseNameValidation(validation, $(this).val());
        }
    })

    $("#cloneOracleID").on("input", function () {
        commonDatabaseValidation($(this).val(), "Enter oracle SID", "cloneOracleIDError");
    })

    $("#cloneInstanceName").on("input", function () {
        commonDatabaseValidation($(this).val(), "Enter instance name", "cloneInstanceNameError");
    })

    $("#addSaveAsDatabase").on("click", async function () {
        let rowCount = $("#cloneDataTable tr").length;
        let databaseProps = clonedDatabaseRowData?.properties;
        let cloneDatabaseName = $("#cloneDatabaseName").val();
        let cloneDatabaseType = $("#cloneDatabaseType").val();
        let cloneDatabaseTypeName = $("#cloneDatabaseType :selected").text();
        let cloneDatabaseNameInput = $("#cloneDatabaseNameInput").val();
        let cloneServerType = $("#cloneServerType").val();
        let cloneServerTypeName = $("#cloneServerType :selected").text();
        let cloneOracleID = $("#cloneOracleID").val();
        let cloneInstanceName = $("#cloneInstanceName").val();
        let cloneDatabaseLicenseKey = $("#cloneDatabaseLicenseKey").val();
        let cloneDatabaseLicenseName = $("#cloneDatabaseLicenseKey :selected").text();

        let validateCloneDatabaseName = await InfraNameValidation(cloneDatabaseNameInput, "", "Configuration/Database/IsDatabaseNameExist",
            $("#cloneDatabaseNameInputError"), "Enter name", 'Special characters not allowed', 'DatabaseName');
        let databaseNameValidation = validateCloneDatabaseName;

        if (validateCloneDatabaseName && flagEdit) {
            databaseNameValidation = await cloneDatabaseNameValidation(validateCloneDatabaseName, cloneDatabaseNameInput);
        }

        if ($("#addSaveAsDatabase").hasClass("cp-update")) {
            flagEdit = true;
            $("#addSaveAsDatabase").addClass("cp-circle-plus").removeClass("cp-update").prop("title", "Add");
        }

        let ValidateDatabaseName = commonDatabaseValidation(cloneDatabaseName, "Select clone name", "cloneDatabaseNameError");

        let ValidateDatabaseType = commonDatabaseValidation(cloneDatabaseType, "Select database type", "cloneDatabaseTypeError");
        let ValidateServerType = commonDatabaseValidation(cloneServerType, "Select server type", "cloneServerTypeError");
        let ValidateOracleSID = commonDatabaseValidation(cloneOracleID, "Enter oracle SID", "cloneOracleIDError");
        let ValidateInstanceName = commonDatabaseValidation(cloneInstanceName, "Enter instance name", "cloneInstanceNameError");
        let ValidatecloneLicenseKey = commonDatabaseValidation(cloneDatabaseLicenseKey, "Select PO", "cloneLicenseKeyError");

        if (validateCloneDatabaseName && ValidateDatabaseType && ValidateDatabaseName
            && ValidateServerType && ValidateOracleSID && ValidateInstanceName && ValidatecloneLicenseKey) {

            let parsedDatabaseProps = JSON.parse(databaseProps);
            if (parsedDatabaseProps.hasOwnProperty("DatabaseSID")) {
                parsedDatabaseProps.DatabaseSID = cloneOracleID;
            }
            if (parsedDatabaseProps.hasOwnProperty("InstanceName")) {
                parsedDatabaseProps.InstanceName = cloneInstanceName;
            }
            databaseProps = JSON.stringify(parsedDatabaseProps);

            $("#cloneTable").show();

            if (cloneDatabaseSlNo > 0) {
                $("#cloneTable tbody tr").each(function () {
                    const row = $(this);
                    const cellWithSerialNumber = row.find("td:first");

                    if (cellWithSerialNumber.text().trim() === String(cloneDatabaseSlNo)) {
                        row.find("td").eq(1).text($("#cloneDatabaseNameInput").val());
                        row.find("td").eq(2).text($("#cloneDatabaseType :selected").text());
                        row.find("td").eq(3).text($("#cloneServerType :selected").text());
                        row.find("td").eq(4).text($("#cloneDatabaseLicenseKey :selected").text());
                        row.find("td").eq(5).text($("#cloneOracleID").val());
                        row.find("td").eq(6).text($("#cloneInstanceName").val());

                        // Update the content or attributes in the 8th column
                        const cell = row.find("td").eq(8);

                        // Update the attributes of the "cloneEditButton"
                        const editButton = cell.find(".cloneEditButton");
                        editButton.attr("data-dbname", $("#cloneDatabaseNameInput").val());
                        editButton.attr("data-typeid", $("#cloneServerType").val());
                        editButton.attr("data-cloneoracle", $("#cloneOracleID").val());
                        editButton.attr("data-cloneinstance", $("#cloneInstanceName").val());
                        editButton.attr("data-licenseid", $("#cloneDatabaseLicenseKey").val());
                        editButton.attr("data-dbtypeid", $("#cloneDatabaseType").val());

                        // update the delete button if needed
                        const deleteButton = cell.find(".saveAsDeleteButton");
                        deleteButton.attr("data-databasename", $("#cloneDatabaseNameInput").val());

                        let selectedDatabase = clonedDatabaseLists.DatabaseList[cloneDatabaseSlNo - 1];
                        Object.assign(selectedDatabase, {
                            Name: cloneDatabaseNameInput,
                            DatabaseTypeId: cloneDatabaseType,
                            DatabaseType: cloneDatabaseTypeName,
                            ServerId: cloneServerType,
                            ServerName: cloneServerTypeName,
                            Properties: databaseProps,
                            LicenseId: cloneDatabaseLicenseKey,
                            LicenseKey: cloneDatabaseLicenseName
                        });
                        return false;
                    }
                });
            } else {
                clonedDatabaseLists.DatabaseId = cloneDatabaseName;
                clonedDatabaseLists.DatabaseList.push({
                    "Name": cloneDatabaseNameInput,
                    "DatabaseTypeId": cloneDatabaseType,
                    "DatabaseType": cloneDatabaseTypeName,
                    "ServerId": cloneServerType,
                    "ServerName": cloneServerTypeName,
                    "Properties": databaseProps,
                    "LicenseId": cloneDatabaseLicenseKey,
                    "LicenseKey": cloneDatabaseLicenseName
                });

                $("#totalCloneDatabase").text(rowCount + 1);
                $("#cloneDataTable").append(`<tr>
                            <td>${rowCount + 1}</td>
                            <td>${cloneDatabaseNameInput}</td>
                            <td class="text-truncate">${cloneDatabaseTypeName}</td>
                            <td>${cloneServerTypeName}</td>
                            <td>${cloneDatabaseLicenseName}</td>
                            <td>${cloneOracleID}</td>
                            <td>${cloneInstanceName}</td>
                            <td><span class="text-warning iconClass"><i class="cp-pending"></i></span></td>
                            <td>
                              <div class="d-flex align-items-center gap-2">
                                  <span role="button" title="Edit" class="cloneEditButton" data-dbname="${cloneDatabaseNameInput}"
                                    data-typeid="${cloneServerType}" data-licenseid="${cloneDatabaseLicenseKey}"
                                    data-cloneoracle="${cloneOracleID}" data-slno="${rowCount + 1}" data-cloneinstance="${cloneInstanceName}"
                                    data-dbtypeid="${cloneDatabaseType}">
                                    <i class="cp-edit"></i>
                                  </span>
                                  <span role="button" title="Delete" data-slno="${rowCount + 1}" data-databasename="${cloneDatabaseNameInput}"
                                       class="button saveAsDeleteButton" data-bs-toggle="modal" data-bs-target="#DeleteModalSaveAs">
                                    <i class="cp-Delete"></i>
                                  </span>
                             </div>
                           </td>
                         </tr>`);
            }

            cloneDatabaseSlNo = 0;

            $("#databaseSaveAs").css({ "opacity": "1.0", "pointer-events": "" });

            $("#cloneDatabaseNameInput, #cloneOracleID, #cloneInstanceName").val("");
            $("#cloneDatabaseLicenseKey, #cloneServerType, #cloneDatabaseType").val("").trigger("change");
            $('#cloneLicenseKeyError, #cloneServerTypeError, #cloneDatabaseTypeError').text('').removeClass('field-validation-error');
        }
    })

    $("#databaseSaveAs").on("click", async function () {
        //await $.ajax({
        //    type: "POST",
        //    url: RootUrl + "Configuration/Database/SaveAllDatabase",
        //    dataType: "json",
        //    headers: {
        //        'RequestVerificationToken': await gettoken()
        //    },
        //    data: { command: clonedDatabaseLists },
        //    success: function (result) {
        //        if (result?.success) {
        //            if (result?.data?.success) {
        //                let resultData = result?.data;
        //                notificationAlert("success", resultData?.message);

        //                //Comment this after added style in modal.
        //                $("#saveasModal").modal("hide");
        //                setTimeout(() => {
        //let selectedType = $("#databaseType :selected").val();
        //dataTableCreateAndUpdate($("#save_btn"), dataTable, $('#selectType'), selectedType);
        //checkedTestConnection = [];
        //                }, 2000);

        //            } else {
        //                errorNotification(result?.data)
        //                $("#cloneModal").modal("hide");
        //            }

        //        } else {
        //            errorNotification(result)
        //            $("#cloneModal").modal("hide");
        //        }
        //    },
        //});
    });

    $("#cloneTable").on("click", ".cloneEditButton", function () {
        $("#cloneDatabaseNameInput").val($(this).attr("data-dbname"));
        $("#cloneDatabaseType").val($(this).attr("data-dbtypeid")).trigger("change");
        $("#cloneServerType").val($(this).attr("data-typeid")).trigger("change");
        $("#cloneOracleID").val($(this).attr("data-cloneoracle"));
        $("#cloneInstanceName").val($(this).attr("data-cloneinstance"));
        licenseCountSaveAs($(this).attr("data-licenseid"));
        cloneDatabaseSlNo = $(this).data("slno");
        flagEdit = false;
        $("#addSaveAsDatabase").removeClass("cp-circle-plus").addClass("cp-update").prop("title", "Update");
    });

    $("#cloneTable").on("click", ".saveAsDeleteButton", function () {
        deleteCloneDatabaseRow = $(this).data("slno");
        $("#deleteCloneDatabaseData").text($(this).data("databasename"));
    });

    $("#deleteSaveAsRow").on("click", async function () {
        $("#cloneTable tbody tr").each(function () {
            const row = $(this);
            const cellWithSerialNumber = row.find("td:first");

            if (cellWithSerialNumber.text().trim() === String(deleteCloneDatabaseRow)) {
                row.remove();
                let index = Number(cellWithSerialNumber.text().trim())
                let indexToRemove = index - 1;
                if (indexToRemove >= 0 && indexToRemove < clonedDatabaseLists.DatabaseList.length) {
                    clonedDatabaseLists.DatabaseList.splice(indexToRemove, 1);
                }
                return false;
            }
        });
        $("#totalCloneDatabase").text($("#totalCloneDatabase").text() - 1);
        $("#saveasModal").modal("show");

        if ($("#cloneDataTable tr").length === 0) {
            $("#cloneTable").hide();
            $("#databaseSaveAs").css({ "opacity": "0.5", "pointer-events": "none" });
        }
    });

    $("#cancelDelete").on("click", function () {
        $("#saveasModal").modal("show");
    });

    //Clone
    $('.databaseCloneButton').on('click', function () {
        $("#cloneModal").modal("show");
        $('#cloneDatabase').val("");
        $('#cloneDatabaseError').text("").removeClass("field-validation-error");
        $('#inputCloneDatabase').val("");
        $('#inputCloneDatabaseError').text("").removeClass("field-validation-error");
        $('#databaseID').val("");
        getDatabaseDataForSaveAsAndClone($("#cloneDatabase"));
    });

    $('#cloneDatabaseButton').on("click", async function () {
        const $selectDatabaseName = $('#cloneDatabase');
        const $cloneDatabaseName = $('#inputCloneDatabase');
        let selectDatabaseNameValidate = commonDatabaseValidation($selectDatabaseName.val(), "Select database name", "cloneDatabaseError");
        let cloneDatabaseNameValidate = await InfraNameValidation($cloneDatabaseName.val(), "", "Configuration/Database/IsDatabaseNameExist",
            $("#inputCloneDatabaseError"), "Enter clone database name", 'Special characters not allowed', 'databasename');

        if (selectDatabaseNameValidate && cloneDatabaseNameValidate) {
            async function saveAs() {
                await $.ajax({
                    type: "POST",
                    url: RootUrl + databaseURL.saveAsDatabase,
                    dataType: "json",
                    headers: {
                        'RequestVerificationToken': await gettoken()
                    },
                    data: { saveAsDatabaseCommand: { "DatabaseId": $('#databaseID').val(), "Name": $cloneDatabaseName.val() } },
                    success: function (result) {
                        if (result.success) {
                            if (result.data.success) {
                                let resultData = result.data;
                                notificationAlert("success", resultData.message);
                                $('#databaseID').val("");

                                //Comment this after added style in modal.
                                $("#cloneModal").modal("hide");
                                setTimeout(() => {
                                    let selectedType = $("#databaseType :selected").val();
                                    dataTableCreateAndUpdate($("#save_btn"), dataTable, $('#selectType'), selectedType);
                                    checkedTestConnection = [];
                                }, 2000);

                            } else {
                                errorNotification(result.data)
                                $("#cloneModal").modal("hide");
                            }

                        } else {
                            errorNotification(result)
                            $("#cloneModal").modal("hide");
                        }
                    },
                });
            }
            saveAs();
        }
    });

    $('#cloneDatabase').on('change', function () {
        const $databaseName = $(this);
        $('#databaseID').val($databaseName.val());
        commonDatabaseValidation($databaseName.val(), "Select database name", "cloneDatabaseError");
    });

    $('#inputCloneDatabase').on('keyup', commonDebounce(async function () {
        const $databaseName = $(this);

        //InfraCommonFunctions.js InfraNameValidation
        await InfraNameValidation($databaseName.val(), "", "Configuration/Database/IsDatabaseNameExist",
            $("#inputCloneDatabaseError"), "Enter clone database name", 'Special characters not allowed', 'databasename');
    }));
})

function cloneDatabaseNameValidation(validation, databasename) {
    if (!validation || clonedDatabaseLists.DatabaseList.length === 0) {
        return true;
    };
    const existsName = clonedDatabaseLists.DatabaseList.some(
        (data) => data.Name.toLowerCase().trim() === databasename.toLowerCase().trim()
    );
    $('#cloneDatabaseNameInputError')
        .text(existsName ? "Name already exists" : "")
        .toggleClass('field-validation-error', existsName);
    return !existsName;
}

async function licenseCountSaveAs(licenseid = null) {
    if ($("#cloneDatabaseType").val() && $("#cloneServerType").val()) {
        let licensesNameLists = "";
        $('#cloneLicenseKeyError').text('').removeClass('field-validation-error');
        await $.ajax({
            type: "GET",
            async: false, //dont't delete.
            url: RootUrl + databaseURL.getLicensesNamesWithCount,
            dataType: "json",
            data: {
                type: "database", roleType: "", siteId: "", serverId: $("#cloneServerType").val(), replicationType: "", databaseTypeId: $("#cloneDatabaseType").val()
            },
            success: function (result) {
                if (result.success) {
                    licensesNameLists = result?.data;
                } else {
                    errorNotification(result)
                }
            },
        });

        if (licensesNameLists && (Array.isArray(licensesNameLists) && licensesNameLists.length > 0)) {
            let license = $('#cloneDatabaseLicenseKey');
            license.empty().append($('<option>').val("").text("Select PO"));
            licensesNameLists.forEach(function (item) {
                license.append($('<option>').val(item.id)
                    .text(`${item?.poNumber || ''}`)
                    .attr('remainingcount', item.remainingCount || '0')
                    .attr('licenseIsApplicable', item.licenseIsApplicable))
            });
        }

        if (licenseid) {
            $("#cloneDatabaseLicenseKey").val(licenseid).trigger("change");
        }
    }
}

async function getDatabaseDataForSaveAsAndClone(selectDatabase) {
    await $.ajax({
        type: "GET",
        async: false, //dont't delete.
        url: RootUrl + databaseURL.getDatabaseNamesForSaveAs,
        dataType: "json",
        success: function (result) {
            if (result.success) {
                let response = result?.data;
                if (response && (Array.isArray(response) && response.length > 0)) {
                    selectDatabase.empty().append($('<option>').val("").text(""));
                    let options = [];
                    response.forEach(function (item) {
                        options.push($('<option>').val(item.id).text(item.name));
                    });
                    selectDatabase.append(options);
                }
            } else {
                errorNotification(result);
            }
        }
    })
}

function getDatabaseData(databaseid) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: RootUrl + databaseURL.getByReferenceId,
            method: 'GET',
            dataType: 'json',
            data: { id: databaseid },
            success: function (result) {
                if (result.success) {
                    resolve(result.data);
                } else {
                    errorNotification(result);
                }
            }
        })
    });
}