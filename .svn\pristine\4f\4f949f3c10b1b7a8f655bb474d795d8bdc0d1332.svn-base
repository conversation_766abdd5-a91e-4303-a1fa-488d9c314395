﻿using ContinuityPatrol.Application.Features.Replication.Events.Delete;
using ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Delete;

namespace ContinuityPatrol.Application.Features.Replication.Commands.Delete;

public class DeleteReplicationCommandHandler : IRequest<PERSON>andler<DeleteReplicationCommand, DeleteReplicationResponse>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public DeleteReplicationCommandHandler(IReplicationRepository replicationRepository, IPublisher publisher,
        IInfraObjectRepository infraObjectRepository, IWorkflowRepository workflowRepository)
    {
        _replicationRepository = replicationRepository;
        _publisher = publisher;
        _infraObjectRepository = infraObjectRepository;
        _workflowRepository = workflowRepository;
    }

    public async Task<DeleteReplicationResponse> Handle(DeleteReplicationCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, $"Invalid Replication Id '{request.Id}'");

        var infraObjects = await _infraObjectRepository.GetInfraObjectByReplicationId(request.Id);

        var workflow = await _workflowRepository.GetWorkflowPropertiesByReplicationId(request.Id);

        if (infraObjects.Count > 0 || workflow.Count > 0)
            throw new InvalidException("The replication is currently in use");

        var eventToDelete = await _replicationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Replication),
            new NotFoundException(nameof(Domain.Entities.Replication), request.Id));

        eventToDelete.IsActive = false;

        eventToDelete.LicenseKey =eventToDelete.LicenseKey.IsNotNullOrWhiteSpace()
                ? SecurityHelper.Encrypt(eventToDelete.LicenseKey)
                : eventToDelete.LicenseKey;

        await _replicationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteReplicationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Replication), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(
            new ReplicationDeletedEvent
                { ReplicationId = eventToDelete.ReferenceId, ReplicationName = eventToDelete.Name },
            cancellationToken);

        if (eventToDelete.Type.ToLower().Contains("perpetuuiti"))
            await _publisher.Publish(
                new ReplicationLicenseInfoDeletedEvent
                    { EntityName = eventToDelete.Name, EntityId = eventToDelete.ReferenceId },
                cancellationToken);

        return response;
    }
}