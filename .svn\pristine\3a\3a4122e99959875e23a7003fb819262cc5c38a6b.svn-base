using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReportScheduleFixture : IDisposable
{
    public List<ReportSchedule> ReportSchedulePaginationList { get; set; }
    public List<ReportSchedule> ReportScheduleList { get; set; }
    public ReportSchedule ReportScheduleDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReportScheduleFixture()
    {
        var fixture = new Fixture();

        ReportScheduleList = fixture.Create<List<ReportSchedule>>();

        ReportSchedulePaginationList = fixture.CreateMany<ReportSchedule>(20).ToList();

        ReportSchedulePaginationList.ForEach(x => x.CompanyId = CompanyId);

        ReportScheduleList.ForEach(x => x.CompanyId = CompanyId);

        ReportScheduleDto = fixture.Create<ReportSchedule>();

        ReportScheduleDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
