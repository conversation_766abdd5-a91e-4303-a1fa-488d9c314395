﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<style>
    .list-container {
        position:absolute;
        bottom:0;
    }
    .monitor_pages .nav-underline .nav-link.active, .nav-underline .show > .nav-link {
        font-weight: 700;
        color: var(--bs-nav-underline-link-active-color);
        border-bottom-color: var(--bs-primary);
    }

    .monitor_pages .nav-underline .nav-link.active span{
            color:var(--bs-primary)
    }

        .monitor_pages .nav-underline .nav-link, .monitor_pages .nav-underline .nav-link span {
        color: var(--bs-gray);
    }
   
   
    </style>
<div class="page-content">
   
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                DB2_HADR_Linux Detail Monitoring :
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span> 
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
        
    </div>
    <div class="monitor_pages" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 104px);">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row mb-2 g-2 mt-0 d-none">
          @*   <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline">
                        <li class="nav-item">
                            <a class="nav-link active" aria-current="page" href="#">PR<span class="mx-2">=======></span>DR</a>
                        </li>
                        <li class="nav-item vr">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2">=======></span>Near DR</a>
                        </li>
                        <li class="nav-item vr">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2">=======></span>For DR</a>
                        </li>
                    
                    </ul>
                </div>
         
            </div> *@
            <div class="col-7 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Postgres_Infra_FullDB</div>

                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Component">Component</th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-ip-address me-1"></i>IP Address/Hostname</td>
                                    <td class="text-truncate">**************</td>
                                    <td class="text-truncate">**************</td>

                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-database me-1"></i>Database Name</td>
                                    <td class="text-truncate">Postgres10.5_DB_Prod</td>
                                    <td class="text-truncate">Postgres10.5_DB_Prod</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-database-success me-1"></i>Database Cluster State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>   
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-database-refresh me-1"></i>Database Recovery State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC Relationship Monitoring</div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Relationship Monitoring ">Relationship Monitoring </th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-hand1 me-1"></i>Relationship Name</td>
                                    <td class="text-truncate">**************</td>
                                    <td class="text-truncate">**************</td>

                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-teams me-1"></i>Group Name</td>
                                    <td class="text-truncate">Postgres10.5_DB_Prod</td>
                                    <td class="text-truncate">Postgres10.5_DB_Prod</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-hand1 me-1"></i>Relationship Primary Value</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-master-change me-1"></i>Master Change Volume Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-auxiliary-cluster-name me-1"></i>Auxilary Change Volume Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-relationship-state me-1"></i>Relationship State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-relationship-progress me-1"></i>Relationship Progress</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC Clustered System Node Monitoring</div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Clustered System Node Monitoring">Clustered System Node Monitoring</th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-cluster-database me-1"></i>Clustered System Node ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>

                                </tr>
                             
                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">SVC Storage Controller Monitoring</div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Controller Monitoring">Controller Monitoring</th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-ip-address me-1"></i>Disk Controller ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-disk-controller-name me-1"></i>Disk Controller Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr> 
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-degrade-disk me-1"></i>Mdisks Degrade Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-controller-product-ID me-1"></i>Controller Product ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-5 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram1"></div>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        SVC-Metro or Global Mirrow Consistency Group Monitoring
                    </div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Clustered System Node Monitoring">Component</th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-stand-server me-1"></i>Group ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-teams me-1"></i>Group Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-cluster-database me-1"></i>Master Cluster Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-auxiliary-cluster-name me-1"></i>Auxiliary Cluster Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-park-solid-group me-1"></i>Group Primary Value</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-ungroup me-1"></i>Group State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-freeze-time me-1"></i>Group Freeze Time</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-user-status me-1"></i>Group Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-user-refresh me-1"></i>GGroup Sync Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-mirror me-1"></i>Mirror Relationship Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">
                        SVC Clusered System Node Detailed Monitoring
                    </div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Node Detailed Monitoring ">Node Detailed Monitoring </th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-stand-server me-1"></i>Node ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-ip-address me-1"></i>Node Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-Job-status me-1"></i>Node Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-ip-address me-1"></i>Service IP Address</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-machine-type me-1"></i>Product Machine Type</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-truncate"><i class="text-primary cp-machine-type me-1"></i>Machine Code Level</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
        
    </div>
        <div class="row ">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">DB2_HADR10.5_Infra</div>

                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <thead>
                                <tr>
                                    <th title="Database (DB2) Monitor">Database (DB2) Monitor</th>
                                    <th title="Primary" class="text-info">Primary</th>
                                    <th title="DR" class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-ip-address me-1"></i>IP Address/Hostname</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRIp"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRIp"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-mysql-data me-1"></i>Database Instance</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRDatabaseInstance"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRDatabaseInstance"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-database-success me-1"></i>Database Status</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRDatabaseStatus"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRDatabaseStatus"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-control-file-name me-1"></i>Last Log Generated/Applied</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRLogFile"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRLogFile"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-last-copied-transaction me-1"></i>LSN</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRLSN"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRLSN"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-apply-finish-time me-1"></i>Time Stamp </td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRTimestamp"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRTimestamp"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-hcl me-1 fs-6"></i>Database Version</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="PRDatabaseVersion"></span></td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="DRDatabaseVersion"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-apply-lag me-1 fs-6"></i>Datalag</td>
                                    <td> <i class="cp-time text-primary mt-2"></i> <span class="text-truncate" id="Datalag"></span></td>
                                    <td></td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Replication (HADR) Monitor</div>
                    <div class="card-body pt-0">
                        <table class="table mb-0">
                            <tbody>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-database-sid me-1"></i>HADR Role</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="HRole"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-database-sid me-1"></i>HADR State</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="State"></span></td>
                                    <td class="fw-bold"><i class="text-primary cp-database-sid me-1"></i>Sync Mode</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="SyncMode"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-test-connection me-1"></i>Connection Status</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="ConnectionStatus"></span></td>
                                    <td class="fw-bold"><i class="text-primary cp-Job-status me-1 fs-6"></i>Heart Beat Status</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="HeartbeatsMissed"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-two-point me-1"></i>Local Host</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="LocalHost"></span></td>
                                    <td class="fw-bold"><i class="text-primary cp-two-point me-1"></i>Local Service</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="LocalService"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary  cp-workflow-configuration me-1"></i>Remote Host</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="RemoteHost"></span></td>
                                    <td class="fw-bold"><i class="text-primary  cp-workflow-configuration me-1"></i>Remote Service</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="RemoteService"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold"><i class="text-primary cp-time me-1"></i>TimeOut</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="Timeout"></span></td>
                                    <td class="fw-bold"><i class="text-primary cp-time me-1 fs-6"></i>LogGap</td>
                                    <td><span class="text-truncate d-inline-block" style="max-width:80%" id="LogGap"></span></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-5 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title" style="font-size:15px" title="Database Size">Database Size</div>
                    <div class="card-body d-flex pt-0 align-items-center gap-4 justify-content-center">
                        <div>
                            <i class="cp-database-sizes text-light" style="font-size: 5.9rem;"></i>
                        </div>
                        <div class="d-grid  border-start border-3">
                            <div class="text-primary ms-2 fw-semibold" title="Primary">Primary</div>
                            <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                            <h6 class="mb-0 fw-bold ms-2" id="PRDBSize"></h6>
                        </div>
                        @*<div class="w-50" id="DatabaseSize"></div>*@
                        <div>
                            <div class="d-grid">
                                <div class="ms-2 fw-semibold dynamicSite-header" title="DR">DR</div>
                                <span class="text-secondary mb-1 ms-2" title="Database Size">Database Size</span>
                                <h6 class="mb-0 fw-bold ms-2" id="DRDBSize"></h6>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-xl-6 d-grid">
                <div class="card Card_Design_None mb-2 h-100" id="mssqlserver">
                    <div class="card-header card-title" title="Service/Process/Workflow">Service/Process/Workflow</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0 noDataimg" style="table-layout:fixed" id="tableCluster">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
        </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
 
<script src="~/js/Monitoring/MonitoringDb2hadr.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>
@* <script src="~/js/Monitoring/SolutionDiagramDB2HADR.js"></script> *@



