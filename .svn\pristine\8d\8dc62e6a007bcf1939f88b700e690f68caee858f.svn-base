using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BiaRulesFixture : IDisposable
{
    public List<BiaRules> BiaRulesPaginationList { get; set; }
    public List<BiaRules> BiaRulesList { get; set; }
    public BiaRules BiaRulesDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BiaRulesFixture()
    {
        var fixture = new Fixture();

        BiaRulesList = fixture.Create<List<BiaRules>>();

        BiaRulesPaginationList = fixture.CreateMany<BiaRules>(20).ToList();

        BiaRulesDto = fixture.Create<BiaRules>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
