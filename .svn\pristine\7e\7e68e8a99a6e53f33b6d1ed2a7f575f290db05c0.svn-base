<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Site Location QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/17.0.1/sinon.min.js"></script>
    <!-- Your actual siteLocation.js goes here -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Sites/Site Location/siteLocation.js"></script>
    <script src="/js/Configuration/Sites/Site Location/SiteLocationTest.js"></script>
    <style>
        #qunit-fixture {
            display: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <!-- Minimal HTML for tests -->
        <input type="search" id="siteLocSearch" class="form-control" placeholder="Search" autocomplete="off" />
        <input class="form-check-input" type="checkbox" value="city=" id="siteLocCityFilter">
        <input class="form-check-input" type="checkbox" value="country=" id="siteLocCountryFilter">
        <button type="button" id="siteLocCreate" class="btn btn-primary btn-sm">Create</button>
        <table id="siteLocationTable" class="table table-hover dataTable" style="width:100%">
            <thead>
                <tr>
                    <th class="SrNo_th">Sr. No.</th>
                    <th>City Name</th>
                    <th>Country Name</th>
                    <th>City Latitude</th>
                    <th>City Longitude</th>
                    <th class="Action-th">Action</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
        <!-- Create Modal -->
        <div id="siteLocCreateModal" class="modal" style="display:none;">
            <form id="siteLocForm">
                <input type="text" id="cityName" />
                <span id="cityNameError"></span>
                <input type="text" id="countryName" />
                <span id="countryNameError"></span>
                <input type="text" id="latitudeID" />
                <span id="latitudeError"></span>
                <input type="text" id="longitudeId" />
                <span id="longitudeError"></span>
                <button type="button" id="siteLocSaveBtn">Save</button>
            </form>
        </div>
        <!-- Delete Modal -->
        <div id="siteLocDeleteModal" class="modal" style="display:none;">
            <span id="siteLocDeleteId"></span>
            <button type="button" id="siteLocDeleteButton">Yes</button>
        </div>
    </div>
</body>
</html>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>