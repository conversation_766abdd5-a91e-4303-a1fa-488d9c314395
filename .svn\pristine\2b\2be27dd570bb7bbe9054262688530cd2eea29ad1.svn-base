﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;
using ContinuityPatrol.Application.Hubs;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;
using Microsoft.AspNetCore.SignalR;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Commands
{
    public class UpdateWorkflowOperationGroupTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IHubContext<WorkflowHub>> _mockHubContext;
        private readonly Mock<ILoadBalancerRepository> _mockNodeConfigurationRepository;
        private readonly Mock<IWorkflowRunningActionRepository> _mockWorkflowRunningActionRepository;
        private readonly Mock<IJobScheduler> _mockJobScheduler;
        private readonly UpdateWorkflowOperationGroupCommandHandler _handler;

        public UpdateWorkflowOperationGroupTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockHubContext = new Mock<IHubContext<WorkflowHub>>();
            _mockWorkflowRunningActionRepository = new Mock<IWorkflowRunningActionRepository>();
            _mockNodeConfigurationRepository = new Mock<ILoadBalancerRepository>();
            _mockJobScheduler = new Mock<IJobScheduler>();

            _handler = new UpdateWorkflowOperationGroupCommandHandler(
                _mockMapper.Object,
                _mockWorkflowOperationGroupRepository.Object,
                _mockHubContext.Object,
                _mockNodeConfigurationRepository.Object,
                _mockJobScheduler.Object,
                _mockWorkflowRunningActionRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldUpdateWorkflowOperationGroup_WhenValidRequest()
        {
            var request = new UpdateWorkflowOperationGroupCommand { Id = Guid.NewGuid().ToString() };

            var workflowGroup = new Domain.Entities.WorkflowOperationGroup
            {
                ReferenceId = request.Id,
                Status = "InProgress",
                ConditionalOperation = 1
            };
            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<Guid>().ToString()))
                .ReturnsAsync(workflowGroup);

            _mockMapper.Setup(m => m.Map(request, workflowGroup, typeof(UpdateWorkflowOperationGroupCommand), typeof(Domain.Entities.WorkflowOperationGroup)));

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.UpdateAsync(workflowGroup)).ReturnsAsync(workflowGroup);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.Equal("Workflow operation Group updated successfully.", result.Message);

            _mockWorkflowOperationGroupRepository.Verify(repo => repo.UpdateAsync(workflowGroup), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenWorkflowOperationGroupNotFound()
        {
            var request = new UpdateWorkflowOperationGroupCommand { Id = Guid.NewGuid().ToString() };

            _mockWorkflowOperationGroupRepository.Setup(repo => repo.GetByReferenceIdAsync(request.Id)).ReturnsAsync((Domain.Entities.WorkflowOperationGroup)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldScheduleJob_WhenConditionalOperationIsSeven()
        {
            var request = new UpdateWorkflowOperationGroupCommand { Id = Guid.NewGuid().ToString() };

            var workflowGroup = new Domain.Entities.WorkflowOperationGroup
            {
                ReferenceId = request.Id,
                ConditionalOperation = 7,
                WorkflowOperationId = Guid.NewGuid().ToString(),
            };
            _mockWorkflowOperationGroupRepository
            .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<Guid>().ToString()))
                .ReturnsAsync(workflowGroup);

            _mockNodeConfigurationRepository
                .Setup(repo => repo.GetNodeConfigurationByTypeAndTypeCategory("ALL", "Load Balancer"))
                .ReturnsAsync(new Domain.Entities.LoadBalancer { ConnectionType = "http", IPAddress = "127.0.0.1", Port = 8080, TypeCategory = "Load Balancer" });

            await _handler.Handle(request, CancellationToken.None);

            _mockJobScheduler.Verify(js => js.ScheduleJob(workflowGroup.WorkflowOperationId, It.IsAny<Dictionary<string, string>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldRemoveMemoryCacheAndDeleteLogs_WhenStatusCompletedOrAborted()
        {
            var request = new UpdateWorkflowOperationGroupCommand { Id = Guid.NewGuid().ToString() };

            var workflowGroup = new Domain.Entities.WorkflowOperationGroup
            {
                ReferenceId = request.Id,
                Status = "Completed",
                WorkflowOperationId = Guid.NewGuid().ToString(),
            };

            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(workflowGroup);

            var cacheEntryMock = new Mock<ICacheEntry>();

            //_mockMemoryCache.Setup(mc => mc.CreateEntry(It.IsAny<object>())).Returns(cacheEntryMock.Object);

            //_mockMemoryCache.Setup(mc => mc.Remove(It.IsAny<object>()));

            var workflowGroups = new List<Domain.Entities.WorkflowOperationGroup>
            {
                new Domain.Entities.WorkflowOperationGroup { WorkflowName = "Workflow1", ReferenceId = Guid.NewGuid().ToString() },
                new Domain.Entities.WorkflowOperationGroup { WorkflowName = "Workflow2", ReferenceId = Guid.NewGuid().ToString() }
            };
            _mockWorkflowOperationGroupRepository
                .Setup(repo => repo.GetWorkflowOperationGroupByWorkflowOperationId(It.IsAny<string>()))
                .ReturnsAsync(workflowGroups);

            await _handler.Handle(request, CancellationToken.None);

            //_mockMemoryCache.Verify(mc => mc.Remove(request.Id), Times.Once);

            var expectedDictionary = workflowGroups.ToDictionary(x => x.WorkflowName, x => x.ReferenceId);

            //_mockSeqService.Verify(ss => ss.DeleteSeqLog(It.Is<Dictionary<string, string>>(d =>
            //    d.SequenceEqual(expectedDictionary))), Times.Once);
        }
    }
}