using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class MongoDBMonitorLogRepositoryTests : IClassFixture<MongoDBMonitorLogFixture>
{
    private readonly MongoDBMonitorLogFixture _mongoDBMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly MongoDBMonitorLogRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public MongoDBMonitorLogRepositoryTests(MongoDBMonitorLogFixture mongoDBMonitorLogFixture)
    {
        _mongoDBMonitorLogFixture = mongoDBMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        
        _repository = new MongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.MongoDbMonitorLogs.RemoveRange(_dbContext.MongoDbMonitorLogs);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var mongoDBMonitorLog = _mongoDBMonitorLogFixture.MongoDBMonitorLogDto;

        // Act
        var result = await _repository.AddAsync(mongoDBMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mongoDBMonitorLog.Type, result.Type);
        Assert.Equal(mongoDBMonitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.MongoDbMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mongoDBMonitorLog = _mongoDBMonitorLogFixture.MongoDBMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mongoDBMonitorLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var mongoDBMonitorLog = _mongoDBMonitorLogFixture.MongoDBMonitorLogDto;
        await _repository.AddAsync(mongoDBMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(mongoDBMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mongoDBMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(mongoDBMonitorLog.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenReferenceIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var mongoDBMonitorLog = _mongoDBMonitorLogFixture.MongoDBMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mongoDBMonitorLog);
        
        addedEntity.Type = "UpdatedType";
        addedEntity.InfraObjectName = "UpdatedName";

        // Act
        await _repository.UpdateAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedType", result.Type);
        Assert.Equal("UpdatedName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        var mongoDBMonitorLog = _mongoDBMonitorLogFixture.MongoDBMonitorLogDto;
        var addedEntity = await _repository.AddAsync(mongoDBMonitorLog);

        // Act
        await _repository.DeleteAsync(addedEntity);
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnLogs_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(type: "TestType", isActive: true);
        await _dbContext.MongoDbMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("TestType");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("TestType", result[0].Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmpty_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(type: "TestType", isActive: true);
        var inactiveLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(type: "TestType", isActive: false);
        
        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(new[] { activeLog, inactiveLog });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType("TestType");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleWhitespaceInType()
    {
        // Arrange
        await ClearDatabase();
        var whitespaceLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithWhitespace();
        whitespaceLog.IsActive = true;
        await _dbContext.MongoDbMonitorLogs.AddAsync(whitespaceLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(whitespaceLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(whitespaceLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleVeryLongTypeNames()
    {
        // Arrange
        await ClearDatabase();
        var longTypeLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithLongType(1000);
        longTypeLog.IsActive = true;
        await _dbContext.MongoDbMonitorLogs.AddAsync(longTypeLog);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(longTypeLog.Type);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(longTypeLog.Type, result.First().Type);
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleMultipleCommonTypes()
    {
        // Arrange
        await ClearDatabase();
        var commonTypes = MongoDBMonitorLogFixture.TestData.CommonTypes;
        var logs = new List<MongoDBMonitorLog>();

        foreach (var type in commonTypes)
        {
            var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(type: type, isActive: true);
            logs.Add(log);
        }

        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(logs);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        foreach (var type in commonTypes)
        {
            var result = await _repository.GetDetailByType(type);
            Assert.Single(result);
            Assert.Equal(type, result.First().Type);
            Assert.True(result.First().IsActive);
        }
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnLogs_WhenDataExists()
    {
        // Arrange
        await ClearDatabase();
        var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MongoDbMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        // Create testable repository that returns false for table existence
        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("infra1", result[0].InfraObjectId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmpty_WhenNoDataExists()
    {
        // Arrange
        await ClearDatabase();
        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("nonexistent", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldFilterByDateRange()
    {
        // Arrange
        await ClearDatabase();
        var oldLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.AddDays(-10));

        var recentLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(new[] { oldLog, recentLog });
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(recentLog.ReferenceId, result[0].ReferenceId);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnOnlyActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        var inactiveLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: "infra1",
            isActive: false,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(new[] { activeLog, inactiveLog });
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId("infra1", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleNullParameters()
    {
        // Arrange
        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act & Assert
        var result1 = await repo.GetByInfraObjectId(null, "2023-01-01", "2023-01-02");
        var result2 = await repo.GetByInfraObjectId("infra1", null, "2023-01-02");
        var result3 = await repo.GetByInfraObjectId("infra1", "2023-01-01", null);

        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.NotNull(result3);
    }

    #endregion

    #region GetTableName Tests

    [Fact]
    public void GetTableName_ShouldReturnCorrectTableName()
    {
        // Act
        var tableName = _repository.GetTableName<MongoDBMonitorLog>();

        // Assert
        Assert.NotNull(tableName);
        Assert.NotEmpty(tableName);
    }

    [Fact]
    public void GetTableName_ShouldReturnNull_ForUnmappedEntity()
    {
        // Act
        var tableName = _repository.GetTableName<UnmappedEntity>();

        // Assert
        Assert.Null(tableName);
    }

    #endregion

    #region IsTableExistAsync Tests

    //[Fact]
    //public async Task IsTableExistAsync_ShouldHandleOracleProvider()
    //{
    //    // Arrange
    //    var tableName = "MongoDbMonitorLogs";
    //    var schemaName = "TESTSCHEMA";
    //    var providerName = "oracle";

    //    // Act & Assert - Should not throw exception
    //    try
    //    {
    //        var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
    //        Assert.IsType<bool>(result);
    //    }
    //    catch (Exception ex)
    //    {
    //        // Expected behavior for test environment without Oracle connection
    //        Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
    //    }
    //}
    [Fact]
    public async Task IsTableExistAsync_ShouldReturnTrue_WhenCalledWithValidParams()
    {
        // Arrange
        var mockRepo = new Mock<MongoDBMonitorLogRepository>();
        mockRepo
            .Setup(x => x.IsTableExistAsync("MyTable", "MySchema", "oracle"))
            .ReturnsAsync(true);

        // Act
        var result = await mockRepo.Object.IsTableExistAsync("MyTable", "MySchema", "oracle");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMsSqlProvider()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleMySqlProvider()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var schemaName = "testdb";
        var providerName = "mysql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without MySQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandlePostgreSqlProvider()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var schemaName = "public";
        var providerName = "npgsql";

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment without PostgreSQL connection
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleCaseInsensitiveProviderName()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var schemaName = "dbo";
        var providerName = "MSSQL"; // Uppercase

        // Act & Assert - Should not throw exception
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleNullTableName()
    {
        // Arrange
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception for null table name
        try
        {
            var result = await _repository.IsTableExistAsync(null, schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment or null handling
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException || ex is ArgumentException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleEmptyTableName()
    {
        // Arrange
        var schemaName = "dbo";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception for empty table name
        try
        {
            var result = await _repository.IsTableExistAsync("", schemaName, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment or empty string handling
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException || ex is ArgumentException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleNullSchemaName()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var providerName = "mssql";

        // Act & Assert - Should not throw exception for null schema name
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, null, providerName);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment or null handling
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException || ex is ArgumentException);
        }
    }

    [Fact]
    public async Task IsTableExistAsync_ShouldHandleNullProviderName()
    {
        // Arrange
        var tableName = "MongoDbMonitorLogs";
        var schemaName = "dbo";

        // Act & Assert - Should not throw exception for null provider name
        try
        {
            var result = await _repository.IsTableExistAsync(tableName, schemaName, null);
            Assert.IsType<bool>(result);
        }
        catch (Exception ex)
        {
            // Expected behavior for test environment or null handling
            Assert.True(ex is InvalidOperationException || ex is NotSupportedException || ex is ArgumentException);
        }
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var activeLogs = new List<MongoDBMonitorLog>
        {
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(isActive: true),
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(isActive: true)
        };
        var inactiveLog = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(isActive: false);

        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(activeLogs.Concat(new[] { inactiveLog }));
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, log => Assert.True(log.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoActiveLogs()
    {
        // Arrange
        await ClearDatabase();
        var inactiveLogs = new List<MongoDBMonitorLog>
        {
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(isActive: false),
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(isActive: false)
        };

        await _dbContext.MongoDbMonitorLogs.AddRangeAsync(inactiveLogs);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var logs = new List<MongoDBMonitorLog>
        {
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(),
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(),
            _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(logs);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(3, allLogs.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region Edge Case Tests

    [Fact]
    public async Task GetDetailByType_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialType = "MongoDB@#$%^&*()";
        var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(type: specialType, isActive: true);
        await _dbContext.MongoDbMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetDetailByType(specialType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialType, result[0].Type);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldHandleSpecialCharactersInInfraObjectId()
    {
        // Arrange
        await ClearDatabase();
        var specialInfraId = "infra@#$%^&*()";
        var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties(
            infraObjectId: specialInfraId,
            isActive: true,
            createdDate: DateTime.UtcNow.Date);

        await _dbContext.MongoDbMonitorLogs.AddAsync(log);
        await _dbContext.SaveChangesAsync();

        var startDate = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.AddDays(1).ToString("yyyy-MM-dd");

        var repo = new TestableMongoDBMonitorLogRepository(_dbContext, _mockConfiguration.Object, false);

        // Act
        var result = await repo.GetByInfraObjectId(specialInfraId, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(specialInfraId, result[0].InfraObjectId);
    }

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var log = _mongoDBMonitorLogFixture.CreateMongoDBMonitorLogWithProperties();
            tasks.Add(_repository.AddAsync(log));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allLogs = await _repository.ListAllAsync();
        Assert.Equal(10, allLogs.Count);
    }

    #endregion

    #region GetDatabaseNameFromConnectionString Tests

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMySql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=testdb;Uid=user;Pwd=password;";
        var provider = "mysql";
        var repo = new TestableMongoDBMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForMsSql()
    {
        // Arrange
        var connectionString = "Server=localhost;Database=testdb;Trusted_Connection=true;";
        var provider = "mssql";
        var repo = new TestableMongoDBMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldReturnDatabaseName_ForPostgreSql()
    {
        // Arrange
        var connectionString = "Host=localhost;Database=testdb;Username=user;Password=password;";
        var provider = "npgsql";
        var repo = new TestableMongoDBMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act
        var result = repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider);

        // Assert
        Assert.Equal("testdb", result);
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldThrow_ForUnsupportedProvider()
    {
        // Arrange
        var connectionString = "some connection string";
        var provider = "unsupported";
        var repo = new TestableMongoDBMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider));
    }

    [Fact]
    public void GetDatabaseNameFromConnectionString_ShouldThrow_WhenDatabaseNotFound()
    {
        // Arrange
        var connectionString = "Server=localhost;Trusted_Connection=true;"; // No Database parameter
        var provider = "mssql";
        var repo = new TestableMongoDBMonitorLogRepositoryWithPublicMethods(_dbContext, _mockConfiguration.Object);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => repo.GetDatabaseNameFromConnectionStringPublic(connectionString, provider));
    }

    #endregion

    #region Helper Classes and Methods

    private class UnmappedEntity { }

    // Helper class to override IsTableExistAsync for testing
    private class TestableMongoDBMonitorLogRepository : MongoDBMonitorLogRepository
    {
        private readonly bool _tableExists;

        public TestableMongoDBMonitorLogRepository(ApplicationDbContext dbContext, IConfiguration config, bool tableExists)
            : base(dbContext, config)
        {
            _tableExists = tableExists;
        }

        public override async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
        {
            // Simulate async operation
            await Task.Delay(1);
            return _tableExists;
        }
    }

    // Helper class to expose private methods for testing
    private class TestableMongoDBMonitorLogRepositoryWithPublicMethods : MongoDBMonitorLogRepository
    {
        public TestableMongoDBMonitorLogRepositoryWithPublicMethods(ApplicationDbContext dbContext, IConfiguration config)
            : base(dbContext, config)
        {
        }

        public string GetDatabaseNameFromConnectionStringPublic(string connectionString, string provider)
        {
            // Use reflection to call the private method
            var method = typeof(MongoDBMonitorLogRepository).GetMethod("GetDatabaseNameFromConnectionString",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return (string)method.Invoke(this, new object[] { connectionString, provider });
        }
    }

    #endregion
}
