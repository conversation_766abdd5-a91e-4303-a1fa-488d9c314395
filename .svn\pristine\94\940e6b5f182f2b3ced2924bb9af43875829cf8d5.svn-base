﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using Newtonsoft.Json;
using Org.BouncyCastle.Tls;
using Org.BouncyCastle.Utilities.Collections;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.Helper;

public class BulkImportJob : IJob
{
    private const string Chars =
        "ABCDEFGHIJKPERPETUUITILMANISANKARNOPQRSTUVWXYZ0123456789ABCDEFGHIJKPERPETUUITILMNOPQRSTUVWXYZ";

    private readonly ILogger<BulkImportJob> _logger;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IJobScheduler _client;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly IVersionManager _versionManager;
    private ILoggedInUserService _loggedInUserService;
    private IBulkImportOperationRepository _bulkImportOperationRepository;
    private IBulkImportOperationGroupRepository _bulkImportOperationGroupRepository;
    private IBulkImportActionResultRepository _bulkImportActionResultRepository;

    public BulkImportJob(ILogger<BulkImportJob> logger, IHttpContextAccessor httpContextAccessor, IServiceScopeFactory scopeFactory, IMapper mapper, IPublisher publisher, IJobScheduler client, IHubContext<NotificationHub> hubContext, IVersionManager versionManager)
    {
        _logger = logger;
        _httpContextAccessor = httpContextAccessor;
        _scopeFactory = scopeFactory;
        _mapper = mapper;
        _publisher = publisher;
        _client = client;
        _hubContext = hubContext;
        _versionManager = versionManager;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobData = context.MergedJobDataMap;

        var operationId = jobData.GetString("bulkImportOperationId");

        var companyId = jobData.GetString("CompanyId");

        var userId = jobData.GetString("UserId");

        _httpContextAccessor.HttpContext = new DefaultHttpContext
        {
            User = GetFakeUser(companyId,userId)
        };

        using var scope = _scopeFactory.CreateScope();


        _loggedInUserService = scope.ServiceProvider.GetRequiredService<ILoggedInUserService>();
        _bulkImportOperationRepository = scope.ServiceProvider.GetRequiredService<IBulkImportOperationRepository>();
        _bulkImportOperationGroupRepository = scope.ServiceProvider.GetRequiredService<IBulkImportOperationGroupRepository>();
        _bulkImportActionResultRepository = scope.ServiceProvider.GetRequiredService<IBulkImportActionResultRepository>();
       

        var bulkImportOperation = await _bulkImportOperationRepository.GetByReferenceIdAsync(operationId);

        bulkImportOperation.Status = "Running";

        await _bulkImportOperationRepository.UpdateAsync(bulkImportOperation);

        var bulkImportOperationGroup = await _bulkImportOperationGroupRepository.GetBulkImportOperationGroupByBulkImportOperationId(operationId);

      
        try
        {
            await Parallel.ForEachAsync(bulkImportOperationGroup, new ParallelOptions
            {
                MaxDegreeOfParallelism = Math.Max(1, bulkImportOperationGroup.Count)
            },
            async (operationGroup, token) =>
            {
                try
                {
                    using var innerScope = _scopeFactory.CreateScope();

                // var innerUserService = innerScope.ServiceProvider.GetRequiredService<ILoggedInUserService>();
                var serverRepo = innerScope.ServiceProvider.GetRequiredService<IServerRepository>();
                var databaseRepo = innerScope.ServiceProvider.GetRequiredService<IDatabaseRepository>();
                var replicationRepo = innerScope.ServiceProvider.GetRequiredService<IReplicationRepository>();
                var infraRepo = innerScope.ServiceProvider.GetRequiredService<IInfraObjectRepository>();
                var siteRepo = innerScope.ServiceProvider.GetRequiredService<ISiteRepository>();
                var siteTypeRepo = innerScope.ServiceProvider.GetRequiredService<ISiteTypeRepository>();
                var licenseRepo = innerScope.ServiceProvider.GetRequiredService<ILicenseManagerRepository>();
                var resultRepo = innerScope.ServiceProvider.GetRequiredService<IBulkImportActionResultRepository>();
                var loadBalancerRepository = scope.ServiceProvider.GetRequiredService<ILoadBalancerRepository>();
                var workflowRepository = scope.ServiceProvider.GetRequiredService<IWorkflowRepository>();
                var workflowHistoryRepository = scope.ServiceProvider.GetRequiredService<IWorkflowHistoryRepository>();


                operationGroup.Status = "Running";
                await _bulkImportOperationGroupRepository.UpdateAsync(operationGroup);

                var deserializeBulkImport = JsonConvert.DeserializeObject<CreateBulkImportOperationListCommand>(operationGroup.Properties);
      
                    await ProcessBulkImportGroupAsync(deserializeBulkImport, operationGroup.BulkImportOperationId, operationGroup.ReferenceId, token,
                        serverRepo, databaseRepo, replicationRepo, infraRepo, siteRepo, siteTypeRepo, licenseRepo, loadBalancerRepository, workflowRepository, workflowHistoryRepository);
                }

                catch (Exception ex)
                {
                    _logger.LogError($"An error occure on  BulkImport operationGroup while create  infraObject:{operationGroup.InfraObjectName}  Error :{ex.GetMessage()}.");
                }
            });
        }
        catch (Exception ex)
        {
            bulkImportOperation.Status = "Error";
            await _bulkImportOperationRepository.UpdateAsync(bulkImportOperation);
            _logger.LogError(ex, "BulkImportJob encountered an error");
        }
    }
    



    public async Task ProcessBulkImportGroupAsync(CreateBulkImportOperationListCommand importData, string operationId, string operationGroupId,CancellationToken cancellationToken, IServerRepository serverRepo, IDatabaseRepository databaseRepo, IReplicationRepository replicationRepo,
        IInfraObjectRepository infraRepo, ISiteRepository siteRepo, ISiteTypeRepository siteTypeRepo,
        ILicenseManagerRepository licenseRepo,ILoadBalancerRepository loadBalancerRepository,IWorkflowRepository  workflowRepository,IWorkflowHistoryRepository workflowHistoryRepository)
    {
        if (importData.ServerList?.Any() == true)
        {
            await CreateServer(
                importData.ServerList,
                operationId,
                operationGroupId, serverRepo, siteRepo, siteTypeRepo, licenseRepo,
                cancellationToken);
        }

        if (importData.DatabaseList?.Any() == true)
        {
            await CreateDatabase(
            importData.DatabaseList,
            operationId,
            operationGroupId, databaseRepo, serverRepo, siteRepo, siteTypeRepo, licenseRepo, cancellationToken);
        }

        if (importData.ReplicationList?.Any() == true)
        {
            await CreateReplication(
                importData.ReplicationList,
                operationId,
                operationGroupId, replicationRepo, siteRepo, siteTypeRepo,
                cancellationToken);
        }

        if (importData.InfraObject != null)
        {
            await CreateInfraObject(
                importData,
                operationId,
                operationGroupId, databaseRepo, replicationRepo, serverRepo, infraRepo, loadBalancerRepository, workflowRepository, workflowHistoryRepository,
            cancellationToken);
        }

        _logger.LogInformation("BulkImport - Create - Bulk Import created successfully.");

    }


    public async Task CreateServer(List<CreateBulkDataServerListCommand> servers, string operationId,
       string operationGroupId, IServerRepository _serverRepository,
       ISiteRepository _siteRepository, ISiteTypeRepository _siteTypeRepository,
        ILicenseManagerRepository _licenseManagerRepository, CancellationToken cancellationToken)
    {
        foreach (var server in servers)
        {
            string serverId = "";
            try
            {
                var startDate = DateTime.Now;
                var serverName = await _serverRepository.GetServerByServerName(server.Name);
                serverId = serverName?.ReferenceId ?? "";
                var serverDto = _mapper.Map<Server>(server);

                var serverCommandHandler =
                    new CreateServerCommandHandler(_mapper, _serverRepository, _publisher, _loggedInUserService,
                        _licenseManagerRepository, _siteRepository, _siteTypeRepository);

                var mapServerCommand = _mapper.Map<CreateServerCommand>(server);

                var updateBaseResponse =
                    await serverCommandHandler.Handle(mapServerCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ServerId, serverDto.Name, "Server", "Success",
                        startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult(serverId, server.Name, "Server", "Error",
                        DateTime.Now, exc.Message, operationId, operationGroupId);
                var ex = new Exception("Server import failed.", exc);
                throw ex;
            }
        
        }
    }


    public async Task CreateDatabase(List<CreateBulkDataDataBaseListCommand> databases, string operationId,
      string operationGroupId,IDatabaseRepository _databaseRepository, IServerRepository _serverRepository,
       ISiteRepository _siteRepository, ISiteTypeRepository _siteTypeRepository,
        ILicenseManagerRepository _licenseManagerRepository, CancellationToken cancellationToken)
    {
        foreach (var dataBaseItem in databases)
        {
            try {
                await Task.Delay(2000, cancellationToken);

                var startDate = DateTime.Now;


                var dataBase = _mapper.Map<Database>(dataBaseItem);

                var server = await _serverRepository.GetServerByServerName(dataBaseItem.ServerName);

                if (server.IsNull())
                    await CreateBulkImportActionResult("", dataBaseItem.Name, "Database", "Error", startDate,
                        $"Server '{dataBaseItem.ServerName}' not configured.", operationId, operationGroupId);

                dataBaseItem.ServerId = server?.ReferenceId ?? string.Empty;

                var databaseCommandHandler =
                    new CreateDatabaseCommandHandler(_mapper, _databaseRepository, _publisher, _loggedInUserService,
                        _licenseManagerRepository, _serverRepository, _siteRepository, _siteTypeRepository);

                var mapDatabaseCommand = _mapper.Map<CreateDatabaseCommand>(dataBaseItem);

                var updateBaseResponse =
                    await databaseCommandHandler.Handle(mapDatabaseCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.DatabaseId, dataBase?.Name, "Database",

                        "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("", dataBaseItem.Name, "Database", "Error",
                        DateTime.Now, exc.Message, operationId, operationGroupId);

                var ex = new Exception("Database import failed.", exc);
                throw ex;

            }
        }
    }

    public async Task CreateReplication(List<CreateBulkDataReplicationListCommand> replications, string operationId,
        string operationGroupId,IReplicationRepository _replicationRepository,
       ISiteRepository _siteRepository, ISiteTypeRepository _siteTypeRepository,
         CancellationToken cancellationToken)
    {
        foreach (var replicationItem in replications)
        {
            try
            {
                await Task.Delay(2000, cancellationToken);

                var startDate = DateTime.Now;

                var replication = _mapper.Map<Replication>(replicationItem);

                var replicationCommandHandler = new CreateReplicationCommandHandler(_mapper, _replicationRepository,
                    _publisher, _loggedInUserService, _siteRepository, _siteTypeRepository);

                var mapReplicationCommand = _mapper.Map<CreateReplicationCommand>(replicationItem);

                var updateBaseResponse =
                    await replicationCommandHandler.Handle(mapReplicationCommand, cancellationToken);

                if (updateBaseResponse.Success)
                    await CreateBulkImportActionResult(updateBaseResponse.ReplicationId, replication?.Name,
                        "Replication", "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("", replicationItem.Name, "Replication", "Error",
                        DateTime.Now, exc.Message, operationId, operationGroupId);
                var ex = new Exception("Replication import failed.", exc);
                throw ex;
            }
        }



    }

    public async Task CreateInfraObject(CreateBulkImportOperationListCommand bulkImport, string operationId,
      string operationGroupId, IDatabaseRepository _databaseRepository, IReplicationRepository _replicationRepository, IServerRepository _serverRepository,
       IInfraObjectRepository _infraObjectRepository, ILoadBalancerRepository _loadBalancerRepository,
       IWorkflowRepository _workflowRepository, IWorkflowHistoryRepository _workflowHistoryRepository, CancellationToken cancellationToken)
    {
        try
        {

            var startDate = DateTime.Now;

            foreach (var server in bulkImport.ServerList)
            {
                var serverName = await _serverRepository.GetServerByServerName(server.Name);

                if (serverName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the server '{server.Name}' not configured.",
                        operationId, operationGroupId);

                bulkImport.InfraObject.ServerProperties = bulkImport.InfraObject.ServerProperties
                    .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{serverName.Name}", serverName.ReferenceId);


                bulkImport.InfraObject.PRServerId = server.ServerType.ToLower().Contains("pr")
                    ? serverName.ReferenceId
                    : bulkImport.InfraObject.PRServerId;

                bulkImport.InfraObject.DRServerId = server.ServerType.ToLower().Contains("dr")
                    ? serverName.ReferenceId
                    : bulkImport.InfraObject.DRServerId;
            }

            foreach (var database in bulkImport.DatabaseList)
            {
                var databaseNames = await _databaseRepository.GetDatabaseNames();

                var databaseName = databaseNames.FirstOrDefault(x => x.Name == database.Name);

                if (databaseName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the database '{database.Name}' not configured.",
                        operationId, operationGroupId);

                bulkImport.InfraObject.DatabaseProperties = bulkImport.InfraObject.DatabaseProperties
                    .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{databaseName?.Name}", databaseName?.ReferenceId);


                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{databaseName.Name}", databaseName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{databaseName.Name}", databaseName.ReferenceId);


                bulkImport.InfraObject.PRDatabaseId = database.Type.ToLower().Contains("pr")
                    ? databaseName?.ReferenceId
                    : bulkImport.InfraObject.PRDatabaseId;

                bulkImport.InfraObject.DRDatabaseId = database.Type.ToLower().Contains("dr")
                    ? databaseName?.ReferenceId
                    : bulkImport.InfraObject.DRDatabaseId;
            }


            foreach (var replication in bulkImport.ReplicationList)
            {
                var replicationNames = await _replicationRepository.GetReplicationNames();

                var replicationName = replicationNames.FirstOrDefault(x => x.Name == replication.Name);

                if (replicationName.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error", startDate,
                        $"The {bulkImport.InfraObject.Name} infraObject was not created because the replication '{replication.Name}' not configured.",
                        operationId, operationGroupId);

                bulkImport.InfraObject.ReplicationProperties = bulkImport.InfraObject.ReplicationProperties
                    .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);

                if (bulkImport.IsSwitchOver)
                    bulkImport.SwitchOverTemplate = bulkImport.SwitchOverTemplate
                        .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


                if (bulkImport.IsSwitchBack)
                    bulkImport.SwitchBackTemplate = bulkImport.SwitchBackTemplate
                        .Replace($"@{replicationName?.Name}", replicationName?.ReferenceId);


                if (bulkImport.IsFailOver)
                    bulkImport.FailOverTemplate = bulkImport.FailOverTemplate
                        .Replace($"@{replicationName.Name}", replicationName.ReferenceId);

                if (bulkImport.IsFailBack)
                    bulkImport.FailBackTemplate = bulkImport.FailBackTemplate
                        .Replace($"@{replicationName.Name}", replicationName.ReferenceId);


                bulkImport.InfraObject.PRReplicationId =
                    replicationName.Name.Equals(bulkImport.InfraObject.PRReplicationName)
                        ? replicationName?.ReferenceId
                        : bulkImport.InfraObject.PRReplicationId;

                bulkImport.InfraObject.DRReplicationId =
                    replicationName.Name.Equals(bulkImport.InfraObject.DRReplicationName)
                        ? replicationName?.ReferenceId
                        : bulkImport.InfraObject.DRReplicationId;
            }

            var infraObject = _mapper.Map<InfraObject>(bulkImport.InfraObject);

            var infraObjectCommandHandler = new CreateInfraObjectCommandHandler(_mapper, _infraObjectRepository,
                _publisher, _loggedInUserService, _loadBalancerRepository, _client);

            var mapInfraObjectCommand = _mapper.Map<CreateInfraObjectCommand>(bulkImport.InfraObject);

            var updateBaseResponse = await infraObjectCommandHandler.Handle(mapInfraObjectCommand, cancellationToken);

            if (updateBaseResponse.Success)
                await CreateBulkImportActionResult(updateBaseResponse.InfraObjectId, infraObject?.Name, "InfraObject",
                    "Success", startDate, updateBaseResponse.Message, operationId, operationGroupId);

            if (bulkImport.IsFailBack || bulkImport.IsFailOver || bulkImport.IsSwitchBack || bulkImport.IsSwitchOver)
            {
                await Task.Delay(2000, cancellationToken);

                await CreateWorkflow(bulkImport, operationId, operationGroupId, _workflowRepository, _workflowHistoryRepository, _infraObjectRepository, cancellationToken);
            }

        }
        catch (Exception exc)
        {
            if (!string.IsNullOrWhiteSpace(exc.Message))
                await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, "InfraObject", "Error",
                    DateTime.Now, exc.Message, operationId, operationGroupId);
            var ex = new Exception("InfraObject import failed.", exc);
            throw ex;
        }
        
    }

    public async Task CreateWorkflow(CreateBulkImportOperationListCommand bulkImport, string operationId,
       string operationGroupId, IWorkflowRepository _workflowRepository, IWorkflowHistoryRepository _workflowHistoryRepository, IInfraObjectRepository _infraObjectRepository, CancellationToken cancellationToken)
    {
        var workflowCommandHandler = new CreateWorkflowCommandHandler(_mapper, _workflowRepository,
               _workflowHistoryRepository, _loggedInUserService, _publisher, _versionManager, _hubContext);

        var workflowActions = new List<(bool IsActive, string Prefix, string Properties)>
        {
            (bulkImport.IsSwitchOver, "SO", bulkImport.SwitchOverTemplate),
            (bulkImport.IsSwitchBack, "SB", bulkImport.SwitchBackTemplate),
            (bulkImport.IsFailOver, "FO", bulkImport.FailOverTemplate),
            (bulkImport.IsFailBack, "FB", bulkImport.FailBackTemplate)
        };

        var filterWorkflows = new List<(bool IsActive, string Prefix, string Properties)>();

        var activeWorkflowActions = workflowActions
            .Where(action => action.IsActive)
            .ToList();

        foreach (var removeAction in activeWorkflowActions)
        {
            var actionResultWorkflowRunningList =
                await _bulkImportActionResultRepository.GetActionByOperationGroupIdAndEntityType(operationGroupId,
                    $"{removeAction.Prefix}Workflow");

            if (actionResultWorkflowRunningList is null) filterWorkflows.Add(removeAction);
        }

        foreach (var action in filterWorkflows)
        {
            var name = "";
            try
            {

                var infraObject = await _infraObjectRepository.GetInfraObjectByName(bulkImport.InfraObject.Name);

                var actionType = $"{action.Prefix}Workflow";

                if (infraObject.IsNull())
                    await CreateBulkImportActionResult("", bulkImport.InfraObject.Name, $"{action.Prefix}Workflow", "Error",
                        DateTime.Now,
                        $"The {action.Prefix} workflow was not created because the infraObject '{bulkImport.InfraObject.Name}' is not configured."
                        , operationId, operationGroupId);

                var randomString =
                    string.Concat(Enumerable.Range(0, 6).Select(_ => Chars[new Random().Next(Chars.Length)]));

                name = $"{bulkImport.InfraObject.ReplicationTypeName}_{action.Prefix}_{randomString}";

                var response = await workflowCommandHandler.Handle(new CreateWorkflowCommand
                {
                    Name = name,
                    Properties = action.Properties,
                    CompanyId = bulkImport.InfraObject.CompanyId,
                    IsPublish = true
                }, CancellationToken.None);

                if (response.Success)
                    await CreateBulkImportActionResult(response.WorkflowId, name, actionType, "Success", DateTime.Now,
                        response.Message, operationId, operationGroupId);
            }
            catch (Exception exc)
            {
                if (!string.IsNullOrWhiteSpace(exc.Message))
                    await CreateBulkImportActionResult("",name, "Workflow", "Error",
                            DateTime.Now, exc.Message, operationId, operationGroupId);
                var ex = new Exception("Workflow import failed.", exc);
            }
        }
    }
        


    private async Task CreateBulkImportActionResult(
    string entityId,
    string entityName,
    string entityType,
    string status,
    DateTime startTime,
    string error,
    string operationId = null,
    string operationGroupId = null)
    {
        var group = await _bulkImportOperationGroupRepository.GetByReferenceIdAsync(operationGroupId);
        var endTime = DateTime.Now;
        var actionResult = entityId.IsNotNullOrEmpty()
            ? await _bulkImportActionResultRepository.GetByEntityIdAndBulkImportOperationId(entityId, operationId)
            : null;

        var isError = status.Equals("error", StringComparison.OrdinalIgnoreCase);

        await _hubContext.Clients.All.SendAsync("notification", new
        {
           Group = "BulkImport",
           Status = status,
           Message = error
        });


        if (actionResult != null)
        {
            actionResult.EntityId = entityId;
            actionResult.EntityName = entityName;
            actionResult.EntityType = entityType;
            actionResult.NodeId = group.NodeId;
            actionResult.Status = status;
            actionResult.StartTime = startTime;
            actionResult.EndTime = endTime;
            actionResult.ErrorMessage = error;

            await _bulkImportActionResultRepository.UpdateAsync(actionResult);
        }
        else
        {
            await _bulkImportActionResultRepository.AddAsync(new BulkImportActionResult
            {
                BulkImportOperationId = operationId,
                BulkImportOperationGroupId = operationGroupId,
                EntityId = entityId,
                EntityName = entityName,
                EntityType = entityType,
                NodeId = group.NodeId,
                Status = status,
                StartTime = startTime,
                EndTime = endTime,
                ErrorMessage = error
            });
        }

        var resultList = await _bulkImportActionResultRepository.GetByOperationIdAndOperationGroupId(operationId, operationGroupId);

        {
            var split = group.ProgressStatus?.Split('/') ?? Array.Empty<string>();
            var updatedProgress = split.Length > 1 ? $"{resultList.Count}/{split[1]}" : group.ProgressStatus;

            group.ProgressStatus = isError ? group.ProgressStatus : updatedProgress;
            group.Status = resultList.Count==Convert.ToInt32(split[1])?"Completed": status;
            group.ErrorMessage = error;

            await _bulkImportOperationGroupRepository.UpdateAsync(group);
        }

        if (isError)
        {
            _logger.LogError($"EntityName '{entityName}' - {error}");
            throw new InvalidException("");
        }

        _logger.LogInformation($"EntityName '{entityName}' - {error}");
    }



    private static ClaimsPrincipal GetFakeUser(string companyId,String userId) => new(new ClaimsIdentity(new[]
    {
        new Claim("uid", userId),
        new Claim("companyId", companyId),
        new Claim("companyName", "System"),
        new Claim("isParent", "true"),
        new Claim("isAllInfra", "true"),
        new Claim(ClaimTypes.Role, "SuperAdmin"),
        new Claim(ClaimTypes.Name, "BulkImport")
    }, "FakeAuth"));
}