using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectSchedulerLogsFixture : IDisposable
{
    public List<InfraObjectSchedulerLogs> InfraObjectSchedulerLogsPaginationList { get; set; }
    public List<InfraObjectSchedulerLogs> InfraObjectSchedulerLogsList { get; set; }
    public InfraObjectSchedulerLogs InfraObjectSchedulerLogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectId = "INFRA_123";
    public const string WorkflowId = "WORKFLOW_123";

    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectSchedulerLogsFixture()
    {
        var fixture = new Fixture();

        InfraObjectSchedulerLogsList = fixture.Create<List<InfraObjectSchedulerLogs>>();

        InfraObjectSchedulerLogsPaginationList = fixture.CreateMany<InfraObjectSchedulerLogs>(20).ToList();

        // Setup proper test data for InfraObjectSchedulerLogsPaginationList
        InfraObjectSchedulerLogsPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerLogsPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerLogsPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerLogsPaginationList.ForEach(x => x.InfraObjectId = InfraObjectId);

        // Setup proper test data for InfraObjectSchedulerLogsList
        InfraObjectSchedulerLogsList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectSchedulerLogsList.ForEach(x => x.IsActive = true);
        InfraObjectSchedulerLogsList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectSchedulerLogsList.ForEach(x => x.InfraObjectId = InfraObjectId);

        InfraObjectSchedulerLogsDto = fixture.Create<InfraObjectSchedulerLogs>();
        InfraObjectSchedulerLogsDto.ReferenceId = Guid.NewGuid().ToString();
        InfraObjectSchedulerLogsDto.IsActive = true;
        InfraObjectSchedulerLogsDto.CompanyId = CompanyId;
        InfraObjectSchedulerLogsDto.InfraObjectId = InfraObjectId;
        InfraObjectSchedulerLogsDto.InfraObjectName = "Test Infrastructure Object";
        InfraObjectSchedulerLogsDto.WorkflowTypeId = "WT_123";
        InfraObjectSchedulerLogsDto.WorkflowType = "Disaster Recovery";
        InfraObjectSchedulerLogsDto.BeforeSwitchOverWorkflowId = WorkflowId;
        InfraObjectSchedulerLogsDto.BeforeSwitchOverWorkflowName = "Before Switchover Workflow";
        InfraObjectSchedulerLogsDto.AfterSwitchOverWorkflowId = "WORKFLOW_456";
        InfraObjectSchedulerLogsDto.AfterSwitchOverWorkflowName = "After Switchover Workflow";
        InfraObjectSchedulerLogsDto.ScheduleType = 1;
        InfraObjectSchedulerLogsDto.CronExpression = "0 0 12 * * ?";
        InfraObjectSchedulerLogsDto.ScheduleTime = "12:00:00";
        InfraObjectSchedulerLogsDto.Status = "Active";
        InfraObjectSchedulerLogsDto.NodeId = "NODE_123";
        InfraObjectSchedulerLogsDto.NodeName = "Test Node";
        InfraObjectSchedulerLogsDto.State = "Running";
        InfraObjectSchedulerLogsDto.IsSchedule = 1;
        InfraObjectSchedulerLogsDto.WorkflowVersion = "1.0";
        InfraObjectSchedulerLogsDto.GroupPolicyId = "GP_123";
        InfraObjectSchedulerLogsDto.GroupPolicyName = "Test Group Policy";
        InfraObjectSchedulerLogsDto.ExecutionPolicy = "Sequential";
        InfraObjectSchedulerLogsDto.IsEnable = true;
        InfraObjectSchedulerLogsDto.LastExecutionTime = DateTime.Now.AddHours(-1).ToString();
        InfraObjectSchedulerLogsDto.ExceptionMessage = null;
        InfraObjectSchedulerLogsDto.CreatedDate = DateTime.Now.AddDays(-1);

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
