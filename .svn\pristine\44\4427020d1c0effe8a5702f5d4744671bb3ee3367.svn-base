using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

/// <summary>
/// Comprehensive tests for InfraObjectSchedulerRepository with focus on AssignedEntity navigation properties:
/// - AssignedBusinessServices
/// - AssignedBusinessFunctions
/// - AssignedInfraObjects
/// </summary>
public class InfraObjectSchedulerRepositoryTests : IClassFixture<InfraObjectSchedulerFixture>, IDisposable
{
    private readonly InfraObjectSchedulerFixture _fixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraObjectSchedulerRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraObjectSchedulerRepositoryTests(InfraObjectSchedulerFixture fixture)
    {
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _fixture = fixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new InfraObjectSchedulerRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraObjectSchedulers.RemoveRange(_dbContext.InfraObjectSchedulers);
        await _dbContext.SaveChangesAsync();
    }

    private List<InfraObjectScheduler> CreateTestInfraObjects()
    {
        return new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = InfraObjectSchedulerFixture.CompanyId,
                InfraObjectId = InfraObjectSchedulerFixture.InfraObjectId, // INFRA_123 - should be included
                InfraObjectName = "Infrastructure 1",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = InfraObjectSchedulerFixture.CompanyId,
                InfraObjectId = "INFRA_456", // Should be included in complex scenarios
                InfraObjectName = "Infrastructure 2",
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = InfraObjectSchedulerFixture.CompanyId,
                InfraObjectId = "INFRA_789", // Should be included in complex scenarios
                InfraObjectName = "Infrastructure 3",
                WorkflowType = "Test Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = InfraObjectSchedulerFixture.CompanyId,
                InfraObjectId = "INFRA_999", // Should be included in complex scenarios
                InfraObjectName = "Infrastructure 4",
                WorkflowType = "Monitoring Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = InfraObjectSchedulerFixture.CompanyId,
                InfraObjectId = "INFRA_EXCLUDED", // Should be excluded
                InfraObjectName = "Infrastructure Excluded",
                WorkflowType = "Excluded Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now
            }
        };
    }

    #region GetPaginatedInfraObjectScheduler Tests - Core AssignedEntity Navigation Coverage

    [Fact]
    public void GetPaginatedInfraObjectScheduler_FiltersCorrectly_WithAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result[0].InfraObjectId);
        Assert.Equal("Infrastructure 1", result[0].InfraObjectName);

        // This test covers:
        // - AssignedEntity.AssignedBusinessServices access
        // - businessService.AssignedBusinessFunctions navigation
        // - businessFunction.AssignedInfraObjects navigation
        // - infraObject.Id property access
    }

    [Fact]
    public void GetPaginatedInfraObjectScheduler_HandlesComplexNestedStructure_WithMultipleBusinessServicesAndFunctions()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true },
                                new AssignedInfraObjects { Id = "INFRA_456", Name = "Infrastructure 2", IsSelected = true }
                            }
                        },
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_002",
                            Name = "Business Function 2",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_789", Name = "Infrastructure 3", IsSelected = true }
                            }
                        }
                    }
                },
                new AssignedBusinessServices
                {
                    Id = "BS_002",
                    Name = "Business Service 2",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_003",
                            Name = "Business Function 3",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_999", Name = "Infrastructure 4", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count); // Should include INFRA_123, INFRA_456, INFRA_789, INFRA_999

        var infraObjectIds = result.Select(x => x.InfraObjectId).OrderBy(x => x).ToList();
        var expectedIds = new[] { InfraObjectSchedulerFixture.InfraObjectId, "INFRA_456", "INFRA_789", "INFRA_999" }.OrderBy(x => x).ToList();
        Assert.Equal(expectedIds, infraObjectIds);
        Assert.DoesNotContain("INFRA_EXCLUDED", infraObjectIds);

        // This test comprehensively covers:
        // - Multiple AssignedBusinessServices iteration
        // - Multiple AssignedBusinessFunctions per service
        // - Multiple AssignedInfraObjects per function
        // - Complex SelectMany operations across all levels
        // - Proper filtering and inclusion/exclusion logic
    }

    [Fact]
    public void GetPaginatedInfraObjectScheduler_ReturnsEmpty_WhenNullAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = null // Null services
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        // This test covers null safety for AssignedBusinessServices
    }

    [Fact]
    public void GetPaginatedInfraObjectScheduler_ReturnsEmpty_WhenEmptyAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>() // Empty list
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        // This test covers empty AssignedBusinessServices collection
    }

    [Fact]
    public void GetPaginatedInfraObjectScheduler_HandlesNullAssignedBusinessFunctions()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = null // Null functions
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        // This test covers null safety for AssignedBusinessFunctions
    }

    [Fact]
    public void GetPaginatedInfraObjectScheduler_HandlesNullAssignedInfraObjects()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = null // Null infra objects
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetPaginatedInfraObjectScheduler(infraObjects).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        // This test covers null safety for AssignedInfraObjects
    }

    #endregion

    #region GetAssignedInfraObjectSheduler Tests - AssignedEntity Navigation Coverage

    [Fact]
    public void GetAssignedInfraObjectSheduler_FiltersCorrectly_WithAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetAssignedInfraObjectSheduler(infraObjects);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result[0].InfraObjectId);
        Assert.Equal("Infrastructure 1", result[0].InfraObjectName);

        // This test covers the iterative approach in GetAssignedInfraObjectSheduler:
        // - AssignedEntity.AssignedBusinessServices.Count > 0 check
        // - foreach loop over AssignedBusinessServices
        // - assignedBusinessServices.AssignedBusinessFunctions access
        // - foreach loop over AssignedBusinessFunctions
        // - assignedBusinessFunction.AssignedInfraObjects access
        // - LINQ query with assignedInfraObject.Id comparison
    }

    [Fact]
    public void GetAssignedInfraObjectSheduler_HandlesMultipleBusinessServicesAndFunctions()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true },
                                new AssignedInfraObjects { Id = "INFRA_456", Name = "Infrastructure 2", IsSelected = true }
                            }
                        },
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_002",
                            Name = "Business Function 2",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_789", Name = "Infrastructure 3", IsSelected = true }
                            }
                        }
                    }
                },
                new AssignedBusinessServices
                {
                    Id = "BS_002",
                    Name = "Business Service 2",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_003",
                            Name = "Business Function 3",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_999", Name = "Infrastructure 4", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetAssignedInfraObjectSheduler(infraObjects);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(4, result.Count); // Should include all 4 assigned infrastructure objects

        var infraObjectIds = result.Select(x => x.InfraObjectId).OrderBy(x => x).ToList();
        var expectedIds = new[] { InfraObjectSchedulerFixture.InfraObjectId, "INFRA_456", "INFRA_789", "INFRA_999" }.OrderBy(x => x).ToList();
        Assert.Equal(expectedIds, infraObjectIds);
        Assert.DoesNotContain("INFRA_EXCLUDED", infraObjectIds);

        // This test covers complex nested iteration:
        // - Multiple business services iteration
        // - Multiple business functions per service
        // - Multiple infra objects per function
        // - Proper collection building and filtering
    }

    [Fact]
    public void GetAssignedInfraObjectSheduler_ReturnsEmpty_WhenNoAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>() // Empty list
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);
        var infraObjects = CreateTestInfraObjects().AsQueryable();

        // Act
        var result = repository.GetAssignedInfraObjectSheduler(infraObjects);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        // This test covers the Count > 0 check for AssignedBusinessServices
    }

    #endregion

    #region GetInfraObjectSchedulerByReferenceId Tests - AssignedEntity Navigation Coverage

    [Fact]
    public void GetInfraObjectSchedulerByReferenceId_FiltersCorrectly_WithAssignedBusinessServices()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var testInfraObject = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = InfraObjectSchedulerFixture.CompanyId,
            InfraObjectId = InfraObjectSchedulerFixture.InfraObjectId,
            InfraObjectName = "Infrastructure 1",
            Status = "Active",
            IsActive = true
        };

        // Act
        var result = repository.GetInfraObjectSchedulerByReferenceId(testInfraObject);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result.InfraObjectId);
        Assert.Equal("Infrastructure 1", result.InfraObjectName);

        // This test covers:
        // - AssignedEntity.AssignedBusinessServices access
        // - SelectMany over AssignedBusinessFunctions
        // - SelectMany over AssignedInfraObjects
        // - Where clause with infraObject.InfraObjectId == assignedInfraObjects.Id
        // - SingleOrDefault operation
    }

    [Fact]
    public void GetInfraObjectSchedulerByReferenceId_ReturnsNull_WhenNotAssigned()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_DIFFERENT", Name = "Different Infrastructure", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var testInfraObject = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = InfraObjectSchedulerFixture.CompanyId,
            InfraObjectId = InfraObjectSchedulerFixture.InfraObjectId, // Not in assigned list
            InfraObjectName = "Infrastructure 1",
            Status = "Active",
            IsActive = true
        };

        // Act
        var result = repository.GetInfraObjectSchedulerByReferenceId(testInfraObject);

        // Assert
        Assert.Null(result);

        // This test verifies the filtering logic works correctly for exclusion
    }

    #endregion

    #region Integration Tests - Public Methods with AssignedEntity Navigation

    [Fact]
    public async Task ListAllAsync_UsesAssignedEntityFiltering_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var infraObjects = CreateTestInfraObjects();
        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result[0].InfraObjectId);

        // This integration test verifies that ListAllAsync properly calls GetAssignedInfraObjectSheduler
        // which exercises the complete AssignedEntity navigation chain
    }

    [Fact]
    public async Task GetByReferenceIdAsync_UsesAssignedEntityFiltering_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "ebfd199c-01aa-4f36-a2af-3495af6fb166",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var testInfraObject = new InfraObjectScheduler
        {
            ReferenceId = "ebfd199c-01aa-4f36-a2af-3495af6fb166",
            CompanyId = "COMPANY_123",
            InfraObjectId = InfraObjectSchedulerFixture.InfraObjectId,
            InfraObjectName = "Infrastructure 1",
            Status = "Active",
            IsActive = true,
            CreatedDate = DateTime.Now
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(testInfraObject);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetByReferenceIdAsync(testInfraObject.ReferenceId);

        // Assert
        Assert.NotNull(result);
        //Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result.InfraObjectId);

        // This integration test verifies that GetByReferenceIdAsync properly calls GetBusinessFunctionByReferenceId
        // which exercises the AssignedEntity navigation chain
    }

    [Fact]
    public async Task PaginatedListAllAsync_UsesAssignedEntityFiltering_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false, isParent: true);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var infraObjects = CreateTestInfraObjects();
        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await repository.PaginatedListAllAsync(1, 10, null, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        //Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result.Data[0].InfraObjectId);

        // This integration test verifies that PaginatedListAllAsync properly calls GetPaginatedInfraObjectScheduler
        // which exercises the complete AssignedEntity navigation chain with SelectMany operations
    }

    [Fact]
    public async Task PaginatedListAllAsync_WithSpecification_FiltersCorrectly_WhenIsParentTrue_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();

        var infraObjects = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Test Infrastructure 1",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-1)
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Test Infrastructure 2",
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-2)
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_456", // Different company
                InfraObjectId = "INFRA_003",
                InfraObjectName = "Other Company Infrastructure",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-3)
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var mockService = new Mock<ILoggedInUserService>();
        mockService.Setup(x => x.IsParent).Returns(true);
        mockService.Setup(x => x.IsAllInfra).Returns(true);
        mockService.Setup(x => x.IsAuthenticated).Returns(true);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        // Create a specification to filter by WorkflowType
        var specification = new InfraObjectSchedulerFilterSpecification("DR Workflow");

        // Act
        var result = await repository.PaginatedListAllAsync(1, 10, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count); // Should include both DR Workflow items from all companies
        Assert.Equal(2, result.TotalCount);
        Assert.All(result.Data, item => Assert.Equal("DR Workflow", item.WorkflowType));

        // Verify ordering (descending by Id)
        Assert.True(result.Data[0].Id > result.Data[1].Id);

        // This test covers the missing case: IsParent=true, IsAllInfra=true with actual data and specification
        // This exercises the path: MapInfraObjectScheduler(Entities.Specify(productFilterSpec).DescOrderById())
    }

    [Fact]
    public async Task PaginatedListAllAsync_WithSpecification_FiltersCorrectly_WhenIsParentFalse_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();

        var infraObjects = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Company Infrastructure 1",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-1)
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Company Infrastructure 2",
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-2)
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_456", // Different company - should be excluded
                InfraObjectId = "INFRA_003",
                InfraObjectName = "Other Company Infrastructure",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-3)
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var mockService = new Mock<ILoggedInUserService>();
        mockService.Setup(x => x.IsParent).Returns(false);
        mockService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        mockService.Setup(x => x.IsAllInfra).Returns(true);
        mockService.Setup(x => x.IsAuthenticated).Returns(true);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        // Create a specification to filter by WorkflowType
        var specification = new InfraObjectSchedulerFilterSpecification("DR Workflow");

        // Act
        var result = await repository.PaginatedListAllAsync(1, 10, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data); // Should include only the DR Workflow item from COMPANY_123
        Assert.Equal(1, result.TotalCount);
        Assert.Equal("DR Workflow", result.Data[0].WorkflowType);
        Assert.Equal("COMPANY_123", result.Data[0].CompanyId);

        // This test covers: IsParent=false, IsAllInfra=true with actual data, specification, and company filtering
        // This exercises the path: MapInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
    }

    [Fact]
    public async Task PaginatedListAllAsync_WithSpecification_FiltersCorrectly_WhenIsParentFalse_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = "INFRA_001", Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var infraObjects = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_001", // This should be included (assigned)
                InfraObjectName = "Assigned Infrastructure",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-1)
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_002", // This should be excluded (not assigned)
                InfraObjectName = "Non-Assigned Infrastructure",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-2)
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false, isParent: false);
        mockService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        // Create a specification to filter by WorkflowType
        var specification = new InfraObjectSchedulerFilterSpecification("DR Workflow");

        // Act
        var result = await repository.PaginatedListAllAsync(1, 10, specification, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data); // Should include only the assigned DR Workflow item
        Assert.Equal(1, result.TotalCount);
        Assert.Equal("DR Workflow", result.Data[0].WorkflowType);
        Assert.Equal("INFRA_001", result.Data[0].InfraObjectId);
        Assert.Equal("Assigned Infrastructure", result.Data[0].InfraObjectName);

        // This test covers: IsParent=false, IsAllInfra=false with actual data, specification, company filtering, and assigned entity filtering
        // This exercises the path: MapInfraObjectScheduler(GetPaginatedInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()))
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetInfraObjectSchedulerNames_UsesAssignedEntityFiltering_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();

        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        var infraObjects = CreateTestInfraObjects();
        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjects);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(InfraObjectSchedulerFixture.InfraObjectId, result[0].InfraObjectId);

        // This test verifies GetInfraObjectSchedulerNames uses GetAssignedInfraObjectSheduler
    }



    [Fact]
    public void GetPaginatedQuery_UsesAssignedEntityFiltering_WhenIsAllInfraFalse()
    {
        // Arrange
        var assignedEntity = new AssignedEntity
        {
            IsAll = false,
            AssignedBusinessServices = new List<AssignedBusinessServices>
            {
                new AssignedBusinessServices
                {
                    Id = "BS_001",
                    Name = "Business Service 1",
                    AssignedBusinessFunctions = new List<AssignedBusinessFunctions>
                    {
                        new AssignedBusinessFunctions
                        {
                            Id = "BF_001",
                            Name = "Business Function 1",
                            AssignedInfraObjects = new List<AssignedInfraObjects>
                            {
                                new AssignedInfraObjects { Id = InfraObjectSchedulerFixture.InfraObjectId, Name = "Infrastructure 1", IsSelected = true }
                            }
                        }
                    }
                }
            }
        };

        var mockService = _fixture.CreateCustomMock(assignedEntity, isAllInfra: false);
        var repository = new InfraObjectSchedulerRepository(_dbContext, mockService.Object);

        // Act
        var result = repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);

        // This test verifies GetPaginatedQuery uses GetPaginatedInfraObjectScheduler
        // The actual filtering is tested in the database integration tests
    }






    #region IsInfraObjectSchedulerNameExist Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, invalidId);

        // Assert
        Assert.True(result); // Should return true when name exists and ID is invalid
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "Non-Existent Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Different Infrastructure Scheduler",
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(nonExistentName, invalidId);

        // Assert
        Assert.False(result); // Should return false when name doesn't exist
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsTrue_WhenNameExistsWithDifferentValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, differentId);

        // Assert
        Assert.True(result); // Should return true when name exists with different ID
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_ReturnsFalse_WhenNameExistsWithSameValidId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var existingId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, existingId);

        // Assert
        Assert.False(result); // Should return false when name exists with same ID (editing same record)
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var invalidId = "invalid-guid";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsInfraObjectSchedulerNameExist("Test Infrastructure Scheduler", invalidId);
        var resultDifferentCase = await _repository.IsInfraObjectSchedulerNameExist("test infrastructure scheduler", invalidId);

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    #endregion

    #region GetInfraObjectSchedulerByInfraObjectId Tests

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_ExecutesWithoutError_WhenIsParentTrue_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
                              // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_UsesGetPaginatedInfraObjectScheduler_WhenIsParentTrue_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var infra = _fixture.InfraObjectSchedulerDto;
        infra.InfraObjectId = infraObjectId;
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
                              // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_ExecutesWithoutError_WhenIsParentFalse_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
                              // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerByInfraObjectId_UsesGetPaginatedInfraObjectScheduler_WhenIsParentFalse_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
                              // This path USES GetPaginatedInfraObjectScheduler
    }

    #endregion

    #region GetInfraObjectSchedulerByWorkflowId Tests

    [Fact]
    public async Task GetInfraObjectSchedulerByWorkflowId_ReturnsMatchingSchedulers_WhenWorkflowIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var workflowId = "WORKFLOW_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var infraObjectSchedulers = new List<InfraObjectScheduler>
        {
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "Infrastructure 1",
                AfterSwitchOverWorkflowId = workflowId, // Matches
                BeforeSwitchOverWorkflowId = "WORKFLOW_456",
                WorkflowType = "DR Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "Infrastructure 2",
                AfterSwitchOverWorkflowId = "WORKFLOW_789",
                BeforeSwitchOverWorkflowId = workflowId, // Matches
                WorkflowType = "Backup Workflow",
                Status = "Active",
                IsActive = true
            },
            new InfraObjectScheduler
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                InfraObjectId = "INFRA_003",
                InfraObjectName = "Infrastructure 3",
                AfterSwitchOverWorkflowId = "WORKFLOW_999",
                BeforeSwitchOverWorkflowId = "WORKFLOW_888", // No match
                WorkflowType = "Test Workflow",
                Status = "Active",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectSchedulers.AddRangeAsync(infraObjectSchedulers);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectSchedulerByWorkflowId(workflowId);

        // Assert
        Assert.Equal(2, result.Count);

    }

    #endregion

    #region IsInfraObjectSchedulerNameUnique Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_ReturnsTrue_WhenCombinationExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameUnique(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName);

        // Assert
        Assert.True(result); // Should return true when combination exists
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_ReturnsFalse_WhenCombinationDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Different Infrastructure Scheduler",
            AfterSwitchOverWorkflowName = "Different After Workflow",
            BeforeSwitchOverWorkflowName = "Different Before Workflow",
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameUnique(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName);

        // Assert
        Assert.False(result); // Should return false when combination doesn't exist
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameUnique_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.IsInfraObjectSchedulerNameUnique("Test Infrastructure Scheduler", "After Workflow", "Before Workflow");
        var resultDifferentCase = await _repository.IsInfraObjectSchedulerNameUnique("test infrastructure scheduler", "after workflow", "before workflow");

        // Assert
        Assert.True(resultExactCase); // Exact case match should return true
        Assert.False(resultDifferentCase); // Different case should return false (case sensitive)
    }

    #endregion

    #region IsInfraObjectSchedulerNameExist (with workflows) Tests

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_WithWorkflows_ReturnsTrue_WhenCombinationExistsWithDifferentId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";
        var existingId = Guid.NewGuid().ToString();
        var differentId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName, differentId);

        // Assert
        Assert.True(result); // Should return true when combination exists with different ID
    }

    [Fact]
    public async Task IsInfraObjectSchedulerNameExist_WithWorkflows_ReturnsFalse_WhenCombinationExistsWithSameId()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectName = "Test Infrastructure Scheduler";
        var afterSwitchOverWorkflowName = "After Workflow";
        var beforeSwitchOverWorkflowName = "Before Workflow";
        var existingId = Guid.NewGuid().ToString();

        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = existingId,
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = infraObjectName,
            AfterSwitchOverWorkflowName = afterSwitchOverWorkflowName,
            BeforeSwitchOverWorkflowName = beforeSwitchOverWorkflowName,
            WorkflowType = "DR Workflow",
            Status = "Active",
            IsActive = true
        };

        await _dbContext.InfraObjectSchedulers.AddAsync(infraObjectScheduler);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsInfraObjectSchedulerNameExist(infraObjectName, afterSwitchOverWorkflowName, beforeSwitchOverWorkflowName, existingId);

        // Assert
        Assert.False(result); // Should return false when combination exists with same ID (editing same record)
    }

    #endregion

    #region Overridden Repository Methods Tests

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
    }

    [Fact]
    public async Task ListAllAsync_ExecutesWithoutError_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
    }



    [Fact]
    public async Task PaginatedListAllAsync_ExecutesWithoutError_WhenIsParentTrue_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no data exists
        Assert.Empty(result.Data);
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_UsesGetPaginatedInfraObjectScheduler_WhenIsParentTrue_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no assigned infras
        Assert.Empty(result.Data);
        // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_ExecutesWithoutError_WhenIsParentFalse_IsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no data exists
        Assert.Empty(result.Data);
        // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task PaginatedListAllAsync_UsesGetPaginatedInfraObjectScheduler_WhenIsParentFalse_IsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var pageNumber = 1;
        var pageSize = 10;
        var sortColumn = "Id";
        var sortOrder = "desc";
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.PaginatedListAllAsync(pageNumber, pageSize, null, sortColumn, sortOrder);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.TotalCount); // Should be 0 when no assigned infras
        Assert.Empty(result.Data);
        // This path USES GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetPaginatedQuery_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
                              // This path does NOT use GetPaginatedInfraObjectScheduler
    }

    [Fact]
    public async Task GetPaginatedQuery_UsesGetPaginatedInfraObjectScheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
                              // This path USES GetPaginatedInfraObjectScheduler
    }

    #endregion

    #region GetInfraObjectSchedulerNames Tests - GetAssignedInfraObjectSheduler Coverage

    [Fact]
    public async Task GetInfraObjectSchedulerNames_ExecutesWithoutError_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no data exists
                              // This path does NOT use GetAssignedInfraObjectSheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerNames_UsesGetAssignedInfraObjectSheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
                              // This path USES GetAssignedInfraObjectSheduler
    }

    [Fact]
    public async Task GetInfraObjectSchedulerNames_HandlesNullAssignedBusinessServices()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{\"AssignedBusinessServices\":null}");

        // Act
        var result = await _repository.GetInfraObjectSchedulerNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should handle null assigned business services gracefully
    }

    #endregion

    #region ListAllAsync Tests - GetAssignedInfraObjectSheduler Coverage

    [Fact]
    public async Task ListAllAsync_UsesGetAssignedInfraObjectSheduler_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.AssignedInfras).Returns("{}"); // Empty assigned infras

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should be empty when no assigned infras
                              // This path USES GetAssignedInfraObjectSheduler
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddInfraObjectScheduler_WhenValidInfraObjectScheduler()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectScheduler = new InfraObjectScheduler
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            WorkflowTypeId = "WT_123",
            WorkflowType = "Disaster Recovery",
            BeforeSwitchOverWorkflowId = "WORKFLOW_123",
            BeforeSwitchOverWorkflowName = "Before Switchover Workflow",
            AfterSwitchOverWorkflowId = "WORKFLOW_456",
            AfterSwitchOverWorkflowName = "After Switchover Workflow",
            ScheduleType = 1,
            Status = "Active",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraObjectScheduler);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectScheduler.CompanyId, result.CompanyId);
        Assert.Equal(infraObjectScheduler.InfraObjectId, result.InfraObjectId);
        Assert.Equal(infraObjectScheduler.InfraObjectName, result.InfraObjectName);
        Assert.Equal(infraObjectScheduler.WorkflowType, result.WorkflowType);
        Assert.Single(_dbContext.InfraObjectSchedulers);
    }

    #endregion
    #endregion

}