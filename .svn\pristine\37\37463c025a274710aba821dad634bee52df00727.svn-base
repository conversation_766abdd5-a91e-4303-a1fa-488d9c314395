﻿using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyLog.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DRReadyLogModel;

namespace ContinuityPatrol.Application.Mappings;

public class DrReadyLogProfile : Profile
{
    public DrReadyLogProfile()
    {
        CreateMap<DRReadyLog, CreateDRReadyLogCommand>().ReverseMap();
        CreateMap<UpdateDRReadyLogCommand, DRReadyLog>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<DRReadyLog, DRReadyLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyLog, DRReadyLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DRReadyLog, DRReadyLogByBusinessServiceIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        //CreateMap<DRReadyLog, DRReadyLogForDRReadyReportVm>()
        //    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
        //    .ForMember(dest => dest.StartTime, opt => opt.MapFrom(src => src.CreatedDate))
        //    .ForMember(dest => dest.EndTime, opt => opt.MapFrom(src => src.LastModifiedDate));

        //CreateMap<DRReadyLog, InfraObjectCountList>().ReverseMap();

        CreateMap<DRReadyLog, DRReadyLogByLast7DaysVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}