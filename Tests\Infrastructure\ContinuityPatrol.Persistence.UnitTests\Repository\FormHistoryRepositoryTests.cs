using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FormHistoryRepositoryTests : IClassFixture<FormHistoryFixture>, IDisposable
{
    private readonly FormHistoryFixture _formHistoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FormHistoryRepository _repository;

    public FormHistoryRepositoryTests(FormHistoryFixture formHistoryFixture)
    {
        _formHistoryFixture = formHistoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FormHistoryRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region GetFormHistoryByFormId Tests

    [Fact]
    public async Task GetFormHistoryByFormId_ReturnsFormHistories_WhenFormIdExists()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var formHistory1 = new Domain.Entities.FormHistory
        {
            FormId = formId,
            FormName = "Test Form 1",
            Version = "1.0",
            CompanyId = FormHistoryFixture.CompanyId,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var formHistory2 = new Domain.Entities.FormHistory
        {
            FormId = formId,
            FormName = "Test Form 2",
            Version = "2.0",
            CompanyId = FormHistoryFixture.CompanyId,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var differentFormHistory = new Domain.Entities.FormHistory
        {
            FormId = Guid.NewGuid().ToString(),
            FormName = "Different Form",
            Version = "1.0",
            CompanyId = FormHistoryFixture.CompanyId,
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FormHistories.AddRangeAsync(new[] { formHistory1, formHistory2, differentFormHistory });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormId(formId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(formId, x.FormId));
    }

    [Fact]
    public async Task GetFormHistoryByFormId_ReturnsEmpty_WhenFormIdDoesNotExist()
    {
        // Arrange
        var nonExistentFormId = Guid.NewGuid().ToString();

        _formHistoryFixture.FormHistoryDto.FormId = Guid.NewGuid().ToString();
        await _dbContext.FormHistories.AddAsync(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormId(nonExistentFormId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFormHistoryByFormId_ThrowsInvalidArgumentException_WhenFormIdIsInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetFormHistoryByFormId("invalid-guid"));
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetFormHistoryByFormId(""));
    }

    [Fact]
    public async Task GetFormHistoryByFormId_ThrowsArgumentNullException_WhenFormIdIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.GetFormHistoryByFormId(null));
    }

    [Fact]
    public async Task GetFormHistoryByFormId_ReturnsAllVersions_WhenMultipleVersionsExist()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var histories = new[]
        {
            new Domain.Entities.FormHistory
            {
                FormId = formId,
                FormName = "Test Form",
                Version = "1.0",
                CompanyId = FormHistoryFixture.CompanyId,
                ReferenceId = Guid.NewGuid().ToString()
            },
            new Domain.Entities.FormHistory
            {
                FormId = formId,
                FormName = "Test Form",
                Version = "1.1",
                CompanyId = FormHistoryFixture.CompanyId,
                ReferenceId = Guid.NewGuid().ToString()
            },
            new Domain.Entities.FormHistory
            {
                FormId = formId,
                FormName = "Test Form",
                Version = "2.0",
                CompanyId = FormHistoryFixture.CompanyId,
                ReferenceId = Guid.NewGuid().ToString()
            }
        };

        await _dbContext.FormHistories.AddRangeAsync(histories);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormId(formId);

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Contains(result, x => x.Version == "1.0");
        Assert.Contains(result, x => x.Version == "1.1");
        Assert.Contains(result, x => x.Version == "2.0");
    }

    #endregion

    #region GetFormHistoryByFormIdAndVersion Tests

    [Fact]
    public async Task GetFormHistoryByFormIdAndVersion_ReturnsFormHistory_WhenFormIdAndVersionExist()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var version = "1.5";
        var formHistory = new Domain.Entities.FormHistory
        {
            FormId = formId,
            FormName = "Specific Version Form",
            Version = version,
            CompanyId = FormHistoryFixture.CompanyId,
            ReferenceId = Guid.NewGuid().ToString(),
            Description = "Test Description"
        };

        await _dbContext.FormHistories.AddAsync(formHistory);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormIdAndVersion(formId, version);

        // Assert
        Assert.Single(result);
        Assert.Equal(formId, result[0].FormId);
        Assert.Equal(version, result[0].Version);
        Assert.Equal("Specific Version Form", result[0].FormName);
    }

    [Fact]
    public async Task GetFormHistoryByFormIdAndVersion_ReturnsEmpty_WhenFormIdDoesNotExist()
    {
        // Arrange
        var nonExistentFormId = Guid.NewGuid().ToString();
        var version = "1.0";

        _formHistoryFixture.FormHistoryDto.FormId = Guid.NewGuid().ToString();
        _formHistoryFixture.FormHistoryDto.Version = version;
        await _dbContext.FormHistories.AddAsync(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormIdAndVersion(nonExistentFormId, version);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFormHistoryByFormIdAndVersion_ReturnsEmpty_WhenVersionDoesNotExist()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var nonExistentVersion = "99.0";

        _formHistoryFixture.FormHistoryDto.FormId = formId;
        _formHistoryFixture.FormHistoryDto.Version = "1.0";
        await _dbContext.FormHistories.AddAsync(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormIdAndVersion(formId, nonExistentVersion);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFormHistoryByFormIdAndVersion_ReturnsMultiple_WhenMultipleRecordsMatch()
    {
        // Arrange
        var formId = Guid.NewGuid().ToString();
        var version = "2.0";
        var histories = new[]
        {
            new Domain.Entities.FormHistory
            {
                FormId = formId,
                FormName = "Form A",
                Version = version,
                CompanyId = FormHistoryFixture.CompanyId,
                ReferenceId = Guid.NewGuid().ToString()
            },
            new Domain.Entities.FormHistory
            {
                FormId = formId,
                FormName = "Form B",
                Version = version,
                CompanyId = FormHistoryFixture.CompanyId,
                ReferenceId = Guid.NewGuid().ToString()
            }
        };

        await _dbContext.FormHistories.AddRangeAsync(histories);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFormHistoryByFormIdAndVersion(formId, version);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(formId, x.FormId));
        Assert.All(result, x => Assert.Equal(version, x.Version));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsFormHistories_WhenFormHistoriesExist()
    {
        // Arrange
        await _dbContext.FormHistories.AddRangeAsync(_formHistoryFixture.FormHistoryList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(FormHistoryFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoFormHistoriesExist()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFormHistory_WhenValidFormHistory()
    {
        // Arrange
        var formHistory = _formHistoryFixture.FormHistoryDto;
        formHistory.FormId = Guid.NewGuid().ToString();
        formHistory.FormName = "Test Form";
        formHistory.Version = "1.0";
        formHistory.Type = "Survey";
        formHistory.Properties = "{\"fields\":[{\"name\":\"field1\",\"type\":\"text\"}]}";
        formHistory.LoginName = "testuser";
        formHistory.UpdaterId = "updater123";
        formHistory.IsPublish = false;
        formHistory.Description = "Test form description";
        formHistory.Comments = "Initial version";

        // Act
        var result = await _repository.AddAsync(formHistory);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(formHistory.FormId, result.FormId);
        Assert.Equal(formHistory.FormName, result.FormName);
        Assert.Equal(formHistory.Version, result.Version);
        Assert.Equal(formHistory.Type, result.Type);
        Assert.Equal(formHistory.Properties, result.Properties);
        Assert.Single(_dbContext.FormHistories);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenFormHistoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsFormHistory_WhenExists()
    {
        // Arrange
        _formHistoryFixture.FormHistoryDto.Id = 1;
        await _dbContext.FormHistories.AddAsync(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_formHistoryFixture.FormHistoryDto.Id, result.Id);
        Assert.Equal(_formHistoryFixture.FormHistoryDto.FormId, result.FormId);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsFormHistory_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _formHistoryFixture.FormHistoryDto.ReferenceId = referenceId;

        await _dbContext.FormHistories.AddAsync(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateFormHistory_WhenValidFormHistory()
    {
        // Arrange
        _dbContext.FormHistories.Add(_formHistoryFixture.FormHistoryDto);
        await _dbContext.SaveChangesAsync();

        _formHistoryFixture.FormHistoryDto.FormName = "Updated Form Name";
        _formHistoryFixture.FormHistoryDto.Version = "2.0";
        _formHistoryFixture.FormHistoryDto.IsPublish = true;
        _formHistoryFixture.FormHistoryDto.Comments = "Updated version";

        // Act
        var result = await _repository.UpdateAsync(_formHistoryFixture.FormHistoryDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Form Name", result.FormName);
        Assert.Equal("2.0", result.Version);
        Assert.True(result.IsPublish);
        Assert.Equal("Updated version", result.Comments);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenFormHistoryIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion
}
