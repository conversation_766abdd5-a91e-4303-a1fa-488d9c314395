using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Delete;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class AdPasswordExpireFixture : IDisposable
{
    public List<AdPasswordExpire> AdPasswordExpires { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateAdPasswordExpireCommand CreateAdPasswordExpireCommand { get; set; }
    public UpdateAdPasswordExpireCommand UpdateAdPasswordExpireCommand { get; set; }
    public DeleteAdPasswordExpireCommand DeleteAdPasswordExpireCommand { get; set; }
    public AdPasswordExpireCreatedEvent AdPasswordExpireCreatedEvent { get; set; }
    public AdPasswordExpireDeletedEvent AdPasswordExpireDeletedEvent { get; set; }
    public AdPasswordExpireUpdatedEvent AdPasswordExpireUpdatedEvent { get; set; }
    public IMapper Mapper { get; set; }

    public AdPasswordExpireFixture()
    {
        AdPasswordExpires = new List<AdPasswordExpire>
        {
            new AdPasswordExpire
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DomainServerId = "DS001",
                DomainServer = "TestDomain.com",
                UserName = "testuser",
                Email = "<EMAIL>",
                ServerList = "Server1,Server2",
                NotificationDays = "7,14,30",
                IsPassword = true,
                IsActive = true
            }
        };

        AdPasswordExpires = AutoAdPasswordExpireFixture.Create<List<AdPasswordExpire>>();
        UserActivities = AutoAdPasswordExpireFixture.Create<List<UserActivity>>();
        CreateAdPasswordExpireCommand = AutoAdPasswordExpireFixture.Create<CreateAdPasswordExpireCommand>();
        UpdateAdPasswordExpireCommand = AutoAdPasswordExpireFixture.Create<UpdateAdPasswordExpireCommand>();
        DeleteAdPasswordExpireCommand = AutoAdPasswordExpireFixture.Create<DeleteAdPasswordExpireCommand>();
        AdPasswordExpireCreatedEvent = AutoAdPasswordExpireFixture.Create<AdPasswordExpireCreatedEvent>();
        AdPasswordExpireDeletedEvent = AutoAdPasswordExpireFixture.Create<AdPasswordExpireDeletedEvent>();
        AdPasswordExpireUpdatedEvent = AutoAdPasswordExpireFixture.Create<AdPasswordExpireUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<AdPasswordExpireProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoAdPasswordExpireFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateAdPasswordExpireCommand>(p => p.UserName, 10));
            fixture.Customizations.Add(new EmailAddressStringsGenerator<CreateAdPasswordExpireCommand>(p => p.Email));
            fixture.Customize<CreateAdPasswordExpireCommand>(c => c.With(b => b.DomainServerId, "DS001"));
            fixture.Customize<CreateAdPasswordExpireCommand>(c => c.With(b => b.IsPassword, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateAdPasswordExpireCommand>(p => p.UserName, 10));
            fixture.Customizations.Add(new EmailAddressStringsGenerator<UpdateAdPasswordExpireCommand>(p => p.Email));
            fixture.Customize<UpdateAdPasswordExpireCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateAdPasswordExpireCommand>(c => c.With(b => b.IsPassword, true));

            fixture.Customize<DeleteAdPasswordExpireCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<AdPasswordExpire>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<AdPasswordExpire>(c => c.With(b => b.IsActive, true));
            fixture.Customize<AdPasswordExpire>(c => c.With(b => b.IsPassword, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordExpireCreatedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordExpireDeletedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordExpireUpdatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
