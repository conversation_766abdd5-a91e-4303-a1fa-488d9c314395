﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;


namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessFunctionRepository : BaseRepository<BusinessFunction>, IBusinessFunctionRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public BusinessFunctionRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<BusinessFunction>> ListAllAsync()
    {
        var businessFunctions =base.QueryAll(bf => bf.CompanyId == _loggedInUserService.CompanyId);
        var bsFunction = MapBusinessFunction(businessFunctions);

        return _loggedInUserService.IsAllInfra
            ? await bsFunction.ToListAsync()
            : GetAssignedBusinessFunctions(bsFunction);
    }

    public override async Task<BusinessFunction> GetByReferenceIdAsync(string id)
    {
        var businessFunction =GetByReferenceId(id, bf => bf.CompanyId ==_loggedInUserService.CompanyId && bf.ReferenceId == id);
        var businessFunctionDto = MapBusinessFunction(businessFunction);

        return _loggedInUserService.IsAllInfra
            ? await businessFunctionDto.FirstOrDefaultAsync()
            : GetBusinessFunctionByReferenceId(businessFunctionDto.FirstOrDefault());
    }
    public async Task<IReadOnlyList<BusinessFunction>> GetByReferenceIdsAsync(List<string> ids)
    {
        var businessFunction = IsParent
           ? Entities.AsNoTracking().DescOrderById().Where(x =>ids.Contains(x.ReferenceId))
           : Entities.AsNoTracking().DescOrderById().Where(bf => ids.Contains(bf.ReferenceId) && bf.CompanyId == _loggedInUserService.CompanyId);

        return _loggedInUserService.IsAllInfra
            ? await businessFunction.ToListAsync()
            : GetAssignedBusinessFunctions(businessFunction);
    }

    public async Task<List<BusinessFunction>> GetBusinessFunctionNames()
    {
        var businessFunctions = base.QueryAll(bf => bf.CompanyId ==_loggedInUserService.CompanyId)
            .Select(x => new BusinessFunction { ReferenceId = x.ReferenceId, Name = x.Name });
        
        return _loggedInUserService.IsAllInfra
            ? await businessFunctions.ToListAsync()
            : GetAssignedBusinessFunctions(businessFunctions).ToList();
    }

    public override async Task<PaginatedResult<BusinessFunction>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<BusinessFunction> specification, string sortColumn, string sortOrder)
    {
        IQueryable<BusinessFunction> query;

        if (IsParent)
        {
            query = _loggedInUserService.IsAllInfra
                ? MapBusinessFunction(Entities.Specify(specification).DescOrderById())
                : GetPaginatedBusinessFunctions(MapBusinessFunction(Entities.Specify(specification)).DescOrderById());
        }
        else
        {
            query = _loggedInUserService.IsAllInfra
                ? MapBusinessFunction(Entities.Specify(specification)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId).DescOrderById())
                : GetPaginatedBusinessFunctions(MapBusinessFunction(Entities.Specify(specification)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId)).DescOrderById());
        }

        return await SelectBusinessFunction(query).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<BusinessFunction> GetPaginatedQuery()
    {
        var businessFunctions =base.QueryAll(x => x.CompanyId ==_loggedInUserService.CompanyId);

        var bsFunction = MapBusinessFunction(businessFunctions);

        return _loggedInUserService.IsAllInfra
            ? bsFunction.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedBusinessFunctions(bsFunction).AsNoTracking().OrderByDescending(x => x.Id);
    }   

    public async Task<List<BusinessFunction>> GetBusinessFunctionListByBusinessServiceId(string businessServiceId)
    {
        var businessFunctions = SelectBusinessFunction(_loggedInUserService.IsParent
           ? base.FilterBy(x => x.BusinessServiceId == businessServiceId)
           : base.FilterBy(x => x.CompanyId == _loggedInUserService.CompanyId && x.BusinessServiceId == businessServiceId));

        var bsFunction = MapBusinessFunction(businessFunctions);

        return _loggedInUserService.IsAllInfra
            ? await bsFunction.ToListAsync()
            : GetAssignedBusinessFunctions(bsFunction).ToList();
    }

    public async Task<List<BusinessFunction>> GetFilterByBusinessServiceId(string businessServiceId)
    {
        var businessFunctions = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId == businessServiceId)
                .Select(x => new BusinessFunction { Id = x.Id, ReferenceId = x.ReferenceId, Name = x.Name, BusinessServiceId = x.BusinessServiceId })
            : base.FilterBy(x => x.CompanyId == _loggedInUserService.CompanyId && x.BusinessServiceId == businessServiceId)
                .Select(x => new BusinessFunction { Id = x.Id, ReferenceId = x.ReferenceId, Name = x.Name, BusinessServiceId = x.BusinessServiceId });

        return _loggedInUserService.IsAllInfra
            ? await businessFunctions.ToListAsync()
            : GetAssignedBusinessFunctions(businessFunctions).ToList();
    }


    public async Task<List<BusinessFunction>> GetNamesByBusinessServiceId(string businessServiceId)
    {
        var businessFunctions = (_loggedInUserService.IsParent
           ? base.FilterBy(x => x.BusinessServiceId == businessServiceId)
           : base.FilterBy(x => x.CompanyId == _loggedInUserService.CompanyId && x.BusinessServiceId == businessServiceId))
           .Select(x => new BusinessFunction {ReferenceId=x.ReferenceId, Name=x.Name, });

        return _loggedInUserService.IsAllInfra
            ? await businessFunctions.ToListAsync()
            : GetAssignedBusinessFunctions(businessFunctions).ToList();
    }
    public async Task<List<BusinessFunction>> GetByBusinessServiceIds(List<string> businessServiceIds)
    {
        var businessFunctions = _loggedInUserService.IsParent
            ? base.FilterBy(x => businessServiceIds.Contains(x.BusinessServiceId))
                .Select(x => new BusinessFunction { Id = x.Id, ReferenceId = x.ReferenceId, Name = x.Name, BusinessServiceId = x.BusinessServiceId })
            : base.FilterBy(x => x.CompanyId == _loggedInUserService.CompanyId && businessServiceIds.Contains(x.BusinessServiceId))
                .Select(x => new BusinessFunction { Id = x.Id, ReferenceId = x.ReferenceId, Name = x.Name, BusinessServiceId = x.BusinessServiceId });

        return _loggedInUserService.IsAllInfra
            ? await businessFunctions.OrderBy(x => x.Id).ToListAsync()
            : GetAssignedBusinessFunctions(businessFunctions).OrderBy(x => x.Id).ToList();
    }

    public async Task<List<GetBusinessFunctionNameByBusinessServiceIdVm>> GetBusinessFunctionNameByBusinessServiceId(string businessServiceId)
    {
        var businessFunctions =_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId == businessServiceId)
            : base.FilterBy(x => x.CompanyId == _loggedInUserService.CompanyId && x.BusinessServiceId == businessServiceId);

        var bsFunction = MapBusinessFunction(businessFunctions)
            .Select(x => new GetBusinessFunctionNameByBusinessServiceIdVm
            {
                Id = x.ReferenceId,
                Name = x.Name,
                BusinessServiceId = x.BusinessServiceId
            });

        return await bsFunction.ToListAsync();
    }
    public async Task<bool> IsBusinessFunctionNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
        {
            return await _dbContext.BusinessFunctions.AnyAsync(x => x.Name == name);
        }

        var matchingFunctions = await _dbContext.BusinessFunctions.Where(x => x.Name == name).ToListAsync();
        return matchingFunctions.Unique(id);
    }

    public async Task<bool> IsBusinessFunctionNameUnique(string name)
    {
        return await _dbContext.BusinessFunctions.AnyAsync(x => x.Name == name);
    }
    private IQueryable<BusinessFunction> SelectBusinessFunction(IQueryable<BusinessFunction> query)
    {
        return query.Select(x => new BusinessFunction
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            Name = x.Name,
            Description = x.Description,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            CriticalityLevel = x.CriticalityLevel,
            ConfiguredMAO = x.ConfiguredMAO,
            ConfiguredRPO = x.ConfiguredRPO,
            ConfiguredRTO = x.ConfiguredRTO,
            RPOThreshold = x.RPOThreshold,
        });
    }
    private IQueryable<BusinessFunction> MapBusinessFunction(IQueryable<BusinessFunction> businessFunctions)
    {
        return businessFunctions.Select(x => new
        {
            Company = _dbContext.Companies.FirstOrDefault(c => c.ReferenceId == x.CompanyId),
            BusinessService = _dbContext.BusinessServices.FirstOrDefault(b => b.ReferenceId == x.BusinessServiceId),
            BusinessFunction = x
        })
       .Select(res => new BusinessFunction
       {
           Id = res.BusinessFunction.Id,
           ReferenceId = res.BusinessFunction.ReferenceId,
           CompanyId = res.Company.ReferenceId,
           Name = res.BusinessFunction.Name,
           Description = res.BusinessFunction.Description,
           BusinessServiceId = res.BusinessService.ReferenceId,
           BusinessServiceName = res.BusinessService.Name,
           CriticalityLevel = res.BusinessFunction.CriticalityLevel,
           ConfiguredRPO = res.BusinessFunction.ConfiguredRPO,
           ConfiguredRTO = res.BusinessFunction.ConfiguredRTO,
           ConfiguredMAO = res.BusinessFunction.ConfiguredMAO,
           RPOThreshold = res.BusinessFunction.RPOThreshold,
           IsActive = res.BusinessFunction.IsActive,
           CreatedBy = res.BusinessFunction.CreatedBy,
           CreatedDate = res.BusinessFunction.CreatedDate,
           LastModifiedBy = res.BusinessFunction.LastModifiedBy,
           LastModifiedDate = res.BusinessFunction.LastModifiedDate,
       });
    }
}