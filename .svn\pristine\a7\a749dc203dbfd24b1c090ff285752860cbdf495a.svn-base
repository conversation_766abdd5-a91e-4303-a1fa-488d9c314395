﻿using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class OperationalFunctionControllerShould
    {
        private readonly Mock<ILogger<OperationalFunctionController>> _mockLogger =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private  OperationalFunctionController _controller;

        public OperationalFunctionControllerShould()
        {
            
            _controller = new OperationalFunctionController(_mockLogger.Object, _mockDataProvider.Object, _mockPublisher.Object, _mockMapper.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult_WithBusinessFunctionModels()
        {
            // Arrange
            var businessServiceView = new List<BusinessServiceNameVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(businessServiceView);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            var model = Assert.IsType<BusinessFunctionModels>(result?.Model);
            Assert.Equal(businessServiceView, model.BusinessServiceNames);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesBusinessFunction_WhenIdIsEmpty()
        {
            // Arrange
            var businessFunctionModel = new AutoFixture.Fixture().Create<BusinessFunctionModels>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty ID for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateBusinessFunctionCommand { Name = "TestFunction" };
            _mockMapper.Setup(m => m.Map<CreateBusinessFunctionCommand>(businessFunctionModel))
                .Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.CreateAsync(createCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(businessFunctionModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            Assert.NotNull(resultValue);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesBusinessFunction_WhenIdIsNotEmpty()
        {
            // Arrange
            var businessFunctionModel = new AutoFixture.Fixture().Create<BusinessFunctionModels>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "123"); // Non-empty ID for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateBusinessFunctionCommand { Name = "TestFunction" };
            _mockMapper.Setup(m => m.Map<UpdateBusinessFunctionCommand>(businessFunctionModel))
                .Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.BusinessFunction.UpdateAsync(updateCommand))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(businessFunctionModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            var resultValue = result.Value;
            Assert.NotNull(resultValue);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.DeleteAsync(id))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_HandlesException()
        {
            // Arrange
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.DeleteAsync(id))
                .ThrowsAsync(new Exception("Delete failed"));

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsBusinessFunctionNameExist_ReturnsTrue()
        {
            // Arrange
            var businessFunctionName = "TestFunction";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.IsBusinessFunctionNameExist(businessFunctionName, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetPaginationList_ReturnsJsonResult_WithPaginatedList()
        {
            // Arrange
            var query = new GetBusinessFunctionPaginatedListQuery();
            var paginatedList = new PaginatedResult<BusinessFunctionListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(query))
                .ReturnsAsync(paginatedList);

            // Act
            var result = await _controller.GetPaginationList(query) as JsonResult;

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(paginatedList, jsonResult.Value);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new OperationalFunctionController(
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockPublisher.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }
        // ===== LIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task List_ShouldPublishBusinessFunctionPaginatedEvent()
        {
            // Arrange
            var businessServiceView = new List<BusinessServiceNameVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(businessServiceView);

            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<BusinessFunctionPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }


        [Fact]
        public async Task List_ShouldCallDataProvider()
        {
            // Arrange
            var businessServiceView = new List<BusinessServiceNameVm>();
            _mockDataProvider.Setup(dp => dp.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(businessServiceView);

            // Act
            await _controller.List();

            // Assert
            _mockDataProvider.Verify(dp => dp.BusinessService.GetBusinessServiceNames(), Times.Once);
        }

        // ===== CREATEORUPDATE METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleException()
        {
            // Arrange
            var businessFunctionModel = new AutoFixture.Fixture().Create<BusinessFunctionModels>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var exception = new Exception("Database connection failed");
            _mockMapper.Setup(m => m.Map<CreateBusinessFunctionCommand>(businessFunctionModel)).Throws(exception);

            // Act
            var result = await _controller.CreateOrUpdate(businessFunctionModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== DELETE METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task Delete_ShouldCallDataProvider()
        {
            // Arrange
            var id = "123";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.DeleteAsync(id))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            // Act
            await _controller.Delete(id);

            // Assert
            _mockDataProvider.Verify(dp => dp.BusinessFunction.DeleteAsync(id), Times.Once);
        }

        // ===== ISBUSINESSFUNCTIONNAMEEXIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var businessFunctionName = "NonExistentFunction";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.IsBusinessFunctionNameExist(businessFunctionName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_OnException()
        {
            // Arrange
            var businessFunctionName = "FunctionName";
            var id = "123";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.IsBusinessFunctionNameExist(businessFunctionName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsBusinessFunctionNameExist_ShouldCallDataProvider()
        {
            // Arrange
            var businessFunctionName = "FunctionName";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id))
                .ReturnsAsync(true);

            // Act
            await _controller.IsBusinessFunctionNameExist(businessFunctionName, id);

            // Assert
            _mockDataProvider.Verify(dp => dp.BusinessFunction.IsBusinessFunctionNameExist(businessFunctionName, id), Times.Once);
        }

        // ===== GETPAGINATIONLIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task GetPaginationList_ShouldHandleException()
        {
            // Arrange
            var query = new GetBusinessFunctionPaginatedListQuery();
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(query))
                .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPaginationList(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }
        
        [Fact]
        public async Task GetPaginationList_ShouldCallDataProvider()
        {
            // Arrange
            var query = new GetBusinessFunctionPaginatedListQuery();
            var paginatedList = new PaginatedResult<BusinessFunctionListVm>();
            _mockDataProvider.Setup(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(query))
                .ReturnsAsync(paginatedList);

            // Act
            await _controller.GetPaginationList(query);

            // Assert
            _mockDataProvider.Verify(dp => dp.BusinessFunction.GetBusinessFunctionPaginatedList(query), Times.Once);
        }
    }
}
