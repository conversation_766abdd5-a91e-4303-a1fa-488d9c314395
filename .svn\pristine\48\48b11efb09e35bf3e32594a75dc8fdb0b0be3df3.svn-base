﻿using ContinuityPatrol.Application.Features.Job.Events.UpdateJobState;

namespace ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;

public class UpdateJobStateCommandHandler : IRequestHandler<UpdateJobStateCommand, UpdateJobStateResponse>
{
    private readonly IJobRepository _jobRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateJobStateCommandHandler(IJobRepository jobRepository, IMapper mapper, IPublisher publisher)
    {
        _jobRepository = jobRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateJobStateResponse> Handle(UpdateJobStateCommand request, CancellationToken cancellationToken)
    {
        if (request.UpdateJobStates.Count == 0)
            throw new InvalidDataException("Update Monitoring Job States List Can't be empty.");

        foreach (var job in request.UpdateJobStates)
        {
            var eventToUpdate = await _jobRepository.GetByReferenceIdAsync(job.Id);

            if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Job), job.Id);

            eventToUpdate.State = job.State;

            _mapper.Map(job, eventToUpdate, typeof(UpdateJobState), typeof(Domain.Entities.Job));

            await _jobRepository.UpdateAsync(eventToUpdate);

            await _publisher.Publish(
                new JobStateUpdatedEvent { State = eventToUpdate.State, JobName = eventToUpdate.Name },
                cancellationToken);
        }

        var response = new UpdateJobStateResponse
        {
            Message = request.UpdateJobStates.Count == 1
                ? $"Monitoring Job state has been updated to '{request.UpdateJobStates[0].State}' state successfully"
                : $"Monitoring Jobs state has been updated to '{request.UpdateJobStates[0].State}' state successfully"
        };
        return response;
    }
}