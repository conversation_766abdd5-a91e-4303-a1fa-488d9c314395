﻿namespace ContinuityPatrol.Application.Constants;

public static class LicenseAuthentication
{
    public static string BaseLicenseNotAvailable = "Base License Not Available.";

    public static string BaseLicenseKeyExist = "The same license key already exist.";

    public static string BaseLicenseExpired = "License Expired.";

    public static string DerivedLicenseExpiryDateMismatchWithBaseLicense =
        "Derived License Expiry Date Mismatch With Base License.";

    public static string UserIsNotParent = "Access Denied";

    public static string DerivedLicenseDelete =
        "Derived license delete is restricted, SiteAdmin and SuperAdmin users only.";

    public static string BaseLicenseKeyCompanyIdMismatch = "Parent CompanyId Mismatch with Base License Key CompanyId.";

    public static string BaseLicenseKeyUsableTimeExpired =
        "Base License Usable Time Expired, Please Contact Our License Team.";

    public static string BaseLicenseKeyMACAddressMismatch =
        "Base License MACAddress and CP Installed System MACAddress Mismatch.";

    public static string BaseLicenseKeyHostNameMismatch =
        "Base License Host Name and CP Installed System Host Name MisMatch.";

    public static string BaseLicenseKeyIPAddressMismatch =
        "Base LicenseKey IP Address and CP Installed System IP Address MisMatch.";

    public static string BaseLicenseKeyPONumberExist = "The same PoNumber already exist.";

    public static string DerivedLicenseKeyPONumberExist = "The same PoNumber already exist in derived license.";

    public static string DerivedLicenseSameCompanyIdAlreadyExist = "License for this Derived Company already exist.";

    public static string DerivedLicenseKeyCompanyIdMismatch = "CP CompanyId Mismatch with Base LicenseKey CompanyId.";

    public static string DerivedLicenseKeyUsableTimeExpired =
        "Derived License Usable Time Expired, Please Contact Our License Team";

    public static string DerivedLicenseKeyMACAddressMismatch =
        "Derived License MACAddress and CP Installed System MACAddress Mismatch.";

    public static string DerivedLicenseKeyHostNameMismatch =
        "Derived License Host Name and CP Installed System Host Name Mismatch.";

    public static string DerivedLicenseKeyIPAddressMismatch =
        "Derived LicenseKey IP Address and CP Installed System IP Address Mismatch.";

    public static string MismatchLicenseDetail = "Mismatch License Detail";

    public static string InvalidLicenseDetail = "Invalid License Detail";

    public static string LicenseAlreadyUsed = "This License Key already used,Please Enter the new license key.";
}

public static class Authentication
{
    public static string UserAccountLocked = "User '{0}' account locked.";

    public static string InvalidLogin = "Invalid Login Credentials.";

    public static string InvalidOldPassword = "Old password is not matched.";

    public static string PasswordUnique = "A same password already exists.";

    public static string InvalidConfirmPassword = "Password and Confirm password does not matched.";

    public static string InvalidAccessKey = "User '{0}' password is not match.";

    public static string LoginExceedMaximumAttempt = "Account '{0}' is locked on multiple invalid login attempts.";

    public static string LoginAttemptUpdated = "User '{0}' invalid Login attempts {1} has been successfully updated.";

    public static string LoginAttemptUpdateError =
        "Update invalid Login attempts failed: User '{0}' invalid Login attempts {1}.";

    public static string InvalidAccess = "Custom role Permission is an invalid format or empty.";

    public static string InvalidDomainAuthentication = "Authentication Failed for User '{0}' with domain '{1}'.";

    public static string InvalidADLogin = "The user name or password is incorrect.";

    public static string InvalidUserRole = "Invalid Login User Role.";

    public static string InvalidLoginAttempt =
        "Invalid login credentials. Only '{0}' attempts left to account '{1}' is lock.";
}