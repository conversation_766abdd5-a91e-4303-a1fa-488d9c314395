﻿let types = [], dataTable, selectedValues = [];
const ExecutionHistorydebounce = (func, timeout = 300) => {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => func.apply(this, args), timeout);
    };
};
const formatData = data => data == null ? "NA" : data;
const getDateVal = id => $(`#${id}`).find("input[type=date]").val() || '';
const validateDates = () => {
    const startDate = getDateVal("wfScheduleStartDate");
    const endDate = getDateVal("wfScheduleEndDate");
    let errorElement = $("#wfSchedulestartdateError");
    if (!startDate && endDate) {
        errorElement.text("Select the start date before end date").addClass('field-validation-error');
        return false;
    } else if (endDate && startDate > endDate) {
        errorElement.text("Start date lesser than end date").addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');
    return true;
};
const renderSpan = data => `<td><span title="${formatData(data)}">${formatData(data)}</span></td>`;

$(function () {
    dataTable = $('#WorkflowScheduleExecutionHistorytabledata').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            },
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: true,
        filter: true,
        order: [],
        ajax: {
            type: "GET",
            url: "/ITAutomation/WorkflowScheduleExecutionHistory/GetPagination",
            dataType: "json",
            data: d => {
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length ? selectedValues.join(';') : $('#wfScheduleSearch').val();
                d.workflowType = $('#typeValue').val() === "All" ? '' : $('#typeValue').val();
                d.start = getDateVal("wfScheduleStartDate");
                d.end = getDateVal("wfScheduleEndDate");
                selectedValues.length = 0;
            },
            dataSrc: json => {
                if (!json.success) return errorNotification(json.data.data);

                const { data } = json.data;
                json.recordsTotal = json.data.totalPages;
                json.recordsFiltered = json.data.totalCount;
                $(".pagination-column").toggleClass("disabled", data.length === 0);

                data.forEach(({ workflowType }) => types.push(workflowType));
                [...new Set(types)].forEach(value => {
                    if (!$(`#typeValue option[value="${value}"]`).length) {
                        $('#typeValue').append(`<option value="${value}">${value}</option>`);
                    }
                });
                return data;
            }
        },
        columnDefs: [{ targets: [0, 1, 2, 3, 4, 5], className: "truncate" }],
        columns: [
            {
                data: null, name: "Sr. No.", autoWidth: true, orderable: false,
                render: (data, type, row, meta) =>
                    type === 'display'
                        ? meta.settings._iDisplayStart + meta.row + 1
                        : data
            },
            { data: "beforeSwitchOverWorkflowName", name: "Workflow Name", autoWidth: true, render: renderSpan },
            { data: "nodeName", name: "Node Name", autoWidth: true, render: renderSpan },
            { data: "workflowType", name: "Type", autoWidth: true, render: data => `<td>${formatData(data)}</td>` },
            {
                data: 'createdDate', name: "Start Time", autoWidth: true,
                render: data => `<td>${formatData(data)?.replace('T', ' ').split('.')[0] ?? 'NA'}</td>`
            },
            { data: "lastExecutionTime", name: "End Time", autoWidth: true, render: data => `<td>${formatData(data)}</td>` },
            {
                data: "status", name: "Status", autoWidth: true,
                render: data => {
                    let iconClass = {
                        "Pending": "cp-pending text-warning me-1",
                        "Running": "text-success cp-reload cp-animate me-1",
                        "Success": "cp-success text-success me-1"
                    }[data] || "cp-error text-danger me-1";
                    return `<td><i class="${iconClass}" id="icon" title="${data || "NA"}" ></i></td>
                            <td><span id="jobmanagestate">${data || "NA"}</span></td>`;
                }
            },
            {
                data: "exceptionMessage", name: "Action", autoWidth: true,
                render: (data, type, row) => {
                    const isError = row.status?.toLowerCase() === 'error';
                    return `<div class="d-flex align-items-center gap-2">
                        <span title="Report" class="buttonHistoryReport" id="${row.beforeSwitchOverWorkflowId}" data-type="${row.beforeSwitchOverWorkflowName}" data-infraReferenceId="${row.id}">
                            <i class="cp-report me-2"></i>
                        </span>
                        <span title="Error Message" errorMessage="${btoa(isError ? data : '')}" class="wfScheduleError ${isError ? '' : 'd-none'}" role="button" data-bs-toggle="modal" data-bs-target="#ErrorModal">
                            <i class="cp-fail-back blink text-danger"></i>
                        </span>
                    </div>`;
                }
            },
        ],
        rowCallback: (row, data, index) => {
            const startIndex = dataTable.context[0]._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete: () => {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });

    dataTable.on('draw.dt', () => {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#wfScheduleSearch').on('keydown input', ExecutionHistorydebounce(e => {
        if (['=', 'Enter'].includes(e.key)) return e.preventDefault();
        selectedValues.push($('#wfScheduleSearch').val());
        if (!isNaN(dataTable.page.info().page + 1)) {
            dataTable.ajax.reload(json => {
                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false);
        }
    }, 500));
});

$("#wfScheduleStartDate,#wfScheduleEndDate").on("keypress", e => e.preventDefault());
$("#typeValue,#wfScheduleStartDate,#wfScheduleEndDate").on('change', () => {
    dataTable.ajax.reload();
    validateDates();
});

$('#WorkflowScheduleExecutionHistorytabledata').on('click', '.wfScheduleError', function () {
    const msg = atob($(this).attr('errorMessage'));
    const noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">';
    $("#error_message").css('text-align', msg && msg !== 'null' ? 'start' : 'center')
        .html(msg && msg !== 'null' ? msg : noData);
});

$(document).on('click', ".buttonHistoryReport", async function () {
    const url = `/ITAutomation/WorkflowScheduleExecutionHistory/DownloadReport?workflowId=${$(this).attr("id")}&workflowName=${$(this).attr("data-type")}&InfraReferenceId=${$(this).attr("data-infraReferenceId")}`;
    let alertClass, reportIconClass, message;
    try {
        const response = await fetch(url);
        if (response.ok) {
            const blob = await response.blob();
            if (blob.size > 0 && blob.type === "application/pdf") {
                const ts = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
                downloadFile(blob, `ScheduledJobWorkflowReport_${$(this).attr("data-type")}_${ts}.pdf`, "application/pdf");
                alertClass = "success-toast";
                reportIconClass = "cp-check toast_icon";
                message = "Scheduled Job Workflow Report downloaded successfully";
            } else {
                alertClass = "warning-toast";
                reportIconClass = "cp-exclamation toast_icon";
                message = "No Data Found";
            }
        } else {
            throw new Error();
        }
    } catch {
        alertClass = "warning-toast";
        reportIconClass = "cp-exclamation toast_icon";
        message = "Scheduled Job Workflow Report Download Error";
    }
    $('#alertClass').removeClass().addClass(alertClass);
    $('#icon_Detail').removeClass().addClass(reportIconClass);
    $('#notificationAlertmessage').text(message);
    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
});
function downloadFile(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error("Error downloading file: " + error.message);
    }
}