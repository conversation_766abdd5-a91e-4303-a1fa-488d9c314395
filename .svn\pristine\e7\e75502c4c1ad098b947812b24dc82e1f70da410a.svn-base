﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertInformationRepository : BaseRepository<AlertInformation>, IAlertInformationRepository
{
    private readonly ApplicationDbContext _dbContext;

    public AlertInformationRepository(ApplicationDbContext dbContext) :
        base(dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<List<AlertInformation>> GetAlertInformationByCode(string code)
    {
        return await _dbContext.AlertInformations.AsNoTracking().Where(x => x.IsActive && x.Code == code).ToListAsync();
    }
}