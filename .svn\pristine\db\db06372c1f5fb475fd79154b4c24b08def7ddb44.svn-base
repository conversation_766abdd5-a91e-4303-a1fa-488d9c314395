using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PageSolutionMappingRepositoryTests : IClassFixture<PageSolutionMappingFixture>
{
    private readonly PageSolutionMappingFixture _pageSolutionMappingFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PageSolutionMappingRepository _repository;

    public PageSolutionMappingRepositoryTests(PageSolutionMappingFixture pageSolutionMappingFixture)
    {
        _pageSolutionMappingFixture = pageSolutionMappingFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        var mockLoggedInUserService = LoggedInUserServiceRepositoryMocks.GetLoggedInUserServiceRepository();
        
        _repository = new PageSolutionMappingRepository(_dbContext, mockLoggedInUserService.Object);
    }

    private async Task ClearDatabase()
    {
        _dbContext.PageSolutionMappings.RemoveRange(_dbContext.PageSolutionMappings);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var pageSolutionMapping = _pageSolutionMappingFixture.PageSolutionMappingDto;

        // Act
        var result = await _repository.AddAsync(pageSolutionMapping);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(pageSolutionMapping.Name, result.Name);
        Assert.Equal(pageSolutionMapping.PageBuilderId, result.PageBuilderId);
        Assert.Single(_dbContext.PageSolutionMappings);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region GetByReplicationCategoryTypeId Tests

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldReturnMapping_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var replicationCategoryTypeId = "TEST_REPLICATION_CATEGORY_123";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificReplicationCategoryTypeId(replicationCategoryTypeId);
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.GetByReplicationCategoryTypeId(replicationCategoryTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationCategoryTypeId, result.ReplicationCategoryTypeId);
        Assert.Equal(mapping.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = "NON_EXISTENT_REPLICATION_CATEGORY";

        // Act
        var result = await _repository.GetByReplicationCategoryTypeId(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldReturnOnlyActiveMapping()
    {
        // Arrange
        await ClearDatabase();
        var replicationCategoryTypeId = "TEST_REPLICATION_CATEGORY_123";
        var activeMapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(
            replicationCategoryTypeId: replicationCategoryTypeId, 
            isActive: true);
        var inactiveMapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(
            replicationCategoryTypeId: replicationCategoryTypeId, 
            isActive: false);

        await _repository.AddAsync(activeMapping);
        await _repository.AddAsync(inactiveMapping);

        // Act
        var result = await _repository.GetByReplicationCategoryTypeId(replicationCategoryTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal(activeMapping.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldReturnFirstMatch_WhenMultipleActiveExist()
    {
        // Arrange
        await ClearDatabase();
        var replicationCategoryTypeId = "TEST_REPLICATION_CATEGORY_123";
        var mappings = _pageSolutionMappingFixture.CreatePageSolutionMappingsWithSameReplicationCategoryTypeId(replicationCategoryTypeId, 3);
        
        await _repository.AddRangeAsync(mappings);

        // Act
        var result = await _repository.GetByReplicationCategoryTypeId(replicationCategoryTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationCategoryTypeId, result.ReplicationCategoryTypeId);
        Assert.True(result.IsActive);
    }

    #endregion

    #region GetByReplicationTypeId Tests

    [Fact]
    public async Task GetByReplicationTypeId_ShouldReturnMapping_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var replicationTypeId = "TEST_REPLICATION_TYPE_123";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificReplicationTypeId(replicationTypeId);
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.GetByReplicationTypeId(replicationTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationTypeId, result.ReplicationTypeId);
        Assert.Equal(mapping.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReplicationTypeId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = "NON_EXISTENT_REPLICATION_TYPE";

        // Act
        var result = await _repository.GetByReplicationTypeId(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReplicationTypeId_ShouldReturnOnlyActiveMapping()
    {
        // Arrange
        await ClearDatabase();
        var replicationTypeId = "TEST_REPLICATION_TYPE_123";
        var activeMapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(
            replicationTypeId: replicationTypeId, 
            isActive: true);
        var inactiveMapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(
            replicationTypeId: replicationTypeId, 
            isActive: false);

        await _repository.AddAsync(activeMapping);
        await _repository.AddAsync(inactiveMapping);

        // Act
        var result = await _repository.GetByReplicationTypeId(replicationTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal(activeMapping.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByReplicationTypeId_ShouldReturnFirstMatch_WhenMultipleActiveExist()
    {
        // Arrange
        await ClearDatabase();
        var replicationTypeId = "TEST_REPLICATION_TYPE_123";
        var mappings = _pageSolutionMappingFixture.CreatePageSolutionMappingsWithSameReplicationTypeId(replicationTypeId, 3);
        
        await _repository.AddRangeAsync(mappings);

        // Act
        var result = await _repository.GetByReplicationTypeId(replicationTypeId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replicationTypeId, result.ReplicationTypeId);
        Assert.True(result.IsActive);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForNewEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageSolutionMapping";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificName(existingName);
        await _repository.AddAsync(mapping);

        // Act - Check for new entity (empty id)
        var result = await _repository.IsNameExist(existingName, string.Empty);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentName = "NonExistentPageSolutionMapping";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, string.Empty);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageSolutionMapping";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificName(existingName);
        await _repository.AddAsync(mapping);

        // Act - Check for same entity (using its own id)
        var result = await _repository.IsNameExist(existingName, mapping.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForDifferentEntity()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageSolutionMapping";
        var mapping1 = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificName(existingName);
        var mapping2 = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificName("DifferentName");
        await _repository.AddAsync(mapping1);
        await _repository.AddAsync(mapping2);

        // Act - Check if name exists for different entity
        var result = await _repository.IsNameExist(existingName, mapping2.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleInvalidGuid()
    {
        // Arrange
        await ClearDatabase();
        var existingName = "ExistingPageSolutionMapping";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificName(existingName);
        await _repository.AddAsync(mapping);

        // Act - Check with invalid GUID (should treat as new entity)
        var result = await _repository.IsNameExist(existingName, "invalid-guid");

        // Assert
        Assert.True(result);
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntitiesWithFilteredFields()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>
        {
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping1"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping2"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping3")
        };
        await _repository.AddRangeAsync(mappings);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        
        // Verify that only filtered fields are populated (as per the FilterRequiredField implementation)
        foreach (var mapping in result)
        {
            Assert.NotNull(mapping.Id);
            Assert.NotNull(mapping.ReferenceId);
            Assert.NotNull(mapping.Name);
            Assert.NotNull(mapping.PageBuilderId);
            Assert.NotNull(mapping.PageBuilderName);
            Assert.NotNull(mapping.MonitorType);
            Assert.NotNull(mapping.Type);
            Assert.NotNull(mapping.TypeName);
            Assert.NotNull(mapping.SubTypeId);
            Assert.NotNull(mapping.SubType);
            Assert.NotNull(mapping.ReplicationTypeId);
            Assert.NotNull(mapping.ReplicationTypeName);
            Assert.NotNull(mapping.ReplicationCategoryType);
            Assert.NotNull(mapping.ReplicationCategoryTypeId);
        }
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmpty_WhenNoEntities()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEntitiesInDescendingOrder()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>
        {
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping1"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping2"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping3")
        };
        
        // Add them one by one to ensure different creation times
        foreach (var mapping in mappings)
        {
            await _repository.AddAsync(mapping);
            await Task.Delay(10); // Small delay to ensure different timestamps
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        // Should be ordered by Id descending (DescOrderById)
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>();
        for (int i = 0; i < 25; i++)
        {
            mappings.Add(_pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: $"Mapping_{i:D2}"));
        }
        await _repository.AddRangeAsync(mappings);

        var specification = new PageSolutionMappingFilterSpecification(string.Empty);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(25, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(3, result.TotalPages); // 25 items / 10 per page = 3 pages
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResult_WhenNoData()
    {
        // Arrange
        await ClearDatabase();
        var specification = new PageSolutionMappingFilterSpecification(string.Empty);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(0, result.TotalPages);
    }

    #endregion

    #region AddRangeAsync Tests

    [Fact]
    public async Task AddRangeAsync_ShouldAddMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>
        {
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties()
        };

        // Act
        await _repository.AddRangeAsync(mappings);

        // Assert
        var allMappings = await _repository.ListAllAsync();
        Assert.Equal(3, allMappings.Count);
    }

    [Fact]
    public async Task AddRangeAsync_ShouldThrow_WhenListIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        await ClearDatabase();
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties();
        await _repository.AddAsync(mapping);

        mapping.Name = "UpdatedPageSolutionMapping";
        mapping.MonitorType = "UpdatedMonitorType";
        mapping.Type = 999;

        // Act
        var result = await _repository.UpdateAsync(mapping);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedPageSolutionMapping", result.Name);
        Assert.Equal("UpdatedMonitorType", result.MonitorType);
        Assert.Equal(999, result.Type);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldDeleteEntity()
    {
        // Arrange
        await ClearDatabase();
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties();
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.DeleteAsync(mapping);

        // Assert
        Assert.NotNull(result);
        var allMappings = await _repository.ListAllAsync();
        Assert.Empty(allMappings);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties();
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.GetByReferenceIdAsync(mapping.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(mapping.ReferenceId, result.ReferenceId);
        Assert.Equal(mapping.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act - Perform concurrent add operations
        for (int i = 0; i < 10; i++)
        {
            var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: $"ConcurrentMapping_{i}");
            tasks.Add(_repository.AddAsync(mapping));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allMappings = await _repository.ListAllAsync();
        Assert.Equal(10, allMappings.Count);
    }

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialId = "<EMAIL>";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificReplicationCategoryTypeId(specialId);
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.GetByReplicationCategoryTypeId(specialId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(specialId, result.ReplicationCategoryTypeId);
    }

    [Fact]
    public async Task GetByReplicationTypeId_ShouldHandleSpecialCharacters()
    {
        // Arrange
        await ClearDatabase();
        var specialId = "<EMAIL>";
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithSpecificReplicationTypeId(specialId);
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.GetByReplicationTypeId(specialId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(specialId, result.ReplicationTypeId);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_WhenUpdatingMultipleEntities()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>
        {
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping1"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping2"),
            _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: "Mapping3")
        };
        await _repository.AddRangeAsync(mappings);

        // Act - Update all entities
        foreach (var mapping in mappings)
        {
            mapping.MonitorType = "UpdatedMonitorType";
            mapping.Type = 999;
            await _repository.UpdateAsync(mapping);
        }

        // Assert
        var allMappings = await _repository.ListAllAsync();
        Assert.Equal(3, allMappings.Count);
        Assert.All(allMappings, m => Assert.Equal("UpdatedMonitorType", m.MonitorType));
        Assert.All(allMappings, m => Assert.Equal(999, m.Type));
    }

    [Fact]
    public async Task Repository_ShouldHandleLargeDataSet()
    {
        // Arrange
        await ClearDatabase();
        var mappings = new List<PageSolutionMapping>();

        // Create 100 mappings
        for (int i = 0; i < 100; i++)
        {
            mappings.Add(_pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties(name: $"Mapping_{i:D3}"));
        }

        await _repository.AddRangeAsync(mappings);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
    }

    #endregion

    #region Edge Cases Tests

    [Fact]
    public async Task Repository_ShouldHandleEntityWithNullProperties()
    {
        // Arrange
        await ClearDatabase();
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties();
        mapping.PageBuilderName = null;
        mapping.MonitorType = null;
        mapping.TypeName = null;
        mapping.SubType = null;
        mapping.ReplicationTypeName = null;
        mapping.ReplicationCategoryType = null;

        // Act & Assert - Should not throw
        var result = await _repository.AddAsync(mapping);
        Assert.NotNull(result);
        Assert.Null(result.PageBuilderName);
        Assert.Null(result.MonitorType);
        Assert.Null(result.TypeName);
        Assert.Null(result.SubType);
        Assert.Null(result.ReplicationTypeName);
        Assert.Null(result.ReplicationCategoryType);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleEmptyAndNullNames()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.IsNameExist(string.Empty, string.Empty);
        var resultNull = await _repository.IsNameExist(null, string.Empty);

        Assert.IsType<bool>(resultEmpty);
        Assert.IsType<bool>(resultNull);
    }

    [Fact]
    public async Task GetByReplicationCategoryTypeId_ShouldHandleNullAndEmptyIds()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.GetByReplicationCategoryTypeId(string.Empty);
        var resultNull = await _repository.GetByReplicationCategoryTypeId(null);

        Assert.Null(resultEmpty);
        Assert.Null(resultNull);
    }

    [Fact]
    public async Task GetByReplicationTypeId_ShouldHandleNullAndEmptyIds()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert - Should not throw
        var resultEmpty = await _repository.GetByReplicationTypeId(string.Empty);
        var resultNull = await _repository.GetByReplicationTypeId(null);

        Assert.Null(resultEmpty);
        Assert.Null(resultNull);
    }

    [Fact]
    public async Task FilterRequiredField_ShouldOnlyReturnSpecifiedFields()
    {
        // Arrange
        await ClearDatabase();
        var mapping = _pageSolutionMappingFixture.CreatePageSolutionMappingWithProperties();
        await _repository.AddAsync(mapping);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);

        var filteredMapping = result.First();
        // Verify that the FilterRequiredField method only returns the specified fields
        // The method creates a new PageSolutionMapping with only specific properties
        Assert.NotNull(filteredMapping.Id);
        Assert.NotNull(filteredMapping.ReferenceId);
        Assert.NotNull(filteredMapping.Name);
        Assert.NotNull(filteredMapping.PageBuilderId);
        Assert.NotNull(filteredMapping.PageBuilderName);
        Assert.NotNull(filteredMapping.MonitorType);
        Assert.NotNull(filteredMapping.Type);
        Assert.NotNull(filteredMapping.TypeName);
        Assert.NotNull(filteredMapping.SubTypeId);
        Assert.NotNull(filteredMapping.SubType);
        Assert.NotNull(filteredMapping.ReplicationTypeId);
        Assert.NotNull(filteredMapping.ReplicationTypeName);
        Assert.NotNull(filteredMapping.ReplicationCategoryType);
        Assert.NotNull(filteredMapping.ReplicationCategoryTypeId);
    }

    #endregion
}
