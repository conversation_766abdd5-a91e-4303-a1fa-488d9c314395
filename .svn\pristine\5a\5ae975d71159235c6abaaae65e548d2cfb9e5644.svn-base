﻿@model ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel.DynamicDashboardWidgetViewModel;

@Html.AntiForgeryToken()
<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        
        <form  asp-route-id="textDeleteId" id="commonFormDelete" method="post" enctype="multipart/form-data">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-semibold">Are you sure?</h5>
                <p>You want to delete <span class="font-weight-bolder text-primary" id="deleteData"></span>?</p>
                <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
            </div>
        </form>
    </div>
</div>  