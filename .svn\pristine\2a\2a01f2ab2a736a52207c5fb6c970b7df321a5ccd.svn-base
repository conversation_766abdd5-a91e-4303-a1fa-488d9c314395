﻿namespace ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Update;

public class UpdateSQLDBMirroringStatusCommand : IRequest<UpdateSQLDBMirroringStatusResponse>
{
    public string Id { get; set; }
    public string Type { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ConfiguredRPO { get; set; }
    public string DataLagValue { get; set; }
    public string Properties { get; set; }
}