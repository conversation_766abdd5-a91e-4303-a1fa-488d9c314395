﻿let globalpath = [];
let logDownload = $('#logDownload');

$(document).ready(function () {

    $.ajax({
        url: '/Configuration/ServerLogHistory/GetServerLogList',
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (response) {
            if (response.success != false) {
                var data = response.data;
                var selectElement = document.getElementById("serverDropdown");
                const primaryServer = $("#serverDropdown option[value='primaryServer']");
                for (var i = 0; i < data.length; i++) {
                    var option = document.createElement("option");
                    option.value = data[i].id;
                    option.textContent = data[i].name;
                    selectElement.appendChild(option);
                }
            }
        },
        error: function () {

        }
    });

    // Initialize the server dropdown
    if ($('#serverDropdown').length) {
        setTimeout(function () {
            $('#serverDropdown').val('primaryServer').trigger('change');
        }, 500); // Delay to ensure the dropdown is initialized
    }

    $(document).on('click', '.folder > .folder-name', function (e) {
        e.preventDefault(); // Prevent default action (just in case)
        e.stopPropagation(); // Stop event bubbling to prevent conflicts
        logDownload.prop('disabled', true);
        var folderElement = $(this).parent();
        var folderPath = folderElement.data('path');
        var folderContents = folderElement.children('.folder-contents');

        folderElement.toggleClass('expanded');  // Toggle the 'expanded' class for the folder

        if (folderContents.is(':visible')) {
            // Folder is currently open, so hide it
            folderContents.hide();
            updateToggleIcon(folderElement, 'close');
        } else {
            // Folder is closed, so show its contents
            if (folderContents.is(':empty')) {
                loadFolderContentsRecursive(folderPath, folderContents, folderElement.data('level') + 1);
            }
            folderContents.show();
            updateToggleIcon(folderElement, 'open');
        }
    });

    function showErrorImage() {
        $('#logDirectoryTitle').text('Log Directory - Host: Connection Failed');
        //$('#logContent').html('<img src="/img/isomatric/ServerLogTestConnection.svg" alt="Error" width="600" height="350">');
        $('#logContent').addClass("d-none");
        $("#connectionError").removeClass("d-none");
        $("#connectionError").addClass("d-flex");
        $('#folderTree').empty(); // 🔹 Ensure folder tree is cleared
    }
    function hideErrorImage() {
        $('#logDirectoryTitle').text('');
        $('#logContent').empty();
        $('#logContent').removeClass("d-none");
        $("#connectionError").removeClass("d-flex");
        $("#connectionError").addClass("d-none");
        $('#folderTree').empty(); // 🔹 Ensure folder tree is cleared
    }

    $(document).on('click', '.file > span', function (e) {
        e.stopPropagation(); // Prevent parent click events
        logDownload.prop('disabled', false);

        var filePath = $(this).parent().data('path'); // Get file path from parent <li>
        if (filePath != '' && filePath != null && filePath != "undefined") {
        globalpath = filePath;
        $('#logContent').html('');
        $.ajax({
            url: '/Configuration/ServerLogHistory/GetLogFileContents',
            method: 'GET',
            data: { fileName: filePath },
            success: function (content) {
                $('#logContent').html(content);
                if (content.includes('The file is currently empty.')) {
                    logDownload.prop('disabled', true);
                }
            },
            error: function () {
                $('#logContent').html('Failed to load file content.');
                logDownload.prop('disabled', true);
            }
        });

        }
        else {
            $('#logContent').html('Invalid file path or file not found.');
            logDownload.prop('disabled', true);
        }
        // Highlight the selected file
        $('.file > span').removeClass('selected-file'); // Remove highlight from all
        $(this).addClass('selected-file'); // Highlight only clicked file
    });

    $('#logDownload').click(function () {
        logDownload.prop('disabled', true);
        downloadFile(globalpath);
        logDownload.prop('disabled', false);
    });
    function loadFolderContentsRecursive(path, container, level) {
        container.empty();
        $.getJSON('/Configuration/ServerLogHistory/GetFolderContents', { path: path || '' }, function (data) {
            if (data.error) {
                logDownload.prop('disabled', true);
                $('#logDirectoryTitle').text(`Log Directory - Host: ${data.error}`);
                var logContent = $('#logContent');
                logContent.html(''); // Clear previous content
                logContent.html('<img src="/img/isomatric/500_Error.svg" alt="Error" width="800" height="500">');
                return;
            }

            var ul = $('<ul></ul>');  // Start the unordered list for the current level

            data.forEach(function (item) {
                if (item.type === 'folder') {
                    var li = $(`
                <li class="folder" data-path="${item.path}" data-level="${level}">
                    <span role="button" class="folder-toggle">
                        <i class="js-toggle-icon"><span class="cp-circle-plus"></span></i> <i class="cp-folder-close"></i> ${item.name}
                    </span>
                    <ul class="folder-contents" style="display: none;"></ul> <!-- Hidden initially -->
                </li>
            `);

                    // Recursively load folder contents when the folder is clicked working good but auto expand
                    li.find('.folder-toggle').click(function () {
                        logDownload.prop('disabled', true);
                        var subContainer = li.find('.folder-contents');
                        var $thisIcon = $(this).find('.js-toggle-icon span');
                        var $folderIcon = $(this).find('.cp-folder-close, .cp-folder-open'); // Target the folder icon

                        if ($thisIcon.hasClass('cp-circle-plus')) {
                            // Expand the folder
                            $thisIcon.removeClass('cp-circle-plus').addClass('cp-circle-minus');
                            $folderIcon.removeClass('cp-folder-close').addClass('cp-folder-open'); // Change folder icon

                            // Only load folder contents if it's not already loaded
                            if (subContainer.is(':empty') && !subContainer.hasClass('loaded')) {
                                loadFolderContentsRecursive(item.path, subContainer, level + 1);
                                subContainer.addClass('loaded');
                            }

                            subContainer.stop(true, true).slideDown('fast');

                            // Collapse other open folders (siblings)
                            li.siblings('.folder').each(function () {
                                var siblingSubContainer = $(this).find('.folder-contents');
                                var siblingIcon = $(this).find('.js-toggle-icon span');
                                var siblingFolderIcon = $(this).find('.cp-folder-open');

                                siblingIcon.removeClass('cp-circle-minus').addClass('cp-circle-plus');
                                siblingFolderIcon.removeClass('cp-folder-open').addClass('cp-folder-close'); // Reset icon
                                siblingSubContainer.stop(true, true).slideUp('fast').removeClass('loaded').empty();
                            });

                        } else if ($thisIcon.hasClass('cp-circle-minus')) {
                            // Collapse the folder
                            $thisIcon.removeClass('cp-circle-minus').addClass('cp-circle-plus');
                            $folderIcon.removeClass('cp-folder-open').addClass('cp-folder-close'); // Change folder icon back
                            subContainer.stop(true, true).slideUp('fast');

                            // Recursively clear all nested subfolders and files
                            subContainer.find('.folder-contents').each(function () {
                                $(this).stop(true, true).slideUp('fast').removeClass('loaded').empty(); // Clear contents
                            });

                            // Reset nested folder states
                            subContainer.find('.folder').each(function () {
                                $(this).find('.js-toggle-icon span').removeClass('cp-circle-minus').addClass('cp-circle-plus');
                                $(this).find('.cp-folder-open').removeClass('cp-folder-open').addClass('cp-folder-close'); // Reset icon
                            });
                        }
                    });

                    ul.append(li);
                } else if (item.type === 'file') {
                    var li = $(`
                <li class="file" data-path="${item.path}">
                    <span role="button"><i class="cp-doc-file me-1"></i> ${item.name}</span>
                </li>
            `);

                    // Click event to change color of selected file to blue
                    li.find('span').click(function () {
                        // Reset the color of any previously selected file
                        container.find('.file span').removeClass('selected-file');

                        // Add the 'selected-file' class to the clicked file to turn it blue
                        $(this).addClass('selected-file');
                    });

                    ul.append(li);
                }
            });

            container.append(ul);  // Append the generated list to the container
        });
    }

    function downloadFile(filePath) {
        $.ajax({
            url: '/Configuration/ServerLogHistory/Download',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(filePath),
            xhrFields: {
                responseType: 'blob'
            },
            success: function (data, status, xhr) {
                const disposition = xhr.getResponseHeader('Content-Disposition');
                const fileName = disposition && disposition.split('filename=')[1]?.split(';')[0]?.replace(/"/g, '') || 'download.txt';

                const url = window.URL.createObjectURL(data);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            },
            error: function () {
                alert('Error downloading file.');
            }
        });
    }

    $('#serverDropdown').change(function () {
        hideErrorImage();
        var selectedServer = $(this).val();
        logDownload.prop('disabled', true);
        $('#serverDropdown').prop('disabled', true);
        if (selectedServer) {
            $('#logDirectoryTitle').text('');
            $('#folderTree').empty(); // 🔹 Clear the folder tree immediately when switching servers

            $.ajax({
                url: "/Configuration/ServerLogHistory/GetSharedFolderPath",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify(selectedServer),
                success: function (response) {
                    if (response.success === true) {
                        $('#logContent').html('');
                        $('#logDirectoryTitle').text(`Log Directory - Host: ${response.data}`);
                        loadFolderContentsRecursive('', $('#folderTree'), 0);
                        $('#serverDropdown').prop('disabled', false);
                    } else {
                        showErrorNotification(`Cannot access remote server: ${response.data}`);
                        logDownload.prop('disabled', true);
                        showErrorImage();
                        $('#folderTree').empty(); // 🔹 Clear folder tree if the server is not accessible
                        $('#serverDropdown').prop('disabled', false);
                    }
                },
                error: function (xhr, status, error) {
                    showErrorNotification(`Error connecting to remote server: ${error}`);
                    logDownload.prop('disabled', true);
                    showErrorImage();
                    $('#folderTree').empty(); // 🔹 Ensure folder tree is cleared on error
                    $('#serverDropdown').prop('disabled', false);
                }
            });
        }
    });

    function showErrorNotification(message) {
        // Display a toast message or alert
        alert(message); // Replace with a better UI message if needed
    }

    // Helper function to toggle folder icon based on expansion
    function updateToggleIcon(folderElement, action) {
        var toggleIcon = folderElement.find('.js-toggle-icon span');
        if (action === 'open') {
            toggleIcon.removeClass('cp-circle-plus').addClass('cp-circle-minus');
            folderElement.find('i.cp-folder-close').removeClass('cp-folder-close').addClass('cp-folder-open');
        } else {
            toggleIcon.removeClass('cp-circle-minus').addClass('cp-circle-plus');
            folderElement.find('i.cp-folder-open').removeClass('cp-folder-open').addClass('cp-folder-close');
        }
    }

});