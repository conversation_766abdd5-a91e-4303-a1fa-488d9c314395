using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MssqlAlwaysOnAvailabilityGroupMonitorLogFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLAlwaysOnAvailabilityGroup";

    public List<MssqlAlwaysOnAvailabilityGroupMonitorLog> MssqlAlwaysOnAvailabilityGroupMonitorLogPaginationList { get; set; }
    public List<MssqlAlwaysOnAvailabilityGroupMonitorLog> MssqlAlwaysOnAvailabilityGroupMonitorLogList { get; set; }
    public MssqlAlwaysOnAvailabilityGroupMonitorLog MssqlAlwaysOnAvailabilityGroupMonitorLogDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MssqlAlwaysOnAvailabilityGroupMonitorLogFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<MssqlAlwaysOnAvailabilityGroupMonitorLog>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        MssqlAlwaysOnAvailabilityGroupMonitorLogPaginationList = _fixture.CreateMany<MssqlAlwaysOnAvailabilityGroupMonitorLog>(20).ToList();
        MssqlAlwaysOnAvailabilityGroupMonitorLogList = _fixture.CreateMany<MssqlAlwaysOnAvailabilityGroupMonitorLog>(5).ToList();
        MssqlAlwaysOnAvailabilityGroupMonitorLogDto = _fixture.Create<MssqlAlwaysOnAvailabilityGroupMonitorLog>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MssqlAlwaysOnAvailabilityGroupMonitorLog CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MssqlAlwaysOnAvailabilityGroupMonitorLog>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MssqlAlwaysOnAvailabilityGroupMonitorLog CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithWhitespace()
    {
        return CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: "  MSSQLAlwaysOnAvailabilityGroup  ");
    }

    public MssqlAlwaysOnAvailabilityGroupMonitorLog CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMssqlAlwaysOnAvailabilityGroupMonitorLogWithProperties(type: longType);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLAlwaysOnAvailabilityGroup", "AlwaysOnAG", "AvailabilityGroup", "MSSQLAG" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
    }
}
