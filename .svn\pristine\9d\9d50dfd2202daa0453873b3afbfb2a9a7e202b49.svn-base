using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class FiaTemplateRepositoryTests : IClassFixture<FiaTemplateFixture>, IDisposable
{
    private readonly FiaTemplateFixture _fiaTemplateFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly FiaTemplateRepository _repository;

    public FiaTemplateRepositoryTests(FiaTemplateFixture fiaTemplateFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _repository = new FiaTemplateRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithoutId()
    {
        // Arrange
        var templateName = "Financial Impact Template";
        _fiaTemplateFixture.FiaTemplateDto.Name = templateName;

        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(templateName, null);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameDoesNotExist_WithoutId()
    {
        // Arrange
        var nonExistentName = "Non Existent Template";

        // Act
        var result = await _repository.IsNameExist(nonExistentName, null);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsFalse_WhenNameExists_WithSameId()
    {
        // Arrange
        var existingId = Guid.NewGuid().ToString();
        var templateName = "Business Impact Template";

        _fiaTemplateFixture.FiaTemplateDto.ReferenceId = existingId;
        _fiaTemplateFixture.FiaTemplateDto.Name = templateName;

        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(templateName, existingId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ReturnsTrue_WhenNameExists_WithDifferentId()
    {
        // Arrange
        var differentId = Guid.NewGuid().ToString();
        var templateName = "Operational Impact Template";

        _fiaTemplateFixture.FiaTemplateDto.Name = templateName;

        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsNameExist(templateName, differentId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetFiaTemplateByImpactCategoryId Tests

    [Fact]
    public async Task GetFiaTemplateByImpactCategoryId_ReturnsTemplates_WhenCategoryIdExists()
    {
        // Arrange
        var categoryId = "CAT_001";
        var template1 = new Domain.Entities.FiaTemplate
        {
            Name = "Template 1",
            Properties = $"{{\"impactCategoryId\":\"{categoryId}\",\"other\":\"value\"}}",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var template2 = new Domain.Entities.FiaTemplate
        {
            Name = "Template 2",
            Properties = $"{{\"impactCategoryId\":\"{categoryId}\",\"type\":\"financial\"}}",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FiaTemplates.AddRangeAsync(new[] { template1, template2 });
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByImpactCategoryId(categoryId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(categoryId, x.Properties));
    }

    [Fact]
    public async Task GetFiaTemplateByImpactCategoryId_ReturnsEmpty_WhenCategoryIdDoesNotExist()
    {
        // Arrange
        var nonExistentCategoryId = "CAT_999";

        _fiaTemplateFixture.FiaTemplateDto.Properties = "{\"impactCategoryId\":\"CAT_001\"}";
        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByImpactCategoryId(nonExistentCategoryId);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFiaTemplateByImpactTypeId Tests

    [Fact]
    public async Task GetFiaTemplateByImpactTypeId_ReturnsTemplates_WhenTypeIdExists()
    {
        // Arrange
        var typeId = "TYPE_001";
        var template = new Domain.Entities.FiaTemplate
        {
            Name = "Impact Type Template",
            Properties = $"{{\"impactTypeId\":\"{typeId}\",\"category\":\"financial\"}}",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FiaTemplates.AddAsync(template);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByImpactTypeId(typeId);

        // Assert
        Assert.Single(result);
        Assert.Contains(typeId, result[0].Properties);
    }

    [Fact]
    public async Task GetFiaTemplateByImpactTypeId_ReturnsEmpty_WhenTypeIdDoesNotExist()
    {
        // Arrange
        var nonExistentTypeId = "TYPE_999";

        _fiaTemplateFixture.FiaTemplateDto.Properties = "{\"impactTypeId\":\"TYPE_001\"}";
        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByImpactTypeId(nonExistentTypeId);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetFiaTemplateByIntervalId Tests

    [Fact]
    public async Task GetFiaTemplateByIntervalId_ReturnsTemplates_WhenIntervalIdExists()
    {
        // Arrange
        var intervalId = "INTERVAL_001";
        var template = new Domain.Entities.FiaTemplate
        {
            Name = "Interval Template",
            Properties = $"{{\"intervalId\":\"{intervalId}\",\"duration\":\"24h\"}}",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        await _dbContext.FiaTemplates.AddAsync(template);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByIntervalId(intervalId);

        // Assert
        Assert.Single(result);
        Assert.Contains(intervalId, result[0].Properties);
    }

    [Fact]
    public async Task GetFiaTemplateByIntervalId_ReturnsEmpty_WhenIntervalIdDoesNotExist()
    {
        // Arrange
        var nonExistentIntervalId = "INTERVAL_999";

        _fiaTemplateFixture.FiaTemplateDto.Properties = "{\"intervalId\":\"INTERVAL_001\"}";
        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFiaTemplateByIntervalId(nonExistentIntervalId);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region Overridden Methods Tests

    [Fact]
    public async Task ListAllAsync_ReturnsOnlyActiveTemplates()
    {
        // Arrange
        var templates = new List<Domain.Entities.FiaTemplate>
{
    new Domain.Entities.FiaTemplate
    {
        Name = "Active Template",
        ReferenceId = Guid.NewGuid().ToString(),
        IsActive = true
    },
    new Domain.Entities.FiaTemplate
    {
        Name = "Inactive Template",
        ReferenceId = Guid.NewGuid().ToString(),
        IsActive = false
    }
};

        await _dbContext.FiaTemplates.AddRangeAsync(templates);
        await _dbContext.SaveChangesAsync();

        var inactiveTemplate = templates[1];
        inactiveTemplate.IsActive = false;

        _dbContext.FiaTemplates.Update(inactiveTemplate);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Single(result);
        Assert.True(result[0].IsActive);
        Assert.Equal("Active Template", result[0].Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsTemplate_WhenExists()
    {
        // Arrange
        var referenceId = Guid.NewGuid().ToString();
        _fiaTemplateFixture.FiaTemplateDto.ReferenceId = referenceId;
        _fiaTemplateFixture.FiaTemplateDto.Name = "Test Template";

        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Test Template", result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        var nonExistentReferenceId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void GetPaginatedQuery_ReturnsActiveTemplatesOrderedById()
    {
        // Arrange
        var activeTemplate1 = new Domain.Entities.FiaTemplate
        {
            Id = 1,
            Name = "Template 1",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var activeTemplate2 = new Domain.Entities.FiaTemplate
        {
            Id = 2,
            Name = "Template 2",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = true
        };

        var inactiveTemplate = new Domain.Entities.FiaTemplate
        {
            Id = 3,
            Name = "Inactive Template",
            ReferenceId = Guid.NewGuid().ToString(),
            IsActive = false
        };

        _dbContext.FiaTemplates.AddRange(new[] { activeTemplate1, activeTemplate2, inactiveTemplate });
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery().ToList();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Equal(2, result[0].Id); // Should be ordered by Id descending
        Assert.Equal(1, result[1].Id);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddFiaTemplate_WhenValidTemplate()
    {
        // Arrange
        var template = _fiaTemplateFixture.FiaTemplateDto;
        template.Name = "Test Template";
        template.Description = "Test Description";
        template.Properties = "{\"category\":\"financial\",\"type\":\"revenue\"}";
        template.UserName = "testuser";
        template.TemplateUsedBy = "TestApp";
        template.TemplateInUsed = false;

        // Act
        var result = await _repository.AddAsync(template);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(template.Name, result.Name);
        Assert.Equal(template.Description, result.Description);
        Assert.Equal(template.Properties, result.Properties);
        Assert.Equal(template.UserName, result.UserName);
        Assert.Single(_dbContext.FiaTemplates);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenTemplateIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsTemplate_WhenExists()
    {
        // Arrange
        _fiaTemplateFixture.FiaTemplateDto.Id = 1;
        await _dbContext.FiaTemplates.AddAsync(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(1);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fiaTemplateFixture.FiaTemplateDto.Id, result.Id);
        Assert.Equal(_fiaTemplateFixture.FiaTemplateDto.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateTemplate_WhenValidTemplate()
    {
        // Arrange
        _dbContext.FiaTemplates.Add(_fiaTemplateFixture.FiaTemplateDto);
        await _dbContext.SaveChangesAsync();

        _fiaTemplateFixture.FiaTemplateDto.Name = "Updated Template Name";
        _fiaTemplateFixture.FiaTemplateDto.Description = "Updated Description";
        _fiaTemplateFixture.FiaTemplateDto.TemplateInUsed = true;

        // Act
        var result = await _repository.UpdateAsync(_fiaTemplateFixture.FiaTemplateDto);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Template Name", result.Name);
        Assert.Equal("Updated Description", result.Description);
        Assert.True(result.TemplateInUsed);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenTemplateIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion
}
