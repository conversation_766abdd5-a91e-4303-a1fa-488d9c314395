using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Create;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftEvent.Commands.Update;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftEvent.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DriftEventModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftEventControllerTests : IClassFixture<DriftEventFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftEventsController _controller;
    private readonly DriftEventFixture _driftEventFixture;

    public DriftEventControllerTests(DriftEventFixture driftEventFixture)
    {
        _driftEventFixture = driftEventFixture;

        var testBuilder = new ControllerTestBuilder<DriftEventsController>();
        _controller = testBuilder.CreateController(
            _ => new DriftEventsController(),
            out _mediatorMock);
    }

    #region GetDriftEvents Tests

    [Fact]
    public async Task GetDriftEvents_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftEventFixture.DriftEventListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftEvents();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftEventListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise", item.EntityName));
    }

    [Fact]
    public async Task GetDriftEvents_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftEventListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftEvents();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftEventListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftEvents_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftEvents());
    }

    #endregion

    #region CreateDriftEvent Tests

    [Fact]
    public async Task CreateDriftEvent_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

   
    [Fact]
    public async Task CreateDriftEvent_WithInvalidInfraObjectId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.InfraObjectId = "invalid-guid"; // Invalid GUID

        var failureResponse = new CreateDriftEventResponse
        {
            Success = false,
            Message = "Invalid InfraObjectId format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftEvent Tests

    [Fact]
    public async Task UpdateDriftEvent_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        var expectedResponse = _driftEventFixture.UpdateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
    }

    

    [Fact]
    public async Task UpdateDriftEvent_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftEventResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftEventById Tests

    [Fact]
    public async Task GetDriftEventById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftEventFixture.DriftEventDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftEventDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftEventById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftEventDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("Enterprise", returnedDetail.EntityName);
    }

    [Fact]
    public async Task GetDriftEventById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftEventById(invalidId));
    }

    [Fact]
    public async Task GetDriftEventById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftEventById(nullId));
    }

    #endregion

    #region DeleteDriftEvent Tests

    [Fact]
    public async Task DeleteDriftEvent_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftEventFixture.DeleteDriftEventResponse;
        expectedResponse.IsActive = false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftEventCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftEvent(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftEventResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftEvent_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftEvent(invalidId));
    }

    [Fact]
    public async Task DeleteDriftEvent_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftEvent(nullId));
    }

    #endregion

    #region Additional Comprehensive Test Cases
    
    [Fact]
    public async Task GetDriftEventById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventDetailQuery>(), default))
            .ThrowsAsync(new NotFoundException(nameof(DriftEvent), nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetDriftEventById(nonExistentId));
    }

    [Fact]
    public async Task DeleteDriftEvent_WithAlreadyDeletedEvent_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftEventFixture.DeleteDriftEventResponse;
        expectedResponse.IsActive = false; // Already deleted

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftEvent(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftEventResponse>(okResult.Value);
        Assert.False(returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
    }

    [Fact]
    public async Task GetDriftEvents_WithLargeDataset_ReturnsOkResult()
    {
        // Arrange
        var largeList = new List<DriftEventListVm>();
        for (int i = 0; i < 1000; i++)
        {
            largeList.Add(new DriftEventListVm
            {
                Id = Guid.NewGuid().ToString(),
                EntityName = $"Enterprise Event {i}",
                EntityType = "Detected",
                EntityStatus = "Pending"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventListQuery>(), default))
            .ReturnsAsync(largeList);

        // Act
        var result = await _controller.GetDriftEvents();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftEventListVm>>(okResult.Value);
        Assert.Equal(1000, returnedList.Count);
    }

    [Fact]
    public async Task CreateDriftEvent_WithCriticalEntityStatus_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.EntityStatus = "Critical";
        command.EntityType = "Database";
        command.Message = "Critical database drift detected in production environment";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Critical", command.EntityStatus);
        Assert.Equal("Database", command.EntityType);
        Assert.Contains("Critical", command.Message);
    }

    [Fact]
    public async Task CreateDriftEvent_WithLongMessage_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.Message = new string('A', 4000); // Long message to test NCLOB handling
        command.EntityName = "Enterprise Database Server";
        command.Entity = "DB_PROD_001";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(4000, command.Message.Length);
        Assert.Equal("Enterprise Database Server", command.EntityName);
        Assert.Equal("DB_PROD_001", command.Entity);
    }

    [Fact]
    public async Task UpdateDriftEvent_WithEntityStatusChange_ReturnsOkResult()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        command.EntityStatus = "Resolved";
        command.Message = "Drift issue has been resolved and configuration synchronized";
        var expectedResponse = _driftEventFixture.UpdateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Resolved", command.EntityStatus);
        Assert.Contains("resolved", command.Message);
    }

    [Fact]
    public async Task CreateDriftEvent_WithNetworkEntityType_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.EntityType = "Network";
        command.Entity = "SWITCH_CORE_01";
        command.EntityName = "Core Network Switch";
        command.EntityStatus = "Warning";
        command.Message = "Network configuration drift detected on core switch";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Network", command.EntityType);
        Assert.Equal("SWITCH_CORE_01", command.Entity);
        Assert.Equal("Core Network Switch", command.EntityName);
        Assert.Equal("Warning", command.EntityStatus);
    }

    [Fact]
    public async Task UpdateDriftEvent_WithDetailedMessage_ReturnsOkResult()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        command.EntityType = "Application";
        command.Message = @"Application configuration drift detected:
                          - Config file: app.config
                          - Parameter: ConnectionString
                          - Expected: Server=PROD;Database=MainDB
                          - Actual: Server=DEV;Database=TestDB
                          - Impact: High
                          - Remediation: Update configuration to production values";
        var expectedResponse = _driftEventFixture.UpdateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Application", command.EntityType);
        Assert.Contains("ConnectionString", command.Message);
        Assert.Contains("Remediation", command.Message);
    }

    [Fact]
    public async Task CreateDriftEvent_WithSpecialCharactersInMessage_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.Message = "Drift detected: Special chars !@#$%^&*()_+-={}[]|\\:;\"'<>?,./~`";
        command.EntityName = "Test Entity with Special Characters: !@#$%";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("!@#$%^&*()", command.Message);
        Assert.Contains("!@#$%", command.EntityName);
    }

    [Fact]
    public async Task CreateDriftEvent_WithUnicodeCharacters_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.EntityName = "服务器配置漂移 - Server Configuration Drift";
        command.Message = "配置漂移检测到: Unicode characters test ñáéíóú αβγδε 中文测试";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("服务器配置漂移", command.EntityName);
        Assert.Contains("中文测试", command.Message);
    }

    [Fact]
    public async Task UpdateDriftEvent_WithEmptyMessage_ReturnsOkResult()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        command.Message = string.Empty;
        command.EntityStatus = "Pending";
        var expectedResponse = _driftEventFixture.UpdateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal(string.Empty, command.Message);
        Assert.Equal("Pending", command.EntityStatus);
    }

    [Fact]
    public async Task CreateDriftEvent_WithServerEntityType_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.EntityType = "Server";
        command.Entity = "SRV-PROD-WEB-01";
        command.EntityName = "Production Web Server 01";
        command.EntityStatus = "Active";
        command.Message = "Server configuration drift detected: Memory allocation changed from 16GB to 8GB";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Equal("Server", command.EntityType);
        Assert.Equal("SRV-PROD-WEB-01", command.Entity);
        Assert.Equal("Production Web Server 01", command.EntityName);
        Assert.Equal("Active", command.EntityStatus);
        Assert.Contains("Memory allocation", command.Message);
    }

    [Fact]
    public async Task UpdateDriftEvent_WithMultilineMessage_ReturnsOkResult()
    {
        // Arrange
        var command = _driftEventFixture.UpdateDriftEventCommand;
        command.Message = @"Configuration Drift Analysis Report:
Line 1: Database connection timeout changed
Line 2: SSL certificate expiration date modified
Line 3: Backup schedule frequency updated
Line 4: User access permissions altered
Line 5: System monitoring thresholds adjusted";
        command.EntityType = "Configuration";
        var expectedResponse = _driftEventFixture.UpdateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftEvent(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftEventResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Line 1:", command.Message);
        Assert.Contains("Line 5:", command.Message);
        Assert.Equal("Configuration", command.EntityType);
    }

    [Fact]
    public async Task CreateDriftEvent_WithJsonFormattedMessage_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftEventFixture.CreateDriftEventCommand;
        command.Message = @"{""driftType"": ""configuration"", ""severity"": ""high"", ""affectedComponents"": [""database"", ""webserver""], ""timestamp"": ""2024-01-15T10:30:00Z"", ""details"": {""configFile"": ""app.config"", ""changedParameters"": [""connectionString"", ""timeout""]}}";
        command.EntityType = "JSON";
        var expectedResponse = _driftEventFixture.CreateDriftEventResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftEventCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftEvent(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftEventResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("driftType", command.Message);
        Assert.Contains("severity", command.Message);
        Assert.Contains("affectedComponents", command.Message);
        Assert.Equal("JSON", command.EntityType);
    }

    #endregion

    #region GetPaginatedDriftEvents Tests

    [Fact]
    public async Task GetPaginatedDriftEvents_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftEventFixture.GetDriftEventPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 10;
        query.SearchString = "Enterprise";
        var expectedResult = _driftEventFixture.DriftEventPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftEvents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftEventListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.True(returnedResult.Data.Count > 0);
        Assert.All(returnedResult.Data, item => Assert.Contains("Enterprise", item.EntityName));
    }

    [Fact]
    public async Task GetPaginatedDriftEvents_WithLargePageSize_ReturnsOkResult()
    {
        // Arrange
        var query = _driftEventFixture.GetDriftEventPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 100;
        query.SearchString = "";
        var expectedResult = _driftEventFixture.DriftEventPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftEvents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftEventListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal(100, query.PageSize);
        Assert.Equal(1, query.PageNumber);
    }

    [Fact]
    public async Task GetPaginatedDriftEvents_WithSpecificEntityTypeFilter_ReturnsFilteredResults()
    {
        // Arrange
        var query = _driftEventFixture.GetDriftEventPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 20;
        query.SearchString = "Database";
        var expectedResult = _driftEventFixture.DriftEventPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftEvents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftEventListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal("Database", query.SearchString);
        Assert.Equal(20, query.PageSize);
    }

    [Fact]
    public async Task GetPaginatedDriftEvents_WithEmptySearchString_ReturnsAllResults()
    {
        // Arrange
        var query = _driftEventFixture.GetDriftEventPaginatedListQuery;
        query.PageNumber = 1;
        query.PageSize = 50;
        query.SearchString = "";
        var expectedResult = _driftEventFixture.DriftEventPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventPaginatedListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftEvents(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftEventListVm>>(okResult.Value);
        Assert.NotNull(returnedResult);
        Assert.Equal("", query.SearchString);
        Assert.True(returnedResult.Data.Count >= 0);
    }

    #endregion

    #region IsDriftEventNameExist Tests

    [Fact]
    public async Task IsDriftEventNameExist_WithExistingName_ReturnsTrue()
    {
        // Arrange
        var driftEventName = "Enterprise Database Configuration Drift";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftEventNameExist(driftEventName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDriftEventNameExist_WithNonExistingName_ReturnsFalse()
    {
        // Arrange
        var driftEventName = "Non-Existing Drift Event Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftEventNameExist(driftEventName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDriftEventNameExist_WithNullId_ReturnsCorrectResult()
    {
        // Arrange
        var driftEventName = "Enterprise Server Configuration Drift";
        string? id = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftEventNameExist(driftEventName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    [Fact]
    public async Task IsDriftEventNameExist_WithSpecialCharactersInName_ReturnsCorrectResult()
    {
        // Arrange
        var driftEventName = "Enterprise-Config_Drift@2024!";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftEventNameExist(driftEventName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.True(returnedValue);
    }

    [Fact]
    public async Task IsDriftEventNameExist_WithLongName_ReturnsCorrectResult()
    {
        // Arrange
        var driftEventName = "Enterprise Database Configuration Drift Event for Critical Production Server Infrastructure Management System";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftEventNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftEventNameExist(driftEventName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var returnedValue = Assert.IsType<bool>(okResult.Value);
        Assert.False(returnedValue);
    }

    #endregion
}
