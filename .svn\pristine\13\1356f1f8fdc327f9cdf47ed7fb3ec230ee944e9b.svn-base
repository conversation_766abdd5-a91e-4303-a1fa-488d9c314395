﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class TeamResourceFilterSpecification : Specification<TeamResource>
{
    public TeamResourceFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("teammastername=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.TeamMasterName.Contains(stringItem.Replace("teammastername=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("resourcename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.ResourceName.Contains(stringItem.Replace("resourcename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.TeamMasterName.Contains(searchString) || p.ResourceName.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.TeamMasterName != null;
        }
    }
}