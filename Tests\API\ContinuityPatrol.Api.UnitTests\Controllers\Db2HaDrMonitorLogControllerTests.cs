using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetList;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class Db2HaDrMonitorLogControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Db2HaDrMonitorLogController _controller;
    private readonly Db2HaDrMonitorLogFixture _db2HaDrMonitorLogFixture;

    public Db2HaDrMonitorLogControllerTests()
    {
        _db2HaDrMonitorLogFixture = new Db2HaDrMonitorLogFixture();

        var testBuilder = new ControllerTestBuilder<Db2HaDrMonitorLogController>();
        _controller = testBuilder.CreateController(
            _ => new Db2HaDrMonitorLogController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDb2HaDrMonitorLog_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorLog(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDB2HADRMonitorLogResponse>(createdResult.Value);
        Assert.Equal("Enterprise DB2 HADR Monitor Log created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorLogById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var expectedDetail = _db2HaDrMonitorLogFixture.DB2HADRMonitorLogDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDB2HADRMonitorLogDetailQuery>(q => q.Id == logId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDb2HaDrMonitorLogById(logId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DB2HADRMonitorLogDetailVm>(okResult.Value);
        Assert.Equal("Enterprise DB2 HADR Detail", returnedDetail.Type);
        Assert.Equal("Enterprise DB2 Primary Database Detail", returnedDetail.InfraObjectName);
        Assert.Equal("Enterprise HADR Detail Workflow", returnedDetail.WorkflowName);
    }

    [Fact]
    public async Task GetAllDb2HaDrMonitorLog_ReturnsOkResult()
    {
        // Arrange
        var logList = _db2HaDrMonitorLogFixture.DB2HADRMonitorLogListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDB2HADRMonitorLogListQuery>(), default))
            .ReturnsAsync(logList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorLog();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DB2HADRMonitorLogListVm>>(okResult.Value);
        Assert.Equal(3, returnedList.Count);
        Assert.All(returnedList, log => Assert.Contains("Enterprise", log.Type));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorLog_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorLogFixture.GetDB2HADRMonitorLogPaginatedListQuery;
        var paginatedResult = _db2HaDrMonitorLogFixture.DB2HADRMonitorLogPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>(okResult.Value);
        Assert.Equal(5, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise", log.Type));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorLogByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var type = "Enterprise DB2 HADR";
        var expectedDetailList = new List<DB2HADRMonitorLogDetailByTypeVm>
        {
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise DB2 HADR Type",
                InfraObjectName = "Enterprise DB2 Type Database",
                WorkflowName = "Enterprise DB2 HADR Workflow",
                ConfiguredRPO = "10",
                DataLagValue = "2"
            },
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise DB2 HADR Type",
                InfraObjectName = "Enterprise DB2 Type Database 2",
                WorkflowName = "Enterprise DB2 HADR Workflow 2",
                ConfiguredRPO = "15",
                DataLagValue = "4"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDB2HADRMonitorLogDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorLogByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<DB2HADRMonitorLogDetailByTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("Enterprise DB2 HADR Type", detail.Type));
        Assert.All(returnedDetailList, detail => Assert.Contains("Enterprise DB2 Type Database", detail.InfraObjectName));
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDb2HaDrMonitorLog_CallsClearDataCache()
    {
        // Arrange
        var command = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDb2HaDrMonitorLog(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    

    [Fact]
    public async Task CreateDb2HaDrMonitorLog_HandlesHighAvailabilityConfiguration()
    {
        // Arrange
        var haCommand = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        haCommand.Type = "Enterprise HA DB2 HADR";
        haCommand.Properties = @"{
            ""primaryServer"": ""PROD-DB2-HA-01"",
            ""standbyServer"": ""PROD-DB2-HA-02"",
            ""replicationMode"": ""SYNC"",
            ""failoverTime"": ""30"",
            ""compressionEnabled"": true,
            ""encryptionEnabled"": true,
            ""monitoringInterval"": 30,
            ""alertThreshold"": ""critical""
        }";
        haCommand.ConfiguredRPO = "5";
        haCommand.Threshold = "15";

        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;

        _mediatorMock
            .Setup(m => m.Send(haCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorLog(haCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDB2HADRMonitorLogResponse>(createdResult.Value);
        
        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise HA DB2 HADR", haCommand.Type);
        Assert.Equal("5", haCommand.ConfiguredRPO);
        Assert.Contains("SYNC", haCommand.Properties);
        Assert.Contains("encryptionEnabled", haCommand.Properties);
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorLog_HandlesLargeDataset()
    {
        // Arrange
        var query = _db2HaDrMonitorLogFixture.GetDB2HADRMonitorLogPaginatedListQuery;
        query.PageSize = 50;
        query.SearchString = "Enterprise Production";

        var largePaginatedResult = new PaginatedResult<DB2HADRMonitorLogPaginatedListVm>
        {
            Data = Enumerable.Range(1, 50).Select(i => new DB2HADRMonitorLogPaginatedListVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = $"Enterprise Production DB2 HADR {i}",
                InfraObjectName = $"Enterprise Production Database {i:D2}",
                WorkflowName = $"Enterprise Production Workflow {i:D2}",
                ConfiguredRPO = (i % 3 == 0 ? "5" : "10"),
                DataLagValue = (i % 2 == 0 ? "2" : "3")
            }).ToList(),
            TotalCount = 50,
            PageSize = 50,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(largePaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>(okResult.Value);
        
        Assert.Equal(50, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise Production", log.Type));
        Assert.Contains(returnedResult.Data, log => log.ConfiguredRPO == "5");
        Assert.Contains(returnedResult.Data, log => log.ConfiguredRPO == "10");
    }

   
    [Fact]
    public async Task GetDb2HaDrMonitorLogByType_WithHighVolumeEnvironment_ReturnsOkResult()
    {
        // Arrange
        var type = "Enterprise High Volume DB2 HADR";
        var expectedDetailList = new List<DB2HADRMonitorLogDetailByTypeVm>
        {
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise High Volume DB2 HADR",
                InfraObjectName = "Enterprise Production DB2 Cluster 1",
                WorkflowName = "Enterprise High Volume HADR Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "1"
            },
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise High Volume DB2 HADR",
                InfraObjectName = "Enterprise Production DB2 Cluster 2",
                WorkflowName = "Enterprise High Volume HADR Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "2"
            },
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise High Volume DB2 HADR",
                InfraObjectName = "Enterprise Production DB2 Cluster 3",
                WorkflowName = "Enterprise High Volume HADR Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "1"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDB2HADRMonitorLogDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorLogByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<DB2HADRMonitorLogDetailByTypeVm>>(okResult.Value);
        Assert.Equal(3, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("Enterprise High Volume DB2 HADR", detail.Type));
        Assert.All(returnedDetailList, detail => Assert.Contains("Enterprise Production DB2 Cluster", detail.InfraObjectName));
        Assert.All(returnedDetailList, detail => Assert.Equal("5", detail.ConfiguredRPO));
        Assert.All(returnedDetailList, detail => Assert.True(int.Parse(detail.DataLagValue) <= 2));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorLog_WithCriticalAlertsFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorLogFixture.GetDB2HADRMonitorLogPaginatedListQuery;
        query.SearchString = "Critical Alert";
        query.PageSize = 15;

        var criticalAlertsPaginatedResult = new PaginatedResult<DB2HADRMonitorLogPaginatedListVm>
        {
            Data = Enumerable.Range(1, 15).Select(i => new DB2HADRMonitorLogPaginatedListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Critical DB2 Server {i:D2}",
                WorkflowName = $"Enterprise Critical Alert Workflow {i:D2}",
                ConfiguredRPO = (5 + i % 3).ToString(),
                DataLagValue = (i % 4 + 1).ToString(),
                Type = i % 2 == 0 ? "CRITICAL_ALERT" : "HIGH_PRIORITY_ALERT"
            }).ToList(),
            TotalCount = 15,
            PageSize = 15,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(criticalAlertsPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>(okResult.Value);
        Assert.Equal(15, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise Critical", log.InfraObjectName));
        Assert.Contains(returnedResult.Data, log => log.Type == "CRITICAL_ALERT");
        Assert.Contains(returnedResult.Data, log => log.Type == "HIGH_PRIORITY_ALERT");
        Assert.All(returnedResult.Data, log => Assert.True(int.Parse(log.ConfiguredRPO) >= 5));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorLog_WithEmptyResult_ReturnsOkResult()
    {
        // Arrange
        var emptyLogList = new List<DB2HADRMonitorLogListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDB2HADRMonitorLogListQuery>(), default))
            .ReturnsAsync(emptyLogList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorLog();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DB2HADRMonitorLogListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDb2HaDrMonitorLog_WithDisasterRecoveryScenario_ReturnsCreatedResult()
    {
        // Arrange
        var drCommand = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        drCommand.InfraObjectName = "Enterprise DR Site DB2 Primary";
        drCommand.WorkflowName = "Enterprise DR Failover Workflow";
        drCommand.Type = "DR_FAILOVER_EXECUTION";
        drCommand.ConfiguredRPO = "0";
        drCommand.DataLagValue = "0";

        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;
        expectedResponse.Message = "Enterprise DR DB2 HADR Monitor Log created successfully during failover!";

        _mediatorMock
            .Setup(m => m.Send(drCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorLog(drCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDB2HADRMonitorLogResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("DR", returnedResponse.Message);
        Assert.Equal("Enterprise DR Site DB2 Primary", drCommand.InfraObjectName);
        Assert.Equal("DR_FAILOVER_EXECUTION", drCommand.Type);
        Assert.Equal("0", drCommand.ConfiguredRPO);
        Assert.Equal("0", drCommand.DataLagValue);
    }


    [Fact]
    public async Task GetDb2HaDrMonitorLogByType_WithCriticalFailoverScenario_ReturnsOkResult()
    {
        // Arrange
        var type = "CRITICAL_FAILOVER_EXECUTION";
        var expectedDetailList = new List<DB2HADRMonitorLogDetailByTypeVm>
        {
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "CRITICAL_FAILOVER_EXECUTION",
                InfraObjectName = "Enterprise Primary DB2 Cluster",
                WorkflowName = "Enterprise Critical Failover Workflow",
                ConfiguredRPO = "0",
                DataLagValue = "0"
            },
            new DB2HADRMonitorLogDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "CRITICAL_FAILOVER_EXECUTION",
                InfraObjectName = "Enterprise Secondary DB2 Cluster",
                WorkflowName = "Enterprise Critical Failover Workflow",
                ConfiguredRPO = "0",
                DataLagValue = "1"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDB2HADRMonitorLogDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorLogByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<DB2HADRMonitorLogDetailByTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("CRITICAL_FAILOVER_EXECUTION", detail.Type));
        Assert.All(returnedDetailList, detail => Assert.Contains("Enterprise", detail.InfraObjectName));
        Assert.All(returnedDetailList, detail => Assert.Equal("0", detail.ConfiguredRPO));
        Assert.True(returnedDetailList.All(detail => int.Parse(detail.DataLagValue) <= 1));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorLog_WithComplianceAuditFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorLogFixture.GetDB2HADRMonitorLogPaginatedListQuery;
        query.SearchString = "Compliance Audit";
        query.PageSize = 20;

        var complianceAuditPaginatedResult = new PaginatedResult<DB2HADRMonitorLogPaginatedListVm>
        {
            Data = Enumerable.Range(1, 20).Select(i => new DB2HADRMonitorLogPaginatedListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Compliance DB2 Server {i:D2}",
                WorkflowName = $"Enterprise SOX Compliance Workflow {i:D2}",
                ConfiguredRPO = (5 + i % 3).ToString(),
                DataLagValue = (i % 2).ToString(),
                Type = i % 3 == 0 ? "SOX_COMPLIANCE_CHECK" : i % 2 == 0 ? "AUDIT_TRAIL_VALIDATION" : "REGULATORY_MONITORING"
            }).ToList(),
            TotalCount = 20,
            PageSize = 20,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(complianceAuditPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>(okResult.Value);
        Assert.Equal(20, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise Compliance", log.InfraObjectName));
        Assert.Contains(returnedResult.Data, log => log.Type == "SOX_COMPLIANCE_CHECK");
        Assert.Contains(returnedResult.Data, log => log.Type == "AUDIT_TRAIL_VALIDATION");
        Assert.Contains(returnedResult.Data, log => log.Type == "REGULATORY_MONITORING");
    }

    [Fact]
    public async Task CreateDb2HaDrMonitorLog_WithBusinessContinuityScenario_ReturnsCreatedResult()
    {
        // Arrange
        var bcpCommand = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        bcpCommand.InfraObjectName = "Enterprise BCP DB2 Primary";
        bcpCommand.WorkflowName = "Enterprise Business Continuity Workflow";
        bcpCommand.Type = "BUSINESS_CONTINUITY_TEST";
        bcpCommand.ConfiguredRPO = "15";
        bcpCommand.DataLagValue = "10";

        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;
        expectedResponse.Message = "Enterprise BCP DB2 HADR Monitor Log created successfully!";

        _mediatorMock
            .Setup(m => m.Send(bcpCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorLog(bcpCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDB2HADRMonitorLogResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("BCP", returnedResponse.Message);
        Assert.Equal("Enterprise BCP DB2 Primary", bcpCommand.InfraObjectName);
        Assert.Equal("BUSINESS_CONTINUITY_TEST", bcpCommand.Type);
        Assert.True(int.Parse(bcpCommand.DataLagValue) < int.Parse(bcpCommand.ConfiguredRPO));
    }

   

    [Fact]
    public async Task GetDb2HaDrMonitorLog_WithZeroDowntimeRequirement_ReturnsOkResult()
    {
        // Arrange
        var zeroDowntimeLogList = Enumerable.Range(1, 50).Select(i => new DB2HADRMonitorLogListVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise Zero-Downtime DB2 {i:D2}",
            WorkflowName = $"Enterprise Zero-Downtime Workflow {i:D2}",
            ConfiguredRPO = "1",
            DataLagValue = "0",
            Type = i % 4 == 0 ? "ZERO_DOWNTIME_VALIDATION" :
                   i % 3 == 0 ? "CONTINUOUS_AVAILABILITY_CHECK" :
                   i % 2 == 0 ? "HIGH_AVAILABILITY_MONITORING" : "REAL_TIME_SYNC_VERIFICATION"
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDB2HADRMonitorLogListQuery>(), default))
            .ReturnsAsync(zeroDowntimeLogList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorLog();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DB2HADRMonitorLogListVm>>(okResult.Value);
        Assert.Equal(50, returnedList.Count);
        Assert.All(returnedList, log => Assert.Contains("Enterprise Zero-Downtime", log.InfraObjectName));
        Assert.All(returnedList, log => Assert.Equal("1", log.ConfiguredRPO));
        Assert.All(returnedList, log => Assert.Equal("0", log.DataLagValue));
        Assert.Contains(returnedList, log => log.Type == "ZERO_DOWNTIME_VALIDATION");
        Assert.Contains(returnedList, log => log.Type == "CONTINUOUS_AVAILABILITY_CHECK");
        Assert.Contains(returnedList, log => log.Type == "HIGH_AVAILABILITY_MONITORING");
        Assert.Contains(returnedList, log => log.Type == "REAL_TIME_SYNC_VERIFICATION");
    }


    [Fact]
    public async Task CreateDb2HaDrMonitorLog_WithMultiRegionReplication_ReturnsCreatedResult()
    {
        // Arrange
        var multiRegionCommand = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogCommand;
        multiRegionCommand.Type = "MULTI_REGION_REPLICATION";
        multiRegionCommand.InfraObjectName = "Enterprise Multi-Region DB2 Primary";
        multiRegionCommand.WorkflowName = "Enterprise Multi-Region HADR Workflow";
        multiRegionCommand.Properties = @"{
            ""primaryRegion"": ""US-East"",
            ""secondaryRegion"": ""US-West"",
            ""tertiaryRegion"": ""EU-Central"",
            ""replicationMode"": ""ASYNC_MULTI"",
            ""crossRegionLatency"": ""50ms"",
            ""compressionEnabled"": true,
            ""encryptionEnabled"": true,
            ""monitoringInterval"": 15,
            ""alertThreshold"": ""warning"",
            ""failoverStrategy"": ""automatic""
        }";
        multiRegionCommand.ConfiguredRPO = "30";
        multiRegionCommand.DataLagValue = "25";
        multiRegionCommand.Threshold = "45";

        var expectedResponse = _db2HaDrMonitorLogFixture.CreateDB2HADRMonitorLogResponse;
        expectedResponse.Message = "Enterprise Multi-Region DB2 HADR Monitor Log created successfully!";

        _mediatorMock
            .Setup(m => m.Send(multiRegionCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorLog(multiRegionCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDB2HADRMonitorLogResponse>(createdResult.Value);
        Assert.Equal("Enterprise Multi-Region DB2 HADR Monitor Log created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorLogById_WithPerformanceOptimizedConfiguration_ReturnsOkResult()
    {
        // Arrange
        var logId = Guid.NewGuid().ToString();
        var performanceOptimizedLog = _db2HaDrMonitorLogFixture.DB2HADRMonitorLogDetailVm;
        performanceOptimizedLog.Id = logId;
        performanceOptimizedLog.Type = "PERFORMANCE_OPTIMIZED_HADR";
        performanceOptimizedLog.InfraObjectName = "Enterprise Performance DB2 Primary";
        performanceOptimizedLog.WorkflowName = "Enterprise Performance HADR Workflow";
        performanceOptimizedLog.ConfiguredRPO = "5";
        performanceOptimizedLog.DataLagValue = "2";
        performanceOptimizedLog.Properties = @"{
            ""performanceMode"": ""high_throughput"",
            ""bufferPoolSize"": ""8GB"",
            ""logBufferSize"": ""256MB"",
            ""compressionLevel"": ""adaptive"",
            ""parallelReplication"": true,
            ""indexOptimization"": ""enabled"",
            ""cacheStrategy"": ""aggressive""
        }";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDB2HADRMonitorLogDetailQuery>(q => q.Id == logId), default))
            .ReturnsAsync(performanceOptimizedLog);

        // Act
        var result = await _controller.GetDb2HaDrMonitorLogById(logId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedLog = Assert.IsType<DB2HADRMonitorLogDetailVm>(okResult.Value);
        Assert.Equal(logId, returnedLog.Id);
        Assert.Equal("PERFORMANCE_OPTIMIZED_HADR", returnedLog.Type);
        Assert.Equal("Enterprise Performance DB2 Primary", returnedLog.InfraObjectName);
        Assert.Equal("5", returnedLog.ConfiguredRPO);
        Assert.Equal("2", returnedLog.DataLagValue);
        Assert.Contains("high_throughput", returnedLog.Properties);
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorLog_WithLargeDatasetFiltering_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorLogFixture.GetDB2HADRMonitorLogPaginatedListQuery;
        query.PageSize = 25;
        query.PageNumber = 1;
        query.SearchString = "Enterprise Large Dataset";

        var largeDatasetLogs = Enumerable.Range(1, 25).Select(i => new DB2HADRMonitorLogPaginatedListVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise Large Dataset DB2 {i:D3}",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowName = $"Enterprise Large Dataset Workflow {i:D3}",
            Type = i % 5 == 0 ? "LARGE_DATASET_CRITICAL" :
                   i % 4 == 0 ? "LARGE_DATASET_WARNING" :
                   i % 3 == 0 ? "LARGE_DATASET_NORMAL" :
                   i % 2 == 0 ? "LARGE_DATASET_OPTIMIZED" : "LARGE_DATASET_MONITORING",
            ConfiguredRPO = (i * 2).ToString(),
            DataLagValue = (i / 2).ToString(),
            Properties = $@"{{
                ""datasetSize"": ""{i * 100}GB"",
                ""compressionRatio"": ""{0.7 + (i * 0.01):F2}"",
                ""indexCount"": {i * 10},
                ""partitionCount"": {i * 2}
            }}"
        }).ToList();

        var paginatedResult = new PaginatedResult<DB2HADRMonitorLogPaginatedListVm>
        {
            Data = largeDatasetLogs,
            TotalCount = 250,
            PageSize = 25,
            CurrentPage = 1,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorLog(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DB2HADRMonitorLogPaginatedListVm>>(okResult.Value);
        Assert.Equal(25, returnedResult.Data.Count);
        Assert.Equal(250, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, log => Assert.Contains("Enterprise Large Dataset", log.InfraObjectName));
        Assert.Contains(returnedResult.Data, log => log.Type == "LARGE_DATASET_CRITICAL");
        Assert.Contains(returnedResult.Data, log => log.Type == "LARGE_DATASET_WARNING");
        Assert.Contains(returnedResult.Data, log => log.Type == "LARGE_DATASET_NORMAL");
    }

}
