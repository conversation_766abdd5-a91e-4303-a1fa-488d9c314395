﻿using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel;

namespace ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetList;

public class
    GetWorkflowPermissionListQueryHandler : IRequestHandler<GetWorkflowPermissionListQuery,
        List<WorkflowPermissionListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPermissionRepository _workflowPermissionRepository;

    public GetWorkflowPermissionListQueryHandler(IMapper mapper,
        IWorkflowPermissionRepository workflowPermissionRepository)
    {
        _mapper = mapper;
        _workflowPermissionRepository = workflowPermissionRepository;
    }

    public async Task<List<WorkflowPermissionListVm>> Handle(GetWorkflowPermissionListQuery request,
        CancellationToken cancellationToken)
    {
        var workflowPermissions = (await _workflowPermissionRepository.ListAllAsync()).ToList();

        return workflowPermissions.Count > 0
            ? _mapper.Map<List<WorkflowPermissionListVm>>(workflowPermissions)
            : new List<WorkflowPermissionListVm>();
    }
}