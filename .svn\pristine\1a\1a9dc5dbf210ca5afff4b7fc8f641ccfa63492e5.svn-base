﻿let rpo<PERSON><PERSON>, rpo<PERSON>ategoryA<PERSON>s, rpoV<PERSON>ueAxis, rpoSeries1, rpoSeries2, rpoLabel
function RpoSummaray(valueData) {

    const timeConverter = (time, mode) => {
        
        if (!time) {
            return mode === 'chart' ? 0 : '00:00';
        }

        let splitTimeWithDay = '';
        let splitTime = '';
        if (time?.includes('.')) {
            splitTimeWithDay = time?.replace('+', '')?.split('.')[0];
            splitTime = time?.split('.')[1];
        } else {
            splitTime = time?.replace('+', '');
        }

        let getTime = splitTime?.split(':');
        if (mode === 'chart') {
            let totalMinutes = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24 * 60) + (+getTime[0] * 60) + +getTime[1];
            return totalMinutes;
        } else {
            let totalHours = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24) + +getTime[0] + Math.trunc(+getTime[1] / 60);
            return splitTimeWithDay ? `${totalHours} hours` : `${+getTime[0]} hours ${+getTime[1]} min`;
        }
    };
    
    // Create chart instance
    if (!rpoChart) {
        rpoChart = am4core.create("RPOSummary", am4charts.RadarChart);
        if (rpoChart.logo) {
            rpoChart.logo.disabled = true;
        }

        // Make chart not full circle
        rpoChart.startAngle = -90;
        rpoChart.endAngle = 180;
        rpoChart.innerRadius = am4core.percent(50);

        // Set number format
        rpoChart.numberFormatter.numberFormat = "#.#'%'";

        // Change the padding values
        rpoChart.padding(-10, -10, -10, -10)
        rpoChart.defaultState.transitionDuration = 1000; 

        rpoCategoryAxis = rpoChart.yAxes.push(new am4charts.CategoryAxis());
        rpoCategoryAxis.renderer.grid.template.location = 0;
        rpoCategoryAxis.renderer.grid.template.strokeOpacity = 0;
        rpoCategoryAxis.renderer.labels.template.fontWeight = 300;
        rpoCategoryAxis.renderer.labels.template.fontSize = 9;
        rpoCategoryAxis.renderer.labels.template.disabled = true;
        rpoCategoryAxis.renderer.minGridDistance = 10;
        rpoCategoryAxis.renderer.cellStartLocation = 0.5;
        rpoCategoryAxis.renderer.cellEndLocation = 1;

        rpoValueAxis = rpoChart.xAxes.push(new am4charts.ValueAxis());
        rpoValueAxis.renderer.grid.template.strokeOpacity = 0;
        rpoValueAxis.strictMinMax = true;
        rpoValueAxis.renderer.labels.template.disabled = true;

        rpoSeries1 = rpoChart.series.push(new am4charts.RadarColumnSeries());
        rpoSeries1.clustered = false;
        rpoSeries1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");
        rpoSeries1.columns.template.fillOpacity = 0.08;
        rpoSeries1.columns.template.cornerRadiusTopLeft = 20;
        rpoSeries1.columns.template.strokeWidth = 0;
        rpoSeries1.columns.template.radarColumn.cornerRadius = 20;
        rpoSeries1.defaultState.transitionDuration = 1000;

        rpoSeries2 = rpoChart.series.push(new am4charts.RadarColumnSeries());
        rpoSeries2.clustered = false;
        rpoSeries2.columns.template.strokeWidth = 0;
        rpoSeries2.columns.template.radarColumn.cornerRadius = 20;
        rpoSeries2.defaultState.transitionDuration = 1000;

        rpoLabel = rpoChart.seriesContainer.createChild(am4core.Label);
        rpoLabel.text = "[bold]RPO[/]";
        rpoLabel.horizontalCenter = "middle";
        rpoLabel.verticalCenter = "middle";
        rpoLabel.fontSize = 12;
    }

    let configuredRPO;
    let rpoThreshold;
    
    // Add data
    if (valueData.hasOwnProperty('id')) {
        let currentRPO = (valueData?.currentRPO !== "NA" && valueData?.currentRPO !== "0" && valueData?.currentRPO !== "" && valueData?.currentRPO !== null && valueData?.currentRPO !== undefined) ? `${valueData?.currentRPO}` : '00:00'
        rpoThreshold = (valueData?.rpoThreshold !== "" && valueData?.rpoThreshold !== null && valueData?.rpoThreshold !== undefined) ? `${valueData?.rpoThreshold}` : 'NA'
        configuredRPO = (valueData?.configuredRPO !== "" && valueData?.configuredRPO !== null && valueData?.configuredRPO !== undefined) ? `${valueData?.configuredRPO}` : 'NA'
      

        rpoChart.data = [{
            "category": "Computed " + timeConverter(valueData?.currentRPO, 'value'),
            "value": timeConverter(valueData?.currentRPO, 'chart'),
            "full": Number(configuredRPO)
        }, {
            "category": "Threshold " + rpoThreshold +" Min",
            "value": rpoThreshold,
            "full": Number(configuredRPO)
        }, {
            "category": "Agreed " + configuredRPO +" Min",
            "value": configuredRPO,
            "full": Number(configuredRPO)
        }];
    }  

    else {

        $('#chartdata')
            .css('text-align', 'center')
            .html('<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">');

    }
    
    let colorvalue = +timeConverter(valueData?.currentRPO, "chart") < +rpoThreshold ? am4core.color("#41c200") : +timeConverter(valueData?.currentRPO, 'chart') > +configuredRPO ? am4core.color("#dc3545") : (+rpoThreshold < +timeConverter(valueData?.currentRPO, 'chart') && +timeConverter(valueData?.currentRPO, "chart") < +configuredRPO) ? am4core.color("#ff9632") : am4core.color("#e0e0e0")

    rpoChart.colors.list = [
        colorvalue,
        am4core.color("#07cedb"),
        am4core.color("#946eff")
    ];
    
    // Create axes   
    rpoCategoryAxis.dataFields.category = "category";
    rpoCategoryAxis.renderer.labels.template.horizontalCenter = "right";
    rpoCategoryAxis.renderer.labels.template.adapter.add("fill", function (fill, target) {
        return (target.dataItem.index >= 0) ? chart.colors.getIndex(target.dataItem.index) : fill;
    });

    rpoValueAxis.min = 0;
    rpoValueAxis.max = Number(configuredRPO);

    // Create series
    rpoSeries1.dataFields.valueX = "full";
    rpoSeries1.dataFields.categoryY = "category";

    rpoSeries2.dataFields.valueX = "value";
    rpoSeries2.dataFields.categoryY = "category";
    rpoSeries2.columns.template.tooltipText = "{category}";

    rpoSeries2.columns.template.adapter.add("fill", function (fill, target) {
        return rpoChart.colors.getIndex(target.dataItem.index);
    });

}

