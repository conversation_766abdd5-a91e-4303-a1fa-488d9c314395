﻿using ContinuityPatrol.Domain.ViewModels.FormModel;

namespace ContinuityPatrol.Application.Features.Form.Queries.GetNames;

public class GetFormNameQueryHandler : IRequestHandler<GetFormNameQuery, List<FormNameVm>>
{
    private readonly IFormRepository _formRepository;
    private readonly IMapper _mapper;

    public GetFormNameQueryHandler(IMapper mapper, IFormRepository formRepository)
    {
        _mapper = mapper;
        _formRepository = formRepository;
    }

    public async Task<List<FormNameVm>> Handle(GetFormNameQuery request, CancellationToken cancellationToken)
    {
        var form = await _formRepository.GetFormNames();

        return _mapper.Map<List<FormNameVm>>(form);
    }
}