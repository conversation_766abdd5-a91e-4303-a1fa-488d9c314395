﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;

public class UpdateCredentialProfileCommandValidator : AbstractValidator<UpdateCredentialProfileCommand>
{
    private readonly ICredentialProfileRepository _credentialProfileRepository;

    public UpdateCredentialProfileCommandValidator(ICredentialProfileRepository credentialProfileRepository)
    {
        _credentialProfileRepository = credentialProfileRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.");

        RuleFor(p => p.CredentialType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Must(p => IsValidJsonObjcet(p)).WithMessage("{PropertyName} must be a valid json string.");
        ;

        RuleFor(e => e)
            .MustAsync(CredentialProfileNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> CredentialProfileNameUnique(UpdateCredentialProfileCommand e, CancellationToken cancel)
    {
        return !await _credentialProfileRepository.IsCredentialProfileNameExist(e.Name, e.Id);
    }

    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}