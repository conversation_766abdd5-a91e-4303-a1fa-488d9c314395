﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.DataSetModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DataSet.Queries.GetPaginatedList;

public class
    GetDataSetPaginatedListQueryHandler : IRequestHandler<GetDataSetPaginatedListQuery,
        PaginatedResult<DataSetListVm>>
{
    private readonly IDataSetRepository _dataSetRepository;
    private readonly IMapper _mapper;

    public GetDataSetPaginatedListQueryHandler(IMapper mapper, IDataSetRepository dataSetRepository)
    {
        _mapper = mapper;
        _dataSetRepository = dataSetRepository;
    }

    public async Task<PaginatedResult<DataSetListVm>> Handle(GetDataSetPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DatasetFilterSpecification(request.SearchString);

        var queryable =await _dataSetRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var reportsList = _mapper.Map<PaginatedResult<DataSetListVm>>(queryable);

        return reportsList;
        //var queryable = _dataSetRepository.GetPaginatedQuery();

        //var productFilterSpec = new DatasetFilterSpecification(request.SearchString);

        //var reportsList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DataSetListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        ////await _publisher.Publish(new DataSetPaginatedEvent(), cancellationToken);
        //return reportsList;
    }
}