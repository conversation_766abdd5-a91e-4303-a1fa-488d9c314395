﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class GroupPolicyRepository : BaseRepository<GroupPolicy>, IGroupPolicyRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public GroupPolicyRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<GroupPolicy> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(groupName =>
                    groupName.ReferenceId.Equals(id) && groupName.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Result
                .SingleOrDefault());
    }

    public override async  Task<IReadOnlyList<GroupPolicy>> ListAllAsync()
    {
        return await FilterRequiredGroupPolicy(base.ListAllAsync(groupPolicy => groupPolicy.CompanyId.Equals(_loggedInUserService.CompanyId))).ToListAsync();
           
    }

    public Task<List<GroupPolicy>> GetGroupPolicyNames()
    {
        var cpNodes = base
            .ListAllAsync(policy => policy.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new GroupPolicy { ReferenceId = x.ReferenceId, GroupName = x.GroupName });

        return cpNodes.ToListAsync();
    }

    public Task<bool> IsGroupPolicyNameExist(string groupName, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.GroupPolicies.Any(e => e.GroupName.Equals(groupName)))
            : Task.FromResult(_dbContext.GroupPolicies.Where(e => e.GroupName.Equals(groupName)).ToList().Unique(id));
    }
    public override async Task<PaginatedResult<GroupPolicy>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<GroupPolicy> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await FilterRequiredGroupPolicy(_loggedInUserService.IsParent 
            ? Entities.Specify(productFilterSpec).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

    }
    public override IQueryable<GroupPolicy> PaginatedListAllAsync()
    {
        return base.ListAllAsync(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
             .AsNoTracking().OrderByDescending(x => x.Id);
    }
   
    public Task<bool> IsGroupPolicyNameUnique(string name)
    {
        var matches = _dbContext.GroupPolicies.Any(e => e.GroupName.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<List<GroupPolicy>> GetType(string type)
    {
        var matches = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.ToLower().Replace(" ", "").Equals(type.ToLower().Replace(" ", "")))
            : base.FilterBy(x => x.Type.ToLower().Replace(" ", "").Equals(type.ToLower().Replace(" ", "")) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        return Task.FromResult(matches.ToList());
    }
    public async Task<List<GroupPolicy>> GetGroupPolicyByLoadBalancerId(string loadBalancerId)
    {
        var groupPolicy = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Properties.Contains(loadBalancerId))
            : base.FilterBy(x => x.Properties.Contains(loadBalancerId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));
        
        return await groupPolicy.ToListAsync();

    }
    private IQueryable<GroupPolicy> FilterRequiredGroupPolicy(IQueryable<GroupPolicy> groupPolicy)
    {
        return groupPolicy.Select(x => new GroupPolicy
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            GroupName = x.GroupName,
            Type = x.Type,
            Properties = x.Properties,
            CompanyId = x.CompanyId
        });
    }
}