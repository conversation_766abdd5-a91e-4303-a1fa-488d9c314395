﻿const errorElements = ['#Name-error', '#Description-error', '#SelectActiveType-error', '#DRSelectServerNames-error', "#SelectSubType-error",
    "#BusinessService-error", "#SelectReplicationType-error", "#SelectPriority-error", '#SelectAssociate-error', '#SelectReplicationName-error',
    '#BusinessFunction-error', '#SelectPairInfra-erro', '#SelectReplication-error', '#SelectServerName-error', '#PRSelectDatabase-error',
    '#DRSelectDatabaseNames-error', "#DatbaseType-error", '#prSrmServer-error', '#drSrmServer-error', '#siteType-error'];

const dynamicErrorElements = ['#DatbaseType-error, #SelectServerName-error', '#DRSelectServerNames-error', '#PRSelectDatabase-error',
    '#DRSelectDatabaseNames-error', '#SelectReplicationName-error', '#DRSelectReplicationNames-error', '#NearReplicationName-error',
    '#prSrmServer-error', '#drSrmServer-error'];

const dynamicElements = ['#SelectServerName', '#PRSelectDatabase',
    '#PRMultipleServer', '#PRMultipleDatabase', '#DRMultipleServer', '#DRMultipleDatabase',
    '#PRReplicationName', '#DRSelectServerNames', '#DRSelectDatabaseNames',
    '#prSrmServer', '#drSrmServer', '#SelectReplicationNames'];

const nameExistUrl = "Configuration/InfraObject/IsInfraObjectNameExist";

let createPermission = $("#configurationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#configurationDelete").data("delete-permission").toLowerCase();
let isEdit = false;

let NodeValuesLength;
let activeType = '';
let databaseType = '';
let replicationType = '';
let replication = '';
let Activitydata = '';
let prServer = '';
let drServer = '';
let ReplicationtypeName = '';
let ReplicationCategory = '';

let siteProperties = [];
let selectedNodeValues = [];
let PRdata = []
let associateProperties = [];
let serverList = [];
let DRdata = []

let prToDrMapping = {};
let infraData = {};
let serverProperties = {};
let databaseProperties = {};
let replicationProperties = {};

let multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
let multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };

let currentSrNo = 1;
let database = '';
let serverDataMap = new Map();

const clusterReplicationData = ['hyper-v', 'zerto', 'robocopy', 'rsync', 'rpforvmreplication', 'srmvmware']
const exceptThisSymbols = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

//----pagination--->
$(function () {

    $("#pairinfra, #associate").hide();
    if (createPermission == 'false') {
        $("#infraObject-createbutton").removeClass('#infraObject-createbutton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }

    btnCrudEnable('confirmDeleteButton')
    let selectedValues = [];
    let dataTable = $('#InfraObjectList').DataTable(

        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/Configuration/InfraObject/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = (d?.order && d.order[0]) ? d.order[0].column : ''; //
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "businessServiceName" : sortIndex === 3 ? "businessFunctionName" :
                        sortIndex === 4 ? "typeName" : sortIndex === 5 ? "replicationCategoryType" : sortIndex === 6 ? "state" : sortIndex === 7 ? "status" : "";
                    let orderValue = (d?.order && d.order[0]) ? d.order[0].dir : 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                },
                "error": function (xhr, status, error) {
                    if (error.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },
            },

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,

                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                            //var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            //return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false,
                },

                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessServiceName", "name": "Business Service", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "businessFunctionName", "name": "Business Function", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "typeName", "name": "Active type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "replicationCategoryType", "name": "Replication Category", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,

                    "render": function (data, type, row) {
                        data = data || 'Maintenance'

                        if (type === 'display') {

                            return `<span title="${data}">
                                     <i class="${data.toLowerCase() === 'locked' ? 'cp-lock text-warning' : data.toLowerCase() === 'active' ? 'cp-active-inactive text-success' : data.toLowerCase() === 'locked' ? 'cp-lock text-warning' : data.toLowerCase() === 'maintenance' ? 'cp-maintenance text-primary' : '-'}"></i>
                               </span>`;

                        }
                        return data;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        let stateData = row.state == "Active" ? "icon-disabled" : ""

                        if (createPermission == 'true' && deletePermission == 'true') {
                            return `
                        <div class="d-flex align-items-center gap-2">

                             <span role="button" title="Edit" class="edit-button ${stateData}"  data-infra-state="${row.state}" data-infra='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>   
                                
                                <span role="button" title="Delete" class="delete-button ${stateData}"  data-infra='${JSON.stringify(row)}' data-infra-state="${row.state}" data-bs-toggle="modal" >
                                    <i class="cp-Delete"></i>
                                </span>   
                    </div>`;
                        }
                        else if (createPermission == 'true' && deletePermission == 'false') {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button ${stateData}" data-infra='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>    
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>   
                    </div>`;
                        }
                        else if (createPermission == 'false' && deletePermission == 'true') {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>    
                                <span role="button" title="Delete" class="delete-button ${stateData}" data-infra-id="${row.id}" data-infra-name="${row.name}" data-bs-toggle="modal">
                                    <i class="cp-Delete"></i>
                                </span>   
                    </div>`;
                        }
                        else if (createPermission == 'false' && deletePermission == 'false') {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>    
                                <span role="button" title="Delete" class="icon-disabled" >
                                    <i class="cp-Delete"></i>
                                </span>   
                    </div>`;
                        }
                    },
                }
            ],

            "columnDefs": [
                {
                    "targets": [1, 2, 3, 5],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {

                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keyup input', commonDebounce(function (e) {

        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        $('.searchCheckbox').each(function () {
            if ($(this).is(':checked')) {
                let checkboxValue = $(this).val();
                let inputValue = $('#search-inp').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });

        dataTable.ajax.reload(function (json) {

            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $('#search-inp').attr('autocomplete', 'off');
    $('.form-select-sm').select2({
        "language": {
            "noResults": function () {
                return "No Results Found";
            }
        },
    });
})

$('#confirmDeleteButton').on('click', function () {

    btnCrudDiasable('confirmDeleteButton');
})

const getServerRole = async () => {

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Configuration/Server/GetServerRole",
        dataType: "json",
        data: {},
        success: function (result) {
            if (result?.success) {
                if (result?.data && Array.isArray(result?.data)) serverList = result?.data

            } else {
                errorNotification(result);
            }
        },
    });
}


const getServerOsType = (id) => {

    let data = []

    if (id) {
        $.ajax({
            type: "GET",
            async: false,
            url: RootUrl + "Configuration/Server/GetServerType",
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result?.success) {
                    if (result?.data && Array.isArray(result?.data)) data = result?.data

                } else {
                    errorNotification(result);
                }
            },
        });
    }

    return data
}

const getVeritasHacmpList = async (mode = '') => {

    $('#clusterPR, #clusterDR').empty();

    try {
        const [veritasResult, hacmpResult] = await Promise.all([
            (mode?.toLowerCase() == "veritas") && $.get(RootUrl + "Configuration/InfraObject/GetVeritasClusters"),
            (mode?.toLowerCase() == "hacmp") && $.get(RootUrl + "Configuration/InfraObject/GetHACMPClusters")
        ]);

        if (mode?.toLowerCase() == 'veritas') {

            if (veritasResult?.success) {
                $('#clusterPR').append(`<option value=""></option>`);

                if (veritasResult?.data && Array.isArray(veritasResult?.data) && veritasResult?.data.length) {
                    for (let cluster of veritasResult?.data) {
                        $('#clusterPR').append(`<option clusterType="PR" value="${cluster?.id}">${cluster?.clusterName}</option>`);
                    }
                }
            } else {
                errorNotification(veritasResult);
            }
        }

        if (mode?.toLowerCase() == 'hacmp') {
            if (hacmpResult?.success) {
                $('#clusterPR, #clusterDR').append(`<option value=""></option>`);

                if (hacmpResult?.data && Array.isArray(hacmpResult?.data) && hacmpResult?.data.length) {
                    for (let cluster of hacmpResult?.data) {
                        $('#clusterPR').append(`<option clusterType="PR" value="${cluster?.id}">${cluster?.name}</option>`);
                        $('#clusterDR').append(`<option clusterType="DR" value="${cluster?.id}">${cluster?.name}</option>`);
                    }
                }
            } else {
                errorNotification(hacmpResult);
            }
        }

        if (isEdit) {
            serverProperties?.clusters && serverProperties['clusters'].length && serverProperties['clusters'].forEach((server) => {
                if (server?.type == 'PR') $('#clusterPR').val(server?.id).trigger('change')
                else if (server?.type == 'DR') $('#clusterDR').val(server?.id).trigger('change')
            })
        }

    } catch (error) {
        console.error(error);
    }
};

// site type list

const getSiteDetailsByOperationService = async (getSiteProperties) => {

    $('#siteType').empty();

    if (getSiteProperties) {

        let siteData = getSiteProperties ? JSON.parse(getSiteProperties) : {}
        let sitePropertiesKeys = Object.keys(siteData)

        for (let i = 0; i < sitePropertiesKeys.length; i++) {
            let getSiteCategory = siteData[sitePropertiesKeys[i]]?.category || await getSiteBySiteId(siteData[sitePropertiesKeys[i]]?.Id);

            if (siteData[sitePropertiesKeys[i]]?.Id) {
                $('#siteType').append(`<option data-category='${getSiteCategory}' data-sitetypename='${sitePropertiesKeys[i]}' value='${siteData[sitePropertiesKeys[i]]?.Id}'>
              ${sitePropertiesKeys[i]} (${siteData[sitePropertiesKeys[i]]?.Name})
               </option >`);
            }
        }

        if (isEdit) {
            let getSiteIds = siteProperties.length && siteProperties.map(data => data?.id)
            if (getSiteIds.length) {
                $('#siteType').val(getSiteIds)
            } else if (infraData?.drReady) {
                $('#siteType option[data-category="DR"]').prop('selected', true).trigger('change')
            }
        }

        if (!$('#siteType').val().length) $('#siteType option[data-category="Primary"]').prop('selected', true).trigger('change').prop('disabled', true)

    }

}

const getSiteBySiteId = async (id) => {

    let category = '';

    if (id) {

        await $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/InfraObject/GetSiteTypeDetails',
            data: { siteId: id },
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    if (result?.data) category = result?.data?.category

                } else {
                    errorNotification(result);
                }

            }
        })
    }

    return category;
}

//---Bussiness Function--->
async function SetBusinessFunction(id) {

    $('#ddlbusinessFunctionId').empty();
    let url = RootUrl + "Configuration/InfraObject/GetBusinessFunctions";

    if (id) {
        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data?.length) {
                        $('#ddlbusinessFunctionId').prepend('<option value="">Select operational function</option>')
                        let length = result?.data.length
                        for (let i = 0; i < length; i++) {
                            $('#ddlbusinessFunctionId').append('<option  value="' + result?.data[i]?.id + '">' + result?.data[i]?.name + '</option>');
                        }
                        if (length === 1) {
                            $('#ddlbusinessFunctionId').val(result?.data[0]?.id).trigger('change')
                        }
                    }

                    if (Object.keys(infraData).length) {
                        $('#ddlbusinessFunctionId').val(infraData?.businessFunctionId);
                        $('#BusinessFunctionId').val(infraData?.businessFunctionId);
                        $('#BusinessFunctionVal').val(infraData?.businessFunctionName);
                    }

                } else {
                    errorNotification(result);
                }
            },
        });

    }
}

//--- PR Server --->
async function SetPRServerType(roleType, type, serverType = 'PRDBServer', oracleFound = false, srm = false) {
    let role = srm ? 'virtualization' : roleType == 'DB' ?
        'database' : roleType == 'Virtual' ? 'virtualization' : roleType.toLowerCase();

    let filteredServer = serverList.length && serverList.find(data => data?.name?.toLowerCase() === role);
    let getServerType = (roleType == 'DB' || srm) && getServerOsType(filteredServer?.id);
    let getServerSubType = Array.isArray(getServerType) && getServerType.length && getServerType.find(data => data?.name?.toLowerCase() === serverType.toLowerCase());

    if (filteredServer) {

        let url = RootUrl + "Configuration/InfraObject/GetServerRoleTypeAndServerType";
        let data = {};
        data.roleType = roleType == 'Virtual' ? '' : filteredServer?.id
        data.serverType = (roleType == 'DB' || srm) ? getServerSubType?.id : ""

        if (type === 'production') $('.PRServerName, .PRDatabaseName').empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result?.success) {

                    if (type === 'production') {
                        if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                            let length = result?.data.length

                            $('.PRServerName').not('[multiple]').append('<option value="" selected hidden disabled>' + "Select Server" + '</option>')

                            for (let index = 0; index < length; index++) {
                                $('.PRServerName').append('<option prSId="' + result?.data[index]?.id + '" value="' + result?.data[index]?.name + '">' + result?.data[index]?.name + '</option>');
                            }

                        }

                        if (Object.keys(infraData).length) {
                            if (result?.data?.length) {

                                if (oracleFound) {

                                    let findLabels = infraData?.serverProperties
                                    let parsedValuess = JSON.parse(findLabels);
                                    let idz = parsedValuess?.PR?.name;
                                    $('#PRMultipleServer').val(idz.split(',')).trigger('change');;


                                    //let prDatabaseArray = [];

                                    //multipleServerDetails['multiplePRServer'] = infraData?.prServerId?.split(',') || []

                                    //multipleServerDetails?.multiplePRServer.length && multipleServerDetails?.multiplePRServer.forEach((data) => {
                                    //    let findLabel = result?.data.filter(s => s?.id == data);

                                    //    if (findLabel.length) prNameArray.push(findLabel[0]?.name)

                                    //    SetServerID(data, '', 'multiplePR', true, prDatabaseArray);
                                    //})

                                    //$('#PRMultipleServer').val(prNameArray).trigger('change');
                                }
                                else {

                                    let parsedValues = JSON.parse(infraData?.serverProperties);
                                    $('#SelectServerName').val(parsedValues?.PR?.name).trigger('change');
                                    SetServerID(parsedValues?.PR?.id, '', '', false);
                                }
                                $('#SelectServerName-error').text('').removeClass('field-validation-error');

                            }
                        }
                    }

                } else {
                    errorNotification(result);
                }
            },
        });
    }
}

//--- DR Server --->
async function SetDRServerType(roleType, serverType = 'DRDBServer', oracleFound = false, srm = false) {

    let role = srm ? 'virtualization' : roleType == 'DB' ?
        'database' : roleType == 'Virtual' ? 'virtualization' : roleType?.toLowerCase();

    let filteredServer = serverList.length && serverList.find(data => data?.name?.toLowerCase() === role)

    let getServerType = (roleType == 'DB' || srm) && getServerOsType(filteredServer?.id);
    let getServerSubType = Array.isArray(getServerType) && getServerType.length && getServerType.find(data => data?.name?.toLowerCase() === serverType.toLowerCase())

    if (filteredServer) {
        let url = RootUrl + "Configuration/InfraObject/GetServerRoleTypeAndServerType";
        let data = {};
        data.roleType = roleType == 'Virtual' ? '' : filteredServer?.id
        data.serverType = (roleType == 'DB' || srm) ? getServerSubType?.id : ""

        $('.DRServerName, .DRDatabaseName').empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                        $('.DRServerName').not('[multiple]').append('<option value="" selected hidden disabled>' + "Select Server" + '</option>')

                        for (let index = 0; index < result?.data.length; index++) {

                            $('.DRServerName').append('<option drSId="' + result?.data[index]?.id + '" value="' + result?.data[index]?.name + '">' + result?.data[index]?.name + '</option>');
                        }

                    }

                    if (Object.keys(infraData).length) {
                        if (result?.data.length) {

                            if (oracleFound) {
                                // let drDatabaseArray = [];
                                let findLabels = infraData?.serverProperties
                                let parsedValuess = JSON.parse(findLabels);
                                let idz = parsedValuess?.DR?.name;
                                $('#DRMultipleServer').val(idz.split(',')).trigger('change');

                                //multipleServerDetails['multipleDRServer'] = infraData?.drServerId?.split(',') || []

                                //multipleServerDetails?.multipleDRServer.length && multipleServerDetails?.multipleDRServer.forEach((data) => {
                                //    let findLabel = result?.data.filter(s => s?.id == data);

                                //    if (findLabel.length) drNameArray.push(findLabel[0]?.name)

                                //   SetDrServerID(data, 'multipleDR', true, drDatabaseArray);
                                //})

                                //$('#DRMultipleServer').val(drNameArray).trigger('change');
                            }
                            else {

                                let parsedValues = JSON.parse(infraData?.serverProperties);
                                $('#DRSelectServerNames').val(parsedValues?.DR?.name).trigger('change');
                                SetDrServerID(parsedValues?.DR?.id, '', false);
                            }
                            $('#DRSelectServerNames-error').text('').removeClass('field-validation-error');
                        }
                    }
                } else {
                    errorNotification(result);
                }
            },
        });
    }

}

// srm server
async function SetSrmServerType(roleType, serverType = '', mode = '') {

    let role = roleType?.toLowerCase();

    let filteredServer = serverList.length && serverList.find(data => data?.name?.toLowerCase() === role)
    let getServerType = getServerOsType(filteredServer?.id);
    let getServerSubType = Array.isArray(getServerType) && getServerType.length && getServerType.find(data => data?.name?.toLowerCase() === serverType.toLowerCase());

    if (filteredServer) {

        let url = RootUrl + "Configuration/InfraObject/GetServerRoleTypeAndServerType";
        let data = {};
        data.roleType = filteredServer?.id
        data.serverType = getServerSubType?.id

        if (mode == 'production') $('#prSrmServer').empty();
        else $('#drSrmServer').empty();

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                        $('#prSrmServer, #drSrmServer').append('<option value="">Select Server</option>');

                        for (let index = 0; index < result?.data?.length; index++) {

                            if (mode == 'production') $('#prSrmServer').append('<option srmType="PR" SrmName="' + result?.data[index]?.name + '"  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>');
                            else $('#drSrmServer').append('<option srmType="DR" SrmName="' + result?.data[index]?.name + '"  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>');
                        }

                    }

                    if (Object.keys(infraData).length && result?.data?.length) {
                        serverProperties?.SRMServer && serverProperties['SRMServer'].length && serverProperties['SRMServer'].forEach((server) => {
                            if (server?.type == 'PR' && mode == 'production') {
                                $('#prSrmServer').val(server?.id);
                                $('#tablesrm, #prSrm').show();
                            } else if (server?.type == 'DR') {
                                $('#drSrmServer').val(server?.id);
                                $('#drSrm').show();
                            }

                        })
                    }

                } else {
                    errorNotification(result);
                }
            },
        });

    }

}

//---PR DataBase--->
async function SetServerID(id, value, type = 'production', oracleFound = false, prDatabaseArray = []) {
    let url = RootUrl + "Configuration/InfraObject/GetDatabase";

    if (id) {

        if (type !== 'multiplePR') {
            $('.PRDatabaseName').empty();
        }

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                        $('.PRDatabaseName').not('[multiple]').append('<option value="" selected hidden disabled>' + "Select Database" + '</option>')

                        for (let index = 0; index < result?.data.length; index++) {

                            $('.PRDatabaseName').append('<option data-type = "' + result?.data[index]?.type + '" prServerId = "' + result?.data[index]?.serverId + '" prDId = "' + result?.data[index]?.id + '" value = "' + result?.data[index]?.name.trim('') + '" > ' + result?.data[index]?.name + '</option > ');
                        }
                        if (result?.data.length === 1 && !isEdit) {
                            if ($('#PRSelectDatabase').is(':visible')) {
                                $('#PRSelectDatabase').val(result?.data[0]?.name).trigger('change');
                            }

                        }
                    }

                    if (Object.keys(infraData).length) {
                        if (result?.data.length) {
                            let oracleFound = getOracleFound()

                            if (oracleFound) {

                                //multipleDatabaseDetails['multiplePRDatabase'] = infraData?.prDatabaseId?.split(',') || []

                                //multipleDatabaseDetails?.multiplePRDatabase.length && multipleDatabaseDetails?.multiplePRDatabase.forEach((data) => {
                                //    let findLabel = result?.data.filter(s => s?.id == data);

                                //    if (findLabel.length) prDatabaseArray.push(findLabel[0]?.name)
                                //})

                                // $('#PRMultipleDatabase').val(prDatabaseArray).trigger('change');

                                // let drDatabaseArray = [];

                                let findLabels = infraData?.databaseProperties
                                let parsedValuess = JSON.parse(findLabels);
                                let idz = parsedValuess?.PR?.name;
                                $('#PRMultipleDatabase').val(idz.split(',')).trigger('change');

                                // $('#PRMultipleDatabase').val(JSON.parse(infraData?.databaseProperties)?.DR?.name?.trim()).trigger('change');
                                //if (prDatabaseArray.length) $('#prDatabaseName').val(prDatabaseArray?.join(','));
                                //$('#PRSelectDatabaseId').val(infraData?.prDatabaseId);
                            }
                            else {

                                // let findLabel = result?.data.filter(s => s?.id == infraData?.prDatabaseId)
                                $('#PRSelectDatabase').val(JSON.parse(infraData?.databaseProperties)?.PR?.name?.trim()).trigger('change');

                                //if (findLabel.length) {
                                //    $('#PRSelectDatabase').val(findLabel.length && findLabel[0]?.name).trigger('change');
                                //    //$('#prDatabaseName').val(findLabel.length && findLabel[0]?.name);
                                //    //$('#PRSelectDatabaseId').val(infraData?.prDatabaseId);
                                //} else {
                                //    $('#PRSelectDatabase').val('').trigger('change');
                                //    //$('#prDatabaseName, #PRSelectDatabaseId').val('');
                                //}
                            }

                        }
                    }

                } else {
                    errorNotification(result);
                }
            },
        });
    }

}
//--- DR DataBase--->
async function SetDrServerID(id, type = '', oracleFound = false, drDatabaseArray = []) {

    let url = RootUrl + "Configuration/InfraObject/GetDatabase";

    if (id) {

        if (type !== 'multipleDR') {
            $('.DRDatabaseName').empty();
        }

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: { id: id },
            success: function (result) {
                if (result.success) {

                    if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                        $('.DRDatabaseName').not('[multiple]').append('<option value="" hidden disabled selected>' + "Select Database" + '</option>')

                        for (let index = 0; index < result?.data.length; index++) {

                            $('.DRDatabaseName').append('<option data-type = "' + result?.data[index]?.type + '" drServerId = "' + result?.data[index]?.serverId + '" drDId="' + result?.data[index]?.id + '" value="' + result?.data[index]?.name + '">' + result?.data[index]?.name + '</option>');
                        }
                        if (result?.data.length === 1 && !isEdit) {
                            if ($('#DRSelectDatabaseNames').is(':visible')) {
                                $('#DRSelectDatabaseNames').val(result?.data[0]?.name).trigger('change')
                            }
                        }
                    }

                    if (Object.keys(infraData).length) {
                        if (result?.data.length) {
                            let oracleFound = getOracleFound() || getMssqlFound();

                            if (oracleFound) {
                                let findLabels = infraData?.databaseProperties
                                let parsedValuess = JSON.parse(findLabels);
                                let idz = parsedValuess?.DR?.name;
                                $('#DRMultipleDatabase').val(idz.split(',')).trigger('change');
                                //multipleDatabaseDetails['multipleDRDatabase'] = infraData?.drDatabaseId?.split(',') || []

                                //multipleDatabaseDetails?.multipleDRDatabase.length && multipleDatabaseDetails?.multipleDRDatabase.forEach((data) => {
                                //    let findLabel = result?.data.filter(s => s?.id == data);

                                //    if (findLabel.length) drDatabaseArray.push(findLabel[0]?.name)
                                //})

                                //$('#DRMultipleDatabase').val(drDatabaseArray).trigger('change');
                                //if (drDatabaseArray.length) $('#drDatabaseName').val(drDatabaseArray?.join(','));
                                //$('#infraDatabaseId').val(infraData?.drDatabaseId);
                            }
                            else {

                                // let findLabel = result.data.filter(s => s?.id == infraData?.drDatabaseId)
                                $('#DRSelectDatabaseNames').val(JSON.parse(infraData?.databaseProperties)?.DR?.name).trigger('change');

                                //if (findLabel.length) {

                                //    $('#DRSelectDatabaseNames').val(findLabel.length && findLabel[0]?.name).trigger('change');
                                //    //$('#drDatabaseName').val(findLabel.length && findLabel[0]?.name);
                                //    //$('#infraDatabaseId').val(infraData?.drDatabaseId);

                                //} else {

                                //    $('#DRSelectDatabaseNames').val('').trigger('change');
                                //    //$('#infraDatabaseId').val(''); //#drDatabaseName, 

                                //}
                            }

                        }
                    }

                } else {
                    errorNotification(result);
                }
            },
        });

    }
    //  }
}

//--- Replication Category --->
async function SetReplicationMaster(type) {
    let url = RootUrl + "Configuration/InfraObject/GetReplicationMasterByInfraMasterName";
    let data = {};
    data.infraMasterName = type == 'Database' ? 'DB' : type;

    $('#SelectReplicationType').empty();
    await $.ajax({
        type: "GET",
        async: false,
        url: url,
        dataType: "json",
        data: data,
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#SelectReplicationType').append('<option value="">Select replication category</option>')

                    for (let i = 0; i < result?.data.length; i++) {
                        $('#SelectReplicationType').append('<option  value=' + result?.data[i]?.id + '>' + result?.data[i]?.name + '</option>');
                    }
                }

                if (Object.keys(infraData).length) {
                    $('#SelectReplicationType').val(infraData?.replicationCategoryTypeId)
                    $('#infraReplicationTypeId').val(infraData?.replicationCategoryTypeId);
                    $('#ReplicationCategorytext').val(infraData?.replicationCategoryType);
                    $('#SelectReplicationType-error').text('').removeClass('field-validation-error')
                    replicationType = infraData?.replicationCategoryTypeId
                    SetReplicationMapping()

                    if ((infraData?.replicationCategoryType !== "Application - No Replication") || (infraData?.replicationCategoryType !== "Database - No Replication")) {
                        $("#DRReplication, #PRReplication,#tablereplication").show();
                        $('#SelectReplicationName-error, #DRSelectReplicationNames-error').text('').removeClass('field-validation-error');
                    }
                }

            } else {
                errorNotification(result);
            }
        },
    });

}

//--- Replication Type --->
async function SetReplicationMapping() {

    let url = RootUrl + "Configuration/InfraObject/GetTypeByDatabaseIdAndReplicationMasterId";
    let data = {};

    data.databaseid = activeType === "DB" ? databaseType : ''
    data.replicationmasterid = replicationType
    data.type = activeType ? activeType == 'DB' ? 'Database' : activeType : infraData?.typeName

    $('#ddlReplicationTypeNameId').empty();

    await $.ajax({
        type: "GET",
        async: false,
        url: url,
        dataType: "json",
        data: data,
        success: function (result) {
            if (result.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    let uniqueIds = new Set();
                    $('#ddlReplicationTypeNameId').append('<option value=""> Select replication type </option>');

                    for (let index = 0; index < result?.data.length; index++) {
                        let properties = JSON.parse(result?.data[index]?.properties)
                        if (properties.length > 0) {
                            for (let j = 0; j < properties.length; j++) {

                                if (!uniqueIds.has(properties[j]?.id)) {
                                    $('#ddlReplicationTypeNameId').append('<option value="' + properties[j]?.id + '">' + properties[j]?.label + '</option>');
                                    uniqueIds.add(properties[j]?.id)
                                }

                            }
                        }
                    }

                }

                if (Object.keys(infraData).length) {
                    $('#ddlReplicationTypeNameId').val(infraData?.replicationTypeId);
                    $('#ReplicationTypeName').val(infraData?.replicationTypeName);
                    $('#ReplicationTypeId').val(infraData?.replicationTypeId);
                    $('#SelectReplication-error').text('').removeClass('field-validation-error');
                }

            } else {
                errorNotification(result);
            }
        },
    });
}

//--- Database Name --->
async function GetdabaseNames() {
    let url = RootUrl + "Configuration/InfraObject/GetDatabaseListByName";

    $('#SelectDatabaseType').empty()

    await $.ajax({
        type: "GET",
        async: false,
        url: url,
        dataType: "json",
        data: {},
        success: function (result) {
            if (result.success) {

                $('#SelectDatabaseType').append('<option value="">select Database</option>');

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    result?.data.forEach((s) => {
                        let optionValues = JSON.parse(s?.properties)
                        $('#SelectDatabaseType').append('<option value="' + s?.id + '">' + optionValues['name'] + '</option>');
                    })

                    if (Object.keys(infraData).length) {
                        $('#SelectDatabaseType').val(infraData?.subTypeId)
                        $('#DatabaseId').val(infraData?.subTypeId);
                        $('#databaseText').val(infraData?.subType);
                    }
                }

            } else {
                errorNotification(result);
            }
        },
    });

}

// load dynamic servers, database, replication

async function SetDynamicServerType(urls, type, serverType, selectType = 'server', value = '', sType = '', srm = false) {

    let html = ''
    let getActiveType = $('#Activetype option:selected').text();

    if (type === 'dynamic') {

        let url = RootUrl + urls;
        let data = {};

        let role = (srm && sType != 'dresxiappserver') ? 'virtualization' : getActiveType == 'DB' ?
            'database' : getActiveType == 'Virtual' ? 'virtualization' : getActiveType?.toLowerCase();

        let filteredServer = serverList.length && serverList.find(data => data?.name?.toLowerCase() === role)

        let getServerType = (getActiveType == 'DB' || srm) && getServerOsType(filteredServer?.id);

        let getServerSubType = Array.isArray(getServerType) && getServerType.length && getServerType.find(data => data?.name?.toLowerCase() === sType?.toLowerCase())

        if (selectType === 'server') {
            if (srm && sType == 'dresxiappserver') {

                data.roleType = filteredServer?.id
                data.serverType = getServerSubType?.id

                $(`#${serverType}SrmServer`).empty();
            } else {
                data.roleType = getActiveType == 'Virtual' ? '' : filteredServer?.id
                data.serverType = (getActiveType == 'DB' || srm) ? getServerSubType?.id : ""

                $(`#${serverType}ServerName, #${serverType}DatabaseName`).empty();
            }
        } else if (selectType === 'multipleserver') {
            data.roleType = getActiveType == 'Virtual' ? '' : filteredServer?.id
            data.serverType = getActiveType == 'DB' ? getServerSubType?.id : ""

            $(`#${serverType}MultipleServer`).empty();
        } else if (selectType === 'database') {

            data.id = value
            $(`#${serverType}DatabaseName`).empty();
        } else {
            data.roleType = ''
            data.serverType = ""

            $(`#${serverType}ReplicationName`).empty();
        }

        if (filteredServer) {

            await $.ajax({
                type: "GET",
                async: false,
                url: url,
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result.success) {

                        if (type === 'dynamic') {

                            if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                                let length = result?.data.length

                                html += `<option value="" hidden selected>Select ${selectType}</option>`

                                for (let index = 0; index < length; index++) {
                                    selectType == 'server' ? html += '<option  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                                        : html += '<option value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                                }
                            }
                        }

                    } else {
                        errorNotification(result);
                    }
                },
            });

        }
    }

    return html
}

async function SetDynamicDatabaseType(urls, type, serverType = 'PRDBServer', selectType = 'database', value = '') {

    let oracleFound = getOracleFound() || getMssqlFound();

    if (type === 'dynamic' && value) {

        let url = RootUrl + urls;
        let data = { id: value };

        if (selectType === 'database') {

            serverDataMap = new Map();
            $(`#${serverType}DatabaseName`).val('').empty();
        }

        await $.ajax({
            type: "GET",
            async: false,
            url: url,
            dataType: "json",
            data: data,
            success: function (result) {
                if (result?.success) {

                    if (type === 'dynamic') {
                        let $select = selectType !== 'database' ? $(`#${serverType}MultipleDatabase`) : $(`#${serverType}DatabaseName`);

                        if (result?.data && Array.isArray(result?.data) && result?.data.length) {

                            let length = result.data.length
                            let selectize = $select && $select[0]?.selectize;

                            if (selectize) {

                                selectize.disable();

                                if (selectType === 'database') {

                                    selectize.options = {};
                                    selectize.clearOptions();
                                    selectize.clear();

                                }

                                for (let index = 0; index < length; index++) {

                                    let serverId = result?.data[index]?.serverId;

                                    selectize.addOption({
                                        value: result?.data[index]?.id,
                                        text: result?.data[index]?.name,
                                    });

                                    serverDataMap.set(result?.data[index]?.id, { serverId });
                                };

                                if (selectType === 'database') selectize.refreshOptions();
                                selectize.enable();
                                selectize.close();

                            }
                        }
                    }

                } else {
                    errorNotification(result);
                }
            },
        });
    }

    if (isEdit) {

        Object.keys(databaseProperties)?.length && Object.keys(databaseProperties).forEach((database) => {
            if (databaseProperties[database] && $(`#${databaseProperties[database]?.siteId}DatabaseName`)?.length && databaseProperties[database]?.type == 'customdatabase') {
                if (oracleFound) {

                    multipleDatabaseDetails[database] = databaseProperties[database]?.id?.split(',') || []

                    $(`#${databaseProperties[database]?.siteId}MultipleDatabase`)[0].selectize.setValue(multipleDatabaseDetails[database]);

                } else {
                    $(`#${databaseProperties[database]?.siteId}DatabaseName`)[0].selectize.setValue(databaseProperties[database]?.id);
                }

            }
        })
    }
}

/// Replication List
const getReplicationList = async (urls, selectType) => {
    let html = ''

    await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + urls,
        dataType: "json",
        data: {},
        success: function (result) {
            if (result?.success) {

                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    let length = result?.data.length;

                    html += `<option value="" hidden selected>Select ${selectType}</option>`

                    for (let index = 0; index < length; index++) {
                        html += '<option  value="' + result?.data[index]?.id + '">' + result?.data[index]?.name + '</option>'
                    }
                }

            } else {
                errorNotification(result);
            }
        },
    });

    return html

}

const getOracleFound = () => {

    let oracleFound = ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication' &&
        (ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication-oracle-rac' || ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'native-replication-oracle-rac') &&
        (database?.replace(/\s+/g, '')?.toLowerCase() === 'oracle-rac' || database?.replace(/\s+/g, '')?.toLowerCase() === 'oracle')) || ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'nativereplication' &&
            ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase().includes('redis') &&
            database?.replace(/\s+/g, '')?.toLowerCase().includes('redis'))

    return oracleFound;

}

const getMssqlFound = () => {

    let mssqlFound = ((activeType?.replace(/\s+/g, '')?.toLowerCase() === 'db' || activeType == '2') && ReplicationCategory?.replace(/\s+/g, '')?.toLowerCase() === 'database-inbuildreplication' &&
        (ReplicationtypeName?.replace(/\s+/g, '')?.toLowerCase() === 'mssql-alwayson-availabilitygroup'))

    return mssqlFound;

}
function clearSelectAndTriggerChange(selectElementId, element) {
    $("#" + selectElementId).val('').trigger('change');
    $('#' + element).text('').removeClass('field-validation-error');
}

const loadDynamicSites = async () => {

    $('#infraDynamicBody').children('tr').slice(2, -2).remove();

    let getReplicationName = $('#ddlReplicationTypeNameId option:selected').text();
    let isSrmFound = getReplicationName && ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes('srmvmware');
    let isRedisFound = getReplicationName && getReplicationName?.toLowerCase().replace(/ /g, "").includes('redis')
    let oracleFound = getOracleFound();
    let mssqlFound = getMssqlFound();

    let html = '';

    for (let i = 0; i < siteProperties.length; i++) {

        if (siteProperties[i]?.category?.toLowerCase() === 'dr') {

            $('#DRCol, #dr_summary_header').show();
            $('#DRCol td:first').text(siteProperties[i]?.name);
            $('#DRSelectDatabaseNames-error, #DRSelectServerNames-error').text('').removeClass('field-validation-error');

            if (isSrmFound) {
                $('#drSrm').show();
                SetSrmServerType(activeType, 'dresxiappserver', 'dr');
            }

            if (oracleFound || mssqlFound) {
                $('#DRServerBody, #DRDatabaseBody').addClass('d-none')
                $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
            } else {
                $('#DRServerBody, #DRDatabaseBody').removeClass('d-none')
                $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
            }
            if (mssqlFound) {
                $('#PRReplication,#DRReplication,#tablereplication').addClass('d-none')

            } else {
                $('#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')

            }
            //SetDRServerType(activeType, '', oracleFound)

        } else if (siteProperties[i]?.category?.toLowerCase() === 'primary') {

            $('#Prtable td:first').text(siteProperties[i]?.name);

        } else {
            let dynamicId = siteProperties[i]?.id;
            let dynamicName = siteProperties[i]?.name;

            html += '<tr class="align-middle">';
            html += `<td>${dynamicName}</td>`;

            // server append
            html += `<td><div class="form-group"><div class="input-group singleServer"><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select id='${dynamicId}ServerName' class="form-select-modal dynamic_infra dynamic_select" data-type="server" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server">`;

            html += '<option value="" hidden selected>Select Server</option>'
            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'server', '', `${isSrmFound ? 'dresxiserver' : 'DRDBServer'}`, isSrmFound);

            html += `</select></div>`;

            html += `<div class="input-group multipleServer d-none" id='${dynamicId}MultipleServerBody'><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select class="form-select-modal dynamic_infra dynamic_select" id='${dynamicId}MultipleServer' data-type="multipleserver" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server" multiple>`;
            html += `<option value="" hidden selected>Select Server</option>`

            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'multipleserver');

            html += `</select></div><span id="${dynamicId}ServerName-error"></span></div></td>`;

            // srm server data
            html += `<td id='${dynamicId}Srm' class='d-none dynamicSrm'><div class="form-group"><div class="input-group"><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select id='${dynamicId}SrmServer' class="form-select-modal dynamic_infra srmServer" data-type="server" name='SRMServer' data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Server">`;

            html += '<option value="" hidden selected>Select Server</option>'
            html += await SetDynamicServerType("Configuration/InfraObject/GetServerRoleTypeAndServerType", 'dynamic', `${dynamicId}`, 'server', '', 'dresxiappserver', isSrmFound);

            html += `</select></div>`;
            html += `<span id="${dynamicId}SrmServer-error"></span></div></td>`;

            // database append
            html += `<td class='dynamicDatabase'><div class="form-group"><div class="input-group singleDatabase"><span class="input-group-text"><i class="cp-database"></i></span>`;
            html += `<select id='${dynamicId}DatabaseName' class="form-select-modal dynamic_infra dynamic_select dynamic_database" data-type="database" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Database">`;
            html += '<option value="" hidden selected>Select Database</option>'
            html += `</select></div>`;

            html += `<div class="input-group multipleDatabase d-none" id='${dynamicId}MultipleDatabaseBody'><span class="input-group-text"><i class="cp-server"></i></span>`;
            html += `<select class="form-select-modal dynamic_infra dynamic_select" id='${dynamicId}MultipleDatabase' data-type="multipledatabase" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Database" multiple>`;
            html += `<option value="" hidden selected>Select Database</option>`

            html += `</select></div><span id="${dynamicId}DatabaseName-error"></span></div></td>`;

            // replication append
            html += `<td class='dynamicReplication'><div class="form-group"><div class="input-group" style="width: 350px !important;"><span class="input-group-text"><i class="cp-replication-on"></i></span>`;
            html += `<select id='${dynamicId}ReplicationName' class="form-select-modal dynamic_infra dynamic_select dynamic_replication" data-type="replication" data-dynamicName='${dynamicId}' data-siteType='${dynamicName}' data-placeholder="Select Replication">`;

            html += '<option value="" hidden selected>Select Replication</option>'
            html += await getReplicationList("Configuration/InfraObject/GetReplicationList", 'replication');

            html += `</select></div>`;
            html += `<span id="${dynamicId}ReplicationName-error"></span></div></td>`;

        }
    }

    let findDrIndex = siteProperties.length && siteProperties.findIndex((site) => site?.category?.toLowerCase() === 'dr')

    if (findDrIndex === -1) {
        if (serverProperties.hasOwnProperty('DR')) delete serverProperties['DR'];
        if (databaseProperties.hasOwnProperty('DR')) delete databaseProperties['DR'];
        if (replicationProperties.hasOwnProperty('DR')) delete replicationProperties['DR'];
        if (serverProperties.hasOwnProperty('SRMServer')) {
            serverProperties['SRMServer'] = serverProperties?.SRMServer && serverProperties?.SRMServer.length && serverProperties?.SRMServer.filter((item) => item.type !== 'DR')
        }

        $('#serverProperties').val(JSON.stringify(serverProperties))
        $('#databaseProperties').val(JSON.stringify(databaseProperties))
        $('#replicationProperties').val(JSON.stringify(replicationProperties))



        $("#DRCol,#drSrm, #dr_summary_header").hide();
        //$("#infraDatabaseId").val('').trigger('change'); //#drServerName, #DRSelectServerNamesId, #drDatabaseName
        clearSelectAndTriggerChange("DRSelectServerNames", 'DRSelectServerNames-error');
        clearSelectAndTriggerChange("DRMultipleServer", 'DRSelectServerNames-error');
        clearSelectAndTriggerChange("DRMultipleDatabase", 'DRSelectDatabaseNames-error');
        clearSelectAndTriggerChange("DRSelectDatabaseNames", 'DRSelectDatabaseNames-error');
        clearSelectAndTriggerChange("SelectReplicationNames", 'DRSelectReplicationNames-error');
    }

    $('#infraDynamicBody tr:nth-child(2)').after(html);

    if ($('#Activetype').val() === "1" || $('#Activetype').val() === "3") {
        $(".dynamicDatabase").hide();
        $('.dynamic_database').val("");
    } else {
        $(".dynamicDatabase").show();
    }

    if ((ReplicationCategory?.toLowerCase().replace(/ /g, "") === "application-noreplication") || (ReplicationCategory?.toLowerCase().replace(/ /g, "") === "database-noreplication")
        || isRedisFound) {
        $(".dynamicReplication").hide();
        $('.dynamic_replication').val("");
    } else {
        $(".dynamicReplication").show();
    }

    if (!isSrmFound) {
        $('#tableServer').text('Server')
        $('.dynamicSrm').addClass('d-none');
    } else {
        $('#tableServer').text('EXSI Server')
        $('.dynamicSrm').removeClass('d-none');
        $('.dynamicReplication').hide();
        $('.dynamic_replication').val("");
    }

    if (oracleFound) {
        $('.singleServer, .singleDatabase').addClass('d-none');
        $('.multipleServer, .multipleDatabase').removeClass('d-none');
    }


    Object.keys(serverProperties)?.length && Object.keys(serverProperties).map((server) => {
        if (typeof serverProperties[server] == 'object' && serverProperties[server]?.type === 'customserver' && server !== 'clusters') {
            if (!$(`#${serverProperties[server]?.siteId}ServerName`).length) delete serverProperties[server]
            else if (!$(`#${serverProperties[server]?.siteId}MultipleServer`).length) delete serverProperties[server]
        }

        if (server?.toLowerCase() === 'srmserver') {
            serverProperties?.SRMServer.length && serverProperties?.SRMServer.forEach((srm, index) => {
                if (srm?.type !== 'PR' && srm?.type !== 'DR' && !$(`#${srm?.type}SrmServer`).length) serverProperties?.SRMServer.splice(index, 1)
            })
        }
    })

    Object.keys(databaseProperties)?.length && Object.keys(databaseProperties).map((database) => {
        if (databaseProperties[database]?.type === 'customdatabase') {

            if (!$(`#${databaseProperties[database]?.siteId}DatabaseName`).length) delete databaseProperties[database]
            else if (!$(`#${databaseProperties[database]?.siteId}MultipleDatabase`).length) delete databaseProperties[database]
        }
    })

    Object.keys(replicationProperties)?.length && Object.keys(replicationProperties).map((replication) => {
        if (replicationProperties[replication]?.type === 'customreplication' && !$(`#${replicationProperties[replication]?.siteId}ReplicationName`).length) delete replicationProperties[replication]
    })

    setTimeout(() => {

        $('.dynamic_infra').selectize({
            normalize: true,
            openOnFocus: false,
            create: false,
            createOnBlur: true,
            closeAfterSelect: true,
            width: '100%',
            score: function (search) {
                let score = this.getScoreFunction(search);
                return function (item) {
                    return score(item) + (item.text.toLowerCase().indexOf(search.toLowerCase()) + 1) * 1000;
                };
            },

            onDropdownOpen: function ($dropdown) {
                let currrentDrop = $dropdown?.parent()?.siblings('select')[0]?.selectize
                $('.selectize-dropdown').each(function () {
                    let selectizeInstance = $(this).parent().siblings('select')[0]?.selectize;
                    if (selectizeInstance && selectizeInstance.isOpen && selectizeInstance !== currrentDrop) {
                        selectizeInstance.close();
                    }
                });
            },

            onItemAdd: function (value, $item) {
                let selectize = this;
                let selectedValues = selectize?.items;
                setTimeout(function () {
                    selectize?.setValue(selectedValues, true);
                }, 0);
            }

        });

        $('.dynamic_infra').css('width', '100%');

        if (isEdit) {

            Object.keys(serverProperties)?.length && Object.keys(serverProperties).forEach((server) => {
                if (serverProperties[server] && serverProperties[server]?.type == 'customserver') {
                    if (oracleFound && $(`#${serverProperties[server]?.siteId}MultipleServer`)?.length) {

                        multipleServerDetails[server] = serverProperties[server]?.id?.split(',') || [];

                        multipleServerDetails[server].length && multipleServerDetails[server].forEach((data) => {

                            SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', serverProperties[server]?.siteId, 'multipleDatabase', data)
                        })

                        $(`#${serverProperties[server]?.siteId}MultipleServer`)[0].selectize.setValue(multipleServerDetails[server]);

                    } else {
                        if ($(`#${serverProperties[server]?.siteId}ServerName`)?.length) $(`#${serverProperties[server]?.siteId}ServerName`)[0].selectize.setValue(serverProperties[server]?.id);
                    }
                } else if (server?.toLowerCase() == 'srmserver') {

                    serverProperties?.SRMServer.length && serverProperties?.SRMServer.forEach((srm) => {
                        if (srm.type !== 'PR' && srm.type !== 'DR') {
                            if ($(`#${srm?.type}SrmServer`)[0]) $(`#${srm?.type}SrmServer`)[0].selectize.setValue(srm?.id);
                        }
                    })
                }
            })

            Object.keys(replicationProperties)?.length && Object.keys(replicationProperties).forEach((replication) => {
                if (replicationProperties[replication] && $(`#${replicationProperties[replication]?.siteId}ReplicationName`)?.length && replicationProperties[replication]?.type) {
                    $(`#${replicationProperties[replication]?.siteId}ReplicationName`)[0].selectize.setValue(replicationProperties[replication]?.id);
                }
            })
        }

        form.steps('next');

    }, 200);
}

$(function () {

    // --- Update --->
    $('#InfraObjectList').on('click', '.edit-button', function () {
        btnCrudEnable('SaveFunction');
        $("#steps-uid-0-t-0, #infraObjectWizard-t-0").trigger("click");
        isEdit = true
        infraData = $(this).data('infra');
        //   $(`#SelectPairInfra option, #SelectAssociate option`).prop('disabled', false);
        clearInputInfraFields();
        getServerRole();
        GetdabaseNames()
        populateModalFields(infraData);
        $('#SaveFunction').text('Update')
        $("#ModelSave").text('Update')
        
        if (infraData?.state !== "Active") {
            $('#CreateModal').modal('show')
        }
    });

    // --- delete --->
    $('#InfraObjectList').on('click', '.delete-button', function () {
        let infraDetails = $(this).data('infra');

        if (infraDetails) {
            $('#deleteData').text(infraDetails?.name).attr('title', infraDetails?.name);
            $('#textDeleteId').val(infraDetails?.id);

            if (infraDetails?.state !== "Active") {
                $('#DeleteModal').modal('show')
            } else {
                if (!$(this).hasClass('icon-disabled')) $(this).addClass('icon-disabled');
            }
        }
    });

    // --- Form Submit ---> 
    $("#SaveFunction").on('click', commonDebounce(function () {

        let form = $("#CreateForm");
        form.trigger('submit');
        btnCrudDiasable('SaveFunction');

    }, 800));

    // --- Create --->
    $('#infraObject-createbutton').on('click', function () {
        btnCrudEnable('SaveFunction');
        $("#steps-uid-0-t-0, #infraObjectWizard-t-0").trigger("click");
        $("#DataTypeCol, #pairinfra, #associate").hide();
        clearInputInfraFields();
        getServerRole();
        isEdit = false;
        $("#ModelSave").text('Save')
        $("#Activetype").val('');
        infraData = {}
        siteProperties = []
        $(`#SelectPairInfra option, #SelectAssociate option`).prop('disabled', false);
        $('#CreateModal').modal('show')
    });

    //InfraObject Name
    $('#textName').on('keyup', commonDebounce(async function () {
        let infraId = $('#infraNameId').val();
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ')
        $(this).val(sanitizedValue);
        await validateName(value, infraId, IsNameExist);
    }, 500));

    $('#Description').on('keyup keypress', async function (event) {
        const value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ')
        $(this).val(sanitizedValue);
        if (exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
        }
        let errorElement = $('#Description-error');
        await validateDescription(value, "Should not allow more than 250 characters", errorElement);
    });

    //BusinessService
    $('#infraBusinessServiceId').on('change', function () {
        const value = $(this)?.val();
        let id = $(this)?.children(":selected")?.attr("id");
        let getSiteProperties = $(this)?.children(":selected")?.attr('siteproperties');
        $('#BusinessServiceId').val(id);
        validateDropDown(value, ' Select operational service', 'BusinessService-error');
        if (id) {
            $('#siteType-error').text('').removeClass('field-validation-error');
            $('#siteType').empty();
            SetBusinessFunction(id);
            if (getSiteProperties) getSiteDetailsByOperationService(getSiteProperties)
        }
    });

    // --- Active Type --->
    $('#Activetype').on('change', async function () {
        const value = $(this).val();
        activeType = $('#Activetype option:selected').text();
        $("#ActiveTypeName").val(activeType);
        $("#DataTypeCol").hide();
        SetPRServerType(activeType, 'production')
        SetDRServerType(activeType)
        activityTypeEnable(value)
        Activitydata = value
        if (value === "1" || value === "3") {
            $("#prdatabase, #drdatabase, #tabledatabase").hide();
        } else {
            $("#prdatabase, #drdatabase, #tabledatabase").show();
        }

        dynamicElements.forEach((id) => $(id).val("").trigger('change'));
        serverProperties = {};
        databaseProperties = {};
        replicationProperties = {};
        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };

        $('#ddlReplicationTypeNameId, #SelectReplicationType').empty();
        SetReplicationMaster(activeType)

        dynamicErrorElements.forEach((id) => $(id).text('').removeClass('field-validation-error'));

        validateDropDown(value, ' Select activity type', 'SelectActiveType-error');
    })
    function activityTypeEnable(value) {

        if (value == '2') {
            $("#DataTypeCol").show();
            GetdabaseNames()
        } else {
            $('#DatabaseId, #databaseText, #SelectDatabaseType').val('');
            $('#SelectDatabaseType').empty().append('<option value=""> Select Database Type </option>');
            $("#DataTypeCol").hide();
        }

    }

    // --- DatabaseType --->
    $("#SelectDatabaseType").on('change', function () {
        const value = $(this).val();
        const databaseText = $('#SelectDatabaseType option:selected').text();
        database = databaseText;
        databaseType = value;
        $('#databaseText').val(databaseText)
        $('#DatabaseId').val(value)
        $('#ddlReplicationTypeNameId').empty().append('<option value=""> Select Replication Type </option>');
        $('#SelectReplicationType').append('<option value="" selected > Select Replication Category </option>');
        validateDropDown(value, "Select Database type", "DatbaseType-error");
    });

    // ---- ReplicationType --->
    $('#ddlReplicationTypeNameId').on('change', function () {
        const value = $(this).val();
        $('#ReplicationTypeId').val(value);

        ReplicationtypeName = $('#ddlReplicationTypeNameId option:selected').text();
        let clusterFound = clusterReplicationData && clusterReplicationData.some((rep) => (ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes(rep)))
        let isOracleFound = getOracleFound();
        let ismssqlFound = getMssqlFound()
        if (ReplicationtypeName && ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes('srmvmware')) {
            SetPRServerType(activeType, 'production', 'presxiserver', false, true)
            SetDRServerType(activeType, 'dresxiserver', false, true)
            SetSrmServerType(activeType, 'presxiappserver', 'production')
            $('#tablesrm, #prSrm, #clusterCol').show();
            $('#cluster').prop('checked', false);
            $('#clusterType, #clusterPR, #clusterDR').val('').trigger('change');
            $('#selectedClusterCol, #clusterProperties,#DRReplication, #PRReplication,#tablereplication').hide()         
            $('#SelectReplicationNames').val(""); 
            if ($('#DREnable').prop('checked')) $('#drSrm').show();

        } else if (ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes('redis')) {

            $("#DRReplication, #PRReplication,#tablereplication").hide();
            $('#SelectReplicationNames').val(""); 

        } else if (ReplicationtypeName && clusterFound) {

            $('#clusterCol').show();
            $('#cluster').prop('checked', false);
            $('#selectedClusterCol, #clusterProperties').hide();

        } else {
            $('#tablesrm, #prSrm, #drSrm, #clusterCol, #selectedClusterCol, #clusterProperties').hide();
        }

        if (isOracleFound) {

            $('.PRDatabaseName, .DRDatabaseName').empty();
            $('#PRServerBody, #PRDatabaseBody, #DRServerBody, #DRDatabaseBody').addClass('d-none')
            $('#PRMultipleServerBody, #PRMultipleDatabaseBody, #DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')

        }
        else {

            $('#PRServerBody, #PRDatabaseBody, #DRServerBody, #DRDatabaseBody').removeClass('d-none')
            $('#PRMultipleServerBody, #PRMultipleDatabaseBody, #DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
        }

        if (ismssqlFound) {

            $('.PRDatabaseName, .DRDatabaseName').empty();
            $(' #DRServerBody, #DRDatabaseBody,#PRReplication,#DRReplication,#tablereplication').addClass('d-none')
            $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')

        }
        else {

            $('#DRServerBody, #DRDatabaseBody,#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')
            $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
        }


        $('#ReplicationTypeName').val(ReplicationtypeName)

        serverProperties = {};
        databaseProperties = {};
        replicationProperties = {};

        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };

        dynamicElements.forEach((id) => $(id).val("").trigger('change'));
        dynamicErrorElements.forEach((id) => $(id).text('').removeClass('field-validation-error'));
        validateDropDown(value, ' select replication type', 'SelectReplication-error');
    });

    // --- Replication Category --->
    $('#SelectReplicationType').on('change', async function () {
        const value = $(this).val();
        ReplicationCategory = $('#SelectReplicationType option:selected').text();
        replicationType = $(this).children(":selected").attr("value");
        $('#infraReplicationTypeId').val(replicationType)
        $('#ReplicationCategorytext').val(ReplicationCategory)
        SetReplicationMapping();
        validateDropDown(value, "Select replication category", "SelectReplicationType-error");

        if ((ReplicationCategory === "Application - No Replication") || (ReplicationCategory === "Database - No Replication")) {
            $("#DRReplication, #PRReplication,#tablereplication").hide();
            $('#SelectReplicationNames').val(""); //#PRSelectReplicationName,
        } else {
            $("#DRReplication, #PRReplication,#tablereplication").show();
            $('#SelectReplicationName-error, #DRSelectReplicationNames-error').text('').removeClass('field-validation-error');
        }

        dynamicElements.forEach((id) => $(id).val("").trigger('change'));
        dynamicErrorElements.forEach((id) => $(id).text('').removeClass('field-validation-error'));
    })

    // --- Pairinfra or Associate --->
    $(document).on('change', '.pair', function (e) {
        let values = $(this).val();
        if (values === "IsPair") {
            if (e.target.checked) {
                clearSelectAndTriggerChange("SelectPairInfra", "SelectPairInfra-error");
                clearSelectAndTriggerChange("PairInfraId", "SelectPairInfra-error");
                $('#AssociateValue').val(false);
                $('#InfraId').prop('checked', false);
                $("#associate").hide();
                $("#pairinfra").show();
                $('#pairValue').val(true)
            } else {
                $("#pairinfra").hide();
                $('#pairValue').val(false)
            }
        } else if (values === "IsAssociate") {
            if (e.target.checked) {
                clearSelectAndTriggerChange("SelectAssociate", "SelectAssociate-error");
                clearSelectAndTriggerChange("AssociateId", "SelectAssociate-error");
                $('#pairValue').val(false)
                $('#AssociateValue').val(true);
                $('#PairId').prop('checked', false);
                $("#pairinfra").hide();
                $("#associate").show();
            } else {
                $("#associate").hide();
                $('#AssociateValue').val(false);
            }
        }
    });

    $('#drift').on('change', function (e) {
        if (e.target.checked) $('#driftValue').val(true)
        else $('#driftValue').val(false)
    })

    // --- Priority --->
    $('input[name="Priority"]').on('change', function () {
        let selectedValue = $('input[name="Priority"]:checked').val();
        $('#PrioritySum').text(selectedValue);
    });

    // --- PRServer --->
    $('#SelectServerName, #PRMultipleServer').on('change', function () {
        const value = $(this).val();
        let getIds = []
        let id = ''

        let isMssqlAG = getMssqlFound()

        if ($(this).attr('id') === 'PRMultipleServer') {

            $('#PRMultipleServer option:selected').each(function () {
                id = $(this).attr('prSId');

                if (id) {
                    getIds.push(id)

                    if (!multipleServerDetails?.multiplePRServer.includes(id)) {
                        multipleServerDetails?.multiplePRServer.push(id)

                        validateDropDown(id, ' select PRServer', 'SelectServerName-error');
                        if ($("#Activetype").val() !== '1') SetServerID(id, value, 'multiplePR');
                    }

                }
            });

            if (multipleServerDetails?.multiplePRServer.length) {
                //$('#infrServerNameId').val(multipleServerDetails?.multiplePRServer.join(','));
                appendDynamicProperties(getIds?.join(','), 'PR', value.join(','), serverProperties, 'PRDBServer');
            } else {
                //$('#infrServerNameId'); //, #prServerName').val('');
                //$('#prDatabaseName, #PRSelectDatabaseId').val('');
            }

            //if (value.length) $('#prServerName').val(value.join(','));

        } else {
            let obj = {}
            prServer = $('#SelectServerName option:selected').text();

            id = $('#SelectServerName option:selected').attr('prSId');

            if (isMssqlAG) obj['currentPR'] = true
            //$('#prServerName').val(prServer)
            //$('#infrServerNameId').val(id)

            appendDynamicProperties(id, 'PR', prServer, serverProperties, 'PRDBServer', '', isMssqlAG ? obj : '')

            validateDropDown(value, ' select PRServer', 'SelectServerName-error');

            if ($("#Activetype").val() !== '1') SetServerID(id, value);

        }
    });

    $('#PRMultipleServer, #DRMultipleServer').on('select2:unselecting select2:unselect', function (e) {

        if ($(this).attr('id') === 'PRMultipleServer') {
            let value = e.params.args ? e.params.args.data?.element?.getAttribute('prsid') : e.params.data?.element?.getAttribute('prsid');

            if (multipleServerDetails?.multiplePRServer.includes(value)) {
                let findIndex = multipleServerDetails?.multiplePRServer.indexOf(value)

                if (findIndex !== -1) multipleServerDetails?.multiplePRServer.splice(findIndex, 1);

                $('#PRMultipleDatabase option[prServerId="' + value + '"]').remove();
            }

            $('#PRMultipleServer').select2('close');
        } else {
            let value = e.params.args ? e.params.args.data?.element?.getAttribute('drsid') : e.params.data?.element?.getAttribute('drsid');

            if (multipleServerDetails?.multipleDRServer.includes(value)) {
                let findIndex = multipleServerDetails?.multipleDRServer.indexOf(value)

                if (findIndex !== -1) multipleServerDetails?.multipleDRServer.splice(findIndex, 1);

                $('#DRMultipleDatabase option[drServerId="' + value + '"]').remove();
            }

            $('#DRMultipleServer').select2('close');
        }

    });


    // --- DRServer --->
    $('#DRSelectServerNames, #DRMultipleServer').on('change', function () {
        const value = $(this).val();
        let getIds = [];
        let id = ''

        let isMssqlAG = getMssqlFound()

        if ($(this).attr('id') === 'DRMultipleServer') {
            let obj = {}

            if (isMssqlAG) obj["currentPRDetails"] = []

            $('#DRMultipleServer option:selected').each(function () {
                id = $(this).attr('drSId');

                if (id) {
                    getIds.push(id)

                    if (obj?.currentPRDetails) {
                        obj?.currentPRDetails?.push({ id, name: $(this).text(), currentPR: false })
                    }

                    if (!multipleServerDetails?.multipleDRServer.includes(id)) {

                        multipleServerDetails?.multipleDRServer.push(id)

                        validateDropDown(id, ' select Server Name', 'DRSelectServerNames-error');
                        if ($("#Activetype").val() !== '1') SetDrServerID(id, 'multipleDR');

                    }
                }
            });

            if (multipleServerDetails?.multipleDRServer.length) {
                //$('#DRSelectServerNamesId').val(multipleServerDetails?.multipleDRServer.join(','));
                appendDynamicProperties(getIds?.join(','), 'DR', value.join(','), serverProperties, 'DRDBServer', '', obj);
            } else {
                //$('#DRSelectServerNamesId').val(''); //#drServerName
                //$('#drDatabaseName, #infraDatabaseId').val('');
            }

            //if (value.length) $('#drServerName').val(value.join(','));

        } else {

            let drName = $('#DRSelectServerNames option:selected').text();

            id = $('#DRSelectServerNames option:selected').attr('drSId');;

            if (id) {
                //$('#drServerName').val(drName)
                //$('#DRSelectServerNamesId').val(id);

                appendDynamicProperties(id, 'DR', value, serverProperties, 'DRDBServer')

                validateDropDown(value, ' select Server Name', 'DRSelectServerNames-error');

                if ($("#Activetype").val() !== '1') SetDrServerID(id);
            }

        }
    });

    // --- PR Database --->
    $('#PRSelectDatabase, #PRMultipleDatabase').on('change', function () {

        const value = $(this).val();

        if ($(this).attr('id') === 'PRMultipleDatabase') {
            let prType = []
            multipleDatabaseDetails['multiplePRDatabase'] = [];

            $('#PRMultipleDatabase option:selected').each(function () {
                let id = $(this).attr('prDId');
                let type = $(this).data('type');

                if (!prType.includes(type)) prType.push(type);

                if (!multipleDatabaseDetails?.multiplePRDatabase.includes(id) && id) {

                    multipleDatabaseDetails?.multiplePRDatabase.push(id);

                    validateDropDown(id, ' select database name', 'PRSelectDatabase-error');
                }
            });

            if (multipleDatabaseDetails?.multiplePRDatabase.length) {
                appendDynamicProperties(multipleDatabaseDetails?.multiplePRDatabase.join(','), 'PR', value.join(','), databaseProperties, prType?.join(','))
                //$('#PRSelectDatabaseId').val(multipleDatabaseDetails?.multiplePRDatabase.join(','));
            }
            //if (value.length) $('#prDatabaseName').val(value.join(','));

        } else {

            const value = $('#PRSelectDatabase option:selected').text();

            let id = $('#PRSelectDatabase option:selected').attr('prDId');
            let type = $('#PRSelectDatabase option:selected').data('type');

            if (id) {
                //$('#prDatabaseName').val(value)
                // $('#PRSelectDatabaseId').val(id)

                appendDynamicProperties(id, 'PR', value, databaseProperties, type)
                validateDropDown(value, ' select database name', 'PRSelectDatabase-error');
            }

        }

    })

    // --- DR Database --->
    $('#DRSelectDatabaseNames, #DRMultipleDatabase').on('change', function () {

        const value = $(this).val();

        if ($(this).attr('id') === 'DRMultipleDatabase') {
            let drType = []
            multipleDatabaseDetails['multipleDRDatabase'] = [];

            $('#DRMultipleDatabase option:selected').each(function () {
                let id = $(this).attr('drDId');
                let type = $(this).data('type');

                if (!drType.includes(type)) drType.push(type);

                if (!multipleDatabaseDetails?.multipleDRDatabase.includes(id) && id) {

                    multipleDatabaseDetails?.multipleDRDatabase.push(id);

                    validateDropDown(id, 'select database name', 'DRSelectDatabaseNames-error');
                }
            });

            if (multipleDatabaseDetails?.multipleDRDatabase.length) {
                appendDynamicProperties(multipleDatabaseDetails?.multipleDRDatabase.join(','), 'DR', value.join(','), databaseProperties, drType?.join(','))
                //$('#infraDatabaseId').val(multipleDatabaseDetails?.multipleDRDatabase.join(','));
            }
            //if (value.length) $('#drDatabaseName').val(value.join(','));

        } else {

            const value = $('#DRSelectDatabaseNames option:selected').text();

            let id = $('#DRSelectDatabaseNames option:selected').attr('drDId');
            let type = $('#DRSelectDatabaseNames option:selected').data('type');

            if (id) {
                //$('#drDatabaseName').val(value)
                //$('#infraDatabaseId').val(id)

                appendDynamicProperties(id, 'DR', value, databaseProperties, type)
                validateDropDown(value, 'select database name', 'DRSelectDatabaseNames-error');
            }
        }
    })

    $('#PRMultipleDatabase, #DRMultipleDatabase').on('select2:unselecting select2:unselect', function (e) {

        if ($(this).attr('id') === 'PRMultipleDatabase') {
            let value = e.params.args ? e.params.args.data?.element?.getAttribute('prdid') : e.params.data?.element?.getAttribute('prdid');
            let selectedText = e.params.args ? e.params.args.data?.text : e.params.data?.text;

            if (multipleDatabaseDetails?.multiplePRDatabase.includes(value)) {
                let findIndex = multipleDatabaseDetails?.multiplePRDatabase.indexOf(value)

                if (findIndex !== -1) multipleDatabaseDetails?.multiplePRDatabase.splice(findIndex, 1);

            }

            //let prDatabaseNameArray = ""//$('#prDatabaseName').val().split(',');
            //let selectedTextIndex = prDatabaseNameArray.length && prDatabaseNameArray.indexOf(selectedText?.trim());
            let prDatabaseNameArray = $('#PRMultipleDatabase').val()

            // Find the index of selectedText (after trimming the value)
            let selectedTextIndex = prDatabaseNameArray && prDatabaseNameArray.indexOf(selectedText?.trim());

            if (selectedTextIndex !== -1) {
                prDatabaseNameArray.splice(selectedTextIndex, 1);
            }

            //$('#prDatabaseName').val(prDatabaseNameArray.join(','));
            //$('#PRSelectDatabaseId').val(multipleDatabaseDetails?.multiplePRDatabase.join(','));

            $('#PRMultipleDatabase').select2('close');
        } else {
            let value = e.params.args ? e.params.args.data?.element?.getAttribute('drdid') : e.params.data?.element?.getAttribute('drdid');
            let selectedText = e.params.args ? e.params.args.data?.text : e.params.data?.text;

            if (multipleDatabaseDetails?.multipleDRDatabase.includes(value)) {
                let findIndex = multipleDatabaseDetails?.multipleDRDatabase.indexOf(value)

                if (findIndex !== -1) multipleDatabaseDetails?.multipleDRDatabase.splice(findIndex, 1);

            }

            //let drDatabaseNameArray = "" // $('#drDatabaseName').val().split(',');
            //let selectedTextIndex = drDatabaseNameArray.length && drDatabaseNameArray.indexOf(selectedText?.trim());
            let drDatabaseNameArray = $('#DRMultipleDatabase').val();

            // Find the index of the selectedText (if it's in the array)
            let selectedTextIndex = drDatabaseNameArray && drDatabaseNameArray.indexOf(selectedText?.trim());
            if (selectedTextIndex !== -1) {
                drDatabaseNameArray.splice(selectedTextIndex, 1);
            }

            //$('#drDatabaseName').val(drDatabaseNameArray.join(','));
            //$('#infraDatabaseId').val(multipleDatabaseDetails?.multipleDRDatabase.join(','));

            $('#DRMultipleDatabase').select2('close');
        }
    });

    // --- Business Function --->
    $('#ddlbusinessFunctionId').on('change', function () {
        const value = $('#ddlbusinessFunctionId option:selected').text();
        const id = $(this).val();
        $('#BusinessFunctionVal').val(value)
        $('#BusinessFunctionId').val(id)
        validateDropDown(value, ' select Database Name', 'BusinessFunction-error');
    })

    // ---  PR Replication --->
    $('#PRSelectReplicationName').on('change', function () {
        const value = $('#PRSelectReplicationName option:selected').text();
        const id = $(this).children(":selected").attr('prRId');
        $('#PRReplicationName').val(id)
        appendDynamicProperties(id, 'PR', value, replicationProperties)
        validateDropDown(value, 'select database name', 'SelectReplicationName-error');
    })

    // ---  DR Replication --->
    $('#SelectReplicationNames').on('change', function () {
        const value = $('#SelectReplicationNames option:selected').text();
        const id = $(this).children(":selected").attr('drRId');
        $('#drReplicationNameId').val(id)

        appendDynamicProperties(id, 'DR', value, replicationProperties)
        validateDropDown(value, 'select replication category', 'DRSelectReplicationNames-error');
    })

    $('#siteType').on('change', function () {
        let selectedOptions = $(this).find('option:selected');
        siteProperties = []

        selectedOptions.length && selectedOptions.each(async function () {
            let getId = $(this).val();
            let getText = $(this).data('sitetypename');
            let category = $(this).data('category');

            let duplicateFound = siteProperties.length && siteProperties.some(data => data.id === getId)

            if (!duplicateFound) {
                let obj = { id: getId, name: getText, category: category }

                siteProperties.push(obj)
            }
        });
    })

    $('#siteType').on('select2:unselecting select2:unselect', function (e) {

        let value = e.params.args ? e.params.args.data?.element?.dataset?.category : e.params.data?.element?.dataset?.category;
        if (value?.toLowerCase() === "primary") e.preventDefault();

    });

    //$('#siteType').select2({
    //    minimumResultsForSearch: Infinity // Completely removes the search field
    //});

    $('#siteType').on('select2:open', function () {
        // Locate the search field inside the Select2 dropdown
        const searchField = document.querySelector('.select2-search__field');
        if (searchField) {
            searchField.remove();
        }
    });

    //$('#siteType').on('select2:open', function () {
    //    const searchField = document?.querySelector('.select2-search__field');

    //    if (searchField) {
    //        searchField?.removeEventListener('keydown', handleBackspace);
    //        searchField?.addEventListener('keydown', handleBackspace);
    //    }
    //});    

    //const handleBackspace = (event) => {
    //    if (event?.key === 'Backspace') {
    //        const searchField = event?.target;

    //        setTimeout(() => {
    //            searchField.value = searchField?.value?.trim();
    //        }, 0);
    //    }
    //}


    /*Srm servers onchange*/
    $(document).on('change', '.srmServer', async function (e) {
        const inputName = e.target.name
        const inputType = $(this).attr('type');

        if (inputType == 'checkbox' && inputName == 'cluster') {
            $('#clusterProperties').toggle(e.target.checked);
            serverProperties["isCluster"] = e.target.checked;

            if (!e.target.checked) {
                $('#clusterType').val('').trigger('change');
                $('#selectedClusterCol').hide();
                return;
            }

        } else if (inputName == "cluster") {

            if (e.target.value == "veritas") {
                getVeritasHacmpList("veritas")
                $(`#${e.target.value}-child`).show();
                $('#HACMP-child').hide();
            } else {
                getVeritasHacmpList("hacmp")
                $(`#${e.target.value}-child`).show();
                $('#veritas-child').show();
            }

            serverProperties["clusterType"] = e.target.value
            serverProperties["clusters"] = []
            $('#clusterPR, #clusterDR').val('').trigger('change');
            $('#clusterPR-error, #clusterDR-error').removeClass('field-validation-error').text('')
            $('#selectedClusterCol').show();

        } else if (inputName == "clusters") {

            let clusterChildId = $(this).attr('id')
            let clusterName = $(this).children(":selected").text();
            let clusterType = $(this).children(":selected").attr('clustertype')

            if ($('#clusterType').val() == 'HACMP') {
                if (clusterChildId.includes('clusterPR')) {
                    $('#clusterDR option[value="' + e.target.value + '"]').prop('disabled', true);
                    $('#clusterDR option').not('[value="' + e.target.value + '"]').prop('disabled', false);

                } else if (clusterChildId.includes('clusterDR')) {
                    $('#clusterPR option[value="' + e.target.value + '"]').prop('disabled', true);
                    $('#clusterPR option').not('[value="' + e.target.value + '"]').prop('disabled', false);

                }
            }

            appendDynamicProperties(e.target.value, inputName, clusterName, serverProperties, clusterType)

        } else {
            let srmName = $(this).find(":selected").text()
            let srmType = $(this).children(":selected").attr('srmtype')
            let id = $(this).data('dynamicname');

            if (srmName) appendDynamicProperties(e.target.value, inputName, srmName, serverProperties, srmType ? srmType : id)
        }

        if (e.target.value) validateDropDown(e.target.value, ' select server ', $(this).attr('id') + '-error');
    })

    $(document).on('change', '.dynamic_select', async function (e) {

        let value = $(this).val();
        let selectTagType = $(this).data('type')
        let selectTagDynamicId = $(this).data('dynamicname');
        let selectTagDynamicType = $(this).data('sitetype');
        let id = $(this).attr('id');
        let selectedText = $(this)?.find('option:selected')?.map(function () {
            return $(this).text();
        }).get().join(', ');


        if (selectTagType == 'multipleserver' && id) {

            if (!multipleServerDetails.hasOwnProperty(selectTagDynamicId)) multipleServerDetails[selectTagDynamicId] = []

            const selectedOptions = $(`#${id} option:selected`);
            const getIds = []
            const removedOptions = multipleServerDetails[selectTagDynamicId].filter(optionId => !selectedOptions.map((_, el) => $(el).val()).get().includes(optionId));
            selectedOptions.each(async function () {
                let optionId = $(this).val();

                if (optionId) {

                    getIds?.push(optionId);

                    if (multipleServerDetails[selectTagDynamicId] && !multipleServerDetails[selectTagDynamicId].includes(optionId)) {
                        multipleServerDetails[selectTagDynamicId].push(optionId)

                        validateDropDown(optionId, 'select server', `${selectTagDynamicId}ServerName-error`);

                        if ($("#Activetype").val() !== '1') {
                            await SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', selectTagDynamicId, 'multipleDatabase', optionId)
                        }
                    }

                }
            });

            if (removedOptions.length) {

                removedOptions.forEach(removedOptionId => {
                    multipleServerDetails[selectTagDynamicId] = multipleServerDetails[selectTagDynamicId].filter(optionId => optionId !== removedOptionId);
                    let selectize = $(`#${selectTagDynamicId}MultipleDatabase`)[0].selectize;

                    for (const [value, data] of serverDataMap.entries()) {
                        if (data.serverId === removedOptionId) {
                            selectize.removeOption(value);

                            $(`#${selectTagDynamicId}MultipleDatabase option[value='${value}']`).remove();

                            serverDataMap.delete(value);
                        }
                    }
                });

            }

            if (multipleServerDetails[selectTagDynamicId] && multipleServerDetails[selectTagDynamicId].length) {
                appendDynamicProperties(getIds?.join(','), selectTagDynamicId, selectedText, serverProperties, 'customserver', selectTagDynamicType?.trim());
            }

        } else if (selectTagType == 'multipledatabase' && id) {

            if (!multipleDatabaseDetails.hasOwnProperty(selectTagDynamicId)) multipleDatabaseDetails[selectTagDynamicId] = [];
            else multipleDatabaseDetails[selectTagDynamicId] = []

            const selectedOptions = $(`#${id} option:selected`);
            selectedOptions.each(function () {
                let optionId = $(this).val();

                if (multipleDatabaseDetails[selectTagDynamicId] && !multipleDatabaseDetails[selectTagDynamicId].includes(optionId) && optionId) {

                    multipleDatabaseDetails[selectTagDynamicId].push(optionId)

                    validateDropDown(optionId, 'select database', `${selectTagDynamicId}DatabaseName-error`);

                }
            });

            multipleDatabaseDetails[selectTagDynamicId] = multipleDatabaseDetails[selectTagDynamicId].filter(optionId => selectedOptions.map((_, el) => $(el).val()).get().includes(optionId));

            if (multipleDatabaseDetails[selectTagDynamicId] && multipleDatabaseDetails[selectTagDynamicId].length) {
                appendDynamicProperties(multipleDatabaseDetails[selectTagDynamicId].join(','), selectTagDynamicId, selectedText, databaseProperties, 'customdatabase', selectTagDynamicType?.trim());
            }

        } else if (selectTagType == 'server') {

            await SetDynamicDatabaseType("Configuration/InfraObject/GetDatabase", 'dynamic', selectTagDynamicId, 'database', value)

            appendDynamicProperties(value, selectTagDynamicId, selectedText, serverProperties, 'customserver', selectTagDynamicType?.trim())

            selectTagDynamicId = `${selectTagDynamicId}ServerName`;

        } else if (selectTagType == 'database') {

            appendDynamicProperties(value, selectTagDynamicId, selectedText, databaseProperties, 'customdatabase', selectTagDynamicType?.trim())

            selectTagDynamicId = `${selectTagDynamicId}DatabaseName`;

        } else {
            appendDynamicProperties(value, selectTagDynamicId, selectedText, replicationProperties, 'customreplication', selectTagDynamicType?.trim())

            selectTagDynamicId = `${selectTagDynamicId}ReplicationName`;
        }

        if (value) validateDropDown(value, `Select ${selectTagType}`, `${selectTagDynamicId}-error`);

    });

    const appendDynamicProperties = (value, inputName, addValue, dynamicProperties, serverType = '', siteType = '', mssqlAGDetails = {}) => {

        if (value) {

            let obj = { id: value, name: addValue };
            if (serverType) obj['type'] = serverType

            if (siteType) obj['siteId'] = inputName

            if (Object?.keys(mssqlAGDetails)?.length) obj = { ...obj, ...mssqlAGDetails }

            if (inputName?.toLowerCase() == 'srmserver' || inputName?.toLowerCase() == 'clusters') {
                let propertyName = inputName?.toLowerCase() == 'srmserver' ? 'SRMServer' : 'clusters'

                dynamicProperties[propertyName] = dynamicProperties[propertyName] || [];

                const existingIndex = dynamicProperties[propertyName].findIndex(prop => prop.type === serverType);

                if (existingIndex !== -1) {
                    dynamicProperties[propertyName][existingIndex] = obj;
                } else {
                    dynamicProperties[propertyName].push(obj);
                }

            } else {
                if (siteType) {

                    Object.keys(dynamicProperties).length && Object.keys(dynamicProperties).forEach((key) => {
                        if (dynamicProperties[key]?.siteId && dynamicProperties[key]?.siteId === inputName) {
                            delete dynamicProperties[key];
                        }
                    });

                    dynamicProperties[siteType] = obj;
                } else {
                    dynamicProperties[inputName] = obj;
                }
            }

        }
    }

    // ---Select Associate --->
    $('#SelectAssociate').on('change', function () {

        const value = $('#SelectAssociate option:selected').text();
        const id = $(this).children(":selected").attr('Acid');
        $('#AssociateId').val(id)
        validateDropDown(value, 'Select associate ', 'SelectAssociate-error');
    })

    // --- Select PairInfra --->
    $('#SelectPairInfra').on('change', function () {
        const value = $('#SelectPairInfra option:selected').text();
        const id = $(this).children(":selected").attr('infraid');
        $('#PairInfraId').val(id)
        validateDropDown(value, 'Select pairInfra', 'SelectPairInfra-error');
    })

    const checkDynamicDataValidation = () => {
        let formIsValid = true;

        $('.dynamic_infra').each(function () {
            let value = $(this).val();
            let getId = $(this).attr('id');
            let getName = $(this).attr('name')
            let getDynamicName = $(this).data('dynamicname');
            let selectTagType = $(this).data('type')
            let isValueEmpty = Array.isArray(value) ? !value.length : !value;

            if (isValueEmpty && getId && $(`#${getId}`).is('select') && !$(this)?.parent()?.hasClass('d-none')) {
                if ($(`#${getId}`).closest('td').is(':visible')) {
                    formIsValid = false;

                    if (getName && getName?.toLowerCase() === 'srmserver') $(`#${getDynamicName}SrmServer-error`).text('Select server').addClass('field-validation-error')
                    else if (selectTagType === 'server' || selectTagType === 'multipleserver') $(`#${getDynamicName}ServerName-error`).text('Select server').addClass('field-validation-error')
                    else if (selectTagType === 'database' || selectTagType === 'multipledatabase') $(`#${getDynamicName}DatabaseName-error`).text('Select database').addClass('field-validation-error')
                    else $(`#${getId}-error`).text('Select replication').addClass('field-validation-error')
                }
            }
        });

        return formIsValid;
    }

    $("#Next").on('click', commonDebounce(async function () {

        let currentLi = $('.steps ul li.current')
        let getReplicationName = $('#ddlReplicationTypeNameId option:selected').text();
        let isSrmFound = getReplicationName && ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes('srmvmware')
        let isOracleFound = getOracleFound();
        let ismssqlFound = getMssqlFound()
        let isClusterFound = getReplicationName && clusterReplicationData && clusterReplicationData.some((rep) => (ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes(rep)))

        //   $('#PRClusterSum, #DRClusterSum, #PRSrmServerSum, #DRSrmServerSum').hide()

        if (currentLi.hasClass('first')) {
            let activeType = $("#Activetype").val()
            let pairInfraValue = $('#PairId').prop('checked') ? $('#PairId').prop('checked') : false;
            let associateValue = $('#InfraId').prop('checked') ? $('#InfraId').prop('checked') : false;

            let isName = await validateName($("#textName").val(), $('#infraNameId').val());
            elementDescription = $("#Description").val()
            errorElement = $('#Description-error');

            let isDescription = await validateDescription(elementDescription, "Between 3 to 250 characters", errorElement);
            let isAssociate = associateValue === true ? validateDropDown($("#SelectAssociate").val(), ' Select associate infraobject', "SelectAssociate-error") : true;
            let isActiveType = validateDropDown(activeType, ' Select activity type', "SelectActiveType-error");
            let isPairInfra = pairInfraValue === true ? await validateDropDown($("#SelectPairInfra").val(), ' Select pair infraobject', "SelectPairInfra-error") : true;
            let isReplication = $("#ReplicationTypeInput").is(":visible") ? validateDropDown($("#ddlReplicationTypeNameId").val(), ' Select replication type', 'SelectReplication-error') : true;
            let isSiteType = validateDropDown(siteProperties, ' Select site type', 'siteType-error');
            let isBusinessService = validateDropDown($("#infraBusinessServiceId").val(), ' Select operational service ', 'BusinessService-error');
            let isBusinessFunction = validateDropDown($("#ddlbusinessFunctionId").val(), ' Select  operational function', 'BusinessFunction-error');
            let isReplicationType = validateDropDown($("#SelectReplicationType").val(), "Select replication category", "SelectReplicationType-error");
            let isDatabaseType = activeType === "2" ? validateDropDown($("#SelectDatabaseType").val(), "Select database type ", "DatbaseType-error") : true;
            let siteData = siteProperties.length && siteProperties.map(site => site.name).join(',')

            if (isName && isDescription && isBusinessService && isBusinessFunction && isReplication && isReplicationType && isDatabaseType && isActiveType && isSiteType) {
                if (isPairInfra && isAssociate) {

                    loadDynamicSites();

                    dynamicErrorElements.forEach((id) => $(id).text('').removeClass('field-validation-error'));
                    if (!isSrmFound) {
                        $('#tablesrm, #prSrm, #drSrm, #nearSrm').hide();
                    } else {
                        $("#NearReplication,#NearReplication, #DRReplication, #PRReplication,#tablereplication").hide();
                        $('#SelectReplicationNames').val("");
                    }
                    if (!isClusterFound && !isSrmFound) $('#clusterCol, #selectedClusterCol, #clusterProperties').hide();

                    if (isOracleFound) {

                        $('#PRServerBody, #PRDatabaseBody').addClass('d-none')
                        $('#PRMultipleServerBody, #PRMultipleDatabaseBody').removeClass('d-none')
                    }
                    else {

                        $('#PRServerBody, #PRDatabaseBody').removeClass('d-none')
                        $('#PRMultipleServerBody, #PRMultipleDatabaseBody').addClass('d-none')
                    }

                    if (isOracleFound || ismssqlFound) {

                        $('#DRServerBody, #DRDatabaseBody').addClass('d-none')
                        $('#DRMultipleServerBody, #DRMultipleDatabaseBody').removeClass('d-none')
                    }
                    else {

                        $('#DRServerBody, #DRDatabaseBody').removeClass('d-none')
                        $('#DRMultipleServerBody, #DRMultipleDatabaseBody').addClass('d-none')
                    }

                    if (ismssqlFound) {

                        $('#PRReplication,#DRReplication,#tablereplication').addClass('d-none')

                    }
                    else {

                        $('#PRReplication,#DRReplication,#tablereplication').removeClass('d-none')

                    }
                    $("#infraObjectTableName").text($("#textName").val() || 'NA').attr('title', $("#textName").val() || 'NA');
                    $("#DescriptionSum").text($("#Description").val() || 'NA').attr('title', $("#Description").val() || 'NA');
                    $("#BusinessServiceSum").text($("#infraBusinessServiceId").val() || 'NA').attr('title', $("#infraBusinessServiceId").val() || 'NA');
                    $("#TypeSum").text($('#Activetype option:selected').text() || 'NA');
                    $("#site_category").text(siteData ? siteData : 'NA');
                    $("#BusinessFunSum").text($("#BusinessFunctionVal").val() || 'NA').attr('title', $("#BusinessFunctionVal").val() || 'NA');
                    $("#IsPairSum").text($('#PairId').prop('checked') ? ($("#SelectPairInfra").val() || 'NA') : 'NA');
                    $("#ReplicationNameSum").text($("#ddlReplicationTypeNameId option:selected").text() || 'NA').attr('title', $("#ddlReplicationTypeNameId option:selected").text() || 'NA');
                    $("#replication_name").text($("#SelectReplicationType option:selected").text() || 'NA').attr('title', $("#SelectReplicationType option:selected").text() || 'NA');
                    $("#database_type").text($("#SelectDatabaseType").val() ? $("#SelectDatabaseType option:selected").text() : 'NA');
                    $("#IsAssociateSum").text($('#InfraId').prop('checked') ? ($("#SelectAssociate").val() || 'NA') : 'NA');
                }
            }

        } else {

            var selectedServer = $(".PRServerName").val() || $("#PRMultipleServer").val();
            var selecteddatabase = $(".PRDatabaseName").val() || $("#PRMultipleDatabase").val();
            var selectedDRServer = $(".DRServerName").val() || $("#DRMultipleServer").val();
            if (getReplicationName === "Native Replication-Oracle-Rac" && $("#DRCol").is(":visible")) {
                approverLists(selectedServer, selectedDRServer)
            }

            var selectedDRdatabase = $(".DRDatabaseName").val() || $("#DRMultipleDatabase").val();
            let isPRserver = validateDropDown(selectedServer, ' Select server ', 'SelectServerName-error');
            let isDrserver = validateDropDown(selectedDRServer, ' Select server ', 'DRSelectServerNames-error');
            let isPRReplications = validateDropDown($("#PRSelectReplicationName").val(), ' Select replication ', 'SelectReplicationName-error');
            let isDRReplications = validateDropDown($("#SelectReplicationNames").val(), ' Select replication ', 'DRSelectReplicationNames-error');
            let isPRDatabase = validateDropDown(selecteddatabase, ' Select database', 'PRSelectDatabase-error');
            let isDRDatabase = validateDropDown(selectedDRdatabase, 'Select database', 'DRSelectDatabaseNames-error');

            let isPRSrmServer = isSrmFound && $("#prSrm").is(":visible") ? validateDropDown($("#prSrmServer").val(), ' Select server', 'prSrmServer-error') : true;
            let isDRSrmServer = isSrmFound && $("#drSrm").is(":visible") ? validateDropDown($("#drSrmServer").val(), ' Select server', 'drSrmServer-error') : true;

            let isCusterType = (isClusterFound || isSrmFound) && $("#clusterProperties").is(":visible") ? validateDropDown($("#clusterType").val(), ' Select server', 'clusterType-error') : true;
            let isVeritasServer = (isClusterFound || isSrmFound) && $("#veritas-child").is(":visible") ? validateDropDown($("#clusterPR").val(), ' Select server', 'clusterPR-error') : true;
            let isHacmpServer = (isClusterFound || isSrmFound) && $("#HACMP-child").is(":visible") ? validateDropDown($("#clusterDR").val(), ' Select server', 'clusterDR-error') : true;

            let checkInfraValidation = ($('#DRCol').is(':visible') ? (($("#DRReplication").is(":visible") ? isDRReplications : true) && ($("#drdatabase").is(":visible") ? isDRDatabase : true) && isDrserver) : true)
            let checkSrmValidation = isPRSrmServer && isDRSrmServer
            let checkClusterValidation = isCusterType && isVeritasServer && isHacmpServer
            let checkDynamicData = checkDynamicDataValidation();


            //isPRserver //$("#prdatabase").is(":visible") ? isPRDatabase : true) //$("#PRReplication").is(":visible") ? isPRReplications : true
            if (isPRserver && ($("#PRReplication").is(":visible") ? isPRReplications : true) && ($("#prdatabase").is(":visible") ? isPRDatabase : true) && checkSrmValidation
                && checkClusterValidation && checkDynamicData && checkInfraValidation) {

                if (getReplicationName === "Native Replication-Oracle-Rac" && $("#DRCol").is(":visible")) {
                    $('#NewModel').modal('show');
                    $('#CreateModal').modal('hide');
                    modelSave();
                }
                $('#summary_header').children('th').slice(3).remove();
                $('#serverTbody').children('td').slice(3).remove();
                $('#srmServerTbody').children('td').slice(3).remove();
                $('#databaseTbody').children('td').slice(3).remove();
                $('#replicationTbody').children('td').slice(3).remove();

                let findOtherSites = siteProperties.length && siteProperties.filter((site) => site?.category?.toLowerCase() !== 'primary')

                if (findOtherSites.length) $('#DREnable').val(true)
                else $('#DREnable').val(false)

                siteProperties.length && siteProperties.forEach((site) => {

                    if (site?.category?.toLowerCase() === 'primary') {
                        $('#pr_summary_header').html(site?.name)

                    } else if (site?.category?.toLowerCase() === 'dr') {
                        $('#dr_summary_header').html(site?.name)
                    } else {
                        let getSiteName = site?.name?.trim();

                        $('#summary_header').append(`<th>${site?.name}</th>`)

                        $('#serverTbody').append(`<td>${serverProperties[getSiteName]?.name || 'NA'}</td>`)
                        $('#databaseTbody').append(`<td>${databaseProperties[getSiteName]?.name || 'NA'}</td>`)
                        $('#replicationTbody').append(`<td>${replicationProperties[getSiteName]?.name || 'NA'}</td>`)
                    }
                })

                if (serverProperties.hasOwnProperty('SRMServer')) {
                    serverProperties.SRMServer.length && serverProperties.SRMServer.forEach((srm) => {
                        if (srm.type !== 'PR' && srm.type !== 'DR') $('#srmServerTbody').append(`<td>${srm?.name || 'NA'}</td>`)
                    })
                }
                //$("#PRServerSum").text($(".PRServerName").val() || 'NA');
                // $("#PRDataBaseSum").text($(".PRDatabaseName").val() || 'NA');
                $("#PRReplicationSum").text($("#PRSelectReplicationName").val() || 'NA');
                // Check which dropdown is visible and update #PRServerSum accordingly
                var selectedserverValues = ($("#PRServerBody").is(':visible') ? $(".PRServerName").val() : $('#PRMultipleServer').val()) || 'NA';
                $("#PRServerSum").text(Array.isArray(selectedserverValues) ? selectedserverValues.join(', ') : selectedserverValues);

                var selectedDBValues = ($("#PRDatabaseBody").is(':visible') ? $(".PRDatabaseName").val() : $('#PRMultipleDatabase').val()) || 'NA';
                $("#PRDataBaseSum").text(Array.isArray(selectedDBValues) ? selectedDBValues.join(', ') : selectedDBValues);
                //if ($("#PRServerBody").is(':visible')) {
                //    $("#PRServerSum").text($(".PRServerName").val() || 'NA');
                //} else if ($("#PRMultipleServerBody").is(':visible')) {
                //    var selectedValues = $('#PRMultipleServer').val();

                //    selectedValues.forEach(function (value) {
                //        $("#PRServerSum").text(value || 'NA');
                //    });

                //}
                var selectedDRserverValues = ($("#DRServerBody").is(':visible') ? $(".DRServerName").val() : $('#DRMultipleServer').val()) || 'NA';
                $("#DRServerSum").text(Array.isArray(selectedDRserverValues) ? selectedDRserverValues.join(', ') : selectedDRserverValues);

                var selectedDRDBValues = ($("#DRDatabaseBody").is(':visible') ? $(".DRDatabaseName").val() : $('#DRMultipleDatabase').val()) || 'NA';
                $("#DRDataBaseSum").text(Array.isArray(selectedDRDBValues) ? selectedDRDBValues.join(', ') : selectedDRDBValues);
                //if ($("#DRServerBody").is(':visible') || $("#DRMultipleServerBody").is(':visible')) {
                //    $('#DRServerSum').show()
                //    $("#DRServerSum").text($("#DRServerName").val() || 'NA');
                //} else $('#DRServerSum').hide()

                //if ($("#DRDatabaseBody").is(':visible') || $("#DRMultipleDatabaseBody").is(':visible')) {
                //    $('#DRDataBaseSum').show()
                //    $("#DRDataBaseSum").text($("#DRDatabaseName").val() || 'NA');
                //} else $('#DRDataBaseSum').hide()

                if ($("#DRReplication").is(':visible')) {
                    $('#DRReplicationSum').show()
                    $("#DRReplicationSum").text($("#SelectReplicationNames").val() || 'NA');
                } else $('#DRReplicationSum').hide()

                if (!$('#tabledatabase').is(':visible')) {
                    $('#databaseTbody').hide();
                } else {
                    $('#databaseTbody').show();
                }

                if (!$('#tablereplication').is(':visible')) {
                    $('#replicationTbody').hide();
                } else {
                    $('#replicationTbody').show();
                }

                if (isSrmFound) {

                    serverProperties?.SRMServer && serverProperties['SRMServer'].length && serverProperties['SRMServer'].forEach((srm) => {
                        if (srm.type == 'PR') $('#PRSrmServerSum').show().text(srm?.name ?? 'NA')
                        else if (srm.type == 'DR') $('#DRSrmServerSum').show().text(srm?.name ?? 'NA')
                    })

                    $('#srmServerTbody').show();
                } else {
                    $('#srmServerTbody').hide();
                }

                if (isClusterFound && $('#cluster').prop('checked')) {

                    serverProperties?.clusters && serverProperties['clusters'].length && serverProperties['clusters'].forEach((server) => {
                        if (server.type == 'PR') $('#PRClusterSum').show().text(server?.name ?? 'NA')
                        else if (server.type == 'DR') $('#DRClusterSum').show().text(server?.name ?? 'NA')

                    })

                    $('#clusterServerTbody').show();
                } else {
                    $('#clusterServerTbody').hide();
                }

                $('#siteProperties').val(JSON.stringify(siteProperties))
                $('#serverProperties').val(JSON.stringify(serverProperties))
                $('#databaseProperties').val(JSON.stringify(databaseProperties))
                $('#replicationProperties').val(JSON.stringify(replicationProperties))

                form.steps('next');
            }
        }
    }, 700));


    // Node 
    function approverLists(prData, drData) {
        $('#addUserApproval').empty();

        const maxLength = Math.max(prData.length, drData.length);

        let nodeProperties = infraData?.nodeProperties && JSON.parse(infraData?.nodeProperties);
        let PRNodeData = [];
        let DRNodeData = [];

        if (nodeProperties && Array.isArray(nodeProperties) && nodeProperties.length) {
         
            selectedNodeValues = nodeProperties
            PRNodeData = nodeProperties?.flatMap((node) => Object.keys(node));
            DRNodeData = nodeProperties?.flatMap((node) => Object.values(node));

        }

        for (let i = 0; i < maxLength; i++) {

            const prItem = prData[i % prData.length];
            const drItem = drData[i % drData.length];

            const html = `
        <tr>
            <!-- Primary column with value from prData -->
            <td>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="cp-city-name"></i>
                        </span>
                        <select class="form-select-modal dataVal dataPr" data-placeholder="Select database" data-pr="${prItem}" value="${prData?.length ? prData[0] : ''}">
                            <option value="" disabled selected>Select database</option>
                            ${prData?.map(option => `
                                <option value="${option}" ${option === getSelectedValueForItem(prItem) ? 'selected' : ''}>${option}</option>
                            `).join('')}
                        </select>
                    </div>
                    <span i" class="error-message"></span>
                </div>
            </td>

            <!-- DR column with a select dropdown (populated with drData) -->
            <td>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="cp-city-name"></i>
                        </span>
                        <select class="form-select-modal dataVal dataDr" data-placeholder="Select database" data-pr="${prItem}" data-dr="${drItem}">
                            <option value="" disabled selected>Select database</option>
                            ${drData?.map(option => `
                                <option value="${option}" ${option === getSelectedValueForItem(drItem) ? 'selected' : ''}>${option}</option>
                            `).join('')}
                        </select>
                    </div>
                    <span  class="error-message"></span>
                </div>
            </td>
        </tr>`;

            // Append the HTML to the approval list container
            $('#addUserApproval').append(html);
        }
        NodeValuesLength = $("#addUserApproval").children().length
        console.log(length)
        $('#addUserApproval .dataPr').each(function (index, element) {
            $(element).val(PRNodeData[index]);
        });

        $('#addUserApproval .dataDr').each(function (index, element) {
            $(element).val(DRNodeData[index]);
        });

    }

    function getSelectedValueForItem(prItem) {

        return prToDrMapping[prItem] || "";
    }

    $("#Modelcancel, #Modelcls").on('click', function () {
        $('#NewModel').modal('hide');
        $('#CreateModal').modal('show');
        form.steps('previous');
    });


    $('#addUserApproval').on('change', '.dataVal', function () {
        const value = $(this).val();
        const prValue = $(this).data('pr');

        let getCurrentTrIndex = $(this)?.closest('tr')?.index()

        if (selectedNodeValues[getCurrentTrIndex]) {
            let obj = { [prValue]: value }
            selectedNodeValues.splice(getCurrentTrIndex, 1, obj);
        } else {

            const newSelection = {};
            newSelection[prValue] = value;
            selectedNodeValues.push(newSelection);
        }
        const errorElementId = $(this).closest('.form-group').find('.error-message')
        validateDropDowns(value, 'Select database', errorElementId);
    });

    // Function to validate all dropdowns before saving
    function validateAllDropdowns() {
        let allValid = true;
        $('.dataVal').each(function () {
            const value = $(this).val();
            const prValue = $(this).data('pr');
            const errorElementId = $(this).closest('.form-group').find('.error-message')

            if (!validateDropDowns(value, 'Select database', errorElementId)) {
                allValid = false;
            }
        });

        return allValid;
    }

    function validateDropDowns(value, errorMessage, errorElementId) {
        if (!value) {
            errorElementId.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        errorElementId.text('').removeClass('field-validation-error');
        return true;
    }
    function modelSave() {
      
        $("#ModelSave").on('click', async function () {
         
            const allValid = validateAllDropdowns();
            if (allValid) {
                $('.dataVal').trigger('change');
                let removedElements = selectedNodeValues.splice(0, NodeValuesLength);
                setTimeout(() => {
                    let nodeData = JSON.stringify(removedElements);
                    $("#NodePropertiesData").val(nodeData);
                    $('#NewModel').modal('hide');
                    $('#CreateModal').modal('show');
                }, 200)

            }
        });
    }

    function validateDropDown(value, errorMessage, errorElement) {
        const isInvalid = Array.isArray(value) ? !value.length : !value;

        if (isInvalid) {
            $(`#${errorElement}`).text(errorMessage).addClass('field-validation-error');
            return false;
        }

        $(`#${errorElement}`).text('').removeClass('field-validation-error');
        return true;
    }

    async function validateDescription(value, errorMessage) {
        const errorElement = $('#Description-error');
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed')
                .addClass('field-validation-error');
            return false;
        }
        if (value.length > 250) {
            errorElement.text(errorMessage).addClass('field-validation-error');
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }

    function validateDropDown(value, errorMessage, errorElement) {
        const isInvalid = Array.isArray(value) ? !value.length : !value;

        if (isInvalid) {
            $(`#${errorElement}`).text(errorMessage).addClass('field-validation-error');
            return false;
        }

        $(`#${errorElement}`).text('').removeClass('field-validation-error');
        return true;
    }

    async function validateName(value, id = null) {
        if (!value) {
            $('#Name-error').text('Enter infraobject name').addClass('field-validation-error');
            return false;
        }
        if (value.includes("<")) {
            $('#Name-error').text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + nameExistUrl;
        let data = { infraObjectName: value, id: id };

        const validationResults = [
            await SpecialCharValidateCustom(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithNumber(value),
            await SpaceWithUnderScore(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsNameExist(url, data, OnError)
        ];
        return await CommonValidation($('#Name-error'), validationResults);
    }
    async function IsNameExist(url, data, errorFunc) {
        return !data.infraObjectName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    // Edit Populate
    function populateModalFields(infraData) {


        serverProperties = infraData?.serverProperties ? JSON.parse(infraData?.serverProperties) : {};
        databaseProperties = infraData?.databaseProperties ? JSON.parse(infraData?.databaseProperties) : {};
        replicationProperties = infraData?.replicationProperties ? JSON.parse(infraData?.replicationProperties) : {};
        siteProperties = infraData?.siteProperties ? JSON.parse(infraData?.siteProperties) : []

        if (infraData?.typeName === "DB") {
            isEdit = true
        }

        databaseType = infraData?.subTypeId
        database = infraData?.subType
        replicationType = infraData?.replicationCategoryTypeId
        activeType = infraData?.typeName
        ReplicationtypeName = infraData?.replicationTypeName
        ReplicationCategory = infraData?.replicationCategoryType

        let clusterFound = clusterReplicationData && clusterReplicationData.some((rep) => (ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes(rep)))

        let oracleFound = getOracleFound();

        $('#textName').val(infraData?.name);
        $('#infraNameId').val(infraData?.id);
        $('#Description').val(infraData?.description);
        $('#infraBusinessServiceId').val(infraData?.businessServiceName).trigger('change');
        $('#BusinessServiceId').val(infraData?.businessServiceId);
        $('#Activetype').val(infraData?.type);
        $('#ActiveTypeName').val(infraData?.typeName);
        $('#StateValue').val(infraData?.state);
        $('#SelectPairInfra option, #SelectAssociate option').prop('disabled', false);

        $(`#SelectPairInfra option[infraid="${infraData?.id}"], #SelectAssociate option[Acid="${infraData?.id}"]`).prop('disabled', true);

        if (infraData?.isDrift) {
            $('#driftValue').val(infraData?.isDrift)
            $('#drift').prop('checked', infraData?.isDrift)
        } else {
            $('#driftValue').val(infraData?.isDrift)
            $('#drift').prop('checked', infraData?.isDrift)
        }

        SetReplicationMapping()
        SetReplicationMaster(infraData?.typeName);

        if (ReplicationtypeName && ReplicationtypeName?.toLowerCase().replace(/ /g, "").includes('srmvmware')) {

            SetPRServerType(activeType, 'production', 'presxiserver', false, true)
            SetDRServerType(activeType, 'dresxiserver', false, true)
            SetSrmServerType(activeType, 'presxiappserver', 'production')

        } else {
            SetPRServerType(infraData?.typeName, 'production', activeType?.toLowerCase() == 'db' ? 'PRDBServer' : '', oracleFound)
            SetDRServerType(infraData.typeName, activeType?.toLowerCase() == 'db' ? 'DRDBServer' : '', oracleFound || getMssqlFound())
        }

        if (clusterFound) {

            if (serverProperties?.isCluster) {
                $('#clusterCol,#cluster').show();
                $('#cluster').prop('checked', true).trigger('change')
                $('#clusterType').val(serverProperties?.clusterType);
                if (serverProperties?.clusterType === 'veritas') {
                    $('#veritas-child').show()
                    $('#HACMP-child').hide()
                } else {
                    $('#veritas-child, #HACMP-child').show();
                }

                getVeritasHacmpList(serverProperties?.clusterType);
            } else {
                $('#clusterCol').show();
                $('#clusterProperties, #selectedClusterCol').hide()
            }
        }

        $('#DataTypeCol').toggle(infraData?.typeName === "DB");
        function toggleReplicationDisplay(replicationName, elementId) {
            if (replicationName) $(elementId).show();
            else $(elementId).hide();
        }

        toggleReplicationDisplay(infraData?.prReplicationName, '#PRReplication');
        toggleReplicationDisplay(infraData?.drReplicationName, '#DRReplication');

        //$('#PRSelectReplicationName').val(infraData?.prReplicationName);
        // pr replication 
        let value = infraData?.replicationProperties
        let parsedValue = JSON.parse(value);
        let id = parsedValue?.PR?.name;
        $('#PRSelectReplicationName').val(id).trigger('change');;

        let DRvalue = infraData?.replicationProperties
        let parsedValues = JSON.parse(DRvalue);
        let idz = parsedValues?.DR?.name;
        $('#SelectReplicationNames').val(idz).trigger('change');;


        // $('#SelectReplicationNames').val(infraData?.drReplicationName).trigger('change');
        //$('#drReplicationNameId').val(infraData?.drReplicationId);


        if ((infraData?.replicationCategoryType === "Application - No Replication") || (infraData?.replicationCategoryType === "Database - No Replication")
            || infraData?.replicationTypeName?.toLowerCase().replace(/ /g, "").includes('redis')) {
            $("#DRReplication, #PRReplication, #tablereplication").hide();

        } else {
            $("#DRReplication, #PRReplication, #tablereplication").show();

        }
        if (infraData?.typeName === "Application" || infraData?.typeName === "Virtual") {
            $("#prdatabase, #drdatabase, #tabledatabase").hide()

        } else {
            $("#prdatabase, #drdatabase, #tabledatabase").show()

        }

        $("#PrioritySum").text(infraData?.priority);
        $('input[name="Priority"]').filter(`[value="${infraData?.priority}"]`).prop('checked', true);
        if (infraData?.isPair) {
            $('#pairinfra').show();
            $('#SelectPairInfra').val(infraData?.pairInfraObjectName);
            $('#PairInfraId').val(infraData?.pairInfraObjectId);
            $('#PairId').prop('checked', infraData?.isPair);
            $('#pairValue').val(infraData?.isPair);
        }
        else {
            $('#pairinfra').hide();
        }
        if (infraData?.isAssociate) {
            $('#associate').show();
            $('#SelectAssociate').val(infraData?.isAssociateInfraObjectName);
            $('#AssociateId').val(infraData?.isAssociateInfraObjectId);
            $('#InfraId').prop('checked', infraData?.isAssociate);
            $('#AssociateValue').val(infraData?.isAssociate);
        }
        else {
            $('#associate').hide()
        }

        let errorElement = ['#Name-error', "#Description-error", '#DRSelectServerNames-error', "#SelectSubType-error", "#BusinessService-error",
            "#SelectReplicationType-error", "#SelectPriority-error", '#SelectAssociate-error', '#SelectReplicationName-error',
            '#BusinessFunction-error', '#SelectPairInfra-error', '#SelectReplication-error', '#SelectServerName-error', '#PRSelectDatabase-error', '#DRSelectDatabaseNames-error', "#DatbaseType-error"]

        errorElement.forEach(element => $(element).text('').removeClass('field-validation-error'));
    }

    const clearInputInfraFields = () => {

        multipleServerDetails = { multiplePRServer: [], multipleDRServer: [] };
        multipleDatabaseDetails = { multiplePRDatabase: [], multipleDRDatabase: [] };
        $("#textName,#NearDRServerName,#SelectPairInfra,#SelectServerName,#nodeProperties,#databaseText,#DatabaseId,#DRSelectServerNames,#DRSelectDatabaseNames,#DRSelectDatabaseNames,#SelectReplicationType,#ddlReplicationNameId, #ddlReplicationTypeNameId,#PRSelectDatabase, #infraNameId").val('');
        $("#SelectDatabaseType,#SelectAssociate,#PairInfraId,#infraReplicationNameId,#infraBusinessServiceId,#NearDRDatabaseNames,#NearDRServerName,#Description,#SelectAssociate, #pairValue, #AssociateValue, #prSrmServer, #drSrmServer, #nearSrmServer, #siteType").val("");
        $("#PRMultipleServer,#DRMultipleServer,#PRMultipleDatabase,#DRMultipleDatabase,#SelectReplicationNames").val("");
        $("#infrServerNameId, #PRServerName, #PRDatabaseName, #PRSelectDatabaseId, #PRSelectReplicationName, #drServerName, #DRSelectServerNamesId, #drDatabaseName, #infraDatabaseId").val('');
        $('#driftValue').val(true)
        $('#drift').prop('checked', true);
        $('.btn-check').prop('checked', false);
        $('#PairId, #InfraId, #cluster').prop('checked', false);
        $('#clusterType, #clusterPR, #clusterDR').val('').trigger('change');
        $('#PairInfra, #Associate, #DRCol').hide();
        $('#ddlbusinessFunctionId, #SelectReplicationType, #ddlReplicationTypeNameId, #tbody1, #siteType').empty();
        $('#SaveFunction').text('Save');
        $('#btnradio1').prop('checked', true).trigger('change');

        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });
    }
});
