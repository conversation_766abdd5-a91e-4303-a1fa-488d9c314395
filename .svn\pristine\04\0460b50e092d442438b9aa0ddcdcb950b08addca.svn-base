﻿namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;

public class GetHeatMapStatusDetailQueryHandler : IRequestHandler<GetHeatMapStatusDetailQuery, HeatMapStatusDetailVm>
{
    private readonly IHeatMapStatusRepository _heatMapStatusRepository;
    private readonly IMapper _mapper;

    public GetHeatMapStatusDetailQueryHandler(IMapper mapper, IHeatMapStatusRepository heatMapStatusRepository)
    {
        _mapper = mapper;
        _heatMapStatusRepository = heatMapStatusRepository;
    }

    public async Task<HeatMapStatusDetailVm> Handle(GetHeatMapStatusDetailQuery request,
        CancellationToken cancellationToken)
    {
        var heatMapStatus = await _heatMapStatusRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(heatMapStatus, nameof(Domain.Entities.HeatMapStatus),
            new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.Id));

        var heatMapStatusDetailDto = _mapper.Map<HeatMapStatusDetailVm>(heatMapStatus);

        return heatMapStatusDetailDto ?? throw new NotFoundException(nameof(Domain.Entities.HeatMapStatus), request.Id);
    }
}