using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataSyncMonitorLogsRepositoryTests : IClassFixture<DataSyncMonitorLogFixture>
{
    private readonly DataSyncMonitorLogFixture _dataSyncMonitorLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataSyncMonitorLogsRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public DataSyncMonitorLogsRepositoryTests(DataSyncMonitorLogFixture dataSyncMonitorLogFixture)
    {
        _dataSyncMonitorLogFixture = dataSyncMonitorLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();

        // Setup configuration mock
        //_mockConfiguration.Setup(x => x.GetConnectionString("Default")).Returns("Server=localhost;Database=test;");
        //_mockConfiguration.Setup(x => x.GetConnectionString("DBProvider")).Returns("mysql");
        
        _repository = new DataSyncMonitorLogsRepository(_dbContext, _mockConfiguration.Object);
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataSyncMonitorLog = _dataSyncMonitorLogFixture.DataSyncMonitorLogDto;

        // Act
        var result = await _repository.AddAsync(dataSyncMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncMonitorLog.Type, result.Type);
        Assert.Equal(dataSyncMonitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.DataSyncMonitorLogs);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataSyncMonitorLog = _dataSyncMonitorLogFixture.DataSyncMonitorLogDto;
        await _repository.AddAsync(dataSyncMonitorLog);

        dataSyncMonitorLog.InfraObjectName = "UpdatedInfraObjectName";

        // Act
        var result = await _repository.UpdateAsync(dataSyncMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataSyncMonitorLog = _dataSyncMonitorLogFixture.DataSyncMonitorLogDto;
        await _repository.AddAsync(dataSyncMonitorLog);

        // Act
        var result = await _repository.DeleteAsync(dataSyncMonitorLog);

        // Assert
        Assert.Equal(dataSyncMonitorLog.Type, result.Type);
        Assert.Empty(_dbContext.DataSyncMonitorLogs);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncMonitorLog = _dataSyncMonitorLogFixture.DataSyncMonitorLogDto;
        var addedEntity = await _repository.AddAsync(dataSyncMonitorLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataSyncMonitorLog = _dataSyncMonitorLogFixture.DataSyncMonitorLogDto;
        await _repository.AddAsync(dataSyncMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataSyncMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(dataSyncMonitorLog.Type, result.Type);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncMonitorLogs.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldNotReturnInactiveEntities()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        dataSyncMonitorLogs.First().IsActive = false; // Make one inactive
        await _dbContext.DataSyncMonitorLogs.AddRangeAsync(dataSyncMonitorLogs);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataSyncMonitorLogs.Count - 1, result.Count); // Should exclude the inactive one
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    #endregion

    #region GetByInfraObjectId Tests

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEntitiesInDateRange()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        // Set some logs within date range
        dataSyncMonitorLogs.Take(3).ToList().ForEach(x => x.CreatedDate = startDate.AddDays(1));
        // Set some logs outside date range
        dataSyncMonitorLogs.Skip(3).ToList().ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-10));
        
        await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            DataSyncMonitorLogFixture.InfraObjectId, 
            startDate.ToString("yyyy-MM-dd"), 
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(DataSyncMonitorLogFixture.InfraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= startDate.Date && x.CreatedDate.Date <= endDate.Date));
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenNoEntitiesInRange()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        // Set all logs outside date range
        dataSyncMonitorLogs.ForEach(x => x.CreatedDate = DateTime.Now.AddDays(-10));
        
        await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            DataSyncMonitorLogFixture.InfraObjectId, 
            startDate.ToString("yyyy-MM-dd"), 
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetByInfraObjectId_ShouldReturnEmptyList_WhenInfraObjectIdNotFound()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        var startDate = DateTime.Now.AddDays(-5);
        var endDate = DateTime.Now.AddDays(-1);
        
        dataSyncMonitorLogs.ForEach(x => x.CreatedDate = startDate.AddDays(1));
        await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Act
        var result = await _repository.GetByInfraObjectId(
            "non-existent-infra-id", 
            startDate.ToString("yyyy-MM-dd"), 
            endDate.ToString("yyyy-MM-dd"));

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;

        // Act
        var result = await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Assert
        Assert.Equal(dataSyncMonitorLogs.Count, result.Count());
        Assert.Equal(dataSyncMonitorLogs.Count, _dbContext.DataSyncMonitorLogs.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList;
        await _repository.AddRangeAsync(dataSyncMonitorLogs);

        // Act
        var result = await _repository.RemoveRangeAsync(dataSyncMonitorLogs);

        // Assert
        Assert.Equal(dataSyncMonitorLogs.Count, result.Count());
        Assert.Empty(_dbContext.DataSyncMonitorLogs);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dataSyncMonitorLogs = _dataSyncMonitorLogFixture.DataSyncMonitorLogList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dataSyncMonitorLogs);
        var initialCount = dataSyncMonitorLogs.Count;
        
        var toUpdate = dataSyncMonitorLogs.Take(2).ToList();
        toUpdate.ForEach(x => x.InfraObjectName = "UpdatedInfraObjectName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dataSyncMonitorLogs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.InfraObjectName == "UpdatedInfraObjectName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
