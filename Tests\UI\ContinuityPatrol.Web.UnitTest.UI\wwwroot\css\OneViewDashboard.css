﻿.carousel__item {
    position: absolute;
    width: 100%;
    opacity: 0;
    will-change: transform, opacity;
    -webkit-animation: carousel-animate-vertical 9s linear infinite;
    animation: carousel-animate-vertical 9s linear infinite;
    margin-bottom: 10px;
}

    .carousel__item:nth-child(1) {
        -webkit-animation-delay: calc(3s * -1);
        animation-delay: calc(3s * -1);
    }

    .carousel__item:nth-child(2) {
        -webkit-animation-delay: calc(3s * 0);
        animation-delay: calc(3s * 0);
    }

    .carousel__item:last-child {
        -webkit-animation-delay: calc(-3s * 2);
        animation-delay: calc(-3s * 2);
    }

.carousel__item-body {
    padding: 6px;
    margin: 2px 0px;
}

@keyframes carousel-animate-vertical {
    0% {
        transform: translateY(100%) scale(1);
        opacity: 0;
        visibility: hidden;
    }

    9%, 33.3333333333% {
        transform: translateY(100%) scale(1);
        opacity: 1;
        visibility: visible;
    }

    42.3333333333%, 66.6666666667% {
        transform: translateY(0) scale(1);
        opacity: 1;
        visibility: visible;
    }

    75.6666666667% {
        transform: translateY(-100%) scale(1);
        opacity: 0;
        visibility: visible;
    }

    100% {
        transform: translateY(-100%) scale(1);
        opacity: 0;
        visibility: hidden;
    }
}

.site_collapse[aria-expanded="true"] {
    background-color: #d3e3fd;
    padding: 4px;
    border-radius: 4px;
}

.carousel-control-next-icon, .carousel-control-prev-icon {
    width: 1rem;
    height: 1rem;
}

.carousel-control-next, .carousel-control-prev {
    width: 5%;
}

.carousel-indicators {
    position: relative;
}
