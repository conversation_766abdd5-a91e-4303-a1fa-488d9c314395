﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Server.Events.SaveAs;

public class ServerSaveAsEventHandler : INotificationHandler<ServerSaveAsEvent>
{
    private readonly ILogger<ServerSaveAsEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerSaveAsEventHandler(ILogger<ServerSaveAsEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ServerSaveAsEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Server.ToString(),
            Action = $"{ActivityType.SaveAs} {Modules.Server}",
            ActivityType = ActivityType.SaveAs.ToString(),
            ActivityDetails = $"Server '{notification.ServerName}' cloned successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Server '{notification.ServerName}' save-as successfully.");
    }
}