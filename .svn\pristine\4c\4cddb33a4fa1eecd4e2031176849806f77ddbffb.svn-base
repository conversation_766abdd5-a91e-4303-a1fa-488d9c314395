using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;

public class UpdateRsyncOptionCommandValidator : AbstractValidator<UpdateRsyncOptionCommand>
{
    private readonly IRsyncOptionRepository _rsyncOptionRepository;

    public UpdateRsyncOptionCommandValidator(IRsyncOptionRepository rsyncOptionRepository)
    {
        _rsyncOptionRepository = rsyncOptionRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.ReplicationType)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.Properties)
            .Must(IsValidJson)
            .WithMessage("{PropertyName} must be a valid JSON string.")
            .When(p => p.Properties.IsNotNullOrWhiteSpace());

        RuleFor(p => p)
            .MustAsync(RsyncOptionNameUnique)
            .WithMessage("A same Name already exist.");
    }

    public async Task<bool> RsyncOptionNameUnique(UpdateRsyncOptionCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "RsyncOption Id");

        return !await _rsyncOptionRepository.IsNameExist(p.Name, p.Id);
    }

    private static bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                return JsonConvert.DeserializeObject(properties) != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}