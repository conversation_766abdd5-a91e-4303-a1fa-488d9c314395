﻿namespace ContinuityPatrol.Application.Features.InfraObject.Events.DashboardViewEvent.Update;

public class InfraObjectDashboardViewUpdatedEvent : INotification
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public int Priority { get; set; }
    public string CompanyId { get; set; }
    public string Description { get; set; }
    public string EntityId { get; set; }
    public string MonitorType { get; set; }
    public int Type { get; set; }
    public string DataLagValue { get; set; }
    public string Status { get; set; }
    public string Properties { get; set; }
    public int ReplicationStatus { get; set; }
    public int DROperationStatus { get; set; }
    public string ConfiguredRPO { get; set; }
    public string ConfiguredRTO { get; set; }
    public string RPOThreshold { get; set; }
    public string SiteProperties { get; set; }
    public string CurrentRPO { get; set; }
    public string CurrentRTO { get; set; }
    public string State { get; set; }
    public string ErrorMessage { get; set; }
    public string EstimatedRTO { get; set; }
}