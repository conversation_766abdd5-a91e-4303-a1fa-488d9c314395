using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PostgresMonitorLogsFixture : IDisposable
{
    public List<PostgresMonitorLogs> PostgresMonitorLogsPaginationList { get; set; }
    public List<PostgresMonitorLogs> PostgresMonitorLogsList { get; set; }
    public PostgresMonitorLogs PostgresMonitorLogsDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public PostgresMonitorLogsFixture()
    {
        var fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        fixture.Customize<PostgresMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            .With(x => x.LastModifiedDate, DateTime.UtcNow)
            .With(x => x.CreatedBy, "TestUser")
            .With(x => x.LastModifiedBy, "TestUser")
            .With(x => x.Type, () =>
            {
                var type = fixture.Create<string>();
                return $"Type_{(type.Length > 8 ? type.Substring(0, 8) : type)}";
            })
            .With(x => x.InfraObjectId, () => Guid.NewGuid().ToString())
            .With(x => x.InfraObjectName, () =>
            {
                var name = fixture.Create<string>();
                return $"InfraObject_{(name.Length > 10 ? name.Substring(0, 10) : name)}";
            })
            .With(x => x.WorkflowId, () => Guid.NewGuid().ToString())
            .With(x => x.WorkflowName, () =>
            {
                var name = fixture.Create<string>();
                return $"Workflow_{(name.Length > 10 ? name.Substring(0, 10) : name)}";
            })
            .With(x => x.Properties, () =>
            {
                var props = fixture.Create<string>();
                return props.Length > 100 ? props.Substring(0, 100) : props;
            })
            .With(x => x.ConfiguredRPO, () =>
            {
                var rpo = fixture.Create<string>();
                return rpo.Length > 20 ? rpo.Substring(0, 20) : rpo;
            })
            .With(x => x.DataLagValue, () =>
            {
                var lag = fixture.Create<string>();
                return lag.Length > 20 ? lag.Substring(0, 20) : lag;
            })
            .With(x => x.Threshold, () =>
            {
                var threshold = fixture.Create<string>();
                return threshold.Length > 20 ? threshold.Substring(0, 20) : threshold;
            }));

        PostgresMonitorLogsList = fixture.CreateMany<PostgresMonitorLogs>(5).ToList();
        PostgresMonitorLogsPaginationList = fixture.CreateMany<PostgresMonitorLogs>(20).ToList();
        PostgresMonitorLogsDto = fixture.Create<PostgresMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public PostgresMonitorLogs CreatePostgresMonitorLogWithProperties(
        string type = null,
        string infraObjectId = null,
        string infraObjectName = null,
        string workflowId = null,
        string workflowName = null,
        DateTime? createdDate = null,
        bool isActive = true)
    {
        var fixture = new Fixture();
        var typeStr = fixture.Create<string>();
        var infraNameStr = fixture.Create<string>();
        var workflowNameStr = fixture.Create<string>();
        var propsStr = fixture.Create<string>();
        var rpoStr = fixture.Create<string>();
        var lagStr = fixture.Create<string>();
        var thresholdStr = fixture.Create<string>();

        return new PostgresMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = type ?? $"Type_{(typeStr.Length > 8 ? typeStr.Substring(0, 8) : typeStr)}",
            InfraObjectId = infraObjectId ?? Guid.NewGuid().ToString(),
            InfraObjectName = infraObjectName ?? $"InfraObject_{(infraNameStr.Length > 10 ? infraNameStr.Substring(0, 10) : infraNameStr)}",
            WorkflowId = workflowId ?? Guid.NewGuid().ToString(),
            WorkflowName = workflowName ?? $"Workflow_{(workflowNameStr.Length > 10 ? workflowNameStr.Substring(0, 10) : workflowNameStr)}",
            Properties = propsStr.Length > 100 ? propsStr.Substring(0, 100) : propsStr,
            ConfiguredRPO = rpoStr.Length > 20 ? rpoStr.Substring(0, 20) : rpoStr,
            DataLagValue = lagStr.Length > 20 ? lagStr.Substring(0, 20) : lagStr,
            Threshold = thresholdStr.Length > 20 ? thresholdStr.Substring(0, 20) : thresholdStr,
            IsActive = isActive,
            CreatedDate = createdDate ?? DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };
    }

    public PostgresMonitorLogs CreatePostgresMonitorLogWithSpecificType(string type)
    {
        return CreatePostgresMonitorLogWithProperties(type: type);
    }

    public List<PostgresMonitorLogs> CreatePostgresMonitorLogsWithSameType(string type, int count)
    {
        var logs = new List<PostgresMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreatePostgresMonitorLogWithProperties(type: type));
        }
        return logs;
    }

    public List<PostgresMonitorLogs> CreatePostgresMonitorLogsWithSameInfraObjectId(string infraObjectId, int count)
    {
        var logs = new List<PostgresMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreatePostgresMonitorLogWithProperties(infraObjectId: infraObjectId));
        }
        return logs;
    }

    public List<PostgresMonitorLogs> CreatePostgresMonitorLogsWithDateRange(
        string infraObjectId,
        DateTime startDate,
        DateTime endDate,
        int count)
    {
        var logs = new List<PostgresMonitorLogs>();
        var dateRange = (endDate - startDate).TotalDays;

        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            logs.Add(CreatePostgresMonitorLogWithProperties(
                infraObjectId: infraObjectId,
                createdDate: randomDate));
        }
        return logs;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
