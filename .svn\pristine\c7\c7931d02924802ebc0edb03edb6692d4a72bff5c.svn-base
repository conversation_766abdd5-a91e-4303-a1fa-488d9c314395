﻿using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;

namespace ContinuityPatrol.Application.Features.MonitorService.Queries.GetByInfraObjectIdAndBusinessServiceId;

public class
    GetByInfraObjectIdAndBusinessServiceIdQueryHandler : IRequestHandler<GetByInfraObjectIdAndBusinessServiceIdQuery,
        List<MonitorServiceListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMonitorServiceRepository _monitorServiceRepositoryRepository;
    private readonly IServerViewRepository _serverViewRepository;

    public GetByInfraObjectIdAndBusinessServiceIdQueryHandler(
        IMonitorServiceRepository monitorServiceRepositoryRepository, IMapper mapper,
        IServerViewRepository serverViewRepository)
    {
        _monitorServiceRepositoryRepository = monitorServiceRepositoryRepository;
        _mapper = mapper;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<List<MonitorServiceListVm>> Handle(GetByInfraObjectIdAndBusinessServiceIdQuery request,
        CancellationToken cancellationToken)
    {
        var monitorServices = request.BusinessServiceId.IsNullOrWhiteSpace()
            ? await _monitorServiceRepositoryRepository.GetMonitorServiceByInfraObjectId(request.InfraObjectId)
            : await _monitorServiceRepositoryRepository.GetByInfraObjectIdAndBusinessFunctionId(request.InfraObjectId,
                request.BusinessServiceId);

        var monitorServiceDto = _mapper.Map<List<MonitorServiceListVm>>(monitorServices);

        var serverIds = monitorServiceDto.Select(x => x.ServerId)
            .Where(x => x.IsNotNullOrWhiteSpace())
            .Distinct()
            .ToList();

        var server = await _serverViewRepository.GetAllByServerIdsAsync(serverIds);

        var serverDictionary = server.ToDictionary(x => x.ReferenceId, x => x.IpAddress);

        monitorServiceDto.ForEach(monitorService =>
        {
            if (monitorService.ServerId.IsNotNullOrWhiteSpace() && serverDictionary.TryGetValue(monitorService.ServerId ,out var ipAddress))
            {
                monitorService.IPAddress = ipAddress;
            }
        });
        return monitorServiceDto;
    }
}