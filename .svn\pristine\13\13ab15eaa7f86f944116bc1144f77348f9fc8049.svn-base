﻿using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DataSetColumnsModel;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSetColumns.Queries;

public class GetDataSetColumnsListQueryHandlerTests : IClassFixture<DataSetColumnsFixture>
{
    private readonly DataSetColumnsFixture _dataSetColumnsFixture;

    private Mock<IDataSetColumnsRepository> _mockDataSetColumnsRepository;

    private readonly GetDataSetColumnsListQueryHandler _handler;

    public GetDataSetColumnsListQueryHandlerTests(DataSetColumnsFixture dataSetColumnsFixture)
    {
        _dataSetColumnsFixture = dataSetColumnsFixture;

        _mockDataSetColumnsRepository = DataSetColumnsRepositoryMocks.GetDataSetColumnsRepository(_dataSetColumnsFixture.DataSetColumns);

        _handler = new GetDataSetColumnsListQueryHandler(_dataSetColumnsFixture.Mapper, _mockDataSetColumnsRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_DataSetColumnsCount()
    {
        var result = await _handler.Handle(new GetDataSetColumnsListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DataSetColumnsListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_DataSetColumnsList()
    {
        var result = await _handler.Handle(new GetDataSetColumnsListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<DataSetColumnsListVm>>();
        result[0].Id.ShouldBe(_dataSetColumnsFixture.DataSetColumns[0].ReferenceId);
        result[0].TableName.ShouldBe(_dataSetColumnsFixture.DataSetColumns[0].TableName);
        result[0].DataSetId.ShouldBe(_dataSetColumnsFixture.DataSetColumns[0].DataSetId);
        result[0].ColumnName.ShouldBe(_dataSetColumnsFixture.DataSetColumns[0].ColumnName);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockDataSetColumnsRepository = DataSetColumnsRepositoryMocks.GetDataSetColumnsEmptyRepository();

        var handler = new GetDataSetColumnsListQueryHandler(_dataSetColumnsFixture.Mapper, _mockDataSetColumnsRepository.Object);

        var result = await handler.Handle(new GetDataSetColumnsListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetDataSetColumnsListQuery(), CancellationToken.None);

        _mockDataSetColumnsRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}