﻿using ContinuityPatrol.Domain.ViewModels.WorkflowProfileModel;

namespace ContinuityPatrol.Application.Features.WorkflowProfile.Queries.GetNames;

public class
    GetWorkflowProfileNameQueryHandler : IRequestHandler<GetWorkflowProfileNameQuery, List<WorkflowProfileNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowProfileRepository _workflowProfileRepository;
    private readonly IWorkflowProfileInfoViewRepository _workflowProfileInfoViewRepository;

    public GetWorkflowProfileNameQueryHandler(IMapper mapper, IWorkflowProfileRepository workflowProfileRepository, IWorkflowProfileInfoViewRepository workflowProfileInfoViewRepository)
    {
        _mapper = mapper;
        _workflowProfileRepository = workflowProfileRepository;
        _workflowProfileInfoViewRepository = workflowProfileInfoViewRepository;
    }

    public async Task<List<WorkflowProfileNameVm>> Handle(GetWorkflowProfileNameQuery request,
        CancellationToken cancellationToken)
    {
        var workflowProfileNames = await _workflowProfileRepository.GetWorkflowProfileNames();

        var profileIds = workflowProfileNames.Select(x => x.ReferenceId).ToList();

        var workflowProfileDto = _mapper.Map<List<WorkflowProfileNameVm>>(workflowProfileNames);

        var runningProfiles = await _workflowProfileInfoViewRepository.GetRunningProfileByProfileIds(profileIds);

        workflowProfileDto.ForEach(x =>
            x.IsRunning = runningProfiles.Any(rp => x.Id == rp.ProfileId)
        );

        return workflowProfileDto;
    }
}