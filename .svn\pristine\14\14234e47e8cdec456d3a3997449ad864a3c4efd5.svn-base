﻿namespace ContinuityPatrol.Application.Features.IncidentDaily.Commands.Update;

public class UpdateIncidentDailyCommandValidator : AbstractValidator<UpdateIncidentDailyCommand>
{
    private readonly IIncidentDailyRepository _incidentDailyRepository;

    public UpdateIncidentDailyCommandValidator(IIncidentDailyRepository incidentDailyRepository)
    {
        _incidentDailyRepository = incidentDailyRepository;

        RuleFor(p => p.ParentBusinessServiceName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.");

        RuleFor(e => e.InfraObjectName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 30).WithMessage("{PropertyName} should contain between 3 to 30 characters.");
    }
}