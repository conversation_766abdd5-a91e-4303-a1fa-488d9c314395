using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowApprovalMappingFixture : IDisposable
{
    public List<WorkflowApprovalMapping> WorkflowApprovalMappingPaginationList { get; set; }
    public List<WorkflowApprovalMapping> WorkflowApprovalMappingList { get; set; }
    public WorkflowApprovalMapping WorkflowApprovalMappingDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowApprovalMappingFixture()
    {
        var fixture = new Fixture();

        WorkflowApprovalMappingList = fixture.Create<List<WorkflowApprovalMapping>>();

        WorkflowApprovalMappingPaginationList = fixture.CreateMany<WorkflowApprovalMapping>(20).ToList();

        WorkflowApprovalMappingDto = fixture.Create<WorkflowApprovalMapping>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
