﻿using DevExpress.XtraReports.UI;
using ContinuityPatrol.Web.Areas.Alert.Controllers;
using System.Drawing;
using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class Alert : DevExpress.XtraReports.UI.XtraReport
    {
        public string username;
        private readonly ILogger<AlertDashboardController> _logger;
        public List<AlertListVm> AlertReport = new List<AlertListVm>();
        private string _reportGenerateName;
        public Alert(string data, string reportGeneratedName)
        {
            try
            {
                _logger = AlertDashboardController._logger;
                _reportGenerateName = reportGeneratedName;
                var jsonValue = JsonConvert.DeserializeObject<PaginatedResult<AlertListVm>>(data);
                AlertReport.AddRange(jsonValue.Data);
                InitializeComponent();
                ClientCompanyLogo();
                this.DataSource = AlertReport;
                xrTableCell7.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Alert_Report. The error message : " + ex.Message); throw; }

        }
        private void xrPageInfo1_BeforePrint(object sender, CancelEventArgs e)
        {

        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;
            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + _reportGenerateName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Alert_Report's User name. The error message : " + ex.Message); throw; }
        }

        public void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Alert_Report's CP Version. The error message : " + ex.Message); throw; }
        }
        private void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(AlertDashboardController.CompanyLogo) ? "NA" : AlertDashboardController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in Alert Report" + ex.Message.ToString());
            }
        }
    }
}
