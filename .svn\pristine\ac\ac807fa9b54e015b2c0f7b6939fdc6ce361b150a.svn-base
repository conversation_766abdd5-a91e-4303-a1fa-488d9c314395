﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Helper;
using MySqlConnector;
using Npgsql;
using System.Data.Common;
using System.Data;

namespace ContinuityPatrol.Persistence.Repositories;

public class ActiveDirectoryMonitorLogRepository :BaseRepository<ActiveDirectoryMonitorLog>, IActiveDirectoryMonitorLogRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly IConfiguration _config;
    public ActiveDirectoryMonitorLogRepository(ApplicationDbContext dbContext, IConfiguration config) : base(dbContext)
    {
        _dbContext = dbContext;
        _config = config;
    }
    public async Task<List<ActiveDirectoryMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate)
    {
        if (string.IsNullOrEmpty(infraObjectId) || string.IsNullOrEmpty(startDate) || string.IsNullOrEmpty(endDate))
            return new List<ActiveDirectoryMonitorLog>();

        var startDateTime = startDate.ToDateTime();
        var endDateTime = endDate.ToDateTime();
        var tableName = GetTableName<ActiveDirectoryMonitorLog>();

        // Get connection details
        var config = _config.GetConnectionString("Default");
        var dbProvider = _config.GetConnectionString("DBProvider");
        var decryptString = CryptographyHelper.Decrypt(config);
        var dbProviderString = CryptographyHelper.Decrypt(dbProvider);
        var schema = GetDatabaseNameFromConnectionString(decryptString, dbProviderString);

        // Get logs from active table
        var logTable = await _dbContext.ActiveDirectoryMonitorLogs
            .AsNoTracking()
            .Active()
            .Where(x => x.InfraObjectId == infraObjectId &&
                        x.CreatedDate.Date >= startDateTime &&
                        x.CreatedDate.Date <= endDateTime)
            .OrderBy(x => x.CreatedDate)
            .ToListAsync();

        // Check if backup table exists
        var tableExist = await IsTableExistAsync($"{tableName}_bkp", schema, dbProviderString);
        if (!tableExist)
            return logTable;

        try
        {
            // Construct SQL query based on database provider
            string sqlQuery;
            if (dbProviderString.ToLower() == "oracle")
            {
                var formattedStartDate = startDateTime.ToString("dd-MM-yyyy");
                var formattedEndDate = endDateTime.ToString("dd-MM-yyyy");
                sqlQuery = $"SELECT * FROM \"{schema}\".\"{tableName}_bkp\" WHERE TRUNC(\"CreatedDate\") >= TO_DATE('{formattedStartDate}', 'DD-MM-YYYY') AND TRUNC(\"CreatedDate\") <= TO_DATE('{formattedEndDate}', 'DD-MM-YYYY')";
            }
            else
            {
                sqlQuery = $"SELECT * FROM {tableName}_bkp WHERE CreatedDate >= {startDate} AND CreatedDate <= {endDate}";
            }

            // Get logs from backup table
            var logBackupTable = await _dbContext.ActiveDirectoryMonitorLogs
                .FromSqlRaw(sqlQuery)
                .AsNoTracking()
                .ToListAsync();

            // Combine and return results
            return logTable.Concat(logBackupTable).ToList();
        }
        catch (Exception)
        {
            // Log exception details if needed
            // _logger.LogError(ex, "Error querying backup table for ActiveDirectoryMonitorLog");

            // Return logs from active table if backup query fails
            return logTable;
        }

    }
    public async Task<List<ActiveDirectoryMonitorLog>> GetDetailByType(string type)
    {
        if (string.IsNullOrEmpty(type))
            return new List<ActiveDirectoryMonitorLog>();

        return await _dbContext.ActiveDirectoryMonitorLogs
            .AsNoTracking()
            .Active()
            .Where(x => x.Type == type)
            .ToListAsync();
    }
    private string GetDatabaseNameFromConnectionString(string connectionString, string provider)
    {
        if (string.IsNullOrEmpty(connectionString) || string.IsNullOrEmpty(provider))
            throw new ArgumentException("Connection string or provider cannot be null or empty");

        DbConnectionStringBuilder builder = provider.ToLower() switch
        {
            "mysql" => new MySqlConnectionStringBuilder(connectionString),
            "oracle" => new OracleConnectionStringBuilder(connectionString),
            "mssql" => new SqlConnectionStringBuilder(connectionString),
            "npgsql" => new NpgsqlConnectionStringBuilder(connectionString),
            _ => throw new ArgumentException($"Unsupported provider name: {provider}")
        };

        if (builder.TryGetValue("Database", out var databaseName))
            return databaseName.ToString();

        if (builder.TryGetValue("User Id", out var dbName))
            return dbName.ToString()?.ToUpper();

        throw new ArgumentException("Unable to extract database name from connection string.");
    }
    public virtual string GetTableName<T>()
    {
        var entityType = DbContext.Model.FindEntityType(typeof(T));

        return entityType?.GetTableName() ?? throw new ArgumentException($"Table name not found for type {typeof(T).Name}");
    }
    public virtual async Task<bool> IsTableExistAsync(string tableName, string schemaName, string providerName)
    {
        if (string.IsNullOrEmpty(tableName) || string.IsNullOrEmpty(schemaName) || string.IsNullOrEmpty(providerName))
            return false;

        var connection = DbContext.Database.GetDbConnection();
        bool wasOpen = connection.State == ConnectionState.Open;

        try
        {
            if (!wasOpen)
                await connection.OpenAsync();

            var isOracle = providerName.ToLower() == "oracle";
            var schemaTable = isOracle
                ? await connection.GetSchemaAsync("Tables", new[] { schemaName, tableName })
                : await connection.GetSchemaAsync("Tables", new[] { null, schemaName, tableName });

            var schema = isOracle ? "OWNER" : "TABLE_SCHEMA";
            return schemaTable.Rows.OfType<DataRow>().Any(row =>
                row[schema].ToString() == schemaName &&
                row["TABLE_NAME"].ToString() == tableName);
        }
        catch (Exception)
        {
            // Log exception details if needed
            // _logger.LogError(ex, $"Error checking if table {tableName} exists in schema {schemaName}");
            return false;
        }
        finally
        {
            if (!wasOpen && connection.State == ConnectionState.Open)
                await connection.CloseAsync();
        }

    }
}