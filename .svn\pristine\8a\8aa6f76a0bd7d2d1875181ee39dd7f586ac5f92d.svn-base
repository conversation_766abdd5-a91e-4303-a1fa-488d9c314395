﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.DataSetModel.DataSetModel;


<style>
    .btn-block + .btn-block {
        margin-top: 5px;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .btn-default {
        color: #333;
        background-color: #fff;
        border-color: #ccc;
    }

    .swap-btn button:hover {
        background-color: var(--bs-primary);
        color: #fff
    }
</style>
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl">
    <form class="modal-content" asp-area="Admin" id="CreateForm" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-configure-dataset"></i><span>Dataset Configuration</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="form-label">Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-name"></i></span>
                    <input asp-for="DataSetName" type="text" maxlength="100" id="datasetName" class="form-control" placeholder="Enter Dataset Name" autocomplete="off" />
                    <input asp-for="Id" id="id" type="hidden" class="form-control" />
                </div>
                <span asp-validation-for="DataSetName" id="DataSetName-error"></span>
            </div>
            <div class=" form-group">
                <div class="form-label">Description <small class="text-secondary">( Optional )</small></div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-description"></i></span>
                    <input type="text" asp-for="Description" class="form-control" id="datasetDescription" placeholder="Enter Description" maxlength="500" />
                </div>
                <span asp-validation-for="Description" id="Description-error"></span>
            </div>
            <div class="row g-3 align-items-center">

                <div class="col">
                    <div class="form-group">
                        <div class="form-label">Schema Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-data-lag"></i></span>
                            <select id="datasetSchemaName" class="form-select-modal schemaName" aria-label="Default select example"
                                    data-live-search="true" data-placeholder="Select Schema Name" required>

                                @foreach (var schema in Model.SchemaList)
                                {
                                    <option id="@schema.Value" value="@schema.Text">@schema.Text</option>
                                }
                            </select>
                        </div>
                        <span id="selectschemaerror"></span>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group" id="table_hide">
                        <div class="form-label">Table Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-table"></i></span>
                            <select asp-for="PrimaryTableName" id="datasetTableName" class="form-select-modal tableName"
                                    data-placeholder="Select Table Name">
                            </select>
                        </div>
                        <input asp-for="TableAccessId" id="tableAccessId" type="hidden" class="form-control" />
                        <span asp-validation-for="PrimaryTableName" id="selecttableerror"></span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-label">Column Name</div>
                <div class="col-5">

                    <select id="datasetColumnName" class="form-control border border-dark-subtle" size="10" multiple="multiple">
                    </select>
                </div>

                <div class="col-2 swap-btn">

                    <button type="button" title="Overall Actions Move" id="datasetColumnName_rightAll" class="btn btn-default btn-block"><i class="cp-right-doublearrow"></i></button>
                    <button type="button" title="Single Action Move" id="datasetColumnName_rightSelected" class="btn btn-default btn-block"><i class="cp-rignt-arrow"></i></button>
                    <button type="button" title="Single Action Back" id="datasetColumnName_leftSelected" class="btn btn-default btn-block"><i class="cp-left-arrow"></i></button>
                    <button type="button" title="Overall Action Back" id="datasetColumnName_leftAll" class="btn btn-default btn-block"><i class="cp-left-doublearrow"></i></button>
                </div>

                <div class="col-5">
                    <select id="datasetColumnName_to" class="form-control border border-dark-subtle" size="10" multiple="multiple"></select>
                </div>
            </div>
            </br>
            <div class="d-flex align-items-center gap-2">
                <div class="form-group w-100 flex-fill">
                    <div class="form-label">Stored Query</div>
                    <textarea class="form-control border " id="datasetStoredQuery" placeholder="Enter Stored Query"></textarea>
                    <div>
                        @*  <input asp-for="StoredQuery" id="StoredQueryHidden" type="hidden" class="form-control" placeholder="Select StoredQuery" /> *@
                    </div>
                    <span asp-validation-for="StoredQuery" id="StoredQuery-error"></span>
                </div>
                <button type="button" class="btn btn-primary btn-block" style="width:80px;" id="btnRunQuery">
                    Run
                    <div id="datasetLoader" class="spinner-border text-white ms-2 mt-1 p-1 d-none" style=" width: 0.8rem; height: 0.8rem;">
                        <span class=" visually-hidden">Loading...</span>
                    </div>
                </button>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between pt-2">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
            </div>
        </div>
    </form>
</div>


@section Scripts
{
    @{
        <partial name="_ValidationScriptsPartial" />
    }
}
