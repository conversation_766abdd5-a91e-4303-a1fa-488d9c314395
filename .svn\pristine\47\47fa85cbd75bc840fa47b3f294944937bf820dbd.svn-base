﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IHeatMapStatusService
{
    //Task<HeatMapStatusResponse> CreateAsync(HeatMapStatusModel heatMapStatusModel);
    //Task<HeatMapStatusResponse> UpdateAsync(HeatMapStatusModel heatMapStatusModel);
    Task<PaginatedResult<HeatMapStatusListVm>> GetPaginatedHeatMapStatus(GetHeatMapStatusPaginatedListQuery query);
    Task<BaseResponse> CreateAsync(CreateHeatMapStatusCommand createHeatMapStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdateHeatMapStatusCommand updateHeatMapStatusCommand);
    Task<BaseResponse> DeleteAsync(string companyId);
    Task<HeatMapStatusDetailVm> GetHeatMapStatusById(string id);
    Task<List<HeatMapStatusListVm>> GetHeatMapStatus();
    Task<ImpactDetailVm> GetImpactDetail(string businessServiceId);
    Task<HeatMapStatusByInfraObjectandEntityIdVm> GetHeatMapStatusByInfraObjectIdAndEntityId(string infraObjectId, string entityId);
    Task<List<HeatMapStatusListVm>> GetHeatMapStatusByType(string businessServiceId, string type,bool isAffected);
    Task<BusinessServiceAvailabilityVm> GetBusinessServiceAvailability();
    Task<BusinessFunctionAvailabilityVm> GetBusinessFunctionAvailability();
}