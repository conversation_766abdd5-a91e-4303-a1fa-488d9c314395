﻿using ContinuityPatrol.Application.Features.ServerLog.Commands.Create;
using ContinuityPatrol.Application.Features.ServerLog.Commands.Delete;
using ContinuityPatrol.Application.Features.ServerLog.Commands.Update;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.ServerLogModel;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ServerLog.Queries.GetNameUnique;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class ServerLogService : BaseService, IServerLogService
{
    public ServerLogService(IHttpContextAccessor accessor) : base(accessor)
    {

    }
    public async Task<PaginatedResult<ServerLogListVm>> GetPaginatedServerLogList(GetServerLogPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Server Log Paginated List");

        return await Mediator.Send(query);
    }
    public async Task<BaseResponse> CreateAsync(CreateServerLogCommand createServerLog)
    {
        Logger.LogDebug($"Create Server Log '{createServerLog.Name}'");

        return await Mediator.Send(createServerLog);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateServerLogCommand updateServerLog)
    {
        Logger.LogDebug($"Update Server Log '{updateServerLog.Name}'");

        return await Mediator.Send(updateServerLog);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Server Log Reference Id");

        Logger.LogDebug($"Delete Server Log Details by Id '{id}'");

        return await Mediator.Send(new DeleteServerLogCommand { Id = id });
    }
    public async Task<List<ServerLogListVm>> GetServerLogList()
    {
        return await Mediator.Send(new GetServerLogListQuery());
    }
    public async Task<GetServerLogDetailVm> GetServerLogDetail(string id)
    {
        return await Mediator.Send(new GetServerLogDetailQuery { Id = id });
    }

    public async Task<bool> IsServerLogNameUnique(string name, string? id)
    {
        return await Mediator.Send(new GetServerLogNameUniqueQuery { Name = name, Id = id });
    }
}