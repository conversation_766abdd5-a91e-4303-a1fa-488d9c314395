﻿namespace ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;

public class GetBusinessFunctionNameByBusinessServiceIdQueryHandler : IRequestHandler<
    GetBusinessFunctionNameByBusinessServiceIdQuery, List<GetBusinessFunctionNameByBusinessServiceIdVm>>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IMapper _mapper;

    public GetBusinessFunctionNameByBusinessServiceIdQueryHandler(
        IBusinessFunctionRepository businessFunctionRepository, IMapper mapper)
    {
        _businessFunctionRepository = businessFunctionRepository;
        _mapper = mapper;
    }

    public async Task<List<GetBusinessFunctionNameByBusinessServiceIdVm>> Handle(
        GetBusinessFunctionNameByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var businessFunctions =
            await _businessFunctionRepository.GetBusinessFunctionListByBusinessServiceId(request.Id);

        var businessFunctionsDto = _mapper.Map<List<GetBusinessFunctionNameByBusinessServiceIdVm>>(businessFunctions);

        return businessFunctionsDto;
    }
}