let monitoringJobUrl = {
    nameExistUrl: "Manage/MonitoringJob/IsJobNameExist",
    getPagination: "/Manage/MonitoringJob/GetPagination",
    updateJobState: "/Manage/MonitoringJob/UpdateJobState",
    resetMonitoringJob: "/Manage/MonitoringJob/ResetMonitoringJob",
    getGroupNodeList: "/Manage/MonitoringJob/GetGroupNodeList",
    getSolutionTypeByPolicy: "/Manage/MonitoringJob/GetSolutionTypeByPolicy",
    getGroupPolicies: "Manage/MonitoringJob/GetGroupPolicies",
    getInfraObjectListByReplicationTypeId: "Manage/MonitoringJob/GetInfraObjectListByReplicationTypeId",
    getTemplateByReplicationTypeId: "Manage/MonitoringJob/GetTemplateByReplicationTypeId",
}
let selectedValues = [], solutiontype = [], GroupPolicy = '', Arraydata = [], dataTable = "";
let solutionTypeID = "";
const exceptThisSymbols = ["e", "E", "+", "-", "."];

$(function () {
    let createPermission = $("#ConfigurationCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#ConfigurationDelete").data("delete-permission").toLowerCase();

    if (createPermission == 'false') {
        $("#CreteButton").removeClass('#CreteButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#tblJobManagement').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""

            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": monitoringJobUrl.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 2 ? "name" : sortIndex === 3 ? "templateName" : sortIndex === 4 ? "nodeName" :
                        sortIndex === 5 ? "solutionType" : sortIndex === 6 ? "scheduleTime" : sortIndex === 7 ? "lastExecutionTime" : sortIndex === 8 ? "status" : sortIndex === 9 ? "state" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json?.data?.totalPages;
                        json.recordsFiltered = json?.data?.totalCount;
                        if (json?.data?.data?.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        return json?.data?.data
                    } else {
                        errorNotification(json)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [2, 3, 4, 5, 6, 7, 8, 9, 10],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,
                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {
                        return `<input type="checkbox" name="rowCheckbox" statename="${data.state}" class="${data.state}" form-check-input custom-cursor-default-hover" checkid="${data.id}" id="${data.state}">`;
                    }
                },
                {
                    "data": "name", "name": "Job Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data || "NA"}">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "templateName", "name": "Template Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data || "NA"}">${data || "NA"}</span>`;
                        }
                        return data;
                    }
                }, {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title="${data || "NA"}">${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "solutionType", "name": "Solution Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        solutiontype.push(data)
                        if (type === 'display') {
                            return `<span title="${data || "NA"}">${data || "NA"}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Schedule Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data || "NA"}">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "Last Monitoring Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data || "NA"}">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data == "Running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data == "Success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }

                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data || "NA"}">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {
                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-danger me-1";
                        }
                        return `<td><i class="${iconClass}" id="icon" title="${data || "NA"}" ></i></td>
                              <td><span id="jobmanagestate">${data || "NA"}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {

                        let errorVisible = row?.exceptionMessage === null || row?.exceptionMessage === undefined || row?.exceptionMessage === '';
                        let errmsg = row?.exceptionMessage
                        let rowdata = JSON.stringify(row)

                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                            <span role="button" title="Edit"  class="jobEditButton ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${btoa(rowdata)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="jobDeleteButton ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span  title="Reset" class="jobResetButton" data-job='${btoa(rowdata)}' role="button">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                      <span title="Error Message" job-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}"   role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">                                     
                                            <span role="button" title="Edit"  class="jobEditButton ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"  data-job='${btoa(rowdata)}'>
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete"  class="icon-disabled ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span title="Reset"  class="jobResetButton" data-job='${btoa(rowdata)}' role="button">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                        <span title="Error Message" job-error_message="${btoa(errmsg)}" class=" Error-button ${errorVisible ? 'd-none' : ''}" role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                                                                        </span>      
                                </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">                                     
                                            <span role="button" title="Edit" class="icon-disabled ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete"  class="jobDeleteButton ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}" data-job-id="${row.id}" data-job-name="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span title="Reset" class="icon-disabled" role="button">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>      
                                                        <span title="Error Message" job-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}" role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                        else {
                            return `
                        <div class="d-flex align-items-center gap-2">                                     
                                            <span role="button" title="Edit" class="icon-disabled ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}">
                                                <i class="cp-edit"></i>
                                            </span>  
                                            <span role="button" title="Delete" class="icon-disabled ${row?.status == "Running" && row?.state == "Active" ? 'form-delete-disable' : ''}"><i class="cp-Delete"></i>
                                                </span>      
                                                    <span title="Reset" class="icon-disabled" role="button">
                                                        <i class="cp-job-reset"></i>                                    
                                                    </span>  
                                                        <span title="Error Message" job-error_message="${btoa(errmsg)}" class="Error-button ${errorVisible ? 'd-none' : ''}" role="button" data-bs-toggle="modal"
                                                       data-bs-target="#ErrorModal">
                                                        <i class="cp-fail-back blink text-danger"></i>                                    
                                                    </span>      
                                </div>`;
                        }
                    }, "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });

    $('#search-inp').on('input', monitordebouncedebounce(function (e) {
        const inputValue = $('#search-inp').val();
        const checkboxes = [
            $("#JobName"),
            $("#Templatename"),
            $("#Solutiontype"),
            $("#State")
        ];

        checkboxes.forEach(checkbox => {
            if (checkbox.is(':checked')) {
                selectedValues.push(checkbox.val() + inputValue);
            }
        });
        var currentPage = dataTable.page.info().page + 1;

        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e.target.value && json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }));

    let monthInput = document.getElementById("lblMonth");
    let today = new Date();
    let currentYear = today.getFullYear();
    let currentMonth = today.getMonth() + 1;
    let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    monthInput.setAttribute("min", minMonth);
    monthInput.setAttribute("max", maxMonth);

    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const hours = String(today.getHours()).padStart(2, '0');
    const minutes = String(today.getMinutes()).padStart(2, '0');
    const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
    const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;

    const datetimeInput = document.getElementById('datetimeCron');
    datetimeInput.min = minformattedDate;
    datetimeInput.max = maxformattedDate;
})

$('#selectExecutionPolicy').on('change', async function () {
    GroupPolicy = $(this).val();
    if (GroupPolicy == '1') {
        $('#groupPolicyDiv').show();
        $("#InfraObject_Name").css("display", "block");
        $("#selectGroupPolicy, #selectInfraObjectName, #selectTemplateName, #selectSolutionType")
            .val(null)
            .trigger('change.select2');

        $('#selectTemplateName').select2('destroy').select2()

        //$("#selectGroupPolicy,#selectInfraObjectName, #selectTemplateName,#selectSolutionType").val('').trigger('change');
        //$('#GroupPolicy-error,#InfraObjectName-error,#TemplateName-error').text('').removeClass('field-validation-error');
        ClearJobErrorElements();
    }
    else {
        $('#groupPolicyDiv').hide();
        $("#InfraObject_Name").css("display", "none")
        $("#selectTemplateName,#selectSolutionType").val('').trigger('change');
        $('#TemplateName-error').text('').removeClass('field-validation-error');
    }
    await solutiontypeData(GroupPolicy);
    validateJobDropDown($(this).val(), "Select execution policy", $('#ExecutionPolicy-error'));
});

$('#Activebtn').on('click', async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {

        if (obj.checked && obj.id != "Active") {
            datas.push({
                "id": obj.getAttribute("checkid"),
                "state": "Active"
            })
        }
    });
    if (datas?.length) {
        await $.ajax({
            url: monitoringJobUrl.updateJobState,
            type: 'PUT',
            data: {
                "updateJobStates": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {

                if (result?.success) {
                    let data = result?.data
                    $('input[name="rowCheckbox"], input[name="checkboxAll"]').prop("checked", false);
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        dataTable.ajax.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {

            if (datas?.length == 0) {
                $('input[name="rowCheckbox"], input[name="checkboxAll"]').prop("checked", false);
                notificationAlert("warning", "Jobs state has already updated to 'Active' state")
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            }
        }
    }
})

$('#Inactivebtn').on('click', async function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {

        if (obj.checked && obj.id != "InActive") {
            datas.push({
                "id": obj.getAttribute("checkid"),
                "state": "InActive"
            })
        }
    })
    if (datas?.length) {
        await $.ajax({
            url: monitoringJobUrl.updateJobState,
            type: 'PUT',
            data: {
                "updateJobStates": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {

                if (result?.success) {
                    let data = result?.data
                    $('input[name="rowCheckbox"], input[name="checkboxAll"]').prop("checked", false);
                    notificationAlert("success", data?.message)
                    setTimeout(() => {
                        dataTable.ajax.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="checkboxAll"]').prop("checked") == true || $('input[name="rowCheckbox"]').prop("checked") == true) {
            if (datas?.length == 0) {
                $('input[name="rowCheckbox"], input[name="checkboxAll"]').prop("checked", false);
                notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            }
        }
    }
})

$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        $('input[name="rowCheckbox"]').prop("checked", e.target.checked);
    }, 100);
});

$("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
    $('input[name="checkboxAll"]').prop("checked", false)
})

$('#selectTemplateName').on('change', async function () {
    const $selectedTemplate = $("#selectTemplateName option:selected");
    const templateId = $selectedTemplate.attr('id');
    const templateName = $selectedTemplate.text();

    if (templateId) {
        $('#textTemplateId').val(templateId);
        $('#textTemplateName').val(templateName);
        const value = $(this).val();
        let filtervalue = $.grep(value, function (value) {
            return value !== "";
        });
        validateJobDropDown(filtervalue, "Select template name", $('#TemplateName-error'));
    }
});

$('#selectInfraObjectName').on('change', async function () {
    var selectInfraObjectName = $(this).find('option:selected');
    Arraydata = []
    let strArrayData = '';
    selectInfraObjectName.each(function () {
        let option = $(this);
        let id = option.attr('id');
        let obj = { InfraObjectId: id, InfraObjectName: option.text() };
        Arraydata.push(obj)
    });
    strArrayData = JSON.stringify(Arraydata)
    $('#textInfraObjectProperties').val(strArrayData)
    validateJobDropDown(selectInfraObjectName.val(), "Select infraObject name", $('#InfraObjectName-error'));
});

// Error message
$('#tblJobManagement').on('click', '.Error-button', function () {
    let noData = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img" style="padding:10px">'
    let job_error_message = atob($(this).attr('job-error_message'))

    if (!job_error_message || job_error_message == 'null') {
        $("#error_message").css('text-align', 'center').html(noData);
    } else {
        $('#error_message').text(job_error_message);
    }
});

//Delete
$('#tblJobManagement').on('click', '.jobDeleteButton', function () {
    var jobId = $(this).data('job-id');
    var jobName = $(this).data('job-name');
    $("#deleteData").attr("title", jobName).text(jobName);
    $('#textDeleteId').val(jobId);
});

//Update   
$('#tblJobManagement').on('click', '.jobEditButton', function () {
    const jobData = atob($(this).data("job"));
    const parsedData = JSON.parse(jobData);
    populateModalFields(parsedData);
    Tab_selection(parsedData);
    Tab_schedule_type(parsedData);
    $('#SaveFunction').text("Update");
    ClearJobErrorElements();
    $('#CreateModal').modal('show');
});

//reset
$('#tblJobManagement').on('click', '.jobResetButton', async function () {
    let resetdata = atob($(this).data("job"))
    let jobData = JSON.parse(resetdata);
    jobData.__RequestVerificationToken = gettoken()
    await $.ajax({
        url: monitoringJobUrl.resetMonitoringJob,
        type: 'POST',
        data: jobData,
        success: function (result) {

            if (result?.success) {
                notificationAlert("success", result?.data?.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                errorNotification(result)
            }
        }
    });
});

$('#textJobName').on('input', monitordebouncedebounce(async function () {
    var jobId = $('#textJobId').val();
    let value = await sanitizeInput($("#textJobName").val());
    $("#textJobName").val(value);
    validateJobName(value, jobId, monitoringJobUrl.nameExistUrl);
}));

$('#selectSolutionType').on('change', async function () {
    let value = $("#selectSolutionType option:selected").attr('id');
    let selectedText = $("#selectSolutionType option:selected").text();

    if (value) {
        await getInfraObjectDetails(value);
        $("#solutionTypeId").val(value);
        $("#solutionType").val(selectedText);
        validateJobDropDown(value, "Select solution type", $('#SolutionType-error'));
    }
});

$('#selectGroupPolicy').on('change', async function () {
    let value = $("#selectExecutionPolicy").val();

    if (value == "1") {
        let Id = $("#selectGroupPolicy option:selected").attr('id');
        let selectedText = $("#selectGroupPolicy option:selected").text();
        $('#policyid').val(Id);
        $('#groupPolicyName').val(selectedText);
        validateJobDropDown($(this).val(), "Select group node policy", $('#GroupPolicy-error'));
    }
    if (value == "2") {
        $('#policyid').val("");
        $('#groupPolicyName').val("");
    }
});

$('#textStateActive, #textStateInactive').on('click', function () {
    ValidateRadioButton($('#state-error'));
});

$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});

$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || $(this).val() > 23) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || $(this).val() > 59) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});

$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})

$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select time", $('#CroneveryHour-error'));
});

$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});

$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select time", $('#CronddlHour-error'));
});

$('#ddlMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#ddlMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select hours", $('#CronddlMin-error'));
});

$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select time", $('#MonthlyHours-error'));
});

$('.datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val())
    let currentdate = new Date(srvTime())

    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});

$('#lblMonth').on("change", function () {
    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    let selectedDate = new Date($(this).val());
    let currentDate = new Date();

    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)

    for (let i = 0; i < daysInmonth; i++) {
        let data = i + 1;

        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            $(this).css("display", checkboxValue > data ? "none" : "block");
        })

        $(".checklabel").each(function () {
            let checkboxValue = parseInt($(this).text());
            $(this).css("display", checkboxValue > data ? "none" : "block");
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
        $("#CronMon-error").text("").removeClass("field-validation-error")
    } else {
        $('input[name="Monthyday"]').each(function () {
            var checkboxValue = parseInt($(this).val());

            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);

            }
        })
    }
});

$('input[name=weekDays]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day(s)", $('#CronDay-error'));
});

$('input[name=Monthyday]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date(s)", $('#CronMon-error'));
});

$('.nav-link, input[name="switchPlan"]').on('click', function () {
    ClearCroneElements();
});

$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});

$('#SaveFunction').on("click", async function () {
    let form = $("#CreateForm");
    GetIsSchedule();
    Get_ScheduleTypes();

    let isName = await validateJobName($("#textJobName").val(), $("#textJobId").val(), monitoringJobUrl.nameExistUrl);
    let isPolicy = validateJobDropDown($('#selectExecutionPolicy').val(), "Select execution policy", $('#ExecutionPolicy-error'));
    let isSolutionType = validateJobDropDown($('#selectSolutionType option:selected').val(), "Select solution type", $('#SolutionType-error'));
    let isGroupPolicy = validateJobDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#GroupPolicy-error'));
    let isWorkflow = validateJobDropDown($('#selectTemplateName').val(), "Select workflow templates", $('#TemplateName-error'));
    let infraobject = validateJobDropDown($("#selectInfraObjectName option:selected").val(), "Select infraObject name", $('#InfraObjectName-error'));
    let isStateActive = ValidateRadioButton($('#state-error'));
    let isScheduler = CronValidation();

    let crontype = $("#datetimeCronlist").val()
    let { CronExpression, listcron } = JobCronExpression();
    document.getElementById("textCronExpression").value = CronExpression;
    document.getElementById("textScheduleTime").value = listcron;
    document.getElementById("cronexpresstype").value = crontype
    document.getElementById("textStatus").value = "Pending"

    if ($('#SaveFunction').text() === 'Save') {
        $('#textStatus').val('Pending')
    }
    if (isName && isSolutionType && isPolicy && isStateActive && isScheduler && isWorkflow && (GroupPolicy == '1' ? isGroupPolicy : true) && ($('#selectExecutionPolicy').val() == '1' ? infraobject : true)) {
        form.trigger("submit");
    }
});

$("#CreteButton").on('click', function () {
    $('#SaveFunction').text("Save");
    $('#groupPolicyDiv').hide();
    $("#textStateActive").prop("checked", true)
    $("#InfraObject_Name").css("display", "none")
    solutionTypeID = "";
    clearJobFields();
    jobOnce();
});

$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").on("click", function () {
    $("#CronMin-error, #CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error, #CronddlHour-error,#CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})

$("#nav-Monthly-tab").on("click", function () {
    if ($("#SaveFunction").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})

$(".monitorbtn-cancel").on("click", function () {
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
})
