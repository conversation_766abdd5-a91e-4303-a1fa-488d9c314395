using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BusinessServiceAvailabilityFixture : IDisposable
{
    public List<BusinessServiceAvailability> BusinessServiceAvailabilityPaginationList { get; set; }
    public List<BusinessServiceAvailability> BusinessServiceAvailabilityList { get; set; }
    public BusinessServiceAvailability BusinessServiceAvailabilityDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public BusinessServiceAvailabilityFixture()
    {
        var fixture = new Fixture();

        BusinessServiceAvailabilityList = fixture.Create<List<BusinessServiceAvailability>>();

        BusinessServiceAvailabilityPaginationList = fixture.CreateMany<BusinessServiceAvailability>(20).ToList();

        BusinessServiceAvailabilityDto = fixture.Create<BusinessServiceAvailability>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
