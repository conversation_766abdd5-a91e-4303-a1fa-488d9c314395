﻿using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class MSSQLMirrorControllerShould
    {
        private readonly Mock<IDashboardViewService> _mockDashboardViewService;
        private readonly Mock<ILogger<MSSQLMirrorController>> _mockLogger;
        private readonly MSSQLMirrorController _controller;

        public MSSQLMirrorControllerShould()
        {
            _mockDashboardViewService = new Mock<IDashboardViewService>();
            _mockLogger = new Mock<ILogger<MSSQLMirrorController>>();
            _controller = new MSSQLMirrorController(_mockDashboardViewService.Object, _mockLogger.Object);
        }

        [Fact]
        public void List_Returns_ViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_Returns_GetByEntityIdVm()
        {
            
            var monitorId = "test-monitor-id";
            var type = "test-type";
            var expectedVm = new GetByEntityIdVm();
            _mockDashboardViewService
                .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
                .ReturnsAsync(expectedVm);

            
            var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
            Assert.NotNull(result);
            Assert.Equal(expectedVm, result);
            _mockDashboardViewService.Verify(service =>
                service.GetMonitorServiceStatusByIdAndType(monitorId, type), Times.Once);
        }

        [Fact]
        public async Task GetMonitorServiceStatusByIdAndType_Logs_Error_On_Exception()
        {
            
            var monitorId = "test-monitor-id";
            var type = "test-type";
            _mockDashboardViewService
                .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
                .ThrowsAsync(new Exception("Test exception"));

            
            var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
            Assert.Null(result);
            
        }
    }
}
