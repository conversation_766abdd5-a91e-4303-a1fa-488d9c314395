using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class GroupPolicyFixture : IDisposable
{
    public List<GroupPolicy> GroupPolicyPaginationList { get; set; }
    public List<GroupPolicy> GroupPolicyList { get; set; }
    public GroupPolicy GroupPolicyDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public GroupPolicyFixture()
    {
        var fixture = new Fixture();

        GroupPolicyList = fixture.Create<List<GroupPolicy>>();

        GroupPolicyPaginationList = fixture.CreateMany<GroupPolicy>(20).ToList();

        GroupPolicyPaginationList.ForEach(x => x.CompanyId = CompanyId);

        GroupPolicyList.ForEach(x => x.CompanyId = CompanyId);

        GroupPolicyDto = fixture.Create<GroupPolicy>();

        GroupPolicyDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
