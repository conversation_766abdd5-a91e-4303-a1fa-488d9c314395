using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertInformationFixture : IDisposable
{
    public List<AlertInformation> AlertInformationPaginationList { get; set; }
    public List<AlertInformation> AlertInformationList { get; set; }
    public AlertInformation AlertInformationDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public AlertInformationFixture()
    {
        var fixture = new Fixture();

        AlertInformationList = fixture.Create<List<AlertInformation>>();

        AlertInformationPaginationList = fixture.CreateMany<AlertInformation>(20).ToList();

        AlertInformationPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertInformationPaginationList.ForEach(x => x.IsActive = true);

        AlertInformationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertInformationList.ForEach(x => x.IsActive = true);

        AlertInformationDto = fixture.Create<AlertInformation>();
        AlertInformationDto.ReferenceId = Guid.NewGuid().ToString();
        AlertInformationDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}