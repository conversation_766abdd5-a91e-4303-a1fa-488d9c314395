﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class TeamMasterRepositoryMocks
{
    public static Mock<ITeamMasterRepository> CreateTeamMasterRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamMasters);

        teamMasterRepository.Setup(repo => repo.AddAsync(It.IsAny<TeamMaster>())).ReturnsAsync((TeamMaster teamMaster) =>
        {
            teamMaster.Id = new Fixture().Create<int>();

            teamMaster.ReferenceId = new Fixture().Create<Guid>().ToString();

            teamMasters.Add(teamMaster);

            return teamMaster;
        });

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> UpdateTeamMasterRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamMasters);

        teamMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamMasters.SingleOrDefault(x => x.ReferenceId == i));

        teamMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TeamMaster>())).ReturnsAsync((TeamMaster teamMaster) =>
        {

            var index = teamMasters.FindIndex(item => item.Id == teamMaster.Id);

            teamMasters[index] = teamMaster;

            return teamMaster;
        });

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> DeleteTeamMasterRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamMasters);

        teamMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamMasters.SingleOrDefault(x => x.ReferenceId == i));

        teamMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TeamMaster>())).ReturnsAsync((TeamMaster teamMaster) =>
        {
            var index = teamMasters.FindIndex(item => item.Id == teamMaster.Id);

            teamMaster.IsActive = false;

            teamMasters[index] = teamMaster;

            return teamMaster;
        });

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> GetTeamMasterRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamMasters);

        teamMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamMasters.SingleOrDefault(x => x.ReferenceId == i));

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> GetTeamMasterEmptyRepository()
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<TeamMaster>());

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> GetTeamMasterNamesRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.GetTeamMasterNames()).ReturnsAsync(teamMasters);

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> GetTeamMasterNameUniqueRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        teamMasterRepository.Setup(repo => repo.IsTeamMasterNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => teamMasters.Exists(x => x.GroupName == i && x.ReferenceId == j));

        return teamMasterRepository;
    }

    public static Mock<ITeamMasterRepository> GetPaginatedTeamMasterRepository(List<TeamMaster> teamMasters)
    {
        var teamMasterRepository = new Mock<ITeamMasterRepository>();

        var queryableTeamMaster = teamMasters.BuildMock();

        teamMasterRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableTeamMaster);

        return teamMasterRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateTeamMasterEventRepository(List<UserActivity> userActivities)
    {
        var mockTeamMasterEventRepository = new Mock<IUserActivityRepository>();

        mockTeamMasterEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockTeamMasterEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockTeamMasterEventRepository;
    }
}
