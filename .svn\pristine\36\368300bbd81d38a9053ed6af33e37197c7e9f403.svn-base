﻿using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class SolutionHistoryService : BaseService, ISolutionHistoryService
{
    public SolutionHistoryService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<SolutionHistoryByActionIdQueryVm>> GetSolutionHistoryByActionId(string actionId)
    {
        Guard.Against.InvalidGuidOrEmpty(actionId, "Action Id");

        Logger.LogInformation($"Get SolutionHistory Detail by Action Id '{actionId}'");

        return await Mediator.Send(new GetSolutionHistoryByActionIdQuery { ActionId = actionId });
    }
}