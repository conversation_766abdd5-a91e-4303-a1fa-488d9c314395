using ContinuityPatrol.Domain.ViewModels.DriftManagementMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.DriftManagementMonitorStatus.Queries.GetList;

public class GetDriftManagementMonitorStatusListQueryHandler : IRequestHandler<GetDriftManagementMonitorStatusListQuery,
    List<DriftManagementMonitorStatusListVm>>
{
    private readonly IDriftManagementMonitorStatusRepository _driftManagementMonitorStatusRepository;
    private readonly IMapper _mapper;

    public GetDriftManagementMonitorStatusListQueryHandler(IMapper mapper,
        IDriftManagementMonitorStatusRepository driftManagementMonitorStatusRepository)
    {
        _mapper = mapper;
        _driftManagementMonitorStatusRepository = driftManagementMonitorStatusRepository;
    }

    public async Task<List<DriftManagementMonitorStatusListVm>> Handle(GetDriftManagementMonitorStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var driftManagementMonitorStatuss = await _driftManagementMonitorStatusRepository.ListAllAsync();

        if (driftManagementMonitorStatuss.Count <= 0) return new List<DriftManagementMonitorStatusListVm>();

        return _mapper.Map<List<DriftManagementMonitorStatusListVm>>(driftManagementMonitorStatuss);
    }
}