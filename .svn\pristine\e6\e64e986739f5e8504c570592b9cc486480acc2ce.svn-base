using ContinuityPatrol.Application.Features.BulkImportOperationGroup.Queries.GetByOperationId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationGroupModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperationGroup.Queries;

public class GetByOperationIdQueryTests : IClassFixture<BulkImportOperationGroupFixture>
{
    private readonly BulkImportOperationGroupFixture _bulkImportOperationGroupFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetByOperationIdQueryHandler _handler;

    public GetByOperationIdQueryTests(BulkImportOperationGroupFixture bulkImportOperationGroupFixture)
    {
        _bulkImportOperationGroupFixture = bulkImportOperationGroupFixture;

        _mockBulkImportOperationGroupRepository = BulkImportOperationGroupRepositoryMocks.CreateQueryBulkImportOperationGroupRepository(_bulkImportOperationGroupFixture.BulkImportOperationGroups);
        _mockMapper = new Mock<IMapper>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()))
            .Returns((List<Domain.Entities.BulkImportOperationGroup> entities) => entities.Select(entity => new BulkImportOperationGroupListVm
            {
                Id = entity.ReferenceId,
                BulkImportOperationId = entity.BulkImportOperationId,
                CompanyId = entity.CompanyId,
                Properties = entity.Properties,
                ProgressStatus = entity.ProgressStatus,
                Status = entity.Status,
                ErrorMessage = entity.ErrorMessage,
                ConditionalOperation = entity.ConditionalOperation,
                NodeId = entity.NodeId,
                InfraObjectName = entity.InfraObjectName
            }).ToList());

        _handler = new GetByOperationIdQueryHandler(
            _mockMapper.Object,
            _mockBulkImportOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationGroupListVm_When_OperationGroupsExist()
    {
        // Arrange
        var testOperationId = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First().BulkImportOperationId;
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBeGreaterThan(0);
        result.First().BulkImportOperationId.ShouldBe(testOperationId);
    }

    [Fact]
    public async Task Handle_Call_GetBulkImportOperationGroupByBulkImportOperationId_OnlyOnce()
    {
        // Arrange
        var testOperationId = "TestOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetBulkImportOperationGroupByBulkImportOperationId(testOperationId), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce_When_DataExists()
    {
        // Arrange
        var testOperationId = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First().BulkImportOperationId;
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoOperationGroupsExist()
    {
        // Arrange
        var nonExistentOperationId = "NonExistentOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = nonExistentOperationId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationId(nonExistentOperationId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_FilterByOperationId_When_MultipleGroupsExist()
    {
        // Arrange
        var specificOperationId = "SpecificOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = specificOperationId };

        // Setup repository to return only groups with the specific operation ID
        var filteredGroups = _bulkImportOperationGroupFixture.BulkImportOperationGroups
            .Where(x => x.BulkImportOperationId == specificOperationId).ToList();
        
        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationId(specificOperationId))
            .ReturnsAsync(filteredGroups);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.All(x => x.BulkImportOperationId == specificOperationId).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_MapEntitiesToViewModels_WithCorrectProperties()
    {
        // Arrange
        var testGroup = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First();
        testGroup.BulkImportOperationId = "TestOperationId";
        testGroup.CompanyId = "TestCompanyId";
        testGroup.Properties = "{\"test\":\"value\"}";
        testGroup.Status = "Pending";
        testGroup.ProgressStatus = "0/5";
        testGroup.InfraObjectName = "TestInfraObject";

        var query = new GetByOperationIdQuery { BulkImportOperationId = testGroup.BulkImportOperationId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBeGreaterThan(0);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe(testGroup.ReferenceId);
        firstItem.BulkImportOperationId.ShouldBe("TestOperationId");
        firstItem.CompanyId.ShouldBe("TestCompanyId");
        firstItem.Properties.ShouldBe("{\"test\":\"value\"}");
        firstItem.Status.ShouldBe("Pending");
        firstItem.ProgressStatus.ShouldBe("0/5");
        firstItem.InfraObjectName.ShouldBe("TestInfraObject");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var testOperationId = _bulkImportOperationGroupFixture.BulkImportOperationGroups.First().BulkImportOperationId;
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportOperationGroupListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportOperationGroupListVm>));
    }

    [Fact]
    public async Task Handle_NotCallMapper_When_NoDataExists()
    {
        // Arrange
        var nonExistentOperationId = "NonExistentOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = nonExistentOperationId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationId(nonExistentOperationId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationGroupListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PassCorrectOperationId_When_CallingRepository()
    {
        // Arrange
        var testOperationId = "SpecificTestOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetBulkImportOperationGroupByBulkImportOperationId(testOperationId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnEmptyListDirectly_When_CountIsZero()
    {
        // Arrange
        var testOperationId = "EmptyOperationId";
        var query = new GetByOperationIdQuery { BulkImportOperationId = testOperationId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationId(testOperationId))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationGroupListVm>));
        result.Count.ShouldBe(0);
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_HandleNullOrEmptyOperationId_When_QueryExecuted()
    {
        // Arrange
        var query = new GetByOperationIdQuery { BulkImportOperationId = "" };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationId(""))
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperationGroup>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Count.ShouldBe(0);
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetBulkImportOperationGroupByBulkImportOperationId(""), Times.Once);
    }
}
