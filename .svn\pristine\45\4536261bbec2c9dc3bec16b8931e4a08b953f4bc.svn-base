﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.SaveAs;

public class SaveAsReplicationEventHandler : INotificationHandler<SaveAsReplicationEvent>
{
    private readonly ILogger<SaveAsReplicationEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SaveAsReplicationEventHandler(ILogger<SaveAsReplicationEventHandler> logger,
        ILoggedInUserService userService, IUserActivityRepository userActivityRepository)
    {
        _userActivityRepository = userActivityRepository;
        _logger = logger;
        _userService = userService;
    }

    public async Task Handle(SaveAsReplicationEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"clone {Modules.Replication}",
            Entity = Modules.Replication.ToString(),
            ActivityType = ActivityType.SaveAs.ToString(),
            ActivityDetails = $"Replication '{notification.Name}' cloned successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Replication '{notification.Name}' cloned successfully.");
    }
}