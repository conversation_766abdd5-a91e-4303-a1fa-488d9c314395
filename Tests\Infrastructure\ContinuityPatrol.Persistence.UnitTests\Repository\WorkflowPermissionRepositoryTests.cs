using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.Extensions.Configuration;
using Xunit;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowPermissionRepositoryTests : IClassFixture<WorkflowPermissionFixture>
    {
        private readonly WorkflowPermissionFixture _fixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowPermissionRepository _repository;

        public WorkflowPermissionRepositoryTests(WorkflowPermissionFixture fixture)
        {
            _fixture = fixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            var config = new ConfigurationBuilder().Build();
            _repository = new WorkflowPermissionRepository(_dbContext, config);
        }

        [Fact]
        public async Task GetWorkflowPermissionByUserIdAsync_ReturnsMatchingPermissions()
        {
            var entity = _fixture.WorkflowPermissionDto;
            entity.UserProperties = "user1,group1";
            await _dbContext.WorkflowPermissions.AddAsync(entity);
            await _dbContext.SaveChangesAsync();

            var result = await _repository.GetWorkflowPermissionByUserIdAsync("user1");

            Assert.All(result, x => Assert.Contains("user1", x.UserProperties));
        }

        [Fact]
        public async Task GetWorkflowPermissionByUserIdAsync_ReturnsEmpty_WhenNoMatch()
        {
            var result = await _repository.GetWorkflowPermissionByUserIdAsync("nonexistent");

            Assert.Empty(result);
        }
    }
}