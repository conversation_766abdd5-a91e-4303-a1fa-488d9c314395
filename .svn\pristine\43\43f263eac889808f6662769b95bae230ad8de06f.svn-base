﻿namespace ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Delete;

public class DeleteInfraObjectSchedulerWorkflowDetailCommandHandler : IRequestHandler<
    DeleteInfraObjectSchedulerWorkflowDetailCommand, DeleteInfraObjectSchedulerWorkflowDetailResponse>
{
    private readonly IInfraObjectSchedulerWorkflowDetailRepository _infraObjectSchedulerWorkflowDetailRepository;

    public DeleteInfraObjectSchedulerWorkflowDetailCommandHandler(
        IInfraObjectSchedulerWorkflowDetailRepository infraObjectSchedulerWorkflowDetailRepository)
    {
        _infraObjectSchedulerWorkflowDetailRepository = infraObjectSchedulerWorkflowDetailRepository;
    }

    public async Task<DeleteInfraObjectSchedulerWorkflowDetailResponse> Handle(
        DeleteInfraObjectSchedulerWorkflowDetailCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _infraObjectSchedulerWorkflowDetailRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.InfraObjectSchedulerWorkflowDetail),
            new NotFoundException(nameof(Domain.Entities.InfraObjectSchedulerWorkflowDetail), request.Id));

        eventToDelete.IsActive = false;

        await _infraObjectSchedulerWorkflowDetailRepository.UpdateAsync(eventToDelete);

        var response = new DeleteInfraObjectSchedulerWorkflowDetailResponse
        {
            Message =
                Message.Delete(nameof(Domain.Entities.InfraObjectSchedulerWorkflowDetail),
                    eventToDelete.InfraObjectName),
            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}