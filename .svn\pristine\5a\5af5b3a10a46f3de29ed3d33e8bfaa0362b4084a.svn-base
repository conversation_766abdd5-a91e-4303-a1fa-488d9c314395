﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Replication.Events.InfraSummaryEvents.Create;

public class ReplicationInfraSummaryCreatedEventHandler : INotificationHandler<ReplicationInfraSummaryCreatedEvent>
{
    private readonly IInfraSummaryRepository _infraSummaryRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<ReplicationInfraSummaryCreatedEventHandler> _logger;

    public ReplicationInfraSummaryCreatedEventHandler(ILogger<ReplicationInfraSummaryCreatedEventHandler> logger,
        IInfraSummaryRepository infraSummaryRepository, ILoggedInUserService loggedInUserService)
    {
        _logger = logger;
        _infraSummaryRepository = infraSummaryRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task Handle(ReplicationInfraSummaryCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var infraSummary = await _infraSummaryRepository.GetInfraSummaryByType(createdEvent.Type);

        if (infraSummary is null)
        {
            await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
            {
                EntityName = Modules.Replication.ToString(),
                Type = createdEvent.Type,
                TypeId = createdEvent.TypeId,
                Logo = createdEvent.Logo,
                Count = 1,
                CompanyId = createdEvent.CompanyId,
                BusinessServiceId = createdEvent.BusinessServiceId
            });
        }
        else
        {
            infraSummary.EntityName = Modules.Replication.ToString();
            infraSummary.Logo = createdEvent.Logo;
            infraSummary.Count += 1;
            infraSummary.CompanyId = createdEvent.CompanyId;
            infraSummary.BusinessServiceId = createdEvent.BusinessServiceId;
            await _infraSummaryRepository.UpdateAsync(infraSummary);
        }

        _logger.LogInformation($"InfraSummary '{createdEvent.Type}' created successfully.");
    }
}