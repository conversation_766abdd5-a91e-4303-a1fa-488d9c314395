using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Persistence.Persistence;

public partial class ApplicationDbContext : ICyberDbContext
{
    #region Context
    public DbSet<ResiliencyReadyWorkflowScheduleLog> ResiliencyReadyWorkflowScheduleLogs { get; set; }
	public DbSet<CyberAirGap> CyberAirGaps { get; set; }

	public DbSet<CyberSnaps> CyberSnaps { get; set; }

	public DbSet<CyberAlert> CyberAlerts { get; set; }

	public DbSet<CyberAirGapLog> CyberAirGapLogs { get; set; }

	public DbSet<CyberAirGapStatus> CyberAirGapStatus { get; set; }

    public DbSet<CyberJobManagement> CyberJobManagements { get; set; }

    public DbSet<CyberComponentGroup> CyberComponentGroups { get; set; }

    public DbSet<CyberComponent> CyberComponents { get; set; }

    public DbSet<CyberMappingHistory> CyberMappingHistory { get; set; }

    public DbSet<CyberComponentMapping> CyberComponentMappings { get; set; }
    #endregion
}
