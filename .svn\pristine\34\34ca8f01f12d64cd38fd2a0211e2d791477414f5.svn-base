﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.TeamMaster.Queries.GetList;

public class GetTeamMasterListQueryHandler : IRequestHandler<GetTeamMasterListQuery, List<TeamMasterDetailVm>>
{
    private readonly IMapper _mapper;
    private readonly ITeamMasterRepository _teamMasterRepository;

    public GetTeamMasterListQueryHandler(IMapper mapper, ITeamMasterRepository teamMasterRepository)
    {
        _mapper = mapper;
        _teamMasterRepository = teamMasterRepository;
    }

    public async Task<List<TeamMasterDetailVm>> Handle(GetTeamMasterListQuery request,
        CancellationToken cancellationToken)
    {
        var teamMasters = (await _teamMasterRepository.ListAllAsync()).ToList();

        if (teamMasters.Count <= 0) return new List<TeamMasterDetailVm>();

        var teamconfiguratioList = new List<TeamMasterDetailVm>();


        foreach (var teamConfiguration in teamMasters.Select(teamConfiguration =>
                     _mapper.Map<TeamMasterDetailVm>(teamConfiguration)))
        {
            Guard.Against.InvalidGuidOrEmpty(teamConfiguration.Id, ErrorMessage.Company.ParentCompanyIdCannotBeEmpty);

            teamconfiguratioList.Add(teamConfiguration);
        }

        return teamMasters.Count <= 0
            ? new List<TeamMasterDetailVm>()
            : _mapper.Map<List<TeamMasterDetailVm>>(teamMasters);
    }
}