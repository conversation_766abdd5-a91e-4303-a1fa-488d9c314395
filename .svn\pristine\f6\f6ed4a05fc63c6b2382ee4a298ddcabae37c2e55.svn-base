﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;

public class CreateBulkDataDataBaseListCommand
{
    public string Name { get; set; }
    public string DatabaseTypeId { get; set; }
    public string DatabaseType { get; set; }
    public string Type { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string Properties { get; set; }
    public string Logo { get; set; }
    public string ModeType { get; set; }
    public string LicenseId { get; set; }
    public string LicenseKey { get; set; }
    public string Version { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string ExceptionMessage { get; set; }

    public string FormVersion { get; set; }

    //[JsonIgnore]
    //public string BulkImportOperationId { get; set; }
    //[JsonIgnore]
    //public string BulkImportOperationGroupId { get; set; }
    public override string ToString()
    {
        return $"Name: {Name};";
    }

    //public void Sanitize()
    //{
    //    var props = GetType().GetProperties()
    //        .Where(p => p.PropertyType == typeof(string) && p.CanRead && p.CanWrite);

    //    foreach (var prop in props)
    //    {
    //        if (prop.GetValue(this) is string value)
    //        {
    //            prop.SetValue(this, value.Trim());
    //        }
    //    }
    //}
}