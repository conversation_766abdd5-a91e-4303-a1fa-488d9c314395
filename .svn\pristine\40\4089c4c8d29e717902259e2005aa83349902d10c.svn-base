using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.IncidentManagement.Events.Update;

public class IncidentManagementUpdatedEventHandler : INotificationHandler<IncidentManagementUpdatedEvent>
{
    private readonly ILogger<IncidentManagementUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public IncidentManagementUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<IncidentManagementUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(IncidentManagementUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} IncidentManagement",
            Entity = "IncidentManagement",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"IncidentManagement '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"IncidentManagement '{updatedEvent.Name}' updated successfully.");
    }
}