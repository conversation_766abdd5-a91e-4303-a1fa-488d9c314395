﻿namespace ContinuityPatrol.Domain.Entities;

public class RpoSlaDeviationReport : AuditableEntity
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public DateTime ProductionLogGeneratedTime { get; set; }
    public string ProductionArchiveLogSequence { get; set; }
    public DateTime AppliedLogTimeAtDr { get; set; }
    public string ArchiveLogAppliedAtDr { get; set; }
    public string DataLag { get; set; }
    public DateTime TimeStamp { get; set; }
}