using ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraMaster.Commands.Update;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.InfraMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraMasterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class InfraMastersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<InfraMasterListVm>>> GetInfraMasters()
    {
        Logger.LogDebug("Get All InfraMasters");

        return Ok(await Mediator.Send(new GetInfraMasterListQuery()));
    }

    [HttpGet("{id}", Name = "GetInfraMaster")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<InfraMasterDetailVm>> GetInfraMasterById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraMaster Id");

        Logger.LogDebug($"Get InfraMaster Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetInfraMasterDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<InfraMasterListVm>>> GetPaginatedInfraMasters([FromQuery] GetInfraMasterPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in InfraMaster Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateInfraMasterResponse>> CreateInfraMaster([FromBody] CreateInfraMasterCommand createInfraMasterCommand)
    {
        Logger.LogDebug($"Create InfraMaster '{createInfraMasterCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateInfraMaster), await Mediator.Send(createInfraMasterCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateInfraMasterResponse>> UpdateInfraMaster([FromBody] UpdateInfraMasterCommand updateInfraMasterCommand)
    {
        Logger.LogDebug($"Update InfraMaster '{updateInfraMasterCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraMasterCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteInfraMasterResponse>> DeleteInfraMaster(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraMaster Id");

        Logger.LogDebug($"Delete InfraMaster Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteInfraMasterCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsInfraMasterNameExist(string infraMasterName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(infraMasterName, "InfraMaster Name");

     Logger.LogDebug($"Check Name Exists Detail by InfraMaster Name '{infraMasterName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetInfraMasterNameUniqueQuery { Name = infraMasterName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


