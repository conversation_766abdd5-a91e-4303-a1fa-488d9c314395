﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <AssemblyName>ContinuityPatrol.Shared.Core</AssemblyName>
    <RootNamespace>ContinuityPatrol.Shared.Core</RootNamespace>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Behaviors\CachingBehavior.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ContinuityPatrol.Shared\ContinuityPatrol.Shared.csproj" />
  </ItemGroup>

</Project>
