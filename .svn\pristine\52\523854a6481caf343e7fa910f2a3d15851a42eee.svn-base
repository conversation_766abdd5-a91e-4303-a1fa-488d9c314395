﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.Create;

public class DatabaseCreatedEventHandler : INotificationHandler<DatabaseCreatedEvent>
{
    private readonly ILogger<DatabaseCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DatabaseCreatedEventHandler(ILoggedInUserService userService, ILogger<DatabaseCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DatabaseCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress?? "::1",
            Action = $"{ActivityType.Create} {Modules.Database}",
            Entity = Modules.Database.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Database '{createdEvent.DatabaseName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Database '{createdEvent.DatabaseName}' created successfully.");
    }
}