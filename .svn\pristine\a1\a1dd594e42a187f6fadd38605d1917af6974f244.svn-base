$(function () {
    let operatorArray = [
        "+", "-", "*", "/", "&&", "<", "<=", "=", "!=", ">", ">=", "AND", "OR", "XOR", "NOT", "EQV", "IMP", ".Contains"
    ];
    let operatorArrayContainer = $("#operatorArrayContainer");

    operatorArray.forEach(function (operator) {
        let spanElement = $("<span>")
            .addClass("border flex-fill Condition_Properties")
            .attr("role", "button")
            .text(operator);
        spanElement.on("click", function () {
            let clickedOperator = $(this).text();
            $("#condition").val(function (index, currentValue) {
                return currentValue + clickedOperator;
            });
        });
        operatorArrayContainer.append(spanElement);
    });
});

function handleDropAndSwitch(actionId, globalId, comment) {
    let condition = '"' + actionId.toLowerCase() + '"';
    condition = condition.replace(/["\s]+/g, "");
    $("#properticsHtml").empty();
    $("#properticsTitle").empty();

    let titleHtml = "";
    titleHtml += "<div class='card-title'>" + actionId + "</h6>";
    $("#properticsTitle").append(titleHtml)
    $("#containerId label").css({
        'font-weight': 'bold',
        'text-transform': 'capitalize'
    });

    var html = "";
    switch (condition) {
        case 'commontextline':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Common textline</label>";
            html += "<div class='input-group'>";
            html += "<textarea id='text' scriptvalue='@@text'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='text'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='text-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'if':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'elseif':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'catch':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'for':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'while':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'switch':
            html = "<div class='form-group scriptData' scriptCmd='" + comment + "'>";
            html += "<label class='animation-label'>Condition</label>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<textarea id='condition' scriptvalue='@@condition'  class='form-control propertiesInput' autofocus autocomplete='off' placeholder='Enter the Condition' rows='2' name='Condition'></textarea>";
            html += "</div>";
            html += "<div class='d-flex flex-wrap' id = 'operatorArrayContainer'>";
            html += "</div>";
            html += " <span id='Name-validaionerror' style='color:red;'></span>";
            html += "</div>";
            break;

        case 'token':
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Token</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' id='Token' scriptvalue='@@Token' autofocus autocomplete='off' placeholder='Enter Token ' type='text' required />";
            html += "</div>";
            html += " <span class='Tokenvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>RequiredEnter</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' id='Match' scriptvalue='@@Match' autofocus autocomplete='off' placeholder='Enter Match Name ' type='text' required />";
            html += "</div>";
            html += " <span class='Matchvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "else":
            break;

        case "endif":
            break;

        case "return":

        case "case":

        case "osconnect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@variable' id='variable' autofocus autocomplete='off' placeholder='Enter the Variable' name='Variable' type='text' required />";
            html += "</div>";
            html += " <span class='variablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "delay":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>"
            html += "<div class='form-group'>";
            html += " <div class='form-label'>Thread Sleep (Sec)</div>"
            html += " <div class='input-group'>"
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "   <input class='propertiesInput form-control' id='value' scriptvalue='@@value' autofocus autocomplete='off' placeholder='Thread Sleep (Sec)' name='Wait' type='number' min='0' required />"
            html += "  </div>"
            html += " <span class='valuevalidaion' style='color:red;'></span>";
            html += " </div>"

            html += " </div>"
            break;

        case "loops":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@value'  id='value' autofocus autocomplete='off' placeholder='Enter the Value' name='Value' type='text' required />";
            html += "</div>";
            html += " <span class='valuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "times":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@value'  id='value' autofocus autocomplete='off' placeholder='Enter the Value' name='Value' type='text' required />";
            html += "</div>";
            html += " <span class='valuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "wait":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Time</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "</div>";
            break;

        case "create":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<div class='form-check form-check-inline'>";
            html += "<input type='radio type' id='type1' name='type' value='File' class='form-check-input' onclick='updateScriptValue(this)'>";
            html += "<label for='type1'>File</label><br>";
            html += "</div>";
            html += "<div class='form-check form-check-inline ml-4'>";
            html += "<input type='radio type' id='type2' name='type' value='Folder' class='form-check-input' onclick='updateScriptValue(this)'>";
            html += "<label for 'type2'>Folder</label><br>";
            html += "</div>";
            html += "</div>";
            html += "<span class='typevalidation' style='color:red;'></span>";
            html += "<div scriptvalue='@@type'></div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Source<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Source'  id='Source' autofocus autocomplete='off' placeholder='Source Variable' name='Properties.Source' type='text' required />";
            html += "</div>";
            html += " <span class='Sourcevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<label class='form-label' for='birthdaytime'>Date and Time</label>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='datetime-local' class='propertiesInput form-control' scriptvalue='@@birthdaytime' pattern='[0 - 3][0 - 9].[01][0 - 9].[0 - 9]{ 4 }' max='9999-12-31T23:59' id='birthdaytime'  autofocus autocomplete='off' placeholder='birthdaytime' name='Properties.birthdaytime' required />";
            html += "</div>";
            html += " <span class='birthdaytimevalidation' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "copy":

        case "move":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<div class='form-check form-check-inline'>";
            html += "<input type='radio' id='type1' name='type' value='File' class='form-check-input'>"
            html += "<label for='type1'>File</label><br>"
            html += "</div>";
            html += "<div class='form-check form-check-inline ml-4'>";
            html += "<input type='radio' id='type2' name='type' value='Folder' class='form-check-input'>"
            html += "<label for='type2'>Folder</label><br>"
            html += "</div>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Delimiter'>Delimiter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Delimiter'  id='Delimiter' autofocus autocomplete='off' placeholder='Delimiter' name='Properties.Delimiter' type='text' required />";
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Source<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Source'  id='Source' autofocus autocomplete='off' placeholder='Source Variable' name='Properties.Source' type='text' required />";
            html += "</div>";
            html += " <span class='Sourcevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Destination<span class='mandatory'>*</span></div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Destination'  id='Destination' autofocus autocomplete='off' placeholder='Destination Variable' name='Properties.Destination' type='text' required />";
            html += "</div>";
            html += " <span class='Destinationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "delete":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<div class='form-check form-check-inline'>";
            html += "<input type='radio' id='type1' name='type' value='File' class='form-check-input'>"
            html += "<label for='type1'>File</label><br>"
            html += "</div>";
            html += "<div class='form-check form-check-inline ml-4'>";
            html += "<input type='radio' id='type2' name='type' value='Folder' class='form-check-input'>"
            html += "<label for='type2'>Folder</label><br>"
            html += "</div>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Delimiter'>Delimiter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Delimiter'  id='Delimiter' autofocus autocomplete='off' placeholder='Delimiter' name='Properties.Delimiter' type='text' required />";
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Source<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Source'  id='Source' autofocus autocomplete='off' placeholder='Source Variable' name='Properties.Source' type='text' required />";
            html += "</div>";
            html += " <span class='Sourcevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "rename":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<div class='form - check form-check-inline'>";
            html += "<input type='radio' id='type1' name='type' value='File' class='form-check-input'>"
            html += "<label for='type1'>File</label><br>"
            html += "</div>";
            html += "<div class='form-check form-check-inline ml-4'>";
            html += "<input type='radio' id='type2' name='type' value='Folder' class='form-check-input'>"
            html += "<label for='type2'>Folder</label><br>"
            html += "</div>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Delimiter'>Delimiter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Delimiter'  id='Delimiter' autofocus autocomplete='off' placeholder='Delimiter' name='Properties.Delimiter' type='text' required />";
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>File Path<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Filepath'  id='Filepath' autofocus autocomplete='off' placeholder='Select Filepath' name='Properties.Filepath' type='text' required />";
            html += "</div>";
            html += " <span class='Filepathvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>New File Name<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Newfilename'  id='Newfilename' autofocus autocomplete='off' placeholder='Select new File path' name='Properties.Newfilename' type='text' required />";
            html += "</div>";
            html += " <span class='Newfilenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "readfile":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Delimiter'>Delimiter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Delimiter'  id='Delimiter' autofocus autocomplete='off' placeholder='Delimiter' name='Properties.Delimiter' type='text' required />";
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Select File<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Filepath'  id='Filepath' autofocus autocomplete='off' placeholder='Select Filepath' name='Properties.Filepath' type='text' required />";
            html += "</div>";
            html += " <span class='Filepathvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "writefile":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Delimiter'>Source Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@SourceVariable'  id='SourceVariable' autofocus autocomplete='off' placeholder='SourceVariable' name='Properties.SourceVariable' type='text' required />";
            html += "</div>";
            html += " <span class='SourceVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='Source'>Select file Name<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Newfilename'  id='Newfilename' autofocus autocomplete='off' placeholder='Select new File path' name='Properties.Newfilename' type='text' required />";
            html += "</div>";
            html += " <span class='Newfilenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getserver":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getdate":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getcyberairgap":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Airgap Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='airgapname' scriptvalue='@@airgapname' class='propertiesInput form-control' placeholder='Enter Airgap Name ' />";
            html += "</div>";
            html += " <span class='airgapnamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "createcybersnaps":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Variable Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='VariableName' scriptvalue='@@VariableName' class='propertiesInput form-control' placeholder='Enter Variable Name ' />";
            html += "</div>";
            html += " <span class='VariableNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Variable Value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='VariableValue' scriptvalue='@@VariableValue' class='propertiesInput form-control' placeholder='Enter Variable Value ' />";
            html += "</div>";
            html += " <span class='VariableValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "updatecybersnaps":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Snap Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='SnapName' scriptvalue='@@SnapName' class='propertiesInput form-control' placeholder='Enter Snap Name ' />";
            html += "</div>";
            html += " <span class='SnapNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Time Stamp</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='TimeStamp' scriptvalue='@@TimeStamp' class='propertiesInput form-control' placeholder='Enter Time Stamp ' />";
            html += "</div>";
            html += " <span class='TimeStampvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Linked Status</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='LinkedStatus' scriptvalue='@@LinkedStatus' class='propertiesInput form-control' placeholder='Enter Linked Status ' />";
            html += "</div>";
            html += " <span class='LinkedStatusvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Status</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='Status' scriptvalue='@@Status' class='propertiesInput form-control' placeholder='Enter Status ' />";
            html += "</div>";
            html += " <span class='Statusvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getvariablefromxml":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Xml Result</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='xmlresult' scriptvalue='@@xmlresult' class='propertiesInput form-control' placeholder='Enter Xml Result ' />";
            html += "</div>";
            html += " <span class='xmlresultvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Attribute Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='attributename' scriptvalue='@@attributename' class='propertiesInput form-control' placeholder='Enter Attribute Name ' />";
            html += "</div>";
            html += " <span class='attributenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Index(Optional)</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='number' id='index' scriptvalue='@@index' class='propertiesInput form-control' placeholder='Enter Index ' />";
            html += "</div>";
            html += " <span class='indexvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getdatabase":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>DatabaseName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='databasename' scriptvalue='@@databasename' class='propertiesInput form-control' placeholder='Enter database Name ' />";
            html += "</div>";
            html += " <span class='databasenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>ReplicationName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='replicationname' scriptvalue='@@replicationname' class='propertiesInput form-control' placeholder='Enter Replication Name ' />";
            html += "</div>";
            html += " <span class='replicationnamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getfile":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>FolderPath</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='FolderPath' scriptvalue='@@FolderPath' class='propertiesInput form-control' placeholder='Enter Folder Path ' />";
            html += "</div>";
            html += " <span class='FolderPathvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>IsRecentFile</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='IsRecentFile' scriptvalue='@@IsRecentFile' class='propertiesInput form-control' placeholder='Enter Recent File Path ' />";
            html += "</div>";
            html += " <span class='IsRecentFilevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "updateinfraobject":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>InfraObjectName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='infraobjectname' scriptvalue='@@infraobjectname' class='propertiesInput form-control' placeholder='Enter InfraObject Name ' />";
            html += "</div>";
            html += " <span class='infraobjectnamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "getdatetime":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='GetDateTime'>Get DateTime<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@GetDateTime'  id='GetDateTime' autofocus autocomplete='off' placeholder='Get DateTime' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='GetDateTimevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "executecpl":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Server Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input type='text' id='ServerName' scriptvalue='@@ServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='ServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Script Block</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<textarea id='ScriptBlock' scriptvalue='@@ScriptBlock' class='propertiesInput form-control' placeholder='Enter Script Block '></textarea>";
            html += "</div>";
            html += " <span class='ScriptBlockvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>SubstituteAuthentication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='SubstituteAuthentication' scriptvalue='@@SubstituteAuthentication' class='propertiesInput form-control' placeholder='Enter substitute authentication ' />";
            html += "</div>";
            html += " <span class='SubstituteAuthenticationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "setproperty":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>InputParameter</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='InputParameter' scriptvalue='@@InputParameter' class='propertiesInput form-control' placeholder='Enter InputParameter' />";
            html += "</div>";
            html += " <span class='InputParametervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>TypeParameter</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='TypeParameter' scriptvalue='@@TypeParameter'>";
            html += "<option value='length'>length</option>";
            html += "<option value='size'>size</option>";
            html += "<option value='count'>count</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='TypeParametervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Output Parameter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@OutputParameter'  id='OutputParameter' autofocus autocomplete='off' placeholder='Enter OutputParameter' type='text' required />";
            html += "</div>";
            html += " <span class='OutputParametervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "loggerwrite":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";
            html += "<option value='error'>Error</option>";
            html += "<option value='information'>Information</option>";
            html += "<option value='waring'>Waring</option>";
            html += "<option value='debugger'>Debugger</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Message</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='message' scriptvalue='@@message' class='propertiesInput form-control' placeholder='Enter Message' />";
            html += "</div>";
            html += " <span class='messagevalidaion' style='color:red;'></span>";
            html += "</div>"

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;
        case "log":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";
            html += "<option value='error'>Error</option>";
            html += "<option value='information'>Information</option>";
            html += "<option value='waring'>Waring</option>";
            html += "<option value='debugger'>Debugger</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Message</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='message' scriptvalue='@@message' class='propertiesInput form-control' placeholder='Enter Message' />";
            html += "</div>";
            html += " <span class='messagevalidaion' style='color:red;'></span>";
            html += "</div>"


            html += "</div>";
            break;
        case "datatype":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";

            html += "<option value='string'>string</option>";
            html += "<option value='int32'>int32</option>";
            html += "<option value='decimal'>decimal</option>";
            html += "<option value='double'>double</option>";
            html += "<option value='GetVariableFromJSON'>GetVariableFromJSON</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='Original' scriptvalue='@@Original' class='propertiesInput form-control' placeholder='Enter Original' />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Enter OutputParameter' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "</div>";
            break;

        case "throw":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>ThrowValue</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ThrowValue' id='ThrowValue' autofocus autocomplete='off' placeholder='Enter the values' name='ThrowValue' type='text' required />";
            html += "</div>";
            html += " <span id='ThrowValuevalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "</div>";
            break;

        case "encrypt":

        case "decrypt":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Security Value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='SecurityValue' scriptvalue='@@SecurityValue' class='propertiesInput form-control' placeholder='Enter Security Value ' />";
            html += "</div>";
            html += " <span class='SecurityValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>ResultVariable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "append":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Append Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='Delimiter' scriptvalue='@@Delimiter' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Separate By</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='Regex' scriptvalue='@@Regex' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Regexvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "changetextcase":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Convert To</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='trimtype' scriptvalue='@@trimtype'>";
            html += "<option value='Uppercase'>Uppercase</option>";
            html += "<option value='Lowercase'>lowercase</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='trimtypevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "trim":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='trimtype' scriptvalue='@@trimtype'>";
            html += "<option value='TrimStart'>TrimStart</option>";
            html += "<option value='TrimEnd'>TrimEnd</option>";
            html += "<option value='Trim'>Trim</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='trimtypevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "regex":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Index</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Is Match<span class='mandatory'>*</span></div>";
            html += "<div class='input-group'>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Match'  id='Match' autofocus autocomplete='off' placeholder='Match Value'  min='0' type='number' required />";
            html += "</div>";
            html += " <span class='Matchvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "split":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Delimiter</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='Delimiter' scriptvalue='@@Delimiter' class='propertiesInput form-control' placeholder='Enter delimiter' required />"
            html += "</div>";
            html += " <span class='Delimitervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Index</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='IndexValue' scriptvalue='@@IndexValue' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='IndexValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "find/replace":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Original' id='Original' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "text":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>InputParameter</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='InputParameter' scriptvalue='@@InputParameter'   class='propertiesInput form-control' placeholder='Enter InputParameter' size='40'/>";
            html += "</div>";
            html += " <span class='InputParametervalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Output Parameter<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@OutputParameter'  id='OutputParameter' autofocus autocomplete='off' placeholder='Enter OutputParameter' type='text' required />";
            html += "</div>";
            html += " <span class='OutputParametervalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "</div>";
            break;

        case "getpropertyjson":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Original Text</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='Original' scriptvalue='@@Original' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Originalvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='value' scriptvalue='@@value' class='propertiesInput form-control' placeholder='Enter DBName ' />";
            html += "</div>";
            html += " <span class='valuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='animation-div'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='resultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "create":

        case "setvariable":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='variable' scriptvalue='@@variable' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='variablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";

            html += "<option value='string'>string</option>";
            html += "<option value='bool'>bool</option>"
            html += "<option value='int32'>int32</option>";
            html += "<option value='decimal'>decimal</option>";
            html += "<option value='GetVariableFromJSON'>GetVariableFromJSON</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "increment":

        case "decrement":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='variable' scriptvalue='@@variable' class='propertiesInput form-control' placeholder='Enter Variable' />";
            html += "</div>";
            html += " <span class='variablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Value</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='value' scriptvalue='@@value' class='propertiesInput form-control' placeholder='Enter Value ' />";
            html += "</div>";
            html += " <span class='valuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='animation-div'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='resultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "connect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "connectsubstitute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";          

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Substitute</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='substitute' scriptvalue='@@substitute' class='propertiesInput form-control' placeholder='Enter substitute authentication ' />";
            html += "</div>";
            html += " <span class='substitutevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "connectdb":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DBName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' id='ConnectDBName' scriptvalue='@@ConnectDBName' autofocus autocomplete='off' placeholder='Enter dbname Name ' type='text' required />";
            html += "</div>";
            html += " <span class='ConnectDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='DBType' scriptvalue='@@DBType'>";

            html += "<option value='SQL'>SQL</option>";
            html += "<option value='MYSQL'>MYSQL</option>";
            html += "<option value='Postgres'>Postgres</option>";
            html += " </select>";
            html += "</div>";
            html += " <span class='DBTypevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "execute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Execute Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' id='command' scriptvalue='@@command' autofocus autocomplete='off' placeholder='Enter Execute command Name ' type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'><input class='propertiesInput form-check-input' scriptvalue='@@IsLog' type='checkbox' value='' id='isLog'> IsLog</div>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "sendkey":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Key</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input class='propertiesInput form-control' id='Sendkey' scriptvalue='@@Sendkey' autofocus autocomplete='off' placeholder='Enter key Name ' type='text' required />";
            html += "</div>";
            html += " <span class='Sendkeyvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>RequiredEnter</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' id='Match' scriptvalue='@@Match' autofocus autocomplete='off' placeholder='Enter Match Name ' type='text' required />";
            html += "</div>";
            html += " <span class='Matchvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "powershellexecute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Commands</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' id='command' scriptvalue='@@command' autofocus autocomplete='off' placeholder='Enter Execute command Name ' type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' type='number' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' min='0' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "powershellcommands":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Commands</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<textarea class='propertiesInput form-control' id='command' scriptvalue='@@command' autofocus autocomplete='off' placeholder='Enter Execute command Name ' type='text' required rows='4' ></textarea>";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DatabaseName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DatabaseName' scriptvalue='@@DatabaseName' class='propertiesInput form-control' placeholder='Enter Database Name ' />";
            html += "</div>";
            html += " <span class='DatabaseNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "dll":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Path</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Path' id='Path' autofocus autocomplete='off' placeholder='Enter the Path Name'  type='text' required />";
            html += "</div>";
            html += " <span class='PathTypevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Class Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='text' id='classname' scriptvalue='@@classname' class='propertiesInput form-control' placeholder='Enter Class Name ' />";
            html += "</div>";
            html += " <span class='classnameValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Method Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@methodname'  id='methodname' autofocus autocomplete='off' placeholder='Enter Method Name' type='text' required />";
            html += "</div>";
            html += " <span class='methodnamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Parameter</div>";

            html += "<div class='propertiesInput' id='tableCollectionData' scriptvalue='@@tableCollectionData' >"
            html += "<table>";
            html += "<thead><tr><th>Type</th><th>Value</th><th></th></tr></thead>";
            html += "<tbody id='tableContainer'>";

            html += "</tbody>";
            html += "</table>";
            html += "</div > ";

            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "executestoreprocedure":

        case "oracleexecutequery":

        case "oracleexecutestoreprocedure":

        case "mysqlexecutequery":

        case "mysqlexecutestoreprocedure":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@command' id='command' autofocus autocomplete='off' placeholder='Enter the command' name='Command' type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@variable' id='variable' autofocus autocomplete='off' placeholder='Enter the Variable' name='Variable' type='text' required />";
            html += "</div>";
            html += " <span class='variablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mssqconnect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@servername' id='servername' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DBName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='text' id='ConnectDBName' scriptvalue='@@ConnectDBName' class='propertiesInput form-control' placeholder='Enter ConnectDBName'  required />"
            html += "</div>";
            html += " <span class='ConnectDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>UseMasterDB</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='MasterDB' scriptvalue='@@MasterDB' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='MasterDBvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "executequery":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@command' id='command' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Index</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='IndexValue' scriptvalue='@@IndexValue' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='IndexValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Connection</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Connection' scriptvalue='@@Connection' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Connectionvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@Replication'  id='Replication' autofocus autocomplete='off' placeholder='Replication Variable' name='Properties.Replication' type='text' required />";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "checkexecutequery":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@command' id='command' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Index</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='IndexValue' scriptvalue='@@IndexValue' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='IndexValuevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Connection</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Connection' scriptvalue='@@Connection' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Connectionvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Check Output</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@CheckOutput'  id='CheckOutput' autofocus autocomplete='off' placeholder='Check Output' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='CheckOutputvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "startstopguiaction":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>IP Address</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ipaddress' id='ipaddress' autofocus autocomplete='off' placeholder='Enter IP Address'  type='text' required />";
            html += "</div>";
            html += " <span class='ipaddressvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Port</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@port' id='port' autofocus autocomplete='off' placeholder='Port'  type='text' required />";
            html += "</div>";
            html += " <span class='portvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Type</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += " <select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='actiontype' scriptvalue='@@actiontype'>";
            html += "<option value='Start'>Start</option>";
            html += "<option value='Stop'>Stop</option>";
            html += "</select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "executeguiaction":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>IP Address</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ipaddress' id='ipaddress' autofocus autocomplete='off' placeholder='Enter IP Address'  type='text' required />";
            html += "</div>";
            html += " <span class='ipaddressvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Port</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@port' id='port' autofocus autocomplete='off' placeholder='Port'  type='text' required />";
            html += "</div>";
            html += " <span class='portvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Script Block</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<textarea id='ScriptBlock' scriptvalue='@@ScriptBlock' class='propertiesInput form-control' placeholder='Enter Script Block '></textarea>";
            html += "</div>";
            html += " <span class='ScriptBlockvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "send":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Baseurl</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Method</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";
            html += "<option value='Post'>Post</option>";
            html += "<option value='Get'>Get</option>";
            html += "<option value='Delete'>Delete</option>";
            html += "<option value='Update'>Update</option>";
            html += "<option value='Put'>Put</option>";
            html += "<option value='Patch'>Patch</option>";
            html += "<option value='Head'>Head</option>";
            html += "<option value='Options'>Options</option>";
            html += "</select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Parameter</div>";

            html += "<div class='propertiesInput' id='sendTableCollectionData' scriptvalue='@@sendTableCollectionData' >"
            html += "<table>";
            html += "<thead><tr><th>Type</th><th>Name</th><th>Value</th><th></th></tr></thead>";
            html += "<tbody id='tableSendContainer'>";

            html += "</tbody>";
            html += "</table>";
            html += "</div > ";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Bearer Token(Optional)</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@BearerToken'  id='BearerToken' autofocus autocomplete='off' placeholder='Bearer Token' name='Properties.ResultVariable' type='text'/>";
            html += "</div>";
            html += "</div>";          

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "reader":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Baseurl</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Method</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<select class='propertiesInput form-select form-select-modal w-100' data-live-search='true' title='Some placeholder text...' id='type' scriptvalue='@@type'>";
            html += "<option value='Post'>Post</option>";
            html += "<option value='Get'>Get</option>";
            html += "<option value='Delete'>Delete</option>";
            html += "<option value='Update'>Update</option>";
            html += "<option value='Put'>Put</option>";
            html += "<option value='Patch'>Patch</option>";
            html += "<option value='Head'>Head</option>";
            html += "<option value='Options'>Options</option>";
            html += "</select>";
            html += "</div>";
            html += " <span class='typevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Parameter</div>";

            html += "<div class='propertiesInput' id='sendTableCollectionData' scriptvalue='@@sendTableCollectionData' >"
            html += "<table>";
            html += "<thead><tr><th>Type</th><th>Name</th><th>Value</th><th></th></tr></thead>";
            html += "<tbody id='tableSendContainer'>";

            html += "</tbody>";
            html += "</table>";
            html += "</div > ";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Bearer Token(Optional)</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@BearerToken'  id='BearerToken' autofocus autocomplete='off' placeholder='Bearer Token' name='Properties.ResultVariable' type='text'/>";
            html += "</div>";
            html += "</div>";
           
            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='resultvariable'>Result Type</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultType'  id='ResultType' autofocus autocomplete='off' placeholder='Result Type' name='Properties.ResultVariable' type='text'/>";
            html += "</div>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='resultvariable'>Header Key</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@HeaderKey'  id='HeaderKey' autofocus autocomplete='off' placeholder='Header Key' name='Properties.ResultVariable' type='text'/>";
            html += "</div>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mssqldbmirroring":
        case "mssqlreplicationalwayson":
        case "azuremssqlpaasmonitor":
        case "goldengatereplicationmonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mssqlreplicationnls":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "postgresreplication":

        case "mssqlreplication":

        case "mysqlreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Replication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Replicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "hp3parreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Replication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Replicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break

        case "hadr":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))'  type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mongodbreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DatabaseName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DatabaseName' scriptvalue='@@DatabaseName' class='propertiesInput form-control' placeholder='Enter Database Name ' />";
            html += "</div>";
            html += " <span class='DatabaseNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";


            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "oraclereplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter PRServer Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name1</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName1' scriptvalue='@@PRServerName1' class='propertiesInput form-control' placeholder='Enter PRServer Name ' />";
            html += "</div>";
            html += " <span class='PRServerName1validaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter DRServer Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name1</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName1' scriptvalue='@@DRServerName1' class='propertiesInput form-control' placeholder='Enter DRServer Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter PRDBServer Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter DRDBServer Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Replication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Replication Name ' />";
            html += "</div>";
            html += " <span class='Replicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "appgm":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRReplication Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplicationName' scriptvalue='@@PRReplicationName' class='propertiesInput form-control' placeholder='Enter Replication Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationName' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRReplication Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplicationName' scriptvalue='@@DRReplicationName' class='propertiesInput form-control' placeholder='Enter Replication Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mssqlfulldb":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";


            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "rsyncmonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "rsyncreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "createlogger":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>File Path</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@filepath' id='filepath' autofocus autocomplete='off' placeholder='Enter the FilePath' name='Filepath' type='text' required />";
            html += "</div>";
            html += " <span class='filepathvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>File Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@filename' id='filename' autofocus autocomplete='off' placeholder='Enter the FileName' name='Filename' type='text' required />";
            html += "</div>";
            html += " <span class='filenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span id='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "as400execute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>TimeOut</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='timeout' scriptvalue='@@timeout' class='propertiesInput form-control' placeholder='Enter Timeout' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timeoutvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='waittime' scriptvalue='@@waittime' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waittimevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Commands</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<textarea class='propertiesInput form-control' id='command' scriptvalue='@@command' autofocus autocomplete='off' placeholder='Enter Execute command Name ' type='text' required rows='1' ></textarea>";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DeviceName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<textarea class='propertiesInput form-control' id='DeviceName' scriptvalue='@@DeviceName' autofocus autocomplete='off' placeholder='Enter Device Name ' type='text' required rows='1' ></textarea>";
            html += "</div>";
            html += " <span class='DeviceNameevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mongodbexecute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DatabaseName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='databasename' scriptvalue='@@databasename' class='propertiesInput form-control' placeholder='Enter Database Name ' />";
            html += "</div>";
            html += " <span class='databasenamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>InfraObject Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@infraobjectname' id='infraobjectname' autofocus autocomplete='off' placeholder='InfraObject Name'  type='text' required />";
            html += "</div>";
            html += " <span class='infraobjectnamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>TimeOut</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='timeout' scriptvalue='@@timeout' class='propertiesInput form-control' placeholder='Enter Timeout' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timeoutvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='waittime' scriptvalue='@@waittime' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waittimevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "consoleconnect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>ServerName</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='servername' scriptvalue='@@servername' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='servernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "consoleexecute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' min='0' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Execute Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input class='propertiesInput form-control' id='command' scriptvalue='@@command' autofocus autocomplete='off' placeholder='Enter Execute command Name ' type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "guiautomationexecute":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>API Url</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@apiurl' id='apiurl' autofocus autocomplete='off' placeholder='API URL'  type='text' required />";
            html += "</div>";
            html += " <span class='apivalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>RPA Tool Path</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@rpatoolpath' id='rpatoolpath' autofocus autocomplete='off' placeholder='Enter RPA Tool Path'  type='text' required />";
            html += "</div>";
            html += " <span class='rpatoolpathvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>User Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@username' id='username' autofocus autocomplete='off' placeholder='Enter User Name'  type='text' required />";
            html += "</div>";
            html += " <span class='usernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Task Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@taskname' id='taskname' autofocus autocomplete='off' placeholder='Enter Task Name'  type='text' required />";
            html += "</div>";
            html += " <span class='tasknamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Task Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@tasktime' class='propertiesInput form-control' placeholder='Enter Task Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='tasktimevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "mssqlconnectinfra":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>InfraObject Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@InfraObjectName' id='InfraObjectName' autofocus autocomplete='off' placeholder='InfraObject Name'  type='text' required />";
            html += "</div>";
            html += " <span class='InfraObjectNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>UseMSDB</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='MSDB' scriptvalue='@@MSDB' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='MSDBvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "wmiconnect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Name Space</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@namespace' id='namespace' autofocus autocomplete='off' placeholder='Enter NameSpace'  type='text' required />";
            html += "</div>";
            html += " <span class='namespacevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Host</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@host' id='host' autofocus autocomplete='off' placeholder='Enter Host'  type='text' required />";
            html += "</div>";
            html += " <span class='hostvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>User Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@username' id='username' autofocus autocomplete='off' placeholder='Enter UserName'  type='text' required />";
            html += "</div>";
            html += " <span class='usernamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Password</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='password' scriptvalue='@@password' class='propertiesInput form-control' placeholder='Enter Password' />";
            html += "</div>";
            html += " <span class='passwordvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "wmiquery":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Scope</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@scope' id='scope' autofocus autocomplete='off' placeholder='Enter Scope'  type='text' required />";
            html += "</div>";
            html += " <span class='scopevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Query</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@query' id='query' autofocus autocomplete='off' placeholder='Enter Query'  type='text' required />";
            html += "</div>";
            html += " <span class='queryvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Status</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@status' id='status' autofocus autocomplete='off' placeholder='Enter Status'  type='text' required />";
            html += "</div>";
            html += " <span class='statusvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "wmiinvokemethod":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Object</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@object' id='object' autofocus autocomplete='off' placeholder='Enter Object'  type='text' required />";
            html += "</div>";
            html += " <span class='objectvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Command</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@command' id='command' autofocus autocomplete='off' placeholder='Enter Command'  type='text' required />";
            html += "</div>";
            html += " <span class='commandvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "hypervreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServer_key' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServer_key' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Replication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Replicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break

        case "mssqldisconnect":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Connection</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Connection' scriptvalue='@@Connection' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Connectionvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "diskglobalmirrormonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "hurmonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case 'sleep':
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'>Seconds</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input id='Sleep' scriptvalue='@@Seconds'  class='propertiesInput form-control' autofocus autocomplete='off' placeholder='Enter the Secoonds' rows='2' name='Sleep'></textarea>";
            html += "</div>";

            html += "</div>";
            html += " <span class='Sleepvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "srmreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";
            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "robocopymonitoring":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "robocopyreplication":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRDBName' scriptvalue='@@PRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRDB Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRDBName' scriptvalue='@@DRDBName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRDBNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "as400executemonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Server Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='ServerName' scriptvalue='@@ServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='ServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='Replication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='Replicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "openshiftmonitoring":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "azuremysqlpaasmonitor":

        case "virtualizationrpforvmreplication":

        case "azurepostgresqlpaasmonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "netappsnapmirrormonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@PRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DR Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRReplication' scriptvalue='@@DRReplication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "azurestoragemonitoring":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Replication</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRReplication' scriptvalue='@@Replication' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRReplicationvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        case "noreplicationmonitor":
            html = "<div class='scriptData' scriptCmd='" + comment + "'>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Prompt</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-parameter-file'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@prompt' id='prompt' autofocus autocomplete='off' placeholder='Prompt Variable'  type='text' required />";
            html += "</div>";
            html += " <span class='promptvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Time Out</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-Timeout'></i></span>";
            html += "<input type='number' id='time' scriptvalue='@@time' class='propertiesInput form-control' placeholder='Enter Timeout' onkeypress='return (event.charCode != 8 && event.charCode == 0 || (event.charCode >= 48 && event.charCode <= 57))' type='number' required />"
            html += "</div>";
            html += " <span class='timevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>Waittime</div>";
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-time'></i></span>";
            html += "<input type='number' id='wait' scriptvalue='@@wait' class='propertiesInput form-control' placeholder='Enter Waittime' type='number' required />"
            html += "</div>";
            html += " <span class='waitvalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>PRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='PRServerName' scriptvalue='@@PRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='PRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label' for='ResultVariable'>DRServer Name</div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-server'></i></span>";
            html += "<input type='text' id='DRServerName' scriptvalue='@@DRServerName' class='propertiesInput form-control' placeholder='Enter Server Name ' />";
            html += "</div>";
            html += " <span class='DRServerNamevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "<div class='form-group'>";
            html += "<div class='form-label'  for='ResultVariable'>Result Variable<span class='mandatory'>*</span></div>";
            html += "<input type='checkbox' title='Encrypt Checkbox' class='position-absolute top-0 end-0 encryptcheckbox'>"
            html += "<div class='input-group'>";
            html += "<span class='input-group-text'><i class='cp-summary'></i></span>";
            html += "<input class='propertiesInput form-control' scriptvalue='@@ResultVariable'  id='ResultVariable' autofocus autocomplete='off' placeholder='Result Variable' name='Properties.ResultVariable' type='text' required />";
            html += "</div>";
            html += " <span class='ResultVariablevalidaion' style='color:red;'></span>";
            html += "</div>";

            html += "</div>";
            break;

        default:      
    }
    $("#properticsHtml").append(html)
}

$(document).on('click', '#btnDescriptionChange', function (e) {
    var tableData = [];
    var flagData = false
    var id = e.target.getAttribute("data-id")
    var nodeId = e.target.getAttribute("data-nodeid")
    var parentId = e.target.getAttribute("data-parentid")
    var titleName = e.target.getAttribute("data-titleName")
    var parentName = e.target.getAttribute("data-parentName")

    $(".dllData").each(function (data) {
        var selectData = $(this).children().children().children("select").val()
        var textData = $(this).children().children().children("textarea").val()
        if (!textData) {
            flagData = true
        }
        var rowData = {
            type: selectData,
            value: textData,
        };
        tableData.push(rowData)
    })

    $(".sendData").each(function (data) {
        let selectData = $(this).children().children().children("select").val()
        let textData = $(this).find("textarea.sendname").val()
        let sendValue = $(this).find("textarea.sendValue").val()

        if (!textData) {
            flagData = true
        }
        let rowData = {
            ParameterType: selectData,
            ParameterName: textData,
            ParameterValue: sendValue
        };
        tableData.push(rowData)
    })

    if (flagData) {
        $('#alertClass').removeClass("success-toast")
        $('#alertClass').addClass("warning-toast")
        $(".iconClass").removeClass("cp-check")
        $(".iconClass").addClass("cp-exclamation")
        $('#message').text("Please Enter Parameter Value!")
        $('#mytoastrdata').toast({ delay: 3000 });
        $('#mytoastrdata').toast('show');
        return false;
    }
    var $tableCollectionData = $('#tableCollectionData');
    var $sendTableCollectionData = $('#sendTableCollectionData');
    var jsonData = JSON.stringify(tableData);

    $tableCollectionData.attr("jsonData", jsonData);
    $sendTableCollectionData.attr("jsonData", jsonData);

    var tabPropertices = $("button.nav-link.active").text().trim()
    $(".spinner").show()
    var boardId = $("#btnDescriptionChange").attr("boardId")
    var boardvalue = $(".scriptData").attr('scriptCmd')

    if (!boardvalue) {
        $(".spinner").hide()
        return;
    }
    boardvalue = boardvalue.replace(/\\n/g, '');
    boardvalue = boardvalue.replace(/\\/g, '');
    var propertics = {}
   
    $(".propertiesInput").each(async function () {
        var key = $(this).attr("id")

        if (key == "tableCollectionData" || key == "sendTableCollectionData") {
            propertics[key] = $(this).attr("jsondata")
            if (key == "sendTableCollectionData") {
                var conditionvalue = await EncryptPassword($(this).attr("jsondata"))
            }
            else {
                var conditionvalue = $(this).attr("jsondata")
            }
        } else {
            if (key == "isLog" /*|| key == "substitute"*/) {
                propertics[key] = $(this).prop("checked")
                var conditionvalue = $(this).prop("checked")
            }
            else {
                let encryptcheckbox = $(this).parent().prev().prop('checked')
                let valueIndex;
                let textboxValue = $(this).val()

                if (encryptcheckbox && textboxValue) {
                    valueIndex = await EncryptPassword(textboxValue)
                }
                else {
                    valueIndex = $(this).val()
                }
                propertics[key] = valueIndex
                var conditionvalue = valueIndex
            }
        }
        var conditionreplacedata = $(this).attr("scriptvalue")
        let replaceValue = getDescription(boardvalue, conditionvalue, conditionreplacedata)
        boardvalue = replaceValue
        $("#" + boardId + " span#childspan").empty()
        $("#" + boardId + " span#childspan").parent().attr("data-propertics", JSON.stringify(propertics).replaceAll("'", '..'))
        $("#" + boardId + " span#childspan").attr("data-propertics", JSON.stringify(propertics).replaceAll("'", '..'))
        $("#" + boardId + " span#childspan").parent().attr("data-content", replaceValue)
        $("#" + boardId + " span#childspan").attr("data-content", replaceValue)
        $("#" + boardId + " span#childspan").append(replaceValue)
    })

    setTimeout(function () {
        $(".spinner").hide()
        $(".propertiWindow").hide();
    }, 1000) 
})

function updateScriptValue(selectedRadio) {
    var scriptValue = selectedRadio.value;
    let selectedRadioType = selectedRadio.getAttribute("name")
    var scriptValueElement = document.querySelector('.' + selectedRadioType);

    if (scriptValueElement) {
        scriptValueElement.setAttribute('scriptvalue', scriptValue);
    }
}

function getDescription(boardvalue, conditionvalue, conditionreplacedata) {
    let description = boardvalue;

    if (conditionvalue !== "") {
        description = description.replace(conditionreplacedata, conditionvalue)
    }
    else {
        let removeAtcondition = conditionreplacedata.replace(/@@(\w+)/g, '$1');
        let regexPatterns = [
            new RegExp(`,"${removeAtcondition}":".*?<span[^>]*>${conditionreplacedata}</span>",`, "g"),
            new RegExp(`,"${removeAtcondition}":".*?<span[^>]*>${conditionreplacedata}</span>"`, "g"),
            new RegExp(`"${removeAtcondition}":".*?<span[^>]*>${conditionreplacedata}</span>",`, "g"),
            new RegExp(`"${removeAtcondition}":".*?<span[^>]*>${conditionreplacedata}</span>"`, "g"),
        ];
        regexPatterns.forEach(regex => {
            description = description.replace(regex, conditionvalue)
        })
    }
    return description;
}

var html1 = ""

function addFields() {
    renderTable();
}

function addSendFields() {
    renderSendTable();
}

function removeFields(index) {
    $("#row" + index).remove();
}

function removeSendFields(index) {
    $("#row" + index).remove();
}

function renderTable() {
    var index = 1 + Math.floor(Math.random() * 99);
    var html1 = "<tr class='dllData' id='row" + index + "'>"
    html1 += "<td>";
    html1 += "<div class='form-group mx-2 '>";
    html1 += "<select class='form-select-modal form-select w-100 textSelect'  data-live-search='true' title='Some placeholder text...' id='type" + index + "'>";
    html1 += "<option value='int32'>Int32</option>";
    html1 += "<option value='int64'>Int64</option>";
    html1 += "<option value='double'>double</option>";
    html1 += "<option value='boolean'>boolean</option>";
    html1 += "<option value='string'>string</option>";
    html1 += "<option value='datetime'>datetime</option>";
    html1 += "<option value='array'>array</option>";
    html1 += "<option value='server'>Server</option>";
    html1 += "<option value='database'>Database</option>";
    html1 += "<option value='infraobject'>infraobject</option>";
    html1 += "<option value='replication'>replication</option>";
    html1 += "</select>";
    html1 += "</div>";
    html1 += "</td>";

    html1 += "<td>";
    html1 += "<div class='form-group mx-2'>";
    html1 += "<textarea name='ParameterValue' class='textField' placeholder='Value' type='text' required  id='value" + index + "'></textarea>";
    html1 += "</div>";
    html1 += "</td>";

    html1 += "<td style='padding-bottom: 13px;'>";
    html1 += "<div>";
    html1 += "<span role='button' onclick='addFields()'><i class='cp-circle-plus'></i></span>";
    html1 += "</div>";
    html1 += "</td>";
    html1 += "<td style='padding-bottom: 13px;'>";
    html1 += "<div>";
    html1 += "<span role='button' onclick='removeFields(" + index + ")'><i class='cp-circle-minus'></i></span>";
    html1 += "</div>";
    html1 += "</td>";
    html1 += "</tr>";

    $("#tableContainer").append(html1);

    var rowDataIndex = {
        type: "type" + index,
        value: "value" + index,
    };
    return rowDataIndex
}

function renderSendTable() {
    var index = 1 + Math.floor(Math.random() * 99);
    var html1 = "<tr class='sendData' id='row" + index + "'>"
    html1 += "<td>";
    html1 += "<div class='form-group mx-2 '>";
    html1 += "<select class='form-select-modal form-select textSelect'  data-live-search='true' title='Some placeholder text...' id='type" + index + "'>";
    html1 += "<option value='HEADER'>HEADER</option>";
    html1 += "<option value='JSON_BODY'>JSON_BODY</option>";
    html1 += "<option value='AuthType'>Auth Type</option>";
    html1 += "<option value='File'>File</option>";
    html1 += "<option value='Parameter'>Parameter</option>";
    html1 += "</select>";
    html1 += "</div>";
    html1 += "</td>";

    html1 += "<td>";
    html1 += "<div class='form-group mx-2'>";
    html1 += "<textarea name='ParameterValue' class='textField sendname' placeholder='Name' type='text' required  id='name" + index + "'></textarea>";
    html1 += "</div>";
    html1 += "</td>";

    html1 += "<td>";
    html1 += "<div class='form-group mx-2'>";
    html1 += "<textarea name='ParameterValue' class='textField sendValue' placeholder='Value' type='text' required  id='value" + index + "'></textarea>";
    html1 += "</div>";
    html1 += "</td>";

    html1 += "<td style='padding-bottom: 13px;'>";
    html1 += "<div>";
    html1 += "<span role='button' onclick='addSendFields()'><i class='cp-circle-plus'></i></span>";
    html1 += "</div>";
    html1 += "</td>";
    html1 += "<td style='padding-bottom: 13px;'>";
    html1 += "<div>";
    html1 += "<span role='button' onclick='removeSendFields(" + index + ")'><i class='cp-circle-minus'></i></span>";
    html1 += "</div>";
    html1 += "</td>";
    html1 += "</tr>";

    $("#tableSendContainer").append(html1);

    var rowDataIndex = {
        type: "type" + index,
        name: "name" + index,
        value: "value" + index,
    };
    return rowDataIndex
}
