﻿using ContinuityPatrol.Application.Features.ManageWorkflow.Queries.GetManagedWorkflow;
using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.Features.Workflow.Commands.Lock;
using ContinuityPatrol.Application.Features.Workflow.Commands.Publish;
using ContinuityPatrol.Application.Features.Workflow.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Workflow.Commands.Update;
using ContinuityPatrol.Application.Features.Workflow.Commands.Verify;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetCpslScript;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetailByActionName;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ManageWorkflow;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowService
{
    Task<List<GetWorkflowActionByIdVm>> GetWorkflowActionByWorkflowIdAndGroupId(string workflowId, string workflowOperationGroupId);
    Task<bool> IsWorkflowNameExist(string workflowName, string id);
    Task<WorkflowDetailVm> GetByReferenceId(string workflowId);
    Task<BaseResponse> DeleteAsync(string workflowId);
    Task<UpdateWorkflowResponse> UpdateAsync(UpdateWorkflowCommand updateWorkflow);
    Task<BaseResponse> UpdateWorkflowPublish(UpdateWorkflowPublishCommand workflowPublish);
    Task<BaseResponse> UpdateWorkflowLock(UpdateWorkflowLockCommand workflowLock);
    Task<CreateWorkflowResponse> CreateAsync(CreateWorkflowCommand createWorkflow);
    Task<SaveAsWorkflowResponse> SaveAsWorkflow(SaveAsWorkflowCommand saveAsWorkflowCommand);
    Task<List<WorkflowListVm>> GetWorkflowList();
    Task<List<WorkflowNameVm>> GetWorkflowNames();
    Task<PaginatedResult<WorkflowListVm>> GetPaginatedWorkflow(GetWorkflowPaginatedListQuery query);
    Task<PaginatedResult<Manageworkflowlist>> GetManagedWorkflow(GetManagedWorkflowListQuery query);
    Task<List<ManageWorkflowModel>> GetManageWorkflows();
    Task<BaseResponse> UpdateIsVerify(UpdateWorkflowVerifyCommand command);
    Task<GetDetailByActionNameVm> GetDetailByActionName(GetDetailByActionNameQuery query);
    Task<GetCpslScriptDetailVm> GetCpslScript(GetCpslScriptDetailQuery query);

}