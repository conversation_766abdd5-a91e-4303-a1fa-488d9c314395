using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create; 
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ApprovalMatrixUsersProfile : Profile
{
    public ApprovalMatrixUsersProfile()
    {

        CreateMap<ApprovalMatrixUsers, ApprovalMatrixUsersListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ApprovalMatrixUsers, ApprovalMatrixUsersDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId)); 
     
        CreateMap<ApprovalMatrixUsers, CreateApprovalMatrixUsersCommand>().ReverseMap();
        CreateMap<ApprovalMatrixUsers, ApprovalMatrixUsersViewModel>().ReverseMap();
        CreateMap<ApprovalMatrixUsers, CreateApprovalMatrixUsersCommandList>().ReverseMap();

        CreateMap<CreateApprovalMatrixUsersCommand, ApprovalMatrixUsersViewModel>().ReverseMap();
        CreateMap<UpdateApprovalMatrixUsersCommand, ApprovalMatrixUsersViewModel>().ReverseMap(); 

        CreateMap<UpdateApprovalMatrixUsersCommand, ApprovalMatrixUsers>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<ApprovalMatrixUsers>, PaginatedResult<ApprovalMatrixUsersListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }
}
