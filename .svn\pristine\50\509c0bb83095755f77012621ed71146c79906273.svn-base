﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class UserFilterSpecification : Specification<UserView>
{
    public UserFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.LoginName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("loginname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.LoginName.Contains(stringItem.Replace("loginname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("companyname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.CompanyName.Contains(stringItem.Replace("companyname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("logintype=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.LoginType.Contains(stringItem.Replace("logintype=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("role=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.RoleName.Contains(stringItem.Replace("role=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("email=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Email.Contains(stringItem.Replace("email=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("logindate=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.LoginDate.Contains(stringItem.Replace("logindate=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.LoginName.Contains(searchString) || p.CompanyName.Contains(searchString) ||
                    p.LoginType.Contains(searchString) || p.RoleName.Contains(searchString)
                    || p.Email.Contains(searchString) || p.LoginDate.Contains(searchString);
            }
        }
    }
}