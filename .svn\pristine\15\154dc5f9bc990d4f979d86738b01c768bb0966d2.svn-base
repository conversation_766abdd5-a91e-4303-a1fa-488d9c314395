using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertId;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertName;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterIdExist;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterNameUnique;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AlertMasterControllerTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AlertMasterController _controller;

    public AlertMasterControllerTests(AlertMasterFixture alertMasterFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        var testBuilder = new ControllerTestBuilder<AlertMasterController>();
        _controller = testBuilder.CreateController(
            _ => new AlertMasterController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAlertMasters_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertMasterCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_alertMasterFixture.AlertMasterListVm);

        // Act
        var result = await _controller.GetAlertMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterListVm>>(okResult.Value);
        Assert.Equal(3, alertMasters.Count);
    }

    [Fact]
    public async Task GetAlertMasters_ReturnsEmptyList_WhenNoAlertMastersExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertMasterCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterListQuery>(), default))
            .ReturnsAsync(new List<AlertMasterListVm>());

        // Act
        var result = await _controller.GetAlertMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterListVm>>(okResult.Value);
        Assert.Empty(alertMasters);
    }

    [Fact]
    public async Task GetAlertMasterById_ReturnsAlertMaster_WhenIdIsValid()
    {
        // Arrange
        var alertMasterId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterDetailQuery>(q => q.Id == alertMasterId), default))
            .ReturnsAsync(_alertMasterFixture.AlertMasterDetailVm);

        // Act
        var result = await _controller.GetAlertMasterById(alertMasterId);

        // Assert
        Assert.IsType<OkObjectResult>(result.Result);
    }

    [Fact]
    public async Task GetAlertMasterById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertMasterById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAlertMaster_Returns201Created()
    {
        // Arrange
        var command = _alertMasterFixture.CreateAlertMasterCommand;
        var expectedMessage = $"AlertMaster '{command.AlertName}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertMasterResponse
            {
                Message = expectedMessage,
                AlertMasterId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertMaster(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertMasterResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task CreateAlertMaster_Throws_WhenAlertNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateAlertMasterCommand>(), default))
            .ThrowsAsync(new InvalidOperationException("AlertName exists"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.CreateAlertMaster(_alertMasterFixture.CreateAlertMasterCommand));
    }

    [Fact]
    public async Task UpdateAlertMaster_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"AlertMaster '{_alertMasterFixture.UpdateAlertMasterCommand.AlertName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAlertMasterCommand>(), default))
            .ReturnsAsync(new UpdateAlertMasterResponse
            {
                Message = expectedMessage,
                AlertMasterId = _alertMasterFixture.UpdateAlertMasterCommand.Id
            });

        // Act
        var result = await _controller.UpdateAlertMaster(_alertMasterFixture.UpdateAlertMasterCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAlertMasterResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertMaster_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "AlertMaster 'Critical Database Alert' has been deleted successfully!.";
        var alertMasterId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertMasterCommand>(c => c.AlertId == alertMasterId), default))
            .ReturnsAsync(new DeleteAlertMasterResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlertMaster(alertMasterId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertMasterResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlertMaster_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteAlertMaster("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedAlertMasters_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAlertMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _alertMasterFixture.AlertMasterListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(PaginatedResult<AlertMasterListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ));

        // Act
        var result = await _controller.GetPaginatedAlertMasters(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertMasterListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertMasterListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetAlertMasterByAlertName_ReturnsAlertMasters_WhenAlertNameIsValid()
    {
        // Arrange
        var alertName = "Critical Database Alert";
        var expectedResult = new List<AlertMasterByAlertNameVm>
        {
            new() { Id = Guid.NewGuid().ToString(), AlertName = alertName, AlertType = 1 }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterByAlertNameQuery>(q => q.AlertName == alertName), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertMasterByAlertName(alertName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterByAlertNameVm>>(okResult.Value);
        Assert.Single(alertMasters);
        Assert.Equal(alertName, alertMasters.First().AlertName);
    }

    [Fact]
    public async Task GetAlertMasterByAlertId_ReturnsAlertMasters_WhenAlertIdIsValid()
    {
        // Arrange
        var alertId = "ALERT_001";
        var expectedResult = new List<AlertMasterByAlertIdVm>
        {
            new() { Id = Guid.NewGuid().ToString(), AlertId = alertId, AlertName = "Test Alert" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterByAlertIdQuery>(q => q.AlertId == alertId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertMasterByAlertId(alertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterByAlertIdVm>>(okResult.Value);
        Assert.Single(alertMasters);
        Assert.Equal(alertId, alertMasters.First().AlertId);
    }

    [Fact]
    public async Task IsAlertMasterNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAlertMasterNameExist("ExistingAlert", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAlertMasterNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetAlertMasterNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAlertMasterNameExist("NewAlert", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsAlertMasterIdExist_ReturnsTrue_WhenIdExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterIdExistQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsAlertMasterIdExist("ALERT_001");

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task GetAlertMasters_CallsCorrectQuery()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertMasterCacheKey + companyId);

        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertMasterListVm>());

        // Act
        await _controller.GetAlertMasters();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateAlertMaster_ValidatesAlertFrequency()
    {
        // Arrange
        var command = new CreateAlertMasterCommand
        {
            AlertId = "ALERT_TEST",
            AlertName = "Test Alert",
            AlertFrequency = -1, // Invalid frequency
            AlertPriority = "High"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("AlertFrequency must be positive"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateAlertMaster(command));
    }

    [Fact]
    public async Task UpdateAlertMaster_ValidatesMasterExists()
    {
        // Arrange
        var command = new UpdateAlertMasterCommand
        {
            Id = Guid.NewGuid().ToString(),
            AlertId = "UPDATED_ALERT",
            AlertName = "Updated Alert",
            AlertFrequency = 10
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("AlertMaster not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateAlertMaster(command));
    }

    [Fact]
    public async Task GetAlertMasterByAlertName_HandlesSpecialCharacters()
    {
        // Arrange
        var alertNameWithSpecialChars = "Critical Alert - Database (Primary)";
        var expectedResult = new List<AlertMasterByAlertNameVm>
        {
            new() { Id = Guid.NewGuid().ToString(), AlertName = alertNameWithSpecialChars, AlertType = 1 }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterByAlertNameQuery>(q => q.AlertName == alertNameWithSpecialChars), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertMasterByAlertName(alertNameWithSpecialChars);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterByAlertNameVm>>(okResult.Value);
        Assert.Single(alertMasters);
        Assert.Equal(alertNameWithSpecialChars, alertMasters.First().AlertName);
    }

    [Fact]
    public async Task GetAlertMasterByAlertId_HandlesMultipleResults()
    {
        // Arrange
        var alertId = "SHARED_ALERT_001";
        var expectedResult = new List<AlertMasterByAlertIdVm>
        {
            new() { Id = Guid.NewGuid().ToString(), AlertId = alertId, AlertName = "Shared Alert Instance 1" },
            new() { Id = Guid.NewGuid().ToString(), AlertId = alertId, AlertName = "Shared Alert Instance 2" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterByAlertIdQuery>(q => q.AlertId == alertId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertMasterByAlertId(alertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertMasters = Assert.IsAssignableFrom<List<AlertMasterByAlertIdVm>>(okResult.Value);
        Assert.Equal(2, alertMasters.Count);
        Assert.All(alertMasters, am => Assert.Equal(alertId, am.AlertId));
    }

    [Fact]
    public async Task IsAlertMasterNameExist_HandlesLongNames()
    {
        // Arrange
        var longAlertName = new string('A', 500); // Very long name

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertMasterNameUniqueQuery>(q => q.AlertName == longAlertName), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsAlertMasterNameExist(longAlertName, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

   

    [Fact]
    public async Task CreateAlertMaster_HandlesComplexConfiguration()
    {
        // Arrange
        var command = new CreateAlertMasterCommand
        {
            AlertId = "COMPLEX_ALERT_001",
            AlertType = 3,
            AlertMessage = "Complex alert with detailed configuration and multiple parameters for comprehensive monitoring",
            RecoveryId = Guid.NewGuid().ToString(),
            AlertPriority = "Critical",
            AlertFrequency = 5,
            AlertSendTime = "02:30:00",
            IsSendEmail = true,
            IsSendSMS = true,
            SmSMessage = "URGENT: Complex system alert requires immediate attention",
            AlertName = "Complex System Alert",
            EscMatId = Guid.NewGuid().ToString(),
            ExceptionId = Guid.NewGuid().ToString(),
            IsAlertActive = true,
            DBType = 1,
            IsFullDB = true
        };

        var expectedMessage = $"AlertMaster '{command.AlertName}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertMasterResponse
            {
                Message = expectedMessage,
                AlertMasterId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlertMaster(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertMasterResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedAlertMasters_HandlesLargePageSizes()
    {
        // Arrange
        var query = new GetAlertMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 500 // Large page size
        };

        var expectedData = _alertMasterFixture.AlertMasterListVm;
        var expectedPaginatedResult = PaginatedResult<AlertMasterListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertMasterPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedAlertMasters(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<AlertMasterListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<AlertMasterListVm>>(okResult.Value);

        Assert.Equal(500, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.Data.Count);
    }
}
