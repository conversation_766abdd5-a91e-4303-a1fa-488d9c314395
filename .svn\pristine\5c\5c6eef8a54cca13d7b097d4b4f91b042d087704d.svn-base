﻿using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectSchedulerWorkflowDetail.Commands;

public class UpdateInfraObjectSchedulerWorkflowDetailTests : IClassFixture<InfraObjectSchedulerWorkflowDetailFixture>
{
    private readonly InfraObjectSchedulerWorkflowDetailFixture _infraObjectSchedulerWorkflowDetailFixture;

    private readonly Mock<IInfraObjectSchedulerWorkflowDetailRepository> _mockInfraObjectSchedulerWorkflowDetailRepository;
    private readonly UpdateInfraObjectSchedulerWorkflowDetailCommandHandler _handler;

    public UpdateInfraObjectSchedulerWorkflowDetailTests(InfraObjectSchedulerWorkflowDetailFixture infraObjectSchedulerWorkflowDetailFixture)
    {
        _infraObjectSchedulerWorkflowDetailFixture = infraObjectSchedulerWorkflowDetailFixture;

        _mockInfraObjectSchedulerWorkflowDetailRepository = InfraObjectSchedulerWorkflowDetailRepositoryMocks
            .UpdateInfraObjectSchedulerWorkflowDetailRepository(_infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails);
        _handler = new UpdateInfraObjectSchedulerWorkflowDetailCommandHandler(_infraObjectSchedulerWorkflowDetailFixture.Mapper, _mockInfraObjectSchedulerWorkflowDetailRepository.Object);
    }

    [Fact]

    public async Task Handle_ValidInfraObjectSchedulerWorkflowDetail_UpdateToInfraObjectSchedulerWorkflowDetailsRepo()
    {
        _infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand.Id =
            _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId;
        var result = await _handler.Handle(_infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateInfraObjectSchedulerWorkflowDetailResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidInfraObjectSchedulerWorkflowDetailId()
    {
        _infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand.Id = _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId;

        await _handler.Handle(_infraObjectSchedulerWorkflowDetailFixture.UpdateInfraObjectSchedulerWorkflowDetailCommand, CancellationToken.None);

        _mockInfraObjectSchedulerWorkflowDetailRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockInfraObjectSchedulerWorkflowDetailRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraObjectSchedulerWorkflowDetail>()), Times.Once);
    }
}
