﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class BusinessServiceHealthLogRepositoryMocks
{
    public static Mock<IBusinessServiceHealthLogRepository> CreateBusinessServiceHealthLogRepository(List<BusinessServiceHealthLog> businessServiceHealthLogs)
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        businessServiceHealthLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthLogs);

        businessServiceHealthLogRepository.Setup(repo => repo.AddAsync(It.IsAny<BusinessServiceHealthLog>())).ReturnsAsync(
            (BusinessServiceHealthLog businessServiceHealthLog) =>
            {
                businessServiceHealthLog.Id = new Fixture().Create<int>();

                businessServiceHealthLog.ReferenceId = new Fixture().Create<Guid>().ToString();

                businessServiceHealthLogs.Add(businessServiceHealthLog);

                return businessServiceHealthLog;
            });

        return businessServiceHealthLogRepository;
    }

    public static Mock<IBusinessServiceHealthLogRepository> UpdateBusinessServiceHealthLogRepository(List<BusinessServiceHealthLog> businessServiceHealthLogs)
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        businessServiceHealthLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthLogs);

        businessServiceHealthLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthLogs.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceHealthLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceHealthLog>())).ReturnsAsync((BusinessServiceHealthLog businessServiceHealthLog) =>
        {
            var index = businessServiceHealthLogs.FindIndex(item => item.ReferenceId == businessServiceHealthLog.ReferenceId);

            businessServiceHealthLogs[index] = businessServiceHealthLog;

            return businessServiceHealthLog;

        });
        return businessServiceHealthLogRepository;
    }

    public static Mock<IBusinessServiceHealthLogRepository> DeleteBusinessServiceHealthLogRepository(List<BusinessServiceHealthLog> businessServiceHealthLogs)
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        businessServiceHealthLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthLogs);

        businessServiceHealthLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthLogs.SingleOrDefault(x => x.ReferenceId == i));

        businessServiceHealthLogRepository.Setup(repo => repo.UpdateAsync(It.IsAny<BusinessServiceHealthLog>())).ReturnsAsync((BusinessServiceHealthLog businessServiceHealthLog) =>
        {
            var index = businessServiceHealthLogs.FindIndex(item => item.ReferenceId == businessServiceHealthLog.ReferenceId);

            businessServiceHealthLog.IsActive = false;

            businessServiceHealthLogs[index] = businessServiceHealthLog;

            return businessServiceHealthLog;
        });

        return businessServiceHealthLogRepository;
    }

    public static Mock<IBusinessServiceHealthLogRepository> GetBusinessServiceHealthLogRepository(List<BusinessServiceHealthLog> businessServiceHealthLogs)
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        businessServiceHealthLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(businessServiceHealthLogs);

        businessServiceHealthLogRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => businessServiceHealthLogs.SingleOrDefault(x => x.ReferenceId == i));

        return businessServiceHealthLogRepository;
    }

    public static Mock<IBusinessServiceHealthLogRepository> GetBusinessServiceHealthLogEmptyRepository()
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        businessServiceHealthLogRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<BusinessServiceHealthLog>());

        return businessServiceHealthLogRepository;
    }

    public static Mock<IBusinessServiceHealthLogRepository> GetPaginatedBusinessServiceHealthLogRepository(List<BusinessServiceHealthLog> businessServiceHealthLogs)
    {
        var businessServiceHealthLogRepository = new Mock<IBusinessServiceHealthLogRepository>();

        var queryableBusinessServiceHealthLog = businessServiceHealthLogs.BuildMock();

        businessServiceHealthLogRepository.Setup(repo => repo.PaginatedListAllAsync()).Returns(queryableBusinessServiceHealthLog);

        return businessServiceHealthLogRepository;
    }
}