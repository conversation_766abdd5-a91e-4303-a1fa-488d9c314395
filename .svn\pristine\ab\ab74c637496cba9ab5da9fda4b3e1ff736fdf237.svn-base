﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperation.Queries;

public class GetWorkflowOperationPaginatedListQueryHandlerTests : IClassFixture<WorkflowOperationFixture>
{
    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private readonly Mock<IWorkflowOperationRepository> _mockWorkflowOperationRepository;

    private readonly GetWorkflowOperationPaginatedListQueryHandler _handler;

    public GetWorkflowOperationPaginatedListQueryHandlerTests(WorkflowOperationFixture workflowOperationFixture)
    {
        _workflowOperationFixture = workflowOperationFixture;

        _mockWorkflowOperationRepository = WorkflowOperationRepositoryMocks.GetPaginatedWorkflowOperationRepository(_workflowOperationFixture.WorkflowOperations);

        _handler = new GetWorkflowOperationPaginatedListQueryHandler(_workflowOperationFixture.Mapper, _mockWorkflowOperationRepository.Object);

        _workflowOperationFixture.WorkflowOperations[0].ProfileId = "WorkflowOperation_Testing";
        _workflowOperationFixture.WorkflowOperations[0].Status = "Pending";
        _workflowOperationFixture.WorkflowOperations[0].ProfileName = "Testing";

        _workflowOperationFixture.WorkflowOperations[1].ProfileId = "Domains";
        _workflowOperationFixture.WorkflowOperations[1].Status = "Start";
        _workflowOperationFixture.WorkflowOperations[1].ProfileName = "Workflow_Test";
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowOperationPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowOperation_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowOperationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Domains" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].ProfileId.ShouldBe("Domains");
        result.Data[0].Status.ShouldBe("Start");
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowOperationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]

    public async Task Handle_Return_WorkflowOperation_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowOperationPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "profileid=WorkflowOperation_Testing;Status=Pending;profilename=Testing" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowOperationListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ProfileId.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].ProfileId);
        result.Data[0].Status.ShouldBe("Pending");
        result.Data[0].ProfileName.ShouldBe("Testing");
        result.Data[0].Description.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].Description);
        result.Data[0].StartTime.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].StartTime);
        result.Data[0].EndTime.ShouldBe(_workflowOperationFixture.WorkflowOperations[0].EndTime);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowOperationPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowOperationRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}