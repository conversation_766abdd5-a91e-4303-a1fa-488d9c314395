﻿namespace ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Create;

public class CreateNodeWorkflowExecutionCommandValidator : AbstractValidator<CreateNodeWorkflowExecutionCommand>
{
    public CreateNodeWorkflowExecutionCommandValidator()
    {
        RuleFor(p => p.WorkflowName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        //RuleFor(p => p.ProfileName)
        //     .NotEmpty().WithMessage("Select {PropertyName}.")
        //     .NotNull();

        RuleFor(p => p.NodeName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();
    }
}