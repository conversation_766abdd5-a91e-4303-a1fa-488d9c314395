﻿using ContinuityPatrol.Application.Features.Database.Commands.Create;
using ContinuityPatrol.Application.Features.Database.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Database.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Database.Commands.Update;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Database.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByLicenseKey;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByServerId;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByUserName;
using ContinuityPatrol.Application.Features.Database.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Database.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDatabaseService
{
    Task<List<DatabaseNameVm>> GetDatabaseNames();
    Task<List<DatabaseListVm>> GetDatabaseList();
    Task<PaginatedResult<DatabaseListVm>> GetDatabasePaginatedList(GetDatabasePaginatedListQuery query);
    Task<BaseResponse> CreateAsync(CreateDatabaseCommand createDatabaseCommand);
    Task<BaseResponse> UpdateAsync(UpdateDatabaseCommand updateDatabaseCommand);
    Task<BaseResponse> DeleteAsync(string databaseId);
    Task<DatabaseDetailVm> GetByReferenceId(string databaseId);
    Task<bool> IsDatabaseNameExist(string databaseName, string id);
    Task<List<GetDatabaseByLicenseKeyListVm>> GetByLicenseKey(string licenseId);
    Task<List<GetDatabaseByServerIdVm>> GetByServerId(string serverId);
    Task<List<GetDatabaseByTypeVm>> GetByType(string type);
    Task<List<DatabaseTypeVm>> GetByDatabaseType(string databaseTypeId);
    Task<BaseResponse> DatabaseTestConnection(DatabaseTestConnectionCommand command);
    Task<BaseResponse> SaveAsDatabase(SaveAsDatabaseCommand saveAsDatabaseCommand);
    Task<List<GetDatabaseByUserNameVm>> GetDatabaseByUserName(string userName, string databaseTypeId);
    Task<UpdateDatabasePasswordResponse> UpdateDatabasePassword(UpdateDatabasePasswordCommand command);
    Task<BaseResponse> UpdateDatabaseFormVersion(UpdateDatabaseVersionCommand updateDatabaseVersionCommand);
}