using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class CyberComponentGroupFixture : IDisposable
{
    public const string CompanyId = "550e8400-e29b-41d4-a716-446655440000";
    public const string ParentCompanyId = "550e8400-e29b-41d4-a716-446655440001";
    public const string SiteId = "GROUP_SITE_001";
    public const string ParentSiteId = "PARENT_GROUP_SITE_001";
    public const string ComponentId = "GROUP_COMP_001";

    public List<CyberComponentGroup> CyberComponentGroupPaginationList { get; set; }
    public List<CyberComponentGroup> CyberComponentGroupList { get; set; }
    public CyberComponentGroup CyberComponentGroupDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public CyberComponentGroupFixture()
    {
        var fixture = new Fixture();

        CyberComponentGroupList = fixture.Create<List<CyberComponentGroup>>();

        CyberComponentGroupPaginationList = fixture.CreateMany<CyberComponentGroup>(20).ToList();

        CyberComponentGroupDto = fixture.Create<CyberComponentGroup>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
