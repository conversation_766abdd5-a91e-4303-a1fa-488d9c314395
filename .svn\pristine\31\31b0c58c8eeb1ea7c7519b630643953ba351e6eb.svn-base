am4core.useTheme(am4themes_animated);

// Create chart instance
var chart = am4core.create("chartdiv", am4charts.PieChart);
if (chart.logo) {
    chart.logo.disabled = true;
}
// Add data
chart.data = [{
    "country": "Srivignesh",
    "litres": 120,
    "color": am4core.color("#ff4800")
}, {
    "country": "Ragul",
    "litres": 99,
    "color": am4core.color("#072448")
}, {
    "country": "Belgium",
    "litres": 60,
    "color": am4core.color("#54d2d2")
}, {
    "country": "The Netherlands",
    "litres": 50,
    "color": am4core.color("#ffcb00")
}];

// Add and configure Series
var pieSeries = chart.series.push(new am4charts.PieSeries());
pieSeries.dataFields.value = "litres";
pieSeries.dataFields.category = "country";
pieSeries.ticks.template.disabled = true;
pieSeries.alignLabels = false;
pieSeries.labels.template.text = "{value.percent.formatNumber('#.0')}";
pieSeries.slices.template.propertyFields.fill = "color";
pieSeries.labels.template.radius = am4core.percent(-30);
pieSeries.labels.template.fill = am4core.color("white");
chart.radius = am4core.percent(100);
let tableone = [1, 2, 3, 4, 5, 6,].map((currEle) => {
    return (
        ` <tr>
         <td>${currEle}</td>
         <td>CP Admin</td>
         <td>Login User</td>
         <td>172.16.16.103</td>
         <td>The User cpadmin logged in</td>
         <td>02 12 2022 11:31:04 AM</td>
        
      </tr>`
    )
}).join(" ");
document.getElementById("tableDataone").innerHTML = tableone;