﻿using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Events.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncOption.Commands
{
    public class UpdateRsyncOptionTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IRsyncOptionRepository> _mockRsyncOptionRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly UpdateRsyncOptionCommandHandler _handler;

        public UpdateRsyncOptionTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockRsyncOptionRepository = new Mock<IRsyncOptionRepository>();
            _mockPublisher = new Mock<IPublisher>();

            _handler = new UpdateRsyncOptionCommandHandler(
                _mockMapper.Object,
                _mockRsyncOptionRepository.Object,
                _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccessResponse_WhenCommandIsValid()
        {
            var command = new UpdateRsyncOptionCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "UpdatedOption"
            };

            var rsyncOptionEntity = new Domain.Entities.RsyncOption
            {
                ReferenceId = command.Id,
                Name = "OriginalOption",
                IsActive = true
            };

            _mockRsyncOptionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(rsyncOptionEntity);

            _mockRsyncOptionRepository
                .Setup(repo => repo.UpdateAsync(rsyncOptionEntity))
                .Returns(ToString);

            _mockMapper
                .Setup(mapper => mapper.Map(command, rsyncOptionEntity, typeof(UpdateRsyncOptionCommand), typeof(Domain.Entities.RsyncOption)))
                .Callback<UpdateRsyncOptionCommand, Domain.Entities.RsyncOption, Type, Type>((src, dest, _, __) =>
                {
                    dest.Name = src.Name;
                });

            _mockPublisher
                .Setup(publisher => publisher.Publish(It.IsAny<RsyncOptionUpdatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.Id, result.Id);
            Assert.Contains("UpdatedOption", result.Message);

            _mockRsyncOptionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncOptionRepository.Verify(repo => repo.UpdateAsync(rsyncOptionEntity), Times.Once);
            _mockPublisher.Verify(publisher => publisher.Publish(
                It.Is<RsyncOptionUpdatedEvent>(e => e.Name == command.Name),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRsyncOptionDoesNotExist()
        {
            var command = new UpdateRsyncOptionCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            _mockRsyncOptionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.RsyncOption)null);

            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(command, CancellationToken.None));

            _mockRsyncOptionRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRsyncOptionRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RsyncOption>()), Times.Never);
            _mockPublisher.Verify(publisher => publisher.Publish(It.IsAny<RsyncOptionUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldMapPropertiesCorrectly_WhenCommandIsValid()
        {
            var command = new UpdateRsyncOptionCommand
            {
                Id = Guid.NewGuid().ToString(),
                Name = "UpdatedOption"
            };

            var rsyncOptionEntity = new Domain.Entities.RsyncOption
            {
                ReferenceId = command.Id,
                Name = "OriginalOption"
            };

            _mockRsyncOptionRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(rsyncOptionEntity);

            _mockMapper
                .Setup(mapper => mapper.Map(command, rsyncOptionEntity, typeof(UpdateRsyncOptionCommand), typeof(Domain.Entities.RsyncOption)))
                .Callback<UpdateRsyncOptionCommand, Domain.Entities.RsyncOption, Type, Type>((src, dest, _, __) =>
                {
                    dest.Name = src.Name;
                });

            await _handler.Handle(command, CancellationToken.None);

            Assert.Equal("UpdatedOption", rsyncOptionEntity.Name);
        }
    }
}
