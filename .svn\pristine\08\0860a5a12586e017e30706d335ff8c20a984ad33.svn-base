﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPaginatedList;

public class GetLicenseManagerPaginatedListQueryHandler : IRequestHandler<GetLicenseManagerPaginatedListQuery,
    PaginatedResult<LicenseManagerListVm>>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;

    public GetLicenseManagerPaginatedListQueryHandler(IMapper mapper,
        ILicenseManagerRepository licenseManagerRepository)
    {
        _mapper = mapper;
        _licenseManagerRepository = licenseManagerRepository;
    }

    public async Task<PaginatedResult<LicenseManagerListVm>> Handle(GetLicenseManagerPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var queryable = _licenseManagerRepository.PaginatedListAllAsync();

        var productFilterSpec = new LicenseManagerFilterSpecification(request.SearchString);

        var licenseManager = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<LicenseManagerListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //await _publisher.Publish(new LicenseManagerPaginatedEvent(), cancellationToken);

        return licenseManager;
    }
}