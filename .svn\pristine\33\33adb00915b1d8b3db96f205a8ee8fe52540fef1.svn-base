using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Events.Create;

public class DriftImpactTypeMasterCreatedEventHandler : INotificationHandler<DriftImpactTypeMasterCreatedEvent>
{
    private readonly ILogger<DriftImpactTypeMasterCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DriftImpactTypeMasterCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DriftImpactTypeMasterCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DriftImpactTypeMasterCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DriftImpactTypeMaster",
            Entity = "DriftImpactTypeMaster",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DriftImpactTypeMaster '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DriftImpactTypeMaster '{createdEvent.Name}' created successfully.");
    }
}