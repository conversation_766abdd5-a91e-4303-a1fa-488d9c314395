using ContinuityPatrol.Domain.ViewModels.DriftEventModel;

namespace ContinuityPatrol.Application.Features.DriftEvent.Queries.GetList;

public class GetDriftEventListQueryHandler : IRequestHandler<GetDriftEventListQuery, List<DriftEventListVm>>
{
    private readonly IDriftEventRepository _driftEventRepository;
    private readonly IMapper _mapper;

    public GetDriftEventListQueryHandler(IMapper mapper, IDriftEventRepository driftEventRepository)
    {
        _mapper = mapper;
        _driftEventRepository = driftEventRepository;
    }

    public async Task<List<DriftEventListVm>> Handle(GetDriftEventListQuery request,
        CancellationToken cancellationToken)
    {
        var driftEvents = await _driftEventRepository.ListAllAsync();

        if (driftEvents.Count <= 0) return new List<DriftEventListVm>();

        return _mapper.Map<List<DriftEventListVm>>(driftEvents);
    }
}