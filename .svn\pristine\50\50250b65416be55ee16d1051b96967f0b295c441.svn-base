﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixRepository : BaseRepository<ApprovalMatrix>, IApprovalMatrixRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService  _loggedInUserService;

    public ApprovalMatrixRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService) : base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService=loggedInUserService;
    }

    public Task<bool> IsApprovalMatrixNameUnique(string name)
    {
        var matches = _dbContext.ApprovalMatrices.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<bool> IsApprovalMatrixNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.ApprovalMatrices.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.ApprovalMatrices.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<string> ApproveRequest(string id, string status, string properties, string approver)
    {
        var approvalMatrix = _dbContext.ApprovalMatrices.Where(e => e.ReferenceId.Equals(id)).FirstOrDefault();
        approvalMatrix.Status = status;
        approvalMatrix.Properties = properties;
        approvalMatrix.ApprovedBy = approver;
        _dbContext.Entry(approvalMatrix).Property(x => x.Status).IsModified = true;
        _dbContext.Entry(approvalMatrix).Property(x => x.Properties).IsModified = true;
        _dbContext.Entry(approvalMatrix).Property(x => x.ApprovedBy).IsModified = true;
        _dbContext.SaveChanges();
        return Task.FromResult("Sucess");
    }

    public Task<string> SetCounterForApproval(string Id, string counter, string properties, string approver)
    {
        var approvalmatrix = _dbContext.ApprovalMatrices.Where(x => x.ReferenceId.Equals(Id)).FirstOrDefault();

        approvalmatrix.ApprovalFlag = counter;
        approvalmatrix.Properties = properties;
        approvalmatrix.ApprovedBy = approver;
        _dbContext.Entry(approvalmatrix).Property(x => x.Properties).IsModified = true;
        _dbContext.Entry(approvalmatrix).Property(x => x.ApprovalFlag).IsModified = true;
        _dbContext.Entry(approvalmatrix).Property(x => x.ApprovedBy).IsModified = true;
        _dbContext.ApprovalMatrices.Update(approvalmatrix);
        _dbContext.SaveChanges();
        return Task.FromResult("Sucess");
    }

    public Task<string> SetCounterForReject(string Id, string counter, string properties, string approver)
    {
        var approvalmatrix = _dbContext.ApprovalMatrices.Where(x => x.ReferenceId.Equals(Id)).FirstOrDefault();

        approvalmatrix.RejectedFlag = counter;
        approvalmatrix.Properties = properties;
        approvalmatrix.ApprovedBy = approver;
        _dbContext.Entry(approvalmatrix).Property(x => x.Properties).IsModified = true;
        _dbContext.Entry(approvalmatrix).Property(x => x.RejectedFlag).IsModified = true;
        _dbContext.Entry(approvalmatrix).Property(x => x.ApprovedBy).IsModified = true;
        _dbContext.ApprovalMatrices.Update(approvalmatrix);
        _dbContext.SaveChanges();
        return Task.FromResult("Sucess");
    }

    public async Task<bool> GetByTemplateName(string templateName)
    {
        var _Isattached =await _dbContext.ApprovalMatrices.Where(x => x.TemplateName.Equals(templateName)).FirstOrDefaultAsync();
        var Isattached = false;
        if (_Isattached != null) Isattached = true;

        return Isattached;
    }

    public override async Task<PaginatedResult<ApprovalMatrix>> PaginatedListAllAsync(int pageNumber, int pageSize,Specification<ApprovalMatrix> specification,string sortColumn,string sortOrder) 
    {
        var result = await Entities.Active().Specify(specification).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        return result;  
    }
}