﻿namespace ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNameUnique;

public class GetGroupPolicyNameUniqueQueryHandler : IRequestHandler<GetGroupPolicyNameUniqueQuery, bool>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;

    public GetGroupPolicyNameUniqueQueryHandler(IGroupPolicyRepository groupPolicyRepository)
    {
        _groupPolicyRepository = groupPolicyRepository;
    }

    public async Task<bool> Handle(GetGroupPolicyNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _groupPolicyRepository.IsGroupPolicyNameExist(request.GroupPolicyName, request.GroupPolicyId);
    }
}