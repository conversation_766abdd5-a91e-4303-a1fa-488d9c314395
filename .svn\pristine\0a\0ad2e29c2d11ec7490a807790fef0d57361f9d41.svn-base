﻿using ContinuityPatrol.Application.Features.Replication.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Views;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Events;

public class DeleteReplicationEventTests : IClassFixture<ReplicationFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ReplicationFixture _replicationFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

   // private readonly Mock<IHeatMapStatusRepository> _mockHeatMapStatusRepository=new();

    private readonly ReplicationDeletedEventHandler _handler;

    public DeleteReplicationEventTests(ReplicationFixture replicationFixture, UserActivityFixture userActivityFixture)
    {
        _replicationFixture = replicationFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockReplicationEventLogger = new Mock<ILogger<ReplicationDeletedEventHandler>>();

        _mockUserActivityRepository = ReplicationRepositoryMocks.CreateReplicationEventRepository(_userActivityFixture.UserActivities);

        _handler = new ReplicationDeletedEventHandler(mockLoggedInUserService.Object, mockReplicationEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteReplicationEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_replicationFixture.ReplicationDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }
    
    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        var heatMap = new List<HeatMapStatusView>();

       // _mockHeatMapStatusRepository.Setup(dp=>dp.GetHeatMapStatusByEntityId(_replicationFixture.ReplicationDeletedEvent.ReplicationId)).ReturnsAsync(heatMap);

        await _handler.Handle(_replicationFixture.ReplicationDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}