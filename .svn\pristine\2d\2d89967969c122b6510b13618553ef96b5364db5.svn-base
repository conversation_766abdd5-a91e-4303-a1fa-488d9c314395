﻿using ContinuityPatrol.Application.Features.Database.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Queries;

public class GetDatabaseListQueryHandlerTests : IClassFixture<DatabaseFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly InfraObjectFixture _infraObjectFixture;

    private Mock<IDatabaseRepository> _mockDatabaseRepository;

    private Mock<IDatabaseViewRepository> _mockDatabaseViewRepository;

    private readonly GetDatabaseListQueryHandler _handler;

    public GetDatabaseListQueryHandlerTests(DatabaseFixture databaseFixture, InfraObjectFixture infraObjectFixture)
    {
        _databaseFixture = databaseFixture;

        _infraObjectFixture = infraObjectFixture;

        _mockDatabaseViewRepository = new Mock<IDatabaseViewRepository>();

        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseRepository(_databaseFixture.Databases);

        _handler = new GetDatabaseListQueryHandler(_databaseFixture.Mapper, _mockDatabaseViewRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_DatabasesCount()
    {
        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_Return_Data_When_Oracle_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "Oracle";

        _databaseFixture.Databases[0].Properties = "{\"OracleSID\":\"DEVDS\",\"InstanceName\":\"DGB\" ,\"UserName\":\"sa\" ,\"Password\":\"Admin@123\" ,\"Archive\":\"/u04/arch\" ,\"ASMGrid\":\"ODG11G_PR\" , \"Redo\":\"/u01/app/oracle/product/11.2.0/Redo\" , \"Home\":\"/u01/app/oracle/product/11.2.0/dbhome_1\" ,  \"IsASM\":\"racv\" ,  \"ASMInstanceName\":\"ADGB\" ,  \"ASMUserName\":\"Asa\" ,  \"ASMPassword\":\"Admin@121312\" ,  \"ASMPath\":\"/ou/th\" ,  \"PreExecutionCommand\":\"test\" ,  \"AssignNodes\":\"Test_node\",\"Port\":1234,\"IsPartofRac\" : false }";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        //_infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("Oracle");
        result[0].Id.ShouldBe(_databaseFixture.Databases[0].ReferenceId);
        result[0].Name.ShouldBe(_databaseFixture.Databases[0].Name);
        result[0].Type.ShouldBe(_databaseFixture.Databases[0].Type);
        result[0].ServerId.ShouldBe(_databaseFixture.Databases[0].ServerId);
        result[0].ServerName.ShouldNotBeNull();
        result[0].Properties.ShouldBe(_databaseFixture.Databases[0].Properties);
        result[0].ModeType.ShouldBe(_databaseFixture.Databases[0].ModeType);
        result[0].LicenseKey.ShouldBe(_databaseFixture.Databases[0].LicenseKey);
    }

    [Fact]
    public async Task Handle_Return_Data_When_MsSql_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MsSql";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseSId\":\"Mirror_sql\",\"DatabaseName\":\"Mirror_sql2\" ,\"AuthenticationMode\":\"SqlServer\",\"Port\":1234  }";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
       // _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MsSql");
    }

    [Fact]
    public async Task Handle_Return_Data_When_CloudantDB_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "CloudantDB";

        _databaseFixture.Databases[0].Properties = "{\"DBPath\":\"demo\", \"DBName\":\"Demotest\", \"LocalBalancerNodeURL\":\"NodeUrl\", \"UserName\":\"Admin\",\"Password\":\"Admin@123\"}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("CloudantDB");
    }

    [Fact]
    public async Task Handle_Return_Data_When_ExchangeDAG_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "ExchangeDAG";

        _databaseFixture.Databases[0].Properties = "{\"MailboxDatabase\":\"Mailbox\",\"SelectAuthenticationType\":\"TestDemo\",\"SelectProtocolType\":\"ProtocolType\"}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("ExchangeDAG");
    }

    [Fact]
    public async Task Handle_Return_Data_When_Exchange_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "Exchange";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseGroupName\":\"DatabaseName\",\"MailboxDatabase\":\"MailboxDemo\"}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("Exchange");
    }

    [Fact]
    public async Task Handle_Return_Data_When_HanaDB_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "HanaDB";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseSID\":\"DBSID\",\"InstanceNumber\":1,\"HostName\":\"HostAdmin\",\"UserName\":\"Admin\",\"Password\":\"Admin@123\",\"Port\":1234}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("HanaDB");
    }

    [Fact]
    public async Task Handle_Return_Data_When_IBMDB2_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "IBMDB2";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseName\":\"Data_demo\",\"UserName\":\"Prathee\",\"Password\":\"Prath@123\",\"InstanceName\":\"username\",\"EnvironmentVariable\":\"variable\",\"Port\":12345}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("IBMDB2");
    }

    [Fact]
    public async Task Handle_Return_Data_When_MariaDB_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MariaDB";

        _databaseFixture.Databases[0].Properties = "{\"AttachedServers\":\"SrverDemo\",\"DatabaseName\":\"DemoDatabase\",\"DatabaseUserName\":\"TestDatabase\",\"DatabasePassword\":\"Data@123\",\"DatabasePort\":12345,\"Port\":1432}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
     //   _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MariaDB");
    }

    [Fact]
    public async Task Handle_Return_Data_When_MaxDB_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MaxDB";

        _databaseFixture.Databases[0].Properties = "{\"MaxDBSID\":\"MDBSID\",\"InstanceName\":\"DemoName\",\"UserName\":\"Test_Database\",\"Password\":\"DataT@123\",\"Port\":12345,\"InstallationPath\":\"Path_demo\",\"MediumName\":\"medium\",\"LogfileName\":\"logfile\"}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
        //_infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MaxDB");
    }

    [Fact]
    public async Task Handle_Return_Data_When_MongoDB_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MongoDB";

        _databaseFixture.Databases[0].Properties = "{\"InstallationPath\":\"PathDemo\",\"InstanceName\":\"DemoName\",\"UserName\":\"Test_Database\",\"Password\":\"DataT@123\",\"Port\":12345}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MongoDB");
    }

    [Fact]
    public async Task Handle_Return_Data_When_MSSQL2kx_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MSSQL2kx";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseName\":\"TestData_Demo\",\"ConfiguredinstanceName\":true,\"InstanceName\":\"Test_Instance\",\"SSOEnabled\":true,\"Port\":12345,\"AuthenticationMode\":\"Authentication\",\"UserName\":\"demoname\",\"Password\":\"Demo@123\",\"SSOArcosProfile\":\"Arcosprofile\",\"Port\":1234}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
       // _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MSSQL2kx");
    }

    [Fact]
    public async Task Handle_Return_Data_When_MySql_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "MySql";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseName\":\"TestData\",\"DatabaseUserName\":\"DB_Demo\",\"DatabasePassword\":\"Db@123\",\"DatabasePort\":123455}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
     //   _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("MySql");
    }

    [Fact]
    public async Task Handle_Return_Data_When_PostgreSQL_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "PostgreSQL";

        _databaseFixture.Databases[0].Properties = "{\"Database\":\"TestDatatesting\",\"UserName\":\"DB_Demotest\",\"Password\":\"Db@123\",\"Port\":12345,\"DatabaseDataDirectory\":\"DataDirectory\",\"DatabaseBinDirectory\":\"Databin\",\"SULogin\":\"UserAdmin\",\"ServiceName\":\"demoServer\"}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("PostgreSQL");
    }

    [Fact]
    public async Task Handle_Return_Data_When_SyBaseWithRsHADR_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "SyBaseWithRsHADR";

        _databaseFixture.Databases[0].Properties = "{\"ASEServerPort\":1021,\"DatabaseName\":\"Demotest\",\"UserName\":\"Sybase\",\"Password\":\"Sybase@123\",\"SyBaseDataServer\":\"Sy_dataserver\",\"SybaseBackUpServer\":\"Backupserver\",\"SybaseEnvPath\":\"BaseEnvpath\",\"Port\":1234}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
       // _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("SyBaseWithRsHADR");
    }

    [Fact]
    public async Task Handle_Return_Data_When_SyBase_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "SyBase";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseSID\":\"tesing_demo\",\"TransactionFileLocation\":\"filelocation\",\"UserName\":\"Sybase_demo\",\"Password\":\"Sybase@123\",\"SyBaseDataServer\":\"dataserver\",\"SybaseBackUpServer\":\"Backupserver\",\"Port\":12345}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("SyBase");
    }

    [Fact]
    public async Task Handle_Return_Data_When_SyBaseWithSRS_Type()
    {
        _databaseFixture.Databases[0].DatabaseType = "SyBaseWithSRS";

        _databaseFixture.Databases[0].Properties = "{\"DatabaseName\":\"tesing_demodatabase\",\"SybaseEnvPath\":\"syenvpath\",\"UserName\":\"Sybase_demo\",\"Password\":\"Sybase@123\",\"SyBaseDataServer\":\"dataserver\",\"SybaseBackUpServer\":\"Backupserver\",\"Port\":12345}";

        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseListVm>>();

        result[0].DatabaseType.ShouldBe("SyBaseWithSRS");
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseEmptyRepository();

        var handler = new GetDatabaseListQueryHandler(_databaseFixture.Mapper, _mockDatabaseViewRepository.Object);

        var result = await handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        var handler = new GetDatabaseListQueryHandler(_databaseFixture.Mapper, _mockDatabaseViewRepository.Object);

        await handler.Handle(new GetDatabaseListQuery(), CancellationToken.None);

        _mockDatabaseRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}