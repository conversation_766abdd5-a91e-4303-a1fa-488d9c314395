﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowActionResultRepository : BaseRepository<WorkflowActionResult>, IWorkflowActionResultRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowActionResultRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<WorkflowActionResult>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilter(
                workflowActionResult => workflowActionResult.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public override Task<WorkflowActionResult> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(workflowActionResult => workflowActionResult.ReferenceId.Equals(id) &&
                                                                   workflowActionResult.CompanyId.Equals(
                                                                       _loggedInUserService.CompanyId)).Result
                .SingleOrDefault());
    }


    public async Task<List<WorkflowActionResult>> GetWorkflowActionResultByGroupId(string groupId)
    {
       return await _dbContext.WorkflowActionResults.AsNoTracking()
           .Active()
           .Where(x => x.WorkflowOperationGroupId == groupId)
           .Select(x => new WorkflowActionResult { ReferenceId = x.ReferenceId, StepId = x.StepId, ActionId = x.ActionId, WorkflowActionName = x.WorkflowActionName, Status = x.Status, StartTime = x.StartTime, EndTime = x.EndTime, Message = x.Message,WorkflowOperationGroupId = x.WorkflowOperationGroupId})
           .ToListAsync();
    }

    public async Task<WorkflowActionResult> GetByOperationGroupIdAndStepId(string groupId, string stepId)
    {
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(x => x.WorkflowOperationGroupId  == groupId && x.StepId ==stepId)
            .OrderByDescending(x => x.Id)).FirstOrDefaultAsync();
    }


    public Task<List<WorkflowActionResult>> GetWorkflowActionResultNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.WorkflowActionResults.AsNoTracking().Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowActionResult
                    { ReferenceId = x.ReferenceId, WorkflowActionName = x.WorkflowActionName })
                .OrderBy(x => x.WorkflowActionName)
                .ToListAsync();
        return _dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Select(x => new WorkflowActionResult
                { ReferenceId = x.ReferenceId, WorkflowActionName = x.WorkflowActionName })
            .OrderBy(x => x.WorkflowActionName)
            .ToListAsync();
    }

    public async Task<List<WorkflowActionResult>> GetWorkflowActionResultByWorkflowOperationGroupId(
        string workflowOperationGroupId)
    {
        if (!_loggedInUserService.IsParent)
            return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
                .Where(e => e.WorkflowOperationGroupId.Equals(workflowOperationGroupId))
                .OrderByDescending(x => x.Id))
                .ToListAsync();
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(e => e.WorkflowOperationGroupId.Equals(workflowOperationGroupId))
            .OrderByDescending(x => x.Id))
            .ToListAsync();
    }

    public async Task<List<WorkflowActionResult>> GetWorkflowActionResultByWorkflowOperationId(
        string workflowOperationId)
    {
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(x => x.WorkflowOperationId.Equals(workflowOperationId))
            .OrderBy(x => x.Id))
            .ToListAsync();
    }

    public async Task<List<WorkflowActionResult>> GetWorkflowActionResultByWorkflowOperationIds(
      List<string> workflowOperationIds)
    {
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(x => workflowOperationIds.Contains(x.WorkflowOperationId))
            .OrderBy(x => x.Id))
            .ToListAsync();
    }


    public async Task<List<WorkflowActionResult>> GetByWorkflowOperationIds(List<string> workflowOperationIds)
    {
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(x => workflowOperationIds.Contains(x.WorkflowOperationId))
            .OrderBy(x => x.Id))
            .ToListAsync();
    }



    public override IQueryable<WorkflowActionResult> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return SelectToWorkflowAction(Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id));
        return SelectToWorkflowAction(Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id));
    }

    public Task<bool> IsWorkflowActionResultNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.WorkflowActionResults.Any(e => e.WorkflowActionName.Equals(name)))
            : Task.FromResult(_dbContext.WorkflowActionResults.Where(e => e.WorkflowActionName.Equals(name)).ToList()
                .Unique(id));
    }

    public async Task<bool> IsDependWorkflowDetails(string workflowOperationId, string workflowOperationGroupId,
        string stepId)
    {
        var count = await _dbContext.WorkflowActionResults.AsNoTracking()
            .Where(w =>
                w.WorkflowOperationId.Equals(workflowOperationId) && w.WorkflowOperationGroupId.Equals(
                                                                      workflowOperationGroupId)
                                                                  && w.StepId.Equals(stepId) &&
                                                                  (w.Status.Trim().ToLower().Equals("success") ||
                                                                   w.Status.Trim().ToLower().Equals("skip") ||
                                                                   w.Status.Trim().ToLower().Equals("aborted")))
            .CountAsync();

        return count > 0;
    }

    public async Task<WorkflowActionResult> GetByInfraObjectAndActionId(string infraObjectId, string actionId)
    {
        if (!_loggedInUserService.IsParent)
            return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
                .Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ActionId.Equals(actionId))
                .OrderByDescending(x => x.Id)).FirstOrDefaultAsync();
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ActionId.Equals(actionId))
            .OrderByDescending(x => x.Id)).FirstOrDefaultAsync();
    }

    public async Task<List<WorkflowActionResult>> GetWorkflowActionResultByWorkflowOperationIdAndGroupId(
        string workflowOperationId, string workflowOperationGroupId)
    {
        return await SelectToWorkflowAction(_dbContext.WorkflowActionResults.AsNoTracking().Active()
            .Where(x => x.WorkflowOperationId.Equals(workflowOperationId) &&
                        x.WorkflowOperationGroupId.Equals(workflowOperationGroupId))
            .OrderBy(x => x.Id))
            .ToListAsync();
    }

    private IQueryable<WorkflowActionResult> SelectToWorkflowAction(IQueryable<WorkflowActionResult> query)
    {
        return query.Select(x => new WorkflowActionResult
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            WorkflowActionName = x.WorkflowActionName,
            CompanyId = x.CompanyId,
            WorkflowOperationId = x.WorkflowOperationId,
            WorkflowOperationGroupId =x.WorkflowOperationGroupId,
            InfraObjectId = x.InfraObjectId,
            InfraObjectName = x.InfraObjectName,
            StepId = x.StepId,
            ActionId = x.ActionId,
            ConditionActionId = x.ConditionActionId,
            StartTime = x.StartTime,
            EndTime = x.EndTime,
            Status = x.Status,
            StartRto = x.StartRto,
            Message = x.Message,
            SkipStep = x.SkipStep,
            IsReload = x.IsReload,
            IsRetry = x.IsRetry,
            IsParallel = x.IsParallel,
            Direction = x.Direction,
            Version = x.Version,
            NodeId = x.NodeId,
            NodeName = x.NodeName
        });
    }

}