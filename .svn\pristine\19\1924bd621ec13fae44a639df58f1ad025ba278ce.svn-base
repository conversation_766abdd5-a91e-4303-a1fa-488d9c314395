using ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Events.Update;

namespace ContinuityPatrol.Application.Features.WorkflowApprovalMapping.Commands.Update;

public class UpdateWorkflowApprovalMappingCommandHandler : IRequestHandler<UpdateWorkflowApprovalMappingCommand, UpdateWorkflowApprovalMappingResponse>
{
    private readonly IWorkflowApprovalMappingRepository _workflowApprovalMappingRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateWorkflowApprovalMappingCommandHandler(IMapper mapper, IWorkflowApprovalMappingRepository workflowApprovalMappingRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _workflowApprovalMappingRepository = workflowApprovalMappingRepository;
        _publisher = publisher;
    }

    public async Task<UpdateWorkflowApprovalMappingResponse> Handle(UpdateWorkflowApprovalMappingCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _workflowApprovalMappingRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.WorkflowApprovalMapping), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateWorkflowApprovalMappingCommand), typeof(Domain.Entities.WorkflowApprovalMapping));

        await _workflowApprovalMappingRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateWorkflowApprovalMappingResponse
        {
            Message = Message.Update(nameof(Domain.Entities.WorkflowApprovalMapping), eventToUpdate.WorkflowName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new WorkflowApprovalMappingUpdatedEvent { Name = eventToUpdate.WorkflowName }, cancellationToken);

        return response;
    }
}
