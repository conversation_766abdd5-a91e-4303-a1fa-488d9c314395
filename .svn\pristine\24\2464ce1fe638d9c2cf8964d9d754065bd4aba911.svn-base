using ContinuityPatrol.Application.Features.DriftProfile.Commands.Create;
using ContinuityPatrol.Application.Features.DriftProfile.Commands.Update;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftProfileService : BaseClient, IDriftProfileService
{
    public DriftProfileService(IConfiguration config, IAppCache cache, ILogger<DriftProfileService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DriftProfileListVm>> GetDriftProfileList()
    {
        var request = new RestRequest("api/v6/driftprofiles");

        return await GetFromCache<List<DriftProfileListVm>>(request, "GetDriftProfileList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftProfileCommand createDriftProfileCommand)
    {
        var request = new RestRequest("api/v6/driftprofiles", Method.Post);

        request.AddJsonBody(createDriftProfileCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftProfileCommand updateDriftProfileCommand)
    {
        var request = new RestRequest("api/v6/driftprofiles", Method.Put);

        request.AddJsonBody(updateDriftProfileCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftprofiles/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DriftProfileDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftprofiles/{id}");

        return await Get<DriftProfileDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftProfileNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftprofiles/name-exist?driftprofileName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftProfileListVm>> GetPaginatedDriftProfiles(GetDriftProfilePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/driftprofiles/paginated-list");

        return await Get<PaginatedResult<DriftProfileListVm>>(request);
    }
    #endregion
}
