﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IMsSqlNativeLogShippingMonitorStatusRepository : IRepository<MsSqlNativeLogShippingMonitorStatus>
{
    Task<List<MsSqlNativeLogShippingMonitorStatus>> GetDetailByType(string type);

    Task<MsSqlNativeLogShippingMonitorStatus> GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(
        string infraObjectId);
}