using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class GlobalVariableFilterSpecification : Specification<GlobalVariable>
{
    public GlobalVariableFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.VariableName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)          
                	if (stringItem.Contains("variablename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.VariableName.Contains(stringItem.Replace("variablename=", "",StringComparison.OrdinalIgnoreCase)));
					else if (stringItem.Contains("variablevalue=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.VariableValue.Contains(stringItem.Replace("variablevalue=", "",StringComparison.OrdinalIgnoreCase)));
                  
            }
            else
            {
                Criteria = p =>p.VariableName.Contains(searchString)||p.VariableValue.Contains(searchString);
            }
        }
    }
}
