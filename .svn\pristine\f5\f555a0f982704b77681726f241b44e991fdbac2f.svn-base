using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.CyberAlert.Queries.GetPaginatedList;

public class
    GetCyberAlertPaginatedListQueryHandler : IRequestHandler<GetCyberAlertPaginatedListQuery,
        PaginatedResult<CyberAlertListVm>>
{
    private readonly ICyberAlertRepository _cyberAlertRepository;
    private readonly IMapper _mapper;

    public GetCyberAlertPaginatedListQueryHandler(IMapper mapper, ICyberAlertRepository cyberAlertRepository)
    {
        _mapper = mapper;
        _cyberAlertRepository = cyberAlertRepository;
    }

    public async Task<PaginatedResult<CyberAlertListVm>> Handle(GetCyberAlertPaginatedListQuery request,
        CancellationToken cancellationToken)
    {

        var productFilterSpec = new CyberAlertFilterSpecification(request.SearchString);

        var queryable = request.severity.IsNotNullOrWhiteSpace()
            ?await  _cyberAlertRepository.GetPaginatedListBySeverity(request.PageNumber,request.PageSize,productFilterSpec, request.severity, request.SortColumn, request.SortOrder)
            :await _cyberAlertRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var cyberAlertList = _mapper.Map<PaginatedResult<CyberAlertListVm>>(queryable);

        return cyberAlertList;
        //var queryable = request.severity.IsNotNullOrWhiteSpace()
        //    ?_cyberAlertRepository.GetPaginatedListBySeverity(request.severity)
        //    :_cyberAlertRepository.GetPaginatedQuery();

        //var productFilterSpec = new CyberAlertFilterSpecification(request.SearchString);

        //var cyberAlertList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<CyberAlertListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return cyberAlertList;
    }
}