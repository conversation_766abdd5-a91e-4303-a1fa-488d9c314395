﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;

public class ItViewByBusinessServiceIdVm
{
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string Status { get; set; }
    public List<ItViewInfraObjectList> InfraObjectDataLag { get; set; } = new();
}

public class ItViewInfraObjectList
{
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public int DrOperationStatus { get; set; }
    public int ReplicationStatus { get; set; }
    public string MonitorId { get; set; }
    public string MonitorType { get; set; }
    public string TypeName { get; set; }
    public string Status { get; set; }
    public string DataLagValue { get; set; }
    public string ConfiguredRPO { get; set; }
    public string Properties { get; set; }
    public string State { get; set; }
    public string ReplicationCategoryType { get; set; }
    public string ReplicationType { get; set; }
    public bool IsMonitorService { get; set; }
}