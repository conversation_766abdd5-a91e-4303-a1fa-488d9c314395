using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLAlwaysOnMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLAlwaysOnMonitorStatus";

    public List<MSSQLAlwaysOnMonitorStatus> MSSQLAlwaysOnMonitorStatusPaginationList { get; set; }
    public List<MSSQLAlwaysOnMonitorStatus> MSSQLAlwaysOnMonitorStatusList { get; set; }
    public MSSQLAlwaysOnMonitorStatus MSSQLAlwaysOnMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLAlwaysOnMonitorStatusFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLAlwaysOnMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            );

        MSSQLAlwaysOnMonitorStatusPaginationList = _fixture.CreateMany<MSSQLAlwaysOnMonitorStatus>(20).ToList();
        MSSQLAlwaysOnMonitorStatusList = _fixture.CreateMany<MSSQLAlwaysOnMonitorStatus>(5).ToList();
        MSSQLAlwaysOnMonitorStatusDto = _fixture.Create<MSSQLAlwaysOnMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLAlwaysOnMonitorStatus CreateMSSQLAlwaysOnMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLAlwaysOnMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLAlwaysOnMonitorStatus CreateMSSQLAlwaysOnMonitorStatusWithWhitespace()
    {
        return CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: "  MSSQLAlwaysOnMonitorStatus  ");
    }

    public MSSQLAlwaysOnMonitorStatus CreateMSSQLAlwaysOnMonitorStatusWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: longType);
    }

    public MSSQLAlwaysOnMonitorStatus CreateMSSQLAlwaysOnMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMSSQLAlwaysOnMonitorStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<MSSQLAlwaysOnMonitorStatus> CreateMultipleMSSQLAlwaysOnMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<MSSQLAlwaysOnMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public List<MSSQLAlwaysOnMonitorStatus> CreateMSSQLAlwaysOnMonitorStatusWithMixedActiveStatus(string type)
    {
        return new List<MSSQLAlwaysOnMonitorStatus>
        {
            CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: type, isActive: true),
            CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: type, isActive: false),
            CreateMSSQLAlwaysOnMonitorStatusWithProperties(type: type, isActive: true)
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLAlwaysOnMonitorStatus", "AlwaysOnStatus", "MSSQLStatus", "DatabaseStatus" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
