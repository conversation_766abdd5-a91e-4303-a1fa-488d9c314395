﻿using ContinuityPatrol.Application.Features.Alert.Events.View;
using ContinuityPatrol.Application.Features.Alert.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using DevExpress.XtraReports.UI;
using Newtonsoft.Json;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Alert.Controllers;

[Area("Alert")]
public class AlertDashboardController : BaseController
{
    public readonly IPublisher _publisher;
    private readonly IAlertService _alertService;
    public static ILogger<AlertDashboardController> _logger;
    public static string CompanyLogo { get; set; }

    // private readonly IUserService userService;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly IAlertMasterService _alertMasterService;

    public AlertDashboardController(IPublisher publisher, IAlertService alertService, ILogger<AlertDashboardController> logger, IDataProvider dataProvider, IMapper mapper, IAlertMasterService alertMasterService)
    {
        _publisher = publisher;
        _alertService = alertService;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
        _alertMasterService = alertMasterService;
    }

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in AlertDashboard");
        await _publisher.Publish(new AlertPaginatedEvent());
        var alerts = (await _alertService.GetAlertList()).ToList();
        return View(alerts);
    }
    public async Task<IActionResult> ListWeek(string startDate, string endDate)
    {
        _logger.LogDebug("Entering ListWeek method in AlertDashboard");
        try
        {
            var alertGetListWeek = await _alertService.GetAlertListByStartOfWeek(startDate, endDate);
            _logger.LogDebug($"Retrieving list of week from {startDate} to {endDate} in alertDashboard");
            return Json(new { Success = true, data = alertGetListWeek });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on alert dashboard page while retrieving the list of week from {startDate} to {endDate}.", ex);
            return ex.GetJsonException();
        }


    }

    public async Task<IActionResult> GetFilterDate(string startDate, string endDate)
    {
        _logger.LogDebug("Entering GetFilterDate method in AlertDashboard");
        try
        {
            var alertGetListWeek = await _alertService.GetAlertListFilterByDate(startDate, endDate);

            return Json(alertGetListWeek);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on alert dashboard page while retrieving alert list from {startDate} to {endDate}.", ex);
            return ex.GetJsonException();
        }
    }
    public async Task<IActionResult> LastAlertCount()
    {
        _logger.LogDebug("Entering LastAlertCount method in AlertDashboard");
        try
        {
            var lastAlertCount = await _alertService.GetLastAlertCount();
            _logger.LogDebug($"Successfully retrieved last alert count {lastAlertCount} in AlertDashboard.");
            return Json(new { Success = true, data = lastAlertCount });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on alert dashboard page while retrieving the last alert count.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]

    public async Task<JsonResult> GetEscalationMatrixList()
    {
        _logger.LogDebug("Entering GetEscalationMatrixList method in AlertDashboard");
        try
        {
            var escalationMatrixList = await _dataProvider.EscalationService.GetPaginatedEscalationList(new GetEscalationMatrixPaginatedListQuery());
            _logger.LogDebug("Successfully retrieved escalation matrix list in AlertDashboard");

            return Json(escalationMatrixList);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while retrieving escalationMatrixList.", ex);
            return null;
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> GetEscalationMatrixLevelByMatrixId(string escMatId)
    {
        _logger.LogDebug("Entering GetEscalationMatrixLevelByMatrixId method in AlertDashboard");
        // var escalationList = await _dataProvider.EscalationMatrixLevelService.GetAllEscalationLevel(EscMatID);
        try
        {
            var escalationMatrixById = await _dataProvider.EscalationService.GetEscMatrixById(escMatId);

            var user = await _dataProvider.User.GetByReferenceId(escalationMatrixById.OwnerID);

            _logger.LogDebug($"Successfully retrieved  escalationMatrixLevel by matrixId {escMatId}");
            return Json(await _dataProvider.EscalationMatrixLevelService.GetAllEscalationLevel(escMatId));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while retrieving escalationMatrixLevel by matrixId.", ex);
            return null;
        }


    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> GetEscalationMatrixOwnerByMatrixId(string escalationOwnerId)
    {

        _logger.LogDebug("Entering GetEscalationMatrixOwnerByMatrixId method in AlertDashboard");
        try
        {
            var escalationMatrixById = await _dataProvider.EscalationService.GetEscMatrixById(escalationOwnerId);

            var usr = await _dataProvider.User.GetByReferenceId(escalationMatrixById.OwnerID);

            _logger.LogDebug($"Successfully retrieved  escalationMatrixOwner by matrixId {escalationOwnerId}");

            return Json(usr.LoginName);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while retrieving escalationMatrixOwner by matrixId .", ex);
            return null;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetAlertPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in AlertDashboard");
        try
        {
            var paginatedList = await _alertService.GetAlertPaginatedList(query);
            _logger.LogDebug("Successfully retrieved alert paginatedList in AlertDashboard");
            var i =new
            {
                item1=paginatedList.Item1,
                item2 =paginatedList.Item2,
            };
            return Json(new { Success = true, data = i });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpGet]
    [AllowAnonymous]
    [SupportedOSPlatform("windows")]
    public async Task<IActionResult> LoadReport(GetAlertPaginatedListQuery query)
    {
        _logger.LogDebug("Entering LoadReport method in AlertDashboard");
        var reportsDirectory = "";
        try
        {
            CompanyLogo = string.Empty;
            var companyId = WebHelper.UserSession.CompanyId;
            var companyDetails = await _dataProvider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo.ToString(); }

            string reportGeneratedName = WebHelper.UserSession.LoginName.ToString();
            query.PageSize = 0;
            query.PageNumber = 1;
            var paginatedList = await _alertService.GetAlertPaginatedList(query);
            var reportData = new
            {
                data = paginatedList.Item1
            };
            string reportf = JsonConvert.SerializeObject(reportData.data);
            XtraReport report = new Report.ReportTemplate.Alert(reportf, reportGeneratedName);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            var fileName = "AlertReport_" + filenameSuffix + ".pdf";
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
            await report.ExportToPdfAsync(reportsDirectory);
            byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);
            return File(fileBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while loading the Alert report.", ex);
            return StatusCode(500, new { message = "An error occurred while generating the Alert report." });
        }
        finally
        {

            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> UpdateAlertMaster(string escMatId)
    {
        _logger.LogDebug("Entering UpdateAlertMaster method in AlertDashboard");
        try
        {
            var alertDetails = await _alertMasterService.GetAlertMasterById(escMatId.Split('#')[1]);
            alertDetails.EscMatId = escMatId.Split('#')[0];
            alertDetails.IsAcknowledgement = true;

            var alertMasterCommand = _mapper.Map<UpdateAlertMasterCommand>(alertDetails);
            var result = await _dataProvider.AlertMasterService.UpdateAsync(alertMasterCommand);

            return Json(result);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert Dashboard while updating alert master.", ex);
            return null;
        }
    }



    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<JsonResult> GetAlertDetailsByAlertId(string alertId)
    {
        _logger.LogDebug("Entering GetAlertDetailsByAlertId method in AlertDashboard");
        try
        {
            var alertDetails = await _alertMasterService.GetAlertMasterById(alertId);
            _logger.LogDebug($"Successfully retrieved alert master detail by id {alertId}");
            return Json(alertDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on alert dashboard page while retrieving alert master details by id.", ex);
            return null;
        }
    }
}