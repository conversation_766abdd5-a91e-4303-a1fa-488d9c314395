using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class ApprovalMatrixUsersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<ApprovalMatrixUsersListVm>>> GetApprovalMatrixUsers()
    {
        Logger.LogDebug("Get All ApprovalMatrixUsers");

        return Ok(await Mediator.Send(new GetApprovalMatrixUsersListQuery()));
    }

    [HttpGet("{id}", Name = "GetApprovalMatrixUsers")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<ApprovalMatrixUsersDetailVm>> GetApprovalMatrixUsersById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixUsers Id");

        Logger.LogDebug($"Get ApprovalMatrixUsers Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetApprovalMatrixUsersDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Manage.View)]
 public async Task<ActionResult<PaginatedResult<ApprovalMatrixUsersListVm>>> GetPaginatedApprovalMatrixUsers([FromQuery] GetApprovalMatrixUsersPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in ApprovalMatrixUsers Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateApprovalMatrixUsersResponse>> CreateApprovalMatrixUsers([FromBody] CreateApprovalMatrixUsersCommand createApprovalMatrixUsersCommand)
    {
        Logger.LogDebug($"Create ApprovalMatrixUsers '{createApprovalMatrixUsersCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateApprovalMatrixUsers), await Mediator.Send(createApprovalMatrixUsersCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Manage.Edit)]
    public async Task<ActionResult<UpdateApprovalMatrixUsersResponse>> UpdateApprovalMatrixUsers([FromBody] UpdateApprovalMatrixUsersCommand updateApprovalMatrixUsersCommand)
    {
        Logger.LogDebug($"Update ApprovalMatrixUsers '{updateApprovalMatrixUsersCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateApprovalMatrixUsersCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Manage.Delete)]
    public async Task<ActionResult<DeleteApprovalMatrixUsersResponse>> DeleteApprovalMatrixUsers(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ApprovalMatrixUsers Id");

        Logger.LogDebug($"Delete ApprovalMatrixUsers Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteApprovalMatrixUsersCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsApprovalMatrixUsersNameExist(string approvalMatrixUsersName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(approvalMatrixUsersName, "ApprovalMatrixUsers Name");

     Logger.LogDebug($"Check Name Exists Detail by ApprovalMatrixUsers Name '{approvalMatrixUsersName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetApprovalMatrixUsersNameUniqueQuery { Name = approvalMatrixUsersName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


