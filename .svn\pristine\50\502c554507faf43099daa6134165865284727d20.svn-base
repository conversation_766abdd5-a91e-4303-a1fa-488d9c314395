﻿let mId = sessionStorage.getItem("monitorId");
let isDefault = true;
let monitortype = 'RecoverPointForVM';
let infraObjectId = sessionStorage.getItem("infraobjectId");
let ReportData;
setTimeout(() => { recoverpointforVMmonitorstatus(mId, monitortype) },500)

setTimeout(() => { monitoringSolution(infraObjectId) }, 250)
$("#downloadReport").prop('disabled', true);
$("#btnDiscover").prop('disabled', true);
$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})
async function recoverpointforVMmonitorstatus(id, type) {
    let url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }    
}
function infraData(value) {
    $('#infraName').text(checkAndReplace(value?.infraObjectName)).attr('title', checkAndReplace(value?.infraObjectName));
    $('#modifiedTime').text(checkAndReplace(value?.rpoGeneratedDate)).attr('title', checkAndReplace(value?.rpoGeneratedDate));
}
let noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">'
let noDataSolutionDiagram = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img">'
function checkAndReplace(value) {
    return (value === null || value === '' || value === undefined) ? 'NA' : value;
}

let snapDetails = [];

$('#btnDiscover').on('click', function () { 
    $('#btnDiscover').prop('disabled', true);
    data = {}
    data.id = infraObjectId
    $.ajax({
        url: "/Monitor/Monitoring/GetDiscover",
        method: 'GET',
        dataType: 'json',
        data: data,
        async: true,
        success: function (response) {
            if (response) {
                if (response?.success) {
                    notificationAlert('success', response?.message)
                } else {
                    notificationAlert('warning', response?.message)
                }
                setTimeout(() => {
                    $('#btnDiscover').prop('disabled', false);
                },1000)
                
            } else {
                setTimeout(() => {
                    $('#btnDiscover').prop('disabled', false);
                }, 1000)
            }
           
        },
      
    })
})

//CG Health Status

$.ajax({
    url: "/Monitor/Monitoring/GetCGHealthStatus",
    method: 'GET',
    dataType: 'json',
    async: true,
    success: function (response) {
        console.log(response, 'health')
        if (response?.success) {
            RP4VMHealthChartdata(response.data)
        }
    }
})

//Consistency Group Details
let requestData = {
    infraObjectId: infraObjectId,
    consistencyGroupName: null,
    state: null,
    availabilityStatus: "Available"
};
$('#myTab button').prop('disabled', true)
function getRPCGDetails(data = requestData) {
    data.consistencyGroupName = data.consistencyGroupName === 'All' ? '' : data.consistencyGroupName;

    $.ajax({
        url: "/Monitor/Monitoring/GetRPPagination",
        method: 'GET',
        dataType: 'json',
        data: data,
        async: true,
        
        success: function (response) {
            
            let data = response?.pagination?.data || [];
            let count = response?.statusCount || {}
            ReportData = data;
            console.log("RPforVMReportDataAdded");
            
            //count?.Available ? $('#availableCount').text(`(${count?.Available})`) : $('#availableCount').text(`0`)
            //count?.New ? $('#newCount').text(`(${count?.New})`) : $('#newCount').text(`(0)`)
            //count?.Removed ? $('#removedCount').text(`(${count?.Removed})`) : $('#removedCount').text(`(0)`)

            const updateTab = (selector, value) => {
                let countValue = value ? `(${value})` : `(0)`;
                $(selector).text(countValue).closest('button').prop('disabled', !value);
            };

            updateTab('#availableCount', count?.Available);
            updateTab('#newCount', count?.New);
            updateTab('#removedCount', count?.Removed);
            
            ConsistencyData(data);
            $("#downloadReport").prop('disabled', false);
            $("#btnDiscover").prop('disabled', false); 
            //snapShotDetails(data);
        }
    });
}

//getRPCGDetails({ ...requestData, availabilityStatus: null }, true);
getRPCGDetails(requestData);
$('#myTab button').on('click', function () {
    
    let newStatus = $(this).text().split('(')[0].trim();
    requestData.availabilityStatus = newStatus === 'Available' ? 'Available' :
        newStatus === 'New' ? 'New' :
            newStatus === 'Removed' ? 'Removed' : null;
    getRPCGDetails(requestData);
    newStatus !== 'Removed' ? $('#home-tab-pane').removeClass('fade').addClass('show active') : $('#home-tab-pane').removeClass('show active').addClass('fade')
});

//Snapshot Details
//function snapShotDetails(data) {
//    $('#snapshotTable').empty()
//    let uniqueSnapTypes = new Set();
//    let uniqueBookmarks = new Set();
//    $('#ddlsnapType').empty().append('<option value="All">All</option>');
//    $('#ddlBookmark').empty().append(`<option value="All">All</option>`);    
//    data?.forEach(snap => {
//        let snapProp = snap?.snapProperties ? JSON?.parse(snap?.snapProperties) : {};       
//        snapDetails = snapProp
//        snapProp?.forEach(data => {
            
//            let snaprow = `
//            <tr>
//                <td class="text-truncate">${data?.Snapshot_Id || '-'}</td>
//                <td class="text-truncate">${data?.Snapshot_Type || '-'}</td>
//                <td class="text-truncate">${data?.Point_In_Time || '-'}</td>
//                <td class="text-truncate">${data?.Size || '-'}</td>                
//                <td class="text-truncate">${data?.Bookmark || '-'}</td>
//            </tr>
//        `;
//            $('#snapshotTable').append(snaprow)
//            let snapType = data?.Snapshot_Type;
//            if (snapType && !uniqueSnapTypes.has(snapType)) {
//                uniqueSnapTypes.add(snapType);
//                $('#ddlsnapType').append(`<option value="${snapType}">${snapType}</option>`);
//            }

//            let bookmark = data?.Bookmark;
//            if (bookmark && !uniqueBookmarks.has(bookmark)) {
//                uniqueBookmarks.add(bookmark);
//                $('#ddlBookmark').append(`<option value="${bookmark}">${bookmark}</option>`);
//            }
//        })
        
//    })
//}
$('#ddlState').on('change', function () {
    const selectedState = $('#ddlState').val();
    if (requestData.state !== selectedState) {
        requestData.state = selectedState;
        getRPCGDetails();
    }    
})

function ConsistencyData(value) { 
    $("#consistencyTable").empty();
    let groupSelectOptions = '<option value="All">All</option>';
    let groupStateOptions = '<option value="All">All</option>';
    let cgRow = ""
    let uniqueState = new Set();

    value?.forEach((data, index) => { 
        let iconClass = data?.state?.toLowerCase() === 'enabled' ?
            "text-success cp-enables fs-6 me-1" : (data?.state?.toLowerCase() === 'disabled' || data?.state?.toLowerCase() === 'disable') ? "text-danger cp-disables fs-6 me-1" : "text-danger cp-disagree fs-6 me-1"
        let statusIcon = data?.transferStatus?.toLowerCase() === 'active' ? 'cp-active-inactive text-success fs-6 me-1' : data?.transferStatus?.toLowerCase() === 'error' ? 'text-danger cp-fail-back fs-6 me-1' : 'text-danger cp-disable fs-6 me-1'
        let datalagIcon = data?.dataLag ? "cp-time text-primary mt-2 me-1 fs-6" : "text-danger cp-disable fs-6 me-1"         

        //Recovery Point Details for 1st Grid
        let collapseId = `collapse${index}`;
        let cgProps = data?.cgProperties ? JSON?.parse(data?.cgProperties) : {}        
        let prCg = cgProps?.VMRecoverPointMonitoringPR?.ReplicationMonitoringPR;
        
        let prType = cgProps?.VMRecoverPointMonitoringPR?.Type?.toLowerCase()?.includes('pr') ? 'Production' : ''
        let drType = cgProps?.VMRecoverPointMonitoring[0]?.Type?.toLowerCase()?.includes('dr') ? 'Remote Replica' : '' 
        
        $('#PluginServerIPAddress').text(prCg?.PluginServerIPAddress)        
        let drCg = cgProps?.VMRecoverPointMonitoring?.find(d => d?.Type === 'DR') || cgProps?.VMRecoverPointMonitoring[0]
        let prVMName = prCg?.ProtectedVMDetails
        let drVMName = drCg?.ReplicationMonitoring?.ProtectedVMDetails
        
        $('#DR_PluginServerIPAddress').text(drCg?.ReplicationMonitoring?.PluginServerIPAddress)
        $('#VCenterName').text(prCg?.VCenterName)
        $('#DR_VCenterName').text(drCg?.ReplicationMonitoring?.VCenterName)
        $('#VRPAClusterName').text(prCg?.VRPAClusterName)
        $('#DR_VRPAClusterName').text(drCg?.ReplicationMonitoring?.VRPAClusterName)
        $('#Version').text(prCg?.Version)
        $('#DR_Version').text(drCg?.ReplicationMonitoring?.Version)
        $('#ProtectedSize').text(data?.protectedSize)
        $('#DR_ProtectedSize').text(data?.protectedSize)
        $('#ProtectedVMNames').text(prCg?.ProtectedVMNames)
        $('#DR_ProtectedVMNames').text(drCg?.ReplicationMonitoring?.ProtectedVMNames)
//For Icons
        let prvcenter = prCg?.VCenterName ? "cp-control-file-type text-primary me-1 fs-6" : "text-danger cp-disable fs-6 me-1"
        let drvcenter = drCg?.ReplicationMonitoring?.VCenterName ? "cp-control-file-type text-primary me-1 fs-6" : "text-danger cp-disable fs-6 me-1"
        let prCluster = prCg?.VRPAClusterName ? "cp-manage-server text-primary fs-6 me-1" : "text-danger cp-disable fs-6 me-1"
        let drCluster = drCg?.ReplicationMonitoring?.VRPAClusterName ? "cp-manage-server text-primary fs-6 me-1" : "text-danger cp-disable fs-6 me-1"
        let prversion = prCg?.Version ? "cp-version text-primary me-1 fs-6" : "text-danger cp-disable fs-6 me-1"
        let drversion = drCg?.ReplicationMonitoring?.Version ? "cp-version text-primary me-1 fs-6" : "text-danger cp-disable fs-6 me-1"

//Consistency Details 1st row        
        cgRow += `<tr>
           <td>
                    <span role="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="false" aria-controls="${collapseId}">
                        <i class="cp-circle-plus text-primary"></i>
                    </span>
                </td>
            <td class="text-truncate">${data?.consistencyGroupName}</td>
            <td class="text-truncate"><i class="${statusIcon}"></i>${data?.transferStatus}</td>
            <td class="text-truncate"><i class="${iconClass}"></i>${data?.state}</td>
            <td class="text-truncate">${data?.protectedSize || '-'}</td>
            <td class="text-truncate">${data?.activityType || '-'}</td>
            <td class="text-truncate">${data?.activityStatus || '-'}</td>
            <td class="text-truncate">${data?.lastSnapShotTime}</td>
            <td class="text-truncate"><i class="${datalagIcon}"></i>${data?.dataLag}</td>
            </tr>`;

        //Consistency based Protected VM details for 2nd row

        cgRow += `<tr class="collapse" id="${collapseId}">
                <td colspan="1"></td>
                <td colspan="7">
                    <table class="table table-bordered">
                        <thead class="position-sticky top-0 z-3">
                            <tr>
                            <th>Type</th>
                            <th>Protected VM Name</th>
                            <th>Recovery Point for VM Version</th>
                           
                            <th>VRPA Cluster Name</th>
                            <th>VCenter Name</th>                            
                            </tr> </thead>
                            <tbody> 
                            <tr>
                            <td>${prType}</td>
                            <td class="text-truncate" title="${prVMName}">
    ${Array.isArray(prVMName)
            ? prVMName?.map((item,index) => `${index + 1}. ${item}`).join('<br>')
            : (typeof prVMName === 'string'
                ? prVMName.split(',').map((item,index) => `${index + 1}. ${item.trim()}`).join('<br>')
                    : '-')}
</td>
                            <td class="text-truncate"><i class="${prversion}"></i>${prCg?.Version || '-'}</td>
                           
                            <td class="text-truncate"><i class="${prCluster}"></i>${prCg?.VRPAClusterName || '-'}</td>
                            <td class="text-truncate"><i class="${prvcenter}"></i>${prCg?.VCenterName || '-'}</td>               
                            </tr>
                            <tr>
                            
                            <td>${drType}</td>
                            
<td class="text-truncate" title="${drVMName}">
    ${Array.isArray(drVMName)
            ? drVMName
                    .map((item, index) => `${index + 1}. ${item}`) // Add Sr. No.
                    .join('<br>')
            : (typeof drVMName === 'string'
                ? drVMName
                        .split(',')
                        .map((item, index) => `${index + 1}. ${item.trim()}`) // Add Sr. No.
                        .join('<br>')
                    : '-')}
</td>
                            <td class="text-truncate"><i class="${drversion}"></i>${drCg?.ReplicationMonitoring?.Version || '-'}</td>
                          
                            <td class="text-truncate"><i class="${drCluster}"></i>${drCg?.ReplicationMonitoring?.VRPAClusterName || '-'}</td>
                            <td class="text-truncate"><i class="${drvcenter}"></i>${drCg?.ReplicationMonitoring?.VCenterName || '-'}</td>
                            
                            </tr>
                            </tbody>
                            </table>
                            </td> </tr>`

        if (isDefault) {
            groupSelectOptions += `<option value="${data?.consistencyGroupName}">${data?.consistencyGroupName}</option>`

        } 
        let state = data?.state
        if (state && !uniqueState.has(state) && isDefault) {
            uniqueState.add(state);
            groupStateOptions += `<option value="${state}">${state}</option>`
           
        }       
    })
    $("#consistencyTable").append(cgRow)
    if (isDefault) {
        $('#ddlState').empty().append(groupStateOptions);
        $('#consistencyDetails').empty().append(groupSelectOptions);
    }
   
    isDefault = false;
    /*if (value?.length) $('#consistencyDetails').val(value[0]?.consistencyGroupName).trigger('change')  */   
}

$('#consistencyDetails').on('change', function () {
    const consistencyValue = $(this).find('option:selected').text(); 
    if (requestData.consistencyGroupName !== consistencyValue) {
        requestData.consistencyGroupName = consistencyValue;  
        getRPCGDetails();
    }
})

//$('#ddlsnapType, #ddlBookmark, #startTime, #endTime').on('change', function () {
//    const selectedSnapshotType = $('#ddlsnapType').val();
//    const selectedBookmark = $('#ddlBookmark').val();
//    const startTimeInput = $('#startTime').val();
//    const endTimeInput = $('#endTime').val();

//    const startTime = startTimeInput ? new Date(startTimeInput).setHours(0, 0, 0, 0) : null;
//    const endTime = endTimeInput ? new Date(endTimeInput).setHours(23, 59, 59, 999) : null;

//    let filteredData = snapDetails;

//    if (selectedSnapshotType !== 'All') {
//        filteredData = filteredData?.filter(data => data?.Snapshot_Type === selectedSnapshotType);
//    }

//    if (this.id === 'ddlsnapType') {
//        $('#ddlBookmark').empty().append('<option value="All">All</option>');

//        const filteredBookmarks = new Set();
//        filteredData?.forEach(data => {
//            if (data?.Bookmark) {
//                filteredBookmarks.add(data?.Bookmark);
//            }
//        });

//        filteredBookmarks.forEach(bookmark => {
//            $('#ddlBookmark').append(`<option value="${bookmark}">${bookmark}</option>`);
//        });

//        $('#ddlBookmark').val('All');
//    }

//    if (selectedBookmark !== 'All') {
//        filteredData = filteredData?.filter(data => data?.Bookmark === selectedBookmark);
//    }
//    if (startTime || endTime) {
//        filteredData = filteredData?.filter(data => {
//            const dataTime = new Date(data?.Point_In_Time).getTime();
//            return (
//                (!startTime || dataTime >= startTime) &&
//                (!endTime || dataTime <= endTime)
//            );
//        });
//    }    
//    updateTable(filteredData);
//});
//function updateTable(filteredData) {
//    const tableBody = $('#snapshotTable');
//    tableBody.empty(); 

//    filteredData?.forEach(data => {
//        const row = `
//            <tr>
//                <td>${data?.Snapshot_Id || '-'}</td>
//                <td>${data.Snapshot_Type || '-'}</td>
//                <td>${data?.Point_In_Time || '-'}</td>
//                <td>${data?.Size || '-'}</td>               
//                <td>${data?.Bookmark || '-'}</td>
//            </tr>`;
//        tableBody.append(row);
//    });
//}
//Search Filter
//$('#search-input').on('keydown input', function (e) {
//    const filter = $(this).val().toLowerCase();
//    if (e.key === '=' || e.key === 'Enter') {
//        e.preventDefault()
//        return false;
//    } else {
//        const filteredData = snapDetails?.filter(data => {
//            return (
//                (data?.Snapshot_Type && data?.Snapshot_Type?.toLowerCase()?.includes(filter)) ||
//                (data?.Bookmark && data?.Bookmark?.toLowerCase()?.includes(filter)) ||
//                (data?.Size && data?.Size?.toLowerCase()?.includes(filter))
//            )
//        })
//        updateTable(filteredData);
//    }
    
//})
$("#downloadReport").on('click', async function () {
    $("#downloadReport").prop('disabled', true);
    const url = `/Monitor/RPForVM/DownloadReport`;
    const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reportData: ReportData })
    });

    if (response.ok) {
        const blob = await response.blob();
        var alertClass, iconClass, message;
        if (blob.size > 0 && blob.type==="application/pdf") {
            const DateTime = new Date().toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3, hour12: false }).replace(/[^0-9]/g, '');
            downloadRecoveryPointReport(blob, "RP4VM_CG_Monitoring_" + DateTime + ".pdf", "application/pdf");
            alertClass = "success-toast";
            iconClass = "cp-check toast_icon";
            message = "RP4VM CG Monitoring report downloaded successfully";
            $("#downloadReport").prop('disabled', false);
            console.log("RPforVM report downloaded");
        }
        else {
            alertClass = "warning-toast";
            iconClass = "cp-exclamation toast_icon";
            message = "No Data Found";
            $("#downloadReport").prop('disabled', false);
            console.log("No data found");
        }
    }
    else {
        alertClass = "warning-toast";
        iconClass = "cp-exclamation toast_icon";
        message = "RP4VM CG Monitoring Download Error";
        $("#downloadReport").prop('disabled', false);
    }
    $('#alertClass').removeClass().addClass(alertClass);
    $('#icon').removeClass().addClass(iconClass);
    $('#notificationAlertmessage').text(message);
    $('#mytoastrdata').toast({ delay: 3000 }).toast('show');
});
function downloadRecoveryPointReport(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        notificationAlert("Error downloading file: " + error.message);
    }
}
function propertiesData(value) {
    
    if (value === undefined || value === null || value === '') {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
    //else {
    //    let data = JSON?.parse(value?.properties);  
        
    //    //ConsistencyData(data?.ConsistencyGroupDetails)
        
    //    let customSite = data?.VMRecoverPointMonitoring?.length > 1;
    //    if (customSite) {
    //        $("#Sitediv").show();
    //    } else {
    //        $("#Sitediv").hide();
    //    }

    //    $(".siteContainer").empty();
    //    data?.VMRecoverPointMonitoring?.forEach((a, index) => {
    //        let selectTab = `
    //        <li class="nav-item siteListChange" id='siteName${index}'>
    //            <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a.Type}</span></a>
    //        </li>
    //        <li class="nav-item vr"></li>`;
    //        $(".siteContainer").append(selectTab);
    //    });

    //    if (data?.VMRecoverPointMonitoring?.length > 0) {
            
    //        $("#siteName0 .nav-link").addClass("active");
    //        displaySiteData(data?.VMRecoverPointMonitoring[0]);
    //    }

    //    let defaultSite = data?.VMRecoverPointMonitoring?.find(d => d?.Type === 'DR') || data?.VMRecoverPointMonitoring[0];
    //    if (defaultSite) {
            
    //        displaySiteData(defaultSite);
    //    }
    //    let activityData = data?.RecoveryActivitiesMonitoring
       
    //    $(document).on('click', '.siteListChange', function () {

    //        $(".siteListChange .nav-link").removeClass("active");
    //        $(this).find(".nav-link").addClass("active");
    //        let siteId = $(this)[0]?.id
    //        let getSiteName = $(`#${siteId} .siteName`).text()

    //        let MonitoringModel = data?.VMRecoverPointMonitoring?.find(d => d?.Type === getSiteName);
    //        if (MonitoringModel) {
                
    //            displaySiteData(MonitoringModel);
    //        }
    //    });
  
    //    function displaySiteData(siteData) {
            
    //        let obj = {};
    //        // $('.dynamicSite-header').text(siteData.Type).attr('title', siteData.Type);

    //        for (let key in siteData?.ReplicationMonitoring) {
    //            obj[`DR_` + key] = siteData?.ReplicationMonitoring[key];
    //        }

    //        let MonitoringModelrecoveryPointVM = [
    //            "DR_PluginServerIPAddress", "DR_VCenterName",
    //            "DR_VRPAClusterName", "DR_Version", "DR_ProtectedSize",
    //            "DR_ProtectedVMNames",
    //            "DR_TransferStatus", "DR_State", "DR_LatestSnapshot"                
    //        ];

    //        if (Object.keys(obj)?.length > 0) {
    //            bindProperties(obj, MonitoringModelrecoveryPointVM, value);
    //        }
    //    }

    //    let dbDetail = data?.VMRecoverPointMonitoringPR?.ReplicationMonitoringPR
        
    //    const dbDetailsProp = [
    //       "PluginServerIPAddress", "VCenterName",
    //        "VRPAClusterName", "Version", "ProtectedSize", "ProtectedVMNames",
    //        /*"TransferStatus", "State", "LatestSnapshot"*/
    //    ];

    //    bindProperties(dbDetail, dbDetailsProp, value);
    //    //Database Details

    //    //Datalag
    //    const datalag = checkAndReplace(data?.Datalag);        
    //    let dataLagValue = (datalag !== undefined && datalag !== "" && datalag !== null && datalag !== "0") ? `${(datalag)}` : 'NA';

    //    var result = "";

    //    if (dataLagValue?.includes(".")) {
    //        var value = dataLagValue?.split(".");
    //        var hours = value[0] * 24;
    //        var minutes = value[1]?.split(':')?.slice(0, 2)?.join(':');
    //        var min = minutes?.split(':');
    //        var firstValue = parseInt(min[0]) + parseInt(hours);
    //        result = firstValue + ":" + min[1];
    //        const minute = (parseInt(result[0]) * 60) + parseInt(result[1]);
    //        minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
    //    }
    //    else if (dataLagValue?.includes("+")) {
    //        const value = dataLagValue.split(" ");
    //        result = value[1]?.split(':')?.slice(0, 2)?.join(':');
    //        const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
    //        const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
    //        minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')

    //    }
    //    else {
    //        result = dataLagValue?.split(':')?.slice(0, 2)?.join(':');
    //        const progressresult = result?.split(':')?.slice(1, 2)?.join(':');
    //        const minute = (parseInt(progressresult[0]) * 60) + parseInt(progressresult[1]);
    //        minute > value?.configuredRPO ? $('#PR_Datalag').text(result).attr('title', result).css('color', 'red') : $('#PR_Datalag').text(result).attr('title', result).css('color', '')
    //    }


    //}
}

//function bindProperties(data, properties, value) {
    
//    properties?.forEach(property => {
//        const values = data[property];
//        const displayedValue = value !== undefined ? checkAndReplace(values) : 'NA';
//        // Displayed value with icon
//        const iconHtml = getIconClass(displayedValue, property, data, value);
//        const mergeValue = `${iconHtml}${displayedValue}`;
//        $(`#${property}`).html(mergeValue).attr('title', displayedValue);
//    });

//}

//function getIconClass(displayedValue, property, data, value) {

//    let prservericon = data?.ConsistencyGroupName ? "text-primary cp-server me-1" : "text-danger cp-disable"
//    let drservericon = data?.DR_ConsistencyGroupName ? "text-primary cp-server me-1" : "text-danger cp-disable"
//    let prServerVersion = data?.PluginServerIPAddress ? "text-success cp-fal-server me-1" : "text-danger cp-disable"
//    let drServerVersion = data?.DR_PluginServerIPAddress ? "text-success cp-fal-server me-1" : "text-danger cp-disable"
//    let prserverfdqn = data?.VCenterName ? "cp-control-file-type text-primary" : "text-danger cp-disable"
//    let drserverfdqn = data?.DR_VCenterName ? "cp-control-file-type text-primary" : "text-danger cp-disable"
//    let prresource = data?.VRPAClusterName ? "cp-manage-server text-primary" : "text-danger cp-disable"
//    let drresource = data?.DR_VRPAClusterName ? "cp-manage-server text-primary" : "text-danger cp-disable"
//    let prlocation = data?.RecoverPoint_VMs_Version ? "cp-database text-primary" : "text-danger cp-disable"
//    let drlocation = data?.DR_RecoverPoint_VMs_Version ? "cp-database text-primary" : "text-danger cp-disable"
//    let prrole = data?.ProtectedSize ? "cp-replication-type text-primary" : "text-danger cp-disable"
//    let drrole = data?.DR_ProtectedSize ? "cp-replication-type text-primary" : "text-danger cp-disable"
//    let prreplication = data?.ProtectedVMNames ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let drreplication = data?.DR_ProtectedVMNames ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let prtransfer = data?.TransferStatus ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let drtransfer = data?.DR_TransferStatus ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let prstate = data?.State ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let drstate = data?.DR_State ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let prsnapshot = data?.LatestSnapshot ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let drsnapshot = data?.DR_LatestSnapshot ? "cp-replication-type text-primary mt-2" : "text-danger cp-disable"
//    let prversion = data?.Version ? "cp-version text-primary me-1 fs-6" : "text-danger cp-disable"
//    let drversion = data?.DR_Version ? "cp-version text-primary me-1 fs-6" : "text-danger cp-disable"
//    let prdatalag = data?.Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"
//    //let drdatalag = data?.DR_Datalag ? "cp-time text-primary mt-2" : "text-danger cp-disable"
    
//    const iconMapping = {
//        'ConsistencyGroupName': prservericon,
//        'DR_ConsistencyGroupName': drservericon,
//        'PluginServerIPAddress': prServerVersion,
//        'DR_PluginServerIPAddress': drServerVersion,
//        'VCenterName': prserverfdqn,
//        'DR_VCenterName': drserverfdqn,
//        'VRPAClusterName': prresource,
//        'DR_VRPAClusterName': drresource,
//        'RecoverPoint_VMs_Version': prlocation,
//        'DR_RecoverPoint_VMs_Version': drlocation,

//        'ProtectedSize': prrole,
//        'DR_ProtectedSize': drrole,
//        'ProtectedVMNames': prreplication,
//        'DR_ProtectedVMNames': drreplication,
//        'Version': prversion,
//        'DR_Version': drversion,

//        'TransferStatus': prtransfer,
//        'DR_TransferStatus': drtransfer,
//        'State': prstate,
//        'DR_State': drstate,
//        'LatestSnapshot': prsnapshot,
//        'DR_LatestSnapshot': drsnapshot,

//        'Datalag': prdatalag,
//        //'DR_Datalag': drdatalag
//    };

//    let iconClass = iconMapping[property] || '';

//    switch (displayedValue?.toLowerCase()) {

//        case 'not allowed':
//        case 'na':
//            iconClass = 'text-danger cp-disable';
//            break;
//        case 'no':
//            iconClass = 'text-danger cp-disagree';
//            break;
//        case 'streaming':
//            iconClass = 'text-success cp-refresh';
//            break;
//        case 'running':
//        case 'run':
//            iconClass = 'text-success cp-reload cp-animate';
//            break;
//        case 'stopped':
//        case 'stop':
//            iconClass = 'text-danger cp-Stopped';
//            break;
//        case 'f':
//        case 'false':
//        case 'defer':
//        case 'deferred':
//            iconClass = 'text-danger cp-error';
//            break;
//        case 't':
//        case 'yes':
//        case 'deferred':
//            iconClass = 'text-success cp-agree';
//            break;
//        case 'valid':
//            iconClass = 'text-success cp-success';
//            break;
//        case 'pending':
//            iconClass = 'text-warning cp-pending';
//            break;
//        case 'pause':
//        case 'paused':
//            iconClass = 'text-warning cp-circle-pause';
//            break;
//        case 'manual':
//            iconClass = 'text-warning cp-settings';
//            break;
//        case 'synchronous_commit':
//        case 'synchronized':
//        case 'synchronizing':
//        case 'sync':
//            iconClass = 'text-success cp-refresh';
//            break;
//        case 'asynchronous_commit':
//        case 'asynchronizing':
//        case 'asynchronized':
//        case 'async':
//            iconClass = 'text-danger cp-refresh';
//            break;
//        case 'online':
//            iconClass = 'text-success cp-online';
//            break;
//        case 'offline':
//            iconClass = 'text-danger cp-offline';
//            break;
//        case 'disabled':
//        case 'disable':
//            iconClass = 'text-danger cp-disables';
//            break;
//        case 'enabled':
//        case 'enable':
//            iconClass = 'text-success cp-enables';
//            break;
//        case 'connected':
//        case 'connect':
//            iconClass = 'text-success cp-connected';
//            break;
//        case 'disconnected':
//        case 'disconnect':
//            iconClass = 'text-danger cp-disconnecteds';
//            break;
//        case 'standby':
//        case 'to standby':
//            iconClass = 'text-warning cp-control-file-type';
//            break;
//        case 'required':
//        case 'require':
//            iconClass = 'text-warning cp-warning';
//            break
//        case 'healthy':
//            iconClass = 'text-success cp-health-success';
//            break;
//        case 'not_healthy':
//        case 'nothealthy':
//        case 'unhealthy':
//            iconClass = 'text-danger cp-health-error';
//            break;
//        case 'error':
//            iconClass = 'text-danger cp-fail-back';
//            break;
//        case 'on':
//            iconClass = 'text-success cp-end';
//            break;
//        case 'off':
//            iconClass = 'text-danger cp-end';
//            break;
//        case 'current':
//        case 'read write':
//            iconClass = 'text-success cp-file-edits';
//            break
//        case 'primary':
//            iconClass = 'text-primary cp-list-prsite';
//            break
//        case 'secondary':
//            iconClass = 'text-info cp-dr';
//            break
//        case 'active':
//            iconClass = 'cp-active-inactive text-success'
//            break
//        case 'physical standby':
//            iconClass = 'text-info cp-physical-drsite';
//            break
//        default:

//            break;

//    }
//    return iconClass ? `<i class="${iconClass} me-1 fs-6"></i>` : '';
//}
