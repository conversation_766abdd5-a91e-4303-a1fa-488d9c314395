﻿using ContinuityPatrol.Application.Features.AccessManager.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AccessManager.Queries;

public class GetAccessManagerListQueryHandlerTests : IClassFixture<AccessManagerFixture>
{
    private readonly AccessManagerFixture _accessManagerFixture;
    private Mock<IAccessManagerRepository> _mockAccessManagerRepository;
    private readonly GetAccessManagerListQueryHandler _handler;

    public GetAccessManagerListQueryHandlerTests(AccessManagerFixture accessManagerFixture)
    {
        _accessManagerFixture = accessManagerFixture;

        _mockAccessManagerRepository = AccessManagerRepositoryMocks.GetAccessManagerRepository(_accessManagerFixture.AccessManagers);

        _handler = new GetAccessManagerListQueryHandler(_accessManagerFixture.Mapper, _mockAccessManagerRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_AccessManagerCount()
    {
        var result = await _handler.Handle(new GetAccessManagerListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AccessManagerListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_ValidAccessManagerList()
    {
        var result = await _handler.Handle(new GetAccessManagerListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AccessManagerListVm>>();

        result.Count.ShouldBe(_accessManagerFixture.AccessManagers.Count);

        result[0].Id.ShouldBe(_accessManagerFixture.AccessManagers[0].ReferenceId);

        result[0].RoleName.ShouldBe(_accessManagerFixture.AccessManagers[0].RoleName);

        result[0].Properties.ShouldBe(_accessManagerFixture.AccessManagers[0].Properties);

    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockAccessManagerRepository = AccessManagerRepositoryMocks.GetAccessManagerEmptyRepository();

        var handler = new GetAccessManagerListQueryHandler(_accessManagerFixture.Mapper, _mockAccessManagerRepository.Object);

        var result = await handler.Handle(new GetAccessManagerListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetAccessManagerListQuery(), CancellationToken.None);

        _mockAccessManagerRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}