﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraObjectViewRepository:BaseRepository<InfraObjectView>, IInfraObjectViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraObjectViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<InfraObjectView>> ListAllAsync()
    {
        var infraObject = FilterRequiredField(
            base.QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            :await Task.Run(()=> GetAssignedInfraObjects(infraObject));
    }

    public override async Task<InfraObjectView> GetByReferenceIdAsync(string id)
    {
        var infraObjects = base.GetByReferenceId(id,
            infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           infraObject.ReferenceId.Equals(id));

        var first = await infraObjects.FirstOrDefaultAsync();

        if (_loggedInUserService.IsAllInfra)
            return first;
        return GetInfraObjectByReferenceId(first);
    }

    public async Task<InfraObjectView> GetInfraObjectByName(string name)
    {
        return await FilterRequiredField(base.FilterBy(x => x.Name.ToLower().Equals(name.ToLower()))).FirstOrDefaultAsync();
    }

   
    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectByBusinessFunctionId(string businessFunctionId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessFunctionId.Equals(businessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessFunctionId.Equals(businessFunctionId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(()=>GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectListByBusinessFunctionIds(List<string> bFunctionId)
    {
        var infraObjects = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => bFunctionId.Contains(x.BusinessFunctionId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && bFunctionId.Contains(x.BusinessFunctionId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObjects.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObjects).ToList());
    }
    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(businessServiceId)));


        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetByBusinessServiceIds(List<string> businessServiceIds)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => businessServiceIds.Contains(x.BusinessServiceId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && businessServiceIds.Contains(x.BusinessServiceId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }


    public override IQueryable<InfraObjectView> GetPaginatedQuery()
    {
        var infraObject =
            base.QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId));

        return _loggedInUserService.IsAllInfra
            ? infraObject.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedInfraObjects(infraObject).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<List<InfraObjectView>> GetInfraObjectByServerId(string serverId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ServerProperties.Contains(serverId))
            : base.FilterBy(x => x.ServerProperties.Contains(serverId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetInfraObjectByReplicationId(string replicationId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationProperties.Contains(replicationId))
            : base.FilterBy(x => x.ReplicationProperties.Contains(replicationId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetInfraObjectByDatabaseId(string databaseId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.DatabaseProperties.Contains(databaseId))
            : base.FilterBy(x => x.DatabaseProperties.Contains(databaseId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetInfraObjectByNodeId(string nodeId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.NodeProperties.Contains(nodeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.NodeProperties.Contains(nodeId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetInfraObjectListByReplicationCategoryType(string replicationCategoryTypeId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationCategoryTypeId.Equals(replicationCategoryTypeId)));


        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<List<InfraObjectView>> GetInfraStateByReferenceIds(List<string> id)
    {
        return await base.FilterBy(x => id.Contains(x.ReferenceId))
            .Select(x => new InfraObjectView
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                State = x.State
            }).ToListAsync();
    }
    public async Task<InfraObjectView> GetInfraObjectViewByInfraObjectId(string infraObjectId)
    {
        var infra = (_loggedInUserService.IsParent
           ? base.FilterBy(x => x.ReferenceId.Equals(infraObjectId))
           : base.FilterBy(x => x.ReferenceId.Equals(infraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)))
           .Select(x => new InfraObjectView
           {
               ReferenceId = x.ReferenceId,
               Name = x.Name,
               BusinessServiceId = x.BusinessServiceId,
               BusinessServiceName = x.BusinessServiceName,
               BusinessFunctionId = x.BusinessFunctionId,
               BusinessFunctionName = x.BusinessFunctionName,
               State = x.State,
               IsDrift = x.IsDrift,
               ReplicationTypeId = x.ReplicationTypeId,
               ReplicationTypeName = x.ReplicationTypeName,
               ReplicationCategoryTypeId = x.ReplicationCategoryTypeId,
               ReplicationCategoryType = x.ReplicationCategoryType,
               SubTypeId = x.SubTypeId,
               NodeProperties = x.NodeProperties,
               ReplicationStatus = x.ReplicationStatus,
               DROperationStatus = x.DROperationStatus,
               Reason = x.Reason,
               TypeName = x.TypeName,
               SubType = x.SubType,
               ServerProperties = x.ServerProperties,
               DatabaseProperties = x.DatabaseProperties,
               ReplicationProperties = x.ReplicationProperties,
               SiteProperties = x.SiteProperties
           });

        var first = await infra.FirstOrDefaultAsync();

        if (_loggedInUserService.IsAllInfra)
            return first;
        return GetInfraObjectByReferenceId(first);
    }
    public async Task<List<InfraObjectView>> GetInfraObjectListByReplicationTypeId(string replicationTypeId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.ReplicationTypeId.Equals(replicationTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.ReplicationTypeId.Equals(replicationTypeId)));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectByStateType(string stateType)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.Trim().ToLower().Equals(stateType.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.State.Trim().ToLower().Equals(stateType.Trim().ToLower())));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectByTypeNameAndState(string state, string type)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.State.Trim().ToLower().Equals(state.Trim().ToLower()) && x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower()))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.TypeName.Trim().ToLower().Equals(type.Trim().ToLower()) && x.State.Trim().ToLower().Equals(state.Trim().ToLower())));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectByBusinessServiceName(string businessServiceName)
    {
        var infraObjects = await FilterRequiredField(_dbContext.InfraObjectViews.Active()
            .Where(infra => infra.BusinessServiceName.Equals(businessServiceName))).ToListAsync();

        return _loggedInUserService.IsParent
            ? _loggedInUserService.IsAllInfra
                ? infraObjects.ToList()
                : GetAssignedInfraObjects(infraObjects.AsQueryable())
                    .Where(infra => infra.BusinessServiceName.Equals(businessServiceName)).ToList()
            : _loggedInUserService.IsAllInfra
                ? infraObjects.Where(infra => infra.CompanyId == _loggedInUserService.CompanyId).ToList()
                : GetAssignedInfraObjects(infraObjects.AsQueryable()).Where(infra =>
                    infra.BusinessServiceName.Equals(businessServiceName) &&
                    infra.CompanyId == _loggedInUserService.CompanyId).ToList();
    }
    public async Task<PaginatedResult<InfraObjectView>> GetPaginatedByBusinessService(string businessServiceId, int pageNumber, int pageSize, Specification<InfraObjectView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)).DescOrderById()
                : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId))
                    .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
            : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                    .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public async Task<PaginatedResult<InfraObjectView>> GetPaginatedByBusinessFunction(string businessFunctionId, int pageNumber, int pageSize, Specification<InfraObjectView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                ? Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById()
                : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId))
                .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
            : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
    }
    public async Task<PaginatedResult<InfraObjectView>> GetPaginatedByBusinessServiceAndFunction(string businessServiceId, string businessFunctionId, int pageNumber, int pageSize, Specification<InfraObjectView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                 ? Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById()
             : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId)).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
            : GetPaginatedInfraObjects(Entities.Specify(productFilterSpec).Where(x => x.BusinessServiceId.Equals(businessServiceId)
             && x.BusinessFunctionId.Equals(businessFunctionId) && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);

    }

    public override async Task<PaginatedResult<InfraObjectView>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<InfraObjectView> specification, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await FilterRequiredField(_loggedInUserService.IsAllInfra
                 ? Entities.Specify(specification).DescOrderById()
                 : GetPaginatedInfraObjects(Entities.Specify(specification)).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await FilterRequiredField(_loggedInUserService.IsAllInfra
            ? Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById()
            : GetPaginatedInfraObjects(Entities.Specify(specification).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))).DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn, sortOrder);


    }
    public async Task<IReadOnlyList<InfraObjectView>> GetInfraObjectViewBySiteTypeId(string siteTypeId)
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.SiteProperties.Contains(siteTypeId))
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.SiteProperties.Contains(siteTypeId)));


        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());

    }

    public async Task<List<InfraObjectView>> GetDriftEnbledInfraObject()
    {
        var infraObject = FilterRequiredField(_loggedInUserService.IsParent
            ? base.FilterBy(x => x.IsDrift)
            : base.FilterBy(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.IsDrift));

        return _loggedInUserService.IsAllInfra
            ? await infraObject.ToListAsync()
            : await Task.Run(() => GetAssignedInfraObjects(infraObject).ToList());
    }

    private IQueryable<InfraObjectView> FilterRequiredField(IQueryable<InfraObjectView> infraObjects)
    {
        return infraObjects.Select(x => new InfraObjectView
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name = x.Name,
            Description = x.Description,
            CompanyId = x.CompanyId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            BusinessFunctionId = x.BusinessFunctionId,
            BusinessFunctionName = x.BusinessFunctionName,
            Type = x.Type,
            SubType = x.SubType,
            DRReady = x.DRReady,
            NearDR = x.NearDR,
            RecoveryType = x.RecoveryType,
            ServerProperties = x.ServerProperties,
            DatabaseProperties = x.DatabaseProperties,
            ReplicationProperties = x.ReplicationProperties,
            Priority = x.Priority,
            State = x.State,
            ReplicationStatus = x.ReplicationStatus,
            DROperationStatus = x.DROperationStatus,
            IsPair = x.IsPair,
            IsDrift = x.IsDrift,
            PairInfraObjectId = x.PairInfraObjectId,
            PairInfraObjectName = x.PairInfraObjectName,
            IsAssociate = x.IsAssociate,
            IsAssociateInfraObjectId = x.IsAssociateInfraObjectId,
            IsAssociateInfraObjectName = x.IsAssociateInfraObjectName,
            ReplicationTypeId = x.ReplicationTypeId,
            ReplicationTypeName = x.ReplicationTypeName,
            ReplicationCategoryTypeId = x.ReplicationCategoryTypeId,
            ReplicationCategoryType = x.ReplicationCategoryType,
            TypeName = x.TypeName,
            SubTypeId = x.SubTypeId,
            NodeProperties = x.NodeProperties,
            Reason = x.Reason,
            SiteProperties = x.SiteProperties,
            BsSiteProperties = x.BsSiteProperties
        });
    }

    
}

