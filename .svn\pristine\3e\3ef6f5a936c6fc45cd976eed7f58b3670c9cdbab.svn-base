﻿using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetList;
using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class Db2HaDrMonitorLogController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Manage.Create)]
    public async Task<ActionResult<CreateDB2HADRMonitorLogResponse>> CreateDb2HaDrMonitorLog([FromBody] CreateDB2HADRMonitorLogCommand createDb2HaDrMonitorLogCommand)
    {
        Logger.LogDebug($"Create Db2HaDrMonitorLog  '{createDb2HaDrMonitorLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDb2HaDrMonitorLog), await Mediator.Send(createDb2HaDrMonitorLogCommand));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<DB2HADRMonitorLogListVm>>> GetAllDb2HaDrMonitorLog()
    {
        Logger.LogDebug("Get All  Db2HaDrMonitorLog");

        return Ok(await Mediator.Send(new GetDB2HADRMonitorLogListQuery()));
    }
    [HttpGet("{id}")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<DB2HADRMonitorLogDetailVm>> GetDb2HaDrMonitorLogById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Db2HaDrMonitorLog Detail By Id");

        Logger.LogDebug($"Get  Db2HaDrMonitorLog Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDB2HADRMonitorLogDetailQuery { Id = id }));
    }
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<List<DB2HADRMonitorLogPaginatedListVm>>> GetPaginatedDb2HaDrMonitorLog([FromQuery] GetDB2HADRMonitorLogPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Db2HaDrMonitorLog Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("type")]
    [Authorize(Policy = Permissions.Manage.View)]
    public async Task<ActionResult<DB2HADRMonitorLogDetailByTypeVm>> GetDb2HaDrMonitorLogByType(string type)
    {
        Guard.Against.NullOrEmpty(type, "Db2HaDrMonitorLog Detail By Type");

        Logger.LogDebug($"Get  Db2HaDrMonitorLog Detail by Id '{type}'");

        return Ok(await Mediator.Send(new GetDB2HADRMonitorLogDetailByTypeQuery { Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllDB2HADRMonitorLogCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllMSSQLMonitorLogsNameCacheKey };

        ClearCache(cacheKeys);
    }
}