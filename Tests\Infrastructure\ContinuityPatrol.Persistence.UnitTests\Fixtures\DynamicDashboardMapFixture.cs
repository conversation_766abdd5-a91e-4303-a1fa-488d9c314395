using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DynamicDashboardMapFixture : IDisposable
{
    public List<DynamicDashboardMap> DynamicDashboardMapPaginationList { get; set; }
    public List<DynamicDashboardMap> DynamicDashboardMapList { get; set; }
    public DynamicDashboardMap DynamicDashboardMapDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string DashBoardSubName = "TestDashboardSubName";
    public const string DashBoardSubId = "DASHBOARD_123";


    public ApplicationDbContext DbContext { get; private set; }

    public DynamicDashboardMapFixture()
    {
        var fixture = new Fixture();

        DynamicDashboardMapList = fixture.Create<List<DynamicDashboardMap>>();

        DynamicDashboardMapPaginationList = fixture.CreateMany<DynamicDashboardMap>(20).ToList();

        DynamicDashboardMapPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicDashboardMapPaginationList.ForEach(x => x.IsActive = true);
        DynamicDashboardMapPaginationList.ForEach(x => x.DashBoardSubName = DashBoardSubName);
        DynamicDashboardMapPaginationList.ForEach(x => x.DashBoardSubId = DashBoardSubId);
        DynamicDashboardMapPaginationList.ForEach(x => x.DashBoardSubName = DashBoardSubName);


        DynamicDashboardMapDto = fixture.Create<DynamicDashboardMap>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
