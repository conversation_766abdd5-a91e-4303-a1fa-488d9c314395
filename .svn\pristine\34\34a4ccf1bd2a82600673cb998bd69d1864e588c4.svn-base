﻿using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.UserLogin.Events.Logout;
using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.ChangePasswordModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Domain.ViewResults;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Infrastructure;
using ContinuityPatrol.Shared.Services.Provider;

namespace ContinuityPatrol.Web.Controllers;

public class AccountController : BaseController
{
    private readonly ILogger<AccountController> _logger;
    private readonly IMapper _mapper;
    private readonly ILoginService _loginService;
    private readonly IDomainService _domainService;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    private readonly IDistributedCache _cache;

    public IEnumerable<SelectListItem> CompanyList => WebHelper.CurrentSession.Get<IEnumerable<SelectListItem>>("CompanyProfiles")
                                                      ?? new List<SelectListItem>();

    public IEnumerable<SelectListItem> Domains => WebHelper.CurrentSession.Get<IEnumerable<SelectListItem>>("Domains")
    ?? new List<SelectListItem>();

    public static string AdPassword;

    public AccountController(IDataProvider dataProvider,
        ILogger<AccountController> logger,
        IMapper mapper,
        ILoginService loginService,
        IDomainService domainService,
        IPublisher publisher,
        IDistributedCache cache)
    {
        _logger = logger;
        _mapper = mapper;
        _loginService = loginService;
        _domainService = domainService;
        _dataProvider = dataProvider;
        _publisher = publisher;
        _cache = cache;
    }

    [AllowAnonymous]
    public async Task<IActionResult> Login()
    {
        _logger.LogDebug("Entering Login method.");

        if (await VerifyOldLoginSessionAsync())
        {
            return RedirectToAction("PreLogin");
        }

        if (CompanyList.Any())
        {
            _logger.LogInformation("Company list is available, returning view.");

            return ReturnView();
        }
        var viewResult = await PrepareView();

        _logger.LogDebug("Exiting Login method.");

        return RouteToPrepareView(viewResult);
    }

    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> LoginAsync(LoginViewModel model, string returnUrl = null, bool isMultipleLogin = false)
    {
        _logger.LogDebug("LoginAsync method with LoginViewModel");

        ValidateLoginModel(model);

        if (!ModelState.IsValid)
        {
            _logger.LogWarning("LoginAsync : Model state is invalid.");

            return RedirectToAction("Login");
        }

        WebHelper.CurrentSession.Set("LoginViewModel" + model.LoginName, model);

        returnUrl ??= Url.Content("~/").TrimEnd('/');

        if (!string.IsNullOrWhiteSpace(returnUrl) && !Url.IsLocalUrl(returnUrl))
        {
            _logger.LogWarning("Invalid return URL: {ReturnUrl}", returnUrl);

            TempData.NotifyWarning("Invalid return URL");

            return RedirectToAction("Login");
        }

        var viewResult = await _loginService.Authenticate(model);

        AdPassword = string.Empty;

        _logger.LogDebug("Exiting LoginAsync method.");

        return RouteToPostView(viewResult, returnUrl, isMultipleLogin);
    }

    private static void ValidateLoginModel(LoginViewModel model)
    {
        Guard.Against.NullOrWhiteSpace(model.LoginName, "Login Name");
        Guard.Against.NullOrWhiteSpace(model.Password, "Password");
        Guard.Against.InvalidGuidOrEmpty(model.CompanyId, "CompanyId");

        if (model.AuthenticationType == AuthenticationType.AD.ToString())
        {
            Guard.Against.NullOrWhiteSpace(model.Domains, "Domain");
        }
    }

    [AllowAnonymous]
    public async Task<IActionResult> PreLogin()
    {
        _logger.LogDebug("Initialize Pre-Login");
        await CleanSignOut();
        return RedirectToAction("Login");
    }
    public IActionResult ChangePassword()
    {
        _logger.LogDebug("Entering ChangePassword method.");
        return View();
    }

    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangePasswordAsync(ChangePasswordViewModel model)
    {
        _logger.LogDebug("Entering ChangePasswordAsync method with ChangePasswordViewModel");

        try
        {
            var changePasswordCommand = _mapper.Map<UpdatePasswordCommand>(model);

            var response = await _dataProvider.User.UpdateUserPassword(changePasswordCommand);

            TempData.NotifySuccess(response.Message);

            await Task.Delay(1000);

            await ClearSession(model.LoginName);

            _logger.LogInformation("Password changed successfully.");

            return RedirectToAction("Login");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while changing password.");

            TempData.NotifyWarning(ex.Message);

            return RedirectToAction("ChangePassword");
        }
    }

    [AllowAnonymous]
    public ActionResult ForgotPassword()
    {
        _logger.LogDebug("Entering ForgotPassword method.");
        return View();
    }

    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ForgotPasswordAsync(ForgotPasswordViewModel model)
    {
        _logger.LogDebug("Entering ForgotPasswordAsync method with ForgotPasswordViewModel");

        try
        {
            if (ModelState.IsValid)
            {
                var forgotPasswordCommand = _mapper.Map<ForgotPasswordCommand>(model);
                var response = await _dataProvider.User.ForgotPassword(forgotPasswordCommand);

                TempData.NotifySuccess(response.Message);
                await Task.Delay(2000);

                _logger.LogInformation("ForgotPassword executed successfully.");

                return RedirectToAction(response.Success ? "Login" : "ForgotPassword");
            }
            _logger.LogWarning("Model state is invalid.");
            return RedirectToAction("ForgotPassword");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while processing forgot password.");
            TempData.NotifyWarning(ex.Message);
            return RedirectToAction("ForgotPassword");
        }
    }


    [AllowAnonymous]
    [HttpPost]
    //[ValidateAntiForgeryToken]
    public async Task<IActionResult> ClearSession(string loginName)
    {
        _logger.LogDebug($"Entering ClearSession method with login name: {loginName}");

        var loginInfo = await _dataProvider.User.GetUserByLoginName(loginName);

        if (!loginInfo.Id.IsValidGuid())
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            await CleanSignOut();
            return Json(new { Success = false });
        }

        await ClearDatabaseSessions(loginInfo.Id);

        if (loginName.IsNotNullOrEmpty())
        {
            var cacheKey = $"Web:{loginName.ToLower()}";

            await _cache.RemoveAsync(cacheKey);
        }

        _logger.LogInformation("Session cleared successfully.");

        var currentLoginModel = WebHelper.CurrentSession.Get<LoginViewModel>("LoginViewModel" + loginName);

        if (currentLoginModel == null) return Json(new { Success = true });

        ViewBag.LoginViewModel = currentLoginModel;

        return Json(new { Success = true, loginViewModel = currentLoginModel });
    }

    private async Task ClearDatabaseSessions(string loginId)
    {
        _logger.LogDebug($"Clearing database sessions for loginId: {loginId}");

        await _dataProvider.UserLogin.ClearDatabaseSession(loginId);

        _logger.LogDebug($"Database sessions cleared for loginId: {loginId}");
    }

    [AllowAnonymous]
    [HttpGet]
    public JsonResult HashPassword(string loginName, string password, bool adChecked)
    {
        _logger.LogDebug($"HashPassword method called with loginName: {loginName}");

        if (loginName.IsNullOrWhiteSpace() || password.IsNullOrWhiteSpace())
        {
            _logger.LogWarning("Login name or password is null or whitespace.");
            return Json(new { encrypt = "" });
        }
        loginName = loginName.ToLower();

        var encrypt = SecurityHelper.Encrypt(adChecked ? $"{password}" : $"{loginName}{password}");

        _logger.LogDebug("Password hashed successfully.");

        return Json(new { encrypt });
    }

    [AllowAnonymous]
    [HttpGet]
    public async Task<JsonResult> GetDomains()
    {
        _logger.LogDebug("Entering GetDomains method.");

        try
        {
            var domainNames = await _domainService.GetDomains();

            if (domainNames.Count > 0)
            {
                WebHelper.CurrentSession.Set("Domains", domainNames);
                _logger.LogInformation("AD domain list added.");
            }
            else
            {
                _logger.LogInformation("AD domain was null.");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred while fetching domains list.");

            TempData.NotifyError(e.Message);
        }

        return Json(new { addomainlist = Domains });
    }

    [AllowAnonymous]
    [HttpGet]
    public async Task<JsonResult> GetADGroup(string domainName)
    {
        _logger.LogDebug("Entering GetADGroup method.");

        try
        {
            var groupNames = await _dataProvider.User.GetDomainGroups(domainName, "");

            return Json(new { Success = true, data = groupNames });
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred while fetching AD group list.");

            return e.GetJsonException();
        }

        
    }

    [AllowAnonymous]
    public async Task<IActionResult> Logout()
    {
        _logger.LogInformation($"Logged out user '{WebHelper.UserSession.LoginName ?? "Anonymous User"}'");

        if (WebHelper.UserSession.LoggedUserId.IsNotNullOrWhiteSpace())
        {
            await _publisher.Publish(new UserLogoutEvent { UserId = WebHelper.UserSession.LoggedUserId, LoginName = WebHelper.UserSession.LoginName });

            await ClearDatabaseSessions(WebHelper.UserSession.LoggedUserId);
        }

        if (WebHelper.UserSession.LoginName.IsNotNullOrWhiteSpace())
        {
            var cacheKey = $"Web:{WebHelper.UserSession.LoginName.ToLower()}";

            await _cache.RemoveAsync(cacheKey);
        }

        await CleanSignOut();

        return View();
    }



    #region Verify Methods


    [AllowAnonymous]
    [HttpGet]
    public IActionResult WriteLogAjaxError(string message, string url)
    {
        _logger.LogError($"AJAX error in request: {url}  Error :{message}");

        return Json(new { Success = true });
    }



    public ActionResult UserProfile()
    {
        _logger.LogDebug("Entering UserProfile method.");

        return View();
    }

    //public IActionResult CPTour()
    //{
    //    _logger.LogDebug("Entering CPTour method.");
    //    return View();
    //}

    public IActionResult PatchList()
    {
        return View();
    }

    public async Task<ActionResult> About()
    {
        _logger.LogDebug("Entering About method.");

        try
        {
            var aboutListVm = await _dataProvider.AboutCP.GetAboutCpList();
            _logger.LogInformation("Exiting About method.");
            return View(aboutListVm);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while processing the About request.");
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    # endregion


    #region Private Methods

    private async Task<PreLoginViewResult> PrepareView()
    {
        var viewResult = await _loginService.PrepareLoginViewAsync();

        if (!string.IsNullOrEmpty(viewResult.Message))
        {
            _logger.LogError($"Pre-Login view Error Message: {viewResult.Message}");
        }

        _logger.LogDebug("Exiting PrepareView method.");

        return viewResult;
    }

    private IActionResult ReturnView()
    {
        _logger.LogDebug("Returning login view.");
        return View(new LoginViewModel { Companies = CompanyList });
    }

    private IActionResult RouteToPrepareView(PreLoginViewResult viewResult)
    {
        _logger.LogInformation($"Routing to prepare view with ApplicationStatus: {viewResult.ApplicationStatus}");

        switch (viewResult.ApplicationStatus)
        {
            case ApplicationStatus.EmptyCompanyProfile:
                return RedirectToAction("Configuration", "Basic");
            case ApplicationStatus.EmptyUser:
                return RedirectToAction("Configuration", "Basic");
            case ApplicationStatus.UnhandledError:
            case ApplicationStatus.Qualified:
                var companies = _mapper.Map<List<SelectListItem>>(viewResult.Companies);
                WebHelper.CurrentSession.Set("CompanyProfiles", companies);
                return ReturnView();
        }
        
        return View();
    }

    private IActionResult RouteToPostView(PostLoginViewResult viewResult, string returnUrl, bool isMultipleLogin)
    {
        _logger.LogInformation($"Post Logged in Status {viewResult.LogInStatus}");

        var message = viewResult.Message;

        switch (viewResult.LogInStatus)
        {
            case LogInStatus.Succeeded:

                if (!string.IsNullOrEmpty(returnUrl))
                {
                    return LocalRedirect(returnUrl);
                }

                _logger.LogDebug($"{viewResult.LoginName} : Logged in successfully");

                if (viewResult.Message.IsNotNullOrEmpty())
                {
                    TempData.NotifyWarning(message);
                }

                if (isMultipleLogin)
                {
                    if (viewResult.IsDefaultDashboard && viewResult.Url.IsNotNullOrEmpty())
                    {
                        return Json(new { Success = true, returnUrl, viewResult.Url });
                    }

                    return Json(new { Success = true, returnUrl });
                }

                if (viewResult.IsDefaultDashboard && viewResult.Url.IsNotNullOrEmpty())
                {
                    return RouteToGetView(viewResult.Url);
                }

                return RedirectToAction("List", "ServiceAvailability", new { Area = "Dashboard" });

            case LogInStatus.SiteAdminLandingPage:

                if (isMultipleLogin)
                {
                    return Json(new { Success = true, returnUrl, Role = "SiteAdmin" });
                }

                return RedirectToAction("SiteAdminLanding", "User", new { Area = "Admin" });

            case LogInStatus.PasswordReset:

                if (viewResult.Message.IsNotNullOrEmpty())
                {
                    TempData.NotifyWarning(message);
                }

                return RedirectToAction("ChangePassword");

            case LogInStatus.EmptyLicense:
                return RedirectToAction("LicenseLanding", "LicenseManager", new { Area = "Admin" });

            case LogInStatus.LicenseExpiry:
                return RedirectToAction("LicenseExpiredLanding", "LicenseManager", new { Area = "Admin" });

            case LogInStatus.MultipleLoginSessionFound:

                _logger.LogWarning(viewResult.Message);

                TempData.NotifyMultiLogin(viewResult.LoginName);
                return RedirectToAction("Login");

            case LogInStatus.InvalidLoginAttempt:

                _logger.LogWarning(viewResult.Message);
                TempData.NotifyUnauthorised(viewResult.Message);
                return RedirectToAction("Login");
            case LogInStatus.AccountLocked:
                TempData.NotifyUnauthorised(viewResult.Message);
                return RedirectToAction("Login");
            case LogInStatus.InvalidCredential:

                TempData.NotifyUnauthorised(message);
                return RedirectToAction("Login");
        }

        return Json(new { Success = true, returnUrl });
    }

    private IActionResult RouteToGetView(string url)
    {
        var splitUrl = url.Split('/');

        if (splitUrl.Length == 3)
        {
            return RedirectToAction(splitUrl[2], splitUrl[1], new { Area = splitUrl[0] });
        }
        return RedirectToAction(splitUrl[3], splitUrl[2], new { area = splitUrl[1] });
    }

    private async Task<bool> VerifyOldLoginSessionAsync()
    {
        if (HttpContext.Request.Cookies.Count == 0)
        {
            return false;
        }

        if (!HttpContext.Request.Cookies.Keys.Any(cookie => cookie.Equals("__Host-Identity"))) return false;

        var userId = WebHelper.UserSession.LoggedUserId;

        if (userId.IsNotNullOrWhiteSpace())
        {
            await ClearDatabaseSessions(userId);
        }
        return true;

    }

    #endregion Private Methods
}