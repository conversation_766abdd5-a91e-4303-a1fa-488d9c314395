﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel.ApprovalMatrixUsersViewModel

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<style>
    .nav-underline.adduserModaltabs .nav-link.active, .nav-underline .show > .nav-link {
        color: var(--bs-primary);
        border-bottom-color: var(--bs-primary);
        padding: 1px 0px;
    }

    .nav-underline.adduserModaltabs .nav-link, .nav-underline .show > .nav-link {
        font-weight: normal;
        font-size: 15px;
        color: #000;
        padding: 1px 0px;
    }

    .Icon {
    font-family: Arial, Helvetica, sans-serif;
    width: 24px;
    height: 24px;
    border-radius: 50px;
    background: #006600;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 1.5rem;
    margin: 1px 0;
    display: inline-block;
    }
</style>
@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-approval-list"></i><span>Approvers List</span></h6>
            <form class="d-flex gap-2 align-items-center">
                <div class="input-group w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off">
                    <div class="input-group-text">
                        <div class="dropdown" title="Filter">
                            <span data-bs-toggle="dropdown"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="name=" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Username
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="displayName=" id="DisplayName">
                                        <label class="form-check-label" for="DisplayName">
                                           Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="webAddress=" id="WebAddress">
                                        <label class="form-check-label" for="WebAddress">
                                            Deligates To 
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="Company-CreateButton" data-bs-target="#adduserModal"><i class="cp-add me-1"></i>Add Approver</button>
            </form>
        </div>
        <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
        <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true">
            <!-- Your Configuration content here -->
        </div>

        <div class="card-body pt-0">
            <table class="table table-hover dataTable" id="ApproverTable" style="width:100%">
                <thead>
                    <tr>
                        <th>Sr. No.</th>
                        <th>Username</th>
                        <th>Type</th>
                        <th>Deligates To</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @* <tr>
                    <td>1</td>
                    <td>Martin Arasaratnam</td>
                    <td><span class="badge bg-success rounded-pill px-2 align-middle">Approver</span></td>
                    <td>No Delicates are assigned</td>
                    <td>
                    <div class="d-flex gap-2 align-items-center">
                    <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                    <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                    <i class="cp-Delete"></i>
                    </span>
                    <span role="button" title="Deligates" data-bs-target="#DeligatesModal" data-bs-toggle="modal">
                    <i class="cp-deligate"></i>
                    </span>
                    </div>
                    </td>
                    </tr>
                    <tr>
                    <td>2</td>
                    <td>Selvam</td>
                    <td><span class="badge bg-success rounded-pill px-2 align-middle">Approver</span></td>
                    <td>No Delicates are assigned</td>
                    <td>
                    <div class="d-flex gap-2 align-items-center">
                    <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                    <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                    <i class="cp-Delete"></i>
                    </span>
                    <span role="button" title="Deligates" data-bs-target="#DeligatesModal" data-bs-toggle="modal">
                    <i class="cp-deligate"></i>
                    </span>
                    </div>
                    </td>
                    </tr>
                    <tr>
                    <td>3</td>
                    <td>Sabeel</td>
                    <td><span class="badge bg-warning rounded-pill px-2 align-middle">Requester</span></td>
                    <td>No Delicates are assigned</td>
                    <td>
                    <div class="d-flex gap-2 align-items-center">
                    <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                    <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                    <i class="cp-Delete"></i>
                    </span>
                    <span role="button" title="Deligates" data-bs-target="#DeligatesModal" data-bs-toggle="modal">
                    <i class="cp-deligate"></i>
                    </span>
                    </div>
                    </td>
                    </tr>
                    <tr>
                    <td>4</td>
                    <td>Karthik</td>
                    <td><span class="badge bg-warning rounded-pill px-2 align-middle">Requester</span></td>
                    <td>No Delicates are assigned</td>
                    <td>
                    <div class="d-flex gap-2 align-items-center">
                    <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                    <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                    <i class="cp-Delete"></i>
                    </span>
                    <span role="button" title="Deligates" data-bs-target="#DeligatesModal" data-bs-toggle="modal">
                    <i class="cp-deligate"></i>
                    </span>
                    </div>
                    </td>
                    </tr>
                    <tr>
                    <td>5</td>
                    <td>Sri Vignesh</td>
                    <td><span class="badge bg-success rounded-pill px-2 align-middle">Approver</span></td>
                    <td>No Delicates are assigned</td>
                    <td>
                    <div class="d-flex gap-2 align-items-center">
                    <span role="button" title="Edit" data-bs-target="#PerformQuantitativeModal" data-bs-toggle="modal">
                    <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" data-bs-target="#DeleteModal" data-bs-toggle="modal">
                    <i class="cp-Delete"></i>
                    </span>
                    <span role="button" title="Deligates" data-bs-target="#DeligatesModal" data-bs-toggle="modal">
                    <i class="cp-deligate"></i>
                    </span>
                    </div>
                    </td>
                    </tr> *@
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add User - Modal Start -->
    <div class="modal fade" id="adduserModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
            <form class="modal-content" id="CreateForm">
                @* method="post" asp-controller="Approval" asp-action="CreateOrUpdate" enctype="multipart/form-data" *@
                <div class="modal-header">
                    <h6 class="page_title" title="Perform Quantitative Financial Impact"><i class="cp-add-user"></i><span>Add Approver</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="height:calc(100vh - 130px)">
                    <div>
                        <div class="d-flex align-items-center justify-content-between">
                            <ul class="nav nav-underline adduserModaltabs" id="myTab" role="tablist">
                                <li class="nav-item me-3" role="presentation">
                                    <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
                                            type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                                        CP User
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                                            type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">
                                        Non-CP User
                                    </button>
                                </li>

                            </ul>
                            <div>
                                <div class="input-group w-auto" id="searchElement">
                                    <input type="search" id="approverSearchInput" class="form-control px-1" autocomplete="off" placeholder="Search Approver">
                                    <span role="button" class="input-group-text">
                                        <i class="cp-search fs-7 bg-body-tertiary p-1" id="searchApprovar" title="Search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">


                                <div class="row row-cols-2 dataTable">
                                    <div>
                                        <h6  class="my-2 d-none unAddedtitle">Users</h6>
                                        <div id="addUserApproval" style="height:calc(100vh - 235px);overflow:auto">
                                        </div>
                                    </div>
                                    <div id="addserApprovalTag">
                                        <h6 class="my-2">Added Users</h6>
                                        <div id="addserApproval" style="height:calc(100vh - 235px);overflow:auto">
                                        </div>
                                    </div>
                                  
                                </div>

                                @* @foreach (var siteLocation in Model.UserNames)
                                {

                                <div id="addUserApproval">
                                <div class="border border-light-subtle rounded p-2 my-2">
                                <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center gap-2" id="@siteLocation.Id">
                                <span><img src="~/img/profile-img/user.jpg" class="img-fluid rounded-circle" width="40" /></span>
                                <div>
                                <p class="mb-0">
                                <span class="d-inline-block text-truncate fw-semibold" style="max-width:120px;width:120px">
                                @siteLocation.LoginName
                                </span>
                                <span class="badge bg-success rounded-pill px-2 ms-2">@siteLocation.RoleName</span>
                                </p>
                                <span class="text-primary">All group approver</span>
                                </div>
                                </div>
                                <button type="button" data-id="@siteLocation.Id" class="btn btn-primary btn-sm ADAddUser"
                                data-name="@siteLocation.LoginName">
                                Add
                                </button>
                                </div>
                                </div>

                                </div>
                                //  <option id="@siteLocation.Id" value="@siteLocation.LoginName" >@siteLocation.LoginName</option>
                                } *@

                            </div>
                            <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                                <div class="my-3">
                                    <div class="form-group">
                                        <label class="form-label">Username</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-name"></i></span>
                                            <input type="text" class="form-control" id="UserName" placeholder="Enter Username" autocomplete="off" name="User Name" value="">
                                        </div>
                                        <span id="UserName-error"></span>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-email"></i></span>
                                            <input type="email" class="form-control" id="mail" placeholder="Enter Email" autocomplete="off">
                                        </div>
                                        <span id="Email-error"></span>
                                    </div>

                                    <div id="mobileInputContainer">
                                        <div class="d-flex gap-2">
                                            <div style="width:155px">
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-mobile-icon"></i></span>
                                                        <select id="mobilepre" class="form-select-modal" autocomplete="off" data-placeholder="Select Country Code">
                                                            <option></option>
                                                        </select>
                                                    </div>
                                                    <span id="MobilePre-error"></span>
                                                    @*  <span asp-validation-for="UserInfoCommand.Mobile" id="MobilePre-error" class="text-start"></span> *@
                                                </div>
                                            </div>
                                            <div class="w-100">
                                                <div class="form-group">
                                                    <div class="input-group">
                                                        <span class="input-group-text"></span>
                                                        <input id="mobilenum" type="text" class="form-control" placeholder="Enter Mobile Number" autocomplete="off" maxlength="15" />
                                                    </div>
                                                    <span id="Mobile-error"></span>
                                                </div>
                                            </div>
                                            <input asp-for="Id" type="hidden" id="userNameId" />
                                            <input asp-for="MobileNumber" type="hidden" id="AMMobileNumber" />
                                            <input asp-for="Email " type="hidden" id="AMEMail" />
                                            <input asp-for="UserName" type="hidden" id="AMUsername" />
                                            <input asp-for="IsLink" type="hidden" id="AMIsLink" />
                                            <input asp-for="UserType" type="hidden" id="AMUserType" />
                                            <input asp-for="AcceptType" type="hidden" id="AMAccessType" value="NA" />
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Operational Service</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-business-service"></i></span>
                                            <select class="form-select-modal" asp-for="BusinessServiceProperties" id="selectBusinessService" data-placeholder="Select Operational Service">
                                            </select>
                                        </div>
                                        <span id="BusinessService-error"></span>
                                    </div>
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="flexCheckDefault" value="" id="flexCheckDefault">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Create a link for login
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer mt-2 d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" id="cancelFunction" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" id="SaveFunction" class="btn btn-primary btn-sm" title="Save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--Add User - Modal End -->
    <!-- Deligates - Modal Start -->
    <div class="modal fade" id="DeligatesModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
            <form class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title" title="Perform Quantitative Financial Impact"><i class="cp-deligate"></i><span>Deligates</span></h6>
                    <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div>
                        <div class="my-2">
                            <div class="form-group">
                                <label class="form-label">Select User</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-user"></i></span>
                                    <select class="form-select-modal" data-placeholder="Select Select User">
                                        <option></option>
                                        <option>Selvam</option>
                                        <option>Karthik</option>
                                        <option>Sabeel</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row row-cols-2">
                                <div class="form-group">
                                    <label class="form-label">Start Date</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                        <input class="form-control" type="date" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">End Date</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-calendar"></i></span>
                                        <input class="form-control" type="date" />
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" title="Cancel" cursorshover="true">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" title="Save">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @* <!--Deligates - Modal End -->

    <div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-modal="true" role="dialog">
    <div class="modal-dialog modal-sm modal-dialog-centered">
    <form action="#" class="w-100">
    <div class="modal-content">
    <div class="modal-header p-0">
    <img class="delete-img" src="/img/isomatric/delete.png">
    </div>
    <div class="modal-body text-center pt-0">
    <h4>Are you sure?</h4>
    <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" title="test">test</span>data?</p>

    </div>
    <div class="modal-footer gap-1 justify-content-center">
    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
    <button type="submit" class="btn btn-primary btn-sm">Yes</button>
    </div>
    </div>
    </form>
    </div>
    </div>
    </div> *@

    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
        <partial name="Delete" />
    </div>
</div>


<script src="~/js/Manage/approvalmatrix/approver.js"></script>
<script src="~/js/Manage/approvalmatrix/approverfunctions.js"></script>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
