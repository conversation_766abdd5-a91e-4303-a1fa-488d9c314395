﻿@{
    ViewData["Title"] = "InfraObjectSummaryReport";
}

@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/viewer.part.bundle.js" asp-append-version="true"></script>
   }


<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("InfraObjectSummaryDocumentViewer").Height("1150px").Bind(new InfraObjectSummary(ViewData["InfraObjectSummaryReportData"].ToString()))

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">InfraObject Summary Report</h6>
        </div>
    </div>
</div>
<div class="card">
    <div>

        <div class="gap-3 mt-3 card-group">
            <div class="rounded card bg-light">
                <div class="card-header text-primary fw-bold border-bottom">
                    InfraObject
                    Summary
                </div>
               
                <div class="p-0 card-body">
                    <div class="rounded-0 list-group">
                        <div class="d-flex justify-content-between px-3 py-2 border-top-0  list-group-item">
                            <div class="me-auto d-flex align-items-center">
                                <i class="cp-business-service"></i>
                                <span class="ms-1 align-middle">Business Service Name</span>
                            </div>
                            All Business Services
                        </div>
                        <li class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                            <div class="me-auto d-flex align-items-center">
                                <span class="ms-1 align-middle text-success d-flex">
                                    DataLag > Configured RPO
                                </span>
                            </div>
                            <div class="d-flex align-items-center ">
                                <div class="me-auto d-flex align-items-center">
                                    <span class="ms-1 align-middle text-danger d-flex">
                                        DataLag &lt;= Configured RPO
                                    </span>
                                </div>
                            </div>
                        </li>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3 prebuildreportcustom">
            <div>
                <div class="table-responsive">
                    <table class="table table-sm" style="font-size: 10px;">
                        <thead class="bg-light">
                            <tr>
                                <th>Sr.No</th>
                                <th>InfraObject</th>
                                <th>Replication Type</th>
                                <th>Configured RPO</th>
                                <th><span>Last Archive Log  PR</span></th>
                                <th>Time Stamp of Archive Log</th>
                                <th>Last Archive Log Applied DR</th>
                                <th>Time Stamp Of last Archive Log Applied</th>
                                <th>DataLag</th>
                                <th>DR Health</th>
                            </tr>
                        </thead>
                        <tbody id="tableDataones">
                            <tr class="text-center">
                                <td>1</td>
                                <td>Infra_ODG</td>
                                <td>NativeReplication_ODG</td>
                                <td>04:56:58</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>00:00:00</td>
                                <td>Healthy</td>
                            </tr>
                            <tr class="text-center">
                                <td>2</td>
                                <td>Infra_ODG</td>
                                <td>NativeReplication_ODG</td>
                                <td>04:56:58</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>00:00:00</td>
                                <td>Healthy</td>
                            </tr>
                            <tr class="text-center">
                                <td>
                                    3
                                </td>
                                <td>Infra_ODG</td>
                                <td>NativeReplication_ODG</td>
                                <td>04:56:58</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>00:00:00</td>
                                <td>Healthy</td>
                            </tr>
                            <tr class="text-center">
                                <td>4</td>
                                <td>Infra_ODG</td>
                                <td>NativeReplication_ODG</td>
                                <td>04:56:58</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>00:00:00</td>
                                <td>Healthy</td>
                            </tr>
                            <tr class="text-center">
                                <td>5</td>
                                <td>Infra_ODG</td>
                                <td>NativeReplication_ODG</td>
                                <td>04:56:58</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>NA</td>
                                <td>00:00:00</td>
                                <td>Healthy</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

    </div>
</div>

<script src="~/js/report-charts/infraobject_summary_report.js"></script>*@