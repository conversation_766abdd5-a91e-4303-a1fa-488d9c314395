﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.Db2HaDrMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetPaginatedList;

public class GetDb2HaDrMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetDb2HaDrMonitorStatusPaginatedListQuery, PaginatedResult<Db2HaDrMonitorStatusListVm>>
{
    private readonly IDb2HaDrMonitorStatusRepository _d2HaDrMonitorStatusRepository;
    private readonly IMapper _mapper;

    public GetDb2HaDrMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        IDb2HaDrMonitorStatusRepository d2HaDrMonitorStatusRepository)
    {
        _mapper = mapper;
        _d2HaDrMonitorStatusRepository = d2HaDrMonitorStatusRepository;
    }

    public async Task<PaginatedResult<Db2HaDrMonitorStatusListVm>> Handle(
        GetDb2HaDrMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _d2HaDrMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new Db2HaDrMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<Db2HaDrMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}