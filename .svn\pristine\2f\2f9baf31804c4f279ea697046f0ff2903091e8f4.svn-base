﻿using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Validators;

public class UpdateServerTypeValidatorTests
{
    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;
    public List<Domain.Entities.ServerType> ServerTypes { get; set; }

    public UpdateServerTypeValidatorTests()
    {
        ServerTypes = new Fixture().Create<List<Domain.Entities.ServerType>>();

        _mockServerTypeRepository = ServerTypeRepositoryMocks.UpdateServerTypeRepository(ServerTypes);
    }

    //TYPE

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Type_InServerType_With_Empty(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Type_InServerType_With_IsNull(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = null;
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Type_InServerType_With_MinimumRange(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "PQ";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Type_InServerType_With_MaximumRange(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRS";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "  Karur  ";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_DoubleSpace_InFront(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "  Karur";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_DoubleSpace_InBack(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "Karur  ";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_TripleSpace_InBetween(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "Karur   Tech";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_SpecialCharacters_InFront(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "&^%$Karur Tech";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_SpecialCharacters_InBetween(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "Karur&^%$#Tech";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_SpecialCharacters_Only(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "#@%&^$#$#";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_UnderScore_InFront(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "_Karur";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_UnderScore_InBack(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "Karur_";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_UnderScore_InFront_AndBack(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "_Karur_";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_Numbers_InFront(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "871Karur";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "_871Karur_";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_UnderScore_InFront_AndNumbers_InBack(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "_Karur564";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoServerTypeData]
    public async Task Verify_Update_Valid_Type_InServerType_With_Numbers_Only(UpdateServerTypeCommand updateServerTypeCommand)
    {
        var validator = new UpdateServerTypeCommandValidator(_mockServerTypeRepository.Object);

        updateServerTypeCommand.Name = "8987412454856";
        updateServerTypeCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateServerTypeCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.ServerType.ServerTypeValidRequired, validateResult.Errors[0].ErrorMessage);
    }
}
