﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Events;

public class WorkflowProfileInfoCreatedEventHandlerTests : IClassFixture<WorkflowProfileInfoFixture>
{
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowProfileInfoCreatedEventHandler _handler;

    public WorkflowProfileInfoCreatedEventHandlerTests(WorkflowProfileInfoFixture workflowProfileInfoFixture)
    {
        _workflowProfileInfoFixture = workflowProfileInfoFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowProfileInfoEventLogger = new Mock<ILogger<WorkflowProfileInfoCreatedEventHandler>>();

        _mockUserActivityRepository = WorkflowProfileInfoRepositoryMocks.CreateWorkflowProfileInfoEventRepository(_workflowProfileInfoFixture.UserActivities);

        _handler = new WorkflowProfileInfoCreatedEventHandler(mockLoggedInUserService.Object, mockWorkflowProfileInfoEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateWorkflowProfileInfoEventCreated()
    {
        _workflowProfileInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoCreatedEvent, CancellationToken.None);

        result.Equals(_workflowProfileInfoFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_CreateWorkflowProfileInfoEventCreated()
    {
        _workflowProfileInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowProfileInfoFixture.WorkflowProfileInfoCreatedEvent, CancellationToken.None);

        result.Equals(_workflowProfileInfoFixture.UserActivities[0].Id);

        result.Equals(_workflowProfileInfoFixture.WorkflowProfileInfoCreatedEvent.WorkflowProfileName);

        await Task.CompletedTask;
    }
}