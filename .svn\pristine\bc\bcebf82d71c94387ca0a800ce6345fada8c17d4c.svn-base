using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IApprovalMatrixUsersRepository : IRepository<ApprovalMatrixUsers>
{
    Task<bool> IsNameExist(string name, string id);
    Task<ApprovalMatrixUsers> GetByUserIdAsync(string userId);
    Task<List<ApprovalMatrixUsers>> GetListByApprovalIdsAsync(List<string> userIds);

    Task<PaginatedResult<ApprovalMatrixUsers>> GetApprovalMatrixUserByType(string type, int pageNumber, int pageSize,
        Specification<ApprovalMatrixUsers> productFilterSpec, string sortColumn, string sortOrder);
}
