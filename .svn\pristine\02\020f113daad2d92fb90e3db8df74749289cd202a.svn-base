﻿using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Update;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorStatus.Commands
{
    public class UpdatePostgresMonitorStatusTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPostgresMonitorStatusRepository> _mockPostgresMonitorStatusRepository;
        private readonly UpdatePostgresMonitorStatusCommandHandler _handler;

        public UpdatePostgresMonitorStatusTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPostgresMonitorStatusRepository = new Mock<IPostgresMonitorStatusRepository>();
            _handler = new UpdatePostgresMonitorStatusCommandHandler(_mockMapper.Object, _mockPostgresMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnValidResponse_WhenPostgresMonitorStatusIsUpdated()
        {
            var command = new UpdatePostgresMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Inactive",
                WorkflowName = "Test_Workflow",
            };

            var existingPostgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Active",
                WorkflowName = "Demo_Test",
            };

            var updatedPostgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Inactive",
                ConfiguredRPO = "66",
            };

            var responseMessage = Message.Update(nameof(PostgresMonitorStatus), updatedPostgresMonitorStatus.ReferenceId);

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingPostgresMonitorStatus);

            _mockMapper.Setup(m => m.Map(command, existingPostgresMonitorStatus))
                .Verifiable();

            _mockPostgresMonitorStatusRepository.Setup(r => r.UpdateAsync(existingPostgresMonitorStatus))
                .ReturnsAsync(updatedPostgresMonitorStatus);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(updatedPostgresMonitorStatus.ReferenceId, result.Id);
            Assert.Equal(responseMessage, result.Message);

            _mockPostgresMonitorStatusRepository.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockPostgresMonitorStatusRepository.Verify(r => r.UpdateAsync(existingPostgresMonitorStatus), Times.Once);
            _mockMapper.Verify(m => m.Map(command, existingPostgresMonitorStatus), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenPostgresMonitorStatusDoesNotExist()
        {
            var command = new UpdatePostgresMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Inactive",
                InfraObjectName = "Test_Infra",
            };
            _mockPostgresMonitorStatusRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.PostgresMonitorStatus)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("PostgresMonitorStatus", exception.Source);
            Assert.Equal("123", exception.Source);
        }

        [Fact]
        public async Task Handle_ShouldReturnValidResponse_WhenPostgresMonitorStatusIsUpdatedWithDifferentFields()
        {
            var command = new UpdatePostgresMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Inactive",
                ConfiguredRPO = "75",
            };

            var existingPostgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Active",
                WorkflowName = "Workflow_Test",
            };

            var updatedPostgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Inactive",
                InfraObjectName = "Infra_Test",
            };

            var responseMessage = Message.Update(nameof(PostgresMonitorStatus), updatedPostgresMonitorStatus.ReferenceId);

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(existingPostgresMonitorStatus);

            _mockMapper.Setup(m => m.Map(command, existingPostgresMonitorStatus))
                .Verifiable();

            _mockPostgresMonitorStatusRepository.Setup(r => r.UpdateAsync(existingPostgresMonitorStatus))
                .ReturnsAsync(updatedPostgresMonitorStatus);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(updatedPostgresMonitorStatus.ReferenceId, result.Id);
            Assert.Equal(responseMessage, result.Message);
        }
    }
}
