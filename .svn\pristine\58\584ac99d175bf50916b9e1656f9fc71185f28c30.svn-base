﻿@using ContinuityPatrol.Domain.ViewModels;

@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration

@model ErrorViewModel
@{
    ViewData["Title"] = "Unauthorized";
    Layout = "~/Views/Shared/_EmptyLayout.cshtml";
}

<body class="vh-100 d-flex align-items-center justify-content-center" style="background: #fafafa!important;">
    <div class="content-center w-100">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-12 mb-3">
                    <img alt="Logo" src="~/img/logo/cplogo.svg" width="320" title="Logo"
                         class="">
                </div>
                <div class="col-lg-12 my-2">
                    <img src="~/img/isomatric/401_error.svg" alt="404Error"
                         class="img-fluid">
                </div>
                <div class="col-lg-12 ml-auto">
                    <div class="ex-page-content">
                        <h3 class="my-3 text-danger">Unauthorized</h3>
                        <h5 class="my-3 text-dark">Your authorization failed, Please try refreshing the page and fill the correct login details</h5>
                        <p>To Login back <a asp-controller="Account" asp-action="PreLogin"> Click Here </a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="fixed-bottom d-flex justify-content-center">

        @{
            var version = Configuration.GetValue<string>("CP:Version");
            var isCOE = Configuration.GetValue<string>("Release:isCOE");
        }

        @if (@isCOE != null)
        {
            <p>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</p>
        }
        else
        {
            <p>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</p>
        }
        
       @*  <p class="mb-1">
            Continuity Patrol Version <span class="cpVersionData"></span><span class="ms-2 me-2 border border-secondary"></span>©
            2024-2025<a class="text-dark" href="https://perpetuuiti.com/" rel="noreferrer"
                        target="_blank">Perpetuuiti</a> - All Rights Reserved
        </p> *@
    </div>
</body>


