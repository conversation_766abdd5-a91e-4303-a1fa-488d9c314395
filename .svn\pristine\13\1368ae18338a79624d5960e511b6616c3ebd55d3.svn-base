using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class VeritasClusterProfile : Profile
{
    public VeritasClusterProfile()
    {
        CreateMap<VeritasCluster, VeritasClusterListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<VeritasCluster, VeritasClusterDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<VeritasCluster, CreateVeritasClusterCommand>().ReverseMap();
        CreateMap<VeritasCluster, VeritasClusterViewModel>().ReverseMap();

        CreateMap<CreateVeritasClusterCommand, VeritasClusterViewModel>().ReverseMap();
        CreateMap<UpdateVeritasClusterCommand, VeritasClusterViewModel>().ReverseMap();

        CreateMap<UpdateVeritasClusterCommand, VeritasCluster>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<PaginatedResult<VeritasCluster>,PaginatedResult<VeritasClusterListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}