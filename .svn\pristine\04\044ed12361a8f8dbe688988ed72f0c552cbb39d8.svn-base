﻿using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrixLevel;

namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Queries.GetDetaill;

public class GetEscalationMatrixLevelDetailQueryHandler:IRequestHandler<GetEscalationMatrixLevelDetailQuery, EscalationMatrixLevelListVm>
{
    private readonly IEscalationMatrixLevelRepository _escalationMatrixLevelRepository;
    private readonly IMapper _mapper;

	public GetEscalationMatrixLevelDetailQueryHandler(IEscalationMatrixLevelRepository escalationMatrixLevelRepository,IMapper mapper)
	{
		_escalationMatrixLevelRepository = escalationMatrixLevelRepository;
        _mapper = mapper;

	}

    public async Task<EscalationMatrixLevelListVm> Handle(GetEscalationMatrixLevelDetailQuery request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Invalid GUID");

        var detail = await _escalationMatrixLevelRepository.GetByReferenceIdAsync(request?.Id);

        return _mapper.Map<EscalationMatrixLevelListVm>(detail);

    }
}
