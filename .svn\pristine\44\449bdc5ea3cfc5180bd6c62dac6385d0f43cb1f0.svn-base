﻿using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Queries;

public class GetWorkflowDetailQueryHandlerTests : IClassFixture<WorkflowFixture>, IClassFixture<WorkflowInfraObjectFixture>, IClassFixture<WorkflowProfileInfoFixture>, IClassFixture<WorkflowProfileInfoViewFixture>
{
    private readonly WorkflowFixture _workflowFixture;
    private readonly WorkflowProfileInfoFixture _workflowProfileInfoFixture;
    private readonly WorkflowProfileInfoViewFixture _workflowProfileInfoViewFixture;
    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
    private readonly Mock<IWorkflowInfraObjectRepository> _mockWorkflowInfraObjectRepository;
    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;
    private readonly Mock<IWorkflowProfileInfoViewRepository> _mockWorkflowProfileInfoViewRepository;
    private readonly Mock<IWorkflowViewRepository> _mockWorkflowViewRepository;
    private readonly GetWorkflowDetailQueryHandler _handler;

    public GetWorkflowDetailQueryHandlerTests(WorkflowFixture workflowFixture, WorkflowInfraObjectFixture workflowInfraObjectFixture, WorkflowProfileInfoFixture workflowProfileInfoFixture, WorkflowProfileInfoViewFixture workflowProfileInfoViewFixture)
    {
        _workflowFixture = workflowFixture;
        _workflowProfileInfoFixture = workflowProfileInfoFixture;
        _workflowProfileInfoViewFixture = workflowProfileInfoViewFixture;
        _mockWorkflowViewRepository = new Mock<IWorkflowViewRepository>();
        _mockWorkflowRepository = WorkflowRepositoryMocks.GetWorkflowRepository(_workflowFixture.Workflows);
        _mockWorkflowProfileInfoViewRepository = WorkflowProfileInfoViewRepositoryMocks.GetWorkflowProfileInfoViewRepository(_workflowProfileInfoViewFixture.WorkflowProfileInfoViews);
        _mockWorkflowProfileInfoRepository = WorkflowProfileInfoRepositoryMocks.GetWorkflowProfileInfoRepository(_workflowProfileInfoFixture.WorkflowProfileInfos);
        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.GetWorkflowInfraObjectRepository(workflowInfraObjectFixture.WorkflowInfraObjects);
        _handler = new GetWorkflowDetailQueryHandler(_mockWorkflowViewRepository.Object);

        _workflowFixture.Workflows[0].Properties = @"{""nodes"":[{""id"":""node_1_78"",""offsetY"":121,""offsetX"":473,""actionInfo"":{""actionName"":""Execute_OSCommand"",""description"":""Execute_OSCommand"",""connectionTimeout"":""2"",""Isemail"":false,""IsSms"":false,""nodeId"":""f760055a-8596-4ec7-8926-f54c02553caa"",""actionType"":""e49d3c06-2e3f-478f-bf9e-81460845539b"",""properties"":{""ServerName"":""4438dec7-6e31-4787-b779-0d3c6950e26a"",""Command"":""ls-ltr"",""@@PRServerName"":""90f20276-2ce6-433a-bf2f-ec9c3d0f5650"",""@@PRDBName"":""3aaafeaf-f849-42e8-9b45-bb5fa35455f7"",""@@DRServerName"":""90f20276-2ce6-433a-bf2f-ec9c3d0f5650"",""@@DRDBName"":""3aaafeaf-f849-42e8-9b45-bb5fa35455f7"",""PR_Server"":""PR_SERVER_KEY"",""DR_Server"":""DR_SERVER_KEY"",""PR_Database"":""PR_DATABASE_KEY"",""DR_Database"":""DR_DATABASE_KEY""}}}],""connectors"":[{""id"":""connector0_818"",""sourceID"":""start"",""targetID"":""node_1_78""}],""nodes[0].actionInfo.properties"":{""ServerName"":""4438dec7-6e31-4787-b779-0d3c6950e26a"",""Command"":""ls-ltr"",""@@PRServerName"":""90f20276-2ce6-433a-bf2f-ec9c3d0f5650"",""@@PRDBName"":""3aaafeaf-f849-42e8-9b45-bb5fa35455f7"",""@@DRServerName"":""90f20276-2ce6-433a-bf2f-ec9c3d0f5650"",""@@DRDBName"":""3aaafeaf-f849-42e8-9b45-bb5fa35455f7"",""PR_Server"":""PR_SERVER_KEY"",""DR_Server"":""DR_SERVER_KEY"",""PR_Database"":""PR_DATABASE_KEY"",""DR_Database"":""DR_DATABASE_KEY""}}";
    }

    [Fact]
    public async Task Handle_Return_Workflow_Details_When_Valid()
    {
        var result = await _handler.Handle(new GetWorkflowDetailQuery { Id = _workflowFixture.Workflows[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<WorkflowDetailVm>();
        result.Id.ShouldBe(_workflowFixture.Workflows[0].ReferenceId);
        result.Name.ShouldBe(_workflowFixture.Workflows[0].Name);
        result.Version.ShouldBe(_workflowFixture.Workflows[0].Version);
        result.Properties.ShouldBe(_workflowFixture.Workflows[0].Properties);
       // result.IsFreeze.ShouldBe(_workflowFixture.Workflows[0].IsFreeze);
        result.IsLock.ShouldBe(_workflowFixture.Workflows[0].IsLock);
        result.IsPublish.ShouldBe(_workflowFixture.Workflows[0].IsPublish);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_Invalid_WorkflowId()
    {
        var handler = new GetWorkflowDetailQueryHandler(_mockWorkflowViewRepository.Object);

        await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetWorkflowDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowByReferenceId_OneTime()
    {
        await _handler.Handle(new GetWorkflowDetailQuery() { Id = _workflowFixture.Workflows[0].ReferenceId }, CancellationToken.None);

        _mockWorkflowRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}