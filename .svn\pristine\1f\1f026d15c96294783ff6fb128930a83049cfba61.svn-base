﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class EmcSrdfOracleRacFullDBControllerShould
    {
        private readonly EmcSrdfOracleRacFullDBController _controller;

        public EmcSrdfOracleRacFullDBControllerShould()
        {
            _controller = new EmcSrdfOracleRacFullDBController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
