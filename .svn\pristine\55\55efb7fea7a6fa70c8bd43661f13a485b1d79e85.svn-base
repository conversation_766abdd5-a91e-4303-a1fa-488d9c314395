﻿using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationJob.Queries
{
    public class GetReplicationJobDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReplicationJobRepository> _mockReplicationJobRepository;
        private readonly GetReplicationJobDetailQueryHandler _handler;

        public GetReplicationJobDetailQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockReplicationJobRepository = new Mock<IReplicationJobRepository>();
            _handler = new GetReplicationJobDetailQueryHandler(_mockMapper.Object, _mockReplicationJobRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnReplicationJobDetail_WhenJobExists()
        {
            var jobId = Guid.NewGuid().ToString();
            var replicationJob = new Domain.Entities.ReplicationJob { Id = 1, IsActive = true };
            var expectedVm = new ReplicationJobListVm { Id = jobId };

            _mockReplicationJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(jobId))
                .ReturnsAsync(replicationJob);

            _mockMapper
                .Setup(mapper => mapper.Map<ReplicationJobListVm>(replicationJob))
                .Returns(expectedVm);

            var query = new GetReplicationJobDetailQuery { JobId = jobId };

            var result = await _handler.Handle(query, CancellationToken.None);

           // result.Should().BeEquivalentTo(expectedVm);
            _mockReplicationJobRepository.Verify(repo => repo.GetByReferenceIdAsync(jobId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<ReplicationJobListVm>(replicationJob), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenJobDoesNotExist()
        {
            var jobId = Guid.NewGuid().ToString();

            _mockReplicationJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(jobId))
                .ReturnsAsync((Domain.Entities.ReplicationJob)null);

            var query = new GetReplicationJobDetailQuery { JobId = jobId };

            Func<Task> act = async () => await _handler.Handle(query, CancellationToken.None);

            //await act.Should().ThrowAsync<NotFoundException>()
            //    .WithMessage($"*{nameof(Domain.Entities.ReplicationJob)}*{jobId}*");

            _mockReplicationJobRepository.Verify(repo => repo.GetByReferenceIdAsync(jobId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<ReplicationJobListVm>(It.IsAny<Domain.Entities.ReplicationJob>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenMapperReturnsNull()
        {
            var jobId = Guid.NewGuid().ToString();
            var replicationJob = new Domain.Entities.ReplicationJob { Id = 1, IsActive = true };

            _mockReplicationJobRepository
                .Setup(repo => repo.GetByReferenceIdAsync(jobId))
                .ReturnsAsync(replicationJob);

            _mockMapper
                .Setup(mapper => mapper.Map<ReplicationJobListVm>(replicationJob))
                .Returns((ReplicationJobListVm)null);

            var query = new GetReplicationJobDetailQuery { JobId = jobId };

            Func<Task> act = async () => await _handler.Handle(query, CancellationToken.None);

            //await act.Should().ThrowAsync<NotFoundException>()
            //    .WithMessage($"*{nameof(replicationJob)}*{jobId}*");

            _mockReplicationJobRepository.Verify(repo => repo.GetByReferenceIdAsync(jobId), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<ReplicationJobListVm>(replicationJob), Times.Once);
        }
    }

}
