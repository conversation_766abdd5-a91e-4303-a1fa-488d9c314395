﻿namespace ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Create;

public class CreateWorkflowExecutionTempCommandHandler : IRequestHandler<CreateWorkflowExecutionTempCommand,
    CreateWorkflowExecutionTempResponse>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowExecutionTempRepository _workflowExecutionTempRepository;

    public CreateWorkflowExecutionTempCommandHandler(IMapper mapper,
        IWorkflowExecutionTempRepository workflowExecutionTempRepository)
    {
        _mapper = mapper;
        _workflowExecutionTempRepository = workflowExecutionTempRepository;
    }

    public async Task<CreateWorkflowExecutionTempResponse> Handle(CreateWorkflowExecutionTempCommand request,
        CancellationToken cancellationToken)
    {
        var workflowExecutionTemp = _mapper.Map<Domain.Entities.WorkflowExecutionTemp>(request);

        workflowExecutionTemp = await _workflowExecutionTempRepository.AddAsync(workflowExecutionTemp);

        var response = new CreateWorkflowExecutionTempResponse
        {
            Message = Message.Create("Workflow Execution", workflowExecutionTemp.WorkflowName),
            WorkflowExecutionTempId = workflowExecutionTemp.ReferenceId
        };

        return response;
    }
}