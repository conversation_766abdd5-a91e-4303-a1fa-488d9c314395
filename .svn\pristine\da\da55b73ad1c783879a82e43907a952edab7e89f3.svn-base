using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DrReadyRepository : BaseRepository<DrReady>, IDrReadyRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DrReadyRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<DrReady>> ListAllAsync()
    {
        return await SelectToDrReadies(base.QueryAll(x=>x.IsActive)).ToListAsync();
    }

    public async Task<DrReady> GetDrReadyByBusinessServiceId(string businessServiceId)
    {
        return await SelectToDrReadies(_dbContext.DrReadys.AsNoTracking().Active()).FirstOrDefaultAsync(x => x.BusinessServiceId.Equals(businessServiceId));
    }

    private IQueryable<DrReady> SelectToDrReadies(IQueryable<DrReady> drReadies)
    {
        return drReadies.Select(x => new DrReady
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            TotalBusinessFunction = x.TotalBusinessFunction,
            BFAvailable = x.BFAvailable,
            BFImpact = x.BFImpact,
            BFDRReady = x.BFDRReady,
            BFDRNotReady = x.BFDRNotReady,
            TotalInfraObject = x.TotalInfraObject,
            InfraAvailable = x.InfraAvailable,
            InfraImpact = x.InfraImpact,
            InfraDRReady = x.InfraDRReady,
            InfraDRNotReady = x.InfraDRNotReady
        });
    }


    //public Task<bool> IsNameExist(string name, string id)
    //{
    //    return Task.FromResult(!id.IsValidGuid()
    //        ? Entities.Any(e => e.Name.Equals(name))
    //        : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    //}
}