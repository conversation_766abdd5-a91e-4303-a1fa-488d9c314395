﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowProfile.Events.Create;

public class WorkflowProfileCreatedEventHandler : INotificationHandler<WorkflowProfileCreatedEvent>
{
    private readonly ILogger<WorkflowProfileCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowProfileCreatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowProfileCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowProfileCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.WorkflowProfile}",
            Entity = Modules.WorkflowProfile.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"WorkflowProfile '{createdEvent.ProfileName}' Created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"WorkflowProfile '{createdEvent.ProfileName}' Created successfully.");
    }
}