﻿namespace ContinuityPatrol.Application.Features.Report.Commands.Update;

public class UpdateReportCommand : IRequest<UpdateReportResponse>
{
    public string Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public string FilterColumn { get; set; }

    public string HeaderColumn { get; set; }

    public string Design { get; set; }

    public string DataSet { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; Id:{Id};";
    }
}