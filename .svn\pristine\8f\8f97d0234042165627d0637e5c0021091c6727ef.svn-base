using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Features.BulkImport.Commands.RollBack;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class BulkImportControllerTests : IClassFixture<BulkImportFixture>
{
    private readonly BulkImportFixture _bulkImportFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly BulkImportController _controller;

    public BulkImportControllerTests(BulkImportFixture bulkImportFixture)
    {
        _bulkImportFixture = bulkImportFixture;

        var testBuilder = new ControllerTestBuilder<BulkImportController>();
        _controller = testBuilder.CreateController(
            _ => new BulkImportController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task CreateBulkImport_Returns201Created()
    {
        // Arrange
        var command = _bulkImportFixture.CreateBulkImportCommand;
        var expectedMessage = "BulkImport operation has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.Id
            });

        // Act
        var result = await _controller.CreateBulkImport(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<CreateBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task CreateBulkImport_ValidatesId()
    {
        // Arrange
        var command = new CreateBulkImportCommand
        {
            Id = "" // Empty ID should cause validation error
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Id is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImport(command));
    }

    [Fact]
    public async Task NextBulkImportAction_ReturnsOk()
    {
        // Arrange
        var command = _bulkImportFixture.NextBulkImportCommand;
        var expectedMessage = "BulkImport next operation has been executed successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new NextBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.NextBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<NextBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task NextBulkImportAction_ValidatesGroupId()
    {
        // Arrange
        var command = new NextBulkImportCommand
        {
            GroupId = "" // Empty GroupId should cause validation error
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("GroupId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.NextBulkImportAction(command));
    }

    [Fact]
    public async Task RollBackBulkImportAction_ReturnsOk()
    {
        // Arrange
        var command = _bulkImportFixture.RollBackBulkImportCommand;
        var expectedMessage = "BulkImport rollback operation has been executed successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new RollBackBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.RollBackBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<RollBackBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task RollBackBulkImport_ValidatesGroupId()
    {
        // Arrange
        var command = new RollBackBulkImportCommand
        {
            GroupId = "" // Empty GroupId should cause validation error
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("GroupId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.RollBackBulkImportAction(command));
    }

    [Fact]
    public async Task CreateBulkImport_HandlesInvalidGuid()
    {
        // Arrange
        var command = new CreateBulkImportCommand
        {
            Id = "invalid-guid-format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidArgumentException("Invalid GUID format"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.CreateBulkImport(command));
    }

    [Fact]
    public async Task NextBulkImportAction_HandlesOperationNotFound()
    {
        // Arrange
        var command = new NextBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("BulkImport operation group not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.NextBulkImportAction(command));
    }

    [Fact]
    public async Task RollBackBulkImportAction_HandlesRollbackFailure()
    {
        // Arrange
        var command = new RollBackBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Rollback operation failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.RollBackBulkImportAction(command));
    }

    [Fact]
    public async Task CreateBulkImport_HandlesComplexScenario()
    {
        // Arrange
        var command = new CreateBulkImportCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        var expectedMessage = "Complex BulkImport operation with multiple dependencies has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.Id
            });

        // Act
        var result = await _controller.CreateBulkImport(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<CreateBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task NextBulkImport_HandlesSequentialOperations()
    {
        // Arrange
        var command = new NextBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };

        var expectedMessage = "Sequential BulkImport next operation completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new NextBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.NextBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<NextBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task RollBackBulkImportAction_HandlesPartialRollback()
    {
        // Arrange
        var command = new RollBackBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
        };

        var expectedMessage = "Partial BulkImport rollback completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new RollBackBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.RollBackBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<RollBackBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions since BulkImportController has empty implementation
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateBulkImport_HandlesComplexOperationGroup()
    {
        // Arrange
        var command = new CreateBulkImportCommand
        {
            Id = Guid.NewGuid().ToString()
           
        };

        var expectedMessage = "BulkImport operation has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.Id
            });

        // Act
        var result = await _controller.CreateBulkImport(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<CreateBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task NextBulkImportAction_HandlesSequentialOperations()
    {
        // Arrange
        var command = new NextBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
           
        };

        var expectedMessage = "BulkImport next operation has been executed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new NextBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.NextBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<NextBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task RollBackBulkImportAction_HandlesPartialRollbackScenario()
    {
        // Arrange
        var command = new RollBackBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
           
        };

        var expectedMessage = "Partial BulkImport rollback completed successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new RollBackBulkImportResponse
            {
                Message = expectedMessage,
                BulkImportOperationGroupId = command.GroupId
            });

        // Act
        var result = await _controller.RollBackBulkImportAction(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = Assert.IsType<RollBackBulkImportResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.GroupId, response.BulkImportOperationGroupId);
    }

    [Fact]
    public async Task CreateBulkImport_ValidatesCompanyId()
    {
        // Arrange
        var command = new CreateBulkImportCommand
        {
            Id = Guid.NewGuid().ToString()
           
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("CompanyId is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateBulkImport(command));
    }

    [Fact]
    public async Task NextBulkImportAction_HandlesOperationSequenceFailure()
    {
        // Arrange
        var command = new NextBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
          
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Operation sequence failed - dependency not met"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.NextBulkImportAction(command));
    }

    [Fact]
    public async Task RollBackBulkImportAction_HandlesRollbackConflict()
    {
        // Arrange
        var command = new RollBackBulkImportCommand
        {
            GroupId = Guid.NewGuid().ToString()
           
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Rollback conflict - operation in progress"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.RollBackBulkImportAction(command));
    }
}
