﻿let globalMapData = [];
let globalMapDataLatLong = [];
let globalinfraData = [];
let impactImage = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img" style="margin-left:75px;margin-top:30px">';
let globalbusinessServiceId = "";
let resiliencyData = [];
let drDrillCalender;
let prCount = 0;
let drCount = 0;
let nearDrCount = 0;
let customDrCount = 0;
let TotalSiteArray = []
let PRSiteArray = []
let DRSiteArray = []
let NearDRSiteArray = []
let CustomDRSiteArray = []

showSpinner();

const tabs = document.querySelectorAll(".siteGroup");
tabs.forEach(tab =>
    tab.addEventListener("click", (e) => {
        tabs.forEach(btn => btn.ariaExpanded = "false");
        tab.ariaExpanded = "true";
        let tapstatus = e.currentTarget.getAttribute("tapstatus");
        let SiteConfiguredCount = $(e.currentTarget).find(".SiteConfigured").text();
        $("#siteTitle").text(tapstatus + " - " + SiteConfiguredCount);
    })
);

function setTextContent(element, value) {
    if (element) {
        element.textContent = value === 0 ? value : value ? value : "0";
    }
}

function showSpinner() {
    ['spinnerOverlayChartOne'] //'spinnerOverlayChartTwo', 'spinnerOverlayChartThree', 'spinnerOverlayChartFour'
        .forEach(id => {
            document.getElementById(id).style.display = 'flex';
        });

    const el = document.querySelector("chartOne");
    if (el) {       
        el.classList.add('opacity-75');
    }
}

function hideSpinner(id, classname) {
    const spinner = document.getElementById(id);
    const el = document.querySelector(classname);
    if (el && spinner) {
        spinner.style.display = 'none';
        el.classList.remove('opacity-75');
    }
}

$(document).ready(fetchAllData);

async function fetchAllData() {
    try {
        const results = await Promise.all([
            GetBusinessServiceDrReady(),
            GetSites(),
            GetBreachDetails(),
            GetLastDrillDetails(),
            GetOneViewEntitiesEventViewList(),
            GetOneViewRiskmitigationCyberSecurityList(),
            GetOneViewRiskmitigationFailedDrillList(),
            GetDrCalendarDrillEvents(),
            GetDcMappingSiteDetails(),
            GetTotalSiteDetailsForOneViewList("", 'collapseExample'),
            GetVerifiedWorkflowList(),
        ]);
        //const results = [
        //    await GetBusinessServiceDrReady(),
        //    await GetSites(),
        //    await GetBreachDetails(),
        //    await GetLastDrillDetails(),
        //    await GetOneViewEntitiesEventViewList(),
        //    await GetOneViewRiskmitigationCyberSecurityList(),
        //    await GetOneViewRiskmitigationFailedDrillList(),
        //    await GetDrCalendarDrillEvents(),
        //    await GetDcMappingSiteDetails(),
        //    await GetTotalSiteDetailsForOneViewList("", 'collapseExample'),
        //    await GetVerifiedWorkflowList(),
        //];
        let firstSite = document.querySelector(".siteGroup");

        if (firstSite) {
            firstSite.setAttribute("aria-expanded", "true"); // Set active state
        }
        hideSpinner('spinnerOverlayChartOne', '.chartOne.opacity-75')
        console.log("All functions executed successfully!", results);
    } catch (error) {
        hideSpinner('spinnerOverlayChartOne', '.chartOne.opacity-75')
        console.error("One or more functions failed!", error);
    }
}

function siteLocation(type) {
    return type === "DRSite" ? "cp-list-drsite" : type === "PRSite" ? "cp-list-prsite" : type === "NearDRSite" ? "cp-list-neardrsite" : "cp-custom";
}

function style(type) {
    return type === "DRSite" ? "#32b5ff" : type === "PRSite" ? "#6a56ff" : type === "NearDRSite" ? "#fe7948" : "#90EE90";
}

function parseDate(dateStr) {
    var parts = dateStr.split('T');
    var dateParts = parts[0].split('-');
    var timeParts = parts[1].split(':');
    var date = new Date(dateParts[0], dateParts[1] - 1, dateParts[2],
        timeParts[0], timeParts[1], timeParts[2]);
    return date;
}

function chartdata(mappingData) {
    mappingData.sort((a, b) => b.name.localeCompare(a.name));
    $(".cardSiteDetails").empty();
    globalMapData = [];
    globalMapDataLatLong = [];
    let siteImage = "";

    for (let x in mappingData) {
        let mapData = {};
        let latlong = {};
        let siteHtml = "";
        mapData.title = mappingData[x].location;
        mapData.latitude = parseFloat(mappingData[x].lat);
        mapData.longitude = parseFloat(mappingData[x].lng);
        mapData.dataTemperature = mappingData[x].dataTemperature;
        mapData.type = mappingData[x].type;
        latlong.latitude = parseFloat(mappingData[x].lat);
        latlong.longitude = parseFloat(mappingData[x].lng);
        mapData.Id = mappingData[x].id;

        if (x.toLowerCase() == 0) {
            mapData.CircleColor = "#f28a2e"
            mapData.color = "#f28a2e"
            siteImage = '<img class="mt-1" src="../../img/input_icons/service_available.svg" width="15" />'
            mapImage = '../../img/input_icons/service_available.svg'
        }
        else if (x.toLowerCase() == 1) {
            mapData.CircleColor = "#6f42c1"
            mapData.color = "#6f42c1"
            siteImage = '<img class="mt-1" src="../../img/input_icons/business_functions.svg" width="15" />'
            mapImage = '../../img/input_icons/business_functions.svg'
        }
        else if (x.toLowerCase() == "neardrsite") {
            mapData.CircleColor = "#00d3d1"
            mapData.color = "#00d3d1"
            siteImage = '<img class="mt-1" src="../../img/input_icons/dr_ready.svg" width="15" />'
            mapImage = '../../img/input_icons/dr_ready.svg'
        }
        else if (x.toLowerCase() == "customsite") {
            mapData.CircleColor = "#ffc107"
            mapData.color = "#ffc107"
            siteImage = '<img class="mt-1" src="../../img/input_icons/alerts.svg" width="15" />'
            mapImage = '../../img/input_icons/alerts.svg'
        }
        else {
            mapData.CircleColor = "#d63384"
            mapData.color = "#d63384"
            siteImage = '<img class="mt-1" src="../../img/input_icons/incidents.svg" width="15" />'
            mapImage = '../../img/input_icons/incidents.svg'
        }
        mapData.imageUrl = mapImage
        mapData.radius = 10;

        if (mapData?.latitude && mapData?.longitude) {
            globalMapData.push(mapData);
        }
        if (latlong?.latitude && latlong?.longitude) {
            globalMapDataLatLong.push(latlong);
        }
        siteHtml += '<div class="col-auto d-flex gap-2 align-items-start" >'
        siteHtml += '<div class="card shadow mb-2">'
        siteHtml += '<div class="card-body p-2 d-flex align-items-start gap-2">'
        siteHtml += siteImage
        siteHtml += '<div>'
        siteHtml += '<span class="fs-8 fw-semibold">' + x + '</span>'
        siteHtml += '<div class="">'
        siteHtml += '<small class="text-light">' + mappingData[x].Location + '</small>'
        //siteHtml += '<span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">1<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        $(".cardSiteDetails").append(siteHtml)
    }
    DCMapChart(globalMapData, globalMapDataLatLong)
}

function getReplicationStatusHtml(infraObject) {
    return infraObject?.state === "Active" ? '<i class="cp-replication-on" title="Active"></i>' : '<i class="cp-replication-Off" title="Maintenance"></i>';
}

function getStatusInfo(status) {
    let result = {};

    if (status && status.toLowerCase().replace(/\s/g, "") === "available") {
        result.statusIcon = "cp-success me-2";
        result.statusClass = "success";
        result.status = status;
    } else if (status && status.toLowerCase().replace(/\s/g, "") === "majorimpact") {
        result.statusIcon = "cp-error me-2";
        result.statusClass = "danger";
        result.status = status;
    } else {
        result.statusIcon = "cp-warning me-2";
        result.statusClass = "warning";
        result.status = "Not Available";
    }
    return result;
}

async function GetBusinessServiceDrReady() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetBusinessServiceDrReady",
        data: { id: '' },
        dataType: "json",
        success: function (result) {
            resiliencyData = [];
            //hideSpinner('spinnerOverlayChartOne', '.chartOne.opacity-75');
            if (result?.success) {
                let ready = 0;
                let notReady = 0;
                $.each(result?.data, function (index, item) {

                    if (item?.drReady === "1") {
                        ready++;
                    }
                    if (item?.drReady === "0") {
                        notReady++;
                    }
                });
                let resiliencyReadyitems = {
                    "country": "Ready",
                    "litres": ready,
                }
                let resiliencyNotReadyitems = {
                    "country": "Not Ready",
                    "litres": notReady,
                }
                $(".DRReadyCount").text(ready)
                $(".DRNotReadyCount").text(notReady)
                const readynessScore = ready != 0 ? (ready / ready + notReady) * 100 : 0
                $(".ReadinessScorePercentage").text(readynessScore + "%")
                resiliencyData.push(resiliencyReadyitems);
                resiliencyData.push(resiliencyNotReadyitems);
                siteDatas(resiliencyData);
            } else {
                errorNotification(result)
            }
        },
    });
}

async function GetSites() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetSites",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                let siteData = result?.data;
                const sortedData = siteData.sort((a, b) => b.name.localeCompare(a.name));
                $('.overallSites').empty();
                let dynamicSite = '';
                $.each(sortedData, function (index, item) {
                    let siteType = item?.type?.split(" ")?.join("");
                    dynamicSite += `<div class="d-flex align-items-center">
                                <div class="rounded-1 text-white p-2" style="background-color:${style(siteType)}">
                                    <i class="${siteLocation(siteType)}" title="${item?.name ? item?.name : "NA"}"></i>
                                </div>
                                <div class="ms-2">
                                    <div class="card-title">${item?.name ? item?.name : "NA"}</div>
                                    <span class="text-secondary">${item?.location ? item?.location : "NA"}</span>
                                </div>
                            </div>`
                });
                $('.overallSites').append(dynamicSite);
            } else {
                errorNotification(result);
            }
        },
    });
}

async function GetVerifiedWorkflowList() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetVerifiedWorkflowList",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                let totalWorkflowCount = result.data.totalWorkflowCount ? result.data.totalWorkflowCount : "NA"
                let workflowNotVerifyCount = result.data.workflowNotVerifyCount ? result.data.workflowNotVerifyCount : 0
                let workflowVerifyCount = result.data.workflowVerifyCount ? result.data.workflowVerifyCount : 0
                $(".totalWorkflowCount").text(totalWorkflowCount)
                let OrchestrationData = [{
                    "country": "Ready to execute",
                    "litres": workflowVerifyCount
                }, {
                    "country": "Not ready",
                    "litres": workflowNotVerifyCount
                }];
                AutomatonOrchestrationChart(OrchestrationData);
            } else {
                errorNotification(result)
            }
        },
    });
}

async function GetBreachDetails() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetBreachDetails",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                let RPOAchieved = result.data.rpoAchievedCount ? result.data.rpoAchievedCount : 0;
                let RTOAchieved = result.data.rtoAchievedCount ? result.data.rtoAchievedCount : 0;
                let RPOExceeded = result.data.rpoExceededCount ? result.data.rpoExceededCount : 0;
                let RTOExceeded = result.data.rtoExceededCount ? result.data.rtoExceededCount : 0;
                $("#RPOAchieved").text(RPOAchieved);
                $("#RTOAchieved").text(RTOAchieved);
                $("#RPOExceeded").text(RPOExceeded);
                $("#RTOExceeded").text(RTOExceeded);
            } else {
                errorNotification(result);
            }
        },
    });
}

async function GetLastDrillDetails() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetLastDrillDetails",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                let profileName = result?.data?.profileName ? result.data.profileName : "NA"
                let status = result?.data?.status ? result.data.status : "NA"
                let lastExecutionTime = result?.data?.lastExecutionTime ? new Date(result.data.lastExecutionTime).toISOString().replace("T", " ").split(".")[0] : "NA"
                let lastDrillDuration = result?.data?.duration ? result.data.duration : "NA"
                let drilStatus = status.toLowerCase() == 'success' ? "text-bg-success" : 'text-bg-danger'
                let dotStatus = status.toLowerCase() == 'success' ? "text-success" : 'text-danger'
                $("#lastDateDrillPerformance").text(lastExecutionTime)
                $("#lastDateDrillStatus").text(status)
                $("#lastDateDrillStatus").removeClass()
                $("#lastDateDrillStatus").addClass('badge rounded-pill fs-9 fw-normal ms-auto ' + drilStatus)
                $("#dotStatus").removeClass()
                $("#dotStatus").addClass("cp-single-dot fs-9 mt-1 " + dotStatus)
                $("#lastDateDrillName").text(profileName)
                $("#lastDrillDuration").text(lastDrillDuration)
            } else {
                errorNotification(result)
            }
        }
    });
}

async function GetOneViewEntitiesEventViewList() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetOneViewEntitiesEventViewList",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                $("#futureEvents").empty()
                result?.data?.forEach((data) => {
                    let lastModifiedDate = data?.lastModifiedDate ? new Date(data.lastModifiedDate).toISOString().replace("T", " ").split(".")[0] : "NA"
                    let entity = data?.entity ? data.entity : "NA"
                    let html = `<div class='carousel__item'>
                                        <div class='carousel__item-body  bg-white border-0 rounded-2'>
                                            <div class="d-flex gap-1">
                                                <i class="cp-single-dot fs-9 mt-1 text-warning"></i>
                                                <span>${lastModifiedDate}</span>
                                            </div>
                                            <div class="header">
                                                <div class="fw-semibold">${entity}</div>
                                                <span class="text-success">Notify</span>
                                            </div>
                                        </div>
                                    </div>`
                    $("#futureEvents").append(html)
                })
            } else {
                $("#futureEvents").empty();
                let noDataFound = `
                        <p class="fw-semibold text-center p-5 bg-white border-0 rounded-2">
                             No Data Found.!
                        </p>
                        `;
                $("#futureEvents").append(noDataFound);
            }
        }
    });
}

let chartData = []
async function GetOneViewRiskmitigationCyberSecurityList() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetOneViewRiskmitigationCyberSecurityList",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                result?.data?.forEach((data) => {
                    chartData.push({
                        "country": data?.status,
                        "litres": data?.statusPercentage
                    })
                })
                CyberSecurityChart(chartData)
            }
        }
    })
}

let failedChartData = []
async function GetOneViewRiskmitigationFailedDrillList() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetOneViewRiskmitigationFailedDrillList",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                result?.data.forEach((data) => {
                    let date = data?.actionDate.split("T")
                    failedChartData.push(
                        {
                            "country": date[0],
                            "visits": data?.failedCount
                        }
                    )
                })
                failoverDrillsChart(failedChartData)
            }
        }
    })
}

async function GetTotalSiteDetailsForOneViewList(d, id) {
    let element = document.getElementById(id);
    element.classList.add("show");

    if (!element.classList.contains("show")) {
        element.classList.add("show");
    }
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetTotalSiteDetailsForOneViewList",
        data: { categoryType: d },
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                let datas = result?.data

                if (result?.success == true) {
                    $("#sitedetails").empty()
                    if (datas?.serverTypeWithListVms.length == 0) {
                        $("#sitedetails").html(`<div style="text-align: center; width: 100%;">No Data Found</div>`);
                    } else {
                        datas?.serverTypeWithListVms?.sort((a, b) => a.roleType.localeCompare(b.roleType))?.forEach((x, i) => {
                            $("#sitedetails").append(`  <div class="col">
                                                     <div>
                                                         <div class="d-flex align-items-start">
                                                           <div role="button" type="${x?.roleType}">
                                                                 <img class="me-1" src="/img/charts_img/datacenter/server.svg" loading="lazy" type="${x?.roleType}" height="35" alt="${x?.roleType} count" title="${x?.roleType} count">
                                                             </div>
                                                             <span>
                                                                 <span>${x?.roleType}</span><span class="fw-semibold ms-2">${x.serverUpCount + x.serverDownCount}</span>
                                                                 <span class="d-flex align-items-center gap-2">
                                                                     <span class="fs-6">
                                                                         ${x.serverUpCount}<i class="cp-up-linearrow ms-1 text-success fs-8 align-middle"></i>
                                                                     </span>
                                                                     <span class="fs-6">
                                                                         ${x.serverDownCount}<i class="cp-down-linearrow ms-1 text-danger fs-8 align-middle"></i>
                                                                     </span>
                                                                 </span>
                                                             </span>
                                                         </div>
                                                     </div>
                                                 </div>`)
                        })
                    }
                }
            }
        }
    })
}

async function GetDcMappingSiteDetails() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetDcMappingSiteDetails",
        dataType: "json",
        success: function (result) {

            if (result?.success) {
                $(".totalSite").text(result?.data?.totalSiteCount);
                $(".totalApp").text(result?.data?.totalAppCount);
                $("#totalSiteConfigured").text(result?.data?.totalSiteCount);
                $("#totalSiteTitleCount").text(result?.data?.totalSiteCount);
                let html = "";
                let bodyHtml = "";

                if (result?.data?.dcMappingSites && result?.data?.dcMappingSites?.length != 0) {
                    chartdata(result?.data?.dcMappingSites);
                    TotalSiteArray = []
                    PRSiteArray = []
                    DRSiteArray = []
                    NearDRSiteArray = []
                    CustomDRSiteArray = []
                    result?.data?.dcMappingSites?.forEach((data) => {
                        TotalSiteArray.push(data.id)
                        switch (data?.type?.toLowerCase()) {
                            case "neardr":
                                NearDRSiteArray.push(data?.id)
                                nearDrCount++;
                                break;
                            case 'drsite':
                                DRSiteArray.push(data?.id)
                                drCount++;
                                break;
                            case 'prsite':
                                PRSiteArray.push(data?.id)
                                prCount++;
                                break;
                            default:
                                CustomDRSiteArray.push(data?.id)
                                customDrCount++;
                                break;
                        }
                    });
                }
                $("#totalPRProfiles").text(prCount)
                $("#totalDRProfiles").text(drCount)
                $("#totalNearDRProfiles").text(nearDrCount)
                $("#totalCustomDRProfiles").text(customDrCount)
            } else {
                errorNotification(result);
            }
        },
    });
}

async function GetDrCalendarDrillEvents() {
    return await $.ajax({
        type: "GET",
        async: false,
        url: RootUrl + "Dashboard/ResiliencyMapping/GetDrCalendarDrillEvents",
        dataType: "json",
        success: function (result) {

            if (result?.success) {

                if (result?.data?.getUpcomingDrillDetailVm?.length !== 0) {
                    let myDate;
                    let profileNameList
                    let currentMonth
                    let currentday
                    let currentFullYear
                    let currentMinutes
                    let upperampm
                    let currentFullDayName;

                    if (result?.data?.getUpcomingDrillDetailVm[0]?.scheduledStartDate) {
                        myDate = parseDate(result.data?.getUpcomingDrillDetailVm[0]?.scheduledStartDate);
                        profileNameList = result.data?.getUpcomingDrillDetailVm[0].responsibilityName;
                        currentMonth = myDate.getUTCMonth()
                        currentday = myDate.getUTCDate()
                        currentFullYear = myDate.getUTCFullYear()
                        currentHours = myDate.getHours();
                        currentHours = ("0" + currentHours).slice(-2);
                        currentMinutes = myDate.getMinutes();
                        currentMinutes = ("0" + currentMinutes).slice(-2);
                        currentFullDayName = myDate.toLocaleDateString('en-US', { weekday: 'long' })
                        let ampm = currentHours >= 12 ? 'pm' : 'am';
                        upperampm = ampm.toUpperCase()
                    }
                    else {
                        profileNameList = []
                        currentMonth = 0
                        currentday = 0
                        currentFullYear = 0
                        currentHours = 0
                        currentMinutes = 0
                        upperampm = 0
                        currentFullDayName = "NA"
                    }
                    if (currentHours == "00" && currentMinutes == "00") {
                        $("#hour").text("12")
                        $("#minutes").text("00")
                    }
                    else {
                        $("#hour").text(currentHours)
                        $("#minutes").text(currentMinutes)
                    }
                    $("#period").text(" " + upperampm)
                    $("#fullDay").text(currentFullDayName)
                    $("#fullDate").text(currentday + "-" + currentMonth + "-" + currentFullYear)
                    $("#totalDrillPlanned").empty();
                    $("#completed").empty();
                    $("#notCompleted").empty();
                    $("#totalDrillPlanned").html(result?.data?.total);
                    $("#completed").html(result?.data?.success);
                    $("#notCompleted").html(result?.data?.failure);
                    $("#ProfileImageList").empty();

                    if (profileNameList.length != 0) {
                        profileNameList.forEach(function (name) {
                            let listItem = `<li title=${name}><img src="../../img/profile-img/dave.png" alt=${name} /></li>`;
                            $("#ProfileImageList").append(listItem);
                        });
                    }
                }
            } else {
                errorNotification(result);
            }
        },
    });
}
