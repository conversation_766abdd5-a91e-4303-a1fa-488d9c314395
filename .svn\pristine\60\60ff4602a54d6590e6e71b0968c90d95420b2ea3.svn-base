using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class NodeFixture : IDisposable
{
    public List<Node> NodePaginationList { get; set; }
    public List<Node> NodeList { get; set; }
    public Node NodeDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public NodeFixture()
    {
        var fixture = new Fixture();

        NodeList = fixture.Create<List<Node>>();

        NodePaginationList = fixture.CreateMany<Node>(20).ToList();

        NodePaginationList.ForEach(x => x.CompanyId = CompanyId);

        NodeList.ForEach(x => x.CompanyId = CompanyId);

        NodeDto = fixture.Create<Node>();

        NodeDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
