﻿//using Moq;
//using Microsoft.AspNetCore.Mvc;
//using ContinuityPatrol.Web.Areas.Configuration.Controllers;
//using ContinuityPatrol.Shared.Core.Responses;
//using ContinuityPatrol.Shared.Services.Provider;
//using ContinuityPatrol.Application.Features.DataSync.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.Features.DataSync.Commands.Create;
//using ContinuityPatrol.Application.Features.DataSync.Commands.Update;
//using ContinuityPatrol.Domain.ViewModels.DataSyncModel;
//using AutoMapper;
//using Microsoft.Extensions.Logging;
//using MediatR;
//using AutoFixture;
//using ContinuityPatrol.Shared.Core.Wrapper;
//using ContinuityPatrol.Shared.Tests.Fakes;
//using Microsoft.AspNetCore.Http;

//namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
//{
//    public class DataSyncPropertiesControllerTests
//    {
//        private readonly Mock<IPublisher> _mockPublisher = new();
//        private readonly Mock<ILogger<DataSyncPropertiesController>> _mockLogger = new();
//        private readonly Mock<IMapper> _mockMapper = new();
//        private readonly Mock<IDataProvider> _mockDataProvider = new();
//        private  DataSyncPropertiesController _controller;

//        public DataSyncPropertiesControllerTests()
//        {
//            Initialize();
//        }
//        internal void Initialize()
//        {
//            _controller = new DataSyncPropertiesController(
//                _mockPublisher.Object,
//                _mockLogger.Object,
//                _mockMapper.Object,
//                _mockDataProvider.Object
//            );
//            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
//            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
//        }
//        [Fact]
//        public async Task List_ShouldReturnViewResult()
//        {
//            var result = await _controller.List() as ViewResult;

//            Assert.NotNull(result);
//        }

//        [Fact]
//        public async Task GetPaginated_ShouldReturn_JsonResult()
//        {
//            var query = new Fixture().Create<GetDataSyncPaginatedListQuery>();
            
//            var paginatedList = new PaginatedResult<DataSyncListVm>();
//            _mockDataProvider.Setup(dp => dp.DataSync.GetPaginatedDataSyncs(query))
//                             .ReturnsAsync(paginatedList);

            
//            var result = await _controller.GetPaginated(query) as JsonResult;
            
//            Assert.NotNull(result);
//            var data = Assert.IsType<JsonResult>(result);
            
//        }

//        [Fact]
//        public async Task CreateOrUpdate_WithNewDataSync_ShouldReturn_RedirectToAction()
//        {

//            var viewModel = new Fixture().Create<DataSyncViewModel>();
//            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
//            dic.Add("id", "");
//            var collection = new FormCollection(dic);
//            _controller.Request.Form = collection;
            
//            var command = new CreateDataSyncCommand();
//            var response = new BaseResponse { Success = true, Message = "Created successfully" };

//            _mockMapper.Setup(m => m.Map<CreateDataSyncCommand>(viewModel)).Returns(command);
//            _mockDataProvider.Setup(dp => dp.DataSync.CreateAsync(command)).ReturnsAsync(response);
//            _mockDataProvider.Setup(dp => dp.DataSync.UpdateAsync(It.IsAny<UpdateDataSyncCommand>())).ReturnsAsync(new BaseResponse { Success = true, Message = "Updated successfully" });

//            var result = await _controller.CreateOrUpdate(viewModel) as RedirectToActionResult;

            
//            Assert.NotNull(result);
//            Assert.Equal("List", result.ActionName);
//        }
//        [Fact]
//        public async Task DeleteAsync_WhileReturn_RedirectToAction()
//        {
            
//            var id = "someId";
//            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

//            _mockDataProvider.Setup(dp => dp.DataSync.DeleteAsync(id)).ReturnsAsync(response);

            
//            var result = await _controller.DeleteAsync(id) as RedirectToActionResult;

            
//            Assert.NotNull(result);
//            Assert.Equal("List", result.ActionName);
//        }

//        [Fact]
//        public async Task IsDataSyncNameExist_ShouldReturn_Bool()
//        {
            
//            var name = "syncName";
//            var id = "someId";
//            _mockDataProvider.Setup(dp => dp.DataSync.IsDataSyncNameExist(name, id)).ReturnsAsync(true);

            
//            var result = await _controller.IsDataSyncNameExist(name, id);

            
//            Assert.True(result);
//        }

        
        
//    }
//}
