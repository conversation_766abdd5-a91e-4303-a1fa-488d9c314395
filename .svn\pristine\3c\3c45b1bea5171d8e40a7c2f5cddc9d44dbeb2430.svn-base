﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetdetailByName;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetNames;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class BusinessServiceService : BaseService, IBusinessServiceService
{
    public BusinessServiceService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateBusinessServiceCommand businessService)
    {
        Logger.LogDebug($"Creating OperationalService '{businessService.Name}'");

        return await Mediator.Send(businessService);
    }

    public async Task<BaseResponse> DeleteAsync(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "OperationalService Id");

        Logger.LogDebug($"Deleting OperationalService Details by Id '{businessServiceId}'");

        return await Mediator.Send(new DeleteBusinessServiceCommand { Id = businessServiceId });
    }

    public async Task<List<BusinessServiceNameVm>> GetBusinessServiceNames()
    {
        Logger.LogDebug("Get All OperationalService Names");

        return await Mediator.Send(new BusinessServiceNameQuery());
    }

    public async Task<PaginatedResult<BusinessServiceListVm>> GetBusinessServicePaginatedList(
        GetBusinessServicePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in OperationalService Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<BusinessServiceDetailVm> GetByReferenceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "OperationalService Id");

        Logger.LogDebug($"Get OperationalService Detail by Id '{businessServiceId}'");

        return await Mediator.Send(new GetBusinessServiceDetailQuery { Id = businessServiceId });
    }

    public async Task<bool> IsBusinessServiceNameExist(string name, string id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Name");

        Logger.LogDebug($"Check Name Exists Detail by Name '{name}'and Id '{id}'");

        return await Mediator.Send(new GetBusinessServiceNameUniqueQuery
            { BusinessServiceName = name, BusinessServiceId = id });
    }

    public async Task<List<BusinessServiceListVm>> GetBusinessServiceList()
    {
        Logger.LogDebug("Get All OperationalServices");

        return await Mediator.Send(new GetBusinessServiceListQuery());
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBusinessServiceCommand businessService)
    {
        Logger.LogDebug($"Updating OperationalService '{businessService.Name}'");

        return await Mediator.Send(businessService);
    }

    public async Task<GetBusinessServiceDiagramDetailVm> GetBusinessServiceDiagramByBusinessServiceId(
        string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "OperationalService Id");

        Logger.LogDebug($"Get OperationalService Diagram details by OperationalService Id '{businessServiceId}'");

        return await Mediator.Send(new GetBusinessServiceDiagramDetailQuery { BusinessServiceId = businessServiceId });
    }

    public async Task<BusinessServiceDetailVm> GetByBusinessServiceName(string businessServiceName)
    {
        Logger.LogDebug($"Get Operational Service Detail by Name '{businessServiceName}'");

        return await Mediator.Send(new GetBusinessServiceDetailByNameQuery { Name = businessServiceName });
    }
}