using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Commands;

public class UpdateBulkImportActionResultTests : IClassFixture<BulkImportActionResultFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly UpdateBulkImportActionResultCommandHandler _handler;

    public UpdateBulkImportActionResultTests(BulkImportActionResultFixture bulkImportActionResultFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;

        _mockBulkImportActionResultRepository = BulkImportActionResultRepositoryMocks.CreateUpdateBulkImportActionResultRepository(_bulkImportActionResultFixture.BulkImportActionResults);
        _mockMapper = new Mock<IMapper>();
        _mockPublisher = new Mock<IPublisher>();

        // Setup mapper
        _mockMapper.Setup(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()))
            .Callback<object, object, Type, Type>((source, destination, srcType, destType) =>
            {
                if (source is UpdateBulkImportActionResultCommand cmd && destination is Domain.Entities.BulkImportActionResult entity)
                {
                    entity.CompanyId = cmd.CompanyId;
                    entity.NodeId = cmd.NodeId;
                    entity.NodeName = cmd.NodeName;
                    entity.BulkImportOperationId = cmd.BulkImportOperationId;
                    entity.BulkImportOperationGroupId = cmd.BulkImportOperationGroupId;
                    entity.ConditionalOperation = cmd.ConditionalOperation;
                    entity.EntityId = cmd.EntityId;
                    entity.EntityName = cmd.EntityName;
                    entity.EntityType = cmd.EntityType;
                    entity.Status = cmd.Status;
                    entity.StartTime = cmd.StartTime;
                    entity.EndTime = cmd.EndTime;
                    entity.ErrorMessage = cmd.ErrorMessage;
                }
            });

        //// Setup default repository behaviors
        //_mockBulkImportActionResultRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
        //    .Returns(Task.CompletedTask);

        _handler = new UpdateBulkImportActionResultCommandHandler(
            _mockMapper.Object,
            _mockBulkImportActionResultRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_UpdateBulkImportActionResultResponse_When_BulkImportActionResultUpdated()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand 
        { 
            Id = existingResult.ReferenceId,
            EntityName = "UpdatedEntity"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(UpdateBulkImportActionResultResponse));
        result.Id.ShouldBe(existingResult.ReferenceId);
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsync_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_OnlyOnce()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map(It.IsAny<object>(), It.IsAny<object>(),
            It.IsAny<Type>(), It.IsAny<Type>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportActionResultNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportActionResultCommand { Id = nonExistentId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportActionResult)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_UpdateEntityProperties_When_CommandMapped()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand 
        { 
            Id = existingResult.ReferenceId,
            CompanyId = "UpdatedCompanyId",
            NodeId = "UpdatedNode",
            NodeName = "UpdatedNodeName",
            EntityId = "UpdatedEntityId",
            EntityName = "UpdatedEntity",
            EntityType = "Database",
            Status = "Completed",
            ConditionalOperation = 2,
            ErrorMessage = "No errors"
        };

        Domain.Entities.BulkImportActionResult capturedEntity = null;

        _mockBulkImportActionResultRepository
          .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
          .Callback<Domain.Entities.BulkImportActionResult>(entity => capturedEntity = entity)
          .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        capturedEntity.ShouldNotBeNull();
        
    }

    [Fact]
    public async Task Handle_CreateResponseWithCorrectProperties_When_BulkImportActionResultUpdated()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        existingResult.EntityName = "TestEntity";
        var command = new UpdateBulkImportActionResultCommand 
        { 
            Id = existingResult.ReferenceId,
            EntityName = "TestEntity"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Id.ShouldBe(existingResult.ReferenceId);
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new UpdateBulkImportActionResultCommand { Id = testId };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportActionResultFixture.BulkImportActionResults.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportActionResultRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_UpdateSuccessful()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand { Id = existingResult.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<UpdateBulkImportActionResultResponse>();
        result.GetType().ShouldBe(typeof(UpdateBulkImportActionResultResponse));
    }

   

    [Fact]
    public async Task Handle_UpdateOperationIds_When_CommandProvided()
    {
        // Arrange
        var existingResult = _bulkImportActionResultFixture.BulkImportActionResults.First();
        var command = new UpdateBulkImportActionResultCommand 
        { 
            Id = existingResult.ReferenceId,
            BulkImportOperationId = "NewOperationId",
            BulkImportOperationGroupId = "NewGroupId"
        };

        _mockBulkImportActionResultRepository
           .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BulkImportActionResult>()))
           .Callback<Domain.Entities.BulkImportActionResult>(entity => existingResult = entity)
           .ReturnsAsync((Domain.Entities.BulkImportActionResult entity) => entity);

        // Act
        await _handler.Handle(command, CancellationToken.None);

    }
}
