using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AlertMasterFixture : IDisposable
{
    public List<AlertMaster> AlertMasterPaginationList { get; set; }
    public List<AlertMaster> AlertMasterList { get; set; }
    public AlertMaster AlertMasterDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public AlertMasterFixture()
    {
        var fixture = new Fixture();

        AlertMasterList = fixture.Create<List<AlertMaster>>();

        AlertMasterList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertMasterList.ForEach(x => x.IsActive = true);

        AlertMasterPaginationList = fixture.CreateMany<AlertMaster>(20).ToList();
        AlertMasterPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AlertMasterPaginationList.ForEach(x => x.IsActive = true);

        AlertMasterDto = fixture.Create<AlertMaster>();
        AlertMasterDto.ReferenceId = Guid.NewGuid().ToString();
        AlertMasterDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
