using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DataLagRepositoryTests : IClassFixture<DataLagFixture>
{
    private readonly DataLagFixture _dataLagFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DataLagRepository _repository;
    private readonly DataLagRepository _repositoryNotParent;

    public DataLagRepositoryTests(DataLagFixture dataLagFixture)
    {
        _dataLagFixture = dataLagFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DataLagRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new DataLagRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;

        // Act
        var result = await _repository.AddAsync(dataLag);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataLag.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(dataLag.BusinessServiceName, result.BusinessServiceName);
        Assert.Single(_dbContext.DataLags);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        await _repository.AddAsync(dataLag);

        dataLag.BusinessServiceName = "Updated Service Name";

        // Act
        var result = await _repository.UpdateAsync(dataLag);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service Name", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        await _repository.AddAsync(dataLag);

        // Act
        var result = await _repository.DeleteAsync(dataLag);

        // Assert
        Assert.Equal(dataLag.BusinessServiceId, result.BusinessServiceId);
        Assert.Empty(_dbContext.DataLags);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        var addedEntity = await _repository.AddAsync(dataLag);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        await _repository.AddAsync(dataLag);

        // Act
        var result = await _repository.GetByReferenceIdAsync(dataLag.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataLag.ReferenceId, result.ReferenceId);
        Assert.Equal(dataLag.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var dataLags = _dataLagFixture.DataLagList;
        await _repository.AddRangeAsync(dataLags);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(dataLags.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetDataLagByBusinessServiceId Tests

    [Fact]
    public async Task GetDataLagByBusinessServiceId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        await _repository.AddAsync(dataLag);

        // Act
        var result = await _repository.GetDataLagByBusinessServiceId(DataLagFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DataLagFixture.BusinessServiceId, result.BusinessServiceId);
        Assert.Equal(dataLag.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(dataLag.TotalBusinessFunction, result.TotalBusinessFunction);
        Assert.Equal(dataLag.TotalInfraObject, result.TotalInfraObject);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var dataLags = _dataLagFixture.DataLagList;
        await _repository.AddRangeAsync(dataLags);

        // Act
        var result = await _repository.GetDataLagByBusinessServiceId("non-existent-service-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_ShouldReturnOnlyActiveEntity()
    {
        // Arrange 
        var datalag = _dataLagFixture.DataLagList;
        var dataLag1 = datalag[0];
        
        var dataLag2 = datalag[1];
     
        dataLag2.BusinessServiceId = DataLagFixture.BusinessServiceId;
        
        await _dbContext.DataLags.AddAsync(dataLag1);
        await _dbContext.DataLags.AddAsync(dataLag2);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.GetDataLagByBusinessServiceId(DataLagFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.IsActive);
        Assert.Equal(dataLag1.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_ShouldReturnCorrectDataLagMetrics()
    {
        // Arrange
        var dataLag = _dataLagFixture.DataLagDto;
        dataLag.TotalBusinessFunction = 10;
        dataLag.BFAvailable = 8;
        dataLag.BFImpact = 1;
        dataLag.BFExceed = 1;
        dataLag.TotalInfraObject = 20;
        dataLag.InfraAvailable = 18;
        dataLag.InfraImpact = 1;
        dataLag.InfraExceed = 1;
        
        await _repository.AddAsync(dataLag);

        // Act
        var result = await _repository.GetDataLagByBusinessServiceId(DataLagFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(10, result.TotalBusinessFunction);
        Assert.Equal(8, result.BFAvailable);
        Assert.Equal(1, result.BFImpact);
        Assert.Equal(1, result.BFExceed);
        Assert.Equal(20, result.TotalInfraObject);
        Assert.Equal(18, result.InfraAvailable);
        Assert.Equal(1, result.InfraImpact);
        Assert.Equal(1, result.InfraExceed);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var dataLags = _dataLagFixture.DataLagList;

        // Act
        var result = await _repository.AddRangeAsync(dataLags);

        // Assert
        Assert.Equal(dataLags.Count, result.Count());
        Assert.Equal(dataLags.Count, _dbContext.DataLags.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var dataLags = _dataLagFixture.DataLagList;
        await _repository.AddRangeAsync(dataLags);

        // Act
        var result = await _repository.RemoveRangeAsync(dataLags);

        // Assert
        Assert.Equal(dataLags.Count, result.Count());
        Assert.Empty(_dbContext.DataLags);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var dataLags = _dataLagFixture.DataLagList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(dataLags);
        var initialCount = dataLags.Count;
        
        var toUpdate = dataLags.Take(2).ToList();
        toUpdate.ForEach(x => x.BusinessServiceName = "UpdatedServiceName");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = dataLags.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.BusinessServiceName == "UpdatedServiceName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
