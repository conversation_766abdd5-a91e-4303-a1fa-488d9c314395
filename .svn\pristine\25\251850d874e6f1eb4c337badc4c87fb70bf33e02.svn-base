using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Delete;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetDetail;
using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.IncidentManagementSummaryModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
//using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetNameUnique;
//using ContinuityPatrol.Application.Features.IncidentManagementSummary.Queries.GetPaginatedList;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class IncidentManagementSummaryService : BaseService, IIncidentManagementSummaryService
{
    public IncidentManagementSummaryService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<IncidentManagementSummaryListVm>> GetIncidentManagementSummaryList()
    {
        Logger.LogDebug("Get All IncidentManagementSummaries");

        return await Mediator.Send(new GetIncidentManagementSummaryListQuery());
    }

    public async Task<IncidentManagementSummaryDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentManagementSummary Id");

        Logger.LogDebug($"Get IncidentManagementSummary Detail by Id '{id}'");

        return await Mediator.Send(new GetIncidentManagementSummaryDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(
        CreateIncidentManagementSummaryCommand createIncidentManagementSummaryCommand)
    {
        Logger.LogDebug($"Create IncidentManagementSummary '{createIncidentManagementSummaryCommand}'");

        return await Mediator.Send(createIncidentManagementSummaryCommand);
    }

    public async Task<BaseResponse> UpdateAsync(
        UpdateIncidentManagementSummaryCommand updateIncidentManagementSummaryCommand)
    {
        Logger.LogDebug($"Update IncidentManagementSummary '{updateIncidentManagementSummaryCommand}'");

        return await Mediator.Send(updateIncidentManagementSummaryCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "IncidentManagementSummary Id");

        Logger.LogDebug($"Delete IncidentManagementSummary Details by Id '{id}'");

        return await Mediator.Send(new DeleteIncidentManagementSummaryCommand { Id = id });
    }

    #region NameExist

    // public async Task<bool> IsIncidentManagementSummaryNameExist(string name, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(name, "IncidentManagementSummary Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by IncidentManagementSummary Name '{name}' and Id '{id}'");
    //
    //     return await Mediator.Send(new GetIncidentManagementSummaryNameUniqueQuery { Name = name, Id = id });
    // }

    #endregion

    #region Paginated

    //public async Task<PaginatedResult<IncidentManagementSummaryListVm>> GetPaginatedIncidentManagementSummarys(GetIncidentManagementSummaryPaginatedListQuery query)
    //{
    //    Logger.LogDebug("Get Searching Details in IncidentManagementSummary Paginated List");
    //
    //    return await Mediator.Send(query);
    //}

    #endregion
}