﻿using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetailByCode;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Queries;

public class GetAlertInformationDetailByCodeQueryHandlerTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly GetAlertInformationDetailByCodeQueryHandler _handler;

    public GetAlertInformationDetailByCodeQueryHandlerTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetAlertInformationByCodeRepository(_alertInformationFixture.AlertInformations);
        _handler = new GetAlertInformationDetailByCodeQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);
    }

    [Fact]
    public async Task Handle_ReturnAlertInformation_When_ValidCode()
    {
        var result = await _handler.Handle(new GetAlertInformationDetailByCodeQuery { Code = _alertInformationFixture.AlertInformations[0].Code }, CancellationToken.None);

        result.ShouldBeOfType<List<AlertInformationDetailByCodeVm>>();

        result.Count.ShouldBe(3);

        result[0].Id.ShouldBe(_alertInformationFixture.AlertInformations[0].ReferenceId);
        result[0].AlertFrequency.ShouldBe(_alertInformationFixture.AlertInformations[0].AlertFrequency);
        result[0].Code.ShouldBe(_alertInformationFixture.AlertInformations[0].Code);
        result[0].Severity.ShouldBe(_alertInformationFixture.AlertInformations[0].Severity);
        result[0].Type.ShouldBe(_alertInformationFixture.AlertInformations[0].Type);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidAlertInformationCode()
    {
        _alertInformationFixture.AlertInformations[0].Code = null;
        _alertInformationFixture.AlertInformations[0].Code = null;
        _alertInformationFixture.AlertInformations[0].Code = null;

        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetAlertInformationEmptyRepository();

        var handler = new GetAlertInformationDetailByCodeQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);

        var result = await Assert.ThrowsAsync<NotFoundException>(() => handler.Handle(new GetAlertInformationDetailByCodeQuery{Code = _alertInformationFixture.AlertInformations[0].Code }, CancellationToken.None));
        
        result.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetAccessManagerByUserIdMethod_OneTime()
    {
        await _handler.Handle(new GetAlertInformationDetailByCodeQuery { Code = _alertInformationFixture.AlertInformations[0].Code }, CancellationToken.None);

        _mockAlertInformationRepository.Verify(x=>x.GetAlertInformationByCode(It.IsAny<string>()), Times.Once);
    }   

    [Fact]
    public async Task Handle_Returns_EmptyList_When_No_Active_Records()
    {  
        var inactiveAlerts = _alertInformationFixture.AlertInformations
            .Select(a => { a.IsActive = false; return a; }).ToList();

        var mockRepo = new Mock<IAlertInformationRepository>();

        mockRepo.Setup(repo => repo.GetAlertInformationByCode(It.IsAny<string>()))
            .ReturnsAsync(inactiveAlerts.Where(a => a.IsActive).ToList()); 

        var handler = new GetAlertInformationDetailByCodeQueryHandler(mockRepo.Object, _alertInformationFixture.Mapper);

        var result = await handler.Handle(new GetAlertInformationDetailByCodeQuery
        {
            Code = _alertInformationFixture.AlertInformations[0].Code
        }, CancellationToken.None);

        result.ShouldBeEmpty();
    }

}
