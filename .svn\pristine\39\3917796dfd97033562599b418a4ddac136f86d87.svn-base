﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class EMCSTARMYSQLFullDBControllerShould
    {
        private readonly EMCSTARMYSQLFullDBController _controller;

        public EMCSTARMYSQLFullDBControllerShould()
        {
            _controller = new EMCSTARMYSQLFullDBController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
