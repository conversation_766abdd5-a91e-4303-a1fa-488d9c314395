﻿using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class UserInfraObjectService : BaseService, IUserInfraObjectService
{
    public UserInfraObjectService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<GetByUserIdAndPropertiesVm>> GetByUserIdAndProperties()
    {
        Logger.LogDebug("Get All UserInfraObject Names");

        return await Mediator.Send(new GetByUserIdAndPropertiesQuery());
    }

    public async Task<GetUserInfraObjectByUserIdVm> GetUserById(string userId)
    {
        Guard.Against.InvalidGuidOrEmpty(userId, "UserInfraObject UserId");

        Logger.LogDebug($"Get UserInfraObject Detail by userId  '{userId}'");

        return await Mediator.Send(new GetUserInfraObjectByUserIdQuery { UserId = userId });
    }

    public async Task<GetUserInfraObjectByBusinessServiceVm> GetUserInfraObjects(string? companyId)
    {
        Logger.LogDebug("Get UserInfraObject by Business Services");

        return await Mediator.Send(new GetUserInfraObjectByBusinessServiceQuery { CompanyId = companyId });
    }
}