using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormFixture : IDisposable
{
    public List<Form> FormPaginationList { get; set; }
    public List<Form> FormList { get; set; }
    public Form FormDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FormFixture()
    {
        var fixture = new Fixture();

        FormList = fixture.Create<List<Form>>();

        FormPaginationList = fixture.CreateMany<Form>(20).ToList();

        FormPaginationList.ForEach(x => x.CompanyId = CompanyId);
        FormPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormPaginationList.ForEach(x => x.IsActive = true);

        FormList.ForEach(x => x.CompanyId = CompanyId);
        FormList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormList.ForEach(x => x.IsActive = true);

        FormDto = fixture.Create<Form>();
        FormDto.CompanyId = CompanyId;
        FormDto.ReferenceId = Guid.NewGuid().ToString();
        FormDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
