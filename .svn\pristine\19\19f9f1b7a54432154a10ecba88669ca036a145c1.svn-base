﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Events.Delete;

namespace ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Delete;

public class
    DeleteInfraObjectSchedulerCommandHandler : IRequestHandler<DeleteInfraObjectSchedulerCommand,
        DeleteInfraObjectSchedulerResponse>
{
    private readonly IInfraObjectSchedulerRepository _infraObjectSchedulerRepository;
    private readonly IPublisher _publisher;

    public DeleteInfraObjectSchedulerCommandHandler(IInfraObjectSchedulerRepository infraObjectSchedulerRepository,
        IPublisher publisher)
    {
        _infraObjectSchedulerRepository = infraObjectSchedulerRepository;
        _publisher = publisher;
    }

    public async Task<DeleteInfraObjectSchedulerResponse> Handle(DeleteInfraObjectSchedulerCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _infraObjectSchedulerRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.InfraObjectScheduler),
            new NotFoundException(nameof(Domain.Entities.InfraObjectScheduler), request.Id));

        eventToDelete.IsActive = false;

        await _infraObjectSchedulerRepository.UpdateAsync(eventToDelete);

        var response = new DeleteInfraObjectSchedulerResponse
        {
            Message = Message.Delete("Manage Resiliency Readiness", eventToDelete.InfraObjectName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(
            new InfraObjectSchedulerDeletedEvent { InfraObjectName = eventToDelete.InfraObjectName },
            cancellationToken);

        return response;
    }
}