﻿namespace ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetNameUnique;

public class GetReportScheduleNameUniqueQueryHandler : IRequestHandler<GetReportScheduleNameUniqueQuery, bool>
{
    private readonly IMapper _mapper;
    private readonly IReportScheduleRepository _reportScheduleRepository;

    public GetReportScheduleNameUniqueQueryHandler(IMapper mapper, IReportScheduleRepository reportScheduleRepository)
    {
        _mapper = mapper;
        _reportScheduleRepository = reportScheduleRepository;
    }

    public async Task<bool> Handle(GetReportScheduleNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _reportScheduleRepository.IsReportScheduleNameExist(request.ReportName, request.ReportId);
    }
}