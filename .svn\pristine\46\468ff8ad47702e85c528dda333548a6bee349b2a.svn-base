﻿namespace ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;

public class GetUserInfraObjectByBusinessServiceQueryHandler : IRequestHandler<GetUserInfraObjectByBusinessServiceQuery,
    GetUserInfraObjectByBusinessServiceVm>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;

    public GetUserInfraObjectByBusinessServiceQueryHandler(IMapper mapper,
        IBusinessFunctionRepository businessFunctionRepository, IBusinessServiceRepository businessServiceRepository,
        IInfraObjectRepository infraObjectRepository)
    {
        _mapper = mapper;
        _businessFunctionRepository = businessFunctionRepository;
        _businessServiceRepository = businessServiceRepository;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<GetUserInfraObjectByBusinessServiceVm> Handle(GetUserInfraObjectByBusinessServiceQuery request,
        CancellationToken cancellationToken)
    {
        var userInfraObjectByBusinessServiceVm = new GetUserInfraObjectByBusinessServiceVm();

        var businessServices = await _businessServiceRepository.GetBusinessServiceNames();

        if (request.CompanyId.IsNotNullOrEmpty())
            businessServices = businessServices.Where(x => x.CompanyId.Equals(request.CompanyId)).ToList();

        var businessServiceDto = _mapper.Map<List<AssignedBusinessServices>>(businessServices);

        var businessServiceIds = businessServiceDto.Select(x => x.Id).ToList();

        var businessFunctions = await _businessFunctionRepository.GetByBusinessServiceIds(businessServiceIds);

        var businessFunctionDto = _mapper.Map<List<AssignedBusinessFunctions>>(businessFunctions);

        var businessFunctionIds = businessFunctionDto.Where(x=>x.Id.IsNotNullOrWhiteSpace()).Select(x => x.Id).ToList();

        var infraObjects = await _infraObjectRepository.GetByBusinessFunctionIds(businessFunctionIds);

        var infraObjectDto = _mapper.Map<List<AssignedInfraObjects>>(infraObjects);


        businessFunctionDto.ForEach(bf =>
            bf.AssignedInfraObjects.AddRange(infraObjectDto.Where(infra => infra.BusinessFunctionId.IsNotNullOrWhiteSpace() && infra.BusinessFunctionId.Equals(bf.Id))));


        businessServiceDto.ForEach(bs =>
            bs.AssignedBusinessFunctions.AddRange(businessFunctionDto.Where(bf => bf.BusinessServiceId.IsNotNullOrWhiteSpace() && bf.BusinessServiceId.Equals(bs.Id))));


        userInfraObjectByBusinessServiceVm.AssignedBusinessServices = businessServiceDto;



        //    businessServiceDto.ForEach(businessFunction =>
        //{
        //    var businessFunctions = _businessFunctionRepository
        //        .GetBusinessFunctionListByBusinessServiceId(businessFunction.Id).Result
        //        .OrderBy(businessFunctionOrder => businessFunctionOrder.Id).ToList();

        //    var businessFunctionDto = _mapper.Map<List<AssignedBusinessFunctions>>(businessFunctions);

        //    businessFunction.AssignedBusinessFunctions.AddRangeAsync(businessFunctionDto);

        //    businessFunction.AssignedBusinessFunctions.ForEach(infraObject =>
        //    {
        //        var infraObjects = _infraObjectRepository.GetInfraObjectByBusinessFunctionId(infraObject.Id).Result
        //            .OrderBy(infraObjectOrder => infraObjectOrder.Id).ToList();

        //        var infraObjectDataLag = _mapper.Map<List<AssignedInfraObjects>>(infraObjects);

        //        infraObject.AssignedInfraObjects.AddRangeAsync(infraObjectDataLag);
        //    });
        //});





        //var ty= userInfraObjectByBusinessServiceVm.AssignedBusinessServices.RemoveAll(service =>
        //     service.AssignedBusinessFunctions.Count == 0);

        // //userInfraObjectByBusinessServiceVm.AssignedBusinessServices.RemoveAll(service =>
        // //    service.AssignedBusinessFunctions.Count == 0 ||
        // //    service.AssignedBusinessFunctions.where(function => function.AssignedInfraObjects.Count == 0));

        return userInfraObjectByBusinessServiceVm;
    }
}