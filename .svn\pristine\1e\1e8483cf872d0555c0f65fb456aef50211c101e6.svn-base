﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetWorkflowprofileByInfraobjectId;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfileInfo.Queries;

public class GetWorkflowprofileByInfraobjectIdQueryHandlerTests
{
    private readonly Mock<IWorkflowProfileInfoRepository> _mockWorkflowProfileInfoRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetWorkflowprofileByInfraobjectIdQueryHandler _handler;

    public GetWorkflowprofileByInfraobjectIdQueryHandlerTests()
    {
        _mockWorkflowProfileInfoRepository = new Mock<IWorkflowProfileInfoRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetWorkflowprofileByInfraobjectIdQueryHandler(
            _mockWorkflowProfileInfoRepository.Object,
            _mockMapper.Object
        );
    }

    [Fact]
    public async Task Handle_ReturnsWorkflowProfileInfoList_WhenInfraObjectIdExists()
    {
        var infraobjectId = Guid.NewGuid().ToString();

        var workflowProfileInfoList = new List<Domain.Entities.WorkflowProfileInfo>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ProfileName = "WF_Dev_Test"
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ProfileName = "Demo_Profile"
            }
        };

        var workflowProfileInfoNameVmList = new List<WorkflowProfileInfoNameVm>
        {
            new() { ProfileName = "WF_Dev_Test" },
            new() { ProfileName = "Demo_Profile" }
        };


        _mockWorkflowProfileInfoRepository
            .Setup(repo => repo.GetWorkflowProfileByInfraId(infraobjectId))
            .ReturnsAsync(workflowProfileInfoList);

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowProfileInfoNameVm>>(workflowProfileInfoList))
            .Returns(workflowProfileInfoNameVmList);

        var query = new GetWorkflowprofileByInfraobjectIdQuery { infraobjectId = infraobjectId };

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);

        Assert.Equal(workflowProfileInfoNameVmList.Count, result.Count);
        Assert.Equal(workflowProfileInfoNameVmList[0].ProfileName, result[0].ProfileName); 
        Assert.Equal(workflowProfileInfoNameVmList[1].ProfileName, result[1].ProfileName);

        _mockWorkflowProfileInfoRepository.Verify(repo => repo.GetWorkflowProfileByInfraId(infraobjectId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<List<WorkflowProfileInfoNameVm>>(workflowProfileInfoList), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenInfraObjectIdDoesNotExist()
    {
        var infraobjectId = Guid.NewGuid().ToString();

        _mockWorkflowProfileInfoRepository
            .Setup(repo => repo.GetWorkflowProfileByInfraId(infraobjectId))
            .Returns(Task.FromResult(new List<Domain.Entities.WorkflowProfileInfo>()));

        _mockMapper
            .Setup(mapper => mapper.Map<List<WorkflowProfileInfoNameVm>>(It.IsAny<List<Domain.Entities.WorkflowProfileInfo>>()))
            .Returns(new List<WorkflowProfileInfoNameVm>());

        var query = new GetWorkflowprofileByInfraobjectIdQuery { infraobjectId = infraobjectId };

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Empty(result);

        _mockWorkflowProfileInfoRepository.Verify(repo => repo.GetWorkflowProfileByInfraId(infraobjectId), Times.Once);
        _mockMapper.Verify(mapper => mapper.Map<List<WorkflowProfileInfoNameVm>>(It.IsAny<List<Domain.Entities.WorkflowProfileInfo>>()), Times.Once);
    }
}