﻿< !DOCTYPE html >
    <html>
        <head>
            <title>Company Config Tests</title>
            <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.0.css">
        </head>
        <body>

            <div id="qunit"></div>
            <div id="qunit-fixture">
                <input type="text" id="companySearchInp">
                    <table id="companyTable"><thead><tr><th></th></tr></thead><tbody></tbody></table>
                    <input type="text" id="companyName" companyDetails="{}">
                        <div id="companyNameError"></div>
                        <input type="text" id="companyDisplayName">
                            <div id="companyDisplayNameError"></div>
                            <input type="text" id="companyWebAddress">
                                <div id="companyWebAddressError"></div>
                                <input type="file" id="companyLogoFile">
                                    <div id="companyLogoError"></div>
                                    <button id="companySaveBtn">Save</button>
                                </div>

                                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                                <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
                                <script src="https://code.jquery.com/qunit/qunit-2.20.0.js"></script>
                                <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.0.1/sinon.min.js"></script>
                                <script>
    // Mocks for dependencies
                                    function gettoken() { return "dummyToken"; }
                                    function notificationAlert(type, message) {console.log(`${type}: ${message}`); }
                                    function errorNotification(response) {console.error("Error:", response); }
                                    function sanitizeContainer() { }
                                    function SpecialCharValidateCustom(v) { return v.includes('@') ? "Invalid" : true; }
                                    function ShouldNotBeginWithUnderScore(v) { return v.startsWith('_') ? "Invalid" : true; }
                                    function ShouldNotBeginWithNumber(v) { return /^[0-9]/.test(v) ? "Invalid" : true; }
                                    function ShouldNotBeginWithDotAndHyphen(v) { return /^[.-]/.test(v) ? "Invalid" : true; }
                                    function ShouldNotConsecutiveDotAndHyphen(v) { return /[.-]{2}/.test(v) ? "Invalid" : true; }
                                    function OnlyNumericsValidate(v) { return /^[0-9]+$/.test(v) ? "Invalid" : true; }
                                    function ShouldNotBeginWithSpace(v) { return /^\s/.test(v) ? "Invalid" : true; }
                                    function SpaceWithUnderScore(v) { return / _|_ /.test(v) ? "Invalid" : true; }
                                    function ShouldNotEndWithUnderScore(v) { return /_$/.test(v) ? "Invalid" : true; }
                                    function ShouldNotEndWithSpace(v) { return /\s$/.test(v) ? "Invalid" : true; }
                                    function MultiUnderScoreRegex(v) { return /_{2,}/.test(v) ? "Invalid" : true; }
                                    function SpaceAndUnderScoreRegex(v) { return / _ /.test(v) ? "Invalid" : true; }
                                    function minMaxCompanylength(v) { return v.length < 3 || v.length > 50 ? "Invalid" : true; }
                                    function secondChar(v) { return v[1] === '-' ? "Invalid" : true; }
                                    function SpecialCharValidate(v) { return /[<>]/.test(v) ? "Invalid" : true; }
                                        function DisplayLength(v) { return v.length < 2 || v.length > 50 ? "Invalid" : true; }
                                        function WebAddressValidate(v) { return /^[a-zA-Z0-9.\-]+$/.test(v) ? true : "Invalid Web Address"; }
                                        function FileSizeValidate(size) { return size < 1048576 ? true : "File too large"; }
                                        function CommonValidation(el, validations) {
        const result = validations.find(v => v !== true);
                                        if (result !== undefined) {
                                            el.text(result).addClass('field-validation-error');
                                        return false;
        }
                                        el.text('').removeClass('field-validation-error');
                                        return true;
    }
                                        async function IsSameNameExist(url, data, errorText) {
        return true; // Assume it always returns true for testing
    }
                                        async function validateName(value, id, url, errorElement, errorText, isNameExist) {
        if (!value) {
                                            errorElement.text(errorText).addClass('field-validation-error');
                                        return false;
        }
                                        let data = {name: value, id: id };
                                        const validationResults = [
                                        SpecialCharValidateCustom(value),
                                        ShouldNotBeginWithUnderScore(value),
                                        ShouldNotBeginWithNumber(value),
                                        ShouldNotBeginWithDotAndHyphen(value),
                                        ShouldNotConsecutiveDotAndHyphen(value),
                                        OnlyNumericsValidate(value),
                                        ShouldNotBeginWithSpace(value),
                                        SpaceWithUnderScore(value),
                                        ShouldNotEndWithUnderScore(value),
                                        ShouldNotEndWithSpace(value),
                                        MultiUnderScoreRegex(value),
                                        SpaceAndUnderScoreRegex(value),
                                        minMaxCompanylength(value),
                                        secondChar(value),
                                        await IsSameNameExist(url, data, isNameExist)
                                        ];
                                        return CommonValidation(errorElement, validationResults);
    }
                                        async function validateDisplayName(value, id, url, errorElement, errorText, isNameExist) {
        if (!value) {
                                            errorElement.text(errorText).addClass('field-validation-error');
                                        return false;
        }
                                        let data = {name: value, id: id };
                                        const validationResults = [
                                        SpecialCharValidate(value),
                                        OnlyNumericsValidate(value),
                                        ShouldNotBeginWithUnderScore(value),
                                        ShouldNotBeginWithSpace(value),
                                        SpaceWithUnderScore(value),
                                        ShouldNotEndWithUnderScore(value),
                                        ShouldNotEndWithSpace(value),
                                        MultiUnderScoreRegex(value),
                                        SpaceAndUnderScoreRegex(value),
                                        DisplayLength(value),
                                        secondChar(value),
                                        await IsSameNameExist(url, data, isNameExist)
                                        ];
                                        return CommonValidation(errorElement, validationResults);
    }
                                        async function validateWebAddress(value) {
        const errorElement = $('#companyWebAddressError');
                                        let format = /[`!@#$%^&*()_+=[\]{ };':"\\|,.<>\/?~]/;
                                            if (!value || format.test(value?.charAt(0))) {
                                                errorElement.text(!value ? 'Enter company web address' : 'Enter valid company web address').addClass('field-validation-error');
                                            return false;
        }
                                            return CommonValidation(errorElement, [WebAddressValidate(value)]);
    }
                                        </script>

                                        <script>
    QUnit.module("Company Config Validations", hooks => {
                                                hooks.beforeEach(() => {
                                                    $('#companyName').val("Test_Company");
                                                    $('#companyNameError').text("").removeClass('field-validation-error');
                                                    $('#companyDisplayName').val("Test Display");
                                                    $('#companyDisplayNameError').text("").removeClass('field-validation-error');
                                                    $('#companyWebAddress').val("test.com");
                                                    $('#companyWebAddressError').text("").removeClass('field-validation-error');
                                                });

        QUnit.test("Company Name should not start with underscore", async assert => {
                                                $('#companyName').val("_Company");
                                            let result = await validateName("_Company", null, "url", $('#companyNameError'), "Enter company name", "Exists");
                                            assert.notOk(result, "Should fail due to underscore");
        });

        QUnit.test("Company Name valid", async assert => {
                                                let result = await validateName("ValidCompany", null, "url", $('#companyNameError'), "Enter company name", "Exists");
                                            assert.ok(result, "Should pass with valid name");
        });

        QUnit.test("Display Name should not be only numbers", async assert => {
                                                let result = await validateDisplayName("12345", null, "url", $('#companyDisplayNameError'), "Enter display name", "Exists");
                                            assert.notOk(result, "Should fail for numeric display name");
        });

        QUnit.test("Display Name valid", async assert => {
                                                let result = await validateDisplayName("CompanyDisplay", null, "url", $('#companyDisplayNameError'), "Enter display name", "Exists");
                                            assert.ok(result, "Should pass for valid display name");
        });

        QUnit.test("Web Address valid", async assert => {
                                                let result = await validateWebAddress("company.com");
                                            assert.ok(result, "Valid web address passes");
        });

        QUnit.test("Web Address with special char fails", async assert => {
                                                let result = await validateWebAddress("@company.com");
                                            assert.notOk(result, "Fails if first character is special");
        });
    });
                                        </script>
                                    </body>
                                </html>
