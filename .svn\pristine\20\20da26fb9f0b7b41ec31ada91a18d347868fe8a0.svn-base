﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Infrastructure.Job;

public class WindowsServiceJob : IJob
{
    private IWindowsService _windowsService;
    private ILogger<JobScheduler> _logger;
    public const string WindowsService = "WindowsService";
    public const string Logger = "Logger";

    public WindowsServiceJob()
    {

    }

    public WindowsServiceJob(IWindowsService windowsService, ILogger<JobScheduler> logger)
    {
        _windowsService = windowsService;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var jobDataMap = context.JobDetail.JobDataMap;

            // Retrieve the logger and WindowsService instances from JobDataMap
            var logger = jobDataMap.Get("Logger") as ILogger<JobScheduler>;
            var windowsService = jobDataMap.Get("WindowsService") as IWindowsService;

            _logger = logger ?? throw new InvalidOperationException("Logger is not provided in JobDataMap.");

            if (windowsService == null)
            {
                logger.LogError("WindowsService is not provided in JobDataMap.");
                return;
            }

            _windowsService = windowsService;

            // Log job start information
            _logger.LogInformation("Starting WindowsServiceJob job execution.");

            // Retrieve the URL from JobDataMap
            var url = jobDataMap.GetString("url");

            var type = jobDataMap.ContainsKey("workflow")
                ? jobDataMap.GetString("workflow")
                : string.Empty;

            var checkService = jobDataMap.ContainsKey("checkServiceStatus")
                ? jobDataMap.GetString("checkServiceStatus")
                : string.Empty;

           // var type = jobDataMap.GetString("workflow") ?? string.Empty;
           // var checkService = jobDataMap.GetString("checkServiceStatus") ?? string.Empty;

            if (url.IsNullOrWhiteSpace())
            {
                _logger.LogWarning("URL is missing in JobDataMap.");
                return;
            }

            _logger.LogDebug("Fetching data from URL: {Url}", url);

            if (checkService.IsNullOrWhiteSpace())
            {
                _logger.LogDebug("checkService is null or whitespace. Calling GetAsync with URL: {Url} and Type: {Type}", url, type);

                await _windowsService.GetAsync(url, type);
            }
            else
            {
                _logger.LogDebug("checkService is not null or whitespace. Calling CheckWindowsService with URL: {Url}", url);
                await _windowsService.CheckWindowsService(url);
            }
        }
        catch (Exception ex)
        {
            _logger?.Exception("An error occurred during job execution.", ex);
        }
        finally
        {
            try
            {
                 await context.Scheduler.Interrupt(context.JobDetail.Key);

                if (await context.Scheduler.DeleteJob(context.JobDetail.Key))
                {
                    _logger?.LogDebug("Job with ID: {JobId} has been deleted after execution.", context.JobDetail.Key);
                }
                else
                {
                    _logger?.LogDebug("Job Deleted Failed.");

                }
            }
            catch (Exception ex)
            {
                _logger?.Exception($"Failed to delete job with ID: {context.JobDetail.Key}.", ex);
            }
        }
    }
}