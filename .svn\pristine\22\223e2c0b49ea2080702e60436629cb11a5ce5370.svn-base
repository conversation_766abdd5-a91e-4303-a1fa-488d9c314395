using AutoFixture;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetList;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DataSyncOptionsFixture
{
    public CreateDataSyncOptionsCommand CreateDataSyncOptionsCommand { get; }
    public CreateDataSyncOptionsResponse CreateDataSyncOptionsResponse { get; }
    public UpdateDataSyncOptionsCommand UpdateDataSyncOptionsCommand { get; }
    public UpdateDataSyncOptionsResponse UpdateDataSyncOptionsResponse { get; }
    public DeleteDataSyncOptionsCommand DeleteDataSyncOptionsCommand { get; }
    public DeleteDataSyncOptionsResponse DeleteDataSyncOptionsResponse { get; }
    public DataSyncOptionsDetailVm DataSyncOptionsDetailVm { get; }
    public DataSyncOptionsListVm DataSyncOptionsListVm { get; }
    public GetDataSyncOptionsListQuery GetDataSyncOptionsListQuery { get; }
    public GetDataSyncOptionsDetailQuery GetDataSyncOptionsDetailQuery { get; }
    public GetDataSyncOptionsNameUniqueQuery GetDataSyncOptionsNameUniqueQuery { get; }
    public GetDataSyncOptionsPaginatedListQuery GetDataSyncOptionsPaginatedListQuery { get; }

    public DataSyncOptionsFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateDataSyncOptionsCommand>(c => c
            .With(b => b.Name, "Enterprise Real-time Sync Options")
            .With(b => b.ReplicationType, "Real-time Bidirectional")
            .With(b => b.Properties, @"{
                ""syncMode"": ""realtime"",
                ""conflictResolution"": ""lastWriteWins"",
                ""compressionLevel"": ""high"",
                ""encryptionEnabled"": true,
                ""batchSize"": 1000,
                ""retryAttempts"": 5,
                ""timeoutSeconds"": 300,
                ""checksumValidation"": true,
                ""deltaSync"": true,
                ""bandwidthThrottling"": ""adaptive""
            }"));

        fixture.Customize<CreateDataSyncOptionsResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Real-time Sync Options created successfully!")
            .With(b => b.Success, true));

        fixture.Customize<UpdateDataSyncOptionsCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Updated Sync Options")
            .With(b => b.ReplicationType, "Batch Unidirectional")
            .With(b => b.Properties, @"{
                ""syncMode"": ""batch"",
                ""conflictResolution"": ""sourceWins"",
                ""compressionLevel"": ""medium"",
                ""encryptionEnabled"": true,
                ""batchSize"": 2000,
                ""retryAttempts"": 3,
                ""timeoutSeconds"": 600,
                ""checksumValidation"": true,
                ""deltaSync"": false,
                ""bandwidthThrottling"": ""fixed""
            }"));

        fixture.Customize<UpdateDataSyncOptionsResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Message, "Enterprise Updated Sync Options updated successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DeleteDataSyncOptionsCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<DeleteDataSyncOptionsResponse>(c => c
            .With(b => b.IsActive, false)
            .With(b => b.Message, "Enterprise Sync Options deleted successfully!")
            .With(b => b.Success, true));

        fixture.Customize<DataSyncOptionsDetailVm>(c => c
            .With(b => b.Id, 1)
            .With(b => b.ReferenceId, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise Detail Sync Options")
            .With(b => b.ReplicationType, "Continuous Synchronization")
            .With(b => b.Properties, @"{
                ""syncMode"": ""continuous"",
                ""conflictResolution"": ""manual"",
                ""compressionLevel"": ""maximum"",
                ""encryptionEnabled"": true,
                ""batchSize"": 500,
                ""retryAttempts"": 10,
                ""timeoutSeconds"": 120,
                ""checksumValidation"": true,
                ""deltaSync"": true,
                ""bandwidthThrottling"": ""none""
            }")
            .With(b => b.IsActive, true)
            .With(b => b.CreatedBy, "Enterprise Admin")
            .With(b => b.CreatedDate, DateTime.UtcNow.AddDays(-30))
            .With(b => b.LastModifiedBy, "Enterprise Admin")
            .With(b => b.LastModifiedDate, DateTime.UtcNow.AddDays(-1)));

        fixture.Customize<DataSyncOptionsListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.Name, "Enterprise List Sync Options")
            .With(b => b.ReplicationType, "Scheduled Synchronization")
            .With(b => b.Properties, @"{
                ""syncMode"": ""scheduled"",
                ""conflictResolution"": ""timestampBased"",
                ""compressionLevel"": ""low"",
                ""encryptionEnabled"": false,
                ""batchSize"": 1500,
                ""retryAttempts"": 2,
                ""timeoutSeconds"": 180,
                ""checksumValidation"": false,
                ""deltaSync"": true,
                ""bandwidthThrottling"": ""moderate""
            }"));

        fixture.Customize<GetDataSyncOptionsDetailQuery>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataSyncOptionsNameUniqueQuery>(c => c
            .With(b => b.Name, "Enterprise Unique Sync Options")
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<GetDataSyncOptionsPaginatedListQuery>(c => c
            .With(b => b.PageNumber, 1)
            .With(b => b.PageSize, 10)
            .With(b => b.SearchString, "Enterprise"));
           
        CreateDataSyncOptionsCommand = fixture.Create<CreateDataSyncOptionsCommand>();
        CreateDataSyncOptionsResponse = fixture.Create<CreateDataSyncOptionsResponse>();
        UpdateDataSyncOptionsCommand = fixture.Create<UpdateDataSyncOptionsCommand>();
        UpdateDataSyncOptionsResponse = fixture.Create<UpdateDataSyncOptionsResponse>();
        DeleteDataSyncOptionsCommand = fixture.Create<DeleteDataSyncOptionsCommand>();
        DeleteDataSyncOptionsResponse = fixture.Create<DeleteDataSyncOptionsResponse>();
        DataSyncOptionsDetailVm = fixture.Create<DataSyncOptionsDetailVm>();
        DataSyncOptionsListVm = fixture.Create<DataSyncOptionsListVm>();
        GetDataSyncOptionsListQuery = fixture.Create<GetDataSyncOptionsListQuery>();
        GetDataSyncOptionsDetailQuery = fixture.Create<GetDataSyncOptionsDetailQuery>();
        GetDataSyncOptionsNameUniqueQuery = fixture.Create<GetDataSyncOptionsNameUniqueQuery>();
        GetDataSyncOptionsPaginatedListQuery = fixture.Create<GetDataSyncOptionsPaginatedListQuery>();
    }
}
