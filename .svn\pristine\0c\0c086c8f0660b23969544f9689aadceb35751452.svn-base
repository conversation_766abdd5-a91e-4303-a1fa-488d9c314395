﻿@model ContinuityPatrol.Domain.ViewModels.ServerModel.ServerViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

<style>
    .f-checkbox {
        display: flex;
        align-items: center;
    }

    .wizard > .content > .body label {
        margin-bottom: -1px;
    }

    .btn-outline-secondary:hover {
        background-color: #ffffff;
        color: var(--bs-primary);
    }

    .btn-outline-secondary:active {
        background-color: #ffffff;
        color: var(--bs-primary);
    }


    .tooltip-inner {
        max-width: 500px !important;
        padding: 8px;
        color: #000000;
        text-align: left;
        text-decoration: none;
        background-color: #fff;
        border-radius: 10px;
        -webkit-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        -moz-box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
        box-shadow: 3px 7px 16px 0px rgba(224,201,224,1);
    }

    .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before, .bs-tooltip-start .tooltip-arrow::before {
        border-left-color: #fff;
    }

</style>

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-server"></i><span>Server</span></h6>
            <form class="d-flex">
                <span class="input-group-text form-label mb-0 me-2" for="basic-url">OS Type</span>
                <div class="input-group">
                    <div class="input-group border-0 me-2" style="width: 250px !important;">
                        <span class="input-group-text"><i class="cp-os-type"></i></span>
                        <select id="searchInTypeServer" class="form-select" data-placeholder="Select Type">
                            <option value="All">All</option>
                        </select>
                    </div>
                </div>
                <div class="input-group mx-2 w-100">
                    <input type="search" id="searchInputServer" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="name=" id="nameFltrServer">
                                        <label class="form-check-label" for="nameFilterServer">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="servertype=" id="typeFltrServer">
                                        <label class="form-check-label" for="typeFltrServer">
                                            Server Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="ipaddress=" id="IPAddressFltrServer">
                                        <label class="form-check-label" for="IPAddressFltrServer">
                                            IP Address
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="host=" id="hostNameFltrServer">
                                        <label class="form-check-label" for="hostNameFltrServer">
                                            Host Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="ostype=" id="OSTypeFltrServer">
                                        <label class="form-check-label" for="OSTypeFltrServer">
                                            OS Type
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="status=" id="statusFltrServer">
                                        <label class="form-check-label" for="statusFltrServer">
                                            Status
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="form-group-lg">
                    <div class="input-group" style="width: 150px;">
                        <span class="input-group-text"><i class="cp-activity-type"></i></span>
                        <select class="form-select" id="ReportType" data-placeholder="Select Report Type">
                            <option value="">Select</option>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                        </select>
                    </div>
                    <span id="ReportType_Error" style="width: 150px;"></span>
                </div>
                <button id="BtnServerDownload" type="button" title="Download" class="btn btn-primary btn-sm ms-2 me-2">
                    <i class="cp-download"></i>
                </button>

                <button type="button" id="testConnectionBtn" class="btn btn-primary text-nowrap me-2 py-1 px-2 d-none">
                    <i class="cp-test-connection" title="Test Connection" role="button"></i>
                </button>

                <button type="button" class="btn btn-sm btn-primary rounded-1 py-1 me-2" title="Refresh" id="buttonRefreshServer">
                    <i class="cp-refresh"></i>
                </button>
                <div class="btn-group">
                    <button type="button" id="createBtn" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createModalServer">
                        <i class="cp-add me-1"></i>Create
                    </button>
                    <button type="button" class="btn btn-primary btn-sm rounded-end dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="cp-down-arrow fs-7"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li id="saveAsServersData"><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#saveAsModalServer">Save As</a></li>
                        <li id="cloneServerData"><a class="dropdown-item">Clone</a></li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="dataTableListsServer" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Server Type</th>
                        <th>IP Address</th>
                        <th>Host Name</th>
                        <th>OS Type</th>
                        <th>Status</th>
                        <th class="Action-th py-0">
                            <div class="d-flex align-items-center py-1">
                                <i id="testConnectionAllServer" class="bg-white btn-outline-secondary p-1 cp-test-connection me-1"
                                   test-status="off" title="Test Connection" role="button" style="margin-left: -4px"></i>
                                Action
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody id="tabledata">
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Create-->
<div class="modal serverModal fade" id="createModalServer" data-bs-backdrop="static" aria-labelledby="configureModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="deleteModalServer" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <form id="formDeleteServer">
                <input asp-for="Id" type="hidden" id="textDeleteIdServer" />
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p class="d-flex align-items-center justify-content-center gap-1">You want to delete the 
                        <span class="font-weight-bolder text-truncate text-primary d-inline-block" 
                              style="max-width:100px" id="deleteDataServer">
                        </span> data?
                    </p>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm" id="confirmDeleteButtonServer">
                        Yes
                        <div id="loginLoader" class="spinner-border text-white ms-2 mt-1 p-1 d-none" style="width: 0.8rem; height: 0.8rem;" role="status">
                             <span class="visually-hidden">Loading...</span>
                        </div>                        
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--Modal Delete Sudo/su Table Row-->
<div class="modal fade" id="deleteModalSudoSuTable" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
            </div>
            <div class="modal-body text-center">
                <h5 class="fw-bold">Are you sure?</h5>
                <p>You want to delete the data?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="deleteSudoSuTableRow">Yes</button>
            </div>
        </div>
    </div>
</div>

<!--Version Restore-->
<div class="modal fade" data-bs-backdrop="static" id="restoreModalServer" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/confirmation.svg" alt="confirmation Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-inline-block align-bottom gap-2 ">
                    You want to continue <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="serverVersionName"></span>
                    data with new version <span class="font-weight-bolder align-bottom text-truncate text-primary d-inline-block" style="max-width:100px" id="newVersion"></span> ?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id="cancelFormRestore" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmFormRestore">Yes</button>
                <button type="submit" class="btn btn-primary btn-sm" id="confirmFormRestoreAll">Yes to all</button>
            </div>
        </div>
    </div>
</div>

<!--Modal Error-->
<div class="modal fade" id="errorModalServer" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-error-message"></i><span>Error Message</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group list-group-flush Profile-Select">
                    <div class="d-grid">
                        <span id="exceptionMessageServer" style="display: block; word-break: break-all;">

                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Save As Modal -->
<div class="modal fade" id="saveAsModalServer" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="saveasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-save"></i><span>Save As</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formSaveAsServer">
                    <div>
                        <div class="form-group w-50">
                            <div class="form-label">Clone Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-copy-option"></i></span>
                                <select data-placeholder="Select Clone Name"
                                        class="form-select-modal"
                                        id="cloneName">
                                    <option></option>
                                </select>
                            </div>
                            <span id="cloneNameError"></span>
                        </div>
                        <div class="row row-cols-6">
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Server Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-server"></i></span>
                                        <input type="text"
                                               autocomplete="off"
                                               class="form-control"
                                               id="cloneServerName"
                                               placeholder="Enter Server Name" />
                                    </div>
                                    <span id="cloneServerNameError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Site Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-web"></i></span>
                                        <select data-placeholder="Select Site Name"
                                                id="cloneSiteName"
                                                class="form-select-modal">
                                            <option></option>
                                        </select>
                                    </div>
                                    <span id="cloneSiteNameError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">Server Type</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-production-server-name"></i></span>
                                        <select data-placeholder="Select Server Type"
                                                id="cloneServerType"
                                                class="form-select-modal">
                                            <option></option>
                                        </select>
                                    </div>
                                    <span id="cloneServerTypeError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label"> License Key</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-license-key"></i></span>
                                        <select data-placeholder="Select License Key"
                                                data-live-search="true"
                                                id="cloneLicenseKey"
                                                class="form-select-modal">
                                            <option></option>
                                        </select>
                                    </div>
                                    <span id="cloneLicenseKeyError"></span>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <div class="form-label">IP Address</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-ip-address"></i></span>
                                        <input type="text"
                                               autocomplete="off"
                                               class="form-control"
                                               id="cloneIPAddress"
                                               placeholder="Enter IP Address" />
                                    </div>
                                    <span id="cloneIPAddressError"></span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <div>
                                    <div class="form-group">
                                        <div class="form-label">Host Name</div>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-host-name"></i></span>
                                            <input type="text"
                                                   autocomplete="off"
                                                   class="form-control"
                                                   id="cloneHostName"
                                                   placeholder="Enter Host Name" />
                                        </div>
                                        <span id="cloneHostNameError"></span>
                                    </div>
                                </div>
                                <div>
                                    <i role="button" title="Add" id="addCloneServer" class="cp-circle-plus fs-5 text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div style="height:300px;overflow:auto">
                    <table class="table table-hover" style="width:100% !important" id="cloneTable">
                        <thead class="position-sticky top-0 z-3">
                            <tr>
                                <th class="SrNo_th">Sr.No</th>
                                <th>Server Name</th>
                                <th>Site Name</th>
                                <th>Server Type</th>
                                <th>License Key</th>
                                <th>IP Address</th>
                                <th>Host Name</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="cloneDataTable">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <div class="d-flex align-items-center gap-3">
                    <span class="fw-semibold fs-7">Total<span class="mx-2 py-1 px-2 rounded-circle bg-primary-subtle text-primary" id="totalCloneServer">0</span></span>
                    <span class="fw-semibold fs-7">Completed<span class="mx-2 py-1 px-2 rounded-circle bg-success-subtle text-success" id="successCloneServer">0</span></span>
                    <span class="fw-semibold fs-7">Error<span class="mx-2 py-1 px-2 rounded-circle bg-danger-subtle text-danger" id="failureCloneServer">0</span></span>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveAllServers">Save All</button>
                </div>

            </div>
        </div>
    </div>
</div>

<!--Clone Modal-->
<div class="modal fade" data-bs-backdrop="static" id="cloneModalServer" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-m modal-dialog-centered">
        <div class="modal-content">
            <form>
                <div class="modal-header p-3">
                    <h6 class="page_title">
                        <i class="cp-server"></i>
                        <span>Clone Server</span>
                    </h6>
                    <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-0">
                    <div class="form-group">
                        <div class="form-label">Server Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <select data-placeholder="Select Server Name"
                                    class="form-select-modal"
                                    id="cloneServer">
                                <option></option>
                            </select>
                        </div>
                        <span id="cloneServerError"></span>
                    </div>
                    <div class="form-group">
                        <div class="form-label">Clone Server Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input maxlength="100" id="inputCloneServer" type="text" class="form-control"
                                   placeholder="Enter Clone Server Name" autocomplete="off" />
                        </div>
                        <span id="inputCloneServerError"></span>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="cp-note me-1"></i>Note: All fields are mandatory
                        except optional
                    </small>
                    <div class="gap-2 d-flex ms-auto">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                        <button type="button" class="btn btn-primary btn-sm" id="cloneButton">Clone</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!--Modal Delete SaveAs Table Row-->
<div class="modal fade" data-bs-backdrop="static" id="deleteModalSaveAsServer" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">
                    You want to delete the
                    <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px"
                          id="deleteCloneDataServer">
                    </span> data?
                </p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" id="cancelDelete" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm" id="deleteSaveAsRow" data-bs-dismiss="modal">Yes</button>
            </div>
        </div>
    </div>
</div>
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/lib/formeo/formeo.min.js"></script>
<script src="~/js/configuration/infra components/commonfunctions.js"></script>
<script src="~/js/configuration/infra components/server/serverformbuilder.js"></script>
<script src="~/js/configuration/infra components/server/serverfunctions.js"></script>
<script src="~/js/configuration/infra components/server/server.js"></script>
<script src="~/js/configuration/infra components/server/serversaveas.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidationonchange.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildervalidation.js"></script>
<script src="~/js/configuration/infra components/form builder/formbuildercommonfunctions.js"></script>
