using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraObjectFixture : IDisposable
{
    public List<InfraObject> InfraObjectPaginationList { get; set; }
    public List<InfraObject> InfraObjectList { get; set; }
    public InfraObject InfraObjectDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string InfraObjectName = "TestInfraObject";
    public const string InfraObjectType = "Server";
    public const string Description = "Test Infrastructure Object";

    public const string Status = "Active";

    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
    public const string busnessFunctionId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
    public const string InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
    public ApplicationDbContext DbContext { get; private set; }

    public InfraObjectFixture()
    {
        var fixture = new Fixture();

        InfraObjectList = fixture.Create<List<InfraObject>>();

        InfraObjectPaginationList = fixture.CreateMany<InfraObject>(20).ToList();

        InfraObjectPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectPaginationList.ForEach(x => x.IsActive = true);
        InfraObjectPaginationList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectPaginationList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraObjectPaginationList.ForEach(x => x.BusinessFunctionId = busnessFunctionId);
    
        InfraObjectList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        InfraObjectList.ForEach(x => x.IsActive = true);
        InfraObjectList.ForEach(x => x.CompanyId = CompanyId);
        InfraObjectList.ForEach(x => x.BusinessServiceId = BusinessServiceId);
        InfraObjectList.ForEach(x => x.BusinessFunctionId = busnessFunctionId);

        InfraObjectDto = fixture.Create<InfraObject>();
        InfraObjectDto.ReferenceId = InfraObjectId;
        InfraObjectDto.IsActive = true;
        InfraObjectDto.CompanyId = CompanyId;
        InfraObjectDto.BusinessServiceId = BusinessServiceId;
        InfraObjectDto.BusinessFunctionId = busnessFunctionId;
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
