﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.InfraObjectSchedulerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Queries;

public class GetInfraObjectSchedulerPaginatedListQueryHandlerTests : IClassFixture<InfraObjectSchedulerFixture>
{
    private readonly Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

    private readonly GetInfraObjectSchedulerPaginatedListQueryHandler _handler;

    public GetInfraObjectSchedulerPaginatedListQueryHandlerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        var infraObjectSchedulerNewFixture = infraObjectSchedulerFixture;

        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.GetPaginatedInfraObjectSchedulerRepository(infraObjectSchedulerNewFixture.InfraObjectSchedulers);

        _handler = new GetInfraObjectSchedulerPaginatedListQueryHandler(infraObjectSchedulerNewFixture.Mapper, _mockInfraObjectSchedulerRepository.Object);

        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].InfraObjectId = "603e38cc-f418-4742-a825-ef002f73fbd0";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].InfraObjectName = "InfraObject_Type";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].AfterSwitchOverWorkflowId = "0d7ec068-feb1-49c4-a5e9-1f97a6290c2d";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].AfterSwitchOverWorkflowName = "Work_Infra";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].ScheduleType = 1;
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].ScheduleTime = "12.00PM";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].IsSchedule = 1;
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[0].WorkflowVersion = "6.0.2";


        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].InfraObjectId = "603e38cc-f418-4742-a825-ef002f73fbd0";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].InfraObjectName = "InfraObject_Type123";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].AfterSwitchOverWorkflowId = "0d7ec068-feb1-49c4-a5e9-1f97a6290c2d";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].AfterSwitchOverWorkflowName = "Work_Infra123";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].ScheduleType = 1;
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].ScheduleTime = "12.00PM";
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].IsSchedule = 1;
        infraObjectSchedulerNewFixture.InfraObjectSchedulers[1].WorkflowVersion = "6.0.2";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "PTS" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Return_PaginatedInfraObjectSchedulers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "InfraObject_Type" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<InfraObjectSchedulerListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("InfraObject_Type");

        result.Data[0].AfterSwitchOverWorkflowName.ShouldBe("Work_Infra");

        result.Data[0].InfraObjectId.ShouldBe("603e38cc-f418-4742-a825-ef002f73fbd0");

        result.Data[0].AfterSwitchOverWorkflowId.ShouldBe("0d7ec068-feb1-49c4-a5e9-1f97a6290c2d");

        result.Data[0].WorkflowVersion.ShouldBe("6.0.2");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_InfraObjects_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetInfraObjectSchedulerPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "infraObjectName=InfraObject_Type;workflowName=Work_Infra;infraObjectId=603e38cc-f418-4742-a825-ef002f73fbd0;workflowId=0d7ec068-feb1-49c4-a5e9-1f97a6290c2d;workflowVersion=6.0.2;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<InfraObjectSchedulerListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].InfraObjectName.ShouldBe("InfraObject_Type");

        result.Data[0].AfterSwitchOverWorkflowName.ShouldBe("Work_Infra");

        result.Data[0].InfraObjectId.ShouldBe("603e38cc-f418-4742-a825-ef002f73fbd0");

        result.Data[0].AfterSwitchOverWorkflowId.ShouldBe("0d7ec068-feb1-49c4-a5e9-1f97a6290c2d");

        result.Data[0].WorkflowVersion.ShouldBe("6.0.2");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetInfraObjectSchedulerPaginatedListQuery(), CancellationToken.None);

        _mockInfraObjectSchedulerRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}