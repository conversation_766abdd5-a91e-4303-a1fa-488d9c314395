﻿using ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries
{
    public class GetUserProfileDetailQueryHandlerTests
    {
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;
        private readonly Mock<IAccessManagerRepository> _mockAccessManagerRepository;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
        private readonly Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;
        private readonly Mock<IMapper> _mockMapper;

        private readonly GetUserProfileDetailQueryHandler _handler;

        public GetUserProfileDetailQueryHandlerTests()
        {
            _mockUserRepository = new Mock<IUserRepository>();
            _mockUserInfoRepository = new Mock<IUserInfoRepository>();
            _mockAccessManagerRepository = new Mock<IAccessManagerRepository>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockWorkflowRepository = new Mock<IWorkflowRepository>();
            _mockWorkflowOperationGroupRepository = new Mock<IWorkflowOperationGroupRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetUserProfileDetailQueryHandler(
                _mockMapper.Object,
                _mockUserRepository.Object,
                _mockWorkflowRepository.Object,
                _mockWorkflowOperationGroupRepository.Object,
                _mockAccessManagerRepository.Object,
                _mockUserActivityRepository.Object,
                _mockUserInfoRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsUserProfileDetail_WhenDataIsValid()
        {
            var userId = Guid.NewGuid().ToString();
            var user = new Domain.Entities.User
            {
                ReferenceId = userId.ToString(),
                LoginName = "testuser",
                Role = "Admin"
            };
            var userInfo = new Domain.Entities.UserInfo { Email = "<EMAIL>", Mobile = "1234567890" };
            var accessManager = new Domain.Entities.AccessManager { Properties = "AccessDetails" };
            var userActivity = new List<Domain.Entities.UserActivity> { new Domain.Entities.UserActivity { LoginName = "testuser" } };
            var workflows = new List<Domain.Entities.Workflow> { new Domain.Entities.Workflow { CreatedBy = userId.ToString() } };
            var workflowGroups = new List<Domain.Entities.WorkflowOperationGroup> { new Domain.Entities.WorkflowOperationGroup { CreatedBy = userId.ToString() } };
            var userProfileVm = new UserProfileDetailVm();

            _mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(userId)).ReturnsAsync(user);
            _mockUserInfoRepository.Setup(repo => repo.GetUserInfoByUserIdAsync(userId)).ReturnsAsync(userInfo);
            _mockAccessManagerRepository.Setup(repo => repo.GetAccessManagerByRoleId(user.Role)).ReturnsAsync(accessManager);
            _mockUserActivityRepository.Setup(repo => repo.GetUserActivityByLoginName(user.LoginName)).ReturnsAsync(userActivity);
            _mockWorkflowRepository.Setup(repo => repo.GetWorkflowNames()).ReturnsAsync(workflows);
            _mockWorkflowOperationGroupRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowGroups);

            _mockMapper.Setup(mapper => mapper.Map<UserProfileDetailVm>(user)).Returns(userProfileVm);
            _mockMapper.Setup(mapper => mapper.Map<List<UserActivityListVm>>(userActivity)).Returns(new List<UserActivityListVm>());
            _mockMapper.Setup(mapper => mapper.Map<List<WorkflowListVm>>(workflows)).Returns(new List<WorkflowListVm>());
            _mockMapper.Setup(mapper => mapper.Map<List<WorkflowOperationGroupListVm>>(workflowGroups)).Returns(new List<WorkflowOperationGroupListVm>());

            var query = new GetUserProfileDetailQuery { UserId = userId };
            var cancellationToken = CancellationToken.None;

            var result = await _handler.Handle(query, cancellationToken);

            Assert.NotNull(result);
            Assert.Equal(userInfo.Email, result.Email);
            Assert.Equal(userInfo.Mobile, result.Mobile);
            Assert.Equal(accessManager.Properties, result.AccessProperties);
            Assert.NotNull(result.UserActivityList);
            Assert.NotNull(result.WorkflowList);
            Assert.NotNull(result.WorkflowOperationGroupList);

            _mockUserRepository.Verify(repo => repo.GetByReferenceIdAsync(userId), Times.Once);
            _mockUserInfoRepository.Verify(repo => repo.GetUserInfoByUserIdAsync(userId), Times.Once);
            _mockAccessManagerRepository.Verify(repo => repo.GetAccessManagerByRoleId(user.Role), Times.Once);
            _mockUserActivityRepository.Verify(repo => repo.GetUserActivityByLoginName(user.LoginName), Times.Once);
            _mockWorkflowRepository.Verify(repo => repo.GetWorkflowNames(), Times.Once);
            _mockWorkflowOperationGroupRepository.Verify(repo => repo.ListAllAsync(), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenUserIsNull()
        {
            var userId = Guid.NewGuid().ToString();
            _mockUserRepository.Setup(repo => repo.GetByReferenceIdAsync(userId)).ReturnsAsync((Domain.Entities.User)null);

            var query = new GetUserProfileDetailQuery { UserId = userId };
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, cancellationToken));
            _mockUserRepository.Verify(repo => repo.GetByReferenceIdAsync(userId), Times.Once);
        }
    }
}
