﻿using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IInfraDashboardViewRepository : IRepository<InfraDashboardView>
{
    Task<List<InfraDashboardView>> GetBusinessServiceList();
    Task<InfraDashboardView> GetInfraObjectDetailsById(string infraObjectId);
    Task<List<InfraDashboardView>> GetInfraObjectViewByBusinessFunctionId(List<string> businessFunctionId);
    Task<InfraDashboardView> GetIsAffectedDetailsById(string infraObjectId);
    Task<(string BusinessServiceId, int RtoAchieved, int RtoExceeded)> GetCurrentRtoByBusinessServiceId(
        string businessServiceId, List<string> woGroupInfraObjectIds);
    Task<List<InfraDashboardView>> GetInfraObjectViewByInfraObjectIds(List<string> infraIds);
    Task<List<InfraDashboardView>> GetBusinessServiceViewDetails();

    Task<List<(string BusinessServiceId, List<InfraDashboardView> Views)>> GroupByBusinessServiceIdListAsync();
  Task<List<InfraDashboardView>> SeviceAvailabilityByInfraObjectIdListAsync();

    Task<List<InfraDashboardView>> GetBsSitePropertiesByBusinessServiceId(string businessServiceId);
    Task<List<InfraDashboardView>> GetDrReadyInfraObjectList();
    Task<BreachDetailVm> GetBreachDetails();
    Task<List<InfraDashboardView>> GetBsSitePropertiesByBusinessServiceIds(List<string> businessServiceId);
}