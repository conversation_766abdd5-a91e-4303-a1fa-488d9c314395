﻿using ContinuityPatrol.Application.Features.ReplicationJob.Events.Delete;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Commands.Delete;

public class
    DeleteReplicationJobCommandHandler : IRequestHandler<DeleteReplicationJobCommand, DeleteReplicationJobResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IPublisher _publisher;
    private readonly IReplicationJobRepository _replicationJobRepository;

    public DeleteReplicationJobCommandHandler(IPublisher publisher, IReplicationJobRepository replicationJobRepository,
        ILoggedInUserService loggedInUserService)
    {
        _publisher = publisher;
        _replicationJobRepository = replicationJobRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<DeleteReplicationJobResponse> Handle(DeleteReplicationJobCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Replication Job Id");

        var eventToDelete = await _replicationJobRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.ReplicationJob),
            new NotFoundException(nameof(Domain.Entities.ReplicationJob), request.Id));

        eventToDelete.IsActive = false;

        await _replicationJobRepository.UpdateAsync(eventToDelete);

        var response = new DeleteReplicationJobResponse
        {
            Message = Message.Delete("Replication Job", eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new ReplicationJobDeletedEvent { ReplicationJobName = eventToDelete.Name },
            cancellationToken);

        return response;
    }
}