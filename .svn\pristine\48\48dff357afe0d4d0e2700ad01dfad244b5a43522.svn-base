﻿using ContinuityPatrol.Application.Constants;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.CommonBaseLicenseUpdate;
using ContinuityPatrol.Application.Features.LicenseManager.Events.BaseLicenseEvent.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Events.DerivedLicenseEvent.Delete;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Delete;

public class
    DeleteDerivedLicenseCommandHandler : IRequestHandler<DeleteDerivedLicenseCommand, DeleteDerivedLicenseResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<UpdateBaseLicenseCommandHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerViewRepository _serverViewRepository;

    public DeleteDerivedLicenseCommandHandler(ILicenseManagerRepository licenseManagerRepository,
        ILoggedInUserService loggedInUserService, IMapper mapper,
        ILicenseHistoryRepository licenseHistoryRepository, IPublisher publisher,
        ILogger<UpdateBaseLicenseCommandHandler> logger, IServerViewRepository serverViewRepository,
        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
        _licenseHistoryRepository = licenseHistoryRepository;
        _publisher = publisher;
        _logger = logger;
        _databaseRepository = databaseRepository;
        _replicationRepository = replicationRepository;
        _serverViewRepository = serverViewRepository;
    }

    public async Task<DeleteDerivedLicenseResponse> Handle(DeleteDerivedLicenseCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDeleteDerived = await _licenseManagerRepository.GetDerivedLicenseByIdAsync(request.Id);

        Guard.Against.Null(eventToDeleteDerived, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), request.Id));

        var server =
            await _serverViewRepository.GetServerByLicenseKey(eventToDeleteDerived!.ReferenceId);

        var database =
            await _databaseRepository.GetDatabaseListByLicenseKey(
                eventToDeleteDerived.ReferenceId);

        var replication =
            await _replicationRepository.GetReplicationListByLicenseKey(
                eventToDeleteDerived.ReferenceId);

        if (server.Count > 0 || database.Count > 0 || replication.Count > 0)
            throw new InvalidException("The license is currently in use");

        eventToDeleteDerived.IsActive = false;

        var baseLicenseDtl =
            await _licenseManagerRepository.GetBaseLicenseDetailByDerivedLicenseDetailAsync(
                eventToDeleteDerived.ParentId, SecurityHelper.Decrypt(eventToDeleteDerived.ParentPoNumber));

        Guard.Against.Null(baseLicenseDtl, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), eventToDeleteDerived.ParentId));

        var eventToUpdateBase = await _licenseManagerRepository.GetByReferenceIdAsync(baseLicenseDtl.ReferenceId);

        Guard.Against.Null(eventToUpdateBase, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), baseLicenseDtl.ReferenceId));

        baseLicenseDtl.Properties =
            GetJsonProperties.AddLicenseCount(SecurityHelper.Decrypt(eventToDeleteDerived.Properties),
                baseLicenseDtl.Properties);

        var updateBaseLicense = new CommonBaseLicenseUpdateCommand
        {
            Id = baseLicenseDtl.ReferenceId,
            PONumber = SecurityHelper.Encrypt(baseLicenseDtl.PoNumber),
            CompanyId = _loggedInUserService.CompanyId,
            CompanyName = _loggedInUserService.CompanyName,
            CPHostName = SecurityHelper.Encrypt(baseLicenseDtl.HostName),
            IPAddress = SecurityHelper.Encrypt(baseLicenseDtl.IpAddress),
            MACAddress = SecurityHelper.Encrypt(baseLicenseDtl.MacAddress),
            Properties = SecurityHelper.Encrypt(baseLicenseDtl.Properties),
            Validity = SecurityHelper.Encrypt(baseLicenseDtl.Validity),
            ExpiryDate = SecurityHelper.Encrypt(baseLicenseDtl.ExpiryDate),
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = baseLicenseDtl.LicenseKey,
            ParentPONumber = SecurityHelper.Encrypt("NA"),
            IsState = baseLicenseDtl.IsState
        };

        //Base License Validator

        if (!_loggedInUserService.IsSuperAdmin && !_loggedInUserService.IsSiteAdmin)
            throw new InvalidException(LicenseAuthentication.DerivedLicenseDelete);

        //var validator = new CommonBaseLicenseUpdateCommandValidator(_licenseManagerRepository, _loggedInUserService);

        //var validationResult = await validator.ValidateAsync(updateBaseLicense, cancellationToken);

        //if (validationResult.Errors.Count > 0)
        //{
        //    throw new Shared.Core.Exceptions.ValidationException(validationResult);
        //}
        //Base  Update on Database

        _mapper.Map(updateBaseLicense, eventToUpdateBase, typeof(CommonBaseLicenseUpdateCommand),
            typeof(Domain.Entities.LicenseManager));

        await _licenseManagerRepository.UpdateAsync(eventToUpdateBase);

        _logger.LogDebug($"Base License id'{eventToUpdateBase.Id}' updated successfully.");

        //Derived License Update
        await _licenseManagerRepository.UpdateAsync(eventToDeleteDerived);

        _logger.LogDebug($"Derived License id'{eventToDeleteDerived.Id}' deleted successfully.");

        //History update
        await _licenseHistoryRepository.AddAsync(new Domain.Entities.LicenseHistory
        {
            LicenseId = updateBaseLicense.Id,
            PONumber = updateBaseLicense.PONumber,
            CompanyId = updateBaseLicense.CompanyId,
            CompanyName = updateBaseLicense.CompanyName,
            CPHostName = updateBaseLicense.CPHostName,
            IPAddress = updateBaseLicense.IPAddress,
            MACAddress = updateBaseLicense.MACAddress,
            Properties = updateBaseLicense.Properties,
            Validity = updateBaseLicense.Validity,
            ExpiryDate = updateBaseLicense.ExpiryDate,
            ParentId = string.Empty,
            IsParent = _loggedInUserService.IsParent,
            LicenseKey = updateBaseLicense.LicenseKey,
            UpdaterId = _loggedInUserService.UserId,
            ParentPONumber = SecurityHelper.Encrypt("NA"),
            IsState = updateBaseLicense.IsState
        });

        var response = new DeleteDerivedLicenseResponse
        {
            Message = $"Derived License '{eventToDeleteDerived.CompanyName}' deleted successfully.",

            IsActive = eventToDeleteDerived.IsActive
        };

        await _publisher.Publish(new BaseLicenseUpdatedEvent { PONumber = eventToUpdateBase.PoNumber },
            cancellationToken);

        await _publisher.Publish(new DerivedLicenseDeletedEvent { PONumber = eventToDeleteDerived.PoNumber },
            cancellationToken);

        return response;
    }
}