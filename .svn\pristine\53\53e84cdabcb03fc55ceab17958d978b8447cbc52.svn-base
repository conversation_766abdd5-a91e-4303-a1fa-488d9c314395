﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;

namespace ContinuityPatrol.Application.Mappings;

public class MssqlAlwaysOnMonitorStatusProfile : Profile
{
    public MssqlAlwaysOnMonitorStatusProfile()
    {
        CreateMap<MSSQLAlwaysOnMonitorStatus, CreateMSSQLAlwaysOnMonitorStatusCommand>().ReverseMap();
        CreateMap<UpdateMSSQLAlwaysOnMonitorStatusCommand, MSSQLAlwaysOnMonitorStatus>()
            .ForMember(x => x.Id, y => y.Ignore());

        CreateMap<MSSQLAlwaysOnMonitorStatus, MSSQLAlwaysOnMonitorStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MSSQLAlwaysOnMonitorStatus, MSSQLAlwaysOnMonitorStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MSSQLAlwaysOnMonitorStatus, MSSQLAlwaysOnMonitorStatusDetailByTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}