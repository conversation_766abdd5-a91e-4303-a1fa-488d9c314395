namespace ContinuityPatrol.Domain.Entities;

public class ApprovalMatrixUsers : AuditableEntity
{
    public string UserName { get; set; }
    public string UserId { get; set; }
	public string Email { get; set; }
	public string MobileNumber { get; set; }
    [Column(TypeName = "NCLOB")] public string UserGroupProperties { get; set; }
    [Column(TypeName = "NCLOB")] public string BusinessServiceProperties { get; set; }
	public string Type { get; set; }
	public string UserType { get; set; }
	public string AcceptType { get; set; }
	public bool IsLink { get; set; }
  
}
