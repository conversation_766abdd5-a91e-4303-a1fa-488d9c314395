﻿using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Delete;
using ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Update;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetImpactAvailabilityList;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetList;
using ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ImpactAvailabilityModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Dashboard;

public class ImpactAvailabilityService : BaseService, IImpactAvailabilityService
{
    public ImpactAvailabilityService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateImpactAvailabilityCommand createImpactAvailabilityCommand)
    {
        Logger.LogDebug($"Create ImpactAvailability '{createImpactAvailabilityCommand}'");

        return await Mediator.Send(createImpactAvailabilityCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateImpactAvailabilityCommand updateImpactAvailabilityCommand)
    {
        Logger.LogDebug($"Update ImpactAvailability '{updateImpactAvailabilityCommand}'");

        return await Mediator.Send(updateImpactAvailabilityCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ImpactAvailability Id");

        Logger.LogDebug($"Delete ImpactAvailability Details by Id '{id}'");

        return await Mediator.Send(new DeleteImpactAvailabilityCommand { Id = id });
    }

    public async Task<ImpactAvailabilityListVm> GetImpactAvailabilities()
    {
        Logger.LogDebug("Get All ImpactAvailabilities");

        return await Mediator.Send(new GetImpactAvailabilityListQuery());
    }

    public async Task<List<ImpactAvailabilityDetailListVm>> GetImpactAvailabilityList()
    {
        Logger.LogDebug("Get All ImpactAvailability Detail List");

        return await Mediator.Send(new GetImpactAvailabilityDetailListQuery());
    }

    public async Task<ImpactAvailabilityDetailVm> GetImpactAvailabilityDetail(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "ImpactAvailability Id");

        Logger.LogDebug($"Get ImpactAvailability Detail by Id '{id}'");

        return await Mediator.Send(new GetImpactAvailabilityDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<ImpactAvailabilityViewModel>> GetPaginatedBusinessServiceHealthStatus(
        GetImpactAvailabilityPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in ImpactAvailability Paginated List");

        return await Mediator.Send(query);
    }
}