using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Delete;
using ContinuityPatrol.Application.Features.AdPasswordJob.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class AdPasswordJobFixture : IDisposable
{
    public List<AdPasswordJob> AdPasswordJobs { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public List<LoadBalancer> LoadBalancers { get; set; }
    public CreateAdPasswordJobCommand CreateAdPasswordJobCommand { get; set; }
    public UpdateAdPasswordJobCommand UpdateAdPasswordJobCommand { get; set; }
    public DeleteAdPasswordJobCommand DeleteAdPasswordJobCommand { get; set; }
    public AdPasswordJobCreatedEvent AdPasswordJobCreatedEvent { get; set; }
    public AdPasswordJobDeletedEvent AdPasswordJobDeletedEvent { get; set; }
    public AdPasswordJobUpdatedEvent AdPasswordJobUpdatedEvent { get; set; }
    public IMapper Mapper { get; set; }

    public AdPasswordJobFixture()
    {
        AdPasswordJobs = new List<AdPasswordJob>
        {
            new AdPasswordJob
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DomainServerId = "DS001",
                DomainServer = "TestDomain.com",
                State = "Active",
                IsSchedule = 1,
                ScheduleType = 1,
                CronExpression = "0 0 12 * * ?",
                ScheduleTime = "12:00:00",
                NodeId = "Node001",
                NodeName = "TestNode",
                ExceptionMessage = "",
                IsActive = true
            }
        };

        LoadBalancers = new List<LoadBalancer>
        {
            new LoadBalancer
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ConnectionType = "https",
                IPAddress = "127.0.0.1",
                Port = 5000,
                Type = "ALL",
                TypeCategory = "LoadBalancer",
                IsActive = true
            }
        };

        AdPasswordJobs = AutoAdPasswordJobFixture.Create<List<AdPasswordJob>>();
        UserActivities = AutoAdPasswordJobFixture.Create<List<UserActivity>>();
        LoadBalancers = AutoAdPasswordJobFixture.Create<List<LoadBalancer>>();
        CreateAdPasswordJobCommand = AutoAdPasswordJobFixture.Create<CreateAdPasswordJobCommand>();
        UpdateAdPasswordJobCommand = AutoAdPasswordJobFixture.Create<UpdateAdPasswordJobCommand>();
        DeleteAdPasswordJobCommand = AutoAdPasswordJobFixture.Create<DeleteAdPasswordJobCommand>();
        AdPasswordJobCreatedEvent = AutoAdPasswordJobFixture.Create<AdPasswordJobCreatedEvent>();
        AdPasswordJobDeletedEvent = AutoAdPasswordJobFixture.Create<AdPasswordJobDeletedEvent>();
        AdPasswordJobUpdatedEvent = AutoAdPasswordJobFixture.Create<AdPasswordJobUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<AdPasswordJobProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoAdPasswordJobFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateAdPasswordJobCommand>(p => p.DomainServer, 10));
            fixture.Customize<CreateAdPasswordJobCommand>(c => c.With(b => b.DomainServerId, "DS001"));
            fixture.Customize<CreateAdPasswordJobCommand>(c => c.With(b => b.IsSchedule, 1));
            fixture.Customize<CreateAdPasswordJobCommand>(c => c.With(b => b.ScheduleType, 1));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateAdPasswordJobCommand>(p => p.DomainServer, 10));
            fixture.Customize<UpdateAdPasswordJobCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));
            fixture.Customize<UpdateAdPasswordJobCommand>(c => c.With(b => b.IsSchedule, 1));
            fixture.Customize<UpdateAdPasswordJobCommand>(c => c.With(b => b.ScheduleType, 1));

            fixture.Customize<DeleteAdPasswordJobCommand>(c => c.With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<AdPasswordJob>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<AdPasswordJob>(c => c.With(b => b.IsActive, true));
            fixture.Customize<AdPasswordJob>(c => c.With(b => b.IsSchedule, 1));
            fixture.Customize<AdPasswordJob>(c => c.With(b => b.ScheduleType, 1));

            fixture.Customize<LoadBalancer>(c => c.With(b => b.ReferenceId, Guid.NewGuid().ToString()));
            fixture.Customize<LoadBalancer>(c => c.With(b => b.IsActive, true));
            fixture.Customize<LoadBalancer>(c => c.With(b => b.Port, 5000));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordJobCreatedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordJobDeletedEvent>(p => p.Name, 10));
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<AdPasswordJobUpdatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
