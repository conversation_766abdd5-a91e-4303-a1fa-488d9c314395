﻿using Microsoft.AspNetCore.Mvc;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller
{
    public class InfraObjectApplicationControllerShould
    {
        private readonly InfraObjectApplicationController _controller;

        public InfraObjectApplicationControllerShould()
        {
           
            _controller = new InfraObjectApplicationController();
        }

        [Fact]
        public void List_ReturnsViewResult()
        {
            
            var result = _controller.List();

            
            var viewResult = Assert.IsType<ViewResult>(result);
            
        }
    }
}
