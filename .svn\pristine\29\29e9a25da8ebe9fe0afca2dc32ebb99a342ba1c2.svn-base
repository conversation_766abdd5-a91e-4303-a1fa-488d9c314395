//functions

function clearCloneDatabaseErrorMessage() {
    const errorElements = ['#cloneDatabaseNameInputError', '#cloneDatabaseTypeError', '#cloneDatabaseNameInputError',
        '#cloneServerTypeError', '#cloneOracleIDError', '#cloneInstanceNameError'];
    clearInputFields('createFormDBSaveAs', errorElements);
}

async function databaseType(onloadorcreate = null) {
    await $.ajax({
        type: "GET",
        async: false, //dont't delete.
        url: RootUrl + databaseURL.databaseType,
        dataType: "json",
        success: function (result) {
            if (result?.success && (Array.isArray(result?.data?.data) && result?.data?.data?.length > 0)) {
                let databaseType = $('#selectType');
                let options = [];
                const uniqueValues = new Set();
                result?.data?.data?.forEach(function (dbtype, index) {
                    const dbTypeId = dbtype?.databaseTypeId?.toLowerCase();
                    if (!uniqueValues.has(dbTypeId)) {
                        options.push($('<option>').val(dbtype?.databaseTypeId).text(dbtype?.databaseType));
                        uniqueValues.add(dbTypeId);
                    }
                });
                databaseType.append(options);
                if (onloadorcreate === "documentready") {
                    databaseType.prop('selectedIndex', 0).trigger('change');
                }
            } else {
                errorNotification(result)
            }
        },
    });
}

async function licenseCount(ponumber = null) {
    if ($("#databaseServer").val() && $("#databaseType").val()) {
        $('#licenseKeyError').text('').removeClass('field-validation-error');
        await $.ajax({
            type: "GET",
            async: false, //dont't delete.
            url: RootUrl + databaseURL.getLicensesNamesWithCount,
            dataType: "json",
            data: {
                type: "database", roleType: "", siteId: "", serverId: $("#databaseServer").val(),
                replicationType: "", databaseTypeId: $("#databaseType").val()
            },
            success: function (result) {
                if (result?.success) {
                    let licenseLists = result?.data;

                    if (licenseLists && (Array.isArray(licenseLists) && licenseLists.length > 0)) {
                        let license = $('#databaseLicenseKey');
                        license.empty().append($('<option>').val("").text("Select PO"));
                        licenseLists.forEach(function (item) {
                            license.append($('<option>').val(item.poNumber)
                                .text(`${item?.poNumber || ''}`)
                                .attr('licenseId', item.id)
                                .attr('remainingcount', item.remainingCount || '0')
                                .attr('licenseIsApplicable', item.licenseIsApplicable))
                        });
                    }

                    if (ponumber) {
                        $('#databaseLicenseKey').val(ponumber)
                    }
                    $("#information").html("")
                } else {
                    errorNotification(result)
                }
            },
        });
    }
}

async function databaseProps(id, version) {
    let dataID = { "formTypeId": id, "version": version };
    let databaseProperties = await infraGetRequestWithData(RootUrl + databaseURL.getFormMappingByFormId, dataID);
    if (typeof databaseProperties === "object" && Object.keys(databaseProperties).length) {
        nextButtonStyle('', '');
        populateDatabaseFormModal(databaseProperties);
        //if ($("#databaseFormVersion").val() === "" || $("#databaseFormVersion").val() === undefined) {
        //    $("#databaseFormVersion").val(form.formVersion);
        //    populateDatabaseFormModal(form);
        //} else if ($("#databaseFormVersion").val() === form.formVersion) {
        //    populateDatabaseFormModal(form);
        //} else {
        //    $("#databaseFormVersion").val(form.formVersion);
        //    $("#databaseVersionName").text($('#databaseName').val())
        //    $("#newVersion").text(form.formVersion);
        //    populateDatabaseFormModal(form);
        //    $('#RestoreDBModal').modal('show');
        //    nextButtonStyle('0.5', 'none');
        //}
    } else {
        nextButtonStyle('0.5', 'none');
    }
}

async function populateDatabaseFormModal(form) {
    $('#databaseFormRenderingArea').empty();

    try {
        let parsedJsonData = JSON.parse(form?.properties);
        var renderedForm = new FormeoRenderer({
            renderContainer: document.querySelector("#databaseFormRenderingArea")
        });
        await renderedForm.render(parsedJsonData);

        //  initalstate;
        if (!isEdit) {
            Object.keys(parsedJsonData?.fields).forEach(function (fieldId) {
                var field = parsedJsonData?.fields[fieldId];
                if (field.conditions && field.conditions.length > 0) {
                    field.conditions.forEach(function (condition) {
                        condition.if.forEach(function (ifClause) {
                            condition.then.forEach(function (thenClause) {
                                if (thenClause.targetProperty === 'isVisible' && thenClause.assignment === 'equals' && ifClause.comparison !== "notEquals") {
                                    var targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                    if (targetElement && !targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                    }
                                }
                            });
                        });
                    });
                }
            });
        }

        setTimeout(() => {
            var selectElements = document.querySelectorAll('.form-select-modal-dynamic');
            selectElements.forEach(async function (selectElement) {
                let $this = $(selectElement);
                $this.select2({
                    dropdownParent: this1.find('.modal-content'),
                    placeholder: $this.attr('placeholder')
                });
            });
            $('.form-select-modal-dynamic').next('.select2-container').css('width', '100%');

            var disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
            disableSelectTagTitle?.forEach(async function (selectElement) {
                let $this = $(selectElement);
                $this.attr('title', '');
            });

            onChangeFormBuilderValidation('database');
        }, 500);

        await populateFormbuilderDynamicFields(parsedJsonData.fields);

        for (const key in parsedJsonData?.fields) {
            if (parsedJsonData.fields.hasOwnProperty(key)) {
                const field = parsedJsonData.fields[key];
                setFieldAttrValues(field);
            }
        }

        $('#databaseFormRenderingArea').on('focus', '.formeo-render .f-field-group input', function (event) {
            let selectedId = event.target.id;
            let type = event.target.type;
            Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                var field = parsedJsonData.fields[fieldId];
                if (selectedId == `f-${fieldId}`) {
                    if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                        $.ajax({
                            type: "POST",
                            url: RootUrl + databaseURL.serverDataDecrypt,
                            data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                            dataType: 'text',
                            success: function (decryptedValue) {
                                event.target.value = decryptedValue;
                            }
                        });
                    }
                }
            })
        });

        $('#databaseFormRenderingArea').on('blur', '.formeo-render .f-field-group input', function (event) {
            let selectedId = event.target.id;
            let type = event.target.type;
            Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                var field = parsedJsonData.fields[fieldId];
                if (selectedId == `f-${fieldId}`) {
                    if (event.target.value !== '' && type == 'text' && field?.attrs?.encryption) {
                        $.ajax({
                            type: "POST",
                            url: RootUrl + databaseURL.serverDataEncrypt,
                            data: { data: event.target.value, __RequestVerificationToken: gettoken() },
                            dataType: 'text',
                            success: function (encryptedValue) {
                                event.target.value = encryptedValue;
                            }
                        });
                    }
                }
            })
        });

        ///on`setconditionals
        $('#databaseFormRenderingArea').on('input', '.formeo-render .f-field-group input', function (event) {
            formBuilderTextConditions(event, parsedJsonData);
        });

        $('#databaseFormRenderingArea').on('change input', '.formeo-render .f-field-group input , .formeo-render .f-field-group select', function (event) {
            formBuilderDBCondition(event, parsedJsonData);
        });

        if (isEdit && databaseProperties) {
            setTimeout(() => {
                populateDatabaseDynamicFields(databaseProperties);
            }, 250)
        }
    } catch (error) {
        notificationAlert("warning", "Form property is not valid format.");
        nextButtonStyle('0.5', 'none');
    }
}

async function databaseSaveFormFields() {
    var formData = {};
    var promises = [];
    let SSOPassword = "";

    $('#databaseFormRenderingArea .formeo-render .f-field-group').each(async function (index, element) {
        let $this = $(this);
        if ($this.is(':visible')) {
            let fieldName = $(element).find('input, select, textarea').attr('name');
            let fieldNameLowerCase = fieldName?.toLowerCase();
            let multiple = $(element).find('select').attr('multiple');
            let fieldVal = $(element).find('input').attr('value');
            let fieldType = $(element).find('input, select, textarea').attr('type');
            let value;

            if (fieldNameLowerCase?.includes("singlesignon") || fieldNameLowerCase?.includes("signonprofile")) {
                let selectElement = document.querySelector(`select[name="${fieldName}"]`);
                formData[fieldName] = selectElement?.options[selectElement.selectedIndex]?.text || "";
            }

            if (fieldType === "date") {
                value = $(element).find('input[type="date"]').val();
                formData[fieldName] = value;
            }
            if (fieldName) {
                if (fieldType === 'checkbox') {
                    value = $(element).find('input[type="checkbox"]').prop('checked');
                } else if (fieldType === 'radio') {
                    value = $(element).find('input[type="radio"]:checked').val();
                    formData[value] = value;
                } else {
                    value = $(element).find('input, select, textarea').val();
                }
                if (fieldType === "checkbox") {
                    formData[fieldVal] = value;
                }
                if (fieldType === "password" && (value && value !== "") && value?.length < 64) {
                    promises.push(await EncryptPassword(value).then(encryptedPassword => {
                        formData[fieldName] = encryptedPassword;
                    }));
                } else {
                    if (fieldNameLowerCase?.includes("singlesignon") || fieldNameLowerCase?.includes("signonprofile")) {
                        formData[fieldName + "ID"] = value;
                    } else {
                        formData[fieldName] = value;
                    }
                }

                if (fieldVal?.toLowerCase() === "ssoenabled" && $(element).find('input[type="checkbox"]').prop('checked')) {
                    SSOPassword = true;
                }

                if (SSOPassword) {
                    formData["Password"] = "";
                }

                if (fieldName?.toLowerCase().includes('nodes') && multiple === 'multiple') {
                    const selectedOptions = $(element).find('select').val(); // Get selected values
                    const idToLabelMapping = {};
                    $(element).find('select option').each(function () {
                        idToLabelMapping[this.value] = $(this).text(); //Don't change to val(); 
                    });

                    // Create key-value pairs of ID and label
                    objects_list = [];
                    const selectedKeyValuePairs = selectedOptions.reduce((acc, curr) => {
                        acc[curr] = idToLabelMapping[curr];
                        return acc;
                    }, {});
                    $.each(selectedKeyValuePairs, function (key, value) {
                        var obj = { 'label': value, 'value': key };
                        objects_list.push(obj);
                    });
                    formData[fieldName] = objects_list; // Save the key-value pairs in formData           
                }
            }
        }
    });
    await Promise.all(promises);
    $('#databaseProps')?.val(JSON.stringify(formData))
    return formData;
}

function populateDatabaseDynamicFields(data) {
    let formData = JSON.parse(data);
    $('#databaseFormRenderingArea .formeo-render .f-field-group').each(function (index, element) {
        let fieldName = $(element).find('input, select, textarea').attr('name');
        let fieldVal = $(element).find('input, select, textarea').attr('value');
        let fieldID = $(element).find('input, select, textarea').attr('id');
        let fieldType = $(element).find('input, select, textarea').attr('type');

        if (fieldName && formData.hasOwnProperty(fieldName) || fieldVal) {
            let value = formData[fieldName];
            let SSOValue = formData[fieldName + "ID"];
            let checkbox = $(element).find('input[type="checkbox"]').attr("value");
            let chkValue = formData[checkbox];
            if (fieldType == 'radio') {
                $(element).find('input[type="radio"]').map((index, radio) => {
                    let radioValue = $(radio).val();
                    if (radioValue === formData[radioValue]) {
                        $(radio).prop('checked', true);
                    }
                });
            }
            if (typeof value === "boolean") {
                $(element).find('input[type="checkbox"]').prop('checked', chkValue);
                $(element).find('input[type="checkbox"]').trigger("change");
            }
            else if (fieldVal) {
                if ($("#databaseType option:selected").text().toLowerCase() === "oracle") {
                    if (fieldVal.toLowerCase() === "israc") {
                        let fieldIDCheckbox = document.getElementById(fieldID);
                        if (fieldIDCheckbox) { // Check if element is found
                            if (chkValue) {
                                fieldIDCheckbox.removeAttribute("disabled");
                            } else {
                                fieldIDCheckbox.setAttribute("disabled", "disabled");
                            }
                        }
                    }
                }
                $(element).find('input[type="checkbox"]').prop('checked', chkValue).trigger("change");
            }
            else if (typeof value === "object") {
                if (value) {
                    const valuesArray = Object.values(value);
                    const containsObject = Array.isArray(valuesArray) && valuesArray.some(item => typeof item === 'object');
                    let labelsArray;
                    if (containsObject) {
                        labelsArray = valuesArray.map(item => item.value);
                    } else {
                        labelsArray = valuesArray.map(item => item);
                    }
                    const selectElement = $(element).find('select');

                    // Loop through options in the select element
                    setTimeout(function () {
                        selectElement?.find('option').each(function () {
                            const optionValue = $(this).val();
                            if (labelsArray.includes(optionValue)) {
                                $(this).prop('selected', true);
                            }
                        });
                        selectElement.trigger('change');
                    }, 350)
                }
            }
            else {
                if (fieldName.includes("singlesignon")) {
                    $(element).find('input, select, textarea').val(SSOValue).trigger("change")
                }
                if (!fieldName.includes("singlesignon")) {
                    $(element).find('input, select, textarea').val(value).trigger("change");

                    //for migration 4.5 to 6. Get Confirmation whether it will affect exist saved data???..
                    //let selectElement = $(element).find('select')?.[0];
                    //if (selectElement) {
                    //    let options = selectElement.getElementsByTagName("option");
                    //    for (let option of options) {
                    //        option.value = option.value.toLowerCase();
                    //    }
                    //    selectElement.innerHTML = selectElement.innerHTML;
                    //    value = value.replace(/\s+/g, '').toLowerCase();
                    //    $(element).find('select').val(value).trigger("change");
                    //}
                }
                //if (fieldName.toLocaleLowerCase()?.trim() === 'ipaddress' || fieldName.toLocaleLowerCase()?.trim() === 'hostname' || fieldName.toLocaleLowerCase()?.trim() === 'virtualipaddress') {
                //    let ipAddressInput = document.querySelector('input[name="IpAddress"]');
                //    if (ipAddressInput) {
                //        ipAddressInput.setAttribute("readonly", "readonly");
                //    }
                //    let VirtualIPAddress = document.querySelector('input[name="VirtualIPAddress"]');
                //    if (VirtualIPAddress) {
                //        VirtualIPAddress.setAttribute("readonly", "readonly");
                //    }
                //    let hostName = document.querySelector('input[name="HostName"]');
                //    if (hostName) {
                //        hostName.setAttribute("readonly", "readonly");
                //    }
                //}
                if (fieldName.toLowerCase().includes("singlesignon")) {
                    if (formData["signonprofileID"]) {
                        setTimeout(() => {
                            $("#f-new-select-id8rhdgry0").val(formData["signonprofileID"]).trigger("change");
                        }, 250);
                    }
                }
                //$(element).find('input, select, textarea').val(value);
                //$(element).find('input, select, textarea').trigger("change");
                //if (fieldName === "@@singlesignon_name") {
                //    $("#f-new-select-id8rhdgry0").val(formData["@@signonprofile"]);
                //    $("#f-new-select-id8rhdgry0").trigger("change");
                //}

                if (fieldType == 'select') {
                    let $select = $(element)?.find('select');

                    if (($(`#${fieldID}`)?.val() == '' || $(`#${fieldID}`)?.val() == null) && value !== '') {
                        $select?.find('option')?.each(function () {
                            if ($(this)?.val()?.toLowerCase() == value?.toLowerCase()) {
                                $(`#${fieldID}`)?.val($(this)?.val())?.trigger('change');
                            }
                        });
                    }

                }

            }
        }
    });
    let encryption = $("input[type='password'][required]");
    encryption?.each(function () {
        let $this = $(this);
        let id = $this.attr("id");
        document.getElementById(id).addEventListener("focus", async function () {
            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await DecryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
        document.getElementById(id).addEventListener('blur', async function () {

            if ($this.is(':visible')) {
                let $thisName = $(this).attr('name');
                let $thisval = $(this).val();
                if ($thisval) {
                    let pwd = await EncryptPassword($thisval);
                    $(`input[name=${$thisName}]`).val(pwd);
                }
            }
        });
    });
}

function populateDatabaseModal(databaseData) {
    $("#databaseID").val(databaseData.id);
    $("#databaseStatus").val(databaseData.modeType);
    $("#databaseFormVersion").val(databaseData.formVersion);
    licenseIdForCountError = databaseData.licenseId;
    //previousVersion = databaseData.formVersion;
    $("#databaseName").val(databaseData.name);
    $("#databaseVersionTwo").val(databaseData.version);
    $("#databaseType").val(databaseData.databaseTypeId).trigger("change");
    $("#databaseServer").val(databaseData.serverId).trigger("change");
    $("#businessServiceID").val(databaseData.businessServiceId).trigger("change");
    $("#businessServiceName").val(databaseData.businessServiceName);
    let businessServiceName = $('#businessServiceID option:selected').text();
    $("#businessServiceName").val(businessServiceName);
    let dbVersion = JSON.parse(databaseData?.properties);
    setTimeout(() => {
        let serverType = $("#databaseServer option:selected").text();
        //$("#databaseLicenseKey").val(databaseData.licenseKey).trigger("change");
        $("#databaseLicenseID").val(databaseData.licenseId);
        versionData(dbVersion.version ? dbVersion.version : databaseData.version)
        licenseCount(databaseData.licenseKey)
        $('#DBServerName').val(serverType);
    }, 150);
    databaseProperties = databaseData.properties;
}

function clearErrorMessage() {
    const errorElements = ['#licenseKeyError', '#serverError', '#databaseTypeError', '#nameError', '#versionError', '#businessServiceIdError'];
    clearInputFields('createFormDB', errorElements);
}

(function () {
    let sessionData = sessionStorage.getItem('databaseFromITView')
    if (sessionData !== undefined && sessionData !== null && sessionData !== '') {
        getdatabase = sessionData;
        isSession = true;
    }
})();

function updateDBFormVersion(serverdata) {
    $.ajax({
        url: RootUrl + databaseURL.updateDatabaseFormVersion,
        type: "POST",
        dataType: "json",
        data: serverdata,
        success: function (result) {
            if (result.success) {
                notificationAlert("success", result?.data?.message);
            } else {
                errorNotification(result);
            }
        }
    });
}

const clearSession = () => {
    setTimeout(() => {
        if (isSession) $('#selectType').val(getdatabase).trigger('change')
        sessionStorage.removeItem('databaseFromITView');
        getdatabase = '';
    })
}

function downloadDB(blob, fileName, contentType) {
    try {
        const link = document.createElement("a");
        link.download = fileName;
        link.href = window.URL.createObjectURL(blob);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        notificationAlert("Error downloading file: " + error.message);
    }
}

//Validations
function licenseCountValidation() {
    const $selectedLicenseKey = $("#databaseLicenseKey option:selected");
    let licenseError = $('#licenseKeyError');
    let licenseIsApplicable = $selectedLicenseKey.attr('licenseIsApplicable');
    const licenseID = $selectedLicenseKey.attr("licenseID");
    const remainingCount = $selectedLicenseKey.attr('remainingcount');

    if (licenseIsApplicable === 'false') {
        licenseError.text('License expired').addClass('field-validation-error');
        $("#information").text("");
        return false;
    } else if (remainingCount) {
        let countInt = parseInt(remainingCount, 10);
        if (countInt > 0) {
            licenseError.text('').removeClass('field-validation-error');
            return true;
        } else {
            if (licenseIdForCountError && licenseIdForCountError === licenseID) {
                licenseError.text('').removeClass('field-validation-error');
                return true;
            }
            licenseError.text('The license count has been exceeded.').addClass('field-validation-error');
            return false;
        }
    }
}

function commonDatabaseValidation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

function versionData(value = null) {
    const $selectedOption = $("#databaseType option:selected");
    let formTypeID = $selectedOption.attr("formTypeID");
    const filteredObjects = versions.filter(obj => {
        try {
            return obj.formTypeId === formTypeID;
        } catch (error) {
            return false;
        }
    });
    const selectedVersion = document.getElementById("databaseVersion");
    selectedVersion.innerHTML = '<option value="">Select version</option>';
    const versionArray = JSON.parse(filteredObjects[0]?.version || "[]");
    versionArray.Version.forEach(formversion => {
        const option = document.createElement("option");
        option.value = formversion;
        option.textContent = formversion;
        option.setAttribute("nodevalue", filteredObjects[0]?.formTypeId);
        selectedVersion.appendChild(option);
    });
    if (value) {
        $("#databaseVersion").val(value).trigger("change");
    }
}