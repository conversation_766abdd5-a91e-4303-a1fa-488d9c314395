const templateArray = []
let restoreFormInput = []
let restoreFieldObj = []
let templateNodes = []
let restoreUniqueObj = []
let operationTemplateData = {}
let replicationByInfra = []
let templateDataList = []
let templateActionType = ''
let templateTypeIcon = ''
let ReplicationTypeName = ''
let ReplicationTypeNameID = ''
let Activedata = ''
let genieSolution = true
let templateCheckId = '';
let replicationDataList = []

var activeType = ''
var databaseType = ''
var replicationType = ''

$('#workflowNameContainer').hide();

$('#btnGenerateTemplate').on('click', () => {
    $("#templateName, #templateType,#Activetype,#SelectDatabaseType,#SelectReplicationType,#ddlReplicationTypeNameId",).val('');
    totalEstimatedRto = 0
    $("#imageSubSelected").attr("class", 'cp-images')
    templateActionType = ''
    templateTypeIcon = ''
    $("#DataTypeCol").hide();
    GetdabaseNames();
    $('#collapseIcon').removeClass('show')
    $('#GenerateModal').modal('show')
})

$("#collapseIcon div.Category_Icon i").on('click', function () {
    templateActionType = $(this).attr("title")
    if ($(this).length) {
        templateTypeIcon = $(this)[0].classList[0]
    }
    $("#imageSubSelected").attr("class", $(this).attr("class"))
    $('#collapseIcon').removeClass('show')
})

const templateValidate = async (type, Id, name) => {
    let data = {}
    data.actionType = type
    data.replicationTypeId = Id

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.TemplateValidation,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result.message.isUnique) {
                    $('#GenerateModal').modal('hide')
                    $('#confirmationTempModalText').html(`<span class='text-primary'>${type}</span> of <span class='text-primary'>${name}</span> solution already exist. Do you want to override ?`)
                    $('#templateConfirmationModal').modal('show')
                    templateCheckId = result.message.templateId
                } else {
                    saveTemplate(null)
                }
            }
        }
    })
}

$('#confirmationtemplateDiscard').on('click', function () {
    $('#templateConfirmationModal').modal('hide')
    $('#GenerateModal').modal('show')
})

$('#templateConfirmation').on('click', function () {
    saveTemplate(templateCheckId)
})

$("#saveTemplate").on('click', function () {
    btnCrudDiasable('saveTemplate')
    $('#saveTemplate').css('pointer-events', 'none')
    let templatename = $("#templateName").val();
    let actionType = $("#templateActionType").val();
    let activeType = $("#Activetype").val();
    let database = $("#SelectDatabaseType").val();
    let Rpelication = $("#SelectReplicationType").find("option:selected").text();
    let RpelicationType = $("#ddlReplicationTypeNameId").val();
    let description = 'This template can be utilized for ' + database + ' databases with ' + ReplicationTypeName + " " + Rpelication + " " + 'specifically for conducting ' + actionType + ' switchover activities.';

    if ((templateNameValidate(templatename) && templateActionValidate(actionType) && ActivetypeValidate(activeType) && (Activedata === "2" ? databaseValidate(database) : true) && ReplicationValidate(Rpelication) && ReplicationTypeValidate(RpelicationType)) === "false" && templatename) {
        let operationType = $("#templateActionType").val()
        if (operationType === "Failover" || operationType === "FailBack" || operationType === "SwitchOver" || operationType === "SwitchBack") {
            templateValidate($("#templateActionType").val(), $('#ddlReplicationTypeNameId :selected')[0].id, $('#ddlReplicationTypeNameId :selected')[0].text)
        } else {
            saveTemplate(null)
        }

    }
    setTimeout(() => {
        $('#saveTemplate').css('pointer-events', '')
    }, 1500)
})


const saveTemplate = async (templateId) => {

    let actionType = $("#templateActionType").val();
    let database = $("#SelectDatabaseType").val();
    let Replication = $("#SelectReplicationType").find("option:selected").text();
    let replicationType = $('#ddlReplicationTypeNameId :selected')[0].text
    let description = 'This template can be utilized for ' + database + ' databases with ' + replicationType + " " + Replication + " " + 'specifically for conducting ' + actionType + ' switchover activities.';
    let templateData = {
        "Name": $("#templateName").val(),
        "CompanyId": "",
        "Properties": JSON.stringify({ 'nodes': getWorkFlowData('template'), 'estimatedrto': totalEstimatedRto }),
        "Type": $('#Activetype option:selected').text(),
        "ActionType": actionType,
        "ReplicationTypeName": $('#ddlReplicationTypeNameId :selected')[0].text,
        "ReplicationTypeId": $('#ddlReplicationTypeNameId :selected')[0].id,
        "ReplicationCategoryTypeId": $('#SelectReplicationType option:selected').val(),
        "ReplicationCategoryTypeName": $('#SelectReplicationType option:selected').text(),
        "Description": description,
        "SubTypeId": $('#Activetype option:selected').text() === 'Database' ? $('#SelectDatabaseType option:selected').attr('id') : null,
        "SubTypeName": $('#Activetype option:selected').text() === 'Database' ? $('#SelectDatabaseType option:selected').val() : null,
        "Icon": templateTypeIcon,
        __RequestVerificationToken: gettoken()
    }

    if (templateId !== null || templateId === '' || templateId !== undefined) {
        templateData['Id'] = templateId;

    }

    await $.ajax({
        type: 'POST',
        url: RootUrl + Urls.createTemplate,
        data: templateData,
        datatype: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                $('#GenerateModal').modal('hide')
                $('#templateConfirmationModal').modal('hide')
                notificationAlert("success", result.data.message)
            } else {
                $('#GenerateModal').modal('hide')
                $('#templateConfirmationModal').modal('hide')
                errorNotification(result)

            }
        },
    });

}

const updateProperties = (details) => {

    if (details?.actionInfo?.properties) {
        details?.actionInfo?.formInput && details.actionInfo.formInput.forEach((keys) => {

            if (keys.type === 'server') {
                details.actionInfo.properties[keys.id] = keys.optionType + '_key'
                delete details.actionInfo.properties[keys.name]
            } else if (keys.type === 'database') {
                details.actionInfo.properties[keys.id] = keys.optionType + '_key'
                delete details.actionInfo.properties[keys.name]
            } else if (keys.type === 'replication') {
                details.actionInfo.properties[keys.id] = keys.optionType + '_key'
                delete details.actionInfo.properties[keys.name]
            } else if (keys.type === 'workflowAction' || keys.name == '@@DependentWorkflowAction') {
                details.actionInfo.properties[keys.id] = keys.optionType ?? keys.name + '_key'
                delete details.actionInfo.properties[keys.name]
            } else if (keys.type === 'workflow') {
                details.actionInfo.properties[keys.id] = keys.optionType + '_key'
                delete details.actionInfo.properties[keys.name]
            }

        })
    }
    return details
}

$('#btnRestoreTemplate').on('click', async function () {
    $('#RestoreModal').modal('show')
    $('.sectionData, .saveAsRestoreCopy, .saveAsRestoreComponents').empty();
    $('#generateAppendCheckBox').prop('checked', false);
    $('#WFReplicationlist, #findGenerateValue, #replaceGenerateValue, #appendGenerateText').val('');
    $("#generateparentCheck, #generateparentFindReplaceCheck").removeClass('d-none').find('input').prop('checked', false)
    $('#positionGenerateContainer, #textGenerateContainer, #generateFindAndReplaceContainer').addClass('d-none')
    $('#templateComponent').addClass('d-none')
    runningCount = '';
    isRunning = false

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getTemplateList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    let templateList = result.data;
                    replicationDataList = templateList

                    $('#WFReplicationList').empty().append(`<option value=''></option>`);
                        templateList.forEach((t) => {
                            $('#WFReplicationList').append('<option value="' + t?.replicationTypeId + '" data-template=\'' + JSON.stringify(t) + '\'>' + t?.replicationTypeName + '</option>')
                        });

                    workFlowData = []
                    operationTemplateData = {}
                    replicationByInfra = []
                    templateNodes = []

                    restoreUniqueObj = []
                    restoreFieldObj = []
                    restoreFormInput = []
                    newActionObj = {
                        actionInfo: {
                            properties: {},
                            propertyData: { propertiesInfo: [] },
                            formInput: []
                        }
                    };
                    infraRoleReverseDetails = [];

                    $('#WFOperationList, #WFWorkflowNameCont, #WFInfraNameSelectList').empty();
                    $('.restore-check').prop('checked', false);
                    $('.restore-check[value="infraobject"]').prop('checked', true);
                    $('.restoreData, .restoreCopy').empty();
                    $('#workflowNameContainer').hide();
                    $('#infraContainer').show();
                    $('#saveRestore').removeClass('next-disabled')
                    $('#replicationRestore-error, #operationRestore-error, #infraNameList-error, #workflowNameCont-error').removeClass('field-validation-error').text('');      
                }
            } else {
                $('#RestoreModal').modal('hide')
                errorNotification(result)
            }
            //disableWorkflowTools(false)
        }
    })
    
})

$(document).on('change', '#WFReplicationList', function () {
    let selectedValue = $('#WFReplicationList :selected').val()
    let filterdData = replicationDataList.filter((d) => d.replicationTypeId === selectedValue)

    if (filterdData[0]?.templateListVm && filterdData[0].templateListVm.length) {
        templateDataList = filterdData[0]
        let arr = [];
        let html = `<option value=''></option>`
        filterdData[0].templateListVm.forEach((t) => {
            if (!arr.includes(t.actionType)) {
                html += `<option value='${t?.id}'>${t?.actionType}</option>`
                arr.push(t.actionType);
            }
        });
        $('#WFOperationList').empty().append(html)
    } else {
        $('#WFOperationList').val('');
    }
    
    getInfraByReplication(selectedValue);
    if (selectedValue) {
        $('#replicationRestore-error').removeClass('field-validation-error').text('');
    }
    restoreFormInput = [];
    restoreFieldObj = [];
    restoreUniqueObj = [];
    $('.restoreData, .restoreCopy').empty();
});

$(document).on('change', '#WFOperationList', function () {
    let selectedValue = $('#WFOperationList :selected').val()
    let seclectedText = $('#WFOperationList :selected').text()

    let filteredTemplateData = templateDataList?.templateListVm && templateDataList?.templateListVm.length && templateDataList.templateListVm.filter((data) => data.actionType == seclectedText)

    restoreFormInput = []
    restoreFieldObj = []
    restoreUniqueObj = []
    $('.restoreData, .restoreCopy').empty()
    if (filteredTemplateData && filteredTemplateData.length) {
        generateWorkflowList(filteredTemplateData)
    }
    if (selectedValue) {
        $('#operationRestore-error').removeClass('field-validation-error').text('');
    }
});

$(document).on('change', '#WFInfraNameSelectList', function (e) {
    if (e.target.value) {
        $('#infraNameList-error').removeClass('field-validation-error').text('');
    }
});

const generateWorkflowList = (filteredTemplate) => {

    let html = '<option value=""></option>';
    filteredTemplate.forEach((t) => {
        html += `<option value='${t?.id}'>${t?.name}</option>`
    });
    $('#WFWorkflowNameCont').empty().append(html)
    $('#workflowNameContainer').show();
}

$(document).on('change', '#WFWorkflowNameCont', function () {

    let selectedValue = $('#WFWorkflowNameCont :selected').val()
    let getTemplate = templateDataList?.templateListVm && templateDataList.templateListVm.filter((d) => d.id === selectedValue)

    let restore_check = $('.restore-check:checked').val();
    operationTemplateData = getTemplate[0];
    restoreFormInput = []
    restoreFieldObj = []
    restoreUniqueObj = []
    $('.restoreData, .restoreCopy').empty()
    if (restore_check === 'custom' && operationTemplateData) getUniqueFormInput(operationTemplateData, 'restoreData', 'restore')
    if (selectedValue) templateRestoreValidate(selectedValue, '#workflowNameCont-error', '');
});

$(document).on('change', '.restore-check', function (e) {
    restoreFormInput = []
    restoreFieldObj = []
    restoreUniqueObj = []
    $('.restoreData, .restoreCopy').empty()
    if (e.target.value == 'custom') {
        if (operationTemplateData?.properties) getUniqueFormInput(operationTemplateData, 'restoreData', 'restore')
        $('#infraContainer').hide();
        $('#infraNameList-error').removeClass('field-validation-error').text('');
    } else {
        $('#infraContainer').show();
        $('#templateComponent').addClass('d-none')
    }
    $('#RestoreModal .modal-body').removeAttr("data-select2-id");

});

$('#RestoreModal .modal-body').on('scroll', function () {  
    setTimeout(() => {
        console.clear();
    },200)
})

const getInfraByReplication = async (replicationId) => {
    $('#WFInfraNameSelectList').empty();

    let data = {}
    data.replicationTypeId = replicationId
    if (replicationId) {
        await $.ajax({
            type: "GET",
            url: RootUrl + Urls.GetInfraByReplication,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (result?.data && result.data.length) {
                        let infraData = result.data
                        let infraDataLength = result.data.length;
                        let html = '<option value=""></option>';
                        for (let i = 0; i < infraDataLength; i++) {
                            html += `<option value='${infraData[i]?.id}'>${infraData[i]?.name}</option>` 
                        }
                        $('#WFInfraNameSelectList').append(html)
                        replicationByInfra = infraData
                    }
                } else {
                    errorNotification(result)
                }
            }
        })
    }

}

$('#SelectReplicationType').on('change', async function () {
    const value = $(this).val();
    replicationType = $(this).children(":selected").attr("value");
    SetReplicationMapping();
    await ReplicationValidate(value);

})

function ReplicationValidate(value) {
    const errorElement = $('#SelectReplicationType-error')
    if (!value || value === 'Select Replication Category') {
        errorElement.text('Select replication category').addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}

function ReplicationTypeValidate(value) {
    const errorElement = $('#ReplicationType-error')
    if (!value) {
        errorElement.text('Select replication type').addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}

$('#SelectDatabaseType').on('change', async function () {
    const value = $(this).val();
    databaseType = $(this).children(":selected").attr("Id");
    // $('#DatabaseId').val(databaseType)
    await databaseValidate(value);
    //validateDropDown(value, "Select DatabaseType", "DatbaseType-error");
})
function databaseValidate(value) {
    const errorElement = $('#SelectDatabaseType-error')
    if (!value) {
        errorElement.text('Select database type').addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}

$('#Activetype').on('change', async function () {
    const value = $(this).val();
    activeType = $('#Activetype option:selected').text();
    $("#DataTypeCol").hide();
    SetReplicationMaster(activeType)
    activityTypeEnable(value)
    Activedata = value
    $('#ddlReplicationTypeNameId').empty();
    $('#ddlReplicationTypeNameId').append('<option value=""> Select Replication Type </option>');
    await ActivetypeValidate(value);
})
function ActivetypeValidate(value) {
    const errorElement = $('#Activetype-error')
    if (!value) {
        errorElement.text('Select activity type ').addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}
function activityTypeEnable(value) {
    if (value == '2') {
        $("#DataTypeCol").show();
    } else {
        $("#DataTypeCol").hide();
    }
}
async function GetdabaseNames() {

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetDatabaseList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    $('#SelectDatabaseType').empty();
                    $('#SelectDatabaseType').append('<option value="">select Database</option>');
                    result.data.forEach((item) => {
                        let optionValues = JSON.parse(item.properties)
                        $('#SelectDatabaseType').append('<option Id="' + item.id + '" value="' + optionValues['name'] + '">' + optionValues['name'] + '</option>');
                    })
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

//ReplicationMaster 
async function SetReplicationMaster(id) {
    let data = {};
    data.ActivityType = id == 'Database' ? 'DB' : id;
    if (id) {
        await $.ajax({
            type: "GET",
            url: RootUrl + Urls.GetReplicationMaster,
            data: data,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (Array.isArray(result.data) && result.data.length > 0) {
                        $('#SelectReplicationType').empty();
                        $('#SelectReplicationType').append('<option id="" value="">' + "Select Replication Category" + '</option>')
                        $.each(result.data, function (index, item) {
                            $('#SelectReplicationType').append('<option  value=' + item.id + '>' + item.name + '</option>');
                        });
                    }
                } else {
                    errorNotification(result)
                }
            }
        })
    }
}

// Replication Mapping
async function SetReplicationMapping() {
    var data = {};
    data.databaseid = activeType === "Database" ? databaseType : ''
    data.replicationmasterid = replicationType
    data.type = activeType

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetType,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {

                    $('#ddlReplicationTypeNameId').empty();
                    let data = result.data
                    //for (let index = 0; index < data.length; index++) {
                    for (let index = 0; index < data.length; index++) {
                        let properties = JSON.parse(data[index].properties)
                        $('#ddlReplicationTypeNameId').append('<option value=""> Select Replication Type </option>');
                        for (let j = 0; j < properties.length; j++) {
                            $('#ddlReplicationTypeNameId').append('<option Id="' + properties[j].id + '" value="' + properties[j].label + '">' + properties[j].label + '</option>');
                        }
                    }
                }
            }
        }
    })
}

$('#ddlReplicationTypeNameId').on('change', function () {
    let value = $(this).val();
    let selectedId2 = $('#ddlReplicationTypeNameId :selected').attr("Id");
    ReplicationTypeName = value
    ReplicationTypeNameID = selectedId2
    ReplicationTypeValidate(value)
});

const getUniqueFormInput = (templateData, classId, mode) => {

    isTemplateWorkFlow = true
    let templateReplication = templateData?.replicationTypeId;
    let templateAction = templateData?.actionType;
    let getMode = mode && mode == 'restore' ? true : false

    $('#saveRestore, #confirmationTemplateGenerate').addClass('next-disabled');

    if (templateData?.properties) {
        let templateProp = templateData?.properties && JSON.parse(templateData?.properties)
        templateNodes = templateProp?.nodes.length && templateProp?.nodes
    } else {
        templateNodes = templateData
    }

    for (let k of templateNodes) {

        if (k?.children) {
            k?.children.forEach((pa) => {
                pa?.actionInfo?.formInput.forEach((formObj) => {

                    if (!restoreFormInput.includes(formObj.id) && formObj?.optionType !== '') {
                        restoreFormInput.push(formObj.id)
                        restoreFieldObj.push(formObj)
                    }
                })
            });
        } else if (k.hasOwnProperty('groupName')) {

            k?.groupActions.forEach((ga) => {
                if (ga.hasOwnProperty('children')) {
                    ga.children.forEach((child) => {
                        if (child?.actionInfo?.properties) {
                            child?.actionInfo.formInput.forEach((formObj) => {

                                if (!restoreFormInput.includes(formObj.id) && formObj?.optionType !== '') {

                                    restoreFormInput.push(formObj.id)
                                    restoreFieldObj.push(formObj)
                                }
                            })
                        }
                    })
                } else {
                    if (ga?.actionInfo?.properties) {
                        ga?.actionInfo.formInput.forEach((formObj) => {

                            if (!restoreFormInput.includes(formObj.id) && formObj?.optionType !== '') {

                                restoreFormInput.push(formObj.id)
                                restoreFieldObj.push(formObj)
                            }
                        })
                    }
                }
            })

        } else if (!k.hasOwnProperty('groupName')) {

            if (k?.actionInfo?.properties) {
                k?.actionInfo.formInput.forEach((formObj) => {

                    if (!restoreFormInput.includes(formObj.id) && formObj?.optionType !== '') {

                        restoreFormInput.push(formObj.id)
                        restoreFieldObj.push(formObj)
                    }
                })
            }
        }
    };

    if (restoreFieldObj.length) {
        let counts = {
            prserver: 0,
            drserver: 0,
            appServer: 0,
            virtualServer: 0,
            dnsServer: 0,
            prdatabase: 0,
            drdatabase: 0,
            replication: 0
        };

        restoreFieldObj.forEach((item) => {
            switch (item.type) {
                case 'server':
                    if (item?.optionRoleType?.toLowerCase() !== 'application' && item?.optionType === 'PRDBServer') {
                        let serverCount = checkRestoreInputCount(restoreFieldObj, 'server').filter((item) => item?.optionRoleType?.toLowerCase() !== 'application' && item.optionType === 'PRDBServer').length;

                        item.label = (serverCount > 1) ? 'Source Server ' + (++counts.prserver) : 'Source Server';
                    } else if (item?.optionRoleType?.toLowerCase() !== 'application' && item?.optionType === 'DRDBServer') {
                        let serverCount = checkRestoreInputCount(restoreFieldObj, 'server').filter((item) => item?.optionRoleType?.toLowerCase() !== 'application' && item.optionType === 'DRDBServer').length;

                        item.label = (serverCount > 1) ? 'Target Server ' + (++counts.drserver) : 'Target Server';
                    } else if (item?.optionRoleType?.toLowerCase() === 'application') {
                        let serverCount = checkRestoreInputCount(restoreFieldObj, 'server').filter((item) => item?.optionRoleType?.toLowerCase() === 'application').length;
                        item.label = (serverCount > 1) ? 'Application Server ' + (++counts.appServer) : 'Application Server';
                    } else if (item?.optionRoleType?.toLowerCase() === 'virtualization') {
                        let serverCount = checkRestoreInputCount(restoreFieldObj, 'server').filter((item) => item?.optionRoleType?.toLowerCase() === 'virtualization').length;
                        item.label = (serverCount > 1) ? 'Virtual Server ' + (++counts.virtualServer) : 'Virtual Server';
                    } else if (item?.optionRoleType?.toLowerCase() === 'dns' || item?.optionType?.replace(/\s+/g, '')?.toLowerCase() === 'dnsserver') {
                        let serverCount = checkRestoreInputCount(restoreFieldObj, 'server').filter((item) => item?.optionRoleType?.toLowerCase() === 'application').length;
                        item.label = (serverCount > 1) ? 'DNS Server ' + (++counts.dnsServer) : 'DNS Server';
                    }
                    break;
                case 'database':
                    if (item?.optionType === 'PRDB') {
                        let databaseCount = checkRestoreInputCount(restoreFieldObj, 'database').filter((item) => item.optionType == 'PRDB').length;
                        item.label = (databaseCount > 1) ? 'Source Database ' + (++counts.prdatabase) : 'Source Database';
                    } else if (item?.optionType === 'DRDB') {
                        let databaseCount = checkRestoreInputCount(restoreFieldObj, 'database').filter((item) => item.optionType == 'DRDB').length;
                        item.label = (databaseCount > 1) ? 'Target Database ' + (++counts.drdatabase) : 'Target Database';
                    }
                    break;
                case 'replication':
                    let replicationCount = checkRestoreInputCount(restoreFieldObj, 'replication').length;
                    item.label = (replicationCount > 1) ? 'Replication ' + (++counts.replication) : 'Replication';
                    break;
            }
        });

    }

    let currentMode = getMode ? 'restore' : mode === 'roleReverse' ? mode : '';

    loadRestoreFormData(restoreFieldObj, classId, templateReplication, templateAction, currentMode)
}

const checkRestoreInputCount = (array, name) => {
    return array.filter((item) => item.type === name)
}

const loadRestoreFormData = async (restoreFieldObj, classId, replicationId = '', actionTemplate = '', mode = '') => {

    if (classId == 'saveAsRestoreComponents' || mode == 'roleReverse') $('#btnSaveAsWorkflow').addClass('next-disabled')

    const groupedFields = {
        'Application Server': [],
        'Source Server': [],
        'Source Database': [],
        'Target Server': [],
        'Target Database': [],
        'others': [],
    };

    restoreFieldObj.forEach(field => {
        const [sourceTarget, type] = field?.label.split(' ');
        const group = `${sourceTarget} ${type}`;

        switch (true) {
            case group.includes('Application'):
                groupedFields['Application Server'].push(field);
                break;
            case group.includes('Source') && group.includes('Server'):
                groupedFields['Source Server'].push(field);
                break;
            case group.includes('Source') && group.includes('Database'):
                groupedFields['Source Database'].push(field);
                break;
            case group.includes('Target') && group.includes('Server'):
                groupedFields['Target Server'].push(field);
                break;
            case group.includes('Target') && group.includes('Database'):
                groupedFields['Target Database'].push(field);
                break;
            default:
                groupedFields['others'].push(field);
                break;
        }
    });

    let sortedFields = [...groupedFields["Application Server"], ...groupedFields["Source Server"], ...groupedFields["Source Database"],
    ...groupedFields["Target Server"], ...groupedFields["Target Database"], ...groupedFields.others]

    if (sortedFields.length) {
        $("." + classId).empty();
        let html = "";
        let data = {};

        for (let i = 0; i < sortedFields.length; i++) {
            let selectIcon = sortedFields[i].type === 'server' ? 'cp-server' : sortedFields[i].type === 'database' ? 'cp-database' : sortedFields[i].type === 'replication' ? 'cp-replication-on' : 'cp-text'

            html += "<div class='form-group'><div class='form-label'>" + sortedFields[i].label + "</div><div class='input-group'><span class='input-group-text'><i class=" + selectIcon + "></i></span>";
            html += `<select class='restoreTemplate select_actions forSelectLoad' data-live-search='${true}' data-typeName=${sortedFields[i].name} id=${sortedFields[i].id} name='${sortedFields[i].name}' onchange='handleFormDataChange(event, "${sortedFields[i].id}-error", "restore", "${sortedFields[i].label}")'>`;
            html += "<option value=''>Select " + sortedFields[i].label + "</option>";

            if (sortedFields[i].type === 'server') {
                data = {}
                data.roleType = sortedFields[i]?.optionRoleType
                data.serverType = sortedFields[i]?.optionType

                if (data.roleType?.toLowerCase() === 'database' || data.roleType?.toLowerCase() === 'application') {
                    const { filteredServer, getServerSubType } = getServerById(sortedFields[i]?.optionRoleType, sortedFields[i]?.optionType)

                    data.roleType = filteredServer?.id
                    data.serverType = getServerSubType?.id
                }

            } else if (sortedFields[i].type === 'database') {
                data = {}
                data.type = ''

            }

            let url = 'ITAutomation/WorkflowConfiguration/GetServerByRoleTypeAndServerType'

            if (sortedFields[i].type === 'server' && mode == 'restore') {

                data = {
                    replicationTypeId: replicationId,
                    actionType: actionTemplate,
                    entityType: 'server',
                    type: sortedFields[i]?.label.includes('Source') ? 'PRDBServer' : sortedFields[i]?.label.includes('Target') ? 'DRDBServer' : '',
                    templateType: sortedFields[i]?.optionRoleType
                }

                url = 'ITAutomation/WorkflowConfiguration/GetTemplateByTypes'

            }
            else if (sortedFields[i].type === 'server' && mode == 'roleReverse') {
                data.roleType = ''
                data.serverType = ''
            }
            else if (sortedFields[i].type === 'database') {
                url = 'ITAutomation/WorkflowConfiguration/GetDataBaseList'

            } else if (sortedFields[i].type === 'replication') {
                data = {}
                url = 'ITAutomation/WorkflowConfiguration/GetReplicationNames'
            } else if (sortedFields[i].type === 'workflow') {
                data = {}
                url = 'ITAutomation/WorkflowConfiguration/GetWorkflowList'
            } else if (sortedFields[i].optionType === 'airgap') {
                data = {}
                url = 'ITAutomation/WorkflowConfiguration/GetAirGapList'
            } else if (sortedFields[i].optionType === 'infraobject') {
                data = {}
                url = 'ITAutomation/WorkflowConfiguration/GetInfraObjectList'
            }

            let serverTypes = await GetAsync(RootUrl + url, data, OnError)
            if (serverTypes?.success && serverTypes?.data?.length === 0) {
                url = 'ITAutomation/WorkflowConfiguration/GetServerByRoleTypeAndServerType';

                serverTypes = await GetAsync(RootUrl + url, {}, OnError)
            }
            serverTypes = sortedFields[i].name !== '@@DependentWorkflowAction' ? serverTypes?.data ? serverTypes.data : serverTypes : []

            for (let k = 0; k < serverTypes.length; k++) {
                let servertype = (sortedFields[i].type === 'server') ? serverTypes[k].serverType : serverTypes[k].type ? serverTypes[k].type : (sortedFields[i].optionType === 'infraobject') ? 'infraobject' : (sortedFields[i].optionType === '@@AirGapName') ? 'airgap' : 'workflow'
                let roletype = (sortedFields[i].type === 'server') ? serverTypes[k].roleType : ''

                html += "<option value='" + serverTypes[k].id + "' data-servertype='" + servertype + "' data-roletype='" + roletype + "'>" + serverTypes[k].name + "</option>";
            }
            html += "</select>"
            html += "</div>";
            html += `<span data-id="${sortedFields[i].id}-error" data-error="Select ${sortedFields[i]?.label?.toLowerCase()}"></span>`;
            html += "</div>";

        }

        if (classId == 'genieSolution') {

            let InfraHtml = `<li id="${getRandomId('genie')}" class="list-group-item AI-Suggestions-Option">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg">
             <p class="mb-0"><i class="cp-infra-object fs-4 text-primary me-2"></i>Choose components</p>
             <ol style="width:220px" class="list-group list-group-flush AI_InfraAppendList list-group-numbered genieRestoreTemplate">${html}</ol>
             <div class="d-flex justify-content-end gap-2"><button class="btn btn-primary btn-sm" id="btnGenerateSolutionTemplate">Submit</button>
                      </div></div></li>`;

            $('#genieChatContainer').append(InfraHtml);
        } else {
            $("." + classId).append(html);

            if (classId == 'saveAsRestoreComponents') {
                $(".saveAsRestoreCopy").append(html);

                for (let i = 0; i < sortedFields?.length; i++) {
                    let findSibling = $(`#${sortedFields[i]?.id}`)?.parent()?.siblings('span')

                    if (sortedFields[i]?.name == '@@DependentWorkflowAction' || sortedFields[i]?.name == '@@workflow_action') {
                        let getDependentWorkflow = sortedFields.find((key) => key?.name?.toLowerCase()?.includes('@@dependentworkflowname') || key?.name?.toLowerCase()?.includes('@@workflowname'));

                        loadWorflowAction(getDependentWorkflow?.id, sortedFields[i]?.id)
                    }
                    if (findSibling) findSibling?.remove();
                    $(`.saveAsRestoreCopy #${sortedFields[i]?.id}`).val(sortedFields[i]?.id).prop('disabled', true).off('change').removeAttr('onchange').removeAttr('id');
                }
            }
        }

        (function () {
            $('.forSelectLoad').selectize({
                normalize: true,
                openOnFocus: false,
                create: false,
                createOnBlur: true,
                closeAfterSelect: true,

                score: function (search) {
                    let score = this.getScoreFunction(search);
                    return function (item) {
                        return score(item) + (item.text.toLowerCase().indexOf(search.toLowerCase()) + 1) * 1000;
                    };
                },

                onDropdownOpen: function ($dropdown) {
                    let currrentDrop = $dropdown?.parent()?.siblings('select')[0]?.selectize
                    $('.selectize-dropdown').each(function () {
                        let selectizeInstance = $(this).parent().siblings('select')[0]?.selectize;
                        if (selectizeInstance && selectizeInstance.isOpen && selectizeInstance !== currrentDrop) {
                            selectizeInstance.close();
                        }
                    });
                }
            });
        })();

        scrollToBottom()
        $('#saveRestore, #btnSaveAsWorkflow, #confirmationTemplateGenerate').removeClass('next-disabled');
        $('#templateComponent').removeClass('d-none')
    }
}

$("#saveRestore").on("click", async function (e) {
    e.preventDefault();

    let getReplicationValue = templateRestoreValidate($('#WFReplicationList').val(), '#replicationRestore-error', 'Select replication type');
    let getOperationValue = templateRestoreValidate($('#WFOperationList').val(), '#operationRestore-error', 'Select operation type');
    let getWorkflowValue = $('#workflowNameContainer').is(':visible') && templateRestoreValidate($('#WFWorkflowNameCont').val(), '#workflowNameCont-error', 'Select template name');
    let getInfraValue = $('#infraContainer').is(':visible') && templateRestoreValidate($('#WFInfraNameSelectList').val(), '#infraNameList-error', 'Select infraObject');

    let findAndReplaceValue = ($('#generateFindReplaceCheckBox').prop('checked') || $('#generateAppendCheckBox').prop('checked')) ? validateAppendCheckBox(false, 'findGenerateValue', 'replaceGenerateValue', 'appendGenerateText', 'findGenerateValue-error', 'replaceGenerateValue-error', 'appendGenerateText-error') : true

    let restore_check = $('.restore-check:checked').val();

    if (getReplicationValue === "false" && getOperationValue === "false" && ($('#infraContainer').is(':visible') ? getInfraValue === "false" : true) && ($('#workflowNameContainer').is(':visible') ? getWorkflowValue === "false" : true) && findAndReplaceValue) {

        getRestoreObj('restoreData');

        if (restore_check == 'custom' && actionRestoreValidation('restoreData', 'restoreTemplate')) {
            //$('#RestoreAnimationModal').modal('show')
           // $('#RestoreModal').modal('hide');

        } else if (restore_check == 'infraobject') {
            if (operationTemplateData?.properties && replicationByInfra.length) {
                let Properties = operationTemplateData?.properties && JSON.parse(operationTemplateData.properties)
                isTemplateWorkFlow = true

                if (Properties && Properties.hasOwnProperty('nodes')) templateNodes = Properties?.nodes

                //$('#RestoreModal').modal('hide');
                //replaceAIWorkflow(Properties, filteredInfra, 'infrarestore')
            } else {
                notificationAlert("warning", 'Replication Type is not configured with InfraObject')
            }
        }
        replaceGenerateWorkflow();
       ///// if (await generateSubmit()) {
            
      //  }
    }
   
});

$('#templateFindConfirmation').on('click', function () {
    let restore_check = $('.restore-check:checked').val();

    $('#templateFindConfirmationModal, #GenerateConditionModal, #RestoreModal').modal('hide')
    $('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').text('').parent().hide();

    if (restore_check == 'custom') {
        $('#RestoreAnimationModal').modal('show')

        let interval = setInterval(() => {
            let progressBarValue = +$('.restoreProgressBar').attr('aria-valuenow')
            progressBarStatus(progressBarValue, interval, restore_check)
        }, 1700)
    } else if(restore_check == 'infraobject'){

        let infraId = $('#WFInfraNameSelectList').val()
        let filteredInfra = replicationByInfra?.length && replicationByInfra.find(infra => infra.id == infraId)
        
        replaceAIWorkflow(templateNodes, filteredInfra, 'infrarestore')
    }

})

$(document).on('click', '#btnGenerateSolutionTemplate', function (e) {

    getRestoreObj('genieRestoreTemplate');

    if (actionRestoreValidation('genieRestoreTemplate', 'restoreTemplate')) {
        $('#genieChatContainer').append(generateTemplate)
        scrollToBottom()

        genieSolution = true
    }

})

const getRestoreObj = (parentId) => {
   /* $('#workflowActions').empty()*/

    $("." + parentId).find(".restoreTemplate").each(async function () {

        let $select = $(this);

        let inputName = $select.attr("name");
        let inputId = $select.attr("id");
        let inputValue = '';

        if ($select.is('select')) {
            let selectedOption = $select[0].selectize.options[$select.val()];

            if (selectedOption) {

                inputValue = selectedOption.value;
                let inputText = selectedOption?.text
                let inputServerType = selectedOption['servertype'];
                let inputRoleType = selectedOption['roletype'];

                if (inputName !== undefined && inputId) {

                    let obj = {
                        //inputName: inputValue,
                        optionType: inputServerType,
                        optionRoleType: inputRoleType,
                        selectedText: inputText
                    }
                    obj[inputId] = inputValue

                    restoreUniqueObj.push(obj)
                }
            }
        }
    });
}

const progressBarStatus = (value, interval,  mode) => {
    if (value < 90) {
        let randomNumber = Math.floor(Math.random() * (21 - 18 + 1) + 18)
        if (value > 70) {
            randomNumber = 100 - value
        }
        $('.progressPercentage').text((value + randomNumber).toString() + '%')
        $('.restoreProgressBar').attr('aria-valuenow', (value + randomNumber).toString())
        $('.restoreProgressBar').css('width', (value + randomNumber).toString() + '%')
        value > 65 ? $('.restoreProgressBar').addClass('bg-success') : null
        value === 0 ? $('.progressText').text('Analyzing template') : value > 17 && value < 35 ? $('.progressText').text('Processing components') : value > 35 && value < 54 ? $('.progressText').text('Converting components') : value > 53 && value < 75 ? $('.progressText').text('Creating workflow') : $('.progressText').text('Completed!')
    } else {
        setTimeout(() => {

            if (mode === 'custom' || mode == 'templateCustom') {
                replaceObjValues(restoreUniqueObj, mode == 'custom' ? 'template' : 'templateCustom', templateNodes)
            } 
           
            $('#RestoreAnimationModal').modal('hide')
            $('.restoreProgressBar').css('width', '0%')
            $('.progressPercentage').text('0%')
            $('.restoreProgressBar').attr('aria-valuenow', '0')
            $('.restoreProgressBar').removeClass('bg-success')

            setTimeout(() => {
                clearInterval(interval)
            }, 1000)

        }, 500)
    }
}

const replaceObjValues = (obj, mode, arr) => {

    let ConvertText = $('#findGenerateValue').val()

    for (let k of arr) {
        if (k?.children) {

            k?.children.forEach((pa) => {
                obj.forEach((item) => {
                    Object.keys(item).forEach((keys) => {

                        let propValues = Object.values(pa.actionInfo.properties)
                        if (pa.actionInfo.properties.hasOwnProperty(keys) || (mode == 'saveas' && propValues.includes(keys))) {

                            let getKeyName = pa?.actionInfo?.formInput?.filter(key => key?.id == keys)
                            let getPropertiesData = pa?.actionInfo?.propertyData?.propertiesInfo?.findIndex(key => key?.id == keys)

                            pa.actionInfo.properties[getKeyName[0]?.name] = item[keys]
                            delete pa.actionInfo.properties[keys]
                            let arrayIndex = pa.actionInfo.formInput.indexOf(getKeyName[0])
                            pa.actionInfo.formInput[arrayIndex].id = item[keys]
                            pa.actionInfo.formInput[arrayIndex].optionType = item.optionType
                            pa.actionInfo.formInput[arrayIndex].optionRoleType = item.optionRoleType

                            replacePropertiesData(getPropertiesData, pa, item[keys], item['selectedText'])

                        }

                        if (ConvertText && mode != 'saveas') pa.actionInfo.actionName = pa?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                    });
                })
            });
        } else if (k.hasOwnProperty('groupName')) {
            k?.groupActions.forEach((ga) => {
                if (ga.hasOwnProperty('children')) {
                    ga.children.forEach((child) => {
                        obj.forEach((item) => {
                            Object.keys(item).forEach((keys) => {

                                let propValues = Object.values(child?.actionInfo?.properties)
                                if (child.actionInfo.properties.hasOwnProperty(keys) || (mode == 'saveas' && propValues.includes(keys))) {

                                    let getKeyName = child?.actionInfo?.formInput?.filter(key => key?.id == keys)
                                    let getPropertiesData = child?.actionInfo?.propertyData?.propertiesInfo?.findIndex(key => key?.id == keys)

                                    child.actionInfo.properties[getKeyName[0]?.name] = item[keys]
                                    delete child.actionInfo.properties[keys]
                                    let arrayIndex = child.actionInfo.formInput.indexOf(getKeyName[0])
                                    child.actionInfo.formInput[arrayIndex].id = item[keys]
                                    child.actionInfo.formInput[arrayIndex].optionType = item.optionType
                                    child.actionInfo.formInput[arrayIndex].optionRoleType = item.optionRoleType

                                    replacePropertiesData(getPropertiesData, child, item[keys], item['selectedText'])

                                }

                                if (ConvertText && mode != 'saveas') child.actionInfo.actionName = child?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                            });
                        });
                    })
                } else {
                    obj.forEach((item) => {
                        Object.keys(item).forEach((keys) => {

                            let propValues = Object.values(ga.actionInfo.properties)
                            if (ga.actionInfo.properties.hasOwnProperty(keys) || (mode == 'saveas' && propValues.includes(keys))) {

                                let getKeyName = ga?.actionInfo?.formInput?.filter(key => key?.id == keys)
                                let getPropertiesData = ga?.actionInfo?.propertyData?.propertiesInfo?.findIndex(key => key?.id == keys)

                                ga.actionInfo.properties[getKeyName[0]?.name] = item[keys]
                                delete ga.actionInfo.properties[keys]
                                let arrayIndex = ga.actionInfo.formInput.indexOf(getKeyName[0])
                                ga.actionInfo.formInput[arrayIndex].id = item[keys]
                                ga.actionInfo.formInput[arrayIndex].optionType = item.optionType
                                ga.actionInfo.formInput[arrayIndex].optionRoleType = item.optionRoleType

                                replacePropertiesData(getPropertiesData, ga, item[keys], item['selectedText'])
                            }

                            if (ConvertText && mode != 'saveas') ga.actionInfo.actionName = ga?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                        });
                    });
                }
            })

        } else if (!k.actionInfo.hasOwnProperty('IsGroup')) {
            if (k?.actionInfo?.properties) {
                obj.forEach((item) => {
                    Object.keys(item).forEach((keys) => {

                        let propValues = Object.values(k.actionInfo.properties)
                        if (k.actionInfo.properties.hasOwnProperty(keys) || (mode == 'saveas' && propValues.includes(keys))) {

                            let getKeyName = k?.actionInfo?.formInput?.filter(key => key?.id == keys)
                            let getPropertiesData = k?.actionInfo?.propertyData?.propertiesInfo?.findIndex(key => key?.id == keys)

                            k.actionInfo.properties[getKeyName[0]?.name] = item[keys]
                            delete k.actionInfo.properties[keys]
                            let arrayIndex = k.actionInfo.formInput.indexOf(getKeyName[0])
                            k.actionInfo.formInput[arrayIndex].id = item[keys]
                            k.actionInfo.formInput[arrayIndex].optionType = item.optionType
                            k.actionInfo.formInput[arrayIndex].optionRoleType = item.optionRoleType

                            replacePropertiesData(getPropertiesData, k, item[keys], item['selectedText'])

                            if (ConvertText && mode != 'saveas') k.actionInfo.actionName = k?.actionInfo?.actionName?.replace(ConvertText, $('#replaceGenerateValue').val())
                        }
                    });
                })
            }
        }
    };

    if (mode == 'template') {
        $("#workflowActions").empty();
        workFlowData.push(...templateNodes)
        loadWorkFlow(templateNodes, mode);
        workflowSaveMode = "Save";
        $('#workflowTitle').text('Untitled Workflow');
        $('#workflowRunningStatus').addClass('d-none');
        $('#workflowLockStatus, #workflowVerifyStatus').hide();
        $(".checkSaveWorkflow").show();
        $('#workflowVersion, #versionText').text('')

    } else if (mode == 'templateCustom') {
        workFlowData.push(...arr)
        $(".checkSaveWorkflow").show();
        loadWorkFlow(arr, mode);

        if ($("#workflowActions")?.children()?.length == 0) {
            workflowSaveMode = "Save";
            $('#workflowTitle').text('Untitled Workflow');
            $('#workflowVersion, #versionText').text('')
        }
       
    }
}

$(document).on('click', '.generateType', function (e) {
    if (e.target.checked) {
        if (e.target.name === 'solution') {
            $('#fileContainer').addClass('d-none')
            $('#solutionContainer').removeClass('d-none')
        }else{
            $('#solutionContainer').addClass('d-none')
            $('#fileContainer').removeClass('d-none')
        }
        $('.generateType').prop('checked', false)
        $(this).prop('checked', true)
    }
})

// Export and Import WorkFlow
$('#btnExportWorkFlow').on('click', function () {
    btnDisableWhileClick()
    $('#ExportName').val('')
    $('#exportName-error').text('').removeClass('field-validation-error');
    $('#ExportWorkflowModal').modal('show')
});

$('#loadExportWorkflow').on('click', function (e) {
    e.preventDefault();
    let exportName = $('#ExportName').val()
    let exportValidation = importExportValidation(exportName, 'exportName-error', 'Enter export name')
    if (exportValidation) {

        const jsonString = JSON.stringify(getWorkFlowData());

        $.ajax({
            type: "POST",
            url: RootUrl + Urls.workflowEncrypt,
            data: { data: jsonString, __RequestVerificationToken: gettoken() },
            dataType: 'text',
            success: function (response) {
                response = JSON.parse(response)
                if (response.success) {
                    const blob = new Blob([response.data], { type: 'application/octet-stream' });
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(blob);
                    a.download = exportName + '_' + new Date().toLocaleDateString().split('/').reverse().join('') + '_' + new Date().toLocaleTimeString().split(':')[0] + '-' + new Date().toLocaleTimeString().split(':')[1] + '_WF' + ".json";
                    // a.download = `${exportName}.json`;
                    a.onclick = function () {
                        $('#ExportWorkflowModal').modal('hide');
                    };
                    a.click();

                    notificationAlert('success', `Workflow ${exportName} has been exported successfully`)
                } else {
                    errorNotification(response)
                }
            }
        });
    }
});

$('#btnImportWorkFlow').on('click', function () {
    $('#importWorkflowContainer .importType').prop('checked', false)
    $('#newWorkflowImport').prop('checked', true)
    if ($('#btnSaveModalOpen').is(':disabled')) {
        $('#importWorkflowContainer').removeClass('d-flex').addClass('d-none')
    } else {
        $('#importWorkflowContainer').removeClass('d-none').addClass('d-flex')
    }
    $('#btnRestoreTemplate').prop('disabled', true)
    setTimeout(() => {
        $('#btnRestoreTemplate').prop('disabled', false)
    },600)
    $('#ImportWorkflow').val('')
    $('#importWorkflow-error').text('').removeClass('field-validation-error');
    $('#ImportWorkflowModal').modal('show')
    
});

$('#importWorkflowContainer').on('change', '.importType', function (e) {
    e.stopPropagation();
    $('#importWorkflowContainer .importType').prop('checked', false)
    if ($(this).val() === 'existing') {
        if (!workflowContainer.children().length) {
            notificationAlert('warning', 'Workflow not found')
            return false
        } else {
            $(this).prop('checked', true)
        }
    } else {
        $(this).prop('checked', true)
    }
})

$('#loadImportWorkflow').on('click', function (e) {
    e.preventDefault();
    const fileInput = $('#ImportWorkflow')[0];
    const file = fileInput.files[0];
    let exportValidation = importExportValidation(file, 'importWorkflow-error', 'Choose workflow')

    if (exportValidation && file) {
        if (file.type === 'application/json') {
            const reader = new FileReader();
            reader.onload = function (e) {
                try {
                    const importedJson = e.target.result;
                    $.ajax({
                        type: "POST",
                        url: RootUrl + Urls.workflowDcrypt,
                        data: { data: importedJson, __RequestVerificationToken: gettoken() },
                        dataType: 'text',
                        success: async function (response) {
                            response = JSON.parse(response)
                            if (response.success) {
                                let parsedData = JSON.parse(response.data)
                                if (!Array.isArray(parsedData) && parsedData.hasOwnProperty('properties')) {
                                    let getNodes = parsedData?.properties ? JSON.parse(parsedData?.properties) : {}
                                    parsedData = getNodes?.nodes ? getNodes.nodes : []
                                }

                                if (parsedData.length) {

                                    let serverList = await getServerList();

                                    serverExistByImport = [];
                                    workflowSaveMode = "Save";
                                    if ($('#importWorkflowContainer .importType:checked').val() !== 'existing') {
                                        $('#workflowActions').empty();
                                        $('#workflowTitle').text('Untitled Workflow');
                                        $('#workflowRunningStatus').addClass('d-none');
                                        $('#workflowLockStatus, #workflowVerifyStatus').hide();
                                        $('#versionText, #workflowVersion').text('')
                                        GlobalWorkflowId = '';
                                        runningCount = '';
                                        isRunning = false;
                                        isTemplateWorkFlow = false;
                                        disableWorkflowTools(true);
                                    } else {
                                        if ($('#workflowTitle').text() !== '' && !$('#workflowTitle').text()?.toLowerCase()?.includes('untitled')) {
                                            workflowSaveMode = "Update";
                                        } 
                                    }
                                    $(".checkSaveWorkflow").show();                                  
                                    loadWorkFlow(parsedData, 'import', serverList); 
                                }
                                $('#ImportWorkflowModal').modal('hide')
                            } else {
                                notificationAlert("warning", 'Error on reading file, Unhandled JSON inserted');
                            }
                        }
                    });
                } catch (error) {
                    notificationAlert("warning", 'Error on reading file');
                }
            };
            reader.onerror = function (e) {
                notificationAlert("warning", 'Error on reading file');
            };
            reader.readAsText(file);
        }
        else {
            //importExportValidation('', 'importWorkflow-error', 'Invalid format')
            $('#importWorkflow-error').text('Invalid format').addClass('field-validation-error');
        }
    }
});

