﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;

public class GetHeatMapStatusPaginatedListQueryHandler : IRequestHandler<GetHeatMapStatusPaginatedListQuery,
    PaginatedResult<HeatMapStatusListVm>>
{
    private readonly IHeatMapStatusViewRepository _heatMapStatusViewRepository;
    private readonly IMapper _mapper;

    public GetHeatMapStatusPaginatedListQueryHandler(IMapper mapper, IHeatMapStatusViewRepository heatMapStatusViewRepository)
    {
        _mapper = mapper;
        _heatMapStatusViewRepository = heatMapStatusViewRepository;
    }

    public async Task<PaginatedResult<HeatMapStatusListVm>> Handle(GetHeatMapStatusPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new HeatMapStatusFilterSpecification(request.SearchString);

        var queryable = request.Type != null
            ?await _heatMapStatusViewRepository.GetHeatMapStatusType(request.Type, request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder)
            :await _heatMapStatusViewRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var heatMapStatus = _mapper.Map<PaginatedResult<HeatMapStatusListVm>>(queryable);


        return heatMapStatus;
    }
}