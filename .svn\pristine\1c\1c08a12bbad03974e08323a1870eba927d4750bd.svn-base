﻿namespace ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;

public class
    MongoDbMonitorStatusDetailQueryHandler : IRequestHandler<MongoDbMonitorStatusDetailQuery,
        MongoDbMonitorStatusDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorStatusRepository _mongoDbMonitorStatusRepository;

    public MongoDbMonitorStatusDetailQueryHandler(IMapper mapper,
        IMongoDbMonitorStatusRepository mongoDbMonitorStatusRepository)
    {
        _mapper = mapper;
        _mongoDbMonitorStatusRepository = mongoDbMonitorStatusRepository;
    }

    public async Task<MongoDbMonitorStatusDetailVm> Handle(MongoDbMonitorStatusDetailQuery request,
        CancellationToken cancellationToken)
    {
        var mongoDbMonitorStatus = await _mongoDbMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(mongoDbMonitorStatus, nameof(Domain.Entities.MongoDbMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.MongoDbMonitorStatus), request.Id));

        var mongoDbMonitorStatusDetail = _mapper.Map<MongoDbMonitorStatusDetailVm>(mongoDbMonitorStatus);

        return mongoDbMonitorStatusDetail ??
               throw new NotFoundException(nameof(Domain.Entities.MongoDbMonitorStatus), request.Id);
    }
}