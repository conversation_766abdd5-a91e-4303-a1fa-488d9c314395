using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixUsersFixture : IDisposable
{
    public List<ApprovalMatrixUsers> ApprovalMatrixUsersPaginationList { get; set; }
    public List<ApprovalMatrixUsers> ApprovalMatrixUsersList { get; set; }
    public ApprovalMatrixUsers ApprovalMatrixUsersDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixUsersFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixUsersList = fixture.Create<List<ApprovalMatrixUsers>>();

        ApprovalMatrixUsersPaginationList = fixture.CreateMany<ApprovalMatrixUsers>(20).ToList();

        ApprovalMatrixUsersDto = fixture.Create<ApprovalMatrixUsers>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
