﻿namespace ContinuityPatrol.Domain.Entities;
 
public class WorkflowPermission : AuditableEntity
{
    public string CompanyId { get; set; }
    [Column(TypeName = "NCLOB")] public string AccessProperties { get; set; }
    [Column(TypeName = "NCLOB")] public string UserProperties { get; set; }
    public string Description { get; set; }
    public string AccessType { get; set; }
    [Column(TypeName = "NCLOB")] public string ScheduleTime { get; set; }
    public string UserAccess { get; set; }
    public string Type { get; set; }
}