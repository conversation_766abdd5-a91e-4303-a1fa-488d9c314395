﻿using ContinuityPatrol.Application.Features.WorkflowProfile.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowProfile.Validators;

public class CreateWorkflowProfileValidatorTests
{
    public List<Domain.Entities.WorkflowProfile> WorkflowProfiles { get; set; }

    private readonly Mock<IWorkflowProfileRepository> _mockWorkflowProfileRepository;

    public CreateWorkflowProfileValidatorTests()
    {
        WorkflowProfiles = new Fixture().Create<List<Domain.Entities.WorkflowProfile>>();

        _mockWorkflowProfileRepository = WorkflowProfileRepositoryMocks.CreateWorkflowProfileRepository(WorkflowProfiles);
    }

    //NAME

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Name_WithEmpty(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Name is Required.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Name_IsNull(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = null;
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("'Name' must not be empty.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameNotNullRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Name_MinimumRange(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "VR";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Name should contain between 3 to 200 characters.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameRangeRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Name_MaximumRange(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHUJKL";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Group Policy Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValid, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "  HCL  ";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_DoubleSpace_InFront(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "  HCL";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_DoubleSpace_InBack(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "HCL  ";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_TripleSpace_InBetween(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "HCL   Tech";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_SpecialCharacters_InFront(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "#$#%$HCL";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_SpecialCharacters_InBack(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "HCL&^^%%$";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_SpecialCharacters_InBetween(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "HCL%$^%(*Tech";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_SpecialCharacters_Only(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "(**&%#@*%";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_UnderScore_InFront(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "_HCL";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_UnderScore_InBack(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "HCL_";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_Numbers_InFront(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "654HCL";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "_654HCL_";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_UnderScore_InFront_AndNumbers_InBack(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "_HCL454";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowProfileData]
    public async Task Verify_CreateWorkflowProfileCommandValidator_Valid_Name_With_Numbers_Only(CreateWorkflowProfileCommand createWorkflowProfileCommand)
    {
        var validator = new CreateWorkflowProfileCommandValidator(_mockWorkflowProfileRepository.Object);

        createWorkflowProfileCommand.Name = "************";
        createWorkflowProfileCommand.GroupPolicyId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(createWorkflowProfileCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowProfileCommand);

        Assert.Contains("Please Enter Valid Name.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.WorkflowProfile.WorkflowProfileNameValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }
}