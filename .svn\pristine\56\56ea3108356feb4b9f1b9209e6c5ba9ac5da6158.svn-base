﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthStatus.Commands;

public class UpdateBusinessServiceHealthStatusTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly BusinessServiceHealthStatusFixture _businessServiceHealthStatusFixture;

    private readonly Mock<IBusinessServiceHealthStatusRepository> _businessServiceHealthStatusRepositoryMock;

    private readonly UpdateBusinessServiceHealthStatusCommandHandler _handler;

    public UpdateBusinessServiceHealthStatusTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusFixture = businessServiceHealthStatusFixture;
    
        _businessServiceHealthStatusRepositoryMock = BusinessServiceHealthStatusRepositoryMocks.UpdateBusinessServiceHealthStatusRepository(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList);
        
        _handler = new UpdateBusinessServiceHealthStatusCommandHandler(_businessServiceHealthStatusRepositoryMock.Object, _businessServiceHealthStatusFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Valid_BusinessServiceHealthStatus_When_UpdateToBusinessServiceHealthStatusRepo()
    {
        _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.Id = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId;

        var result = await _handler.Handle(_businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand, CancellationToken.None);

        var businessServiceHealthStatus = await _businessServiceHealthStatusRepositoryMock.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.ProblemState, businessServiceHealthStatus.ProblemState);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceHealthStatusResponse_When_BusinessServiceHealthStatus_Updated()
    {
        _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.Id = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId;

        var result = await _handler.Handle(_businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateBusinessServiceHealthStatusResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessServiceHealthStatusId()
    {
        _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand.Id = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId;

        await _handler.Handle( _businessServiceHealthStatusFixture.UpdateBusinessServiceHealthStatusCommand , CancellationToken.None);

        _businessServiceHealthStatusRepositoryMock.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _businessServiceHealthStatusRepositoryMock.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceHealthStatus>()), Times.Once);
    }
}