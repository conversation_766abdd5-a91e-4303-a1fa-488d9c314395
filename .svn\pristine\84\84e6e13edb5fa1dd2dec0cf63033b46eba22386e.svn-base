﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowHistory.Queries;

public class GetWorkflowHistoryPaginatedListQueryHandlerTests : IClassFixture<WorkflowHistoryFixture>
{
    private readonly WorkflowHistoryFixture _workflowHistoryFixture;
    private readonly GetWorkflowHistoryPaginatedListQueryHandler _handler;
    private readonly Mock<IWorkflowHistoryRepository> _mockWorkflowHistoryRepository;

    public GetWorkflowHistoryPaginatedListQueryHandlerTests(WorkflowHistoryFixture workflowHistoryFixture)
    {
        _workflowHistoryFixture = workflowHistoryFixture;

        _workflowHistoryFixture.WorkflowHistories[0].WorkflowName = "Test_Case";

        _workflowHistoryFixture.WorkflowHistories[0].LoginName = "Test1";

        _workflowHistoryFixture.WorkflowHistories[1].WorkflowName = "Demo";

        _workflowHistoryFixture.WorkflowHistories[1].LoginName = "Check";
        _workflowHistoryFixture.WorkflowHistories[1].Version = "6";

        _mockWorkflowHistoryRepository = WorkflowHistoryRepositoryMocks.GetPaginatedWorkflowHistoryRepository(_workflowHistoryFixture.WorkflowHistories);
        _handler = new GetWorkflowHistoryPaginatedListQueryHandler(_workflowHistoryFixture.Mapper, _mockWorkflowHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowHistoryListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_WorkflowHistory_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "workflowname=Test;updatorname=MYSQL;loginname=Test1" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowHistoryListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].WorkflowName.ShouldBe("Test_Case");
        
        result.Data[0].LoginName.ShouldBe("Test1");
    }

    [Fact]
    public async Task Handle_Return_PaginatedWorkflowHistory_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Check" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowHistoryListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].WorkflowName.ShouldBe("Demo");

        result.Data[0].WorkflowId.ShouldBeGreaterThan(0.ToString());
        
        result.Data[0].Version.ShouldBe(_workflowHistoryFixture.WorkflowHistories[1].Version);

        result.Data[0].Description.ShouldNotBeEmpty();

        result.Data[0].LoginName.ShouldBe("Check");

        result.Data[0].Comments.ShouldNotBeEmpty();

        result.Data[0].CompanyId.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Properties.ShouldBeGreaterThan(0.ToString());

        result.Data[0].UpdaterId.ShouldBeGreaterThan(0.ToString());
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowHistoryPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<WorkflowHistoryListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetWorkflowHistoryPaginatedListQuery(), CancellationToken.None);

        _mockWorkflowHistoryRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}