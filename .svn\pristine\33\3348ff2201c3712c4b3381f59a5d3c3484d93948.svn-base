using ContinuityPatrol.Application.Features.CyberMappingHistory.Queries.GetCyberMappingHistoryById;
using ContinuityPatrol.Domain.ViewModels.CyberMappingHistoryModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberMappingHistoryFixture
{
    public CyberMappingHistoryListVm CyberMappingHistoryListVm { get; }
    public CyberMappingHistoryViewModel CyberMappingHistoryViewModel { get; }
    public CyberMappingHistoryIdVm CyberMappingHistoryIdVm { get; }
    public List<CyberMappingHistoryIdVm> CyberMappingHistoryIdVmList { get; }

    public CyberMappingHistoryFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CyberMappingHistoryListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.<PERSON>r<PERSON>ame, () => $"user.{fixture.Create<string>().Substring(0, 8)}@company.com")
            .With(b => b.CyberComponentMappingId, Guid.NewGuid().ToString())
            .With(b => b.CyberComponentMappingName, () => $"ComponentMapping-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.Properties, () => $"{{\"mappingType\":\"standard\",\"version\":\"1.{fixture.Create<int>() % 10}.0\",\"lastModified\":\"{DateTime.Now:yyyy-MM-dd HH:mm:ss}\"}}")
            .With(b => b.Comments, () => $"Mapping history entry - {fixture.Create<string>().Substring(0, 15)}"));

        fixture.Customize<CyberMappingHistoryViewModel>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.UserName, "<EMAIL>")
            .With(b => b.CyberComponentMappingId, Guid.NewGuid().ToString())
            .With(b => b.CyberComponentMappingName, "Enterprise Database Mapping Configuration")
            .With(b => b.Properties, "{\"mappingHistory\":{\"version\":\"2.1.0\",\"changeType\":\"Major Update\",\"modifications\":[{\"field\":\"sourceDatabase\",\"oldValue\":\"PROD_DB_01\",\"newValue\":\"PROD_DB_02\",\"timestamp\":\"2024-01-15T14:30:00Z\",\"reason\":\"Database server migration\"},{\"field\":\"targetDatabase\",\"oldValue\":\"DR_DB_01\",\"newValue\":\"DR_DB_02\",\"timestamp\":\"2024-01-15T14:30:00Z\",\"reason\":\"DR infrastructure upgrade\"},{\"field\":\"replicationSettings\",\"oldValue\":{\"rpo\":\"15min\",\"method\":\"async\"},\"newValue\":{\"rpo\":\"5min\",\"method\":\"sync\"},\"timestamp\":\"2024-01-15T14:30:00Z\",\"reason\":\"Enhanced RTO/RPO requirements\"}],\"approvals\":[{\"approver\":\"<EMAIL>\",\"role\":\"Database Administrator\",\"timestamp\":\"2024-01-15T13:45:00Z\",\"status\":\"Approved\"},{\"approver\":\"<EMAIL>\",\"role\":\"IT Manager\",\"timestamp\":\"2024-01-15T14:00:00Z\",\"status\":\"Approved\"}],\"testing\":{\"preDeploymentTests\":\"Passed\",\"rollbackPlan\":\"Available\",\"impactAssessment\":\"Low Risk\"},\"compliance\":{\"changeControl\":\"CHG-2024-001234\",\"documentation\":\"Updated\",\"auditTrail\":\"Complete\"}}}")
            .With(b => b.Comments, "Major configuration update for enhanced disaster recovery capabilities with improved RTO/RPO targets and synchronous replication implementation."));

        fixture.Customize<CyberMappingHistoryIdVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.UserName, "<EMAIL>")
            .With(b => b.CyberComponentMappingId, Guid.NewGuid().ToString())
            .With(b => b.CyberComponentMappingName, "Critical Infrastructure Mapping History")
            .With(b => b.Properties, "{\"historyDetails\":{\"version\":\"3.0.0\",\"changeType\":\"Critical Update\",\"businessJustification\":\"Compliance requirement for SOX and PCI-DSS\",\"technicalChanges\":[{\"component\":\"Primary Database\",\"change\":\"Encryption at rest enabled\",\"impact\":\"Enhanced security posture\"},{\"component\":\"Replication Network\",\"change\":\"Dedicated VLAN implementation\",\"impact\":\"Improved network isolation\"},{\"component\":\"Backup Storage\",\"change\":\"Multi-site backup strategy\",\"impact\":\"Enhanced data protection\"}],\"riskAssessment\":{\"level\":\"Medium\",\"mitigations\":[\"Comprehensive testing in staging\",\"Rollback procedures validated\",\"24x7 monitoring during implementation\"],\"contingencyPlan\":\"Immediate rollback to previous configuration if issues detected\"},\"implementation\":{\"maintenanceWindow\":\"2024-01-15 02:00-06:00 EST\",\"expectedDowntime\":\"30 minutes\",\"rollbackTime\":\"15 minutes\",\"stakeholders\":[\"Database Team\",\"Network Team\",\"Security Team\",\"Business Continuity Team\"]}}}")
            .With(b => b.Comments, "Critical infrastructure mapping update to meet enhanced compliance requirements and improve overall system resilience."));

        CyberMappingHistoryListVm = fixture.Create<CyberMappingHistoryListVm>();
        CyberMappingHistoryViewModel = fixture.Create<CyberMappingHistoryViewModel>();
        CyberMappingHistoryIdVm = fixture.Create<CyberMappingHistoryIdVm>();
        CyberMappingHistoryIdVmList = new List<CyberMappingHistoryIdVm>
        {
            CyberMappingHistoryIdVm,
            fixture.Create<CyberMappingHistoryIdVm>()
        };
    }
}
