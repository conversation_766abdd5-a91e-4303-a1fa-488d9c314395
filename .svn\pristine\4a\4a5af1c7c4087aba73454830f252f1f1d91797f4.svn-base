﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.PageWidget.Events.PaginatedView;

public class PageWidgetPaginatedEventHandler : INotificationHandler<PageWidgetPaginatedEvent>
{
    private readonly ILogger<PageWidgetPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public PageWidgetPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<PageWidgetPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(PageWidgetPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = "PageWidget",
            Action = $"{ActivityType.View} PageWidget",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Configure Widget viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Configure Widget viewed");
    }
}