﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IInfraReplicationMappingRepository : IRepository<InfraReplicationMapping>
{
    Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByDatabaseId(string databaseId,
        string replicationMasterId);

    Task<List<InfraReplicationMapping>> GetTypeByDatabaseIdAndReplicationMasterId(string databaseId,
        string replicationMasterId, string type);

    Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByType(string type);
    Task<List<InfraReplicationMapping>> GetInfraReplicationMappingByComponentId(string componentId);
}