﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title" title="POSTGRES DETAIL MONITORING">
            <i class="cp-monitoring"></i><span>Postgres Detail Monitoring :</span>
            <span id="infraName"></span>
        </h6>
        <div class="d-flex align-items-center">
        <span title="Last Monitored Time :"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>
    </div>
    <div id="noDataimg" class="monitor_pages">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
       
            @* <div class="col-md-4 col-lg-4 col-xl-4">
            <div class="row h-100">
            <div class="col-12 d-grid">


            </div>
            </div>
            </div>*@
            <div class="col-md-6 col-lg-6 col-xl-6 d-grid">
               
                <div class="card Card_Design_None h-100">
                    <div class="card-header card-title" title="Replication Monitoring">Replication Monitoring</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0 noDataimg" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Replication Details">Replication Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Replication Status">
                                        <i class="text-secondary cp-database-warning me-1 fs-6"></i>Replication Status
                                    </td>
                                    <td class="text-truncate"><span id="PR_ReplicationStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_ReplicationStatus"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Database Recovery"><i class="text-secondary cp-time me-1 fs-6"></i>Database Recovery</td>
                                    <td class="text-truncate"><span id="PR_RecoveryStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_RecoveryStatus"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Data Directory Path">
                                        <i class="text-secondary cp-folder-server me-1 fs-6"></i>Data Directory Path
                                    </td>
                                    <td class="text-truncate"><span id="PR_DataDirectoryPath"></span></td>
                                    <td class="text-truncate"><span id="DR_DataDirectoryPath"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Current WAL File Name">
                                        <i class="text-secondary cp-control-file-name me-1 fs-6"></i>Current WAL File Name
                                    </td>
                                    <td class="text-truncate"><span id="PR_CurrentWalLsnFileName"></span></td>
                                    <td class="text-truncate"><span id="DR_CurrentWalLsnFileName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Current WAL Log Location">
                                        <i class="text-secondary cp-file-location me-1 fs-6"></i>Current WAL Log Location
                                    </td>
                                    <td class="text-truncate"><span id="CurrentWalLsnPR"></span></td>
                                    <td class="text-truncate"><span id="DR_CurrentWalLsn"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Last WAL Receive FileName">
                                        <i class="text-secondary cp-arrow-page me-1 fs-6"></i>Last WAL Receive FileName
                                    </td>
                                    <td class="text-truncate"><span id="PR_LastWalReceiveLsnFileName"></span></td>
                                    <td class="text-truncate"><span id="DR_LastWalReceiveLsnFileName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Last WAL Log Receive">
                                        <i class="text-secondary cp-location-down me-1 fs-6"></i>Last WAL Log Receive
                                    </td>
                                    <td class="text-truncate"><span id="PR_LastWalReceiveLsn"></span></td>
                                    <td class="text-truncate"><span id="DR_LastWalReceiveLsn"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Last WAL Replay File Name">
                                        <i class="text-secondary cp-database-page me-1 fs-6"></i>Last WAL Replay File Name
                                    </td>
                                    <td class="text-truncate"><span id="PR_LastWalReplayLsnFileName"></span></td>
                                    <td class="text-truncate"><span id="DR_LastWalReplayLsnFileNameDR"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Last WAL Replay Location">
                                        <i class="text-secondary cp-retry me-1 fs-6"></i>Last WAL Replay Location
                                    </td>
                                    <td class="text-truncate"><span id="PR_LastWalReplayLsnPR"></span></td>
                                    <td class="text-truncate"><span id="DR_LastWalReplayLsnDR"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Datalag(in MB)"><i class="text-secondary cp-log-file-name me-1 fs-6"></i>Datalag(in MB)</td>
                                    <td class="text-truncate"><span id="PR_DataLagInSize"></span></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Datalag (HH:MM)"><i class="text-secondary cp-calendar me-1 fs-6"></i>Datalag (HH:MM)</td>
                                    @* <td class="text-truncate"><i class="cp-time text-primary me-1 fs-6"></i><span id="PR_Datalag"></span></td> *@
                                    <td class="text-truncate"><span id="PR_Datalag"></span></td>
                                    <td></td>
                                </tr>

                            </tbody>

                        </table>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-6 col-xl-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                    <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                </div>
                <div class="card Card_Design_None mb-2 h-100">
                    <div class="card-header card-title" title="Database Details">Database Details</div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0 noDataimg" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Database Details">Database Details</th>
                                    <th class="text-primary" title="Primary">Primary</th>
                                    <th class="text-info dynamicSite-header" title="DR">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate" title="Server IP / Host Name">
                                        <i class="text-secondary cp-ip-address me-1 fs-6"></i>Server IP / Host Name
                                    </td>
                                    <td class="text-truncate"><span id="PR_Server_IpAddress"></span></td>
                                    <td class="text-truncate"><span id="DR_Server_IpAddress"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Database Name">
                                        <i class="text-secondary cp-database-type me-1 fs-6"></i>Database Name
                                    </td>
                                    <td class="text-truncate"><span id="PR_Database"></span></td>
                                    <td class="text-truncate"><span id="DR_Database"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Database Version"><i class="text-secondary cp-database-role me-1 fs-6"></i>Database Version</td>
                                    <td class="text-truncate"><span id="PR_Database_Version"></span></td>
                                    <td class="text-truncate">
                                        <span id="DR_Database_Version"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Database Service">
                                        <i class="text-secondary cp-mysql-data me-1 fs-6"></i>Database Service
                                    </td>
                                    <td class="text-truncate"><span id="PR_DatabaseServiceStatus"></span></td>
                                    <td class="text-truncate">
                                        <span id="DR_DatabaseServiceStatus"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate " title="Database Cluster">
                                        <i class="text-secondary cp-cluster-database
                                                me-1 fs-6"></i>Database Cluster
                                    </td>
                                    <td class="text-truncate"><span id="PR_DatabaseClusterStatus"></span></td>
                                    <td class="text-truncate">
                                        <span id="DR_DatabaseClusterStatus"></span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>

            <div class="col-12 d-grid">
                <div class="card Card_Design_None mb-2" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th class="">Status</th>
                                </tr>
                            </thead>
                            <tbody id="mssqlserverbody">
                              
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
@* <script src="~/js/monitoring/postgresdetail.js"></script> *@
<script src="~/js/Monitoring/MonitoringPostgres.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
