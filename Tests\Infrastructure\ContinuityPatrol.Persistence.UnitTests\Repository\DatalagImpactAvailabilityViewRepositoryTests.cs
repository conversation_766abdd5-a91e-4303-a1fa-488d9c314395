using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DatalagImpactAvailabilityViewRepositoryTests : IClassFixture<DatalagImpactAvailabilityViewFixture>
{
    private readonly DatalagImpactAvailabilityViewFixture _datalagImpactAvailabilityViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly DatalagImpactAvailabilityViewRepository _repository;

    public DatalagImpactAvailabilityViewRepositoryTests(DatalagImpactAvailabilityViewFixture datalagImpactAvailabilityViewFixture)
    {
        _datalagImpactAvailabilityViewFixture = datalagImpactAvailabilityViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new DatalagImpactAvailabilityViewRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        await _dbContext.DatalagImpactAvailabilityViews.AddAsync(datalagImpactAvailabilityView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(datalagImpactAvailabilityView.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityView.Id, result.Id);
        Assert.Equal(datalagImpactAvailabilityView.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        await _dbContext.DatalagImpactAvailabilityViews.AddAsync(datalagImpactAvailabilityView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(datalagImpactAvailabilityView.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityView.ReferenceId, result.ReferenceId);
        Assert.Equal(datalagImpactAvailabilityView.BusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        await _dbContext.DatalagImpactAvailabilityViews.AddRangeAsync(datalagImpactAvailabilityViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(datalagImpactAvailabilityViews.Count, result.Count);
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetByBusinessServiceId Tests

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        datalagImpactAvailabilityView.ReferenceId = DatalagImpactAvailabilityViewFixture.BusinessServiceId;
        await _dbContext.DatalagImpactAvailabilityViews.AddAsync(datalagImpactAvailabilityView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessServiceId(DatalagImpactAvailabilityViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(DatalagImpactAvailabilityViewFixture.BusinessServiceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        await _dbContext.DatalagImpactAvailabilityViews.AddRangeAsync(datalagImpactAvailabilityViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessServiceId("non-existent-business-service-id");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnCorrectEntity_WhenMultipleExist()
    {
        // Arrange
        var datalagImpactAvailabilityViews = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewList;
        var targetBusinessServiceId = "TARGET_BS_123";
        datalagImpactAvailabilityViews.First().ReferenceId = targetBusinessServiceId;
        await _dbContext.DatalagImpactAvailabilityViews.AddRangeAsync(datalagImpactAvailabilityViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessServiceId(targetBusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(targetBusinessServiceId, result.ReferenceId);
    }

    #endregion

    #region View-Specific GET Operations Tests

    [Fact]
    public async Task GetByBusinessServiceId_ShouldHandleMultipleCallsCorrectly()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;

        datalagImpactAvailabilityView.ReferenceId = DatalagImpactAvailabilityViewFixture.BusinessServiceId;
        await _dbContext.DatalagImpactAvailabilityViews.AddAsync(datalagImpactAvailabilityView);
        await _dbContext.SaveChangesAsync();

        // Act - Multiple calls to the same method
        var result1 = await _repository.GetByBusinessServiceId(DatalagImpactAvailabilityViewFixture.BusinessServiceId);
        var result2 = await _repository.GetByBusinessServiceId(DatalagImpactAvailabilityViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result1);
        Assert.NotNull(result2);
        Assert.Equal(result1.Id, result2.Id);
        Assert.Equal(result1.ReferenceId, result2.ReferenceId);
    }

    [Fact]
    public async Task GetByBusinessServiceId_ShouldReturnCorrectBusinessServiceData()
    {
        // Arrange
        var datalagImpactAvailabilityView = _datalagImpactAvailabilityViewFixture.DatalagImpactAvailabilityViewDto;
        datalagImpactAvailabilityView.ReferenceId = DatalagImpactAvailabilityViewFixture.BusinessServiceId;
        datalagImpactAvailabilityView.BusinessServiceName = "Test Business Service";
        datalagImpactAvailabilityView.TotalBusinessFunctionCount = 10;
        datalagImpactAvailabilityView.TotalInfraObjectCount = 5;
        await _dbContext.DatalagImpactAvailabilityViews.AddAsync(datalagImpactAvailabilityView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByBusinessServiceId(DatalagImpactAvailabilityViewFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Business Service", result.BusinessServiceName);
        Assert.Equal(10, result.TotalBusinessFunctionCount);
        Assert.Equal(5, result.TotalInfraObjectCount);
    }

    #endregion
}
