﻿using ContinuityPatrol.Application.Features.Server.Queries.GetServerByServerName;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerByServerNameQueryHandlerTests : IClassFixture<ServerFixture>
{
    private readonly ServerFixture _serverFixture;
    private readonly GetServerByServerNameQueryHandler _handler;

    public GetServerByServerNameQueryHandlerTests(ServerFixture serverFixture)
    {
        _serverFixture = serverFixture;

        var mockServerRepository = ServerRepositoryMocks.GetServerByServerNameRepository(_serverFixture.Servers);

        _handler = new GetServerByServerNameQueryHandler(_serverFixture.Mapper, mockServerRepository.Object);

        _serverFixture.Servers[0].Properties = "{\"Name\": \"admin\", \"password\": \"sMJQhxC4i2EA2P1gYrvS28WIeJ5oVBiRMFPbmc/mPFc=$7+r7KhHaxwlJR856USBDGQuWvKiKJSvnCv4Ff0oxUQC9lNXNiw==\"}";

        _serverFixture.Servers[0].LicenseKey = SecurityHelper.Encrypt(_serverFixture.Servers[0].LicenseKey);
    }

    [Fact]
    public async Task Handle_Return_Valid_ServersByServerName()
    {
        var result = await _handler.Handle(new GetServerByServerNameQuery { ServerName = _serverFixture.Servers[0].Name }, CancellationToken.None);

        result.ShouldBeOfType<ServerByServerNameVm>();

        result.Id.ShouldBe(_serverFixture.Servers[0].ReferenceId);
        result.Name.ShouldBe(_serverFixture.Servers[0].Name);
        result.CompanyId.ShouldBe(_serverFixture.Servers[0].CompanyId);
        result.SiteId.ShouldBe(_serverFixture.Servers[0].SiteId);
        result.SiteName.ShouldBe(_serverFixture.Servers[0].SiteName);
        result.RoleType.ShouldBe(_serverFixture.Servers[0].RoleType);
        result.Status.ShouldBe(_serverFixture.Servers[0].Status);
        result.ServerType.ShouldBe(_serverFixture.Servers[0].ServerType);
        result.OSType.ShouldBe(_serverFixture.Servers[0].OSType);
        result.Properties.ShouldBe(_serverFixture.Servers[0].Properties);
        result.LicenseKey.ShouldBe(_serverFixture.Servers[0].LicenseKey);
    }
}