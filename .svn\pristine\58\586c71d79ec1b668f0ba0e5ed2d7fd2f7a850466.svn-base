﻿namespace ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;

public class GetByInfraMasterNameQueryHandler : IRequestHandler<GetByInfraMasterNameQuery, List<GetByInfraMasterNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationMasterRepository _replicationMasterRepository;

    public GetByInfraMasterNameQueryHandler(IMapper mapper, IReplicationMasterRepository replicationMasterRepository)
    {
        _mapper = mapper;
        _replicationMasterRepository = replicationMasterRepository;
    }

    public async Task<List<GetByInfraMasterNameVm>> Handle(GetByInfraMasterNameQuery request,
        CancellationToken cancellationToken)
    {
        var replicationMaster =
            await _replicationMasterRepository.GetReplicationMasterByInfraMasterName(request.InfraMasterName);

        return replicationMaster.Count <= 0
            ? new List<GetByInfraMasterNameVm>()
            : _mapper.Map<List<GetByInfraMasterNameVm>>(replicationMaster);
    }
}