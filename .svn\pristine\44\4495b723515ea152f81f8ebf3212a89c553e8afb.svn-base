﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title" title="Approval Matrix">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix</span>
                    </h6>
                </div>
                <form class="d-flex">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                        <div class="input-group-text">
                            <div class="dropdown">
                                <span data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-funnel"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Name
                                            </label>
                                        </div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#CreateModal">
                        <i class="cp-add me-1"></i>Create
                    </button>
                </form>

            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="text-center">Sr.No</th>
                        <th>Matrix Code &amp; Name</th>
                        <th>Matrix User</th>
                        <th>Matrix Create Update Details</th>
                        <th class="text-center">Matrix Owner</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="tablebody">
                </tbody>
            </table>

        </div>
    </div>

    <!--Modal Create-->
    <div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl ">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="page_title">
                        <i class="cp-escalation_matrix_header-icon-3"></i>
                        <span>Escalation Matrix</span>
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="workflow-action row row-cols-2">

                        <div class="col">
                            <div class="mx-auto" style="width: 30rem;">
                                <button type="button" class="btn btn-primary btn-sm">End</button>
                                <div class="Escalation_Timeline">
                                    <ul class="ul">
                                        <li class="li">
                                            <span>
                                                <button type="button"
                                                        class="rounded-circle btn btn-primary btn-sm">
                                                    +
                                                </button>
                                            </span>
                                        </li>
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-danger">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level bg-danger badge bg-primary">
                                                        Level
                                                        4
                                                    </span>
                                                    <div class="d-grid ms-3">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Management
                                                        </h6>
                                                        <span>
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                        </span>
                                                    </div>
                                                    <div class="d-flex ms-auto">
                                                        <span class="text-primary me-2 text-truncate">
                                                            <i class="cp-apply-finish-time"></i>
                                                            &nbsp;08 Hour
                                                        </span>
                                                        <i class="cp-edit fs-5 me-2"></i>
                                                        <i class="cp-Delete fs-5 me-2" data-bs-toggle="modal" data-bs-target="#DeleteModal" role="button"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-primary">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level bg-primary badge bg-primary">
                                                        Level
                                                        3
                                                    </span>
                                                    <div class="d-grid ms-3">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Management
                                                        </h6>
                                                        <span>
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                        </span>
                                                    </div>
                                                    <div class="d-flex ms-auto">
                                                        <span class="text-primary me-2 text-truncate">
                                                            <i class="cp-apply-finish-time"></i>
                                                            &nbsp;08 Hour
                                                        </span>
                                                        <i class="cp-edit fs-5 me-2"></i>
                                                        <i class="cp-Delete fs-5 me-2"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-warning">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level bg-warning badge bg-warning">
                                                        Level
                                                        3
                                                    </span>
                                                    <div class="d-grid ms-3">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Management
                                                        </h6>
                                                        <span>
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                        </span>
                                                    </div>
                                                    <div class="d-flex ms-auto">
                                                        <span class="text-primary me-2 text-truncate">
                                                            <i class="cp-apply-finish-time"></i>
                                                            &nbsp;08 Hour
                                                        </span>
                                                        <i class="cp-edit fs-5 me-2"></i>
                                                        <i class="cp-Delete fs-5 me-2"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="li">
                                            <div class="Escalation_Timeline_Card card border-success">
                                                <div class="d-flex align-items-center">
                                                    <span class="Timeline_Card_Level bg-success badge bg-success">
                                                        Level
                                                        3
                                                    </span>
                                                    <div class="d-grid ms-3">
                                                        <h6 class="mb-1 text-truncate" title="Management">
                                                            Management
                                                        </h6>
                                                        <span>
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                            <img class="rounded-circle" alt=""
                                                                 src="/img/input_Icons/user-3.jpg" width="20"
                                                                 height="20">
                                                        </span>
                                                    </div>
                                                    <div class="d-flex ms-auto">
                                                        <span class="text-primary me-2 text-truncate">
                                                            <i class="cp-apply-finish-time"></i>
                                                            &nbsp;08 Hour
                                                        </span>
                                                        <i class="cp-edit fs-5 me-2"></i>
                                                        <i class="cp-Delete fs-5 me-2"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>

                                    </ul>
                                    <button type="button" class="btn btn-primary btn-sm">Start Escalation</button>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="mb-0 h-100 card shadow-sm">
                                <div class="card-body">
                                    <ul class="ms-2 nav nav-pills" id="pills-tab" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="pills-Level-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-Level" type="button" role="tab"
                                                    aria-controls="pills-Level" aria-selected="true">
                                                <i class="cp-teams"></i> Level
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="pills-Teams-tab" data-bs-toggle="pill"
                                                    data-bs-target="#pills-Teams" type="button" role="tab"
                                                    aria-controls="pills-Teams" aria-selected="false">
                                                <i class="cp-teams"></i> Teams
                                            </button>
                                        </li>
                                    </ul>

                                    <div class="tab-content mt-3" id="pills-tabContent">
                                        <div class="tab-pane fade show active" id="pills-Level" role="tabpanel"
                                             aria-labelledby="pills-Level-tab" tabindex="0">
                                            <form>
                                                <div class="mb-3 form-group">
                                                    <div class="form-label">Level Name</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-name"></i>
                                                        </span>
                                                        <input type="text" class="form-control"
                                                               placeholder="Enter Level Name" />
                                                    </div>
                                                </div>
                                                <div class="mb-3 form-group">
                                                    <div class="form-label">Level Description</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text">
                                                            <i class="cp-description"></i>
                                                        </span>
                                                        <input type="text" class="form-control"
                                                               placeholder="Enter Level Description" />
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="d-flex align-items-end gap-3 mb-3">
                                                        <div class="form-group w-50">
                                                            <label class="form-label custom-cursor-default-hover"
                                                                   for="formBasicEmail">Escalation Time</label>
                                                            <div class="input-group">
                                                                <span class="input-group-text" id="basic-addon1">
                                                                    <i class="cp-apply-finish-time"></i>
                                                                </span>
                                                                <select class="form-select-modal">
                                                                    <option>Select Hours</option>
                                                                    <option value="1">1</option>
                                                                    <option value="2">2</option>
                                                                    <option value="3">3</option>
                                                                </select>
                                                                <span class="input-group-text form-label mb-0">Mins</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group w-50">
                                                            <div class="input-group">
                                                                <span class="input-group-text" id="basic-addon1">
                                                                    <i class="cp-apply-finish-time"></i>
                                                                </span>
                                                                <select aria-label="Default select example"
                                                                        class="form-select-modal" id="formBasicEmail">
                                                                    <option>Select Mins</option>
                                                                    <option value="1">1</option>
                                                                    <option value="2">2</option>
                                                                    <option value="3">3</option>
                                                                </select>
                                                                <span class="input-group-text form-label mb-0">Mins</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-label">Business Service Name</div>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="cp-business-service"></i></span>
                                                        <select class="form-select-modal" title="Some placeholder text...">
                                                            <option value="One">Business Service</option>
                                                            <option value="Two">Two</option>
                                                            <option value="Three">Three</option>

                                                        </select>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="form-check form-check-inline">
                                                        <input name="group1"
                                                               type="checkbox"
                                                               class="form-check-input custom-cursor-default-hover"><label title=""
                                                                                                                           class="form-check-label custom-cursor-default-hover">
                                                            Workflow
                                                            Creation
                                                        </label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input name="group1"
                                                               type="checkbox"
                                                               class="form-check-input custom-cursor-default-hover"
                                                               cursorshover="true"><label title=""
                                                                                          class="form-check-label custom-cursor-default-hover">
                                                            Workflow
                                                            Modification
                                                        </label>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <div class="tab-pane fade show" id="pills-Teams" role="tabpanel"
                                             aria-labelledby="pills-Teams-tab" tabindex="0">

                                            <div class="d-flex">
                                                <div class="Filter_Search me-2 input-group">
                                                    <input autocomplete="off" type="search" id="txtSearch"
                                                           class="form-control" value="" style="min-height: 34px;">
                                                    <span class="ps-1  input-group-text">
                                                        <i class="cp-search"></i>

                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="dropdown dropend">
                                                        <button type="button" class="btn btn-primary"
                                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                                data-bs-auto-close="outside">
                                                            Add
                                                        </button>
                                                        <div class="dropdown-menu py-0 "
                                                             style="width:16rem; border:1px solid #e9e9e9 !important;">

                                                            <form class="p-2">
                                                                <div class="mb-3 form-group">
                                                                    <div class="form-label">Name</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-name"></i>
                                                                        </span>
                                                                        <input type="text" class="form-control"
                                                                               placeholder="Enter Name" />
                                                                    </div>
                                                                </div>
                                                                <div class="mb-3 form-group">
                                                                    <div class="form-label">Email ID</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-email"></i>
                                                                        </span>
                                                                        <input type="email" class="form-control"
                                                                               placeholder="Enter Email" />
                                                                    </div>
                                                                </div>
                                                                <div class="mb-3 form-group">
                                                                    <div class="form-label">Phone Number</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-mobile-icon"></i>
                                                                        </span>
                                                                        <input type="text" class="form-control"
                                                                               placeholder="Enter Phone Number" />
                                                                    </div>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <div class="form-label">Teams Group</div>
                                                                    <div class="input-group">
                                                                        <span class="input-group-text">
                                                                            <i class="cp-teams"></i>
                                                                        </span>
                                                                        <select class="form-select-modal"
                                                                                title="Some placeholder text...">
                                                                            <option value="One">One</option>
                                                                            <option value="Two">Two</option>
                                                                            <option value="Three">Three</option>

                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <div class="text-end">
                                                                    <button type="submit"
                                                                            class="btn btn-secondary">
                                                                        cancel
                                                                    </button>
                                                                    <button type="submit"
                                                                            class="btn btn-primary">
                                                                        save
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>


                                                </div>
                                            </div>

                                            <div class="accordion mt-3 border border-secondary-subtle rounded"
                                                 id="accordionExample">
                                                <div class="accordion-item border-secondary-subtle">
                                                    <div class="border-secondary-subtle border-bottom  p-2 d-flex justify-content-between">
                                                        <span>Team Name</span><span>
                                                            <span class="me-3">Mail</span><span class="me-4">SMS</span>
                                                        </span>
                                                    </div>
                                                    <h2 class="accordion-header p-2">
                                                        <button class="accordion-button" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                                                aria-expanded="true" aria-controls="collapseOne">
                                                            <div class="d-flex justify-content-between w-100 align-items-center">
                                                                <div class="form-check">
                                                                    <input type="checkbox" class="form-check-input">
                                                                    <label title=""
                                                                           class="form-check-label custom-cursor-default-hover"
                                                                           cursorshover="true">Dev_Team</label>
                                                                </div>
                                                                <span>
                                                                    <span class="me-4">
                                                                        <i class="cp-email
                                                                       "></i>
                                                                    </span>
                                                                    <span class="me-1">
                                                                        <i class="cp-mobile-icon
                                                                       "></i>
                                                                    </span>
                                                                </span>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="collapseOne" class="accordion-collapse collapse show"
                                                         data-bs-parent="#accordionExample">
                                                        <div class="accordion-body pt-0">
                                                            <table class="table-borderless table">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="mt-0 align-middle form-check">
                                                                                <input type="checkbox"
                                                                                       class="form-check-input"
                                                                                       cursorshover="true"><label title=""
                                                                                                                  class="form-check-label"
                                                                                                                  cursorshover="true">Dev_Team</label>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <img class="rounded-circle me-2"
                                                                                 alt="userimg"
                                                                                 src="/img/input_Icons/user-3.jpg"
                                                                                 width="30" height="30"
                                                                                 title="User">Ragul
                                                                        </td>
                                                                        <td><EMAIL> </td>

                                                                        <td>
                                                                            <div>
                                                                                <input type="checkbox" name="toggle"
                                                                                       id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header  p-2">
                                                        <button class="accordion-button collapsed" type="button"
                                                                data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                                                aria-expanded="false" aria-controls="collapseTwo">
                                                            <div class="d-flex justify-content-between w-100 align-items-center">
                                                                <div class="form-check">
                                                                    <input type="checkbox" class="form-check-input">
                                                                    <label title=""
                                                                           class="form-check-label custom-cursor-default-hover"
                                                                           cursorshover="true">Dev_Team</label>
                                                                </div>
                                                                <span>
                                                                    <span class="me-4">
                                                                        <i class="cp-email
                                                                       "></i>
                                                                    </span>
                                                                    <span class="me-1">
                                                                        <i class="cp-mobile-icon
                                                                       "></i>
                                                                    </span>
                                                                </span>
                                                            </div>
                                                        </button>
                                                    </h2>
                                                    <div id="collapseTwo" class="accordion-collapse collapse"
                                                         data-bs-parent="#accordionExample">
                                                        <div class="accordion-body pt-0">
                                                            <table class="table-borderless table">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="mt-0 align-middle form-check">
                                                                                <input type="checkbox"
                                                                                       class="form-check-input"
                                                                                       cursorshover="true"><label title=""
                                                                                                                  class="form-check-label"
                                                                                                                  cursorshover="true">Dev_Team</label>
                                                                            </div>
                                                                        </td>
                                                                        <td>
                                                                            <img class="rounded-circle me-2"
                                                                                 alt="userimg"
                                                                                 src="/img/input_Icons/user-3.jpg"
                                                                                 width="30" height="30"
                                                                                 title="User">Ragul
                                                                        </td>
                                                                        <td><EMAIL> </td>

                                                                        <td>
                                                                            <div>
                                                                                <input type="checkbox" name="toggle"
                                                                                       id="SMSNotificationtoggle"><label for="SMSNotificationtoggle"></label>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <small class="text-secondary">
                        <i class="bi bi-info-circle me-1"></i>Note: All fields are mandatory
                        except Optional
                    </small>
                    <div class="gap-2 d-flex">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" id="btnClick">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Modal Delete-->
    <div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" />
                </div>
                <div class="modal-body text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p>
                        You want to delete <span class="font-weight-bolder text-primary">Congizant Techno Services</span>
                        data?
                    </p>
                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary btn-sm">Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/js/escalationmatrix.js"></script>
