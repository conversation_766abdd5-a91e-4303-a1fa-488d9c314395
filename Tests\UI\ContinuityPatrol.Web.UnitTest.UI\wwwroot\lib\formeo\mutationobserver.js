// mutationobserver-shim v0.3.3 (github.com/megawac/MutationObserver.js)
// Authors: <AUTHORS>
/*
 Shim for MutationObserver interface
 Author: <PERSON> (github.com/megawac)
 Repository: https://github.com/megawac/MutationObserver.js
 License: WTFPL V2, 2004 (wtfpl.net).
 Though credit and staring the repo will make me feel pretty, you can modify and redistribute as you please.
 Attempts to follow spec (https://www.w3.org/TR/dom/#mutation-observers) as closely as possible for native javascript
 See https://github.com/WebKit/webkit/blob/master/Source/WebCore/dom/MutationObserver.cpp for current webkit source c++ implementation
*/
window.MutationObserver || (window.MutationObserver = function (y) {
    function z(a) { this.i = []; this.m = a } function K(a) { (function c() { var d = a.takeRecords(); d.length && a.m(d, a); a.h = setTimeout(c, z._period) })() } function r(a) { var b = { type: null, target: null, addedNodes: [], removedNodes: [], previousSibling: null, nextSibling: null, attributeName: null, attributeNamespace: null, oldValue: null }, c; for (c in a) b[c] !== y && a[c] !== y && (b[c] = a[c]); return b } function L(a, b) {
        var c = E(a, b); return function (d) {
            var f = d.length; b.a && 3 === a.nodeType &&
                a.nodeValue !== c.a && d.push(new r({ type: "characterData", target: a, oldValue: c.a })); b.b && c.b && C(d, a, c.b, b.f); if (b.c || b.g) var m = M(d, a, c, b); if (m || d.length !== f) c = E(a, b)
        }
    } function N(a, b) { return b.value } function O(a, b) { return "style" !== b.name ? b.value : a.style.cssText } function C(a, b, c, d) {
        for (var f = {}, m = b.attributes, k, g, p = m.length; p--;)k = m[p], g = k.name, d && d[g] === y || (F(b, k) !== c[g] && a.push(r({ type: "attributes", target: b, attributeName: g, oldValue: c[g], attributeNamespace: k.namespaceURI })), f[g] = !0); for (g in c) f[g] || a.push(r({
            target: b,
            type: "attributes", attributeName: g, oldValue: c[g]
        }))
    } function M(a, b, c, d) {
        function f(g, p, t, q, x) { var A = g.length - 1; x = -~((A - x) / 2); for (var h, l, e; e = g.pop();)h = t[e.j], l = q[e.l], d.c && x && Math.abs(e.j - e.l) >= A && (a.push(r({ type: "childList", target: p, addedNodes: [h], removedNodes: [h], nextSibling: h.nextSibling, previousSibling: h.previousSibling })), x--), d.b && l.b && C(a, h, l.b, d.f), d.a && 3 === h.nodeType && h.nodeValue !== l.a && a.push(r({ type: "characterData", target: h, oldValue: l.a })), d.g && m(h, l) } function m(g, p) {
            for (var t = g.childNodes,
                q = p.c, x = t.length, A = q ? q.length : 0, h, l, e, n, v, B = 0, w = 0, u = 0; w < x || u < A;)n = t[w], v = (e = q[u]) && e.node, n === v ? (d.b && e.b && C(a, n, e.b, d.f), d.a && e.a !== y && n.nodeValue !== e.a && a.push(r({ type: "characterData", target: n, oldValue: e.a })), l && f(l, g, t, q, B), d.g && (n.childNodes.length || e.c && e.c.length) && m(n, e), w++, u++) : (k = !0, h || (h = {}, l = []), n && (h[e = G(n)] || (h[e] = !0, -1 === (e = H(q, n, u, "node")) ? d.c && (a.push(r({ type: "childList", target: g, addedNodes: [n], nextSibling: n.nextSibling, previousSibling: n.previousSibling })), B++) : l.push({ j: w, l: e })),
                    w++), v && v !== t[w] && (h[e = G(v)] || (h[e] = !0, -1 === (e = H(t, v, w)) ? d.c && (a.push(r({ type: "childList", target: p.node, removedNodes: [v], nextSibling: q[u + 1], previousSibling: q[u - 1] })), B--) : l.push({ j: e, l: u })), u++)); l && f(l, g, t, q, B)
        } var k; m(b, c); return k
    } function E(a, b) {
        var c = !0; return function m(f) {
            var k = { node: f }; !b.a || 3 !== f.nodeType && 8 !== f.nodeType ? (b.b && c && 1 === f.nodeType && (k.b = I(f.attributes, function (g, p) { if (!b.f || b.f[p.name]) g[p.name] = F(f, p); return g }, {})), c && (b.c || b.a || b.b && b.g) && (k.c = P(f.childNodes, m)), c = b.g) : k.a =
                f.nodeValue; return k
        }(a)
    } function G(a) { try { return a.id || (a.mo_id = a.mo_id || J++) } catch (b) { try { return a.nodeValue } catch (c) { return J++ } } } function P(a, b) { for (var c = [], d = 0; d < a.length; d++)c[d] = b(a[d], d, a); return c } function I(a, b, c) { for (var d = 0; d < a.length; d++)c = b(c, a[d], d, a); return c } function H(a, b, c, d) { for (; c < a.length; c++)if ((d ? a[c][d] : a[c]) === b) return c; return -1 } z._period = 30; z.prototype = {
        observe: function (a, b) {
            for (var c = {
                b: !!(b.attributes || b.attributeFilter || b.attributeOldValue), c: !!b.childList, g: !!b.subtree,
                a: !(!b.characterData && !b.characterDataOldValue)
            }, d = this.i, f = 0; f < d.length; f++)d[f].s === a && d.splice(f, 1); b.attributeFilter && (c.f = I(b.attributeFilter, function (m, k) { m[k] = !0; return m }, {})); d.push({ s: a, o: L(a, c) }); this.h || K(this)
        }, takeRecords: function () { for (var a = [], b = this.i, c = 0; c < b.length; c++)b[c].o(a); return a }, disconnect: function () { this.i = []; clearTimeout(this.h); this.h = null }
    }; var D = document.createElement("i"); D.style.top = 0; var F = (D = "null" != D.attributes.style.value) ? N : O, J = 1; return z
}(void 0));
//# sourceMappingURL=mutationobserver.map