﻿$(function () {
    //Functions
    async function getdata(profileId) {       
            const data = { profileId };
            const workflowProfileInfo = await getAysncWithHandler(RootUrl + workflowProfileURL.getWorkflowProfileUrl,data,OnError);            
            if (!workflowProfileInfo?.length) {
                $("#workflowtable").append(NoDataFoundImage);
                return false;
            }           
            const profileInfos = workflowProfileInfo[0]?.workflowProfileInfos || [];
            let isRunning = profileInfos.some(p => p?.isRunning);
            $("#btnSaveProfile,#btnProfileChangePassword,#btnWorkflowProfileEdit,#btnWorkfloProfileDelete").prop("disabled", isRunning).toggleClass("icon-disabled", isRunning);               
            $('.actionWorkflowprofileEdit, .actionWorkflowprofileDelete').prop('disabled', isRunning);
            $('#workprofilerunning').toggle(isRunning);            
            profileInfos.forEach((profile, index) => {
            appendProfileData(index, profile);
            });        
    }
    function appendProfileData(index, data) {     
        let actionHtml = "";
        if (createPermission === 'true') {
            actionHtml = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                <i role="button" title="Remove" id="deleteProfileId"
                   class="cp-circle-minus fs-5 text-secondary actionWorkflowprofileDelete actionIcon ${data.isRunning ? "icon-disabled text-muted" : ""}"
                   data-bs-toggle="modal" data-bs-target="#DeleteWorkflowProfileModal"
                   workflowProInfoId="${data.id}" workflowName="${data.workflowName ?? "NA"}"
                   ${data.isRunning ? "disabled style='pointer-events: none;'" : ""}></i>

                <i role="button" title="Edit" id="editProfileId"
                   class="cp-edit fs-6 text-primary actionWorkflowprofileEdit actionIcon ${data.isRunning ? "icon-disabled text-muted" : ""}"
                   style="border: none; background-color: transparent;"
                   workflowProInfoId="${data.id}" workflowName="${data.workflowName ?? "NA"}"
                   ${data.isRunning ? "disabled style='pointer-events: none;'" : ""}></i>
            </div>`;
        } else {
            actionHtml = `<i title="Remove" class="icon-disabled"></i>`;
        }
        const html = `
        <tr>
            <td class="text-truncate">${index + 1}</td>
            <td class="text-truncate">${data.businessServiceName ?? "NA" }</td>
            <td class="text-truncate">${data.businessFunctionName ?? "NA"}</td>
            <td class="text-truncate">${data.infraObjectName ?? "NA"}</td>
            <td class="text-truncate">${data.workflowName ?? "NA"}</td>
            <td class="Action-th text-center" style="padding-left: 38px;">${actionHtml}</td>
        </tr>
        <input id="proId" type="hidden" value="${data.id}" class="form-control" />`;
        $("#workflowtable").append(html);
    }
    function validateDropDown(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }  

    async function populateProfileModalFields(profileData) {
        $('#textProfileId').val(profileData.id);
        $('#workflowConfirmPassword').val(profileData.password);
        $('#textProfileName').val(profileData.name);
        $('#workflowPassword').val(profileData.password);
        $('#selectExecutionPolicy').val(profileData.executionPolicy);
        if (profileData.executionPolicy == '1') {
            getGroupNode(profileData);
        } else {
            $('#groupPolicy').hide();
        }
        $('#textProfileNameError, #selectExecutionPolicyError, #selectGroupPolicyError').text('').removeClass('field-validation-error');
    }

    async function getGroupNode(grpNodeData) {
        $('#groupPolicy').show();
        await $.ajax({
            type: "GET",
            url: RootUrl + workflowProfileURL.getGroupNode,
            dataType: "json",
            success: function (response) {
                if (response && response.length > 0) {
                    const $dropdown = $('#selectGroupPolicy');
                    $dropdown.empty().append('<option value="">Select group node policy</option>');
                    response.forEach(item => {
                        if (item?.groupName) {
                            $dropdown.append(`<option value="${item.id}" data-name="${item.groupName}">${item.groupName}</option>`);
                        }
                    });
                    const selectedId = grpNodeData?.groupPolicyId;
                    $dropdown.val(selectedId).trigger('change');
                    const selectedOption = $dropdown.find(`option[value="${selectedId}"]`);
                    const selectedName = selectedOption.text();
                    $('#textGroupPolicyId').val(selectedId);
                    $('#selectGroupPolicyName').val(selectedName);
                }
            }
        });
    }

    async function IsSameNameExist(url, inputValue) {
        return !inputValue.name.trim() ? true : (await getAysncWithHandler(url, inputValue, OnError)) ? " Profile name already exists" : true;
    }

    async function profileValidateName(value, id = null, IsNameUrl) {
        const errorElement = $('#ProfileNameError');
        if (!value) {
            errorElement.text('Enter profile name').addClass('field-validation-error');
            return false;
        }
        else if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        else {
            let url = RootUrl + IsNameUrl;
            let data = {};
            data.name = value;
            data.id = id;
            const validationResults = [
                SpecialCharValidateCustom(value),
                ShouldNotBeginWithUnderScore(value),
                ShouldNotBeginWithSpace(value),
                OnlyNumericsValidate(value),
                SpaceWithUnderScore(value),
                ShouldNotEndWithUnderScore(value),
                ShouldNotEndWithSpace(value),
                MultiUnderScoreRegex(value),
                SpaceAndUnderScoreRegex(value),
                minMaxlength(value, 200),
                secondChar(value),
                await IsSameNameExist(url, data)
            ];
            return CommonValidation(errorElement, validationResults);
        }
    }
    function sanitizeInput(value) {
        return value?.replace(/\s+/g, '');
    }

    async function PasswordPolicy(value, errorElement) {        
        let defaultSkey = "Password Policy";
        try {
            const response = await $.ajax({
                type: "GET",
                url: RootUrl + workflowProfileURL.settingList,
                async: true
            });
            if (response && response.length > 0) {
                const passwordPolicy = response.find(pwdplcy => pwdplcy.sKey === defaultSkey);
                if (passwordPolicy) {
                    let passwordRules = JSON.parse(passwordPolicy.sValue);
                    let minSValue = passwordRules.minSValue;
                    let maxSValue = passwordRules.maxSValue;
                    let minUpSValue = passwordRules.minUpSValue;
                    let minNumSValue = passwordRules.minNumSValue;
                    let minLowSValue = passwordRules.minLowSValue;
                    let minSpclSValue = passwordRules.minSpclSValue;
                    if (value == "Password_strength") {

                        return { minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue };
                    }
                    return validatePolicyPassword(value, errorElement, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue);
                }
                else {
                    return settingPassword(value)
                }
            }
            else {
                return settingPassword(value)
            }
        } catch (error) {
            console.error("Error fetching password policy: " + error);
            return "Error fetching password policy";
        }

    }
    // Events
    // Select Profile Name
    $('#selectWorkflowprofileName').on('change', async function () {
        $("#workprofilerunning").hide();
        let selectedValue = $('#selectWorkflowprofileName option:selected').text();
        let workflowProfileId = $('#selectWorkflowprofileName').val();
        $("#profiletable, #btnSaveProfile, #btnWorkflowProfileEdit, #btnWorkfloProfileDelete, #btnProfileChangePassword").show();
        $("#WorkflowImage,#btnWorkflowProfileEditProfile,#btnWorkflowProfileResetProfile, #btnEditProfile").hide();
        $("#profileId").val(workflowProfileId);
        $("#profilename").val(selectedValue);
        $("#workflowtable").empty();
        clearProfileInfo();
        $('#operationalServiceError, #operationalFunctionError, #infraObjectError, #workflowError').text('').removeClass('field-validation-error');
        await getdata(workflowProfileId);
    });
    //Create button
    $('#btnWorkflowProfileCreate').on('click', function () {
        clearFields();
        $('#btnprofileSaveFunctions').text("Save");
        $('#wfProfilePassword, #wfProfileConfirmPassword').show()
        if ($('#selectExecutionPolicy').val() == "") {
            $('#groupPolicy').hide();
        }
    });   

    $('#textProfileName').on('keyup', commonDebounce(async function () {
        let profileId = $('#textProfileId').val();
        let elementValue = $(this).val();
        let sanitizedValue = elementValue.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await profileValidateName(elementValue, profileId, workflowProfileURL.nameUrl);
    }));

    $('#selectExecutionPolicy').on('change', function () {
        const value = $(this).val();
        if (value) {
            $('#groupPolicy').toggle(value === '1');
        }
        $('#selectGroupPolicy').val('').trigger('change');
        $('#GroupPolicyError').text('').removeClass('field-validation-error');
        validateDropDown(value, "Select execution policy", $('#ExecutionPolicyError'));
        $.ajax({
            type: "GET",
            url: RootUrl + workflowProfileURL.getGroupNode,
            dataType: "json",
            success: function (response) {
                if (response && Array.isArray(response)) {
                    const $dropdown = $('#selectGroupPolicy');
                    $dropdown.empty().append('<option value="">Select group node policy</option>');
                    response.forEach(item => {
                        if (item?.groupName) {
                            $dropdown.append(`<option value="${item?.id}">${item?.groupName}</option>`);
                        }
                    });
                }
            },
        });
    });

    $('#selectGroupPolicy').on('change', function () {
        const id = $("#selectGroupPolicy").val();
        $('#textGroupPolicyId').val(id);
        validateDropDown($(this).val(), "Select group node policy", $('#GroupPolicyError'));
    });
    // save and Update Function
    $('#btnprofileSaveFunctions').on('click', commonDebounce(async function () {
        const passwordd = $('#workflowPassword').val();
        const confirmPass = $('#workflowConfirmPassword').val();
        const isPassword = await validateLoginPassword(passwordd, $('#wfProfilePasswordError'));
        const isConfirmpass = await validateConfirmPassword(confirmPass, $('#wfProfileConfirmpasswordError'));
        const isName = await profileValidateName($("#textProfileName").val(), $("#textProfileId").val(), workflowProfileURL?.nameUrl);
        const isExectionPolicy = validateDropDown($('#selectExecutionPolicy').val(), "Select execution policy", $('#ExecutionPolicyError'));
        let isGroupPolicy = true;
        if ($('#selectExecutionPolicy').val() === '1') {
            isGroupPolicy = validateDropDown($('#selectGroupPolicy').val(), "Select group node policy", $('#GroupPolicyError'));
        }
        const isSaveAction = $('#btnprofileSaveFunctions').text() === 'Save';
        const isValid = isName && isExectionPolicy && isPassword && isConfirmpass && isGroupPolicy;
        if (!isValid) return;
        const postData = {
            Name: $("#textProfileName").val(),
            Password: passwordd,
            ConfirmPassword: confirmPass,
            ExecutionPolicy: $('#selectExecutionPolicy option:selected').val(),
            GroupPolicyName: $('#selectGroupPolicy option:selected').text(),
            GroupPolicyId: $('#selectGroupPolicy').val(),
            __RequestVerificationToken: gettoken()
        };
        if (!isSaveAction) {
            postData.Id = $("#selectWorkflowprofileName option:selected")?.attr('id');
        }
        $('#btnprofileSaveFunctions').prop('disabled', true).text(isSaveAction ? 'Save' : 'Update');
        await $.ajax({
            url: RootUrl + workflowProfileURL.getSavefunctionUrl,
            type: "POST",
            dataType: "json",
            data: postData,
            success: function (result) {
                if (result?.success && result?.data?.message?.length > 0) {
                    notificationAlert("success", result.data.message);
                    $('#CreateModal').modal('hide');
                    window.location.reload();
                } else {
                    errorNotification(result);
                }
            },
        });

    }, 800));
    //Edit button
    $(document).on('click', '#btnWorkflowProfileEdit', commonDebounce(async function () {
        let workflowProfileId = $('#selectWorkflowprofileName').val();
        if (workflowProfileId) {
            let data = { 'workflowProfileId': workflowProfileId };
            await $.ajax({
                type: "GET",
                url: RootUrl + workflowProfileURL.getpolicyUrl,
                data: data,
                dataType: "json",
                success: function (result) {
                    if (result&&result?.success) {
                        populateProfileModalFields(result.data);
                    } else {
                        errorNotification(result);
                    }
                },
            });
            $('#btnprofileSaveFunctions').text('Update');
            $('#wfProfilePassword,#wfProfileConfirmPassword').hide();
            $('#CreateModal').modal('show');
        }
    }, 800));
    ///Delete button
    $('#btnWorkfloProfileDelete').on('click', commonDebounce(function () {
        let profileId = $("#selectWorkflowprofileName").val();
        let profileName = $('#selectWorkflowprofileName option:selected').text();
        $('#deleteData').text(profileName);
        $('#textDeleteId').val(profileId);
        $('#DeleteModal').modal('show');
    }, 800));

    $('#btnProfileConfirmDelete').on('click', commonDebounce(async function () {
        let profileId = $("#selectWorkflowprofileName").val();
            const result = await $.ajax({
                url: RootUrl + workflowProfileURL.getDeleteUrl,
                type: "DELETE",
                dataType: "json",
                data: { id: profileId }
            });
            if (result?.success && result?.data?.message?.length > 0) {
                notificationAlert("success", result.data.message);
                $('#DeleteModal').modal('hide');
                window.location.reload();
            } else {
                errorNotification(result);
                $('#DeleteModal').modal('hide');
            }

    }, 800));
   
    ///Password button
    $('#workflowConfirmPassword').on('input', async function () {
        const errorElement = $('#wfProfileConfirmpasswordError');
        const encryptedPassword = $('#workflowPassword').val();
        const confirmPassword = sanitizeInput(this.value);
        let decryptedPassword = '';
        if (encryptedPassword) {
            decryptedPassword = await DecryptPassword(encryptedPassword);
        }
        inputConfirmpassword(this.id, confirmPassword);
        if (!confirmPassword) {
            errorElement.text('Enter confirm password').addClass('field-validation-error');
            return;
        }
        if (decryptedPassword && confirmPassword !== decryptedPassword) {
            errorElement.text('Password does not match').addClass('field-validation-error');
            return;
        }
        errorElement.text('').removeClass('field-validation-error');
    });

    $('#workflowPassword').on('input', function () {
        const errorElement = $('#wfProfilePasswordError');
        const passwordValue = sanitizeInput(this.value);
        inputpassword(this.id, passwordValue);
        $('#workflowConfirmPassword').val('');
        $('#wfProfileConfirmpasswordError').text('').removeClass('field-validation-error');
        if (!passwordValue) {
            errorElement.text('Enter password').addClass('field-validation-error');
        } else {
            errorElement.text('').removeClass('field-validation-error');
            PasswordPolicy(passwordValue, errorElement);
        }
    });    

    $(document).on('blur', '#workflowPassword, #workflowConfirmPassword', function () {
        if (this.value) {
            const value = sanitizeInput(this.value);
            blurpassword(this.id, value);
        }
    });

    $('#workflowPassword').on('focus', function () {
        if (this.id) focuspassword(this.id);
    });

    $('#workflowConfirmPassword').on('focus', function () {
        if (this.id) focusconfirmpassword(this.id);
    });   

    $('#btnProfileChangePassword').on('click', function () {
        clearDataFields();
        $('#ProfileChangepasswordModal').modal('show');
    });

    //Valaidation field

    const clearProfileInfo = () => {
        $('#operationalServiceId').val('').trigger('change');
        $('#operationalFunction, #infraObject, #workflowName').empty();
    };

    const clearFields = () => {
        const errorElements = ['#ProfileNameError', '#wfProfilePasswordError', '#wfProfileConfirmpasswordError', '#GroupPolicyError', '#ExecutionPolicyError']
        $('#textProfileName, #workflowPassword, #workflowConfirmPassword, #textProfileId').val('');
        $('#selectGroupPolicy, #selectExecutionPolicy').val('').trigger('change');
        $('.input-group-text.toggle-password').html('<i class="cp-password-visible fs-6"></i>');
        errorElements.forEach(element => {$(element).text('').removeClass('field-validation-error');
        });         
    };

    const clearDataFields = () => {
        $('#wfProfileCurrentPassword, #wfProfileNewPassword, #wfProfileConfirmPassword').val('');
        ['#profileCurrentPasswordError', '#wfProfileNewPasswordError', '#profileConfirmPasswordError'].forEach(selector => {
            $(selector).text('').removeClass('field-validation-error');
        });
    };
})
