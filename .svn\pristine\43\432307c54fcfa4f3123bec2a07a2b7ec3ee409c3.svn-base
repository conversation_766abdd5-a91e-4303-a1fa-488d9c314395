﻿using ContinuityPatrol.Application.Features.RoboCopy.Commands.Delete;
using ContinuityPatrol.Application.Features.RoboCopy.Events.Delete;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Commands
{
    public class DeleteRoboCopyTests
    {
        private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly DeleteRoboCopyCommandHandler _handler;

        public DeleteRoboCopyTests()
        {
            _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new DeleteRoboCopyCommandHandler(
                _mockRoboCopyRepository.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldDeleteRoboCopySuccessfully()
        {
            var command = new DeleteRoboCopyCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            var roboCopyEntity = new Domain.Entities.RoboCopy
            {
                ReferenceId = command.Id,
                Name = "TestRoboCopy",
                IsActive = true
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(roboCopyEntity);

            _mockRoboCopyRepository
                .Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()))
                .Returns(ToString);

            _mockPublisher
                .Setup(p => p.Publish(It.IsAny<RoboCopyDeletedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.False(result.IsActive);
            Assert.Contains(roboCopyEntity.Name, result.Message);

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.RoboCopy>(r => !r.IsActive)), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRoboCopyDoesNotExist()
        {
            var command = new DeleteRoboCopyCommand
            {
                Id = Guid.NewGuid().ToString(),
            };

            _mockRoboCopyRepository
                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.RoboCopy)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowArgumentException_WhenIdIsInvalid()
        {
            var command = new DeleteRoboCopyCommand
            {
                Id = Guid.Empty.ToString(),
            };

            await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));

            _mockRoboCopyRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Never);
            _mockRoboCopyRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.RoboCopy>()), Times.Never);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
