﻿using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetByOperationGroupId;

public class GetBulkImportActionResultByGroupIdQueryHandler : IRequestHandler<GetBulkImportActionResultByGroupIdQuery, List<BulkImportActionResultListVm>>
{
    private readonly IMapper _mapper;
    private readonly IBulkImportActionResultRepository _bulkImportActionResultRepository;

    public GetBulkImportActionResultByGroupIdQueryHandler(IMapper mapper, IBulkImportActionResultRepository bulkImportActionResultRepository)
    {
        _mapper = mapper;
        _bulkImportActionResultRepository = bulkImportActionResultRepository;
    }

    public async Task<List<BulkImportActionResultListVm>> Handle(GetBulkImportActionResultByGroupIdQuery request, CancellationToken cancellationToken)
    {
        var bulkImportActionResults = await _bulkImportActionResultRepository.GetBulkImportActionResultOperationGroupId(request.BulkImportOperationGroupId);

        return bulkImportActionResults.Count == 0
            ? new List<BulkImportActionResultListVm>()
            : _mapper.Map<List<BulkImportActionResultListVm>>(bulkImportActionResults);
    }
}