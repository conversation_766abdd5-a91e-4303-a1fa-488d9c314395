using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AdPasswordExpireRepositoryTests : IClassFixture<AdPasswordExpireFixture>
{
    private readonly AdPasswordExpireFixture _adPasswordExpireFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AdPasswordExpireRepository _repository;

    public AdPasswordExpireRepositoryTests(AdPasswordExpireFixture adPasswordExpireFixture)
    {
        _adPasswordExpireFixture = adPasswordExpireFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AdPasswordExpireRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;

        // Act
        var result = await _repository.AddAsync(adPasswordExpire);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordExpire.UserName, result.UserName);
        Assert.Single(_dbContext.AdPasswordExpires);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        await _repository.AddAsync(adPasswordExpire);

        adPasswordExpire.UserName = "UpdatedUser";
        adPasswordExpire.Email = "<EMAIL>";

        // Act
        var result = await _repository.UpdateAsync(adPasswordExpire);

        // Assert
        Assert.Equal("UpdatedUser", result.UserName);
        Assert.Equal("<EMAIL>", result.Email);

    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        await _repository.AddAsync(adPasswordExpire);

        // Act
        var result = await _repository.DeleteAsync(adPasswordExpire);

        // Assert
        Assert.Equal(adPasswordExpire.UserName, result.UserName);
        Assert.Empty(_dbContext.AdPasswordExpires);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        var addedEntity = await _repository.AddAsync(adPasswordExpire);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.UserName, result.UserName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        await _repository.AddAsync(adPasswordExpire);

        // Act
        var result = await _repository.GetByReferenceIdAsync(adPasswordExpire.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordExpire.ReferenceId, result.ReferenceId);
        Assert.Equal(adPasswordExpire.UserName, result.UserName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }
    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }



    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;
        await _repository.AddRange(adPasswordExpires);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(adPasswordExpires.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;

        // Act
        var result = await _repository.AddRange(adPasswordExpires);

        // Assert
        Assert.Equal(adPasswordExpires.Count, result.Count());
        Assert.Equal(adPasswordExpires.Count, _dbContext.AdPasswordExpires.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRange(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;
        await _repository.AddRange(adPasswordExpires);

        // Act
        var result = await _repository.RemoveRange(adPasswordExpires);

        // Assert
        Assert.Equal(adPasswordExpires.Count, result.Count());
        Assert.Empty(_dbContext.AdPasswordExpires);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRange(null));
    }

    #endregion

    #region FindByFilter Tests

    [Fact]
    public async Task FindByFilter_ShouldReturnFilteredEntities()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;
        var targetUserName = "TEST_USER";
        adPasswordExpires.First().UserName = targetUserName;
        await _repository.AddRange(adPasswordExpires);

        // Act
        var result = await _repository.FindByFilter(x => x.UserName == targetUserName);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(targetUserName, result.First().UserName);
    }

    [Fact]
    public async Task FindByFilter_ShouldReturnEmptyList_WhenNoMatch()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;
        await _repository.AddRange(adPasswordExpires);

        // Act
        var result = await _repository.FindByFilter(x => x.UserName == "NON_EXISTENT_USER");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenUserNameExistsAndIdIsInvalid()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        adPasswordExpire.UserName = "ExistingUser";
        await _repository.AddAsync(adPasswordExpire);

        // Act
        var result = await _repository.IsNameExist("ExistingUser", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenUserNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList;
        await _repository.AddRange(adPasswordExpires);

        // Act
        var result = await _repository.IsNameExist("NonExistentUser", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenUserNameExistsForSameEntity()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireDto;
        adPasswordExpire.UserName = "SameUser";
        await _repository.AddAsync(adPasswordExpire);

        // Act
        var result = await _repository.IsNameExist("SameUser", adPasswordExpire.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestUser", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var adPasswordExpire = _adPasswordExpireFixture.AdPasswordExpireList;
        var adPasswordExpire1 = adPasswordExpire[0];
        var adPasswordExpire2 = adPasswordExpire[1];

        var task1 = _repository.AddAsync(adPasswordExpire1);
        var task2 = _repository.AddAsync(adPasswordExpire2);
        
        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.AdPasswordExpires.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var adPasswordExpires = _adPasswordExpireFixture.AdPasswordExpireList.Take(5).ToList();
        
        // Act - Add, then update some, then delete some
        await _repository.AddRange(adPasswordExpires);
        var initialCount = adPasswordExpires.Count;
        
        var toUpdate = adPasswordExpires.Take(2).ToList();
        toUpdate.ForEach(x => x.UserName = "UpdatedUser");
        await _repository.UpdateRange(toUpdate);
        
        var toDelete = adPasswordExpires.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.UserName == "UpdatedUser").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
