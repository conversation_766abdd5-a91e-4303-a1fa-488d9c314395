using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Delete;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetList;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ApprovalMatrixUsersControllerTests : IClassFixture<ApprovalMatrixUsersFixture>
{
    private readonly ApprovalMatrixUsersFixture _approvalMatrixUsersFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ApprovalMatrixUsersController _controller;

    public ApprovalMatrixUsersControllerTests(ApprovalMatrixUsersFixture approvalMatrixUsersFixture)
    {
        _approvalMatrixUsersFixture = approvalMatrixUsersFixture;

        var testBuilder = new ControllerTestBuilder<ApprovalMatrixUsersController>();
        _controller = testBuilder.CreateController(
            _ => new ApprovalMatrixUsersController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetApprovalMatrixUsers_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_approvalMatrixUsersFixture.ApprovalMatrixUsersListVm);

        // Act
        var result = await _controller.GetApprovalMatrixUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var users = Assert.IsAssignableFrom<List<ApprovalMatrixUsersListVm>>(okResult.Value);
        Assert.Equal(3, users.Count);
    }

    [Fact]
    public async Task GetApprovalMatrixUsers_ReturnsEmptyList_WhenNoUsersExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersListQuery>(), default))
            .ReturnsAsync(new List<ApprovalMatrixUsersListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var users = Assert.IsAssignableFrom<List<ApprovalMatrixUsersListVm>>(okResult.Value);
        Assert.Empty(users);
    }

    [Fact]
    public async Task GetApprovalMatrixUsersById_ReturnsUser_WhenIdIsValid()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixUsersDetailQuery>(q => q.Id == userId), default))
            .ReturnsAsync(_approvalMatrixUsersFixture.ApprovalMatrixUsersDetailVm);

        // Act
        var result = await _controller.GetApprovalMatrixUsersById(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var user = Assert.IsType<ApprovalMatrixUsersDetailVm>(okResult.Value);
        Assert.NotNull(user);
    }

    [Fact]
    public async Task GetApprovalMatrixUsersById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetApprovalMatrixUsersById("invalid-guid"));
    }

    [Fact]
    public async Task CreateApprovalMatrixUsers_Returns201Created()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var command = _approvalMatrixUsersFixture.CreateApprovalMatrixUsersCommand;
        var expectedMessage = $"ApprovalMatrixUsers have been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixUsersResponse
            {
                Message = expectedMessage,
                Id =  id
            });

        // Act
        var result = await _controller.CreateApprovalMatrixUsers(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixUsersResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(id, response.Id);
    }

    [Fact]
    public async Task UpdateApprovalMatrixUsers_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"ApprovalMatrixUsers '{_approvalMatrixUsersFixture.UpdateApprovalMatrixUsersCommand.UserName}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateApprovalMatrixUsersCommand>(), default))
            .ReturnsAsync(new UpdateApprovalMatrixUsersResponse
            {
                Message = expectedMessage,
                Id = _approvalMatrixUsersFixture.UpdateApprovalMatrixUsersCommand.Id
            });

        // Act
        var result = await _controller.UpdateApprovalMatrixUsers(_approvalMatrixUsersFixture.UpdateApprovalMatrixUsersCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateApprovalMatrixUsersResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteApprovalMatrixUsers_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "ApprovalMatrixUsers 'Test User' has been deleted successfully!.";
        var userId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixUsersCommand>(c => c.Id == userId), default))
            .ReturnsAsync(new DeleteApprovalMatrixUsersResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixUsers(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixUsersResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrixUsers_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _approvalMatrixUsersFixture.ApprovalMatrixUsersListVm;
        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixUsers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixUsersListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixUsersListVm>>(okResult.Value);

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task IsApprovalMatrixUsersNameExist_ReturnsTrue_WhenNameExists()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersNameUniqueQuery>(), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsApprovalMatrixUsersNameExist("ExistingUser", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsApprovalMatrixUsersNameExist_ReturnsFalse_WhenNameDoesNotExist()
    {
        // Arrange
        _mediatorMock.Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersNameUniqueQuery>(), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixUsersNameExist("NewUser", null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task GetApprovalMatrixUsers_CallsCorrectQuery()
    {
        // Arrange
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<ApprovalMatrixUsersListVm>());

        // Act
        await _controller.GetApprovalMatrixUsers();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateApprovalMatrixUsers_ValidatesEmailFormat()
    {
        // Arrange
        var command = new CreateApprovalMatrixUsersCommand
        {
            ApprovalMatrixUsers = new List<CreateApprovalMatrixUsersCommandList>
            {
                new()
                {
                    UserName = "test.user",
                    Email = "invalid-email", // Invalid email format
                    MobileNumber = "******-1234"
                }
            }
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Invalid email format"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateApprovalMatrixUsers(command));
    }

    [Fact]
    public async Task UpdateApprovalMatrixUsers_ValidatesUserExists()
    {
        // Arrange
        var command = new UpdateApprovalMatrixUsersCommand
        {
            Id = Guid.NewGuid().ToString(),
            UserName = "nonexistent.user",
            Email = "<EMAIL>"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("ApprovalMatrixUsers not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateApprovalMatrixUsers(command));
    }

    [Fact]
    public async Task GetPaginatedApprovalMatrixUsers_HandlesFilteringByUserType()
    {
        // Arrange
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var managerUsers = new List<ApprovalMatrixUsersListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "manager1",
                Email = "<EMAIL>",
                UserType = "Manager",
                AcceptType = "Approver",
                IsLink = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "manager2",
                Email = "<EMAIL>",
                UserType = "Manager",
                AcceptType = "Approver",
                IsLink = true
            }
        };

        var expectedPaginatedResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(
            data: managerUsers,
            count: managerUsers.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedApprovalMatrixUsers(query);

        // Assert
        var actionResult = Assert.IsType<ActionResult<PaginatedResult<ApprovalMatrixUsersListVm>>>(result);
        var okResult = Assert.IsType<OkObjectResult>(actionResult.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<ApprovalMatrixUsersListVm>>(okResult.Value);

        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, user => Assert.Equal("Manager", user.UserType));
    }

    [Fact]
    public async Task CreateApprovalMatrixUsers_HandlesBulkUserCreation()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var command = new CreateApprovalMatrixUsersCommand
        {
            ApprovalMatrixUsers = new List<CreateApprovalMatrixUsersCommandList>
            {
                new()
                {
                    UserId = Guid.NewGuid().ToString(),
                    UserName = "bulk.user1",
                    Email = "<EMAIL>",
                    MobileNumber = "******-1001",
                    BusinessServiceProperties = "{\"department\":\"Finance\",\"role\":\"Analyst\",\"clearanceLevel\":\"Level2\"}",
                    UserType = "Analyst",
                    AcceptType = "Reviewer",
                    IsLink = true
                },
                new()
                {
                    UserId = Guid.NewGuid().ToString(),
                    UserName = "bulk.user2",
                    Email = "<EMAIL>",
                    MobileNumber = "******-1002",
                    BusinessServiceProperties = "{\"department\":\"Operations\",\"role\":\"Manager\",\"clearanceLevel\":\"Level3\"}",
                    UserType = "Manager",
                    AcceptType = "Approver",
                    IsLink = true
                },
                new()
                {
                    UserId = Guid.NewGuid().ToString(),
                    UserName = "bulk.user3",
                    Email = "<EMAIL>",
                    MobileNumber = "******-1003",
                    BusinessServiceProperties = "{\"department\":\"Security\",\"role\":\"Director\",\"clearanceLevel\":\"Level4\"}",
                    UserType = "Director",
                    AcceptType = "Final Approver",
                    IsLink = true
                }
            }
        };

        var expectedMessage = "ApprovalMatrixUsers have been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateApprovalMatrixUsersResponse
            {
                Message = expectedMessage,
               Id=id
            });

        // Act
        var result = await _controller.CreateApprovalMatrixUsers(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateApprovalMatrixUsersResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(id, response.Id);
    }

    [Fact]
    public async Task IsApprovalMatrixUsersNameExist_HandlesSpecialCharacters()
    {
        // Arrange
        var nameWithSpecialChars = "O'Connor-Smith, John Jr.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetApprovalMatrixUsersNameUniqueQuery>(q => q.Name == nameWithSpecialChars), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsApprovalMatrixUsersNameExist(nameWithSpecialChars, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.False((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteApprovalMatrixUsers_VerifiesUserIsDeactivated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var expectedMessage = "ApprovalMatrixUsers 'Test User' has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteApprovalMatrixUsersCommand>(c => c.Id == userId), default))
            .ReturnsAsync(new DeleteApprovalMatrixUsersResponse
            {
                IsActive = false, // Verify it's deactivated
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteApprovalMatrixUsers(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteApprovalMatrixUsersResponse>(okResult.Value);
        Assert.False(response.IsActive);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetApprovalMatrixUsers_HandlesComplexBusinessServiceProperties()
    {
        // Arrange
        var usersWithComplexProperties = new List<ApprovalMatrixUsersListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "complex.user",
                Email = "<EMAIL>",
                BusinessServiceProperties = "{\"department\":\"Enterprise Security\",\"role\":\"Chief Security Officer\",\"clearanceLevel\":\"Top Secret\",\"specialPermissions\":[\"nuclear_access\",\"financial_oversight\"],\"certifications\":[\"CISSP\",\"CISM\"],\"emergencyContact\":{\"name\":\"Emergency Team\",\"phone\":\"******-EMERGENCY\"}}",
                UserType = "Executive",
                AcceptType = "Ultimate Approver"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(usersWithComplexProperties);

        // Act
        var result = await _controller.GetApprovalMatrixUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var users = Assert.IsAssignableFrom<List<ApprovalMatrixUsersListVm>>(okResult.Value);
        Assert.Single(users);
        Assert.Contains("Enterprise Security", users.First().BusinessServiceProperties);
        Assert.Contains("Top Secret", users.First().BusinessServiceProperties);
    }

    [Fact]
    public async Task GetApprovalMatrixUsers_HandlesEmptyDatabase()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetApprovalMatrixUsersListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<ApprovalMatrixUsersListVm>());

        // Act
        var result = await _controller.GetApprovalMatrixUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var users = Assert.IsAssignableFrom<List<ApprovalMatrixUsersListVm>>(okResult.Value);
        Assert.Empty(users);
    }
}
