using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowDrCalenderFixture : IDisposable
{
    public List<WorkflowDrCalender> WorkflowDrCalenderPaginationList { get; set; }
    public List<WorkflowDrCalender> WorkflowDrCalenderList { get; set; }
    public WorkflowDrCalender WorkflowDrCalenderDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowDrCalenderFixture()
    {
        var fixture = new Fixture();

        WorkflowDrCalenderList = fixture.Create<List<WorkflowDrCalender>>();

        WorkflowDrCalenderPaginationList = fixture.CreateMany<WorkflowDrCalender>(20).ToList();

        WorkflowDrCalenderPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowDrCalenderList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowDrCalenderDto = fixture.Create<WorkflowDrCalender>();

        WorkflowDrCalenderDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
