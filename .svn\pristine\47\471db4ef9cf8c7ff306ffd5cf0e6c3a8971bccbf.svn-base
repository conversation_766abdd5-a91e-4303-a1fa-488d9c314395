using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class VeritasClusterFilterSpecification : Specification<VeritasCluster>
{
    public VeritasClusterFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ClusterName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("clusterprofilename=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ClusterProfileName.Contains(stringItem.Replace("clusterprofilename=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("clusterserverid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ClusterServerId.Contains(stringItem.Replace("clusterserverid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("clusterservername=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ClusterServerName.Contains(stringItem.Replace("clusterservername=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("clustername=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ClusterName.Contains(stringItem.Replace("clustername=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("clusterbinpath=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ClusterBinPath.Contains(stringItem.Replace("clusterbinpath=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ClusterProfileName.Contains(searchString) || p.ClusterServerId.Contains(searchString) ||
                    p.ClusterServerName.Contains(searchString) || p.ClusterName.Contains(searchString) ||
                    p.ClusterBinPath.Contains(searchString);
            }
        }
    }
}