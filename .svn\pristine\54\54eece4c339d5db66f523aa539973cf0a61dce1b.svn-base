using ContinuityPatrol.Application.Contexts;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.Services;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.Services;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Infrastructure.Extensions;


namespace ContinuityPatrol.Persistence.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration config)
    {
        services.AddDatabaseContext<ApplicationDbContext>(config)
            .AddScoped<IOrchestrationDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<IAdminDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<IDashboardDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<IManageDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<IConfigurationDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<ICyberDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddScoped<IDriftDbContext>(provider => provider.GetService<ApplicationDbContext>())
            .AddSingleton<ILoggerFactory, LoggerFactory>()
            .AddScoped(typeof(IRepository<>), typeof(BaseRepository<>))

            .AddScoped<DbContext, ApplicationDbContext>()

        #region Admin

            .AddScoped<IDynamicDashboardWidgetRepository, DynamicDashboardWidgetRepository>()
            .AddScoped<IDynamicDashboardMapRepository, DynamicDashboardMapRepository>()
            .AddScoped<IDynamicSubDashboardRepository, DynamicSubDashboardRepository>()
            .AddScoped<IDynamicDashboardRepository, DynamicDashboardRepository>()
            .AddScoped<IBackUpLogRepository, BackUpLogRepository>()
            .AddScoped<IInfraMasterRepository, InfraMasterRepository>()
            .AddScoped<IBackUpRepository, BackUpRepository>()
            .AddScoped<IArchiveRepository, ArchiveRepository>()
            .AddScoped<IPageWidgetRepository, PageWidgetRepository>()
            .AddScoped<IPageBuilderRepository, PageBuilderRepository>()
            .AddScoped<IPageSolutionMappingRepository, PageSolutionMappingRepository>()
            .AddScoped<IFormRepository, FormRepository>()
            .AddScoped<IWorkflowCategoryRepository, WorkflowCategoryRepository>()
            .AddScoped<IWorkflowActionRepository, WorkflowActionRepository>()
            .AddScoped<ILicenseManagerRepository, LicenseManagerRepository>()
            .AddScoped<IFormHistoryRepository, FormHistoryRepository>()
            .AddScoped<ISolutionHistoryRepository, SolutionHistoryRepository>()
            .AddScoped<IFormTypeRepository, FormTypeRepository>()
            .AddScoped<IPluginManagerRepository, PluginManagerRepository>()
            .AddScoped<IPluginManagerHistoryRepository, PluginManagerHistoryRepository>()
            .AddScoped<ILicenseInfoRepository, LicenseInfoRepository>()
            .AddScoped<ILoadBalancerRepository, LoadBalancerRepository>()
            .AddScoped<ILicenseHistoryRepository, LicenseHistoryRepository>()
            .AddScoped<IFormTypeCategoryRepository, FormTypeCategoryRepository>()
            .AddScoped<IGroupPolicyRepository, GroupPolicyRepository>()
            .AddScoped<IComplianceHistoryRepository, ComplianceHistoryRepository>()
            .AddScoped<IWorkflowCategoryViewRepository, WorkflowCategoryViewRepository>()
            .AddScoped<ISolutionTypeTablesRepository,SolutionTypeTablesRepository>()


        #endregion

        #region Configuration
            .AddScoped<IGlobalVariableRepository, GlobalVariableRepository>()
            .AddScoped<IFiaCostRepository, FiaCostRepository>()
            .AddScoped<IComponentSaveAllRepository, ComponentSaveAllRepository>()
            .AddScoped<IAdPasswordJobRepository, AdPasswordJobRepository>()
            .AddScoped<IAdPasswordExpireRepository, AdPasswordExpireRepository>()
            .AddScoped<IEmployeeRepository, EmployeeRepository>()
            .AddScoped<IBulkImportActionResultRepository, BulkImportActionResultRepository>()
            .AddScoped<BulkImportHelperService>()
            .AddScoped<IBulkImportOperationGroupRepository, BulkImportOperationGroupRepository>()
            .AddScoped<IBulkImportOperationRepository, BulkImportOperationRepository>()
            .AddScoped<IInfraOperationalStatusRepository, InfraOperationalStatusRepository>()
            .AddScoped<IVeritasClusterRepository, VeritasClusterRepository>()
            .AddScoped<IHacmpClusterRepository, HacmpClusterRepository>()
            .AddScoped<IRsyncOptionRepository, RsyncOptionRepository>()
            .AddScoped<IRoboCopyRepository, RoboCopyRepository>()
            .AddScoped<IDataSyncOptionsRepository, DataSyncOptionsRepository>()
            .AddScoped<ISiteLocationRepository, SiteLocationRepository>()
            .AddScoped<IIncidentManagementSummaryRepository, IncidentManagementSummaryRepository>()
            .AddScoped<IIncidentManagementRepository, IncidentManagementRepository>()
            .AddScoped<IServerSubTypeRepository, ServerSubTypeRepository>()
            .AddScoped<ICompanyRepository, CompanyRepository>()
            .AddScoped<ISiteRepository, SiteRepository>()
            .AddScoped<ITeamMasterRepository, TeamMasterRepository>()
            .AddScoped<ITeamResourceRepository, TeamResourceRepository>()
            .AddScoped<ISiteTypeRepository, SiteTypeRepository>()
            .AddScoped<ISettingRepository, SettingRepository>()
            .AddScoped<IBusinessServiceRepository, BusinessServiceRepository>()
            .AddScoped<IBusinessFunctionRepository, BusinessFunctionRepository>()
            .AddScoped<IServerRepository, ServerRepository>()
            .AddScoped<IServerTypeRepository, ServerTypeRepository>()
            .AddScoped<IComponentTypeRepository, ComponentTypeRepository>()
            .AddScoped<IReplicationRepository, ReplicationRepository>()
            .AddScoped<ISingleSignOnRepository, SingleSignOnRepository>()
            .AddScoped<IDatabaseRepository, DatabaseRepository>()
            .AddScoped<ICredentialProfileRepository, CredentialProfileRepository>()
            .AddScoped<INodeRepository, NodeRepository>()
            .AddScoped<IInfraObjectRepository, InfraObjectRepository>()
            .AddScoped<IInfraObjectSchedulerRepository, InfraObjectSchedulerRepository>()
            .AddScoped<IInfraObjectSchedulerLogsRepository, InfraObjectSchedulerLogsRepository>()
            .AddScoped<IAccessManagerRepository, AccessManagerRepository>()
            .AddScoped<IJobRepository, JobRepository>()
            .AddScoped<IAlertRepository, AlertRepository>()
            .AddScoped<IAlertMasterRepository, AlertMasterRepository>()
            .AddScoped<IAlertReceiverRepository, AlertReceiverRepository>()
            .AddScoped<IAlertNotificationRepository, AlertNotificationRepository>()
            .AddScoped<ISmtpConfigurationRepository, SmtpConfigurationRepository>()
            .AddScoped<IAlertInformationRepository, AlertInformationRepository>()
            .AddScoped<ISmsConfigurationRepository, SmsConfigurationRepository>()
            .AddScoped<IReportRepository, ReportRepository>()
            .AddScoped<IReportScheduleRepository, ReportScheduleRepository>()
            .AddScoped<IReportScheduleExecutionRepository, ReportScheduleExecutionRepository>()
            .AddScoped<ITableAccessRepository, TableAccessRepository>()
            .AddScoped<IDataSetRepository, DataSetRepository>()
            .AddScoped<IDataSetColumnsRepository, DataSetColumnsRepository>()
            .AddScoped<IUserRepository, UserRepository>()
            .AddScoped<IUserInfoRepository, UserInfoRepository>()
            .AddScoped<IUserInfraObjectRepository, UserInfraObjectRepository>()
            .AddScoped<IUserLoginRepository, UserLoginRepository>()
            .AddScoped<IUserActivityRepository, UserActivityRepository>()
            .AddScoped<IUserRoleRepository, UserRoleRepository>()
            .AddScoped<IUserCredentialRepository, UserCredentialRepository>()
            .AddScoped<IApprovalMatrixRepository, ApprovalMatrixRepository>()
            .AddScoped<IWorkflowRepository, WorkFlowRepository>()
            .AddScoped<IWorkflowProfileInfoRepository, WorkflowProfileInfoRepository>()
            .AddScoped<IWorkflowHistoryRepository, WorkflowHistoryRepository>()
            .AddScoped<IWorkflowInfraObjectRepository, WorkflowInfraObjectRepository>()
            .AddScoped<IWorkflowActionTypeRepository, WorkflowActionTypeRepository>()
            .AddScoped<IWorkflowProfileRepository, WorkflowProfileRepository>()
            .AddScoped<IWorkflowOperationRepository, WorkflowOperationRepository>()
            .AddScoped<IWorkflowOperationGroupRepository, WorkflowOperationGroupRepository>()
            .AddScoped<IWorkflowActionResultRepository, WorkflowActionResultRepository>()
            .AddScoped<ITemplateRepository, TemplateRepository>()
            .AddScoped<IWorkflowExecutionTempRepository, WorkflowExecutionTempRepository>()
            .AddScoped<INodeWorkflowExecutionRepository, NodeWorkflowExecutionRepository>()
            .AddScoped<IWorkflowExecutionEventLogRepository, WorkflowExecutionEventLogRepository>()
            .AddScoped<IWorkflowPermissionRepository, WorkflowPermissionRepository>()
            .AddScoped<IInfraObjectSchedulerWorkflowDetailRepository, InfraObjectSchedulerWorkflowDetailRepository>()
            .AddScoped<IReplicationMasterRepository, ReplicationMasterRepository>()
            .AddScoped<IInfraReplicationMappingRepository, InfraReplicationMappingRepository>()
            .AddScoped<IRpoSlaDeviationReportRepository, RpoSlaDeviationReportRepository>()
            .AddScoped<IAboutCpRepository, AboutCPRepository>()
            .AddScoped<IDrCalenderRepository, DrCalenderRepository>()
            .AddScoped<IGlobalSettingRepository, GlobalSettingRepository>()
            .AddScoped<IAuthenticationService, AdAuthenticationService>()
            .AddScoped<IAuthenticationService, PasswordAuthenticationService>()
            .AddScoped<IAuthenticationServiceFactory, AuthenticationServiceFactory>()
            .AddScoped<DbContext, ApplicationDbContext>()
            .AddScoped<IUserGroupRepository, UserGroupRepository>()
            .AddScoped<IManageWorkflowRepository, ManageWorkflowRepository>()
            .AddScoped<IFourEyeRepository, FourEyeRepository>()
            .AddScoped(typeof(IUnitOfWork<>), typeof(UnitOfWork<>))
            .AddScoped<IRoboCopyJobRepository, RoboCopyJobRepository>()
            .AddScoped<IDataSyncJobRepository, DataSyncJobRepository>()
            .AddScoped<IRsyncJobRepository, RsyncJobRepository>()
            .AddScoped<IReplicationJobRepository, ReplicationJobRepository>()
            .AddScoped<IServerViewRepository, ServerViewRepository>()
            .AddScoped<IDatabaseViewRepository, DatabaseViewRepository>()
            .AddScoped<IUserViewRepository, UserViewRepository>()
            .AddScoped<IReplicationViewRepository, ReplicationViewRepository>()
            .AddScoped<IServerLogRepository, ServerLogRepository>()

        #endregion

        #region DashBoard

            .AddScoped<IDrReadyRepository, DrReadyRepository>()
            .AddScoped<IImpactActivityRepository, ImpactActivityRepository>()
            .AddScoped<IRtoRepository, RtoRepository>()
            .AddScoped<IDataLagRepository, DataLagRepository>()
            .AddScoped<IDashboardViewLogRepository, DashboardViewLogRepository>()
            .AddScoped<IDrReadyLogRepository, DRReadyLogRepository>()
            .AddScoped<IHeatMapLogRepository, HeatMapLogRepository>()
            .AddScoped<IHeatMapStatusRepository, HeatMapStatusRepository>()
            .AddScoped<IInfraSummaryRepository, InfraSummaryRepository>()
            .AddScoped<IInfraObjectInfoRepository, InfraObjectInfoRepository>()
            .AddScoped<IImpactAvailabilityRepository, ImpactAvailabilityRepository>()
            .AddScoped<IDrReadyStatusRepository, DRReadyStatusRepository>()
            .AddScoped<IRiskMitigationRepository, RiskMitigationRepository>()
            .AddScoped<IBusinessServiceAvailabilityRepository, BusinessServiceAvailabilityRepository>()
            .AddScoped<IBusinessServiceEvaluationRepository, BusinessServiceEvaluationRepository>()
            .AddScoped<IBusinessServiceHealthStatusRepository, BusinessServiceHealthStatusRepository>()
            .AddScoped<IBusinessServiceHealthLogRepository, BusinessServiceHealthLogRepository>()
            .AddScoped<IDashboardViewRepository, DashboardViewRepository>()
            .AddScoped<IDataSetColumnsRepository, DataSetColumnsRepository>()
            .AddScoped<IIncidentRepository, IncidentRepository>()
            .AddScoped<IIncidentDailyRepository, IncidentDailyRepository>()
            .AddScoped<IIncidentLogsRepository, IncidentLogsRepository>()
            .AddScoped<IRpForVmMonitorStatusRepository, RpForVmMonitorStatusRepository>()
            .AddScoped<IInfraObjectViewRepository, InfraObjectViewRepository>()
            .AddScoped<IInfraDashboardViewRepository, InfraDashboardViewRepository>()
            .AddScoped<IOneViewEntitiesEventViewRepository, OneViewEntitiesEventViewRepository>()
            .AddScoped<IOneViewRiskMitigationFailedDrillViewRepository, OneViewRiskMitigationFailedDrillViewRepository>()
            .AddScoped<IOneViewRiskMitigationCyberSecurityViewRepository, OneViewRiskMitigationCyberSecurityViewRepository>()

        #endregion

        #region Orchestration
            .AddScoped<IWorkflowDrCalenderRepository, WorkflowDrCalenderRepository>()
            .AddScoped<IWorkflowTempRepository, WorkflowTempRepository>()
            .AddScoped<IWorkflowDrCalenderRepository, WorkflowDrCalenderRepository>()
            .AddScoped<IWorkflowRunningActionRepository, WorkflowRunningActionRepository>()
            .AddScoped<IWorkflowActionFieldMasterRepository, WorkflowActionFieldMasterRepository>()
            .AddScoped<IApprovalMatrixRepository, ApprovalMatrixRepository>()
            .AddScoped<IWorkflowRepository, WorkFlowRepository>()
            .AddScoped<IWorkflowProfileInfoRepository, WorkflowProfileInfoRepository>()
            .AddScoped<IWorkflowHistoryRepository, WorkflowHistoryRepository>()
            .AddScoped<IWorkflowInfraObjectRepository, WorkflowInfraObjectRepository>()
            .AddScoped<IWorkflowActionTypeRepository, WorkflowActionTypeRepository>()
            .AddScoped<IWorkflowProfileRepository, WorkflowProfileRepository>()
            .AddScoped<IWorkflowOperationRepository, WorkflowOperationRepository>()
            .AddScoped<IWorkflowOperationGroupRepository, WorkflowOperationGroupRepository>()
            .AddScoped<IWorkflowActionResultRepository, WorkflowActionResultRepository>()
            .AddScoped<ITemplateRepository, TemplateRepository>()
            .AddScoped<ITemplateHistoryRepository, TemplateHistoryRepository>()
            .AddScoped<IWorkflowExecutionTempRepository, WorkflowExecutionTempRepository>()
            .AddScoped<INodeWorkflowExecutionRepository, NodeWorkflowExecutionRepository>()
            .AddScoped<IWorkflowExecutionEventLogRepository, WorkflowExecutionEventLogRepository>()
            .AddScoped<IWorkflowPermissionRepository, WorkflowPermissionRepository>()
            .AddScoped<IInfraObjectSchedulerWorkflowDetailRepository, InfraObjectSchedulerWorkflowDetailRepository>()
            .AddScoped<IWorkflowPredictionRepository, WorkflowPredictionRepository>()
            .AddScoped<IWorkflowViewRepository, WorkflowViewRepository>()
            .AddScoped<IWorkflowProfileInfoViewRepository, WorkflowProfileInfoViewRepository>()
            .AddScoped<IWorkflowTempRepository, WorkflowTempRepository>()
            .AddScoped<IRpForVmCgEnableDisableStatusRepository, RpForVmCgEnableDisableStatusRepository>()
            .AddScoped<ISchedulerWorkflowActionResultsRepository, SchedulerWorkflowActionResultsRepository>()


        #endregion

        #region Manage
    .AddScoped<IApprovalMatrixUsersRepository, ApprovalMatrixUsersRepository>()


            .AddScoped<IApprovalMatrixApprovalRepository, ApprovalMatrixApprovalRepository>()
            .AddScoped<IApprovalMatrixRequestRepository, ApprovalMatrixRequestRepository>()
            .AddScoped<IMssqlMonitorLogsRepository, MSSQLMonitorLogsRepository>()
            .AddScoped<IMysqlMonitorLogsRepository, MYSQLMonitorLogsRepository>()
            .AddScoped<IOracleMonitorLogsRepository, OracleMonitorLogsRepository>()
            .AddScoped<IPostgresMonitorLogsRepository, PostgresMonitorLogsRepository>()
            .AddScoped<IOracleRacMonitorLogsRepository, OracleRACMonitorLogsRepository>()
            .AddScoped<IMssqlAlwaysOnMonitorLogsRepository, MSSQLAlwaysOnMonitorLogsRepository>()
            .AddScoped<IMssqlMonitorStatusRepository, MSSQLMonitorStatusRepository>()
            .AddScoped<IMysqlMonitorStatusRepository, MYSQLMonitorStatusRepository>()
            .AddScoped<IOracleMonitorStatusRepository, OracleMonitorStatusRepository>()
            .AddScoped<IOracleRacMonitorStatusRepository, OracleRACMonitorStatusRepository>()
            .AddScoped<IPostgresMonitorStatusRepository, PostgresMonitorStatusRepository>()
            .AddScoped<IMssqlAlwaysOnMonitorStatusRepository, MSSQLAlwaysOnMonitorStatusRepository>()
            .AddScoped<IStateMonitorStatusRepository, StateMonitorStatusRepository>()
            .AddScoped<IStateMonitorLogRepository, StateMonitorLogRepository>()
            .AddScoped<IMonitorServiceRepository, MonitorServiceRepository>()
            .AddScoped<ISVCGMMonitorStatusRepository, SVCGMMonitorStatusRepository>()
            .AddScoped<ISVCGMMonitorLogRepository, IsvcgmMonitorLogRepository>()
            .AddScoped<IDb2HaDrMonitorStatusRepository, Db2HaDrMonitorStatusRepository>()
            .AddScoped<IDb2HadrMonitorLogRepository, DB2HADRMonitorLogRespository>()
            .AddScoped<IMssqlNativeLogShippingMonitorLogRepository, MssqlNativeLogShippingMonitorLogRepository>()
            .AddScoped<IMongoDbMonitorLogRepository, MongoDBMonitorLogRepository>()
            .AddScoped<ISVCMssqlMonitorLogRepository, SVCMssqlMonitorLogRepository>()
            .AddScoped<IMsSqlNativeLogShippingMonitorStatusRepository, MsSqlNativeLogShippingMonitorStatusRepository>()
            .AddScoped<IMongoDbMonitorStatusRepository, MongoDbMonitorStatusRepository>()
            .AddScoped<ISvcMsSqlMonitorStatusRepository, SvcMsSqlMonitorStatusRepository>()
            .AddScoped<IMsSqlDbMirroringStatusRepository, MsSqlDbMirroringStatusStatusRepository>()
            .AddScoped<IEscalationMatrixRepository, EscalationMatrixRepository>()
            .AddScoped<IEscalationMatrixLevelRepository, EscalationMatrixLevelRepository>()
            .AddScoped<IMsSqlDbMirroringLogRepository, MsSqlDbMirroingLogsRepository>()
            .AddScoped<IRoboCopyMonitoringLogsRepository, RoboCopyMonitoringLogsRepository>()
            .AddScoped<IRoboCopyMonitorStatusRepository, RoboCopyMonitorStatusRepository>()
            .AddScoped<IRsyncMonitorLogRepository, RsyncMonitorLogRepository>()
            .AddScoped<IRsyncMonitorStatusRepository, RsyncMonitorStatusRepository>()
            .AddScoped<ISRMMonitorLogRepository, SRMMonitorLogRepository>()
            .AddScoped<IAzureStorageAccountMonitorLogsRepository, AzureStorageAccountMonitorLogsRepository>()
            .AddScoped<IPowerMaxMonitorStatusRepository, PowerMaxMonitorStatusRepository>()
            .AddScoped<IRpForVmCGMonitorLogsRepository, RpForVmCGMonitorLogsRepository>()
            .AddScoped<IRpForVmCGMonitorStatusRepository, RpForVmCGMonitorStatusRepository>()
            .AddScoped<IFastCopyMonitorRepository, FastCopyMonitorRepository>()
            .AddScoped<IActiveDirectoryMonitorLogRepository, ActiveDirectoryMonitorLogRepository>()
            .AddScoped<IDataSyncMonitorLogsRepository, DataSyncMonitorLogsRepository>()
            .AddScoped<IFastCopyMonitorLogsRepository, FastCopyMonitorLogsRepository>()
            .AddScoped<IZertoVpgMonitorLogsRepository, ZertoVpgMonitorLogsRepository>()
            .AddScoped<ISybaseRSHADRMonitorLogsRepository, SybaseRSHADRMonitorLogsRepository>()
            .AddScoped<IMssqlAlwaysOnAvailabilityGroupMonitorLogRepository,MssqlAlwaysOnAvailabilityGroupMonitorLogRepository>()

        #endregion

        #region Cyber
            .AddScoped<ICyberJobWorkflowSchedulerRepository,CyberJobWorkflowSchedulerRepository>()
            .AddScoped<ICyberJobManagementLogsRepository,CyberJobManagementLogsRepository>()
            .AddScoped<ICyberAirGapRepository, CyberAirGapRepository>()
            .AddScoped<ICyberSnapsRepository, CyberSnapsRepository>()
            .AddScoped<ICyberAlertRepository, CyberAlertRepository>()
            .AddScoped<ICyberAirGapLogRepository, CyberAirGapLogRepository>()
            .AddScoped<ICyberAirGapStatusRepository, CyberAirGapStatusRepository>()
            .AddScoped<ICyberJobManagementRepository, CyberJobManagementRepository>()
            .AddScoped<ICyberComponentGroupRepository, CyberComponentGroupRepository>()
            .AddScoped<ICyberComponentRepository, CyberComponentRepository>()
            .AddScoped<ICyberMappingHistoryRepository, CyberMappingHistoryRepository>()
            .AddScoped<ICyberComponentMappingRepository, CyberComponentMappingRepository>()
             .AddScoped<IResiliencyReadyWorkflowScheduleLogRepository, ResiliencyReadyWorkflowScheduleLogRepository>()

        #endregion

        #region Drift

            .AddScoped<IDriftJobRepository, DriftJobRepository>()
            .AddScoped<IDriftProfileRepository, DriftProfileRepository>()
            .AddScoped<IDriftImpactTypeMasterRepository, DriftImpactTypeMasterRepository>()
            .AddScoped<IDriftCategoryMasterRepository, DriftCategoryMasterRepository>()
            .AddScoped<IDriftEventRepository, DriftEventRepository>()
            .AddScoped<IDriftManagementMonitorLogsRepository, DriftManagementMonitorLogsRepository>()
            .AddScoped<IDriftResourceSummaryRepository, DriftResourceSummaryRepository>()
            .AddScoped<IDriftManagementMonitorStatusRepository, DriftManagementMonitorStatusRepository>()
            .AddScoped<IDriftParameterRepository, DriftParameterRepository>()

        #endregion

        #region FiaBia

            .AddScoped<IFiaTemplateRepository, FiaTemplateRepository>()
            .AddScoped<IBiaRulesRepository, BiaRulesRepository>()
            .AddScoped<IFiaIntervalRepository, FiaIntervalRepository>()
            .AddScoped<IFiaImpactTypeRepository, FiaImpactTypeRepository>()
            .AddScoped<IFiaImpactCategoryRepository, FiaImpactCategoryRepository>();

        #endregion

        return services;
    }
}
