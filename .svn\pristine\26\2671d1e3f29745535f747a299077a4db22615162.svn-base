﻿using Moq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using AutoMapper;
using MediatR;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RsyncJobModel;
using ContinuityPatrol.Shared.Tests.Fakes;
using Microsoft.AspNetCore.Http;
using AutoFixture;
using ContinuityPatrol.Shared.Core.Wrapper;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class RSyncOptionsControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<RSyncOptionsController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private RSyncOptionsController _controller;

        public RSyncOptionsControllerShould()
        {
           
            _controller = new RSyncOptionsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");

        }

        [Fact]
        public void List_CallsPublish()
        {
            // Act
            var result = _controller.List();

            // Assert
            //_mockPublisher.Verify(p => p.Publish(It.Is<RsyncOptionPaginatedEvent>(e => e != null)), Times.Once);
            Assert.IsType<ViewResult>(result);
        }
        
        [Fact]
        public async Task CreateOrUpdate_CreatesWhenIdIsEmpty()
        {
            
            var viewModel = new AutoFixture.Fixture().Create<RsyncOptionViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateRsyncOptionCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateRsyncOptionCommand>(viewModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.RsyncOption.CreateAsync(createCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
           
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesWhenIdIsNotEmpty()
        {

            var viewModel = new AutoFixture.Fixture().Create<RsyncOptionViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateRsyncOptionCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };
            _mockMapper.Setup(m => m.Map<UpdateRsyncOptionCommand>(viewModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.RsyncOption.UpdateAsync(updateCommand)).ReturnsAsync(response);

            
            var result = await _controller.CreateOrUpdate(viewModel);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            
        }

        [Fact]
        public void CreateOrUpdate_HandlesGeneralException()
        {
            
            var viewModel = new RsyncOptionViewModel();
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.CreateAsync(It.IsAny<CreateRsyncOptionCommand>())).ThrowsAsync(exception);

            
            var result =  _controller.CreateOrUpdate(viewModel);

            
            Assert.Equal("Faulted", result.Status.ToString());
            Assert.NotNull(result);
        }

        [Fact]
        public async Task Delete_DeletesSuccessfully()
        {
            
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.RsyncOption.DeleteAsync(id)).ReturnsAsync(response);

            
            var result = await _controller.Delete(id);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            
        }

        

        [Fact]
        public async Task Delete_HandlesGeneralException()
        {
            
            var id = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.DeleteAsync(id)).ThrowsAsync(exception);

            
            var result = await _controller.Delete(id);

            
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            
            var query = new GetRsyncOptionPaginatedListQuery();
            var expectedResult = new PaginatedResult<RsyncOptionListVm> ();
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetPaginatedRsyncOptions(query)).ReturnsAsync(expectedResult);

            
            var result = await _controller.GetPagination(query);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetPagination_HandlesException()
        {
            
            var query = new GetRsyncOptionPaginatedListQuery();
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetPaginatedRsyncOptions(query)).ThrowsAsync(exception);

            
            var result = await _controller.GetPagination(query);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetRsyncOptionsDataById_ReturnsJsonResult()
        {
            // Arrange
            var id = "1";
            var expectedData = new RsyncOptionDetailVm();
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetByReferenceId(id)).ReturnsAsync(expectedData);

            // Act
            var result = await _controller.GetRsyncOptionsDataById(id);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }           

        [Fact]
        public async Task GetRsyncOptionsDataById_HandlesException()
        {
            
            var id = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncOption.GetByReferenceId(id)).ThrowsAsync(exception);

            
            var result = await _controller.GetRsyncOptionsDataById(id);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task IsRsyncNameExist_ReturnsCorrectResult()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.RsyncOption.IsRsyncOptionNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsRsyncNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void GetRsyncJobByReplicationId_ReturnsJsonResult()
        {
            
            var replicationId = "1";
            var expectedJobs = new List<RsyncJobListVm>();
            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs()).ReturnsAsync(expectedJobs);

            
            var result = _controller.GetRsyncJobByReplicationId(replicationId);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public void GetRsyncJobByReplicationId_HandlesException()
        {
            
            var replicationId = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RsyncJob.GetRsyncJobs()).ThrowsAsync(exception);

            
            var result = _controller.GetRsyncJobByReplicationId(replicationId);

            
            var jsonResult = Assert.IsType<JsonResult>(result);
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }
    }
}
