﻿using ContinuityPatrol.Application.Features.Replication.Events.InfraSummaryEvents.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.Replication.Events
{
    public class DeleteReplicationInfraSummaryEventTests
    {
        private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
        private readonly Mock<ILogger<ReplicationInfraSummaryDeletedEventHandler>> _mockLogger;
        private readonly ReplicationInfraSummaryDeletedEventHandler _handler;

        public DeleteReplicationInfraSummaryEventTests()
        {
            _mockInfraSummaryRepository = new Mock<IInfraSummaryRepository>();
            _mockLogger = new Mock<ILogger<ReplicationInfraSummaryDeletedEventHandler>>();
            _handler = new ReplicationInfraSummaryDeletedEventHandler(
                _mockLogger.Object,
                _mockInfraSummaryRepository.Object);
        }

        [Fact]
        public async Task Handle_Should_DeleteInfraSummary_When_CountIsOne()
        {
            var deletedEvent = new ReplicationInfraSummaryDeletedEvent
            {
                Type = "TestType",
                BusinessServiceId = "TestBusinessServiceId",
                CompanyId = Guid.NewGuid().ToString(),
            };

            var infraSummary = new Domain.Entities.InfraSummary
            {
                Type = deletedEvent.Type,
                Count = 1,
                BusinessServiceId = deletedEvent.BusinessServiceId,
                CompanyId = deletedEvent.CompanyId
            };

            _mockInfraSummaryRepository.Setup(r => r.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                deletedEvent.Type, deletedEvent.BusinessServiceId, deletedEvent.CompanyId)).ReturnsAsync(infraSummary);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(r => r.DeleteAsync(infraSummary), Times.Once);
            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s => s.Contains("deleted successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_UpdateInfraSummary_When_CountIsGreaterThanOne()
        {
            var deletedEvent = new ReplicationInfraSummaryDeletedEvent
            {
                Type = "TestType",
                BusinessServiceId = "TestBusinessServiceId",
                CompanyId = Guid.NewGuid().ToString()
            };

            var infraSummary = new Domain.Entities.InfraSummary
            {
                Type = deletedEvent.Type,
                Count = 2,
                BusinessServiceId = deletedEvent.BusinessServiceId,
                CompanyId = deletedEvent.CompanyId
            };

            _mockInfraSummaryRepository.Setup(r => r.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                deletedEvent.Type, deletedEvent.BusinessServiceId, deletedEvent.CompanyId)).ReturnsAsync(infraSummary);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(r => r.UpdateAsync(It.Is<Domain.Entities.InfraSummary>(i =>
                i.Count == 1
            )), Times.Once);

            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s => s.Contains("deleted successfully"))), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_DoNothing_When_InfraSummaryDoesNotExist()
        {
            var deletedEvent = new ReplicationInfraSummaryDeletedEvent
            {
                Type = "TestType",
                BusinessServiceId = "TestBusinessServiceId",
                CompanyId = Guid.NewGuid().ToString(),
            };

            _mockInfraSummaryRepository.Setup(r => r.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                deletedEvent.Type, deletedEvent.BusinessServiceId, deletedEvent.CompanyId)).ReturnsAsync((Domain.Entities.InfraSummary)null);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(r => r.DeleteAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Never);
            _mockInfraSummaryRepository.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Never);
            _mockLogger.Verify(l => l.LogInformation(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_Should_LogInformation_When_InfraSummaryDeleted()
        {
            var deletedEvent = new ReplicationInfraSummaryDeletedEvent
            {
                Type = "TestType",
                BusinessServiceId = "TestBusinessServiceId",
                CompanyId = Guid.NewGuid().ToString()
            };

            var infraSummary = new Domain.Entities.InfraSummary
            {
                Type = deletedEvent.Type,
                Count = 2,
                BusinessServiceId = deletedEvent.BusinessServiceId,
                CompanyId = deletedEvent.CompanyId
            };

            _mockInfraSummaryRepository.Setup(r => r.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                deletedEvent.Type, deletedEvent.BusinessServiceId, deletedEvent.CompanyId)).ReturnsAsync(infraSummary);

            await _handler.Handle(deletedEvent, CancellationToken.None);

            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s => s.Contains("deleted successfully"))), Times.Once);
        }
    }
}
