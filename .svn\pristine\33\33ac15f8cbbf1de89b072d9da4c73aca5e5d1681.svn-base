﻿using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetList;

public class
    GetMSSQLMonitorStatusListQueryHandler : IRequestHandler<GetMSSQLMonitorStatusListQuery,
        List<MSSQLMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorStatusRepository _mssqlMonitorStatusRepository;

    public GetMSSQLMonitorStatusListQueryHandler(IMssqlMonitorStatusRepository mssqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mssqlMonitorStatusRepository = mssqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<MSSQLMonitorStatusListVm>> Handle(GetMSSQLMonitorStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var mssqlMonitorStatus = await _mssqlMonitorStatusRepository.ListAllAsync();

        return mssqlMonitorStatus.Count <= 0
            ? new List<MSSQLMonitorStatusListVm>()
            : _mapper.Map<List<MSSQLMonitorStatusListVm>>(mssqlMonitorStatus);
    }
}