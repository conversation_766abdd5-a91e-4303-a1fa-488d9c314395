using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class PageBuilderFixture : IDisposable
{
    public List<PageBuilder> PageBuilderPaginationList { get; set; }
    public List<PageBuilder> PageBuilderList { get; set; }
    public PageBuilder PageBuilderDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public PageBuilderFixture()
    {
        var fixture = new Fixture();

        PageBuilderList = fixture.Create<List<PageBuilder>>();

        PageBuilderPaginationList = fixture.CreateMany<PageBuilder>(20).ToList();

        PageBuilderDto = fixture.Create<PageBuilder>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
