﻿namespace ContinuityPatrol.Application.Features.User.Commands.Create;

public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    private readonly List<string> _allowedLoginTypes = new() { "in house", "ad" };
    private readonly IUserRepository _userRepository;

    public CreateUserCommandValidator(IUserRepository userRepository)
    {
        _userRepository = userRepository;

        RuleFor(p => p.LoginName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p).MustAsync(IsValidLoginName).WithMessage("Login Name format is invalid.");

        RuleFor(p => p.LoginPassword)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .Length(64, 500).WithMessage("{PropertyName} format is invalid.")
            .When(p => p.LoginType != null && p.LoginType.ToLower() != "ad");

        RuleFor(p => p.CompanyName)
            .NotEmpty().WithMessage("Select Company Name.");

        RuleFor(p => p.LoginType)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .Must(value => value != null && _allowedLoginTypes.Contains(value.ToLower()))
            .WithMessage("{PropertyName} is invalid.");

        RuleFor(p => p.Role)
            .NotEmpty().WithMessage("{PropertyName} is Required.");

        RuleFor(p => p.SessionTimeout)
            .NotEmpty().WithMessage("{PropertyName} is Required.");

        RuleFor(userInfo => userInfo.UserInfoCommand.UserName)
            .NotEmpty().WithMessage("User Name is Required.")
            .Length(3, 100).WithMessage("User Name should contain between 3 to 100 characters.");

        RuleFor(userInfo => userInfo.UserInfoCommand.Email)
            .NotEmpty().WithMessage("Email is required.")
            .Matches(@"^[^@\s]+@[^@\s]+\.[^@\s]+$").WithMessage("Please enter a valid email.");

        RuleFor(e => e)
            .MustAsync(LoginNameUnique)
            .WithMessage("A same name already exists.");

        RuleFor(p => p)
            .NotEmpty().WithMessage("Role is Required.")
            .MustAsync(RolePolicy).WithMessage("The user Role and Login User Role are not matched.");

        RuleFor(y => y)
            .NotNull()
            .MustAsync(VerifyGuid)
            .WithMessage("Id is invalid");
    }

    private Task<bool> VerifyGuid(CreateUserCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.CompanyId, "Company Id");

        Guard.Against.InvalidGuidOrEmpty(p.Role, "Role Id");

        return Task.FromResult(true);
    }

    private async Task<bool> LoginNameUnique(CreateUserCommand e, CancellationToken token)
    {
        return !await _userRepository.IsUserNameUnique(e.LoginName);
    }

    private async Task<bool> RolePolicy(CreateUserCommand e, CancellationToken token)
    {
        return await _userRepository.IsLoginRoleUnique(e.Role);
    }

    private async Task<bool> IsValidLoginName(CreateUserCommand e, CancellationToken cancellationToken)
    {
        //var adRegex = new Regex(@"^(?!_)(?!\d)(?!.*_\.)[a-zA-Z0-9\\$]+(?:\.[a-zA-Z0-9\\$]+)*$");
        var adRegex = new Regex(@"^(?!_)(?!\d)(?!.*_\.)[a-zA-Z0-9\\$_\s]+(?:\.[a-zA-Z0-9\\$_\s]+)*$");

        
        var userNameRegex = new Regex(@"^(?!_)(?!.*_\.)[a-zA-Z0-9_. ]+(?<![_\.\s])$");

        if (!string.IsNullOrWhiteSpace(e.LoginName) && !string.IsNullOrWhiteSpace(e.LoginType))
            return e.LoginType.ToLower().Equals("ad")
                ? await Task.FromResult(adRegex.Match(e.LoginName).Success)
                : await Task.FromResult(userNameRegex.Match(e.LoginName).Success);

        return false;
    }
}