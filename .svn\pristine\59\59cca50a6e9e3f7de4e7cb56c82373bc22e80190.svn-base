﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class SettingRepositoryMocks
{
    public static Mock<ISettingRepository> CreateSettingRepository(List<Setting> settings)
    {
        var settingRepository = new Mock<ISettingRepository>();

        settingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(settings);

        settingRepository.Setup(repo => repo.AddAsync(It.IsAny<Setting>())).ReturnsAsync(
            (Setting setting) =>
            {
                setting.Id = new Fixture().Create<int>();

                setting.ReferenceId = new Fixture().Create<Guid>().ToString();

                settings.Add(setting);

                return setting;
            });

        return settingRepository;
    }

    public static Mock<ISettingRepository> UpdateSettingRepository(List<Setting> settings)
    {
        var mockSettingRepository = new Mock<ISettingRepository>();

        mockSettingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(settings);

        mockSettingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => settings.SingleOrDefault(x => x.ReferenceId == i));

        mockSettingRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Setting>())).ReturnsAsync((Setting setting) =>
        {
            var index = settings.FindIndex(item => item.Id == setting.Id);

            settings[index] = setting;

            return setting;
        });

        return mockSettingRepository;
    }

    public static Mock<ISettingRepository> DeleteSettingRepository(List<Setting> settings)
    {
        var mockSettingRepository = new Mock<ISettingRepository>();

        mockSettingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(settings);

        mockSettingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => settings.SingleOrDefault(x => x.ReferenceId == i));

        mockSettingRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Setting>())).ReturnsAsync((Setting setting) =>
        {
            var index = settings.FindIndex(item => item.Id == setting.Id);

            setting.IsActive = false;

            settings[index] = setting;

            return setting;
        });

        return mockSettingRepository;
    }

    public static Mock<ISettingRepository> GetSettingRepository(List<Setting> settings)
    {
        var settingRepository = new Mock<ISettingRepository>();

        settingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(settings);

        settingRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => settings.SingleOrDefault(x => x.ReferenceId == i));

        return settingRepository;
    }

    public static Mock<ISettingRepository> GetSettingKeyRepository(List<Setting> settings)
    {
        var mockSettingRepository = new Mock<ISettingRepository>();

        mockSettingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(settings);

        return mockSettingRepository;
    }

    public static Mock<ISettingRepository> GetSettingNameUniqueRepository(List<Setting> settings)
    {
        var settingNameUniqueRepository = new Mock<ISettingRepository>();

        settingNameUniqueRepository.Setup(repo => repo.IsSettingKeyExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => settings.Exists(x => x.SKey == i && x.ReferenceId == j));

        return settingNameUniqueRepository;
    }

    public static Mock<ISettingRepository> GetSettingEmptyRepository()
    {
        var mockSettingRepository = new Mock<ISettingRepository>();

        mockSettingRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Setting>());

        return mockSettingRepository;
    }

    public static Mock<ISettingRepository> GetPaginatedSettingRepository(List<Setting> settings)
    {
        var settingRepository = new Mock<ISettingRepository>();

        var queryableSettings = settings.BuildMock();

        settingRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableSettings);

        return settingRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateSettingEventRepository(List<UserActivity> userActivities)
    {
        var settingEventRepository = new Mock<IUserActivityRepository>();

        settingEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        settingEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return settingEventRepository;
    }
}