using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class HeatMapStatusViewRepositoryTests : IClassFixture<HeatMapStatusViewFixture>,IClassFixture<BusinessServiceFixture>,IClassFixture<InfraObjectFixture>,IClassFixture<BusinessFunctionFixture>, IDisposable
{
    private readonly HeatMapStatusViewFixture _heatMapStatusViewFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly HeatMapStatusViewRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly BusinessServiceFixture _businessSeviceFixture;
    private readonly InfraObjectFixture _infraObjectFixture;
    private readonly BusinessFunctionFixture _businessFunctionFixture;

    public HeatMapStatusViewRepositoryTests(HeatMapStatusViewFixture heatMapStatusViewFixture,BusinessServiceFixture businessServiceFixture, InfraObjectFixture infraObjectFixture,BusinessFunctionFixture businessFunctionFixture) 
    {
        _heatMapStatusViewFixture = heatMapStatusViewFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new HeatMapStatusViewRepository(_dbContext, _mockLoggedInUserService.Object);
        _businessSeviceFixture = businessServiceFixture;
        _infraObjectFixture = infraObjectFixture;
        _businessFunctionFixture = businessFunctionFixture;

    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.HeatMapStatusViews.RemoveRange(_dbContext.HeatMapStatusViews);
        await _dbContext.SaveChangesAsync();
    }

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ReturnsAllActiveHeatMapStatusViews_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Business Service 1",
                BusinessFunctionId = "BF_123",
                BusinessFunctionName = "Business Function 1",
                InfraObjectId = "IO_123",
                InfraObjectName = "Infrastructure Object 1",
                EntityId = "E_123",
                HeatmapType = "Server",
                HeatmapStatus = "Online",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Business Service 2",
                BusinessFunctionId = "BF_456",
                BusinessFunctionName = "Business Function 2",
                InfraObjectId = "IO_456",
                InfraObjectName = "Infrastructure Object 2",
                EntityId = "E_456",
                HeatmapType = "Database",
                HeatmapStatus = "Offline",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                BusinessServiceName = "Business Service 3",
                BusinessFunctionId = "BF_789",
                BusinessFunctionName = "Business Function 3",
                InfraObjectId = "IO_789",
                InfraObjectName = "Infrastructure Object 3",
                EntityId = "E_789",
                HeatmapType = "Network",
                HeatmapStatus = "Warning",
                IsAffected = false,
                IsActive = false // This should be excluded
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count); // Only active records
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Contains(result, x => x.BusinessServiceName == "Business Service 1");
        Assert.Contains(result, x => x.BusinessServiceName == "Business Service 2");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Business Service 3");
    }

    [Fact]
    public async Task ListAllAsync_ReturnsEmpty_WhenNoActiveData()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Business Service 1",
            IsActive = false
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsHeatMapStatusView_WhenIsAllInfraTrue()
    {
        // Arrange
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Business Service 1";
        await _dbContext.BusinessServices.AddAsync(businessSevice);
        await _dbContext.SaveChangesAsync();
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var referenceId = Guid.NewGuid().ToString();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = referenceId,
            BusinessServiceId = businessSevice.ReferenceId,
            BusinessServiceName = businessSevice.Name,
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Business Function 1",
            InfraObjectId = "IO_123",
            InfraObjectName = "Infrastructure Object 1",
            EntityId = "E_123",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            IsActive = true
        };

        _dbContext.HeatMapStatusViews.Add(heatMapStatusView);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(referenceId, result.ReferenceId);
        Assert.Equal("Business Service 1", result.BusinessServiceName);
        Assert.Equal("Server", result.HeatmapType);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);
        var nonExistentId = Guid.NewGuid().ToString();

        // Act
        var result = await _repository.GetByReferenceIdAsync(nonExistentId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetHeatMapStatusTypeAsync Tests

    [Fact]
    public async Task GetHeatMapStatusTypeAsync_ReturnsFilteredResults_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                HeatmapType = "Server",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                HeatmapType = "Database",
                IsAffected = true,
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusTypeAsync("Server", true);

        // Assert
        Assert.Single(result);
        Assert.Equal("Server", result.First().HeatmapType);
        Assert.True(result.First().IsAffected);
    }

    [Fact]
    public async Task GetHeatMapStatusTypeAsync_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            HeatmapType = "Database",
            IsAffected = false,
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusTypeAsync("Server", true);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusType Tests

    [Fact]
    public async Task GetHeatMapStatusType_ReturnsQueryable_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                HeatmapType = "Database",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var query = _repository.GetHeatMapStatusType("Server");
        var result = query.ToList();

        // Assert
        Assert.Single(result);
        Assert.Equal("Server", result.First().HeatmapType);
    }

    #endregion

    #region GetImpactDetail Tests

    [Fact]
    public async Task GetImpactDetail_ReturnsAffectedActiveRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                BusinessServiceName = "Affected Service 1",
                HeatmapType = "Server",
                IsAffected = true,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                BusinessServiceName = "Not Affected Service",
                HeatmapType = "Database",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_789",
                BusinessServiceName = "Affected Service 2",
                HeatmapType = "Network",
                IsAffected = true,
                IsActive = false // Should be excluded
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_101",
                BusinessServiceName = "Affected Service 3",
                HeatmapType = "Application",
                IsAffected = true,
                IsActive = true
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetImpactDetail();

        // Assert
        Assert.Equal(2, result.Count); // Only affected and active records
        Assert.All(result, x => Assert.True(x.IsAffected));
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.Contains(result, x => x.BusinessServiceName == "Affected Service 1");
        Assert.Contains(result, x => x.BusinessServiceName == "Affected Service 3");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Not Affected Service");
        Assert.DoesNotContain(result, x => x.BusinessServiceName == "Affected Service 2");
    }

    [Fact]
    public async Task GetImpactDetail_ReturnsEmpty_WhenNoAffectedActiveRecords()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_123",
                IsAffected = false,
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456",
                IsAffected = true,
                IsActive = false
            }
        };

         _dbContext.HeatMapStatusViews.AddRange(heatMapStatusViews);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetImpactDetail();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapDetailByInfraObjectId Tests

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var infraobject = _infraObjectFixture.InfraObjectDto;

        infraobject.Name = "IO_123";
        await _dbContext.InfraObjects.AddAsync(infraobject);
        await _dbContext.SaveChangesAsync();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraObjectId = infraobject.ReferenceId;
        var heatMapType = "Server";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                HeatmapType = heatMapType,
                BusinessServiceName = "Service 1",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                HeatmapType = "Database", // Different type
                BusinessServiceName = "Service 2",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "IO_456", // Different infra object
                HeatmapType = heatMapType,
                BusinessServiceName = "Service 3",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapDetailByInfraObjectId(infraObjectId, heatMapType);

        // Assert
        Assert.Single(result);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
        Assert.Equal(heatMapType, result.First().HeatmapType);
        Assert.Equal("Service 1", result.First().BusinessServiceName);
    }

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectId_IsCaseInsensitive()
    {
        // Arrange
        var infraobject = _infraObjectFixture.InfraObjectDto;

        infraobject.Name = "IO_123";
        await _dbContext.InfraObjects.AddAsync(infraobject);
        await _dbContext.SaveChangesAsync();
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraObjectId = infraobject.ReferenceId;
        var heatMapType = "Server";

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = infraObjectId,
            HeatmapType = heatMapType,
            BusinessServiceName = "Service 1",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapDetailByInfraObjectId(infraObjectId, "SERVER");

        // Assert
        Assert.Single(result);
        Assert.Equal(infraObjectId, result.First().InfraObjectId);
        Assert.Equal(heatMapType, result.First().HeatmapType);
    }

    #endregion

    #region GetHeatMapDetailByInfraObjectAndEntityId Tests

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectAndEntityId_ReturnsMatchingRecord_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var infraObjectId = "IO_123";
        var entityId = "E_456";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                EntityId = entityId,
                BusinessServiceName = "Matching Service",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                EntityId = "E_789", // Different entity
                BusinessServiceName = "Different Entity Service",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "IO_999", // Different infra object
                EntityId = entityId,
                BusinessServiceName = "Different Infra Service",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapDetailByInfraObjectAndEntityId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(entityId, result.EntityId);
        Assert.Equal("Matching Service", result.BusinessServiceName);
        Assert.Equal("Server", result.HeatmapType);
    }

    [Fact]
    public async Task GetHeatMapDetailByInfraObjectAndEntityId_ReturnsNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "IO_123",
            EntityId = "E_456",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapDetailByInfraObjectAndEntityId("IO_999", "E_999");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetHeatMapListByBusinessServiceId Tests

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessSevice = _businessSeviceFixture.BusinessServiceDto;
        businessSevice.Name = "Business Service 1";
        await _dbContext.BusinessServices.AddAsync(businessSevice);
        await _dbContext.SaveChangesAsync();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var businessServiceId = businessSevice.ReferenceId;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456", // Different business service
                BusinessServiceName = "Service 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessServiceId, x.BusinessServiceId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidGuid = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetHeatMapListByBusinessServiceId(invalidGuid));
    }

    [Fact]
    public async Task GetHeatMapListByBusinessServiceId_ThrowsException_WhenEmptyGuid()
    {
        // Arrange
        var emptyGuid = "";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetHeatMapListByBusinessServiceId(emptyGuid));
    }

    #endregion

    #region GetHeatMapListByBusinessFunctionId Tests

    [Fact]
    public async Task GetHeatMapListByBusinessFunctionId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var businessFunctionId = businessFunction.ReferenceId;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = businessFunctionId,
                BusinessFunctionName = "Function 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = businessFunctionId,
                BusinessFunctionName = "Function 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessFunctionId = "BF_456", // Different business function
                BusinessFunctionName = "Function 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapListByBusinessFunctionId(businessFunctionId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessFunctionId, x.BusinessFunctionId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapListByBusinessFunctionId_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidGuid = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _repository.GetHeatMapListByBusinessFunctionId(invalidGuid));
    }

    #endregion

    #region GetHeatMapStatusByEntityId Tests

    [Fact]
    public async Task GetHeatMapStatusByEntityId_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var entityId = "E_123";

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = entityId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = entityId,
                BusinessServiceName = "Service 1",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_456", // Different entity
                BusinessServiceName = "Service 2",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusByEntityId(entityId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(entityId, x.EntityId));
        Assert.Contains(result, x => x.HeatmapType == "Server");
        Assert.Contains(result, x => x.HeatmapType == "Database");
        Assert.DoesNotContain(result, x => x.HeatmapType == "Network");
    }

    [Fact]
    public async Task GetHeatMapStatusByEntityId_ReturnsEmpty_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityId = "E_123",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusByEntityId("E_999");

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusByEntityIds Tests

    [Fact]
    public async Task GetHeatMapStatusByEntityIds_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var entityIds = new List<string> { "E_123", "E_456" };

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_123",
                BusinessServiceName = "Service 1",
                HeatmapType = "Server",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_456",
                BusinessServiceName = "Service 2",
                HeatmapType = "Database",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                EntityId = "E_789", // Not in the list
                BusinessServiceName = "Service 3",
                HeatmapType = "Network",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusByEntityIds(entityIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.EntityId == "E_123");
        Assert.Contains(result, x => x.EntityId == "E_456");
        Assert.DoesNotContain(result, x => x.EntityId == "E_789");
    }

    [Fact]
    public async Task GetHeatMapStatusByEntityIds_ReturnsEmpty_WhenEmptyList()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            EntityId = "E_123",
            BusinessServiceName = "Service",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusByEntityIds(new List<string>());

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetHeatMapStatusesByBusinessServiceIdAndType Tests

    [Fact]
    public async Task GetHeatMapStatusesByBusinessServiceIdAndType_ReturnsMatchingRecords_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var businessServiceId = "BS_123";
        var type = "Server";
        var isAffected = true;

        var heatMapStatusViews = new List<HeatMapStatusView>
        {
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = type,
                IsAffected = isAffected,
                BusinessServiceName = "Matching Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = type,
                IsAffected = false, // Different affected status
                BusinessServiceName = "Non-Affected Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = businessServiceId,
                HeatmapType = "Database", // Different type
                IsAffected = isAffected,
                BusinessServiceName = "Different Type Service",
                IsActive = true
            },
            new HeatMapStatusView
            {
                ReferenceId = Guid.NewGuid().ToString(),
                BusinessServiceId = "BS_456", // Different business service
                HeatmapType = type,
                IsAffected = isAffected,
                BusinessServiceName = "Different Service",
                IsActive = true
            }
        };

        await _dbContext.HeatMapStatusViews.AddRangeAsync(heatMapStatusViews);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetHeatMapStatusesByBusinessServiceIdAndType(businessServiceId, type, isAffected);

        // Assert
        Assert.Single(result);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
        Assert.Equal(type, result.First().HeatmapType);
        Assert.Equal(isAffected, result.First().IsAffected);
        Assert.Equal("Matching Service", result.First().BusinessServiceName);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddHeatMapStatusView_WhenValidHeatMapStatusView()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceId = "BS_123",
            BusinessServiceName = "Test Business Service",
            BusinessFunctionId = "BF_123",
            BusinessFunctionName = "Test Business Function",
            InfraObjectId = "IO_123",
            InfraObjectName = "Test Infrastructure Object",
            EntityId = "E_123",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            Properties = "Test Properties",
            ErrorMessage = "Test Error Message",
            PropertyKey = "TestKey",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(heatMapStatusView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(heatMapStatusView.BusinessServiceName, result.BusinessServiceName);
        Assert.Equal(heatMapStatusView.BusinessFunctionName, result.BusinessFunctionName);
        Assert.Equal(heatMapStatusView.InfraObjectName, result.InfraObjectName);
        Assert.Equal(heatMapStatusView.HeatmapType, result.HeatmapType);
        Assert.Equal(heatMapStatusView.HeatmapStatus, result.HeatmapStatus);
        Assert.Equal(heatMapStatusView.IsAffected, result.IsAffected);
        Assert.Single(_dbContext.HeatMapStatusViews);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenHeatMapStatusViewIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsHeatMapStatusView_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(heatMapStatusView.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(heatMapStatusView.Id, result.Id);
        Assert.Equal(heatMapStatusView.BusinessServiceName, result.BusinessServiceName);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateHeatMapStatusView_WhenValidHeatMapStatusView()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Original Service",
            HeatmapType = "Server",
            HeatmapStatus = "Online",
            IsAffected = false,
            IsActive = true
        };

        _dbContext.HeatMapStatusViews.Add(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        heatMapStatusView.BusinessServiceName = "Updated Service";
        heatMapStatusView.HeatmapStatus = "Offline";
        heatMapStatusView.IsAffected = true;

        // Act
        var result = await _repository.UpdateAsync(heatMapStatusView);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Service", result.BusinessServiceName);
        Assert.Equal("Offline", result.HeatmapStatus);
        Assert.True(result.IsAffected);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenHeatMapStatusViewIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveHeatMapStatusView_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var heatMapStatusView = new HeatMapStatusView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            BusinessServiceName = "Test Service",
            HeatmapType = "Server",
            IsActive = true
        };

        await _dbContext.HeatMapStatusViews.AddAsync(heatMapStatusView);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(heatMapStatusView);

        // Assert
        var deletedView = await _dbContext.HeatMapStatusViews.FindAsync(heatMapStatusView.Id);
        Assert.Null(deletedView);
    }

    #endregion
}
