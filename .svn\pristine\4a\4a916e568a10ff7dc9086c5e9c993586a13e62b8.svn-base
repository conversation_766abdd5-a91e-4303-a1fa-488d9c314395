using ContinuityPatrol.Application.Features.BulkImport.Commands.Next;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImport.Commands;

public class NextBulkImportTests : IClassFixture<BulkImportFixture>
{
    private readonly BulkImportFixture _bulkImportFixture;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly BulkImportHelperService _bulkImportHelperService;
    private readonly NextBulkImportCommandHandler _handler;

    public NextBulkImportTests(BulkImportFixture bulkImportFixture)
    {
        _bulkImportFixture = bulkImportFixture;

        _mockBulkImportOperationGroupRepository = BulkImportRepositoryMocks.CreateBulkImportOperationGroupRepository(_bulkImportFixture.BulkImportOperationGroups);
        _mockBulkImportActionResultRepository = BulkImportRepositoryMocks.CreateBulkImportActionResultRepository(_bulkImportFixture.BulkImportActionResults);
        _bulkImportHelperService = BulkImportRepositoryMocks.CreateBulkImportHelperService();

        _handler = new NextBulkImportCommandHandler(
            _mockBulkImportOperationGroupRepository.Object,
            _mockBulkImportActionResultRepository.Object,
            _bulkImportHelperService);
    }

    [Fact]
    public async Task Handle_Return_NextBulkImportResponse_When_NextBulkImportExecuted()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 1; // Set to create operation
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(NextBulkImportResponse));
        result.BulkImportOperationGroupId.ShouldBe(existingGroup.ReferenceId);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new NextBulkImportCommand { GroupId = nonExistentId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(nonExistentId))
            .ReturnsAsync((Domain.Entities.BulkImportOperationGroup)null);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BulkImportOperationGroupIsInactive()
    {
        // Arrange
        var inactiveGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var command = new NextBulkImportCommand { GroupId = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    

    [Fact]
    public async Task Handle_ProcessDeleteOperation_When_ConditionalOperationIs2()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Setup action results for the group
        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Server"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(actionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        //_mockBulkImportActionResultRepository.Verify(x => x.GetByOperationIdAndOperationGroupId(
        //    existingGroup.BulkImportOperationId, existingGroup.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallDeleteServer_When_ConditionalOperationIs2AndServerExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var serverActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Server"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(serverActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Note: Cannot verify BulkImportHelperService calls as it's a concrete service with non-virtual methods
        // The service will execute its actual logic during the test
    }

    [Fact]
    public async Task Handle_CallDeleteDatabase_When_ConditionalOperationIs2AndDatabaseExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var databaseActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Database"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(databaseActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Note: Cannot verify BulkImportHelperService calls as it's a concrete service with non-virtual methods
    }

    [Fact]
    public async Task Handle_CallDeleteReplication_When_ConditionalOperationIs2AndReplicationExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var replicationActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "Replication"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(replicationActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Note: Cannot verify BulkImportHelperService calls as it's a concrete service with non-virtual methods
    }

    [Fact]
    public async Task Handle_CallDeleteInfraObject_When_ConditionalOperationIs2AndInfraObjectExists()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var infraObjectActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult 
            { 
                BulkImportOperationId = existingGroup.BulkImportOperationId,
                BulkImportOperationGroupId = existingGroup.ReferenceId,
                EntityType = "InfraObject"
            }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(infraObjectActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Note: Cannot verify BulkImportHelperService calls as it's a concrete service with non-virtual methods
    }

    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_OperationSuccessful()
    {
        // Arrange
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<NextBulkImportResponse>();
        result.GetType().ShouldBe(typeof(NextBulkImportResponse));
    }

    [Fact]
    public async Task Handle_CallRepositoryWithCorrectId_When_QueryExecuted()
    {
        // Arrange
        var testId = Guid.NewGuid().ToString();
        var command = new NextBulkImportCommand { GroupId = testId };

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetByReferenceIdAsync(testId))
            .ReturnsAsync(_bulkImportFixture.BulkImportOperationGroups.First());

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetByReferenceIdAsync(testId), Times.Once);
    }


    [Fact]
    public async Task Handle_UseGuardAgainstNullOrDeactive_When_GroupValidation()
    {
        // Arrange
        var inactiveGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        inactiveGroup.IsActive = false;
        var command = new NextBulkImportCommand { GroupId = inactiveGroup.ReferenceId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ProcessMultipleEntityTypes_When_MixedActionResults()
    {
        // Arrange  
        var existingGroup = _bulkImportFixture.BulkImportOperationGroups.First();
        existingGroup.ConditionalOperation = 2;
        var command = new NextBulkImportCommand { GroupId = existingGroup.ReferenceId };

        var mixedActionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { EntityType = "Server", EntityId = "server1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "Database", EntityId = "db1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "Replication", EntityId = "rep1" },
            new Domain.Entities.BulkImportActionResult { EntityType = "InfraObject", EntityId = "infra1" }
        };

        _mockBulkImportActionResultRepository.Setup(x => x.GetByOperationIdAndOperationGroupId(
            existingGroup.BulkImportOperationId, existingGroup.ReferenceId))
            .ReturnsAsync(mixedActionResults);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Note: Cannot verify BulkImportHelperService calls as it's a concrete service with non-virtual methods
        // The service will execute its actual delete logic for all entity types during the test
    }

   

   
}
