using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DrCalenderFixture : IDisposable
{
    public List<DrCalenderActivity> DrCalenderActivityPaginationList { get; set; }
    public List<DrCalenderActivity> DrCalenderActivityList { get; set; }
    public DrCalenderActivity DrCalenderActivityDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string WorkflowProfileId = "WF_PROFILE_123";
    public const string ActivityName = "TestActivity";

    public ApplicationDbContext DbContext { get; private set; }

    public DrCalenderFixture()
    {
        var fixture = new Fixture();

        DrCalenderActivityList = fixture.Create<List<DrCalenderActivity>>();

        DrCalenderActivityPaginationList = fixture.CreateMany<DrCalenderActivity>(20).ToList();

        DrCalenderActivityPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DrCalenderActivityPaginationList.ForEach(x => x.IsActive = true);
        DrCalenderActivityPaginationList.ForEach(x => x.CompanyId = CompanyId);
        DrCalenderActivityPaginationList.ForEach(x => x.WorkflowProfiles = WorkflowProfileId);
        DrCalenderActivityPaginationList.ForEach(x => x.ActivityName = ActivityName);
        DrCalenderActivityPaginationList.ForEach(x => x.ScheduledStartDate = DateTime.Now.AddDays(1));
        DrCalenderActivityPaginationList.ForEach(x => x.ScheduledEndDate = DateTime.Now.AddDays(2));

        DrCalenderActivityList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DrCalenderActivityList.ForEach(x => x.IsActive = true);
        DrCalenderActivityList.ForEach(x => x.CompanyId = CompanyId);
        DrCalenderActivityList.ForEach(x => x.WorkflowProfiles = WorkflowProfileId);
        DrCalenderActivityList.ForEach(x => x.ActivityName = ActivityName);
        DrCalenderActivityList.ForEach(x => x.ScheduledStartDate = DateTime.Now.AddDays(1));
        DrCalenderActivityList.ForEach(x => x.ScheduledEndDate = DateTime.Now.AddDays(2));

        DrCalenderActivityDto = fixture.Create<DrCalenderActivity>();
        DrCalenderActivityDto.ReferenceId = Guid.NewGuid().ToString();
        DrCalenderActivityDto.IsActive = true;
        DrCalenderActivityDto.CompanyId = CompanyId;
        DrCalenderActivityDto.WorkflowProfiles = WorkflowProfileId;
        DrCalenderActivityDto.ActivityName = ActivityName;
        DrCalenderActivityDto.ScheduledStartDate = DateTime.Now.AddDays(1);
        DrCalenderActivityDto.ScheduledEndDate = DateTime.Now.AddDays(2);

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
