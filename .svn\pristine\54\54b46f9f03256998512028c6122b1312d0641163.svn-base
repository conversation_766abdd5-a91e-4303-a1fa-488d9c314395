using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftParameterService : BaseClient, IDriftParameterService
{
    public DriftParameterService(IConfiguration config, IAppCache cache, ILogger<DriftParameterService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DriftParameterListVm>> GetDriftParameterList()
    {
        var request = new RestRequest("api/v6/driftparameters");

        return await GetFromCache<List<DriftParameterListVm>>(request, "GetDriftParameterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftParameterCommand createDriftParameterCommand)
    {
        var request = new RestRequest("api/v6/driftparameters", Method.Post);

        request.AddJsonBody(createDriftParameterCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftParameterCommand updateDriftParameterCommand)
    {
        var request = new RestRequest("api/v6/driftparameters", Method.Put);

        request.AddJsonBody(updateDriftParameterCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftparameters/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DriftParameterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftparameters/{id}");

        return await Get<DriftParameterDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftParameterNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftparameters/name-exist?driftparameterName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftParameterListVm>> GetPaginatedDriftParameters(GetDriftParameterPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/driftparameters/paginated-list");

        return await Get<PaginatedResult<DriftParameterListVm>>(request);
    }
    #endregion
}
