﻿namespace ContinuityPatrol.Application.Features.AlertInformation.Commands.Create;

public class
    CreateAlertInformationCommandHandler : IRequestHandler<CreateAlertInformationCommand,
        CreateAlertInformationResponse>
{
    private readonly IAlertInformationRepository _alertInformationRepository;
    private readonly IMapper _mapper;

    public CreateAlertInformationCommandHandler(IAlertInformationRepository alertInformationRepository, IMapper mapper)
    {
        _alertInformationRepository = alertInformationRepository;
        _mapper = mapper;
    }

    public async Task<CreateAlertInformationResponse> Handle(CreateAlertInformationCommand request,
        CancellationToken cancellationToken)
    {
        var alertInformation = await _alertInformationRepository.AddAsync(new Domain.Entities.AlertInformation
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = request.Type,
            Severity = request.Severity,
            Code = request.Code,
            AlertFrequency = request.AlertFrequency
        });

        var response = new CreateAlertInformationResponse
        {
            Message = Message.Create(nameof(Domain.Entities.AlertInformation), alertInformation.Type),
            Id = alertInformation.ReferenceId
        };

        return response;
    }
}