﻿namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetNameUnique;

public class GetInfraObjectNameUniqueQueryHandler : IRequestHandler<GetInfraObjectNameUniqueQuery, bool>
{
    private readonly IInfraObjectRepository _infraObjectRepository;

    public GetInfraObjectNameUniqueQueryHandler(IInfraObjectRepository infraObjectRepository)
    {
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<bool> Handle(GetInfraObjectNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _infraObjectRepository.IsInfraObjectNameExist(request.InfraObjectName, request.InfraObjectId);
    }
}