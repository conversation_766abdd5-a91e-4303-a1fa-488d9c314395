﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Paginated;

public class WorkflowProfileInfoPaginatedEventHandler : INotificationHandler<WorkflowProfileInfoPaginatedEvent>
{
    private readonly ILogger<WorkflowProfileInfoPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowProfileInfoPaginatedEventHandler(ILogger<WorkflowProfileInfoPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }


    public async Task Handle(WorkflowProfileInfoPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.WorkflowProfileInfo.ToString(),
            Action = $"{ActivityType.View} {Modules.WorkflowProfileInfo}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Workflow Profile Management viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Workflow Profile Management viewed");
    }
}