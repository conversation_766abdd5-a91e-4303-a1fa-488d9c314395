namespace ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail
{
    public class GetMenuBuilderDetailsQueryHandler : IRequestHandler<GetMenuBuilderDetailQuery, MenuBuilderDetailVm>
    {
        private readonly IMenuBuilderRepository _menuBuilderRepository;
        private readonly IMapper _mapper;

        public GetMenuBuilderDetailsQueryHandler(IMapper mapper, IMenuBuilderRepository menuBuilderRepository)
        {
            _mapper = mapper;
            _menuBuilderRepository = menuBuilderRepository;
        }

        public async Task<MenuBuilderDetailVm> Handle(GetMenuBuilderDetailQuery request, CancellationToken cancellationToken)
        {
            var menuBuilder = await _menuBuilderRepository.GetByReferenceIdAsync(request.Id);

            Guard.Against.NullOrDeactive(menuBuilder, nameof(Domain.Entities.MenuBuilder), new NotFoundException(nameof(Domain.Entities.MenuBuilder), request.Id));

            var menuBuilderDetailDto = _mapper.Map<MenuBuilderDetailVm>(menuBuilder);

            return menuBuilderDetailDto;
        }
    }
}
