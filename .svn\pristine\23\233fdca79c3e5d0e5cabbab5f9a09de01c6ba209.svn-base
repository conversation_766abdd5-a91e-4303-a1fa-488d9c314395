﻿using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SvcMsSqlMonitorStatus.Queries
{
    public class SvcMsSqlMonitorStatusDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISvcMsSqlMonitorStatusRepository> _mockSvcMsSqlMonitorStatusRepository;
        private readonly SvcMsSqlMonitorStatusDetailQueryHandler _handler;

        public SvcMsSqlMonitorStatusDetailQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSvcMsSqlMonitorStatusRepository = new Mock<ISvcMsSqlMonitorStatusRepository>();
            _handler = new SvcMsSqlMonitorStatusDetailQueryHandler(_mockMapper.Object, _mockSvcMsSqlMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnDetailVm_WhenEntityExists()
        {
            var query = new SvcMsSqlMonitorStatusDetailQuery { Id = Guid.NewGuid().ToString() };

            var svcMsSqlMonitorStatus = new Domain.Entities.SvcMsSqlMonitorStatus
            {
                ReferenceId = query.Id,
                WorkflowName = "Critical",
                Type = "Active"
            };

            var expectedViewModel = new SvcMsSqlMonitorStatusDetailVm
            {
                Id = query.Id,
                WorkflowName = "Critical",
                Type = "Active"
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(svcMsSqlMonitorStatus);

            _mockMapper.Setup(m => m.Map<SvcMsSqlMonitorStatusDetailVm>(svcMsSqlMonitorStatus))
                .Returns(expectedViewModel);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedViewModel.Id, result.Id);
            Assert.Equal(expectedViewModel.WorkflowName, result.WorkflowName);
            Assert.Equal(expectedViewModel.Type, result.Type);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusDetailVm>(svcMsSqlMonitorStatus), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
        {
            var query = new SvcMsSqlMonitorStatusDetailQuery { Id = Guid.NewGuid().ToString() };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync((Domain.Entities.SvcMsSqlMonitorStatus)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusDetailVm>(It.IsAny<Domain.Entities.SvcMsSqlMonitorStatus>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenMappedResultIsNull()
        {
            var query = new SvcMsSqlMonitorStatusDetailQuery { Id = Guid.NewGuid().ToString() };

            var svcMsSqlMonitorStatus = new Domain.Entities.SvcMsSqlMonitorStatus
            {
                ReferenceId = query.Id,
                WorkflowName = "Critical",
                Type = "Active"
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(svcMsSqlMonitorStatus);

            _mockMapper.Setup(m => m.Map<SvcMsSqlMonitorStatusDetailVm>(svcMsSqlMonitorStatus))
                .Returns((SvcMsSqlMonitorStatusDetailVm)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<SvcMsSqlMonitorStatusDetailVm>(svcMsSqlMonitorStatus), Times.Once);
        }
    }
}
