﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.DRCalendar;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DRCalendar.Queries.GetPaginatedList;

public class GetDrCalendarPaginatedListQueryHandler : IRequestHandler<GetDrCalendarPaginatedListQuery,
    PaginatedResult<DrCalendarActivityListVm>>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDrCalenderRepository _drCalenderRepository;
    private readonly IMapper _mapper;

    public GetDrCalendarPaginatedListQueryHandler(IMapper mapper, IDrCalenderRepository drCalenderRepository,
        IBusinessServiceRepository businessServiceRepository)
    {
        _mapper = mapper;
        _drCalenderRepository = drCalenderRepository;
        _businessServiceRepository = businessServiceRepository;
    }

    public async Task<PaginatedResult<DrCalendarActivityListVm>> Handle(GetDrCalendarPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DrCalendarFilterSpecification(request.SearchString);

        var queryable =await _drCalenderRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var drCalendarQueryable = _mapper.Map<PaginatedResult<DrCalendarActivityListVm>>(queryable);
      
        var businessServiceIds = drCalendarQueryable.Data.Where(x=>x.BusinessServiceId.IsNotNullOrWhiteSpace()).Select(x => x.BusinessServiceId).ToList();

        var businessServices = await _businessServiceRepository.GetByReferenceIdsAsync(businessServiceIds);

        var businessServiceDict = businessServices.ToDictionary(bs => bs.ReferenceId, bs => bs.Name);
        
        drCalendarQueryable.Data.ForEach(query =>
        {
            if (!string.IsNullOrWhiteSpace(query.BusinessServiceId))
            {
                query.BusinessServiceName = businessServiceDict.TryGetValue(query.BusinessServiceId, out var name) ? name : "NA";
            }
        });        

        //foreach (var query in drCalendarQueryable1.Data)
        //    if (query.BusinessServiceId.IsNotNullOrWhiteSpace())
        //    {
        //        var businessService = await _businessServiceRepository.GetByReferenceIdAsync(query.BusinessServiceId);

        //        query.BusinessServiceName = businessService?.Name ?? "NA";
        //    }
       
        return drCalendarQueryable;
    }
}