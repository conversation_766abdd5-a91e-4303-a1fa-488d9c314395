using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class RoboCopyMonitoringLogsFixture : IDisposable
{
    public List<RoboCopyMonitorLogs> RoboCopyMonitorLogsPaginationList { get; set; }
    public List<RoboCopyMonitorLogs> RoboCopyMonitorLogsList { get; set; }
    public RoboCopyMonitorLogs RoboCopyMonitorLogsDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public RoboCopyMonitoringLogsFixture()
    {
        var fixture = new Fixture();

        RoboCopyMonitorLogsList = fixture.Create<List<RoboCopyMonitorLogs>>();

        RoboCopyMonitorLogsPaginationList = fixture.CreateMany<RoboCopyMonitorLogs>(20).ToList();

        RoboCopyMonitorLogsDto = fixture.Create<RoboCopyMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
