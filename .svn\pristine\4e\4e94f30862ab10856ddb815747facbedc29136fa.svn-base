﻿namespace ContinuityPatrol.Shared.Services.Extension;

public static class RestClientExtension
{
    public static async Task<RestResponse> PostAsJson<T>(this RestClient httpClient, string resource, T data,
        IConfiguration configuration, string token = null)
    {
        var request = new RestRequest(resource, Method.Post);

        if (!string.IsNullOrWhiteSpace(token))
        {
            request.AddHeader("authorization", "Bearer " + token);
        }
        else
        {
            var xapikey = configuration.GetValue<string>("x-api-key");
            request.AddHeader("x-api-key", xapikey);
        }

        request.AddHeader("Accept", "text/plain");
        request.AddHeader("user-agent", "CP6");
        //request.AddHeader("x-api-key", "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp");

        var dataAsString = JsonSerializer.Serialize(data);

        request.AddParameter("application/json", dataAsString, ParameterType.RequestBody);

        return await httpClient.ExecuteAsync(request);
    }

    public static async Task<RestResponse> PutAsJson<T>(this RestClient httpClient, string resource, T data,
        IConfiguration configuration, string token = null)
    {
        var request = new RestRequest(resource, Method.Put);

        if (!string.IsNullOrWhiteSpace(token))
        {
            request.AddHeader("authorization", "Bearer " + token);
        }
        else
        {
            var xapikey = configuration.GetValue<string>("x-api-key");
            request.AddHeader("x-api-key", xapikey);
        }

        request.AddHeader("Accept", "text/plain");
        request.AddHeader("user-agent", "CP6");

        var dataAsString = JsonSerializer.Serialize(data);

        request.AddParameter("application/json", dataAsString, ParameterType.RequestBody);

        return await httpClient.ExecuteAsync(request);
    }

    public static async Task<RestResponse> PutRangeAsJson<T>(this RestClient httpClient, string resource, List<T> data,
        IConfiguration configuration, string token = null)
    {
        var request = new RestRequest(resource, Method.Put);

        if (!string.IsNullOrWhiteSpace(token))
        {
            request.AddHeader("authorization", "Bearer " + token);
        }
        else
        {
            var xapikey = configuration.GetValue<string>("x-api-key");
            request.AddHeader("x-api-key", xapikey);
        }

        request.AddHeader("Accept", "text/plain");
        request.AddHeader("user-agent", "CP6");

        var dataAsString = JsonSerializer.Serialize(data);

        request.AddParameter("application/json", dataAsString, ParameterType.RequestBody);

        return await httpClient.ExecuteAsync(request);
    }

    public static async Task<RestResponse> DeleteAsJson<T>(this RestClient httpClient, string resource, T data,
        IConfiguration configuration, string token = null)
    {
        var request = new RestRequest(resource, Method.Delete);

        if (!string.IsNullOrWhiteSpace(token))
        {
            request.AddHeader("authorization", "Bearer " + token);
        }
        else
        {
            var xapikey = configuration.GetValue<string>("x-api-key");
            request.AddHeader("x-api-key", xapikey);
        }

        request.AddHeader("Accept", "text/plain");
        request.AddHeader("user-agent", "CP6");

        var dataAsString = JsonSerializer.Serialize(data);

        request.AddParameter("application/json", dataAsString, ParameterType.RequestBody);

        return await httpClient.ExecuteAsync(request);
    }
}