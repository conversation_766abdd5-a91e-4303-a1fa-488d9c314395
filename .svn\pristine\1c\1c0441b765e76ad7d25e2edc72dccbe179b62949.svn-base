﻿using ContinuityPatrol.Application.Helper;
using System.Net;
using System.Net.Mail;

namespace ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.SendEmail;

public class WorkflowDrCalenderSendEmailCommandHandler : IRequestHandler<WorkflowDrCalenderSendEmailCommand, WorkflowDrCalenderSendEmailResponse>
{
    private readonly IDrCalenderRepository _drCalenderRepository;

    private readonly ILogger<WorkflowDrCalenderSendEmailCommandHandler> _logger;  
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowProfileInfoViewRepository _workflowProfileInfoViewRepository;
    private readonly IUserRepository _userRepository;
 

    public WorkflowDrCalenderSendEmailCommandHandler(IDrCalenderRepository drCalenderRepository,ILogger<WorkflowDrCalenderSendEmailCommandHandler> logger,
        ISmtpConfigurationRepository smtpConfigurationRepository,IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowProfileInfoViewRepository workflowProfileInfoViewRepository,IUserRepository userRepository)
    {
        _drCalenderRepository = drCalenderRepository;
        _logger = logger;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;   
        _workflowProfileInfoViewRepository=workflowProfileInfoViewRepository;
        _userRepository=userRepository;

    }
    public async Task<WorkflowDrCalenderSendEmailResponse> Handle(WorkflowDrCalenderSendEmailCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var drCalenderDetail = await _drCalenderRepository.GetByReferenceIdAsync(request.DrCalenderId);

            var smtpDtl = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

            var workflowProfileInfoViews = await _workflowProfileInfoViewRepository.GetWorkflowProfileInfoViewByProfileIdandWorkflowIds(request.ProfileId, request.WorkflowIds);
           
            var workflowOperationGroupList = await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationIdAndWorkflowIds(request.WorkflowIds, request.WOrkflowOperationId);

            if (drCalenderDetail is not null && smtpDtl is not null)
            {
                var tableRows = new List<WorkflowDrCalenderSendEmailDataTableVm>();

                var organizer = await _userRepository.GetByReferenceIdAsync(drCalenderDetail.CreatedBy);

                request.WorkflowIds.ForEach(x =>
                {
                    tableRows.Add(new WorkflowDrCalenderSendEmailDataTableVm
                    {
                        WorkflowName = workflowProfileInfoViews.FirstOrDefault(wp => wp.WorkflowId == x).WorkflowName,
                        ActionType = workflowProfileInfoViews.FirstOrDefault(wp => wp.WorkflowId == x).WorkflowType ?? "Na",
                        StartTime = workflowOperationGroupList.FirstOrDefault(wg => wg.WorkflowId == x).CreatedDate,
                        EndTime = workflowOperationGroupList.FirstOrDefault(wg => wg.WorkflowId == x).LastModifiedDate,
                        Status = workflowOperationGroupList.FirstOrDefault(wg => wg.WorkflowId == x).Status ?? "Na"
                    });

                });

                var mail = new MailMessage
                {
                    From = new MailAddress(SecurityHelper.Decrypt(smtpDtl.UserName)),
                    Subject = $"CP Alert:Disaster Recovery (DR) Activity",
                    IsBodyHtml = true
                };


                foreach (var user in request.ResponsibleUser)
                {
                    if (!string.IsNullOrWhiteSpace(user.Email))
                    {
                        mail.To.Add(new MailAddress(user.Email));
                    }
                }
                var body = EmailTemplateHelper.WorkflowDrCalenderEmailBody(tableRows, drCalenderDetail.ScheduledStartDate, drCalenderDetail.ScheduledStartDate, drCalenderDetail.ScheduledEndDate, drCalenderDetail.Location, organizer.LoginName, request.ResponsibleUser.Select(x=>x.UserName).ToList(), request.ActivityDetail);

                var imageNames = new List<string>
                {
                };


                var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, "");

                mail.AlternateViews.Add(htmlView);

                using var smtpClient = new SmtpClient(smtpDtl.SmtpHost, Convert.ToInt32(smtpDtl.Port))
                {
                    Credentials = new NetworkCredential(
                               SecurityHelper.Decrypt(smtpDtl.UserName),
                               SecurityHelper.Decrypt(smtpDtl.Password)
                           ),
                    EnableSsl = smtpDtl.EnableSSL
                };


                await smtpClient.SendMailAsync(mail);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in workflow Dercalander send email");
            return new WorkflowDrCalenderSendEmailResponse
            {
                Message = ex.Message
            };
        }

        return new WorkflowDrCalenderSendEmailResponse
        {
            Message = "Email sent successfully"
        };

    }


}
