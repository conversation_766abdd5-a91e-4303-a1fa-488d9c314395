﻿@using ContinuityPatrol.Shared.Services.Helper
@model ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel.CyberComponentMappingViewModel;

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()
<link href="~/css/color_pallete.css" rel="stylesheet" />
<link href="~/css/workflowconfiguration.css" rel="stylesheet" />
@* <link href="~/lib/jquery-ui/dist/themes/base/jquery-ui.css" rel="stylesheet" />
<link href="~/lib/jquery-ui/dist/themes/base/jquery-ui.min.css" rel="stylesheet" /> *@
<style>
    .dot-border-bg .drag-option-card ul li {
        cursor: move;
    }

    .dot-border-bg {
        background-image: radial-gradient(#ebebeb 1px, #fff 1px) !important;
        background-size: 15px 15px;
        border-radius: var(--bs-card-border-radius);
    }

        .dot-border-bg .drag-option-card ul li a, .zoom-list li a {
            text-decoration: none;
            color: #333;
        }

        .dot-border-bg .drag-option-card ul li a {
            cursor: move;
        }

    .drag-card, .drag-card-inside {
        border: 1px solid #959595;
        background: transparent;
    }

        .drag-card .card-body {
            display: flex;
            align-items: center;
            justify-content: center;
        }

    .drag-btn {
        background-color: #C7E1FF;
        color: #067AFF;
        border-color: #C7E1FF;
    }

    .drag-option-card {
        width: 65px;
        border-radius: 50px;
        box-shadow: 0 .12rem 1rem rgb(0 0 0 / 7%) !important;
    }

    .zoom-list li {
        margin: 15px 0px;
        padding: 10px 8px;
        border-radius: 10px;
    }

        .zoom-list li a:hover i, .drag-option-card ul li:hover a {
            color: var(--bs-primary)
        }

    .circle-img {
        position: absolute;
        top: 35%;
        left: 50%;
        width: 35px;
    }

    .activeMenu {
        color: var(--bs-primary) !important;
    }
</style>
<div class="page-content" style="overflow-x: auto">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-mapping"></i><span>Mapping</span></h6>
            <button class="btn btn-primary" id="btnSaveCyberMapping">Save</button>
        </div>

        <div class="dot-border-bg card-body">
            <div class="row">
                <div class="col-1">
                    <div class="card list-card drag-option-card mx-auto text-center">
                        <ul class="list mb-0 d-flex flex-column align-items-center justif-content-center gap-3" style="list-style:none;padding:15px 0px;">
                            
                           @*  <li>
                                <a href="#" class="draggableCont menuSelection"> 
                                    <span class="d-block"><i class="cp-selection fs-5"></i></span> 
                                    <span>Selection</span> 
                                </a>
                            </li> *@

                            <li>
                                <a href="#" class="draggableCont">
                                    <span class="d-block"><i class="cp-area fs-5"></i></span>
                                    <span>Zone</span>
                                </a>
                            </li>
                            @* <li>
                            <a href="#" class="draggableCont">
                            <span class="d-block"><i class="cp-connects fs-5"></i></span>
                            <span>Connect</span>
                            </a>
                            </li> *@
                            <li>
                                <a href="#" id="btnAirgap">
                                    <span class="d-block"><i class="cp-air-gap fs-5"></i></span>
                                    <span>Air Gap</span>
                                </a>
                            </li>
                            @*   <li>
                            <a href="#" id="btnCreateGroup">
                            <span class="d-block"><i class="cp-park-solid-group fs-5"></i></span>
                            <span>Group</span>
                            </a>
                            </li> *@
                            @*  <li>
                            <a href="#" id="btnRemoveZone">
                            <span class="d-block"><i class="cp-Delete-table fs-5"></i></span>
                            <span>Remove Zone</span>
                            </a>
                            </li>
                            <li>
                            <a href="#" id="btnRemoveAirgap">
                            <span class="d-block"><i class="cp-file-not-to-delete fs-5"></i></span>
                            <span>Remove AirGap</span>
                            </a>
                            </li> *@
                            <li>
                                <a href="#" id="btnDeleteMapping">
                                    <span class="d-block"><i class="cp-Delete text-dark fs-5"></i></span>
                                    <span>Delete</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="contextMenu dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <ul class="UlContextBtn dropdown-menu dropdown-menu-lg-end fs-8" style="display:block;">
                        <li id="btnEdit">
                            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                <span><i class="cp-edit me-1 text-dark fs-7"></i>Edit</span>
                                @* <small class="font-monospace">ctrl + E</small> *@
                            </a>
                        </li>
                        <li id="btndelete">
                            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                <span><i class="cp-Delete me-1 text-dark  fs-7"></i>Delete</span>
                                @* <small class="font-monospace">Del</small> *@
                            </a>
                        </li>
                        <li id="btnRemove">
                            <a class="dropdown-item d-flex align-items-center justify-content-between px-2">
                                <span><i class="cp-file-not-to-delete me-1 text-dark  fs-7"></i>Remove</span>
                                @* <small class="font-monospace">Del</small> *@
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-11" id="diagramaticContainer" style="overflow-y: auto; height: calc(100vh - 193px);">
                    <div id="cyberContainer"></div>
                </div>
                @*   <div class="col-1 my-auto">
                <ul class="zoom-list p-0 d-grid justify-content-center mx-auto" style="list-style:none; width:fit-content">
                <li class="bg-white shadow-sm">
                <a href="#">
                <span class="d-block" data-bs-toggle="modal" data-bs-target="#CreateModal"><i class="cp-full-screen fs-5"></i></span>
                </a>
                </li>
                <li class="bg-white shadow-sm">
                <a href="#">
                <span class="d-block"><i class="cp-circle-plus fs-5"></i></span>
                </a>
                </li>
                <li class="bg-white shadow-sm">
                <a href="#">
                <span class="d-block"><i class="cp-circle-minus fs-5"></i></span>
                </a>
                </li>
                </ul>
                </div> *@
            </div>
        </div>
    </div>
</div>

<div class="modal fade" tabindex="-1" id="cyberZoneModal" aria-labelledby="cyberZoneModal" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-open"></i><span>Load Cyber Zone</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form id="example-form">
                    <div class="form-group">
                        <div class="form-label">Zone</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-area"></i></span>
                            <select class="form-select-modal select_actions" id="cyberZoneList" name="cyberZoneList" data-placeholder="Select Zone">
                            </select>
                        </div>
                        <span id="cyberZone-error"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary workflow" id="btnLoadComponents">Load</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="loadAirgapModal" aria-labelledby="configureModalLabel" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-open"></i><span>Add Airgap</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <form id="example-form">
                    <div class="form-group">
                        <div class="form-label">Airgap</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-airgap"></i></span>
                            <select class="form-select-modal " id="airgapList" name="airgapList" data-placeholder="Select Airgap">
                            </select>
                            <span class="input-group-text" type="button" data-bs-toggle="collapse" data-bs-target="#multiCollapseExample1" aria-expanded="false" aria-controls="multiCollapseExample1">
                                <i class="cp-colour-picker"></i>
                            </span>
                        </div>
                        <span id="workflowList-error"></span>
                    </div>
                    <div class="collapse multi-collapse" id="multiCollapseExample1">
                        <table class="table mb-1 text-center">
                            <tbody id="colorTable">
                                <tr>
                                    <td><input type="radio" name="color" id="red" value="red"> <label for="red"><span class="red "></span></label></td>
                                    <td><input type="radio" name="color" id="green"> <label for="green"><span class="green dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="yellow"> <label for="yellow"><span class="yellow dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="olive"> <label for="olive"><span class="olive dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="orange"> <label for="orange"><span class="orange dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="teal"> <label for="teal"><span class="teal dynamicColor"></span></label></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="color" id="blue"> <label for="blue"><span class="blue dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="violet"> <label for="violet"><span class="violet dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="purple"> <label for="purple"><span class="purple dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="pink"> <label for="pink"><span class="pink dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="darkblue"> <label for="darkblue"><span class="darkblue dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="pgreen"> <label for="pgreen"><span class="pgreen dynamicColor"></span></label></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="color" id="skyblue"> <label for="skyblue"><span class="skyblue dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="darkred"> <label for="darkred"><span class="darkred dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="darkpink"> <label for="darkpink"><span class="darkpink dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="green2"> <label for="green2"><span class="green2 dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="jupitar"> <label for="jupitar"><span class="jupitar dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="mustrad"> <label for="mustrad"><span class="mustrad dynamicColor"></span></label></td>
                                </tr>
                                <tr>
                                    <td><input type="radio" name="color" id="melon"> <label for="melon"><span class="melon dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="darkgrey"> <label for="darkgrey"><span class="darkgrey dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="violet_lite"> <label for="violet_lite"><span class="violet_lite dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="black"> <label for="black"><span class="black dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="chacolate"> <label for="chacolate"><span class="chacolate dynamicColor"></span></label></td>
                                    <td><input type="radio" name="color" id="pasigreen"> <label for="pasigreen"><span class="pasigreen dynamicColor"></span></label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary workflow" id="btnAddAirgap">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="ComponentMappingDelete" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <form enctype="multipart/form-data" class="w-100">
            <div class="modal-content">

                <div class="modal-header p-0">
                    <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
                </div>
                <div class="modal-body   text-center pt-0">
                    <h4>Are you sure?</h4>
                    <p class="d-flex align-items-center justify-content-center gap-1">You want to delete <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="deleteData">Cyber Mapping</span> data?</p>

                </div>
                <div class="modal-footer gap-2 justify-content-center">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary btn-sm" id="btnDeleteCyberComponentMapping">Delete</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="CybermanageCreate" data-create-permission="@WebHelper.CurrentSession.Permissions.Cyber.CreateAndEdit" aria-hidden="true"></div>
<script src="~/js/cyberresiliency/Manage/CyberMapping.js"></script>
<script src="~/js/CyberResiliency/Manage/Html2Canvas.js"></script>