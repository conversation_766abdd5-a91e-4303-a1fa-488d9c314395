using ContinuityPatrol.Domain.ViewModels.FiaCostModel;

namespace ContinuityPatrol.Application.Features.FiaCost.Queries.GetList;

public class GetFiaCostListQueryHandler : IRequestHandler<GetFiaCostListQuery, List<FiaCostListVm>>
{
    private readonly IFiaCostRepository _fiaCostRepository;
    private readonly IMapper _mapper;

    public GetFiaCostListQueryHandler(IMapper mapper, IFiaCostRepository fiaCostRepository)
    {
        _mapper = mapper;
        _fiaCostRepository = fiaCostRepository;
    }

    public async Task<List<FiaCostListVm>> Handle(GetFiaCostListQuery request, CancellationToken cancellationToken)
    {
        var fiaCosts = await _fiaCostRepository.ListAllAsync();

        if (fiaCosts.Count <= 0) return new List<FiaCostListVm>();

        return _mapper.Map<List<FiaCostListVm>>(fiaCosts);
    }
}
