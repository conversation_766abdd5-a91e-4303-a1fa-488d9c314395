﻿using ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Commands.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Events.Create;
using ContinuityPatrol.Application.Features.TeamMaster.Events.Delete;
using ContinuityPatrol.Application.Features.TeamMaster.Events.PaginatedView;
using ContinuityPatrol.Application.Features.TeamMaster.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class TeamMasterFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<TeamMaster> TeamMasters { get; set; }

    public CreateTeamMasterCommand CreateTeamMasterCommand { get; set; }
    public UpdateTeamMasterCommand UpdateTeamMasterCommand { get; set; }
    public TeamMasterCreatedEvent TeamMasterCreatedEvent { get; set; }
    public TeamMasterDeletedEvent TeamMasterDeletedEvent { get; set; }
    public TeamMasterUpdatedEvent TeamMasterUpdatedEvent { get; set; }
    public TeamMasterPaginatedEvent TeamMasterPaginatedEvent { get; set; }

    public TeamMasterFixture()
    {
        TeamMasters = AutoTeamMasterFixture.Create<List<TeamMaster>>();

        CreateTeamMasterCommand = AutoTeamMasterFixture.Create<CreateTeamMasterCommand>();

        UpdateTeamMasterCommand = AutoTeamMasterFixture.Create<UpdateTeamMasterCommand>();

        TeamMasterCreatedEvent = AutoTeamMasterFixture.Create<TeamMasterCreatedEvent>();

        TeamMasterDeletedEvent = AutoTeamMasterFixture.Create<TeamMasterDeletedEvent>();

        TeamMasterUpdatedEvent = AutoTeamMasterFixture.Create<TeamMasterUpdatedEvent>();

        TeamMasterPaginatedEvent = AutoTeamMasterFixture.Create<TeamMasterPaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<TeamMasterProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoTeamMasterFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateTeamMasterCommand>(p => p.GroupName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateTeamMasterCommand>(p => p.GroupName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TeamMasterCreatedEvent>(p => p.GroupName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TeamMasterDeletedEvent>(p => p.GroupName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TeamMasterUpdatedEvent>(p => p.GroupName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TeamMasterPaginatedEvent>(p => p.GroupName, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
