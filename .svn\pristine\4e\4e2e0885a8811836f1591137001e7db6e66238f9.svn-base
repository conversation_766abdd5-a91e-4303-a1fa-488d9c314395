using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixRequestModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class ApprovalMatrixRequestProfile : Profile
{
    public ApprovalMatrixRequestProfile()
    {
        CreateMap<ApprovalMatrixRequest, ApprovalMatrixRequestListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<ApprovalMatrixRequest, ApprovalMatrixRequestDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<ApprovalMatrixRequest, CreateApprovalMatrixRequestCommand>().ReverseMap();
        CreateMap<ApprovalMatrixRequest, ApprovalMatrixRequestViewModel>().ReverseMap();

        CreateMap<CreateApprovalMatrixRequestCommand, ApprovalMatrixRequestViewModel>().ReverseMap();
        CreateMap<UpdateApprovalMatrixRequestCommand, ApprovalMatrixRequestViewModel>().ReverseMap();

        CreateMap<UpdateApprovalMatrixRequestCommand, ApprovalMatrixRequest>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<ApprovalMatrixRequest>,PaginatedResult<ApprovalMatrixRequestListVm>>()
            .ForMember(dest=>dest.Data,opt=>opt.MapFrom(src=>src.Data));    
    }
}