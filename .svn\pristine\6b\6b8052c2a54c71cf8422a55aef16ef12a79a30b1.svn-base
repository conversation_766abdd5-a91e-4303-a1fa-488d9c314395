using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DriftImpactTypeMasterControllerTests : IClassFixture<DriftImpactTypeMasterFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DriftImpactTypeMastersController _controller;
    private readonly DriftImpactTypeMasterFixture _driftImpactTypeMasterFixture;

    public DriftImpactTypeMasterControllerTests(DriftImpactTypeMasterFixture driftImpactTypeMasterFixture)
    {
        _driftImpactTypeMasterFixture = driftImpactTypeMasterFixture;

        var testBuilder = new ControllerTestBuilder<DriftImpactTypeMastersController>();
        _controller = testBuilder.CreateController(
            _ => new DriftImpactTypeMastersController(),
            out _mediatorMock);
    }

    #region GetDriftImpactTypeMasters Tests

    [Fact]
    public async Task GetDriftImpactTypeMasters_WithValidRequest_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _driftImpactTypeMasterFixture.DriftImpactTypeMasterListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDriftImpactTypeMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.All(returnedList, item => Assert.Contains("Enterprise", item.ImpactType));
    }

    [Fact]
    public async Task GetDriftImpactTypeMasters_WithEmptyResult_ReturnsOkWithEmptyList()
    {
        // Arrange
        var emptyList = new List<DriftImpactTypeMasterListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDriftImpactTypeMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task GetDriftImpactTypeMasters_WithException_ThrowsException()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterListQuery>(), default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.GetDriftImpactTypeMasters());
    }

    #endregion

    #region CreateDriftImpactTypeMaster Tests

    [Fact]
    public async Task CreateDriftImpactTypeMaster_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterCommand;
        var expectedResponse = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftImpactTypeMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftImpactTypeMasterResponse>(createdResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftImpactTypeMaster_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        CreateDriftImpactTypeMasterCommand nullCommand = null;

        var successResponse = new CreateDriftImpactTypeMasterResponse
        {
            Success = true,
            Message = "DriftImpactTypeMaster created successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.CreateDriftImpactTypeMaster(nullCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftImpactTypeMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreateDriftImpactTypeMaster_WithEmptyImpactTypeName_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterCommand;
        command.ImpactType = ""; // Invalid empty name

        var failureResponse = new CreateDriftImpactTypeMasterResponse
        {
            Success = false,
            Message = "Impact type name cannot be empty"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.CreateDriftImpactTypeMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftImpactTypeMasterResponse>(createdResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("empty", returnedResponse.Message);
    }

    #endregion

    #region UpdateDriftImpactTypeMaster Tests

    [Fact]
    public async Task UpdateDriftImpactTypeMaster_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.UpdateDriftImpactTypeMasterCommand;
        var expectedResponse = _driftImpactTypeMasterFixture.UpdateDriftImpactTypeMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDriftImpactTypeMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftImpactTypeMasterResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
        Assert.True(Guid.TryParse(returnedResponse.Id, out _));
    }

    [Fact]
    public async Task UpdateDriftImpactTypeMaster_WithNullCommand_HandlesGracefully()
    {
        // Arrange
        UpdateDriftImpactTypeMasterCommand nullCommand = null;

        var successResponse = new UpdateDriftImpactTypeMasterResponse
        {
            Success = true,
            Message = "DriftImpactTypeMaster updated successfully",
            Id = Guid.NewGuid().ToString()
        };

        _mediatorMock
            .Setup(m => m.Send(nullCommand, default))
            .ReturnsAsync(successResponse);

        // Act
        var result = await _controller.UpdateDriftImpactTypeMaster(nullCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftImpactTypeMasterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDriftImpactTypeMaster_WithInvalidId_ReturnsFailureResponse()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.UpdateDriftImpactTypeMasterCommand;
        command.Id = "invalid-guid";

        var failureResponse = new UpdateDriftImpactTypeMasterResponse
        {
            Success = false,
            Message = "Invalid ID format"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(failureResponse);

        // Act
        var result = await _controller.UpdateDriftImpactTypeMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDriftImpactTypeMasterResponse>(okResult.Value);
        Assert.False(returnedResponse.Success);
        Assert.Contains("Invalid", returnedResponse.Message);
    }

    #endregion

    #region GetDriftImpactTypeMasterById Tests

    [Fact]
    public async Task GetDriftImpactTypeMasterById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftImpactTypeMasterFixture.DriftImpactTypeMasterDetailVm;
        expectedDetail.Id = id;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftImpactTypeMasterDetailQuery>(q => q.Id == id), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftImpactTypeMasterById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftImpactTypeMasterDetailVm>(okResult.Value);
        Assert.Equal(id, returnedDetail.Id);
        Assert.Contains("Enterprise", returnedDetail.ImpactType);
    }

    [Fact]
    public async Task GetDriftImpactTypeMasterById_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDriftImpactTypeMasterById(invalidId));
    }

    [Fact]
    public async Task GetDriftImpactTypeMasterById_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDriftImpactTypeMasterById(nullId));
    }

    #endregion

    #region DeleteDriftImpactTypeMaster Tests

    [Fact]
    public async Task DeleteDriftImpactTypeMaster_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftImpactTypeMasterFixture.DeleteDriftImpactTypeMasterResponse;
        expectedResponse.IsActive=false;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDriftImpactTypeMasterCommand>(c => c.Id == id), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftImpactTypeMaster(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftImpactTypeMasterResponse>(okResult.Value);
        Assert.Equal(false, returnedResponse.IsActive);
        Assert.True(returnedResponse.Success);
        Assert.Contains("deleted successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task DeleteDriftImpactTypeMaster_WithInvalidId_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDriftImpactTypeMaster(invalidId));
    }

    [Fact]
    public async Task DeleteDriftImpactTypeMaster_WithNullId_ThrowsArgumentNullException()
    {
        // Arrange
        string nullId = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDriftImpactTypeMaster(nullId));
    }

    #endregion

    #region GetPaginatedDriftImpactTypeMasters Tests

    [Fact]
    public async Task GetPaginatedDriftImpactTypeMasters_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _driftImpactTypeMasterFixture.GetDriftImpactTypeMasterPaginatedListQuery;
        var expectedResult = _driftImpactTypeMasterFixture.DriftImpactTypeMasterPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedDriftImpactTypeMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.NotEmpty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftImpactTypeMasters_WithNullQuery_HandlesGracefully()
    {
        // Arrange
        GetDriftImpactTypeMasterPaginatedListQuery nullQuery = null;

        var emptyResult = new PaginatedResult<DriftImpactTypeMasterListVm>
        {
            Data = new List<DriftImpactTypeMasterListVm>(),
            TotalCount = 0,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(nullQuery, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftImpactTypeMasters(nullQuery);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.True(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    [Fact]
    public async Task GetPaginatedDriftImpactTypeMasters_WithInvalidPageSize_ReturnsEmptyResult()
    {
        // Arrange
        var query = _driftImpactTypeMasterFixture.GetDriftImpactTypeMasterPaginatedListQuery;
        query.PageSize = 0; // Invalid page size

        var emptyResult = new PaginatedResult<DriftImpactTypeMasterListVm>
        {
            Data = new List<DriftImpactTypeMasterListVm>(),
            TotalCount = 0,
            Succeeded = false
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedDriftImpactTypeMasters(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.False(returnedResult.Succeeded);
        Assert.Empty(returnedResult.Data);
    }

    #endregion

    #region IsDriftImpactTypeMasterNameUnique Tests

    [Fact]
    public async Task IsDriftImpactTypeMasterNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var impactTypeName = "Unique Enterprise Impact Type";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftImpactTypeMasterNameUniqueQuery>(q => q.Name == impactTypeName), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsDriftImpactTypeMasterNameExist(impactTypeName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.True(isUnique);
    }

    [Fact]
    public async Task IsDriftImpactTypeMasterNameUnique_WithDuplicateName_ReturnsFalse()
    {
        // Arrange
        var impactTypeName = "Duplicate Enterprise Impact Type";
        var id = Guid.NewGuid().ToString();


        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDriftImpactTypeMasterNameUniqueQuery>(q => q.Name == impactTypeName), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsDriftImpactTypeMasterNameExist(impactTypeName,id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var isUnique = Assert.IsType<bool>(okResult.Value);
        Assert.False(isUnique);
    }

    [Fact]
    public async Task IsDriftImpactTypeMasterNameUnique_WithNullName_ThrowsArgumentNullException()
    {
        // Arrange
        string nullName = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsDriftImpactTypeMasterNameExist(nullName,""));
    }

    #endregion

    #region Additional Comprehensive Test Cases

    [Fact]
    public async Task CreateDriftImpactTypeMaster_WithHighImpactLevel_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterCommand;
        command.ImpactType = "High Impact Enterprise Type";
     
        var expectedResponse = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftImpactTypeMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftImpactTypeMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftImpactTypeMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("High", command.ImpactType);
    }

    [Fact]
    public async Task CreateDriftImpactTypeMaster_WithLowImpactLevel_ReturnsCreatedResult()
    {
        // Arrange
        var command = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterCommand;
        command.ImpactType = "Low Impact Enterprise Type";
       
        var expectedResponse = _driftImpactTypeMasterFixture.CreateDriftImpactTypeMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreateDriftImpactTypeMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDriftImpactTypeMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDriftImpactTypeMasterResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("Low", command.ImpactType);
    }


    [Fact]
    public async Task GetDriftImpactTypeMasterById_WithActiveStatus_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedDetail = _driftImpactTypeMasterFixture.DriftImpactTypeMasterDetailVm;
        expectedDetail.IsActive = true;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterDetailQuery>(), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDriftImpactTypeMasterById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DriftImpactTypeMasterDetailVm>(okResult.Value);
        Assert.True(returnedDetail.IsActive);
    }

    [Fact]
    public async Task DeleteDriftImpactTypeMaster_WithDependentRecords_ReturnsOkResult()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = _driftImpactTypeMasterFixture.DeleteDriftImpactTypeMasterResponse;
        expectedResponse.Message = "Impact type master and dependent records deleted successfully";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<DeleteDriftImpactTypeMasterCommand>(), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDriftImpactTypeMaster(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDriftImpactTypeMasterResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("dependent records", returnedResponse.Message);
    }

    [Fact]
    public async Task IsDriftImpactTypeMasterNameUnique_WithSimilarName_ReturnsFalse()
    {
        // Arrange
        var impactTypeName = "Enterprise Impact Type Similar";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterNameUniqueQuery>(), default))
            .ReturnsAsync(true); // Name exists

        // Act
        var result = await _controller.IsDriftImpactTypeMasterNameExist(impactTypeName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var nameExists = Assert.IsType<bool>(okResult.Value);
        Assert.True(nameExists);
    }

    [Fact]
    public async Task GetDriftImpactTypeMasters_WithSortedByImpactLevel_ReturnsOkResult()
    {
        // Arrange
        var sortedList = new List<DriftImpactTypeMasterListVm>
        {
            new DriftImpactTypeMasterListVm
            {
                Id = Guid.NewGuid().ToString(),
                ImpactType = "Enterprise Critical Impact",
              
            },
            new DriftImpactTypeMasterListVm
            {
                Id = Guid.NewGuid().ToString(),
                ImpactType = "Enterprise Medium Impact",
               
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftImpactTypeMasterListQuery>(), default))
            .ReturnsAsync(sortedList);

        // Act
        var result = await _controller.GetDriftImpactTypeMasters();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DriftImpactTypeMasterListVm>>(okResult.Value);
        Assert.Equal(2, returnedList.Count);
        
    }

    #endregion
}
