﻿using ContinuityPatrol.Application.Features.Form.Events.Import;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Application.Features.Form.Commands.Import;

public class ImportFormCommandHandler : IRequestHandler<ImportFormCommand, ImportFormResponse>
{
    private readonly IFormRepository _formRepository;
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;
    private readonly IComponentTypeRepository _componentTypeRepository;
    private readonly IPublisher _publisher;
    private readonly ILogger<ImportFormCommandHandler> _logger;
    private readonly IMapper _mapper;


    public ImportFormCommandHandler(IFormRepository formRepository, IFormTypeCategoryRepository formTypeCategoryRepository, IComponentTypeRepository componentTypeRepository, ILogger<ImportFormCommandHandler> logger, IMapper mapper
        ,IPublisher publisher)
    {
        _formRepository = formRepository;
        _formTypeCategoryRepository = formTypeCategoryRepository;
        _componentTypeRepository = componentTypeRepository;
        _logger = logger;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<ImportFormResponse> Handle(ImportFormCommand request, CancellationToken cancellationToken)
    {
        try
        {

            await CreateForm(request);
            await CreateFormCategory(request);
            await CreateComponentType(request);

           await _publisher.Publish(new FormImportEvent());
            return new ImportFormResponse
            {
                Message = "Forms imported successfully!"
            };

        }
        catch (Exception ex)
        {
            _logger.Exception("Error in ImportFormCommandHandler",ex);

            return new ImportFormResponse
            {
                Success = false,
                Message = ex.GetMessage()
            };
        }

       
    }

    private async Task CreateComponentType(ImportFormCommand request)
    {
        _logger.LogDebug("Starting CreateComponentType method.");

        var ids = request.ComponentTypes?
            .Where(x => x.Id.IsNotNullOrWhiteSpace())
            .Select(x => x.Id).ToList();


        if (ids is not null && ids.Count > 0)
        {
            var eventToUpdates = await _componentTypeRepository.GetComponentTypeByIds(ids);

            var eventToUpdatesDict = eventToUpdates.ToDictionary(y => y.ReferenceId);

            var updateComponentType = request.ComponentTypes
                .Where(x => eventToUpdatesDict.ContainsKey(x.Id)) // Check existence
                .Select(x =>
                {
                    var matched = eventToUpdatesDict[x.Id];

                    var f = _mapper.Map<Domain.Entities.ComponentType>(x);

                    f.ReferenceId = x.Id;
                    f.Id = matched.Id;
                    f.CreatedBy = matched.CreatedBy;
                    f.CreatedDate = matched.CreatedDate;
                    f.LastModifiedBy = matched.LastModifiedBy;
                    f.LastModifiedDate = matched.LastModifiedDate;

                    return f;
                })
                .ToList();

            if(updateComponentType.Any())
                await _componentTypeRepository.UpdateRangeAsync(updateComponentType);

            var newComponentType = request.ComponentTypes
                .Where(x => !eventToUpdates.Select(y => y.ReferenceId).Contains(x.Id))
                .Select(x =>
                {
                    var formTypeCategory = _mapper.Map<Domain.Entities.ComponentType>(x);
                    formTypeCategory.ReferenceId = x.Id;

                    return formTypeCategory;
                }).ToList();

            if(newComponentType.Any())
                await _componentTypeRepository.AddRangeAsync(newComponentType);
        }
        else
        {
            _logger.LogWarning("No valid component names found to process.");
        }

        _logger.LogDebug("CreateComponentType method completed.");
    }

    private async Task CreateFormCategory(ImportFormCommand request)
    {
        _logger.LogDebug("Starting CreateFormCategory method.");

        var ids = request.FormTypeCategories?
            .Where(x => x.Id.IsNotNullOrWhiteSpace())
            .Select(x => x.Id).ToList();

        if (ids is not null && ids.Count > 0)
        {
            var eventToUpdates = await _formTypeCategoryRepository.GetFormTypeCategoryByIds(ids);

            var eventToUpdatesDict = eventToUpdates.ToDictionary(y => y.ReferenceId);

            var updateFormTypeCategory = request.FormTypeCategories
                .Where(x => eventToUpdatesDict.ContainsKey(x.Id)) // Check existence
                .Select(x =>
                {
                    var matched = eventToUpdatesDict[x.Id];

                    var f = _mapper.Map<Domain.Entities.FormTypeCategory>(x);

                    f.ReferenceId = x.Id;
                    f.Id = matched.Id;
                    f.CreatedBy = matched.CreatedBy;
                    f.CreatedDate = matched.CreatedDate;
                    f.LastModifiedBy = matched.LastModifiedBy;
                    f.LastModifiedDate = matched.LastModifiedDate;

                    return f;
                })
                .ToList();

            if(updateFormTypeCategory.Any())
                await _formTypeCategoryRepository.UpdateRangeAsync(updateFormTypeCategory);

            var newFormTypeCategory = request.FormTypeCategories
                .Where(x => !eventToUpdates.Select(y => y.ReferenceId).Contains(x.Id))
                .Select(x =>
                {
                    var formTypeCategory = _mapper.Map<Domain.Entities.FormTypeCategory>(x);
                    formTypeCategory.ReferenceId = x.Id;

                    return formTypeCategory;
                }).ToList();

            if(newFormTypeCategory.Any())
                await _formTypeCategoryRepository.AddRangeAsync(newFormTypeCategory);
        }
        else
        {
            _logger.LogDebug("No valid form type categories found to process.");
        }

        _logger.LogDebug("CreateFormCategory method completed.");
    }

    private async Task CreateForm(ImportFormCommand request)
    {
        _logger.LogDebug("Starting CreateForm method.");

        var ids = request.Forms
            .Where(x => x.Id.IsNotNullOrWhiteSpace())
            .Select(x => x.Id).ToList();

        if (ids.Count > 0)
        {
            _logger.LogDebug("Processing {Count} forms.", ids.Count);

            var eventToUpdates = await _formRepository.GetFormsByIds(ids);

            var eventToUpdatesDict = eventToUpdates.ToDictionary(y => y.ReferenceId);

            var updateForms = request.Forms
                .Where(x => eventToUpdatesDict.ContainsKey(x.Id)) // Check existence
                .Select(x =>
                {
                    var matched = eventToUpdatesDict[x.Id];

                    var f = _mapper.Map<Domain.Entities.Form>(x);

                    f.ReferenceId = x.Id;
                    f.Id = matched.Id;
                    f.IsLock = matched.IsLock;
                    f.CreatedBy = matched.CreatedBy;
                    f.CreatedDate = matched.CreatedDate;
                    f.LastModifiedBy = matched.LastModifiedBy;
                    f.LastModifiedDate = matched.LastModifiedDate;

                    return f;
                })
                .ToList();

            if(updateForms.Any())
                await _formRepository.UpdateRangeAsync(updateForms);

            var newForms = request.Forms
                .Where(x => !eventToUpdates.Select(y => y.ReferenceId).Contains(x.Id))
                .Select(x =>
                {
                    var form = _mapper.Map<Domain.Entities.Form>(x);
                    form.ReferenceId = x.Id;

                    return form;
                }).ToList();

            if(newForms.Any())
             await _formRepository.AddRangeAsync(newForms);
        }
        else
        {
            _logger.LogDebug("No valid forms found to process.");
        }

        _logger.LogDebug("CreateForm method completed.");
    }
}