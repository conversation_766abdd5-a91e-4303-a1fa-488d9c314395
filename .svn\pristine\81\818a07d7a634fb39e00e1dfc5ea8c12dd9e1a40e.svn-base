﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class UserGroupRepository : BaseRepository<UserGroup>, IUserGroupRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public UserGroupRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<UserGroup>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(company => company.ReferenceId.Equals(_loggedInUserService.CompanyId));
    }

    public override Task<UserGroup> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(userGroup =>
                    userGroup.ReferenceId.Equals(id)).Result
                .SingleOrDefault());
    }

    public  async Task<List<UserGroup>> GetUserGroupByUserId(string userId)
    {
        var userGroups = await (_loggedInUserService.IsParent
        ? base.FilterBy(x => x.UserProperties.Contains(userId))
        : base.FilterBy(x => x.UserProperties.Contains(userId))).ToListAsync();        

        return userGroups;
    }

    public override async Task<PaginatedResult<UserGroup>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<UserGroup> specification, string sortColumn, string sortOrder)
    {
        return await SelectToUserGroups(Entities.Specify(specification).DescOrderById())
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }


    public async Task<List<UserGroup>> GetUserGroupNames()
    {
        return await Entities
            .Active()
            .Select(x => new UserGroup
                { ReferenceId = x.ReferenceId, GroupName = x.GroupName })
            .OrderBy(x => x.GroupName)
            .ToListAsync();
    }

    public Task<bool> IsGroupNameUnique(string name)
    {
        return Task.FromResult(_dbContext.UserGroup.Any(e => e.GroupName.Equals(name)));
    }

    public Task<bool> IsGroupNameExist(string name, string id)
    {
        var match = !id.IsValidGuid() ?
            _dbContext.UserGroup.Any(e => e.GroupName.Equals(name))
            : _dbContext.UserGroup.Where(e => e.GroupName.Equals(name)).ToList().Unique(id);

        return Task.FromResult(match);

    }

    private IQueryable<UserGroup> SelectToUserGroups(IQueryable<UserGroup> query)
    {
        return query.Select(x=> new UserGroup
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            GroupDescription= x.GroupDescription,   
            GroupName = x.GroupName,
            UserId = x.UserId,
            UserNames = x.UserNames,
            UserProperties = x.UserProperties
        });
    }

}