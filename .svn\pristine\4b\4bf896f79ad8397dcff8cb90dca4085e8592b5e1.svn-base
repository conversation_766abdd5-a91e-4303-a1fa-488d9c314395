﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;

public class GetReplicationJobPaginatedListQueryHandler : IRequestHandler<GetReplicationJobPaginatedListQuery,
    PaginatedResult<ReplicationJobListVm>>
{
    private readonly IMapper _mapper;
    private readonly IReplicationJobRepository _replicationJobRepository;


    public GetReplicationJobPaginatedListQueryHandler(IMapper mapper,
        IReplicationJobRepository replicationJobRepository)
    {
        _mapper = mapper;
        _replicationJobRepository = replicationJobRepository;
    }

    public async Task<PaginatedResult<ReplicationJobListVm>> Handle(GetReplicationJobPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new ReplicationJobFilterSpecification(request.SearchString);

        var queryable =await _replicationJobRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var replicationList = _mapper.Map<PaginatedResult<ReplicationJobListVm>>(queryable);

        return replicationList;
    }
}