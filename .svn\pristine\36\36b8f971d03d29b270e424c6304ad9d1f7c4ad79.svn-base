﻿using ContinuityPatrol.Application.Features.TeamResource.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Commands;

public class UpdateTeamResourceTests : IClassFixture<TeamResourceFixture>
{
    private readonly TeamResourceFixture _teamResourceFixture;

    private readonly Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    private readonly UpdateTeamResourceCommandHandler _handler;

    public UpdateTeamResourceTests(TeamResourceFixture teamResourceFixture)
    {
        _teamResourceFixture = teamResourceFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockTeamResourceRepository = TeamResourceRepositoryMocks.UpdateTeamResourceRepository(_teamResourceFixture.TeamResources);

        _handler = new UpdateTeamResourceCommandHandler(_teamResourceFixture.Mapper, _mockTeamResourceRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidTeamResource_UpdateReferenceIdAsync_ToTeamResourcesRepo()
    {
        _teamResourceFixture.UpdateTeamResourceCommand.Id = _teamResourceFixture.TeamResources[0].ReferenceId;

        var result = await _handler.Handle(_teamResourceFixture.UpdateTeamResourceCommand, CancellationToken.None);

        var teamResource = await _mockTeamResourceRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_teamResourceFixture.UpdateTeamResourceCommand.ResourceName, teamResource.ResourceName);
    }

    [Fact]
    public async Task Handle_Return_ValidTeamResourceResponse_WhenUpdate_TeamResource()
    {
        _teamResourceFixture.UpdateTeamResourceCommand.Id = _teamResourceFixture.TeamResources[0].ReferenceId;

        var result = await _handler.Handle(_teamResourceFixture.UpdateTeamResourceCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateTeamResourceResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_teamResourceFixture.UpdateTeamResourceCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidTeamResourceId()
    {
        _teamResourceFixture.UpdateTeamResourceCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_teamResourceFixture.UpdateTeamResourceCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _teamResourceFixture.UpdateTeamResourceCommand.Id = _teamResourceFixture.TeamResources[0].ReferenceId;

        await _handler.Handle(_teamResourceFixture.UpdateTeamResourceCommand, CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockTeamResourceRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.TeamResource>()), Times.Once);
    }
}
