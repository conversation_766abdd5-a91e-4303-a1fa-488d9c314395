﻿
importScripts('../../common/signalr.js')

self.addEventListener('message', function (e) {
    const { action, connections } = e.data;
    const connectionMap = {};

    if (action === 'start') {      
        connections.forEach(conn => {
            //const connection = new signalR.HubConnectionBuilder()
            //    .withUrl(conn.url)
            //    .configureLogging(signalR.LogLevel.Information)
            //    .build();

            const connection = new signalR.HubConnectionBuilder()
                .withUrl(conn.url, {
                    skipNegotiation: true,
                    rejectUnauthorized: false,
                    //secure: false,
                    //ws: true,
                    transport: signalR.HttpTransportType.WebSockets
                })
                .configureLogging(signalR.LogLevel.Information)
                .build();

            connection.start()
                .then(() => self.postMessage({ status: "connected", name: conn.name }))
                .catch(err => self.postMessage({ status: "error", name: conn.name, message: err.toString() }));

            //if (conn?.sendConnection) {
            //    connection.on(conn?.sendConnection, function (user, message) {
            //        self.postMessage({ status: "message", name: conn.name, user, message });
            //    });
            //}

            connection.on(conn?.receiveConnection, function (user, message) {
                self.postMessage({ status: "message", name: conn.name, user, message });
            });

            connectionMap[conn.name] = connection;
        });
    }

    if (action === 'stop') {
        for (const name in connectionMap) {
            connectionMap[name].stop().then(() => {
                self.postMessage({ status: "disconnected", name });
            });
        }
    }
});
