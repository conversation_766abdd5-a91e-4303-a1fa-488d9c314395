﻿namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;

public class UpdateWorkflowProfileInfoCommand : IRequest<UpdateWorkflowProfileInfoResponse>
{
    public string Id { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string CurrentActionId { get; set; }
    public string CurrentActionName { get; set; }
    public string Message { get; set; }
    public string ProgressStatus { get; set; }
    public string Status { get; set; }
    public int ConditionalOperation { get; set; }
    public string WorkflowType { get; set; }
    public string ActionMode { get; set; }
    public bool IsLock { get; set; }
    public bool IsPublish { get; set; }
    public int Index { get; set; }
    public override string ToString()
    {
        return $"Name: {ProfileName}; Id:{Id};";
    }
}