﻿using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    public partial class RPOSLADeviationReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public  GetRpoSlaDeviationReportVm getRpoSlaDeviationReportVm = new GetRpoSlaDeviationReportVm();
        public RPOSLADeviationReport(string data)
        {
            try
            {                               
                getRpoSlaDeviationReportVm = JsonConvert.DeserializeObject<GetRpoSlaDeviationReportVm>(data);                 
                _logger = PreBuildReportController._logger;
                var report = getRpoSlaDeviationReportVm.RpoSlaDeviationReportByStartTimeAndEndTimeVms;
                InitializeComponent();
                this.DataSource = report;
                ClientCompanyLogo();
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
                var startDate = getRpoSlaDeviationReportVm.ActiveStartDate.ToString();
                xrLabel4.Text = startDate.ToDateTime().ToString("dd-MM-yyyy");
                var endDate = getRpoSlaDeviationReportVm.ActiveEndDate.ToString();
                xrLabel5.Text = endDate.ToDateTime().ToString("dd-MM-yyyy");
                xrLblInfra.Text = getRpoSlaDeviationReportVm.REPOSLADeviationReportDataName.ToString();
                this.DisplayName = getRpoSlaDeviationReportVm.REPOSLADeviationReportDataName + "_RPOSLADeviationReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");

            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLADeviation Report. The error message : " + ex.Message); throw; }

        }
        private int serialNumber = 1;
        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + getRpoSlaDeviationReportVm.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLADeviation Report's User Name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RPOSLADeviation Report's CP Version. The error message : " + ex.Message); throw; }
        }

        [SupportedOSPlatform("windows")]
        private static Image LoadImageFromFile(string path)
        {
            return Image.FromFile(path);
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        if (OperatingSystem.IsWindows())
                        {
                            prClientLogo.Image = LoadImageFromFile(ms.ToString());
                        }
                        else
                        {
                            throw new PlatformNotSupportedException("Image loading only works on Windows in this context.");
                        }
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the RPOSLADeviation Report's customer logo" + ex.Message.ToString());
            }
        }
    }
}
