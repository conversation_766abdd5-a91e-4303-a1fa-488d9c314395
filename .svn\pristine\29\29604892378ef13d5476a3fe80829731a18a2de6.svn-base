namespace ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;

public class UpdateVeritasClusterCommandValidator : AbstractValidator<UpdateVeritasClusterCommand>
{
    private readonly IVeritasClusterRepository _veritasClusterRepository;

    public UpdateVeritasClusterCommandValidator(IVeritasClusterRepository veritasClusterRepository)
    {
        _veritasClusterRepository = veritasClusterRepository;

        RuleFor(p => p.ClusterProfileName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.ClusterName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.ClusterServerName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(e => e)
            .MustAsync(SiteNameUnique)
            .WithMessage("A same name already exists");
    }

    private async Task<bool> SiteNameUnique(UpdateVeritasClusterCommand e, CancellationToken token)
    {
        return !await _veritasClusterRepository.IsNameExist(e.ClusterProfileName, e.Id);
    }
}