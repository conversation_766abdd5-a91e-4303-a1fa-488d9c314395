﻿using ContinuityPatrol.Application.Features.UserLogin.Events.Logout;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserLogin.Events;

public class UserLogoutEventTests : IClassFixture<UserLoginFixture>
{
    private readonly UserLoginFixture _userLoginFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly UserLogoutEventHandler _handler;

    public UserLogoutEventTests(UserLoginFixture userLoginFixture)
    {
        _userLoginFixture = userLoginFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockUserLoginEventLogger = new Mock<ILogger<UserLogoutEventHandler>>();

        _mockUserActivityRepository = UserLoginRepositoryMocks.CreateUserLoginEventRepository(_userLoginFixture.UserActivities);

        _handler = new UserLogoutEventHandler(mockLoggedInUserService.Object, mockUserLoginEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UserLogoutEvent()
    {
        _userLoginFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_userLoginFixture.UserLogoutEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_userLoginFixture.UserLogoutEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UserLogoutEvent()
    {
        _userLoginFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_userLoginFixture.UserLogoutEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }
}