﻿using AutoFixture;
using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.Features.Node.Commands.Update;
using ContinuityPatrol.Application.Features.Node.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Node.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class NodeControllerShould
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<NodeController>> _mockLogger =new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  NodeController _controller;

        public NodeControllerShould()
        {
            
            _controller = new NodeController(_mockPublisher.Object, _mockLogger.Object, _mockDataProvider.Object, _mockMapper.Object);
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewResult_WithNodeViewModel()
        {
            // Arrange
            var nodeList = new List<NodeListVm>();
            //_mockPublisher.Setup(p => p.Publish(It.IsAny<NodePaginatedEvent>())).Returns(Task.CompletedTask);
            _mockDataProvider.Setup(dp => dp.Node.GetNodeList()).ReturnsAsync(nodeList);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            var model = Assert.IsType<NodeViewModel>(result?.Model);
            Assert.Equal(nodeList, model.Node);
        }
        
        [Fact]
        public async Task GetPagination_ReturnsJsonResult_WithNodePaginatedList()
        {
            
            var query = new GetNodePaginatedListQuery();
            var nodeLists = new PaginatedResult<NodeListVm>();
            _mockDataProvider.Setup(dp => dp.Node.GetPaginatedNodes(query)).ReturnsAsync(nodeLists);

            
            var result = await _controller.GetPagination(query) as JsonResult;

            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesNode_WhenIdIsEmpty()
        {
            // Arrange
            var nodeViewModel = new AutoFixture.Fixture().Create<NodeViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<CreateNodeCommand>(nodeViewModel)).Returns(new CreateNodeCommand());
            _mockDataProvider.Setup(dp => dp.Node.CreateAsync(It.IsAny<CreateNodeCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Created" });

            // Act
            var result = await _controller.CreateOrUpdate(nodeViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesNode_WhenIdIsNotEmpty()
        {
            // Arrange
            
            var nodeViewModel = new AutoFixture.Fixture().Create<NodeViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id","22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            _mockMapper.Setup(m => m.Map<UpdateNodeCommand>(nodeViewModel)).Returns(new UpdateNodeCommand());
            _mockDataProvider.Setup(dp => dp.Node.UpdateAsync(It.IsAny<UpdateNodeCommand>()))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Updated" });

            // Act
            var result = await _controller.CreateOrUpdate(nodeViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_ReturnsRedirectToActionResult()
        {
            
            var id = "1";
            _mockDataProvider.Setup(dp => dp.Node.DeleteAsync(id))
                .ReturnsAsync(new BaseResponse { Success = true, Message = "Deleted" });

            
            var result = await _controller.Delete(id) as RedirectToActionResult;

            
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsNodeNameExist_ReturnsTrue()
        {
            
            var nodeName = "Node1";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.Node.IsNodeNameExist(nodeName, id))
                .ReturnsAsync(true);

            
            var result = await _controller.IsNodeNameExist(nodeName, id);

            
            Assert.True(result);
        }

        [Fact]
        public async Task GetNodeNames_ReturnsJsonResult_WithNodeNames()
        {
            
            var nodeNames = new List<NodeNameVm>();
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ReturnsAsync(nodeNames);

            var result = await _controller.GetNodeNames() as JsonResult;

            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetByReferenceId_ReturnsJsonResult_WithNode()
        {

            var id = "1";
            var node = new NodeDetailVm();
            _mockDataProvider.Setup(dp => dp.Node.GetByReferenceId(id)).ReturnsAsync(node);


            var result = await _controller.GetByReferenceId(id) as JsonResult;



            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"success\":true", json);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new NodeController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        // ===== LIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task List_ShouldPublishNodePaginatedEvent()
        {
            // Arrange
            var nodeList = new List<NodeListVm>();
            _mockDataProvider.Setup(dp => dp.Node.GetNodeList()).ReturnsAsync(nodeList);

            // Act
            await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<NodePaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task List_ShouldCallDataProvider()
        {
            // Arrange
            var nodeList = new List<NodeListVm>();
            _mockDataProvider.Setup(dp => dp.Node.GetNodeList()).ReturnsAsync(nodeList);

            // Act
            await _controller.List();

            // Assert
            _mockDataProvider.Verify(dp => dp.Node.GetNodeList(), Times.Once);
        }

        // ===== GETPAGINATION METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task GetPagination_ShouldReturnJsonException_WhenExceptionOccurs()
        {
            // Arrange
            var query = new GetNodePaginatedListQuery();
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Node.GetPaginatedNodes(query)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetPagination(query) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        // ===== CREATEORUPDATE METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleValidationException()
        {
            // Arrange
            var nodeViewModel = new AutoFixture.Fixture().Create<NodeViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Name", "Name is required"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateNodeCommand>(nodeViewModel)).Throws(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(nodeViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldHandleGeneralException()
        {
            // Arrange
            var nodeViewModel = new AutoFixture.Fixture().Create<NodeViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var exception = new Exception("Database connection failed");
            _mockMapper.Setup(m => m.Map<CreateNodeCommand>(nodeViewModel)).Throws(exception);

            // Act
            var result = await _controller.CreateOrUpdate(nodeViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        // ===== DELETE METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task Delete_ShouldHandleException()
        {
            // Arrange
            var id = "123";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Node.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        // ===== ISNODENNAMEEXIST METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task IsNodeNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
        {
            // Arrange
            var nodeName = "NonExistentNode";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.Node.IsNodeNameExist(nodeName, id)).ReturnsAsync(false);

            // Act
            var result = await _controller.IsNodeNameExist(nodeName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsNodeNameExist_ShouldReturnFalse_OnException()
        {
            // Arrange
            var nodeName = "NodeName";
            var id = "123";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Node.IsNodeNameExist(nodeName, id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.IsNodeNameExist(nodeName, id);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsNodeNameExist_ShouldCallDataProvider()
        {
            // Arrange
            var nodeName = "NodeName";
            var id = "123";
            _mockDataProvider.Setup(dp => dp.Node.IsNodeNameExist(nodeName, id)).ReturnsAsync(true);

            // Act
            await _controller.IsNodeNameExist(nodeName, id);

            // Assert
            _mockDataProvider.Verify(dp => dp.Node.IsNodeNameExist(nodeName, id), Times.Once);
        }

        // ===== GETNODENAMES METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task GetNodeNames_ShouldHandleException()
        {
            // Arrange
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetNodeNames() as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetNodeNames_ShouldCallDataProvider()
        {
            // Arrange
            var nodeNames = new List<NodeNameVm>();
            _mockDataProvider.Setup(dp => dp.Node.GetNodeNames()).ReturnsAsync(nodeNames);

            // Act
            await _controller.GetNodeNames();

            // Assert
            _mockDataProvider.Verify(dp => dp.Node.GetNodeNames(), Times.Once);
        }

        // ===== GETBYREFERENCEID METHOD ADDITIONAL TESTS =====

        [Fact]
        public async Task GetByReferenceId_ShouldHandleException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Database error");
            _mockDataProvider.Setup(dp => dp.Node.GetByReferenceId(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.GetByReferenceId(id) as JsonResult;

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetByReferenceId_ShouldCallDataProvider()
        {
            // Arrange
            var id = "1";
            var node = new NodeDetailVm();
            _mockDataProvider.Setup(dp => dp.Node.GetByReferenceId(id)).ReturnsAsync(node);

            // Act
            await _controller.GetByReferenceId(id);

            // Assert
            _mockDataProvider.Verify(dp => dp.Node.GetByReferenceId(id), Times.Once);
        }
    }
}
