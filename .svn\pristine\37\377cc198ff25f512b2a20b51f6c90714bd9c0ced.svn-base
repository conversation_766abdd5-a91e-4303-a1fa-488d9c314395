﻿using ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncJob.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Commands
{
    public class CreateRsyncJobTests
    {
        private readonly Mock<IRsyncJobRepository> _mockRsyncJobRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly CreateRsyncJobCommandHandler _handler;

        public CreateRsyncJobTests()
        {
            _mockRsyncJobRepository = new Mock<IRsyncJobRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new CreateRsyncJobCommandHandler(
                _mockRsyncJobRepository.Object,
                _mockMapper.Object,
                _mockPublisher.Object
            );
        }

        [Fact]
        public async Task Handle_CreatesRsyncJob_Successfully()
        {
            var command = new CreateRsyncJobCommand
            {
                ReplicationName = "TestJob",
                SourceDirectory = "/source",
                DestinationDirectory = "/destination"
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ReplicationName = "TestJob"
            };

            var response = new CreateRsyncJobResponse
            {
                Id = rsyncJob.ReferenceId,
                Message = $"RsyncJob '{rsyncJob.ReplicationName}' created successfully."
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.RsyncJob>(command)).Returns(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.AddAsync(rsyncJob)).Returns(ToString);
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(response.Id, result.Id);
            Assert.Contains(rsyncJob.ReplicationName, result.Message);
            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.RsyncJob>(command), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.AddAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenMappingFails()
        {
            var command = new CreateRsyncJobCommand
            {
                ReplicationName = "TestJob"
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.RsyncJob>(command))
                .Throws(new Exception("Mapping error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Mapping error", exception.Message);
            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.RsyncJob>(command), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.RsyncJob>()), Times.Never);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenRepositoryFails()
        {
            var command = new CreateRsyncJobCommand
            {
                ReplicationName = "TestJob"
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReplicationName = "TestJob"
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.RsyncJob>(command)).Returns(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.AddAsync(rsyncJob)).Throws(new Exception("Repository error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Repository error", exception.Message);
            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.RsyncJob>(command), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.AddAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenPublisherFails()
        {
            var command = new CreateRsyncJobCommand
            {
                ReplicationName = "TestJob"
            };

            var rsyncJob = new Domain.Entities.RsyncJob
            {
                ReplicationName = "TestJob"
            };

            _mockMapper.Setup(mapper => mapper.Map<Domain.Entities.RsyncJob>(command)).Returns(rsyncJob);
            _mockRsyncJobRepository.Setup(repo => repo.AddAsync(rsyncJob)).Returns(ToString);
            _mockPublisher.Setup(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()))
                .Throws(new Exception("Publisher error"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
            Assert.Equal("Publisher error", exception.Message);
            _mockMapper.Verify(mapper => mapper.Map<Domain.Entities.RsyncJob>(command), Times.Once);
            _mockRsyncJobRepository.Verify(repo => repo.AddAsync(rsyncJob), Times.Once);
            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<RsyncJobCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
