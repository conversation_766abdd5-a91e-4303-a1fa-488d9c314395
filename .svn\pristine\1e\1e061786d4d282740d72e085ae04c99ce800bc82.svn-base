﻿namespace ContinuityPatrol.Application.Features.Workflow.Commands.CreateBotWorkFlow;

public class CreateBotWorkflowCommandValidator : AbstractValidator<CreateBotWorkflowCommand>
{
    private readonly IWorkflowRepository _workflowRepository;

    public CreateBotWorkflowCommandValidator(IWorkflowRepository workflowRepository)
    {
        _workflowRepository = workflowRepository;

        RuleFor(p => p.WorkflowName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters");

        RuleFor(e => e)
            .MustAsync(WorkflowNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> WorkflowNameUnique(CreateBotWorkflowCommand e, CancellationToken token)
    {
        return !await _workflowRepository.IsWorkflowNameUnique(e.WorkflowName);
    }
}