using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraObjectInfoRepositoryTests : IClassFixture<InfraObjectInfoFixture>, IDisposable
{
    private readonly InfraObjectInfoFixture _infraObjectInfoFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraObjectInfoRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraObjectInfoRepositoryTests(InfraObjectInfoFixture infraObjectInfoFixture)
    {
        _infraObjectInfoFixture = infraObjectInfoFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext(Guid.NewGuid().ToString());
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraObjectInfoRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraObjectInfos.RemoveRange(_dbContext.InfraObjectInfos);
        await _dbContext.SaveChangesAsync();
    }

    #region GetInfraObjectInfoByInfraObjectId Tests

    [Fact]
    public async Task GetInfraObjectInfoByInfraObjectId_ReturnsMatchingRecord_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        var infraObjectInfos = new List<InfraObjectInfo>
        {
            new InfraObjectInfo
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                InfraObjectName = "Matching Infrastructure Object",
                PreviousState = "Offline",
                CurrentState = "Online",
                IsActive = true
            },
            new InfraObjectInfo
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = "INFRA_456", // Different infra object
                InfraObjectName = "Different Infrastructure Object",
                PreviousState = "Online",
                CurrentState = "Offline",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectInfos.AddRangeAsync(infraObjectInfos);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectInfoByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal("Matching Infrastructure Object", result.InfraObjectName);
        Assert.Equal("Offline", result.PreviousState);
        Assert.Equal("Online", result.CurrentState);
    }

    [Fact]
    public async Task GetInfraObjectInfoByInfraObjectId_ReturnsNull_WhenNoMatch()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_456", // Different infra object
            InfraObjectName = "Different Infrastructure Object",
            PreviousState = "Online",
            CurrentState = "Offline",
            IsActive = true
        };

        await _dbContext.InfraObjectInfos.AddAsync(infraObjectInfo);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectInfoByInfraObjectId(infraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetInfraObjectInfoByInfraObjectId_ReturnsFirstMatch_WhenMultipleMatches()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        var infraObjectInfos = new List<InfraObjectInfo>
        {
            new InfraObjectInfo
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                InfraObjectName = "First Match",
                PreviousState = "Offline",
                CurrentState = "Online",
                IsActive = true
            },
            new InfraObjectInfo
            {
                ReferenceId = Guid.NewGuid().ToString(),
                InfraObjectId = infraObjectId,
                InfraObjectName = "Second Match",
                PreviousState = "Online",
                CurrentState = "Offline",
                IsActive = true
            }
        };

        await _dbContext.InfraObjectInfos.AddRangeAsync(infraObjectInfos);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectInfoByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        // Should return the first match found
        Assert.True(result.InfraObjectName == "First Match" || result.InfraObjectName == "Second Match");
    }

    [Fact]
    public async Task GetInfraObjectInfoByInfraObjectId_HandlesEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = "INFRA_123";

        // Act
        var result = await _repository.GetInfraObjectInfoByInfraObjectId(infraObjectId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetInfraObjectInfoByInfraObjectId_HandlesNullInfraObjectId()
    {
        // Arrange
        await ClearDatabase();

        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            IsActive = true
        };

        await _dbContext.InfraObjectInfos.AddAsync(infraObjectInfo);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraObjectInfoByInfraObjectId(null);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Basic Repository Tests

    [Fact]
    public async Task AddAsync_ShouldAddInfraObjectInfo_WhenValidInfraObjectInfo()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            PreviousState = "Offline",
            CurrentState = "Online",
            IsActive = true
        };

        // Act
        var result = await _repository.AddAsync(infraObjectInfo);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectInfo.InfraObjectId, result.InfraObjectId);
        Assert.Equal(infraObjectInfo.InfraObjectName, result.InfraObjectName);
        Assert.Equal(infraObjectInfo.PreviousState, result.PreviousState);
        Assert.Equal(infraObjectInfo.CurrentState, result.CurrentState);
        Assert.Single(_dbContext.InfraObjectInfos);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenInfraObjectInfoIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsInfraObjectInfo_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            PreviousState = "Offline",
            CurrentState = "Online",
            IsActive = true
        };

        await _dbContext.InfraObjectInfos.AddAsync(infraObjectInfo);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(infraObjectInfo.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectInfo.Id, result.Id);
        Assert.Equal(infraObjectInfo.InfraObjectId, result.InfraObjectId);
        Assert.Equal(infraObjectInfo.InfraObjectName, result.InfraObjectName);
        Assert.Equal(infraObjectInfo.PreviousState, result.PreviousState);
        Assert.Equal(infraObjectInfo.CurrentState, result.CurrentState);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsNull_WhenDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateInfraObjectInfo_WhenValidInfraObjectInfo()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Original Infrastructure Object",
            PreviousState = "Offline",
            CurrentState = "Online",
            IsActive = true
        };

        _dbContext.InfraObjectInfos.Add(infraObjectInfo);
        await _dbContext.SaveChangesAsync();

        infraObjectInfo.InfraObjectName = "Updated Infrastructure Object";
        infraObjectInfo.PreviousState = "Online";
        infraObjectInfo.CurrentState = "Offline";

        // Act
        var result = await _repository.UpdateAsync(infraObjectInfo);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Infrastructure Object", result.InfraObjectName);
        Assert.Equal("Online", result.PreviousState);
        Assert.Equal("Offline", result.CurrentState);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenInfraObjectInfoIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveInfraObjectInfo_WhenExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectInfo = new InfraObjectInfo
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            InfraObjectName = "Test Infrastructure Object",
            PreviousState = "Offline",
            CurrentState = "Online",
            IsActive = true
        };

        await _dbContext.InfraObjectInfos.AddAsync(infraObjectInfo);
        await _dbContext.SaveChangesAsync();

        // Act
        await _repository.DeleteAsync(infraObjectInfo);

        // Assert
        var deletedInfo = await _dbContext.InfraObjectInfos.FindAsync(infraObjectInfo.Id);
        Assert.Null(deletedInfo);
    }

    #endregion
}
