﻿namespace ContinuityPatrol.Domain.Entities;

public class MSSQLDBMirroringStatus : AuditableEntity
{
    public string Type { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    public string ConfiguredRPO { get; set; }
    public string DataLagValue { get; set; }
    public string Threshold { get; set; }
}