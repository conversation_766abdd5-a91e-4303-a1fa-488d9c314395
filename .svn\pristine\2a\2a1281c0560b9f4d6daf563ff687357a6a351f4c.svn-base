﻿using ContinuityPatrol.Application.Features.Server.Events.InfraSummaryEvents.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Events
{
    public class ServerInfraSummaryUpdatedEventTests
    {
        private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
        private readonly Mock<ILogger<ServerInfraSummaryUpdatedEventHandler>> _mockLogger;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly ServerInfraSummaryUpdatedEventHandler _handler;

        public ServerInfraSummaryUpdatedEventTests()
        {
            _mockInfraSummaryRepository = new Mock<IInfraSummaryRepository>();
            _mockLogger = new Mock<ILogger<ServerInfraSummaryUpdatedEventHandler>>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _handler = new ServerInfraSummaryUpdatedEventHandler(
                _mockLogger.Object,
                _mockInfraSummaryRepository.Object,
                _mockLoggedInUserService.Object
            );
        }

        [Fact]
        public async Task Handle_InfraSummaryPreviousOSTypeCountIsOne_DeletesAndAddsNewInfraSummary()
        {
            var updatedEvent = new ServerInfraSummaryUpdatedEvent
            {
                PreviousOsType = "Windows",
                PreviousBusinessServiceId = "1",
                CurrentOsType = "Linux",
                CurrentOsTypeId = "2",
                BusinessServiceId = "1",
                CompanyId = "123",
                Logo = "logo.png"
            };

            var previousSummary = new Domain.Entities.InfraSummary
            {
                Type = "Windows",
                Count = 1,
                BusinessServiceId = "1",
                CompanyId = "123"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.PreviousOsType, updatedEvent.PreviousBusinessServiceId, updatedEvent.CompanyId))
                .ReturnsAsync(previousSummary);

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId))
                .ReturnsAsync((Domain.Entities.InfraSummary)null);

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.DeleteAsync(previousSummary), Times.Once);
            _mockInfraSummaryRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.InfraSummary>(s =>
                s.Type == updatedEvent.CurrentOsType &&
                s.TypeId == updatedEvent.CurrentOsTypeId &&
                s.BusinessServiceId == updatedEvent.BusinessServiceId &&
                s.CompanyId == updatedEvent.CompanyId &&
                s.Count == 1 &&
                s.EntityName == "Server" &&
                s.Logo == updatedEvent.Logo
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(
                It.Is<string>(msg => msg.Contains($"InfraSummary '{updatedEvent.CurrentOsType}' updated successfully."))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_InfraSummaryCurrentOSTypeExists_UpdatesCount()
        {
            var updatedEvent = new ServerInfraSummaryUpdatedEvent
            {
                PreviousOsType = "Windows",
                PreviousBusinessServiceId = "1",
                CurrentOsType = "Linux",
                CurrentOsTypeId = "2",
                BusinessServiceId = "1",
                CompanyId = "123",
                Logo = "logo.png"
            };

            var previousSummary = new Domain.Entities.InfraSummary
            {
                Type = "Windows",
                Count = 2,
                BusinessServiceId = "1",
                CompanyId = "123"
            };

            var currentSummary = new Domain.Entities.InfraSummary
            {
                Type = "Linux",
                Count = 1,
                BusinessServiceId = "1",
                CompanyId = "123"
            };

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.PreviousOsType, updatedEvent.PreviousBusinessServiceId, updatedEvent.CompanyId))
                .ReturnsAsync(previousSummary);

            _mockInfraSummaryRepository
                .Setup(repo => repo.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentOsType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId))
                .ReturnsAsync(currentSummary);

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.Is<Domain.Entities.InfraSummary>(s =>
                s.Type == updatedEvent.CurrentOsType &&
                s.Count == 2 &&
                s.BusinessServiceId == updatedEvent.BusinessServiceId &&
                s.CompanyId == updatedEvent.CompanyId &&
                s.Logo == updatedEvent.Logo
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation(
                It.Is<string>(msg => msg.Contains($"InfraSummary '{updatedEvent.CurrentOsType}' updated successfully."))),
                Times.Once);
        }
    }
}
