﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationMaster.Queries;

public class GetReplicationMasterPaginatedListQueryHandlerTests : IClassFixture<ReplicationMasterFixture>
{
    private readonly GetReplicationMasterPaginatedListQueryHandler _handler;

    private readonly Mock<IReplicationMasterRepository> _mockReplicationMasterRepository;

    public GetReplicationMasterPaginatedListQueryHandlerTests(ReplicationMasterFixture replicationMasterFixture)
    {
        var replicationMasterNewFixture = replicationMasterFixture;

        replicationMasterNewFixture.ReplicationMasters[0].Name = "Replication_Master";

        replicationMasterNewFixture.ReplicationMasters[1].Name = "Repli_Infra";



        _mockReplicationMasterRepository = ReplicationMasterRepositoryMocks.GetPaginatedReplicationMasterRepository(replicationMasterNewFixture.ReplicationMasters);

        _handler = new GetReplicationMasterPaginatedListQueryHandler(_mockReplicationMasterRepository.Object, replicationMasterNewFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetReplicationMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Replication_Master" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationMasterListVm>>();

        result.TotalCount.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_PaginatedTeamResources_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetReplicationMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Replication_Master" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationMasterListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ReplicationMasterListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Replication_Master");
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetReplicationMasterPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationMasterListVm>>();

        result.TotalCount.ShouldBe(2);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_TeamResources_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetReplicationMasterPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "name=Replication_Master;" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ReplicationMasterListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].Name.ShouldBe("Replication_Master");
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetReplicationMasterPaginatedListQuery(), CancellationToken.None);

        _mockReplicationMasterRepository.Verify(x => x.PaginatedListAllAsync(), Times.Once);
    }
}