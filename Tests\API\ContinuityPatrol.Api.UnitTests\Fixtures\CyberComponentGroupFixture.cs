using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Create;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Commands.Update;
using ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class CyberComponentGroupFixture
{
    public CreateCyberComponentGroupCommand CreateCyberComponentGroupCommand { get; }
    public UpdateCyberComponentGroupCommand UpdateCyberComponentGroupCommand { get; }
    public DeleteCyberComponentGroupCommand DeleteCyberComponentGroupCommand { get; }
    public CyberComponentGroupListVm CyberComponentGroupListVm { get; }
    public CyberComponentGroupDetailVm CyberComponentGroupDetailVm { get; }

    public CyberComponentGroupFixture()
    {
        var fixture = new Fixture();

        fixture.Customize<CreateCyberComponentGroupCommand>(c => c
            .With(b => b.GroupName, "Critical Infrastructure Group")
            .With(b => b.ComponentProperties, "{\"groupConfiguration\":{\"type\":\"Infrastructure\",\"priority\":\"Critical\",\"components\":[{\"id\":\"comp-001\",\"name\":\"Primary Database Server\",\"type\":\"Database\",\"role\":\"Primary\",\"specifications\":{\"cpu\":\"32 cores\",\"memory\":\"128GB\",\"storage\":\"2TB SSD\"},\"dependencies\":[\"comp-002\",\"comp-003\"]},{\"id\":\"comp-002\",\"name\":\"Application Server Cluster\",\"type\":\"Application\",\"role\":\"Active-Active\",\"specifications\":{\"cpu\":\"16 cores\",\"memory\":\"64GB\",\"storage\":\"500GB SSD\"},\"loadBalancer\":\"enabled\"},{\"id\":\"comp-003\",\"name\":\"Web Server Farm\",\"type\":\"Web\",\"role\":\"Load Balanced\",\"specifications\":{\"cpu\":\"8 cores\",\"memory\":\"32GB\",\"storage\":\"250GB SSD\"},\"instances\":4}],\"networking\":{\"vlan\":\"VLAN-100\",\"subnet\":\"192.168.100.0/24\",\"firewall\":\"enabled\",\"monitoring\":\"24x7\"},\"backup\":{\"frequency\":\"daily\",\"retention\":\"30 days\",\"location\":\"offsite\"},\"monitoring\":{\"healthChecks\":\"enabled\",\"alerting\":\"immediate\",\"dashboard\":\"real-time\"}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Primary Data Center"));

        fixture.Customize<UpdateCyberComponentGroupCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.GroupName, "Updated Critical Infrastructure Group")
            .With(b => b.ComponentProperties, "{\"groupConfiguration\":{\"type\":\"Infrastructure\",\"priority\":\"High\",\"components\":[{\"id\":\"comp-001\",\"name\":\"Updated Primary Database Server\",\"type\":\"Database\",\"role\":\"Primary\",\"specifications\":{\"cpu\":\"64 cores\",\"memory\":\"256GB\",\"storage\":\"4TB NVMe\"},\"dependencies\":[\"comp-002\",\"comp-003\",\"comp-004\"]},{\"id\":\"comp-002\",\"name\":\"Enhanced Application Server Cluster\",\"type\":\"Application\",\"role\":\"Active-Active\",\"specifications\":{\"cpu\":\"32 cores\",\"memory\":\"128GB\",\"storage\":\"1TB NVMe\"},\"loadBalancer\":\"enhanced\"},{\"id\":\"comp-003\",\"name\":\"Scaled Web Server Farm\",\"type\":\"Web\",\"role\":\"Auto-Scaling\",\"specifications\":{\"cpu\":\"16 cores\",\"memory\":\"64GB\",\"storage\":\"500GB NVMe\"},\"instances\":8},{\"id\":\"comp-004\",\"name\":\"Cache Layer\",\"type\":\"Cache\",\"role\":\"Distributed\",\"specifications\":{\"cpu\":\"8 cores\",\"memory\":\"32GB\",\"storage\":\"100GB NVMe\"},\"instances\":3}],\"networking\":{\"vlan\":\"VLAN-100,VLAN-101\",\"subnet\":\"192.168.100.0/24,192.168.101.0/24\",\"firewall\":\"enhanced\",\"monitoring\":\"24x7\",\"redundancy\":\"dual-path\"},\"backup\":{\"frequency\":\"hourly\",\"retention\":\"90 days\",\"location\":\"multi-site\",\"encryption\":\"AES-256\"},\"monitoring\":{\"healthChecks\":\"advanced\",\"alerting\":\"predictive\",\"dashboard\":\"AI-powered\",\"metrics\":\"comprehensive\"}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enhanced Primary Data Center"));

        fixture.Customize<DeleteCyberComponentGroupCommand>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString()));

        fixture.Customize<CyberComponentGroupListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.GroupName, () => $"ComponentGroup-{fixture.Create<string>().Substring(0, 8)}")
            .With(b => b.ComponentProperties, () => $"{{\"groupType\":\"standard\",\"componentCount\":{fixture.Create<int>() % 10 + 1},\"status\":\"active\"}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, () => $"Site-{fixture.Create<string>().Substring(0, 6)}"));

        fixture.Customize<CyberComponentGroupDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString())
            .With(b => b.GroupName, "Enterprise Mission-Critical Component Group")
            .With(b => b.ComponentProperties, "{\"enterpriseGroup\":{\"classification\":\"Mission-Critical\",\"businessImpact\":\"High\",\"rto\":\"1 hour\",\"rpo\":\"15 minutes\",\"components\":[{\"id\":\"ent-db-001\",\"name\":\"Enterprise Database Cluster\",\"type\":\"Database\",\"role\":\"Primary-Secondary\",\"specifications\":{\"cpu\":\"128 cores\",\"memory\":\"1TB\",\"storage\":\"20TB Enterprise SAN\"},\"highAvailability\":{\"clustering\":\"Oracle RAC\",\"replication\":\"synchronous\",\"failover\":\"automatic\"},\"performance\":{\"iops\":\"100000\",\"throughput\":\"10GB/s\",\"latency\":\"<1ms\"}},{\"id\":\"ent-app-001\",\"name\":\"Enterprise Application Tier\",\"type\":\"Application\",\"role\":\"Multi-Tier\",\"specifications\":{\"cpu\":\"256 cores\",\"memory\":\"2TB\",\"storage\":\"10TB NVMe\"},\"scalability\":{\"horizontal\":\"auto-scaling\",\"vertical\":\"dynamic\",\"maxInstances\":20},\"middleware\":{\"appServer\":\"WebLogic\",\"messaging\":\"JMS\",\"caching\":\"Coherence\"}},{\"id\":\"ent-web-001\",\"name\":\"Enterprise Web Gateway\",\"type\":\"Web\",\"role\":\"Load-Balanced\",\"specifications\":{\"cpu\":\"64 cores\",\"memory\":\"512GB\",\"storage\":\"2TB NVMe\"},\"security\":{\"waf\":\"enabled\",\"ssl\":\"TLS 1.3\",\"ddos\":\"protected\"},\"performance\":{\"connections\":\"100000\",\"throughput\":\"50Gbps\"}}],\"infrastructure\":{\"networking\":{\"core\":\"100Gbps\",\"redundancy\":\"N+1\",\"segmentation\":\"micro-segmented\"},\"storage\":{\"primary\":\"All-Flash Array\",\"backup\":\"Tape Library\",\"archive\":\"Cloud Storage\"},\"compute\":{\"virtualization\":\"VMware vSphere\",\"containers\":\"Kubernetes\",\"serverless\":\"enabled\"}},\"operations\":{\"monitoring\":{\"apm\":\"enabled\",\"infrastructure\":\"comprehensive\",\"business\":\"real-time\"},\"automation\":{\"deployment\":\"CI/CD\",\"scaling\":\"auto\",\"healing\":\"self\"},\"compliance\":{\"standards\":[\"SOX\",\"PCI-DSS\",\"GDPR\"],\"auditing\":\"continuous\",\"reporting\":\"automated\"}}}}")
            .With(b => b.SiteId, Guid.NewGuid().ToString())
            .With(b => b.SiteName, "Enterprise Primary Data Center"));

        CreateCyberComponentGroupCommand = fixture.Create<CreateCyberComponentGroupCommand>();
        UpdateCyberComponentGroupCommand = fixture.Create<UpdateCyberComponentGroupCommand>();
        DeleteCyberComponentGroupCommand = fixture.Create<DeleteCyberComponentGroupCommand>();
        CyberComponentGroupListVm = fixture.Create<CyberComponentGroupListVm>();
        CyberComponentGroupDetailVm = fixture.Create<CyberComponentGroupDetailVm>();
    }
}
