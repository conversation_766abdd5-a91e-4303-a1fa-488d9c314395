﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class InfraObjectSchedulerRepository : BaseRepository<InfraObjectScheduler>, IInfraObjectSchedulerRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public InfraObjectSchedulerRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<InfraObjectScheduler>> ListAllAsync()
    {
        var infraObjects = base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraDto = MapInfraObjectScheduler(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraDto.ToListAsync()
            : GetAssignedInfraObjectSheduler(infraDto);
    }
    public override async Task<InfraObjectScheduler> GetByReferenceIdAsync(string id)
    {
        var infraObject = base.GetByReferenceId(id,
            infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                           infraObject.ReferenceId.Equals(id));

        var infraDto = MapInfraObjectScheduler(infraObject);

        return _loggedInUserService.IsAllInfra
            ? await infraDto.FirstOrDefaultAsync()
            : GetBusinessFunctionByReferenceId(infraDto.FirstOrDefault());
    }
    public override async Task<PaginatedResult<InfraObjectScheduler>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<InfraObjectScheduler> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await (_loggedInUserService.IsAllInfra
                ? MapInfraObjectScheduler(Entities.Specify(productFilterSpec).DescOrderById())
                :MapInfraObjectScheduler(GetPaginatedInfraObjectScheduler(Entities.Specify(productFilterSpec).DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize,sortColumn,sortOrder);
        }
        return await (_loggedInUserService.IsAllInfra
            ? MapInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById())
            : MapInfraObjectScheduler(GetPaginatedInfraObjectScheduler(Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .DescOrderById()))).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public override IQueryable<InfraObjectScheduler> GetPaginatedQuery()
    {
        var infraObjects =
            base.QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraDto = MapInfraObjectScheduler(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? infraDto.AsNoTracking().OrderByDescending(x => x.Id)
            : GetPaginatedInfraObjectScheduler(infraDto).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public Task<bool> IsInfraObjectSchedulerNameUnique(string infraObjectName, string afterSwitchOverWorkflowName,
        string beforeSwitchOverWorkflowName)
    {
        var matches = _dbContext.InfraObjectSchedulers.Active().Any(e =>
            e.InfraObjectName.Equals(infraObjectName) &&
            e.AfterSwitchOverWorkflowName.Equals(afterSwitchOverWorkflowName) &&
            e.BeforeSwitchOverWorkflowName.Equals(beforeSwitchOverWorkflowName));

        return Task.FromResult(matches);
    }

    public Task<bool> IsInfraObjectSchedulerNameExist(string infraObjectName, string afterSwitchOverWorkflowName,
        string beforeSwitchOverWorkflowName,string id)
    {
        var matches = _dbContext.InfraObjectSchedulers.Active().Where(e =>
            e.InfraObjectName.Equals(infraObjectName) &&
            e.AfterSwitchOverWorkflowName.Equals(afterSwitchOverWorkflowName) &&
            e.BeforeSwitchOverWorkflowName.Equals(beforeSwitchOverWorkflowName)).ToList().Unique(id);

        return Task.FromResult(matches);
    }

    public async Task<List<InfraObjectScheduler>> GetInfraObjectSchedulerNames()
    {
        var infraObjects = base.QueryAll(x => x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraDto = MapInfraObjectScheduler(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraDto.ToListAsync()
            : GetAssignedInfraObjectSheduler(infraDto).ToList();
    }

    public Task<bool> IsInfraObjectSchedulerNameExist(string infraObjectName, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.InfraObjectSchedulers.Any(e => e.InfraObjectName.Equals(infraObjectName)))
            : Task.FromResult(_dbContext.InfraObjectSchedulers.Where(e => e.InfraObjectName.Equals(infraObjectName))
                .ToList().Unique(id));
    }

    public async Task<List<InfraObjectScheduler>> GetInfraObjectSchedulerByInfraObjectId(string infraObjectId)
    {
        var infraObjects = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId))
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraDto = MapInfraObjectScheduler(infraObjects);

        return _loggedInUserService.IsAllInfra
            ? await infraDto.ToListAsync()
            : await GetPaginatedInfraObjectScheduler(infraDto).ToListAsync();
    }

    public async Task<List<InfraObjectScheduler>> GetInfraObjectSchedulerByWorkflowId(string workflowId)
    {
        var infraObjects = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.AfterSwitchOverWorkflowId.Equals(workflowId)
                    || x.BeforeSwitchOverWorkflowId.Equals(workflowId))
            : base.FilterBy(x => x.AfterSwitchOverWorkflowId.Equals(workflowId)
                    || x.BeforeSwitchOverWorkflowId.Equals(workflowId) && x.CompanyId.Equals(_loggedInUserService.CompanyId));

        var infraDto = MapInfraObjectScheduler(infraObjects);

        return await infraDto.ToListAsync();
    }

    public IQueryable<InfraObjectScheduler> GetPaginatedInfraObjectScheduler(
        IQueryable<InfraObjectScheduler> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(businessService => businessService.AssignedBusinessFunctions)
            .SelectMany(businessFunction => businessFunction.AssignedInfraObjects)
            .Select(infraObject => infraObject.Id);

        return infraObjects.Where(infraObject => assignedInfraObjectIds.Contains(infraObject.InfraObjectId));
    }

    public InfraObjectScheduler GetInfraObjectSchedulerByReferenceId(InfraObjectScheduler infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject.InfraObjectId == assignedInfraObjects.Id)
            .Select(x => infraObject).SingleOrDefault();

        return services;
    }

    public IReadOnlyList<InfraObjectScheduler> GetAssignedInfraObjectSheduler(
        IQueryable<InfraObjectScheduler> infraObjects)
    {
        var infraObjectList = new List<InfraObjectScheduler>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }

    private IQueryable<InfraObjectScheduler> MapInfraObjectScheduler(IQueryable<InfraObjectScheduler> infraObjects)
    {
        var mappedInfra = infraObjects.Select(data => new
        {
            InfraObjectScheduler = data,
            WorkflowAction = _dbContext.WorkflowActionTypes.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.WorkflowTypeId)),
            BeforeWorkflow = _dbContext.Workflows.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BeforeSwitchOverWorkflowId)),
            AfterWorkflow = _dbContext.Workflows.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.AfterSwitchOverWorkflowId)),
            GroupPolicy = _dbContext.GroupPolicies.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.GroupPolicyId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId))
        });

        var mappedInfraQuery = mappedInfra.Select(result => new InfraObjectScheduler
        {
            Id = result.InfraObjectScheduler.Id,
            ReferenceId = result.InfraObjectScheduler.ReferenceId,
            CompanyId = result.InfraObjectScheduler.CompanyId,
            InfraObjectId = result.InfraObject.ReferenceId,
            InfraObjectName = result.InfraObject.Name,
            WorkflowTypeId = result.WorkflowAction.ReferenceId,
            WorkflowType = result.WorkflowAction.ActionType,
            BeforeSwitchOverWorkflowId = result.BeforeWorkflow.ReferenceId,
            BeforeSwitchOverWorkflowName = result.BeforeWorkflow.Name,
            AfterSwitchOverWorkflowId = result.AfterWorkflow.ReferenceId,
            AfterSwitchOverWorkflowName = result.AfterWorkflow.Name,
            ScheduleType = result.InfraObjectScheduler.ScheduleType,
            ScheduleTime = result.InfraObjectScheduler.ScheduleTime,
            NodeId = result.InfraObjectScheduler.NodeId,
            NodeName = result.InfraObjectScheduler.NodeName,
            CronExpression = result.InfraObjectScheduler.CronExpression,
            IsEnable = result.InfraObjectScheduler.IsEnable,
            IsSchedule = result.InfraObjectScheduler.IsSchedule,
            WorkflowVersion = result.InfraObjectScheduler.WorkflowVersion,
            Status = result.InfraObjectScheduler.Status,
            GroupPolicyId = result.GroupPolicy.ReferenceId,
            GroupPolicyName = result.GroupPolicy.GroupName,
            ExecutionPolicy = result.InfraObjectScheduler.ExecutionPolicy,
            State = result.InfraObjectScheduler.State,
            ExceptionMessage=result.InfraObjectScheduler.ExceptionMessage,
            IsActive = result.InfraObjectScheduler.IsActive,
            CreatedBy = result.InfraObjectScheduler.CreatedBy,
            CreatedDate = result.InfraObjectScheduler.CreatedDate,
            LastModifiedBy = result.InfraObjectScheduler.LastModifiedBy,
            LastModifiedDate = result.InfraObjectScheduler.LastModifiedDate,
            LastExecutionTime=result.InfraObjectScheduler.LastExecutionTime,
        });

        return mappedInfraQuery;
    }
}