﻿namespace ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDb2HaDrMonitorStatusByInfraObjectId;

public class
    GetDb2HaDrMonitorStatusByInfraObjectIdQueryHandler : IRequestHandler<GetDb2HaDrMonitorStatusByInfraObjectIdQuery,
        string>
{
    private readonly IDb2HaDrMonitorStatusRepository _d2HaDrMonitorStatusRepository;
    private readonly IMapper _mapper;

    public GetDb2HaDrMonitorStatusByInfraObjectIdQueryHandler(IMapper mapper,
        IDb2HaDrMonitorStatusRepository d2HaDrMonitorStatusRepository)
    {
        _mapper = mapper;
        _d2HaDrMonitorStatusRepository = d2HaDrMonitorStatusRepository;
    }

    public async Task<string> Handle(GetDb2HaDrMonitorStatusByInfraObjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var d2HaDrMonitorStatus =
            await _d2HaDrMonitorStatusRepository.GetDb2HaDrMonitorStatusByInfraObjectId(request.InfraObjectId);

        Guard.Against.NullOrDeactive(d2HaDrMonitorStatus, nameof(Domain.Entities.Db2HaDrMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.Db2HaDrMonitorStatus), request.InfraObjectId));

        var d2HaDrMonitorStatusDetailDto = d2HaDrMonitorStatus.ReferenceId;

        return d2HaDrMonitorStatusDetailDto;
    }
}