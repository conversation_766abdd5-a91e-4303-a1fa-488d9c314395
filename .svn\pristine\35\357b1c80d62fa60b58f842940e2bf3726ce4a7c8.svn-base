using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDynamicSubDashboardService
{
    Task<List<DynamicSubDashboardListVm>> GetDynamicSubDashboardList();
    Task<BaseResponse> CreateAsync(CreateDynamicSubDashboardCommand createDynamicSubDashboardCommand);
    Task<BaseResponse> UpdateAsync(UpdateDynamicSubDashboardCommand updateDynamicSubDashboardCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DynamicSubDashboardDetailVm> GetByReferenceId(string id);
    Task<List<DynamicSubDashboardListVm>> GetByDashboardIdAsync(string dynamicDashboardId);
    #region NameExist
 Task<bool> IsDynamicSubDashboardNameExist(string name, string id);
   #endregion
    #region Paginated
 Task<PaginatedResult<DynamicSubDashboardListVm>> GetPaginatedDynamicSubDashboards(GetDynamicSubDashboardPaginatedListQuery query);
    #endregion
}
