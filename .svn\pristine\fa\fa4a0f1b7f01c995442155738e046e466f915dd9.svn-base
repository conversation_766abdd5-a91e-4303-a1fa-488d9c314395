﻿let globalId = "", globaldeletedid = "", dataTable, selectedValues = [], IsSchedulevalue = "", IsScheduletype = "", strArrayData = '', isNameExits = "Drift/DriftManagement/IsNameExist"

let driftJobUrls = {
    GetPagination: "/Drift/DriftManagement/GetPagination",
    Delete: "Drift/DriftManagement/Delete",
    CheckWindowsService: 'ITAutomation/WorkflowExecution/CheckWindowsService',
    GetSolutionDetails: "Drift/DriftManagement/GetSolutionDetails",
    ResetDrift: "/Drift/DriftManagement/ResetDrift",
    UpdateJobState: "/Drift/DriftManagement/UpdateJobState",
    GetDriftProfileList: "Drift/DriftManagement/GetDriftProfileList",
    CreateOrUpdate:"Drift/DriftManagement/CreateOrUpdate"
}
function validateProfileDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function validateProfileName(value, errorMsg, errorElement, url) {
    if (!value) {
        errorElement.text('Enter job name').addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    var url = RootUrl + url;
    var data = {};
    data.id = null;
    data.name = value
    const validationResults = [
        await SpecialCharValidateCustom(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsSameNameExist(url, data)
    ];
    const failedValidations = validationResults.filter(result => result !== true);
    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0]).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
async function IsSameNameExist(url, inputValue) {
    return !inputValue.name.trim() || $("#driftManagementSave").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}
$(function () {
    let createPermission = $("#DriftCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#DriftDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#driftJobCreateBtn").removeClass('#driftJobCreateBtn').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    dataTable = $('#driftJobTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                }, infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": driftJobUrls.GetPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "properties" : sortIndex === 3 ? "nodename" :
                        sortIndex === 4 ? "scheduleTime" : sortIndex === 5 ? "lastExecutionTime" : sortIndex === 6 ? "status" : ""
                    let orderValue = d.order[0]?.dir || 'asc';
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#driftJobSearchInp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json.success) {
                        json.recordsTotal = json.data.totalPages;
                        json.recordsFiltered = json.data.totalCount;
                        if (json.data.data.length === 0) {
                            $(".pagination-column").addClass("disabled")
                        }
                        else {
                            $(".pagination-column").removeClass("disabled")
                        }
                        update_properties = []
                        update_properties.push(json.data.data)
                        return json.data.data;
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [0, 1, 2, 3, 4, 5],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {

                            var page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    }, "orderable": false,

                },
                {
                    "data": null, "name": "CheckboxAll", "autoWidth": true, "orderable": false,
                    "render": function (data, type, full, meta) {

                        return '<input type="checkbox" name="rowCheckbox" statename="' + data.name + '" class="' + data.name + ' form-check-input custom-cursor-default-hover" title=' + data.id + ' id="' + data.name + '">';

                    }
                },
                {
                    "data": "name", "name": "Job Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": "properties", "name": "Drift Profile", "autoWidth": true,
                    "render": function (data, type, row) {
                        let property = []
                        JSON.parse(data)?.forEach((x, i) => {
                            property.push(x.value)
                        })
                        let properties = property.join(", ")
                        return `<td><span title='${properties}'>${properties}</span></td>`;
                    }
                },
                {
                    "data": "nodeName", "name": "Node Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data == null || data === "" ? "NA" : data}'>${data == null || data === "" ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Scheduled Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data == null ? "NA" : data}'>${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "lastExecutionTime", "name": "LastExecuted Time", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title='${data || "NA"}'>${data || "NA"}</span></td>`;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass = '';
                        if (data.toLowerCase() == "pending") {
                            iconClass = "cp-pending text-warning me-1";
                        } else if (data.toLowerCase() == "running") {
                            iconClass = "text-primary cp-reload cp-animate me-1";
                        } else if (data.toLowerCase() == "success") {
                            iconClass = "cp-success text-success me-1";
                        } else {
                            iconClass = "cp-error text-danger me-1";
                        }
                        return `<td><i class="${iconClass}"></i></td>
                              <td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "data": "state", "name": "State", "autoWidth": true,
                    "render": function (data, type, row) {

                        var iconClass = '';
                        if (data == "Active") {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data === 'InActive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data === null) {
                            iconClass = "cp-active-inactive text-danger me-1";
                        }


                        return `<td><i class="${iconClass}" id="icon" title="${data}" ></i></td>
                              <td><span id="jobmanagestate"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (createPermission == "true" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" data-job='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                                 </span>
                                                    <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" /*data-bs-toggle="modal"*/
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>        
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "true") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                            <i class="cp-edit"></i>
                                        </span>
                                <span role="button" title="Delete" class="delete-button" delete_id="${row.id}" deletename="${row.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                                 <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span>   
                            </div>
                        </td>`;
                        }
                        if (createPermission == "true" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit-button" name="overallupdate" data-job='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                             </span>
                                <span role="button" title="Delete" class="icon-disabled">
                        <i class="cp-Delete"></i>

                    </span>
                     <span  title="Reset" id="reset" class="reset-button" data-job='${JSON.stringify(row)}' role="button" data-bs-toggle="modal"
                                                       >
                                                        <i class="cp-job-reset"></i>                                    
                                                 </span> 
                            </div>
                        </td>`;
                        }
                        if (createPermission == "false" && deletePermission == "false") {
                            return `<td>
                            <div class="d-flex align-items-center  gap-2">
                              <span role="button" title="Edit" class="icon-disabled">
                                <i class="cp-edit"></i>
                            </span>
                                <span role="button" title="Delete" class="icon-disabled">
                        <i class="cp-Delete"></i>
                    </span>
                      <span  title="Reset" id="reset" class="icon-disabled">
                                                        <i class="cp-job-reset"></i>
                                                 </span>
                            </div>
                        </td>`;
                        }
                    },
                    "orderable": false,
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )
})
$(document).on('click', '.delete-button', function () {
    globaldeletedid = $(this).attr("delete_id")
    $("#overall_deleted_id").text($(this).attr("deletename")).attr("title", $(this).attr("deletename"))
})
$("#overall_confirmDeleteButton").on("click", async function () {
    await $.ajax({
        type: "POST",
        url: RootUrl + driftJobUrls.Delete ,
        dataType: "json",
        data: {
            id: globaldeletedid,
            __RequestVerificationToken: gettoken()
        },
        success: function (result) {
            let data = result.data
            if (result.success) {
                $('#DeleteModal').modal('hide');
                $('#driftManagementSave').text("Save");
                notificationAlert("success", data.message)
                dataTable.ajax.reload()
            } else {
                errorNotification(result)
            }
        },
    })
})
$(async function () {
    await DriftJobService()
})
const DriftJobService = async () => {
    await $.ajax({
        type: "POST",
        url: RootUrl + driftJobUrls.CheckWindowsService ,
        data: { type: 'monitor', __RequestVerificationToken: gettoken() },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result && result.success) {
                    let html = DriftJobMessage(result)
                    notificationAlert("success", html, 'execution')
                } else {
                    notificationAlert("warning", response.message);
                }

            } else {
                errorNotification(result)
            }
        }
    })
}
const DriftJobMessage = (result) => {
    let html = ''
    if (result?.activeNodes && Array.isArray(result?.activeNodes) && result?.activeNodes?.length) {
        for (let i = 0; i < result?.activeNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-success" ></i> '${result.activeNodes[i]}'</div>`;
        }
    }
    if (result?.inActiveNodes && Array.isArray(result?.activeNodes) && result?.inActiveNodes?.length) {
        for (let i = 0; i < result?.inActiveNodes?.length; i++) {
            html += `<div class='mb-1'><i class="cp-network fs-5 text-danger" ></i> '${result.inActiveNodes[i]}'</div>`;
        }
    }
    return html;
}
getSolutionDetails()
function getSolutionDetails() {
    $.ajax({
        type: "GET",
        url: RootUrl + driftJobUrls.GetSolutionDetails ,
        dataType: "json",
        success: function (result) {
            $("#selectSolutionType").empty().append("<option></option>")
            if (result.data.length != 0) {
                result.data?.forEach((datavalue) => {
                    $("#selectSolutionType").append("<option value=" + datavalue.id + " >" + datavalue.componentName + "</option>")
                })
            }
        },
    })
}
function driftmanagementdebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}
$('#driftJobSearchInp').on('keydown input', driftmanagementdebounce(function (e) {
    if (e.key === '=' || e.key === 'Enter') {
        e.preventDefault();
        return false;
    }
    const profilename = $("#Jobname");
    const profileCheckbox = $("#driftprofile");
    const statusCheckbox = $("#status");
    const inputValue = $('#driftJobSearchInp').val();
    if (profileCheckbox.is(':checked')) {
        selectedValues.push(profileCheckbox.val() + inputValue);
    }
    if (statusCheckbox.is(':checked')) {
        selectedValues.push(statusCheckbox.val() + inputValue);
    }
    if (profilename.is(':checked')) {
        selectedValues.push(profilename.val() + inputValue);
    }
    var currentPage = dataTable.page.info().page + 1;
    if (!isNaN(currentPage)) {
        dataTable.ajax.reload(function (json) {
            if (e.target.value && json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        }, false)
    }
}, 500))

$('#driftJobTable').on('click', '#reset', function () {
    var jobData = $(this).data('job'); 
    jobData.__RequestVerificationToken = gettoken()
    $.ajax({
        url: driftJobUrls.ResetDrift,
        type: 'POST',
        data: jobData,
        success: function (result) {
            if (result.success) {
                notificationAlert("success", result?.data.message);
                setTimeout(() => {
                    dataTable.ajax.reload();
                }, 2000)
            } else {
                 errorNotification(result)
            }
        }
    });
});


$('#driftJobActiveBtn').on('click', function () {  
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "Active") {
            datas.push({
                "id": obj.title,
                "name": obj.getAttribute('statename')
            })
        }
    })
    if (datas.length) {     
        $.ajax({
            url: driftJobUrls.UpdateJobState ,
            type: 'PUT',
            data: {
                "State":"Active",
                "UpdateDriftJobState": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Jobs state has already updated to 'Active' state ")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
$('#driftJobInactiveBtn').on('click', function () {
    let checkFind = document.querySelectorAll('input[name="rowCheckbox"]')
    let datas = []
    checkFind?.forEach((obj, idx) => {
        if (obj.checked && obj.id != "InActive") {
            datas.push({
                "id": obj.title,
                "name": obj.getAttribute('statename')
            })
        }
    })
    if (datas.length) {
        $.ajax({
            url: driftJobUrls.UpdateJobState ,
            type: 'PUT',
            data: {
                "State": "InActive",
                "UpdateDriftJobState": datas,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result.success) {
                    var data = result?.data
                    $('input[name="rowCheckbox"]').prop("checked", false)
                    $('input[name="checkboxAll"]').prop("checked", false)
                    notificationAlert("success", data.message)
                    setTimeout(() => {
                        location.reload();
                    }, 2000)
                } else {
                    errorNotification(result)
                }
            },
        });
    } else {
        if ($('input[name="rowCheckbox"]').prop("checked")) {
            notificationAlert("warning", "Jobs state has already updated to 'InActive' state ")
            setTimeout(() => {
                location.reload();
            }, 2000)
        }
    }
})
$("#flexCheckDefault").on('change', function (e) {
    setTimeout(() => {
        if (e.target.checked) {
            $('input[name="rowCheckbox"]').prop("checked", true);
        } else {
            $('input[name="rowCheckbox"]').prop("checked", false)
        }
    }, 100)
})
$("#tblJobManagement").on('change', 'input[name="rowCheckbox"]', function (e) {
    $('input[name="checkboxAll"]').prop("checked", false)
})

$('#driftJobTable').on('click', '.edit-button', function () {
    var jobData = $(this).data("job");
    populateModalFields(jobData);
    Tab_selection(jobData);
    Tab_schedule_type(jobData);
    $('#driftManagementSave').text("Update");
    ClearJobErrorElements();
    $('#driftJobCreateModal').modal('show');
});
function populateModalFields(data) {  
    globalId = data.id
    $("#driftJobProfileName").val(data.name)
    let Arr = [];
    JSON.parse(data?.properties)?.forEach((d) => {
        Arr.push(d.Id)
    })
    $("#selectSolutionType").val(data.solutionTypeId).trigger("change")
    setTimeout(() => {
        $('#DriftJobProfile').val(Arr).trigger('change')
    }, 1000)
    var scheduleTime = data.scheduleTime.split(" ")
    if (data.state == "Active") {
        $("#textStateActive").prop("checked", true);
    }
    else {
        $("#textStateInactive").prop("checked", true);
    }
    setTimeout(() => {
        if (scheduleTime.length == 3) {
            $("#txtMins").val(scheduleTime[1])
        }
        if (data.scheduleTime.includes("Every day") == true) {
            $("#defaultCheck-everyday").prop("checked", true)
            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
        }
        if (data.scheduleTime.includes("MON-FRI") == true) {
            $("#defaultCheck-MON-FRI").prop("checked", true)
            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
        }
        if (scheduleTime.length == 7) {
            $("#txtMinutes").val(scheduleTime[5])
            $("#txtHours").val(scheduleTime[1])
        }
        
        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
            if (data.scheduleTime.includes("MON") == true) {
                $("#defaultCheck-1").prop("checked", true)
            }
            if (data.scheduleTime.includes("TUE") == true) {
                $("#defaultCheck-2").prop("checked", true)
            }
            if (data.scheduleTime.includes("WED") == true) {
                $("#defaultCheck-3").prop("checked", true)
            }
            if (data.scheduleTime.includes("THU") == true) {
                $("#defaultCheck-4").prop("checked", true)
            }
            if (data.scheduleTime.includes("FRI") == true) {
                $("#defaultCheck-5").prop("checked", true)
            }
            if (data.scheduleTime.includes("SAT") == true) {
                $("#defaultCheck-6").prop("checked", true)
            }
            if (data.scheduleTime.includes("SUN") == true) {
                $("#defaultCheck-0").prop("checked", true)
            }
            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
        }
        if (scheduleTime.length >= 12) {
            let year = parseInt(scheduleTime[12])
            let month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
            if (month <= 9 && month > 0) {
                month = "0" + month;
            }
            else if (month == 0) {
                month = "12";
                year = year - 1;
            }
            var newdate = year + "-" + month;
            $("#lblMonth").val(newdate).trigger("change")
            scheduleTime[5]?.split(",")?.forEach(function (i) {
                if (i) {
                    $("#inlineCheckbox" + i).prop("checked", true)
                } else {
                    $("#inlineCheckbox" + i).prop("checked", false)
                }
            })
            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
        }
    }, 500)
}
$(".driftJobProfileCancel").on("click", function () {
    jobOnce()
    ClearCroneElements()
    $("#driftJobProfileName,#DriftJobProfile").val("")
    $("#driftJobProfileNameError,#DriftJobProfileError").text('').removeClass('field-validation-error');
    $("#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab").removeClass("active");
    $("#nav-Hourly,#nav-Daily,#nav-Weekly,#nav-Monthly").removeClass("show active");
    $("#nav-Minutes").addClass("show active");
    $("#nav-Minutes-tab").addClass("active");
})
$("#driftJobCreateBtn").on("click", async function () {
    $("#textStateActive").prop("checked", true);
    jobOnce()
    $('#SaveFunction').text("Save");
    clearJobFields();
    profileData()
})
async function profileData() {
    $("#DriftJobProfile").empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + driftJobUrls.GetDriftProfileList ,
        dataType: "json",
        success: function (result) {
            let data = result.data
            if (result.success) {
                data?.forEach((x, i) => {
                    $("#DriftJobProfile").append(`<option value=""></option>`)
                    $("#DriftJobProfile").append(`<option value="${x.id}">${x.name}</option>`)
                })
            } else {
                errorNotification(result)
            }
        },
    })
}
profileData()
const clearJobFields = () => {
    $('#driftManagementSave').text("Save");
$("#driftJobProfileName,#textNodeId,#textNodeName,#textCronExpression,#textStatus,#textIsSchedule,#textScheduleType").val("")
    $("#DriftJobProfile,#selectSolutionType").val("").trigger("change")
    $("#textstateinactive").prop("checked", false)
    ClearJobErrorElements();
    ClearCroneElements();
}
function ClearJobErrorElements() {
    $("#driftJobProfileNameError,#SolutionType-error,#DriftJobProfileError,#CronMin-error,#CronHourly-error,#CroneveryHour-error,#ExecutionPolicy-error, #CronddlMin-error,#CronddlHour-error,#CronDay-error,#CronExpression-error,#Crondaysevery-error,#InfraObjectName-error,#MonthlyHours-error,#CronMon-error,#CronMonthly-error,#CronExpression-error").text('').removeClass('field-validation-error');
}
$('#driftJobProfileName').on('input', driftmanagementdebounce(async function () {
    let value = await sanitizeInput($('#driftJobProfileName').val());
    $("#driftJobProfileName").val(value);
    await validateProfileName(value, "Enter job name", $("#driftJobProfileNameError"), isNameExits);
}, 400));
$('#DriftJobProfile').on('change',  function () {
    validateProfileDropDown($(this).val(), "Select drift profile", $("#DriftJobProfileError"));
    let selectName = $(this).find('option:selected');
    Arraydata = []
    selectName.each(function () {
        let option = $(this);
        let val = option.text();
        let id = option.val()
        let obj = {
            Id: id,
            value: val
        };
        Arraydata.push(obj)
    });
    strArrayData = JSON.stringify(Arraydata)
});
$("#selectSolutionType").on("change", async function () {
    await validateProfileDropDown($(this).val(), "Select solution type", $("#SolutionType-error"));
})
$('#txtMins').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#txtMins').val('');
    }
    if ($(this).val() == 0 || $(this).val() > 59) {
        $('#txtMins').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
});
$('#txtHours').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 23) {
        event.preventDefault();
        $('#txtHours').val('');
    }
    if ($(this).val() == 0) {
        $('#txtHours').val("")
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
});

$('#txtMinutes').on('input keypress', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key) || $(this).val() > 59) {
        event.preventDefault();
        $('#txtMinutes').val('');
    }
    if ($(this).val().length >= 2) {
        event.preventDefault()
    }
    validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
});
$("#txtMinutes,#txtHours").on("input", function () {
    if ($("#txtMinutes").val() == "00" && $("#txtHours").val() == "00" || $("#txtMinutes").val() == "0" && $("#txtHours").val() == "0") {
        $("#txtMinutes").val("")
        setTimeout(() => {
            $('#CronHourMin-error').text("Enter the proper hours and minites")
        }, 200)
    }
})
$('#everyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#everyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CroneveryHour-error'));
});
$('#everyMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#everyMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
});
$('#ddlHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        $('#ddlHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
});

$('#ddlMinutes').on('click', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69) {
        event.preventDefault();
        $('#ddlMinutes').val('');
    }
    validateMinJobNumber($(this).val(), "Select hours", $('#CronddlMin-error'));
});
$('#MonthlyHours').on('input', function (event) {
    if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
        event.preventDefault();
        $('#MonthlyHours').val('');
    }
    validateHourJobNumber($(this).val(), "Select start time", $('#MonthlyHours-error'));
});
function srvTime() {
    try {
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (eerr3) {
                alert("AJAX not supported");
            }
        }
    }
    xmlHttp.open('HEAD', window.location.href.toString(), false);
    xmlHttp.setRequestHeader("Content-Type", "text/html");
    xmlHttp.send('');
    return xmlHttp.getResponseHeader("Date");
}
$('.datetimeCron').on('change', function () {
    validateDayNumber($(this).val(), "Select schedule time", $('#CronExpression-error'));
    let selectdate = new Date($(this).val()), currentdate = new Date(srvTime())
    if (selectdate > currentdate) {
        $('#CronExpression-error').text('').removeClass('field-validation-error');
        return true;
    } else if (selectdate < currentdate) {
        $('#CronExpression-error').text("Select schedule date and time should be greater than current date and time").addClass('field-validation-error');
        return false;
    }
});
$('#lblMonth').on("change", function () {
    $('input[name="Monthyday"]').prop("checked", false)
    validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
    let selectedDate = new Date($(this).val()), currentDate = new Date()
    const getDays = (year, month) => {
        return new Date(year, month, 0).getDate();
    };
    const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
    for (let i = 0; i < daysInmonth; i++) {
        let data = ""
        data = i + 1
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
        $(".checklabel").each(function () {
            let checkboxValue = parseInt($(this).text());
            if (checkboxValue > data) {
                $(this).css("display", "none")
            } else {
                $(this).css("display", "block")
            }
        })
    }
    if ($(this).val() == "") {
        $('input[name="Monthyday"]').prop('disabled', true);
        $('input[name="Monthyday"]').prop('checked', false);
    } else {
        $('input[name="Monthyday"]').each(function () {
            let checkboxValue = parseInt($(this).val());
            if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                $(this).prop('disabled', true);
            } else {
                $(this).prop('disabled', false);
            }
        })
    }
});
$('input[name=weekDays]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(Dayvalue, "Select day(s)", $('#CronDay-error'));
});
$('input[name=Monthyday]').on('click', function () {
    let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
});
$('.nav-link').on("click", function () {
    ClearCroneElements();
});
$('input[name = "switchPlan"]').on('click', function () {
    ClearCroneElements();
});
$('input[name=daysevery]').on('click', function () {
    ValidateCronRadioButton($('#Crondaysevery-error'));
});
$('#textStateActive').on('click', function () {
    const errorElement = $('#state-error');
    ValidateRadioButton(errorElement);
});
$('#textStateInactive').on('click', function () {
    const errorElement = $('#state-error');
    ValidateRadioButton(errorElement);
});

function ValidateRadioButton(errorElement) {
    if ($('input[name=state]:checked').length > 0) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
    else {
        errorElement.text("Select state").addClass('field-validation-error');;
        return false;
    }
}
function jobOnce() {
    Drready_SM2 = document.getElementById("switchMonthly");
    Drready_SM2.checked = true;
    let elementToHide11 = document.getElementById("monthgroup");
    elementToHide11.style.display = "block";
    let elementToHide22 = document.getElementById("yeargroup");
    elementToHide22.style.display = "none";
}
$(".nav-link,#nav-Minutes-tab,#nav-Hourly-tab,#nav-Daily-tab,#nav-Weekly-tab,#nav-Monthly-tab,#Next_profile_data").on("click", function () {
    $("#CronMin-error,#CronHourly-error,#CronHourMin-error,#Crondaysevery-error,#CroneveryHour-error,#CronDay-error,#CronddlHour-error, #CronMonthly-error,#CronMon-error,#MonthlyHours-error,#CronExpression-error").text('').removeClass('field-validation-error');
})
$("#nav-Monthly-tab").on("click", function () {
    if ($("#driftManagementSave").text() == "Save") {
        $('input[name=Monthyday]').attr('disabled', 'disabled');
    }
})
function ClearCroneElements() {
    $("#txtMins,#txtHours,#txtMinutes,#ddlHours,#everyHours,#lblMonth,#MonthlyHours,#datetimeCron").val('');
    $("input[name=weekDays],input[name=daysevery],input[name=Monthyday],input[name=Monthyday]").prop("checked", false);
}
$("#driftManagementSave").on("click", async function () {
    let form = $("#driftJobCreateModal")
    GetIsSchedule();
    Get_Drift_ScheduleTypes();
    errorElement = $('#state-error');
    var isStateActive = ValidateRadioButton(errorElement);

    const profile_name = await validateProfileName($('#driftJobProfileName').val(), "Enter job name", $("#driftJobProfileNameError"), isNameExits);
    const drift_profile = validateProfileDropDown($('#DriftJobProfile').val(), "Select drift profile", $("#DriftJobProfileError"));
    const drift_solutiontype = validateProfileDropDown($('#selectSolutionType').val(), "Select solution type", $("#SolutionType-error"));
    let isScheduler = CronValidation();
    var { CronExpression, listcron } = JobCronExpression();
    
    if (profile_name && drift_profile && drift_solutiontype && isScheduler && isStateActive) {
        form.trigger("submit")
        let data = {
            "Name": $('#driftJobProfileName').val(),
            "Properties": strArrayData,
            "SolutionTypeId": $('#selectSolutionType option:selected').val(),
            "SolutionTypeName": $('#selectSolutionType option:selected').text(),
            "NodeId": "",
            "State": $("input[name='state']:checked").val(),
            "NodeName": "",
            "Status": "Pending",
            "CronExpression": CronExpression,
            "IsSchedule": IsSchedulevalue,
            "ScheduleType": IsScheduletype,
            "ScheduleTime": listcron,
            "ExceptionMessage": "",
            __RequestVerificationToken: gettoken()
        }
        $('#driftManagementSave').text() === "Update" ? data["id"] = globalId : null
        await $.ajax({
            type: "POST",
            url: RootUrl + driftJobUrls.CreateOrUpdate ,
            dataType: "json",
            data: data,
            success: function (result) {
                let data = result.data
                if (result.success) {
                    $('#driftJobCreateModal').modal('hide');
                    notificationAlert("success", data.message)
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
        clearJobFields()
    }
})
function CronValidation() {
    var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
    var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
    var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
    var monthlymonth = $('#lblMonth').val();
    var Minutes = $('#txtMins').val();
    var txtHours = $('#txtHours').val();
    var txtHourMinutes = $('#txtMinutes').val();
    var everyHours = $('#everyHours').val();
    var datetime = $('#datetimeCron').val();
    var MonthlyHours = $('#MonthlyHours').val();
    var isScheduler = '';

    if (document.getElementById('switchMonthly').checked == true) {

        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();
        switch (Scheduler_types) {
            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;
            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;
            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day(s)", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                isSchedulerDay = validateDayNumber(txtmonthday, "Select date", $('#CronMon-error'));
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
    }
    else {
        isScheduler = validateDayNumber(datetime, "Select schedule time", $('#CronExpression-error')) && validateprevNumber(datetime, "", $('#CronExpression-error'));
    }
    return isScheduler;
}
function validateJobDropDown(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}
function GetIsSchedule() {
    var schedule_type = document.querySelector('input[name = "switchPlan"]:checked');
    if (schedule_type.value === "Once") {
        IsSchedulevalue = 1
        $('#textIsSchedule').val(1);
    } else {
        IsSchedulevalue = 2
        IsSchedule = $('#textIsSchedule').val(2);
    }
}
function Get_Drift_ScheduleTypes() {
    var Scheduler_types = $('.nav-tabs .active').text().trim();
    switch (Scheduler_types) {
        case "Minutes":
            IsScheduletype = 1
            $('#textScheduleType').val(1);
            break;
        case "Hourly":
            IsScheduletype = 2
            $('#textScheduleType').val(2);
            break;
        case "Daily":
            IsScheduletype = 3
            $('#textScheduleType').val(3);
            break;
        case "Weekly":
            IsScheduletype = 4
            $('#textScheduleType').val(4);
            break;
        case "Monthly":
            IsScheduletype = 5
            $('#textScheduleType').val(5);
            break;
    }
}

let monthInput = document.getElementById("lblMonth");
let today = new Date();
let currentYear = today.getFullYear();
let currentMonth = today.getMonth() + 1;
let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
monthInput.setAttribute("min", minMonth);
monthInput.setAttribute("max", maxMonth)

const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');
const minformattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
const maxformattedDate = `${year + 77}-${month}-${day}T${hours}:${minutes}`;
const datetimeInput = document.getElementById('datetimeCron');
datetimeInput.min = minformattedDate;
datetimeInput.max = maxformattedDate;

