namespace ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail
{
    public class GetAdPasswordExpireDetailsQueryHandler : IRequestHandler<GetAdPasswordExpireDetailQuery, AdPasswordExpireDetailVm>
    {
        private readonly IAdPasswordExpireRepository _adPasswordExpireRepository;
        private readonly IMapper _mapper;

        public GetAdPasswordExpireDetailsQueryHandler(IMapper mapper, IAdPasswordExpireRepository adPasswordExpireRepository)
        {
            _mapper = mapper;
            _adPasswordExpireRepository = adPasswordExpireRepository;
        }

        public async Task<AdPasswordExpireDetailVm> Handle(GetAdPasswordExpireDetailQuery request, CancellationToken cancellationToken)
        {
            var adPasswordExpire = await _adPasswordExpireRepository.GetByReferenceIdAsync(request.Id);

            Guard.Against.NullOrDeactive(adPasswordExpire, nameof(Domain.Entities.AdPasswordExpire), new NotFoundException(nameof(Domain.Entities.AdPasswordExpire), request.Id));

            var adPasswordExpireDetailDto = _mapper.Map<AdPasswordExpireDetailVm>(adPasswordExpire);

            return adPasswordExpireDetailDto;
        }
    }
}
