﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;

public class GetPostgresMonitorLogsPaginatedListQueryHandler : IRequestHandler<GetPostgresMonitorLogsPaginatedListQuery,
    PaginatedResult<PostgresMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorLogsRepository _postgresMonitorLogsRepository;

    public GetPostgresMonitorLogsPaginatedListQueryHandler(IPostgresMonitorLogsRepository postgresMonitorLogsRepository,
        IMapper mapper)
    {
        _postgresMonitorLogsRepository = postgresMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<PostgresMonitorLogsListVm>> Handle(
        GetPostgresMonitorLogsPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _postgresMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new PostgresMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<PostgresMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}