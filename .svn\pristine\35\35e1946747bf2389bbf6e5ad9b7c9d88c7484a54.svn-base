﻿using ContinuityPatrol.Application.Features.DashboardView.Event.ServiceAvailabilityView;
using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Helper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Report.ReportTemplate;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;
using System.Globalization;

namespace ContinuityPatrol.Web.Areas.Dashboard.Controllers;

[Area("Dashboard")]
public class ServiceAvailabilityController : BaseController
{
    private readonly IDataProvider _provider;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ILogger<ServiceAvailabilityController> _logger;
    private readonly IPublisher _publisher;

    public static string CompanyLogo { get; set; }

    public ServiceAvailabilityController(IDataProvider provider, IMapper mapper,IPublisher publisher
        , ILogger<ServiceAvailabilityController> logger, ILoggedInUserService loggedInUserService)
    {
        _provider = provider;
        _mapper = mapper;
        _logger = logger;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }
   
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new ServiceAvailabilityEvent());

        _logger.LogDebug("Entering List method in service availability");
        
        var businessView = await _provider.DashboardView.GetBusinessViews();
        //var businessServiceAvailability = await _provider.BusinessServiceAvailability.GetBusinessServiceAvailabilityList();
        var dashboardView = new DashboardBusinessViewModel
        {
            GetPaginatedBusinessViews = businessView,
            //BusinessServiceAvailabilityList = businessServiceAvailability,
        };

        return View(dashboardView);
    }
  
    [AntiXss]
    public async Task<IActionResult> BusinessServiceOverview()
    {
        _logger.LogDebug("Entering BusinessServiceOverview method in service availability page");
        try
        {
            var businessView = await _provider.DashboardView.GetBusinessViews();

            var dashboardView = new DashboardBusinessViewModel
            {
                GetPaginatedBusinessViews = businessView,
            };
            _logger.LogDebug("Successfully retrieved business service overview list in service availability page.");
           
            return Json(new { success = true, data = dashboardView });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving the business service overview list", ex);

            return ex.GetJsonException();
        }
    }


    public async Task<IActionResult> GetImpactDetailByHeatMapStatusType(string businessServiceId, string heatMapType,bool isAffected)
    {
        _logger.LogDebug("Entering GetImpactDetailByHeatMapStatusType method in service availability page.");

        if (string.IsNullOrWhiteSpace(heatMapType) && string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "BusinessServiceId is not valid format", ErrorCode = 0 });
        }

        try
        {
            var downDetails = await _provider.HeatMapStatus.GetHeatMapStatusByType(businessServiceId, heatMapType, isAffected);
            
            _logger.LogDebug($"Successfully retrieved impact detail by heatMap status type in service availability page.");

            return Json(downDetails);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving the impact detail by heatmap status type.",ex);

            return Json("");
        }
    }

    public async Task<IActionResult> GetBusinessServiceTreeViewListByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetBusinessServiceTreeViewListByBusinessServiceId  method in service availability page.");

        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "BusinessServiceId is not valid format", ErrorCode = 0 });
        }
        try
        {
            var treeView = await _provider.BusinessService.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId);

            _logger.LogDebug($"Successfully retrieved business service tree view list by business service id:{businessServiceId} in service availability page.");
            return Json(treeView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving the business service tree view list by business service id.", ex);

            return ex.GetJsonException();
        }

    }




    [HttpGet]
    public async Task<ActionResult> BusinessServiceOverviewReport(string selectedValues)
    {
        _logger.LogDebug("Entering BusinessServiceOverviewReport method in service availability page.");

        var value = selectedValues.Split("+");
        var status = value[0];
        var priority = value[1];
        var reportsDirectory = "";
        XtraReport report = null;
        var fileName = "";


        status = status.ToLower() switch { "all" => "", _=> status };
        priority = priority.ToLower() switch
        {
            "high" => "1",
            "medium" => "2",
            "low" => "3",
            "all" => "",
            _ => priority  // Keep the original value if not high, medium, or low
        };

        try
        {
           var reportGeneratedName = _loggedInUserService.LoginName;
            var businessView = await _provider.DashboardView.GetBusinessViews();
            var dashboardView = new DashboardBusinessViewModel { GetPaginatedBusinessViews = businessView };

            var getValue = dashboardView.GetPaginatedBusinessViews;

            if(!string.IsNullOrEmpty(priority)) { getValue = getValue.Where(x => x.Priority.ToString().ToLower() == priority).ToList(); }
            if(!string.IsNullOrEmpty(status)) {  getValue = getValue.Where(x => x.Status.ToString().ToLower().Replace(" ","") == status).ToList(); }

            string outputValue = JsonConvert.SerializeObject(getValue);

            CompanyLogo = string.Empty;
            var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo; }
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
           
            if (value[2] == "List")
            {
                 report = new BusinessServiceOverviewReport(outputValue, reportGeneratedName);
                 fileName = "BusinessServiceOverviewReport_" + filenameSuffix + ".pdf";
            }
            if (value[2] == "Card") 
            {
                report = new BusinessServiceCardviewReport(outputValue, reportGeneratedName);
                fileName = "BusinessServiceCardviewReport_" + filenameSuffix + ".pdf";
            }            
            
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);

            if (report != null) report.ExportToPdf(reportsDirectory);
            byte[] fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);

            _logger.LogDebug($"Successfully generated business service overview report in service availability page");

            return File(fileBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while generating the business overview report.", ex);
            return StatusCode(500, new { message = "An error occurred while generating the dashboard Business Service report." });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }



    }

    // RTOByBusinessService
    public async Task<IActionResult> GetRtoByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetRTOByBusinessServiceId method in service availability page.");

        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "RTOByBusinessService is not valid format", ErrorCode = 0 });
        }

        try
        {
            var treeView = await _provider.DashboardView.GetRTOByBusinessServiceId(businessServiceId);

            _logger.LogDebug($"Successfully retrieved RTO by business service id {businessServiceId} in service availability page.");

            return Json(treeView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving RTO by business service id.", ex);

            return Json("");
        }

    }

    public async Task<IActionResult> ImpactAvailabilityByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering ImpactAvailabilityByBusinessServiceId method in service availability page.");

        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "RTOByBusinessService is not valid format", ErrorCode = 0 });
        }

        try
        {
            var treeView = await _provider.DashboardView.GetImpactAvailabilityByBusinessServiceId(businessServiceId);

            _logger.LogDebug($"Successfully retrieved impact availability by business service id {businessServiceId} in service availability page.");

            return Json(treeView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving the impact availability by business service id.", ex);

            return Json("");
        }
    }


    public async Task<IActionResult> DataLagByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering DataLagByBusinessServiceId method in service availability page.");

        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "RTOByBusinessService is not valid format", ErrorCode = 0 });
        }

        try
        {
            var treeView = await _provider.DashboardView.GetDataLagByBusinessServiceId(businessServiceId);

            _logger.LogDebug($"Successfully retrieved datalag detail by business service id {businessServiceId} in service availability page.");

            return Json(treeView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving datalag detail by business service id.", ex);

            return Json("");
        }
    }

    public async Task<IActionResult> GetSitePropertiesByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetSitePropertiesByBusinessServiceId method in service availability page.");

        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json(new { Success = false, Message = "RTOByBusinessService is not valid format", ErrorCode = 0 });
        }

        try
        {
            var treeView = await _provider.DashboardView.GetSitePropertiesByBusinessServiceId(businessServiceId);

            _logger.LogDebug($"Successfully retrieved site properties by business service id {businessServiceId} in service availability page.");

            return Json(treeView);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving site properties by business service id.", ex);

            return Json(ex.GetJsonException());
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetImpactDetailCount(string? businessServiceId)
    {
        try
        {
            var impactDetail = await _provider.HeatMapStatus.GetImpactDetail(businessServiceId);
            return Json(new { Success = true, data = impactDetail });

        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on DCMapping page while processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    public async Task<ActionResult> DownloadResiliencyReadinessReport(List<string> selectedValues)
    {
        _logger.LogDebug("Entering DownloadResiliencyReadinessReport method in service availability page.");

        var reportsDirectory = "";
        var selectedValuess = selectedValues[0]?.Split(",");

        try
        {
            var reportGeneratedName = _loggedInUserService.LoginName;
            var drStatusList = await _provider.DrReadyStatus.GetBusinessServiceDrReady("");
            if (selectedValuess != null && selectedValuess[0] != "all") { drStatusList = drStatusList.Where(x => x.BusinessServiceId == selectedValuess[0]).ToList(); }
            var isReady = selectedValuess != null && selectedValuess.Any(value => string.Equals(value, "ready", StringComparison.OrdinalIgnoreCase));
            var isNotReady = selectedValuess != null && selectedValuess.Any(value => string.Equals(value, "not ready", StringComparison.OrdinalIgnoreCase));
            var isParReady = selectedValuess != null && selectedValuess.Any(value => string.Equals(value, "partial ready", StringComparison.OrdinalIgnoreCase));
            if (selectedValuess is { Length: > 1 })
            {
                if (isReady) { drStatusList = drStatusList.Where(x => x.DRReady == "1" && x.DrReadyListVm.IsDataLagExceed == false).ToList(); }
                if (isNotReady) { drStatusList = drStatusList.Where(x => x.DRReady == "0" && x.DrReadyListVm.DrReadyWorkflowExecutions.Count == 0).ToList(); }
                if (isParReady) { drStatusList = drStatusList.Where(x => (x.DRReady == "0" && x.DrReadyListVm.DrReadyWorkflowExecutions.Count > 0) || (x.DrReadyListVm.IsDataLagExceed && x.DRReady == "1")).ToList(); }
            }
            var reportValue = JsonConvert.SerializeObject(drStatusList);
            CompanyLogo = string.Empty;
            var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo; }
            XtraReport report = new ResiliencyReadinessReport(reportValue, reportGeneratedName);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            var fileName = "ResiliencyReadinessReport_" + filenameSuffix + ".pdf";
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);

            report.ExportToPdf(reportsDirectory);
            var fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);

            _logger.LogDebug("download initaiated for resiliency readiness report service availability page");

            return File(fileBytes, "application/pdf", fileName);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while downloading resiliency readiness report.", ex);

            return StatusCode(500, new { message = "An error occurred while generating the Resiliency Readiness report." });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }



    }

    public async Task<IActionResult> GetDrDrillDetailsByBusinessServiceId(string businessServiceId,string isReport)
    {
        _logger.LogDebug("Entering GetDrDrillDetailsByBusinessServiceId method in service availability page.");


        try
        {

                if (string.IsNullOrEmpty(businessServiceId))
                {
                    var drStatusList = await _provider.WorkflowOperation.GetDrDrillDetailsByBusinessServiceId(businessServiceId);

                    _logger.LogDebug($"Successfully retrieved dr drill details by business service id {businessServiceId} in service availability page.");

                    return Json(new { Success = true, data = drStatusList });
                }
                else
                {
                    var drStatusList = await _provider.WorkflowOperation.GetDrDrillDetailsByBusinessServiceId(businessServiceId);
                    _logger.LogDebug($"Successfully retrieved dr drill details by business service id {businessServiceId} in service availability page.");

                    return Json(new { Success = true, data = drStatusList });
                }
            
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while downloading operational functions availability report.", ex);

        
            return ex.GetJsonException();
        }

    }

    //New Executed DR Drill
    public async Task<IActionResult> GetWorkflowOperation(string workflowOperationId)
    {
        _logger.LogDebug("Entering GetWorkflowOperation method in service availability page.");

        var reportsDirectory = "";
        try
        {
            CompanyLogo = string.Empty;
            var companyDetails = await _provider.Company.GetCompanyById(WebHelper.UserSession.CompanyId);
            if (!string.IsNullOrEmpty(companyDetails.CompanyLogo) && companyDetails.CompanyLogo != "NA") { CompanyLogo = companyDetails.CompanyLogo; }
            var drillReport = await _provider.Report.GetDrDrillReportByWorkflowOperationId(workflowOperationId, "",false);
            var outputValue = JsonConvert.SerializeObject(drillReport);
            XtraReport report = new ExecutedDRDrillReport(outputValue);
            var filenameSuffix = DateTime.Now.ToString("MMddyyyyhhmmsstt");
            var fileName = "ExecutedDRDrillReport_" + filenameSuffix + ".pdf";
            reportsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Report", fileName);
            report.ExportToPdf(reportsDirectory);
            var fileBytes = System.IO.File.ReadAllBytes(reportsDirectory);

            _logger.LogDebug("download initaiated for workflow operation report in service availability page");

            await _publisher.Publish(new ReportViewedEvent { ActivityType = ActivityType.Generate.ToString(), ReportName = "ExecutedDRDrillReport" });
            return File(fileBytes, "application/pdf", fileName);

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while downloading workflow operation report.", ex);

            return Json(new { success = false, message = ex.GetMessage() });
        }
        finally
        {
            if (System.IO.File.Exists(reportsDirectory))
            {
                System.IO.File.Delete(reportsDirectory);
            }
        }
    }
    //End

    public async Task<IActionResult> GetDynamicDashboardList()
    {
        _logger.LogDebug("Entering GetDynamicDashboardList method in service availability page.");
        try
        {
            var dynamicDashboardList = await _provider.DynamicDashboard.GetDynamicDashboardList();
            var dynamicDashboardMap = _mapper.Map<List<DynamicDashboardViewModel>>(dynamicDashboardList);

            var dynamicDashboardMapList = (await _provider.DynamicDashboardMap.GetDynamicDashboardMapList()).ToList();
            var allDynamicSubDashboards = await _provider.DynamicSubDashboard.GetDynamicSubDashboardList(); 

            var dashboardMapLookup = dynamicDashboardMapList.GroupBy(x => x.DashBoardSubId)
                                                             .ToDictionary(g => g.Key, g => g.ToList());

            foreach (var dynamicDashboard in dynamicDashboardMap)
            {
                var dynamicSubDashboards = allDynamicSubDashboards.Where(sub => sub.DynamicDashBoardId == dynamicDashboard.Id).ToList();

                foreach (var subDashboard in dynamicSubDashboards)
                {
             
                    if (dashboardMapLookup.TryGetValue(subDashboard.Id, out var relatedMaps) && relatedMaps.Count > 0)
                    {
                        dynamicDashboard.DynamicSubDashboardListVms.Add(subDashboard);
                    }
                }
            }

            _logger.LogDebug($"Successfully retrieved dynamic dashboard list in service availability page.");

            return Json(new { Success = true, Message = dynamicDashboardMap });

            // var dynamicDashboardList = await _provider.DynamicDashboard.GetDynamicDashboardList();

            // var dynamicDashboardMap = _mapper.Map<List<DynamicDashboardViewModel>>(dynamicDashboardList);

            // var dynamicDashboardMapList = (await _provider.DynamicDashboardMap.GetDynamicDashboardMapList()).ToList();

            //foreach ( var dynamicDashboard in dynamicDashboardMap)
            //{
            //     var dynamicSubDashBoard = await _provider.DynamicSubDashboard.GetByDashboardIdAsync(dynamicDashboard.Id);

            //     foreach (var subDashboard in dynamicSubDashBoard)
            //     {
            //         var dynamicDashboardMapBySubDashBoard = dynamicDashboardMapList.Where(x=>x.DashBoardSubId.Equals(subDashboard.Id)).ToList();

            //         if (dynamicDashboardMapBySubDashBoard.Count > 0)
            //         {
            //             dynamicDashboard.DynamicSubDashboardListVms.Add(subDashboard);
            //         }
            //     }
            //}
            // _logger.LogDebug($"Successfully retrieved dynamic dashboard list in service availability page.");


            // return Json(new { Success = true, Message = dynamicDashboardMap });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving dynamic dashboard list.", ex);

            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    public async Task<IActionResult> GetCustomDashboardNames()
    {
        _logger.LogDebug("Entering GetCustomDashboardNames method in service availability page.");
        try
        {
            var dynamicDashboardList = await _provider.DynamicDashboard.GetDynamicDashboardList();

            var isCustumDashboard = dynamicDashboardList.FirstOrDefault(x => x.IsDelete);

            _logger.LogDebug("Successfully retrieved custom dashboard names in service availability page.");
            return Json(new { Success = true, Message = isCustumDashboard });

        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving custom dashboard names.", ex);
            return Json(ex.GetJsonException());
        }

    }

    public async Task<IActionResult> GetDynamicSubDashboardList()
    {
        _logger.LogDebug("Entering GetDynamicSubDashboardList method in service availability page.");
        try
        {
            var dynamicSubDashboardList = await _provider.DynamicSubDashboard.GetDynamicSubDashboardList();

            _logger.LogDebug("Successfully retrieved dynamic sub dashboard list in service availability page.");

            return Json(new { Success = true, Message = dynamicSubDashboardList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on service availability page while retrieving dynamic sub dashboard list.", ex);
            
            return Json(new { Success = false, Message = "Data not found." });
        }
    }


    public async Task<IActionResult> DrillOverviewService(string businessServiceId)
    {
        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json("");
        }

        try
        {
            var profileExecuted = await _provider.WorkflowOperation.GetProfileExecutorByBusinessServiceId(businessServiceId);
          
            var dashboardView = new DashboardBusinessViewModel
            {
                ProfileExecute = profileExecuted

            };
            ViewBag.LastDate = profileExecuted.LastModifiedDate.ToString(CultureInfo.InvariantCulture);
            return Json(new { success = true, data = dashboardView });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred while processing the request. {ex.Message}");

            return Json("");
        }
    }



}