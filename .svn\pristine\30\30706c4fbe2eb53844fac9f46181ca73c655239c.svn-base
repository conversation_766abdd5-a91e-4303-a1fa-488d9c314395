﻿namespace ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetByType;

public class GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQueryHandler : IRequestHandler<
    GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQuery, List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IMsSqlNativeLogShippingMonitorStatusRepository _msSqlNativeLogShippingMonitorStatusRepository;

    public GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQueryHandler(IMapper mapper,
        IMsSqlNativeLogShippingMonitorStatusRepository msSqlNativeLogShippingMonitorStatusRepository)
    {
        _mapper = mapper;
        _msSqlNativeLogShippingMonitorStatusRepository = msSqlNativeLogShippingMonitorStatusRepository;
    }

    public async Task<List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>> Handle(
        GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQuery request, CancellationToken cancellationToken)
    {
        var msSqlNativeLogShippingMonitorStatus =
            await _msSqlNativeLogShippingMonitorStatusRepository.GetDetailByType(request.Type);

        return msSqlNativeLogShippingMonitorStatus.Count <= 0
            ? new List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>()
            : _mapper.Map<List<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>>(msSqlNativeLogShippingMonitorStatus);
    }
}