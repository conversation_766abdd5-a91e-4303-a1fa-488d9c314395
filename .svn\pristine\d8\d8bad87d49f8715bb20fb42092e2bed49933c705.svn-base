﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CredentialProfileRepository : BaseRepository<CredentialProfile>, ICredentialProfileRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CredentialProfileRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;

        _loggedInUserService = loggedInUserService;
    }

    public override Task<IReadOnlyList<CredentialProfile>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilterAsync(credentialProfile => credentialProfile.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public Task<List<CredentialProfile>> GetType(string type)
    {
        var matches = _dbContext.CredentialProfiles.Where(x => x.CredentialType.Equals(type) && x.IsActive).ToList();

        return Task.FromResult(matches);
    }

    public override Task<CredentialProfile> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(credentialProfile =>
                credentialProfile.ReferenceId.Equals(id) &&
                credentialProfile.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }

    public Task<List<CredentialProfile>> GetCredentialProfileNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.CredentialProfiles.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new CredentialProfile { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();
        return _dbContext.CredentialProfiles
            .Active()
            .Select(x => new CredentialProfile { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public override async Task<PaginatedResult<CredentialProfile>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CredentialProfile> specification, string sortColumn, string sortOrder)
    {
        return _loggedInUserService.IsParent
            ?await  Entities.Specify(specification).DescOrderById().ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder)
            :await Entities.Specify(specification).Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId)).DescOrderById().ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public override IQueryable<CredentialProfile> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }


    public Task<bool> IsCredentialProfileNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.CredentialProfiles.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.CredentialProfiles.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsCredentialProfileNameUnique(string name)
    {
        var matches = _dbContext.CredentialProfiles.Any(e => e.Name.Equals(name));
        return Task.FromResult(matches);
    }

    public async Task<PaginatedResult<CredentialProfile>> GetCredentialProfileByType(int pageNumber,int pageSize,Specification<CredentialProfile> specification, string type,string sortColumn,string sortOrder)
    {
        var credProfileTypeList = await Entities.Specify(specification).Where(x => x.CredentialType.Equals(type)).DescOrderById().ToSortedPaginatedListAsync(pageNumber,pageSize,sortColumn,sortOrder);

        return credProfileTypeList;
    }

    public IQueryable<CredentialProfile> GetCredentialProfileByType(string type)
    {
        var credProfileTypeList = _dbContext.CredentialProfiles.Active().Where(x => x.CredentialType.Equals(type))
            .OrderByDescending(x => x.Id);

        return credProfileTypeList;
    }

    public override Task<CredentialProfile> GetByIdAsync(int id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByIdAsync(id)
            : Task.FromResult(FindByFilterAsync(credentialProfile =>
                    credentialProfile.Id.Equals(id) &&
                    credentialProfile.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Result.SingleOrDefault());
    }
}