using ContinuityPatrol.Application.Features.BulkImportActionResult.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportActionResult.Events;

public class DeleteBulkImportActionResultEventTests : IClassFixture<BulkImportActionResultFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BulkImportActionResultFixture _bulkImportActionResultFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly BulkImportActionResultDeletedEventHandler _handler;

    public DeleteBulkImportActionResultEventTests(BulkImportActionResultFixture bulkImportActionResultFixture, UserActivityFixture userActivityFixture)
    {
        _bulkImportActionResultFixture = bulkImportActionResultFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Tester");
        mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/bulkimportactionresult");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        var mockBulkImportActionResultEventLogger = new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>();

        _mockUserActivityRepository = BulkImportActionResultRepositoryMocks.CreateBulkImportActionResultEventRepository(_userActivityFixture.UserActivities);

        _handler = new BulkImportActionResultDeletedEventHandler(
            mockLoggedInUserService.Object, 
            mockBulkImportActionResultEventLogger.Object, 
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteBulkImportActionResultEventCreated()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };

        // Act
        var result = _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };

        // Act
        await _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_WithCorrectProperties()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.LoginName.ShouldBe("Tester");
        capturedUserActivity.Action.ShouldContain("Delete BulkImportActionResult");
        capturedUserActivity.Entity.ShouldBe("BulkImportActionResult");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldContain("TestEntity");
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_LogCorrectInformation_When_BulkImportActionResultDeleted()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        var mockLogger = new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>();

        var handler = new BulkImportActionResultDeletedEventHandler(
            new Mock<ILoggedInUserService>().Object,
            mockLogger.Object,
            _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        //mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //        It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("TestEntity") && v.ToString().Contains("deleted successfully")),
        //        It.IsAny<Exception>(),
        //        It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserActivityProperties_When_UserServiceHasData()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var testUrl = "/api/test";
        var testIpAddress = "***********";
        var testLoginName = "TestUser";

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);
        mockLoggedInUserService.Setup(x => x.LoginName).Returns(testLoginName);
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testUrl);
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testIpAddress);

        var handler = new BulkImportActionResultDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.UserId.ShouldBe(testUserId);
        capturedUserActivity.LoginName.ShouldBe(testLoginName);
        capturedUserActivity.RequestUrl.ShouldBe(testUrl);
        capturedUserActivity.HostAddress.ShouldBe(testIpAddress);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_EventHandled()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "ProductionEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.Action.ShouldBe("Delete BulkImportActionResult");
        capturedUserActivity.Entity.ShouldBe("BulkImportActionResult");
        capturedUserActivity.ActivityType.ShouldBe("Delete");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportActionResult 'ProductionEntity' deleted successfully.");
    }

    [Fact]
    public async Task Handle_NotSetCreatedByAndLastModifiedBy_When_DeleteEvent()
    {
        // Arrange
        var testUserId = Guid.NewGuid().ToString();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns(testUserId);

        var handler = new BulkImportActionResultDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        // Delete events don't set CreatedBy and LastModifiedBy unlike Create events
        capturedUserActivity.CreatedBy.ShouldBeNull();
        capturedUserActivity.LastModifiedBy.ShouldBeNull();
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_EventProcessed()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = null };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("deleted successfully");
    }

    [Fact]
    public async Task Handle_SetCorrectRequestUrl_When_UserServiceProvided()
    {
        // Arrange
        var testRequestUrl = "/api/bulkimportactionresult/delete";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(testRequestUrl);

        var handler = new BulkImportActionResultDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.RequestUrl.ShouldBe(testRequestUrl);
    }

    [Fact]
    public async Task Handle_SetCorrectHostAddress_When_UserServiceProvided()
    {
        // Arrange
        var testHostAddress = "********";
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns(testHostAddress);

        var handler = new BulkImportActionResultDeletedEventHandler(
            mockLoggedInUserService.Object,
            new Mock<ILogger<BulkImportActionResultDeletedEventHandler>>().Object,
            _mockUserActivityRepository.Object);

        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "TestEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.HostAddress.ShouldBe(testHostAddress);
    }

    [Fact]
    public async Task Handle_UseEntityNameAsEventName_When_BulkImportActionResultDeleted()
    {
        // Arrange
        var bulkImportActionResultDeletedEvent = new BulkImportActionResultDeletedEvent { Name = "ServerEntity" };
        Domain.Entities.UserActivity capturedUserActivity = null;

        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua)
            .ReturnsAsync((Domain.Entities.UserActivity ua) => ua);

        // Act
        await _handler.Handle(bulkImportActionResultDeletedEvent, CancellationToken.None);

        // Assert
        capturedUserActivity.ShouldNotBeNull();
        capturedUserActivity.ActivityDetails.ShouldContain("ServerEntity");
        capturedUserActivity.ActivityDetails.ShouldBe("BulkImportActionResult 'ServerEntity' deleted successfully.");
    }
}
