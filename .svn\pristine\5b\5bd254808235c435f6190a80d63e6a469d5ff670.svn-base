﻿using ContinuityPatrol.Application.Features.WorkflowAction.Events.ImportWorkflowAction;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Commands.Import;

public class
    ImportWorkflowActionCommandHandler : IRequestHandler<ImportWorkflowActionCommand, ImportWorkflowActionResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISolutionHistoryRepository _solutionRepository;
    private readonly IVersionManager _versionManager;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public ImportWorkflowActionCommandHandler(IMapper mapper, IWorkflowActionRepository workflowActionRepository, IPublisher publisher, IVersionManager versionManager,
        ISolutionHistoryRepository solutionRepository, ILoggedInUserService loggedInUserService)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
        _publisher = publisher;
        _versionManager = versionManager;
        _solutionRepository = solutionRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<ImportWorkflowActionResponse> Handle(ImportWorkflowActionCommand request, CancellationToken cancellationToken)
    {
        var actionIds = request.ImportWorkflowActionListCommands
            .Where(x=>x.Id.IsNotNullOrWhiteSpace())
            .Select(x => x.Id)
            .ToList();

        var eventToUpdates = await _workflowActionRepository.GetWorkflowActionsByIds(actionIds);

        var eventToUpdatesDict = eventToUpdates.ToDictionary(y => y.ReferenceId);

        var versionDict = (await _versionManager.GetUpdateVersionList(
                eventToUpdates
                    .Where(x => x.Version.IsNotNullOrWhiteSpace())
                    .Select(x => x.Version)
                    .Distinct()
                    .ToList()
            ))
            .Zip(eventToUpdates.Select(x => x.Version).Distinct(), (newVer, oldVer) => new { oldVer, newVer })
            .ToDictionary(x => x.oldVer, x => x.newVer);


        var updateActions = request.ImportWorkflowActionListCommands
            .Where(x => eventToUpdatesDict.ContainsKey(x.Id)) // Check existence
            .Select(x =>
            {
                var matched = eventToUpdatesDict[x.Id];

                var f = _mapper.Map<Domain.Entities.WorkflowAction>(x);

                f.ReferenceId = x.Id;
                f.Id = matched.Id;
                f.Version = versionDict.TryGetValue(x.Version, out var newVer) ? newVer : x.Version;
                f.IsLock = matched.IsLock;
                f.CreatedBy = matched.CreatedBy;
                f.CreatedDate = matched.CreatedDate;
                f.LastModifiedBy = matched.LastModifiedBy;
                f.LastModifiedDate = matched.LastModifiedDate;

                return f;
            })
            .ToList();

        if(updateActions.Any())
         await _workflowActionRepository.UpdateRangeAsync(updateActions);

        var newActions = request.ImportWorkflowActionListCommands
            .Where(x => !eventToUpdates.Select(y => y.ReferenceId).Contains(x.Id))
            .Select(x =>
            {
                var f = _mapper.Map<Domain.Entities.WorkflowAction>(x);
                f.ReferenceId = x.Id;
                return f;
            })
            .ToList();

        if(newActions.Any())
            await _workflowActionRepository.AddRangeAsync(newActions);

        var workflowActions = updateActions.Concat(newActions);

        var solutionHistories = workflowActions.Select(x => new Domain.Entities.SolutionHistory
        {
            LoginName = _loggedInUserService.LoginName,
            CompanyId = _loggedInUserService.CompanyId,
            NodeId = x.NodeId,
            ActionId = x.ReferenceId,
            ActionName = x.ActionName,
            Version = x.Version,
            Properties = x.Properties,
            UpdaterId = _loggedInUserService.UserId
        }).ToList();

        await _solutionRepository.AddRangeAsync(solutionHistories);

        var response = new ImportWorkflowActionResponse
        {
            Message = "Workflow Action imported successfully."
        };

        await _publisher.Publish(new ImportWorkflowActionEvent(), cancellationToken);

        return response;


        //var actions = await _workflowActionRepository.ListAllAsync();

        //if (request.ImportWorkflowActionListCommands == null)
        //    throw new NotFoundException(nameof(Domain.Entities.WorkflowAction),
        //        request.ImportWorkflowActionListCommands);



        //foreach (var item in request.ImportWorkflowActionListCommands)
        //    if (actions.Any(e => e.ReferenceId.Equals(item.Id)))
        //    {
        //        var test = actions.Where(x => x.ReferenceId.Equals(item.Id));

        //        var eventToUpdate = await _workflowActionRepository.GetByReferenceIdAsync(item.Id);

        //        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.WorkflowAction), item.Id);

        //        var newVersion = await _versionManager.GetUpdateVersion(eventToUpdate.Version);

        //        eventToUpdate.Version = newVersion;

        //        var update = _mapper.Map(item, eventToUpdate, typeof(ImportWorkflowActionListCommand),
        //            typeof(Domain.Entities.WorkflowAction));

        //        await _workflowActionRepository.UpdateAsync((Domain.Entities.WorkflowAction)update);

        //        var solutionHistory = new Domain.Entities.SolutionHistory
        //        {
        //            LoginName = _loggedInUserService.LoginName,
        //            CompanyId = _loggedInUserService.CompanyId,
        //            NodeId = eventToUpdate.NodeId,
        //            ActionId = eventToUpdate.ReferenceId,
        //            ActionName = eventToUpdate.ActionName,
        //            Version = eventToUpdate.Version,
        //            Properties = eventToUpdate.Properties,
        //            UpdaterId = _loggedInUserService.UserId
        //        };
        //        await _solutionRepository.AddAsync(solutionHistory);
        //    }
        //    else
        //    {
        //        var workflowAction = _mapper.Map<Domain.Entities.WorkflowAction>(item);

        //        workflowAction.ReferenceId = item.Id;

        //        var version = await _versionManager.GetVersion(workflowAction.Version);

        //        workflowAction.Version = version;

        //        workflowAction = await _workflowActionRepository.AddAsync(workflowAction);

        //        var solutionHistory = new Domain.Entities.SolutionHistory
        //        {
        //            LoginName = _loggedInUserService.LoginName,
        //            CompanyId = _loggedInUserService.CompanyId,
        //            NodeId = workflowAction.NodeId,
        //            ActionId = workflowAction.ReferenceId,
        //            ActionName = workflowAction.ActionName,
        //            Version = workflowAction.Version,
        //            Properties = workflowAction.Properties,
        //            UpdaterId = _loggedInUserService.UserId
        //        };
        //        await _solutionRepository.AddAsync(solutionHistory);
        //    }


    }
}