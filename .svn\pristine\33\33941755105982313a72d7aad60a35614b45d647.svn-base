﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Events;

public class UpdateApprovalMatrixEventTests : IClassFixture<ApprovalMatrixFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly ApprovalMatrixUpdatedEventHandler _handler;

    public UpdateApprovalMatrixEventTests(ApprovalMatrixFixture approvalMatrixFixture, UserActivityFixture userActivityFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;
        _userActivityFixture = userActivityFixture;
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var mockAccessManagerEventLogger = new Mock<ILogger<ApprovalMatrixUpdatedEventHandler>>();
        _mockUserActivityRepository = ApprovalMatrixRepositoryMocks.CreateApprovalMatrixEventRepository(_approvalMatrixFixture.UserActivities);
        _handler = new ApprovalMatrixUpdatedEventHandler(mockLoggedInUserService.Object,mockAccessManagerEventLogger.Object,_mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateApprovalMatrixEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_approvalMatrixFixture.ApprovalMatrixUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_approvalMatrixFixture.ApprovalMatrixUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}