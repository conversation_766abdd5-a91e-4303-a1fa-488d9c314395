﻿using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

public partial class CyberSnapReport : DevExpress.XtraReports.UI.XtraReport
{
    private static readonly ILogger<CyberSnapReport> _logger;
    public  GetCyberSnapsReportVm getCyberSnapsReportVms = new GetCyberSnapsReportVm();
    public CyberSnapReport(string data)
    {
        try
        {                
            getCyberSnapsReportVms = JsonConvert.DeserializeObject<GetCyberSnapsReportVm>(data);                 
            var reportDetails = getCyberSnapsReportVms.CyberSnapsReportVm;
            //Initaialize the cyber snap report
            InitializeComponent();

            //Client company logo
            ClientCompanyLogo();

            _cyberSnapName.Text = getCyberSnapsReportVms.CyberSnapReportDataName;

            //Before print serial number
            _serialNumber.BeforePrint += _serialNumberBeforePrint;

            //Data Source assigned
            this.DetailReport.DataSource = reportDetails;
        }
        catch(Exception ex)
        {
            _logger.LogError($"An error occured while processing the cyber snap report." + ex.Message.ToString());
            throw;
        }
    }

    private int serialNumber = 1;

    private void _serialNumberBeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = serialNumber.ToString();
        serialNumber++;
    }
    private void _userNameBeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            _userName.Text = "Report Generated By : " + getCyberSnapsReportVms.ReportGeneratedBy.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured while displaying the username : " + ex.Message.ToString());
            throw; 
        }
    }
    private void _versionBeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            _cpVersion.Text = "Continuity Patrol Version " + version + " |  © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex)
        { 
            _logger.LogError($"An error occured while displaying thecyber snap report CP Version : " + ex.Message.ToString());
            throw;
        }
    }

    [SupportedOSPlatform("windows")]
    private static Image LoadImageFromFile(MemoryStream path)
    {
        return Image.FromStream(path);
    }

    public void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    if (OperatingSystem.IsWindows())
                    {
                        prClientLogo.Image = LoadImageFromFile(ms);
                    }
                    else
                    {
                        throw new PlatformNotSupportedException("Image loading only works on Windows in this context.");
                    }
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occured while processing the customer logo in cyber snap report" + ex.Message.ToString());
            throw;
        }
    }
}
