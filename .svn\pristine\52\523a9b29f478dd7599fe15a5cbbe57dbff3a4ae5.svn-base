﻿namespace ContinuityPatrol.Application.Features.ImpactAvailability.Commands.Create;

public class CreateImpactAvailabilityCommand : IRequest<CreateImpactAvailabilityResponse>
{
    public string TotalServiceCount { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string ServiceUp { get; set; }
    public string ServiceDown { get; set; }
    public string TotalBusinessFunctionCount { get; set; }
    public string BusinessFunctionUp { get; set; }
    public string BusinessFunctionDown { get; set; }
    public string TotalInfraObjectCount { get; set; }
    public string InfraObjectUp { get; set; }
    public string InfraObjectDown { get; set; }
    public string MajorServiceImpact { get; set; }
    public string MinorServiceImpact { get; set; }
    public string TotalBusinessFunctionImpact { get; set; }
    public string BusinessFunctionAvailable { get; set; }
    public string TotalInfraObjectImpact { get; set; }
    public string InfraObjectAvailable { get; set; }
    public string InfraPartial { get; set; }
    public string InfraMajor { get; set; }

    public override string ToString()
    {
        return $"BusinessService Name: {BusinessServiceName};";
    }
}