using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AdPasswordExpireFixture
{
    public List<AdPasswordExpireListVm> AdPasswordExpireListVm { get; }
    public AdPasswordExpireDetailVm AdPasswordExpireDetailVm { get; }
    public CreateAdPasswordExpireCommand CreateAdPasswordExpireCommand { get; }
    public UpdateAdPasswordExpireCommand UpdateAdPasswordExpireCommand { get; }

    public AdPasswordExpireFixture()
    {
        var fixture = new Fixture();

        // Create sample AdPasswordExpire list data
        AdPasswordExpireListVm = new List<AdPasswordExpireListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC01.contoso.com",
                UserName = "john.doe",
                Email = "<EMAIL>",
                ServerList = "Server01,Server02,Server03",
                NotificationDays = "7,14,30",
                IsPassword = true
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC02.contoso.com",
                UserName = "jane.smith",
                Email = "<EMAIL>",
                ServerList = "Server04,Server05",
                NotificationDays = "3,7,14",
                IsPassword = false
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                DomainServerId = Guid.NewGuid().ToString(),
                DomainServer = "DC03.contoso.com",
                UserName = "admin.user",
                Email = "<EMAIL>",
                ServerList = "Server01,Server02,Server03,Server04,Server05",
                NotificationDays = "1,3,7,14,30",
                IsPassword = true
            }
        };

        // Create detailed AdPasswordExpire data
        AdPasswordExpireDetailVm = new AdPasswordExpireDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC01.contoso.com",
            UserName = "test.user",
            Email = "<EMAIL>",
            ServerList = "Server01,Server02,Server03",
            NotificationDays = "7,14,30",
            IsPassword = true
        };

        // Create command for creating AdPasswordExpire
        CreateAdPasswordExpireCommand = new CreateAdPasswordExpireCommand
        {
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC01.contoso.com",
            UserName = "new.user",
            Email = "<EMAIL>",
            ServerList = "Server01,Server02",
            NotificationDays = "7,14",
            IsPassword = true
        };

        // Create command for updating AdPasswordExpire
        UpdateAdPasswordExpireCommand = new UpdateAdPasswordExpireCommand
        {
            Id = Guid.NewGuid().ToString(),
            DomainServerId = Guid.NewGuid().ToString(),
            DomainServer = "DC02.contoso.com",
            UserName = "updated.user",
            Email = "<EMAIL>",
            ServerList = "Server03,Server04,Server05",
            NotificationDays = "3,7,14,30",
            IsPassword = false
        };
    }
}
