﻿using ContinuityPatrol.Application.Features.SingleSignOn.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Events
{
    public class SingleSignOnPaginatedEventTests
    {
        private readonly Mock<ILogger<SingleSignOnPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly SingleSignOnPaginatedEventHandler _handler;

        public SingleSignOnPaginatedEventTests()
        {
            _mockLogger = new Mock<ILogger<SingleSignOnPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();
            _handler = new SingleSignOnPaginatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogInformationAndAddUserActivity_WhenEventIsHandled()
        {
            var paginatedEvent = new SingleSignOnPaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(service => service.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://localhost");
            _mockUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());

            await _handler.Handle(paginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == _mockUserService.Object.UserId &&
                activity.LoginName == "TestUser" &&
                activity.RequestUrl == "http://localhost" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.CompanyId == _mockUserService.Object.CompanyId &&
                activity.Action == $"{ActivityType.View} {Modules.SingleSignOn}" &&
                activity.Entity == Modules.SingleSignOn.ToString() &&
                activity.ActivityType == ActivityType.View.ToString() &&
                activity.ActivityDetails == "Single Sign-On viewed"
            )), Times.Once);

            _mockLogger.Verify(logger =>
                logger.LogInformation("Single Sign-On viewed"),
                Times.Once
            );
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenUserActivityRepositoryFails()
        {
            var paginatedEvent = new SingleSignOnPaginatedEvent();
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(service => service.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://localhost");
            _mockUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(service => service.CompanyId).Returns(Guid.NewGuid().ToString());

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ThrowsAsync(new Exception("Database error"));

            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(paginatedEvent, cancellationToken));

            _mockLogger.Verify(logger =>
                logger.LogInformation("Single Sign-On viewed"),
                Times.Never
            );
        }
    }
}
