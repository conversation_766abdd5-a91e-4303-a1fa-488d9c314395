﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ISiteTypeService
{
    Task<BaseResponse> CreateAsync(CreateSiteTypeCommand createSiteTypeCommand);
    Task<BaseResponse> UpdateAsync(UpdateSiteTypeCommand updateSiteTypeCommand);
    Task<BaseResponse> DeleteAsync(string id, string name);
    Task<List<SiteTypeListVm>> GetSiteTypeList();
    Task<bool> IsSiteTypeExist(string siteType, string id);
    Task<PaginatedResult<SiteTypeListVm>> GetSiteTypePaginatedList(GetSiteTypePaginatedListQuery query);
}