﻿using ContinuityPatrol.Application.Features.Rto.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.Rto.Events
{
    public class CreateRtoEventTests
    {
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<RtoCreatedEventHandler>> _mockLogger;
        private readonly RtoCreatedEventHandler _handler;

        public CreateRtoEventTests()
        {
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<RtoCreatedEventHandler>>();
            _handler = new RtoCreatedEventHandler(_mockUserService.Object, _mockLogger.Object, _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_Should_Log_And_Add_UserActivity_When_Event_Is_Handled()
        {
            var rtoCreatedEvent = new RtoCreatedEvent { Name = "TestRto" };

            _mockUserService.Setup(us => us.UserId).Returns("test-user-id");
            _mockUserService.Setup(us => us.LoginName).Returns("test-login-name");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/api/rto/create");
            _mockUserService.Setup(us => us.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(us => us.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(rtoCreatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(
                ua => ua.UserId == "test-user-id" &&
                      ua.LoginName == "test-login-name" &&
                      ua.RequestUrl == "/api/rto/create" &&
                      ua.CompanyId == "test-company-id" &&
                      ua.HostAddress == "127.0.0.1" &&
                      ua.Action == "Create Rto" &&
                      ua.Entity == "Rto" &&
                      ua.ActivityType == "Create" &&
                      ua.ActivityDetails == "Rto 'TestRto' created successfully.")), Times.Once);

            _mockLogger.Verify(
                logger => logger.LogInformation(
                    It.Is<string>(msg => msg.Contains("Rto 'TestRto' created successfully."))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Use_Default_UserId_When_UserId_Is_NullOrEmpty()
        {
            var rtoCreatedEvent = new RtoCreatedEvent { Name = "TestRto" };

            _mockUserService.Setup(us => us.UserId).Returns((string)null);
            _mockUserService.Setup(us => us.LoginName).Returns("test-login-name");
            _mockUserService.Setup(us => us.RequestedUrl).Returns("/api/rto/create");
            _mockUserService.Setup(us => us.CompanyId).Returns("test-company-id");
            _mockUserService.Setup(us => us.IpAddress).Returns("127.0.0.1");

            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Returns(ToString);

            await _handler.Handle(rtoCreatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(
                ua => !string.IsNullOrEmpty(ua.CreatedBy) &&
                      ua.CreatedBy != "test-user-id")), Times.Once);
        }
    }
}
