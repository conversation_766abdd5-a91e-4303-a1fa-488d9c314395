﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetNameUnique;

public class GetMatrixNameUniqueQueryHandler : IRequestHandler<GetMatrixNameUniqueQuery, bool>
{
    private readonly IApprovalMatrixRepository _approvalMatrixRepository;

    public GetMatrixNameUniqueQueryHandler(IApprovalMatrixRepository approvalMatrixRepository)
    {
        _approvalMatrixRepository = approvalMatrixRepository;
    }

    public async Task<bool> Handle(GetMatrixNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _approvalMatrixRepository.IsApprovalMatrixNameExist(request.MatrixName,request.Id);
    }
}