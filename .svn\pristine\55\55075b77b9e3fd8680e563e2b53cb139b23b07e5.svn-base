﻿namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    partial class ScheduledJobWorkflowReport
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScheduledJobWorkflowReport));
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.prperpetuuitiLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox5 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.prClientLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this._userName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo2 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.SubBand1 = new DevExpress.XtraReports.UI.SubBand();
            this.xrPictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblSuccessCount = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPictureBox37 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLabel106 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel88 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel87 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel86 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine10 = new DevExpress.XtraReports.UI.XRLine();
            this.lblTotalCount = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPictureBox4 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLine4 = new DevExpress.XtraReports.UI.XRLine();
            this.xrPictureBox59 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLabel56 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblErrorCount = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine5 = new DevExpress.XtraReports.UI.XRLine();
            this.lblWorkflowName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrChart4 = new DevExpress.XtraReports.UI.XRChart();
            this.xrPictureBox27 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.PageFooter = new DevExpress.XtraReports.UI.PageFooterBand();
            this.xrLabel49 = new DevExpress.XtraReports.UI.XRLabel();
            this.pageInfo2 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrPictureBox26 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell27 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell28 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTableCell29 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel57 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel137 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel138 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTableCell32 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrSerialNumber = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell33 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell34 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell35 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell36 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPictureBox45 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox34 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox79 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox53 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox46 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox35 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox42 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLabel52 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrControlStyle1 = new DevExpress.XtraReports.UI.XRControlStyle();
            ((System.ComponentModel.ISupportInitialize)(this.xrChart4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.prperpetuuitiLogo,
            this.xrPictureBox5,
            this.prClientLogo});
            this.TopMargin.HeightF = 46.875F;
            this.TopMargin.Name = "TopMargin";
            // 
            // prperpetuuitiLogo
            // 
            this.prperpetuuitiLogo.BorderWidth = 0F;
            this.prperpetuuitiLogo.ImageAlignment = DevExpress.XtraPrinting.ImageAlignment.MiddleCenter;
            this.prperpetuuitiLogo.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("prperpetuuitiLogo.ImageSource"));
            this.prperpetuuitiLogo.LocationFloat = new DevExpress.Utils.PointFloat(910.55F, 6.92F);
            this.prperpetuuitiLogo.Name = "prperpetuuitiLogo";
            this.prperpetuuitiLogo.SizeF = new System.Drawing.SizeF(175.38F, 35F);
            this.prperpetuuitiLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.prperpetuuitiLogo.StylePriority.UseBorderWidth = false;
            // 
            // xrPictureBox5
            // 
            this.xrPictureBox5.BorderWidth = 0F;
            this.xrPictureBox5.ImageAlignment = DevExpress.XtraPrinting.ImageAlignment.BottomCenter;
            this.xrPictureBox5.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox5.ImageSource"));
            this.xrPictureBox5.LocationFloat = new DevExpress.Utils.PointFloat(15.26F, 6.92F);
            this.xrPictureBox5.Name = "xrPictureBox5";
            this.xrPictureBox5.SizeF = new System.Drawing.SizeF(175.38F, 35F);
            this.xrPictureBox5.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox5.StylePriority.UseBorderWidth = false;
            // 
            // prClientLogo
            // 
            this.prClientLogo.LocationFloat = new DevExpress.Utils.PointFloat(910.5499F, 6.919988F);
            this.prClientLogo.Name = "prClientLogo";
            this.prClientLogo.SizeF = new System.Drawing.SizeF(175.38F, 35F);
            this.prClientLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 2.128347F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // Detail
            // 
            this.Detail.HeightF = 0F;
            this.Detail.Name = "Detail";
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this._userName,
            this.xrLabel5,
            this.xrPageInfo2,
            this.xrLabel1});
            this.ReportHeader.HeightF = 59.33F;
            this.ReportHeader.Name = "ReportHeader";
            this.ReportHeader.SubBands.AddRange(new DevExpress.XtraReports.UI.SubBand[] {
            this.SubBand1});
            // 
            // _userName
            // 
            this._userName.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this._userName.ForeColor = System.Drawing.Color.White;
            this._userName.LocationFloat = new DevExpress.Utils.PointFloat(571.08F, 8F);
            this._userName.Multiline = true;
            this._userName.Name = "_userName";
            this._userName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this._userName.SizeF = new System.Drawing.SizeF(514.85F, 23F);
            this._userName.StylePriority.UseFont = false;
            this._userName.StylePriority.UseForeColor = false;
            this._userName.StylePriority.UseTextAlignment = false;
            this._userName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel5.ForeColor = System.Drawing.Color.White;
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(851.84F, 32F);
            this.xrLabel5.Multiline = true;
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(88.79492F, 22.99998F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseForeColor = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "Date & Time :";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrPageInfo2
            // 
            this.xrPageInfo2.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrPageInfo2.ForeColor = System.Drawing.Color.White;
            this.xrPageInfo2.LocationFloat = new DevExpress.Utils.PointFloat(941.56F, 32F);
            this.xrPageInfo2.Name = "xrPageInfo2";
            this.xrPageInfo2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo2.PageInfo = DevExpress.XtraPrinting.PageInfo.DateTime;
            this.xrPageInfo2.SizeF = new System.Drawing.SizeF(144.3708F, 22.99999F);
            this.xrPageInfo2.StylePriority.UseFont = false;
            this.xrPageInfo2.StylePriority.UseForeColor = false;
            this.xrPageInfo2.StylePriority.UseTextAlignment = false;
            this.xrPageInfo2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.xrPageInfo2.TextFormatString = "{0:dd-MM-yyyy hh:mm tt}";
            // 
            // xrLabel1
            // 
            this.xrLabel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(126)))), ((int)(((byte)(130)))));
            this.xrLabel1.Font = new DevExpress.Drawing.DXFont("SF UI Text", 13F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel1.ForeColor = System.Drawing.Color.White;
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 3.12F);
            this.xrLabel1.Multiline = true;
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(1100F, 56.21F);
            this.xrLabel1.StylePriority.UseBackColor = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseForeColor = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "  Scheduled Job Workflow Report";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // SubBand1
            // 
            this.SubBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox1,
            this.lblSuccessCount,
            this.xrPictureBox37,
            this.xrLabel106,
            this.xrLabel88,
            this.xrLabel87,
            this.xrLabel86,
            this.xrLine10,
            this.lblTotalCount,
            this.xrLabel4,
            this.xrPictureBox4,
            this.xrLine4,
            this.xrPictureBox59,
            this.xrLabel56,
            this.lblErrorCount,
            this.xrLabel29,
            this.xrLine5,
            this.lblWorkflowName,
            this.xrLabel2,
            this.xrChart4,
            this.xrPictureBox27});
            this.SubBand1.HeightF = 449.3878F;
            this.SubBand1.Name = "SubBand1";
            // 
            // xrPictureBox1
            // 
            this.xrPictureBox1.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox1.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox1.ImageSource"));
            this.xrPictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(641.2471F, 196.524F);
            this.xrPictureBox1.Name = "xrPictureBox1";
            this.xrPictureBox1.SizeF = new System.Drawing.SizeF(21F, 21.20821F);
            this.xrPictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox1.StylePriority.UseBackColor = false;
            // 
            // lblSuccessCount
            // 
            this.lblSuccessCount.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8.5F, DevExpress.Drawing.DXFontStyle.Bold);
            this.lblSuccessCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.lblSuccessCount.LocationFloat = new DevExpress.Utils.PointFloat(740.371F, 160.5433F);
            this.lblSuccessCount.Multiline = true;
            this.lblSuccessCount.Name = "lblSuccessCount";
            this.lblSuccessCount.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblSuccessCount.SizeF = new System.Drawing.SizeF(47.87579F, 21.20837F);
            this.lblSuccessCount.StylePriority.UseFont = false;
            this.lblSuccessCount.StylePriority.UseForeColor = false;
            this.lblSuccessCount.StylePriority.UseTextAlignment = false;
            this.lblSuccessCount.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrPictureBox37
            // 
            this.xrPictureBox37.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox37.ImageSource"));
            this.xrPictureBox37.LocationFloat = new DevExpress.Utils.PointFloat(641.5133F, 160.5433F);
            this.xrPictureBox37.Name = "xrPictureBox37";
            this.xrPictureBox37.SizeF = new System.Drawing.SizeF(20.73393F, 21.20819F);
            this.xrPictureBox37.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrLabel106
            // 
            this.xrLabel106.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrLabel106.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(144)))), ((int)(((byte)(154)))));
            this.xrLabel106.LocationFloat = new DevExpress.Utils.PointFloat(667.2499F, 160.5433F);
            this.xrLabel106.Multiline = true;
            this.xrLabel106.Name = "xrLabel106";
            this.xrLabel106.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel106.SizeF = new System.Drawing.SizeF(64.45563F, 21.20837F);
            this.xrLabel106.StylePriority.UseFont = false;
            this.xrLabel106.StylePriority.UseForeColor = false;
            this.xrLabel106.StylePriority.UseTextAlignment = false;
            this.xrLabel106.Text = "Success";
            this.xrLabel106.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel88
            // 
            this.xrLabel88.BackColor = System.Drawing.Color.Transparent;
            this.xrLabel88.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(13)))), ((int)(((byte)(110)))), ((int)(((byte)(253)))));
            this.xrLabel88.BorderWidth = 0F;
            this.xrLabel88.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10.7F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel88.ForeColor = System.Drawing.Color.Black;
            this.xrLabel88.LocationFloat = new DevExpress.Utils.PointFloat(64.69633F, 83.37857F);
            this.xrLabel88.Multiline = true;
            this.xrLabel88.Name = "xrLabel88";
            this.xrLabel88.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel88.SizeF = new System.Drawing.SizeF(231.8999F, 23.00002F);
            this.xrLabel88.StylePriority.UseBackColor = false;
            this.xrLabel88.StylePriority.UseBorderColor = false;
            this.xrLabel88.StylePriority.UseBorderWidth = false;
            this.xrLabel88.StylePriority.UseFont = false;
            this.xrLabel88.StylePriority.UseForeColor = false;
            this.xrLabel88.StylePriority.UseTextAlignment = false;
            this.xrLabel88.Text = "Workflow Action Status Info";
            this.xrLabel88.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel87
            // 
            this.xrLabel87.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F);
            this.xrLabel87.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(144)))), ((int)(((byte)(154)))));
            this.xrLabel87.LocationFloat = new DevExpress.Utils.PointFloat(740.371F, 132.2499F);
            this.xrLabel87.Multiline = true;
            this.xrLabel87.Name = "xrLabel87";
            this.xrLabel87.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel87.SizeF = new System.Drawing.SizeF(47.87585F, 15.00003F);
            this.xrLabel87.StylePriority.UseFont = false;
            this.xrLabel87.StylePriority.UseForeColor = false;
            this.xrLabel87.StylePriority.UseTextAlignment = false;
            this.xrLabel87.Text = "Count";
            this.xrLabel87.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel86
            // 
            this.xrLabel86.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F);
            this.xrLabel86.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(144)))), ((int)(((byte)(154)))));
            this.xrLabel86.LocationFloat = new DevExpress.Utils.PointFloat(641.5133F, 132.2499F);
            this.xrLabel86.Multiline = true;
            this.xrLabel86.Name = "xrLabel86";
            this.xrLabel86.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel86.SizeF = new System.Drawing.SizeF(61.15619F, 14.99997F);
            this.xrLabel86.StylePriority.UseFont = false;
            this.xrLabel86.StylePriority.UseForeColor = false;
            this.xrLabel86.StylePriority.UseTextAlignment = false;
            this.xrLabel86.Text = "Status";
            this.xrLabel86.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLine10
            // 
            this.xrLine10.ForeColor = System.Drawing.Color.LightGray;
            this.xrLine10.LocationFloat = new DevExpress.Utils.PointFloat(631.275F, 147.5434F);
            this.xrLine10.Name = "xrLine10";
            this.xrLine10.SizeF = new System.Drawing.SizeF(164.7884F, 5.208374F);
            this.xrLine10.StylePriority.UseForeColor = false;
            // 
            // lblTotalCount
            // 
            this.lblTotalCount.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8.5F, DevExpress.Drawing.DXFontStyle.Bold);
            this.lblTotalCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.lblTotalCount.LocationFloat = new DevExpress.Utils.PointFloat(740.371F, 244.0911F);
            this.lblTotalCount.Multiline = true;
            this.lblTotalCount.Name = "lblTotalCount";
            this.lblTotalCount.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalCount.SizeF = new System.Drawing.SizeF(47.87573F, 21.20844F);
            this.lblTotalCount.StylePriority.UseFont = false;
            this.lblTotalCount.StylePriority.UseForeColor = false;
            this.lblTotalCount.StylePriority.UseTextAlignment = false;
            this.lblTotalCount.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(144)))), ((int)(((byte)(154)))));
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(667.2499F, 244.0911F);
            this.xrLabel4.Multiline = true;
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(64.45563F, 21.20844F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseForeColor = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "Total";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrPictureBox4
            // 
            this.xrPictureBox4.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox4.ImageSource"));
            this.xrPictureBox4.LocationFloat = new DevExpress.Utils.PointFloat(641.5133F, 244.0911F);
            this.xrPictureBox4.Name = "xrPictureBox4";
            this.xrPictureBox4.SizeF = new System.Drawing.SizeF(20.73395F, 21.20819F);
            this.xrPictureBox4.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrLine4
            // 
            this.xrLine4.ForeColor = System.Drawing.Color.LightGray;
            this.xrLine4.LocationFloat = new DevExpress.Utils.PointFloat(64.69633F, 110.5014F);
            this.xrLine4.Name = "xrLine4";
            this.xrLine4.SizeF = new System.Drawing.SizeF(372.4507F, 5.208344F);
            this.xrLine4.StylePriority.UseForeColor = false;
            // 
            // xrPictureBox59
            // 
            this.xrPictureBox59.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox59.ImageSource"));
            this.xrPictureBox59.LocationFloat = new DevExpress.Utils.PointFloat(615.7245F, 307.7104F);
            this.xrPictureBox59.Name = "xrPictureBox59";
            this.xrPictureBox59.SizeF = new System.Drawing.SizeF(20.73F, 16.75F);
            this.xrPictureBox59.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrLabel56
            // 
            this.xrLabel56.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrLabel56.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.xrLabel56.LocationFloat = new DevExpress.Utils.PointFloat(641.2471F, 307.7104F);
            this.xrLabel56.Multiline = true;
            this.xrLabel56.Name = "xrLabel56";
            this.xrLabel56.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel56.SizeF = new System.Drawing.SizeF(176F, 16.75F);
            this.xrLabel56.StylePriority.UseFont = false;
            this.xrLabel56.StylePriority.UseForeColor = false;
            this.xrLabel56.StylePriority.UseTextAlignment = false;
            this.xrLabel56.Text = "Legend    NA: Not Applicable";
            this.xrLabel56.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // lblErrorCount
            // 
            this.lblErrorCount.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8.5F, DevExpress.Drawing.DXFontStyle.Bold);
            this.lblErrorCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(10)))), ((int)(((byte)(10)))), ((int)(((byte)(10)))));
            this.lblErrorCount.LocationFloat = new DevExpress.Utils.PointFloat(740.371F, 196.524F);
            this.lblErrorCount.Multiline = true;
            this.lblErrorCount.Name = "lblErrorCount";
            this.lblErrorCount.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblErrorCount.SizeF = new System.Drawing.SizeF(47.87579F, 21.20819F);
            this.lblErrorCount.StylePriority.UseFont = false;
            this.lblErrorCount.StylePriority.UseForeColor = false;
            this.lblErrorCount.StylePriority.UseTextAlignment = false;
            this.lblErrorCount.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel29
            // 
            this.xrLabel29.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrLabel29.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(144)))), ((int)(((byte)(154)))));
            this.xrLabel29.LocationFloat = new DevExpress.Utils.PointFloat(667.2499F, 196.524F);
            this.xrLabel29.Multiline = true;
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel29.SizeF = new System.Drawing.SizeF(64.45563F, 21.20819F);
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UseForeColor = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            this.xrLabel29.Text = "Error";
            this.xrLabel29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLine5
            // 
            this.xrLine5.ForeColor = System.Drawing.Color.LightGray;
            this.xrLine5.LocationFloat = new DevExpress.Utils.PointFloat(631.4582F, 227.7382F);
            this.xrLine5.Name = "xrLine5";
            this.xrLine5.SizeF = new System.Drawing.SizeF(164.7884F, 5.208374F);
            this.xrLine5.StylePriority.UseForeColor = false;
            // 
            // lblWorkflowName
            // 
            this.lblWorkflowName.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F);
            this.lblWorkflowName.LocationFloat = new DevExpress.Utils.PointFloat(155.8368F, 22.99999F);
            this.lblWorkflowName.Multiline = true;
            this.lblWorkflowName.Name = "lblWorkflowName";
            this.lblWorkflowName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblWorkflowName.SizeF = new System.Drawing.SizeF(330.3002F, 23F);
            this.lblWorkflowName.StylePriority.UseFont = false;
            this.lblWorkflowName.StylePriority.UseTextAlignment = false;
            this.lblWorkflowName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(34.59423F, 22.99999F);
            this.xrLabel2.Multiline = true;
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(120.4925F, 23F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "Workflow Name :";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrChart4
            // 
            this.xrChart4.BorderColor = System.Drawing.Color.Black;
            this.xrChart4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrChart4.Legend.LegendID = -1;
            this.xrChart4.LocationFloat = new DevExpress.Utils.PointFloat(147.5633F, 129.4583F);
            this.xrChart4.Name = "xrChart4";
            this.xrChart4.PaletteName = "Palette 1";
            this.xrChart4.PaletteRepository.Add("Palette 1", new DevExpress.XtraCharts.Palette("Palette 1", DevExpress.XtraCharts.PaletteScaleMode.Repeat, new DevExpress.XtraCharts.PaletteEntry[] {
                new DevExpress.XtraCharts.PaletteEntry(System.Drawing.Color.FromArgb(((int)(((byte)(65)))), ((int)(((byte)(194)))), ((int)(((byte)(0))))), System.Drawing.Color.FromArgb(((int)(((byte)(65)))), ((int)(((byte)(194)))), ((int)(((byte)(0)))))),
                new DevExpress.XtraCharts.PaletteEntry(System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(0)))), ((int)(((byte)(43))))), System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(0)))), ((int)(((byte)(43))))))}));
            this.xrChart4.SeriesSerializable = new DevExpress.XtraCharts.Series[0];
            this.xrChart4.SizeF = new System.Drawing.SizeF(387.7008F, 266.5032F);
            this.xrChart4.BeforePrint += new DevExpress.XtraReports.UI.BeforePrintEventHandler(this.xrChart4_BeforePrint);
            // 
            // xrPictureBox27
            // 
            this.xrPictureBox27.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox27.ImageSource"));
            this.xrPictureBox27.LocationFloat = new DevExpress.Utils.PointFloat(15.26F, 46F);
            this.xrPictureBox27.Name = "xrPictureBox27";
            this.xrPictureBox27.SizeF = new System.Drawing.SizeF(1074.74F, 385.3239F);
            this.xrPictureBox27.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // PageFooter
            // 
            this.PageFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel49,
            this.pageInfo2,
            this.xrPictureBox26});
            this.PageFooter.Name = "PageFooter";
            // 
            // xrLabel49
            // 
            this.xrLabel49.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
            this.xrLabel49.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(50)))), ((int)(((byte)(52)))));
            this.xrLabel49.LocationFloat = new DevExpress.Utils.PointFloat(297.2588F, 63.50011F);
            this.xrLabel49.Multiline = true;
            this.xrLabel49.Name = "xrLabel49";
            this.xrLabel49.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel49.SizeF = new System.Drawing.SizeF(465.1902F, 23.00001F);
            this.xrLabel49.StylePriority.UseFont = false;
            this.xrLabel49.StylePriority.UseForeColor = false;
            this.xrLabel49.StylePriority.UseTextAlignment = false;
            this.xrLabel49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrLabel49.BeforePrint += new DevExpress.XtraReports.UI.BeforePrintEventHandler(this._version_BeforePrint);
            // 
            // pageInfo2
            // 
            this.pageInfo2.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
            this.pageInfo2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(50)))), ((int)(((byte)(52)))));
            this.pageInfo2.LocationFloat = new DevExpress.Utils.PointFloat(987.792F, 63.50011F);
            this.pageInfo2.Name = "pageInfo2";
            this.pageInfo2.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.pageInfo2.StylePriority.UseFont = false;
            this.pageInfo2.StylePriority.UseForeColor = false;
            this.pageInfo2.StylePriority.UseTextAlignment = false;
            this.pageInfo2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.pageInfo2.TextFormatString = "Page {0} of {1}";
            // 
            // xrPictureBox26
            // 
            this.xrPictureBox26.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox26.ImageSource"));
            this.xrPictureBox26.LocationFloat = new DevExpress.Utils.PointFloat(0F, 53.0834F);
            this.xrPictureBox26.Name = "xrPictureBox26";
            this.xrPictureBox26.SizeF = new System.Drawing.SizeF(1099F, 40F);
            this.xrPictureBox26.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable4});
            this.GroupHeader1.HeightF = 37.54501F;
            this.GroupHeader1.Name = "GroupHeader1";
            this.GroupHeader1.RepeatEveryPage = true;
            // 
            // xrTable4
            // 
            this.xrTable4.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(6.26F, 0F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(1084.74F, 35F);
            this.xrTable4.StylePriority.UseFont = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell26,
            this.xrTableCell27,
            this.xrTableCell1,
            this.xrTableCell28,
            this.xrTableCell29,
            this.xrTableCell30,
            this.xrTableCell32,
            this.xrTableCell7});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 6.3452282227545007D;
            // 
            // xrTableCell26
            // 
            this.xrTableCell26.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell26.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell26.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell26.Multiline = true;
            this.xrTableCell26.Name = "xrTableCell26";
            this.xrTableCell26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell26.StylePriority.UseBackColor = false;
            this.xrTableCell26.StylePriority.UseBorderColor = false;
            this.xrTableCell26.StylePriority.UseFont = false;
            this.xrTableCell26.StylePriority.UseTextAlignment = false;
            this.xrTableCell26.Text = "Sr.No.";
            this.xrTableCell26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell26.Weight = 52.324410795588143D;
            // 
            // xrTableCell27
            // 
            this.xrTableCell27.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell27.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell27.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell27.Multiline = true;
            this.xrTableCell27.Name = "xrTableCell27";
            this.xrTableCell27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell27.StylePriority.UseBackColor = false;
            this.xrTableCell27.StylePriority.UseBorderColor = false;
            this.xrTableCell27.StylePriority.UseFont = false;
            this.xrTableCell27.StylePriority.UseTextAlignment = false;
            this.xrTableCell27.Text = "WorkFlow Action Name";
            this.xrTableCell27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell27.Weight = 205.55701050941897D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell1.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell1.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell1.Multiline = true;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorderColor = false;
            this.xrTableCell1.StylePriority.UseFont = false;
            this.xrTableCell1.StylePriority.UseTextAlignment = false;
            this.xrTableCell1.Text = "Node Name";
            this.xrTableCell1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell1.Weight = 205.55701050941897D;
            // 
            // xrTableCell28
            // 
            this.xrTableCell28.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell28.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell28.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel13,
            this.xrLabel14});
            this.xrTableCell28.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell28.Multiline = true;
            this.xrTableCell28.Name = "xrTableCell28";
            this.xrTableCell28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell28.StylePriority.UseBackColor = false;
            this.xrTableCell28.StylePriority.UseBorderColor = false;
            this.xrTableCell28.StylePriority.UseFont = false;
            this.xrTableCell28.StylePriority.UseTextAlignment = false;
            this.xrTableCell28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell28.Weight = 165.92332416401962D;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(0.000213623F, 0F);
            this.xrLabel13.Multiline = true;
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(76.17215F, 35F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.Text = "Start Time";
            // 
            // xrLabel14
            // 
            this.xrLabel14.Font = new DevExpress.Drawing.DXFont("SF UI Text", 7F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(76.17236F, 1.00003F);
            this.xrLabel14.Multiline = true;
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(63.21695F, 33.99995F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.Text = "(HH:MM:SS)";
            // 
            // xrTableCell29
            // 
            this.xrTableCell29.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell29.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell29.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel15,
            this.xrLabel57});
            this.xrTableCell29.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell29.Multiline = true;
            this.xrTableCell29.Name = "xrTableCell29";
            this.xrTableCell29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell29.StylePriority.UseBackColor = false;
            this.xrTableCell29.StylePriority.UseBorderColor = false;
            this.xrTableCell29.StylePriority.UseFont = false;
            this.xrTableCell29.StylePriority.UseTextAlignment = false;
            this.xrTableCell29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell29.Weight = 160.5125227654602D;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(0.000213623F, 0F);
            this.xrLabel15.Multiline = true;
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(69.17215F, 35F);
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.Text = "End Time";
            // 
            // xrLabel57
            // 
            this.xrLabel57.Font = new DevExpress.Drawing.DXFont("SF UI Text", 7F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel57.LocationFloat = new DevExpress.Utils.PointFloat(69.10358F, 1.00003F);
            this.xrLabel57.Multiline = true;
            this.xrLabel57.Name = "xrLabel57";
            this.xrLabel57.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel57.SizeF = new System.Drawing.SizeF(63.21695F, 33.99995F);
            this.xrLabel57.StylePriority.UseFont = false;
            this.xrLabel57.Text = "(HH:MM:SS)";
            // 
            // xrTableCell30
            // 
            this.xrTableCell30.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell30.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell30.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel137,
            this.xrLabel138});
            this.xrTableCell30.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell30.Multiline = true;
            this.xrTableCell30.Name = "xrTableCell30";
            this.xrTableCell30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell30.StylePriority.UseBackColor = false;
            this.xrTableCell30.StylePriority.UseBorderColor = false;
            this.xrTableCell30.StylePriority.UseFont = false;
            this.xrTableCell30.StylePriority.UseTextAlignment = false;
            this.xrTableCell30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell30.Weight = 165.48157722259816D;
            // 
            // xrLabel137
            // 
            this.xrLabel137.Font = new DevExpress.Drawing.DXFont("SF UI Text", 10F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel137.LocationFloat = new DevExpress.Utils.PointFloat(0.000213623F, 0F);
            this.xrLabel137.Multiline = true;
            this.xrLabel137.Name = "xrLabel137";
            this.xrLabel137.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel137.SizeF = new System.Drawing.SizeF(76.17215F, 35F);
            this.xrLabel137.StylePriority.UseFont = false;
            this.xrLabel137.Text = "Total Time";
            // 
            // xrLabel138
            // 
            this.xrLabel138.Font = new DevExpress.Drawing.DXFont("SF UI Text", 7F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel138.LocationFloat = new DevExpress.Utils.PointFloat(76.17236F, 1.00003F);
            this.xrLabel138.Multiline = true;
            this.xrLabel138.Name = "xrLabel138";
            this.xrLabel138.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel138.SizeF = new System.Drawing.SizeF(63.21695F, 33.99995F);
            this.xrLabel138.StylePriority.UseFont = false;
            this.xrLabel138.Text = "(HH:MM:SS)";
            // 
            // xrTableCell32
            // 
            this.xrTableCell32.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell32.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell32.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell32.Multiline = true;
            this.xrTableCell32.Name = "xrTableCell32";
            this.xrTableCell32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell32.StylePriority.UseBackColor = false;
            this.xrTableCell32.StylePriority.UseBorderColor = false;
            this.xrTableCell32.StylePriority.UseFont = false;
            this.xrTableCell32.StylePriority.UseTextAlignment = false;
            this.xrTableCell32.Text = "Status";
            this.xrTableCell32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell32.Weight = 130.49142330405775D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(237)))), ((int)(((byte)(245)))), ((int)(((byte)(255)))));
            this.xrTableCell7.BorderColor = System.Drawing.Color.Transparent;
            this.xrTableCell7.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell7.Multiline = true;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorderColor = false;
            this.xrTableCell7.StylePriority.UseFont = false;
            this.xrTableCell7.StylePriority.UseTextAlignment = false;
            this.xrTableCell7.Text = "Message";
            this.xrTableCell7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell7.Weight = 114.78470294042306D;
            // 
            // DetailReport
            // 
            this.DetailReport.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail1});
            this.DetailReport.Level = 0;
            this.DetailReport.Name = "DetailReport";
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable5});
            this.Detail1.HeightF = 42.20132F;
            this.Detail1.Name = "Detail1";
            // 
            // xrTable5
            // 
            this.xrTable5.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
            this.xrTable5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(26)))), ((int)(((byte)(26)))), ((int)(((byte)(26)))));
            this.xrTable5.LocationFloat = new DevExpress.Utils.PointFloat(6.26F, 0F);
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.OddStyleName = "xrControlStyle1";
            this.xrTable5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.SizeF = new System.Drawing.SizeF(1084.74F, 35F);
            this.xrTable5.StylePriority.UseFont = false;
            this.xrTable5.StylePriority.UseForeColor = false;
            this.xrTable5.StylePriority.UseTextAlignment = false;
            this.xrTable5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrSerialNumber,
            this.xrTableCell31,
            this.xrTableCell2,
            this.xrTableCell33,
            this.xrTableCell34,
            this.xrTableCell35,
            this.xrTableCell36,
            this.xrTableCell8});
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.Weight = 1D;
            // 
            // xrSerialNumber
            // 
            this.xrSerialNumber.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrSerialNumber.ForeColor = System.Drawing.Color.Black;
            this.xrSerialNumber.Multiline = true;
            this.xrSerialNumber.Name = "xrSerialNumber";
            this.xrSerialNumber.StylePriority.UseFont = false;
            this.xrSerialNumber.StylePriority.UseForeColor = false;
            this.xrSerialNumber.StylePriority.UseTextAlignment = false;
            this.xrSerialNumber.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrSerialNumber.Weight = 0.38452199866591474D;
            this.xrSerialNumber.BeforePrint += new DevExpress.XtraReports.UI.BeforePrintEventHandler(this.xrSerialNumber_BeforePrint);
            // 
            // xrTableCell31
            // 
            this.xrTableCell31.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([WorkflowActionName]) || [WorkflowActionName] ==\'NA\' , \'-\',[Wor" +
                    "kflowActionName])")});
            this.xrTableCell31.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrTableCell31.ForeColor = System.Drawing.Color.Black;
            this.xrTableCell31.Multiline = true;
            this.xrTableCell31.Name = "xrTableCell31";
            this.xrTableCell31.StylePriority.UseFont = false;
            this.xrTableCell31.StylePriority.UseForeColor = false;
            this.xrTableCell31.Text = "xrTableCell31";
            this.xrTableCell31.Weight = 1.510600631131845D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([NodeName]) || [NodeName] ==\'NA\' , \'-\',[NodeName])")});
            this.xrTableCell2.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrTableCell2.ForeColor = System.Drawing.Color.Black;
            this.xrTableCell2.Multiline = true;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseFont = false;
            this.xrTableCell2.StylePriority.UseForeColor = false;
            this.xrTableCell2.Weight = 1.510600631131845D;
            // 
            // xrTableCell33
            // 
            this.xrTableCell33.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([StartTime]) || [StartTime] ==\'NA\' , \'-\',[StartTime])")});
            this.xrTableCell33.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrTableCell33.ForeColor = System.Drawing.Color.Black;
            this.xrTableCell33.Multiline = true;
            this.xrTableCell33.Name = "xrTableCell33";
            this.xrTableCell33.StylePriority.UseFont = false;
            this.xrTableCell33.StylePriority.UseForeColor = false;
            this.xrTableCell33.Text = "xrTableCell33";
            this.xrTableCell33.Weight = 1.2193385404933794D;
            // 
            // xrTableCell34
            // 
            this.xrTableCell34.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([EndTime]) || [EndTime] ==\'NA\' , \'-\',[EndTime])")});
            this.xrTableCell34.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrTableCell34.ForeColor = System.Drawing.Color.Black;
            this.xrTableCell34.Multiline = true;
            this.xrTableCell34.Name = "xrTableCell34";
            this.xrTableCell34.StylePriority.UseFont = false;
            this.xrTableCell34.StylePriority.UseForeColor = false;
            this.xrTableCell34.Text = "xrTableCell34";
            this.xrTableCell34.Weight = 1.1795740162701251D;
            // 
            // xrTableCell35
            // 
            this.xrTableCell35.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([TotalTime]) || [TotalTime] ==\'NA\' , \'-\',[TotalTime])")});
            this.xrTableCell35.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrTableCell35.ForeColor = System.Drawing.Color.Black;
            this.xrTableCell35.Multiline = true;
            this.xrTableCell35.Name = "xrTableCell35";
            this.xrTableCell35.StylePriority.UseFont = false;
            this.xrTableCell35.StylePriority.UseForeColor = false;
            this.xrTableCell35.Text = "xrTableCell35";
            this.xrTableCell35.Weight = 1.216092383718665D;
            // 
            // xrTableCell36
            // 
            this.xrTableCell36.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox45,
            this.xrPictureBox34,
            this.xrPictureBox79,
            this.xrPictureBox53,
            this.xrPictureBox46,
            this.xrPictureBox35,
            this.xrPictureBox42,
            this.xrLabel52});
            this.xrTableCell36.Multiline = true;
            this.xrTableCell36.Name = "xrTableCell36";
            this.xrTableCell36.Text = "xrTableCell36";
            this.xrTableCell36.Weight = 0.95895690741839479D;
            // 
            // xrPictureBox45
            // 
            this.xrPictureBox45.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox45.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Pending\',true,false)\n")});
            this.xrPictureBox45.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox45.ImageSource"));
            this.xrPictureBox45.LocationFloat = new DevExpress.Utils.PointFloat(0F, 9.386601F);
            this.xrPictureBox45.Name = "xrPictureBox45";
            this.xrPictureBox45.SizeF = new System.Drawing.SizeF(21F, 16F);
            this.xrPictureBox45.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox45.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox34
            // 
            this.xrPictureBox34.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox34.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Skipped\'||[Status]==\'Skip\',true,false)")});
            this.xrPictureBox34.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox34.ImageSource"));
            this.xrPictureBox34.LocationFloat = new DevExpress.Utils.PointFloat(0.1870728F, 9.386601F);
            this.xrPictureBox34.Name = "xrPictureBox34";
            this.xrPictureBox34.SizeF = new System.Drawing.SizeF(21F, 16F);
            this.xrPictureBox34.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox34.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox79
            // 
            this.xrPictureBox79.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox79.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif (Lower([Status])==\'bypassed\',true,false)")});
            this.xrPictureBox79.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox79.ImageSource"));
            this.xrPictureBox79.LocationFloat = new DevExpress.Utils.PointFloat(0.1870728F, 9.386601F);
            this.xrPictureBox79.Name = "xrPictureBox79";
            this.xrPictureBox79.SizeF = new System.Drawing.SizeF(20.99994F, 16F);
            this.xrPictureBox79.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox79.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox53
            // 
            this.xrPictureBox53.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox53.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Aborted\',true,false)\n")});
            this.xrPictureBox53.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox53.ImageSource"));
            this.xrPictureBox53.LocationFloat = new DevExpress.Utils.PointFloat(0F, 9.386578F);
            this.xrPictureBox53.Name = "xrPictureBox53";
            this.xrPictureBox53.SizeF = new System.Drawing.SizeF(20.99994F, 16F);
            this.xrPictureBox53.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox53.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox46
            // 
            this.xrPictureBox46.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox46.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Error\',true,false)")});
            this.xrPictureBox46.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox46.ImageSource"));
            this.xrPictureBox46.LocationFloat = new DevExpress.Utils.PointFloat(0.1870728F, 9.386601F);
            this.xrPictureBox46.Name = "xrPictureBox46";
            this.xrPictureBox46.SizeF = new System.Drawing.SizeF(21F, 16F);
            this.xrPictureBox46.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox46.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox35
            // 
            this.xrPictureBox35.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox35.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Success\',true,false)")});
            this.xrPictureBox35.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox35.ImageSource"));
            this.xrPictureBox35.LocationFloat = new DevExpress.Utils.PointFloat(0.1870728F, 9.386601F);
            this.xrPictureBox35.Name = "xrPictureBox35";
            this.xrPictureBox35.SizeF = new System.Drawing.SizeF(21F, 16F);
            this.xrPictureBox35.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox35.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox42
            // 
            this.xrPictureBox42.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox42.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Visible", "Iif ([Status]==\'Running\',true,false)")});
            this.xrPictureBox42.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("svg", resources.GetString("xrPictureBox42.ImageSource"));
            this.xrPictureBox42.LocationFloat = new DevExpress.Utils.PointFloat(6.103516E-05F, 9.386602F);
            this.xrPictureBox42.Name = "xrPictureBox42";
            this.xrPictureBox42.SizeF = new System.Drawing.SizeF(21F, 16F);
            this.xrPictureBox42.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox42.StylePriority.UseBackColor = false;
            // 
            // xrLabel52
            // 
            this.xrLabel52.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([Status]) || [Status] ==\'NA\' , \'-\',[Status])")});
            this.xrLabel52.Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F);
            this.xrLabel52.ForeColor = System.Drawing.Color.Black;
            this.xrLabel52.LocationFloat = new DevExpress.Utils.PointFloat(21.18707F, 0F);
            this.xrLabel52.Multiline = true;
            this.xrLabel52.Name = "xrLabel52";
            this.xrLabel52.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel52.SizeF = new System.Drawing.SizeF(95.13831F, 35F);
            this.xrLabel52.StylePriority.UseFont = false;
            this.xrLabel52.StylePriority.UseForeColor = false;
            this.xrLabel52.StylePriority.UseTextAlignment = false;
            this.xrLabel52.Text = "xrLabel5";
            this.xrLabel52.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Iif(IsNullOrEmpty([Message]) || [Message] ==\'NA\' , \'-\',[Message])")});
            this.xrTableCell8.Multiline = true;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.Weight = 0.84352905805668277D;
            // 
            // xrControlStyle1
            // 
            this.xrControlStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(245)))), ((int)(((byte)(248)))));
            this.xrControlStyle1.BorderColor = System.Drawing.Color.Transparent;
            this.xrControlStyle1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrControlStyle1.BorderWidth = 1F;
            this.xrControlStyle1.ForeColor = System.Drawing.Color.Black;
            this.xrControlStyle1.Name = "xrControlStyle1";
            this.xrControlStyle1.Padding = new DevExpress.XtraPrinting.PaddingInfo(1, 0, 0, 0, 100F);
            // 
            // ScheduledJobWorkflowReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.TopMargin,
            this.BottomMargin,
            this.Detail,
            this.ReportHeader,
            this.PageFooter,
            this.GroupHeader1,
            this.DetailReport});
            this.Font = new DevExpress.Drawing.DXFont("Arial", 9.75F);
            this.Landscape = true;
            this.Margins = new DevExpress.Drawing.DXMargins(0F, 0F, 46.875F, 2.128347F);
            this.PageHeight = 850;
            this.PageWidth = 1100;
            this.StyleSheet.AddRange(new DevExpress.XtraReports.UI.XRControlStyle[] {
            this.xrControlStyle1});
            this.Version = "23.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrChart4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private TopMarginBand TopMargin;
        private BottomMarginBand BottomMargin;
        private DetailBand Detail;
        private XRPictureBox prperpetuuitiLogo;
        private XRPictureBox xrPictureBox5;
        private XRPictureBox prClientLogo;
        private ReportHeaderBand ReportHeader;
        private XRLabel _userName;
        private XRLabel xrLabel5;
        private XRPageInfo xrPageInfo2;
        private XRLabel xrLabel1;
        private PageFooterBand PageFooter;
        private XRLabel xrLabel49;
        private XRPageInfo pageInfo2;
        private XRPictureBox xrPictureBox26;
        private GroupHeaderBand GroupHeader1;
        private DetailReportBand DetailReport;
        private DetailBand Detail1;
        private SubBand SubBand1;
        private XRLabel lblWorkflowName;
        private XRLabel xrLabel2;
        private XRControlStyle xrControlStyle1;
        private XRTable xrTable4;
        private XRTableRow xrTableRow4;
        private XRTableCell xrTableCell26;
        private XRTableCell xrTableCell27;
        private XRTableCell xrTableCell28;
        private XRLabel xrLabel13;
        private XRLabel xrLabel14;
        private XRTableCell xrTableCell29;
        private XRLabel xrLabel15;
        private XRLabel xrLabel57;
        private XRTableCell xrTableCell30;
        private XRLabel xrLabel137;
        private XRLabel xrLabel138;
        private XRTableCell xrTableCell32;
        private XRTableCell xrTableCell7;
        private XRTable xrTable5;
        private XRTableRow xrTableRow5;
        private XRTableCell xrSerialNumber;
        private XRTableCell xrTableCell31;
        private XRTableCell xrTableCell33;
        private XRTableCell xrTableCell34;
        private XRTableCell xrTableCell35;
        private XRTableCell xrTableCell36;
        private XRPictureBox xrPictureBox45;
        private XRPictureBox xrPictureBox34;
        private XRPictureBox xrPictureBox79;
        private XRPictureBox xrPictureBox53;
        private XRPictureBox xrPictureBox46;
        private XRPictureBox xrPictureBox35;
        private XRPictureBox xrPictureBox42;
        private XRLabel xrLabel52;
        private XRTableCell xrTableCell8;
        private XRLabel lblSuccessCount;
        private XRPictureBox xrPictureBox37;
        private XRLabel xrLabel106;
        private XRLabel xrLabel88;
        private XRChart xrChart4;
        private XRLabel xrLabel87;
        private XRLabel xrLabel86;
        private XRLine xrLine10;
        private XRLabel lblTotalCount;
        private XRLabel xrLabel4;
        private XRPictureBox xrPictureBox4;
        private XRLine xrLine4;
        private XRPictureBox xrPictureBox59;
        private XRLabel xrLabel56;
        private XRLabel lblErrorCount;
        private XRLabel xrLabel29;
        private XRLine xrLine5;
        private XRPictureBox xrPictureBox27;
        private XRPictureBox xrPictureBox1;
        private XRTableCell xrTableCell1;
        private XRTableCell xrTableCell2;
    }
}
