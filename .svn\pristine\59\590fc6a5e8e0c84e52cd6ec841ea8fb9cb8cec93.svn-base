﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public  class ApprovalMatrixService : BaseClient, IApprovalMatrixService
{

    public ApprovalMatrixService(IConfiguration config, IAppCache cache, ILogger<ApprovalMatrixService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateApprovalMatrixCommand approvalMatrixCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrix", Method.Post);

        request.AddJsonBody(approvalMatrixCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateApprovalMatrixCommand approvalMatrixCommand)
    {
        var request = new RestRequest("api/v6/approvalmatrix", Method.Put);

        request.AddJsonBody(approvalMatrixCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrix/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<List<ApprovalMatrixListVm>> GetApprovalMatrixList()
    {
        var request = new RestRequest("api/v6/approvalmatrix");

        return await GetFromCache<List<ApprovalMatrixListVm>>(request, "GetApprovalMatrixApprovalList");
    }

    public async Task<PaginatedResult<ApprovalMatrixListVm>> GetPaginatedApprovalMatrices(GetApprovalMatrixPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/approvalmatrix/paginated-list{query}");

        return await Get<PaginatedResult<ApprovalMatrixListVm>>(request);
    }

    public async Task<bool> IsApprovalMatrixNameExist(string name, string id)
    {
        var request = new RestRequest($"api/v6/approvalmatrix/name-exist?name={name}&id={id}");

        return await Get<bool>(request);
    }

}