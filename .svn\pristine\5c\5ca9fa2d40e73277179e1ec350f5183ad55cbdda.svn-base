﻿using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.Features.Server.Commands.SaveAs;

public class SaveAsServerCommandValidator : AbstractValidator<SaveAsServerCommand>
{
    private readonly IServerRepository _serverRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ISiteTypeRepository _siteTypeRepository;    
    public Domain.Entities.Server ServerDtl;
    public Domain.Entities.LicenseManager LicenseDtl;

    public SaveAsServerCommandValidator(IServerRepository serverRepository, ILicenseManagerRepository licenseManagerRepository,
        ISiteRepository siteRepository, ILicenseValidationService licenseValidationService, ISiteTypeRepository siteTypeRepository)
    {
        _serverRepository = serverRepository;       
        _licenseManagerRepository = licenseManagerRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _siteTypeRepository = siteTypeRepository;


        RuleFor(p => p.ServerId)
            .NotEmpty().WithMessage("{PropertyName} is required.").NotNull()
            .WithMessage("{PropertyName} cannot be null.")
            .MustAsync(async (serverId, _) =>await GetServerDetailAsync(serverId))
            .WithMessage("Server detail not found.");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p).MustAsync(ServerNameUnique)
            .WithMessage("A Same Name Already Exists.");

        RuleFor(p => p)
            .MustAsync(IsLicenseActiveAsync)
            .WithMessage("License is in 'InActive' state.");

        RuleFor(p => p)
            .MustAsync(IsLicenseExpiredAsync)
            .WithMessage("The license key has expired.");


        RuleFor(p => p)
            .MustAsync(ValidateLicenseCountAsync)
            .WithMessage("Server count reached maximum limit.");
    }
    private async Task<bool> GetServerDetailAsync(string serverId)
    {
        var server = await _serverRepository.GetByReferenceIdAsync(serverId);

        ServerDtl = server ?? throw new InvalidException ("Server detail was not found.");

        return true;
    }

    private async Task<bool> ServerNameUnique(SaveAsServerCommand saveAsServerCommand, CancellationToken token)
    {
        return !await _serverRepository.IsServerNameUnique(saveAsServerCommand.Name);
    }
    private async Task<bool> IsLicenseActiveAsync(SaveAsServerCommand p, CancellationToken cancellationToken)
    {
        if (ServerDtl.LicenseId.IsNullOrWhiteSpace()) return false;

        LicenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(ServerDtl.LicenseId) ??
                         throw new InvalidException("License detail was not found.");
        return LicenseDtl.IsState;
    }

    private async Task<bool> IsLicenseExpiredAsync(SaveAsServerCommand p, CancellationToken cancellationToken)
    {
        return await _licenseValidationService.IsLicenseExpired(LicenseDtl?.ExpiryDate);
    }
    private async Task<bool> ValidateLicenseCountAsync(SaveAsServerCommand p, CancellationToken cancel)
    {
        var site = await _siteRepository.GetByReferenceIdAsync(ServerDtl?.SiteId);

        Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
            new NotFoundException(nameof(Domain.Entities.Site), ServerDtl?.SiteId));

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site?.TypeId);

        Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
            new NotFoundException(nameof(Domain.Entities.SiteType), site?.TypeId));

        var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType?.ReferenceId);

        var serverCount = await _serverRepository.GetServerCountByLicenseKey(ServerDtl?.LicenseId, ServerDtl?.RoleType, siteType?.ReferenceId);

        return await _licenseValidationService.IsServerLicenseCountExitMaxLimit(LicenseDtl, siteType, ServerDtl?.RoleType,
            serverCount, index);
    }

}