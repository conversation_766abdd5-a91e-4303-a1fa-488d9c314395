﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Manage;

public class MssqlAlwaysOnMonitorLogsService : BaseClient, IMssqlAlwaysOnMonitorLogsService
{
    public MssqlAlwaysOnMonitorLogsService(IConfiguration config, IAppCache cache, ILogger<MssqlAlwaysOnMonitorLogsService> logger) 
        : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateMSSQLAlwaysOnMonitorLogCommand createMssqlAlwaysOnMonitorLogCommand)
    {
        var request = new RestRequest("api/v6/mssqlalwaysonmonitorlogs", Method.Post);

        request.AddJsonBody(createMssqlAlwaysOnMonitorLogCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<List<MssqlAlwaysOnMonitorLogsListVm>> GetAllMSSQLAlwaysOnMonitorLogs()
    {
        var request = new RestRequest("api/v6/mssqlalwaysonmonitorlogs");

        return await GetFromCache<List<MssqlAlwaysOnMonitorLogsListVm>>(request, "GetAllMSSQLAlwaysOnMonitorLogs");
    }

    public async Task<MSSQLAlwaysOnMonitorLogsDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/mssqlalwaysonmonitorlogs/{id}");

        return await Get<MSSQLAlwaysOnMonitorLogsDetailVm>(request);
    }

    public async Task<List<MSSQLAlwaysOnMonitorLogsDetailByTypeVm>> GetMSSQLAlwaysOnMonitorLogsByType(string type)
    {
        var request = new RestRequest($"api/v6/mssqlalwaysonmonitorlogs/type?type={type}");

        return await Get<List<MSSQLAlwaysOnMonitorLogsDetailByTypeVm>>(request);
    }

    public async Task<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>> GetPaginatedMSSQLAlwaysOnMonitorLogs(GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/mssqlalwaysonmonitorlogs/paginated-list");

        return await Get<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>(request);
    }
}