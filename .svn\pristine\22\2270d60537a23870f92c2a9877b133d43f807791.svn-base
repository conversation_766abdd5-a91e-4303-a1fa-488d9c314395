﻿namespace ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;

public class CreateCGExecutionCommand : IRequest<CreateCGExecutionResponse>
{
    public string WorkflowOperationId { get; set; }
    public string WorkflowName { get; set; }
    public string CGName { get; set; }
    public string Status { get; set; }
    public DateTime EnableStartTime { get; set; }
    public DateTime EnableEndTime { get; set; }
    public DateTime? DisableStartTime { get; set; }
    public DateTime? DisableEndTime { get; set; }
    public string ErrorMessage { get; set; }
    public string Description { get; set; }
    public string RemoteReplicaClusterName { get; set; }
    public string ProductionClusterName { get; set; }
    public string Type { get; set; }
    public string JobId { get; set; }
    public string DisableStatus { get; set; }
    public string EnableRemarks { get; set; }
    public string DisableRemarks { get; set; }
}
