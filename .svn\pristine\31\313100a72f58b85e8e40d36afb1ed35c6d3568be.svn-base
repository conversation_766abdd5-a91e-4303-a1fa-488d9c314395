﻿using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCyberSnapList;
using ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSchedulerLogs.Models;
using ContinuityPatrol.Application.Features.Report.Queries.GetSchedulerWorkflowActionResultsReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetCGExecutionReport;

namespace ContinuityPatrol.Services.Api.Impl.Report;

public class ReportService : BaseClient, IReportService
{
    public ReportService(IConfiguration config, IAppCache cache, ILogger<ReportService> logger) : base(config, cache, logger)
    {
    }
 
    public async Task<LicenseReport> GetLicenseReportById(string? licenseId)
    {
        var request = new RestRequest($"api/v6/reports/licenseid?licenseId={licenseId}");

        return await Get<LicenseReport>(request);
    }

    public async Task<object> GetRpoSlaReportByInfraObjectId(string infraObjectId, string type, string reportStartDate, string reportEndDate, string dateOption)
    {
        var request = new RestRequest($"api/v6/reports/rposla-report?infraObjectId={infraObjectId}&type={type}&reportStartDate={reportStartDate}&reportEndDate={reportEndDate}");

        return await Get<object>(request);
    }

    public async Task<RTOReports> GetRtoReportByWorkflowOperationId(string workflowOperationId)
    {
        var request = new RestRequest($"api/v6/reports/rto-report?workflowOperationId={workflowOperationId}");

        return await Get<RTOReports>(request);
    }
    public async Task<DrReadyStatusReport> GetDrReadyStatusReportByBusinessServiceId(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/reports/dr-readystatus-report?businessServiceId={businessServiceId}");

        return await Get<DrReadyStatusReport>(request);
    }

    public async Task<DRReadyExecutionReport> GetDrReadyExecutionLogReport(string? businessServiceId,string? startTime, string? endTime)
    {
        var request = new RestRequest($"api/v6/reports/drready-execution-report?businessServiceId={businessServiceId}&startTime={startTime}&endTime={endTime}");

        return await Get<DRReadyExecutionReport>(request);
    }

    public async Task<BusinessServiceSummaryReport> GetBusinessServiceSummaryReport()
    {
        var request = new RestRequest($"api/v6/reports/businessservies-summary-report");

        return await Get<BusinessServiceSummaryReport>(request);
    }
    public async Task<InfraObjectSummaryReport> GetInfraObjectSummaryReport(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/reports/infraobject-summary-report?businessServiceId={businessServiceId}");

        return await Get<InfraObjectSummaryReport>(request);
    }
    public async Task<GetRpoSlaDeviationReportVm> GetRpoSlaDeviationReportByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(string? businessServiceId,string? infraObjectId, string? createdDate,
       string? lastModifiedDate)
    {
        var request = new RestRequest($"api/v6/reports/rposla-deviation-report?businessServiceId={businessServiceId}&infraObjectId={infraObjectId}&createDate={createdDate}&lastModifiedDate={lastModifiedDate}");

        return await Get<GetRpoSlaDeviationReportVm>(request);
    }
    public async Task<InfraReport> GetInfraObjectConfigurationReport(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/reports/infra-configuration-report?infraObjectId={infraObjectId}");

        return await Get<InfraReport>(request);
    }

    public async Task<UserActivityReport> GetUserActivityReport(string? userId, string createDate, string lastModifiedDate)
    {
        var request = new RestRequest($"api/v6/reports/user-activity-report?userId={userId}&createDate={createDate}&lastModifiedDate={lastModifiedDate}");

        return await Get<UserActivityReport> (request);
    }
    
    public async Task<DrDrillReport> GetDrDrillReportByWorkflowOperationId(string workflowOperationId,string? runMode, bool isCustom)
    {
        var request = new RestRequest($"api/v6/reports/drdrill-report?workflowOperationId={workflowOperationId}&runMode={runMode}&isCustom={isCustom}");

        return await Get<DrDrillReport>(request);
    }
    public async Task<LicenseReportByBusinessServiceReport> GetLicenseUtilizationReportByBusinessServiceId(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/reports/license-utilization-report-business-service{businessServiceId}");

        return await Get<LicenseReportByBusinessServiceReport>(request);
    }
    public async Task<GetRunBookReportVm> GetRunbookReportByWorkflowId(string workflowId)
    {
        var request = new RestRequest($"api/v6/reports/runbook-report?workflowId={workflowId}");

        return await Get<GetRunBookReportVm>(request);
    }

    public async Task<AirGapLogReportVm> GetAirGapReport(string startDate, string endDate, string airGapId)
    {
        var request = new RestRequest($"api/v6/reports/airgap-report?airGapId={airGapId}");

        return await Get<AirGapLogReportVm>(request);
    }

    public async Task<GetBulkImportReportVm> GetBulkImportReport(string operationId)
    {
        var request = new RestRequest($"api/v6/reports/bulkimport-report?operationId={operationId}");

        return await Get<GetBulkImportReportVm>(request);
    }

    public  Task<DriftReportVm> GetDriftReport(string startDate, string endDate, string InfraId, string DriftStatusId)
    {
        throw new NotImplementedException();
    }
    public Task<List<DriftEventReportVm>> GetDriftReportInfraId(string startDate, string endDate)
    {
        throw new NotImplementedException();
    }
    public Task<List<DriftEventReportVm>> GetDriftReportStatus(string startDate, string endDate, string InfraId)
    {
        throw new NotImplementedException();
    }

    public Task<List<GetAirGapListVm>> GetAirGapList(string startDate, string endDate)
    {
        throw new NotImplementedException();
    }

    public Task<List<GetCyberSnapsListVm>> GetCyberSnapsList(string startDate, string endDate)
    {
        throw new NotImplementedException();
    }

    public Task<GetCyberSnapsReportVm> GetCyberSnapsBySnapTagName(string CyberTagName, string startDate, string endDate)
    {
        throw new NotImplementedException();
    }

    public Task<GetResiliencyReadinessSchedulerLogReportVm> GetInfraObjectSchedulerLogList(string startDate, string endDate)
    {
        throw new NotImplementedException();
    }
    public Task<SchedulerWorkflowActionResultsVm> GetScheduleWorkflowActionResultReport(string workflowId, string infraReferenceId)
    {
        throw new NotImplementedException();
    }

    public Task<CGExecutionReportResultVm> GetCGExecutionReport(string workflowOperationId)
    {
        throw new NotImplementedException();
    }
}
