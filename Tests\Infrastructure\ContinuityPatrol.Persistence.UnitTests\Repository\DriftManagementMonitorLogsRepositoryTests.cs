using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using ContinuityPatrol.Shared.Tests.Mocks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class DriftManagementMonitorLogsRepositoryTests
{
    private readonly ApplicationDbContext _dbContext;
    private readonly DriftManagementMonitorLogsRepository _repository;
    private readonly Mock<IConfiguration> _mockConfiguration;

    public DriftManagementMonitorLogsRepositoryTests()
    {
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockConfiguration = ConfigurationRepositoryMocks.GetConnectionString();
        _repository = new DriftManagementMonitorLogsRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var driftManagementMonitorLog = new DriftManagementMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            Status= "Active",
            IsActive = true,
            CreatedDate = DateTime.Now
        };

        // Act
        var result = await _repository.AddAsync(driftManagementMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftManagementMonitorLog.Status, result.Status);
        Assert.Equal(driftManagementMonitorLog.InfraObjectId, result.InfraObjectId);
        Assert.Single(_dbContext.DriftManagementMonitorLogss);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var driftManagementMonitorLog = new DriftManagementMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
      Status = "Pending",
            IsActive = true
        };
        await _repository.AddAsync(driftManagementMonitorLog);

        driftManagementMonitorLog.Status = "Pending";

        // Act
        var result = await _repository.UpdateAsync(driftManagementMonitorLog);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Pending", result.Status);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var driftManagementMonitorLog = new DriftManagementMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            Status = "Pending",
            IsActive = true
        };
        await _repository.AddAsync(driftManagementMonitorLog);

        // Act
        var result = await _repository.DeleteAsync(driftManagementMonitorLog);

        // Assert
        Assert.Equal(driftManagementMonitorLog.InfraObjectName, result.InfraObjectName);
        Assert.Equal(driftManagementMonitorLog.Status, result.Status);
        Assert.Empty(_dbContext.DriftManagementMonitorLogss);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftManagementMonitorLog = new DriftManagementMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            Status = "Pending",
            IsActive = true
        };
        var addedEntity = await _repository.AddAsync(driftManagementMonitorLog);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Status, result.Status);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var driftManagementMonitorLog = new DriftManagementMonitorLogs
        {
            ReferenceId = Guid.NewGuid().ToString(),
            InfraObjectId = "INFRA_123",
            Status = "Running",
            IsActive = true
        };
        await _repository.AddAsync(driftManagementMonitorLog);

        // Act
        var result = await _repository.GetByReferenceIdAsync(driftManagementMonitorLog.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(driftManagementMonitorLog.ReferenceId, result.ReferenceId);
        Assert.Equal(driftManagementMonitorLog.Status, result.Status);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllActiveEntities()
    {
        // Arrange
        var logs = new List<DriftManagementMonitorLogs>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123",  Status = "Running", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456",  Status = "Running", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_789",  Status = "Running", IsActive = false }
        };
        _dbContext.DriftManagementMonitorLogss.AddRange(logs);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Only active entities
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region AddRange Tests

    [Fact]
    public async Task AddRange_ShouldAddEntities()
    {
        // Arrange
        var logs = new List<DriftManagementMonitorLogs>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", Status = "Pending", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", Status = "Running", IsActive = true }
        };

        // Act
        var result = await _repository.AddRangeAsync(logs);

        // Assert
        Assert.Equal(logs.Count, result.Count());
        Assert.Equal(logs.Count, _dbContext.DriftManagementMonitorLogss.Count());
    }

    [Fact]
    public async Task AddRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    }

    #endregion

    #region RemoveRange Tests

    [Fact]
    public async Task RemoveRange_ShouldRemoveEntities()
    {
        // Arrange
        var logs = new List<DriftManagementMonitorLogs>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", Status="pennding" , IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", Status = "Running", IsActive = true }
        };
        await _repository.AddRangeAsync(logs);

        // Act
        var result = await _repository.RemoveRangeAsync(logs);

        // Assert
        Assert.Equal(logs.Count, result.Count());
        Assert.Empty(_dbContext.DriftManagementMonitorLogss);
    }

    [Fact]
    public async Task RemoveRange_ShouldThrow_WhenEntitiesIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.RemoveRangeAsync(null));
    }

    #endregion

    #region Complex Operations Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var logs = new List<DriftManagementMonitorLogs>
        {
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_123", Status = "Running", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_456", Status = "pending", IsActive = true },
            new() { ReferenceId = Guid.NewGuid().ToString(), InfraObjectId = "INFRA_789", Status = "Success", IsActive = true }
        };
        
        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(logs);
        var initialCount = logs.Count;
        
        var toUpdate = logs.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Success");
        await _repository.UpdateRangeAsync(toUpdate);
        
        var toDelete = logs.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);
        
        var updated = remaining.Where(x => x.Status == "Success").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
