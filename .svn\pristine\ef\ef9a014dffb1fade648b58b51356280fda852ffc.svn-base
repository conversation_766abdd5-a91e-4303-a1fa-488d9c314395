using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TeamMasterFixture : IDisposable
{
    public List<TeamMaster> TeamMasterPaginationList { get; set; }
    public List<TeamMaster> TeamMasterList { get; set; }
    public TeamMaster TeamMasterDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TeamMasterFixture()
    {
        var fixture = new Fixture();

        TeamMasterList = fixture.Create<List<TeamMaster>>();

        TeamMasterPaginationList = fixture.CreateMany<TeamMaster>(20).ToList();

        TeamMasterDto = fixture.Create<TeamMaster>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
