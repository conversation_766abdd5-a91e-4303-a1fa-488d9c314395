﻿using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Queries;

public class GetWorkflowActionNameQueryHandlerTests : IClassFixture<WorkflowActionFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    private readonly GetWorkflowActionNameQueryHandler _handler;

    public GetWorkflowActionNameQueryHandlerTests(WorkflowActionFixture workflowActionFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.GetWorkflowActionNamesRepository(_workflowActionFixture.WorkflowActions);

        _handler = new GetWorkflowActionNameQueryHandler(_workflowActionFixture.Mapper, _mockWorkflowActionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowActions_Name()
    {
        var result = await _handler.Handle(new GetWorkflowActionNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionNameVm>>();

        result[0].Id.ShouldBe(_workflowActionFixture.WorkflowActions[0].ReferenceId);
        result[0].ActionName.ShouldBe(_workflowActionFixture.WorkflowActions[0].ActionName);
        result[0].NodeId.ShouldBe(_workflowActionFixture.WorkflowActions[0].NodeId);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowActionNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowActionNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionNameVm>>();

        result.Count.ShouldBe(_workflowActionFixture.WorkflowActions.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.GetWorkflowActionEmptyRepository();

        var handler = new GetWorkflowActionNameQueryHandler(_workflowActionFixture.Mapper, _mockWorkflowActionRepository.Object);

        var result = await handler.Handle(new GetWorkflowActionNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowActionNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowActionNameQuery(), CancellationToken.None);

        _mockWorkflowActionRepository.Verify(x => x.GetWorkflowActionNames(), Times.Once);
    }
}