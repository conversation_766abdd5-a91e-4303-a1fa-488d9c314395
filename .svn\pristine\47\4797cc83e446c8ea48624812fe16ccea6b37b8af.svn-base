﻿namespace ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Queries.GetDetail;

public class GetWorkflowExecutionEventLogDetailQueryHandler : IRequestHandler<GetWorkflowExecutionEventLogDetailQuery,
    WorkflowExecutionEventLogDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowExecutionEventLogRepository _workflowExecutionEventLogRepository;

    public GetWorkflowExecutionEventLogDetailQueryHandler(IMapper mapper,
        IWorkflowExecutionEventLogRepository workflowExecutionEventLogRepository)
    {
        _mapper = mapper;
        _workflowExecutionEventLogRepository = workflowExecutionEventLogRepository;
    }

    public async Task<WorkflowExecutionEventLogDetailVm> Handle(GetWorkflowExecutionEventLogDetailQuery request,
        CancellationToken cancellationToken)
    {
        var workflowExecutionEventLog = await _workflowExecutionEventLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(workflowExecutionEventLog, nameof(Domain.Entities.WorkflowExecutionEventLog),
            new NotFoundException(nameof(Domain.Entities.WorkflowExecutionEventLog), request.Id));

        var workflowExecutionEventLogDetail = _mapper.Map<WorkflowExecutionEventLogDetailVm>(workflowExecutionEventLog);

        return workflowExecutionEventLogDetail;
    }
}