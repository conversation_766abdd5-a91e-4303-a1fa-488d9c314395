﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowIdAndVersion;
using ContinuityPatrol.Domain.ViewModels.WorkflowHistoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowHistoryService
{
    Task<List<WorkflowHistoryListVm>> GetWorkflowHistoryList();
    Task<List<WorkflowHistoryByWorkflowIdVm>> GetWorkflowHistoryByWorkflowId(string workflowId);
    Task<PaginatedResult<WorkflowHistoryListVm>> GetPaginatedWorkflowHistories(GetWorkflowHistoryPaginatedListQuery query);
    Task<WorkflowHistoryByWorkflowIdAndVersionVm> GetWorkflowHistoryByWorkflowIdAndVersion(string workflowId, string version);
   
}