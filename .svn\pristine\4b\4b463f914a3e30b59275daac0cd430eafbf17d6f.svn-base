async function formMappingSaveAndUpdate() {
    const form = $('#CreateForm')[0];
    const formData = new FormData(form);
    let response = await createOrUpdate(RootUrl + "Admin/FormMapping/CreateOrUpdate", formData); //commonfunctions.js      
    $('#CreateModal').modal('hide');
    btnDisableFormMapping = false;

    if (response?.success) {
        notificationAlert("success", response?.data?.message);
        setTimeout(() => {
            dataTableCreateAndUpdate($("#saveButton"), dataTable);
        }, 2000)
    } else {
        errorNotification(response);
    }
}

async function getFormNamesAndTypes(name = null, type = null) {
    let formbuilderNames = await getRequest(RootUrl + mappingURL.GetNames) //commonfunctions.js

    if (Array.isArray(formbuilderNames) && formbuilderNames?.length > 0) {
        let formMappingName = document.getElementById("formMappingName");
        formbuilderNames?.forEach(formList => {

            if (formList?.isPublish) {
                const { name, id } = formList;
                const option = document.createElement("option");
                option.value = name;
                option.setAttribute("formmappingformid", id);
                option.textContent = name;
                formMappingName.appendChild(option);
            }
        });

        if (name) {
            $("#formMappingName").val(name);
        }
    }
    let formTypeNames = await getRequest(RootUrl + mappingURL.GetFormType) //commonfunctions.js

    if (Array.isArray(formTypeNames) && formTypeNames?.length > 0) {
        let formMappingType = $('#formMappingType');
        formMappingType.empty().append($('<option>').val("").text("Select Type"));

        formTypeNames?.forEach(function (item) {
            const formTypeName = item?.formTypeName || "";
            const modifiedType = formTypeName.toLowerCase().replace(/\s+/g, "");
            const displayText = (modifiedType === "singlesignon") ? "Single sign-on" : formTypeName;
            formMappingType.append($('<option>').val(formTypeName).text(displayText));
        });

        if (type) {
            formMappingType.val(type);
        }
    }
}

function formMappingNameValidation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

async function validateFormMappingOSName(versionarray) {
    let $formMappingOSName = $("#formMappingOSName :selected");
    let formMappingOSName = $formMappingOSName.text();
    let formMappingOSNameID = $formMappingOSName.val();
    let logo = $formMappingOSName.attr("logo") || "cp-images";
    $("#iconChange").removeClass().addClass(logo);
    $("#formMappingLogo").val(logo);

    const filteredObjects = versions?.filter(obj => {
        try {
            const propertyObj = JSON?.parse(obj?.properties);
            return propertyObj?.name === formMappingOSName;
        } catch (error) {
            return false;
        }
    });

    if (filteredObjects?.length > 0) {
        const versionObj = JSON?.parse(filteredObjects[0]?.properties);
        const selectedVersion = document.getElementById("selectedVersion");
        $("#selectedVersion").empty();

        if (versionObj && versionObj?.version !== "") {
            const version = JSON?.parse(versionObj?.version);
            version?.forEach(formversion => {
                const option = document.createElement("option");
                option.value = formversion;

                if (versionarray !== undefined && versionarray?.length !== 0) {
                    versionarray?.Version?.forEach(version => {

                        if (version == formversion) {
                            option.selected = version;
                        }
                    })
                }
                option.textContent = formversion;
                selectedVersion.appendChild(option);
            });
        }
    } else {
        $("#selectedVersion").empty().append("<option value=''>" + "Select Version" + "</option>");
    }
    await formMappingOSNameValidation(formMappingOSName, formName, "osNameError", formMappingOSNameID);
};

async function IsFormNameExist(url, data, errorFunc) {
    let newStr = $("#typeName").text()?.replace('T', 't');
    return !data.formTypeName.trim() ? true : (await GetFormAsync(url, data, errorFunc)) ? `${newStr} already exists` : true;
}

async function GetFormAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

async function formMappingOSNameValidation(value, errorMessage, errorElement, formMappingOSNameID) {
    const errorElementNameExist = $("#osNameError");

    if (!formMappingOSNameID) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    }

    if (formMappingTypeID) {
        $('#' + errorElement).text('').removeClass('field-validation-error');

        if (formMappingTypeID !== formMappingOSNameID) {
            let urlpath = RootUrl + mappingURL.NameExist;
            let dataObj = {
                formTypeName: value,
                formTypeId: formMappingOSNameID,
            };
            const validationResults = [
                await IsFormNameExist(urlpath, dataObj, OnError)
            ];
            const failedValidations = validationResults.filter(result => result !== true);

            if (failedValidations.length > 0) {
                errorElementNameExist.text(failedValidations[0].charAt(0).toUpperCase() + failedValidations[0].slice(1).toLowerCase())
                    .addClass('field-validation-error')
                return false;
            } else {
                errorElementNameExist.text('').removeClass('field-validation-error')
                return true;
            }
        }
        return true;
    }
}

function formMappingTypeValidation(value, errorMessage, errorElement) {
    if (value.length === 0) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

async function validateFormMappingVersion() {
    let formMappingType = $("#formMappingType").val();

    if (formMappingType == "Server" || formMappingType == "Database") {
        await formMappingVersionValidation($("#selectedVersion").val(), " Select version", "versionError");
    }
    if (formMappingType == "Replication" || formMappingType == "Single SignOn" || formMappingType == "Node") {
        $("#versionError").text("").removeClass("field-validation-error")
    }
};

function formMappingVersionValidation(value, errorMessage, errorElement) {

    if (value.length === 0) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
}

function iconChange(value) {
    nameicon.removeClass();
    typeicon.removeClass();
    nameicon.addClass(value);
    typeicon.addClass(value);
}

async function TypeValidation(value, formTypeId) {
    let modifiedType = value.toLowerCase().split(" ").join("");
    let placeholder = modifiedType === "singlesignon" ? "Single Sign-On" : value;

    if (modifiedType != "server" && modifiedType != "database") {
        let element = document.getElementById("typeName");

        if (modifiedType == "replication") {
            element.innerHTML = value + " Type";
            iconChange("cp-replication-on");
        }
        if (modifiedType == "node") {
            element.innerHTML = value + " Type";
            iconChange("cp-network");
        }
        if (modifiedType == "singlesignon") {
            element.innerHTML = "Single Sign-On Type";
            iconChange("cp-single-sign_on");
        }
        if (modifiedType == "replicationoptions") {
            element.innerHTML = value + " Type";
            iconChange("cp-replication-type");
        }
        if (modifiedType == "credentialprofile") {
            element.innerHTML = value + " Type";
            iconChange("cp-credential-profile");
        }
        $('#selectedVersion').val("");
        $('.hideForReplication').hide();
        $('#versionError').text('').removeClass('field-validation-error');
    } else if (modifiedType == "server" || modifiedType == "database") {
        let element = document.getElementById("typeName");

        if (modifiedType == "server") {
            element.innerHTML = value + " Type";
            iconChange("cp-server");
        }
        if (modifiedType == "database") {
            element.innerHTML = value + " Type";
            iconChange("cp-database");
        }
        $('.hideForReplication').show();
    } else {
        let element = document.getElementById("typeName");
        element.innerHTML = value + " Type";
        iconChange("cp-mapping");
    }

    versions = "";
    let length = modifiedType.trim().length;

    if (length > 0) {
        const OSNameSelect = document.getElementById("formMappingOSName");
        OSNameSelect.innerHTML = '';
        let data = {
            name: value,
            __RequestVerificationToken: gettoken()
        }
        let result = await getRequestWithData(RootUrl + mappingURL.TypeListByName, data); //commonfunctions.js

        if (result?.length) {
            $("#selectedVersion").empty();
            versions = result;
            $("#formMappingOSName").prop("disabled", false);
            if ($('#formMappingOSName').data('select2') === undefined) {
                $('#formMappingOSName').select2();
            }
            $('#formMappingOSName').data('select2').selection.placeholder.text = `Select ${placeholder} Type`; //Dynamic change select tag placeholder
            $('#formMappingOSName').trigger('change');
            $('#osNameError').text("").removeClass("field-validation-error");
            let name = `Select ${placeholder} Type`; //value == "Server" ? "OS" :
            formName = name.charAt(0).toUpperCase() + name.slice(1).toLowerCase();
            const defaultOption = document.createElement("option");
            defaultOption.value = '';
            defaultOption.text = `Select ${placeholder} Type`; //value == "Server" ? "OS" :
            defaultOption.hidden = true;
            OSNameSelect.appendChild(defaultOption);
            result.forEach(formtypecategory => {
                const option = document.createElement("option");
                var propertiesParse = JSON.parse(formtypecategory?.properties);
                option.value = formtypecategory?.id;
                option.setAttribute("logo", propertiesParse?.icon);
                option.textContent = formtypecategory?.componentName;
                OSNameSelect.appendChild(option);
            });
            if (formTypeId !== undefined) {
                $('#formMappingOSName').val(formTypeId).trigger("change");
                let logo = $("#formMappingOSName option:selected").attr("logo");
                if (!logo) {
                    logo = "cp-images";
                }
                $("#iconChange").removeClass().addClass(logo);
                $("#formMappingLogo").val(logo);
            }
        }

    } else {
        $("#formMappingOSName").prop("disabled", true);
    }
    formMappingTypeValidation(value, " Select form type", "typeError");
}

function clearErrorMessages() {
    $("#selectedVersion").empty();
    const errorElements = ['#formNameError', '#osNameError', '#versionError', '#typeError'];
    clearInputFields('CreateForm', errorElements);
}

async function populateModelFields(formMappingData) {
    formMappingTypeID = formMappingData?.formTypeId;
    $('#textFormMappingId').val(formMappingData?.id);
    TypeValidation(formMappingData?.name, formMappingData?.formTypeId);
    await getFormNamesAndTypes(formMappingData?.formName, formMappingData?.name)
    //$('#formMappingName').val(formMappingData.formName);
    //$("#formMappingType").val(formMappingData.name);
    if (formMappingData?.name == "Server" || formMappingData?.name == "Database") {
        const selectedVersion = document.getElementById("selectedVersion");
        selectedVersion.innerHTML = '';
        let version = JSON.parse(formMappingData?.version);
        let data = { Name: formMappingData.name };
        let result = await getRequestWithData(RootUrl + mappingURL.TypeListByName, data); //commonfunctions.js
        if (result?.length) {
            versions = result;
            validateFormMappingOSName(version);
        }       
    }
}