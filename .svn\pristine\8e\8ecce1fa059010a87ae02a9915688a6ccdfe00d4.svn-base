using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class MenuBuilderService : BaseClient, IMenuBuilderService
{
    public MenuBuilderService(IConfiguration config, IAppCache cache, ILogger<MenuBuilderService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<MenuBuilderListVm>> GetMenuBuilderList()
    {
        var request = new RestRequest("api/v6/menubuilders");

        return await GetFromCache<List<MenuBuilderListVm>>(request, "GetMenuBuilderList");
    }

    public async Task<BaseResponse> CreateAsync(CreateMenuBuilderCommand createMenuBuilderCommand)
    {
        var request = new RestRequest("api/v6/menubuilders", Method.Post);

        request.AddJsonBody(createMenuBuilderCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateMenuBuilderCommand updateMenuBuilderCommand)
    {
        var request = new RestRequest("api/v6/menubuilders", Method.Put);

        request.AddJsonBody(updateMenuBuilderCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/menubuilders/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<MenuBuilderDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/menubuilders/{id}");

        return await Get<MenuBuilderDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsMenuBuilderNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/menubuilders/name-exist?menubuilderName={name}&id={id}");

        return await Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<MenuBuilderListVm>> GetPaginatedMenuBuilders(GetMenuBuilderPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/menubuilders/paginated-list");

        return await Get<PaginatedResult<MenuBuilderListVm>>(request);
    }
    #endregion
}
