﻿using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;
using ContinuityPatrol.Application.Features.EscalationMatrix.Events.PaginatedView;
using ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using DocumentFormat.OpenXml.Spreadsheet;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class EscalationMatrixController : BaseController
{

    private readonly IPublisher _publisher;
    private readonly ILogger<EscalationMatrixController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;


    public EscalationMatrixController(IPublisher publisher,ILogger<EscalationMatrixController> logger, IMapper mapper, IDataProvider dataProvider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
    }


    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new EscalationMatrixPaginatedEvent());
        var userList = await _dataProvider.User.GetUserPaginatedList(new GetUserPaginatedListQuery());
        var teamMasterList = await _dataProvider.TeamMasterService.GetTeamConfigurationList(new GetTeamMasterPaginatedListQuery());

        EscalationMatrixViewModel escalationMatrixViewModel = new EscalationMatrixViewModel
        {
           // PaginatedUserList = userList,
            PaginatedResultMasterList=teamMasterList
        };
        return View(escalationMatrixViewModel);
    }

    public IActionResult Template()
    {        
        return View();
    }

    [HttpPost]
    [AntiXss]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateOrUpdate(EscalationMatrixViewModel escalationModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Escalation Matrix.");
        var id = escalationModel.Id;
        try
        {
            if (id.IsNullOrWhiteSpace())
            {
                escalationModel.CompanyId = LoggedInUserCompanyId;
                escalationModel.ApproverId = "1";
                escalationModel.EscMatStatus = "0";
                escalationModel.OwnerName = LoggedInName;
                escalationModel.OwnerId = LoggedInUserId;
                var escalationCommand = _mapper.Map<CreateEscalationMatrixCommand>(escalationModel);
                var result = await _dataProvider.EscalationService.CreateAsync(escalationCommand);
                _logger.LogInformation($"Creating Escalation Matrix");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in Escalation Matrix, returning view.");

                return Json(new { Success = true, data = result.Message });
            }
            else
            {
                escalationModel.EscMatStatus = "0";
                var escalationCommand = _mapper.Map<UpdateEscalationMatrixCommand>(escalationModel);
                var result = await _dataProvider.EscalationService.UpdateAsync(escalationCommand);
                _logger.LogInformation($"Updating Escalation Matrix");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in Escalation Matrix, returning view.");
                return Json(new { Success = true, data = result.Message });

            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on Escalation Matrix page while processing the request for create or update.", ex);

            return ex.GetJsonException();
        }
    }


    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdateEscLevel(EscalationMatrixViewModel escalationModel)
    {
        if (string.IsNullOrEmpty(escalationModel.EscalationMatrixLevelViewModel.Id))
        { 
            var command = _mapper.Map<CreateEscalationMatrixLevelCommand>(escalationModel.EscalationMatrixLevelViewModel);

            var result = await _dataProvider.EscalationMatrixLevelService.CreateAsync(command);

            //return RouteToPostView(result);
            return Json(result);
        }
        else
        {
            var escalationCommand = _mapper.Map<UpdateEscalationMatrixLevelCommand>(escalationModel.EscalationMatrixLevelViewModel);


            _logger.LogInformation($"Updating Escalation Matrix'");

            var result = await _dataProvider.EscalationMatrixLevelService.UpdateAsync(escalationCommand);

            // return RouteToPostView(result);
            return Json(result);

        }
    }

    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in  Escalation Matrix");

        try
        {
            var result = await _dataProvider.EscalationService.DeleteAsync(id);
            _logger.LogDebug($"Successfully deleted record in  Escalation Matrix");
            return Json(new { Success = true, data = result.Message });

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on dataSet.", ex);
            return Json(new { Success = false, data = ex.GetMessage() });
        }
    }


    public async Task<IActionResult> DeleteLevel(string id)
    {
        _logger.LogInformation($"Deleting Escalation Matrix level Details by Id '{id}'");


        return RouteToPostView(await _dataProvider.EscalationMatrixLevelService.DeleteAsync(id));
    }


    [HttpGet]
    public async Task<JsonResult> GetPagination(GetEscalationMatrixPaginatedListQuery query)
    {
        return Json(await _dataProvider.EscalationService.GetPaginatedEscalationList(query));
    }


    [HttpGet]
    public async Task<JsonResult> GetPaginationEscMatLevel(string query)
    {
        //query="379ddfa9-0234-4467-be07-cad1cafc0c4c";

        return Json(await _dataProvider.EscalationMatrixLevelService.GetAllEscalationLevel(query));
    }

    private IActionResult RouteToPostView(BaseResponse result)
    {
        TempData.NotifySuccess(result.Message);

        return RedirectToAction("List");
    }

    [HttpGet]
    public async Task<bool> IsEscalationMatrixNameExist(string teamName)
    {
        var nameExist = await _dataProvider.EscalationService.IsEscalationMatrixExist(teamName,"");

        return nameExist;
    }


    [HttpGet]
    public async Task<bool> CheckEscalationLevelNameExist(string levelName)
    {
        var nameExist = await _dataProvider.EscalationMatrixLevelService.IsEscalationMatrixLevelExist(levelName);

        return nameExist;
    }
    [HttpGet]
    public async Task<JsonResult> GetOperationalService()
    {
        var operationalService = await _dataProvider.BusinessService.GetBusinessServiceNames(); 
        return Json(operationalService); 
    }
    public async Task<JsonResult> GetUserNames()
    {
        var users = await _dataProvider.User.GetUsers();
        return Json(users);
    }
    public async Task<JsonResult> GetUserGroup()
    {
        var users = await _dataProvider.UserGroup.GetUserGroupList();
        return Json(users);
    }
    public async Task<JsonResult> getuserinfo(string id)
    {
        var users = await _dataProvider.User.GetUserProfileById(id);
        return Json(users);
    }

}