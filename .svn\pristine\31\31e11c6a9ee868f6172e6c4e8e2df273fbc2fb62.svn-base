﻿using ContinuityPatrol.Application.Features.RsyncJob.Events.Update;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncJob.Events
{
    public class UpdateRsyncJobEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<RsyncJobUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly RsyncJobUpdatedEventHandler _handler;

        public UpdateRsyncJobEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<RsyncJobUpdatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _handler = new RsyncJobUpdatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_Successfully()
        {
            var notification = new RsyncJobUpdatedEvent { Name = "TestRsyncJob" };

            _mockUserService.Setup(s => s.UserId).Returns("123");
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.CompanyId).Returns("456");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://testurl");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            var addedActivity = (Domain.Entities.UserActivity)null;
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => addedActivity = activity)
                .Returns(ToString);

            await _handler.Handle(notification, CancellationToken.None);

            Assert.NotNull(addedActivity);
            Assert.Equal("123", addedActivity.UserId);
            Assert.Equal("TestUser", addedActivity.LoginName);
            Assert.Equal("456", addedActivity.CompanyId);
            Assert.Equal("http://testurl", addedActivity.RequestUrl);
            Assert.Equal("***********", addedActivity.HostAddress);
            Assert.Equal("Update RsyncJob", addedActivity.Action);
            Assert.Equal("RsyncJob", addedActivity.Entity);
            Assert.Equal(ActivityType.Update.ToString(), addedActivity.ActivityType);
            Assert.Equal($"Rsync Job '{notification.Name}' updated successfully.", addedActivity.ActivityDetails);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(log => log.LogInformation($"Rsync Job '{notification.Name}' updated successfully."), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsException_WhenAddAsyncFails()
        {
            var notification = new RsyncJobUpdatedEvent { Name = "TestRsyncJob" };

            _mockUserService.Setup(s => s.UserId).Returns("123");
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Throws(new Exception("Add failed"));

            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(notification, CancellationToken.None));
            Assert.Equal("Add failed", exception.Message);
            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(log => log.LogInformation(It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task Handle_HandlesEmptyUserId_Gracefully()
        {
            var notification = new RsyncJobUpdatedEvent { Name = "TestRsyncJob" };

            _mockUserService.Setup(s => s.UserId).Returns(string.Empty);
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");
            _mockUserService.Setup(s => s.CompanyId).Returns("456");

            var addedActivity = (Domain.Entities.UserActivity)null;
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => addedActivity = activity)
                .Returns(ToString);

            await _handler.Handle(notification, CancellationToken.None);

            Assert.NotNull(addedActivity);
            Assert.False(string.IsNullOrEmpty(addedActivity.CreatedBy));
            Assert.False(string.IsNullOrEmpty(addedActivity.LastModifiedBy));
            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(log => log.LogInformation(It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Handle_LogsInformationSuccessfully_WhenUserServiceIsIncomplete()
        {
            var notification = new RsyncJobUpdatedEvent { Name = "TestRsyncJob" };

            _mockUserService.Setup(s => s.UserId).Returns((string)null);
            _mockUserService.Setup(s => s.LoginName).Returns("TestUser");

            var addedActivity = (Domain.Entities.UserActivity)null;
            _mockUserActivityRepository
                .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(activity => addedActivity = activity)
                .Returns(ToString);

            await _handler.Handle(notification, CancellationToken.None);

            Assert.NotNull(addedActivity);
            Assert.NotNull(addedActivity.CreatedBy);
            Assert.NotNull(addedActivity.LastModifiedBy);
            _mockLogger.Verify(log => log.LogInformation($"Rsync Job '{notification.Name}' updated successfully."), Times.Once);
        }
    }
}
