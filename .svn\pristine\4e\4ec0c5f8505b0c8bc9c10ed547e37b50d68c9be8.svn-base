﻿const nameExistUrl = "Admin/UserRole/IsRoleNameExist";
let globalUserRoleId = '';
$(function () {
    var createPermission = $("#AdminCreate").data("create-permission").toLowerCase();
    if (createPermission == 'false') {
        $(".btn-userrole-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    btnCrudEnable('saveFunction');
    btnCrudEnable('confirmDeleteButton'); 
    $(".accessMGLink-button").on('click', function () {
        var link = $("#accessMgLink");
        sessionStorage.setItem('userRoleId', $(this).data('user-id'));
        //var userDetails = {
        //    "userRole": $(this).data('user-role'),
        //    "userId": $(this).data('user-id')
        //}
        ////var userId = 
        //localStorage.setItem("myDataKey", JSON.stringify(userDetails));       

    });

    $('#totalListCount').text($(".roleData").length);

    var table = $('#UserRoleList').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow"></i>',
                previous: '<i class="cp-left-arrow"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        deferRender: true,
        scroller: true,
    });

    $('#search-inp').on('keyup', function () {
        table.search($(this).val()).draw();
    });

    $('#btnCreateUserRole').on('click', function () {
        globalUserRoleId = '';       
        $("#cpRoleName").val('')
        $('#Name-error').text('').removeClass('field-validation-error');
        $('#saveFunction').text('Save');
        $('#colorTable [type="radio"]').prop('checked', false)
        $('#multiCollapseExample1').collapse('hide');
        $('#CreateModal').modal('show');
        //var colorRadioButtons = document.querySelectorAll('#colorTable input[type="radio"]');
        //colorRadioButtons.forEach(function (radioButton) {
        //    radioButton.checked = false;
        //});

        //if (colorPickerVisible) {
        //    $('#multiCollapseExample1').collapse('hide');
        //    colorPickerVisible = false;
        //}
    });
    document.getElementById('cpRoleName').addEventListener('keypress', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
    });
    //Save Function
    $("#saveFunction").on('click', async function () { 
        let roleName = $("#cpRoleName").val();
        let isNameValid = await validateName(roleName, globalUserRoleId, nameExistUrl);

        let logo = $('.dynamicColor.active').css('background-color')

        if (isNameValid) {
            let data = {
                'Id': globalUserRoleId,
                'Role': roleName,
                'Logo': logo?.toString(),
                'IsDelete': true,
                __RequestVerificationToken: gettoken()
            }

            //let url = globalUserRoleId.length ? "Admin/UserRole/Update" : "Admin/UserRole/CreateOrUpdate"
             createOrUpdate(data)

            $('#CreateModal').modal('hide');
            setTimeout(() => {
                window.location.reload();
            }, 1000)
        }
    });

    async function createOrUpdate(formData) {
        btnCrudDiasable('saveFunction');
        
        await $.ajax({
            url: RootUrl + "Admin/UserRole/CreateOrUpdate",
            type: "POST",
            data: formData,
            async: true,
            success: function (result) {
                if (result) {
                    notificationAlert('success', result.data.message)
                }

            },

        });
        btnCrudEnable('saveFunction');
    }

    $('input[type="radio"][name="color"]').on('click',function () {

        let colorId = $(this).attr('id');
        let spanStyle = $('label[for="' + colorId + '"] span').css('background-color');
        let rgbArray = spanStyle.match(/\d+/g);
        let rgbString = 'rgb(' + rgbArray.join(', ') + ')';
        $('#textLogo').val(rgbString);
       // $('#cpRoleName').css('color', rgbString);
    });

    var colorMap = {};
    $('#colorTable input[type="radio"]').each(function () {
        let id = $(this).attr('id');
        let spanStyle = $('label[for="' + id + '"] span').css('background-color');
        let rgbArray = spanStyle.match(/\d+/g);
        let rgbValue = `${rgbArray[0]},${rgbArray[1]},${rgbArray[2]}`;
        colorMap[rgbValue] = id;
    });


    function getColorIdFromRGB(rgbValue, colorMap) {       
        var rgbArray = rgbValue.match(/\d+/g);
        var r = parseInt(rgbArray[0]);
        var g = parseInt(rgbArray[1]);
        var b = parseInt(rgbArray[2]);

        var colorId = colorMap[`${r},${g},${b}`] || 'unknown';
        return colorId;
    }

    //Update
    $('.edit-button').on('click', function () {
        $('#Name-error').text('');
        $('#Name-error').removeClass('field-validation-error');
        var userData = $(this).data('role');
        populateModalFields(userData);
        $('#saveFunction').text('Update');       
        $('#CreateModal').modal('show');
    });

    function populateModalFields(userData) {

        $('#cpRoleName').val(userData.role);
        globalUserRoleId = userData.id
       // $('#textUpdateId').val(userData.id);
       // $('#textLogo').val(userData.logo);

          let rgbString = userData.logo || 'rgb(0,0,255)'
        
       // $('#cpRoleName').css('color', rgbString);
       
        let convertedColorId = getColorIdFromRGB(rgbString, colorMap);
        $('#' + convertedColorId).prop('checked', true);
    }

    //Validation
    $(document).on('keyup input', '#cpRoleName', commonDebounce(async function () {
        var userRoleId = $('#textUpdateId').val();
        const value = $(this).val();
        var sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, userRoleId, nameExistUrl);
    }, 400));

    //Delete
    $('.delete-button').on('click',function () {
        var UserRoleId = $(this).data('user-id');
        var userRole = $(this).data('user-role');

        $('#deleteData').text(userRole);
        $('#textDeleteId').val(UserRoleId);
    });
});

async function validateName(value, id = null, url) {   
    const errorElement = $('#Name-error');

    if (!value) {
        errorElement.text('Enter role name').addClass('field-validation-error');
        return false;
    } else {
        if (value.includes("<")) {
            errorElement.text('Special characters not allowed');
            errorElement.addClass('field-validation-error');
            return false;
        }

        let data = {
            'userRoleName': value,
            'id' : id
        };

        const validationResults = [
            SpecialCharValidate(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            ShouldNotBeginWithNumber(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxlength(value),
            secondChar(value),
            await IsNameExist(RootUrl + url, data, OnError)
        ];
        return CommonValidation(errorElement, validationResults)
    }
    
}

async function IsNameExist(url, data, OnError) {
    let nameExist = !data.userRoleName.trim() ? true : (await GetAsync(url, data, OnError)) ? "Name already exists" : true;
    return nameExist;
}

//Clear data
//$('[data-bs-target="#CreateModal"]').on('click', function () {
//    const errorElements = ['#Name-error'];
//    clearInputFields('CreateForm', errorElements);

//    var colorRadioButtons = document.querySelectorAll('#colorTable input[type="radio"]');
//    colorRadioButtons.forEach(function (radioButton) {
//        radioButton.checked = false;
//    });
//    //$('#userRoleName').css('color', '');
//    $('#saveFunction').text('Save');
//});

//Clear data
    let colorPickerVisible = false; 


    
    $('#multiCollapseExample1Color').on('click', function (e) {
        if (!colorPickerVisible) { 
            $('#multiCollapseExample1').collapse('show');
            colorPickerVisible = true; 
        } else {
            e.preventDefault();
        }
    });

$(document).on('click', '.dynamicColor', function (e) {
       $('.dynamicColor').removeClass('active')
       $(this).addClass('active')
})
   
    $(document).on('click', function (event) {
        if (!$(event.target).closest('#multiCollapseExample1Color, #multiCollapseExample1').length && colorPickerVisible) {
            $('#multiCollapseExample1').collapse('hide');
            colorPickerVisible = false;
        }
    });

