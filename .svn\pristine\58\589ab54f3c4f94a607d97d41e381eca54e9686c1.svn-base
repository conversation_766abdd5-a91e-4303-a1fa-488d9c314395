using ContinuityPatrol.Application.Features.RoboCopy.Events.Delete;

namespace ContinuityPatrol.Application.Features.RoboCopy.Commands.Delete;

public class DeleteRoboCopyCommandHandler : IRequestHandler<DeleteRoboCopyCommand, DeleteRoboCopyResponse>
{
    private readonly IPublisher _publisher;
    private readonly IRoboCopyRepository _roboCopyRepository;

    public DeleteRoboCopyCommandHandler(IRoboCopyRepository roboCopyRepository, IPublisher publisher)
    {
        _roboCopyRepository = roboCopyRepository;

        _publisher = publisher;
    }

    public async Task<DeleteRoboCopyResponse> Handle(DeleteRoboCopyCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "RoboCopy Id");

        var eventToDelete = await _roboCopyRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.RoboCopy),
            new NotFoundException(nameof(Domain.Entities.RoboCopy), request.Id));

        eventToDelete.IsActive = false;

        await _roboCopyRepository.UpdateAsync(eventToDelete);

        var response = new DeleteRoboCopyResponse
        {
            Message = Message.Delete("RoboCopy Options", eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new RoboCopyDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}