﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;

public class GetSitePropertiesByBusinessServiceIdQueryHandler : IRequestHandler<
    GetSitePropertiesByBusinessServiceIdQuery, SitePropertiesByBusinessServiceIdVm>
{
    private readonly IInfraDashboardViewRepository _infraDashboardViewRepository;
    private readonly ISiteRepository _siteRepository;

    public GetSitePropertiesByBusinessServiceIdQueryHandler(IInfraDashboardViewRepository infraDashboardViewRepository, ISiteRepository siteRepository)
    {
        _infraDashboardViewRepository = infraDashboardViewRepository;
        _siteRepository = siteRepository;
    }

    public async Task<SitePropertiesByBusinessServiceIdVm> Handle(GetSitePropertiesByBusinessServiceIdQuery request,
        CancellationToken cancellationToken)
    {
        var infraObjects = await _infraDashboardViewRepository.GetBsSitePropertiesByBusinessServiceId(request.BusinessServiceId);

        if (infraObjects.Count == 0) return new SitePropertiesByBusinessServiceIdVm();

        var bsSiteProperties = infraObjects
            .Where(item => item.BsSiteProperties.IsNotNullOrWhiteSpace())
            .Select(item =>
            {
                var siteProperties = item.BsSiteProperties;
                return siteProperties.IsNotNullOrWhiteSpace()
                    ? JObject.Parse(siteProperties) 
                    : null;
            })
            .FirstOrDefault();

        var siteIds = bsSiteProperties?.Properties() 
            .Where(site => site.Value is JObject)
            .SelectMany(site =>
            {
                var siteObject = site.Value as JObject;
                var siteId = siteObject?["Id"]?.ToString();
                return siteId != null ? new[] { siteId } : Array.Empty<string>();
            }).Where(x => x !=null)
            .ToList();

        var sites = siteIds!.Count > 0 ? await _siteRepository.GetSitesByIds(siteIds)
            :new List<Domain.Entities.Site>();

        if (sites.Any())
        {
            var siteDictionary = sites
                .Where(siteDto => siteDto != null && siteDto.Name.IsNotNullOrWhiteSpace())
                .ToDictionary(siteDto => siteDto.ReferenceId, siteDto => siteDto.Name);

            foreach (var site in bsSiteProperties.Properties())
            {
                if (site.Value is JObject siteObject)
                {
                    var siteId = siteObject["Id"]?.ToString();

                    if (siteId != null && siteDictionary.TryGetValue(siteId, out var siteName))
                    {
                        siteObject["Name"] = siteName;
                    }
                }
            }
        }

        var sitePropDto = new SitePropertiesByBusinessServiceIdVm();

        sitePropDto.SiteProperties = sites.Count == 0 
            ? sitePropDto.SiteProperties ?? bsSiteProperties.ToString(Newtonsoft.Json.Formatting.Indented)
            : bsSiteProperties.ToString(Newtonsoft.Json.Formatting.Indented); 

        var replicationStatusValues = infraObjects
            .Where(item => item?.ReplicationStatus != null)
            .Select(item => item.ReplicationStatus)
            .Distinct().ToList();

        var drOperationStatusValues = infraObjects
            .Where(item => item?.DROperationStatus != null)
            .Select(item => item.DROperationStatus)
            .Distinct().ToList();

        sitePropDto.DROperationStatus.AddRange(drOperationStatusValues);
        sitePropDto.ReplicationStatus.AddRange(replicationStatusValues);

        return sitePropDto;
    }
}