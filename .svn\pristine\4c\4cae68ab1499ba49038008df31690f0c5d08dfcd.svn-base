﻿using ContinuityPatrol.Application.Features.Database.Events.SaveAs;

namespace ContinuityPatrol.Application.Features.Database.Commands.SaveAs;

public class SaveAsDatabaseCommandHandler : IRequestHandler<SaveAsDatabaseCommand, SaveAsDatabaseResponse>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public SaveAsDatabaseCommandHandler(IPublisher publisher, IMapper mapper, IDatabaseRepository databaseRepository)
    {
        _publisher = publisher;
        _mapper = mapper;
        _databaseRepository = databaseRepository;
    }

    public async Task<SaveAsDatabaseResponse> Handle(SaveAsDatabaseCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _databaseRepository.GetByReferenceIdAsync(request.DatabaseId);

        eventToUpdate.Id = 0;

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Database), request.DatabaseId);

        eventToUpdate.ReferenceId = "";

        _mapper.Map(request, eventToUpdate, typeof(SaveAsDatabaseCommand), typeof(Domain.Entities.Database));

        await _databaseRepository.AddAsync(eventToUpdate);

        var response = new SaveAsDatabaseResponse
        {
            Id = eventToUpdate.ReferenceId,
            Message = Message.Create(nameof(Domain.Entities.Database), eventToUpdate.Name)
        };

        await _publisher.Publish(new SaveAsDatabaseEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}