using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ArchiveFixture : IDisposable
{
    public List<Archive> ArchivePaginationList { get; set; }
    public List<Archive> ArchiveList { get; set; }
    public Archive ArchiveDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ArchiveFixture()
    {
        var fixture = new Fixture();

        ArchiveList = fixture.Create<List<Archive>>();

        ArchivePaginationList = fixture.CreateMany<Archive>(20).ToList();

        ArchivePaginationList.ForEach(x => x.CompanyId = CompanyId);
        ArchivePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ArchivePaginationList.ForEach(x => x.IsActive = true);

        ArchiveList.ForEach(x => x.CompanyId = CompanyId);
        ArchiveList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ArchiveList.ForEach(x => x.IsActive = true);

        ArchiveDto = fixture.Create<Archive>();
        ArchiveDto.CompanyId = CompanyId;
        ArchiveDto.ReferenceId = Guid.NewGuid().ToString();
        ArchiveDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
