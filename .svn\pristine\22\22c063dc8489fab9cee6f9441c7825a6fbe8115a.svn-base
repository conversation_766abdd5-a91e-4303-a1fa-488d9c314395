﻿using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Update;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrixLevel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Manage
{
    public class EscalationMatrixLevelService : BaseClient, IEscalationMatrixLevelService
    {
        public EscalationMatrixLevelService(IConfiguration config, IAppCache cache, ILogger<BaseClient> logger) : base(config, cache, logger)
        {
        }

       public async Task<BaseResponse>  CreateAsync(CreateEscalationMatrixLevelCommand team)
        {
            var request = new RestRequest("api/v6/EscalationMatrixLevelService", Method.Post);

            request.AddJsonBody(team);

            return await Post<BaseResponse>(request);
        }

        public Task<bool> IsEscalationMatrixLevelExist(string name)
        {
            throw new NotImplementedException();
        }

        Task<BaseResponse> IEscalationMatrixLevelService.DeleteAsync(string teamId)
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<EscalationMatrixLevelListVm>> IEscalationMatrixLevelService.GetAllEscalationLevel(string query)
        {
            throw new NotImplementedException();
        }

        Task<PaginatedResult<EscalationMatrixLevelListVm>> IEscalationMatrixLevelService.GetPaginatedEscalationLevelList(GetEscalationMatrixLevelPaginatedListQuery query)
        {
            throw new NotImplementedException();
        }

        Task<BaseResponse> IEscalationMatrixLevelService.UpdateAsync(UpdateEscalationMatrixLevelCommand escalationMatrixCommand)
        {
            throw new NotImplementedException();
        }
    }
}
