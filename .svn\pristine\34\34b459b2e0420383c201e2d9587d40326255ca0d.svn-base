﻿using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;

[SupportedOSPlatform("windows")]
public partial class DriftReport : DevExpress.XtraReports.UI.XtraReport
{
    public static List<DriftEventReportVm> driftEventReportVms = new List<DriftEventReportVm>();
    private static ILogger<PreBuildReportController> logger;
    public DriftReportVm driftReport = new DriftReportVm();

    public DriftReport(string data)
    {
        logger = PreBuildReportController._logger;
        var report = new DriftReportXLS(data);
        driftReport = JsonConvert.DeserializeObject<DriftReportVm>(data);
        InitializeComponent();
        ClientCompanyLogo();
        var reportValues = driftReport.DriftEventReportVm;
        //Assigned Data Source
        this.DetailReport.DataSource = reportValues;
        //Serial Number Before Print
        _serialNumber.BeforePrint += _serialNumber_BeforePrint;
        _infraObjectCount.Text = reportValues.Count.ToString();
    }
    public void ClientCompanyLogo()
    {
        try
        {
            string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
            if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
            {
                prperpetuuitiLogo.Visible = false;
                if (imgbase64String.Contains(","))
                {
                    imgbase64String = imgbase64String.Split(',')[1];
                }
                byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    Image image = Image.FromStream(ms);
                    prClientLogo.Image = image;
                }
            }
            else
            {
                prClientLogo.Visible = false;
                prperpetuuitiLogo.Visible = true;
            }
        }
        catch (Exception ex)
        {
            logger.LogError("Error occured while display the customer logo in DrDrill Summary Report" + ex.Message.ToString());
        }
    }
    private void _driftChart_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            Int64 plannedCount = 0;
            Int64 unPlannedCount = 0;
            Int64 noDataFound = 0;

            var chartValues = driftReport.DriftEventReportVm;
            foreach (var report in chartValues)
            {
                if (report.EntityStatus.ToLower().Equals("planned"))
                {
                    plannedCount = plannedCount + 1;
                }
                if (report.EntityStatus.ToLower().Equals("unplanned"))
                {
                    unPlannedCount = unPlannedCount + 1;
                }
            }

            if (plannedCount == 0 && unPlannedCount == 0)
            {
                Series series1 = new Series("Series1", ViewType.Doughnut);
                _driftChart.Series.Add(series1);
                noDataFound = 1;
                series1.DataSource = CreateChartData(plannedCount, unPlannedCount, noDataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 75D;
                series1.View = doughnutSeriesView;
                series1.ArgumentScaleType = ScaleType.Auto;
                series1.ArgumentDataMember = "Argument";
                series1.ValueScaleType = ScaleType.Numerical;
                series1.ValueDataMembers.AddRange(new string[] { "Value" });
                series1.Label.TextPattern = "{A}";
                series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                _driftChart.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
            else
            {
                Series series = new Series("Series1", ViewType.Doughnut);
                _driftChart.Series.Add(series);

                series.DataSource = CreateChartData(plannedCount, unPlannedCount, noDataFound);
                DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                doughnutSeriesView.MinAllowedSizePercentage = 75D;
                series.View = doughnutSeriesView;
                series.ArgumentScaleType = ScaleType.Auto;
                series.ArgumentDataMember = "Argument";
                series.ValueScaleType = ScaleType.Numerical;
                series.ValueDataMembers.AddRange(new string[] { "Value" });
                series.Label.TextPattern = "{A}\n{V}";
                ((DoughnutSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;
                series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                _driftChart.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            }
        }
        catch (Exception ex)
        {
            logger.LogError($" An error occurred in _driftChart_BeforePrint : " + ex.Message);
            throw;
        }
    }
    private DataTable CreateChartData(Int64 plannedCount, Int64 unPlannedCount, Int64 noDataFound)
    {
        DataTable table = new DataTable("Table1");

        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Value", typeof(Int64));

        Random rnd = new Random();

        table.Rows.Add("Planned", plannedCount);
        table.Rows.Add("Unplanned", unPlannedCount);
        table.Rows.Add("No Data Found", noDataFound);


        return table;
    }
    private int SerialNumber = 1;
    private void _userName_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            Username.Text = "Report Generated By : " + driftReport.ReportGeneratedBy.ToString();
        }
        catch (Exception ex)
        {
            logger.LogError($"An error occurred in drift report username : " + ex.Message);
            throw;
        }
    }

    private DataTable CreateChartDataConflict(List<DriftResourceVm> infraList)
    {
        DataTable table = new DataTable("Table1");
        table.Columns.Add("Argument", typeof(string));
        table.Columns.Add("Conflict", typeof(Int64));
        table.Columns.Add("NotConflict", typeof(Int64));

        foreach (var infra in infraList)
        {
            int Conflict = 0;
            int notConflict = 0;

            if (!string.IsNullOrEmpty(infra.InfraObjectName) && infra.IsConflict)
            {
                Conflict = 1;
            }
            else
            {
                notConflict = 1;
            }
            table.Rows.Add(infra.InfraObjectName, Conflict, notConflict);
        }


        return table;
    }
    private void _driftConflict_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {

            var infraList = driftReport.DriftResourceSummaryVms;
            #region Chart 
            float calculatedHeight = 0;
            double dynamicBarWidth = 0;
            int dataPointCount = infraList.Count;
            int infraObjectCount = infraList.Select(x => x.InfraObjectId).Distinct().Count();

            if (infraObjectCount < 5)
            {
                dynamicBarWidth = 0.5;
                calculatedHeight = 361.1277F;
            }
            else if (infraObjectCount < 9)
            {
                dynamicBarWidth = 1;
                calculatedHeight = (int)Math.Max(0, infraObjectCount * 60 * dynamicBarWidth);
            }
            else if (infraObjectCount < 16)
            {
                dynamicBarWidth = 0.7;
                calculatedHeight = (int)Math.Max(0, infraObjectCount * 50 * dynamicBarWidth);
            }
            else
            {
                dynamicBarWidth = 0.5;
                calculatedHeight = (int)Math.Max(0, infraObjectCount * 65 * dynamicBarWidth);
            }
            xrChart1.HeightF = calculatedHeight;
            #endregion

            Series series = new Series("Series1", ViewType.Bar);
            xrChart1.Series.Add(series);
            series.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series.DataSource = CreateChartDataConflict(infraList);
            series.ArgumentScaleType = ScaleType.Auto;
            series.ArgumentDataMember = "Argument";
            series.ValueScaleType = ScaleType.Numerical;
            series.ValueDataMembers.AddRange(new string[] { "Conflict" });

            SideBySideBarSeriesView view = series.View as SideBySideBarSeriesView;
            view.BarWidth = dynamicBarWidth;
            view.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view.Pane.BorderVisible = false;
            Series series2 = new Series("Series2", ViewType.Bar);
            xrChart1.Series.Add(series2);
            series2.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            series2.DataSource = CreateChartDataConflict(infraList);
            series2.ArgumentScaleType = ScaleType.Auto;
            series2.ArgumentDataMember = "Argument";
            series2.ValueScaleType = ScaleType.Numerical;
            series2.ValueDataMembers.AddRange(new string[] { "NotConflict" });

            SideBySideBarSeriesView view1 = series2.View as SideBySideBarSeriesView;
            view1.BarWidth = dynamicBarWidth;
            view1.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
            view1.Pane.BorderVisible = false;
            xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
            ((XYDiagram)xrChart1.Diagram).Rotated = true;
            ((XYDiagram)xrChart1.Diagram).AxisY.GridLines.Visible = false;
            int red = 138;
            int green = 144;
            int blue = 154;
            Color customColor = Color.FromArgb(red, green, blue);
            ((XYDiagram)xrChart1.Diagram).AxisX.Label.TextColor = customColor;
            ((XYDiagram)xrChart1.Diagram).AxisX.Label.TextPattern = "{A:n0}\n";
            ((XYDiagram)xrChart1.Diagram).AxisX.Label.MaxWidth = 150;
            //((XYDiagram)xrChart1.Diagram).AxisX.GridSpacingAuto = false;
            ((XYDiagram)xrChart1.Diagram).AxisX.MinorCount = 1;
            //((XYDiagram)xrChart1.Diagram).AxisX.GridSpacing = 0.1;
            ((XYDiagram)xrChart1.Diagram).AxisX.Label.Font = new Font("Arial", 6, FontStyle.Regular);
            NumericScaleOptions numericScaleOptions = ((XYDiagram)xrChart1.Diagram).AxisY.NumericScaleOptions;
            numericScaleOptions.GridOffset = 0;
            numericScaleOptions.AutoGrid = false;
            numericScaleOptions.GridAlignment = NumericGridAlignment.Ones;
            numericScaleOptions.GridSpacing = 1;
            xrChart1.Series.AddRange(new Series[] { series, series2 });
        }
        catch (Exception ex)
        {
            logger.LogError($"An error occurred in _driftConflict_BeforePrint : " + ex.Message);
            throw;
        }
    }
    private void _serialNumber_BeforePrint(object sender, CancelEventArgs e)
    {
        XRTableCell cell = (XRTableCell)sender;

        cell.Text = SerialNumber.ToString();
        SerialNumber++;
    }
    private void _version_BeforePrint(object sender, CancelEventArgs e)
    {
        try
        {
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            _version.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
        }
        catch (Exception ex)
        {
            logger.LogError($"An error occurred in drift report version : " + ex.Message);
            throw;
        }
    }
}