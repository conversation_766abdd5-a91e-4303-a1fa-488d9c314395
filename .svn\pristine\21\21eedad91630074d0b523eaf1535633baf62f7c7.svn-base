using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class ApprovalMatrixApprovalRepository : BaseRepository<ApprovalMatrixApproval>, IApprovalMatrixApprovalRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public ApprovalMatrixApprovalRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.ProcessName.Equals(name))
            : Entities.Where(e => e.ProcessName.Equals(name)).ToList().Unique(id));
    }

    public async Task<List<ApprovalMatrixApproval>> GetApprovalMatrixByApprovalIds(List<string> approvalIds)
    {
        return await base.FilterBy(x => approvalIds.Contains(x.ApproverId)).ToListAsync();
    }

    public override async Task<PaginatedResult<ApprovalMatrixApproval>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<ApprovalMatrixApproval> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await Entities.Specify(productFilterSpec)
            .Where(x=>x.IsApproval && (x.CreatedBy.Equals(_loggedInUserService.UserId) || x.ApproverId.Equals(_loggedInUserService.UserId)))
            .DescOrderById()
            .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public async Task<List<ApprovalMatrixApproval>> GetUnapprovedByRequestId(string requestId)
    {
        return await FilterBy(x => x.RequestId.Equals(requestId) && !x.Status.Equals("Approved")).ToListAsync();
    }

}
