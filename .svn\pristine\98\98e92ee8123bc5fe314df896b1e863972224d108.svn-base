using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class AdPasswordExpireFixture : IDisposable
{
    public List<AdPasswordExpire> AdPasswordExpirePaginationList { get; set; }
    public List<AdPasswordExpire> AdPasswordExpireList { get; set; }
    public AdPasswordExpire AdPasswordExpireDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public AdPasswordExpireFixture()
    {
        var fixture = new Fixture();

        AdPasswordExpireList = fixture.Create<List<AdPasswordExpire>>();

        AdPasswordExpirePaginationList = fixture.CreateMany<AdPasswordExpire>(20).ToList();

        AdPasswordExpirePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AdPasswordExpirePaginationList.ForEach(x => x.IsActive = true);

        AdPasswordExpireList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        AdPasswordExpireList.ForEach(x => x.IsActive = true);

        AdPasswordExpireDto = fixture.Create<AdPasswordExpire>();
        AdPasswordExpireDto.ReferenceId = Guid.NewGuid().ToString();
        AdPasswordExpireDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
