﻿using AutoFixture;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Events.PaginatedView;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using Microsoft.Extensions.Primitives;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class AccessManagerControllerShould
    {
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private readonly Mock<ILogger<AccessManagerController>> _mockLogger = new();
        private readonly Mock<IPublisher> _publisherMock = new();
        private  AccessManagerController _controller;
        public const string UserId = "72216288-072f-4ed0-9848-a469cbcfabdc";

        public AccessManagerControllerShould()
        {
            
            Initialize();
        }
        internal void Initialize()
        {
            _controller = new AccessManagerController(
                _mockLogger.Object,
                _mapperMock.Object,
                _publisherMock.Object,
                _dataProviderMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_ReturnsViewWithModel_WhenSuccessful()
        {
            // Arrange
            var userRoleNames = new List<UserRoleNamesVm>
            {
                new UserRoleNamesVm { Id = "1", Role = "Admin" },
                new UserRoleNamesVm { Id = "2", Role = "User" }
            };

            _publisherMock
                .Setup(p => p.Publish(It.IsAny<AccessManagerPaginatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _dataProviderMock
                .Setup(dp => dp.UserRole.GetUserRoleNames())
                .ReturnsAsync(userRoleNames);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<AccessManagerViewModel>(result.Model);
            var model = result.Model as AccessManagerViewModel;
            Assert.NotNull(model.UserRoles);
            Assert.Equal(2, model.UserRoles.Count());
        }

        [Fact]
        public async Task List_ReturnsEmptyView_WhenExceptionOccurs()
        {
            // Arrange
            _publisherMock
                .Setup(p => p.Publish(It.IsAny<AccessManagerPaginatedEvent>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Model);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_CreatesNewAccessManager()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateAccessManagerCommand();
            var response = new BaseResponse { Success = true, Message = "Success" };

            _mapperMock.Setup(m => m.Map<CreateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidId_UpdatesAccessManager()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateAccessManagerCommand();
            var response = new BaseResponse { Success = true, Message = "Updated Successfully" };

            _mapperMock.Setup(m => m.Map<UpdateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateAccessManagerCommand();
            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mapperMock.Setup(m => m.Map<CreateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.CreateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateAccessManagerCommand();

            _mapperMock.Setup(m => m.Map<CreateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.CreateAsync(command))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }
        [Fact]
        public async Task GetUserByRole_ReturnsListOfUsersByRole_WhenSuccessful()
        {
            // Arrange
            var role = "Admin";
            var usersByRole = new List<UsersByUserRoleVm>
            {
                new UsersByUserRoleVm {Id = "1", LoginName = "User1"},
                new UsersByUserRoleVm {Id = "2", LoginName = "User2"}
            };

            _dataProviderMock.Setup(dp => dp.User.GetUserByRole(role))
                .ReturnsAsync(usersByRole);

            // Act
            var result = await _controller.GetUserByRole(role);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<UsersByUserRoleVm>>(result);
            Assert.Equal(2, result.Count);
        }

        [Fact]
        public async Task GetUserByRole_ReturnsEmptyList_WhenExceptionOccurs()
        {
            // Arrange
            var role = "Admin";

            _dataProviderMock.Setup(dp => dp.User.GetUserByRole(role))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetUserByRole(role);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<UsersByUserRoleVm>>(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetRoleDetails_ReturnsJsonResult_WhenSuccessful()
        {
            // Arrange
            var role = "Admin";
            var roleDetails = new GetByRoleIdVm { RoleName = role };

            _dataProviderMock.Setup(dp => dp.AccessManager.GetByUserRole(role))
                .ReturnsAsync(roleDetails);

            // Act
            var result = await _controller.GetRoleDetails(role) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task GetRoleDetails_ReturnsEmptyJson_WhenExceptionOccurs()
        {
            // Arrange
            var role = "Admin";

            _dataProviderMock.Setup(dp => dp.AccessManager.GetByUserRole(role))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetRoleDetails(role) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);
            Assert.Equal("", result.Value);
        }

        [Fact]
        public async Task GetUserInfraByUser_ReturnsUserInfraObject_WhenSuccessful()
        {
            // Arrange
            var userId = "123";
            var userInfraObject = new GetUserInfraObjectByUserIdVm
            {
                UserId = userId,
                Properties = "Test Properties"
            };

            _dataProviderMock.Setup(dp => dp.User.GetUserInfraObject(userId))
                .ReturnsAsync(userInfraObject);

            // Act
            var result = await _controller.GetUserInfraByUser(userId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<GetUserInfraObjectByUserIdVm>(result);
            Assert.Equal(userId, result.UserId);
        }

        [Fact]
        public async Task GetUserInfraByUser_ReturnsEmptyObject_WhenExceptionOccurs()
        {
            // Arrange
            var userId = "123";

            _dataProviderMock.Setup(dp => dp.User.GetUserInfraObject(userId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetUserInfraByUser(userId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<GetUserInfraObjectByUserIdVm>(result);
            Assert.Null(result.UserId);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidId_AndValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateAccessManagerCommand();
            var validationResult = new FluentValidation.Results.ValidationResult();
            validationResult.Errors.Add(new FluentValidation.Results.ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mapperMock.Setup(m => m.Map<UpdateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.UpdateAsync(command))
                .ThrowsAsync(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidId_AndGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateAccessManagerCommand();

            _mapperMock.Setup(m => m.Map<UpdateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.UpdateAsync(command))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as RedirectToActionResult;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_WithEmptyId_ReturnsCorrectJsonResponse()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateAccessManagerCommand();
            var response = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mapperMock.Setup(m => m.Map<CreateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.CreateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);

            var jsonString = JsonConvert.SerializeObject(result.Value);
            var jsonObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
            Assert.True((bool)jsonObject.Success);
            Assert.Equal("Created Successfully", (string)jsonObject.data);
        }

        [Fact]
        public async Task CreateOrUpdate_WithValidId_ReturnsCorrectJsonResponse()
        {
            // Arrange
            var accessManagerViewModel = new AutoFixture.Fixture().Create<AccessManagerViewModel>();
            var dic = new Dictionary<string, StringValues>();
            dic.Add("Id", "123");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateAccessManagerCommand();
            var response = new BaseResponse { Success = true, Message = "Updated Successfully" };

            _mapperMock.Setup(m => m.Map<UpdateAccessManagerCommand>(accessManagerViewModel))
                .Returns(command);

            _dataProviderMock.Setup(dp => dp.AccessManager.UpdateAsync(command))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(accessManagerViewModel) as JsonResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<JsonResult>(result);

            var jsonString = JsonConvert.SerializeObject(result.Value);
            var jsonObject = JsonConvert.DeserializeObject<dynamic>(jsonString);
            Assert.True((bool)jsonObject.Success);
            Assert.Equal("Updated Successfully", (string)jsonObject.data);
        }

        [Fact]
        public async Task List_ReturnsViewWithCorrectSelectListItems()
        {
            // Arrange
            var userRoleNames = new List<UserRoleNamesVm>
            {
                new UserRoleNamesVm { Id = "1", Role = "Admin" },
                new UserRoleNamesVm { Id = "2", Role = "User" }
            };

            _publisherMock
                .Setup(p => p.Publish(It.IsAny<AccessManagerPaginatedEvent>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _dataProviderMock
                .Setup(dp => dp.UserRole.GetUserRoleNames())
                .ReturnsAsync(userRoleNames);

            // Act
            var result = await _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<AccessManagerViewModel>(result.Model);
            var model = result.Model as AccessManagerViewModel;
            Assert.NotNull(model.UserRoles);
            Assert.Equal(2, model.UserRoles.Count());

            var firstRole = model.UserRoles.First();
            Assert.Equal("1", firstRole.Value);
            Assert.Equal("Admin", firstRole.Text);
        }
    }
}
