using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class CyberJobWorkflowSchedulerRepositoryTests : IClassFixture<CyberJobWorkflowSchedulerFixture>
{
    private readonly CyberJobWorkflowSchedulerFixture _cyberJobWorkflowSchedulerFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberJobWorkflowSchedulerRepository _repository;

    public CyberJobWorkflowSchedulerRepositoryTests(CyberJobWorkflowSchedulerFixture cyberJobWorkflowSchedulerFixture)
    {
        _cyberJobWorkflowSchedulerFixture = cyberJobWorkflowSchedulerFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberJobWorkflowSchedulerRepository(_dbContext);
    }

    #region GetCyberJobWorkflowSchedulerByJobId Tests

    [Fact]
    public async Task GetCyberJobWorkflowSchedulerByJobId_ShouldReturnSchedulerForJob()
    {
        // Arrange
        var jobId = "4c89cb4e-394a-4f94-b528-f188f476feb6";
        var schedulers = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerList;
        schedulers[0].JobId = jobId;
        schedulers[0].Name = "Scheduler1";
        schedulers[0].Status = "Completed";
              
        await _repository.AddRangeAsync(schedulers);

        // Act
        var result = await _repository.GetCyberJobWorkflowSchedulerByJobId(jobId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(jobId, result.JobId);
        Assert.Equal("Scheduler1", result.Name);
        Assert.Equal("Completed", result.Status);
    }

    [Fact]
    public async Task GetCyberJobWorkflowSchedulerByJobId_ShouldReturnNull_WhenJobNotFound()
    {
        // Arrange
        var schedulers = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerList;
        await _repository.AddRangeAsync(schedulers);

        // Act
        var result = await _repository.GetCyberJobWorkflowSchedulerByJobId("NON_EXISTENT_JOB");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetCyberJobWorkflowSchedulerPagination Tests

    [Fact]
    public async Task GetCyberJobWorkflowSchedulerPagination_ShouldReturnInRangePaginatedResults()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var schedulers =_cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerPaginationList;
        schedulers.ForEach(x => x.StartTime = baseDate.AddDays(-325));
        schedulers[0]. StartTime = baseDate.AddDays(-3);
        schedulers[0]. EndTime = baseDate.AddDays(-3).AddHours(2);
        schedulers[1]. StartTime = baseDate.AddDays(-1);
        schedulers[1].EndTime = baseDate.AddDays(-1).AddHours(1);
        schedulers[2].StartTime = baseDate.AddDays(-15);
        schedulers[2].EndTime = baseDate.AddDays(-15).AddHours(1);
           

        await _repository.AddRangeAsync(schedulers);

        var specification = new CyberJobWorkflowSchedulerFilterSpecification("");

        // Act
        var result = await _repository.GetCyberJobWorkflowSchedulerPagination(1, 10, specification, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Data.Count); 
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalCount);
    }

    [Fact]
    public async Task GetCyberJobWorkflowSchedulerPagination_ShouldReturnEmpty_WhenNoRecordsInDateRange()
    {
        // Arrange
        var schedulers = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerList;
        await _repository.AddRangeAsync(schedulers);

        var futureStartDate = DateTime.Now.AddDays(10).ToString("yyyy-MM-dd");
        var futureEndDate = DateTime.Now.AddDays(15).ToString("yyyy-MM-dd");
        var specification = new CyberJobWorkflowSchedulerFilterSpecification("");

        // Act
        var result = await _repository.GetCyberJobWorkflowSchedulerPagination(1, 10, specification, futureStartDate, futureEndDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
    }

    #endregion

    

    #region Infrastructure Assignment Tests

    [Fact]
    public async Task Repository_ShouldHandleJobAndWorkflow_For_Filter_Assignments()
    {
        // Arrange
        var schedulers = _cyberJobWorkflowSchedulerFixture.CyberJobWorkflowSchedulerList;

        schedulers[0].JobId = "DB-0d029a9f-1c44-488c-8c80-72be44db8138-123";
        schedulers[0].Status = "Completed";

        schedulers[1].WorkflowId = "D8-0d029a9f-1c44-488c-8c80-72be44db8138-487";
        schedulers[1].ScheduleType= 1;
        schedulers[1].CurrentActionId= "0d029a9f-488c-8c80-72be44db8138";
        
        await _repository.AddRangeAsync(schedulers);

        // Act
        var byJobId = await _repository.FindByFilterAsync(x => x.JobId.Contains("DB-0d029a9f-1c44-488c-8c80-72be44db8138-123"));
        var byWorkflowId = await _repository.FindByFilterAsync(x => x.WorkflowId.Contains("D8-0d029a9f-1c44-488c-8c80-72be44db8138-487"));
 

        // Assert
        Assert.Single(byJobId);
        Assert.Equal("Completed", byJobId.First().Status);
        Assert.Contains("DB-0d029a9f-1c44-488c-8c80-72be44db8138-123", byJobId.First().JobId);

        Assert.Single(byWorkflowId);
        Assert.Contains("D8-0d029a9f-1c44-488c-8c80-72be44db8138-487", byWorkflowId.First().WorkflowId);

        Assert.Equal(1,byWorkflowId.First().ScheduleType);

    }

    #endregion
}
