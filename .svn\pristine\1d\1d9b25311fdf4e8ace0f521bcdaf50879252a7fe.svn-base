﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class BusinessServiceService : BaseClient, IBusinessServiceService
{
    public BusinessServiceService(IConfiguration config, IAppCache cache, ILogger<BusinessServiceService> logger)
        : base(config, cache, logger)
    {
    }

    public async Task<List<BusinessServiceNameVm>> GetBusinessServiceNames()
    {
        var request = new RestRequest("api/v6/businessservices/names");

        return await Get<List<BusinessServiceNameVm>>(request);

    }

    public async Task<bool> IsBusinessServiceNameExist(string name, string id)
    {
        var request = new RestRequest($"api/v6/businessservices/name-exist?businessServiceName={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateBusinessServiceCommand businessService)
    {
        var request = new RestRequest("api/v6/businessservices", Method.Post);

        request.AddJsonBody(businessService);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateBusinessServiceCommand businessService)
    {
        var request = new RestRequest("api/v6/businessservices", Method.Put);

        request.AddJsonBody(businessService);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/businessservices/{businessServiceId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BusinessServiceDetailVm> GetByReferenceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/businessservices/{businessServiceId}");

        return await Get<BusinessServiceDetailVm>(request);
    }

    public async Task<PaginatedResult<BusinessServiceListVm>> GetBusinessServicePaginatedList(GetBusinessServicePaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/businessservices/paginated-list");

        return await Get<PaginatedResult<BusinessServiceListVm>>(request);
    }

    public async Task<GetBusinessServiceDiagramDetailVm> GetBusinessServiceDiagramByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/businessservices/businessservicediagram?businessServiceId={businessServiceId}");

        return await Get<GetBusinessServiceDiagramDetailVm>(request);
    }

    public async Task<List<BusinessServiceListVm>> GetBusinessServiceList()
    {
        var request = new RestRequest("api/v6/businessservices");

        return await Get<List<BusinessServiceListVm>>(request);
    }

    public Task<BusinessServiceDetailVm> GetByBusinessServiceName(string businessServiceName)
    {
        throw new NotImplementedException();
    }
}