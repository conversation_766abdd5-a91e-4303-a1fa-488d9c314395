﻿const serverMappingURL = {
    getPagination: "/Admin/ServerMapping/GetPagination",
    isNameExits: "Admin/ServerMapping/servertypeIsNameExist",
    isNamesubExits: "Admin/ServerMapping/serversubtypeIsNameExist",
    getServerType: "Admin/ServerMapping/GetServerTypeList",
    serverTypeCreate: "Admin/ServerMapping/serverTypeCreateOrUpdate",
    serversubTypeDelete: "Admin/ServerMapping/serversubTypeDelete",
    ServerSubTypeCreateOrUpdate: "Admin/ServerMapping/ServerSubTypeCreateOrUpdate",
    serverTypeDelete: "Admin/ServerMapping/serverTypeDelete"
};

let globalroleId = ""
let globalroledeleteid = ""
let globalId = ""
let globaldeleteid = ""
let selectedValues = [];
let dataTable = "";

$(function () {
    dataTable = $('#serverMappingTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": serverMappingURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = d?.order[0]?.column || '';
                    let sortValue = sortIndex === 1 ? "name" : sortIndex === 2 ? "serverTypeName" : "";
                    let orderValue = d?.order[0]?.dir || 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#searchInputSM').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    if (json?.success) {
                        const { totalPages, totalCount, data } = json?.data;
                        json.recordsTotal = totalPages;
                        json.recordsFiltered = totalCount;
                        $(".pagination-column").toggleClass("disabled", data?.length === 0);
                        return data;
                    } else {
                        errorNotification(result)
                    }
                }
            },
            "columnDefs": [
                {
                    "targets": [1, 2, 3],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            let page = meta.settings._iDisplayStart / meta.settings._iDisplayLength;
                            return (page * meta.settings._iDisplayLength) + meta.row + 1;
                        }
                        return data;
                    },
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`
                    }
                },
                {
                    "data": "serverTypeName", "name": "Server Role", "autoWidth": true,
                    "render": function (data, type, row) {
                        return `<td><span title="${data == null ? "NA" : data}"> ${data == null ? "NA" : data}</span></td>`;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        return `<td> 
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="edit_businessService-button" name="overallupdate" updateId="${row.id}" roletype="${row.name}" serverrole="${row.serverTypeId}" onclick="overall_Edit(this)" data-bs-toggle="modal" data-bs-target="#createModal">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-bservice-button" deletename='${row.name}' deleteId='${row.id}' onclick="overalldeleteBtn(this)" data-bs-toggle="modal" data-bs-target="#deleteModalSM">
                                    <i class="cp-Delete"></i>
                                </span>
                            </div>
                        </td>`;
                    },
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        }
    )

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#searchInputSM').on('keydown', function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });

    $('#searchInputSM').on('input', servertypedebounce(function (e) {
        let $searchInput = $('#searchInputSM');
        const nameCheckbox = $("#nameSM");
        const serverRole = $("#serverRoleSM");
        let sanitizedValue = $searchInput.val().replace(/^\s/, '').replace(/\s+/g, ' ').replace(/\s$/, ' ');
        if (sanitizedValue.trim() === "") {
            $searchInput.val("");
            sanitizedValue = "";
        } else {
            $searchInput.val(sanitizedValue);
        }

        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + sanitizedValue);
        }
        if (serverRole.is(':checked')) {
            selectedValues.push(serverRole.val() + sanitizedValue);
        }
        let currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {
                if (e.target.value && json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }
    }));

    $("#serverTypeName").on("input", servertypedebounce(async function () {
        const inputElement = $("#serverTypeName");
        const value = await sanitizeInput(inputElement.val());
        inputElement.val(value);
        await validatesubtypename(value, "Enter type", $("#serverTypeNameError"), serverMappingURL.isNamesubExits);
    }));

    $("#serverTypeRole").on("change", function () {
        validaterole($(this).val(), "Select server role", $("#serverTypeRoleError"))
    })

    $("#createModalButton").on("click", function () {
        $("#saveButtonSM").text("save")
        $("#serverTypeName").val("")
        $("#serverTypeRole").val("").trigger("change")
        $('#serverTypeNameError, #serverTypeRoleError').text("").removeClass('field-validation-error');
    })

    $("#confirmDeleteBtnSM").on("click", async function () {
        await $.ajax({
            type: "POST",
            url: RootUrl + serverMappingURL.serversubTypeDelete,
            dataType: "json",
            data: {
                serverSubTypeId: globaldeleteid,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result?.success) {
                    let data = result?.data
                    $('#deleteModalSM').modal('hide');
                    notificationAlert("success", data?.message)
                    dataTable.ajax.reload()
                } else {
                    errorNotification(result)
                }
            },
        })
    })

    $("#saveButtonSM").on("click", async function () {
        let form = $("#createModal")
        let $serverTypeRole = $("#serverTypeRole option:selected");
        let serverTyperoleId = $serverTypeRole.val()
        let serverTyperoleName = $serverTypeRole.text()
        let serverTypeName = $("#serverTypeName").val()
        let serverTypeNamevalidation = await validatesubtypename(serverTypeName, "Enter type", $('#serverTypeNameError'), serverMappingURL.isNamesubExits)
        let serverTypeRolevalidation = validaterole(serverTyperoleId, "Select server role", $('#serverTypeRoleError'))

        if (serverTypeNamevalidation && serverTypeRolevalidation) {
            form.trigger("submit")
            let data = {
                "ServerTypeId": serverTyperoleId,
                "serverTypeName": serverTyperoleName,
                "Name": serverTypeName,
                __RequestVerificationToken: gettoken()
            }
            $('#saveButtonSM').text() === "Update" ? data["id"] = globalId : null

            await $.ajax({
                type: "POST",
                url: RootUrl + serverMappingURL.ServerSubTypeCreateOrUpdate,
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result?.success) {
                        let data = result?.data
                        $("#createModal").modal("hide")
                        notificationAlert("success", data?.message)
                        dataTable.ajax.reload()
                    } else {
                        errorNotification(result)
                    }
                },
            })
            $("#serverTypeName").val("")
            $("#serverTypeRole ").val("").trigger("change")
            $('#saveButtonSM').text("Save")
            serversubtypeModalerror()
        }
    });

    $("#rolename").on("input", servertypedebounce(async function () {
        let $roleName = $("#rolename")
        let value = await sanitizeInput($roleName.val());
        $roleName.val(value);
        validatename(value, "Enter role name", $("#roleNameError"), serverMappingURL.isNameExits)
    }));

    $("#serverRoleModalButton").on("click", function () {
        $("#rolename").val("")
        $('#roleNameError').text("").removeClass("field-validation-error")
    });

    $(".roleCloseButtonSM").on("click", function () {
        $("#serverRoleModal").modal("hide")
        $("#createModal").modal("show")
    })

    $("#roleNameSaveButtonSM").on("click", async function () {
        let form = $("#serverRoleModal")
        let RoleName = $("#rolename").val()
        let serverroleNamevalidation = await validatename($("#rolename").val(), "Enter role name", $('#roleNameError'), serverMappingURL.isNameExits)

        if (serverroleNamevalidation) {
            form.trigger("submit")
            let data = {
                "name": RoleName,
                __RequestVerificationToken: gettoken()
            }
            $('#roleNameSaveButtonSM').text() === "Update" ? data["id"] = globalroleId : null

            await $.ajax({
                type: "POST",
                url: RootUrl + serverMappingURL.serverTypeCreate,
                dataType: "json",
                data: data,
                success: function (result) {
                    if (result?.success) {
                        let data = result?.data
                        notificationAlert("success", data?.message)
                        getserverrolename()
                    } else {
                        errorNotification(result)
                    }
                },
            })
            $("#rolename").val("")
            $("#roleNameSaveButtonSM").text("Save")
            rolenameerrorelement()
        }
    })

    $("#confirmRoleDeleteBtnSM").on("click", async function () {
        await $.ajax({
            type: "POST",
            url: RootUrl + serverMappingURL.serverTypeDelete,
            dataType: "json",
            data: {
                serverTypeId: globalroledeleteid,
                __RequestVerificationToken: gettoken()
            },
            success: function (result) {
                if (result?.success) {
                    let data = result?.data
                    $('#roleNameDeleteModal').modal('hide');
                    $("#serverRoleModal").modal("show")
                    notificationAlert("success", data?.message)
                    getserverrolename()
                } else {
                    errorNotification(result)
                }
            },
        })
    })
});

function servertypedebounce(func, timeout = 300) {
    let timer;
    return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
    };
}

async function validatename(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.serverType = value

        const validationResults = [
            //SpecialCharValidate(value),
            SpecialCharValidateCustom(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSameNameExist(url, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}

async function IsSameNameExist(url, inputValue) {
    return !inputValue.serverType.trim() || $("#roleNameSaveButtonSM").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}

async function validatesubtypename(value, errorMsg, errorElement, url) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        var url = RootUrl + url;
        var data = {};
        data.id = null;
        data.subType = value

        const validationResults = [
            //SpecialCharValidate(value),
            SpecialCharValidateCustom(value),
            value.charAt(0) == "_" ? ShouldNotBeginWithUnderScore(value) : value.trim() == '' ? ShouldNotBeginWithSpace(value) :
                OnlyNumericsValidate(value),
            minMaxlength(value),
            ShouldNotBeginWithSpace(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            secondChar(value),
            await IsSamesubtypeNameExist(url, data)
        ];
        const failedValidations = validationResults.filter(result => result !== true);

        if (failedValidations.length > 0) {
            errorElement.text(failedValidations[0]).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
}

async function IsSamesubtypeNameExist(url, inputValue) {
    return !inputValue.subType.trim() || $("#saveButtonSM").text() == "Update" ? true : (await GetAsync(url, inputValue, OnError)) ? " Name already exists" : true;
}

function validaterole(value, errorMsg, errorElement) {
    if (!value || value.length === 0) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    } else {
        errorElement.text("").removeClass('field-validation-error');
        return true;
    }
}

function serversubtypeModalerror() {
    $('#serverTypeNameError, #serverTypeRoleError').text("").removeClass('field-validation-error');
}

function overall_Edit(data) {
    globalId = $(data).attr("updateId")
    $("#saveButtonSM").text("Update")
    $("#serverTypeName").val($(data).attr("roletype"))
    $("#serverTypeRole").val($(data).attr("serverrole"))
}

function overalldeleteBtn(data) {
    globaldeleteid = $(data).attr("deleteId")
    $("#deleteData").text($(data).attr('deletename'))
    $("#deleteData").attr("title", $(data).attr('deletename'));
}

async function getserverrolename() {
    $("#roleNametable tbody ").empty()
    await $.ajax({
        type: "GET",
        url: RootUrl + serverMappingURL.getServerType,
        dataType: "json",
        success: function (result) {
            if (result?.success) {
                let data = result?.data
                $("#serverTypeRole").empty()
                data.forEach(function (item, i) {
                    $("#serverTypeRole").append('<option value=""></option>')
                    $("#serverTypeRole").append('<option value="' + item.id + '">' + item.name + '</option>')
                    let sno = i + 1
                    $("#roleNametable tbody ").append('<tr>' +
                        '<td>' + sno + '</td>' +
                        '<td title="' + item.name + '">' + item.name + '</td>' +
                        '<td><div class="d-flex align-items-center  gap-2"><span role="button" title ="Edit" onclick="rolenameedit(this)" rolename="' + item.name + '" updateId=' + item.id + '  class= "edit_businessService-button"><i class="cp-edit"></i></span><span role="button" title="Delete" deleteId="' + item.id + '" deleted_name="' + item.name + '" onclick="rolenamedeleteBtn(this)" class="delete-bservice-button" data-bs-toggle="modal" data-bs-target="#"><i class="cp-Delete"></i></span></div></td>' +
                        '</tr>')
                })
            } else {
                errorNotification(result)
            }
        },
    })
}

getserverrolename();
function rolenameedit(data) {
    globalroleId = $(data).attr("updateId")
    $("#rolename").val($(data).attr("rolename"))
    $("#roleNameSaveButtonSM").text("Update")
}

function rolenameerrorelement() {
    $("#rolename").val("")
    $('#roleNameError').text("").removeClass("field-validation-error")
}

function rolenamedeleteBtn(data) {
    $('#roleNameDeleteModal').modal('show');
    globalroledeleteid = $(data).attr('deleteId')
    const deletedName = $(data).attr('deleted_name');
    $("#roleNameSM").text(deletedName).attr("title", deletedName);
}
