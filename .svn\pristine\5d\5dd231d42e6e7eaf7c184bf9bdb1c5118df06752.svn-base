﻿using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;
[ApiVersion("6")]
public class CyberJobWorkflowSchedulerController : CommonBaseController
{
    [AllowAnonymous]
    [Route("paginated-list"), HttpGet]
    //[Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<PaginatedResult<CyberJobWorkflowSchedulerListVm>>> GetPaginatedCyberJobWorkflowScheduler([FromQuery] GetCyberJobWorkflowSchedulerPaginatedListQuery query)
    {
        Logger.LogDebug("Get Details in CyberJobWorkflowScheduler Paginated List");

        return Ok(await Mediator.Send(query));

    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }

}
