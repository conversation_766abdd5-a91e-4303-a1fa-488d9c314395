﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;

public class GetMongoDbMonitorStatusPaginatedListQueryHandler : IRequestHandler<
    GetMongoDbMonitorStatusPaginatedListQuery, PaginatedResult<MongoDbMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMongoDbMonitorStatusRepository _mongoDbMonitorStatusRepository;

    public GetMongoDbMonitorStatusPaginatedListQueryHandler(IMapper mapper,
        IMongoDbMonitorStatusRepository mongoDbMonitorStatusRepository)
    {
        _mapper = mapper;
        _mongoDbMonitorStatusRepository = mongoDbMonitorStatusRepository;
    }

    public async Task<PaginatedResult<MongoDbMonitorStatusListVm>> Handle(
        GetMongoDbMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _mongoDbMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new MongoDbMonitorStatusFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MongoDbMonitorStatusListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}