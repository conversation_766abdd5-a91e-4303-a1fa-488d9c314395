﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@{
    ViewData["Title"] = "List";
}
@using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel

<link href="~/css/switch_slide_toggle.css" rel="stylesheet" />

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">

        <div class="">
            <div class="d-flex align-items-center justify-content-between flex-wrap card-header">
                <div class="d-flex align-items-center">
                    <h6 class="page_title">
                        <i class="cp-user-privileges"></i>
                        <span>User Privileges</span>
                    </h6>
                </div>
                <form class="d-flex align-items-center">
                    <div class="input-group me-2 w-auto">
                        <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                        <div class="input-group-text">
                            <div class="dropdown" title="Filter">
                                <span data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="cp-filter"></i>
                                </span>
                                <ul class="dropdown-menu filter-dropdown">
                                    <li>
                                        <h6 class="dropdown-header">Filter Search</h6>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="accessProperties=" id="Name">
                                            <label class="form-check-label" for="Name">
                                                Privilege
                                            </label>
                                        </div>
                                    </li>
                                    <li class="dropdown-item">
                                        <div>
                                            <input class="form-check-input" type="checkbox" value="userproperties=" id="User">
                                            <label class="form-check-label" for="User">
                                                User
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" id="btnCreate" data-bs-target="#CreateModal"><i class="cp-add me-1"></i>Create</button>
                </form>
            </div>
        </div>
        <div class="card-body pt-0">
            <table class="table table-hover dataTable" style="width:100%" id="user_privilege">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Privilege</th>
                        <th>Users</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="CreateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <Form id="userPrivilege">
            <div class="modal-dialog modal-dialog-centered  modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="page_title"><i class="cp-user-privileges"></i><span>User Privilege Configuration </span></h6>
                        <button type="button" title="Close" class="btn-close" id="close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <div class="d-flex gap-4 flex-wrap" id="div-custom">
                                <div class="position-relative">
                                    <input class="btn-check" type="radio" name="type" id="Profile" value="Profile" data-toggle="button" autocomplete="off" />
                                    <label class="site_type btn border-secondary-subtle" for="Profile">
                                        <i class="cp-workflow-profile fs-1"></i><br />
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate"> Workflow Profile </div>
                                </div>
                                <div class="position-relative">
                                    <input class="btn-check" type="radio" name="type" id="Workflow_List" value="Workflow" data-toggle="button" autocomplete="off" />
                                    <label class="site_type btn border-secondary-subtle" for="Workflow_List">
                                        <i class="cp-workflow fs-1"></i><br />
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px">Workflow</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="Profile_input">
                            <div class="form-label">Workflow Profile List</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-workflow-profile"></i></span>
                                <select class="form-select-modal" id="Profilelist" data-placeholder="Select Workflow Profile List" multiple>
                                </select>
                            </div>
                            <span id="ProfileError"></span>
                        </div>
                        <div class="form-group" id="Workflow_List_input">
                            <div class="form-label" title="Starts at">Workflow List</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-workflow"></i></span>
                                <select class="form-select-modal" id="Workflowlist" data-placeholder="Select Workflow List" multiple>
                                </select>
                            </div>
                            <span id="Workflow_List-Error"></span>
                        </div>
                        <div class="form-group mt-3" id="Radio">
                            <div class="form-check form-check-inline" id="user_list">
                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="Userclick" value="Users">
                                <label class="form-check-label" for="UserList">User List</label>
                            </div>
                            <div class="form-check form-check-inline" id="user_group">
                                <input class="form-check-input" type="radio" name="inlineRadioOptions" id="UserGroupclick" value="UserGroup">
                                <label class="form-check-label" for="UserGroup">User Group</label>
                            </div>
                            <span id="permission-radio-error"></span>
                        </div>
                        <div class="form-group" id="Username_dropdowm">
                            <div class="form-label">User List</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user"></i></span>
                                <select class="form-select-modal" id="Userlist" data-placeholder="Select User List" multiple>
                                    <option value=""></option>
                                </select>
                            </div>
                            <span id="UserError"></span>
                        </div>
                        <div class="form-group" id="UserGroup_dropdown">
                            <div class="form-label" title="Starts at">User Group</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-LDAP"></i></span>
                                <select class="form-select-modal" id="UserGroupList" data-placeholder="Select User Group" multiple>
                                    <option value=""></option>
                                </select>
                            </div>
                            <span id="UserGroupError"></span>
                        </div>
                        <div class="switches-container mb-3" style="width:190px">
                            <input type="radio" id="switchMonthly" name="switchPlan" value="Temporary" checked="checked" />
                            <input type="radio" id="switchYearly" name="switchPlan" value="Permanent" />
                            <label for="switchMonthly">Temporary</label>
                            <label for="switchYearly">Permanent</label>
                            <div class="switch-wrapper">
                                <div class="switch">
                                    <div>Temporary</div>
                                    <div style="width:96%">Permanent</div>
                                </div>
                            </div>
                        </div>
                        <div class="month" id="monthgroup">
                            <div class="d-flex gap-3">
                                <div class="form-group w-50">
                                    <div class="form-label">Start Date</div>
                                    <div class="input-group"><span class="input-group-text"> <i class="cp-calendar"></i></span> <input id="startDateInput" class="form-control datetimeCron" placeholder="Schedule Time" type="datetime-local" name="sdate" cursorshover="true"></div>
                                    <span id="Startdate-error"></span>
                                </div>
                                <div class="form-group w-50">
                                    <div class="form-label">End Date</div>
                                    <div class="input-group"><span class="input-group-text"> <i class="cp-calendar"></i></span> <input id="endDateInput" class="form-control datetimeCron" placeholder="Schedule Time" type="datetime-local" name="edate" cursorshover="true"></div>
                                    <span id="Enddate-error"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-label">Description <small class="text-secondary">( Optional )</small></div>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="cp-description"></i>
                                </span>
                                <input autocomplete="off" class="form-control" type="text" id="Description" maxlength="250" placeholder="Enter Description">
                            </div>
                            <span id="Description-error"></span>
                        </div>
                    </div>
                    <div class="modal-footer d-flex justify-content-between">
                        <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                        <div class="gap-2 d-flex">
                            <button type="button" class="btn btn-secondary btn-sm" id="Cancel" data-bs-dismiss="modal" cursorshover="false">Cancel</button>
                            <button type="button" class="btn btn-primary btn-sm" id="SaveFunction" cursorshover="false">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    </div>
</div>
<div id="orchestrationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.CreateAndEdit" aria-hidden="true"></div>
<div id="orchestrationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.Delete" aria-hidden="true"></div>

<script src="~/js/itautomation/userprivileges/userprivilege.js"></script>
<script src="~/js/slide_toggle.js"></script>
<script src="~/lib/moment/jquerymoment.js"></script>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<!-- delete -->

<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="delete" />
</div>




