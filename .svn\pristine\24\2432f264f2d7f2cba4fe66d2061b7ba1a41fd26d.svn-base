﻿using ContinuityPatrol.Application.Features.Database.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Queries;

public class GetDatabaseNameUniqueQueryHandlerTests : IClassFixture<DatabaseFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private Mock<IDatabaseRepository> _mockDatabaseRepository;

    private readonly GetDatabaseNameUniqueQueryHandler _handler;

    public GetDatabaseNameUniqueQueryHandlerTests(DatabaseFixture databaseFixture)
    {
        _databaseFixture = databaseFixture;

        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseNameUniqueRepository(_databaseFixture.Databases);

        _handler = new GetDatabaseNameUniqueQueryHandler(_mockDatabaseRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_DatabaseName_Exist()
    {
        _databaseFixture.Databases[0].Name = "Cloudant";
        _databaseFixture.Databases[0].IsActive = true;

        var result = await _handler.Handle(new GetDatabaseNameUniqueQuery { DatabaseName = _databaseFixture.Databases[0].Name, DatabaseId = _databaseFixture.Databases[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_DatabaseNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetDatabaseNameUniqueQuery { DatabaseName = "Data_Maria", DatabaseId = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_DatabaseName_NotMatch()
    {
        var result = await _handler.Handle(new GetDatabaseNameUniqueQuery { DatabaseName = "Exchangedag", DatabaseId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsDatabaseNameExist_OneTime()
    {
        await _handler.Handle(new GetDatabaseNameUniqueQuery(), CancellationToken.None);

        _mockDatabaseRepository.Verify(x => x.IsDatabaseNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseEmptyRepository();

        var result = await _handler.Handle(new GetDatabaseNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}