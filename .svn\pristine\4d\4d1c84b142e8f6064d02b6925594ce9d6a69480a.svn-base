﻿namespace ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;

public class GetDrReadinessByBusinessServiceVm
{
    public int TotalBusinessServiceCount { get; set; }
    public int TotalDrReadyInfraObjectCount { get; set; }
    public int RelatedServiceCount { get; set; }
    public int TotalOrchestrationCount { get; set; }
    public Components Components { get; set; } = new();
    public Orchestration Orchestration { get; set; } = new();
    public RelatedService RelatedService { get; set; } = new();
}

public class Components
{
    public int ServerDownCount { get; set; }
    public int DatabaseDownCount { get; set; }
    public int DataLagDownCount { get; set; }
    public int WorkflowNotConfiguredCount { get; set; }
}

public class Orchestration
{
    public int TotalDrReadyCount { get; set; }
    public List<GetTotalDrReadyVm> GetTotalDrReadyVms { get; set; } = new();
    public int ExecutedErrorCount { get; set; }
    public List<GetDrReadyErrorExecutionVm> GetErrorExecutions { get; set; } = new();
    public List<GetWorkflowNotConfiguredVm> GetWorkflowNotConfiguredVms { get; set; } = new();
}

public class RelatedService
{
    public int RelatedServiceErrorCount { get; set; }
    public int RelatedServiceNotReadyCount { get; set; }
    public List<RelatedServiceDto> RelatedErrorService { get; set; } = new();
    public List<RelatedServiceDto> RelatedNotReadyService { get; set; } = new();
}

public class ServiceProperties
{
    public string Type { get; set; }
    public string IpAddress { get; set; }
    public string ServiceName { get; set; }
    public string Status  { get; set; }
}


public class RelatedServiceDto
{
    public string Id { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ServicePath { get; set; }
    public string Type { get; set; }
    public string ThreadType { get; set; }
    public string Status { get; set; }
    public string WorkflowType { get; set; }
    public string IsServiceUpdate { get; set; }
    public List<ServiceProperties> ServiceProperties { get; set; } = new();
}

public class GetDrReadyErrorExecutionVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string FailedActionId { get; set; }
    public string FailedActionName { get; set; }
    public string ErrorMessage { get; set; }
    public string WorkflowStatus { get; set; }
}

public class GetTotalDrReadyVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ErrorMessage { get; set; }
}

public class GetWorkflowNotConfiguredVm
{
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string ErrorMessage { get; set; }
}