using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowRunningActionFixture : IDisposable
{
    public List<WorkflowRunningAction> WorkflowRunningActionPaginationList { get; set; }
    public List<WorkflowRunningAction> WorkflowRunningActionList { get; set; }
    public WorkflowRunningAction WorkflowRunningActionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowRunningActionFixture()
    {
        var fixture = new Fixture();

        WorkflowRunningActionList = fixture.Create<List<WorkflowRunningAction>>();

        WorkflowRunningActionPaginationList = fixture.CreateMany<WorkflowRunningAction>(20).ToList();

        WorkflowRunningActionDto = fixture.Create<WorkflowRunningAction>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
