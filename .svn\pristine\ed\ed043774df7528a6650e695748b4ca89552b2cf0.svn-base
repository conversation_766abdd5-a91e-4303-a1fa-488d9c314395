let createPermission = $("#airGapCreate").data("create-permission")?.toLowerCase();
let deletePermission = $("#airGapDelete").data("delete-permission")?.toLowerCase();
let isEdit = false;
let sourceData = [];
let targetData = [];

////----pagination--->
$(function () {
    //btnCrudEnable('confirmDeleteButton');
    getCyberComponent();
    if (createPermission == 'false') {
        $("#airGapCreateButton").removeClass('#airGapCreateButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    let selectedValues = [];
    let dataTable = $('#airGap_table').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous" ></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": "/CyberResiliency/AirGap/GetPagination",
                "dataType": "json",
                "data": function (d) {
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.data?.totalPages;
                    json.recordsFiltered = json?.data?.totalCount;

                    if (json?.data && json?.data?.data && json?.data?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data?.data;
                },
                "error": function (xhr, status, error) {
                    if (error?.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },
            },

            "columns": [
                {
                    "data": null, "name": "Sr. No.", "autoWidth": true,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                    "orderable": false,
                },

                {
                    "data": "name", "name": "Airgap Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<div>
                               
                                <label class="form-check-label" for="flexCheckDefault">
                                    ${data || 'NA'}
                                </label>
                            </div>`
                        }
                        return data;
                    }
                },
                //<input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                //{
                //    "data": "description", "name": "Description", "autoWidth": true,
                //    "render": function (data, type, row) {

                //        if (data) {
                //            if (type === 'display') {
                //                return '<span  title="' + (data || 'NA') + '" class="text-truncate" style="max-width:450px;display:inline-block">' + (data || 'NA') + '</span>';
                //            }
                //            return data;
                //        }

                //        return '<span title="NA">NA</span>';
                //    }
                //},
                //{
                //    "data": "port", "name": "Port", "autoWidth": true,
                //    "render": function (data, type, row) {
                //        if (type === 'display') {
                //            return '<span>' + data + '</span>';
                //        }
                //        return data;
                //    }
                //},
                {
                    "data": "port", "name": "Port", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span>${data || 'NA'}</span>`;
                        }
                        return data;
                    }
                },
                {
                    "data": "status", "name": "Status", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return `<span title=${data || 'NA'}><i class="cp-air-gap text-${data?.toLowerCase() == 'open' ? 'success' : 'danger'}"></i></span>`;
                        }
                        return data;
                    }
                },
                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        if (createPermission === 'true' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-airgap='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="delete-button" data-airGap-id="${row?.id}" data-airGap-name="${row?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>
                               
                            </div>`;

                        }
                        else if (createPermission === 'true' && deletePermission === "false") {
                            return `
                             <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="edit-button" data-airgap='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                 <span role="button" title="Delete" class="delete-button" data-airGap-id="${row?.id}" data-airGap-name="${row?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else if (createPermission === 'false' && deletePermission === "false") {
                            return `
                            <div class="d-flex align-items-center gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                 <span role="button" title="Delete" class="icon-disabled">
                                    <i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                    }
                }
            ],
            "columnDefs": [
                { "targets": [1], "className": "truncate" }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            }
        });

    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const AirGapNameCheckbox = $("#Name");
        const inputValue = $('#search-inp').val();

        if (AirGapNameCheckbox.is(':checked')) {
            selectedValues.push(AirGapNameCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {
            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    $('#search-inp').attr('autocomplete', 'off');

    $('#btnSave').on('click', commonDebounce(async function () {
        let textName = $('#btnSave').text()
        let airGapId = $('#btnSave').attr("updateId")
        $('#Port').val('1')
        let nameValidation;
        let data = [
            { id: 'airGapName', message: 'Enter airgap name' },
            // { id: 'Port', message: 'Enter port' },
            { id: 'sourceComponent', message: 'Select source zone' },
            { id: 'sourceServer', message: 'Select source host' },
            { id: 'targetComponent', message: 'Select target zone' },
            { id: 'targetServer', message: 'Select target host' },
            //{ id: 'enable-workflow', message: 'Select enable workfow' },
            //{ id: 'disable-workflow', message: 'Select disable workfow' }
        ];
        let formIsValid = true;
        data.length && data.forEach((item) => {
            let value = $(`#${item?.id}`).val();
            if (!value || !value?.length) {
                $(`#${item?.id}-error`).text(item?.message).addClass('field-validation-error')
                formIsValid = false
            }
        })

        //if ($('#targetServer').val()?.length > $('#sourceServer')?.val()?.length) {
           // $('#targetServer-error').text('Target host switches must match the source host switches').addClass('field-validation-error')
           // formIsValid = false;
       // } else if ($('#targetServer').val()?.length !== 0) {
         //   $('#targetServer-error').text('').removeClass('field-validation-error')
        // }

        let isPort = $('#portContainer').children().length

        if (!isPort) {
            $('#Port-error').text('Enter port').addClass('field-validation-error')
            formIsValid = false;
        } else {
            let portValueList = ''
            $('#Port-error').text('').removeClass('field-validation-error')
            $('#portContainer .portValue').each(function (index, obj) {
                portValueList += this.textContent + ','
            })
            portValueList = portValueList.slice(0, -1)
            $('#Port').val(portValueList)
        }

        if (textName == "Update") {
            nameValidation = await validateName($('#airGapName').val(), airGapId);
        }
        else {
            nameValidation = await validateName($('#airGapName').val());
        }
        if (nameValidation && formIsValid) {
            $('#CreateForm').trigger('submit');
        }
    }, 800))

    $('#airGapName').on('keyup', commonDebounce(async function () {
        let airGapId = $('#id').val();
        const value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateName(value, airGapId);
    }, 500));
})

const getCyberComponent = async () => {
    $('#sourceComponent, #targetComponent').empty();

    await $.ajax({
        type: "GET",
        url: RootUrl + 'CyberResiliency/Component/GetSiteList',//'Cyber/AirGap/GetCyberComponentList',
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {               
                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#sourceComponent, #targetComponent').append('<option value=""></option>');
                    result?.data.forEach((item) => {
                        $('#sourceComponent, #targetComponent').append(`<option value=${item?.id}>${item?.name}</option>`) // properties=${item?.properties}
                    })
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

const getWorkflowList = async (obj = {}) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + 'CyberResiliency/AirGap/GetWorkflowList',
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#enable-workflow, #disable-workflow').append('<option value=""></option>');
                    result?.data.forEach((item) => {
                        $('#enable-workflow, #disable-workflow').append(`<option value=${item?.id}>${item?.name}</option>`)
                    })
                }
            } else {
                errorNotification(result)
            }
        }
    })

    if (isEdit && Object.keys(obj).length) {
        $('#enable-workflow').val(obj?.enableWorkflowId).trigger('change')
        $('#disable-workflow').val(obj?.disableWorkflowId).trigger('change')
    }
}


// Create Modal Open
$('#airGapCreateButton').on('click', () => {
    isEdit = false;
    clearInputAirGapFields();
    //getWorkflowList();
    $("#btnSave").attr("updateId", "")
    $('#btnSave').text('Save');
    $('#CreateModal').modal('show')
})

// Edit AirGap
$('#airGap_table').on('click', '.edit-button', function () {
    let particularObj = $(this).data("airgap");
    isEdit = true;
    $("#btnSave").attr("updateId", particularObj.id)
    clearInputAirGapFields();
    $('#btnSave').text('Update');
    populateAirGap(particularObj)
    $('#CreateModal').modal('show')
});

// --- delete --->
$('#airGap_table').on('click', '.delete-button', function () {
    let airGapId = $(this).data("airgapId");
    let airGapName = $(this).data("airgapName")
    $('#textDeleteId').val(airGapId);
    $('#deleteData').text(airGapName);
});

$('#sourceComponent').on('input', function (e) {
    let selectedText = $('#sourceComponent option:selected').text();
    $('#sourceComponentName').val(selectedText)
    loadServers($(this), 'sourceServer', 'Source', 'targetComponent')
    if (e?.target?.value) $(`#sourceComponent-error`).text('').removeClass('field-validation-error')
})

$('#targetComponent').on('input', function (e) {
    let selectedText = $('#targetComponent option:selected').text();
    $('#targetComponentName').val(selectedText)
    loadServers2($(this), 'targetServer', 'Target', 'sourceComponent')
    if (e?.target?.value) $(`#targetComponent-error`).text('').removeClass('field-validation-error')
})

$('#airGapPort').on('input', async function () {
    const value = $(this)?.val();
    const sanitizedValue = value.replace(/[^0-9]/g, '');
    $(this).val(sanitizedValue);

    if (sanitizedValue?.length) $('#btnAddPort').show()
    else $('#btnAddPort').hide()

    await Validation(sanitizedValue, 'Enter port', 'Port-error');
});

function Validation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
};

$('#sourceServer, #targetServer, #enable-workflow, #disable-workflow').on('change', function () {
    let id = $(this).attr('id')
    let value = $(this).val();

    let details = [];
    let object = {
        arrayValue: "",
    }

    if (id == 'sourceServer' || id == 'targetServer') {

        $(`#${id} option:selected`).each(function () {
            let value = $(this)?.val();
            let text = $(this)?.text();

            let obj = { id: value, name: text }

            details.push(obj);
            object.arrayValue = details;
        });

        $(`#${id}_details`).val(JSON.stringify(object));

    }

    if (value) $(`#${id}-error`).text('').removeClass('field-validation-error')

    if (id == 'targetServer') {
        if (value?.length > $('#sourceServer')?.val()?.length) $('#targetServer-error').text('Target host switches must match the source host switches').addClass('field-validation-error')
        else $('#targetServer-error').text('').removeClass('field-validation-error')

    }

})

const loadServers = async (selectElement, tagId, label = '', disableId = '') => {
    $(`#${tagId}`).empty();
    //let html = ''
    let id = selectElement?.val();
    await $.ajax({
        type: "GET",
        url: RootUrl + "CyberResiliency/ComponentGroup/GetServerBySiteId",
        data: { 'siteId': id },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                $('#sourceServer').empty();
                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#sourceServer').append(`<option value="">Select ${label} Server</option>`);
                    result?.data.forEach((item) => {
                        if (item?.serverType?.toLowerCase() === 'switch') $('#sourceServer').append(`<option value=${item?.id}>${item?.name}</option>`) // properties=${item?.properties}
                    })

                    if (sourceData && Array.isArray(sourceData) && sourceData?.length) {
                        let getIds = sourceData.map((data) => data?.id)

                        $('#sourceServer').val(getIds)
                    }
                }
            } else {
                errorNotification(result)
            }
        }
    })

    //let selectedOption = selectElement.find('option:selected');
    //let serverProperties = selectedOption.length && selectedOption[0]?.getAttribute('properties');
    //serverProperties = serverProperties ? JSON.parse(serverProperties) : '';

    //if (serverProperties && Array.isArray(serverProperties)) {

    //    html += `<option value=''>Select ${label} Server</option>`

    //    for (let i = 0; i < serverProperties.length; i++) {
    //        html += `<option value=${serverProperties[i]?.id}>${serverProperties[i]?.name}</option>`
    //    }

    //    $(`#${id}-error`).text('').removeClass('field-validation-error')
    //    $(`#${disableId} option[value=${selectedOption[0]?.value}]`).prop('disabled', true);
    //    $(`#${disableId} option`).not('[value="' + selectedOption[0]?.value + '"]').prop('disabled', false);
    //}

    //$(`#${tagId}`).append(html)
}

const loadServers2 = async (selectElement, tagId, label = '', disableId = '') => {
    $(`#${tagId}`).empty();
    //let html = ''
    let id = selectElement?.val();
    await $.ajax({
        type: "GET",
        url: RootUrl + "CyberResiliency/ComponentGroup/GetServerBySiteId",
        data: { 'siteId': id },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {
                $('#targetServer').empty();
                if (result?.data && Array.isArray(result?.data) && result?.data.length) {
                    $('#targetServer').append(`<option value="">Select ${label} Server</option>`);
                    result?.data.forEach((item) => {
                        if (item?.serverType?.toLowerCase() === 'switch') $('#targetServer').append(`<option value=${item?.id}>${item?.name}</option>`) // properties=${item?.properties}
                    })

                    if (targetData && Array.isArray(targetData) && targetData?.length) {
                        let getIds = targetData.map((data) => data?.id)

                        $('#targetServer').val(getIds)
                    }
                }
            } else {
                errorNotification(result)
            }
        }
    })

    //let selectedOption = selectElement.find('option:selected');
    //let serverProperties = selectedOption.length && selectedOption[0]?.getAttribute('properties');
    //serverProperties = serverProperties ? JSON.parse(serverProperties) : '';
    //if (serverProperties && Array.isArray(serverProperties)) {
    //    html += `<option value=''>Select ${label} Server</option>`
    //    for (let i = 0; i < serverProperties.length; i++) {
    //        html += `<option value=${serverProperties[i]?.id}>${serverProperties[i]?.name}</option>`
    //    }
    //    $(`#${id}-error`).text('').removeClass('field-validation-error')
    //    $(`#${disableId} option[value=${selectedOption[0]?.value}]`).prop('disabled', true);
    //    $(`#${disableId} option`).not('[value="' + selectedOption[0]?.value + '"]').prop('disabled', false);
    //}
    //$(`#${tagId}`).append(html)
}

const populateAirGap = (obj) => {
    //getCyberComponent(obj);
    // getWorkflowList(obj)
    let parsedSourceData = obj?.source && JSON.parse(obj?.source);
    let parsedTargetData = obj?.target && JSON.parse(obj?.target);
    sourceData = parsedSourceData?.arrayValue; //&& JSON.parse(parsedSourceData?.arrayValue);
    targetData = parsedTargetData?.arrayValue; //&& JSON.parse(parsedTargetData?.arrayValue);

    if (obj?.port) {
        let portList = obj?.port.includes(',') ? obj?.port?.split(',') : [obj?.port]
        let html = '';
        portList.forEach((d) => {
            html += `<span class="align-middle bg-primary-subtle text-primary badge ms-2 p-2 portValue">${d}<i role="button" class="ms-2 cp-close fs-9 text-secondary btnRemovePort"></i></span>`
        })
        $('#portContainer').empty().append(html)
    }

    $('#sourceComponent').val(obj?.sourceComponentId).trigger('change')
    $('#targetComponent').val(obj?.targetComponentId).trigger('change')
    $('#sourceComponentName').val(obj?.sourceComponentName)
    $('#targetComponentName').val(obj?.targetComponentName)
    $('#airGapName').val(obj?.name)
    $('#status').val(obj?.status)
    $('#airGapId').val(obj?.id)
    //$('#Port').val(obj?.port)
    $('#start_time').val(obj?.startTime)
    $('#end_time').val(obj?.endTime)
    $('#Description').val(obj?.description)
    $('#sourceServer_details').val(obj?.source)
    $('#targetServer_details').val(obj?.target)
}

const clearInputAirGapFields = () => {

    sourceData = [];
    targetData = [];
    $('#airGapName, #Description, #airGapPort,#sourceComponent, #sourceComponentName, #sourceServer, #targetComponent, #targetComponentName, #targetServer, #airGapId, #enable-workflow, #disable-workflow').val('')
    $('#airGapName-error, #Description-error, #Port-error, #sourceComponent-error, #sourceServer-error, #targetComponent-error, #targetServer-error, #enable-workflow-error, #disable - workflow - error').
        text('').removeClass('field-validation-error');
    $('#status').val('Disable')
    $('#sourceServer, #targetServer, #portContainer').empty()
}

// check Name Exist
async function validateName(value, id = null) {
    if (!value) {
        $('#airGapName-error').text('Enter airgap name').addClass('field-validation-error');
        return false;
    }else if (value.includes("<")) {
        $('#airGapName-error').text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }

    let url = RootUrl + 'CyberResiliency/AirGap/IsAirGapNameExist';
    let data = { name: value, id: id };
    const validationResults = [
        await SpecialCharValidate(value), await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value), await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value), await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value), await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value), await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value), await secondChar(value), await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation($('#airGapName-error'), validationResults);
}

async function IsNameExist(url, data, errorFunc) {
    return !data?.name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

$('#btnAddPort').on('click', function () {
    let getPort = $('#airGapPort').val();
    let html = `<span class="align-middle bg-primary-subtle text-primary badge ms-2 p-2 portValue">${getPort}<i role="button" class="ms-2 cp-close fs-9 text-secondary btnRemovePort" ></i></span>`
    $('#portContainer').append(html);
    $('#airGapPort').val('');
    $('#btnAddPort').hide()
})

$(document).on('click', '.btnRemovePort', function () {
    $(this).parent().remove()
})
