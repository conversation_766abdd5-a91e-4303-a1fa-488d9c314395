using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ApprovalMatrixRequestRepositoryTests : IClassFixture<ApprovalMatrixRequestFixture>
{
    private readonly ApprovalMatrixRequestFixture _approvalMatrixRequestFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ApprovalMatrixRequestRepository _repository;

    public ApprovalMatrixRequestRepositoryTests(ApprovalMatrixRequestFixture approvalMatrixRequestFixture)
    {
        _approvalMatrixRequestFixture = approvalMatrixRequestFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ApprovalMatrixRequestRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;

        // Act
        var result = await _repository.AddAsync(approvalMatrixRequest);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixRequest.ProcessName, result.ProcessName);
        Assert.Equal(approvalMatrixRequest.RequestId, result.RequestId);
        Assert.Single(_dbContext.ApprovalMatrixRequests);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        await _repository.AddAsync(approvalMatrixRequest);

        approvalMatrixRequest.ProcessName = "UpdatedProcessName";
        approvalMatrixRequest.Status = "Approved";
        approvalMatrixRequest.Message = "UpdatedComments";

        // Act
        var result = await _repository.UpdateAsync(approvalMatrixRequest);

        // Assert
        Assert.Equal("UpdatedProcessName", result.ProcessName);
        Assert.Equal("Approved", result.Status);
        Assert.Equal("UpdatedComments", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.NullReferenceException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.DeleteAsync(approvalMatrixRequest);

        // Assert
        Assert.Equal(approvalMatrixRequest.ProcessName, result.ProcessName);
        Assert.Empty(_dbContext.ApprovalMatrixRequests);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        var addedEntity = await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.ProcessName, result.ProcessName);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.GetByReferenceIdAsync(approvalMatrixRequest.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixRequest.ReferenceId, result.ReferenceId);
        Assert.Equal(approvalMatrixRequest.ProcessName, result.ProcessName);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var approvalMatrixRequests = _approvalMatrixRequestFixture.ApprovalMatrixRequestList;
        await _repository.AddRange(approvalMatrixRequests);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(approvalMatrixRequests.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenProcessNameExistsAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.ProcessName = "ExistingProcessName";
        await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.IsNameExist("ExistingProcessName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenProcessNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var approvalMatrixRequests = _approvalMatrixRequestFixture.ApprovalMatrixRequestList;
        await _repository.AddRange(approvalMatrixRequests);

        // Act
        var result = await _repository.IsNameExist("NonExistentProcessName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenProcessNameExistsForSameEntity()
    {
        // Arrange
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.ProcessName = "SameProcessName";
        await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.IsNameExist("SameProcessName", approvalMatrixRequest.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsValidWithdrawUser Tests

    [Fact]
    public async Task IsValidWithdrawUser_ShouldReturnTrue_WhenUserIsCreator()
    {
        // Arrange
        
        var repository = new ApprovalMatrixRequestRepository(_dbContext, DbContextFactory.GetMockUserService());
        
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.CreatedBy = "USER_456";
        await repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await repository.IsValidWithdrawUser(approvalMatrixRequest.ReferenceId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsValidWithdrawUser_ShouldReturnFalse_WhenUserIsNotCreator()
    {
        // Arrange
        var mockUserService = DbContextFactory.GetMockUserService();
        
        var repository = new ApprovalMatrixRequestRepository(_dbContext, DbContextFactory.GetMockUserService());
        
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.CreatedBy = "TEST_USER_ID";
        await repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await repository.IsValidWithdrawUser(approvalMatrixRequest.ReferenceId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsValidWithdrawUser_ShouldReturnFalse_WhenRequestNotExists()
    {
        // Arrange
        var repository = new ApprovalMatrixRequestRepository(_dbContext, DbContextFactory.GetMockUserService());

        // Act
        var result = await repository.IsValidWithdrawUser("NON_EXISTENT_ID");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetByRequestId Tests

    [Fact]
    public async Task GetByRequestId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var requestId = "REQUEST_001";
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.RequestId = requestId;
        approvalMatrixRequest.IsActive = true;
        await _repository.AddAsync(approvalMatrixRequest);

        // Act
        var result = await _repository.GetByRequestId(requestId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(requestId, result.RequestId);
        Assert.True(result.IsActive);
    }

    [Fact]
    public async Task GetByRequestId_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var approvalMatrixRequests = _approvalMatrixRequestFixture.ApprovalMatrixRequestList;
        await _repository.AddRange(approvalMatrixRequests);

        // Act
        var result = await _repository.GetByRequestId("NON_EXISTENT_REQUEST");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByRequestId_ShouldReturnNull_WhenInactive()
    {
        // Arrange
        var requestId = "REQUEST_001";
        var approvalMatrixRequest = _approvalMatrixRequestFixture.ApprovalMatrixRequestDto;
        approvalMatrixRequest.RequestId = requestId;
        approvalMatrixRequest.IsActive = false;
        _dbContext.ApprovalMatrixRequests.Add(approvalMatrixRequest);

        // Act
        var result = await _repository.GetByRequestId(requestId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var requests = _approvalMatrixRequestFixture.ApprovalMatrixRequestList;
        var request1 = requests[0];
        var request2 = requests[1];

        // Act
        var task1 = _repository.AddAsync(request1);
        var task2 = _repository.AddAsync(request2);

        var results = await Task.WhenAll(task1, task2);

        // Assert
        Assert.Equal(2, results.Length);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ApprovalMatrixRequests.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var requests = _approvalMatrixRequestFixture.ApprovalMatrixRequestList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRange(requests);
        var initialCount = requests.Count;

        var toUpdate = requests.Take(2).ToList();
        toUpdate.ForEach(x => x.Status = "Approved");
        await _repository.UpdateRange(toUpdate);

        var toDelete = requests.Skip(2).Take(1).ToList();
        await _repository.RemoveRange(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Status == "Approved").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task IsNameExist_ShouldHandleNullParameters()
    {
        // Act & Assert
        var result1 = await _repository.IsNameExist(null, "valid-guid");
        var result2 = await _repository.IsNameExist("TestProcessName", null);
        var result3 = await _repository.IsNameExist(null, null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task GetByRequestId_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.GetByRequestId(null);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task IsValidWithdrawUser_ShouldHandleNullParameter()
    {
        // Act
        var result = await _repository.IsValidWithdrawUser(null);

        // Assert
        Assert.False(result);
    }

    #endregion
}
