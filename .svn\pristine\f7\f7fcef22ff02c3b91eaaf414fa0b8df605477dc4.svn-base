using ContinuityPatrol.Application.Features.BulkImport.Commands.Create.Helper;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Delete;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BulkImportOperationFixture : IDisposable
{
    public List<BulkImportOperation> BulkImportOperations { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBulkImportOperationCommand CreateBulkImportOperationCommand { get; set; }
    public UpdateBulkImportOperationCommand UpdateBulkImportOperationCommand { get; set; }
    public DeleteBulkImportOperationCommand DeleteBulkImportOperationCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BulkImportOperationFixture()
    {
        // Initialize with manual data first
        BulkImportOperations = new List<BulkImportOperation>
        {
            new BulkImportOperation
            {
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                UserName = "TestUser",
                Description = "Test bulk import operation",
                Status = "Pending",
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddHours(1),
                InfraObjectName = "TestInfraObject",
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture
        try
        {
            var additionalOperations = AutoBulkImportOperationFixture.CreateMany<BulkImportOperation>(2).ToList();
            BulkImportOperations.AddRange(additionalOperations);

            UserActivities = AutoBulkImportOperationFixture.CreateMany<UserActivity>(3).ToList();
            CreateBulkImportOperationCommand = AutoBulkImportOperationFixture.Create<CreateBulkImportOperationCommand>();
            UpdateBulkImportOperationCommand = AutoBulkImportOperationFixture.Create<UpdateBulkImportOperationCommand>();
            DeleteBulkImportOperationCommand = AutoBulkImportOperationFixture.Create<DeleteBulkImportOperationCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateBulkImportOperationCommand = new CreateBulkImportOperationCommand();
            UpdateBulkImportOperationCommand = new UpdateBulkImportOperationCommand();
            DeleteBulkImportOperationCommand = new DeleteBulkImportOperationCommand();
        }

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BulkImportOperationProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBulkImportOperationFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBulkImportOperationCommand>(p => p.Description, 50));
            fixture.Customize<CreateBulkImportOperationCommand>(c => c
                .With(b => b.CompanyId, () => Guid.NewGuid().ToString())
                .With(b => b.UserName, () => $"TestUser{fixture.Create<int>()}")
                .With(b => b.Description, () => $"Test bulk import operation {fixture.Create<int>()}")
                .With(b => b.Status, "Pending")
                .With(b => b.StartTime, DateTime.Now)
                .With(b => b.EndTime, DateTime.Now.AddHours(1))
                //.With(b => b.InfraObjectName, () => $"TestInfraObject{fixture.Create<int>()}")
                .With(b => b.BulkImportOperationList, new List<CreateBulkImportOperationListCommand>()));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBulkImportOperationCommand>(p => p.Description, 50));
            fixture.Customize<UpdateBulkImportOperationCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.CompanyId, () => Guid.NewGuid().ToString())
                .With(b => b.UserName, () => $"UpdatedUser{fixture.Create<int>()}")
                .With(b => b.Description, () => $"Updated bulk import operation {fixture.Create<int>()}")
                .With(b => b.Status, "InProgress")
                .With(b => b.StartTime, DateTime.Now)
                .With(b => b.EndTime, DateTime.Now.AddHours(2)));
                //.With(b => b.InfraObjectName, () => $"UpdatedInfraObject{fixture.Create<int>()}"));

            fixture.Customize<DeleteBulkImportOperationCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<BulkImportOperation>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.CompanyId, () => Guid.NewGuid().ToString())
                .With(b => b.UserName, () => $"TestUser{fixture.Create<int>()}")
                .With(b => b.Description, () => $"Test bulk import operation {fixture.Create<int>()}")
                .With(b => b.Status, "Pending")
                .With(b => b.StartTime, DateTime.Now)
                .With(b => b.EndTime, DateTime.Now.AddHours(1))
                .With(b => b.InfraObjectName, () => $"TestInfraObject{fixture.Create<int>()}"));
               // .Without(b => b.BulkImportOperationGroups)); // Exclude potentially problematic navigation properties

            // Add UserActivity customization
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "BulkImportOperation")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
