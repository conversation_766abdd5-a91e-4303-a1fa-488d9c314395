﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Extensions;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertMasterRepositoryMocks
{
    public static Mock<IAlertMasterRepository> CreateAlertMasterRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertMasterRepository.Setup(repo => repo.AddAsync(It.IsAny<AlertMaster>())).ReturnsAsync(
            (AlertMaster alert) =>
            {
                alert.Id = new Fixture().Create<int>();

                alert.ReferenceId = new Fixture().Create<Guid>().ToString();

                alerts.Add(alert);

                return alert;
            });

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> UpdateAlertMasterRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertMaster>())).ReturnsAsync((AlertMaster alert) =>
        {
            var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

            alerts[index] = alert;

            return alert;

        });
        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> DeleteAlertMasterRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertMasterRepository.Setup(repo => repo.UpdateAsync(It.IsAny<AlertMaster>())).ReturnsAsync((AlertMaster alert) =>
        {
            var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

            alert.IsActive = false;

            alerts[index] = alert;

            return alert;
        });

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertMasterRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterEmptyRepository()
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.GetAlertMasterByAlertId(It.IsAny<string>())).ReturnsAsync(new List<AlertMaster>());

        alertMasterRepository.Setup(repo => repo.GetAlertMasterByAlertName(It.IsAny<string>())).ReturnsAsync(new List<AlertMaster>());

        alertMasterRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AlertMaster>());

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterNamesRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.GetAlertMasterNames()).ReturnsAsync(alerts);

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetPaginatedAlertMasterRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        var queryableAlertMaster = alerts.BuildMock();

        alertMasterRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableAlertMaster);

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterByAlertIdRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.GetAlertMasterByAlertId(It.IsAny<string>())).ReturnsAsync(alerts);

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterByAlertNameRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.GetAlertMasterByAlertName(It.IsAny<string>())).ReturnsAsync(alerts);

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterIdExistRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.IsAlertIdExist(It.IsAny<string>())).ReturnsAsync((string id) =>
        {
            return !id.IsValidGuid() && alerts.Any(e => e.AlertId.Equals(id));
        });

        return alertMasterRepository;
    }

    public static Mock<IAlertMasterRepository> GetAlertMasterNameUniqueRepository(List<AlertMaster> alerts)
    {
        var alertMasterRepository = new Mock<IAlertMasterRepository>();

        alertMasterRepository.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync((string name, string id) =>
            {
                if (!id.IsValidGuid())
                {
                    return alerts.Any(e => e.AlertName.Equals(name, StringComparison.OrdinalIgnoreCase));
                }
                else
                {
                    return alerts
                        .Where(e => e.AlertName.Equals(name, StringComparison.OrdinalIgnoreCase))
                        .ToList()
                        .Unique(id);
                }
            });

        return alertMasterRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateAlertMasterEventRepository(List<UserActivity> userActivities)
    {
        var alertMasterEventRepository = new Mock<IUserActivityRepository>();

        alertMasterEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        alertMasterEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return alertMasterEventRepository;
    }
}