using ContinuityPatrol.Application.Features.Alert.Commands.Create;
using ContinuityPatrol.Application.Features.Alert.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.AlertModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AlertFixture
{
    public List<AlertListVm> AlertListVm { get; }
    public CreateAlertCommand CreateAlertCommand { get; }
    public UpdateAlertCommand UpdateAlertCommand { get; }

    public AlertFixture()
    {
        var fixture = new Fixture();

        // Create sample Alert list data
        AlertListVm = new List<AlertListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Type = "Critical",
                Severity = "High",
                SystemMessage = "Database connection failed",
                UserMessage = "Unable to connect to the primary database server",
                JobName = "DatabaseMonitorJob",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "PrimaryDB01",
                ClientAlertId = Guid.NewGuid().ToString(),
                IsResolve = 0,
                IsAcknowledgement = 0,
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Database",
                AlertCategoryId = 1,
                CreatedDate = DateTime.Now.AddHours(-2).ToString("yyyy-MM-dd HH:mm:ss"),
                LastModifiedDate = DateTime.Now.AddHours(-1).ToString("yyyy-MM-dd HH:mm:ss")
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Type = "Warning",
                Severity = "Medium",
                SystemMessage = "High CPU usage detected",
                UserMessage = "Server CPU usage is above 85%",
                JobName = "SystemMonitorJob",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "AppServer01",
                ClientAlertId = Guid.NewGuid().ToString(),
                IsResolve = 0,
                IsAcknowledgement = 1,
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Server",
                AlertCategoryId = 2,
                CreatedDate = DateTime.Now.AddHours(-4).ToString("yyyy-MM-dd HH:mm:ss"),
                LastModifiedDate = DateTime.Now.AddHours(-3).ToString("yyyy-MM-dd HH:mm:ss")
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Type = "Critical",
                Severity = "High",
                SystemMessage = "Backup job failed",
                UserMessage = "Scheduled backup process encountered an error",
                JobName = "BackupJob",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "BackupServer01",
                ClientAlertId = Guid.NewGuid().ToString(),
                IsResolve = 1,
                IsAcknowledgement = 1,
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Backup",
                AlertCategoryId = 3,
                CreatedDate = DateTime.Now.AddHours(-6).ToString("yyyy-MM-dd HH:mm:ss"),
                LastModifiedDate = DateTime.Now.AddHours(-5).ToString("yyyy-MM-dd HH:mm:ss")
            }
        };

        // Create command for creating Alert
        CreateAlertCommand = new CreateAlertCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Type = "Information",
            Severity = "Low",
            SystemMessage = "System maintenance completed",
            UserMessage = "Scheduled maintenance has been completed successfully",
            JobName = "MaintenanceJob",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "MaintenanceServer01",
            ClientAlertId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "Maintenance",
            AlertCategoryId = 4
        };

        // Create command for updating Alert
        UpdateAlertCommand = new UpdateAlertCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Warning",
            Severity = "Medium",
            SystemMessage = "Updated system message",
            UserMessage = "Updated user message",
            JobName = "UpdatedJob",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "UpdatedServer01",
            ClientAlertId = Guid.NewGuid().ToString(),
            IsResolve = 1,
            IsAcknowledgement = 1,
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "Updated",
            AlertCategoryId = 2
        };
    }
}
