﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;

public class GetWorkflowProfileInfoPaginatedListQueryHandler : IRequestHandler<GetWorkflowProfileInfoPaginatedListQuery,
    PaginatedResult<WorkflowProfileInfoListVm>>
{

    private readonly IMapper _mapper;
    private readonly IWorkflowViewRepository _workflowViewRepository;

    //private readonly IUserRepository _userRepository;
    //private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    //private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    //private readonly IWorkflowRepository _workflowRepository; 
    //private readonly IInfraObjectRepository _infraObjectRepository;

    public GetWorkflowProfileInfoPaginatedListQueryHandler(IMapper mapper, IWorkflowViewRepository workflowViewRepository)
    //IWorkflowProfileInfoRepository workflowProfileInfoRepository, IWorkflowRepository workflowRepository
    //, IUserRepository userRepository, IWorkflowInfraObjectRepository workflowInfraObjectRepository,
    //IInfraObjectRepository infraObjectRepository, )
    {
        _mapper = mapper;
        _workflowViewRepository = workflowViewRepository;
        //_workflowProfileInfoRepository = workflowProfileInfoRepository;
        //_workflowRepository = workflowRepository;
        //_userRepository = userRepository;
        //_workflowInfraObjectRepository = workflowInfraObjectRepository;
        //_infraObjectRepository = infraObjectRepository;

    }

    public async Task<PaginatedResult<WorkflowProfileInfoListVm>> Handle(
        GetWorkflowProfileInfoPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var productFilterSpec = new WorkflowProfileInfoFilterSpecification(request.SearchString);

        var workflow = await _workflowViewRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);
       
        var serverList = _mapper.Map<PaginatedResult<WorkflowProfileInfoListVm>>(workflow);

        return serverList;

        #region Old Code

        //var workflowProfileInfos = await _workflowProfileInfoRepository.ListAllAsync();

        //foreach (var item in workflowProfileInfos.Where(x => x.CreatedBy is not null))
        //{
        //    if (item?.WorkflowId is not null)
        //    {
        //        var createdBy = await _workflowRepository.GetByReferenceIdAsync(item?.WorkflowId);
        //        item.CreatedBy = createdBy?.CreatedBy;
        //    }
        //}

        //var notAttachedWorkflowProfileInfoListVm = new List<Domain.Entities.WorkflowProfileInfo>();

        //var workflow = await _workflowRepository.GetWorkflowNames();

        //foreach(var y in workflow)
        //{
        //    var isWorkflowNotAttached = false;

        //    var infraObject = new Domain.Entities.InfraObject();

        //    var workflowInfraObject = await _workflowInfraObjectRepository.GetWorkflowInfraObjectByWorkflowIdAsync(y.ReferenceId);

        //    if (workflowInfraObject != null)
        //    {
        //        infraObject = await _infraObjectRepository.GetByReferenceIdAsync(workflowInfraObject.InfraObjectId);

        //        isWorkflowNotAttached = true;
        //    }
        //    else
        //    {
        //        var workflowInfraObjectDtl = await _workflowInfraObjectRepository.GetWorkflowInfraObjectByWorkflowIdForWorkflowList(y.ReferenceId);


        //        if (workflowInfraObjectDtl is null) isWorkflowNotAttached = true;
        //    }

        //    if (isWorkflowNotAttached)
        //    {
        //        var workflowProfileInfo = new Domain.Entities.WorkflowProfileInfo
        //        {
        //            BusinessFunctionId = infraObject?.BusinessFunctionId ?? "NA",
        //            BusinessFunctionName = infraObject?.BusinessFunctionName ?? "NA",
        //            BusinessServiceId = infraObject?.BusinessServiceId ?? "NA",
        //            BusinessServiceName = infraObject?.BusinessServiceName ?? "NA",
        //            WorkflowType = workflowInfraObject?.ActionType ?? "NA",
        //            ProfileName = "NA",
        //            InfraObjectId = infraObject?.ReferenceId ?? "NA",
        //            InfraObjectName = infraObject?.Name ?? "NA",
        //            WorkflowId = y.ReferenceId,
        //            WorkflowName = y.Name,
        //            IsLock = y.IsLock,
        //            CreatedBy = y.CreatedBy,
        //            IsPublish = y.IsPublish,
        //            IsFourEye = y.IsFourEye
        //        };
        //        notAttachedWorkflowProfileInfoListVm.Add(workflowProfileInfo);
        //    }
        //}

        //var removeUniqueWorkflowId =
        //    notAttachedWorkflowProfileInfoListVm.Where(x => workflowProfileInfos.All(y => y.WorkflowId != x.WorkflowId))
        //        .ToList();

        //var workflowProfileList = workflowProfileInfos.Union(removeUniqueWorkflowId);

        //var workflowProfileListQueryable =
        //    _mapper.Map<List<WorkflowProfileInfoListVm>>(workflowProfileList).AsQueryable();


        //var workflowInfoProductFilterSpec1 = new WorkflowProfileInfoFilterSpecification(request.SearchString);

        //var workflowProfileList1 = await workflowProfileListQueryable
        //    .Specify(workflowInfoProductFilterSpec1)
        //    .Select(m => _mapper.Map<WorkflowProfileInfoListVm>(m))
        //    .ToPaginatedList(request.PageNumber, request.PageSize);

        //workflowProfileList1.Data
        //    .Where(wrk => wrk.CreatedBy is not null)
        //    .ToList()
        //    .ForEach(wrk =>
        //        wrk.CreatedName = _userRepository.GetByReferenceIdAsync(wrk.CreatedBy).Result?.LoginName ?? "NA");

        //return workflowProfileList1;


        #endregion
    }
}