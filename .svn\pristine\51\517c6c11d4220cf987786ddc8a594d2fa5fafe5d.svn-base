﻿namespace ContinuityPatrol.Application.Features.Server.Queries.GetDetail;

public class GetServerDetailQueryHandler : IRequestHandler<GetServerDetailQuery, ServerDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IServerRepository _serverRepository;

    public GetServerDetailQueryHandler(IMapper mapper, IServerRepository serverRepository)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
    }

    public async Task<ServerDetailVm> Handle(GetServerDetailQuery request, CancellationToken cancellationToken)
    {
        //Guard.Against.NegativeOrZero(request.Id, nameof(request.Id), ErrorMessage.Server.ServerIdCannotBeZero);

        var server = await _serverRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(server, nameof(Domain.Entities.Server),
            new NotFoundException(nameof(Domain.Entities.Server), request.Id));

        //server.LicenseKey = SecurityHelper.Decrypt(server.LicenseKey);

        //server.Properties = GetJsonProperties.PasswordDecryption(server.Properties);

        var serverDetailDto = _mapper.Map<ServerDetailVm>(server);

        return serverDetailDto;
    }
}