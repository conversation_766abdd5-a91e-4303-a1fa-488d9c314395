﻿namespace ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;

public record ImpactDetailVm
{
 
    public int ServerTotalCount { get; set; }
    public int ServerDownCount { get; set; }
    public int DatabaseTotalCount { get; set; }
    public int DatabaseDownCount { get; set; }
    public int ReplicationTotalCount { get; set; }
    public int ReplicationDownCount { get; set; }
    public List<HeatMapStatusListVm> ServerDownList { get; set; }
    public List<HeatMapStatusListVm> DatabaseDownList { get; set; }
    public List<HeatMapStatusListVm> ReplicationDownList { get; set; }
    //public int ApplicationTotalCount { get; set; }
    //public int ApplicationDownCount { get; set; }
    //public int StorageTotalCount { get; set; }
    //public int StorageDownCount { get; set; }
    //public int NetworkTotalCount { get; set; }
    //public int NetworkDownCount { get; set; }
}

