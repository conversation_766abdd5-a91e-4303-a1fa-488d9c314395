﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ManageWorkflowListSpecification : Specification<Workflow>
{
    public ManageWorkflowListSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.Name.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}