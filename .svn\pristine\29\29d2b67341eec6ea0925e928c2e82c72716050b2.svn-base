﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Events.Update;

namespace ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;

public class UpdateRoboCopyJobCommandHandler : IRequestHandler<UpdateRoboCopyJobCommand, UpdateRoboCopyJobResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IRoboCopyJobRepository _roboCopyJobRepository;

    public UpdateRoboCopyJobCommandHandler(IRoboCopyJobRepository roboCopyJobRepository, IMapper mapper,
        IPublisher publisher)
    {
        _roboCopyJobRepository = roboCopyJobRepository;
        _mapper = mapper;
        _publisher = publisher;
    }

    public async Task<UpdateRoboCopyJobResponse> Handle(UpdateRoboCopyJobCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _roboCopyJobRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.RoboCopyJob), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateRoboCopyJobCommand), typeof(Domain.Entities.RoboCopyJob));

        await _roboCopyJobRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateRoboCopyJobResponse
        {
            Message = Message.Update(nameof(Domain.Entities.RoboCopyJob), eventToUpdate.ReferenceId),
            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(new RoboCopyJobUpdatedEvent { ReplicationName = eventToUpdate.ReplicationName },
            cancellationToken);

        return response;
    }
}