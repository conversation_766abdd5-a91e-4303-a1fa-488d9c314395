using ContinuityPatrol.Application.Features.AlertInformation.Commands.Create;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;
using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class AlertInformationFixture
{
    public List<AlertInformationListVm> AlertInformationListVm { get; }
    public AlertInformationDetailVm AlertInformationDetailVm { get; }
    public CreateAlertInformationCommand CreateAlertInformationCommand { get; }
    public UpdateAlertInformationCommand UpdateAlertInformationCommand { get; }

    public AlertInformationFixture()
    {
        var fixture = new Fixture();

        // Create sample AlertInformation list data
        AlertInformationListVm = new List<AlertInformationListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Critical",
                Severity = "High",
                Code = "CRIT_001",
                AlertFrequency = 5
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Warning",
                Severity = "Medium",
                Code = "WARN_001",
                AlertFrequency = 10
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Information",
                Severity = "Low",
                Code = "INFO_001",
                AlertFrequency = 30
            }
        };

        // Create detailed AlertInformation data
        AlertInformationDetailVm = new AlertInformationDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Critical",
            Severity = "High",
            Code = "CRIT_001",
            AlertFrequency = 5
        };

        // Create command for creating AlertInformation
        CreateAlertInformationCommand = new CreateAlertInformationCommand
        {
            Type = "Error",
            Severity = "High",
            Code = "ERR_001",
            AlertFrequency = 3
        };

        // Create command for updating AlertInformation
        UpdateAlertInformationCommand = new UpdateAlertInformationCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Updated Warning",
            Severity = "Medium",
            Code = "WARN_002",
            AlertFrequency = 15
        };
    }
}
