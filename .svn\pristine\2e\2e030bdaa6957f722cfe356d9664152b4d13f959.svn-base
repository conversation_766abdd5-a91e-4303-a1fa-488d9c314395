﻿using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class SingleSignOnProfile : Profile
{
    public SingleSignOnProfile()
    {
        CreateMap<CreateSingleSignOnCommand, SingleSignOnViewModel>().ReverseMap();
        CreateMap<UpdateSingleSignOnCommand, SingleSignOnViewModel>().ReverseMap();

        CreateMap<SingleSignOn, CreateSingleSignOnCommand>().ReverseMap();
        CreateMap<UpdateSingleSignOnCommand, SingleSignOn>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<SingleSignOn, SingleSignOnListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SingleSignOn, SingleSignOnDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SingleSignOn, SingleSignOnNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<SingleSignOn, SingleSignOnTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<SingleSignOn>,PaginatedResult<SingleSignOnListVm>>()
             .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }
}