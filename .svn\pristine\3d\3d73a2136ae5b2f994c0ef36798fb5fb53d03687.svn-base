﻿namespace ContinuityPatrol.Application.Features.DRCalendar.Queries.GetDrCalendarDrillEvents;

public class GetUpcomingDrillCountVm
{
    public int Total { get; set; }
    public int Success { get; set; }
    public int Failure { get; set; }

    public List<GetDrCalendarDrillEventsVm> GetUpcomingDrillDetailVm { get; set; } = new();
}

public class GetDrCalendarDrillEventsVm
{
    public string Id { get; set; }
    public string ActivityName { get; set; }
    public string ActivityType { get; set; }
    public string Description { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public int InvitationNo { get; set; }
    public string CompanyId { get; set; }
    public string Location { get; set; }
    public DateTime ScheduledStartDate { get; set; }
    public DateTime ScheduledEndDate { get; set; }
    public string Responsibility { get; set; }
    public List<string> ResponsibilityName { get; set; } = new();
    public string ActivityStatus { get; set; }
    public string RecipientTwo { get; set; }
    public List<string> RecipientTwoName { get; set; } = new();
    public string SetReminders { get; set; }
    public string WorkflowProfiles { get; set; }
    public List<string> WorkflowProfileName { get; set; } = new();
    public string FileName { get; set; }


    //public string CompanyId { get; set; }
    //public string ActivityName { get; set; }
    //public int ActivityType { get; set; }
    //public string Location { get; set; }
    //public string BusinessServiceId { get; set; }
    //public string ScheduledStartDate { get; set; }
    //public string ScheduledEndDate { get; set; }
    //public string Recipient2 { get; set; }
    //public string RecipientName { get; set; }
    //public string SetReminders { get; set; }
    //public string Profiles { get; set; }
    //public string FileName { get; set; }
    //public string Responsibility { get; set; }
    //public string ResponsibilityName { get; set; }
    //public string ActivityDetails { get; set; }
    //public string ActivityStatus { get; set; }
}