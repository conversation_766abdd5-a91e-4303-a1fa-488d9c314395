﻿// https://observablehq.com/@d3/collapsible-tree
// new sample from June 2023

var image = "<img src='../../img/isomatric/no_data_found.svg' class='Card_NoData_Img'>"
let statusData = '';

function BusinessFunctionDetails(data, values) {
    console.log(data, values)
    $('#businessOverviewTable').empty();

    if (data.length) {
        console.log(data)
        let tabledata = '';

        data.forEach(function (parentItem) {
            if (parentItem?.getInfraObjectList.length) {
                parentItem.getInfraObjectList.forEach(function (childItem) {
                    var remarkText = '';
                    var remarkClass = '';
                    var remarkStatus = '';
                    var remarkStatusText = '';

                    if (childItem.status.toLowerCase() === 'up') {
                        remarkStatus = 'cp-up-linearrow';
                        remarkStatusText = 'text-success';
                    } else if (childItem.status.toLowerCase() === 'pending') {
                        remarkStatus = 'cp-pending';
                        remarkStatusText = 'text-danger';
                    } else if (childItem.status.toLowerCase() === 'databasedown' || childItem.status.toLowerCase() === 'down') {
                        remarkStatus = 'cp-down-linearrow';
                        remarkStatusText = 'text-danger';
                    } else {
                        remarkStatus = 'cp-datalog';
                        remarkStatusText = 'text-danger';
                    }

                    if (childItem.reMark === 'Affected') {
                        remarkText = 'text-danger';
                        remarkClass = 'cp-affecteds';
                    } else if (childItem.reMark === 'Not Affected') {
                        remarkText = 'text-success';
                        remarkClass = 'cp-success';
                    } else if (childItem.reMark === 'WorkFlow Not Configured') {
                        remarkText = 'text-danger';
                        remarkClass = 'workflow-not-configured';
                    } else if (childItem.reMark !== '') {
                        remarkText = 'text - warning';
                        remarkClass = 'cp-pending';
                    } else {
                        remarkText = 'text-warning';
                        remarkClass = 'cp-disable'
                    }
                    tabledata += '<tr>';
                    tabledata += '<td>' + values + '</td>';
                    tabledata += '<td>' + parentItem?.businessFunctionName + '</td>';
                    tabledata += '<td title="Infra">' + childItem?.infraObjectName + '</td>';
                    tabledata += '<td title="Status">' + '<i class=" ' + remarkStatus + ' ' + remarkStatusText + ' "></i>' + childItem?.status + '</td>';
                    tabledata += '<td title="Remarks"><i class="' + remarkClass + ' ' + remarkText + ' me-1"></i>' + (childItem?.reMark ? childItem.reMark : 'NA') + '</td>';
                    tabledata += '</tr>';
                })
            } else {
                tabledata += '<tr>';
                tabledata += '<td>' + values + '</td>';
                tabledata += '<td title="Name">' + parentItem?.businessFunctionName + '</td>';
                tabledata += '<td title="Infra">' + "NA" + '</td>';
                tabledata += '<td title="Status">' + "NA" + '</td>';
                tabledata += '<td title="Remarks"><i class="' + remarkClass + ' ' + remarkText + ' me-1"></i>' + "NA" + '</td>';
                tabledata += '</tr>';
            }

        });

        $('#businessOverviewTable').append(tabledata);
    } else {
        var tabledata = '<tr>';
        tabledata += '<td>' + values + '</td>';
        tabledata += '<td title="Name">' + "NA" + '</td>';
        tabledata += '<td title="Infra">' + "NA" + '</td>';
        tabledata += '<td title="Status">' + "NA" + '</td>';
        tabledata += '<td title="Remarks">' + "NA" + '</td>';
        tabledata += '</tr>';
        $('#businessOverviewTable').append(tabledata);
    }

}
let datacenterFlagStatus = true
function BusinessTreeView(ServiceId) {

    $.ajax({
        url: "/Dashboard/ServiceAvailability/GetBusinessServiceTreeViewListByBusinessServiceId",
        data: {
            businessServiceId: ServiceId
        },
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (data) {

            datacenterFlagStatus = true
            $(".treewrapper").empty()
            if (data == "") {
                $(".treewrapper").css('text-align', 'center').html(image);
            }
            else {

                newJsonCreate(data)

                //if (data?.businessFunctionDataLag.length==0) {
                //	datacenterFlagStatus=false
                //}
                SitePropertiesBusinessService(ServiceId, datacenterFlagStatus)
                $('#ConfiguredBusinessFunction').text(data?.businessFunctionDataLag && data?.businessFunctionDataLag.length);
                let totalInfraCount = 0;

                if (data?.businessFunctionDataLag && data?.businessFunctionDataLag.length != 0) {
                    for (let i = 0; i < data?.businessFunctionDataLag.length; i++) {
                        totalInfraCount = totalInfraCount + data?.businessFunctionDataLag[i]?.infraObjectDataLag.length
                    }
                }

                $('#ConfiguredInfraobject').text(totalInfraCount);
            }

        },
        error: function (data) {

            $(".treewrapper").empty()
            if (data == "") {
                $(".treewrapper").css('text-align', 'center').html(image);
            }


        }

    })
}

function newJsonCreate(data) {

    var result = {}
    var businessServicsSize = "businessServicsSize"
    var businessFunctionSize = "businessFunctionSize"
    var infraObjectSize = "infraObjectSize"
    var summarySize = "summarySize"
    var detailsSize = "detailsSize"
    result = {
        name: data.businessServiceName,
        index: 0,
        children: [],
        size: businessServicsSize,
    }

    // Iterate through the nestedArray

    if (data.businessFunctionDataLag?.length != 0 && data.businessFunctionDataLag) {
        data.businessFunctionDataLag.forEach(function (parentItem, index) {
            let arrayInfraStatusfunc = []
            let flagStatusfunc = ""
            if (parentItem.infraObjectDataLag.length != 0) {
                parentItem.infraObjectDataLag.forEach(function (childItem, index) {
                    //arrayInfraStatusfunc = []
                    flagStatusfunc = "Up"
                    childItem.serverDtoVm.forEach((Serveritem) => {
                        arrayInfraStatusfunc.push(Serveritem)
                    })
                    childItem.databaseDtoVm.forEach((database) => {
                        arrayInfraStatusfunc.push(database)
                    })
                })
                arrayInfraStatusfunc.forEach((item) => {
                    if (item?.status == "Down" || item?.modeType == 'Down') {
                        flagStatusfunc = 'Down'
                    }
                })
                
                var parent = {
                    name: parentItem.businessFunctionName,
                    alert: "true",
                    index: index,
                    size: businessFunctionSize,
                    status: flagStatusfunc,
                    children: []
                };
            }
            else {


                //console.log(parentItem)
                var parent = {
                    name: parentItem.businessFunctionName,
                    alert: "true",
                    index: index,
                    size: businessFunctionSize,
                    status: flagStatusfunc,
                    children: []
                };
            }
            result.status = flagStatusfunc
            if (parentItem.infraObjectDataLag.length != 0) {
                let arrayInfraStatus = []
                let flagStatus = "Up"
                //if (parentItem.infraObjectDataLag.length == 0) {
                //	 flagStatus = "normal"
                //}

                // Iterate through the children of each parent

                parentItem.infraObjectDataLag.forEach(function (childItem, index) {
                    arrayInfraStatus = []
                    flagStatus = "Up"
                    childItem.serverDtoVm.forEach((Serveritem) => {
                        arrayInfraStatus.push(Serveritem)
                    })
                    childItem.databaseDtoVm.forEach((database) => {
                        arrayInfraStatus.push(database)
                    })
                    //arrayInfraStatus.push(childItem.drServereDataLag)
                    //arrayInfraStatus.push(childItem.prServereDataLag)
                    //arrayInfraStatus.push(childItem.drDatabaseDataLag)
                    //arrayInfraStatus.push(childItem.prDatabaseDataLag)
                    arrayInfraStatus.forEach((item) => {
                        if (item?.status == "Down" || item?.modeType == 'Down') {
                            flagStatus = 'Down'
                        }
                    })
                    //result.status = flagStatus
                    if (childItem.infraObjectName !== "NA") {
                        var child = {
                            name: childItem.infraObjectName,
                            alert: "true",
                            index: index,
                            size: infraObjectSize,
                            status: flagStatus,
                            children: []
                            //status: childItem.drServereDataLag
                            //	? childItem.drServereDataLag.status
                            //	: childItem.prServereDataLag
                            //		? childItem.prServereDataLag.status
                            //		: childItem.drDatabaseDataLag ? childItem.drDatabaseDataLag.modeType
                            //			: childItem.prDatabaseDataLag ? childItem.prDatabaseDataLag.modeType :
                            //				childItem.replicationDataLag ? childItem.replicationDataLag.status:"unknown", // Set status based on available properties

                        };

                        for (var prop in childItem) {

                            //console.log(prop)
                            if (childItem.hasOwnProperty(prop)) {

                                var innerObj = {};
                                if (prop == "databaseDtoVm") {
                                    if (childItem[prop]) {
                                        childItem?.databaseDtoVm?.forEach(function (databasedtovm, index) {
                                            if (databasedtovm?.databaseName !== "NA" && databasedtovm?.databaseName !== null && databasedtovm?.databaseName !== undefined) {
                                                let databaseData = []
                                                if (databasedtovm.databaseType != "NA") {
                                                    databaseData.push({
                                                        name: databasedtovm.databaseType,
                                                        value: databasedtovm.databaseType,
                                                        status: databasedtovm.modeType,
                                                        size: detailsSize,
                                                    })
                                                }
                                                if (databasedtovm.oracleSID != "NA") {
                                                    databaseData.push({
                                                        name: databasedtovm.oracleSID,
                                                        value: databasedtovm.oracleSID,
                                                        status: databasedtovm.modeType,
                                                        size: detailsSize,
                                                    })
                                                }
                                                child.children.push({
                                                    name: databasedtovm.databaseName,
                                                    alert: "true",
                                                    size: summarySize,
                                                    status: databasedtovm.modeType,
                                                    children: databaseData
                                                })
                                            }

                                        });
                                    }
                                }
                                else if (prop == "serverDtoVm") {
                                    if (childItem[prop]) {
                                        childItem?.serverDtoVm?.forEach(function (serverdtovm, index) {
                                           
                                            if (serverdtovm?.serverName !== "NA" && serverdtovm?.serverName !== null && serverdtovm?.serverName !== undefined) {
                                                let serverData = []
                                                if (serverdtovm.osType != "NA") {
                                                    serverData.push({
                                                        name: serverdtovm.osType,
                                                        value: serverdtovm.osType,
                                                        status: serverdtovm.status,
                                                        size: detailsSize,
                                                    })
                                                }
                                                if (serverdtovm?.connectViaHostName == "False" || serverdtovm?.connectViaHostName == "NA") {
                                                    if (serverdtovm?.ipAddress != "NA") {
                                                        serverData.push({
                                                            name: serverdtovm.ipAddress,
                                                            value: serverdtovm.ipAddress,
                                                            status: serverdtovm.status,
                                                            size: detailsSize,
                                                        })
                                                    }
                                                }
                                                else{
                                                    if (serverdtovm?.hostName != "NA") {
                                                        serverData.push({
                                                            name: serverdtovm?.hostName,
                                                            value: serverdtovm?.hostName,
                                                            status: serverdtovm?.status,
                                                            size: detailsSize,
                                                        })
                                                    }
                                                }
                                                child.children.push({
                                                    name: serverdtovm.serverName,
                                                    alert: "false",
                                                    status: serverdtovm.status,
                                                    size: summarySize,
                                                    children: serverData
                                                })
                                            }
                                        });
                                    }
                                }
                                //else if (prop == "prDatabaseDataLag") {
                                //    if (childItem[prop]) {
                                //        if (childItem[prop].prDatabaseName !== "NA" && childItem[prop].prDatabaseName !== null) {
                                //            child.children.push({
                                //                name: childItem[prop].prDatabaseName,
                                //                alert: "false",
                                //                status: childItem[prop].modeType,
                                //                size: summarySize,
                                //                children: [
                                //                    {
                                //                        name: childItem[prop].databaseType,
                                //                        value: childItem[prop].databaseType,
                                //                        status: childItem[prop].modeType,
                                //                        size: detailsSize,
                                //                    },
                                //                    {
                                //                        name: childItem[prop].oracleSID,
                                //                        value: childItem[prop].oracleSID,
                                //                        status: childItem[prop].modeType,
                                //                        size: detailsSize,
                                //                    }
                                //                ]
                                //            })
                                //        }
                                //    }

                                //}
                                //else if (prop == "prServereDataLag") {
                                //    if (childItem[prop]) {
                                //        if (childItem[prop].prServerName !== "NA" && childItem[prop].prServerName !== null) {
                                //            child.children.push({
                                //                name: childItem[prop].prServerName,
                                //                alert: "true",
                                //                status: childItem[prop].status,
                                //                size: summarySize,
                                //                children: [
                                //                    {
                                //                        name: childItem[prop].osType,
                                //                        value: childItem[prop].osType,
                                //                        status: childItem[prop].status,
                                //                        size: detailsSize,
                                //                    },
                                //                    {
                                //                        name: childItem[prop].ipAddress,
                                //                        value: childItem[prop].ipAddress,
                                //                        status: childItem[prop].status,
                                //                        size: detailsSize,
                                //                    }
                                //                ]
                                //            })
                                //        }
                                //    }

                                //}
                                else if (prop == "replicationDtoVm") {

                                    if (childItem[prop]) {
                                        childItem?.replicationDtoVm?.forEach(function (replicationDtoVm, index) {
                                            if (replicationDtoVm.name != null && replicationDtoVm.name != "NA" && replicationDtoVm.name != undefined) {
                                                child.children.push({
                                                    name: replicationDtoVm?.name,
                                                    alert: "true",
                                                    status: replicationDtoVm?.status,
                                                    size: summarySize,
                                                    children: [
                                                        {
                                                            name: replicationDtoVm?.type,
                                                            value: replicationDtoVm?.type,
                                                            status: replicationDtoVm?.status,
                                                            size: detailsSize,
                                                        }
                                                        //{
                                                        //	name: childItem[prop].replicationTypeId,
                                                        //	value: childItem[prop].replicationTypeId,
                                                        //	status: childItem[prop].status,
                                                        //	size: detailsSize,
                                                        //}
                                                    ]
                                                })
                                            }
                                        })
                                    }
                                }

                            }
                        }

                        parent.children.push(child);
                    }
                });
            }

            result.children.push(parent);
        });

    }

    drawTree(result)



}
//$(document).ready(function () {
//    // Using a Promise to ensure asynchronous code is completed
//    drawTree().then(function () {
//        // Code to execute after drawTree has completed
//        console.log("Tree drawn successfully.");
//    }).catch(function (error) {
//        // Handle errors if needed
//        console.error("Error drawing tree:", error);
//    });
//});

async function drawTree(treeData) {
    
    
    console.log(treeData)
    // 1. Access data
    //const data = await d3.json("https://gist.githubusercontent.com/robschmuecker/7880033/raw/9c1afaee2cb2531897923b87feb00ddbaf725aab/flare.json");
    

    // Get sorted data object
    const sortedData = sortHierarchy(treeData);
    
    let data = {}
    data = await sortedData
    //console.log(data)
    // 2. Create chart dimensions
    const dimension = {
        width: 1000,
        margin: { top: 20, right: 0, bottom: 30, left: 50 },
        height: 500

    }


    const root = d3.hierarchy(data);

    const dx = 50;
    const dy = (dimension.width - dimension.margin.right - dimension.margin.left) / (1 + root.height);

    // Define the tree layout and the shape for links.
    const tree = d3.tree()
        .nodeSize([dx, dy]);
    const diagonal = d3.linkHorizontal()
        .x(d => d.y)
        .y(d => d.x);

    //function collapse(d) {
    //	if (d.children) {
    //		d.all_children = d.children;
    //		d._children = d.children;
    //		d._children.forEach(collapse);
    //		d.children = null;
    //		d.hidden = true;
    //	}
    //}

    // Define the zoom function for the zoomable tree
    const handleZoom = (e) => bounds.attr('transform', e.transform);
    const zoom = d3.zoom().on('zoom', handleZoom).scaleExtent([.5, 20])

    // Create the SVG container, a layer for the links and a layer for the nodes.
    const svg = d3.selectAll("#treewrapper,#wrapper1")
        .append("svg")
        .attr("class", "D3overview")
        .attr("width", dimension.width)
        .attr("height", dx)
        .style("overflow-y", "scroll")
        .attr("viewBox", [-dimension.margin.left, -dimension.margin.top, dimension.width, dx])
        .call(zoom);

    d3.select("#wrapper1").select("svg")
        .attr("width", 1980)
        .attr("height", 400)


    // Add tooltips using d3-tip
    //const tip = d3.tip()
    //	.attr('class', 'd3-tip')
    //	.offset([-10, 0])

    //	.html(d => d.data.tooltip); // Set the tooltip content based on JSON data

    //svg.call(tip);
    var tooltip = d3.select("body").append("div")
        .attr("class", "tooltip")
        .style("opacity", 0);

    // Attach the tooltip to tree nodes
    var nodes = d3.selectAll(".node"); // Select your tree nodes
    nodes.on("mouseover", function (d) {
        tooltip.transition()
            .duration(200)
            .style("opacity", 0.9);
        tooltip.html(d?.data?.name) // Customize the content of the tooltip
            .style("left", (d3?.event?.pageX) + "px")
            .style("top", (d3?.event?.pageY - 28) + "px");
    });

    nodes.on("mouseout", function (d) {
        tooltip.transition()
            .duration(500)
            .style("opacity", 0);
    });
    //nodeSelection.on('mouseover', tip.show)
    //	.on('mouseout', tip.hide);
    //
    // Create a intrim gorup for zoom & pan
    const bounds = svg.append("g");

    const gLink = bounds.append("g")
        .attr("class", "link")


    const gNode = bounds.append("g")
        .attr("class", "node")
        .attr("pointer-events", "all");

    function update(event, source) {

        const duration = event?.altKey ? 2500 : 250; // hold the alt key to slow down the transition
        //const nodes = root.descendants().reverse();
        //const links = root.links();
        var nodes = root.descendants().filter(function (d) { return !d.hidden; }).reverse();
        const links = root.links();
        // Compute the new tree layout.
        tree(root);


        let left = root;
        let right = root;
        root.eachBefore(node => {
            if (node.x < left.x) left = node;
            if (node.x > right.x) right = node;
        });
        //links.style("stroke", "red");
        const height = right.x - left.x + dimension.margin.top + dimension.margin.bottom;

        const transition = svg.transition()
            .duration(duration)
            .attr("height", height)
            .attr("viewBox", [-dimension.margin.left, left.x - dimension.margin.top, dimension.width, height])
            .tween("resize", window.ResizeObserver ? null : () => () => svg.dispatch("toggle"));

        // Update the nodes…


        const node = gNode.selectAll("g")
            .data(nodes, d => d.id);


        // Enter any new nodes at the parent's previous position.
        const nodeEnter = node
            .enter()
            .append("g")
            .attr("transform", d => `translate(${source.y0},${source.x0})`)
            .attr("fill-opacity", 0)
            .attr("stroke-opacity", 0)
            .attr("id", d => d?.data?.size)
            .attr("class", d => d?.data?.size)
            .on("click", (event, d) => {


                if (d.children) {
                    d._children = d.children;
                    d.children = null;
                } else {
                    d.children = d._children;
                    d._children = null;
                }
                // If the node has a parent, then collapse its child nodes
                // except for this clicked node.
                if (d.parent) {

                    d.parent.children.forEach(function (element) {
                        if (d !== element) {
                            collapse(element);
                        }
                    });
                }

                update(null, d);

            });
        // Create links (edges)

        nodeEnter.append("circle")
            .attr('class', 'nodecircle')
            .attr("r", 0)

        // Show the clicked node
        //d3.select(this.parentNode).classed("active", true);
        nodeEnter.append("text")
            .attr("dy", d => d?.data?.size == "businessServicsSize" ? "-1em" : d?.data?.size == "businessFunctionSize" ? "2em" : d?.data?.size == "infraObjectSize" ? "-1em" : d?.data?.size == "summarySize" ? "-1em" : "-1em")
            .attr("x", d => d?.data?.size == "businessServicsSize" ? 6 : d?.data?.size == "businessFunctionSize" ? 6 : d?.data?.size == "infraObjectSize" ? 6 : d?.data?.size == "summarySize" ? 6 : -6)
            .attr("text-anchor", d => d._children ? "middle" : "middle")
            .text(d => {
                if (d.data.name && d.data.name.length <= 15) {
                    return d.data.name
                }
                else if (d.data.name) {

                    return d.data.name.slice(0, 15) + "..."
                } else {
                    return "NA"; // Return an empty string if name is null or undefined
                }

            })



        // Change the circle fill depending on whether it has children and is collapsed

        nodeEnter.select("circle")
            .attr("r", 5)
            .attr('class', 'nodeCircle')
            .style("fill", function (d) {

                if (d?.data?.status?.toLowerCase() === "up") {
                    return "#267100"; // Green color for "Up" status
                } else if (d?.data?.status?.toLowerCase() === "down") {
                    return "#FF0000"; // Red color for other statuses
                }
                else if (d?.target?.data?.children?.length === 0 || d?.data?.size == "detailsSize") {
                    return "#267100"; // Red color for other statuses
                }
                else if (d?.data?.status?.toLowerCase()==="" || d?.data?.children?.length === 0 || d?.target?.data?.children === null) {
                    return "#FFA500"; // yellow color for other statuses
                }
                else {
                    return "#267100";
                }

            });

        // Transition nodes to their new position.
        const nodeUpdate = node.merge(nodeEnter)
            .transition(transition)
            .attr("transform", d => `translate(${d.y},${d.x})`)
            .attr("fill-opacity", 1)
            .attr("stroke-opacity", 1);

        nodeUpdate.each(function (d) {
            if (d.data.ipaddress !== undefined) {
                let titleText = `IP Address: ${d.data.ipaddress}`;
                if (d.data.ostype !== undefined) {
                    titleText += `\nOS Type: ${d.data.ostype}`;
                }
                d3.select(this).append("title")
                    .text(titleText);
            }
        });
        nodeEnter.append("title")
            .text(d => d.data.name);


        // Transition exiting nodes to the parent's new position.
        const nodeExit = node.exit()
            .transition(transition)
            .remove()
            .attr("transform", d => `translate(${source.y},${source.x})`)
            .attr("fill-opacity", 0)
            .attr("stroke-opacity", 0);

        // Update the links…
        const link = gLink.selectAll("path")
            .data(links, d => d.target.id)


        // Enter any new links at the parent's previous position.
        const linkEnter = link.enter()
            .append("path")
            .attr("id", d => d?.data?.size)
            .attr("class", d => d?.data?.size)
            .attr("d", d => {
                const o = {
                    x: source.x0,
                    y: source.y0
                };
                return diagonal({
                    source: o,
                    target: o
                });
            })

        // Transition links to their new position.
        link.merge(linkEnter)

            .transition(transition)
            .attr("d", diagonal)
            .attr("stroke", function (d) {
                
                var status = d?.target?.data?.status?.toLowerCase();
                if (status === "up") {
                    return "#267100"; // Green color for "Up" status
                } else if (status === "down") {
                    return "#FF0000"; // Red color for other statuses
                }
                else if (status==="" || d?.target?.data?.children === null || d?.data?.children?.length === 0 || d?.target?.data?.children?.length === 0 || d?.data?.size == "detailsSize") {
                    return "#FFA500"; // yellow color for other statuses
                }
                else {
                    return "#267100";
                }

            })
            .attr("id", d => { return d?.data?.size })
            .attr("class", d => { return d?.data?.size })

        // Transition exiting nodes to the parent's new position.
        link.exit()
            .transition(transition)
            .remove()
            .attr("d", d => {
                const o = {
                    x: source.x,
                    y: source.y
                };
                return diagonal({
                    source: o,
                    target: o
                });
            });

        // Stash the old positions for transition.
        root.eachBefore(d => {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    root.x0 = dy / 2;
    root.y0 = 0;
    const maxDepth = 2;

    root.descendants().forEach((d, i) => {

        d.id = i;
        d._children = d.children;
        if (d.data?.index != 0) {

            d.children = null;

        }
    });
    update(null, root);
}


function sortHierarchy(node) {
    if (!node.children) return node; // If no children, return as is
    // Recursively sort children first
    node.children = node.children.map(sortHierarchy);
    if (node.children && node.children.length != 0) {
        // Sort children: Pr first, Dr last, others remain in order
        node.children.sort((a, b) => {
            const nameA = a.name ? a.name.toLowerCase() : "NA";
            const nameB = b.name ? b.name.toLowerCase() : "NA";

            if (nameA.includes("pr") && !nameB.includes("pr")) return -1; // "Pr" first
            if (nameB.includes("pr") && !nameA.includes("pr")) return 1;
            if (nameA.includes("dr") && !nameB.includes("dr")) return 1; // "Dr" last
            if (nameB.includes("dr") && !nameA.includes("dr")) return -1;
            return 0; // Keep original order for other nodes
        });
    }

    return node;
}

function collapse(d) {

    if (d.children) {
        d._children = d.children;
        d._children.forEach(collapse);
        d.children = null;


    }
}

function zoomed() {

    var transform = d3.event.transform;

    // Zoom the circles
    var xNewScale = transform.rescaleX(xScale);
    circles
        .attr("cx", function (d) {
            return xNewScale(d);
        });

    // Zoom the axis
    xAxis.scale(xNewScale);
    axis.call(xAxis);
}


