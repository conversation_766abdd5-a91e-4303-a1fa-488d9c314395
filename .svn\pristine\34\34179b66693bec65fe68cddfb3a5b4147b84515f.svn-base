using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class IncidentFixture : IDisposable
{
    public List<Incident> IncidentPaginationList { get; set; }
    public List<Incident> IncidentList { get; set; }
    public Incident IncidentDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public IncidentFixture()
    {
        var fixture = new Fixture();

        IncidentList = fixture.Create<List<Incident>>();

        IncidentPaginationList = fixture.CreateMany<Incident>(20).ToList();

        // Setup proper test data for IncidentPaginationList
        IncidentPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentPaginationList.ForEach(x => x.IsActive = true);

        // Setup proper test data for IncidentList
        IncidentList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        IncidentList.ForEach(x => x.IsActive = true);

        IncidentDto = fixture.Create<Incident>();
        IncidentDto.ReferenceId = Guid.NewGuid().ToString();
        IncidentDto.IsActive = true;
        IncidentDto.IncidentName = "Test Incident";
        IncidentDto.IncidentNumber = "INC-001";
        IncidentDto.Description = "Test incident description";

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
