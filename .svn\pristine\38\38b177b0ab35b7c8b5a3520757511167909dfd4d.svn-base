﻿using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;

public class UpdateBaseLicenseCommandValidator : AbstractValidator<UpdateBaseLicenseCommand>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;

    public UpdateBaseLicenseCommandValidator(ILicenseManagerRepository licenseManagerRepository,
        ILoggedInUserService loggedInUserService, IMapper mapper, ILicenseValidationService licenseValidationService)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
        _licenseValidationService = licenseValidationService;

        RuleFor(p => p)
            .MustAsync(IsParentId)
            .WithMessage("Only a parent company is authorized to create a license.");

        RuleFor(p => p)
            .MustAsync(IsAuthorizedUser)
            .WithMessage("Access denied. This action can only be performed by a Super Admin or Site Admin.");

        RuleFor(p => p)
            .MustAsync(ValidateLicenseKeyDate)
            .WithMessage("Base License Usable Time Expired, Please Contact Our License Team.");

        RuleFor(p => p)
            .MustAsync(IsValidLicenseKeyAsync)
            .WithMessage("Invalid LicenseKey.");

        RuleFor(p => p)
            .MustAsync(IsCompanyNameValidate)
            .WithMessage("Invalid LicenseKey.");

        RuleFor(p => p)
            .MustAsync(IsRenewalLicenseValidate)
            .WithMessage("Invalid LicenseKey.");

        RuleFor(p => p)
            .MustAsync(IsMacAddressValidAsync)
            .WithMessage("Invalid LicenseKey.");

        RuleFor(p => p)
            .MustAsync(IsIpAddressValidAsync)
            .WithMessage("Invalid LicenseKey.");

        RuleFor(p => p)
            .MustAsync(IsHostNameValidAsync)
            .WithMessage("Invalid LicenseKey.");

        //RuleFor(p => p)
        //    .MustAsync(IsValidLicense);
    }

    private async Task<bool> IsCompanyNameValidate(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var licenseKey = SecurityHelper.Decrypt(e.LicenseKey);

        var licenseList = licenseKey.Split('*');

        var licenseKeyDto = _mapper.Map<LicenseDto>(licenseList);

        var existLicense = await _licenseManagerRepository.GetByReferenceIdAsync(e.Id);

        return await _licenseValidationService.IsCompanyNameValidate(licenseKeyDto.CompanyName,
            existLicense.CompanyName);
    }


    private async Task<bool> IsRenewalLicenseValidate(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var licenseKey = SecurityHelper.Decrypt(e.LicenseKey);

        var licenseList = licenseKey.Split('*');

        var licenseKeyDto = _mapper.Map<LicenseDto>(licenseList);

        return await _licenseValidationService.IsRenewalLicenseValidate(licenseKeyDto.LicenseActionType);
    }

    private async Task<bool> IsMacAddressValidAsync(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var licenseKey = SecurityHelper.Decrypt(e.LicenseKey);

        var licenseList = licenseKey.Split('*');

        var licenseKeyDto = _mapper.Map<LicenseDto>(licenseList);

        return await _licenseValidationService.IsMacAddressValidAsync(licenseKeyDto.MacAddress);
    }

    private async Task<bool> IsIpAddressValidAsync(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var licenseKey = SecurityHelper.Decrypt(e.LicenseKey);

        var licenseList = licenseKey.Split('*');

        var licenseKeyDto = _mapper.Map<LicenseDto>(licenseList);

        return await _licenseValidationService.IsIpAddressValidAsync(licenseKeyDto.IpAddress);
    }

    private async Task<bool> IsHostNameValidAsync(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var licenseKey = SecurityHelper.Decrypt(e.LicenseKey);

        var licenseList = licenseKey.Split('*');

        var licenseKeyDto = _mapper.Map<LicenseDto>(licenseList);

        return await _licenseValidationService.IsHostNameValidAsync(licenseKeyDto.CpHostName);
    }

    private async Task<bool> ValidateLicenseKeyDate(UpdateBaseLicenseCommand e, CancellationToken token)
    {
        var existLicense = await _licenseManagerRepository.GetByReferenceIdAsync(e.Id);

        var existLicenseKey = SecurityHelper.Decrypt(existLicense.LicenseKey).Split('*');

        var existLicenseKeyDto = _mapper.Map<LicenseDto>(existLicenseKey);

        var newLicenseKey = SecurityHelper.Decrypt(e.LicenseKey).Split('*');

        var newLicenseKeyDto = _mapper.Map<LicenseDto>(newLicenseKey);

        if (!existLicenseKeyDto.LicenseGeneratorDate.Equals(newLicenseKeyDto.LicenseGeneratorDate))
            return await _licenseValidationService.ValidateLicenseKeyDate(newLicenseKeyDto.LicenseGeneratorDate);
        return true;
    }


    private Task<bool> IsParentId(UpdateBaseLicenseCommand updateLicenseManagerCommand, CancellationToken cancellation)
    {
        return Task.FromResult(_loggedInUserService.IsParent);
    }

    private Task<bool> IsAuthorizedUser(UpdateBaseLicenseCommand updateLicenseManagerCommand,
        CancellationToken cancellation)
    {
        return Task.FromResult(_loggedInUserService.IsSiteAdmin || _loggedInUserService.IsSuperAdmin);
    }

    private async Task<bool> IsValidLicenseKeyAsync(UpdateBaseLicenseCommand createLicenseManagerCommand,
        CancellationToken token)
    {
        if (createLicenseManagerCommand.LicenseKey.IsNullOrWhiteSpace())
            return false;

        var isValidFormat = await _licenseValidationService.IsLicenseKeyFormatValid(createLicenseManagerCommand.LicenseKey);

        if(!isValidFormat) return false;

        return await _licenseManagerRepository.IsLicenseKeyUnique(createLicenseManagerCommand.Id,
            SecurityHelper.Decrypt(createLicenseManagerCommand.LicenseKey));

       // return await _licenseValidationService.IsLicenseKeyFormatValid(createLicenseManagerCommand.LicenseKey);
    }
}