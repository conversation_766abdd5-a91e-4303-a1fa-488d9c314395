using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Approval;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Update;
using ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixApprovalModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ApprovalMatrixApprovalFixture
{
    public List<ApprovalMatrixApprovalListVm> ApprovalMatrixApprovalListVm { get; }
    public ApprovalMatrixApprovalDetailVm ApprovalMatrixApprovalDetailVm { get; }
    public CreateApprovalMatrixApprovalCommand CreateApprovalMatrixApprovalCommand { get; }
    public UpdateApprovalMatrixApprovalCommand UpdateApprovalMatrixApprovalCommand { get; }
    public ApprovalMatrixApprovalCommand ApprovalMatrixApprovalCommand { get; }

    public ApprovalMatrixApprovalFixture()
    {
        var fixture = new Fixture();

        // Create sample ApprovalMatrixApproval list data
        ApprovalMatrixApprovalListVm = new List<ApprovalMatrixApprovalListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ProcessName = "Workflow Approval Process",
                Description = "Critical workflow requires approval from senior management",
                UserName = "john.doe",
                Status = "Pending",
                ApproverName = "jane.smith",
                CreatedDate = DateTime.Now.AddHours(-2)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ProcessName = "Profile Modification Approval",
                Description = "User profile changes require security team approval",
                UserName = "bob.wilson",
                Status = "Approved",
                ApproverName = "alice.johnson",
                CreatedDate = DateTime.Now.AddHours(-4)
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                RequestId = Guid.NewGuid().ToString(),
                ProcessName = "System Configuration Change",
                Description = "Critical system configuration modification request",
                UserName = "sarah.connor",
                Status = "Rejected",
                ApproverName = "mike.davis",
                CreatedDate = DateTime.Now.AddHours(-6)
            }
        };

        // Create detailed ApprovalMatrixApproval data
        ApprovalMatrixApprovalDetailVm = new ApprovalMatrixApprovalDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Emergency Access Request",
            Description = "Emergency access to production systems for critical incident response",
            UserName = "emergency.admin",
            Status = "Pending",
            Approver = "security.manager",
            StartDateTime = DateTime.Now.AddMinutes(-30),
            EndDateTime = DateTime.Now.AddHours(4),
            IsActive = true,
            CreatedBy = "system.admin",
            CreatedDate = DateTime.Now.AddMinutes(-30),
            LastModifiedBy = "system.admin",
            LastModifiedDate = DateTime.Now.AddMinutes(-30)
        };

        // Create command for creating ApprovalMatrixApproval
        CreateApprovalMatrixApprovalCommand = new CreateApprovalMatrixApprovalCommand
        {
            ProcessName = "New Process Approval",
            Description = "New business process requires management approval",
            UserName = "process.owner",
            Status = "Pending",
            Approver = "department.head",
            StartDateTime = DateTime.Now,
            EndDateTime = DateTime.Now.AddDays(7)
        };

        // Create command for updating ApprovalMatrixApproval
        UpdateApprovalMatrixApprovalCommand = new UpdateApprovalMatrixApprovalCommand
        {
            Id = Guid.NewGuid().ToString(),
            ProcessName = "Updated Process Approval",
            Description = "Updated business process with revised requirements",
            UserName = "updated.owner",
            Status = "In Review",
            Approver = "senior.manager",
            StartDateTime = DateTime.Now,
            EndDateTime = DateTime.Now.AddDays(5)
        };

        //// Create command for approval/rejection
        //ApprovalMatrixApprovalCommand = new ApprovalMatrixApprovalCommand
        //{
        //    Id = Guid.NewGuid().ToString(),
        //    ProcessName = "Approval Process",
        //    Status = "Approved"
        //};
    }
}
