﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetManagedWorkflowProfileInfos;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowProfileInfoService : BaseClient,IWorkflowProfileInfoService
{
    public WorkflowProfileInfoService(IConfiguration config, IAppCache cache, ILogger<WorkflowProfileInfoService> logger)
        : base(config, cache, logger)
    {
       
    }

    public async Task<List<WorkflowProfileInfoNameVm>> GetWorkflowProfileInfoNames()
    {
        var request = new RestRequest("api/v6/workflowprofileinfo/names");

        return await GetFromCache<List<WorkflowProfileInfoNameVm>>(request, "GetWorkflowProfileInfoNames");
    }

    public async Task<List<WorkflowProfileInfoListVm>> GetWorkflowProfileInfoList()
    {
        var request = new RestRequest("api/v6/workflowprofileinfo");

        return await Get<List<WorkflowProfileInfoListVm>>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowProfileInfoId)
    {
        var request = new RestRequest($"api/v6/workflowprofileinfo/{workflowProfileInfoId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowProfileInfoCommand updateWorkflowProfileInfo)
    {
        var request = new RestRequest("api/v6/workflowprofileinfo", Method.Put);

        request.AddJsonBody(updateWorkflowProfileInfo);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowProfileInfoCommand createWorkflowProfileInfo)
    {
        var request = new RestRequest("api/v6/workflowprofileinfo", Method.Post);

        request.AddJsonBody(createWorkflowProfileInfo);

        return await Post<BaseResponse>(request);
    }

    public async Task<WorkflowProfileInfoDetailVm> GetByReferenceId(string workflowProfileId)
    {
        var request = new RestRequest($"api/v6/workflowprofileinfo/{workflowProfileId}");

        return await Get<WorkflowProfileInfoDetailVm>(request);
    }

    public async Task<bool> IsWorkflowProfileInfoNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/workflowprofileinfo/name-exist?name={name}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<GetWorkflowProfileInfoByWorkflowIdVm> WorkflowProfileInfoByWorkflowIdExist(string workflowId)
    {
        var request = new RestRequest($"api/v6/workflowprofileinfo/workflowid-exist?workflowId={workflowId}");

        return await Get<GetWorkflowProfileInfoByWorkflowIdVm>(request);
    }

    public async Task<List<GetWorkflowProfileInfoByProfileIdVm>> GetWorkflowProfileInfoByProfileId(string profileId)
    {
        var request = new RestRequest($"api/v6/workflowprofileinfo/by/{profileId}");

        return await Get<List<GetWorkflowProfileInfoByProfileIdVm>>(request);
    }

    public async Task<PaginatedResult<WorkflowProfileInfoListVm>> GetPaginatedWorkflowProfileInfos(GetWorkflowProfileInfoPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowprofileinfo/paginated-list");

        return await Get<PaginatedResult<WorkflowProfileInfoListVm>>(request);
    }

    public async Task<List<WorkflowProfileInfoNameVm>> WorkflowProfileInfoNames()
    {
        var request = new RestRequest("api/v6/workflowprofileinfo/names");

        return await Get<List<WorkflowProfileInfoNameVm>>(request);
    }

    public Task<BaseResponse> UpdateIsFourEye(string id, string isFourEye)
    {
        throw new NotImplementedException();
    }

    public Task<List<WorkflowProfileInfoNameVm>> GetWorkflowProfileInfoByInfraObjectId(string infraId)
    {
        throw new NotImplementedException();
    }

    public Task<PaginatedResult<WorkflowProfileInfoListVm>> GetManagedWorkflowProfileInfos(GetManagedWorkflowProfileInfosQuery query)
    {
        throw new NotImplementedException();
    }
}