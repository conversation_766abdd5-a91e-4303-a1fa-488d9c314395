using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IFiaImpactCategoryService
{
    Task<List<FiaImpactCategoryListVm>> GetFiaImpactCategoryList();
    Task<BaseResponse> CreateAsync(CreateFiaImpactCategoryCommand createFiaImpactCategoryCommand);
    Task<BaseResponse> UpdateAsync(UpdateFiaImpactCategoryCommand updateFiaImpactCategoryCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<FiaImpactCategoryDetailVm> GetByReferenceId(string id);
    #region NameExist
    Task<bool> IsFiaImpactCategoryNameExist(string name, string id);
    #endregion
    #region Paginated
    Task<PaginatedResult<FiaImpactCategoryListVm>> GetPaginatedFiaImpactCategorys(GetFiaImpactCategoryPaginatedListQuery query);
    #endregion
}
