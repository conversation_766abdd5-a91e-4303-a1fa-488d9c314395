using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class WorkflowExecutionEventLogFixture : IDisposable
{
    public List<WorkflowExecutionEventLog> WorkflowExecutionEventLogPaginationList { get; set; }
    public List<WorkflowExecutionEventLog> WorkflowExecutionEventLogList { get; set; }
    public WorkflowExecutionEventLog WorkflowExecutionEventLogDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public WorkflowExecutionEventLogFixture()
    {
        var fixture = new Fixture();

        WorkflowExecutionEventLogList = fixture.Create<List<WorkflowExecutionEventLog>>();

        WorkflowExecutionEventLogPaginationList = fixture.CreateMany<WorkflowExecutionEventLog>(20).ToList();

        WorkflowExecutionEventLogPaginationList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowExecutionEventLogList.ForEach(x => x.CompanyId = CompanyId);

        WorkflowExecutionEventLogDto = fixture.Create<WorkflowExecutionEventLog>();

        WorkflowExecutionEventLogDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
