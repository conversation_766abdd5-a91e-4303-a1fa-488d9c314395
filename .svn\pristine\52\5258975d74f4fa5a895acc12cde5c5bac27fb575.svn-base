﻿using ContinuityPatrol.Application.Features.User.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.UserModel;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries;

public class GetUserNameQueryHandlerTests : IClassFixture<UserFixture>
{
    private readonly UserFixture _userFixture;
    private Mock<IUserRepository> _mockUserRepository;
    private readonly GetUserNameQueryHandler _handler;

    public GetUserNameQueryHandlerTests(UserFixture userFixture)
    {
        _userFixture = userFixture;
        _mockUserRepository = UserRepositoryMocks.GetUserNamesRepository(_userFixture.Users);
        _handler = new GetUserNameQueryHandler(_userFixture.Mapper, _mockUserRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_Users_Name()
    {
        var result = await _handler.Handle(new GetUserNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<UserNameVm>>();

        result[0].Id.ShouldBe(_userFixture.Users[0].ReferenceId);
        result[0].LoginName.ShouldBe(_userFixture.Users[0].LoginName);
    }

    [Fact]
    public async Task Handle_Return_Active_UserNamesCount()
    {
        var result = await _handler.Handle(new GetUserNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<UserNameVm>>();

        result.Count.ShouldBe(_userFixture.Users.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockUserRepository = UserRepositoryMocks.GetUserEmptyRepository();

        var handler = new GetUserNameQueryHandler(_userFixture.Mapper, _mockUserRepository.Object);

        var result = await handler.Handle(new GetUserNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetUserNamesMethod_OneTime()
    {
        await _handler.Handle(new GetUserNameQuery(), CancellationToken.None);

        _mockUserRepository.Verify(x => x.GetUserNames(), Times.Once);
    }
}