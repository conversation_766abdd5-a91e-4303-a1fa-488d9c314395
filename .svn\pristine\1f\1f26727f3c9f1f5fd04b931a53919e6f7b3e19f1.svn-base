﻿//using ContinuityPatrol.Application.Features.Database.Commands.Create;
//using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
//using ContinuityPatrol.Application.Features.Replication.Commands.Create;
//using ContinuityPatrol.Application.Features.Server.Commands.Create;
//using ContinuityPatrol.Infrastructure.Contract;

//namespace ContinuityPatrol.Application.Features.InfraObject.Commands.CreateBulkData;

//public class CreateBulkDataCommandValidator : AbstractValidator<CreateBulkDataCommand>
//{
//    private readonly IDatabaseRepository _databaseRepository;
//    private readonly IInfraObjectRepository _infraObjectRepository;
//    private readonly IReplicationRepository _replicationRepository;
//    private readonly IServerRepository _serverRepository;
//    private readonly ISiteRepository _siteRepository;
//    private readonly ILicenseValidationService _licenseValidationService;
//    private readonly ILicenseManagerRepository _licenseManagerRepository;
//    private readonly ISiteTypeRepository _siteTypeRepository;
//    private readonly IComponentTypeRepository _componentTypeRepository;

//    public CreateBulkDataCommandValidator(IServerRepository serverRepository, IDatabaseRepository databaseRepository,
//        IReplicationRepository replicationRepository, IInfraObjectRepository infraObjectRepository, ISiteRepository siteRepository, 
//        ILicenseValidationService licenseValidationService, 
//        ILicenseManagerRepository licenseManagerRepository, 
//        ISiteTypeRepository siteTypeRepository,
//        IComponentTypeRepository componentTypeRepository)
//    {
//        _serverRepository = serverRepository;
//        _databaseRepository = databaseRepository;
//        _replicationRepository = replicationRepository;
//        _infraObjectRepository = infraObjectRepository;
//        _siteRepository = siteRepository;
//        _licenseValidationService = licenseValidationService;
//        _licenseManagerRepository = licenseManagerRepository;
//        _siteTypeRepository = siteTypeRepository;
//        _componentTypeRepository = componentTypeRepository;

//        RuleForEach(x => x.ServerCommand)
//            .NotEmpty()
//            //.WithState(_ => nameof(CreateServerCommandValidator))
//            .SetValidator(new CreateServerCommandValidator(_serverRepository,_siteRepository, _licenseValidationService,_licenseManagerRepository, _siteTypeRepository))
//            .NotNull();

//        RuleForEach(x => x.DatabaseCommand)
//            .NotEmpty()
//            //.WithState(_ => nameof(CreateDatabaseCommandValidator))
//            .SetValidator(new CreateDatabaseCommandValidator(_databaseRepository, _serverRepository, _siteRepository, _licenseValidationService, _licenseManagerRepository, _siteTypeRepository))
//            .NotNull();

//        RuleForEach(x => x.ReplicationCommand)
//            .NotEmpty()
//            //.WithState(_ => nameof(CreateReplicationCommandValidator))
//            .SetValidator(new CreateReplicationCommandValidator(_replicationRepository, _siteRepository, _licenseValidationService, _licenseManagerRepository, _siteTypeRepository, _componentTypeRepository))
//            .NotNull();

//        RuleForEach(x => x.InfraObjectCommand)
//            .NotEmpty()
//            //.WithState(_ => nameof(CreateInfraObjectCommandValidator))
//            .SetValidator(new CreateInfraObjectCommandValidator(_infraObjectRepository))
//            .NotNull();
//    }
//}

