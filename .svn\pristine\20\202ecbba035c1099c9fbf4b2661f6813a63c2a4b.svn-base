﻿
function monitorTypeMongoDB(value, infraObjectName, moniterType, parsedData) {
    
    const getDRDetails = (data, value, obj = null) => {

        let tdHtml = '';
        data?.forEach((item, i) => {
            let iconClass = getIconClass(value, item);
            let tableData = obj ? item[obj][value] : item[value];

            tdHtml += `<td class="text-truncate"><i class="${iconClass} me-1"></i>${tableData || 'NA'}</td>`
        })
        return tdHtml
    }
    const getIconClass = (value, monitoringData) => {
        let iconClass = '';

        if (value == 'Server_IpAddress') {
            let text = monitoringData?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'Server_HostName') {
            let text = monitoringData?.Server_Status?.toLowerCase()
            iconClass = text === 'down' ? "cp-down-linearrow me-1 text-danger" : text === 'pending' ? 'cp-pending me-1 text-warning' : text === 'up' ? "cp-up-linearrow me-1 text-success" : "cp-pending me-1 text-warning";

        } else if (value === 'DBName') {

            iconClass = monitoringData?.MongoDBMonitoring?.DBName ? 'cp-database me-1 text-primary' : "cp-disable me-1 text-danger";

        } else if (value === 'MongodbStatus') {
            iconClass = monitoringData?.MongoDBMonitoring?.MongodbStatus?.toLowerCase() === "running" ? "text-success cp-reload cp-animate me-1 fs-6" : monitoringData?.MongoDBMonitoring?.MongodbStatus?.toLowerCase()?.includes("na") ? "cp-disable me-1 text-danger" : "cp-error me-1 text-danger";

        } else if (value === 'DBVersion') {
            iconClass = monitoringData?.MongoDBMonitoring?.DBVersion ? "text-primary me-1 cp-database" : "cp-disable me-1 text-danger";

        } else if (value === 'StateDescription') {
            iconClass = monitoringData?.MongoDBMonitoring?.StateDescription ? "text-success cp-relationship-state me-1" : "cp-disable me-1 text-danger";

        } else if (value === 'Health') {
            iconClass = monitoringData?.MongoDBMonitoring?.Health?.toLowerCase() === "up" ? "text-success me-1 cp-health" : monitoringData?.MongoDBMonitoring?.Health?.toLowerCase()?.includes('na') ? "cp-disable me-1 text-danger" :"cp-error me-1 text-danger";

        } else if (value === 'LastHeartbeatMessage') {
            iconClass = monitoringData?.MongoDBMonitoring?.LastHeartbeatMessage?.toLowerCase()!=="na" ? "text-success me-1 cp-online" : "cp-disable me-1 text-danger";

        } else if (value === 'ReplicaSetName') {
            iconClass = monitoringData?.MongoDBMonitoring?.ReplicaSetName ? "text-success me-1 cp-replication-source" : "cp-disable me-1 text-danger";

        } else if (value === 'MemberID') {
            iconClass = monitoringData?.MongoDBMonitoring?.MemberID ? "text-primary me-1 cp-id" : "cp-disable me-1 text-danger";

        } else if (value === 'CurrentPriority') {
            iconClass = monitoringData?.MongoDBMonitoring?.CurrentPriority ? "text-success me-1 cp-priority" : "cp-disable me-1 text-danger";

        } else if (value === 'Datalag') {
            iconClass = monitoringData?.MongoDBMonitoring?.Datalag ? "cp-time text-primary mt-2" : "cp-disable me-1 text-danger";

        } 

        return iconClass;
    }

    const getDynamicHeader = (MongoDBMonitoringModel) => {

        let dynamicHeader = '';

        MongoDBMonitoringModel?.length && MongoDBMonitoringModel?.map((data) => {
            dynamicHeader += `<th>${data?.Type}</th>`
        })

        return dynamicHeader;
    }
    if (moniterType === "MongoDB") {
        let application = value?.monitorServiceDetails[0]?.isServiceUpdate?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.isServiceUpdate?.toLowerCase()?.includes("running") ? "text-success cp-reload cp-animate" : "text-danger cp-fail-back";
        let workflow = value?.monitorServiceDetails[0]?.workflowName?.includes("NA") ? "cp-disable me-1 text-danger" : value?.monitorServiceDetails[0]?.workflowName ? "cp-log-archive-config me-1 text-primary" : "cp-disable me-1 text-danger";
        let prreplicationStatus = parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRlastHeartbeatMessage?.includes("NA") ? "cp-disable me-1 text-danger" : "text-success me-1 cp-online";
        let prdatabase = parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBName ? "cp-database me-1 text-primary" : "cp-disable me-1 text-danger";

        let prrecoverystate = parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus === "Running" ? "text-success cp-reload cp-animate me-1 fs-6" : "cp-error me-1 text-danger";
       
        let pripaddress = value?.prServerStatus?.includes("NA") ? "cp-down-linearrow me-1 text-danger" : (value?.prServerStatus)?.toLowerCase() === "up" ? "cp-up-linearrow me-1 text-success" : "cp-down-linearrow me-1 text-danger";
        
        let PRhealth = parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth?.includes("NA") ? "cp-disable me-1 text-danger" : parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth === "up" ? "text-success me-1 cp-health" : "cp-error me-1 text-danger";
        //let ipprdata = parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.Pr_ConnectViaHostName.toLowerCase() === "true" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_HostName : parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_IpAddress
        let infraobjectdata =
            '<div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;">' +
            '<tr>' +
            '<th>Component Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MongoDBMonitoringModel)}` +
            '</tr>' + 
            '</thead>' +
            '<tbody>' +
            '<tr>' +
            '<td>' + 'IP Address/Host Name' + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_IpAddress !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_IpAddress !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_IpAddress !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_IpAddress : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'Server_IpAddress')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + 'Database Name' + '</td>' +
            '<td>' + '<i class="' + prdatabase + '"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBName !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBName !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBName !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'DBName','MongoDBMonitoring')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "MongoDB Status" + '</td>' +
            '<td>' + '<i class="' + prrecoverystate + '"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRMongodbStatus : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'MongodbStatus','MongoDBMonitoring')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "Host Name" + '</td>' +
            '<td>' + '<i class="' + pripaddress + '"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_HostName !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_HostName !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_HostName !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PR_Server_HostName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'Server_HostName')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "Version" + '</td>' +
            '<td>' + '<i class="text-primary me-1 cp-database"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBVersion !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBVersion !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBVersion !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRDBVersion : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'DBVersion','MongoDBMonitoring')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "State Description" + '</td>' +
            '<td>' + '<i class="text-success cp-relationship-state me-1"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRStateDescription !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRStateDescription !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRStateDescription !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRStateDescription : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'StateDescription','MongoDBMonitoring')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "Health" + '</td>' +
            '<td>' + '<i class="' + PRhealth + '"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRhealth : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'Health','MongoDBMonitoring')}` +
            
            '</tr>' +
            '<tr>' +
            '<td>' + "LastHeartbeatMessage" + '</td>' +
            '<td>' + '<i class="'+prreplicationStatus+'"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRlastHeartbeatMessage !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRlastHeartbeatMessage !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRlastHeartbeatMessage !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRlastHeartbeatMessage : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'LastHeartbeatMessage','MongoDBMonitoring')}` +
            
            '</tr>';
            infraobjectdata += generateMonitorServiceDetailsRow(workflow, application, value?.monitorServiceDetails);
        infraobjectdata += '</tbody>' +
           
            '</table>' +
            '</div>' +
            '<div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">' +
            '<table class="table table-hover mb-0 table-sm">' +
            '<thead style="position: sticky;top: 0px;z-index: 1;">' +
            '<tr>' +
            '<th>Replication Monitor</th>' +
            '<th>Production Server</th>' +
            `${getDynamicHeader(parsedData?.MongoDBMonitoringModel)}` +
            '</tr>' +
            '</thead>' +
            '<tbody style="font-size:10px">' +
            '<tr>' +
            '<td>' + "ReplicaSet Name" + '</td>' +
            '<td>' + '<i class="text-success me-1 cp-replication-source"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRreplicaSetName !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRreplicaSetName !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRreplicaSetName !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRreplicaSetName : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'ReplicaSetName', 'MongoDBMonitoring')}` +
        
            '</tr>' +
            '<tr>' +
            '<td>' + "MemberID" + '</td>' +
            '<td>' + '<i class="text-primary me-1 cp-id"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRmemberID !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRmemberID !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRmemberID !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRmemberID : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'MemberID', 'MongoDBMonitoring')}` +
        
            '</tr>' +
            '<tr>' +
            '<td>' + "Current Priority" + '</td>' +
            '<td>' + '<i class="text-success me-1 cp-priority"></i>' + (parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRCurrentPriority !== undefined && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRCurrentPriority !== null && parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRCurrentPriority !== "" ? parsedData?.PRMongoDBMonitoringPRModel?.PRMongoDBMonitoringModel?.PRMongoDBMonitoring?.PRCurrentPriority : 'NA') + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'CurrentPriority', 'MongoDBMonitoring')}` +
        
            '</tr>' +
            '<tr>' +
            '<td>' + "Datalag" + '</td>' +
            `${getDRDetails(parsedData?.MongoDBMonitoringModel, 'Datalag', 'MongoDBMonitoring')}` +
           
            '</tr>' +           
            '</tbody>' +
            '</table>' +
            '</div>' 
            


        setTimeout(() => {
            $("#infraobjectalldata").append(infraobjectdata);
        }, 200)


    }
}
