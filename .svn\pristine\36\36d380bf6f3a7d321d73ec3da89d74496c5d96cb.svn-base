﻿using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Infrastructure.Contract;

public interface IWindowsService
{
    Task<bool> GetAsync(string url, [Optional] string type);

    Task<ServiceResponse> CheckWindowsService(string url);

    Task<bool> IsTcpClientConnectionSuccessfulAsync(string ipAddress, int port);

    // Task<(bool Success, string Content)> CheckWindowsService(string url);
    // Task<(bool Success, ServiceResponse Content)> CheckWindowsService1(string url);
}