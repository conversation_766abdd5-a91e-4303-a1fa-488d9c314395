﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupNameQueryHandlerTests : IClassFixture<WorkflowOperationGroupFixture>
{
    private readonly WorkflowOperationGroupFixture _workflowOperationGroupFixture;

    private Mock<IWorkflowOperationGroupRepository> _mockWorkflowOperationGroupRepository;

    private readonly GetWorkflowOperationGroupNameQueryHandler _handler;

    public GetWorkflowOperationGroupNameQueryHandlerTests(WorkflowOperationGroupFixture workflowOperationGroupFixture)
    {
        _workflowOperationGroupFixture = workflowOperationGroupFixture;

        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupNamesRepository(_workflowOperationGroupFixture.WorkflowOperationGroups);

        _handler = new GetWorkflowOperationGroupNameQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowOperationGroup_Name()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowOperationGroupNameVm>>();
        result[0].Id = _workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId;
        result[0].CurrentActionName = _workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionName;

        result[0].Id.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].ReferenceId);
        result[0].CurrentActionName.ShouldBe(_workflowOperationGroupFixture.WorkflowOperationGroups[0].CurrentActionName);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowOperationGroupNamesCount()
    {
        var result = await _handler.Handle(new GetWorkflowOperationGroupNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowOperationGroupNameVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowOperationGroupRepository = WorkflowOperationGroupRepositoryMocks.GetWorkflowOperationGroupEmptyRepository();

        var handler = new GetWorkflowOperationGroupNameQueryHandler(_workflowOperationGroupFixture.Mapper, _mockWorkflowOperationGroupRepository.Object);

        var result = await handler.Handle(new GetWorkflowOperationGroupNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowOperationGroupNamesMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowOperationGroupNameQuery(), CancellationToken.None);

        _mockWorkflowOperationGroupRepository.Verify(x => x.GetWorkflowOperationGroupNames(), Times.Once);
    }
}