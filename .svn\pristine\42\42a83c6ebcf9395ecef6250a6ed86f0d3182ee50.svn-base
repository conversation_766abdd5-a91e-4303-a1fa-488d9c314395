﻿using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;

namespace ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetailByName;

public class GetGlobalVariableDetailByNameQueryHanlder:IRequestHandler<GetGlobalVariableDetailByNameQuery,List<GlobalVariableDetailVm>>
{
    private readonly IGlobalVariableRepository _globalVariableRepository;
    private readonly IMapper _mapper;
    public GetGlobalVariableDetailByNameQueryHanlder(IGlobalVariableRepository globalVariableRepository,IMapper mapper)
    {
        _globalVariableRepository = globalVariableRepository;
        _mapper = mapper;
    }

    public async  Task<List<GlobalVariableDetailVm>> Handle(GetGlobalVariableDetailByNameQuery request, CancellationToken cancellationToken)
    {

        var globalVariable = await _globalVariableRepository.GetByVariableName(request?.Name.ToLower());
       
        var globalVariableDetailDto = _mapper.Map<List<GlobalVariableDetailVm>>(globalVariable);

        return globalVariableDetailDto;
    }
}
