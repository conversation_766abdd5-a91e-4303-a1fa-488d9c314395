﻿using ContinuityPatrol.Application.Features.ServerLog.Events.Create;

namespace ContinuityPatrol.Application.Features.ServerLog.Commands.Create;

public class CreateServerLogCommandHandler : IRequestHandler<CreateServerLogCommand, CreateServerLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerLogRepository _serverLogRepository;

    public CreateServerLogCommandHandler(IMapper mapper, IPublisher publisher, IServerLogRepository serverLogRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _serverLogRepository = serverLogRepository;
    }

    public async Task<CreateServerLogResponse> Handle(CreateServerLogCommand request, CancellationToken cancellationToken)
    {
        var serverLog = _mapper.Map<Domain.Entities.ServerLog>(request);
        serverLog = await _serverLogRepository.AddAsync(serverLog);
        var response = new CreateServerLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.ServerLog), serverLog.Name),
            Id = serverLog.ReferenceId
        };
        await _publisher.Publish(new ServerLogCreatedEvent { Name = serverLog.Name}, cancellationToken);
        return response;
    }
}
