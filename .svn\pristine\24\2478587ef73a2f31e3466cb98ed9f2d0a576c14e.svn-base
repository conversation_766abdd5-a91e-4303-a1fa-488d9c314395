﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class LicenseInfoRepository : BaseRepository<LicenseInfo>, ILicenseInfoRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public LicenseInfoRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<int> GetAvailableCountByLicenseId(string licenseId, string entity)
    {
        return await _dbContext.LicenseInfo.Active().CountAsync(x =>
            x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()));
    }






    public async Task<int> GetAvailableCountByLicenseIdAndBusinessServiceId(string licenseId, string entity,string businessServiceId)
    {
        return await _dbContext.LicenseInfo.Active().CountAsync(x =>
            x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()) && x.BusinessServiceId.Equals(businessServiceId));
    }

    public async Task<int> GetAvailableLicenseByLicenseIdAndEntityType(string licenseId, string entity,
        string entityType)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .CountAsync(x =>
                x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()) &&
                x.EntityType.ToLower().Equals(entityType.ToLower()));
    }


    public async Task<List<GroupedLicenseInfo>> GroupByUsageCountByLicenseId(List<string> licenseId)
    {
        var licenseInfoList = await _dbContext.LicenseInfo.Active().Where(x => licenseId.Contains(x.LicenseId))
            .ToListAsync();

        return licenseInfoList.GroupBy(x => new { x.LicenseId, x.PONumber, x.Entity, x.EntityType })
            .Select(g => new GroupedLicenseInfo
            {
                LicenseId = g.Key.LicenseId,
                PoNumber = g.Key.PONumber,
                Entity = g.Key.Entity,
                EntityType = g.Key.EntityType,
                Count = g.Count()
            }).ToList();
    }



    public async Task<int> GetAvailableLicenseByLicenseIdAndEntityTypeAndBusinessServiceId(string licenseId, string entity,
        string entityType,string businessServiceId)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .CountAsync(x =>
                x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()) &&
                x.EntityType.ToLower().Equals(entityType.ToLower()) && x.BusinessServiceId.Equals(businessServiceId));
    }

    public async Task<LicenseInfo> GetByEntityId(string id)
    {
        return _loggedInUserService.IsParent
            ? await _dbContext.LicenseInfo.Active().FirstOrDefaultAsync(x => x.EntityId.Equals(id))
            : await _dbContext.LicenseInfo.Active().Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .FirstOrDefaultAsync(x => x.EntityId.Equals(id));
    }

    public override Task<IReadOnlyList<LicenseInfo>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? base.ListAllAsync()
            : FindByFilter(company => company.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public async Task<List<LicenseInfo>> GetLicenseByBusinessServiceId(string bServiceId)
    {
        return _loggedInUserService.IsParent
            ? await _dbContext.LicenseInfo
                .Active()
                .Where(x => x.BusinessServiceId.Equals(bServiceId))
                .ToListAsync()
            : await _dbContext.LicenseInfo
                .Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            x.BusinessServiceId.Equals(bServiceId))
                .ToListAsync();
        
    }
    public async Task<List<LicenseInfo>> GetLicenseByBusinessServiceIdAndLicenseId(string bServiceId, string licenseId)
    {
        var t = _loggedInUserService.IsParent
            ? await _dbContext.LicenseInfo.Active()
            .Where(x => x.BusinessServiceId.Equals(bServiceId) && x.LicenseId.Equals(licenseId)).ToListAsync()
            : await _dbContext.LicenseInfo.Active()
            .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.BusinessServiceId.Equals(bServiceId) && x.LicenseId.Equals(licenseId)).ToListAsync();
        return t;
    }

    public async Task<List<LicenseInfo>> GetLicenseInfoDetailByLicenseId(string licenseId)
    {
        return _loggedInUserService.IsParent
            ? await _dbContext.LicenseInfo
                .Active()
                .Where(x => x.LicenseId.Equals(licenseId))
                .ToListAsync()
            : await _dbContext.LicenseInfo
                .Active()
                .Where(x => x.LicenseId.Equals(licenseId))
                .ToListAsync();
    }

    public async Task<List<LicenseInfo>> GetLicenseInfoByLicenseIdAndEntity(string licenseId, string entity)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .Where(x => x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()))
            .ToListAsync();
    }

    public async Task<List<LicenseInfo>> GetLicenseInfoByLicenseIdAndEntityAndEntityType(string licenseId,
        string entity, string entityType)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .Where(x => x.LicenseId.Equals(licenseId) && x.Entity.ToLower().Equals(entity.ToLower()) &&
                        x.EntityType.ToLower().Equals(entityType.ToLower()))
            .ToListAsync();
    }

    public async Task<LicenseInfo> GetLicenseInfoByLicenseIdAndEntityId(string licenseId, string entityId)
    {
        return await _dbContext.LicenseInfo.Active()
            .Where(x => x.LicenseId.Equals(licenseId) && x.EntityId.Equals(entityId)).FirstOrDefaultAsync();
    }

    public async Task<LicenseInfo> GetLicenseInfoByEntityId(string entityId)
    {
        return _loggedInUserService.IsParent
            ? await _dbContext.LicenseInfo.Active()
                .Where(x => x.EntityId.Equals(entityId))
                .FirstOrDefaultAsync()
            : await _dbContext.LicenseInfo
                .Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.EntityId.Equals(entityId))
                .FirstOrDefaultAsync();
    }
    public async Task<PaginatedResult<LicenseInfo>> GetLicenseInfoEntityQueryable(string entity, int pageNumber, int pageSize, Specification<LicenseInfo> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsParent
            ? Entities.Specify(productFilterSpec).Where(x => x.Entity.ToLower().Equals(entity.ToLower())).DescOrderById()
            : Entities.Specify(productFilterSpec).Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) && x.Entity.ToLower().Equals(entity.ToLower()))
                .DescOrderById()).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
    public IQueryable<LicenseInfo> GetLicenseInfoEntityQueryable(string entity)
    {
        return _loggedInUserService.IsParent
            ? _dbContext.LicenseInfo.Active().Where(x => x.Entity.ToLower().Equals(entity.ToLower()))
                .OrderByDescending(x => x.Id)
            : _dbContext.LicenseInfo.Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            x.Entity.ToLower().Equals(entity.ToLower())).OrderByDescending(x => x.Id);
    }

    public async Task<List<LicenseInfo>> GetLicenseInfoByLicenseIdAndType(string licenseId, string type)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .Where(x => x.LicenseId.Equals(licenseId) && x.Type.ToLower().Equals(type.ToLower())).ToListAsync();
    }

    public async Task<List<LicenseInfo>> GetLicenseInfoByLicenseIdAndTypeAndEntityType(string licenseId, string type,
        string entityType)
    {
        return await _dbContext.LicenseInfo
            .Active()
            .Where(x => x.LicenseId.Equals(licenseId) && x.Type.ToLower().Equals(type.ToLower()) &&
                        x.EntityType.ToLower().Equals(entityType.ToLower()))
            .ToListAsync();
    }
}