﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class NodeRepositoryMocks
{
    public static Mock<INodeRepository> CreateNodeRepository(List<Node> nodes)
    {
        var nodeRepository = new Mock<INodeRepository>();

        nodeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodes);

        nodeRepository.Setup(repo => repo.AddAsync(It.IsAny<Node>())).ReturnsAsync((Node node) =>
        {
            node.Id = new Fixture().Create<int>();

            node.ReferenceId = new Fixture().Create<Guid>().ToString();

            nodes.Add(node);

            return node;
        });

        return nodeRepository;
    }

    public static Mock<INodeRepository> UpdateNodeRepository(List<Node> nodes)
    {
        var nodeRepository = new Mock<INodeRepository>();

        nodeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodes);

        nodeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodes.SingleOrDefault(x => x.ReferenceId == i));

        nodeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Node>())).ReturnsAsync((Node node) =>
        {

            var index = nodes.FindIndex(item => item.Id == node.Id);

            nodes[index] = node;

            return node;
        });

        return nodeRepository;
    }

    public static Mock<INodeRepository> DeleteNodeRepository(List<Node> nodes)
    {
        var mockNodeRepository = new Mock<INodeRepository>();

        mockNodeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodes);

        mockNodeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodes.SingleOrDefault(x => x.ReferenceId == i));

        mockNodeRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Node>())).ReturnsAsync((Node node) =>
        {
            var index = nodes.FindIndex(item => item.Id == node.Id);

            node.IsActive = false;

            nodes[index] = node;

            return node;
        });

        return mockNodeRepository;
    }

    public static Mock<INodeRepository> GetNodeRepository(List<Node> nodes)
    {
        var mockNodeRepository = new Mock<INodeRepository>();

        mockNodeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(nodes);

        mockNodeRepository.Setup(repo => repo.GetNodeListType(It.IsAny<string>())).ReturnsAsync(nodes);

        mockNodeRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => nodes.SingleOrDefault(x => x.ReferenceId == i));

        return mockNodeRepository;
    }

    public static Mock<INodeRepository> GetNodeNameUniqueRepository(List<Node> nodes)
    {
        var mockNodeRepository = new Mock<INodeRepository>();

        mockNodeRepository.Setup(repo => repo.IsNodeNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => nodes.Exists(x => x.Name == i && x.ReferenceId == j));

        return mockNodeRepository;
    }

    public static Mock<INodeRepository> GetNodeEmptyRepository()
    {
        var mockNodeRepository = new Mock<INodeRepository>();

        mockNodeRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Node>());

        mockNodeRepository.Setup(repo => repo.GetNodeListType(It.IsAny<string>())).ReturnsAsync(new List<Node>());

        return mockNodeRepository;
    }

    public static Mock<INodeRepository> GetNodeNamesRepository(List<Node> nodes)
    {
        var mockNodeRepository = new Mock<INodeRepository>();

        mockNodeRepository.Setup(repo => repo.GetNodeNames()).ReturnsAsync(nodes);

        return mockNodeRepository;
    }

    public static Mock<INodeRepository> GetPaginatedNodeRepository(List<Node> nodes)
    {
        var nodeRepository = new Mock<INodeRepository>();

        var queryableNode = nodes.BuildMock();

        nodeRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNode);

        return nodeRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateNodeEventRepository(List<UserActivity> userActivities)
    {
        var nodeEventRepository = new Mock<IUserActivityRepository>();

        nodeEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        nodeEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
        {
            userActivity.Id = new Fixture().Create<int>();

            userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

            userActivities.Add(userActivity);

            return userActivity;
        });

        return nodeEventRepository;
    }
}