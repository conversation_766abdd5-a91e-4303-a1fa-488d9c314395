﻿using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Shared.Core.Extensions;
using Microsoft.AspNetCore.Diagnostics;

namespace ContinuityPatrol.Web.Controllers;

public class ErrorController : BaseController
{
    private readonly ILogger<ErrorController> _logger;

    public ErrorController(ILogger<ErrorController> logger)
    {
        _logger = logger;
    }

    [Route("Error/{statusCode:int}")]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult HttpStatusCodeHandler(int statusCode)
    {
        var result = HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

        switch (statusCode)
        {
            case 400:
                _logger.LogWarning($"BadRequest status code encountered. Request may be malformed or invalid. Path: {result?.OriginalPath}");
                ViewBag.ErrorMessage = "BadRequest";

                return View("BadRequest");

            case 401:
                _logger.LogWarning($"Unauthorized status code encountered. User is not authenticated to access the resource. Path: {result?.OriginalPath}");
                return View("Unauthorized");

            case 403:
                _logger.LogWarning($"Forbidden status code encountered. Access to the resource is denied. Path: {result?.OriginalPath}");
                ViewBag.ErrorMessage = "Access denied or bad request.";
                return View("Forbidden");

            case 404:
                _logger.LogWarning($"NotFound status code encountered. The requested resource was not found. Path: {result?.OriginalPath}");
                var notFoundModel = new ErrorViewModel
                {
                    Message = "The resource you requested was not found.",
                    StackTrace = string.Empty
                };
                return View("NotFound", notFoundModel);

            case 500:
                _logger.LogError($"ServerError status code encountered. An internal server error occurred while processing the request. Path: {result?.OriginalPath}" );
                ViewBag.ErrorMessage = "An internal server error occurred while processing the request.";
                return View("ServerError");

            default:
                _logger.LogWarning($"Unexpected status code encountered: {statusCode}. Path: {result?.OriginalPath}");
                ViewBag.ErrorMessage = "An unexpected error occurred.";
                return View("BadRequest");
        }
    }

    [Route("Error")]
    [AllowAnonymous]
    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public async Task<IActionResult> UnhandledException()
    {
        _logger.LogInformation($"ErrorController : Enter Global Unhandled Exception");

        await CleanSignOut();

        var pathFeature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();

        var exception = pathFeature?.Error;

        if (exception is null)
        {
            _logger.LogInformation($"ErrorController : Exception is null return view ");

            return View();
        }

        _logger.LogError($"Global Exception Handling : Error Message : {exception.Message}, Path :{pathFeature.Path}");

        if (exception.Message.Contains("Session logged out"))
        {
            TempData.NotifyWarning("Session logged out, no action taken too long");

            _logger.LogError("Session logged out, no action taken too long");

            //return RedirectToAction("PreLogin", "Account");

            return RedirectToAction("Logout", "Account");
        }
        _logger.LogError($"Global Exception Handling : Error Message : {exception.Message}, Path :{pathFeature.Path}");

        var errorViewModel = new ErrorViewModel
        {
            Message = exception.Message,
            Path = pathFeature.Path
        };

        return View(errorViewModel);
    }
}
