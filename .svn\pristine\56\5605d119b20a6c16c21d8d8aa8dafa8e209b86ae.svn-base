﻿namespace ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Update;

public class UpdateBusinessServiceHealthLogCommandHandler : IRequestHandler<UpdateBusinessServiceHealthLogCommand,
    UpdateBusinessServiceHealthLogResponse>
{
    private readonly IBusinessServiceHealthLogRepository _businessServiceLogRepository;
    private readonly IMapper _mapper;

    public UpdateBusinessServiceHealthLogCommandHandler(IBusinessServiceHealthLogRepository drReadyLogRepository,
        IMapper mapper)
    {
        _businessServiceLogRepository = drReadyLogRepository;
        _mapper = mapper;
    }

    public async Task<UpdateBusinessServiceHealthLogResponse> Handle(UpdateBusinessServiceHealthLogCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _businessServiceLogRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.BusinessServiceHealthLog), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateBusinessServiceHealthLogCommand),
            typeof(Domain.Entities.BusinessServiceHealthLog));

        await _businessServiceLogRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBusinessServiceHealthLogResponse
        {
            Message = Message.Update(nameof(Domain.Entities.BusinessServiceHealthLog), eventToUpdate.ReferenceId),
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}