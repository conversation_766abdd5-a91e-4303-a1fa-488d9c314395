﻿.Report-Header {
    text-align: center;
    padding: 5px;
    background-color: var(--bs-secondary);
    color: var(--bs-white);
    font-size: var(--bs-nav-menu-font-size);
    margin: 10px 0px;
}

.Report-Scroll {
    overflow-y: auto;
    height: calc(100vh - 157px);
}

.status_warning {
    display: block !important;
    border-radius: 1rem;
    margin: auto;
    width: 12px;
    height: 12px;
}

/*Custom Report Style*/
.Custom_Report_Bg {
    /*    background: radial-gradient(#efefef 3px, transparent 4px), radial-gradient(#efefef 3px, transparent 4px), linear-gradient(#fff 4px, transparent 0), linear-gradient(45deg, transparent 74px, transparent 75px, #efefef 75px, #efefef 76px, transparent 77px, transparent 109px), linear-gradient(-45deg, transparent 75px, transparent 76px, #efefef 76px, #efefef 77px, transparent 78px, transparent 109px), #fff;
    background-size: 109px 109px, 109px 109px,100% 6px, 109px 109px, 109px 109px;
    background-position: 54px 55px, 0px 0px, 0px 0px, 0px 0px, 0px 0px;
    background-color: #efefef;*/
    background-image: linear-gradient(#e1e1e1 1px, transparent 1px), linear-gradient(90deg, #e1e1e1, transparent 1px), linear-gradient(#f9f9f9 1px, transparent 1px), linear-gradient(90deg, #f9f9f9 1px, transparent 1px);
    background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
    background-position: -2px -2px, -2px -2px, -1px -1px, -1px -1px;
}

.CreateWidget_accordion {
    --bs-accordion-btn-icon-width: 0.9rem !important;
    --bs-accordion-btn-padding-x: 0.8rem;
    --bs-accordion-btn-padding-y: 0.5rem;
}

    .CreateWidget_accordion .accordion-button {
        font-size: 13px;
    }

.drag > li {
    cursor: move !important;
    border: 1px solid var(--bs-gray-300);
    background: #fff;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    border-radius: 5px !important;
    width: 30%;
    height: 70px;
    float: left;
    margin: 5px 5px !important;
    box-shadow: 0 0.1rem 0.5rem rgba(0, 0, 0, 0.1);
}

.drag .drag-button {
    cursor: move;
    line-height: 1.8em;
    display: block;
    height: 100%;
    width: 100%;
    background: transparent;
    border: 0;
    text-align: left;
    padding: 10px;
    border-radius: 0;
    display: grid;
    align-items: center;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    justify-content: center;
}

    .drag .drag-button > span {
        float: none !important;
        margin-right: auto !important;
        text-align: center !important;
        width: 20px !important;
        height: 20px !important;
        margin: auto;
    }

/* End Custom Report Style*/