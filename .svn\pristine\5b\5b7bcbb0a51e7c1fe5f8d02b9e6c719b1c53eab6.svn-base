﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Alert.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Alert.Controllers
{
    public class ManageAlertControllerTests
    {
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<ILogger<ManageAlertController>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private ManageAlertController _controller;

        public ManageAlertControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new ManageAlertController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockMapper.Object,
                _mockDataProvider.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldCreateNewAlert_WhenIdIsNull()
        {
            var viewModel = new Fixture().Create<AlertMasterListVm>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateAlertMasterCommand();

            var alertModel = new AlertMasterListVm();
            var createCommand = new CreateAlertMasterCommand();
            _mockMapper.Setup(m => m.Map<CreateAlertMasterCommand>(alertModel))
                       .Returns(createCommand);

            var response = new BaseResponse { Success = true };
            _mockDataProvider.Setup(d => d.AlertMasterService.CreateAsync(createCommand))
                             .ReturnsAsync(response);

            var controllerContext = new ControllerContext();
            controllerContext.HttpContext = new DefaultHttpContext();
            controllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>());

            _controller.ControllerContext = controllerContext;

            var result = await _controller.CreateOrUpdate(alertModel) as JsonResult;

            Assert.NotNull(result);
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
            _mockDataProvider.Verify(d => d.AlertMasterService.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_ShouldUpdateAlert_WhenIdIsProvided()
        {
            var alertModel = new AlertMasterListVm();
            var updateCommand = new UpdateAlertMasterCommand();
            _mockMapper.Setup(m => m.Map<UpdateAlertMasterCommand>(alertModel))
                       .Returns(updateCommand);

            var response = new BaseResponse { Success = true };
            _mockDataProvider.Setup(d => d.AlertMasterService.UpdateAsync(updateCommand))
                             .ReturnsAsync(response);

            var controllerContext = new ControllerContext();
            controllerContext.HttpContext = new DefaultHttpContext();
            controllerContext.HttpContext.Request.Form = new FormCollection(new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>
            {
              { "id", "some-id" }
            });

            _controller.ControllerContext = controllerContext;

            var result = await _controller.CreateOrUpdate(alertModel) as JsonResult;
            Assert.NotNull(result);         
            var json = JsonConvert.SerializeObject(result);
            Assert.Contains("\"Success\":true", json);
            _mockDataProvider.Verify(d => d.AlertMasterService.UpdateAsync(updateCommand), Times.Once);
        }
    }
}
