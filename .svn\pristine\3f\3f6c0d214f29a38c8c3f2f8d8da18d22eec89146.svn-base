using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class DynamicDashboardWidgetService : BaseService,IDynamicDashboardWidgetService
{
    public DynamicDashboardWidgetService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DynamicDashboardWidgetListVm>> GetDynamicDashboardWidgetList()
    {
        Logger.LogDebug("Get All DynamicDashboardWidgets");

        return await Mediator.Send(new GetDynamicDashboardWidgetListQuery());
    }

    public async Task<DynamicDashboardWidgetDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardWidget Id");

        Logger.LogDebug($"Get DynamicDashboardWidget Detail by Id '{id}'");

        return await Mediator.Send(new GetDynamicDashboardWidgetDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateDynamicDashboardWidgetCommand createDynamicDashboardWidgetCommand)
    {
        Logger.LogDebug($"Create DynamicDashboardWidget '{createDynamicDashboardWidgetCommand}'");

        return await Mediator.Send(createDynamicDashboardWidgetCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDynamicDashboardWidgetCommand updateDynamicDashboardWidgetCommand)
    {
        Logger.LogDebug($"Update DynamicDashboardWidget '{updateDynamicDashboardWidgetCommand}'");

        return await Mediator.Send(updateDynamicDashboardWidgetCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DynamicDashboardWidget Id");

        Logger.LogDebug($"Delete DynamicDashboardWidget Details by Id '{id}'");

        return await Mediator.Send(new DeleteDynamicDashboardWidgetCommand { Id = id });
    }
     #region NameExist
    public async Task<bool> IsDynamicDashboardWidgetNameExist(string name, string id)
    {
     Guard.Against.NullOrWhiteSpace(name, "DynamicDashboardWidget Name");

     Logger.LogDebug($"Check Name Exists Detail by DynamicDashboardWidget Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetDynamicDashboardWidgetNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

     #region Paginated
    public async Task<PaginatedResult<DynamicDashboardWidgetListVm>> GetPaginatedDynamicDashboardWidgets(GetDynamicDashboardWidgetPaginatedListQuery query)
    {
    Logger.LogDebug("Get Searching Details in DynamicDashboardWidget Paginated List");

    return await Mediator.Send(query);
    }
     #endregion
}
