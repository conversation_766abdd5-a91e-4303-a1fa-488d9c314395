using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DynamicSubDashboardFixture : IDisposable
{
    public List<DynamicSubDashboard> DynamicSubDashboardPaginationList { get; set; }
    public List<DynamicSubDashboard> DynamicSubDashboardList { get; set; }
    public DynamicSubDashboard DynamicSubDashboardDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string SubDashboardName = "TestSubDashboard";
    public const string DashboardId = "DASHBOARD_123";
    public const string Description = "Test Sub Dashboard Description";
    public const string Layout = "Grid";

    public ApplicationDbContext DbContext { get; private set; }

    public DynamicSubDashboardFixture()
    {
        var fixture = new Fixture();

        DynamicSubDashboardList = fixture.Create<List<DynamicSubDashboard>>();

        DynamicSubDashboardPaginationList = fixture.CreateMany<DynamicSubDashboard>(20).ToList();

        DynamicSubDashboardPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicSubDashboardPaginationList.ForEach(x => x.IsActive = true);
    

        DynamicSubDashboardList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DynamicSubDashboardList.ForEach(x => x.IsActive = true);
      

        DynamicSubDashboardDto = fixture.Create<DynamicSubDashboard>();
        DynamicSubDashboardDto.ReferenceId = Guid.NewGuid().ToString();
        DynamicSubDashboardDto.IsActive = true;
      
        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
