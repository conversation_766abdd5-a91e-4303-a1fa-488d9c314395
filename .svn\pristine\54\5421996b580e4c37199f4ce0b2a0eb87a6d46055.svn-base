﻿using ContinuityPatrol.Application.Features.BusinessService.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessService.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessService.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BusinessService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]

public class OperationalServiceController : BaseController
{
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ILogger<OperationalServiceController> _logger;

    public OperationalServiceController(ILogger<OperationalServiceController> logger, IPublisher publisher, IDataProvider dataProvider, IMapper mapper)
    {
        _dataProvider = dataProvider;
        _mapper = mapper;
        _logger = logger;
        _publisher = publisher;
    }

    [AntiXss]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in OperationalService");

        await _publisher.Publish(new BusinessServicePaginatedEvent());

      //  var businessServices = await _dataProvider.BusinessService.GetBusinessServicePaginatedList(new GetBusinessServicePaginatedListQuery());
        var siteTypes = await _dataProvider.SiteType.GetSiteTypeList();

        return View(new BusinessServiceViewModel
        {
          //  BusinessServices = businessServices,
            SiteTypes = siteTypes
        });
    }

    public async Task<IActionResult> GetList()
    {
        _logger.LogDebug("Entering GetList method in OperationalService");
        try
        {
            var businessServices = await _dataProvider.BusinessService.GetBusinessServiceList();
            return Json(businessServices);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on operational service page while processing the request.", ex);

            return Json("");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    public async Task<IActionResult> CreateOrUpdate(BusinessServiceViewModel businessServiceViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in OperationalService");

        var businessServiceId = Request.Form["Id"].ToString();

        try
        {
            BaseResponse result;

            if (businessServiceId.IsNullOrWhiteSpace())
            {
                var businessServiceCreate = _mapper.Map<CreateBusinessServiceCommand>(businessServiceViewModel);

                result = await _dataProvider.BusinessService.CreateAsync(businessServiceCreate);

                _logger.LogDebug($"Creating OperationalService '{businessServiceCreate.Name}'");
              //  TempData.NotifySuccess(result.Message);

            }
            else
            {
                var businessServiceUpdate = _mapper.Map<UpdateBusinessServiceCommand>(businessServiceViewModel);

                result = await _dataProvider.BusinessService.UpdateAsync(businessServiceUpdate);

                _logger.LogDebug($"Updating OperationalService '{businessServiceUpdate.Name}'");
             //   TempData.NotifySuccess(result.Message);
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in OperationalService, returning view.");
            return Json(new { Success = true, data = result });
        }
        //catch (ValidationException ex)
        //{
        //    _logger.LogError($"Validation error on operational service page: {ex.ValidationErrors.FirstOrDefault()}");

        //   // TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

        //    return RedirectToAction("List");
        //}
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on operational service page while processing the request for create or update.", ex);

            // TempData.NotifyWarning(ex.GetMessage());

            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in OperationalService");

        try
        {
            var response = await _dataProvider.BusinessService.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in OperationalService");

            TempData.NotifySuccess(response.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while deleting record on OperationalService.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
   
    [HttpGet]
    public async Task<JsonResult> GetSiteByTypeAndCompanyId(string companyId)
    {
        try
        {
            _logger.LogDebug("Entering GetSiteByTypeAndCompanyId method in site");

            var siteList = await _dataProvider.Site.GetSiteByCompanyId(companyId);

            _logger.LogDebug($"Successfully retrieved Get Site By Type  And CompanyId '{companyId}' on Site");

            return Json(siteList);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on SingleSignOn page while retrieving the site by companyId and siteType.", ex);

            return ex.GetJsonException();
        }


    }

    public async Task<bool> IsBusinessServiceNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsBusinessServiceNameExist method in OperationalService");

        try
        {
            return await _dataProvider.BusinessService.IsBusinessServiceNameExist(name, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on operational service while checking if operational service  name exists for : {name}.", ex);

            return false;
        }
    }

    [HttpGet]
    public async Task<JsonResult> GetPagination(GetBusinessServicePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in OperationalService");
        try
        {
            _logger.LogDebug("Successfully retrieved operational service paginated list on OperationalService page");
            return Json(await _dataProvider.BusinessService.GetBusinessServicePaginatedList(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on operational service page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    //For Database, server, replication
    public async Task<JsonResult> GetBusinessServiceNames()
    {
        _logger.LogDebug("Entering GetBusinessServiceNames method in OperationalService");

        try
        {
            var result = await _dataProvider.BusinessService.GetBusinessServiceNames();
            _logger.LogDebug("Successfully retrieved operational service names");
            return Json(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on operational service while retrieving operational service names. {ex.GetMessage()}");
            return Json(new { success = false, message = ex.GetMessage() });
        }
    }
}