﻿namespace ContinuityPatrol.Infrastructure.Helper;

public static class JsonHelper
{
    public static int GetLicenseJsonValue(string json, string jsonPath)
    {
        try
        {
            var jsonObject = JObject.Parse(json);
            return jsonObject.SelectToken(jsonPath)?.Value<int>() ?? 0;
        }
        catch (Exception)
        {
            return 0;
        }
    }

    public static bool GetJsonValueAsBool(string json, string jsonPath)
    {
        try
        {
            var jsonObject = JObject.Parse(json);
            return jsonObject.SelectToken(jsonPath)?.Value<bool>() ?? false;
        }
        catch (Exception)
        {
            return false;
        }
    }
}