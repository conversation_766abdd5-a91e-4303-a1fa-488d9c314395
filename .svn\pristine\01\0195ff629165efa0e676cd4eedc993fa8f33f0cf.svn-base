﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.VeritasClusterModel.VeritasClusterViewModel
@using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel
@using ContinuityPatrol.Shared.Services.Helper
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-veritas-cluster"></i><span>Veritas Cluster</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clusterprofilename=" id="profilename">
                                        <label class="form-check-label" for="profilename">
                                            Cluster Profile Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clusterservername=" id="clusterserver">
                                        <label class="form-check-label" for="clusterserver">
                                            Cluster Server
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="clustername=" id="clustername">
                                        <label class="form-check-label" for="clustername">
                                            Cluster Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="create" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="pt-1 card-body" style="height: calc(100vh - 141px);">
            <div>
                <table id="VeritasClusterTable" class="table">
                    <thead>
                        <tr>
                            <th>Sr No</th>
                            <th>Cluster Profile Name </th>
                            <th>Cluster Server</th>
                            <th>Cluster Name</th>
                            <th>Cluster Bin Path</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!--Modal Create-->
<div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <form class="modal-content" asp-controller="VeritasCluster" id="CreateForm" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">

            <div class="modal-header">
                <h6 class="page_title"><i class="cp-veritas-cluster"></i><span>Veritas Cluster Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <div class="form-label">Cluster Profile Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-user-configuration"></i></span>
                                <input asp-for="ClusterProfileName" maxlength="100" id="clusterProfileName" autocomplete="off" class="form-control" placeholder="Enter Cluster Profile Name" />
                            </div>
                            <span asp-validation-for="ClusterProfileName" id="clusterProfileName-error"></span>
                            <input asp-for="Id" type="hidden" id="clusterProfileNameId" />
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <div class="form-label">Cluster Server</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-fal-server"></i></span>
                                <select id="clusterServerName" class="form-select-modal" data-live-search="true" data-placeholder="Select Cluster Server">
                                </select>
                            </div>
                            <span asp-validation-for="ClusterServerName" id="clusterServerName-error"></span>
                            <input asp-for="ClusterServerName" type="hidden" id="clusterServerNames" />
                            <input asp-for="ClusterServerId" type="hidden" id="clusterServerNameId" />
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <div class="form-label">Cluster Name</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-auxiliary-cluster-name"></i></span>
                                <input asp-for="ClusterName" id="clusterName" class="form-control" autocomplete="off" placeholder="Enter Cluster Name" maxlength="100" />
                            </div>
                            <span asp-validation-for="ClusterName" id="clusterName-error"></span>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <div class="form-label">Cluster Bin Path</div>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-endpoint-port-number"></i></span>
                                <input asp-for="ClusterBinPath" id="clusterBinPath" autocomplete="off" class="form-control" placeholder="Enter Cluster Bin Path" maxlength="200" />
                            </div>
                            <span asp-validation-for="ClusterBinPath" id="clusterBinPath-error"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="configurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="configurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script src="~/js/Configuration//Infra Components/Veritas Cluster/VeritasCluster.js"></script>


