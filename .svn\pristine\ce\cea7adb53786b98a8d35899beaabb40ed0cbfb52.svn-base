using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLMonitorLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLMonitor";

    public List<MSSQLMonitorLogs> MSSQLMonitorLogsPaginationList { get; set; }
    public List<MSSQLMonitorLogs> MSSQLMonitorLogsList { get; set; }
    public MSSQLMonitorLogs MSSQLMonitorLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLMonitorLogsFixture()
    {
        _fixture = new Fixture();
        
        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLMonitorLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
           );

        MSSQLMonitorLogsPaginationList = _fixture.CreateMany<MSSQLMonitorLogs>(20).ToList();
        MSSQLMonitorLogsList = _fixture.CreateMany<MSSQLMonitorLogs>(5).ToList();
        MSSQLMonitorLogsDto = _fixture.Create<MSSQLMonitorLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLMonitorLogs CreateMSSQLMonitorLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLMonitorLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
       
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLMonitorLogs CreateMSSQLMonitorLogsWithWhitespace()
    {
        return CreateMSSQLMonitorLogsWithProperties(type: "  MSSQLMonitor  ");
    }

    public MSSQLMonitorLogs CreateMSSQLMonitorLogsWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLMonitorLogsWithProperties(type: longType);
    }

    public MSSQLMonitorLogs CreateMSSQLMonitorLogsWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMSSQLMonitorLogsWithProperties(infraObjectId: infraObjectId);
    }

    public List<MSSQLMonitorLogs> CreateMultipleMSSQLMonitorLogsWithSameType(string type, int count)
    {
        var logs = new List<MSSQLMonitorLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateMSSQLMonitorLogsWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public List<MSSQLMonitorLogs> CreateMSSQLMonitorLogsWithMixedActiveStatus(string type)
    {
        return new List<MSSQLMonitorLogs>
        {
            CreateMSSQLMonitorLogsWithProperties(type: type, isActive: true),
            CreateMSSQLMonitorLogsWithProperties(type: type, isActive: false),
            CreateMSSQLMonitorLogsWithProperties(type: type, isActive: true)
        };
    }

    public List<MSSQLMonitorLogs> CreateMSSQLMonitorLogsWithDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<MSSQLMonitorLogs>();
        var dateRange = (endDate - startDate).TotalDays;
        
        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            logs.Add(CreateMSSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: randomDate));
        }
        return logs;
    }

    public List<MSSQLMonitorLogs> CreateMSSQLMonitorLogsOutsideDateRange(string infraObjectId, DateTime startDate, DateTime endDate)
    {
        return new List<MSSQLMonitorLogs>
        {
            CreateMSSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: startDate.AddDays(-5)), // Before range
            CreateMSSQLMonitorLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: endDate.AddDays(5)) // After range
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLMonitor", "MSSQL", "SQLServer", "DatabaseMonitor" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
