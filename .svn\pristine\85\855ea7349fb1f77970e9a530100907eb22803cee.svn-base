﻿QUnit.module("NotificationManager", {
    beforeEach: function () {
        // Reset state before each test
        state.jsonData = {
            assignedBusinessServices: []
        };
        state.selectedValues = [];
        state.checkDetail = false;
        state.infraCheck = [];

        // Mock RootUrl
        window.RootUrl = "/";

        // Mock DataTable
        $.fn.DataTable = function () {
            return {
                ajax: function () { return this; },
                on: function () { return this; },
                page: { info: function () { return { page: 0 }; } },
                draw: function () { return this; }
            };
        };

        // Initialize DataTable
        state.dataTable = $('#notificationManager').DataTable({});

        // Mock jQuery AJAX methods
        $.ajax = function (options) {
            console.log("Mock AJAX called with:", options);

            // Return a proper jQuery deferred object
            const deferred = $.Deferred();

            if (options.url.includes("GetAllInfraObjectList")) {
                deferred.resolve({
                    success: true,
                    data: {
                        assignedBusinessServices: [
                            {
                                id: "service1",
                                name: "Service 1",
                                isAll: false,
                                isPartial: false,
                                assignedBusinessFunctions: [
                                    {
                                        id: "func1",
                                        name: "Function 1",
                                        isAll: false,
                                        isPartial: false,
                                        assignedInfraObjects: [
                                            {
                                                id: "infra1",
                                                name: "Infra 1",
                                                isSelected: false
                                            },
                                            {
                                                id: "infra2",
                                                name: "Infra 2",
                                                isSelected: false
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                });
            }
            else if (options.url.includes("GetPagination")) {
                deferred.resolve({
                    success: true,
                    data: {
                        data: [
                            {
                                id: 1,
                                name: "Test User",
                                emailAddress: "<EMAIL>",
                                properties: JSON.stringify({
                                    assignedBusinessServices: []
                                }),
                                mobileNumber: "**********",
                                isActiveUser: true,
                                isMail: true,
                                isSendReport: true
                            }
                        ],
                        totalPages: 1,
                        totalCount: 1
                    }
                });
            }
            else if (options.url.includes("CountryDailCode.json")) {
                deferred.resolve({
                    countrycode: [
                        { dial_code: "+1" },
                        { dial_code: "+91" }
                    ]
                });
            }
            else if (options.url.includes("IsAlertReceiverNameExist")) {
                // Mock name existence check - return false (name doesn't exist)
                deferred.resolve(false);
            }
            else {
                deferred.reject("No mock for URL: " + options.url);
            }

            return deferred.promise();
        };

        // Mock $.get specifically
        $.get = function (url, data) {
            console.log("Mock $.get called with:", url, data);
            const deferred = $.Deferred();

            if (url.includes("IsAlertReceiverNameExist")) {
                deferred.resolve(false); // Name doesn't exist
            } else {
                deferred.reject("No mock for URL: " + url);
            }

            return deferred.promise();
        };

        // Clear all form fields
        clearInputField();
    }
});

// Core Functionality Tests
QUnit.test("Initialization", function (assert) {
    assert.ok(state, "State object exists");
    assert.equal(typeof notifDebounce, "function", "notifDebounce function exists");
    assert.ok($.fn.DataTable, "DataTables is loaded");
});

// Validation Tests
QUnit.test("validateName function", function (assert) {
    const done = assert.async();

    // Test empty name
    validateName("", null, NotificationURL.nameExistUrl).then(function (result) {
        assert.false(result, "Empty name should fail validation");
        assert.equal($("#notifNameError").text(), "Enter notification name", "Correct error for empty name");

        // Test name with special characters
        return validateName("test<name", null, NotificationURL.nameExistUrl);
    }).then(function (result) {
        assert.false(result, "Name with special characters should fail");
        assert.equal($("#notifNameError").text(), "Special characters not allowed", "Correct error for special chars");

        // Test valid name
        return validateName("valid name", null, NotificationURL.nameExistUrl);
    }).then(function (result) {
        assert.true(result, "Valid name should pass");
        assert.equal($("#notifNameError").text(), "", "No error for valid name");
        done();
    }).catch(function (error) {
        assert.ok(false, "Error in validateName test: " + error);
        done();
    });
});

QUnit.test("validateEmail function", function (assert) {
    const done = assert.async();

    // Test empty email
    validateEmail("").then(function (result) {
        assert.false(result, "Empty email should fail");
        assert.equal($("#notifEmailError").text(), "Enter email address", "Correct error for empty email");

        // Test invalid email
        return validateEmail("invalid@email");
    }).then(function (result) {
        assert.false(result, "Invalid email should fail");
        assert.equal($("#notifEmailError").text(), "Invalid email", "Correct error for invalid email");

        // Test valid email
        return validateEmail("<EMAIL>");
    }).then(function (result) {
        assert.true(result, "Valid email should pass");
        assert.equal($("#notifEmailError").text(), "", "No error for valid email");
        done();
    }).catch(function (error) {
        assert.ok(false, "Error in validateEmail test: " + error);
        done();
    });
});

QUnit.test("validateMobile function", function (assert) {
    const done = assert.async();

    // Test empty mobile
    validateMobile("").then(function (result) {
        assert.false(result, "✓ Empty mobile should fail");
        assert.equal($("#notifMobileError").text(), "Enter mobile number", "Correct error for empty mobile");

        // Test mobile starting with 0
        return validateMobile("0123456");
    }).then(function (result) {
        assert.false(result, "Mobile starting with 0 should fail");
        assert.equal($("#notifMobileError").text(), "Number not starts with zero", "Correct error for mobile starting with 0");

        // Test mobile too short
        return validateMobile("123");
    }).then(function (result) {
        assert.false(result, "Short mobile should fail");
        assert.equal($("#notifMobileError").text(), "Must be at least 7 characters", "Correct error for short mobile");

        // Test valid mobile
        return validateMobile("1234567");
    }).then(function (result) {
        assert.true(result, "Valid mobile should pass");
        assert.equal($("#notifMobileError").text(), "", "No error for valid mobile");
        done();
    }).catch(function (error) {
        assert.ok(false, "Error in validateMobile test: " + error);
        done();
    });
});

QUnit.test("validateInfraTree function", function (assert) {
    // Test with no selected infra objects
    state.checkDetail = false;
    let result = validateInfraTree(null, {
        assignedBusinessServices: [{
            assignedBusinessFunctions: [{
                assignedInfraObjects: [{ isSelected: false }]
            }]
        }]
    });
    assert.false(result, "Should fail with no selected infra objects");
    assert.equal($("#notifTreeError").text(), "Select at least one infraobject", "Correct error for no selection");

    // Test with selected infra objects
    state.checkDetail = true;
    result = validateInfraTree(null, {
        assignedBusinessServices: [{
            assignedBusinessFunctions: [{
                assignedInfraObjects: [{ isSelected: true }]
            }]
        }]
    });
    assert.true(result, "Should pass with selected infra objects");
    assert.equal($("#notifTreeError").text(), "", "No error for valid selection");
});

// Form Field Tests
QUnit.test("clearInputField function", function (assert) {
    // Set some values first
    $("#notifyId").val("123");
    $("#notifName").val("Test");
    $("#notifEmail").val("<EMAIL>");
    $("#notifMobileNum").val("1234567");
    $("#notifMobilePre").val("+1");
    $("#notifIsMobile").prop("checked", true);
    $("#notifActiveUser").prop("checked", true);
    $("#notifSendReport").prop("checked", true);
    $("#notifNameError").addClass("field-validation-error").text("Error");
    $("#notifMob").show();
    $("#notifMobPre").show();

    clearInputField();

    assert.equal($("#notifyId").val(), "", "ID cleared");
    assert.equal($("#notifName").val(), "", "Name cleared");
    assert.equal($("#notifEmail").val(), "", "Email cleared");
    assert.equal($("#notifMobileNum").val(), "", "Mobile number cleared");
    assert.equal($("#notifNameError").text(), "", "Error message cleared");
    assert.false($("#notifNameError").hasClass("field-validation-error"), "Error class removed");
    assert.equal($("#notifMob").css("display"), "none", "Mobile fields hidden");
    assert.equal($("#notifMobPre").css("display"), "none", "Mobile prefix fields hidden");
});

QUnit.test("populateModalFields function", function (assert) {
    const testData = {
        id: "123",
        name: "Test User",
        emailAddress: "<EMAIL>",
        properties: JSON.stringify({
            assignedBusinessServices: [{
                assignedBusinessFunctions: [{
                    assignedInfraObjects: [{ isSelected: true }]
                }]
            }]
        }),
        isMail: true,
        mobileNumber: "**********",
        isActiveUser: true,
        isSendReport: true
    };

    // Add options to mobile prefix select
    $('#notifMobilePre').append('<option value="+1">+1</option>');

    populateModalFields(testData);

    assert.equal($("#notifyId").val(), "123", "ID populated");
    assert.equal($("#notifName").val(), "Test User", "Name populated");
    assert.equal($("#notifEmail").val(), "<EMAIL>", "Email populated");
    assert.true($("#notifIsMobile").prop("checked"), "Mobile checkbox checked");
    assert.equal($("#notifMobilePre").val(), "+1", "Mobile prefix populated");
    assert.equal($("#notifMobileNum").val(), "1234567", "Mobile number populated");
    assert.ok(state.jsonData, "JSON data populated in state");
    assert.equal(state.jsonData.assignedBusinessServices.length, 1, "Service data exists");
});

// Tree View Tests
QUnit.test("treeListView function", function (assert) {
    const done = assert.async();

    treeListView().then(function () {
        assert.ok(state.jsonData, "JSON data loaded");
        assert.ok(state.jsonData.assignedBusinessServices, "Services data exists");
        assert.equal($("#notifyTreeView").children().length, 1, "Tree view populated");
        done();
    }).catch(function (error) {
        assert.ok(false, "Error in treeListView test: " + error);
        done();
    });
});

QUnit.test("createTreeView function", function (assert) {
    const testData = {
        isAll: false,
        assignedBusinessServices: [{
            id: "service1",
            name: "Service 1",
            isAll: false,
            isPartial: false,
            assignedBusinessFunctions: [{
                id: "func1",
                name: "Function 1",
                isAll: false,
                isPartial: false,
                assignedInfraObjects: [
                    { id: "infra1", name: "Infra 1", isSelected: false },
                    { id: "infra2", name: "Infra 2", isSelected: false }
                ]
            }]
        }]
    };

    createTreeView($("#notifyTreeView"), testData);

    const $service = $("#notifyTreeView > details");
    assert.equal($service.length, 1, "Service element created");
    assert.equal($service.find("summary input").attr("businessid"), "service1", "Service checkbox has correct ID");

    const $function = $service.find("ul.tree > details");
    assert.equal($function.find("summary input").attr("functionid"), "func1", "Function checkbox has correct ID");

    const $infras = $function.find("ul.tree > details");
    assert.equal($infras.length, 2, "Infra elements created");
    assert.equal($infras.first().find("input").attr("infraid"), "infra1", "First infra checkbox has correct ID");
});

QUnit.test("JsonTreeView function", function (assert) {
    // First load some data
    state.jsonData = {
        isAll: false,
        assignedBusinessServices: [{
            id: "service1",
            name: "Service 1",
            isAll: false,
            isPartial: false,
            assignedBusinessFunctions: [{
                id: "func1",
                name: "Function 1",
                isAll: false,
                isPartial: false,
                assignedInfraObjects: [
                    { id: "infra1", name: "Infra 1", isSelected: false },
                    { id: "infra2", name: "Infra 2", isSelected: false }
                ]
            }]
        }]
    };

    // Test selecting all
    JsonAllTreeView(true);
    assert.true(state.jsonData.isAll, "All selected in root");
    assert.true(state.jsonData.assignedBusinessServices[0].isAll, "Service selected");
    assert.true(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Function selected");
    assert.true(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Infra selected");

    // Test deselecting all
    JsonAllTreeView(false);
    assert.false(state.jsonData.isAll, "All deselected in root");
    assert.false(state.jsonData.assignedBusinessServices[0].isAll, "Service deselected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Function deselected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Infra deselected");

    // Test selecting specific infra
    JsonTreeView(true, "service1", "func1", "infra1");
    assert.false(state.jsonData.isAll, "Root not fully selected");
    assert.false(state.jsonData.assignedBusinessServices[0].isAll, "Service not fully selected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Function not fully selected");
    assert.true(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Specific infra selected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[1].isSelected, "Other infra not selected");
});

// Event Handler Tests
QUnit.test("Create button click", function (assert) {
    const done = assert.async();

    // Mock modal show
    $('#CreateModal').modal = function (action) {
        if (action === 'show') {
            assert.ok(true, "Create modal shown on button click");
            done();
            return true;
        }
    };

    // Trigger create button click
    $("#CreteButton").trigger("click");
});

QUnit.test("Save button click with valid data", function (assert) {
    const done = assert.async();

    // Set up valid form data
    $("#notifName").val("Valid Name");
    $("#notifEmail").val("<EMAIL>");
    $("#notifMobilePre").val("+1");
    $("#notifMobileNum").val("1234567");
    $("#notifIsMobile").prop("checked", true);

    // Set up mock tree data
    state.jsonData = {
        assignedBusinessServices: [{
            assignedBusinessFunctions: [{
                assignedInfraObjects: [{ isSelected: true }]
            }]
        }]
    };
    state.checkDetail = true;

    // Mock form submission
    $("#CreateForm").on("submit", function (e) {
        e.preventDefault();
        assert.ok(true, "Form submitted with valid data");
        done();
    });

    // Trigger save click
    $("#notifSave").trigger("click");
});

QUnit.test("Edit button click", function (assert) {
    const testData = {
        id: "123",
        name: "Test User",
        emailAddress: "<EMAIL>",
        properties: JSON.stringify({
            assignedBusinessServices: []
        }),
        isMail: true,
        mobileNumber: "**********",
        isActiveUser: true,
        isSendReport: true
    };

    // Add edit button to DOM
    const $row = $('<div><span class="edit-button" data-alertreceiver=\'' + JSON.stringify(testData) + '\'></span></div>');
    $('#notificationManager').append($row);

    // Mock modal show
    $('#CreateModal').modal = function (action) {
        if (action === 'show') {
            assert.ok(true, "Modal shown on edit");
            assert.equal($("#notifyId").val(), "123", "ID populated correctly");
            assert.equal($("#notifName").val(), "Test User", "Name populated correctly");
            assert.equal($('#notifSave').text(), "Update", "Save button text changed to Update");
            return true;
        }
    };

    // Trigger edit click
    $row.find('.edit-button').trigger('click');
});

QUnit.test("Delete button click", function (assert) {
    const testData = {
        id: "123",
        name: "Test User"
    };

    // Add delete button to DOM
    const $row = $('<div><span class="delete-button" data-alertreceiver-id="123" data-alertreceiver-name="Test User"></span></div>');
    $('#notificationManager').append($row);

    // Mock modal show
    $('#DeleteModal').modal = function (action) {
        if (action === 'show') {
            assert.ok(true, "Delete modal shown");
            assert.equal($("#deleteData").text(), "Test User", "Name displayed correctly");
            assert.equal($('#textDeleteId').val(), "123", "ID set correctly");
            return true;
        }
    };

    // Trigger delete click
    $row.find('.delete-button').trigger('click');
});

QUnit.test("Mobile checkbox toggle", function (assert) {
    // Ensure initial state
    $("#notifMob, #notifMobPre").hide();

    // Test showing mobile fields
    $("#notifIsMobile").prop("checked", true).trigger("change");
    assert.equal($("#notifMob").css("display"), "block", "Mobile fields shown when checked");
    assert.equal($("#notifMobPre").css("display"), "block", "Mobile prefix fields shown when checked");

    // Test hiding mobile fields
    $("#notifIsMobile").prop("checked", false).trigger("change");
    assert.equal($("#notifMob").css("display"), "none", "Mobile fields hidden when unchecked");
    assert.equal($("#notifMobPre").css("display"), "none", "Mobile prefix fields hidden when unchecked");
});

QUnit.test("Select All checkbox in tree view", function (assert) {
    // First load some data
    state.jsonData = {
        isAll: false,
        assignedBusinessServices: [{
            id: "service1",
            name: "Service 1",
            isAll: false,
            isPartial: false,
            assignedBusinessFunctions: [{
                id: "func1",
                name: "Function 1",
                isAll: false,
                isPartial: false,
                assignedInfraObjects: [
                    { id: "infra1", name: "Infra 1", isSelected: false },
                    { id: "infra2", name: "Infra 2", isSelected: false }
                ]
            }]
        }]
    };

    // Test selecting all
    $("#notifSelectAll").prop("checked", true).trigger("change");
    assert.true(state.jsonData.isAll, "All selected in root");
    assert.true(state.jsonData.assignedBusinessServices[0].isAll, "Service selected");
    assert.true(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Function selected");
    assert.true(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Infra selected");

    // Test deselecting all
    $("#notifSelectAll").prop("checked", false).trigger("change");
    assert.false(state.jsonData.isAll, "All deselected in root");
    assert.false(state.jsonData.assignedBusinessServices[0].isAll, "Service deselected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Function deselected");
    assert.false(state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Infra deselected");
});

QUnit.test("Cancel button click", function (assert) {
    // Set some values first
    $("#notifyId").val("123");
    $("#notifName").val("Test");
    $("#notifEmail").val("<EMAIL>");

    // Trigger cancel click
    $("#notifyBtnCancel").trigger("click");

    assert.equal($("#notifyId").val(), "", "ID cleared");
    assert.equal($("#notifName").val(), "", "Name cleared");
    assert.equal($("#notifEmail").val(), "", "Email cleared");
});

QUnit.test("Search functionality", function (assert) {
    const done = assert.async();

    // Mock DataTable
    state.dataTable = {
        ajax: {
            reload: function (callback, resetPaging) {
                assert.true(resetPaging, "Should reset paging on search");
                callback({
                    success: true,
                    data: {
                        data: [],
                        totalPages: 0,
                        totalCount: 0,
                        recordsFiltered: 0
                    }
                });
            }
        },
        page: {
            info: function () {
                return { page: 0 };
            }
        }
    };

    // Set up test data
    $("#Name").prop("checked", true);
    $("#notifSearchInp").val("test");

    // Trigger search
    $('#notifSearchInp').trigger("input");

    setTimeout(() => {
        assert.deepEqual(state.selectedValues, [], "Selected values set correctly");
        done();
    }, 350);
});

QUnit.test("Search input with debounce", function (assert) {
    const done = assert.async();
    let searchCount = 0;

    // Mock DataTable reload
    state.dataTable = {
        ajax: {
            reload: function () {
                searchCount++;
            }
        },
        page: {
            info: function () {
                return { page: 0 };
            }
        }
    };

    // Set up test data
    $("#Name").prop("checked", true);
    $("#notifSearchInp").val("test");

    // Trigger multiple rapid inputs (should be debounced)
    $('#notifSearchInp').trigger("input");
    $('#notifSearchInp').trigger("input");
    $('#notifSearchInp').trigger("input");

    setTimeout(() => {
        assert.equal(searchCount, 1, "Search function called only once due to debounce");
        done();
    }, 350);
});

// Permission Tests
QUnit.test("Permission handling", function (assert) {
    // Test with create permission false
    const createFalsePermission = {
        createPermission: "false",
        deletePermission: "true"
    };

    if (createFalsePermission.createPermission == 'false') {
        $("#CreteButton").removeClass('#CreteButton').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled');
    }

    assert.true($("#CreteButton").hasClass("btn-disabled"), "Create button disabled when no permission");
    assert.false($("#CreteButton").attr("data-bs-toggle"), "Data attributes removed when no permission");

    // Reset for other tests
    $("#CreteButton").addClass('#CreteButton').attr('data-bs-toggle', 'modal').attr('data-bs-target', '#CreateModal').removeClass('btn-disabled');
});

// Debounce Function Test
QUnit.test("notifDebounce function", function (assert) {
    const done = assert.async();
    let counter = 0;

    const debouncedFn = notifDebounce(() => {
        counter++;
    }, 100);

    debouncedFn();
    debouncedFn();
    debouncedFn();

    assert.equal(counter, 0, "Function not called immediately");

    setTimeout(() => {
        assert.equal(counter, 1, "Function called once after delay");
        done();
    }, 150);
});

QUnit.module("NotificationManager Events", {
    beforeEach: function () {
        // Initialize state safely
        if (!window.state) {
            window.state = {
                jsonData: { assignedBusinessServices: [] },
                selectedValues: [],
                checkDetail: false,
                infraCheck: [],
                dataTable: null
            };
        } else {
            // Reset state properties
            window.state.jsonData = { assignedBusinessServices: [] };
            window.state.selectedValues = [];
            window.state.checkDetail = false;
            window.state.infraCheck = [];
            window.state.dataTable = null;
        }

        // Set up test fixtures
        $('#qunit-fixture').html(`
            <div id="notificationManager"></div>
            <input id="notifName" type="text">
            <input id="notifEmail" type="text">
            <div id="notifyTreeView"></div>
            <span id="notifNameError"></span>
            <span id="notifEmailError"></span>
            <span id="notifTreeError"></span>
            <button id="notifSave"></button>
            <form id="CreateForm"></form>
            <input id="notifyId" type="hidden">
            <div id="DeleteModal"></div>
            <span id="deleteData"></span>
            <input id="textDeleteId" type="hidden">
            <button id="CreteButton"></button>
            <button id="notifyBtnCancel"></button>
            <input id="notifIsMobile" type="checkbox">
            <div id="notifMob" style="display:none"></div>
            <div id="notifMobPre" style="display:none"></div>
            <input id="notifSelectAll" type="checkbox">
            <input id="notifSearchInp" type="text">
            <input id="Name" type="checkbox" value="name">
            <input id="Email" type="checkbox" value="email">
            <input id="Mobile" type="checkbox" value="mobile">
            <div id="CreateModal"></div>
        `);

        // Mock essential functions
        $.fn.modal = function () { return this; };
        $.fn.DataTable = function () {
            return {
                ajax: function () { return this; },
                on: function () { return this; },
                page: { info: function () { return { page: 0 }; } },
                draw: function () { return this; },
                destroy: function () { return this; }
            };
        };

        window.state.dataTable = $('#notificationManager').DataTable();
    },
    afterEach: function () {
        // Clean up
        if (window.state.dataTable) {
            window.state.dataTable.destroy();
        }
    }
});

QUnit.test("Create button click", function (assert) {
    const done = assert.async();
    let modalShown = false;

    $('#CreateModal').modal = function (action) {
        if (action === 'show') {
            modalShown = true;
            return this;
        }
    };

    $("#CreteButton").on("click", function () {
        assert.ok(modalShown, "Create modal should be shown");
        done();
    });

    $("#CreteButton").trigger("click");
});

QUnit.test("Save button click with valid data", function (assert) {
    const done = assert.async();

    // Setup valid form
    $("#notifName").val("Valid Name");
    $("#notifEmail").val("<EMAIL>");
    $("#notifMobilePre").val("+1");
    $("#notifMobileNum").val("1234567");
    $("#notifIsMobile").prop("checked", true);

    window.state.jsonData = {
        assignedBusinessServices: [{
            assignedBusinessFunctions: [{
                assignedInfraObjects: [{ isSelected: true }]
            }]
        }]
    };
    window.state.checkDetail = true;

    $("#CreateForm").on("submit", function (e) {
        e.preventDefault();
        assert.ok(true, "Form should submit with valid data");
        done();
    });

    $("#notifSave").trigger("click");
});

QUnit.test("Edit button click", function (assert) {
    const done = assert.async();
    const testData = {
        id: "123",
        name: "Test User",
        emailAddress: "<EMAIL>",
        properties: '{"assignedBusinessServices":[]}',
        isMail: true,
        mobileNumber: "**********",
        isActiveUser: true,
        isSendReport: true
    };

    const $row = $('<div><span class="edit-button" data-alertreceiver=\'' + JSON.stringify(testData) + '\'></span></div>');
    $('#notificationManager').append($row);

    let modalShown = false;
    $('#CreateModal').modal = function (action) {
        if (action === 'show') {
            modalShown = true;
            assert.equal($("#notifyId").val(), "123", "Should set ID");
            assert.equal($("#notifName").val(), "Test User", "Should set name");
            assert.equal($('#notifSave').text(), "Update", "Should change button text");
            done();
            return this;
        }
    };

    $row.find('.edit-button').trigger('click');
});

QUnit.test("Delete button click", function (assert) {
    const done = assert.async();
    const testData = {
        id: "123",
        name: "Test User"
    };

    const $row = $('<div><span class="delete-button" data-alertreceiver-id="123" data-alertreceiver-name="Test User"></span></div>');
    $('#notificationManager').append($row);

    let modalShown = false;
    $('#DeleteModal').modal = function (action) {
        if (action === 'show') {
            modalShown = true;
            assert.equal($("#deleteData").text(), "Test User", "Should display name");
            assert.equal($('#textDeleteId').val(), "123", "Should set ID");
            done();
            return this;
        }
    };

    $row.find('.delete-button').trigger('click');
});

QUnit.test("Mobile checkbox toggle", function (assert) {
    $("#notifMob, #notifMobPre").hide();

    $("#notifIsMobile").prop("checked", true).trigger("change");
    assert.equal($("#notifMob").css("display"), "block", "Should show mobile fields when checked");
    assert.equal($("#notifMobPre").css("display"), "block", "Should show prefix fields when checked");

    $("#notifIsMobile").prop("checked", false).trigger("change");
    assert.equal($("#notifMob").css("display"), "none", "Should hide mobile fields when unchecked");
    assert.equal($("#notifMobPre").css("display"), "none", "Should hide prefix fields when unchecked");
});

QUnit.test("Select All checkbox in tree view", function (assert) {
    window.state.jsonData = {
        isAll: false,
        assignedBusinessServices: [{
            id: "service1",
            name: "Service 1",
            isAll: false,
            isPartial: false,
            assignedBusinessFunctions: [{
                id: "func1",
                name: "Function 1",
                isAll: false,
                isPartial: false,
                assignedInfraObjects: [
                    { id: "infra1", name: "Infra 1", isSelected: false },
                    { id: "infra2", name: "Infra 2", isSelected: false }
                ]
            }]
        }]
    };

    $("#notifSelectAll").prop("checked", true).trigger("change");
    assert.true(window.state.jsonData.isAll, "Should select all in root");
    assert.true(window.state.jsonData.assignedBusinessServices[0].isAll, "Should select service");
    assert.true(window.state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Should select function");
    assert.true(window.state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Should select infra");

    $("#notifSelectAll").prop("checked", false).trigger("change");
    assert.false(window.state.jsonData.isAll, "Should deselect all in root");
    assert.false(window.state.jsonData.assignedBusinessServices[0].isAll, "Should deselect service");
    assert.false(window.state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].isAll, "Should deselect function");
    assert.false(window.state.jsonData.assignedBusinessServices[0].assignedBusinessFunctions[0].assignedInfraObjects[0].isSelected, "Should deselect infra");
});

QUnit.test("Cancel button click", function (assert) {
    $("#notifyId").val("123");
    $("#notifName").val("Test");
    $("#notifEmail").val("<EMAIL>");

    $("#notifyBtnCancel").trigger("click");

    assert.equal($("#notifyId").val(), "", "Should clear ID");
    assert.equal($("#notifName").val(), "", "Should clear name");
    assert.equal($("#notifEmail").val(), "", "Should clear email");
});



QUnit.test("Search input with debounce", function (assert) {
    const done = assert.async();
    let searchCount = 0;

    window.state.dataTable = {
        ajax: {
            reload: function () {
                searchCount++;
            }
        },
        page: { info: function () { return { page: 0 }; } }
    };

    $("#Name").prop("checked", true);
    $("#notifSearchInp").val("test");

    $('#notifSearchInp').trigger("input");
    $('#notifSearchInp').trigger("input");
    $('#notifSearchInp').trigger("input");

    setTimeout(() => {
        assert.equal(searchCount, 1, "Should debounce multiple inputs");
        done();
    }, 350);
});

QUnit.done(function () {
    delete window.state;
});