﻿using ContinuityPatrol.Domain.ViewModels.WorkflowModel;

namespace ContinuityPatrol.Application.Features.Workflow.Queries.GetNames;

public class GetWorkflowNameQueryHandler : IRequestHandler<GetWorkflowNameQuery, List<WorkflowNameVm>>
{
   
    private readonly IMapper _mapper;
    private readonly IWorkflowViewRepository _workflowViewRepository;


    public GetWorkflowNameQueryHandler(IMapper mapper, IWorkflowViewRepository workflowViewRepository)
    {
        _mapper = mapper;
        _workflowViewRepository = workflowViewRepository;
    }

    public async Task<List<WorkflowNameVm>> Handle(GetWorkflowNameQuery request, CancellationToken cancellationToken)
    {
        var workflowNames = await _workflowViewRepository.GetWorkflowNames();

        return workflowNames.Count == 0 
            ? new List<WorkflowNameVm>() 
            : _mapper.Map<List<WorkflowNameVm>>(workflowNames);


        // var workflowNames = await _workflowRepository.GetWorkflowNames();

        ////  var workflowPermission = await _workflowRepository.GetWorkflowPermissions("workflow");

        //  var workflowDto = _mapper.Map<List<WorkflowNameVm>>(workflowNames);

        //  if (!_loggedInUserService.IsAllInfra)
        //  {
        //      var workflowNamesDto = new List<WorkflowNameVm>();
        //      workflowNames.ForEach(y =>
        //      {
        //          var isWorkflow = false;
        //          var infraObject = new Domain.Entities.InfraObject();

        //          var workflowInfraObject = _workflowInfraObjectRepository
        //              .GetWorkflowInfraObjectByWorkflowIdAsync(y.ReferenceId).Result;

        //          if (workflowInfraObject != null)
        //          {
        //              infraObject = _infraObjectRepository.GetByReferenceIdAsync(workflowInfraObject.InfraObjectId)
        //                  .Result;
        //              isWorkflow = true;
        //          }
        //          else
        //          {
        //              var workflowInfraObjectDtl = _workflowInfraObjectRepository
        //                  .GetWorkflowInfraObjectByWorkflowIdForWorkflowList(y.ReferenceId).Result;

        //              if (workflowInfraObjectDtl is null) isWorkflow = true;
        //          }

        //          if (isWorkflow)
        //          {
        //              var workflowName = new Domain.Entities.Workflow
        //              {
        //                  Name = y.Name,
        //                  ReferenceId = y.ReferenceId
        //              };
        //              workflowNamesDto.Add(_mapper.Map<WorkflowNameVm>(workflowName));
        //              workflowDto = workflowNamesDto;
        //          }
        //      });
        //      //workflowPermission?.ForEach(x =>
        //      //{
        //      //    var workflowdtl = _workflowRepository.GetByReferenceIdAsync(x).Result;
        //      //    if (workflowdtl != null) workflowDto.Add(_mapper.Map<WorkflowNameVm>(workflowdtl));
        //      //});
        //  }

        // return workflowDto?.DistinctBy(x => x.Id).ToList() ?? new List<WorkflowNameVm>();
    }
}