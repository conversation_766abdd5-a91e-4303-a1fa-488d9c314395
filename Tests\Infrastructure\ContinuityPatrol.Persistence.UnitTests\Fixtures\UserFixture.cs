using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserFixture : IDisposable
{
    public List<User> UserPaginationList { get; set; }
    public List<User> UserList { get; set; }
    public User UserDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserFixture()
    {
        var fixture = new Fixture();

        UserList = fixture.Create<List<User>>();

        UserPaginationList = fixture.CreateMany<User>(20).ToList();

        UserPaginationList.ForEach(x => x.CompanyId = CompanyId);

        UserList.ForEach(x => x.CompanyId = CompanyId);

        UserDto = fixture.Create<User>();

        UserDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
