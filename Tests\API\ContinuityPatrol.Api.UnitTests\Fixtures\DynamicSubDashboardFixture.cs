using AutoFixture;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetByDynamicDashboardId;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DynamicSubDashboardFixture
{
    public CreateDynamicSubDashboardCommand CreateDynamicSubDashboardCommand { get; set; }
    public CreateDynamicSubDashboardResponse CreateDynamicSubDashboardResponse { get; set; }
    public UpdateDynamicSubDashboardCommand UpdateDynamicSubDashboardCommand { get; set; }
    public UpdateDynamicSubDashboardResponse UpdateDynamicSubDashboardResponse { get; set; }
    public DeleteDynamicSubDashboardCommand DeleteDynamicSubDashboardCommand { get; set; }
    public DeleteDynamicSubDashboardResponse DeleteDynamicSubDashboardResponse { get; set; }
    public GetDynamicSubDashboardDetailQuery GetDynamicSubDashboardDetailQuery { get; set; }
    public DynamicSubDashboardDetailVm DynamicSubDashboardDetailVm { get; set; }
    public GetDynamicSubDashboardListQuery GetDynamicSubDashboardListQuery { get; set; }
    public List<DynamicSubDashboardListVm> DynamicSubDashboardListVm { get; set; }
    public GetDynamicSubDashboardNameUniqueQuery GetDynamicSubDashboardNameUniqueQuery { get; set; }
    public GetDynamicSubDashboardPaginatedListQuery GetDynamicSubDashboardPaginatedListQuery { get; set; }
    public PaginatedResult<DynamicSubDashboardListVm> DynamicSubDashboardPaginatedResult { get; set; }
    public GetByDynamicDashBoardIdListQuery GetByDynamicDashBoardIdListQuery { get; set; }

    public DynamicSubDashboardFixture()
    {
        var fixture = new Fixture();

        // Configure fixture to handle circular references
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        // Create Commands
        CreateDynamicSubDashboardCommand = fixture.Create<CreateDynamicSubDashboardCommand>();
        UpdateDynamicSubDashboardCommand = fixture.Create<UpdateDynamicSubDashboardCommand>();
        DeleteDynamicSubDashboardCommand = fixture.Create<DeleteDynamicSubDashboardCommand>();

        // Create Responses
        fixture.Customize<CreateDynamicSubDashboardResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicSubDashboard created successfully"));
        CreateDynamicSubDashboardResponse = fixture.Create<CreateDynamicSubDashboardResponse>();

        fixture.Customize<UpdateDynamicSubDashboardResponse>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Success, true)
            .With(b => b.Message, "DynamicSubDashboard updated successfully"));
        UpdateDynamicSubDashboardResponse = fixture.Create<UpdateDynamicSubDashboardResponse>();

        fixture.Customize<DeleteDynamicSubDashboardResponse>(c => c
            .With(b => b.Success, true)
            .With(b => b.IsActive, false)
            .With(b => b.Message, "DynamicSubDashboard deleted successfully"));
        DeleteDynamicSubDashboardResponse = fixture.Create<DeleteDynamicSubDashboardResponse>();

        // Create Queries
        GetDynamicSubDashboardDetailQuery = fixture.Create<GetDynamicSubDashboardDetailQuery>();
        GetDynamicSubDashboardListQuery = fixture.Create<GetDynamicSubDashboardListQuery>();
        GetDynamicSubDashboardNameUniqueQuery = fixture.Create<GetDynamicSubDashboardNameUniqueQuery>();
        GetDynamicSubDashboardPaginatedListQuery = fixture.Create<GetDynamicSubDashboardPaginatedListQuery>();
        GetByDynamicDashBoardIdListQuery = fixture.Create<GetByDynamicDashBoardIdListQuery>();

        // Create ViewModels
        fixture.Customize<DynamicSubDashboardDetailVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Sub Dashboard")
            .With(b => b.DynamicDashBoardId, Guid.NewGuid().ToString)
            .With(b => b.DynamicDashBoardName, "Main Enterprise Dashboard")
            .With(b => b.Properties, "{\"layout\":\"grid\",\"widgets\":[]}"));
        DynamicSubDashboardDetailVm = fixture.Create<DynamicSubDashboardDetailVm>();

        fixture.Customize<DynamicSubDashboardListVm>(c => c
            .With(b => b.Id, Guid.NewGuid().ToString)
            .With(b => b.Name, "Enterprise Sub Dashboard")
            .With(b => b.DynamicDashBoardId, Guid.NewGuid().ToString)
            .With(b => b.DynamicDashBoardName, "Main Enterprise Dashboard")
            .With(b => b.Properties, "{\"layout\":\"grid\",\"widgets\":[]}"));
        DynamicSubDashboardListVm = fixture.CreateMany<DynamicSubDashboardListVm>(5).ToList();

        // Create PaginatedResult using the Success factory method
        DynamicSubDashboardPaginatedResult = PaginatedResult<DynamicSubDashboardListVm>.Success(
            data: DynamicSubDashboardListVm,
            count: DynamicSubDashboardListVm.Count,
            page: 1,
            pageSize: 10
        );
    }
}
