using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ReplicationRepositoryTests : IClassFixture<ReplicationFixture>, IDisposable
{
    private readonly ReplicationFixture _replicationFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ReplicationRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;

    public ReplicationRepositoryTests(ReplicationFixture replicationFixture)
    {
        _replicationFixture = replicationFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);
        _mockLicenseManagerRepository = new Mock<ILicenseManagerRepository>();
        _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
        _repository = new ReplicationRepository(_dbContext, _mockLoggedInUserService.Object, _mockLicenseManagerRepository.Object, _mockInfraObjectRepository.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.Replications.RemoveRange(_dbContext.Replications);
        await _dbContext.SaveChangesAsync();
    }

    #region GetReplicationNames Tests

    [Fact]
    public async Task GetReplicationNames_ShouldReturnActiveReplications_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "Active1", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "Active2", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "Inactive1", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = false 
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.All(result, r => Assert.Equal(ReplicationFixture.CompanyId, r.CompanyId));
        Assert.Contains(result, r => r.Name == "Active1");
        Assert.Contains(result, r => r.Name == "Active2");
        Assert.DoesNotContain(result, r => r.Name == "Inactive1");
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnOnlySpecificProperties()
    {
        // Arrange
        await ClearDatabase();

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            Type = "TestType",
            TypeId = "TypeId1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Single(result);
        var returnedReplication = result[0];
        Assert.Equal(replication.ReferenceId, returnedReplication.ReferenceId);
        Assert.Equal(replication.Name, returnedReplication.Name);
        Assert.Equal(replication.BusinessServiceId, returnedReplication.BusinessServiceId);
        // Other properties should be null/default as they're not selected
        Assert.Null(returnedReplication.Type);
        Assert.Null(returnedReplication.TypeId);
    }

    [Fact]
    public async Task GetReplicationNames_ShouldFilterByCompanyId()
    {
        // Arrange
        await ClearDatabase();

        var replications = new List<Replication>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "SameCompany", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "DifferentCompany", 
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true 
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result[0].Name);
        Assert.Equal(ReplicationFixture.CompanyId, result[0].CompanyId);
    }

    [Fact]
    public async Task GetReplicationNames_ShouldReturnEmptyList_WhenNoActiveReplications()
    {
        // Arrange
        await ClearDatabase();

        var inactiveReplications = new List<Replication>
        {
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "Inactive1", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = false 
            },
            new() 
            { 
                ReferenceId = Guid.NewGuid().ToString(), 
                Name = "Inactive2", 
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = false 
            }
        };

        foreach (var replication in inactiveReplications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationNames();

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region IsReplicationNameUnique Tests

    [Fact]
    public async Task IsReplicationNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.IsReplicationNameUnique("ExistingName");

        // Assert
        Assert.True(result); // Method returns true when name exists (opposite of unique)
    }

    [Fact]
    public async Task IsReplicationNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.IsReplicationNameUnique("NonExistentName");

        // Assert
        Assert.False(result); // Method returns false when name doesn't exist (is unique)
    }

    [Fact]
    public async Task IsReplicationNameUnique_ShouldBeCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "CaseSensitiveName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var result1 = await _repository.IsReplicationNameUnique("CaseSensitiveName");
        var result2 = await _repository.IsReplicationNameUnique("casesensitivename");
        var result3 = await _repository.IsReplicationNameUnique("CASESENSITIVENAME");

        // Assert
        Assert.True(result1);   // Exact match should return true
        Assert.False(result2);  // Different case should return false
        Assert.False(result3);  // Different case should return false
    }

    [Fact]
    public async Task IsReplicationNameUnique_ShouldHandleNullName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationNameUnique(null);

        // Assert
        Assert.False(result); // Should return false when searching for null name
    }

    [Fact]
    public async Task IsReplicationNameUnique_ShouldHandleEmptyName()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationNameUnique("");

        // Assert
        Assert.False(result); // Should return false when searching for empty name
    }

    #endregion

    #region IsReplicationNameExist Tests

    [Fact]
    public async Task IsReplicationNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.IsReplicationNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationNameExist_ShouldReturnFalse_WhenNameExistsAndIdMatchesExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.IsReplicationNameExist("ExistingName", replication.ReferenceId);

        // Assert
        Assert.False(result); // Should return false because it's the same entity
    }

    [Fact]
    public async Task IsReplicationNameExist_ShouldReturnTrue_WhenNameExistsAndIdDoesNotMatchExistingEntity()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "ExistingName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };
        await _repository.AddAsync(replication);

        // Act
        var differentId = Guid.NewGuid().ToString();
        var result = await _repository.IsReplicationNameExist("ExistingName", differentId);

        // Assert
        Assert.True(result); // Should return true because it's a different entity with same name
    }

    [Fact]
    public async Task IsReplicationNameExist_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.IsReplicationNameExist("NonExistentName", "any-id");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region GetType Tests

    [Fact]
    public async Task GetType_ShouldReturnReplicationsByTypeId_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var typeId = "TYPE_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                TypeId = typeId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                TypeId = typeId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                TypeId = "DIFFERENT_TYPE",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetType(typeId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(typeId, r.TypeId));
        Assert.Contains(result, r => r.Name == "Replication1");
        Assert.Contains(result, r => r.Name == "Replication2");
        Assert.DoesNotContain(result, r => r.Name == "Replication3");
    }

    [Fact]
    public async Task GetType_ShouldReturnReplicationsByTypeId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var typeId = "TYPE_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                TypeId = typeId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                TypeId = typeId,
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetType(typeId);

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result[0].Name);
        Assert.Equal(ReplicationFixture.CompanyId, result[0].CompanyId);
    }

    [Fact]
    public async Task GetType_ShouldReturnEmptyList_WhenNoMatchingTypeId()
    {
        // Arrange
        await ClearDatabase();

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            TypeId = "DIFFERENT_TYPE",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetType("NON_EXISTENT_TYPE");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetType_ShouldHandleNullTypeId()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetType(null);

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnReplication_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetByReferenceIdAsync(replication.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replication.ReferenceId, result.ReferenceId);
        Assert.Equal(replication.Name, result.Name);
        Assert.Equal(replication.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldFilterByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result1 = await _repository.GetByReferenceIdAsync(replications[0].ReferenceId);
        var result2 = await _repository.GetByReferenceIdAsync(replications[1].ReferenceId);

        // Assert
        Assert.NotNull(result1);
        Assert.Equal("SameCompany", result1.Name);
        Assert.Null(result2); // Should be null because it's from different company
    }

    #endregion

    #region GetPaginatedQuery Tests

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnActiveReplications_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Active1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Active2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Inactive1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var query = _repository.GetPaginatedQuery();
        var result = await query.ToListAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.All(result, r => Assert.Equal(ReplicationFixture.CompanyId, r.CompanyId));
        Assert.Contains(result, r => r.Name == "Active1");
        Assert.Contains(result, r => r.Name == "Active2");
        Assert.DoesNotContain(result, r => r.Name == "Inactive1");
    }

    [Fact]
    public async Task GetPaginatedQuery_ShouldReturnOrderedByIdDescending()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "First",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Second",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var query = _repository.GetPaginatedQuery();
        var result = await query.ToListAsync();

        // Assert
        Assert.Equal(2, result.Count);
        // Should be ordered by Id descending (newer records first)
        Assert.True(result[0].Id > result[1].Id);
    }

    #endregion

    #region GetReplicationCountByLicenseKey Tests

    [Fact]
    public async Task GetReplicationCountByLicenseKey_ShouldReturnCorrectCount()
    {
        // Arrange
        await ClearDatabase();
        var licenseId = "LICENSE_123";
        var siteTypeId = "SITE_TYPE_123";

        // Create sites with the specified type
        var sites = new List<Site>
        {
            new() { ReferenceId = "SITE_1", TypeId = siteTypeId, IsActive = true },
            new() { ReferenceId = "SITE_2", TypeId = siteTypeId, IsActive = true },
            new() { ReferenceId = "SITE_3", TypeId = "DIFFERENT_TYPE", IsActive = true }
        };

        foreach (var site in sites)
        {
            _dbContext.Sites.Add(site);
        }
        await _dbContext.SaveChangesAsync();

        // Create replications
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                LicenseId = licenseId,
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                LicenseId = licenseId,
                SiteId = "SITE_2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                LicenseId = "DIFFERENT_LICENSE",
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication4",
                LicenseId = licenseId,
                SiteId = "SITE_3", // Different site type
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS4",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationCountByLicenseKey(licenseId, siteTypeId);

        // Assert
        Assert.Equal(2, result); // Only 2 replications match both license and site type
    }

    [Fact]
    public async Task GetReplicationCountByLicenseKey_ShouldReturnZero_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetReplicationCountByLicenseKey("NON_EXISTENT_LICENSE", "NON_EXISTENT_SITE_TYPE");

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task GetReplicationCountByLicenseKey_ShouldOnlyCountActiveReplications()
    {
        // Arrange
        await ClearDatabase();
        var licenseId = "LICENSE_123";
        var siteTypeId = "SITE_TYPE_123";

        // Create site
        var site = new Site { ReferenceId = "SITE_1", TypeId = siteTypeId, IsActive = true };
        _dbContext.Sites.Add(site);
        await _dbContext.SaveChangesAsync();

        // Create replications (one active, one inactive)
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "ActiveReplication",
                LicenseId = licenseId,
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "InactiveReplication",
                LicenseId = licenseId,
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationCountByLicenseKey(licenseId, siteTypeId);

        // Assert
        Assert.Equal(1, result); // Only active replication should be counted
    }

    #endregion

    #region GetReplicationListByLicenseKey Tests

    [Fact]
    public async Task GetReplicationListByLicenseKey_ShouldReturnReplicationsByLicenseId_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var licenseId = "LICENSE_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                LicenseId = licenseId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                LicenseId = licenseId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                LicenseId = "DIFFERENT_LICENSE",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(licenseId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(licenseId, r.LicenseId));
        Assert.Contains(result, r => r.Name == "Replication1");
        Assert.Contains(result, r => r.Name == "Replication2");
        Assert.DoesNotContain(result, r => r.Name == "Replication3");
    }

    [Fact]
    public async Task GetReplicationListByLicenseKey_ShouldReturnReplicationsByLicenseId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var licenseId = "LICENSE_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                LicenseId = licenseId,
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                LicenseId = licenseId,
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationListByLicenseKey(licenseId);

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result[0].Name);
        Assert.Equal(ReplicationFixture.CompanyId, result[0].CompanyId);
    }

    [Fact]
    public async Task GetReplicationListByLicenseKey_ShouldReturnEmptyList_WhenNoMatchingLicenseId()
    {
        // Arrange
        await ClearDatabase();

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            LicenseId = "DIFFERENT_LICENSE",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetReplicationListByLicenseKey("NON_EXISTENT_LICENSE");

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationByBusinessServiceId Tests

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ShouldReturnReplicationsByBusinessServiceId_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var businessServiceId = "BS_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                BusinessServiceId = businessServiceId,
                CompanyId = ReplicationFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                BusinessServiceId = businessServiceId,
                CompanyId = ReplicationFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                BusinessServiceId = "DIFFERENT_BS",
                CompanyId = ReplicationFixture.CompanyId,
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Equal(businessServiceId, r.BusinessServiceId));
        Assert.Contains(result, r => r.Name == "Replication1");
        Assert.Contains(result, r => r.Name == "Replication2");
        Assert.DoesNotContain(result, r => r.Name == "Replication3");
    }

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ShouldReturnReplicationsByBusinessServiceId_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var businessServiceId = "BS_123";
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                BusinessServiceId = businessServiceId,
                CompanyId = ReplicationFixture.CompanyId,
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                BusinessServiceId = businessServiceId,
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result[0].Name);
        Assert.Equal(ReplicationFixture.CompanyId, result[0].CompanyId);
    }

    [Fact]
    public async Task GetReplicationByBusinessServiceId_ShouldReturnEmptyList_WhenNoMatchingBusinessServiceId()
    {
        // Arrange
        await ClearDatabase();

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            BusinessServiceId = "DIFFERENT_BS",
            CompanyId = ReplicationFixture.CompanyId,
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetReplicationByBusinessServiceId("NON_EXISTENT_BS");

        // Assert
        Assert.Empty(result);
    }

    #endregion

    #region GetReplicationByTypeIds Tests

    [Fact]
    public async Task GetReplicationByTypeIds_ShouldReturnReplicationsByTypeIds_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var typeIds = new List<string> { "TYPE_1", "TYPE_2" };
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                TypeId = "TYPE_1",
                Type = "Type1Name",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                TypeId = "TYPE_2",
                Type = "Type2Name",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                TypeId = "TYPE_3",
                Type = "Type3Name",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationByTypeIds(typeIds);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.Contains(r.TypeId, typeIds));
        Assert.Contains(result, r => r.Name == "Replication1");
        Assert.Contains(result, r => r.Name == "Replication2");
        Assert.DoesNotContain(result, r => r.Name == "Replication3");
    }

    [Fact]
    public async Task GetReplicationByTypeIds_ShouldReturnReplicationsByTypeIds_WhenIsParentFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var typeIds = new List<string> { "TYPE_1" };
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                TypeId = "TYPE_1",
                Type = "Type1Name",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                TypeId = "TYPE_1",
                Type = "Type1Name",
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationByTypeIds(typeIds);

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result[0].Name);
        Assert.Equal(ReplicationFixture.CompanyId, result[0].CompanyId);
    }

    [Fact]
    public async Task GetReplicationByTypeIds_ShouldReturnEmptyList_WhenNoMatchingTypeIds()
    {
        // Arrange
        await ClearDatabase();

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            TypeId = "DIFFERENT_TYPE",
            Type = "DifferentTypeName",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetReplicationByTypeIds(new List<string> { "NON_EXISTENT_TYPE" });

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationByTypeIds_ShouldReturnOnlySpecificProperties()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var typeIds = new List<string> { "TYPE_1" };
        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            TypeId = "TYPE_1",
            Type = "Type1Name",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            Properties = "SomeProperties",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetReplicationByTypeIds(typeIds);

        // Assert
        Assert.Single(result);
        var returnedReplication = result[0];
        Assert.Equal(replication.ReferenceId, returnedReplication.ReferenceId);
        Assert.Equal(replication.Name, returnedReplication.Name);
        Assert.Equal(replication.Type, returnedReplication.Type);
        Assert.Equal(replication.TypeId, returnedReplication.TypeId);
        // Properties should be null as it's not selected
        Assert.Null(returnedReplication.Properties);
    }

    #endregion

    #region IsReplicationLicenseState Tests

    [Fact]
    public async Task IsReplicationLicenseState_ShouldReturnTrue_WhenLicenseIsActive()
    {
        // Arrange
        var licenseId = "LICENSE_123";
        var licenseManager = new LicenseManager
        {
            ReferenceId = licenseId,
            IsState = true,
            IsActive = true
        };

        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(licenseId))
            .ReturnsAsync(licenseManager);

        // Act
        var result = await _repository.IsReplicationLicenseState(licenseId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsReplicationLicenseState_ShouldReturnFalse_WhenLicenseIsInactive()
    {
        // Arrange
        var licenseId = "LICENSE_123";
        var licenseManager = new LicenseManager
        {
            ReferenceId = licenseId,
            IsState = false,
            IsActive = true
        };

        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(licenseId))
            .ReturnsAsync(licenseManager);

        // Act
        var result = await _repository.IsReplicationLicenseState(licenseId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsReplicationLicenseState_ShouldThrowException_WhenLicenseNotFound()
    {
        // Arrange
        var licenseId = "NON_EXISTENT_LICENSE";
        _mockLicenseManagerRepository.Setup(x => x.GetLicenseDetailByIdAsync(licenseId))
            .ReturnsAsync((LicenseManager)null);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidException>(() => _repository.IsReplicationLicenseState(licenseId));
    }

    #endregion

    #region GetReplicationCountByLicenseIds Tests

    [Fact]
    public async Task GetReplicationCountByLicenseIds_ShouldReturnCorrectCounts()
    {
        // Arrange
        await ClearDatabase();
        var licenseIds = new List<string> { "LICENSE_1", "LICENSE_2", "LICENSE_3" };
        var siteTypeId = "SITE_TYPE_123";

        // Create sites with the specified type
        var sites = new List<Site>
        {
            new() { ReferenceId = "SITE_1", TypeId = siteTypeId, IsActive = true },
            new() { ReferenceId = "SITE_2", TypeId = siteTypeId, IsActive = true }
        };

        foreach (var site in sites)
        {
            _dbContext.Sites.Add(site);
        }
        await _dbContext.SaveChangesAsync();

        // Create replications
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                LicenseId = "LICENSE_1",
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                LicenseId = "LICENSE_1",
                SiteId = "SITE_2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication3",
                LicenseId = "LICENSE_2",
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationCountByLicenseIds(licenseIds, siteTypeId);

        // Assert
        Assert.Equal(3, result.Count);
        Assert.Equal(2, result["LICENSE_1"]); // 2 replications for LICENSE_1
        Assert.Equal(1, result["LICENSE_2"]); // 1 replication for LICENSE_2
        Assert.Equal(0, result["LICENSE_3"]); // 0 replications for LICENSE_3
    }

    [Fact]
    public async Task GetReplicationCountByLicenseIds_ShouldReturnEmptyDictionary_WhenNoMatches()
    {
        // Arrange
        await ClearDatabase();
        var licenseIds = new List<string> { "NON_EXISTENT_LICENSE" };

        // Act
        var result = await _repository.GetReplicationCountByLicenseIds(licenseIds, "NON_EXISTENT_SITE_TYPE");

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetReplicationCountByLicenseIds_ShouldOnlyCountActiveReplications()
    {
        // Arrange
        await ClearDatabase();
        var licenseIds = new List<string> { "LICENSE_1" };
        var siteTypeId = "SITE_TYPE_123";

        // Create site
        var site = new Site { ReferenceId = "SITE_1", TypeId = siteTypeId, IsActive = true };
        _dbContext.Sites.Add(site);
        await _dbContext.SaveChangesAsync();

        // Create replications (one active, one inactive)
        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "ActiveReplication",
                LicenseId = "LICENSE_1",
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "InactiveReplication",
                LicenseId = "LICENSE_1",
                SiteId = "SITE_1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.GetReplicationCountByLicenseIds(licenseIds, siteTypeId);

        // Assert
        Assert.Single(result);
        Assert.Equal(1, result["LICENSE_1"]); // Only active replication should be counted
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnActiveReplications_WhenIsAllInfraTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Active1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Active2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Inactive1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.All(result, r => Assert.Equal(ReplicationFixture.CompanyId, r.CompanyId));
        Assert.Contains(result, r => r.Name == "Active1");
        Assert.Contains(result, r => r.Name == "Active2");
        Assert.DoesNotContain(result, r => r.Name == "Inactive1");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoActiveReplications()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var inactiveReplication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Inactive",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = false
        };

        await _repository.AddAsync(inactiveReplication);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task ListAllAsync_ShouldFilterByCompanyId()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("SameCompany", result.First().Name);
        Assert.Equal(ReplicationFixture.CompanyId, result.First().CompanyId);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAssignedBusinessServices_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Replication2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, r => Assert.True(r.IsActive));
        Assert.All(result, r => Assert.Equal(ReplicationFixture.CompanyId, r.CompanyId));
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>();
        for (int i = 1; i <= 15; i++)
        {
            replications.Add(new Replication
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Replication{i}",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = $"BS{i}",
                IsActive = true
            });
        }

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
        Assert.All(result.Data, r => Assert.True(r.IsActive));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnSecondPage_WhenPageNumberIsTwo()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>();
        for (int i = 1; i <= 15; i++)
        {
            replications.Add(new Replication
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Replication{i:D2}",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = $"BS{i}",
                IsActive = true
            });
        }

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(2, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(5, result.Data.Count); // Remaining items on second page
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnEmptyResult_WhenNoDataExists()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(0, result.TotalPages);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterBySpecification()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "TestReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "OtherReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "InactiveReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification("Test");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
        Assert.Equal("TestReplication", result.Data.First().Name);
        Assert.Equal(1, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldSortDescending_WhenSortDirectionIsDesc()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "A_Replication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Z_Replication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal("Z_Replication", result.Data.First().Name);
        Assert.Equal("A_Replication", result.Data.Last().Name);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldHandleNullSpecification()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, null, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
        Assert.Equal("TestReplication", result.Data.First().Name);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnCorrectPageSize()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>();
        for (int i = 1; i <= 7; i++)
        {
            replications.Add(new Replication
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Replication{i}",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = $"BS{i}",
                IsActive = true
            });
        }

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 3, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal(7, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(3, result.PageSize);
        Assert.Equal(3, result.TotalPages); // 7 items / 3 per page = 3 pages (ceiling)
    }

    #endregion

    #region GetReplicationByType Tests (IQueryable)

    [Fact]
    public void GetReplicationByType_ShouldReturnFilteredReplications_WhenIsParentTrue()
    {
        // Arrange
        ClearDatabase().Wait();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Type1Replication1",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Type1Replication2",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Type2Replication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                TypeId = "TYPE2",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            _repository.AddAsync(replication).Wait();
        }

        // Act
        var result = _repository.GetReplicationByType("TYPE1");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);
        Assert.All(resultList, r => Assert.Equal("TYPE1", r.TypeId));
        Assert.Contains(resultList, r => r.Name == "Type1Replication1");
        Assert.Contains(resultList, r => r.Name == "Type1Replication2");
    }

    [Fact]
    public void GetReplicationByType_ShouldReturnEmpty_WhenNoMatchingType()
    {
        // Arrange
        ClearDatabase().Wait();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        _repository.AddAsync(replication).Wait();

        // Act
        var result = _repository.GetReplicationByType("NONEXISTENT_TYPE");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Empty(resultList);
    }

    [Fact]
    public void GetReplicationByType_ShouldFilterByCompany_WhenIsParentFalse()
    {
        // Arrange
        ClearDatabase().Wait();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "SameCompany",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "DifferentCompany",
                CompanyId = "DIFFERENT_COMPANY",
                BusinessServiceId = "BS2",
                TypeId = "TYPE1",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            _repository.AddAsync(replication).Wait();
        }

        // Act
        var result = _repository.GetReplicationByType("TYPE1");

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal("SameCompany", resultList.First().Name);
        Assert.Equal(ReplicationFixture.CompanyId, resultList.First().CompanyId);
    }

    #endregion

    #region GetReplicationByType Tests (6 Parameters - Paginated)

    [Fact]
    public async Task GetReplicationByType_Paginated_ShouldReturnPaginatedResults_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>();
        for (int i = 1; i <= 15; i++)
        {
            replications.Add(new Replication
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Type1Replication{i}",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = $"BS{i}",
                TypeId = "TYPE1",
                IsActive = true
            });
        }

        // Add some replications with different type
        replications.Add(new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "Type2Replication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS16",
            TypeId = "TYPE2",
            IsActive = true
        });

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.GetReplicationByType("TYPE1", 1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(15, result.TotalCount); // Only TYPE1 replications
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
        Assert.All(result.Data, r => Assert.Equal("TYPE1", r.TypeId));
    }

    [Fact]
    public async Task GetReplicationByType_Paginated_ShouldReturnSecondPage()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>();
        for (int i = 1; i <= 15; i++)
        {
            replications.Add(new Replication
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = $"Type1Replication{i:D2}",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = $"BS{i}",
                TypeId = "TYPE1",
                IsActive = true
            });
        }

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.GetReplicationByType("TYPE1", 2, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(5, result.Data.Count); // Remaining items on second page
        Assert.Equal(15, result.TotalCount);
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(2, result.TotalPages);
        Assert.All(result.Data, r => Assert.Equal("TYPE1", r.TypeId));
    }

    [Fact]
    public async Task GetReplicationByType_Paginated_ShouldReturnEmptyResult_WhenNoMatchingType()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.GetReplicationByType("NONEXISTENT_TYPE", 1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Empty(result.Data);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(1, result.CurrentPage);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(0, result.TotalPages);
    }

    [Fact]
    public async Task GetReplicationByType_Paginated_ShouldFilterBySpecification()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "TestReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "OtherReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "InactiveReplication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS3",
                TypeId = "TYPE1",
                IsActive = false
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification("Test");

        // Act
        var result = await _repository.GetReplicationByType("TYPE1", 1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Single(result.Data);
        Assert.Equal("TestReplication", result.Data.First().Name);
        Assert.Equal("TYPE1", result.Data.First().TypeId);
        Assert.Equal(1, result.TotalCount);
    }

    [Fact]
    public async Task GetReplicationByType_Paginated_ShouldSortDescending()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        var replications = new List<Replication>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "A_Replication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS1",
                TypeId = "TYPE1",
                IsActive = true
            },
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Name = "Z_Replication",
                CompanyId = ReplicationFixture.CompanyId,
                BusinessServiceId = "BS2",
                TypeId = "TYPE1",
                IsActive = true
            }
        };

        foreach (var replication in replications)
        {
            await _repository.AddAsync(replication);
        }

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.GetReplicationByType("TYPE1", 1, 10, specification, "Name", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(2, result.Data.Count);
        Assert.Equal("Z_Replication", result.Data.First().Name);
        Assert.Equal("A_Replication", result.Data.Last().Name);
        Assert.All(result.Data, r => Assert.Equal("TYPE1", r.TypeId));
    }

    #endregion

    #region GetAssignedBusinessServicesByReplications Tests (Indirect)

    [Fact]
    public async Task ListAllAsync_ShouldExecuteSuccessfully_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationFixture.CompanyId);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // This test verifies the method executes without error when IsAllInfra is false
        // The actual filtering logic depends on assigned business services setup
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldExecuteSuccessfully_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationFixture.CompanyId);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        var specification = new ReplicationFilterSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        // This test verifies the method executes without error when IsAllInfra is false
        // The actual filtering logic depends on assigned business services setup
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnReplication_WhenIsParentTrue()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetByIdAsync(replication.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replication.Id, result.Id);
        Assert.Equal(replication.Name, result.Name);
        Assert.Equal(replication.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnReplication_WhenIsParentFalseAndSameCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(ReplicationFixture.CompanyId);

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetByIdAsync(replication.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(replication.Id, result.Id);
        Assert.Equal(replication.Name, result.Name);
        Assert.Equal(replication.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenIsParentFalseAndDifferentCompany()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("DIFFERENT_COMPANY");

        var replication = new Replication
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "TestReplication",
            CompanyId = ReplicationFixture.CompanyId,
            BusinessServiceId = "BS1",
            TypeId = "TYPE1",
            IsActive = true
        };

        await _repository.AddAsync(replication);

        // Act
        var result = await _repository.GetByIdAsync(replication.Id);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion
}

// Simple concrete specification for testing
public class ReplicationFilterSpecification : Specification<Replication>
{
    public ReplicationFilterSpecification(string searchString = null)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.Name.Contains(searchString);
        }
    }
}
