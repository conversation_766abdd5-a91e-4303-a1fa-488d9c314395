﻿
$(".toggle-password").on('click',async function () {
    let input = $(this).prev();
    let icon = $(this).find("i");
    let loginName = $("#LoginName").data("loginnames");
    if (input.attr("type") === "password") {
        showPassword(input, icon);
        let encryptedPassword = input.val();
        if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
            let afterLoginName = await onfocusPassword(encryptedPassword);
            if (loginName) {
                afterLoginName = afterLoginName?.substring(loginName.length);
            }
            input.val(afterLoginName);
        }
    } else {
        hidePassword(input, icon);
        let value = input.val().replace(/\s+/g, '');
        blurpassword(input.attr("id"), value);
    }
});

//$(".cp-password-hide").on('mouseover',function () {
//    $(this).attr("title", "Hide Password");
//});

//$(".cp-password-visible").on('mouseover',function () {
//    $(this).attr("title", "Show Password");
//});




//function showPassword(input, icon) {
//    input.attr("type", "text");
//    icon.removeClass("cp-password-visible").addClass("cp-password-hide")
//    var icon = $(".cp-password-hide");
//    icon.attr("title", "Hide Password");
//    $(".cp-password-hide").on('mouseover',function () {
//        $(this).attr("title", "Hide Password");
//    });


//}

//function hidePassword(input, icon) {
//    input.attr("type", "password");
//    icon.removeClass("cp-password-hide").addClass("cp-password-visible");
//    var icon = $(".cp-password-visible");
//    icon.attr("title", "Show Password");
//    $(".cp-password-visible").on('mouseover',function () {
//        $(this).attr("title", "Show Password");
//    });


//}
$("#reset_value").on('click', function () {
    $('#OldPassword-error, #NewPassword-error, #ConfirmPassword-error').text('').removeClass('field-validation-error');
    $("#CurrentPassword, #changeConfirmPassword, #changeNewPassword").val("");
    $('.toggle-password i').addClass('cp-password-visible fs-6');
    $('#CurrentPassword').attr('type', 'password');
    $('#changeNewPassword').attr('type', 'password');
});

$('#CurrentPassword,#changeConfirmPassword').on('blur', async function () {
    let value = this.value.replace(/\s+/g, '');
    blurpassword(this.id, value);
});
$(document).on('focus', '#CurrentPassword,#changeNewPassword', function () {
    focuspassword(this.id);
});
$(document).on('input', '#CurrentPassword', function () {
    let value = this.value.replace(/\s+/g, '');
    inputpassword(this.id, value);
});
$(document).on('input', '#changeNewPassword', function () {
    let value = this.value.replace(/\s+/g, '');
    inputpassword(this.id, value);
    $('#changeConfirmPassword').val('');
    $('#ConfirmPassword-error').text('').removeClass('field-validation-error');
});
$(document).on('input', '#changeConfirmPassword', function () {
    let value = this.value.replace(/\s+/g, '');
    inputConfirmpassword(this.id, value);
});

//$('#CurrentPassword').on('keydown keyup', async function () {
//    let passwordcurrentId = $('#CurrentPassword').val();
//    const value = $(this).val();
//    const sanitizedValue = value.replace(/\s+/g, '');
//    $(this).val(sanitizedValue);
//    await validateCurrentPassword(sanitizedValue, passwordcurrentId);
//});
//$('#Password').on('keydown keyup', async function () {
//    let passwordId = $('#Password').val();
//    const value = $(this).val();
//    const passwordValue = value.replace(/\s+/g, '');
//    $(this).val(passwordValue);
//    await validateNewPassword(passwordValue, passwordId);
//    $('#ConfirmPassword').val('');
//});
//$('#ConfirmPassword').on('keydown keyup', async function () {
//    let passwordconfirmId = $('#ConfirmPassword').val();
//    const value = $(this).val();
//    const confirmValue = value.replace(/\s+/g, '');
//    $(this).val(confirmValue);
//    await validateConfirmPassword(confirmValue, passwordconfirmId);
//});

//$("#CurrentPassword, #Password, #ConfirmPassword").on('blur',async function () {
//    let loginName = $("#loginName").data("loginnames").toLowerCase();
//    let password = $(this).val();

//    if (password != "" && password.length > 0 && password.length < 64) {
//        try {
//            let blurPassword = await EncryptPassword(loginName + password, "#" + this.id);
//            $(this).val(blurPassword);
//        } catch (error) {
          
//        }
//    }

//    $(this).attr('type', 'password');
//    $('.toggle-password i').removeClass('fs-6');
//    $(this).siblings('.toggle-password').find('i').addClass('cp-password-visible fs-6');
//});


//$("#CurrentPassword, #Password, #ConfirmPassword").on('focus',async function () {

//    let encryptedPassword = $(this).val();
//    if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
//        let afterLoginName = await onfocusPassword(encryptedPassword);
//        $(this).val(afterLoginName);
//    }

//});

//async function onfocusPassword(encryptedPassword) {
//    let loginName = $("#loginName").data("loginnames");

//    if (encryptedPassword !== "" && encryptedPassword.length > 0 && encryptedPassword.length > 30) {
//        try {

//            let decryptedPassword = await DecryptPassword(encryptedPassword);

//            let afterLoginName = decryptedPassword?.substring(loginName.length);

//            return afterLoginName



//        } catch (error) {
            
//        }
//    }

//    return null;
//}

function loginpasswordDebounce(func, delay = 300) {
    let timer;
    return function () {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
            func.apply(context, args);
        }, delay);
    };
}

$("#SaveFunction").prop('disabled', true);

$("#SaveFunction").on('click', loginpasswordDebounce(async function () {
    
    let currentPassword = $("#CurrentPassword").val();
    let newPassword = $("#changeNewPassword").val();
    let confirmPassword = $("#changeConfirmPassword").val();

    let loginName = $("#LoginName").data("loginnames");
    let loginId = $("#loginId").data("loginid");

    let isCurrentPassword = await CurrentPassword(currentPassword);
    let isNewPassword = await NewPassword(newPassword);
    let isConfirmPassword = await ConfirmPassword(confirmPassword);

    if (loginName && isCurrentPassword && isNewPassword && isConfirmPassword) {
        try {
            if (currentPassword.length < 30) {
                const encryptedCurrentPassword = await EncryptPassword(loginName.toLowerCase() + currentPassword);
                $("#CurrentPassword").attr('type', 'password');
                $("#CurrentPassword").val(encryptedCurrentPassword);
            }
            if (newPassword.length < 30) {
                const encryptedNewPassword = await EncryptPassword(loginName.toLowerCase() + newPassword);
                $("#changeNewPassword").attr('type', 'password');
                $("#changeNewPassword").val(encryptedNewPassword);
            }
            if (confirmPassword.length < 30) {
                const encryptedConfirmPassword = await EncryptPassword(loginName.toLowerCase() + confirmPassword);
                $("#changeConfirmPassword").attr('type', 'password');
                $("#changeConfirmPassword").val(encryptedConfirmPassword);
            }
           
            $("#LoginName").val(loginName);
            $("#loginId").val(loginId);
            $(this).prop('disabled', true);         
            $("#CreateForm").trigger('submit');                
        } catch (error) {
            
        }
    }
},500));


async function CurrentPassword(value) {
    const errorElement = $('#OldPassword-error');

    if (!value) {
        errorElement.text('Enter current password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    return true;
}
async function NewPassword(value) {
    const errorElement = $('#NewPassword-error');


    if (!value) {
        errorElement.text('Enter new password').addClass('field-validation-error');
        return false;
    }

    else {
        errorElement.text('').removeClass('field-validation-error');
    }

    return true;
}
async function ConfirmPassword(value) {
    const errorElement = $('#ConfirmPassword-error');

    if (!value) {
        errorElement.text('Enter confirm password').addClass('field-validation-error');
        return false;
    } else {
        errorElement.text('').removeClass('field-validation-error');
    }

    return true;
}
//async function validateCurrentPassword(value) {

//    let currentPassword1 = $("#CurrentPassword").val();
//    let SamePassword1 = await onfocusPassword(currentPassword1);
//    const errorElement = $('#OldPassword-error');
//    let Passwordvalid1 = $("#Password").val();
//    let Passwordsame = await onfocusPassword(Passwordvalid1);
//    if (!value) {
//        errorElement.text('Enter current password').addClass('field-validation-error');

//        return false;

//    } else {
//        errorElement.text('').removeClass('field-validation-error');

//    }
//    if (SamePassword1 === value || (Passwordsame === value)) {
//        $('#NewPassword-error').text('Same password already exists').addClass('field-validation-error');
//        return false;
//    }
//    else {

//        $('#NewPassword-error').text('').removeClass('field-validation-error');
//        return true;
//    }


//    return true;
//}
//async function validateNewPassword(value) {

//    const errorElement = $('#NewPassword-error');
//    let currentPassword = $("#CurrentPassword").val();
//    let SamePassword = await onfocusPassword(currentPassword);
//    if (!value) {
//        errorElement.text('Enter new password').addClass('field-validation-error');
//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }
//    else if (SamePassword === value || currentPassword === value) {

//        errorElement.text('Same password already exists').addClass('field-validation-error');
//        return false;
//    }

//    try {

//        const validationResult = await PasswordPolicy(value);

//        if (validationResult === true) {

//            errorElement.text('').removeClass('field-validation-error');
//            $("#SaveFunction").prop('disabled', false);
//            return true;
//        }

//        else {
//            $("#SaveFunction").prop('disabled', true);
//            return false;
//        }

//    } catch (error) {

//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }

//}
//async function PasswordPolicy(value) {
//    const settingList = "Admin/Settings/GetList";
//    let defaultSkey = "Password Policy";

//    try {
//        const response = await $.ajax({
//            type: "GET",
//            url: RootUrl + settingList,
//            async: true
//        });

//        if (response && response.data && response.data.length > 0) {
//            const passwordPolicy = response.data.find(pwdplcy => pwdplcy.sKey === defaultSkey);
//            if (passwordPolicy) {
//                let passwordRules = JSON.parse(passwordPolicy.sValue);
//                let minSValue = passwordRules.minSValue;
//                let maxSValue = passwordRules.maxSValue;
//                let minUpSValue = passwordRules.minUpSValue;
//                let minNumSValue = passwordRules.minNumSValue;
//                let minLowSValue = passwordRules.minLowSValue;
//                let minSpclSValue = passwordRules.minSpclSValue;

//                return validatePassword(value, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue);
//            }
//            else {
//                return settingPassword(value)
//            }
//        }
//        else {
//            return settingPassword(value)
//        }
//    } catch (error) {   
//        return "Error fetching password policy";
//    }
//}
//function validatePassword(value, minSValue, minUpSValue, minNumSValue, minLowSValue, minSpclSValue, maxSValue) {

//    const uppercaseCount = (value.match(/[A-Z]/g) || []).length;
//    const numericCount = (value.match(/[0-9]/g) || []).length;
//    const lowercaseCount = (value.match(/[a-z]/g) || []).length;
//    const specialCount = (value.match(/[^a-zA-Z0-9]/g) || []).length;
//    const errorElement = $('#NewPassword-error');

//    if (value.length < parseInt(minSValue)) {
//        errorElement.text("Password is too short").addClass('field-validation-error');
//        return false;
//    }

//    if (uppercaseCount < parseInt(minUpSValue)) {
//        errorElement.text("Password should contain at least " + minUpSValue + " uppercase character(s).").addClass('field-validation-error');
//        return false;
//    }

//    if (numericCount < parseInt(minNumSValue)) {
//        errorElement.text("Password should contain at least " + minNumSValue + " numeric character(s).").addClass('field-validation-error');
//        return false;
//    }

//    if (value.length > parseInt(maxSValue)) {
//        errorElement.text("Password is too long").addClass('field-validation-error');
//        return false;
//    }

//    if (lowercaseCount < parseInt(minLowSValue)) {
//        errorElement.text("Password should contain at least " + minLowSValue + " lowercase character(s)").addClass('field-validation-error');
//        return false;
//    }

//    if (specialCount < parseInt(minSpclSValue)) {
//        errorElement.text("Password should contain at least " + minSpclSValue + " special character(s)").addClass('field-validation-error');
//        return false;
//    }

//    errorElement.text('').removeClass('field-validation-error');

//    return true;

//}
//function settingPassword(value) {

//    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#$@!%&*?])[A-Za-z\d#$@!%&*?]{8,15}$/;
//    const errorElement = $('#NewPassword-error');
//    const errorMessages = {
//        invalid: "Invalid password",
//        length: "Password must be at least 8 characters",
//        number: "Password must contain at least one number",
//        symbol: "Password must contain at least one symbol",
//        uppercase: "Password must contain at least one uppercase letter",
//        lowercase: "Password must contain at least one lowercase letter",
//    };

//    if (value.length < 8) {
//        errorElement.text(errorMessages.length).addClass('field-validation-error');
//        return false;
//    } else if (!/\d/.test(value)) {
//        errorElement.text(errorMessages.number).addClass('field-validation-error');
//        return false;
//    } else if (!/[!@#$%^&*]/.test(value)) {
//        errorElement.text(errorMessages.symbol).addClass('field-validation-error');
//        return false;
//    } else if (!/[A-Z]/.test(value)) {
//        errorElement.text(errorMessages.uppercase).addClass('field-validation-error');
//        return false;
//    } else if (!/[a-z]/.test(value)) {
//        errorElement.text(errorMessages.lowercase).addClass('field-validation-error');
//        return false;
//    }
//    else if (!passwordRegex.test(value)) {
//        errorElement.text(errorMessages.invalid).addClass('field-validation-error');
//        return false;
//    }
//    errorElement.text('').removeClass('field-validation-error');

//    return true;

//}
//async function validateConfirmPassword(value) {
//    const errorElement = $('#ConfirmPassword-error');

//    if (!value) {
//        errorElement.text('Enter confirm password').addClass('field-validation-error');
//        $("#SaveFunction").prop('disabled', true);
//        return false;
//    }

//    const encryptPassword = $('#Password').val();
//    let newPassword = await onfocusPassword(encryptPassword);
//    let newdecryptPassword = encryptPassword;

//    if (newPassword !== value && newdecryptPassword !== value) {
//        errorElement.text('Password does not match').addClass('field-validation-error');
//    } else {
//        errorElement.text('').removeClass('field-validation-error');
//    }

//    if ($('#NewPassword-error').text()) {

//        $("#SaveFunction").prop('disabled', true);
//    } else if (errorElement.text() === '') {
//        $("#SaveFunction").prop('disabled', false);
//        return true;
//    } else {
//        $("#SaveFunction").prop('disabled', true);
//    }

//    return false;
//}


//$('#ConfirmPassword').on('input', function () {
//    const confirmPassword = $(this).val();
//    validateConfirmPassword(confirmPassword);
//});

const EncryptPassword = async (text) => {
    
    const generateKey = async () => {
        const key = await window.crypto.subtle.generateKey({
            name: 'AES-GCM',
            length: 256
        },
            true, [
            'encrypt',
            'decrypt'
        ]);
        const exportedKey = await window.crypto.subtle.exportKey(
            'raw',
            key,
        );
        return bufferToBase64(exportedKey);
    }

    // arrayBuffer to base64
    const bufferToBase64 = (arrayBuffer) => {
        return window.btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
    }

    // load a base64 encoded key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const cryptGcm = async (base64Key, bytes) => {
        const key = await loadKey(base64Key);
        const iv = window.crypto.getRandomValues(new Uint8Array(12));
        const algorithm = {
            iv,
            name: 'AES-GCM'
        };
        const cipherData = await window.crypto.subtle.encrypt(
            algorithm,
            key,
            bytes
        );

        // prepend the random IV bytes to raw cipherdata
        const cipherText = concatArrayBuffers(iv.buffer, cipherData);
        return bufferToBase64(cipherText);
    }

    // concatenate two array buffers
    const concatArrayBuffers = (buffer1, buffer2) => {
        let tmp = new Uint8Array(buffer1.byteLength + buffer2.byteLength);
        tmp.set(new Uint8Array(buffer1), 0);
        tmp.set(new Uint8Array(buffer2), buffer1.byteLength);
        return tmp.buffer;
    }

    const plaintext = text;
    const plaintextBytes = (new TextEncoder()).encode(plaintext, 'utf-8');
    const encryptionKey = await generateKey();
    const ciphertext = await cryptGcm(encryptionKey, plaintextBytes);
  
    if (encryptionKey && ciphertext) {
        return encryptionKey + "$" + ciphertext;
    }


}

const DecryptPassword = async (encryptedText) => {
    const [encryptionKeyBase64, ciphertextBase64] = encryptedText.split("$");

    // Load the encryption key
    const loadKey = async (base64Key) => {
        return await window.crypto.subtle.importKey(
            'raw',
            base64ToBuffer(base64Key),
            "AES-GCM",
            true, [
            "encrypt",
            "decrypt"
        ]
        );
    }

    // base64 to arrayBuffer
    const base64ToBuffer = (base64) => {
        const binary_string = window.atob(base64);
        const len = binary_string.length;
        let bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }

    const key = await loadKey(encryptionKeyBase64);
    const ciphertextBuffer = base64ToBuffer(ciphertextBase64);
    const iv = ciphertextBuffer.slice(0, 12);
    const encryptedData = ciphertextBuffer.slice(12);

    const algorithm = {
        iv,
        name: 'AES-GCM'
    };

    const decryptedData = await window.crypto.subtle.decrypt(algorithm, key, encryptedData);
    const decryptedText = new TextDecoder().decode(decryptedData);
    return decryptedText;
}

//const getEncryptionPassword = async (text) => {
//    let dataPassword = await EncryptPassword(text).then((data) => {
//        return data
//    })
//    return dataPassword
//}
function CommonValidation(errorElement, validationResults) {

    const failedValidations = validationResults.filter(result => result !== true);

    if (failedValidations.length > 0) {
        errorElement.text(failedValidations[0])
            .addClass('field-validation-error')

        return false;
    } else {
        errorElement.text('')
            .removeClass('field-validation-error')
        return true;
    }
}
