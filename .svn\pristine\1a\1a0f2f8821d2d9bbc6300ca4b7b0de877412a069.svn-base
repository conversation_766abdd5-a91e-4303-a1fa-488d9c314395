﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace ContinuityPatrol.Application.Features.Report.Queries.CyberSnapsReport
{
    public class GetCyberSnapsReportQueryHandler : IRequestHandler<GetCyberSnapsReportQuery, GetCyberSnapsReportVm>
    {
        private readonly ILogger<GetCyberSnapsReportQueryHandler> _logger;
        private readonly ICyberSnapsRepository _cyberSnapsRepository;
        private readonly ILoggedInUserService _loggedInUserService;
        private readonly IMapper _mapper;
        private readonly IPublisher _publisher;
        public GetCyberSnapsReportQueryHandler(ILogger<GetCyberSnapsReportQueryHandler> logger, ICyberSnapsRepository cyberSnapsRepository
            , ILoggedInUserService loggedInUserService, IMapper mapper, IPublisher publisher)
        {
            _logger = logger;
            _cyberSnapsRepository = cyberSnapsRepository;
            _loggedInUserService = loggedInUserService;
            _mapper = mapper;
            _publisher = publisher;
        }
        public async Task<GetCyberSnapsReportVm> Handle(GetCyberSnapsReportQuery request, CancellationToken cancellationToken)
        {
            var mappedCyberSnapsReportVms = new List<CyberSnapsReportVm>();

            var getCyberSnapsReportVm = await _cyberSnapsRepository.GetCyberSnapsBySnapTagName(request.CyberSnapTagName, request.StartDate, request.EndDate);

            mappedCyberSnapsReportVms = _mapper.Map<List<CyberSnapsReportVm>>(getCyberSnapsReportVm);

            if(mappedCyberSnapsReportVms.Count > 0)
            {
                await _publisher.Publish(
                    new ReportViewedEvent
                    { ReportName = "Snap Report", ActivityType = ActivityType.View.ToString() },
                    CancellationToken.None);
            }
            return new GetCyberSnapsReportVm
            {
                ReportGeneratedBy = _loggedInUserService.LoginName,
                ReportGeneratedTime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt"),
                CyberSnapsReportVm = mappedCyberSnapsReportVms
            };
        }
    }
}
