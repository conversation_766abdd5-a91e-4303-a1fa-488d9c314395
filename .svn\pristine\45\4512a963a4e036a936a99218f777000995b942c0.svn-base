﻿using ContinuityPatrol.Domain.ViewModels.NodeWorkflowExecutionModel;

namespace ContinuityPatrol.Application.Features.NodeWorkflowExecution.Queries.GetList;

public class
    GetNodeWorkflowExecutionListQueryHandler : IRequestHandler<GetNodeWorkflowExecutionListQuery,
        List<NodeWorkflowExecutionListVm>>
{
    private readonly IMapper _mapper;
    private readonly INodeWorkflowExecutionRepository _nodeWorkflowExecutionRepository;

    public GetNodeWorkflowExecutionListQueryHandler(IMapper mapper,
        INodeWorkflowExecutionRepository nodeWorkflowExecutionRepository)
    {
        _mapper = mapper;
        _nodeWorkflowExecutionRepository = nodeWorkflowExecutionRepository;
    }

    public async Task<List<NodeWorkflowExecutionListVm>> Handle(GetNodeWorkflowExecutionListQuery request,
        CancellationToken cancellationToken)
    {
        var nodeWorkflowExecution = (await _nodeWorkflowExecutionRepository.ListAllAsync()).ToList();

        return nodeWorkflowExecution.Count == 0
            ? new List<NodeWorkflowExecutionListVm>()
            : _mapper.Map<List<NodeWorkflowExecutionListVm>>(nodeWorkflowExecution);
    }
}