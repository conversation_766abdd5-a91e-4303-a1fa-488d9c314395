﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class PageSolutionMappingController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<PageSolutionMappingListVm>>> GetPageSolutionMapping()
    {
        Logger.LogDebug("Get All PageSolutionMapping");

        return Ok(await Mediator.Send(new GetPageSolutionMappingListQuery()));
    }

    [HttpGet("id", Name = "Get PageSolutionMapping")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<PageSolutionMappingDetailVm>>> GetPageSolutionMappingById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageSolutionMapping Id");

        Logger.LogDebug($"Get PageSolutionMapping Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetPageSolutionMappingDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreatePageSolutionMappingResponse>> CreatePageSolutionMapping([FromBody] CreatePageSolutionMappingCommand createPageSolutionMappingCommand)
    {
        Logger.LogDebug($"Creating PageSolutionMapping '{createPageSolutionMappingCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreatePageSolutionMapping), await Mediator.Send(createPageSolutionMappingCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdatePageSolutionMappingResponse>> UpdatePageSolutionMapping([FromBody] UpdatePageSolutionMappingCommand updatePageSolutionMappingCommand)
    {
        Logger.LogDebug($"Updating PageSolutionMapping '{updatePageSolutionMappingCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updatePageSolutionMappingCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeletePageSolutionMappingResponse>> DeletePageSolutionMapping(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "PageSolutionMapping Id");

        Logger.LogDebug($"Delete PageSolutionMapping Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeletePageSolutionMappingCommand { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<PaginatedResult<PageSolutionMappingListVm>>> GetPaginatedPageSolutionMapping([FromQuery] GetPageSolutionMappingPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in PageSolutionMapping Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsPageSolutionMappingNameExist(string pageSolutionName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(pageSolutionName, "PageSolutionMapping Name");

        Logger.LogDebug($"Check Name Exists Detail by PageSolutionMapping Name '{pageSolutionName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetPageSolutionMappingNameUniqueQuery { Name = pageSolutionName, Id = id }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllPageSolutionMappingCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}
