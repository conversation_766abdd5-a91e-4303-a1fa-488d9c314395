﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class PluginManagerRepositoryMocks
{
    public static Mock<IPluginManagerRepository> CreatePluginManagerRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();
        pluginManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(pluginManagers);
        pluginManagerRepository.Setup(repo => repo.AddAsync(It.IsAny<PluginManager>())).ReturnsAsync(
            (PluginManager pluginManager) =>
            {
                pluginManager.Id = new Fixture().Create<int>();
                pluginManager.ReferenceId = new Fixture().Create<Guid>().ToString();
                pluginManagers.Add(pluginManager);
                return pluginManager;
            });

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> UpdatePluginManagerRepository(List<PluginManager> pluginManagers)
    {

        var pluginManagerRepository = new Mock<IPluginManagerRepository>();
        pluginManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(pluginManagers);

        pluginManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => pluginManagers.SingleOrDefault(x => x.ReferenceId == i));

        pluginManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<PluginManager>())).ReturnsAsync((PluginManager pluginManager) =>
        {
            var index = pluginManagers.FindIndex(item => item.ReferenceId == pluginManager.ReferenceId);

            pluginManagers[index] = pluginManager;

            return pluginManager;
        });


        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> DeletePluginManagerRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();
        pluginManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(pluginManagers);

        pluginManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => pluginManagers.SingleOrDefault(x => x.ReferenceId == i));

        pluginManagerRepository.Setup(repo => repo.UpdateAsync(It.IsAny<PluginManager>())).ReturnsAsync((PluginManager pluginManager) =>
        {
            var index = pluginManagers.FindIndex(item => item.ReferenceId == pluginManager.ReferenceId);

            pluginManager.IsActive = false;
            pluginManagers[index] = pluginManager;

            return pluginManager;
        });

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> GetPluginManagerRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();

        pluginManagerRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => pluginManagers.SingleOrDefault(x => x.ReferenceId == i));

        pluginManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(pluginManagers);

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> GetPluginManagerEmptyRepository()
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();

        pluginManagerRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<PluginManager>());

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> GetPluginManagerNamesRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();

        pluginManagerRepository.Setup(repo => repo.GetPluginNames()).ReturnsAsync(pluginManagers);

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> GetPluginManagerNameUniqueRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();

        pluginManagerRepository.Setup(repo => repo.IsPluginNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => pluginManagers.Exists(x => x.Name == i && x.ReferenceId == j));

        return pluginManagerRepository;
    }

    public static Mock<IPluginManagerRepository> GetPaginatedPluginManagerRepository(List<PluginManager> pluginManagers)
    {
        var pluginManagerRepository = new Mock<IPluginManagerRepository>();

        var queryablePluginManager = pluginManagers.BuildMock();

        pluginManagerRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryablePluginManager);

        return pluginManagerRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreatePluginManagerEventRepository(List<UserActivity> userActivities)
    {
        var pluginManagerEventRepository = new Mock<IUserActivityRepository>();
        
        pluginManagerEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return pluginManagerEventRepository;
    }
}