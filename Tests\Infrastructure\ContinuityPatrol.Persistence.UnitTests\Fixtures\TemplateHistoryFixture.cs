using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class TemplateHistoryFixture : IDisposable
{
    public List<TemplateHistory> TemplateHistoryPaginationList { get; set; }
    public List<TemplateHistory> TemplateHistoryList { get; set; }
    public TemplateHistory TemplateHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public TemplateHistoryFixture()
    {
        var fixture = new Fixture();

        TemplateHistoryList = fixture.Create<List<TemplateHistory>>();

        TemplateHistoryPaginationList = fixture.CreateMany<TemplateHistory>(20).ToList();

        TemplateHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        TemplateHistoryList.ForEach(x => x.CompanyId = CompanyId);

        TemplateHistoryDto = fixture.Create<TemplateHistory>();

        TemplateHistoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public TemplateHistory CreateTemplateHistory(
        string companyId = "COMPANY_123",
        string loginName = "<EMAIL>",
        string templateId = null,
        string templateName = "Default Template",
        string description = "Default Description",
        string type = "WORKFLOW",
        string actionType = "CREATE",
        string version = "1.0",
        string icon = "default-icon.png",
        string properties = null,
        string replicationTypeId = "REPL_001",
        string replicationTypeName = "Default Replication",
        string updaterId = "UPDATER_001",
        string comments = "Default Comments",
        bool isActive = true,
        bool isDelete = false)
    {
        return new TemplateHistory
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = companyId,
            LoginName = loginName,
            TemplateId = templateId ?? Guid.NewGuid().ToString(),
            TemplateName = templateName,
            Description = description,
            Type = type,
            ActionType = actionType,
            Version = version,
            Icon = icon,
            Properties = properties ?? "{\"type\": \"template\", \"status\": \"active\", \"lastModified\": \"2024-01-01T10:00:00Z\"}",
            ReplicationTypeId = replicationTypeId,
            ReplicationTypeName = replicationTypeName,
            UpdaterId = updaterId,
            Comments = comments,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<TemplateHistory> CreateMultipleTemplateHistories(int count, string templateId = null, string companyId = "COMPANY_123")
    {
        var histories = new List<TemplateHistory>();
        var tId = templateId ?? Guid.NewGuid().ToString();

        for (int i = 1; i <= count; i++)
        {
            histories.Add(CreateTemplateHistory(
                companyId: companyId,
                templateId: tId,
                templateName: $"Template {i}",
                version: $"{i}.0",
                actionType: i == 1 ? "CREATE" : "UPDATE",
                description: $"Description for version {i}.0"
            ));
        }
        return histories;
    }

    public TemplateHistory CreateTemplateHistoryWithSpecificId(string referenceId, string templateId = null)
    {
        return new TemplateHistory
        {
            ReferenceId = referenceId,
            CompanyId = "COMPANY_123",
            LoginName = "<EMAIL>",
            TemplateId = templateId ?? Guid.NewGuid().ToString(),
            TemplateName = "Test Template",
            Description = "Test Description",
            Type = "WORKFLOW",
            ActionType = "CREATE",
            Version = "1.0",
            Icon = "test-icon.png",
            Properties = "{\"test\": true}",
            ReplicationTypeId = "REPL_TEST",
            ReplicationTypeName = "Test Replication",
            UpdaterId = "TEST_UPDATER",
            Comments = "Test Comments",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public TemplateHistory CreateTemplateHistoryForTemplate(string templateId, string templateName, string companyId = "COMPANY_123")
    {
        return CreateTemplateHistory(
            companyId: companyId,
            templateId: templateId,
            templateName: templateName,
            description: $"History for {templateName}"
        );
    }

    public List<TemplateHistory> CreateTemplateHistoriesForTemplate(string templateId, string templateName, int count, string companyId = "COMPANY_123")
    {
        var histories = new List<TemplateHistory>();
        for (int i = 1; i <= count; i++)
        {
            histories.Add(CreateTemplateHistory(
                companyId: companyId,
                templateId: templateId,
                templateName: templateName,
                version: $"{i}.0",
                actionType: i == 1 ? "CREATE" : "UPDATE",
                description: $"Version {i}.0 of {templateName}",
                comments: $"Changes in version {i}.0"
            ));
        }
        return histories;
    }

    public List<TemplateHistory> CreateTemplateHistoriesWithStatus(int activeCount, int inactiveCount, string templateId = null, string companyId = "COMPANY_123")
    {
        var histories = new List<TemplateHistory>();
        var tId = templateId ?? Guid.NewGuid().ToString();

        for (int i = 1; i <= activeCount; i++)
        {
            histories.Add(CreateTemplateHistory(
                companyId: companyId,
                templateId: tId,
                templateName: $"Active Template {i}",
                version: $"{i}.0",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            histories.Add(CreateTemplateHistory(
                companyId: companyId,
                templateId: tId,
                templateName: $"Inactive Template {i}",
                version: $"{i + activeCount}.0",
                isActive: false
            ));
        }

        return histories;
    }

    public TemplateHistory CreateWorkflowTemplateHistory(string templateId = null, string companyId = "COMPANY_123")
    {
        return CreateTemplateHistory(
            companyId: companyId,
            templateId: templateId ?? Guid.NewGuid().ToString(),
            templateName: "Workflow Template",
            type: "WORKFLOW",
            actionType: "CREATE",
            icon: "workflow-icon.png",
            properties: "{\"type\": \"workflow\", \"steps\": 5, \"complexity\": \"medium\"}"
        );
    }

    public TemplateHistory CreateFormTemplateHistory(string templateId = null, string companyId = "COMPANY_123")
    {
        return CreateTemplateHistory(
            companyId: companyId,
            templateId: templateId ?? Guid.NewGuid().ToString(),
            templateName: "Form Template",
            type: "FORM",
            actionType: "CREATE",
            icon: "form-icon.png",
            properties: "{\"type\": \"form\", \"fields\": 10, \"validation\": true}"
        );
    }

    public TemplateHistory CreateReportTemplateHistory(string templateId = null, string companyId = "COMPANY_123")
    {
        return CreateTemplateHistory(
            companyId: companyId,
            templateId: templateId ?? Guid.NewGuid().ToString(),
            templateName: "Report Template",
            type: "REPORT",
            actionType: "CREATE",
            icon: "report-icon.png",
            properties: "{\"type\": \"report\", \"charts\": 3, \"tables\": 2}"
        );
    }

    public List<TemplateHistory> CreateStandardTemplateHistories(string companyId = "COMPANY_123")
    {
        return new List<TemplateHistory>
        {
            CreateWorkflowTemplateHistory(companyId: companyId),
            CreateFormTemplateHistory(companyId: companyId),
            CreateReportTemplateHistory(companyId: companyId)
        };
    }

    public TemplateHistory CreateTemplateHistoryWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateTemplateHistory(properties: propertiesJson);
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
