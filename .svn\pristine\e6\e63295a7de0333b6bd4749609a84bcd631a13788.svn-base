﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;


namespace ContinuityPatrol.Application.Contracts.Persistence;

    public interface IZertoVpgMonitorLogsRepository : IRepository<ZertoVpgMonitorLog>
    {
        Task<List<ZertoVpgMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
        Task<List<ZertoVpgMonitorLog>> GetDetailByType(string type);
    }

