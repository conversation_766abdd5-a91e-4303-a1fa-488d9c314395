﻿using ContinuityPatrol.Application.Features.SiteLocation.Events.Update;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteLocation.Events
{
    public class SiteLocationUpdatedEventTests
    {
        private readonly Mock<ILogger<SiteLocationUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly SiteLocationUpdatedEventHandler _handler;

        public SiteLocationUpdatedEventTests()
        {
            _mockLogger = new Mock<ILogger<SiteLocationUpdatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new SiteLocationUpdatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldLogInformation_WhenSiteLocationUpdated()
        {
            var updatedEvent = new SiteLocationUpdatedEvent { Name = "New York" };

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockLogger.Verify(
                logger => logger.LogInformation(It.Is<string>(s => s.Contains("New York"))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldAddUserActivity_WhenSiteLocationUpdated()
        {
            var updatedEvent = new SiteLocationUpdatedEvent { Name = "New York" };

            _mockUserService.Setup(s => s.UserId).Returns(Guid.NewGuid().ToString());
            _mockUserService.Setup(s => s.LoginName).Returns("testuser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://test.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(s => s.CompanyId).Returns("Company123");

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    ua.UserId == _mockUserService.Object.UserId &&
                    ua.LoginName == _mockUserService.Object.LoginName &&
                    ua.RequestUrl == _mockUserService.Object.RequestedUrl &&
                    ua.HostAddress == _mockUserService.Object.IpAddress &&
                    ua.CompanyId == _mockUserService.Object.CompanyId &&
                    ua.Action == $"{ActivityType.Update} SiteLocation" &&
                    ua.Entity == "SiteLocation" &&
                    ua.ActivityDetails == $"SiteLocation '{updatedEvent.Name}' updated successfully."
                )),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldGenerateNewUserId_WhenUserIdIsNullOrEmpty()
        {
            var updatedEvent = new SiteLocationUpdatedEvent { Name = "New York" };

            _mockUserService.Setup(s => s.UserId).Returns(string.Empty);
            _mockUserService.Setup(s => s.LoginName).Returns("testuser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://test.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(s => s.CompanyId).Returns("Company123");

            await _handler.Handle(updatedEvent, CancellationToken.None);

            _mockUserActivityRepository.Verify(
                repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                    !string.IsNullOrEmpty(ua.CreatedBy) && ua.CreatedBy != _mockUserService.Object.UserId
                )),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldNotThrowException_WhenUserServiceHasNullUserId()
        {
            var updatedEvent = new SiteLocationUpdatedEvent { Name = "New York" };

            _mockUserService.Setup(s => s.UserId).Returns((string)null);
            _mockUserService.Setup(s => s.LoginName).Returns("testuser");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("http://test.com");
            _mockUserService.Setup(s => s.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(s => s.CompanyId).Returns("Company123");

            await _handler.Handle(updatedEvent, CancellationToken.None);
        }
    }
}
