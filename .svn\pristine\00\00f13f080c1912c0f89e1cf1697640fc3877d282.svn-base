﻿let globalAllLoadBalancer = [];
let globalType;
let globalTypeCategory;
let page = 0;
let ip_port_data = ""
let data = {}
// URL
const loadBalancerURL = {
    nameExistUrl : "Admin/LoadBalancer/IsLoadBalancerNameExist",
    portandIpExistUrl: "Admin/LoadBalancer/IsLoadBalancerIpandPortExist",
    Pagination: "/Admin/LoadBalancer/GetPagination",
    nodeStatus: "Admin/LoadBalancer/UpdateNodeStatus",
    testConfiguration: "Admin/LoadBalancer/TestConfiguration",
    idDefault:"Admin/LoadBalancer/IsDefault"
}

const loadBalancerPermission = {
     createPermission : $("#adminCreate").data("create-permission").toLowerCase(),
     deletePermission : $("#adminDelete").data("delete-permission").toLowerCase()
}


if (loadBalancerPermission.createPermission == 'false') {
    $(".createBtn").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$('#DeleteModal').attr('data-bs-backdrop', 'static');

$(function () {
    btnCrudEnable('confirmDeleteButton')

    let selectedValues = [];

    let dataTable = $('#nodeConfig').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row mt-3 align-items-center"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            retrieve: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],
            "ajax": {
                "type": "GET",
                "url": loadBalancerURL.Pagination,
                "dataType": "json",
                "data": function (d) {

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "error": function (xhr, err) {
                    if (xhr.status === 401) {
                        window.location.assign('/Account/Logout');
                    }
                },
                "dataSrc": function (json) {

                    globalAllLoadBalancer = [];
                    const allLoadBalancer = json?.data && json?.data?.length && json.data.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer");
                    setTimeout(function () {
                        globalAllLoadBalancer = allLoadBalancer;
                    }, 100);
                    ip_port_data = json?.data
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    return json?.data;
                }
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },
                },
                {
                    "data": "name", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            //<i class="${row.isDefault ? "cp-single-dot text-primary fs-10 me-1" : "bg-transparent fs-10 me-3"}"></i>
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "typeCategory", "name": "Configuration Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (data) {
                            data = data.replace('LoadBalancer', 'Load Balancer');
                            data = data.replace('CPNode', 'CP Node');
                        }
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "ipAddress", "name": "IP Address", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "connectionType", "name": "Connection Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "type", "name": "Service Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        data = data?.replace(/([a-z])([A-Z])/g, '$1 $2');

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "hostName", "name": "Host Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "port", "name": "Port", "autoWidth": true,
                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return '<span title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },

                {
                    "data": "healthStatus", "name": "Health State", "autoWidth": true,
                    "render": function (data, type, row) {
                        let iconClass = '';

                        if (data?.toLowerCase() === 'active') {
                            iconClass = "cp-active-inactive text-success me-1";
                        } else if (data?.toLowerCase() === 'inactive') {
                            iconClass = "cp-active-inactive text-danger me-1";
                        } else if (data?.toLowerCase() === 'pending') {
                            iconClass = "cp-pending text-warning me-1";
                        } else {
                            iconClass = "cp-default-class";
                        }

                        return `<td><i title="${data || 'NA'}" class="${iconClass}"></i></td>`

                    }
                },

                {
                    "data": "isNodeStatus", "name": "Node State", "autoWidth": true,
                    "render": function (data, type, row) {

                        return `
                            <div class="me-3 d-flex align-items-center gap-2 form-switch">
                                <input type="checkbox" id="chk-activity-${row.id}"
                                        class="form-check-input custom-cursor-default-hover toggle-switch"
                                        data-id="${row.id}" data-name="${row.name}" ${row.isNodeStatus ? 'checked' : ''} title="${row.isNodeStatus ? 'Enable' : 'Disable'}">
                            </div>`;
                    }
                },

                {
                    "orderable": false,
                    "render": function (data, type, row) {
                        const isStatusActive = row.status === 'Active';
                        let nodeType = row.type.trim().toLowerCase();
                        let iconClass = nodeType === "workflowservice" ? "cp-Workflow-service" :
                            nodeType === "monitorservice" ? "cp-moitoring-service" : nodeType === "resiliencyreadyservice" ? "cp-resielncy-service" : "";
                        let isDefault = `<span role="button" style="${row.isDefault ? 'pointer-events: none; opacity: 0.5;' : ''}" title="${row.isDefault ? "Active" : "Set As Default"}" class="default-button" data-name='${row.name}' data-default='${row.isDefault}' data-id='${row.id}'>
                                             <i class="${row.typeCategory.toLowerCase() === 'cpnode' ? row.isDefault ? `${iconClass} text-success` : `${iconClass} text-secondary` : ''} "></i>
                                         </span> `;

                        if (loadBalancerPermission.createPermission == "true" && loadBalancerPermission.deletePermission == "true") {
                            return `
                                    <div class="d-flex align-items-center gap-2">
                                  
                                       <span role="button" title="Test Connection" id="btnTestConnection" data-test-connection='${JSON.stringify(row)}'>
                                            <i class="cp-test-connection"></i>
                                       </span>
                                        <span role="button" title="Edit" class="edit-button" data-loadbalancer='${JSON.stringify(row)}'>
                                            <i class="cp-edit"></i>
                                        </span>                                 
                                        <span role="button" title="Delete" ${isStatusActive ? 'class = "delete-button icon-disabled"' : 'class="delete-button" data-loadbalancer-id="' + row.id + '" data-loadbalancer-name="' + row.name + '" data-bs-toggle="modal" data-bs-target="#DeleteModal"'}>
                                            <i class="cp-Delete"></i>
                                        </span>
                                          ${isDefault}
                                          ${row?.exceptionMessage ?
                                    `<span title="Exception" role="button" id="load-exception-button" data-bs-toggle="modal" data-bs-target="#ErrorModal" data-loadException ='${row?.exceptionMessage || ''}'>
                                              <i class="cp-error-message"></i>                                    
                                     </span>` : ''}     

                                    </div>`;
                        }
                        else if (loadBalancerPermission.createPermission == "true" && loadBalancerPermission.deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                  
                           <span role="button" title="Test Connection" id="btnTestConnection" data-test-connection='${JSON.stringify(row)}'>
                                <i class="cp-test-connection"></i>
                           </span>
                            <span role="button" title="Edit" class="edit-button" data-loadbalancer='${JSON.stringify(row)}'>
                                <i class="cp-edit"></i>
                            </span>                                 
                            <span role="button" title="Delete" class="btn-disabled">
                                <i class="cp-Delete"></i>
                            </span>
                            ${isDefault}
                            ${row?.exceptionMessage ?
                                    `<span title="Exception" role="button" id="load-exception-button" data-bs-toggle="modal" data-bs-target="#ErrorModal" data-loadException ='${row?.exceptionMessage || ''}'>
                      <i class="cp-error-message"></i>                                    
                  </span>` : ''}     
                                
                        </div>`;
                        }
                        else if (loadBalancerPermission.createPermission == "false" && loadBalancerPermission.deletePermission == "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                  
                           <span role="button" title="Test Connection" class="btn-disabled"  data-test-connection='${JSON.stringify(row)}'>
                                <i class="cp-test-connection"></i>
                           </span>
                            <span role="button" title="Edit" class="btn-disabled">
                                <i class="cp-edit"></i>
                            </span>                                 
                            <span role="button" title="Delete" ${isStatusActive ? 'class = "delete-button icon-disabled"' : 'class="delete-button" data-loadbalancer-id="' + row.id + '" data-loadbalancer-name="' + row.name + '" data-bs-toggle="modal" data-bs-target="#DeleteModal"'}>
                                <i class="cp-Delete"></i>
                            </span>
                            ${isDefault}
                            ${row?.exceptionMessage ?
                                    `<span title="Exception" role="button" id="load-exception-button" data-bs-toggle="modal" data-bs-target="#ErrorModal" data-loadException ='${row?.exceptionMessage || ''}'>
                      <i class="cp-error-message"></i>                                    
                  </span>` : ''}     

                        </div>`;
                        }
                        else if (loadBalancerPermission.createPermission == "false" && loadBalancerPermission.deletePermission == "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">
                                  
                           <span role="button" title="Test Connection" class="btn-disabled"  data-test-connection='${JSON.stringify(row)}'>
                                <i class="cp-test-connection"></i>
                           </span>
                            <span role="button" title="Edit" class="btn-disabled">
                                <i class="cp-edit"></i>
                            </span>                                 
                            <span role="button" title="Delete" class="btn-disabled">
                                <i class="cp-Delete"></i>
                            </span>
                            ${isDefault}
                            ${row?.exceptionMessage ?
                                    `<span title="Exception" role="button" id="load-exception-button" data-bs-toggle="modal" data-bs-target="#ErrorModal" data-loadException ='${row?.exceptionMessage || ''}'>
                      <i class="cp-error-message"></i>                                    
                  </span>` : ''}     
                                
                        </div>`;
                        }

                    },
                }
            ],

            "columnDefs": [
                {
                    "targets": [2, 6],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },


        });
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    dataTable.on('order', function () {
        if (dataTable.page() !== page) {
            dataTable.page(page).draw('page');
        }
    });

    dataTable.on('page', function () {
        page = dataTable.page();
    });

    //search

    $('#search-inp').on('keydown input', commonDebounce(function (e) {

        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const configurationCheckbox = $("#configuration_type");
        const NameCheckbox = $("#Name");
        const ipaddressCheckbox = $("#ip_address");
        const connectiontypeCheckbox = $("#connection_type");

        const servicetypenCheckbox = $("#service_type");
        const hostnameCheckbox = $("#host_name");
        const portsCheckbox = $("#ports");
       

        const inputValue = $('#search-inp').val();
        if (configurationCheckbox.is(':checked')) {
            selectedValues.push(configurationCheckbox.val() + inputValue);
        }
        if (NameCheckbox.is(':checked')) {
            selectedValues.push(NameCheckbox.val() + inputValue);
        }
        if (ipaddressCheckbox.is(':checked')) {
            selectedValues.push(ipaddressCheckbox.val() + inputValue);
        }
        if (connectiontypeCheckbox.is(':checked')) {
            selectedValues.push(connectiontypeCheckbox.val() + inputValue);
        }
        if (hostnameCheckbox.is(':checked')) {
            selectedValues.push(hostnameCheckbox.val() + inputValue);
        }
        if (servicetypenCheckbox.is(':checked')) {
            selectedValues.push(servicetypenCheckbox.val() + inputValue);
        }
        if (portsCheckbox.is(':checked')) {
            selectedValues.push(portsCheckbox.val() + inputValue);
        }

        var currentPage = dataTable.page.info().page + 1;
        if (!isNaN(currentPage)) {
            dataTable.ajax.reload(function (json) {

                if (e.target.value && json?.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            }, false)
        }

    }, 500)); 


    //Update Node Status
   
    $(document).on("click", ".toggle-switch", function (event) {
        event.preventDefault();
        let nodeId = $(this).data("id");
        let nodeName = $(this).data('name')
        let isChecked = $(this).is(":checked");
        //let previousState = $(this).attr("data-previous-state") === "true";
        let statusValue = isChecked ? "true" : "false";
        $(this).val(statusValue);
        data = { id: nodeId, isNodeStatus: statusValue, name: nodeName }
        $("#statusData").text(isChecked ? "Enable" : "Disable");
        $("#statusPreviousData").text(!isChecked ? "Enable" : "Disable");
        $('#DuplicateActionsModal').modal('show');
        $("#duplicateConfirmation").data("checkbox", $(this));


    })
    $("#duplicateConfirmation").on('click', function () {
        let checkbox = $(this).data("checkbox");
        let isChecked = !checkbox.prop("checked");
        checkbox.prop("checked", isChecked);
        //checkbox.attr("data-previous-state", isChecked ? "true" : "false");
        $.ajax({
            type: "POST",
            url: RootUrl + loadBalancerURL.nodeStatus,
            data: data,

            traditional: true,
            headers: {
                'RequestVerificationToken': gettoken()
            },
            dataType: 'json',

            success: function (result) {
                console.log(result, 'node')
                let dataTable = $('#nodeConfig').DataTable();
                if (result?.data?.success) {
                    let currentPage = dataTable.page();
                    dataTable.ajax.reload(function () {
                        dataTable.page(currentPage).draw(false);
                    }, false);
                    notificationAlert("success", result?.data?.message)

                } else {
                    errorNotification(result);

                }
            }
        })
        $('#DuplicateActionsModal').modal('hide');
    })
   
    // TestConnection
    $("#nodeConfig").on("click", "#btnTestConnection", commonDebounce(async function (data) {

        const value = $(this).data("test-connection");
        const url = RootUrl + loadBalancerURL.testConfiguration;
        const nodeData = {};
        nodeData.nodeId = value?.id;

        $.ajax({
            type: "GET",
            url: url,
            data: nodeData,
            dataType: 'json',
            success: function (data) {

                if (data?.success) {
                    dataTable.ajax.reload();
                    notificationAlert("success", data?.data?.message);
                }
                else {
                    errorNotification(data)
                }
            }
        })
    }, 800));

    //Update
    $('#nodeConfig').on('click', '.edit-button', function () {
        const loadBalancerData = $(this).data('loadbalancer');
        $('#save').text('Update');
        btnCrudEnable('save');
        populateModalFields(loadBalancerData);
        $('#CreateModal').modal('show');
    });

    //delete
    $("#nodeConfig").on('click', '.delete-button', function () {
        const loadBalancerId = $(this).data('loadbalancer-id');
        const loadBalancerName = $(this).data('loadbalancer-name');
        $('#deleteData').text(loadBalancerName);
        $('#textDeleteId').val(loadBalancerId);
    });


    // Name
    $('#textName').on('keyup', commonDebounce(async function () {
        const loadBalancerId = $('#textLoadBalancerId').val();
        const value = $(this).val();
        if (value !== undefined) {
            $(this).val(value.replace(/  +/g, " "));
        }
        await validateName(value, loadBalancerId, loadBalancerURL.nameExistUrl);
    }, 500));

    $('#ipAddress,#port').on('input', commonDebounce(async function () {
        const ip_value = $("#ipAddress").val();
        const port_value = $("#port").val();
        await check_port(ip_value, port_value)
        if ($("#save").text() == "Update") {
            await validateportExists(port_value, ip_value, $('#textLoadBalancerId').val(), loadBalancerURL.portandIpExistUrl);
        }
    }, 500));

    //$(function () {

    //    const isChecked = $('#default').prop('checked');
    //    const value = isChecked ? true : false;
    //    $('#default').val(value);
    //    $(document).on('change', '.Checkboxs', function (e) {
    //        const isChecked = $(this).prop('checked');
    //        const value = isChecked ? true : false;
    //        $('#default').val(value);
    //    });
    //});

    //$(function () {

    //    const isChecked = $('#Disconection').prop('checked');
    //    const value = isChecked ? true : false;
    //    $('#Disconection').val(value);
    //    $(document).on('change', '.Checkboxs', function (e) {
    //        const isChecked = $(this).prop('checked');
    //        const value = isChecked ? true : false;
    //        $('#Disconection').val(value);
    //    });
    //});

    $(function () {
        // Function to handle checkbox changes
        function handleCheckboxChange(selector) {
            const isChecked = $(selector).prop('checked');
            const value = isChecked ? true : false;
            $(selector).val(value);
        }

        // Initial value setup for both checkboxes
        //handleCheckboxChange('#default');
        handleCheckboxChange('#Testconnection');

        // Event listener for checkbox change
        $(document).on('change', '.Checkboxs', function () {
            //handleCheckboxChange('#default');
            handleCheckboxChange('#Testconnection');
        });
    });


    //Create
    $("#save").on('click', commonDebounce(async function () {
        const form = $("#example-form");
        const name = $("#textName").val();
        const ipAddress = $("#ipAddress").val();
        const hostName = $("#hostName").val();
        const port = $("#port").val();
        const loadBalancerId = $("#textLoadBalancerId").val();
        const isName = await validateName(name, loadBalancerId, loadBalancerURL.nameExistUrl)
        const isIpAddress = await validateIpAddress(ipAddress)
        const isHostName = await validateHostName(hostName)
        const isPort = await validatePort(port)
        $("#nodeStatus").prop("checked", true);
        const checkportdata = await check_port(ipAddress, port)
        let portcheck = true
        if ($("#save").text() == "Update") {
            portcheck = await validateportExists($("#port").val(), $("#ipAddress").val(), $('#textLoadBalancerId').val(), loadBalancerURL.portandIpExistUrl);

        }

        $('#status').val('Pending')

        if (isName && isIpAddress && isHostName && isPort && (checkportdata == undefined ? true : checkportdata) && portcheck) {
            $(this).prop('disabled', true);

            form.trigger('submit');
            btnCrudDiasable('save')
        }
    }, 800));

    //Is Default
    $('#nodeConfig').on('click', '.default-button', commonDebounce(function () {
        const id = $(this).data('id');
        const isDefault = true; // $(this).data('default') 
        const name = $(this).data('name');
        $.ajax({
            type: "POST",
            url: RootUrl + loadBalancerURL.idDefault,
            dataType: "json",
            data: { "Id": id, "Name": name, "IsDefault": isDefault, __RequestVerificationToken: gettoken() },
            success: function (response) {
                if (response.success) {
                    notificationAlert("success", response?.data?.message);
                    dataTable.ajax.reload();
                } else {
                    errorNotification(response);
                }
            }
        });
    }));

    $("#all").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#Testconnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });

    $("#monitorService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#Testconnection").prop('checked', true);
            $("#defaultConnection").removeClass("d-none");
        }
    });

    $("#workflowService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#Testconnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });

    $("#bothService").on("change", function () {
        if ($(this).prop("checked") === true) {
            $("#Testconnection").prop('checked', false);
            $("#defaultConnection").addClass("d-none");
        }
    });
});

// load exception message

$('#nodeConfig').on('click', '#load-exception-button', function () {

    let getExceptionData = $(this).data('loadexception')

    if (getExceptionData) {
        $('#loadExceptionNA').addClass('d-none');
        $('#loadException').text(getExceptionData);
    } else {
        $('#loadException').text("")
        $('#loadExceptionNA').removeClass('d-none');
    }

});

$('#confirmDeleteButton').on('click', function () {     
    btnCrudDiasable('confirmDeleteButton');
})

$('#ipAddress').on('keypress keyup', async function (event) {
    const value = $(this).val();
    if (!/^[0-9.]+$/.test(event.key)) {
        event.preventDefault();
    }
    await validateIpAddress(value);
});


async function check_port(ip, port) {
    const portExists = ip_port_data?.some(x => x.ipAddress.trim() == ip && x.port == port);

    if ($("#save").text() == "Save" && port != "") {
        if (portExists) {
            setTimeout(() => {
                $("#Port-error")
                    .text("Port number already exists with IP address")
                    .addClass("field-validation-error");
            }, 100)
            return false;
        } else {
            $("#Port-error").text("").removeClass("field-validation-error");
            return true;
        }
    }

}
async function validateportExists(port, ip, id, url) {
    const errorElement = $("#Port-error");

    let url_name = RootUrl + url;
    let data = {};
    data.port = port;
    data.ipAddress = ip
    data.id = id
    const validationResults = [

        await IsPortExist(url_name, data, errorElement)

    ];
    return await CommonValidation(errorElement, validationResults);
}
async function IsPortExist(url, data, errorFunc) {
    return !data.port.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Port already exists" : true;
}
$('.pair').on('change', function () {
    typeCategoryBased();
})

$('#hostName').on('keyup', async function () {
    const value = $(this).val();
    if (value !== undefined) {
        $(this).val(value.replace(/  +/g, " "));
    }
    await validateHostName(value);
});

$('#port').on('keypress keyup', async function (event) {
    const value = $(this).val();
    if (!/[0-9]/.test(event.key)) {
        event.preventDefault();
    }
    await validatePort(value);
});


//Name
async function validateName(value, id = null, url) {
    const errorElement = $('#Name-error');
    if (!value) {
        errorElement.text('Enter name')
            .addClass('field-validation-error');
        return false;
    }
    if (value.includes("<")) {
        errorElement.text('Special characters not allowed')
            .addClass('field-validation-error');
        return false;
    }
    let url_name = RootUrl + url;
    let data = {};
    data.loadBalancerName = value;
    data.id = id;
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await ShouldNotBeginWithNumber(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url_name, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
};

//IPAddress Validation
async function validateIpAddress(value) {
    const errorElement = $('#IPAddress-error');
    if (!value) {
        errorElement.text('Enter IP address')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [await IpaddressReg(value)];
    return await CommonValidation(errorElement, validationResults);
};

//Host Name
async function validateHostName(value) {
    const errorElement = $('#HostName-error');
    if (!value) {
        errorElement.text('Enter host name')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        await HostNameReg(value),
        await ShouldNotBeginWithUnderScore(value),
        await HostNameBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await ShouldNotBeginWithNumber(value),
        await minMaxlength(value, 30),
        await secondChar(value)
    ];

    return await CommonValidation(errorElement, validationResults);
};

const HostNameBeginWithSpace = (value) => {
    const regex = /^(?!\.)(?![\s-])[\w\s.-]*\w[\w\s.-]*(?<!\.)$/;
    return regex.test(value) ? true : "Should not begin with space";
}


//Port
async function validatePort(value) {

    const errorElement = $('#Port-error');
    if (!value) {
        errorElement.text('Enter port')
            .addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await OnlyNum(value),
        await PortReg(value)

    ];

    return await CommonValidation(errorElement, validationResults);
};

//edit
function populateModalFields(loadBalancerData) {
    $('#textName').val(loadBalancerData?.name);
    $('#ipAddress').val(loadBalancerData?.ipAddress);
    let typeCategory = loadBalancerData?.typeCategory;
    globalTypeCategory = typeCategory;
    $('input[name="typecategory"][value="' + typeCategory + '"]').prop("checked", true);
    $('#prIcon, #drIcon').prop('disabled', false)
    //$("#default").prop("checked", loadBalancerData.isDefault);
    $("#Testconnection").prop("checked", loadBalancerData.isConnection);

    if (typeCategory?.toLowerCase() != "loadbalancer") {
        $('#typeAll').hide();
        $('#monitorService, #workflowService, #bothService').prop('disabled', false)
        //$('#defaultConnection').show()
        toggleDisableEnable($('#prIcon'), true);
        $('#node-http').removeClass('d-none');
        $('input[name="type"][value="All"]').prop('checked', false)
    }
    else {
        $('#typeAll').show();
        //$('#defaultConnection').hide()
        toggleDisableEnable($('#drIcon'), true);
        $('#node-http').addClass('d-none');
        $('input[name="type"][value="All"]').prop('checked', true);
    }

    let connection = loadBalancerData?.connectionType;
    $('input[name="connectionType"][value="' + connection + '"]').prop("checked", true);

    let serviceType = loadBalancerData?.type;
    globalType = serviceType;

    $('input[name="type"][value="' + serviceType + '"]').prop("checked", true);
    const serviceMap = {
        monitorservice: "#monitorService",
        workflowservice: "#workflowService",
        resiliencyreadyservice: "#bothService",
        all: "#all"
    };

    const selector = serviceMap[serviceType.toLowerCase()];
    if (selector) {
        $(selector).trigger("change");
    }
    serviceTypeToggle();
    $('#hostName').val(loadBalancerData?.hostName);
    $('#port').val(loadBalancerData?.port);
    $('#textLoadBalancerId').val(loadBalancerData?.id);
    $('#status').val(loadBalancerData?.status);

    const errorElement = ['#Name-error', '#IPAddress-error', '#HostName-error', '#Port-error'];
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
}
//ServiceType based show 
//$('input[name="type"]').on('change', function () {
//    if ($(this).val() === "MonitorService") {
//        $('#testDiv').show();
//    } else {
//        $('#testDiv').hide();
//    }
//});

//Clear data
$('#create').on('click', function () {
    //$("#default").prop('checked', true);
    $("#Testconnection").prop('checked', true);
    $("#defaultConnection").removeClass("d-none");
    const errorElements = ['#Name-error', '#IPAddress-error', '#HostName-error', '#Port-error'];
    clearInputFields('example-form', errorElements);
    $('#textLoadBalancerId, #textName, #ipAddress, #hostName, #status').val('');
    $('#monitorService, #workflowService, #bothService').prop('disabled', false)
    btnCrudEnable('save');
    $('#save').text('Save');

    const allItems = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type === "All");

    if (allItems?.length > 0) {        
        toggleDisableEnable($('#prIcon'), true);
        $('#typeAll').hide();
        $('#prIcon').prop('checked', false);
        $('#drIcon').prop('checked', true);
        $('#node-http').removeClass('d-none')
        $('input[name="connectionType"][value="http"]').prop("checked", true);
        $('input[name="type"][value="MonitorService"]').prop("checked", true).prop('disabled', false);
        $('#workflowService, #bothService').prop('disabled', false)
    } else {
        $("#defaultConnection").addClass("d-none");
        $("#Testconnection").prop('checked', false);
        $("#default").prop('checked', false);
        const allLoadBalancerTypes = ["MonitorService", "WorkflowService", "ResiliencyReadyService"];
        let isAllTypesPresent = allLoadBalancerTypes.some(type => {
            return globalAllLoadBalancer.length && globalAllLoadBalancer.some(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item.type === type);
        });

        if (isAllTypesPresent) {
            toggleDisableEnable($('#prIcon'), true);
            $('#prIcon').prop('checked', false);
            $('#drIcon, #monitorService').prop('checked', true).prop('disabled', false);
            $('#node-http').removeClass('d-none')
            $('#typeAll').hide();

        } else {
            toggleDisableEnable($('#prIcon'), false);
            $('#prIcon').prop('checked', true);
            $('#drIcon').prop('checked', false);
            $('#node-http').addClass('d-none')
            $('input[name="connectionType"][value="https"]').prop("checked", true);
            $('#typeAll').show();

        }
        const moniterType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "monitorservice");
        const workflowType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "workflowservice");
        const drReadyType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "resiliencyreadyservice");
        serviceTypeToggle(moniterType, workflowType, drReadyType, isAllTypesPresent);
        if (moniterType?.length === 0 && workflowType?.length === 0 && drReadyType?.length === 0) {
            toggleDisableEnable($('#prIcon'), false);
            $('#typeAll').show();
            $('#prIcon').prop('checked', true);
            $('#drIcon').prop('checked', false);
        }
    }
    $("#save").prop('disabled', false);
    $('#port').val('');
    $('#CreateModal').modal('show')
});


async function IsNameExist(url, data, errorFunc) {
    return !data.loadBalancerName.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

function toggleDisableEnable(element, condition) {
    element.prop('disabled', condition);
}
function typeCategoryBased() {

    const values = $('input[name="typecategory"]:checked').val();
    const moniterType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item.type?.replace(/\s+/g, '') === "MonitorService");
    const workflowType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item.type?.replace(/\s+/g, '') === "WorkflowService");
    const drReadyType = globalAllLoadBalancer.length && globalAllLoadBalancer.filter(item => iitem?.typeCategory?.toLowerCase() === "loadbalancer" && item.type?.replace(/\s+/g, '') === "ResiliencyReadyService");
    const isMode = $('#save').text();
    if (values === "Load Balancer") {

        $('#node-http').addClass('d-none');
        $('input[name="connectionType"][value="https"]').prop("checked", true);

        if (isMode === 'Save') {
            $('#prIcon').prop('checked');
            $('input[name="type"][value="MonitorService"]').prop("checked", false);
            $('#typeAll').show();
            toggleDisableEnable($('#all'), false);
            serviceTypeToggle(moniterType, workflowType, drReadyType);
        } else {
            $('#prIcon').prop('checked');
            $('input[name="type"][value="MonitorService"]').prop("checked", false);
            $('#typeAll').show();
            toggleDisableEnable($('#all'), false);

            if (moniterType?.length > 0) {
                moniterTypeToggle();

            }

            if (workflowType?.length > 0) {
                workflowTypeToggle();

            }

            if (drReadyType?.length > 0) {
                drReadyTypeToggle();

            }

            if (globalType?.toLowerCase() === 'all') {
                $('#monitorService, #workflowService, #bothService').prop('disabled', true)
            }

            if (globalType === 'MonitorService') {
                moniterTypeToggle();
                toggleDisableEnable($('#monitorService'), false);
            }
            if (globalType === 'WorkflowService') {
                workflowTypeToggle();
                toggleDisableEnable($('#workflowService'), false);
            }
            if (globalType === 'ResiliencyReadyService') {
                drReadyTypeToggle();
                toggleDisableEnable($('#bothService'), false);
            }
            $('input[name="type"][value="' + globalType + '"]').prop("checked", true);
        }
    }
    else {
        $('#drIcon').prop('checked', true);
        $('#typeAll').hide();
        $('input[name="type"][value="All"]').prop("checked", false);
        $('#node-http').removeClass('d-none');
        $('input[name="connectionType"][value="http"]').prop("checked", true);
        $('input[name="type"][value="MonitorService"]').prop("checked", true);
        toggleDisableEnable($('#prIcon'), false);
        toggleDisableEnable($('#monitorService'), false);
        toggleDisableEnable($('#workflowService'), false);
        toggleDisableEnable($('#bothService'), false);

    }
}
function serviceTypeToggle(moniterType, workflowType, drReadyType, isAllTypesPresent = false) {

    let isMode = $('#save').text();
    if (isMode === 'Save') {
        if (moniterType?.length > 0 && !isAllTypesPresent) {
            moniterTypeToggle();

        }

        if (workflowType?.length > 0 && !isAllTypesPresent) {
            workflowTypeToggle();

        }

        if (drReadyType?.length > 0 && !isAllTypesPresent) {
            drReadyTypeToggle();

        }
        let enabledTypeCheckboxes = findEnabledTypeCheckboxes();

        if (!isAllTypesPresent) $('input[name="type"][value="' + enabledTypeCheckboxes[0] + '"]').prop("checked", true)
    }
    if (isMode === 'Update') {

        $('input[name="type"][value="' + globalType + '"]').prop("checked", true);
        //console.log(globalType,'globalType')
        if (globalTypeCategory?.toLowerCase() === 'cpnode') {
            const allItems = globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item.type === "All");
            if (allItems?.length > 0) {
                toggleDisableEnable($('#prIcon'), true);
                $('#typeAll').hide();
                //globalType === "MonitorService" ? $('#testDiv').show() : $('#testDiv').hide()    
            }
        } else {
            const moniterType = globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "monitorservice");
            const workflowType = globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "workflowservice");
            const drReadyType = globalAllLoadBalancer.filter(item => item?.typeCategory?.toLowerCase() === "loadbalancer" && item?.type?.toLowerCase() === "resiliencyreadyservice");
            if (moniterType?.length > 0) {
                moniterTypeToggle();

            }

            if (workflowType?.length > 0) {
                workflowTypeToggle();

            }

            if (drReadyType?.length > 0) {
                drReadyTypeToggle();

            }
            if (globalType === 'MonitorService') {
                moniterTypeToggle();
                toggleDisableEnable($('#monitorService'), false);
            }
            if (globalType === 'WorkflowService') {
                workflowTypeToggle();
                toggleDisableEnable($('#workflowService'), false);
            }
            if (globalType === 'ResiliencyReadyService') {
                drReadyTypeToggle();
                toggleDisableEnable($('#bothService'), false);
            }
            if (globalType === 'All') {
                toggleDisableEnable($('#prIcon'), false);
                toggleDisableEnable($('#all'), false);
                toggleDisableEnable($('#monitorService'), true);
                toggleDisableEnable($('#workflowService'), true);
                toggleDisableEnable($('#bothService'), true);
            }

        }

    }
}
function moniterTypeToggle() {
    $('#typeAll').show();
    toggleDisableEnable($('#prIcon'), false);
    toggleDisableEnable($('#all'), true);
    toggleDisableEnable($('#monitorService'), true);
    $('input[name="type"][value="All"]').prop("checked", false)
    $('#prIcon').prop('checked', true);
    $('#drIcon').prop('checked', false);
}
function workflowTypeToggle() {
    $('#typeAll').show();
    toggleDisableEnable($('#prIcon'), false);
    toggleDisableEnable($('#all'), true);
    toggleDisableEnable($('#workflowService'), true);
    $('input[name="type"][value="All"]').prop("checked", false)
    $('#prIcon').prop('checked', true);
    $('#drIcon').prop('checked', false);
}
function drReadyTypeToggle() {
    $('#typeAll').show();
    toggleDisableEnable($('#prIcon'), false);
    toggleDisableEnable($('#all'), true);
    toggleDisableEnable($('#bothService'), true);
    $('input[name="type"][value="All"]').prop("checked", false)
    $('#prIcon').prop('checked', true);
    $('#drIcon').prop('checked', false);
}
function findEnabledTypeCheckboxes() {
    let enabledCheckboxes = [];
    $('input[name="type"]').each(function () {

        if (!$(this).prop('disabled')) {
            enabledCheckboxes.push($(this).val());
        }
    });
    return enabledCheckboxes;
}
