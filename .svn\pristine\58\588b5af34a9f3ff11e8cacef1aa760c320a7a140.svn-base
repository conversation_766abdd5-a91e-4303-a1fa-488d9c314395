﻿using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorStatus.Queries
{
    public class GetPostgresMonitorStatusPaginatedListQueryHandlerTests
    {
        private readonly Mock<IPostgresMonitorStatusRepository> _mockPostgresMonitorStatusRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetPostgresMonitorStatusPaginatedListQueryHanlder _handler;

        public GetPostgresMonitorStatusPaginatedListQueryHandlerTests()
        {
            _mockPostgresMonitorStatusRepository = new Mock<IPostgresMonitorStatusRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetPostgresMonitorStatusPaginatedListQueryHanlder(_mockPostgresMonitorStatusRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPaginatedResult_WhenDataExists()
        {
            var query = new GetPostgresMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = null
            };

            var monitorStatuses = new List<Domain.Entities.PostgresMonitorStatus>
            {
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id1", Type = "Type1" },
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id2", Type = "Type2" },
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id3", Type = "Type3" }
            };

            var mappedResults = new List<PostgresMonitorStatusListVm>
            {
                new PostgresMonitorStatusListVm { Id = "id1", Type = "Type1" },
                new PostgresMonitorStatusListVm { Id = "id2", Type = "Type2" }
            };

            var paginatedResult = new PaginatedResult<PostgresMonitorStatusListVm>(mappedResults);

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(monitorStatuses.AsQueryable());

            _mockMapper.Setup(m => m.Map<PostgresMonitorStatusListVm>(It.IsAny<Domain.Entities.PostgresMonitorStatus>()))
                .Returns((Domain.Entities.PostgresMonitorStatus status) => new PostgresMonitorStatusListVm
                {
                    Id = status.ReferenceId,
                    Type = status.Type
                });
            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal(3, result.TotalCount);

            _mockPostgresMonitorStatusRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorStatusListVm>(It.IsAny<Domain.Entities.PostgresMonitorStatus>()), Times.Exactly(2));
        }

        [Fact]
        public async Task Handle_ShouldReturnFilteredPaginatedResult_WhenSearchStringProvided()
        {
            var query = new GetPostgresMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "Type1"
            };

            var monitorStatuses = new List<Domain.Entities.PostgresMonitorStatus>
            {
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id1", Type = "Type1" },
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id2", Type = "Type2" },
                new Domain.Entities.PostgresMonitorStatus { ReferenceId = "id3", Type = "Type3" }
            };

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(monitorStatuses.AsQueryable());

            _mockMapper.Setup(m => m.Map<PostgresMonitorStatusListVm>(It.IsAny<Domain.Entities.PostgresMonitorStatus>()))
                .Returns((Domain.Entities.PostgresMonitorStatus status) => new PostgresMonitorStatusListVm
                {
                    Id = status.ReferenceId,
                    Type = status.Type
                });
            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Single(result.Data);
            Assert.Equal("Type1", result.Data.First().Type);

            _mockPostgresMonitorStatusRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorStatusListVm>(It.IsAny<Domain.Entities.PostgresMonitorStatus>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyPaginatedResult_WhenNoDataMatchesSearch()
        {
            var query = new GetPostgresMonitorStatusPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 2,
                SearchString = "NonExistent"
            };

            var monitorStatuses = new List<Domain.Entities.PostgresMonitorStatus>();

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetPaginatedQuery())
                .Returns(monitorStatuses.AsQueryable());

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result.Data);
            Assert.Equal(0, result.TotalCount);

            _mockPostgresMonitorStatusRepository.Verify(r => r.GetPaginatedQuery(), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorStatusListVm>(It.IsAny<Domain.Entities.PostgresMonitorStatus>()), Times.Never);
        }
    }
}
