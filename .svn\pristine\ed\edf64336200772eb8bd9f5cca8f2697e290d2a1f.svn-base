﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = "PowerMaxSRDF";
let infraObjectId = sessionStorage.getItem("infraobjectId");

setTimeout(() => {powerMaxSRDFmonitorstatus(mId, monitortype);}, 500);

setTimeout(() => { monitoringSolution(infraObjectId);}, 250);

$("#backtoITview").on("click", function () {
    window.location.assign("/Dashboard/ITResiliencyView/List");
});

async function powerMaxSRDFmonitorstatus(id, type) {
    const url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    const data = { monitorId: id, type };

    const monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css("text-align", "center").html(noDataImage);
    }
}

function infraData(value) {
    $("#infraName").text(checkAndReplace(value?.infraObjectName))
    .attr("title", checkAndReplace(value?.infraObjectName));

    $("#modifiedTime").text(checkAndReplace(value?.rpoGeneratedDate))
        .attr("title", checkAndReplace(value?.rpoGeneratedDate));
}

const noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">';

function checkAndReplace(value) {
    return value == null || value === "" ? "NA" : value;
}
function propertiesData(value) {
    if (!value) {
        $("#noDataimg").css("text-align", "center").html(noDataImage);
        return;
    }

    const data = JSON.parse(value?.properties);
    console.log(data,'props')
    const siteData = data?.PowerMaxSrdfReplicationMonitoring;

    $("#SiteName").text(siteData?.SiteName);
    $("#PowermaxIPPort").text(siteData?.PowermaxIPPort);
    $("#SourceArray").text(siteData?.SourceArray);
    $("#StorageGroupName").text(siteData?.StorageGroupName);
    $("#ProtectedvCenterSite").text(siteData?.ProtectedvCenterSite);
    $("#ProtectedDatastores").text(siteData?.ProtectedDatastores);
    $("#TotalProtectedVMCount").text(siteData?.TotalProtectedVMCount);
    $("#RDFTypeAndState").text(siteData?.RDFTypeAndState);

    const replicationData = siteData?.PowerMaxSrdfSiteReplicationMonitoring;

    siteReplicationDetails(replicationData);
    storageRDFVolume(replicationData);
    protectedVMDetails(replicationData);
    storageGroupDetails(replicationData);
}

function renderTable(selector, headers, rowMap) {    

    const theadHtml = `<tr>${headers?.map((header, index) => index === 0 ? `<th>${header}</th>` : `<th style='font-weight:normal'>${header}</th>`).join('')}</tr>`;

    let tbodyHtml = '';
    Object.entries(rowMap)?.forEach(([label, values]) => {
        tbodyHtml += `<tr><td><strong>${label}</strong></td>${values?.map(v => `<td>${v}</td>`).join('')}</tr>`;
    });
    $(`${selector} thead`).html(theadHtml);
    $(`${selector} tbody`).html(tbodyHtml);
}

function siteReplicationDetails(data) {
    const siteNames = [];
    const rows = {
        "Dell PowerMax - IP Address, Port No.": [],
        "Dell PowerMax - Build (version)": [],
        "Dell PowerMax - Array ID": [],
        "Dell PowerMax - Storage Group Name": []
    };

    data?.forEach(site => {
        site?.SiteBaseReplication?.forEach(rep => {
            const m = rep?.ReplicationStorageArrayMonitoring || {};
            siteNames.push(m?.SiteName || "");
            rows["Dell PowerMax - IP Address, Port No."].push(m?.PowerMaxIPAddressPort || "");
            rows["Dell PowerMax - Build (version)"].push(m?.PowerMaxBuildVersion || "");
            rows["Dell PowerMax - Array ID"].push(`((${m?.PowerMaxArrayId || ""}))`);
            rows["Dell PowerMax - Storage Group Name"].push(m?.PowerMaxStorageGroupName || "");
        });
    });

    renderTable("#tblReplication", ["Site Name", ...siteNames], rows);
}
function storageGroupDetails(data) {
    const status = [];
    const rows = {
        "SRDF Group Pair Mode": [],
        "SRDF Group Pair Status": [],
        "Consistency Protection": [],
        "SRDF Group Number": [],
        "SRDF Type": [],
        "Replication Direction": [],
        "Datalag(Sec)": [],
        "Masking View Name": []
    };

    data?.forEach(site => {
        site?.SiteBaseReplication?.forEach(rep => {
            const group = rep?.ReplicationStorageGroupRDFGroupMonitoring || {};
            status.push(group?.SRDFGroupProtectedStatus || "");
            let iconHtml = '';
            if (group?.ReplicationDirection) {
                iconHtml = "<i class='text-success cp-right-linearrow me-1 fs-6' title='Replication Direction'></i>";
            }
            rows["SRDF Group Pair Mode"].push(group?.SRDFGroupPairMode);
            rows["SRDF Group Pair Status"].push(group?.SRDFType);
            rows["Consistency Protection"].push(group?.ConsistencyProtection);
            rows["SRDF Group Number"].push(group?.StorageGroupRDFGroupNumber);
            rows["SRDF Type"].push(group?.SRDFType);
            rows["Replication Direction"].push(iconHtml);
            rows["Datalag(Sec)"].push(group?.DataLag);
            rows["Masking View Name"].push(group?.MaskingViewName);
        });
    });

    renderTable("#tblRDFGroup", ["SRDF Group Protected Status", ...status], rows);
}
function storageRDFVolume(data) {
    const headers = ["Storage Group-Volume Name (RDF Type)"];
    const rowMap = { "Storage Group-Volume Name (Volume State)": [] };

    data?.forEach(site => {
        site?.SiteBaseReplication?.forEach(rep => {
            const m = rep?.ReplicationStorageGroupRDFVolumeMonitoring || {};
            const rdfTypes = m?.StorageGroupVolumeNameRDFType || [];
            const volStates = m?.StorageGroupVolumeNameVolumeState || [];
            headers.push(rdfTypes.join('<br>'));
            rowMap["Storage Group-Volume Name (Volume State)"].push(volStates.join('<br>'));
        });
    });

    renderTable("#tblRDFVolume", headers, rowMap);
}
function protectedVMDetails(data) {
    const siteIPs = [];
    const rows = {
        "Protected Datastore LUN Attached Status": [],
        "Protected Datastore Name": [],
        "Protected VM Count": [],
        "Total Protected VM Count": []
    };

    data?.forEach(site => {
        site?.SiteBaseReplication?.forEach(rep => {
            const vm = rep?.ProtectedVMsMonitoring || {};
            siteIPs.push(vm?.vCenterIPAddress || "");
            rows["Protected Datastore LUN Attached Status"].push(vm.ProtectedDatastoreLUNAttachedStatus || "");
            rows["Protected Datastore Name"].push(vm.ProtectedDatastoreName || "");
            rows["Protected VM Count"].push(vm.ProtectedVMCount || "");
            rows["Total Protected VM Count"].push(vm.TotalProtectedVMCount || "");
        });
    });

    renderTable("#tblProtectedVM", ["vCenter IP Address", ...siteIPs], rows);
}