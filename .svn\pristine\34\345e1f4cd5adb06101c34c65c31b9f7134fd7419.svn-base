﻿using ContinuityPatrol.Application.Features.TeamResource.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Commands;

public class DeleteTeamResourceTests : IClassFixture<TeamResourceFixture>
{
    private readonly TeamResourceFixture _teamResourceFixture;

    private readonly Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    private readonly DeleteTeamResourceCommandHandler _handler;

    public DeleteTeamResourceTests(TeamResourceFixture teamResourceFixture)
    {
        _teamResourceFixture = teamResourceFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockTeamResourceRepository = TeamResourceRepositoryMocks.DeleteTeamResourceRepository(_teamResourceFixture.TeamResources);

        _handler = new DeleteTeamResourceCommandHandler(_mockTeamResourceRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Return_Success_When_Delete_TeamResource()
    {
        var result = await _handler.Handle(new DeleteTeamResourceCommand { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteTeamResourceResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_Delete_TeamResource()
    {
        var result = await _handler.Handle(new DeleteTeamResourceCommand { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_IsActive_False_When_GetByReferenceIdAsyncMethod_DeleteTeamResource()
    {
        await _handler.Handle(new DeleteTeamResourceCommand { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        var teamResource = await _mockTeamResourceRepository.Object.GetByReferenceIdAsync(_teamResourceFixture.TeamResources[0].ReferenceId);

        teamResource.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidTeamResourceId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteTeamResourceCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteTeamResourceCommand { Id = _teamResourceFixture.TeamResources[0].ReferenceId }, CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockTeamResourceRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.TeamResource>()), Times.Once);
    }
}
