using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormHistoryFixture : IDisposable
{
    public List<FormHistory> FormHistoryPaginationList { get; set; }
    public List<FormHistory> FormHistoryList { get; set; }
    public FormHistory FormHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public FormHistoryFixture()
    {
        var fixture = new Fixture();

        FormHistoryList = fixture.Create<List<FormHistory>>();

        FormHistoryPaginationList = fixture.CreateMany<FormHistory>(20).ToList();

        FormHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);
        FormHistoryPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormHistoryPaginationList.ForEach(x => x.IsActive = true);

        FormHistoryList.ForEach(x => x.CompanyId = CompanyId);
        FormHistoryList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormHistoryList.ForEach(x => x.IsActive = true);

        FormHistoryDto = fixture.Create<FormHistory>();
        FormHistoryDto.CompanyId = CompanyId;
        FormHistoryDto.ReferenceId = Guid.NewGuid().ToString();
        FormHistoryDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
