﻿using ContinuityPatrol.Application.Features.LicenseManager.Events.UpdateState;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;

public class UpdateLicenseStateCommandHandler : IRequestHandler<UpdateLicenseStateCommand, UpdateLicenseStateResponse>
{
    private readonly IInfraObjectInfoRepository _infraObjectInfoRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IPublisher _publisher;


    public UpdateLicenseStateCommandHandler(ILicenseManagerRepository licenseManagerRepository, IPublisher publisher,
        ILoggedInUserService loggedInUserService,
        IInfraObjectRepository infraObjectRepository, IInfraObjectInfoRepository infraObjectInfoRepository,
        ILicenseInfoRepository licenseInfoRepository)
    {
        _licenseManagerRepository = licenseManagerRepository;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
        _infraObjectRepository = infraObjectRepository;
        _infraObjectInfoRepository = infraObjectInfoRepository;
        _licenseInfoRepository = licenseInfoRepository;
    }

    public async Task<UpdateLicenseStateResponse> Handle(UpdateLicenseStateCommand request,
        CancellationToken cancellationToken)
    {
        if (!_loggedInUserService.IsSiteAdmin) throw new InvalidException("Access denied!.");

        var eventToUpdate = await _licenseManagerRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToUpdate, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), request.Id));

        eventToUpdate.IsState = request.IsState;

        await _licenseManagerRepository.UpdateAsync(eventToUpdate);

        var status = eventToUpdate.IsState ? "Active" : "InActive";

        if (status.Equals("InActive"))
            await UpdateInfraObjectStateToLocked(eventToUpdate.ReferenceId);
        else
            await UpdateInfraObjectState(eventToUpdate.ReferenceId);

        var response = new UpdateLicenseStateResponse
        {
            Message = $"License {SecurityHelper.Decrypt(eventToUpdate.PoNumber)} '{status}' successfully",
            Id = request.Id
        };

        await _publisher.Publish(
            new UpdateLicenseStateEvent { PONumber = eventToUpdate.PoNumber, IsState = eventToUpdate.IsState },
            cancellationToken);

        return response;
    }

    private async Task UpdateInfraObjectStateToLocked(string licenseId)
    {
        var result = new List<Domain.Entities.InfraObject>();

        var licenseInfoList = await _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(licenseId);

        var infraObjectList = await _infraObjectRepository.ListAllAsync();

        foreach (var licenseInfo in licenseInfoList)
        {
            var attachedInfraList = infraObjectList.Where(x =>
                (x.ServerProperties.IsNotNullOrWhiteSpace() && x.ServerProperties.Contains(licenseInfo.EntityId)) ||
                (x.ReplicationProperties.IsNotNullOrWhiteSpace() &&
                 x.ReplicationProperties.Contains(licenseInfo.EntityId))
                || (x.DatabaseProperties.IsNotNullOrWhiteSpace() &&
                    x.DatabaseProperties.Contains(licenseInfo.EntityId))).ToList();


            result.AddRange(attachedInfraList);
        }

        var attachedInfraObjectlist = result.DistinctBy(x => x.ReferenceId).ToList();
        foreach (var infraObject in attachedInfraObjectlist)
        {
            var infraObjectInfoDtl =
                await _infraObjectInfoRepository.GetInfraObjectInfoByInfraObjectId(infraObject.ReferenceId);

            if (infraObjectInfoDtl is null)
            {
                var inraInfo = new Domain.Entities.InfraObjectInfo
                {
                    InfraObjectId = infraObject.ReferenceId,
                    InfraObjectName = infraObject.Name,
                    PreviousState = infraObject.State,
                    CurrentState = "Deactivate"
                };

                await _infraObjectInfoRepository.AddAsync(inraInfo);
            }
            else if (infraObjectInfoDtl is not null)
            {
                infraObjectInfoDtl.PreviousState = infraObject.State;
                infraObjectInfoDtl.CurrentState = "Deactivate";

                await _infraObjectInfoRepository.UpdateAsync(infraObjectInfoDtl);
            }

            infraObject.State = "Deactivate";

            await _infraObjectRepository.UpdateAsync(infraObject);
        }
    }

    private async Task UpdateInfraObjectState(string licenseId)
    {
        var licenseInfos = await _licenseInfoRepository.GetLicenseInfoDetailByLicenseId(licenseId);

        var groupByEntity = licenseInfos.GroupBy(x => x.Entity)
            .Select(x => new
            {
                x.Key,
                LicenseInfos = x.ToList()
            }).ToList();


        var infraObjectList = new List<Domain.Entities.InfraObject>();

        foreach (var licenseInfo in groupByEntity)
        foreach (var server in licenseInfo.LicenseInfos)
        {
            var infraObjects = licenseInfo.Key.Trim().ToLower() switch
            {
                var key when key.Contains("server") => await _infraObjectRepository.GetInfraObjectByServerId(
                    server.EntityId),
                var key when key.Contains("database") => await _infraObjectRepository.GetInfraObjectByDatabaseId(
                    server.EntityId),
                var key when key.Contains("replication") => await _infraObjectRepository.GetInfraObjectByReplicationId(
                    server.EntityId),
                _ => Enumerable.Empty<Domain.Entities.InfraObject>()
            };

            infraObjectList.AddRange(infraObjects);
        }

        foreach (var infraObject in infraObjectList.DistinctBy(x => x.ReferenceId))
        {
            var infraObjectInfo =
                await _infraObjectInfoRepository.GetInfraObjectInfoByInfraObjectId(infraObject.ReferenceId);

            var eventToUpdate = await _infraObjectRepository.GetByReferenceIdAsync(infraObject.ReferenceId);

            eventToUpdate.State = infraObjectInfo?.PreviousState ?? "Active";
            await _infraObjectRepository.UpdateAsync(eventToUpdate);
        }
    }
}