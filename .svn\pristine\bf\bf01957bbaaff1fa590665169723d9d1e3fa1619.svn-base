using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Alert.Commands.Create;
using ContinuityPatrol.Application.Features.Alert.Commands.Delete;
using ContinuityPatrol.Application.Features.Alert.Commands.Update;
using ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListByStartOfWeek;
using ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListFilterByDate;
using ContinuityPatrol.Application.Features.Alert.Queries.GetClientAlertId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Alert.Queries.GetInfraObjectId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetLastAlertId;
using ContinuityPatrol.Application.Features.Alert.Queries.GetList;
using ContinuityPatrol.Application.Features.Alert.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class AlertsControllerTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly AlertsController _controller;

    public AlertsControllerTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;

        var testBuilder = new ControllerTestBuilder<AlertsController>();
        _controller = testBuilder.CreateController(
            _ => new AlertsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAlerts_ReturnsExpectedList()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_alertFixture.AlertListVm);

        // Act
        var result = await _controller.GetAlerts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertListVm>>(okResult.Value);
        Assert.Equal(3, alerts.Count);
    }

    [Fact]
    public async Task GetAlerts_ReturnsEmptyList_WhenNoAlertsExist()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertCacheKey + companyId);

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertListQuery>(), default))
            .ReturnsAsync(new List<AlertListVm>());

        // Act
        var result = await _controller.GetAlerts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertListVm>>(okResult.Value);
        Assert.Empty(alerts);
    }

    [Fact]
    public async Task GetAlertById_ReturnsAlert_WhenIdIsValid()
    {
        // Arrange
        var alertId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertDetailQuery>(q => q.Id == alertId), default))
            .ReturnsAsync(_alertFixture.AlertListVm);

        // Act
        var result = await _controller.GetAlertById(alertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertDetail = Assert.IsType<List<AlertListVm>>(okResult.Value);
        Assert.NotNull(alertDetail);
    }

    [Fact]
    public async Task GetAlertById_Throws_WhenIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertById("invalid-guid"));
    }

    [Fact]
    public async Task CreateAlert_Returns201Created()
    {
        // Arrange
        var command = _alertFixture.CreateAlertCommand;
        var expectedMessage = $"Alert '{command.Type}' has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertResponse
            {
                Message = expectedMessage,
                AlertId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlert(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateAlert_ReturnsOk()
    {
        // Arrange
        var expectedMessage = $"Alert '{_alertFixture.UpdateAlertCommand.Type}' has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdateAlertCommand>(), default))
            .ReturnsAsync(new UpdateAlertResponse
            {
                Message = expectedMessage,
                Id = _alertFixture.UpdateAlertCommand.Id
            });

        // Act
        var result = await _controller.UpdateAlert(_alertFixture.UpdateAlertCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteAlert_ReturnsOk()
    {
        // Arrange
        var expectedMessage = "Alert 'Critical Alert' has been deleted successfully!.";
        var alertId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteAlertCommand>(c => c.Id == alertId), default))
            .ReturnsAsync(new DeleteAlertResponse
            {
                IsActive = false,
                Message = expectedMessage
            });

        // Act
        var result = await _controller.DeleteAlert(alertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task GetPaginatedAlerts_ReturnsExpectedPaginatedList()
    {
        // Arrange
        var query = new GetAlertPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _alertFixture.AlertListVm;
        var alertCounts = new Dictionary<string, int> { { "Critical", 2 }, { "Warning", 1 } };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PaginatedResult<AlertListVm>.Success(
                data: expectedData,
                count: expectedData.Count,
                page: query.PageNumber,
                pageSize: query.PageSize
            ), alertCounts));

        // Act
        var result = await _controller.GetPaginatedAlerts(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var (paginatedResult, counts) = ((PaginatedResult<AlertListVm>, Dictionary<string, int>))okResult.Value!;

        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(2, counts["Critical"]);
        Assert.Equal(1, counts["Warning"]);
    }

    [Fact]
    public async Task GetLastAlertCount_ReturnsAlertCount()
    {
        // Arrange
        var expectedResult = new GetLastAlertDetailVm
        {
            CompanyId = "TEST_COMPANY_123",
            UserId = "TEST_USER_123",
            UserName = "TestUser",
            AlertId = 1055,
            AlertCount = 25
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetLastAlertDetailQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetLastAlertCount();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<GetLastAlertDetailVm>(okResult.Value);
        Assert.Equal(25, response.AlertCount);
    }

    [Fact]
    public async Task GetAlertByClientAlertId_ReturnsAlerts_WhenClientAlertIdIsValid()
    {
        // Arrange
        var clientAlertId = Guid.NewGuid().ToString();
        var expectedResult = new List<AlertByClientAlertIdVm>
        {
            new() { Id = Guid.NewGuid().ToString(), ClientAlertId = clientAlertId, Type = "Critical" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertByClientAlertIdQuery>(q => q.ClientAlertId == clientAlertId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertByClientAlertId(clientAlertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertByClientAlertIdVm>>(okResult.Value);
        Assert.Single(alerts);
        Assert.Equal(clientAlertId, alerts.First().ClientAlertId);
    }

    [Fact]
    public async Task GetAlertByClientAlertId_Throws_WhenClientAlertIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertByClientAlertId("invalid-guid"));
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_ReturnsAlerts_WhenInfraObjectIdIsValid()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var entityId = Guid.NewGuid().ToString();
        var expectedResult = new List<AlertByInfraObjectIdVm>
        {
            new() { Id = Guid.NewGuid().ToString(), InfraObjectId = infraObjectId, Type = "Warning" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertByInfraObjectId(infraObjectId,entityId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertByInfraObjectIdVm>>(okResult.Value);
        Assert.Single(alerts);
        Assert.Equal(infraObjectId, alerts.First().InfraObjectId);
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_Throws_WhenInfraObjectIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetAlertByInfraObjectId("invalid-guid","invalid-guid"));
    }

    [Fact]
    public async Task GetAlerts_CallsCorrectQuery()
    {
        // Arrange
        var companyId = "TEST_COMPANY_123";
        _controller.Cache.Remove(ApplicationConstants.Cache.AllAlertCacheKey + companyId);

        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(new List<AlertListVm>());

        // Act
        await _controller.GetAlerts();

        // Assert
        Assert.True(queryExecuted);
    }

    [Fact]
    public async Task GetAlertListByStartOfWeek_ReturnsExpectedList_WhenDatesProvided()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-01-07";
        var expectedResult = new List<AlertListByStartOfWeekVm>
        {
            new() { StartOfWeeks = DateTime.Parse("2024-01-01"), AlertListCount = "5" },
            new() { StartOfWeeks = DateTime.Parse("2024-01-02"), AlertListCount = "3" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertListByStartOfWeekQuery>(q => q.StartDate == startDate && q.EndDate == endDate), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertListByStartOfWeek(startDate, endDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertsByWeek = Assert.IsAssignableFrom<List<AlertListByStartOfWeekVm>>(okResult.Value);
        Assert.Equal(2, alertsByWeek.Count);
        Assert.Equal("5", alertsByWeek.First().AlertListCount);
    }

    [Fact]
    public async Task GetAlertListByStartOfWeek_ReturnsExpectedList_WhenNoDatesProvided()
    {
        // Arrange
        var expectedResult = new List<AlertListByStartOfWeekVm>
        {
            new() { StartOfWeeks = DateTime.Now.Date, AlertListCount = "10" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertListByStartOfWeekQuery>(q => q.StartDate == null && q.EndDate == null), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertListByStartOfWeek(null, null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertsByWeek = Assert.IsAssignableFrom<List<AlertListByStartOfWeekVm>>(okResult.Value);
        Assert.Single(alertsByWeek);
        Assert.Equal("10", alertsByWeek.First().AlertListCount);
    }

    [Fact]
    public async Task GetAlertListFilterByDate_ReturnsExpectedList()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";
        var expectedResult = new List<AlertListFilterByDateVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Type = "Critical",
                Severity = "High",
                SystemMessage = "Database connection failed",
                UserMessage = "Unable to connect to the primary database server",
                JobName = "DatabaseMonitorJob",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "PrimaryDB01",
                ClientAlertId = Guid.NewGuid().ToString(),
                IsResolve = 0,
                IsAcknowledgement = 0,
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Database",
                AlertCategoryId = 1,
                LastModifiedDate = DateTime.Parse("2024-01-15")
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = Guid.NewGuid().ToString(),
                Type = "Warning",
                Severity = "Medium",
                SystemMessage = "High CPU usage detected",
                UserMessage = "Server CPU usage is above 85%",
                JobName = "SystemMonitorJob",
                InfraObjectId = Guid.NewGuid().ToString(),
                InfraObjectName = "AppServer01",
                ClientAlertId = Guid.NewGuid().ToString(),
                IsResolve = 0,
                IsAcknowledgement = 1,
                EntityId = Guid.NewGuid().ToString(),
                EntityType = "Server",
                AlertCategoryId = 2,
                LastModifiedDate = DateTime.Parse("2024-01-20")
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertListFilterByDateQuery>(q => q.StartDate == startDate && q.EndDate == endDate), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertListFilterByDate(startDate, endDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var filteredAlerts = Assert.IsAssignableFrom<List<AlertListFilterByDateVm>>(okResult.Value);
        Assert.Equal(2, filteredAlerts.Count);
        Assert.Equal("Critical", filteredAlerts.First().Type);
        Assert.Equal("Warning", filteredAlerts.Last().Type);
    }

    [Fact]
    public async Task GetAlertListFilterByDate_ReturnsEmptyList_WhenNoAlertsInDateRange()
    {
        // Arrange
        var startDate = "2024-01-01";
        var endDate = "2024-01-31";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertListFilterByDateQuery>(q => q.StartDate == startDate && q.EndDate == endDate), default))
            .ReturnsAsync(new List<AlertListFilterByDateVm>());

        // Act
        var result = await _controller.GetAlertListFilterByDate(startDate, endDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var filteredAlerts = Assert.IsAssignableFrom<List<AlertListFilterByDateVm>>(okResult.Value);
        Assert.Empty(filteredAlerts);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act & Assert
        // This should not throw any exceptions
        _controller.ClearDataCache();

        // The method should complete without errors
        Assert.True(true);
    }

    [Fact]
    public async Task CreateAlert_ValidatesRequiredFields()
    {
        // Arrange
        var command = new CreateAlertCommand
        {
            Type = "", // Empty type should cause validation error
            Severity = "High",
            SystemMessage = "Test message"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new ArgumentException("Type is required"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _controller.CreateAlert(command));
    }

    [Fact]
    public async Task UpdateAlert_ValidatesAlertExists()
    {
        // Arrange
        var command = new UpdateAlertCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Updated Alert",
            Severity = "Medium"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Alert not found"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.UpdateAlert(command));
    }

    [Fact]
    public async Task GetPaginatedAlerts_HandlesEmptyResults()
    {
        // Arrange
        var query = new GetAlertPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var emptyData = new List<AlertListVm>();
        var alertCounts = new Dictionary<string, int>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PaginatedResult<AlertListVm>.Success(
                data: emptyData,
                count: 0,
                page: query.PageNumber,
                pageSize: query.PageSize
            ), alertCounts));

        // Act
        var result = await _controller.GetPaginatedAlerts(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var (paginatedResult, counts) = ((PaginatedResult<AlertListVm>, Dictionary<string, int>))okResult.Value!;

        Assert.Empty(paginatedResult.Data);
        Assert.Equal(0, paginatedResult.TotalCount);
        Assert.Empty(counts);
    }

    [Fact]
    public async Task GetAlertByClientAlertId_HandlesMultipleResults()
    {
        // Arrange
        var clientAlertId = Guid.NewGuid().ToString();
        var expectedResult = new List<AlertByClientAlertIdVm>
        {
            new() { Id = Guid.NewGuid().ToString(), ClientAlertId = clientAlertId, Type = "Critical" },
            new() { Id = Guid.NewGuid().ToString(), ClientAlertId = clientAlertId, Type = "Warning" },
            new() { Id = Guid.NewGuid().ToString(), ClientAlertId = clientAlertId, Type = "Information" }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertByClientAlertIdQuery>(q => q.ClientAlertId == clientAlertId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAlertByClientAlertId(clientAlertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertByClientAlertIdVm>>(okResult.Value);
        Assert.Equal(3, alerts.Count);
        Assert.All(alerts, alert => Assert.Equal(clientAlertId, alert.ClientAlertId));
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_HandlesEmptyResults()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var entityId = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.Is<GetAlertByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(new List<AlertByInfraObjectIdVm>());

        // Act
        var result = await _controller.GetAlertByInfraObjectId(infraObjectId,entityId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alerts = Assert.IsAssignableFrom<List<AlertByInfraObjectIdVm>>(okResult.Value);
        Assert.Empty(alerts);
    }

    [Fact]
    public async Task GetAlertListByStartOfWeek_HandlesInvalidDateFormat()
    {
        // Arrange
        var invalidStartDate = "invalid-date";
        var validEndDate = "2024-01-07";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertListByStartOfWeekQuery>(), default))
            .ThrowsAsync(new FormatException("Invalid date format"));

        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() =>
            _controller.GetAlertListByStartOfWeek(invalidStartDate, validEndDate));
    }

    [Fact]
    public async Task GetAlertListFilterByDate_ValidatesDateRange()
    {
        // Arrange
        var startDate = "2024-01-31"; // Start date after end date
        var endDate = "2024-01-01";

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetAlertListFilterByDateQuery>(), default))
            .ThrowsAsync(new ArgumentException("Start date must be before end date"));

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _controller.GetAlertListFilterByDate(startDate, endDate));
    }

    [Fact]
    public async Task CreateAlert_HandlesComplexAlertData()
    {
        // Arrange
        var command = new CreateAlertCommand
        {
            CompanyId = Guid.NewGuid().ToString(),
            Type = "System Critical",
            Severity = "High",
            SystemMessage = "Complex system failure detected with multiple components affected",
            UserMessage = "Multiple systems are experiencing issues. Please contact IT immediately.",
            JobName = "ComplexMonitoringJob",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = "ComplexInfrastructure01",
            ClientAlertId = Guid.NewGuid().ToString(),
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "ComplexSystem",
            AlertCategoryId = 5
        };

        var expectedMessage = $"Alert '{command.Type}' has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateAlertResponse
            {
                Message = expectedMessage,
                AlertId = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateAlert(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateAlertResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Contains("System Critical", response.Message);
    }
}
