﻿using ContinuityPatrol.Application.Features.FormHistory.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormHistory.Validators;

public class CreateFormHistoryValidatorTests
{
    private readonly Mock<IFormHistoryRepository> _mockFormHistoryRepository;
    public List<Domain.Entities.FormHistory> FormHistories { get; set; }

    public CreateFormHistoryValidatorTests()
    {
        FormHistories = new Fixture().Create<List<Domain.Entities.FormHistory>>();

        _mockFormHistoryRepository = FormHistoryRepositoryMocks.CreateFormHistoryRepository(FormHistories);
    }


    //Name

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Name_InFormHistory_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Name_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter Valid Type.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Name_InFormHistory_MinimumRange(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "CB";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Name_InFormHistory_MaximumRange(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONM";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Valid_Name_InFormHistory(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "  Infosys  ";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_DoubleSpace_InFront(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "  Infosys";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_DoubleSpace_InBack(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "Infosys  ";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_TripleSpace_InBetween(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "Infosys   Tech";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_SpecialCharacters_InFront(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "*&^%%$Infosys Tech";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_SpecialCharacters_InBack(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "Infosys Tech*&^%$#$";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_SpecialCharacters_InBetween(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "Infosys*&^&^%$Tech";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_SpecialCharacters_Only(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "*&^&^%&%";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_UnderScore_InFront(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "_Infosys";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_UnderScore_InBack(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "Infosys_";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_UnderScore_InFront_With_Numbers_InFront(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "_434Infosys";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_Numbers_InFront(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "341Infosys";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_UnderScore_InFront_With_Numbers_InBack(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "_Infosys874";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Valid_Create_Name_InFormHistory_With_Numbers_Only(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.FormName = "846486454542";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[0].ErrorMessage);
    }


    //LoginName

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_LoginName_InFormHistory_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.LoginName = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_LoginName_InFormHistory_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.LoginName = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[2].ErrorMessage);
    }


    //Properties

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Properties_InFormHistory_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Properties = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_Create_Properties_InFormHistory_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Properties = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.FormHistory.FormHistoryTypeValidRequired, (string)validateResult.Errors[2].ErrorMessage);
    }


    //Type

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Type_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Type = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Type is Required.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Type_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Type = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "'Type' must not be empty.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Valid_Type(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Type = " CTS ";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter Valid Type.");
    }


    //Description

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Description_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Description = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter the Description.");
    }


    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Description_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Description = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);
        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "'Description' must not be empty.");
    }


    //Comments

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Comments_WithEmpty(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Comments = "";

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter the Comments.");
    }

    [Theory]
    [AutoFormHistoryData]
    public async Task Verify_CreateFormHistoryCommandValidator_Comments_IsNull(CreateFormHistoryCommand createFormHistoryCommand)
    {
        var validator = new CreateFormHistoryCommandValidator(_mockFormHistoryRepository.Object);

        createFormHistoryCommand.Comments = null;

        var validateResult = await validator.ValidateAsync(createFormHistoryCommand, CancellationToken.None);

        Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please Enter the Comments.");
    }
}