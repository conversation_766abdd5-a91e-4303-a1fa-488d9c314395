﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.Features.Replication.Commands.Create;

public class CreateReplicationCommandValidator : AbstractValidator<CreateReplicationCommand>
{
    private readonly IComponentTypeRepository _componentTypeRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly IReplicationRepository _replicationRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;


    public CreateReplicationCommandValidator(IReplicationRepository replicationRepository,
        ISiteRepository siteRepository,
        ILicenseValidationService licenseValidationService,
        ILicenseManagerRepository licenseManagerRepository,
        ISiteTypeRepository siteTypeRepository,
        IComponentTypeRepository componentTypeRepository)
    {
        _replicationRepository = replicationRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _licenseManagerRepository = licenseManagerRepository;
        _siteTypeRepository = siteTypeRepository;
        _componentTypeRepository = componentTypeRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(e => e)
            .MustAsync(IsValidReplicationTypeAsync)
            .WithMessage("Replication Type is invalid.");

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}");

        RuleFor(e => e)
            .MustAsync(IsValidSiteAsync)
            .WithMessage("Site is invalid.");

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p)
            .MustAsync(IsLicenseExpiredAsync)
            .WithMessage("The license key has expired.")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p)
            .MustAsync(IsLicenseActiveAsync)
            .WithMessage("License is in 'InActive' state")
            .When(p => p.LicenseId != "NA");

        RuleFor(p => p)
            .MustAsync(ValidateLicenseCountAsync)
            .WithMessage("Replication count Reached maximum limit.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s\-]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid Operational Service")
            .NotNull()
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Must(GetJsonProperties.IsValidJson).WithMessage("{PropertyName} must be a valid JSON string.");

        RuleFor(e => e)
            .MustAsync(ReplicationNameUnique)
            .WithMessage("A same name already exists.");
    }


    public async Task<bool> IsValidSiteAsync(CreateReplicationCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.SiteId, "Site Id is invalid");
        var site = await _siteRepository.GetByReferenceIdAsync(p.SiteId);
        if (site == null) return false;
        return site.Name.Equals(p.SiteName, StringComparison.OrdinalIgnoreCase);
    }

    public async Task<bool> IsValidReplicationTypeAsync(CreateReplicationCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.TypeId, "ReplicationType Id is invalid");
        var componentType = await _componentTypeRepository.GetComponentTypeById(p.TypeId);
        if (componentType == null) return false;
        return componentType.ComponentName.Equals(p.Type, StringComparison.OrdinalIgnoreCase);
    }

    public async Task<bool> IsLicenseExpiredAsync(CreateReplicationCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.LicenseId, "License Id");
        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId);
        return await _licenseValidationService.IsLicenseExpired(licenseManager.ExpiryDate);
    }

    public async Task<bool> IsLicenseActiveAsync(CreateReplicationCommand p, CancellationToken cancellationToken)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;
        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                         throw new InvalidException("License GetList is null.");

        return licenseDtl.IsState;
    }

    private async Task<bool> ReplicationNameUnique(CreateReplicationCommand e, CancellationToken cancellationToken)
    {
        return !await _replicationRepository.IsReplicationNameUnique(e.Name);
    }

    public async Task<bool> ValidateLicenseCountAsync(CreateReplicationCommand p, CancellationToken cancellationToken)
    {
        if (!p.Type.ToLower().Contains("perpetuuiti")) return true;

        if (p.LicenseId.IsNullOrWhiteSpace()) return false;

        var site = await _siteRepository.GetByReferenceIdAsync(p.SiteId);

        Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
            new NotFoundException(nameof(Domain.Entities.Site), p.SiteId));

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

        Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
            new NotFoundException(nameof(Domain.Entities.SiteType), site.TypeId));

        var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

        var license = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId);

        Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), p.LicenseId));

        var replicationCount = await _replicationRepository.GetReplicationCountByLicenseKey(p.LicenseId, siteType.ReferenceId);

        return await _licenseValidationService.IsReplicationLicenseCountExitMaxLimit(license, siteType,
            replicationCount, index);
    }
}