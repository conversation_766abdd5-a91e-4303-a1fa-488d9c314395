﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponent.Events.Paginated;

public class CyberComponentPaginatedEventHandler : INotificationHandler<CyberComponentPaginatedEvent>
{
    private readonly ILogger<CyberComponentPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentPaginatedEventHandler(ILogger<CyberComponentPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(CyberComponentPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.CyberComponent}",
            Entity = Modules.CyberComponent.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency Component viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency Component viewed");
    }
}