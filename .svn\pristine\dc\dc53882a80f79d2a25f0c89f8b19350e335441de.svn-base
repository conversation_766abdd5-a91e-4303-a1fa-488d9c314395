﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowInfraObjectRepository : BaseRepository<WorkflowInfraObject>, IWorkflowInfraObjectRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowInfraObjectRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<WorkflowInfraObject>> ListAllAsync()
    {
        var wfInfraObjects = base.QueryAll(infra =>
                   infra.CompanyId.Equals(_loggedInUserService.CompanyId));

        var wfInfraObjectList = MapWorkflowInfraObject(wfInfraObjects);

        return _loggedInUserService.IsAllInfra
            ? await wfInfraObjectList.ToListAsync()
            : AssignedInfraObjects(wfInfraObjectList);
    }

    public override Task<WorkflowInfraObject> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilter(workflowInfraObject => workflowInfraObject.ReferenceId.Equals(id) &&
                                                                  workflowInfraObject.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }

    public async Task<WorkflowInfraObject> GetInfraObjectIdAttachByWorkflowId(string workflowId, string infraObjectId, string actionType)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
            ? base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.InfraObjectId.Equals(infraObjectId) &&
                                             wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && wf.IsAttach)
            : base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.InfraObjectId.Equals(infraObjectId) &&
                                            wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && wf.IsAttach &&
                                                                            wf.CompanyId.Equals(_loggedInUserService.CompanyId));
        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
        ? await workflowInfra.FirstOrDefaultAsync()
        : GetInfraObjectById(workflowInfra.FirstOrDefault());
    }

    public async Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectFromInfraObjectId(string infraObjectId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
             ? base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId))
             .Select(x => new WorkflowInfraObject
             {
                 InfraObjectId = x.InfraObjectId,
                 WorkflowId = x.WorkflowId,
                 WorkflowName = x.WorkflowName,
                 ActionType = x.ActionType
             })
             : base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.CompanyId.Equals(_loggedInUserService.CompanyId))
             .Select(x => new WorkflowInfraObject
             {
                 InfraObjectId = x.InfraObjectId,
                 WorkflowId = x.WorkflowId,
                 WorkflowName = x.WorkflowName,
                 ActionType = x.ActionType
             });

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();
    }
    public async Task<List<WorkflowInfraObject>> GetResilienceWorkflowByInfraObjectId(string infraObjectId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
            ? base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.ActionType.Trim().ToLower().Equals("resiliency ready"))
            : base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.ActionType.Trim().ToLower().Equals("resiliency ready") && wf.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();

    }
    public async Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectDetailByInfraObjectId(string infraObjectId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
             ? base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId))
             : base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();
    }

    public async Task<List<WorkflowInfraObject>> GetInfraObjectFromWorkflowId(string workflowId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
            ? base.FilterBy(wf => wf.WorkflowId.Equals(workflowId))
            .Select(x => new WorkflowInfraObject
            {
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                ActionType = x.ActionType,
                IsAttach = x.IsAttach
            })
            : base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new WorkflowInfraObject
            {
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName,
                ActionType = x.ActionType,
                IsAttach = x.IsAttach
            });

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();
    }

    public async Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectFromWorkflowId(string workflowId, string infraObjectId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
            ? base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.InfraObjectId.Equals(infraObjectId))
            .Select(x => new WorkflowInfraObject
            {
                InfraObjectId = x.InfraObjectId,
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                ActionType = x.ActionType
            })
            : base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.InfraObjectId.Equals(infraObjectId) && wf.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new WorkflowInfraObject
            {
                InfraObjectId = x.InfraObjectId,
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                ActionType = x.ActionType
            });

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();
    }

    public async Task<WorkflowInfraObject> GetWorkflowInfraObjectByWorkflowIdAsync(string workflowId)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
             ? base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.IsAttach)
             : base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.IsAttach && wf.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.FirstOrDefaultAsync()
            : GetInfraObjectById(workflowInfra.FirstOrDefault());
    }

    public async Task<WorkflowInfraObject> GetWorkflowIdAttachByActionType(string workflowId, string actionType)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
            ? base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && wf.IsAttach)
            : base.FilterBy(wf => wf.WorkflowId.Equals(workflowId) && wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && wf.IsAttach && wf.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.FirstOrDefaultAsync()
            : GetInfraObjectById(workflowInfra.FirstOrDefault());
    }
    public async Task<List<WorkflowInfraObject>> GetWorkflowInfraObjectByInfraObjectIdAndActionType(string infraObjectId, string actionType)
    {
        var workflowInfraObject = _loggedInUserService.IsParent
             ? base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()))
             : base.FilterBy(wf => wf.InfraObjectId.Equals(infraObjectId) && wf.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && wf.CompanyId.Equals(_loggedInUserService.CompanyId));

        var workflowInfra = MapWorkflowInfraObject(workflowInfraObject);

        return _loggedInUserService.IsAllInfra
            ? await workflowInfra.ToListAsync()
            : AssignedInfraObjects(workflowInfra).ToList();
    }
    public Task<bool> WorkflowIdAndInfraObjectIdUnique(string workflowId, string infraObjectId, string actionType)
    {
        var matches = _dbContext.WorkflowInfraObjects
            .Active()
            .Any(e => e.WorkflowId.Equals(workflowId) && e.InfraObjectId.Equals(infraObjectId) &&
                      e.ActionType.Trim().ToLower().Equals(actionType.Trim().ToLower()) && e.IsAttach);

        return Task.FromResult(matches);
    }

    public Task<bool> IsWorkflowIdUnique(string workflowId)
    {
        var matches = _dbContext.WorkflowInfraObjects
            .AsNoTracking()
            .Active()
            .Any(e => e.WorkflowId.Equals(workflowId));

        return Task.FromResult(matches);
    }

    public Task<bool> IsInfraObjectIdUnique(string infraObjectId)
    {
        var matches = _dbContext.WorkflowInfraObjects.Active().Any(e => e.InfraObjectId.Equals(infraObjectId));

        return Task.FromResult(matches);
    }

    // No Need to Check Assigned InfraObjects
    public async Task<WorkflowInfraObject> GetWorkflowInfraObjectByWorkflowIdForWorkflowList(string workflowId)
    {
        var matches = await _dbContext.WorkflowInfraObjects.Active().FirstOrDefaultAsync(e => e.WorkflowId.Equals(workflowId));

        return matches;
    }

    //Filter by CompanyId And Assigned InfraObjects
    private IQueryable<WorkflowInfraObject> MapWorkflowInfraObject(IQueryable<WorkflowInfraObject> workflowInfraObjects)
    {
        return workflowInfraObjects.Select(x => new
        {
            InfraObject = _dbContext.InfraObjects.FirstOrDefault(c => c.ReferenceId.Equals(x.InfraObjectId)),
            WorkFlow = _dbContext.Workflows.FirstOrDefault(b => b.ReferenceId.Equals(x.WorkflowId)),
            WorkflowInfraObject = x
        })
        .Select(res => new WorkflowInfraObject
        {
            Id = res.WorkflowInfraObject.Id,
            ReferenceId = res.WorkflowInfraObject.ReferenceId,
            CompanyId = res.WorkflowInfraObject.ReferenceId,
            InfraObjectId = res.InfraObject.ReferenceId,
            InfraObjectName = res.InfraObject.Name,
            WorkflowId = res.WorkFlow.ReferenceId,
            WorkflowName = res.WorkFlow.Name,
            WorkflowVersion = res.WorkFlow.Version,
            ActionType = res.WorkflowInfraObject.ActionType,
            TotalRTO = res.WorkflowInfraObject.TotalRTO,
            IsAttach = res.WorkflowInfraObject.IsAttach,
            IsActive = res.WorkflowInfraObject.IsActive,
            CreatedBy = res.WorkflowInfraObject.CreatedBy,
            CreatedDate = res.WorkflowInfraObject.CreatedDate,
            LastModifiedBy = res.WorkflowInfraObject.LastModifiedBy,
            LastModifiedDate = res.WorkflowInfraObject.LastModifiedDate,
        });
    }


    private IReadOnlyList<WorkflowInfraObject> AssignedInfraObjects(IQueryable<WorkflowInfraObject> infraObjects)
    {
        var infraObjectList = new List<WorkflowInfraObject>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }
    private WorkflowInfraObject GetInfraObjectById(WorkflowInfraObject infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }
}