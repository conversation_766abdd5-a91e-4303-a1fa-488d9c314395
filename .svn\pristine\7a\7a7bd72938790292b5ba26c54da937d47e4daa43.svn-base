﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Events.Create;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Create;

public class CreateWorkflowOperationGroupCommandHandler : IRequestHandler<CreateWorkflowOperationGroupCommand,
    CreateWorkflowOperationGroupResponse>
{
    private readonly ILoadBalancerRepository _loadBalancerRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IJobScheduler _client;
    private readonly ILogger<CreateWorkflowOperationGroupCommandHandler> _logger;

    public CreateWorkflowOperationGroupCommandHandler(I<PERSON><PERSON>per mapper,
        ILoggedInUserService loggedInUserService,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IPublisher publisher, ILoadBalancerRepository loadBalancerRepository, IJobScheduler client,
        ILogger<CreateWorkflowOperationGroupCommandHandler> logger)
    {
        _mapper = mapper;
        _loggedInUserService = loggedInUserService;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _publisher = publisher;
        _loadBalancerRepository = loadBalancerRepository;
        _client = client;
        _logger = logger;
    }

    public async Task<CreateWorkflowOperationGroupResponse> Handle(CreateWorkflowOperationGroupCommand request,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("Starting Handle method for CreateWorkflowOperationGroupCommand.");

        if (!request.CreateWorkflowOperationGroupListCommands.Any())
        {
            _logger.LogError("Action List is empty.");
            throw new InvalidException("Action List is empty.");
        }

        _logger.LogDebug("Fetching Node Configuration for type 'ALL' and 'WorkflowService'...");

        var nodeConfig =
            await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString())
            ?? await _loadBalancerRepository.GetNodeConfigurationByTypeAndTypeCategory(ServiceType.WorkflowService.ToString(),
                ServiceType.LoadBalancer.ToString());

        if (nodeConfig is null)
        {
            _logger.LogError("LoadBalancer not configured.");
            throw new InvalidException("LoadBalancer not configured!.");
        }

        _logger.LogDebug("Node configuration fetched successfully.");

        request.CompanyId = _loggedInUserService.CompanyId;

        _logger.LogDebug("Assigned CompanyId {CompanyId} to request.", request.CompanyId);

        request.CreateWorkflowOperationGroupListCommands.ForEach(x => x.CompanyId = _loggedInUserService.CompanyId);

        _logger.LogDebug("Assigned CompanyId to each item in CreateWorkflowOperationGroupListCommands.");

        _logger.LogDebug("Mapping CreateWorkflowOperationGroupListCommands to WorkflowOperationGroup entities...");
        var workflowOperationGroup =
            _mapper.Map<List<Domain.Entities.WorkflowOperationGroup>>(request.CreateWorkflowOperationGroupListCommands);

        _logger.LogDebug("Mapping request to WorkflowOperation entity...");
        var workflowOperations = _mapper.Map<Domain.Entities.WorkflowOperation>(request);

        workflowOperations.CompanyId = _loggedInUserService.CompanyId;
        workflowOperations.UserName = _loggedInUserService.LoginName;
        workflowOperations.StartTime = DateTime.Now;
        workflowOperations.Status = "Pending";

        _logger.LogDebug("Saving WorkflowOperation entity with CompanyId: {CompanyId}, UserName: {UserName}, StartTime: {StartTime}, Status: {Status}.",
            workflowOperations.CompanyId, workflowOperations.UserName, workflowOperations.StartTime, workflowOperations.Status);

        await _workflowOperationRepository.AddAsync(workflowOperations);

        workflowOperationGroup.ForEach(x => x.WorkflowOperationId = workflowOperations.ReferenceId);

        _logger.LogDebug("Assigned WorkflowOperationId {WorkflowOperationId} to each WorkflowOperationGroup entity.", workflowOperations.ReferenceId);

        _ = await _workflowOperationGroupRepository.AddRangeAsync(workflowOperationGroup) as
            List<Domain.Entities.WorkflowOperationGroup>;

        _logger.LogDebug("Saved WorkflowOperationGroup entities.");

        var actionList = request.CreateWorkflowOperationGroupListCommands
            .Select(x => new ActionList
            {
                WorkflowId = x.WorkflowId,
                WorkflowName = x.WorkflowName,
                ActionMode = x.ActionMode,
                InfraObjectId = x.InfraObjectId,
                InfraObjectName = x.InfraObjectName
            }).ToList();

        await _publisher.Publish(
            new WorkflowOperationGroupCreatedEvent
            {
                ProfileId = workflowOperations.ProfileId,
                ProfileName = workflowOperations.ProfileName,
                RunMode = workflowOperations.RunMode,
                IsDrCalendar = workflowOperations.IsDrCalendar,
                DrCalendarId = request.DrCalendarId,
                WorkflowOperationId = workflowOperations.ReferenceId,
                Actions = actionList
            }, cancellationToken);


        _logger.LogDebug("Constructing base URL with ConnectionType: {ConnectionType}, IPAddress: {IPAddress}, Port: {Port}.",
            nodeConfig.ConnectionType, nodeConfig.IPAddress, nodeConfig.Port);

        var baseUrl = $"{nodeConfig.ConnectionType}://{nodeConfig.IPAddress}:{nodeConfig.Port}";

        _logger.LogDebug("Constructing URL based on TypeCategory: {TypeCategory}. ReferenceId: {ReferenceId}",
            nodeConfig.TypeCategory, workflowOperations.ReferenceId);

        var url = UrlHelper.GenerateExecutionUrl(nodeConfig.TypeCategory, baseUrl, workflowOperations.ReferenceId);

        _logger.LogDebug("URL constructed: {Url}", url);

        var jobData = new Dictionary<string, string> { ["url"] = url, ["workflow"] = "workflow" };

        _logger.LogDebug("Scheduling job with ReferenceId: {ReferenceId}.", workflowOperations.ReferenceId);

        await _client.ScheduleJob(workflowOperations.ReferenceId, jobData);

        var seqJobData = new Dictionary<string, string> { ["groupId"] = workflowOperationGroup.FirstOrDefault()?.ReferenceId, ["IsHub"] = "true", ["IsFilter"] = "true" }; 
        
        await _client.ScheduleSeqServiceJob(workflowOperationGroup.FirstOrDefault()?.ReferenceId, seqJobData);


        _logger.LogDebug("Creating response with message and WorkflowOperationId.");

        var response = new CreateWorkflowOperationGroupResponse
        {
            Message = "Profile Initiated Successfully.",

            WorkflowOperationId = workflowOperations.ReferenceId
        };
        _logger.LogDebug("Response created with Message: {Message} and WorkflowOperationId: {WorkflowOperationId}.",
            response.Message, response.WorkflowOperationId);

        return response;
    }
}