﻿using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;

namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetList;

public class
    GetSmtpConfigurationListQueryHandler : IRequestHandler<GetSmtpConfigurationListQuery, SmtpConfigurationListVm>
{
    private readonly IMapper _mapper;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;

    public GetSmtpConfigurationListQueryHandler(IMapper mapper,
        ISmtpConfigurationRepository smtpConfigurationRepository)
    {
        _mapper = mapper;
        _smtpConfigurationRepository = smtpConfigurationRepository;
    }

    public async Task<SmtpConfigurationListVm> Handle(GetSmtpConfigurationListQuery request,
        CancellationToken cancellationToken)
    {
        var smtpConfigurations = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        var result = smtpConfigurations != null
            ? _mapper.Map<SmtpConfigurationListVm>(smtpConfigurations)
            : throw new Exception("Smtp Configuration Detail was not found.");

        result.UserName = SecurityHelper.Decrypt(result.UserName);

        return result;
    }
}