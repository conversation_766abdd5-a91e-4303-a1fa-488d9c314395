﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceEvaluation.Commands;

public class CreateBusinessServiceEvaluationTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly Mock<IBusinessServiceEvaluationRepository> _mockBusinessServiceEvaluationRepository;
    private readonly CreateBusinessServiceEvaluationCommandHandler _handler;

    public CreateBusinessServiceEvaluationTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.CreateBusinessServiceEvaluationRepository(_businessServiceEvaluationFixture.BusinessServiceEvaluations);

        _handler = new CreateBusinessServiceEvaluationCommandHandler(_businessServiceEvaluationFixture.Mapper, _mockBusinessServiceEvaluationRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseBusinessServiceEvaluationCount_When_BusinessServiceEvaluationCreated()
    {
        await _handler.Handle(_businessServiceEvaluationFixture.CreateBusinessServiceEvaluationCommand, CancellationToken.None);

        var allCategories = await _mockBusinessServiceEvaluationRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations.Count);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceEvaluationResponse_When_BusinessServiceEvaluationCreated()
    {
        var result = await _handler.Handle(_businessServiceEvaluationFixture.CreateBusinessServiceEvaluationCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBusinessServiceEvaluationResponse));

        result.BusinessServiceEvaluationId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceEvaluationFixture.CreateBusinessServiceEvaluationCommand, CancellationToken.None);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }
}