using System.ComponentModel.DataAnnotations.Schema;

namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;

public class CreateCyberAirGapStatusCommand : IRequest<CreateCyberAirGapStatusResponse>
{
    public string AirGapId { get; set; }
    public string AirGapName { get; set; }
    public string Description { get; set; }
    public string SourceSiteId { get; set; }
    public string SourceSiteName { get; set; }
    public string TargetSiteName { get; set; }
    public string TargetSiteId { get; set; }
    public int Port { get; set; }
    [Column(TypeName = "NCLOB")] public string Source { get; set; }
    [Column(TypeName = "NCLOB")] public string Target { get; set; }
    public string SourceComponentId { get; set; }
    public string SourceComponentName { get; set; }
    public string TargetComponentId { get; set; }
    public string TargetComponentName { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowStatus { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string RPO { get; set; }
    public string Status { get; set; }

    public bool IsFileTransfered { get; set; }
}