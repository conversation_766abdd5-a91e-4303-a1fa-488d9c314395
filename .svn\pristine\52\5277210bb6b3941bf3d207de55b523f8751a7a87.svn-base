﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetDetail;

public class
    GetMYSQLMonitorStatusDetailQueryHandler : IRequestHandler<GetMYSQLMonitorStatusDetailQuery,
        MYSQLMonitorStatusDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorStatusRepository _mysqlMonitorStatusRepository;

    public GetMYSQLMonitorStatusDetailQueryHandler(IMysqlMonitorStatusRepository mysqlMonitorStatusRepository,
        IMapper mapper)
    {
        _mysqlMonitorStatusRepository = mysqlMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<MYSQLMonitorStatusDetailVm> Handle(GetMYSQLMonitorStatusDetailQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorstatus = await _mysqlMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(mysqlMonitorstatus, nameof(Domain.Entities.MYSQLMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.MYSQLMonitorStatus), request.Id));

        var mysqlMonitorstatusDetail = _mapper.Map<MYSQLMonitorStatusDetailVm>(mysqlMonitorstatus);

        return mysqlMonitorstatusDetail ??
               throw new NotFoundException(nameof(Domain.Entities.MYSQLMonitorStatus), request.Id);
    }
}