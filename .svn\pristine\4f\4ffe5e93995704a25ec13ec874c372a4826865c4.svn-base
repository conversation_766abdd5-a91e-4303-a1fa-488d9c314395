﻿using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Commands;

public class CreateReportTests : IClassFixture<ReportFixture>
{
    private readonly ReportFixture _reportFixture;

    private readonly Mock<IReportRepository> _mockReportRepository;

    private readonly CreateReportCommandHandler _handler;

    public CreateReportTests(ReportFixture reportFixture)
    {
        _reportFixture = reportFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockReportRepository = ReportRepositoryMocks.CreateReportRepository(_reportFixture.Reports);

        _handler = new CreateReportCommandHandler(_reportFixture.Mapper, _mockReportRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_Report()
    {
        await _handler.Handle(_reportFixture.CreateReportCommand, CancellationToken.None);

        var allCategories = await _mockReportRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_reportFixture.Reports.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulReportResponse_When_AddValidReport()
    {
        var result = await _handler.Handle(_reportFixture.CreateReportCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateReportResponse));

        result.ReportId.ShouldBeGreaterThan(0);

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_reportFixture.CreateReportCommand, CancellationToken.None);

        _mockReportRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Report>()), Times.Once);
    }
}