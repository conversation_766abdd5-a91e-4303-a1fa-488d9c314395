﻿using ContinuityPatrol.Application.Features.Alert.Queries.GetAlertListByStartOfWeek;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Alert.Queries;

public class GetAlertListByStartOfWeekQueryHandlerTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;

    private Mock<IAlertRepository> _mockAlertRepository;

    private readonly GetAlertListByStartOfWeekQueryHandler _handler;

    public GetAlertListByStartOfWeekQueryHandlerTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;
        
        _mockAlertRepository = AlertRepositoryMocks.GetAlertListByStartOfWeekRepository(_alertFixture.Alerts);

        _handler = new GetAlertListByStartOfWeekQueryHandler(_mockAlertRepository.Object, _alertFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_ActiveAlertList_ByStartOfWeek_Counts()
    {
        var result = await _handler.Handle(new GetAlertListByStartOfWeekQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertListByStartOfWeekVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Valid_AlertList()
    {
        var result = await _handler.Handle(new GetAlertListByStartOfWeekQuery(), CancellationToken.None);
        
        result.ShouldBeOfType<List<AlertListByStartOfWeekVm>>();
       
        result.Count.ShouldBe(3);

        var startOfWeek = DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek + (int)DayOfWeek.Monday);

        //result[0].StartOfWeeks.DayOfWeek.ShouldBe(DayOfWeek.Monday);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockAlertRepository = AlertRepositoryMocks.GetAlertEmptyRepository();

        var handler = new GetAlertListByStartOfWeekQueryHandler(_mockAlertRepository.Object, _alertFixture.Mapper);

        var result = await handler.Handle(new GetAlertListByStartOfWeekQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetAlertListByStartOfWeekQuery(), CancellationToken.None);

        _mockAlertRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}