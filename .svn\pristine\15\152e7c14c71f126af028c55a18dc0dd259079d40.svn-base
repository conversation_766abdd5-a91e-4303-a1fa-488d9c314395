using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Events.Update;

public class CyberComponentMappingUpdatedEventHandler : INotificationHandler<CyberComponentMappingUpdatedEvent>
{
    private readonly ILogger<CyberComponentMappingUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberComponentMappingUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CyberComponentMappingUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(CyberComponentMappingUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.CyberResiliencyMapping}",
            Entity = Modules.CyberResiliencyMapping.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Cyber Resiliency Mapping '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Cyber Resiliency Mapping '{updatedEvent.Name}' updated successfully.");
    }
}