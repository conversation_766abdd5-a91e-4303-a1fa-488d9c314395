﻿@using ContinuityPatrol.Shared.Services.Helper
@model ContinuityPatrol.Domain.ViewModels.AlertReceiverModel.AlertReceiverViewModal
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<style>
    .list-container {
        position: absolute;
        z-index: 9999;
        bottom:0
    }
</style>
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-notification-manager"></i><span>Notification Manager</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="notifSearchInp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" title="Filter">
                                <i class="cp-filter"></i>
                            </span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li >
                                    <div class="dropdown-item">
                                        <input class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                    <div class="dropdown-item">
                                        <input class="form-check-input" type="checkbox" value="emailaddress=" id="Email">
                                        <label class="form-check-label" for="Email">
                                            Email Address
                                        </label>
                                    </div>
                                    <div class="dropdown-item">
                                        <input class="form-check-input" type="checkbox" value="mobilenumber=" id="Mobile">
                                        <label class="form-check-label" for="Mobile">
                                            Mobile Number
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="notifCreateButton" class="btn btn-primary btn-sm" data-bs-toggle="modal"
                        data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <div id="collapetable">
                <table id="notificationManager" class="table table-hover dataTable no-footer" style="width:100%">
                    <thead>
                        <tr>
                            <th class=" SrNo_th">Sr.No</th>
                            <th>Name</th>
                            <th>Email Address</th>
                            <th>InfraObject Name</th> 
                           
                            <th>Mobile Number</th>
                            <th>Active</th>
                            <th class="Action-th">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
           
        </div>
        <div>
            <span class="align-middle" id="userRole" style="display:none;">  @WebHelper.UserSession.RoleName</span>
            <span class="align-middle" id="userRoleValue" style="display:none;">  @WebHelper.UserSession.RoleName</span>
            <span class="align-middle" id="loggedInUserId" style="display:none;">  @WebHelper.UserSession.LoggedUserId</span>
        </div>
    </div>
    <div id="ConfigurationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.CreateAndEdit" aria-hidden="true"></div>
    <div id="ConfigurationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Manage.Delete" aria-hidden="true"></div>
    <!--Modal Create-->
    <div class="modal fade" id="CreateModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <partial name="Configuration" />
    </div>

</div>
<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabels" data-bs-backdrop="static" data-bs-keyboard="false" aria-hidden="true">
    <partial name="Delete" />
</div>


<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Manage/Notification Manager/NotificationManager.js"></script>










