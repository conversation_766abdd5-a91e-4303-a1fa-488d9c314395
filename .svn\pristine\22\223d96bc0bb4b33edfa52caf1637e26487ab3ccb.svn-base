﻿using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardWidgetModel;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Commands.Update;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Application.Features.DynamicDashboardWidget.Events.Paginated;
using DevExpress.CodeParser;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class TileConfigurationController : Controller
    {
    private readonly IDynamicDashboardWidgetService _pageWidgetService;
    private readonly ILogger<TileConfigurationController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IDataSetService _dataSetService;
    private readonly IPublisher _publisher;
    public TileConfigurationController(IDataProvider dataProvider, IDynamicDashboardWidgetService pageWidgetService, IDataSetService dataSetService, ILogger<TileConfigurationController> logger, IMapper mapper,IPublisher publisher)
    {
        _pageWidgetService = pageWidgetService;
        _dataSetService = dataSetService;
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }
   
    [AntiXss]

    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering  into  Tile Configuration.");

        await _publisher.Publish(new DynamicDashboardWidgetPaginatedEvent());

        return View();

        }

    private IActionResult RouteToPostView(BaseResponse result)
    {
        TempData.Set(result.Success
            ? new NotificationMessage(NotificationType.Success, result.Message)
            : new NotificationMessage(NotificationType.Error, result.Message));

        return RedirectToAction("List", "TileConfiguration", new { area = "Manage" });
    }


    [AntiXss]
    public async Task<IActionResult> GetPageWidgetList()
    {
        try
        {
            var pageWidgetList = await _pageWidgetService.GetDynamicDashboardWidgetList();
            return Json(new { Success = true, Message = pageWidgetList });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on get pageWidget list while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [AntiXss]
    public async Task<IActionResult> GetAllTableAccesses()
    {
        try
        {
            var tableAccesses = await _dataSetService.GetDataSetList();

            return Json(new { Success = true, Message = tableAccesses });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on get all table accesses while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [AntiXss]
    public async Task<JsonResult> DatasetDetails(string data)
    {
        try
        {
            var dataSetServiceDetails = await _dataSetService.RunQuery(data);

            return Json(new { Success = true, Message = dataSetServiceDetails });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on get dataset details while processing the request.{ex.GetMessage()}");
            return Json(new { Success = false, Message = "Data not found." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    public async Task<IActionResult> CreateOrUpdate(DynamicDashboardWidgetViewModel pageWidgetView)
    {
        var pageWidgetId = Request.Form["id"];
        BaseResponse result;

        if (string.IsNullOrEmpty(pageWidgetId))
        {
            var pageWidgetModel = _mapper.Map<CreateDynamicDashboardWidgetCommand>(pageWidgetView);
            result = await _pageWidgetService.CreateAsync(pageWidgetModel);
        }
        else
        {
            var pageWidgetModel = _mapper.Map<UpdateDynamicDashboardWidgetCommand>(pageWidgetView);
            result = await _pageWidgetService.UpdateAsync(pageWidgetModel);
        }

        return Json(result);
    }
    [ValidateAntiForgeryToken]
    [AntiXss]

    public async Task<IActionResult> Delete(string id)
    {
        var deleteWidgetPage = await _pageWidgetService.DeleteAsync(id);

        return RouteToPostView(deleteWidgetPage);
    }

    [AntiXss]
    public async Task<JsonResult> GetMonitorServiceStatusByIdAndType(string monitorId, string type)
    {
        try
        {
            var monitoringData = await _dataProvider.DashboardView.GetMonitorServiceStatusByIdAndType(monitorId, type);
            return Json(new { Success = true, data = monitoringData });
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on processing the request. {ex.GetJsonException()}");
            return ex.GetJsonException();

        }
    }
}

