﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetFormTypeCategoryByName;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeCategoryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IFormMappingService
{
    Task<FormTypeCategoryByFormTypeIdVm> GetFormMappingByFormTypeId(string formTypeId, string version);
    Task<bool> IsFormMappingExist(string name, string id);
    Task<FormTypeCategoryDetailVm> GetByReferenceId(string id);
    Task<BaseResponse> DeleteAsync(string id);
    Task<BaseResponse> UpdateAsync(UpdateFormTypeCategoryCommand updateCommand);
    Task<BaseResponse> CreateAsync(CreateFormTypeCategoryCommand createCommand);
    Task<List<FormTypeCategoryListVm>> GetFormMappingList();
    Task<List<FormTypeCategoryNameVm>> GetFormMappingNames();
    Task<PaginatedResult<FormTypeCategoryListVm>> GetPaginatedFormTypeCategory(GetFormTypeCategoryPaginatedListQuery query);
    Task<List<FormTypeCategoryByNameVm>> GetFormMappingListByName(string name);
}
