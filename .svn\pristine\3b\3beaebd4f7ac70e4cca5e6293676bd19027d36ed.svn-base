﻿using ContinuityPatrol.Application.Features.TableAccess.Event.PaginatedView;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Events
{
    public class PaginatedTableAccessEventTests
    {
        private readonly Mock<ILogger<TableAccessPaginatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TableAccessPaginatedEventHandler _handler;

        public PaginatedTableAccessEventTests()
        {
            _mockLogger = new Mock<ILogger<TableAccessPaginatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TableAccessPaginatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ValidEvent_LogsInformationAndAddsUserActivity()
        {
            var tableAccessPaginatedEvent = new TableAccessPaginatedEvent();

            _mockUserService.Setup(service => service.UserId).Returns("TestUserId");
            _mockUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(service => service.CompanyId).Returns("TestCompanyId");
            _mockUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(tableAccessPaginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "TestUserId" &&
                activity.LoginName == "TestUser" &&
                activity.RequestUrl == "http://example.com" &&
                activity.CompanyId == "TestCompanyId" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Entity == "TableAccess" &&
                activity.Action == "View TableAccess" &&
                activity.ActivityType == "View" &&
                activity.ActivityDetails == "Table Access viewed"
            )), Times.Once);

            _mockLogger.Verify(logger => logger.LogInformation("Table Access viewed"), Times.Once);
        }

        [Fact]
        public async Task Handle_NullEvent_ThrowsArgumentNullException()
        {
            TableAccessPaginatedEvent tableAccessPaginatedEvent = null;
            var cancellationToken = CancellationToken.None;

            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(tableAccessPaginatedEvent, cancellationToken));
        }

        [Fact]
        public async Task Handle_ValidEvent_CallsAddAsyncOnce()
        {
            var tableAccessPaginatedEvent = new TableAccessPaginatedEvent();

            _mockUserService.Setup(service => service.UserId).Returns("AnotherUserId");
            _mockUserService.Setup(service => service.LoginName).Returns("AnotherUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(service => service.CompanyId).Returns("AnotherCompanyId");
            _mockUserService.Setup(service => service.IpAddress).Returns("***********");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(tableAccessPaginatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }
    }
}
