﻿@model ContinuityPatrol.Domain.ViewModels.BasicCompanyViewModal;
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "UserInfo";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/password_stregnth_meter.css" rel="stylesheet" />

    <div class="container-fluid">
    <div class="align-items-center vh-100 row">
        <div class="col-xl-5 col-lg-6 col-md-9 col-sm-8">
            <div class="justify-content-center row">
                <div class="col-xxl-7 col-xl-9 col-lg-8 col-md-9 col-sm-8">
                    <div class="card login_card">
                        <div class="d-flex align-items-end p-3 card-header">
                            <img src="~/img/logo/cplogo.svg" title="CP Logo" alt="Logo" width="320" />
                        </div>
                        <div class="card-body pb-0">                      
                            <h6 id="login">
                                        <span class="step">
                                            <i class="cp-logs"></i>
                                        </span>
                                        <span class="step_title">
                                            Login Information
                                        </span>
                                    </h6>

                            <form id="example-form" asp-controller="Basic" asp-action="UserInfo">
                                <div class="form-group">
                                    <div class="form-label">Login Name</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-user"></i></span>
                                        <input asp-for="LoginName" id="textLoginName" type="text" class="form-control" placeholder="Enter Login Name" maxlength="30" autocomplete="off" />
                                    </div>
                                    <span asp-validation-for="LoginName" id="LoginName-error"></span>
                                </div>
                                <div class="form-group">
                                    <div class="form-label">Login Password</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input asp-for="LoginPassword" id="Password" type="password" class="form-control" placeholder="Enter Login password" maxlength="40" autocomplete="off" />
                                        <span class="input-group-text toggle-password"><i class="cp-password-visible fs-6"></i></span>
                                    </div>
                                    <span asp-validation-for="LoginPassword" id="LoginPassword-error"></span>
                                    <div id="mycPass_strength_wrap">
                                        <div id="passwordDescription">Password not entered</div>
                                        <div id="passwordStrength" class="strength0"></div>
                                        <div id="pswd_info">
                                            <strong>Strong Password Tips:</strong>
                                            <ul>
                                                <li class="invalid" id="length">At least 8 characters</li>
                                                <li class="invalid" id="pnum">At least one number</li>
                                                <li class="invalid" id="capital">At least one lowercase &amp; one uppercase letter</li>
                                                <li class="invalid" id="spchar">At least one special character</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-label">Confirm Password</div>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="cp-lock"></i></span>
                                        <input asp-for="ConfrimPassword" id="textConfirmPassword" autocomplete="off" type="password" maxlength="40" class="form-control" placeholder="Enter Confirm password" />

                                    </div>
                                    <span asp-validation-for="ConfrimPassword" id="ConfirmPassword-error"></span>
                                </div>
                                <input asp-for="Id" type="hidden" id="textLoginId" class="form-control" />
                            </form>
                        </div>                        
                        <div class="card-footer">
                            <button type="button" class="btn btn-secondary btn-sm"  id="btncancel" data-bs-dismiss="modal">Reset</button>                        
                            <button id="save" class="btn btn-primary  btn-sm">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-grid h-100 justify-content-center align-items-center col">
            <div></div>
            <img src="~/img/isomatric/CompanyInformation.svg" alt="Login_ISO" />
            <div class="text-center mb-auto">
                <h5 class="fw-bold">Business Continuity & Disaster Management</h5>
                <span class="text-secondary fw-normal">Workflows to perform actions & failovers in few clicks.</span>
            </div>
        </div>
    </div>
</div>
<footer class="text-center fixed-bottom p-1">
    @{
        var version = Configuration.GetValue<string>("CP:Version");
        var isCOE = Configuration.GetValue<string>("Release:isCOE");
    }

    @if (@isCOE != null)
    {
        <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
    }
    else
    {
        <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
    }
    @* <small>Continuity Patrol Version <span class="cpVersionData"></span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small> *@
</footer>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
 <script>

        //disable back button in browser
        function preventBack() {
            window.history.forward();
        }
        setTimeout("preventBack()", 0);
        window.onunload = function () { null }
    </script>
<script src="~/js/common/wizard.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/configuration/basiccompany/basiccompany.js"></script>
<script src="~/js/configuration/basiccompany/basicuser.js"></script>
<script src="~/js/common/password_stregnth_meter.js"></script>
<script src="~/js/common.js"></script>
