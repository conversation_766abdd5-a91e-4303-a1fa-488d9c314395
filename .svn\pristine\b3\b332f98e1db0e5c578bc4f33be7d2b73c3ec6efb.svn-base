using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ComplianceHistoryRepositoryTests : IClassFixture<ComplianceHistoryFixture>
{
    private readonly ComplianceHistoryFixture _complianceHistoryFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ComplianceHistoryRepository _repository;

    public ComplianceHistoryRepositoryTests(ComplianceHistoryFixture complianceHistoryFixture)
    {
        _complianceHistoryFixture = complianceHistoryFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ComplianceHistoryRepository(_dbContext, DbContextFactory.GetMockUserService());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var complianceHistory = _complianceHistoryFixture.ComplianceHistoryDto;

        // Act
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(complianceHistory.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(complianceHistory.Entity, result.Entity);
        Assert.Equal(complianceHistory.EntityId,result.EntityId);
        Assert.Equal(complianceHistory.LicenseId, result.LicenseId);
        Assert.Single(_dbContext.ComplianceHistories);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var complianceHistory = _complianceHistoryFixture.ComplianceHistoryDto;
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory);
        await _dbContext.SaveChangesAsync();

        complianceHistory.Entity = "Server_Test";
        complianceHistory.EntityId = "4c573802-70d1-4c40-9337-6322358f4673";
        complianceHistory.LicenseId = "e62a3563-339f-42bf-bd17-9399555722ad";
        // Act
        _dbContext.ComplianceHistories.Update(complianceHistory);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(complianceHistory.ReferenceId);

        // Assert
        Assert.Equal("Server_Test", result.Entity);
        Assert.Equal("4c573802-70d1-4c40-9337-6322358f4673", result.EntityId);
        Assert.Equal("e62a3563-339f-42bf-bd17-9399555722ad", result.LicenseId);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var complianceHistory = _complianceHistoryFixture.ComplianceHistoryDto;
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory);
        await _dbContext.SaveChangesAsync();

        // Act
        complianceHistory.IsActive = false;

        _dbContext.ComplianceHistories.Update(complianceHistory);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var complianceHistory = _complianceHistoryFixture.ComplianceHistoryDto;
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(complianceHistory.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.EntityId, result.EntityId);
        Assert.Equal(addedEntity.LicenseId, result.LicenseId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var complianceHistory = _complianceHistoryFixture.ComplianceHistoryDto;
        complianceHistory.ReferenceId= "083e0ff4-61b6-4cc5-b84b-4afa49f73d7a";
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(complianceHistory.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(complianceHistory.ReferenceId, result.ReferenceId);
        Assert.Equal(complianceHistory.LicenseId, result.LicenseId);
        Assert.Equal(complianceHistory.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var complianceHistories = _complianceHistoryFixture.ComplianceHistoryList;
        await _repository.AddRangeAsync(complianceHistories);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(complianceHistories.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Parent/Child Company Tests (IsParent Based)


   

    #endregion

    #region Infrastructure Object Tests

    
    [Fact]
    public async Task Repository_ShouldHandleMultipleEntities()
    {
        // Arrange
        var complianceHistories = _complianceHistoryFixture.ComplianceHistoryList;

        complianceHistories[0].Entity = "Server";
        complianceHistories[1].Entity = "DB";
        complianceHistories[2].Entity = "Replication";
        await _repository.AddRangeAsync(complianceHistories);

        // Act
        var server = await _repository.FindByFilterAsync(x => x.Entity.Contains("Server"));
        var db = await _repository.FindByFilterAsync(x => x.Entity.Contains("DB"));
        var replic = await _repository.FindByFilterAsync(x => x.Entity.Contains("Replication"));

        // Assert
        Assert.Single(server);
        Assert.Single(db);
        Assert.Single(replic);
        Assert.Contains("Server", server.First().Entity);
        Assert.Contains("DB", db.First().Entity);
        Assert.Contains("Replication", replic.First().Entity);
    }

    #endregion


   
    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var complainceHistory = _complianceHistoryFixture.ComplianceHistoryList;
        var complianceHistory1 = complainceHistory[0];
        var complianceHistory2 = complainceHistory[1];


        // Act

        await _dbContext.ComplianceHistories.AddAsync(complianceHistory2);
        await _dbContext.ComplianceHistories.AddAsync(complianceHistory1);
        await _dbContext.SaveChangesAsync();
        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.ComplianceHistories.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var complianceHistories = _complianceHistoryFixture.ComplianceHistoryList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(complianceHistories);
        var initialCount = complianceHistories.Count;

        var toUpdate = complianceHistories.Take(2).ToList();
        toUpdate.ForEach(x => x.Entity = "Server");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = complianceHistories.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Entity == "Server").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
}
