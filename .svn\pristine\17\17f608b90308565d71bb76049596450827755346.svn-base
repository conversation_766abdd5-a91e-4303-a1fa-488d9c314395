using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FiaImpactCategory.Events.Update;

public class FiaImpactCategoryUpdatedEventHandler : INotificationHandler<FiaImpactCategoryUpdatedEvent>
{
    private readonly ILogger<FiaImpactCategoryUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FiaImpactCategoryUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<FiaImpactCategoryUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(FiaImpactCategoryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} FiaImpactCategory",
            Entity = "FiaImpactCategory",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"FiaImpactCategory '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"FiaImpactCategory '{updatedEvent.Name}' updated successfully.");
    }
}