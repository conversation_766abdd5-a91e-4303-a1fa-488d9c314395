﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.AlertReceiver.Event.Create;

public class AlertReceiverCreatedEventHandler : INotificationHandler<AlertReceiverCreatedEvent>
{
    private readonly ILogger<AlertReceiverCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public AlertReceiverCreatedEventHandler(ILogger<AlertReceiverCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(AlertReceiverCreatedEvent alertCreatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.NotificationManager}",
            Entity = Modules.NotificationManager.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Notification manager '{alertCreatedEvent.AlertReceiverName}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Notification manager '{alertCreatedEvent.AlertReceiverName}' created successfully.");
    }
}