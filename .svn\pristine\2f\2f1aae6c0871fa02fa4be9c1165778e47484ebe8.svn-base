﻿using ContinuityPatrol.Application.Features.Setting.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Setting.Queries;

public class GetSettingNameQueryHandlerTests : IClassFixture<SettingFixture>
{
    private readonly SettingFixture _settingFixture;

    private Mock<ISettingRepository> _mockSettingRepository;

    private readonly GetSettingNameQueryHandler _handler;

    public GetSettingNameQueryHandlerTests(SettingFixture settingFixture)
    {
        _settingFixture = settingFixture;

        _mockSettingRepository = SettingRepositoryMocks.GetSettingKeyRepository(_settingFixture.Settings);

        _handler = new GetSettingNameQueryHandler(_mockSettingRepository.Object, _settingFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Active_Settings_Name()
    {
        var result = await _handler.Handle(new GetSettingNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SettingNameVm>>();

        result[0].Id.ShouldBe(_settingFixture.Settings[0].ReferenceId);
        result[0].SKey.ShouldBe(_settingFixture.Settings[0].SKey);
    }

    [Fact]
    public async Task Handle_Return_Active_SettingNamesCount()
    {
        var result = await _handler.Handle(new GetSettingNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SettingNameVm>>();

        result.Count.ShouldBe(_settingFixture.Settings.Count);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockSettingRepository = SettingRepositoryMocks.GetSettingEmptyRepository();

        var handler = new GetSettingNameQueryHandler(_mockSettingRepository.Object, _settingFixture.Mapper);

        var result = await handler.Handle(new GetSettingNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSettingNameQuery(), CancellationToken.None);

        _mockSettingRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}